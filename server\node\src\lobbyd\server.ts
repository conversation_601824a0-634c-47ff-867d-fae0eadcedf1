// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import amqp from 'amqplib';
import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import morgan from 'morgan';
import * as net from 'net';
import path from 'path';
import { nanoid } from 'nanoid';
import Container, { Service } from 'typedi';
import _ from 'lodash';
import assert from 'assert';

import cms, { load as loadCms } from '../cms';
import * as CMSConst from '../cms/const';
import * as OceanProp from '../cms/oceanProp';
import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import { DBConnPool, DbConnPoolManager } from '../mysqllib/pool';
import { MRedisConnPool, AuctionRedisConnPoolMgr } from '../redislib/connPool';
import Pubsub from '../redislib/pubsub';
import * as tcp from '../tcplib';
import { TcpClient, TcpClientSessionManager } from '../tcplib';
import * as lobbyPubsub from './lobbyPubsub';
import NationManager, { Load as LoadNation, NationIntimacy } from './nationManager';
import * as proto from '../proto/lobby/proto';
import { QuestScriptManager } from './quest';
import { ScaledClock } from './scaledClock';
import { TownManager } from './townManager';
import { UserSocket } from './user';
import { DisconnectReason } from './userConnection';
import { UserManager, UserCount } from './userManager';
import * as lobbyRabbitMQ from './lobbyRabbitMQ';
import { Protocol } from '../proto/oceand-lobbyd/protocol';
import { SailProtocol } from '../proto/saild-lobbyd/protocol';
import { ZoneType } from '../cms/ex';
import { DistanceFromTileToTownData } from './distanceFromTileToTownData';
import { LobbyPerfmonManager } from './lobbyPerfmonManager';
import { PacketPerfmon, PacketRecvType, UnitPacketRecvStat } from '../motiflib/packetPerfmon';
import { OccupiedRegionNation, WorldEventNotificationType } from './type/worldEventNotification';
import { LineMailManager } from './lineMailManager';
import stoppable from 'stoppable';
import { MongoDbConnection } from '../mongooselib/mongoDbConnection';
import {
  CollectionLookupTable,
  OceanNpcTemplateLookupTable,
  MateTemplateGroupLookupTable,
  OceanNpcTemplateArenaBotGroupLookupTable,
  OceanNpcTemplateDispatchLookupTable,
  OceanNpcDispatchLookupTable,
  NationPolicyCostLookupTable,
  NationPolicyEffectLookupTable,
  NationDonationRankingSortedLookupTable,
  NationCabinetRewardMailLookupTable,
  FleetDispatchDominationAddSpecialStatsLookupTable,
  TranscendenceLookupTable,
  ResearchLookupTable,
  InvestSeasonLookupTable,
  ReentryCostLookupTable,
  ClashTierListLookupTable,
  ClashSeasonPrizeLookupTable,
  InvestSeasonRankingRewardLookupTable,
} from '../motiflib/model/cmsKeyGroup';
import * as configPubsubSyncer from '../motiflib/model/config/configPubsubSyncer';
import { InterServerConnector } from './interServerConnector';
import { onGuildSubscribe } from './guildPubsub';
import { ArenaManager } from './arenaManager';
import { WalkableTileData } from '../oceand/walkableTileData';
import * as Sentry from '@sentry/node';
import { SimplePacketMonitor } from './simplePacketMonitor';
import { FfiQueryClient } from './lobbyGameGuard';
import { RaidManager } from './raidManager';
import { RankingManager } from './rankingManager';
import { GuildRaidManager } from './guildRaidManager';
import { VillageManager } from './villageManager';
import * as globalLobbyPubsub from './globalLobbyPubsub';
import { InvestmentSeasonRankingManager } from './investmentSeasonRankingManager';
import { loadLoc } from '../motiflib/loc';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);

  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Api interfaces.
// -------------------------------------------------------------------------------------------------

@Service()
export class LobbyService {
  oceanClock: ScaledClock;
  seasonClock: ScaledClock;

  auctionRedisConnPoolMgr: AuctionRedisConnPoolMgr;

  // 레디스연결.
  townRedis: MRedisConnPool;
  userRedis: MRedisConnPool;
  collectorRedis: MRedisConnPool;
  sailRedis: MRedisConnPool;
  monitorRedis: MRedisConnPool;
  guildRedis: MRedisConnPool;
  arenaRedis: MRedisConnPool;
  orderRedis: MRedisConnPool;
  raidRedis: MRedisConnPool;
  userCacheRedis: MRedisConnPool;
  battleLogRedis: MRedisConnPool;
  nationRedis: MRedisConnPool;
  rankingRedis: MRedisConnPool;
  blindBidRedis: MRedisConnPool;
  globalMatchRedis: MRedisConnPool;
  globalBattleLogRedis: MRedisConnPool;

  // publish / subcribe
  authPubsub: Pubsub;
  worldPubsub: Pubsub;
  configPubsub: Pubsub;
  guildPubsub: Pubsub;
  globalPubsub: Pubsub;

  nationManager: NationManager;
  nationIntimacy: NationIntimacy;
  battleJudgeMongoDbConn: MongoDbConnection;
  amqpConn: amqp.Connection;
  amqpCh: amqp.Channel | null;
  weatherPropGroup: OceanProp.DynamicOceanPropGroup;
  userDbConnPoolMgr: DbConnPoolManager;
  worldDbConnPool: DBConnPool;
  questScriptManager: QuestScriptManager;
  tcpClientSessionManager: TcpClientSessionManager;

  // 리전 점령국버프 필터용( 동일한 국가가 또 점령하였다고 pub/sub 올 때)
  occupiedRegionNation: OccupiedRegionNation;
  pingInterval: NodeJS.Timeout;

  collectionLookupTable: CollectionLookupTable;
  updateLobbydPingMonitor: SimplePacketMonitor;
  oceanNpcTemplateLookup: OceanNpcTemplateLookupTable;
  mateTemplateGroupLookup: MateTemplateGroupLookupTable;
  oceanNpcTemplateArenaBotGroupLookupTable: OceanNpcTemplateArenaBotGroupLookupTable;
  oceanNpcTemplateDispatchLookupTable: OceanNpcTemplateDispatchLookupTable;
  oceanNpcDispatchLookupTable: OceanNpcDispatchLookupTable;
  nationPolicyCostLookupTable: NationPolicyCostLookupTable;
  nationPolicyEffectLookupTable: NationPolicyEffectLookupTable;
  nationDonationRankingSortedLookupTable: NationDonationRankingSortedLookupTable;
  nationCabinetRewardMailLookupTable: NationCabinetRewardMailLookupTable;
  fleetDispatchDominationAddSpecialStatsLookupTable: FleetDispatchDominationAddSpecialStatsLookupTable;
  mateTranscendenceLookupTable: TranscendenceLookupTable;
  researchLookupTable: ResearchLookupTable;
  investSeasonLookupTable: InvestSeasonLookupTable;
  reentryCostLookupTable: ReentryCostLookupTable;
  clashTierListLookupTable: ClashTierListLookupTable;
  clashSeasonPrizeLookupTable: ClashSeasonPrizeLookupTable;
  investSeasonRankingRewardLookupTable: InvestSeasonRankingRewardLookupTable;

  // 레이드 컨텐츠
  raidManager: RaidManager;
  rankingManager: RankingManager;
  investmentSeasonRankingManager: InvestmentSeasonRankingManager;

  // 상회레이드
  guildRaidManager: GuildRaidManager;

  ffiQueryClient: FfiQueryClient;

  async init() {
    await startTogglet();

    const curTimeUtc = mutil.curTimeUtc();

    await loadLoc();

    this.oceanClock = new ScaledClock(CMSConst.get('OceanTimeScale'));
    this.seasonClock = new ScaledClock(CMSConst.get('SeasonTimeScale'));

    // Init redis pool.
    this.townRedis = Container.of('town-redis').get(MRedisConnPool);
    await this.townRedis.init('town-redis', mconf.townRedis);

    this.userRedis = Container.of('user-redis').get(MRedisConnPool);
    await this.userRedis.init('user-redis', mconf.userRedis);

    this.collectorRedis = Container.of('collector-redis').get(MRedisConnPool);
    await this.collectorRedis.init('collector-redis', mconf.collectorRedis);

    this.sailRedis = Container.of('sail-redis').get(MRedisConnPool);
    await this.sailRedis.init('sail-redis', mconf.sailRedis);

    this.monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
    await this.monitorRedis.init('monitor-redis', mconf.monitorRedis);

    this.auctionRedisConnPoolMgr = Container.of('auction-redis').get(AuctionRedisConnPoolMgr);
    await this.auctionRedisConnPoolMgr.init('auction-redis', mconf.auctionRedis);

    this.guildRedis = Container.of('guild-redis').get(MRedisConnPool);
    await this.guildRedis.init('guild-redis', mconf.guildRedis);

    this.arenaRedis = Container.of('arena-redis').get(MRedisConnPool);
    await this.arenaRedis.init('arena-redis', mconf.arenaRedis);

    this.orderRedis = Container.of('order-redis').get(MRedisConnPool);
    await this.orderRedis.init('order-redis', mconf.orderRedis);

    this.raidRedis = Container.of('raid-redis').get(MRedisConnPool);
    await this.raidRedis.init('raid-redis', mconf.raidRedis);

    this.blindBidRedis = Container.of('blind-bid-redis').get(MRedisConnPool);
    await this.blindBidRedis.init('blind-bid-redis', mconf.blindBidRedis);

    // Init redis pubsub.
    this.authPubsub = Container.of('pubsub-auth').get(Pubsub);
    this.authPubsub.init(mconf.authPubsubRedis);
    this.worldPubsub = Container.of('pubsub-world').get(Pubsub);
    this.worldPubsub.init(mconf.getWorldConfig().worldPubsubRedis);
    this.configPubsub = Container.of('pubsub-config').get(Pubsub);
    this.configPubsub.init(mconf.configPubsubRedis);
    this.guildPubsub = Container.of('pubsub-guild').get(Pubsub);
    this.guildPubsub.init(mconf.getWorldConfig().guildPubsubRedis);
    this.globalPubsub = Container.of('pubsub-global').get(Pubsub);
    this.globalPubsub.init(mconf.globalPubsubRedis);

    lobbyPubsub.init();
    globalLobbyPubsub.init();

    this.worldPubsub.subscribe('nation_intimacy_updated', async () => {
      mlog.info('reloading nation intimacy');
      await this.nationIntimacy.reloadIntimacy();
    });

    this.worldPubsub.subscribe('population_updated', async () => {
      mlog.info('reloading nation population');
      await this.nationManager.reloadPopulation();
    });

    this.worldPubsub.subscribe('national_power_updated', async (msgStr) => {
      mlog.info('reloading national power');
      this.nationManager.onNationalPowerUpdated(msgStr);
    });

    // 해당 권역에 위치한 항해 유저들에게 리전 점령국 변경 통보(점령국 버프/디버프 업데이트용).
    this.worldPubsub.subscribe('region_nation_changed', async (msgStr) => {
      const msg = JSON.parse(msgStr);

      mlog.info('recv region_nation_changed', {
        msg,
      });

      // 이미 동일한 국가가 해당 리전을 점유/점령했을 경우 .
      if (
        this.occupiedRegionNation[msg.regionCmsId].nationCmsId ===
          msg.regionOccupation.nationCmsId &&
        this.occupiedRegionNation[msg.regionCmsId].bComplete === msg.regionOccupation.bComplete
      ) {
        return;
      }
      mlog.info('reloading region_nation_changed', {
        regionCmsId: msg.regionCmsId,
        oldNationCmsId: this.occupiedRegionNation[msg.regionCmsId].nationCmsId,
        newNationCmsId: msg.regionOccupation.nationCmsId,
        bComplete: msg.regionOccupation.bComplete,
      });
      this.occupiedRegionNation[msg.regionCmsId] = msg.regionOccupation;

      const userManager = Container.get(UserManager);
      userManager.notifyWorldEventToAllUser(WorldEventNotificationType.ChangedRegionNation, msg);
    });

    this.guildPubsub.subscribe('guild_updated', onGuildSubscribe);

    // Init/load quest scripts.
    this.questScriptManager = new QuestScriptManager();
    this.questScriptManager.load();

    const weatherRoot = path.join(cmsRoot, 'weather');
    this.weatherPropGroup = await OceanProp.BuildDynamicOceanPropGroup(weatherRoot);

    // Init mysql connection pool.
    this.userDbConnPoolMgr = Container.of('user').get(DbConnPoolManager);
    await this.userDbConnPoolMgr.init(mconf.mysqlUserDb);

    this.worldDbConnPool = Container.of('world').get(DBConnPool);
    await this.worldDbConnPool.init(mconf.mysqlWorldDb);

    // Init user cache redis pool.
    this.userCacheRedis = Container.of('user-cache-redis').get(MRedisConnPool);
    await this.userCacheRedis.init('user-cache-redis', mconf.userCacheRedis);

    // Init nation redis pool.
    this.nationRedis = Container.of('nation-redis').get(MRedisConnPool);
    await this.nationRedis.init('nation-redis', mconf.nationRedis);

    // Init nation redis pool.
    this.rankingRedis = Container.of('ranking-redis').get(MRedisConnPool);
    await this.rankingRedis.init('ranking-redis', mconf.rankingRedis);

    // Init battle log redis pool.
    this.battleLogRedis = Container.of('battle-log-redis').get(MRedisConnPool);
    await this.battleLogRedis.init('battle-log-redis', mconf.battleLogRedis);

    this.globalBattleLogRedis = Container.of('global-battle-log-redis').get(MRedisConnPool);
    await this.globalBattleLogRedis.init('global-battle-log-redis', mconf.globalBattleLogRedis);

    // Init battle log mongoDb pool.
    this.battleJudgeMongoDbConn = Container.of('battle-log-mongodb').get(MongoDbConnection);
    this.battleJudgeMongoDbConn.init(mconf.battleLogMongoDb);

    this.globalMatchRedis = Container.of('global-match-redis').get(MRedisConnPool);
    await this.globalMatchRedis.init('global-match-redis', mconf.globalMatchRedis);

    // Init town manager
    const townManager = Container.get(TownManager);
    await townManager.init();
    this.occupiedRegionNation = townManager.getAllRegionNationOccupation();

    // Load nation informations.
    const { nationManager, nationIntimacy } = await LoadNation(this);
    this.nationManager = nationManager;
    this.nationIntimacy = nationIntimacy;

    // 선출관련 초기화, 선거, 내각 정보 로드
    await nationManager.initElection();

    // Initialize RabbitMQ consume
    await this.connectToRabbitMq();

    // Init InvestmentSeasonRankingManager
    this.investmentSeasonRankingManager = new InvestmentSeasonRankingManager();
    await this.investmentSeasonRankingManager.init();

    ///--------------------------------------------------------------------------------------------
    /// 각 컨텐츠 초기 설정 실패는 직접 catch할것.
    /// 서버 오픈 시 실패된 기능만 이용 못하게 하되 이 외에 기능은 정상적인 플레이 가능하게 처리.
    /// 오픈 된 이후,  해당 설정실패 원인을 찾는 것이 주목적.
    ///--------------------------------------------------------------------------------------------
    // Init arena manager
    const arenaManager = Container.get(ArenaManager);
    await arenaManager.init();

    // Load world line mails
    const lineMailManager = Container.get(LineMailManager);
    lineMailManager.init();

    const villageManager = Container.get(VillageManager);
    await villageManager.init(curTimeUtc);

    // Load Raid Manager
    this.raidManager = new RaidManager();
    await this.raidManager.init(curTimeUtc);

    // Load ranking Manager
    this.rankingManager = new RankingManager();

    // Load guild raid manager
    this.guildRaidManager = new GuildRaidManager();
    await this.guildRaidManager.loadGuildRaid();

    ///----------------------------------------------
    // Init TcpClient
    this.tcpClientSessionManager = new TcpClientSessionManager();
    InterServerConnector.tryConnect();

    // 동적 서버 인스턴스 증가 대응(scale out)
    configPubsubSyncer.subscribeForRegisterInstance(
      this.configPubsub,
      () => {
        // 새로운 인스턴스 등록 이벤트 발생시 처리할 작업을 등록
        InterServerConnector.tryConnect();
      },
      isStopping,
      stop
    );

    // cms common
    const distanceFromTileToTownPath = path.resolve(
      path.join(__dirname, '..', '..', '..', '..', 'cms', 'common', 'distancesFromTileToTown.dtt')
    );
    const data = await DistanceFromTileToTownData.fromFile(distanceFromTileToTownPath);
    Container.set(DistanceFromTileToTownData, data);

    // Assets
    const walkableTileData = await WalkableTileData.fromFile(
      path.resolve(__dirname, '../../../assets/walkabledata.tiles')
    );
    Container.set(WalkableTileData, walkableTileData);

    const walkableTileData_Low = await WalkableTileData.fromFile(
      path.resolve(__dirname, '../../../assets/walkabledata_Low.tiles')
    );

    Container.set('WalkableTileData_Low', walkableTileData_Low);

    this.collectionLookupTable = new CollectionLookupTable(cms);
    this.updateLobbydPingMonitor = new SimplePacketMonitor('updateLobbydPingMonitor', 60 * 1000);
    this.updateLobbydPingMonitor.startMonitor();
    this.oceanNpcTemplateLookup = new OceanNpcTemplateLookupTable(cms);
    this.mateTemplateGroupLookup = new MateTemplateGroupLookupTable(cms);
    this.oceanNpcTemplateArenaBotGroupLookupTable = new OceanNpcTemplateArenaBotGroupLookupTable(
      cms
    );
    this.oceanNpcTemplateDispatchLookupTable = new OceanNpcTemplateDispatchLookupTable(cms);
    this.oceanNpcDispatchLookupTable = new OceanNpcDispatchLookupTable(cms);
    this.nationPolicyCostLookupTable = new NationPolicyCostLookupTable(cms);
    this.nationPolicyEffectLookupTable = new NationPolicyEffectLookupTable(cms);
    this.nationDonationRankingSortedLookupTable = new NationDonationRankingSortedLookupTable(cms);
    this.nationCabinetRewardMailLookupTable = new NationCabinetRewardMailLookupTable(cms);
    this.fleetDispatchDominationAddSpecialStatsLookupTable =
      new FleetDispatchDominationAddSpecialStatsLookupTable(cms);
    this.mateTranscendenceLookupTable = new TranscendenceLookupTable(cms);
    this.researchLookupTable = new ResearchLookupTable(cms);
    this.investSeasonLookupTable = new InvestSeasonLookupTable(cms);
    this.reentryCostLookupTable = new ReentryCostLookupTable(cms);
    this.clashTierListLookupTable = new ClashTierListLookupTable(cms);
    this.clashSeasonPrizeLookupTable = new ClashSeasonPrizeLookupTable(cms);
    this.investSeasonRankingRewardLookupTable = new InvestSeasonRankingRewardLookupTable(cms);

    this.ffiQueryClient = new FfiQueryClient();
    this.ffiQueryClient.init();
  }

  async connectToRabbitMq(reconnect: boolean = false) {
    try {
      this.amqpConn = await amqp.connect(mconf.amqp);
    } catch (err) {
      mlog.error('Failed to connect to RabbitMQ server.', {
        message: err.message,
        stack: err.stack,
      });
      if (reconnect) {
        await mutil.sleep(1000);
        this.connectToRabbitMq(reconnect);
        return;
      } else {
        throw err;
      }
    }

    mlog.info('connected to RabbitMQ', {
      config: mconf.amqp,
    });

    this.amqpConn.on('error', (err) => {
      if (err) {
        mlog.error('The rabbitmq error occurs.', {
          message: err.message,
          stack: err.stack,
        });
      } else {
        mlog.error('An error is undefined at [error] event of amqpConn.');
      }
    });

    this.amqpConn.on('close', async (err) => {
      if (err) {
        mlog.error('The RabbitMQ has been disconnected by server.', {
          message: err.message,
          stack: err.stack,
        });
      } else {
        // 서버로부터 끊긴 것이 아니라, 직접 종료한 경우에는 err 값이 없다.
        mlog.error('An error is undefined at [close] event of amqpConn.');
      }

      if (!isStopping()) {
        await mutil.sleep(1000);
        this.connectToRabbitMq(true);
      }
    });
    this.amqpCh = await this.amqpConn.createChannel();

    await lobbyRabbitMQ.init();
  }

  async destroy() {
    await this.authPubsub.quit();
    await this.worldPubsub.quit();
    await this.configPubsub.quit();
    await this.guildPubsub.quit();
    await this.globalPubsub.quit();

    await this.userRedis.destroy();
    await this.townRedis.destroy();
    await this.guildRedis.destroy();
    await this.auctionRedisConnPoolMgr.destroy();
    await this.arenaRedis.destroy();
    await this.orderRedis.destroy();
    await this.raidRedis.destroy();

    await this.userDbConnPoolMgr.destroy();
    await this.worldDbConnPool.destroy();
    await this.amqpConn.close();
    await this.battleLogRedis.destroy();
    await this.userCacheRedis.destroy();
    await this.battleJudgeMongoDbConn.destroy();
    await this.globalBattleLogRedis.destroy();

    await this.nationRedis.destroy();
    await this.rankingRedis.destroy();
    await this.blindBidRedis.destroy();
    await this.globalMatchRedis.destroy();

    this.tcpClientSessionManager.dispose();

    this.updateLobbydPingMonitor.stopMonitor();

    stopTogglet();
  }

  startPing() {
    if (stopping) {
      return;
    }

    mlog.info('startPing', { pingIntervalTime });

    this.pingInterval = setInterval(() => {
      const userManager = Container.get(UserManager);
      const userCount = userManager.getUserCount();
      this._updateLobbydPing(
        mconf.appId,
        mutil.curTimeUtc(),
        mconf.worldId,
        userCount.userCount,
        userCount.botCount
      );
    }, pingIntervalTime);
  }

  stopPing() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }
  }

  // ----------------------------------------------------------------------------
  // realmd에서 bStop 값 으로 true를 반환하는 경우 해당 lobbyd가 online상태가 아니라는 의미
  // online 상태가 아닌경우의 처리 방침은 lobbyd 를 재시작 시킨다.
  private async _updateLobbydPing(
    appId: string,
    curTimeUtc: number,
    worldId: string,
    userCount: number,
    botCount: number
  ): Promise<void> {
    try {
      if (stopping) {
        return;
      }

      this.updateLobbydPingMonitor.addSendTriesCount();

      const resp = await mhttp.zonelbd.updateLobbydPing(
        appId,
        curTimeUtc,
        worldId,
        userCount,
        botCount
      );

      this.updateLobbydPingMonitor.addSendCompletedCount();

      if (resp.bStop) {
        Sentry.captureMessage('updateLobbydPing signaled to stop. begin stopping server');

        mlog.warn('updateLobbydPing signaled to stop. begin stopping server');
        stop();
      } else {
        // mlog.info('updateLobbydPing succeeded.');
      }
    } catch (err) {
      this.updateLobbydPingMonitor.addSendFailedCount();
      mlog.warn(err.message);
    }
  }
}

// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------

const cmsRoot = path.resolve(path.join(__dirname, '..', '..', '..', '..', 'cms', 'server'));

// Socket server.
let socketServer: net.Server = null;

// Rest API server.
const lobbyApiApp = express();

lobbyApiApp.disable('x-powered-by');
lobbyApiApp.disable('etag');
lobbyApiApp.disable('content-type');

const lobbyApiServer = stoppable(http.createServer(lobbyApiApp), 60000);
lobbyApiServer.keepAliveTimeout = 0;

tcp.logger.setMoudle(mlog);

// Stopping flag.
let stopping = false;

let pingIntervalTime: number = 2000;

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

function lobbyApiReqLog(tokens, req, res) {
  {
    let packetType = 0;
    if (req.url === '/relay/broadcast') {
      packetType = req.body.packetType;
    }

    const netPerfmon = Container.get(PacketPerfmon);
    const unitPacketStat: UnitPacketRecvStat = {
      packetId: packetType,
      packetIdStr: req.url,
      size: parseInt(tokens['res'](req, res, 'content-length'), 10),
      duration: parseFloat(tokens['response-time'](req, res)),
    };
    netPerfmon.addApiRecvStat(PacketRecvType.SERVER_API_RECEIVED, unitPacketStat);
  }

  if (
    req.url === '/health' ||
    req.url === '/common/friendNotification' ||
    req.url === '/town/userMove' ||
    req.url === '/town/userLeave' ||
    req.url === '/town/townUserSyncDataUpdate' ||
    req.url === '/town/userUpdate' ||
    req.url === '/town/userUpdate' ||
    req.url === '/town/nearbyUserInfos'
  ) {
    return;
  }
  if (req.url === '/relay/broadcast' && req.body.packetType === proto.Ocean.NET_USER_MOVE_SC) {
    return;
  }

  mlog.info('api-req', {
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  });

  return null;
}

function createSocketServer() {
  socketServer = net.createServer((sock: UserSocket) => {
    // Assign unique ID to the socket.
    // Unique within the same server.
    sock.id = nanoid(8);
    sock.setNoDelay();

    // 소켓 연결 이후 로그인 전까지 유지되는 타임아웃 설정 (기본값: 5초)
    const loginTimeout = mconf.loginTimeout || 5000;
    sock.setTimeout(loginTimeout);

    mlog.info('new client connection', {
      address: `${sock.remoteAddress}:${sock.remotePort}`,
      id: sock.id,
    });

    // Add to the user session manager.
    const userManager = Container.get(UserManager);
    sock.user = userManager.addUser(sock);
    if (!sock.user) {
      mlog.error('Failed to add newly connected user!', {
        connId: sock.id,
      });
      sock.destroy();
      return;
    }

    sock.on('timeout', () => {
      mlog.warn('[SOCKET] timeout', {
        id: sock.id,
        userId: sock.user.userId,
      });
      sock.destroy();
    });

    sock.on('data', (data) => {
      sock.user.onSocketRecv(data);
    });

    sock.once('drain', () => {
      mlog.warn('Socket drained.', {
        connId: sock.id,
        userId: sock.user.userId,
        writableLength: sock.writableLength,
        bytesRead: sock.bytesRead,
        bytesWritten: sock.bytesWritten,
      });
    });

    sock.on('end', () => {
      mlog.info('[SOCKET] connection reset by peer', {
        id: sock.id,
        userId: sock.user.userId,
      });
      sock.destroy();
    });

    sock.on('close', () => {
      mlog.debug('[SOCKET] closed', {
        id: sock.id,
        userId: sock.user.userId,
      });
      userManager.onSocketClose(sock.id);
    });

    sock.on('error', (e) => {
      mlog.warn('[SOCKET] client error', {
        id: sock.id,
        userId: sock.user.userId,
        errMsg: e.message,
      });
      sock.destroy(e);
    });
  });
}

async function closeApiServer() {
  return new Promise((resolve, reject) => {
    lobbyApiServer.stop((err) => {
      if (err) return reject(err);
      mlog.info('API server closed.');
      resolve(null);
    });
  });
}

async function closeSocketServer() {
  return new Promise((resolve, reject) => {
    socketServer.close((err) => {
      socketServer.unref();

      if (err) return reject(err);
      mlog.info('Socket server closed.');
      resolve(null);
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');

    await closeApiServer();

    const app = Container.get(LobbyService);
    app.stopPing();

    const lobbyPerfmonManager = Container.get(LobbyPerfmonManager);
    lobbyPerfmonManager.stopPerfmonTick();

    const userManager = Container.get(UserManager);
    userManager.disconnectAll(DisconnectReason.StopServer);

    // Listen 을 그만하고, 더이상 연결된 커넥션들이 없을때 리턴.
    await closeSocketServer();

    // 모든 유저들이 로그아웃 처리 될 때까지 기다린다.
    await userManager.waitForTermination();

    // 모든 유저의 로그아웃 처리 후  tick을 종료한다.
    userManager.stopUserTick();

    // Close mysql connection pool.
    await app.destroy();

    mlog.info('server stopped');
  } catch (error) {
    mlog.error('graceful shutdown failed', { error: error.message });
    process.exit(1);
  }
}

async function registerLobbyd(
  id: string,
  url: string,
  curTimeUtc: number,
  worldId: string
): Promise<void> {
  try {
    const resp = await mhttp.realmd.registerLobbyd(id, url, curTimeUtc, worldId);

    pingIntervalTime = resp.pingInterval;

    mlog.info('registerLobbyd succeeded', { pingIntervalTime, curTimeUtc });
  } catch (err) {
    mlog.warn(err.message);

    if (stopping) {
      return;
    }

    mlog.warn('Retry register lobbyd.');

    await mutil.sleep(1000);
    await registerLobbyd(id, url, mutil.curTimeUtc(), worldId);
  }
}

async function unregisterLobbyd(id: string): Promise<void> {
  try {
    await mhttp.realmd.unregisterLobbyd(id);
  } catch (err) {
    mlog.warn(err.message);

    if (stopping) {
      return;
    }

    mlog.warn('Retry unregister lobbyd.');

    await mutil.sleep(1000);
    await unregisterLobbyd(id);
  }
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export async function start() {
  try {
    // Init config.

    await mhttp.configd.registerInstance(
      process.env.WORLD_ID ? process.env.WORLD_ID : mconf.instance.worldId,
      mconf.appInstanceId,
      mconf.hostname
    );

    mutil.initSentry();

    // Init http clients.
    mhttp.init();

    // Add glog transport for line games.
    // glogInstance.add(
    //   new winston.transports.Http({
    //     host: mconf.http.lgd.url,
    //     path: '/api/api_mongo/multi/saveCommonLog',
    //     headers: {
    //       nfToken
    //       gameCd: mconf.LineGameCode,
    //     },
    //   })
    // );

    // Init cms.
    loadCms();
    mlog.info(`World ID: ${mconf.worldId}`);

    const app = Container.get(LobbyService);
    await app.init();

    // Init api server.
    {
      const bindAddress = mconf.apiService.bindAddress;
      const port = mconf.apiService.port;

      lobbyApiApp.use(morgan(lobbyApiReqLog));
      lobbyApiApp.use(bodyParser.json({ limit: '50mb' }));
      mutil.registerHealthCheck(lobbyApiApp);
      mutil.registerGarbageCollector(lobbyApiApp);
      await dirAsApi.register(lobbyApiApp, path.join(__dirname, 'api'));

      // Init user tick.
      const userManager = Container.get(UserManager);
      userManager.userTick(0);

      const rankingManager = Container.get(RankingManager);
      rankingManager.tick();

      const lobbyPerfmonManager = Container.get(LobbyPerfmonManager);
      lobbyPerfmonManager.startPerfmonTick();

      lobbyApiApp.use(expressError.middleware);
      lobbyApiServer.listen(port, bindAddress, () => {
        mlog.info('API start listening ... ', { bindAddress, port });
      });
    }

    // Init socket server.
    {
      const bindAddress = mconf.socketServer.bindAddress;
      const port = mconf.socketServer.port;
      mlog.verbose('create socket server ...');
      createSocketServer();
      mlog.info('SOCKET start listening ...', { bindAddress, port });
      socketServer.listen(port, bindAddress);
    }

    // config final sync
    const beforeVer = mconf.layoutVersion;
    await mhttp.configd
      .sync(beforeVer, isStopping, stop)
      .then(() => {
        if (beforeVer < mconf.layoutVersion) {
          mlog.info('layoutVersion increased during preparing... tryConnect again', {
            beforeVer,
            afterVer: mconf.layoutVersion,
          });
          InterServerConnector.tryConnect();
        }
      })
      .catch((err) => {
        mlog.error('sync failed..', { msg: err.message });
      });

    // register to realmd.
    {
      await unregisterLobbyd(mconf.appId);
      await registerLobbyd(mconf.appId, mconf.apiService.url, mutil.curTimeUtc(), mconf.worldId);
    }

    app.startPing();
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
}

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await unregisterLobbyd(mconf.appId);

  await stopServer();

  process.exit(0);
}
