"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stop = exports.start = exports.ZonelbService = void 0;
const body_parser_1 = __importDefault(require("body-parser"));
const express_1 = __importDefault(require("express"));
require("express-async-errors");
const http_1 = __importDefault(require("http"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const typedi_1 = __importStar(require("typedi"));
const dirAsApi = __importStar(require("../motiflib/directoryAsApi"));
const expressError = __importStar(require("../motiflib/expressError"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const slackNotifier_1 = require("../motiflib/slackNotifier");
const connPool_1 = require("../redislib/connPool");
const merror_1 = require("../motiflib/merror");
const cmsEx = __importStar(require("../cms/ex"));
const stoppable_1 = __importDefault(require("stoppable"));
const pingChecker_1 = __importDefault(require("./pingChecker"));
const Sentry = __importStar(require("@sentry/node"));
// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('uncaught Exception', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('unhandled Rejection', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Api interfaces.
// -------------------------------------------------------------------------------------------------
let ZonelbService = class ZonelbService {
    constructor() {
        this.pingChecker = new pingChecker_1.default();
    }
    async init() {
        this.monitorRedis = typedi_1.default.of('monitor-redis').get(connPool_1.MRedisConnPool);
        await this.monitorRedis.init('monitor-redis', mconf_1.default.monitorRedis);
        this.townLbRedis = typedi_1.default.of('town-lb-redis').get(connPool_1.MRedisConnPool);
        await this.townLbRedis.init('town-lb-redis', mconf_1.default.townLbRedis);
        this.oceanLbRedis = typedi_1.default.of('ocean-lb-redis').get(connPool_1.MRedisConnPool);
        await this.oceanLbRedis.init('ocean-lb-redis', mconf_1.default.oceanLbRedis);
    }
    async destory() {
        this.pingChecker.stopTick();
        await this.townLbRedis.destroy();
        await this.oceanLbRedis.destroy();
        await this.monitorRedis.destroy();
    }
    get(zoneType) {
        if (cmsEx.ZoneType.TOWN == zoneType)
            return this.townLbRedis;
        else if (cmsEx.ZoneType.OCEAN == zoneType)
            return this.oceanLbRedis;
        else {
            throw new merror_1.MError('invalid-redis-zone-type', merror_1.MErrorCode.INTERNAL_ERROR);
        }
    }
};
ZonelbService = __decorate([
    (0, typedi_1.Service)(),
    __metadata("design:paramtypes", [])
], ZonelbService);
exports.ZonelbService = ZonelbService;
// -------------------------------------------------------------------------------------------------
// Module variables.
// -------------------------------------------------------------------------------------------------
const app = (0, express_1.default)();
app.disable('x-powered-by');
app.disable('etag');
app.disable('content-type');
const server = (0, stoppable_1.default)(http_1.default.createServer(app));
server.keepAliveTimeout = 0;
let stopping = false;
// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------
morgan_1.default.token('mcode', (_req, res) => res.mcode || 0);
function zonelbReqLog(tokens, req, res) {
    if (req.url === '/updateServerdPing' ||
        req.url === '/updateLobbydPing' ||
        req.url === '/health') {
        return;
    }
    mlog_1.default.info('zonelbd-req', {
        url: tokens['url'](req, res),
        status: tokens['status'](req, res),
        'response-time': tokens['response-time'](req, res),
        mcode: tokens['mcode'](req, res),
    });
    return null;
}
async function closeServer() {
    return new Promise((resolve, reject) => {
        server.stop((err) => {
            if (err)
                return reject(err);
            resolve();
        });
    });
}
async function stopServer() {
    try {
        mlog_1.default.info('stopping server ...');
        await closeServer();
        mlog_1.default.info('unregister all serverd');
        const zonelbServie = typedi_1.default.get(ZonelbService);
        await zonelbServie.destory();
        mlog_1.default.info('server stopped');
        process.exitCode = 0;
    }
    catch (error) {
        mlog_1.default.error('graceful shutdown failed', { error: error.message });
        process.exit(1);
    }
}
// -------------------------------------------------------------------------------------------------
// Public functions.
// -------------------------------------------------------------------------------------------------
async function start() {
    try {
        await mhttp_1.default.configd.registerInstance(process.env.WORLD_ID ? process.env.WORLD_ID : mconf_1.default.instance.worldId, mconf_1.default.appInstanceId, mconf_1.default.hostname);
        mutil.initSentry();
        // Init http clients.
        mhttp_1.default.init();
        const zonelbServie = typedi_1.default.get(ZonelbService);
        await zonelbServie.init();
        const bindAddress = mconf_1.default.apiService.bindAddress;
        const port = mconf_1.default.apiService.port;
        app.use((0, morgan_1.default)(zonelbReqLog));
        app.use(body_parser_1.default.json());
        mutil.registerHealthCheck(app);
        mutil.registerGarbageCollector(app);
        await dirAsApi.register(app, path_1.default.join(__dirname, 'api'));
        app.use(expressError.middleware);
        server.listen(port, bindAddress, () => {
            mlog_1.default.info('start listening ...', { bindAddress, port });
        });
        // config final sync
        const beforeVer = mconf_1.default.layoutVersion;
        await mhttp_1.default.configd.sync(beforeVer, isStopping, stop).then(() => {
            if (beforeVer < mconf_1.default.layoutVersion) {
                // do something
            }
        });
        setTimeout(() => {
            if (stopping) {
                return;
            }
            // zonelbd시작후 일정시간동안 타임아웃 처리를 유예하여
            // 이미 동작중인 존서버들의 ping처리가 가능하도록 한다.
            zonelbServie.pingChecker.startTick();
        }, mconf_1.default.ping.timeout);
    }
    catch (error) {
        mlog_1.default.error('failed to start', { error: error.message, extra: error.extra });
        mlog_1.default.error(error.stack);
        const slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
        await slackNotifier.notify({ username: process.name, text: error.message });
        process.exit(1);
    }
}
exports.start = start;
function isStopping() {
    return stopping;
}
async function stop() {
    if (stopping) {
        return;
    }
    stopping = true;
    await stopServer();
}
exports.stop = stop;
//# sourceMappingURL=server.js.map