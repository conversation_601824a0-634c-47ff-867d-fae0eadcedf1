#!/bin/bash

CWD="$(dirname "$0")"

if [ ! -f $CWD/_config.sh ]; then
	echo "Please create a custom '_config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/_query.sh ]; then
	echo "Please create a custom '_query.sh' file at '$CWD' directory."
	exit 1
fi


SECONDS=0

source $CWD/_config.sh
source $CWD/_query.sh

main() 
{
  echo "===== UPDATE a_world_users.name"
  q="
    UPDATE a_world_users
      JOIN integration_duplicate_name AS TMP
      ON a_world_users.userId = TMP.userId
    SET a_world_users.name=TMP.name;
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"

}



main "$@"; 

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
exit
