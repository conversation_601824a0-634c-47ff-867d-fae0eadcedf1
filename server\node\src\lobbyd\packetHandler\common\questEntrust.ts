// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Promise as promise } from 'bluebird';
import _ from 'lodash';
import { Container } from 'typedi';

import cms from '../../../cms';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { QuestBlockState, QuestState } from '../../quest';
import { Quest<PERSON>xe<PERSON>, QuestEncount } from '../../questExec';
import { LobbyService } from '../../server';
import { Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { RewardInfo } from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import { QuestUtil } from '../../questUtil';
import { ClientPacketHandler } from '../index';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const apiName = '/questEntrust';

interface RequestBody {
  cmsId: number;
  cmdIdx: number;
}

interface ResponseBody {
  execCount: number;
  sync: Sync;
  rewardInfo?: RewardInfo;
  bRoyalTitle?: boolean;
  encount?: QuestEncount;
  fishSize?: {
    fishId: number;
    oldMaxSize: number;
    newSize: number;
  };
}

// ----------------------------------------------------------------------------
export class Cph_Common_QuestEntrust implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const userId = user.userId;

    const body: RequestBody = packet.bodyObj;

    const { cmsId, cmdIdx } = body;

    const { questScriptManager } = Container.get(LobbyService);

    const questCms = cms.Quest[cmsId];
    if (!questCms) {
      throw new MError('invalid-quest-cms-id', MErrorCode.INVALID_REQUEST);
    }

    const userQuestMgr = user.questManager;
    const questCtx = userQuestMgr.getCurContext(questCms.category);
    if (!questCtx || questCtx.cmsId !== cmsId) {
      mlog.warn(`[${apiName}] Not currently executing quest.`, { userId, cmsId, cmdIdx });
      throw new MError('not-currently-executing-quest', MErrorCode.QUEST_NOT_IN_PROGRESS, {
        questCtx,
        cmsId,
      });
    }

    if (questCtx.state === QuestState.DEACTIVATED) {
      throw new MError('deactivated-quest', MErrorCode.DEACTIVATED_QUEST);
    }

    // Check the context's blockState.
    if (questCtx.blockState !== QuestBlockState.CLIENT_EXEC) {
      mlog.warn(`[${apiName}] Invalid quest block state.`, {
        userId,
        cmdIdx,
        blockState: questCtx.blockState,
      });
      throw new MError('invalid-block-state', MErrorCode.QUEST_INVALID_BLOCK_STATE);
    }

    // Get current script block.
    const questNodeCmsId = questCms.nodes[questCtx.nodeIdx];
    const questNodeCms = cms.QuestNode[questNodeCmsId];
    const scriptName = questNodeCms.script;
    const questBlock = questScriptManager.getBlock(scriptName, questCtx.blockId);

    // Check 'cmdIdx'.
    const cmdSeq = questBlock.seq;
    if (cmdIdx < 0 || cmdIdx >= cmdSeq.length) {
      mlog.warn(`[${apiName}] Invalid quest block cmd idx.`, {
        userId,
        questCmsId: questCtx.cmsId,
        cmdIdx,
        blockId: questCtx.blockId,
      });
      throw new MError('invalid-quest-cmd-idx', MErrorCode.QUEST_INVALID_CMD_IDX, {
        cmdSeq,
        cmdIdx,
      });
    }

    // Make sure it's a server command.
    const cmdObj = cmdSeq[cmdIdx];
    if (!QuestUtil.isServerCmd(cmdObj.cmd)) {
      mlog.warn(`[${apiName}] Not a server command.`, {
        userId,
        cmdIdx,
        cmdName: cmdObj.cmd,
      });
      throw new MError('cannot-entrust-client-cmd', MErrorCode.QUEST_CANNOT_ENTRUST_CLIENT_CMD);
    }

    // Gather server command objects until we run into a client command
    // or the end of the block.
    const cmdList: any[] = [];
    for (let i = cmdIdx; i < cmdSeq.length; ++i) {
      const cmdObj = cmdSeq[i];

      // Push server commands and labels.
      if (!cmdObj.label) {
        if (!QuestUtil.isServerCmd(cmdObj.cmd)) {
          break;
        }
      }

      cmdList.push(cmdObj);
    }

    // Exec all commands.
    const resp: ResponseBody = {
      execCount: 0,
      sync: {},
    };

    return promise
      .each(cmdList, (cmdObj) => {
        // mlog.verbose('[TEMP] entrust cmd:', { userId: user.userId, cmd: cmdObj.cmd });
        return QuestExec.runCmd(cmdObj, user, questCtx).then((res) => {
          if (res) {
            if (res.sync) {
              _.merge(resp.sync, res.sync);
              // 퀘스트 completed 시 sync.add.questData에 context가 포함되어 있다면, 데이터가 꼬여 에러가 발생할 수 있다.
              // 이미 완료되어 없어질 데이터에 기록을 할 필요가 없음으로 contexts를 삭제한다.
              if (resp.sync.add?.questData?.completed) {
                //const questData = _.omit(resp.sync.add.questData, ['contexts']);
                //resp.sync.add.questData = questData;
              }
            }
            if (res.rewardInfo) {
              resp.rewardInfo = res.rewardInfo;
            }
            if (res.bRoyalTitle) {
              resp.bRoyalTitle = res.bRoyalTitle;
            }
            if (res.encount) {
              resp.encount = res.encount;
            }

            if (res.fishSize) {
              resp.fishSize = res.fishSize;
            }
          }

          ++resp.execCount;
        });
      })
      .then(() => {
        return user.sendJsonPacket(packet.seqNum, packet.type, resp);
      });
  }
}
