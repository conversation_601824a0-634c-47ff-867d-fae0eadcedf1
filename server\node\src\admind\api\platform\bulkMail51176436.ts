// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import Container from 'typedi';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import { AdminService } from '../../server';
import { CommonResponseBody } from '../../adminCommon';

// https://developer.line.games/pages/viewpage.action?pageId=19600116

interface MailData {
  nid: string;
  title: string;
  contents: string;
  itemCmd: string;
  itemCnt: number;
  channel: string; // optional
  gameInfo1: string; // optional
  gameInfo2: string; // optional
  expireYmdt: string;  // 만료시간을 무제한으로 할수 있나?

  // gameUserId: number;
  // itemCd: string;
  // itemCnt: number;
  // expireYmdt: string;
}

interface RequestBody {
  gameServerId: string;
  configId: number;
  mailDataList: MailData[];
}

interface ResponseBody extends CommonResponseBody {}

export = (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /platform/bulkMail', { body: req.body });

  const { gameServerId: worldId, mailDataList, configId }: RequestBody = req.body;

  if (!mailDataList || !_.isArray(mailDataList) || mailDataList.length === 0) {
    return res.status(400).json({
      isSuccess: false,
      msg: 'Invalid mailDataList',
      errorCd: 'INVALID_PARAMETER',
    });
  }

  const { userRedises } = Container.get(AdminService);
  const userRedis = userRedises[worldId];
  if (!userRedis) {
    return res.status(400).json({
      isSuccess: false,
      msg: 'Invalid gameServerId',
      errorCd: 'INVALID_PARAMETER',
    });
  }

  // 요청 큐잉
  return userRedis['addBulkMail'](configId, JSON.stringify(mailDataList)).then(() => {
    const resp: ResponseBody = {
      isSuccess: true,
      msg: 'request success',
    };

    mlog.info('[TX] /platform/bulkMail', { body: resp });
    return res.json(resp);
  });
};
