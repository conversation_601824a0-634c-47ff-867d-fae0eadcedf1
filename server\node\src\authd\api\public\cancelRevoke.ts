// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';

interface RequestBody {
  platform: number;
  gnidSessionToken: string;
  revision: string;
}

type ResponseErrCd =
  | 'UWO_INTERNAL_ERROR'
  | 'SYSTEM_ERROR'
  | 'SYSTEM_MAINTENANCE'
  | 'INVALID_PARAMETER'
  | 'INVALID_PARAMETER'
  | 'NOT_LEAVE_USER'
  | 'NOT_EXIST_DATA'
  | 'OVER_DAY_RECOVERY_LEAVE';

interface ResponseBody {
  isSuccess: boolean;
  errorCd?: ResponseErrCd;
}

export = async (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /cancelRevoke', { url: req.url, body: req.body });
  const { gnidSessionToken } = req.body;

  try {
    // 호출서버 IP인증 확인(플랫폼 서버에서 제공하는 API를 통해 IP리스트는 확인 가능)
    const success: boolean = await mhttp.lgd.cancelRevoke(gnidSessionToken);
    return res.json({ isSuccess: success });
  } catch (error) {
    mlog.error('api cancelRevoke exception', error);
    return res.json({
      isSuccess: false,
      errorCd: ((err) => {
        if (err instanceof MError) {
          return error.extra ? error.extra.errorCd : 'UNKNOWN';
        }
        return 'UWO_INTERNAL_ERROR';
      })(error),
    });
  }
};
