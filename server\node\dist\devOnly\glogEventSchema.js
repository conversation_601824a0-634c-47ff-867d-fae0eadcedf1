"use strict";
/**
 * UWO Game Log (GLog) Event Schema Documentation
 *
 * This file documents all glog events used in the UWO game server,
 * including their parameters, usage contexts, and data structures.
 *
 * Generated from codebase analysis of user.glog() and gameLog.glog() calls.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GLOG_SCENES = exports.COMMON_REASONS = exports.GLOG_COLLECTIONS = void 0;
// =============================================================================
// GLog Collection Names (Event Types)
// =============================================================================
exports.GLOG_COLLECTIONS = {
    // Battle events
    BATTLE_PVE_START: 'battle_pve_start',
    BATTLE_PVE_FINISH: 'battle_pve_finish',
    BATTLE_PVP_START: 'battle_pvp_start',
    BATTLE_PVP_FINISH: 'battle_pvp_finish',
    BATTLE_CHALLENGE_START: 'battle_challenge_start',
    BATTLE_CHALLENGE_FINISH: 'battle_challenge_finish',
    BATTLE_ARENA_START: 'battle_arena_start',
    BATTLE_ARENA_FINISH: 'battle_arena_finish',
    BOSS_RAID_START: 'boss_raid_start',
    BOSS_RAID_FINISH: 'boss_raid_finish',
    GUILD_BOSS_RAID_START: 'guild_boss_raid_start',
    GUILD_BOSS_RAID_FINISH: 'guild_boss_raid_finish',
    BATTLE_FRIENDLY_FINISH: 'battle_friendly_finish',
    LIGHTHOUSE_START: 'lighthouse_start',
    LIGHTHOUSE_FINISH: 'lighthouse_finish',
    BATTLE_CLASH_START: 'battle_clash_start',
    BATTLE_CLASH_END: 'battle_clash_end',
    // User state events
    COMMON_DURATION: 'common_duration',
    COMPANY_EXP: 'company_exp',
    KARMA: 'karma',
    LOGIN_OUT_SAVE: 'login_out_save',
    // Collection events
    COLLECTION_REGISTER: 'collection_register',
    COLLECTION_COMPLETE: 'collection_complete',
    DISCOVERY_CONTRACT: 'discovery_contract',
    // Ocean events
    OCEAN_DOODAD: 'ocean_doodad',
    FISHING: 'fishing',
    // Guild events
    GUILD_CREATE: 'guild_create',
    // Quest events
    QUEST: 'quest',
    ADMIRAL_STORY: 'admiral_story',
};
// =============================================================================
// Additional GLog Events Found in Codebase
// =============================================================================
// Item and Economy Events (handled by SDO GLog system)
// - Money changes: handled by SdoGLogEvents.uwo_money_glog()
// - Item changes: handled by SdoGLogEvents.uwo_item_glog()
// - Deposits: handled by SdoGLogEvents.uwo_deposit_glog()
// Character Events (handled by SDO GLog system)
// - Character creation: handled by SdoGLogEvents.uwo_character_glog()
// - Login: handled by SdoGLogEvents.uwo_character_login_glog()
// - Logout: handled by SdoGLogEvents.uwo_character_logout_glog()
// - Level up: handled by SdoGLogEvents.uwo_character_levelup_glog()
// Trade Events (handled by SDO GLog system)
// - Player trades: handled by SdoGLogEvents.uwo_trade_glog()
// Guild Events (handled by SDO GLog system)
// - Guild operations: handled by SdoGLogEvents.uwo_guild_glog()
// - Guild member changes: handled by SdoGLogEvents.uwo_guild_member_glog()
// - Guild buffs: handled by SdoGLogEvents.uwo_guild_buff_glog()
// Chat Events (handled by SDO GLog system)
// - Chat messages: handled by SdoGLogEvents.uwo_chat_glog()
// Task/Quest Events (handled by SDO GLog system)
// - Quest progress: handled by SdoGLogEvents.uwo_task_glog()
// CDKey Events (handled by SDO GLog system)
// - CDKey usage: handled by SdoGLogEvents.uwo_cdkey_glog()
// Buff Events (handled by SDO GLog system)
// - Personal buffs: handled by SdoGLogEvents.uwo_buff_glog()
// SIF (Server Integration Framework) Events (handled by SDO GLog system)
// - SIF rebates: handled by SdoGLogEvents.uwo_sif_rebate_glog()
// - SIF free deposits: handled by SdoGLogEvents.uwo_sif_free_deposit_glog()
// - SIF trades: handled by SdoGLogEvents.uwo_sif_trade_glog()
// - SIF consumption: handled by SdoGLogEvents.uwo_sif_consume_glog()
// Server Events (handled by SDO GLog system)
// - Online numbers: handled by SdoGLogEvents.uwo_olnum_glog()
// =============================================================================
// Usage Patterns Summary
// =============================================================================
/**
 * GLog Usage Patterns in UWO:
 *
 * 1. Legacy GLog System (user.glog() calls):
 *    - Used for detailed game mechanics logging
 *    - Automatically adds common user data (nid, gnid, level, etc.)
 *    - Primarily used for battle events, user state changes, collections
 *    - Data goes to internal analytics system
 *
 * 2. SDO GLog System (SdoGLogEvents.* calls):
 *    - Used for standardized events required by SDO platform
 *    - Follows specific schema defined in uwo_glog.xml
 *    - Used for character lifecycle, economy, social features
 *    - Data goes to SDO analytics platform
 *
 * 3. Hybrid Approach:
 *    - Some events are logged to both systems
 *    - gameLog.glog() can convert legacy events to SDO format
 *    - Ensures compatibility with both internal and external analytics
 *
 * Common Parameters:
 * - rsn (reason): Primary reason for the event
 * - add_rsn (additional reason): Secondary context
 * - region_id: Current game region
 * - coordinates: Player location in game world
 * - reward_data: Items/rewards gained from the event
 * - exchange_hash: Unique identifier for transactions
 */
// =============================================================================
// Event Context and Reasons
// =============================================================================
exports.COMMON_REASONS = {
    // Battle reasons
    BATTLE_START: 'battle_start',
    BATTLE_WIN: 'battle_win',
    BATTLE_LOSE: 'battle_lose',
    // Ocean exploration
    OCEAN_DOODAD_TRIGGERED: 'ocean_doodad_triggered',
    // Collection reasons
    COLLECTION_REGISTER: 'collection_register',
    COLLECTION_COMPLETE: 'collection_complete',
    // Discovery reasons
    DISCOVERY_CONTRACT: 'discovery_contract',
    DISCOVERY_CANCEL: 'discovery_cancel',
    // Guild reasons
    GUILD_CREATE: 'guild_create',
    GUILD_JOIN: 'guild_join',
    GUILD_LEAVE: 'guild_leave',
    // Quest reasons
    QUEST_ACCEPT: 'quest_accept',
    QUEST_COMPLETE: 'quest_complete',
    QUEST_ABANDON: 'quest_abandon',
};
exports.GLOG_SCENES = {
    NONE: 'NONE',
    TOWN: 'TOWN',
    OCEAN: 'OCEAN',
    BATTLE: 'BATTLE',
    MENU: 'MENU',
    // Add more scenes as needed
};
//# sourceMappingURL=glogEventSchema.js.map