"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
const protocol_1 = require("../../proto/townd-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
// ----------------------------------------------------------------------------
// 로비서버와 연결 완료
// ----------------------------------------------------------------------------
router.on(protocol_1.TownProtocol.LB2TO_REQ_CONNECTED, async (req, res) => {
    const packet = new protocol_1.TownProtocol.TO2LB_RES_CONNECTED();
    // 동기화 필요시 요청.
    res.send(packet);
});
module.exports = router;
//# sourceMappingURL=townPacketHandlerSync.js.map