#!/bin/bash

CWD="$(dirname "$0")"

if [ ! -f $CWD/_config.sh ]; then
	echo "Please create a custom '_config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/_query.sh ]; then
	echo "Please create a custom '_query.sh' file at '$CWD' directory."
	exit 1
fi

if [ -z "$1" ]
  then
    echo "No argument source_db_name"
    exit 1
fi

if [ -z "$2" ]
  then
    echo "No argument target_db_name"
    exit 1
fi

SECONDS=0

source $CWD/_config.sh
source $CWD/_query.sh

SOURCE_DB_NAME=$1
TARGET_DB_NAME=$2


# select * 와일드 카드 변경 안되게 처리
set -f

main() 
{
  echo "===== SHOW TABLES"
  q="SHOW TABLES;"
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${SOURCE_DB_NAME}"  "${q}"


  echo "===== COPY TABLES"
  tableList=(${QUERY_RESULT})
  for tableName in ${tableList[@]}
  do
    if [[ "$tableName" == "u_"* &&  "$tableName" != "u_arena_grade_rewards" && "$tableName" != "u_direct_mail_pendings" ]]
    then
      q="
      INSERT INTO ${TARGET_DB_NAME}.${tableName} 
        SELECT * FROM ${tableName};
      "
      queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${SOURCE_DB_NAME}" "${q}"
    fi
  done


  q="
    INSERT INTO ${TARGET_DB_NAME}.u_direct_mail_pendings
    (userId, cmsId, createTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment,
    title, titleFormatValue, body, bodyFormatValue, attachment, isValid)
    SELECT userId, cmsId, createTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment,
      title, titleFormatValue, body, bodyFormatValue, attachment, isValid
    FROM u_direct_mail_pendings;
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${SOURCE_DB_NAME}" "${q}"
}



main "$@"; 

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
exit
