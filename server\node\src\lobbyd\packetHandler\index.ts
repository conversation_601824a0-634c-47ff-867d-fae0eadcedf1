// ------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ------------------------------------------------------------------------------------------------

import Container from 'typedi';

import * as proto from '../../proto/lobby/proto';
import { User } from '../user';
import { CPacket } from '../userConnection';
import mlog from '../../motiflib/mlog';
import { MError, ME<PERSON>rCode } from '../../motiflib/merror';

import { Cph_Auth_Hello } from './auth/hello';
import { Cph_Auth_EnterWorld } from './auth/enterWorld';
import { Cph_Auth_ChangeName } from './auth/changeName';
import { Cph_Auth_CompletePrologue } from './auth/completePrologue';
import { Cph_Auth_Revoke } from './auth/revoke';
import { Cph_Auth_GameGuardCheck } from './auth/gameGuardCheck';
import { Cph_Auth_AppGuardCheck } from './auth/appGuardCheck';
import { Cph_Town_Enter } from './town/enter';
import { Cph_Town_LoadComplete } from './town/loadComplete';
import { Cph_Town_Move } from './town/move';
import { Cph_Town_QueryMate } from './town/queryMate';
import { Cph_Town_EnterBuilding } from './town/enterBuilding';
import { Cph_Town_LeaveBuilding } from './town/leaveBuilding';
import { Cph_Town_BankDeposit } from './town/bankDeposit';
import { Cph_Town_BankWithdraw } from './town/bankWithdraw';
import { Cph_Town_BankDepositInstallmentSavings } from './town/bankDepositInstallmentSavings';
import { Cph_Town_BankWithdrawInstallmentSavings } from './town/bankWithdrawInstallmentSavings';
import { Cph_Town_BuyInsurance } from './town/bankBuyInsurance';
import { Cph_Town_DepartChangeShipFleetFormation } from './town/departChangeShipFleetFormation';
import { Cph_Town_DepartBuySupplies } from './town/departBuySupplies';
import { Cph_Town_DepartDepart } from './town/departDepart';
import { Cph_Town_ShipyardCreateShip } from './town/shipyardCreateShip';
import { Cph_Town_ShipyardRepair } from './town/shipyardRepair';
import { Cph_Town_ShipyardSellShip } from './town/shipyardSellShip';
import { Cph_Town_ShipyardChangeBlueprintSlot } from './town/shipyardChangeBlueprintSlot';
import { Cph_Town_PubDraftSailor } from './town/pubDraftSailor';
import { Cph_Town_PubGetSyncData } from './town/pubGetSyncData';
import { Cph_Town_PubRecruitMate } from './town/pubRecruitMate';
import { Cph_Town_PubBuyDrink } from './town/pubBuyDrink';
import { Cph_Town_GoverInvest } from './town/goverInvest';
import { Cph_Town_GoverGetSyncData } from './town/goverGetSyncData';
import { Cph_Town_TradeGetSyncData } from './town/tradeGetSyncData';
import { Cph_Town_TradeBuy } from './town/tradeBuy';
import { Cph_Town_TradeSell } from './town/tradeSell';
import { Cph_Town_TradeGetAllSessionPricePercents } from './town/tradeGetAllSessionPricePercents';
import { Cph_Town_ReligionUpdateBuff } from './town/religionUpdateBuff';
import { Cph_Town_ShopBuyEx } from './town/shopBuyEx';
import { Cph_Town_ShopGetTownBlackMarket } from './town/shopGetTownBlackMarket';
import { Cph_Town_ShopSell } from './town/shopSell';
import { Cph_Town_ShopResetTownBlackMarket } from './town/shopResetTownBlackMarket';
import { Cph_Town_ReceiveInsurance } from './town/receiveInsurance';
import { Cph_Town_ChangeShipSlot } from './town/changeShipSlot';
import { Cph_Town_ChangeShipSlots } from './town/changeShipSlots';
import { Cph_Town_UnionResetRequest } from './town/unionResetRequest';
import { Cph_Town_PubGift } from './town/pubGift';
import { Cph_Town_TradeTryBeginNego } from './town/tradeTryBeginNego';
import { Cph_Town_TradeTryNego } from './town/tradeTryNego';
import { Cph_Town_NoviceSupply } from './town/noviceSupply';
import { Cph_Town_PalaceGetRoyalOrder } from './town/palaceGetRoyalOrder';
import { Cph_Town_PalaceRejectRoyalOrder } from './town/palaceRejectRoyalOrder';
import { Cph_Town_ManticFortune } from './town/manticFortune';
import { Cph_Town_ArrestUserChoice } from './town/arrestUserChoice';
import { Cph_Town_CollectorReportDiscoveries } from './town/collectorReportDiscoveries';
import { Cph_CollectorContract } from './town/collectorContract';
import { Cph_Town_CollectorGetRank } from './town/collectorGetRank';
import { Cph_Town_TradeResetBought } from './town/tradeResetBought';
import { Cph_Town_CollectorReportWorldMapTiles } from './town/collectorReportWorldMapTiles';
import { Cph_Town_ShipyardBuy } from './town/shipyardBuy';
import { Cph_Town_ShipyardEnchant } from './town/shipyardEnchant';
import { Cph_Town_ShipyardChoiceEnchantResult } from './town/shipyardChoiceEnchantResult';
import { Cph_Town_ShipyardDismantleShip } from './town/shipyardDismantleShip';
import { Cph_Town_SocialAniPersist } from './town/updateSocialAniPersist';
import { Cph_Town_ShowSocialAniInstant } from './town/showSocialAniInstant';
import { Cph_Town_ShowEmoticonInstant } from './town/showEmoticonInstant';
import { Cph_Town_ManticSwapPiece } from './town/manticSwapPiece';
import { Cph_Town_GoverChangeMayorTax } from './town/goverChangeMayorTax';
import { Cph_Town_PubStaffNomination } from './town/pubStaffNomination';
import { Cph_Town_PubStaffTalking } from './town/pubStaffTalking';
import { Cph_Town_PubStaffBoasting } from './town/pubStaffBoasting';
import { Cph_Town_PubStaffGift } from './town/pubStaffGift';
import { Cph_Town_PubStaffCall } from './town/pubStaffCall';
import { Cph_Town_PubStaffGetSyncData } from './town/pubStaffGetSyncData';
import { Cph_Town_GoverGetLastWeekRank } from './town/goverGetLastWeekRank';
import { Cph_Town_PubReduceRecruitNegoWaitTime } from './town/pubReduceRecruitNegoWaitTime';
import { Cph_Town_PubResetRecruitNegoWaitTime } from './town/pubResetRecruitNegoWaitTime';
import { Cph_Town_PubSponsorMyMate } from './town/pubSponsorMyMate';
import { Cph_Town_PubReduceMyMateSponsorWaitTime } from './town/pubReduceMyMateSponsorWaitTime';
import { Cph_Town_PubResetMyMateSponsorWaitTime } from './town/pubResetMyMateSponsorWaitTime';
import { Cph_Ocean_Arrive } from './ocean/arrive';
import { Cph_Ocean_LoadComplete } from './ocean/loadComplete';
import { Cph_Ocean_Enter } from './ocean/enter';
import { Cph_Ocean_Move } from './ocean/move';
import { Cph_Ocean_InspectTown } from './ocean/inspectTown';
import { Cph_Ocean_BeginAutoSailing } from './ocean/beginAutoSailing';
import { Cph_Ocean_EndAutoSailing } from './ocean/endAutoSailing';
import { Cph_Ocean_UpdateAutoSailing } from './ocean/updateAutoSailing';
import { Cph_Ocean_ResolveDisaster } from './ocean/resolveDisaster';
import { Cph_Ocean_EncountUserChoice } from './ocean/encountUserChoice';
import { Cph_Ocean_EncountByUser } from './ocean/encountByUser';
import { Cph_Ocean_EmergencyRecoverShips } from './ocean/emergencyRecoverShips';
import { Cph_Ocean_GameOverResurrect } from './ocean/gameOverResurrect';
import { Cph_Ocean_GetUserTownState } from './ocean/getUserTownState';
import { Cph_Ocean_EncountEnd } from './ocean/encountEnd';
import { Cph_Ocean_ZoomInNpc } from './ocean/zoomInNpc';
import { Cph_Ocean_ZoomInUser } from './ocean/zoomInUser';
import { Cph_Ocean_RevealWorldMapTile } from './ocean/revealWorldMapTile';
import { Cph_Ocean_DiscoverVillage } from './ocean/discoverVillage';
import { Cph_Ocean_AddQuestionPlace } from './ocean/addQuestionPlace';
import { Cph_Ocean_ActivateOceanDoodad } from './ocean/activateOceanDoodad';
import { Cph_Ocean_LandEnter } from './ocean/landEnter';
import { Cph_Ocean_LandLeave } from './ocean/landLeave';
import { Cph_Ocean_LandExplore } from './ocean/landExplore';
import { Cph_Ocean_LandStart } from './ocean/landStart';
import { Cph_Ocean_InspectVillage } from './ocean/inspectVillage';
import { Cph_Ocean_VillageEnter } from './ocean/villageEnter';
import { Cph_Ocean_VillageLeave } from './ocean/villageLeave';
import { Cph_Ocean_VillageGift } from './ocean/villageGift';
import { Cph_Ocean_VillageDraftSailor } from './ocean/villageDraftSailor';
import { Cph_Ocean_VillageExplore } from './ocean/villageExplore';
import { Cph_Ocean_OfflineSailingMoveDelegateStart } from './ocean/offlineSailingMoveDelegateStart';
import { Cph_Ocean_ShowEmoticonInstant } from './ocean/showEmoticonInstant';
import { Cph_Ocean_FishingStart } from './ocean/fishingStart';
import { Cph_Ocean_FishingFighting } from './ocean/fishingFighting';
import { Cph_Ocean_FishingEnd } from './ocean/fishingEnd';
import { Cph_Ocean_ItemResurrect } from './ocean/itemResurrect';
import { Cph_Battle_Action } from './battle/battleAction';
import { Cph_Battle_End } from './battle/battleEnd';
import { Cph_Battle_EndRaid } from './battle/battleEndRaid';
import { Cph_Battle_EndChallenge } from './battle/battleEndChallenge';
import { Cph_Battle_LoadComplete } from './battle/battleLoadComplete';
import { Cph_Battle_Resume } from './battle/battleResume';
import { Cph_Battle_Start } from './battle/battleStart';
import { Cph_Battle_StartChallenge } from './battle/battleStartChallenge';
import { Cph_Battle_Lose } from './battle/battleLose';
import { Cph_Battle_StartArena } from './battle/battleStartArena';
import { Cph_Battle_Cancel } from './battle/battleCancel';
import { Cph_Battle_Quest_Resume_Block } from './battle/battleQuestResumeBlock';
import { Cph_Duel_Start } from './duel/duelStart';
import { Cph_Duel_Action } from './duel/duelAction';
import { Cph_Duel_End } from './duel/duelEnd';
import { Cph_Duel_Resume } from './duel/duelResume';
import { Cph_Common_CreateFirstMate } from './common/createFirstMate';
import { Cph_Common_EquipMateEquipment } from './common/equipMateEquipment';
import { Cph_Common_QueryNation } from './common/queryNation';
import { Cph_Common_SelectNation } from './common/selectNation';
import { Cph_Common_ChangeNation } from './common/changeNation';
import { Cph_Common_GetUserLightInfos } from './common/getUserLightInfos';
import { Cph_Common_ChangeCompanyJob } from './common/changeCompanyJob';
import { Cph_Common_ChangeShipName } from './common/changeShipName';
import { Cph_Common_RelocateSailor } from './common/relocateSailor';
import { Cph_Common_RemoveItem } from './common/removeItem';
import { Cph_Common_RemoveMateEquipment } from './common/removeMateEquipment';
import { Cph_Common_ExpandInventorySlot } from './common/expandInventorySlot';
import { Cph_Common_ReceiveMails } from './common/receiveMails';
import { Cph_Common_QuestAccept } from './common/questAccept';
import { Cph_Common_QuestStartBlock } from './common/questStartBlock';
import { Cph_Common_QuestEntrust } from './common/questEntrust';
import { Cph_Common_QuestEndBlock } from './common/questEndBlock';
import { Cph_Common_QuestRegStore } from './common/questRegStore';
import { Cph_Common_DeleteMails } from './common/deleteMails';
import { Cph_Common_ReadMails } from './common/readMails';
import { Cph_Common_RemoveCargo } from './common/removeCargo';
import { Cph_Common_ReloadCargo } from './common/reloadCargo';
import { Cph_Common_GetTownTradePricePercents } from './common/getTownTradePricePercents';
import { Cph_Common_QuestDrop } from './common/questDrop';
import { Cph_Common_UseItem } from './common/useItem';
import { Cph_Common_QuestGoto } from './common/questGoto';
import { Cph_Common_LockShipSlots } from './common/lockShipSlots';
import { Cph_Common_EquipMateEquipments } from './common/equipMateEquipments';
import { Cph_Common_BuyAdmiral } from './common/buyAdmiral';
import { Cph_Common_RecoverInjury } from './common/recoverInjury';
import { Cph_Common_IncreaseLoyalty } from './common/increaseLoyalty';
import { Cph_Common_MeetMates } from './common/meetMates';
import { Cph_Common_TalkMates } from './common/talkMates';
import { Cph_Common_SetCargoLoadPreset } from './common/setCargoLoadPreset';
import { Cph_Common_QuestOperateReg } from './common/questOperateReg';
import { Cph_Common_RecoverShips } from './common/recoverShips';
import { Cph_Common_ResetTask } from './common/resetTask';
import { Cph_Common_ReceiveAchievementReward } from './common/receiveAchievementReward';
import { Cph_Common_ReceiveTaskReward } from './common/receiveTaskReward';
import { Cph_Common_GetTownNationSharePoints } from './common/getTownNationSharePoints';
import { Cph_Common_CompleteTaskImmediately } from './common/completeTaskImmediately';
import { Cph_Common_ReceiveTaskCategoryReward } from './common/receiveTaskCategoryReward';
import { Cph_Common_ReceiveAchievementPointReward } from './common/receiveAchievementPointReward';
import { Cph_Common_ReceiveHotTime } from './common/receiveHotTime';
import { Cph_Common_BuyTaxFreePermit } from './common/buyTaxFreePermit';
import { Cph_Common_GetWorldMapTownInfo } from './common/getWorldMapTownInfo';
import { Cph_Common_ConsumeQuestEnergy } from './common/consumeQuestEnergy';
import { Cph_Common_ExpandRequestSlot } from './common/expandRequestSlot';
import { Cph_Common_GetNationTowns } from './common/getNationTowns';
import { Cph_Common_RemoveExpiredRequestSlot } from './common/removeExpiredRequestSlot';
import { Cph_Common_CashShopGetProducts } from './common/cashShopGetProducts';
import { Cph_Common_CashShopBuyWithoutPurchase } from './common/cashShopBuyWithoutPurchase';
import { Cph_Common_GetRegionOccupations } from './common/getRegionOccupations';
import { Cph_Common_GetUserLightInfoByName } from './common/getUserLightInfoByName';
import { Cph_Common_CashShopReceiveGachaBoxGuarantee } from './common/cashShopReceiveGachaBoxGuarantee';
import { Cph_Common_GetUserLightInfosOnlyIsOnline } from './common/getUserLightInfosOnlyIsOnline';
import { Cph_Common_UnlockShipCustomizing } from './common/unlockShipCustomizing';
import { Cph_Common_CustomizeShip } from './common/customizeShip';
import { Cph_Common_ChatInit } from './common/chatInit';
import { Cph_Common_ChatJoinChannel } from './common/chatJoinChannel';
import { Cph_Common_MateStartAwaken } from './common/mateStartAwaken';
import { Cph_Common_MateStartLearnPassive } from './common/mateStartLearnPassive';
import { Cph_Common_QuestItemSetQuest } from './common/questItemSetQuest';
import { Cph_Common_QuestItemUse } from './common/questItemUse';
import { Cph_Common_Attendance } from './common/attendance';
import { Cph_Common_MateEquipPassives } from './common/mateEquipPassives';
import { Cph_Common_QuestSetPause } from './common/questSetPause';
import { Cph_Common_CustomizeMateEquip } from './common/customizeMateEquip';
import { Cph_Common_UnlockMateEquipColor } from './common/unlockMateEquipColor';
import { Cph_Common_BubbleEventAction } from './common/bubbleEventAction';
import { Cph_Common_ShipSlotItemSell } from './common/shipSlotItemSell';
import { Cph_Common_ShipSlotItemRemove } from './common/shipSlotItemRemove';
import { Cph_Common_QuestItemDrop } from './common/questItemDrop';
import { Cph_Common_SetGameOptionPushNotification } from './common/setGameOptionPushNotification';
import { Cph_Common_GetGameOptionPushNotification } from './common/getGameOptionPushNotification';
import { Cph_Common_ToogleEncountShieldActivation } from './common/toogleEncountShieldActivation';
import { Cph_Common_BattleFormationAcquire } from './common/battleFormationAcquire';
import { Cph_Common_BattleFormationChange } from './common/battleFormationChange';
import { Cph_Common_QueryNearestTown } from './common/queryNearestTown';
import { Cph_Common_UserWorldSkill } from './common/useWorldSkill';
import { Cph_Common_MateCompleteAwaken } from './common/mateCompleteAwaken';
import { Cph_Common_MateCompleteAwakenImmediately } from './common/mateCompleteAwakenImmediately';
import { Cph_Common_MateReduceAwakenTime } from './common/mateReduceAwakenTime';
import { Cph_Common_MateCompleteLearnPassive } from './common/mateCompleteLearnPassive';
import { Cph_Common_MateCompleteLearnPassiveImmediately } from './common/mateCompleteLearnPassiveImmediately';
import { Cph_Common_MateReduceLearnPassiveTime } from './common/mateReduceLearnPassiveTime';
import { Cph_Common_UpdateAdjutantDelegationConfig } from './common/updateAdjutantDelegationConfig';
import { Cph_Common_QuestStateChange } from './common/questStateChange';
import { Cph_BattleReward_ReceiveBattleReward } from './battleReward/receiveBattleReward';
import { Cph_BattleReward_Leave } from './battleReward/leave';
import { Cph_BattleReward_Enter } from './battleReward/enter';
import { Cph_BattleReward_LoadComplete } from './battleReward/loadComplete';
import { Cph_LandExploreReward_OpenBox } from './landExploreReward/openBox';
import { Cph_LandExploreReward_ReceiveEnter } from './landExploreReward/receiveEnter';
import { Cph_LandExploreReward_ReceiveLeave } from './landExploreReward/receiveLeave';
import { Cph_LandExploreReward_Receive } from './landExploreReward/receive';
import { Cph_Etc_Ping } from './etc/ping';
import { Cph_Etc_SetClientBackgroundState } from './etc/setClientBackgroundState';
import { Cph_Admin_TeleportTown } from './admin/teleportTown';
import { Cph_Admin_TeleportToUser } from './admin/teleportToUser';
import { Cph_Dev_GiveMateEquipment } from './dev/giveMateEquipment';
import { Cph_Dev_AddPoint } from './dev/addPoint';
import { Cph_Dev_AddMate } from './dev/addMate';
import { Cph_Dev_SetShipSailor } from './dev/setShipSailor';
import { Cph_Dev_AddShip } from './dev/addShip';
import { Cph_Dev_UpdateNation } from './dev/updateNation';
import { Cph_Dev_UpdateNationIntimacy } from './dev/updateNationIntimacy';
import { Cph_Dev_SetNation } from './dev/setNation';
import { Cph_Dev_UpdateReputation } from './dev/updateReputation';
import { Cph_Dev_InvokeEvent } from './dev/invokeEvent';
import { Cph_Dev_InvokeNationIntimacyUpdateJob } from './dev/invokeNationIntimacyUpdateJob';
import { Cph_Dev_RemovePoint } from './dev/removePoint';
import { Cph_Dev_SetPoint } from './dev/setPoint';
import { Cph_Dev_SetShipDurability } from './dev/setShipDurability';
import { Cph_Dev_RemoveAllShipCargos } from './dev/removeAllShipCargos';
import { Cph_Dev_SetFirstShipCargo } from './dev/setFirstShipCargo';
import { Cph_Dev_AddItem } from './dev/addItem';
import { Cph_Dev_SetDevelopmentLevel } from './dev/setDevelopmentLevel';
import { Cph_Dev_SetMateFame } from './dev/setMateFame';
import { Cph_Dev_SetMateRoyalTitle } from './dev/setMateRoyalTitle';
import { Cph_Dev_AddDirectMail } from './dev/addDirectMail';
import { Cph_Dev_AddInstallmentSavingsLastDepositTime } from './dev/addInstallmentSavingsLastDepositTime';
import { Cph_Dev_SetUserLevel } from './dev/setUserLevel';
import { Cph_Dev_SetUserKarma } from './dev/setUserKarma';
import { Cph_Dev_SetUserCompanyJob } from './dev/setUserCompanyJob';
import { Cph_Dev_SetMateInjury } from './dev/setMateInjury';
import { Cph_Dev_QuestSetFlags } from './dev/questSetFlags';
import { Cph_Dev_QuestSetRegister } from './dev/questSetRegister';
import { Cph_Dev_QuestSetTempRegister } from './dev/questSetTempRegister';
import { Cph_Dev_QuestSetAccum } from './dev/questSetAccum';
import { Cph_Dev_QuestSetCompleted } from './dev/questSetCompleted';
import { Cph_Dev_QuestSetNodeIdx } from './dev/questSetNodeIdx';
import { Cph_Dev_PredictDisaster } from './dev/predictDisaster';
import { Cph_Dev_GenerateDisaster } from './dev/generateDisaster';
import { Cph_Dev_ResolveDisaster } from './dev/resolveDisaster';
import { Cph_Dev_UnlimitedInvest } from './dev/unlimitedInvest';
import { Cph_Dev_SetIgnoreNpcEncount } from './dev/setIgnoreNpcEncount';
import { Cph_Dev_StatDump } from './dev/statDump';
import { Cph_Dev_StatSet } from './dev/statSet';
import { Cph_Dev_AddNearSpawner } from './dev/addNearSpawner';
import { Cph_Dev_WorldBuffAdd } from './dev/worldBuffAdd';
import { Cph_Dev_WorldBuffRem } from './dev/worldBuffRem';
import { Cph_Dev_DisconnectServer } from './dev/disconnectServer';
import { Cph_Dev_EncountNpcAttChoice } from './dev/encountNpcAttChoice';
import { Cph_Dev_EncountNpcDevChoice } from './dev/encountNpcDefChoice';
import { Cph_Dev_ChangeNpcAttackRadius } from './dev/changeNpcAttackRadius';
import { Cph_Dev_TradeDump } from './dev/tradeDump';
import { Cph_Dev_ChangeNpcTickPerSec } from './dev/changeNpcTickPerSec';
import { Cph_Dev_RemoveNearSpawner } from './dev/removeNearSpawner';
import { Cph_Dev_SetLoyalty } from './dev/setLoyalty';
import { Cph_Dev_ResetPubMates } from './dev/resetPubMates';
import { Cph_Dev_AddAllMates } from './dev/addAllMates';
import { Cph_Dev_GetNpcLocationByOceanNpcAreaSpawner } from './dev/getNpcLocationByOceanNpcAreaSpawer';
import { Cph_Dev_QuestForceExec } from './dev/questForceExec';
import { Cph_Dev_AddOceanDoodadNearSpawner } from './dev/addOceanDoodadNearSpawner';
import { Cph_Dev_RemoveOceanDoodadNearSpawner } from './dev/removeOceanDoodadNearSpawner';
import { Cph_Dev_SetLocalNpcSpawn } from './dev/setLocalNpcSpawn';
import { Cph_Dev_SetLocalDoodadSpawn } from './dev/setLocalDoodadSpawn';
import { Cph_Dev_SetDisasterLuck } from './dev/setDisasterLuck';
import { Cph_Dev_PredictProtection } from './dev/predictProtection';
import { Cph_Dev_GenerateProtection } from './dev/generateProtection';
import { Cph_Dev_ResolveProtection } from './dev/resolveProtection';
import { Cph_Dev_TeleportToLocation } from './dev/teleportToLocation';
import { Cph_Dev_SetMateLevel } from './dev/setMateLevel';
import { Cph_Dev_AddUserDataNpcSpawner } from './dev/addUserDataNpcSpawner';
import { Cph_Dev_RevealAllWorldMapTiles } from './dev/revealAllWorldMapTIles';
import { Cph_Dev_DiscoverAllTowns } from './dev/discoverAllTowns';
import { Cph_Dev_AddAllTownsToQuestionPlace } from './dev/addAllTownsToQuestionPlace';
import { Cph_Dev_WorldPassiveAdd } from './dev/worldPassiveAdd';
import { Cph_Dev_WorldPassiveRem } from './dev/worldPassiveRem';
import { Cph_Dev_DebuffImmune } from './dev/debuffImmune';
import { Cph_Dev_DisasterImmune } from './dev/disasterImmune';
import { Cph_Dev_ShowDisasterStat } from './dev/showDisasterStat';
import { Cph_Dev_SetTradeGoodsBreedSuccess } from './dev/setTradeGoodsBreedSuccess';
import { Cph_Dev_Discover } from './dev/discover';
import { Cph_Dev_BattleResumeForUserId } from './dev/battleResumeForUserId';
import { Cph_Dev_GiveAllMateEquipments } from './dev/giveAllMateEquipments';
import { Cph_Dev_DiscoverAll } from './dev/discoverAll';
import { Cph_Dev_AttackToMe } from './dev/attackToMe';
import { Cph_Dev_AddBattleFormation } from './dev/addBattleFormation';
import { Cph_Dev_SpecialStatDump } from './dev/specialStatDump';
import { Cph_Dev_SetMateAwaken } from './dev/setMateAwaken';
import { Cph_Dev_PubStaffReset } from './dev/pubStaffReset';
import { Cph_Dev_SetTownNationSharePoint } from './dev/setTownNationSharePoint';
import { Cph_Dev_IAmMayor } from './dev/iAmMayor';
import { Cph_Dev_ChangeMayorTax } from './dev/changeMayorTax';
import { Cph_Dev_EasyLanguage } from './dev/easyLanguage';
import { Cph_Dev_ChangeSpawnedLocalNpcNum } from './dev/changeSpawnedLocalNpcNum';
import { Cph_Dev_SetSailingDaysOnOff } from './dev/setSailingDaysOnOff';
import { Cph_Temp_ButtonLog } from './temp/buttonLog';
import { Cph_Ocean_VillagePlunder } from './ocean/villagePlunder';
import { Cph_Dev_AddParts } from './dev/addParts';
import { Cph_Dev_ResetCollection } from './dev/resetCollection';
import { Cph_Common_RegisterCollection } from './common/registerCollection';
import { Cph_Town_ShipyardReceiveShip } from './town/shipyardReceiveShip';
import { Cph_Common_ShipBuildingDecreaseExpireTime } from './common/shipBuildingDecreaseExpireTime';
import { Cph_Common_ShipBuildingCompleteExpireTime } from './common/shipBuildingCompleteExpireTime';
import { Cph_Common_ShipBuildingDeliver } from './common/shipBuildingDeliver';
import { Cph_Dev_SetMateTalkWaitTime } from './dev/setMateTalkWaitTime';
import { Cph_Common_ReceiveEventMissionReward } from './common/receiveEventMissionReward';
import { Cph_Common_FindDistanceFromTileToTown } from './common/findDistanceFromTileToTown';
import { Cph_Common_GetAdmiralProfile } from './common/getAdmiralProfile';
import { Cph_Common_GetFlagShipProfile } from './common/getFlagShipProfile';
import { Cph_Common_SetOptionProfiles } from './common/setOptionProfiles';
import { Cph_Town_ShipyardVerifyTowShip } from './town/shipyardVerifyTowShip';
import { Cph_Common_RefreshWeeklyEvent } from './common/refreshWeeklyEvent';
import { Cph_Common_AuctionLoadProducts } from './common/auctionLoadMyProducts';
import { Cph_Common_AuctionRegister } from './common/auctionRegister';
import { Cph_Common_AuctionCancel } from './common/auctionCancel';
import { Cph_Common_AuctionReregister } from './common/auctionReregister';
import { Cph_Common_AuctionQueryProducts } from './common/auctionQueryProducts';
import { Cph_Common_AuctionBuy } from './common/auctionBuy';
import { Cph_Common_AuctionReceiveProceeds } from './common/auctionReceiveProceeds';
import { Cph_Town_ShipyardRepairLifeShip } from './town/shipyardRepairLifeShip';
import { Cph_Dev_Load } from './dev/load';
import { Cph_Dev_ResetPalaceRoyalOrder } from './dev/resetPalaceRoyalOrder';
import { Cph_Ocean_RevealRegion } from './ocean/revealRegion';
import { Cph_Common_AuctionQueryShipProducts } from './common/auctionQueryShipProducts';
import { Cph_Ocean_RevealOceanDoodads } from './ocean/revealOceanDoodads';
import { Cph_Ocean_DiscoverOceanDoodad } from './ocean/discoverOceanDoodad';
import { Cph_Common_BillingQuerySalesList } from './common/billingQuerySalesList';
import { Cph_Common_BillingReservePurchase } from './common/billingReservePurchase';
import { Cph_Common_BillingCancelReservedPurchase } from './common/billingCancelReservedPurchase';
import { Cph_Common_BillingCompleteReservedPurchaseAndGive } from './common/billingCompleteReservedPurchaseAndGive';
import { Cph_Common_BillingQueryPurchaseDetail } from './common/billingQueryPurchaseDetail';
import { Cph_Common_BillingQueryProductGiveItemDetail } from './common/billingQueryProductGiveItemDetail';
import { Cph_Common_BillingChargeByPurchaseProduct } from './common/billingChargeByPurchaseProduct';
import { Cph_Dev_QuestSetGlobalRegister } from './dev/questSetGlobalRegister';
import { Cph_Dev_UnlockAllMateAwakenAndSkill } from './dev/unlockAllMateAwakenAndSkill';
import { Cph_Ocean_LandExploreQueryFeature } from './ocean/landExploreQueryFeature';
import { Cph_Guild_Create } from './guild/guildCreate';
import { Cph_Guild_CheckForDuplicatesName } from './guild/guildCheckForDuplicatesName';
import { Cph_Guild_Disband } from './guild/guildDisband';
import { Cph_Guild_ShowList } from './guild/guildShowList';
import { Cph_Guild_Join } from './guild/guildJoin';
import { Cph_Guild_JoinCancel } from './guild/guildJoinCancel';
import { Cph_Guild_Leave } from './guild/guildLeave';
import { Cph_Guild_ManagingAcceptJoining } from './guild/guildManagingAcceptJoining';
import { Cph_Guild_ManagingRefuseToJoin } from './guild/guildManagingRefuseToJoin';
import { Cph_Guild_ManagingChangeMemberGrade } from './guild/guildManagingChangeMemberGrade';
import { Cph_Guild_ManagingKickMember } from './guild/guildManagingKickMember';
import { Cph_Guild_GetMyGuildInfo } from './guild/guildGetMyGuildInfo';
import { Cph_Guild_GetDetailInfo } from './guild/guildGetDetailInfo';
import { Cph_Guild_Craft } from './guild/guildCraft';

import { Cph_Guild_PickupDailyReward } from './guild/guildPickupDailyReward';
import { Cph_Guild_ManagingChangeInfo } from './guild/guildManagingChangeInfo';
import { Cph_Guild_ManagingDelegateMaster } from './guild/guildManagingDelegateMaster';
import { Cph_Dev_ResetGuildLeftTimeTemporarily } from './dev/resetGuildLeftTimeTemporarily';
import { Cph_Dev_AddGuildPoint } from './dev/addGuildPoint';
import { Cph_Guild_CheckedUpgradePopup } from './guild/guildCheckedUpgradePopup';
import { Cph_Guild_GetLightInfo } from './guild/guildGetLightInfo';
import { Cph_Common_AuctionQuerySalePrices } from './common/auctionQuerySalePrices';
import { Cph_Dev_SetGuildUpgradePopup } from './dev/setGuildUpgradePopup';
import { Cph_Common_SetPingTimeout } from './common/setPingTimeout';
import { Cph_Town_PalaceGetContributionShopSyncData } from './town/palaceGetContributionShopSyncData';
import { Cph_Town_PalaceBuyContributionShopProduct } from './town/palaceBuyContributionShopProduct';
import { Cph_Common_ArenaEnter } from './common/arenaEnter';
import { Cph_Common_ArenaMatchListRefresh } from './common/arenaMatchListRefresh';
import { Cph_Common_ArenaTicketBuy } from './common/arenaTicketBuy';
import { Cph_Common_ArenaRewardReceive } from './common/arenaRewardReceive';
import { Cph_Dev_SetArenaScore } from './dev/setArenaScore';
import { Cph_Dev_SetArenaTicketCount } from './dev/setArenaTicketCount';
import { Cph_Dev_SetArenaTicketBoughtCount } from './dev/setArenaTicketBoughtCount';
import { Cph_Dev_SetArenaMatchListRefreshCount } from './dev/setArenaMatchListRefreshCount';
import { Cph_BattleLobby_Enter } from './battleLobby/enter';
import { Cph_BattleLobby_Leave } from './battleLobby/leave';
import { Cph_Common_ArenaFleetUpdate } from './common/arenaFleetUpdate';
import { Cph_Dev_ResetArenaData } from './dev/resetArenaData';
import { Cph_Common_QueryMyMayorTowns } from './common/queryMyMayorTowns';
import { Cph_Common_UpdateShields } from './common/updateShields';
import { Cph_Common_QueryVillage } from './common/queryVillage';
import { Cph_Common_PrologueStart } from './common/prologueStart';
import { Cph_Common_IncreaseLoyaltyUseItem } from './common/increaseLoyaltyUseItem';
import { Cph_Common_QueryReportedWorldMapTiles as Cph_Common_QueryReportedWorldMapTiles } from './common/queryReportedWorldMapTiles';
import { Cph_Common_CashShopBuyDailyProduct } from './common/cashShopBuyDailyProduct';
import { Cph_Dev_ResetCashShopDailyProducts } from './dev/resetCashShopDailyProducts';
import { Cph_Dev_SetLeaderMateSwitchCount } from './dev/setLeaderMateSwitchCount';
import { Cph_Common_QueryAchievements } from './common/queryAchievements';
import { Cph_Common_CashShopGetDailySale } from './common/cashShopGetDailySale';
import { Cph_Dev_QuestSetAdminPause } from './dev/questSetAdminPause';
import { Cph_Dev_GenerateArenaDummyUsers } from './dev/generateArenaDummyUsers';
import { Cph_Common_QueryRedGemDetail } from './common/queryRedGemDetail';
import { Cph_Dev_SetShipBuildLevel } from './dev/setShipBuildLevel';
import { Cph_Dev_SetUserShipBuildLevel } from './dev/setUserShipBuildLevel';
import { Cph_Dev_SetVillageFriendship } from './dev/setVillageFriendship';
import { Cph_Common_AuctionQueryClosed } from './common/auctionQueryClosed';
import { Cph_Dev_SetDebugMsgForEncountByNpc } from './dev/setDebugMsgForEncountByNpc';
import { Cph_Dev_ShowSpawnedNpcCount } from './dev/showSpawnedNpcCount';
import { Cph_Common_QUERY_LOCKED_TOWN_TRADE_GOODS } from './common/queryLockedTownTradeGoods';
import { Cph_Common_ReceivePassEventMissionReward } from './common/receivePassEventMissionReward';
import { Cph_Common_BuyPassEventExp } from './common/buyPassEventExp';
import { Cph_Dev_InitExploreTicket } from './dev/initExploreTicket';
import { Cph_Guild_GetGuildShopSyncData } from './guild/guildGetGuildShopSyncData';
import { Cph_Guild_BuyGuildShopProduct } from './guild/guildBuyGuildShopProduct';
import { Cph_Dev_ResetGuildShop } from './dev/resetGuildShop';
import { Cph_Dev_SetPassEventExp } from './dev/setPassEventExp';
import { Cph_Dev_SetEventPageProduct } from './dev/setEventPageProduct';
import { Cph_Dev_EstimatedSailingTime } from './dev/estimatedSailingTime';
import { Cph_Dev_FixedSpeed } from './dev/fixedSpeed';
import { Cph_Dev_DisableHackSpeed } from './dev/disableHackSpeed';
import { Cph_Common_BillingCompleteReservedPurchase } from './common/billingCompleteReservedPurchase';
import { Cph_Common_BillingQueryInvenPurchaseList } from './common/billingQueryInvenPurchaseList';
import { Cph_Common_BillingReceiveInvenPurchases } from './common/billingReceiveInvenPurchases';
import { Cph_Dev_SetExploreTimeCheck } from './dev/setExploreTimeCheck';
import { Cph_Common_EventShopBuy } from './common/eventShopBuy';
import { Cph_Common_EventShopGetProducts } from './common/eventShopGetProducts';
import { Cph_Town_UnionResetEventRequest } from './town/unionResetEventRequest';
import { Cph_Common_BillingQueryLatestReservedPurchase } from './common/billingQueryLatestReservedPurchase';
import { Cph_Common_ReportBadChatting } from './common/reportBadChatting';
import { Cph_Common_GetReportedBadChattingList } from './common/getReportedBadChattingList';
import { Cph_Guild_ReceiveWeeklyRewardMail } from './guild/guildReceiveWeeklyRewardMail';
import { Cph_Dev_ResetGuildDate } from './dev/resetGuildDate';
import { Cph_Town_ShipSlotItemsEquip } from './town/shipSlotItemsEquip';
import { Cph_Dev_SetShipLife } from './dev/setShipLife';
import { Cph_Guild_Craft_Create } from './guild/guildCraftCreate';
import { Cph_Guild_Craft_Receive } from './guild/guildCraftReceive';
import { Cph_Guild_CraftDecreaseExpireTime } from './guild/guildCraftDecreaseExpireTime';
import { Cph_Guild_CraftCompleteExpireTime } from './guild/guildCraftCompleteExpireTime';
import { Cph_Common_ChatMuteUser } from './common/chatMuteUser';
import { Cph_Common_ChatUnmuteUser } from './common/chatUnmuteUser';
import { Cph_Dev_AddAllShips } from './dev/addAllShips';
import { Cph_Dev_AddAllParts } from './dev/addAllParts';
import { Cph_Common_SaveFleetPreset } from './common/saveFleetPreset';
import { Cph_Common_DeleteFleetPreset } from './common/deleteFleetPreset';
import { Cph_Common_LoadFleetPreset } from './common/loadFleetPreset';
import { Cph_Raid_GetInfo } from './raid/raidGetInfo';
import { Cph_Dev_NoticeRaid } from './dev/noticeRaid';
import { Cph_Raid_GetRankingPage } from './raid/raidGetRankingPage';
import { Cph_Dev_AddRaidDamage } from './dev/addRaidDamage';
import { Cph_Dev_GetRaidState } from './dev/getRaidState';
import { Cph_Common_SetSailWaypoint } from './common/setSailWaypoint';
import { Cph_Common_QueryAllTownInvestments } from './common/queryAllTownInvestments';
import { Cph_Common_RemoveSailWaypoint } from './common/removeSailWaypoint';
import { Cph_Common_QueryTradeArea } from './common/queryTradeArea';
import { Cph_Town_TradeAreaRewardReceive } from './town/tradeAreaRewardReceive';
import { Cph_Common_ChangeFleetPresetName } from './common/changeFleetPresetName';
import { Cph_Battle_StartRaid } from './battle/battleStartRaid';
import { Cph_Raid_PickupReward } from './raid/raidPickupReward';
import { Cph_Dev_SetRaidShcheduleEnd } from './dev/setRaidShcheduleEnd';
import { Cph_Dev_SetTradePoint } from './dev/setTradePoint';
import { Cph_Dev_ResetWorldSkill } from './dev/resetWorldSkill';
import { Cph_Town_ShipyardRelaunchShip } from './town/shipyardRelaunchShip';
import { Cph_Dev_SetNationLastUpdateTime } from './dev/setNationLastUpdateTime';
import { Cph_Common_AddMateExpUseItem } from './common/addMateExpUseItem';
import { Cph_Dev_QuestResetAllDailyLimitCompletedCount } from './dev/questResetAllDailyLimitCompletedCount';
import { Cph_Dev_SetMateState } from './dev/setMateState';
import { Cph_Common_LockShip } from './common/lockShip';
import { Cph_Common_EventGetMiniBoardGame } from './common/eventGetMiniBoardGame';
import { Cph_Common_EventPlayMiniBoardGame } from './common/eventPlayMiniBoardGame';
import { Cph_Dev_AddFleet } from './dev/addFleet';
import { Cph_Common_HideEquipSlots } from './common/hideEquipSlots';
import { Cph_Common_BillingSteamPurchaseInitTxn } from './common/billingSteamPurchaseInitTxn';
import { Cph_Common_QueryCrazeEventBudget } from './common/queryCrazeEventBudget';
import { Cph_Friend_RequestFriend } from './friend/requestFriend';
import { Cph_Friend_CancelFriendRequest } from './friend/cancelFriendRequest';
import { Cph_Friend_DeleteFriend } from './friend/deleteFriend';
import { Cph_Friend_AcceptFriendRequest } from './friend/acceptFriendRequest';
import { Cph_Friend_DenyFriendRequest } from './friend/denyFriendRequest';
import { Cph_Friend_SendPoint } from './friend/sendPoint';
import { Cph_Friend_PickupPoint } from './friend/pickupPoint';
import { Cph_Dev_SetShipSailMasteryLevel as Cph_Dev_SetShipSailMasteryLevel } from './dev/setShipSailMasteryLevel';
import { Cph_Common_FleetDispatch_Start } from './common/fleetDispatchStart';
import { Cph_Common_FleetDispatch_End } from './common/fleetDispatchEnd';
import { Cph_Common_FleetDispatch_Reward_Receive } from './common/fleetDispatchRewardReceive';
import { Cph_Common_FleetDispatch_Cancel } from './common/fleetDispatchCancel';
import { Cph_Common_FleetDispatch_Slot_Open } from './common/fleetDispatchSlotOpen';
import { Cph_Dev_MakeDispatchEnd } from './dev/makeDispatchEnd';
import { Cph_Dev_AddAllItems } from './dev/addAllItems';
import { Cph_Ocean_ApplyWaypointSupply } from './ocean/applyWaypointSupply';
import { Cph_Dev_ResetWaypointSupplyTicket } from './dev/resetWaypointSupplyTicket';
import { Cph_Dev_SetShipSailMasteryExp } from './dev/setShipSailMasteryExp';
import { Cph_Common_FleetDispatchDecreasExpireTime } from './common/fleetDispatchDecreaseExpireTime';
import { Cph_Common_FleetDispatchRewardChoice } from './common/fleetDispatchRewardChoice';
import { Cph_Common_FleetDispatchQueryInProgreesState } from './common/fleetDispatchQueryInProgreesState';
import { Cph_Dev_ResetForTimeTravel } from './dev/resetForTimeTravel';
import { Cph_Dev_UnlinkCompany } from './dev/unlinkCompany';
import { Cph_Common_GetWorldRanking } from './common/getWorldRanking';
import { Cph_Common_MatesCompleteAwaken } from './common/matesCompleteAwaken';
import { Cph_Common_MatesCompleteLearnPassive } from './common/matesCompleteLearnPassive';
import { Cph_Dev_ClearDiscover } from './dev/clearDiscover';
import { Cph_Town_PubResetMatesByPaid } from './town/pubResetMatesByPaid';
import { Cph_Dev_RemoveAllItems } from './dev/removeAllItems';
import { Cph_Dev_RemoveUnequipEquipments } from './dev/removeUnequipEquipments';
import { Cph_Dev_RemoveUnequipParts } from './dev/removeUnequipParts';
import { Cph_Dev_AddReleasedMates } from './dev/addReleasedMates';
import { Cph_Dev_AddAllMatesAwaken } from './dev/addAllMatesAwaken';
import { Cph_Dev_SetAllMatesLevel } from './dev/setAllMatesLevel';
import { Cph_Common_UpdateBattleContinuousResult } from './common/updateBattleContinuousResult';
import { Cph_Common_ToggleBattleContinuous } from './common/toggleBattleContinuous';
import { Cph_Common_TranslateChat } from './common/translateChat';
import { Cph_Common_BuyChatTranslationCount } from './common/buyChatTranslationCount';
import { Cph_Dev_RecoverChatTranslation } from './dev/recoverChatTranslation';
import { Cph_Dev_ShowDispatchActionResultStat } from './dev/showDispatchActionResultStat';
import { Cph_Common_BuyAttendance } from './common/buyAttendance';
import { Cph_Common_receiveDailySubscriptionReward } from './common/receiveDailySubscriptionReward';
import { Cph_Guild_Synthesis_Receive } from './guild/guildSynthesisReceive';
import { Cph_Guild_Synthesis_Create } from './guild/guildSynthesisCreate';
import { Cph_Guild_SynthesisDecreaseExpireTime } from './guild/guildSynthesisDecreaseExpireTime';
import { Cph_Guild_SynthesisCompleteExpireTime } from './guild/guildSynthesisCompleteExpireTime';
import { Cph_Dev_ChangeGuildSynthesisProbGreatSuccess } from './dev/changeGuildSynthesisProbGreatSuccess';
import { Cph_Common_PublishToastMessage } from './common/publishToastMessage';
import { Cph_Common_MateStartTraining } from './common/mateStartTraining';
import { Cph_Common_MatesCompleteTraining } from './common/matesCompleteTraining';
import { Cph_Common_MateCompleteTrainingImmediately } from './common/mateCompleteTrainingImmediately';
import { Cph_Common_MateReduceTrainingTime } from './common/mateReduceTrainingTime';
import { Cph_Common_MateUseTrainingPoints } from './common/mateUseTrainingPoints';
import { Cph_Common_MateResetTrainingPoints } from './common/mateResetTrainingPoints';
import { Cph_Battle_MultiActionUserChange } from './battle/battleMultiActionUserChange';
import { Cph_Common_OpenHotSpotProduct } from './common/openHotSpotProduct';
import { Cph_Guild_Donate } from './guild/guildDonate';
import { Cph_Guild_RaidOpen } from './guild/guildRaidOpen';
import { Cph_Guild_RaidGetInfo } from './guild/guildRaidGetInfo';
import { Cph_Guild_Raid_GetRankingPage as Cph_Guild_RaidGetRankingPage } from './guild/guildRaidGetRankingPage';
import { Cph_Guild_Raid_PickupReward as Cph_Guild_RaidPickupReward } from './guild/guildRaidPickupReward';
import { Cph_Guild_Raid_BuyTicket as Cph_Guild_RaidBuyTicket } from './guild/guildRaidBuyTicket';
import { Cph_Reset_Guild_Raid_Ticket } from './dev/resetGuildRaidTicket';
import { Cph_Battle_StartGuildRaid } from './battle/battleStartGuildRaid';
import { Cph_Guild_RaidGetPrevRanking } from './guild/guildRaidGetPrevRanking';
import { Cph_Dev_SetGuildRaidShcheduleEnd } from './dev/setGuildRaidShcheduleEnd';
import { Cph_Dev_AddGulidRaidDamage } from './dev/addGuildRaidDamage';
import { Cph_Ocean_UpdateAutoSailOptionForServer } from './ocean/updateAutoSailOptionForServer';
import { Cph_Dev_AddGuildResource } from './dev/addGuildResource';
import { Cph_Battle_EndMultiPvp } from './battle/battleEndMultiPvp';
import { Cph_Common_GetWorldEventRanking } from './common/getWorldEventRanking';
import { Cph_Common_ReceiveEventRankingMissionReward } from './common/receiveEventRankingMissionReward';
import { Cph_Common_ReceiveEventRankingReward } from './common/receiveEventRankingReward';
import { Cph_Battle_MultiSyncInit } from './battle/battleMultiSyncInit';
import { Cph_Battle_MultiCtrlRequest } from './battle/battleMultiCtrlRequest';
import { Cph_Battle_MultiAutoChange } from './battle/battleMultiAutoChangeRequest';
import { Cph_Battle_MultiActionRequest } from './battle/battleMultiActionRequest';
import { Cph_Battle_MultiActionPhaseConfirm } from './battle/battleMultiActionPhaseConfirm';
import { Cph_Battle_MultiTimeoutNotification } from './battle/battleMultiTimeoutNotification';
import { Cph_Battle_MultiGiveUp } from './battle/battleMultiGiveUp';
import { Cph_Dev_ResetGuildRaidOpenTime } from './dev/resetGuildRaidOpenTime';
import { Cph_Dev_ResetGuildRaidBattleNRewardHistory } from './dev/resetGuildRaidBattleNRewardHistory';
import { Cph_Common_ShipSlotItemLock } from './common/shipSlotItemLock';
import { Cph_Common_ReceiveDiscoveryReward as Cph_Common_ReceiveDiscoveryReward } from './common/receiveDiscoveryRewards';
import { Cph_Common_GetSailingDiaries } from './common/getSailingDiaries';
import { Cph_Common_EquipMateIllust } from './common/equipMateIllust';
import { Cph_Town_PalaceGetRoyalTitleOrder } from './town/palaceGetRoyalTitleOrder';
import { Cph_Ocean_SearchConstellation } from './ocean/searchConstellation';
import { Cph_Ocean_DiscoverConstellation } from './ocean/discoverConstellation';
import { Cph_Town_GoverChangeMayorShipyardTax } from './town/goverChangeMayorShipyardTax';
import { Cph_Dev_AddShipCamouflage } from './dev/addShipCamouflage';
import { Cph_Town_ChangeShipCamouflage } from './town/changeShipCamouflage';
import { Cph_Guild_BuyDonationCount } from './guild/guildBuyDonationCount';
import { Cph_Dev_AddMateIllust } from './dev/addMateIllust';
import { Cph_Common_ReceiveFishSizeRewards } from './common/receiveFishSizeRewards';
import { Cph_Common_QueryGuildTowns } from './common/queryGuildTowns';
import { Cph_Common_QueryAllInvestedTownsGuildSharePoints } from './common/queryAllInvestedTownsGuildSharePoints';
import { Cph_Common_QueryGuildInvestmentUserScores } from './common/queryGuildInvestmentUserScores';
import { Cph_Guild_LearnGuildBuff } from './guild/guildLearnGuildBuff';
import { Cph_Guild_SelectGuildBuffCategory } from './guild/guildSelectGuildBuffCategory';
import { Cph_Guild_RegisterGuildBuffItem } from './guild/guildRegisterGuildBuffItem';
import { Cph_Common_UpdateHotTimeBuff } from './common/updateHotTimeBuff';
import { Cph_Common_QueryTownsFirstGuildSharePoint } from './common/queryTownsFirstGuildSharePoint';
import { Cph_Dev_SetUserLastLoginTimeDaysAgo } from './dev/setUserLastLoginTimeDaysAgo';
import { Cph_Dev_SetUserAttendance } from './dev/setUserAttendance';
import { Cph_Guild_BuyGuildRaidBuff } from './guild/guildBuyGuildRaidBuff';
import { Cph_Common_ChangeUserTitle } from './common/changeUserTitle';
import { Cph_Dev_AddUserTitle } from './dev/addUserTitle';
import { Cph_Common_EquipBattleQuickSkill } from './common/equipBattleQuickSkill';
import { Cph_Battle_MultiActionPhasePass } from './battle/battleMultiActionPhasePass';
import { Cph_Dev_ResetSessionRanking } from './dev/resetSessionRanking';
import { Cph_Common_QueryNationElectionSync } from './common/queryNationElectionSync';
import { Cph_Common_RegisterNationElectionCandidate } from './common/registerNationElectionCandidate';
import { Cph_Common_VoteToNationElectionCandidate } from './common/voteToNationElectionCandidate';
import { Cph_Dev_RemoveNationElectionCandidate } from './dev/removeNationElectionCandidate';
import { Cph_Common_ApplyFleetPreset } from './common/applyFleetPreset';
import { Cph_Common_ShipBuildingCancel } from './common/shipBuildingCancel';
import { Cph_Ocean_Village_Exchange } from './ocean/villageExchange';
import { Cph_Ocean_Village_Exchange_Try_Nego } from './ocean/villageExchangeTryNego';
import { Cph_Ocean_Village_Friendship_Reward } from './ocean/villageFriendshipReward';
import { Cph_Town_ShipyardBuyImprovedBlueprint } from './town/shipyardBuyImprovedBlueprint';
import { Cph_Town_GoverRegisterMayorTradeEvent } from './town/goverRegisterMayorTradeEvent';
import { Cph_Dev_ReserveMayorTradeEvent } from './dev/reserveMayorTradeEvent';
import { Cph_Dev_DeleteMayorTradeEvent } from './dev/deleteMayorTradeEvent';
import { Cph_Common_ReceiveNationElectionVoteReward } from './common/receiveNationElectionVoteReward';
import { Cph_Common_ReceiveNationGoalPromiseReward } from './common/receiveNationGoalPromiseReward';
import { Cph_Common_ModifyNationElectionCandidate } from './common/modifyNationElectionCandidate';
import { Cph_Dev_ChangeNationSessionId } from './dev/changeNationSessionId';
import { Cph_Common_ChangeCargoPresetName } from './common/changeCargoPresetName';
import { Cph_Common_ChangeEquipmentToCostume } from './common/changeEquipmentToCostume';
import { Cph_Common_ChangeCargoPresetIdInFleetPreset } from './common/changeCargoPresetIdInFleetPreset';
import { Cph_Dev_ReceiveProducts } from './dev/receiveProducts';
import { Cph_Ocean_DelegateNaviQueryResult } from './ocean/delegateNaviQueryResult';
import { Cph_Ocean_SweepLocalNpc } from './ocean/sweepLocalNpc';
import { Cph_Dev_SetSweepTicket } from './dev/setSweepTicket';
import { Cph_Dev_ResetExchangeHistory } from './dev/resetExchangeHistory';
import { Cph_Dev_SetVillageEvent } from './dev/setVillageEvent';
import { Cph_Common_ShipBuildingTermsInfo } from './common/shipBuildingTermsInfo';
import { Cph_Common_RemoteCreateShip } from './common/remoteCreateShip';
import { Cph_Dev_SetNationPolicy } from './dev/setNationPolicy';
import { Cph_Common_UpgradeNationPolicyStep } from './common/upgradeNationPolicyStep';
import { Cph_Dev_SetNationBudget } from './dev/setNationBudget';
import { Cph_Common_DonateToNationBudget } from './common/donateToNationBudget';
import { Cph_Dev_DeleteAllDirectMails } from './dev/deleteAllDirectMails';
import { Cph_Town_UseCanal } from './town/useCanal';
import { Cph_Dev_GetNationAccumulatedTax } from './dev/getNationAccumulatedTax';
import { Cph_Dev_SetQuestPass } from './dev/setQuestPass';
import { Cph_Common_BuyNationSupportShop } from './common/buyNationSupportShop';
import { Cph_Common_ReceiveNationSupportShopReward } from './common/receiveNationSupportShopReward';
import { Cph_Common_GetNationWeeklyDonationRanks } from './common/getNationWeeklyDonationRanks';
import { Cph_Common_ReceiveNationWeeklyDonationRankReward } from './common/receiveNationWeeklyDonationRankReward';
import { Cph_Common_EnableLastWeekInvestmentReward } from './common/enableLastWeekInvestmentReward';
import { Cph_Common_ReceiveLastWeekInvestmentReward } from './common/receiveLastWeekInvestmentReward';
import { Cph_Dev_IAmNationPrimeMinister } from './dev/iAmPrimeMinister';
import { Cph_Common_JoinToNationCabinetApplicants } from './common/joinToNationCabinetApplicants';
import { Cph_Common_AppointNationCabinetMember } from './common/appointNationCabinetMember';
import { Cph_Common_DismissNationCabinetMember } from './common/dismissNationCabinetMember';
import { Cph_Common_LoadNationCabinetApplicants } from './common/loadNationCabinetApplicants';
import { Cph_Common_SetNationWageRate } from './common/setNationWageRate';
import { Cph_Common_WriteNationNotice } from './common/writeNationNotice';
import { Cph_Dev_ResetNationCabinetLastAppointedTimes } from './dev/resetNationCabinetLastAppointedTimes';
import { Cph_Town_CostumeShipSlotItemsEquip } from './town/costumeShipSlotItemsEquip';
import { Cph_Common_WriteNationPrimeMinisterThought } from './common/writeNationPrimeMinisterThought';
import { Cph_Dev_ResetMayorRemoteInvest } from './dev/resetMayorRemoteInvest';
import { Cph_Dev_ResetRemoteInvestCount } from './dev/resetRemoteInvestCount';
import { Cph_Ocean_SweepRaid } from './ocean/sweepRaid';
import { Cph_Common_SetOptionFleetPreset } from './common/setOptionFleetPreset';
import { Cph_Common_GetOpenedFleetPresets } from './common/getOpenedFleetPresets';
import { Cph_Common_SetRepresentedMate } from './common/setRepresentedMate';
import { Cph_Common_QuestRegCurTimeUtc as Cph_Common_QuestRegUtcTime } from './common/questRegUtcTime';
import { Cph_Town_EnchantEquipment } from './town/enchantEquipment';
import { Cph_Town_EnchantShipSlot } from './town/enchantShipSlot';
import { Cph_Common_GetMarketEventInfo } from './common/getMarketEventInfo';
import { Cph_Common_SetSidekickMate } from './common/setSidekickMate';
import { Cph_Dev_AddPet } from './dev/addPet';
import { Cph_Common_SetSidekickPet } from './common/setSidekickPet';
import { Cph_Dev_ResetBlackMarketRefreshCount } from './dev/resetBlackMarketRefreshCount';
import { Cph_Common_ReceiveEventRankingGuildReward } from './common/receiveEventRankingGuildReward';
import { Cph_Raid_PickRaidBoss } from './raid/raidPickRaidBoss';
import { Cph_Ocean_SalvageGameStart } from './ocean/salvageGameStart';
import { Cph_Ocean_SalvageGameEnd } from './ocean/salvageGameEnd';
import { Cph_Ocean_SalvageEnter } from './ocean/salvageEnter';
import { Cph_Ocean_SalvageLeave } from './ocean/salvageLeave';
import { Cph_Common_SetFavoriteMate } from './common/setFavoriteMate';
import { Cph_Ocean_GetExchangeInfo } from './ocean/getExchangeInfo';
import { Cph_Town_ShipCompose } from './town/shipCompose';
import { Cph_Dev_OpenBuyableHotSpotProducts } from './dev/openBuyableHotSpotProducts';
import { Cph_Town_NpcShopBuy } from './town/npcShopBuy';
import { Cph_Town_SmuggleEnterConfirm } from './town/smuggleEnterConfirm';
import { Cph_Town_ContactNpc } from './town/contactNpc';
import { Cph_Town_AwayNpc } from './town/awayNpc';
import { Cph_Town_SmuggleGetSyncData } from './town/smuggleGetSyncData';
import { Cph_Town_SmuggleTryBeginNego } from './town/smuggleTryBeginNego';
import { Cph_Town_SmuggleTryNego } from './town/smuggleTryNego';
import { Cph_Town_SmuggleBuy } from './town/smuggleBuy';
import { Cph_Town_SmuggleSell } from './town/smuggleSell';
import { Cph_Town_SmuggleResetBought } from './town/smuggleResetBought';
import { Cph_Dev_Test } from './dev/test';
import { Cph_Common_GetMyBlindBidInven } from './common/getMyBlindBidInven';
import { Cph_Common_GetBlindBidCompletedProducts } from './common/getBlindBidCompletedProducts';
import { Cph_Common_TryBlindBid } from './common/tryBlindBid';
import { Cph_Common_ReceiveBlindBidWinnerReward } from './common/receiveBlindBidWinnerReward';
import { Cph_Common_RefundBlindBid } from './common/refundBlindBid';
import { Cph_Common_BlindBidTicketBuy } from './common/BlindBidTicketBuy';
import { Cph_Dev_ResetHotSpotProductsHistory } from './dev/resetHotSpotProductsHistory';
import { Cph_Common_MatesCompleteTranscendence } from './common/matesCompleteTranscendence';
import { Cph_Common_MateCompleteTranscendenceImmediately } from './common/mateCompleteTranscendenceImmediately';
import { Cph_Common_MateStartTranscendence } from './common/mateStartTranscendence';
import { Cph_Town_ShipyardResetEnchantCount } from './town/shipyardResetEnchantCount';
import { Cph_Battle_StartInfiniteLighthouse } from './battle/battleStartInfiniteLighthouse';
import { Cph_Battle_EndInfiniteLighthouse } from './battle/battleEndInfiniteLighthouse';
import { Cph_Common_GetInfiniteLighthouseFriendClearedInfo } from './common/getInfiniteLighthouseFriendClearedInfo';
import { Cph_Common_SweepInfiniteLighthouseStage } from './common/sweepInfiniteLighthouseStage';
import { Cph_Dev_ChangeInfiniteLighthouseClearedInfoSession } from './dev/changeInfiniteLighthouseClearedInfoSession';
import { Cph_Dev_AddAllPubMates } from './dev/addAllPubMates';
import { Cph_Dev_ClearInfiniteLighthouseStage } from './dev/clearInfinitelighthouseStage';
import { Cph_Common_GetUserFirstFleetInfos } from './common/getUserFirstFleetInfos';
import { Cph_Common_GetUserFirstFleetInfoByName } from './common/getUserFirstFleetInfoByName';
import { Cph_Dev_ResetMyFirstFleetInfo } from './dev/resetMyFirstFleetInfo';
import { Cph_Common_SetOptionfriendlyBattle } from './common/setOptionFriendlyBattle';
import { Cph_Town_FriendlyEncount } from './town/friendlyEncount';
import { Cph_Town_friendlyEncountChoice as Cph_Town_FriendlyEncountChoice } from './town/friendlyEncountChoice';
import { Cph_Battle_EndFriendly } from './battle/battleEndFriendly';
import { Cph_Town_FriendlyEncountCancel } from './town/friendlyEncountCancel';
import { Cph_Common_QueryUnpopularEvent } from './common/queryUnpopularEvent';
import { Cph_Dev_ReserveUnpopularTradeEvent } from './dev/reserveUnpopularTradeEvent';
import { Cph_Raid_GetRewardStates } from './raid/raidGetRewardStates';
import { Cph_Common_ResearchStart } from './common/researchStart';
import { Cph_Common_ResearchCancel } from './common/researchCancel';
import { Cph_Common_ResearchReset } from './common/researchReset';
import { Cph_Common_ResearchReport } from './common/researchReport';
import { Cph_Dev_DeleteUnpopularTradeEvent } from './dev/deleteUnpopularTradeEvent';
import { Cph_Dev_UpdateResearch } from './dev/updateResearch';
import { Cph_Town_ClashRegister } from './town/clashRegister';
import { Cph_Town_ClashUnregister } from './town/clashUnregister';
import { Cph_Town_ClashChoice } from './town/clashChoice';
import { Cph_Town_ClashClearRejectCooldown } from './town/clashClearRejectCooldown';
import { Cph_Common_GetClashPage } from './common/getClashPage';
import { Cph_Common_ClearReentryCooldown } from './common/clearReentryCooldown';
import { Cph_Battle_EndClash } from './battle/battleEndClash';
import { Cph_Battle_StartClash } from './battle/battleStartClash';
import { Cph_Common_ReceiveClashReward } from './common/receiveClashReward';
import { Cph_Dev_SetClashScoreAndWinStreak } from './dev/setClashScoreAndWinStreak';
import { Cph_Dev_ActiveClashBattleRecord } from './dev/activeClashBattleRecord';
import { Cph_Common_GetInvestmentSeasonRanking } from './common/getInvestmentSeasonRanking';
import { Cph_Common_GetMyInvestmentSeasonNationRankingScore } from './common/getMyInvestmentSeasonNationRankingScore';
import { Cph_Dev_GenerateTownUserWeeklyInvestmentScore } from './dev/generateTownUserWeeklyInvestmentScore';
import { Cph_Ocean_EnterContinuousSweepReward } from './ocean/enterContinuousSweepReward';
import { Cph_Ocean_LeaveContinuousSweepReward } from './ocean/leaveContinuousSweepReward';
import { Cph_Ocean_VillageGift_Paid } from './ocean/villageGiftPaid';
import { Cph_Common_CheckLastSeasonInvestmentRankRewardable } from './common/checkLastSeasonInvestmentRankRewardable';
import { Cph_Common_ReceiveLastSeasonInvestmentRankReward } from './common/receiveLastSeasonInvestmentRankReward';
import { Cph_Common_AutoChangeItemsTry } from './common/autoChangeItemsTry';
import { Cph_Common_AutoChangeItemsHistoryGet } from './common/autoChangeItemsHistoryGet';
import { Cph_Common_AutoChangeItemsHistoryReset } from './common/autoChangeItemsHistoryReset';
import { Cph_Dev_SetTutorialCrazeEventBudget } from './dev/setTutorialCrazeEventBudget';
import { Cph_Dev_TryForceAddCrazeEvent } from './dev/tryForceAddCrazeEvent';
import { Cph_Dev_SetManufactureLevel } from './dev/setManufactureLevel';
import { Cph_Manufacture_Start } from './manufacture/manufactureStart';
import { Cph_Manufacture_Receive } from './manufacture/manufactureReceive';
import { Cph_Manufacture_Decrease_Expire_Time } from './manufacture/manufactureDecreaseExpireTime';
import { Cph_Manufacture_Complete_Expire_Time } from './manufacture/manufactureCompleteExpireTime';
import { Cph_Manufacture_UnlockRecipe } from './manufacture/manufactureUnlockRecipe';
import { Cph_Manufacture_End } from './manufacture/manufactureEnd';
import { Cph_Raid_PickRaidBossCancel } from './raid/raidPickRaidBossCancel';

// ----------------------------------------------------------------------------
// 모든 패킷 핸들러는, 이 interface 를 상속 받는다.
// ----------------------------------------------------------------------------
export interface ClientPacketHandler {
  // 이 패킷을 받을수 있는 상태인지 체크.
  testGameState(user: User): boolean;

  // 로직 수행.
  exec(user: User, packet: CPacket): Promise<any>;
}

// ----------------------------------------------------------------------------
// 패킷별 핸들러 등록.
// ----------------------------------------------------------------------------

const handlers: {
  [protoId: number]: ClientPacketHandler;
} = {};

// Auth packets.
handlers[proto.Auth.ENTER_WORLD] = new Cph_Auth_EnterWorld();
handlers[proto.Auth.HELLO] = new Cph_Auth_Hello();
handlers[proto.Auth.CHANGE_NAME] = new Cph_Auth_ChangeName();
handlers[proto.Auth.COMPLETE_PROLOGUE] = new Cph_Auth_CompletePrologue();
handlers[proto.Auth.REVOKE] = new Cph_Auth_Revoke();
handlers[proto.Auth.GAME_GUARD_CHECK_CS] = new Cph_Auth_GameGuardCheck();
handlers[proto.Auth.APP_GUARD_CHECK_CS] = new Cph_Auth_AppGuardCheck();

// Town packets.
handlers[proto.Town.ENTER] = new Cph_Town_Enter();
handlers[proto.Town.LOAD_COMPLETE] = new Cph_Town_LoadComplete();
handlers[proto.Town.MOVE_CS] = new Cph_Town_Move();
handlers[proto.Town.QUERY_MATE_CS] = new Cph_Town_QueryMate();
handlers[proto.Town.ENTER_BUILDING] = new Cph_Town_EnterBuilding();
handlers[proto.Town.LEAVE_BUILDING] = new Cph_Town_LeaveBuilding();
handlers[proto.Town.BANK_DEPOSIT] = new Cph_Town_BankDeposit();
handlers[proto.Town.BANK_WITHDRAW] = new Cph_Town_BankWithdraw();
handlers[proto.Town.BANK_DEPOSIT_INSTALLMENT_SAVINGS] =
  new Cph_Town_BankDepositInstallmentSavings();
handlers[proto.Town.BANK_WITHDRAW_INSTALLMENT_SAVINGS] =
  new Cph_Town_BankWithdrawInstallmentSavings();
handlers[proto.Town.BANK_BUY_INSURANCE] = new Cph_Town_BuyInsurance();
handlers[proto.Town.DEPART_CHANGE_SHIP_FLEET_FORMATION] =
  new Cph_Town_DepartChangeShipFleetFormation();
handlers[proto.Town.DEPART_BUY_SUPPLIES] = new Cph_Town_DepartBuySupplies();
handlers[proto.Town.DEPART_DEPART] = new Cph_Town_DepartDepart();
handlers[proto.Town.SHIPYARD_CREATE_SHIP] = new Cph_Town_ShipyardCreateShip();
//handlers[proto.Town.SHIPYARD_UPGRADE_BLUEPRINT] = new Cph_Town_ShipyardUpgradeBlueprint();
handlers[proto.Town.SHIPYARD_REPAIR] = new Cph_Town_ShipyardRepair();
handlers[proto.Town.SHIPYARD_SELL_SHIP] = new Cph_Town_ShipyardSellShip();
handlers[proto.Town.SHIPYARD_CHANGE_BLUEPRINT_SLOT] = new Cph_Town_ShipyardChangeBlueprintSlot();
handlers[proto.Town.PUB_DRAFT_SAILOR] = new Cph_Town_PubDraftSailor();
handlers[proto.Town.PUB_GET_SYNC_DATA] = new Cph_Town_PubGetSyncData();
handlers[proto.Town.PUB_RECRUIT_MATE] = new Cph_Town_PubRecruitMate();
handlers[proto.Town.PUB_BUY_DRINK] = new Cph_Town_PubBuyDrink();
handlers[proto.Town.GOVER_INVEST] = new Cph_Town_GoverInvest();
handlers[proto.Town.GOVER_GET_SYNC_DATA] = new Cph_Town_GoverGetSyncData();
handlers[proto.Town.TRADE_GET_SYNC_DATA] = new Cph_Town_TradeGetSyncData();
handlers[proto.Town.TRADE_BUY] = new Cph_Town_TradeBuy();
handlers[proto.Town.TRADE_SELL] = new Cph_Town_TradeSell();
handlers[proto.Town.TRADE_GET_ALL_SESSION_PRICE_PERCENTS] =
  new Cph_Town_TradeGetAllSessionPricePercents();
handlers[proto.Town.RELIGION_UPDATE_BUFF] = new Cph_Town_ReligionUpdateBuff();
handlers[proto.Town.SHOP_BUY_EX] = new Cph_Town_ShopBuyEx();
handlers[proto.Town.SHOP_GET_TOWN_BLACK_MARKET] = new Cph_Town_ShopGetTownBlackMarket();
handlers[proto.Town.SHOP_SELL] = new Cph_Town_ShopSell();
handlers[proto.Town.SHOP_RESET_TOWN_BLACK_MARKET] = new Cph_Town_ShopResetTownBlackMarket();
handlers[proto.Town.RECEIVE_INSURANCE] = new Cph_Town_ReceiveInsurance();
handlers[proto.Town.CHANGE_SHIP_SLOT] = new Cph_Town_ChangeShipSlot();
handlers[proto.Town.CHANGE_SHIP_SLOTS] = new Cph_Town_ChangeShipSlots();
handlers[proto.Town.UNION_RESET_REQUEST] = new Cph_Town_UnionResetRequest();
handlers[proto.Town.PUB_GIFT] = new Cph_Town_PubGift();
handlers[proto.Town.TRADE_TRY_BEGIN_NEGO] = new Cph_Town_TradeTryBeginNego();
handlers[proto.Town.TRADE_TRY_NEGO] = new Cph_Town_TradeTryNego();
handlers[proto.Town.NOVICE_SUPPLY] = new Cph_Town_NoviceSupply();
handlers[proto.Town.PALACE_GET_ROYAL_ORDER] = new Cph_Town_PalaceGetRoyalOrder();
handlers[proto.Town.PALACE_REJECT_ROYAL_ORDER] = new Cph_Town_PalaceRejectRoyalOrder();
handlers[proto.Town.MANTIC_FORTUNE] = new Cph_Town_ManticFortune();
handlers[proto.Town.ARREST_USER_CHOICE] = new Cph_Town_ArrestUserChoice();
handlers[proto.Town.COLLECTOR_REPORT_DISCOVERIES] = new Cph_Town_CollectorReportDiscoveries();
handlers[proto.Town.COLLECTOR_CONTRACT] = new Cph_CollectorContract();
handlers[proto.Town.COLLECTOR_GET_RANK] = new Cph_Town_CollectorGetRank();
handlers[proto.Town.TRADE_RESET_BOUGHT] = new Cph_Town_TradeResetBought();
handlers[proto.Town.COLLECTOR_REPORT_WORLD_MAP_TILES] = new Cph_Town_CollectorReportWorldMapTiles();
handlers[proto.Town.SHIPYARD_BUY] = new Cph_Town_ShipyardBuy();
handlers[proto.Town.SHIPYARD_ENCHANT] = new Cph_Town_ShipyardEnchant();
handlers[proto.Town.SHIPYARD_CHOICE_ENCHANT_RESULT] = new Cph_Town_ShipyardChoiceEnchantResult();
handlers[proto.Town.SHIPYARD_DISMANTLE_SHIP] = new Cph_Town_ShipyardDismantleShip();
handlers[proto.Town.UPDATE_SOCIAL_ANI_PERSIST_CS] = new Cph_Town_SocialAniPersist();
handlers[proto.Town.SHOW_SOCIAL_ANI_INSTANT_CS] = new Cph_Town_ShowSocialAniInstant();
handlers[proto.Town.SHOW_EMOTICON_INSTANT_CS] = new Cph_Town_ShowEmoticonInstant();
handlers[proto.Town.MANTIC_SWAP_PIECE] = new Cph_Town_ManticSwapPiece();
handlers[proto.Town.GOVER_CHANGE_MAYOR_TAX] = new Cph_Town_GoverChangeMayorTax();
handlers[proto.Town.PUB_STAFF_NOMINATION] = new Cph_Town_PubStaffNomination();
handlers[proto.Town.PUB_STAFF_TALKING] = new Cph_Town_PubStaffTalking();
handlers[proto.Town.PUB_STAFF_BOASTING] = new Cph_Town_PubStaffBoasting();
handlers[proto.Town.PUB_STAFF_GIFT] = new Cph_Town_PubStaffGift();
handlers[proto.Town.PUB_STAFF_CALL] = new Cph_Town_PubStaffCall();
handlers[proto.Town.PUB_STAFF_GET_SYNC_DATA] = new Cph_Town_PubStaffGetSyncData();
handlers[proto.Town.GOVER_GET_LAST_WEEK_RANK] = new Cph_Town_GoverGetLastWeekRank();
handlers[proto.Town.PUB_REDUCE_RECRUIT_NEGO_WAIT_TIME] =
  new Cph_Town_PubReduceRecruitNegoWaitTime();
handlers[proto.Town.PUB_RESET_RECRUIT_NEGO_WAIT_TIME] = new Cph_Town_PubResetRecruitNegoWaitTime();
handlers[proto.Town.SHIPYARD_RECEIVE_SHIP] = new Cph_Town_ShipyardReceiveShip();
handlers[proto.Town.PUB_SPONSOR_MY_MATE] = new Cph_Town_PubSponsorMyMate();
handlers[proto.Town.PUB_REDUCE_MY_MATE_SPONSOR_WAIT_TIME] =
  new Cph_Town_PubReduceMyMateSponsorWaitTime();
handlers[proto.Town.PUB_RESET_MY_MATE_SPONSOR_WAIT_TIME] =
  new Cph_Town_PubResetMyMateSponsorWaitTime();
handlers[proto.Town.SHIPYARD_VERIFY_TOW_SHIP] = new Cph_Town_ShipyardVerifyTowShip();
handlers[proto.Town.SHIPYARD_REPAIR_LIFE_SHIP] = new Cph_Town_ShipyardRepairLifeShip();
handlers[proto.Town.PALACE_GET_CONTRIBUTION_SHOP_SYNC_DATA] =
  new Cph_Town_PalaceGetContributionShopSyncData();
handlers[proto.Town.PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT] =
  new Cph_Town_PalaceBuyContributionShopProduct();
handlers[proto.Town.UNION_RESET_EVENT_REQUEST] = new Cph_Town_UnionResetEventRequest();
handlers[proto.Town.SHIP_SLOT_ITEMS_EQUIP] = new Cph_Town_ShipSlotItemsEquip();
handlers[proto.Town.TRADE_RECEIVE_TRADE_AREA_REWARD] = new Cph_Town_TradeAreaRewardReceive();
handlers[proto.Town.SHIPYARD_RELAUNCH_SHIP] = new Cph_Town_ShipyardRelaunchShip();
handlers[proto.Town.PUB_RESET_MATES_BY_PAID] = new Cph_Town_PubResetMatesByPaid();
handlers[proto.Town.PALACE_GET_ROYAL_TITLE_ORDER] = new Cph_Town_PalaceGetRoyalTitleOrder();
handlers[proto.Town.GOVER_CHANGE_MAYOR_SHIPYARD_TAX] = new Cph_Town_GoverChangeMayorShipyardTax();
handlers[proto.Town.CHANGE_SHIP_CAMOUFLAGE] = new Cph_Town_ChangeShipCamouflage();
handlers[proto.Town.SHIPYARD_BUY_IMPROVED_BLUEPRINT] = new Cph_Town_ShipyardBuyImprovedBlueprint();
handlers[proto.Town.GOVER_REGISTER_MAYOR_TRADE_EVENT] = new Cph_Town_GoverRegisterMayorTradeEvent();
handlers[proto.Town.USE_CANAL] = new Cph_Town_UseCanal();
handlers[proto.Town.COSTUME_SHIP_SLOT_ITEMS_EQUIP] = new Cph_Town_CostumeShipSlotItemsEquip();
handlers[proto.Town.ENCHANT_EQUIPMENT] = new Cph_Town_EnchantEquipment();
handlers[proto.Town.ENCHANT_SHIP_SLOT] = new Cph_Town_EnchantShipSlot();
handlers[proto.Town.SHIP_COMPOSE] = new Cph_Town_ShipCompose();
handlers[proto.Town.NPC_SHOP_BUY] = new Cph_Town_NpcShopBuy();
handlers[proto.Town.SMUGGLE_ENTER_CONFIRM] = new Cph_Town_SmuggleEnterConfirm();
handlers[proto.Town.CONTACT_NPC] = new Cph_Town_ContactNpc();
handlers[proto.Town.AWAY_NPC] = new Cph_Town_AwayNpc();
handlers[proto.Town.SMUGGLE_GET_SYNC_DATA] = new Cph_Town_SmuggleGetSyncData();
handlers[proto.Town.SMUGGLE_TRY_BEGIN_NEGO] = new Cph_Town_SmuggleTryBeginNego();
handlers[proto.Town.SMUGGLE_TRY_NEGO] = new Cph_Town_SmuggleTryNego();
handlers[proto.Town.SMUGGLE_BUY] = new Cph_Town_SmuggleBuy();
handlers[proto.Town.SMUGGLE_SELL] = new Cph_Town_SmuggleSell();
handlers[proto.Town.SMUGGLE_RESET_BOUGHT] = new Cph_Town_SmuggleResetBought();
handlers[proto.Town.SHIPYARD_RESET_ENCHANT_COUNT] = new Cph_Town_ShipyardResetEnchantCount();
handlers[proto.Town.FRIENDLY_ENCOUNT] = new Cph_Town_FriendlyEncount();
handlers[proto.Town.FRIENDLY_ENCOUNT_CHOICE] = new Cph_Town_FriendlyEncountChoice();
handlers[proto.Town.FRIENDLY_ENCOUNT_CANCEL] = new Cph_Town_FriendlyEncountCancel();
handlers[proto.Town.CLASH_REGISTER] = new Cph_Town_ClashRegister();
handlers[proto.Town.CLASH_UNREGISTER] = new Cph_Town_ClashUnregister();
handlers[proto.Town.CLASH_CHOICE] = new Cph_Town_ClashChoice();
handlers[proto.Town.CLASH_CLEAR_REJECT_COOL_DOWN] = new Cph_Town_ClashClearRejectCooldown();

// Ocean packets.
handlers[proto.Ocean.ARRIVE] = new Cph_Ocean_Arrive();
handlers[proto.Ocean.LOAD_COMPLETE] = new Cph_Ocean_LoadComplete();
handlers[proto.Ocean.ENTER] = new Cph_Ocean_Enter();
handlers[proto.Ocean.MOVE_CS] = new Cph_Ocean_Move();
handlers[proto.Ocean.INSPECT_TOWN] = new Cph_Ocean_InspectTown();
handlers[proto.Ocean.BEGIN_AUTO_SAILING] = new Cph_Ocean_BeginAutoSailing();
handlers[proto.Ocean.END_AUTO_SAILING] = new Cph_Ocean_EndAutoSailing();
handlers[proto.Ocean.UPDATE_AUTO_SAILING] = new Cph_Ocean_UpdateAutoSailing();
handlers[proto.Ocean.RESOLVE_DISASTER] = new Cph_Ocean_ResolveDisaster();
handlers[proto.Ocean.ENCOUNT_USER_CHOICE] = new Cph_Ocean_EncountUserChoice();
handlers[proto.Ocean.ENCOUNT_BY_USER] = new Cph_Ocean_EncountByUser();
handlers[proto.Ocean.EMERGENCY_RECOVER_SHIPS] = new Cph_Ocean_EmergencyRecoverShips();
handlers[proto.Ocean.GAME_OVER_RESURRECT] = new Cph_Ocean_GameOverResurrect();
handlers[proto.Ocean.GET_USER_TOWN_STATE] = new Cph_Ocean_GetUserTownState();
handlers[proto.Ocean.ENCOUNT_END] = new Cph_Ocean_EncountEnd();
handlers[proto.Ocean.ZOOM_IN_NPC] = new Cph_Ocean_ZoomInNpc();
handlers[proto.Ocean.ZOOM_IN_USER] = new Cph_Ocean_ZoomInUser();
handlers[proto.Ocean.REVEAL_WORLD_MAP_TILE] = new Cph_Ocean_RevealWorldMapTile();
handlers[proto.Ocean.DISCOVER_VILLAGE] = new Cph_Ocean_DiscoverVillage();
handlers[proto.Ocean.ADD_QUESTION_PLACE] = new Cph_Ocean_AddQuestionPlace();
handlers[proto.Ocean.ACTIVATE_OCEAN_DOODAD] = new Cph_Ocean_ActivateOceanDoodad();
handlers[proto.Ocean.LAND_ENTER] = new Cph_Ocean_LandEnter();
handlers[proto.Ocean.LAND_LEAVE] = new Cph_Ocean_LandLeave();
handlers[proto.Ocean.LAND_EXPLORE] = new Cph_Ocean_LandExplore();
handlers[proto.Ocean.LAND_START_CS] = new Cph_Ocean_LandStart();
handlers[proto.Ocean.INSPECT_VILLAGE] = new Cph_Ocean_InspectVillage();
handlers[proto.Ocean.VILLAGE_ENTER] = new Cph_Ocean_VillageEnter();
handlers[proto.Ocean.VILLAGE_LEAVE] = new Cph_Ocean_VillageLeave();
handlers[proto.Ocean.VILLAGE_GIFT] = new Cph_Ocean_VillageGift();
handlers[proto.Ocean.VILLAGE_DRAFT_SAILOR] = new Cph_Ocean_VillageDraftSailor();
handlers[proto.Ocean.VILLAGE_PLUNDER] = new Cph_Ocean_VillagePlunder();
handlers[proto.Ocean.VILLAGE_EXPLORE] = new Cph_Ocean_VillageExplore();
handlers[proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START] =
  new Cph_Ocean_OfflineSailingMoveDelegateStart();

handlers[proto.Ocean.SALVAGE_ENTER] = new Cph_Ocean_SalvageEnter();
handlers[proto.Ocean.SALVAGE_GAME_START] = new Cph_Ocean_SalvageGameStart();
handlers[proto.Ocean.SALVAGE_GAME_END] = new Cph_Ocean_SalvageGameEnd();
handlers[proto.Ocean.SALVAGE_LEAVE] = new Cph_Ocean_SalvageLeave();

handlers[proto.Ocean.SHOW_EMOTICON_INSTANT_CS] = new Cph_Ocean_ShowEmoticonInstant();
handlers[proto.Ocean.FISHING_START] = new Cph_Ocean_FishingStart();
handlers[proto.Ocean.FISHING_FIGHTING] = new Cph_Ocean_FishingFighting();
handlers[proto.Ocean.FISHING_END] = new Cph_Ocean_FishingEnd();
handlers[proto.Ocean.ITEM_RESURRECT] = new Cph_Ocean_ItemResurrect();
handlers[proto.Ocean.REVEAL_REGION] = new Cph_Ocean_RevealRegion();
handlers[proto.Ocean.REVEAL_OCEAN_DOODADS] = new Cph_Ocean_RevealOceanDoodads();
handlers[proto.Ocean.DISCOVER_OCEAN_DOODAD] = new Cph_Ocean_DiscoverOceanDoodad();
handlers[proto.Ocean.LAND_EXPLORE_QUERY_FEATURE] = new Cph_Ocean_LandExploreQueryFeature();
handlers[proto.Ocean.APPLY_WAYPOINT_SUPPLY] = new Cph_Ocean_ApplyWaypointSupply();
handlers[proto.Ocean.UPDATE_AUTO_SAIL_OPTION_FOR_SERVER] =
  new Cph_Ocean_UpdateAutoSailOptionForServer();
handlers[proto.Ocean.SEARCH_CONSTELLATION] = new Cph_Ocean_SearchConstellation();
handlers[proto.Ocean.DISCOVER_CONSTELLATION] = new Cph_Ocean_DiscoverConstellation();

handlers[proto.Ocean.VILLAGE_EXCHANGE] = new Cph_Ocean_Village_Exchange();
handlers[proto.Ocean.VILLAGE_EXCHANGE_TRY_NEGO] = new Cph_Ocean_Village_Exchange_Try_Nego();
handlers[proto.Ocean.VILLAGE_FRIENDSHIP_REWARD] = new Cph_Ocean_Village_Friendship_Reward();
handlers[proto.Ocean.DELEGATE_NAVI_QUERY_RESULT] = new Cph_Ocean_DelegateNaviQueryResult();
handlers[proto.Ocean.SWEEP_LOCAL_NPC] = new Cph_Ocean_SweepLocalNpc();
handlers[proto.Ocean.SWEEP_RAID] = new Cph_Ocean_SweepRaid();

handlers[proto.Ocean.GET_EXCHANGE_INFO] = new Cph_Ocean_GetExchangeInfo();

handlers[proto.Ocean.ENTER_CONTINUOUS_SWEEP_REWARD] = new Cph_Ocean_EnterContinuousSweepReward();
handlers[proto.Ocean.LEAVE_CONTINUOUS_SWEEP_REWARD] = new Cph_Ocean_LeaveContinuousSweepReward();

handlers[proto.Ocean.VILLAGE_GIFT_PAID] = new Cph_Ocean_VillageGift_Paid();

// Battle packets.
handlers[proto.Battle.ACTION] = new Cph_Battle_Action();
handlers[proto.Battle.END] = new Cph_Battle_End();
handlers[proto.Battle.END_RAID] = new Cph_Battle_EndRaid();
handlers[proto.Battle.END_CHALLENGE] = new Cph_Battle_EndChallenge();
handlers[proto.Battle.LOAD_COMPLETE] = new Cph_Battle_LoadComplete();
handlers[proto.Battle.RESUME] = new Cph_Battle_Resume();
handlers[proto.Battle.START] = new Cph_Battle_Start();
handlers[proto.Battle.START_CHALLENGE] = new Cph_Battle_StartChallenge();
handlers[proto.Battle.LOSE] = new Cph_Battle_Lose();
handlers[proto.Battle.START_ARENA] = new Cph_Battle_StartArena();
handlers[proto.Battle.CANCEL] = new Cph_Battle_Cancel();
handlers[proto.Battle.QUEST_RESUME_BLOCK] = new Cph_Battle_Quest_Resume_Block();
handlers[proto.Battle.START_RAID] = new Cph_Battle_StartRaid();
handlers[proto.Battle.MULTI_ACTION_USER_CHANGE_CS] = new Cph_Battle_MultiActionUserChange();
handlers[proto.Battle.START_GUILD_RAID] = new Cph_Battle_StartGuildRaid();
handlers[proto.Battle.END_MULTI_PVP] = new Cph_Battle_EndMultiPvp();
handlers[proto.Battle.MULTI_SYNC_INIT] = new Cph_Battle_MultiSyncInit();
handlers[proto.Battle.MULTI_CTRL_REQUEST] = new Cph_Battle_MultiCtrlRequest();
handlers[proto.Battle.MULTI_AUTO_CHANGE_REQUEST_CS] = new Cph_Battle_MultiAutoChange();
handlers[proto.Battle.MULTI_ACTION_REQUEST] = new Cph_Battle_MultiActionRequest();
handlers[proto.Battle.MULTI_TIMEOUT_NOTIFICATION] = new Cph_Battle_MultiTimeoutNotification();
handlers[proto.Battle.MULTI_ACTION_PHASE_CONFIRM_CS] = new Cph_Battle_MultiActionPhaseConfirm();
handlers[proto.Battle.MULTI_GIVE_UP_CS] = new Cph_Battle_MultiGiveUp();
handlers[proto.Battle.MULTI_ACTION_PHASE_PASS_CS] = new Cph_Battle_MultiActionPhasePass();
handlers[proto.Battle.START_INFINITE_LIGHT_HOUSE] = new Cph_Battle_StartInfiniteLighthouse();
handlers[proto.Battle.END_INFINITE_LIGHT_HOUSE] = new Cph_Battle_EndInfiniteLighthouse();
handlers[proto.Battle.END_FRIENDLY] = new Cph_Battle_EndFriendly();
handlers[proto.Battle.END_CLASH] = new Cph_Battle_EndClash();
handlers[proto.Battle.START_CLASH] = new Cph_Battle_StartClash();

// Duel packets.
handlers[proto.Duel.START] = new Cph_Duel_Start();
handlers[proto.Duel.ACTION] = new Cph_Duel_Action();
handlers[proto.Duel.END] = new Cph_Duel_End();
handlers[proto.Duel.RESUME] = new Cph_Duel_Resume();

// Common packets.
handlers[proto.Common.CREATE_FIRST_MATE] = new Cph_Common_CreateFirstMate();
handlers[proto.Common.EQUIP_MATE_EQUIPMENT] = new Cph_Common_EquipMateEquipment();
handlers[proto.Common.QUERY_NATION] = new Cph_Common_QueryNation();
handlers[proto.Common.SELECT_NATION] = new Cph_Common_SelectNation();
handlers[proto.Common.CHANGE_NATION] = new Cph_Common_ChangeNation();
handlers[proto.Common.GET_USER_LIGHT_INFOS] = new Cph_Common_GetUserLightInfos();
handlers[proto.Common.CHANGE_COMPANY_JOB] = new Cph_Common_ChangeCompanyJob();
handlers[proto.Common.CHANGE_SHIP_NAME] = new Cph_Common_ChangeShipName();
handlers[proto.Common.RELOCATE_SAILOR] = new Cph_Common_RelocateSailor();
handlers[proto.Common.REMOVE_ITEM] = new Cph_Common_RemoveItem();
handlers[proto.Common.REMOVE_MATE_EQUIPMENT] = new Cph_Common_RemoveMateEquipment();
handlers[proto.Common.EXPAND_INVENTORY_SLOT] = new Cph_Common_ExpandInventorySlot();
handlers[proto.Common.RECEIVE_MAILS] = new Cph_Common_ReceiveMails();
handlers[proto.Common.QUEST_ACCEPT] = new Cph_Common_QuestAccept();
handlers[proto.Common.QUEST_START_BLOCK] = new Cph_Common_QuestStartBlock();
handlers[proto.Common.QUEST_ENTRUST] = new Cph_Common_QuestEntrust();
handlers[proto.Common.QUEST_END_BLOCK] = new Cph_Common_QuestEndBlock();
handlers[proto.Common.QUEST_REG_STORE] = new Cph_Common_QuestRegStore();
handlers[proto.Common.DELETE_MAILS] = new Cph_Common_DeleteMails();
handlers[proto.Common.READ_MAILS] = new Cph_Common_ReadMails();
handlers[proto.Common.REMOVE_CARGO] = new Cph_Common_RemoveCargo();
handlers[proto.Common.RELOAD_CARGO] = new Cph_Common_ReloadCargo();
handlers[proto.Common.GET_TOWN_TRADE_PRICE_PERCENTS] = new Cph_Common_GetTownTradePricePercents();
handlers[proto.Common.QUEST_DROP] = new Cph_Common_QuestDrop();
handlers[proto.Common.USE_ITEM] = new Cph_Common_UseItem();
handlers[proto.Common.QUEST_GOTO] = new Cph_Common_QuestGoto();
handlers[proto.Common.LOCK_SHIP_SLOTS] = new Cph_Common_LockShipSlots();
handlers[proto.Common.EQUIP_MATE_EQUIPMENTS] = new Cph_Common_EquipMateEquipments();
handlers[proto.Common.BUY_ADMIRAL] = new Cph_Common_BuyAdmiral();
handlers[proto.Common.RECOVER_INJURY] = new Cph_Common_RecoverInjury();
handlers[proto.Common.INCREASE_ROYALTY] = new Cph_Common_IncreaseLoyalty();
handlers[proto.Common.MEET_MATES] = new Cph_Common_MeetMates();
handlers[proto.Common.TALK_MATES] = new Cph_Common_TalkMates();
handlers[proto.Common.SET_CARGO_LOAD_PRESET] = new Cph_Common_SetCargoLoadPreset();
handlers[proto.Common.QUEST_OPERATE_REG] = new Cph_Common_QuestOperateReg();
handlers[proto.Common.RECOVER_SHIPS] = new Cph_Common_RecoverShips();
handlers[proto.Common.RESET_TASK] = new Cph_Common_ResetTask();
handlers[proto.Common.RECEIVE_ACHIEVEMENT_REWARD] = new Cph_Common_ReceiveAchievementReward();
handlers[proto.Common.RECEIVE_TASK_REWARD] = new Cph_Common_ReceiveTaskReward();
handlers[proto.Common.GET_TOWN_NATION_SHARE_POINTS] = new Cph_Common_GetTownNationSharePoints();
handlers[proto.Common.COMPLETE_TASK_IMMEDIATELY] = new Cph_Common_CompleteTaskImmediately();
handlers[proto.Common.RECEIVE_TASK_CATEGORY_REWARD] = new Cph_Common_ReceiveTaskCategoryReward();
handlers[proto.Common.RECEIVE_ACHIEVEMENT_POINT_REWARD] =
  new Cph_Common_ReceiveAchievementPointReward();
handlers[proto.Common.RECEIVE_HOT_TIME] = new Cph_Common_ReceiveHotTime();
handlers[proto.Common.BUY_TAX_FREE_PERMIT] = new Cph_Common_BuyTaxFreePermit();
handlers[proto.Common.GET_WORLD_MAP_TOWN_INFO] = new Cph_Common_GetWorldMapTownInfo();
handlers[proto.Common.CONSUME_QUEST_ENERGY] = new Cph_Common_ConsumeQuestEnergy();
handlers[proto.Common.EXPAND_REQUEST_SLOT] = new Cph_Common_ExpandRequestSlot();
handlers[proto.Common.GET_NATION_TOWNS] = new Cph_Common_GetNationTowns();
handlers[proto.Common.REMOVE_EXPIRED_REQUEST_SLOT] = new Cph_Common_RemoveExpiredRequestSlot();
handlers[proto.Common.CASH_SHOP_GET_PRODUCTS] = new Cph_Common_CashShopGetProducts();
handlers[proto.Common.CASH_SHOP_BUY_WITHOUT_PURCHASE] = new Cph_Common_CashShopBuyWithoutPurchase();
handlers[proto.Common.GET_REGION_OCCUPATIONS] = new Cph_Common_GetRegionOccupations();
handlers[proto.Common.CASH_SHOP_RECEIVE_GACHA_BOX_GUARANTEE] =
  new Cph_Common_CashShopReceiveGachaBoxGuarantee();
handlers[proto.Common.GET_USER_LIGHT_INFO_BY_NAME] = new Cph_Common_GetUserLightInfoByName();
handlers[proto.Common.GET_USER_LIGHT_INFOS_ONLY_IS_ONLINE] =
  new Cph_Common_GetUserLightInfosOnlyIsOnline();
handlers[proto.Common.UNLOCK_SHIP_CUSTOMIZING] = new Cph_Common_UnlockShipCustomizing();
handlers[proto.Common.CUSTOMIZE_SHIP] = new Cph_Common_CustomizeShip();
handlers[proto.Common.CHAT_INIT] = new Cph_Common_ChatInit();
handlers[proto.Common.CHAT_JOIN_CHANNEL] = new Cph_Common_ChatJoinChannel();
handlers[proto.Common.MATE_START_AWAKEN] = new Cph_Common_MateStartAwaken();
handlers[proto.Common.MATE_START_LEARN_PASSIVE] = new Cph_Common_MateStartLearnPassive();
handlers[proto.Common.QUEST_ITEM_SET_QUEST] = new Cph_Common_QuestItemSetQuest();
handlers[proto.Common.QUEST_ITEM_USE] = new Cph_Common_QuestItemUse();
handlers[proto.Common.ATTENDANCE] = new Cph_Common_Attendance();
handlers[proto.Common.MATE_EQUIP_PASSIVES] = new Cph_Common_MateEquipPassives();
handlers[proto.Common.QUEST_SET_PAUSE] = new Cph_Common_QuestSetPause();
handlers[proto.Common.CUSTOMIZE_MATE_EQUIP] = new Cph_Common_CustomizeMateEquip();
handlers[proto.Common.UNLOCK_MATE_EQUIP_COLOR] = new Cph_Common_UnlockMateEquipColor();
handlers[proto.Common.BUBBLE_EVENT_ACTION] = new Cph_Common_BubbleEventAction();
handlers[proto.Common.SHIP_SLOT_ITEM_SELL] = new Cph_Common_ShipSlotItemSell();
handlers[proto.Common.SHIP_SLOT_ITEM_REMOVE] = new Cph_Common_ShipSlotItemRemove();
// handlers[proto.Common.QUEST_ITEM_DROP] = new Cph_Common_QuestItemDrop();
handlers[proto.Common.SET_GAME_OPTION_PUSH_NOTIFICATION_CS] =
  new Cph_Common_SetGameOptionPushNotification();
handlers[proto.Common.GET_GAME_OPTION_PUSH_NOTIFICATION] =
  new Cph_Common_GetGameOptionPushNotification();
handlers[proto.Common.TOOGLE_ENCOUNT_SHIELD_ACTIVATION] =
  new Cph_Common_ToogleEncountShieldActivation();
handlers[proto.Common.BATTLE_FORMATION_ACQUIRE] = new Cph_Common_BattleFormationAcquire();
handlers[proto.Common.BATTLE_FORMATION_CHANGE] = new Cph_Common_BattleFormationChange();
handlers[proto.Common.QUERY_NEAREST_TOWN] = new Cph_Common_QueryNearestTown();
handlers[proto.Common.USE_WORLD_SKILL] = new Cph_Common_UserWorldSkill();
handlers[proto.Common.REGISTER_COLLECTION] = new Cph_Common_RegisterCollection();
handlers[proto.Common.SHIPYARD_SHIP_BUILDING_DECREASE_EXPIRE_TIME] =
  new Cph_Common_ShipBuildingDecreaseExpireTime();
handlers[proto.Common.SHIPYARD_SHIP_BUILDING_COMPLETE_EXPIRE_TIME] =
  new Cph_Common_ShipBuildingCompleteExpireTime();
handlers[proto.Common.SHIPYARD_SHIP_BUILDING_DELIVER] = new Cph_Common_ShipBuildingDeliver();
handlers[proto.Common.RECEIVE_EVENT_MISSION_REWARD] = new Cph_Common_ReceiveEventMissionReward();
handlers[proto.Common.REFRESH_WEEKLY_EVENT] = new Cph_Common_RefreshWeeklyEvent();
handlers[proto.Common.MATE_COMPLETE_AWAKEN] = new Cph_Common_MateCompleteAwaken();
handlers[proto.Common.MATE_COMPLETE_AWAKEN_IMMEDIATELY] =
  new Cph_Common_MateCompleteAwakenImmediately();
handlers[proto.Common.MATE_REDUCE_AWAKEN_TIME] = new Cph_Common_MateReduceAwakenTime();
handlers[proto.Common.FIND_DISTANCE_FROM_TILE_TO_TOWN] =
  new Cph_Common_FindDistanceFromTileToTown();
handlers[proto.Common.GET_ADMIRAL_PROFILE] = new Cph_Common_GetAdmiralProfile();
handlers[proto.Common.GET_FLAG_SHIP_PROFILE] = new Cph_Common_GetFlagShipProfile();
handlers[proto.Common.SET_OPTION_PROFILES] = new Cph_Common_SetOptionProfiles();
handlers[proto.Common.MATE_COMPLETE_LEARN_PASSIVE] = new Cph_Common_MateCompleteLearnPassive();
handlers[proto.Common.MATE_COMPLETE_LEARN_PASSIVE_IMMEDIATELY] =
  new Cph_Common_MateCompleteLearnPassiveImmediately();
handlers[proto.Common.MATE_REDUCE_LEARN_PASSIVE_TIME] = new Cph_Common_MateReduceLearnPassiveTime();
handlers[proto.Common.AUCTION_LOAD_MY_PRODUCTS] = new Cph_Common_AuctionLoadProducts();
handlers[proto.Common.AUCTION_REGISTER] = new Cph_Common_AuctionRegister();
handlers[proto.Common.AUCTION_CANCEL] = new Cph_Common_AuctionCancel();
handlers[proto.Common.AUCTION_QUERY_SALE_PRICES] = new Cph_Common_AuctionQuerySalePrices();
handlers[proto.Common.AUCTION_REREGISTER] = new Cph_Common_AuctionReregister();
handlers[proto.Common.AUCTION_RECEIVE_PROCEEDS] = new Cph_Common_AuctionReceiveProceeds();
handlers[proto.Common.AUCTION_QUERY_PRODUCTS] = new Cph_Common_AuctionQueryProducts();
handlers[proto.Common.AUCTION_BUY] = new Cph_Common_AuctionBuy();
handlers[proto.Common.UPDATE_ADJUTANT_DELEGATION_CONFIG] =
  new Cph_Common_UpdateAdjutantDelegationConfig();
handlers[proto.Common.AUCTION_QUERY_SHIP_PRODUCTS] = new Cph_Common_AuctionQueryShipProducts();
handlers[proto.Common.BILLING_QUERY_SALES_LIST] = new Cph_Common_BillingQuerySalesList();
handlers[proto.Common.BILLING_RESERVE_PURCHASE] = new Cph_Common_BillingReservePurchase();
handlers[proto.Common.BILLING_CANCEL_RESERVED_PURCHASE] =
  new Cph_Common_BillingCancelReservedPurchase();
handlers[proto.Common.BILLING_COMPLETE_RESERVED_PURCHASE_AND_GIVE] =
  new Cph_Common_BillingCompleteReservedPurchaseAndGive();
handlers[proto.Common.BILLING_QUERY_PURCHASE_DETAIL] = new Cph_Common_BillingQueryPurchaseDetail();
handlers[proto.Common.BILLING_QUERY_PRODUCT_GIVE_ITEM_DETAIL] =
  new Cph_Common_BillingQueryProductGiveItemDetail();
handlers[proto.Common.BILLING_CHARGE_BY_PURCHASE_PRODUCT] =
  new Cph_Common_BillingChargeByPurchaseProduct();
handlers[proto.Common.SET_PING_TIME_OUT] = new Cph_Common_SetPingTimeout();
handlers[proto.Common.ARENA_ENTER] = new Cph_Common_ArenaEnter();
handlers[proto.Common.ARENA_MATCH_LIST_REFRESH] = new Cph_Common_ArenaMatchListRefresh();
handlers[proto.Common.ARENA_TICKET_BUY] = new Cph_Common_ArenaTicketBuy();
handlers[proto.Common.ARENA_REWARD_RECEIVE] = new Cph_Common_ArenaRewardReceive();
handlers[proto.Common.ARENA_FLEET_UPDATE] = new Cph_Common_ArenaFleetUpdate();
handlers[proto.Common.QUERY_MY_MAYOR_TOWNS] = new Cph_Common_QueryMyMayorTowns();
handlers[proto.Common.UPDATE_SHIELDS] = new Cph_Common_UpdateShields();
handlers[proto.Common.QUERY_VILLAGE] = new Cph_Common_QueryVillage();
handlers[proto.Common.PROLOGUE_START] = new Cph_Common_PrologueStart();
handlers[proto.Common.INCREASE_LOYALTY_USE_ITEM] = new Cph_Common_IncreaseLoyaltyUseItem();
handlers[proto.Common.QUERY_REPORTED_WORLD_MAP_TILES] = new Cph_Common_QueryReportedWorldMapTiles();
handlers[proto.Common.CASH_SHOP_BUY_DAILY_PRODUCT] = new Cph_Common_CashShopBuyDailyProduct();
handlers[proto.Common.QUERY_ACHIEVEMENTS] = new Cph_Common_QueryAchievements();
handlers[proto.Common.CASH_SHOP_GET_DAILY_SALE] = new Cph_Common_CashShopGetDailySale();
handlers[proto.Common.QUERY_RED_GEM_DETAIL] = new Cph_Common_QueryRedGemDetail();
handlers[proto.Common.AUCTION_QUERY_CLOSED] = new Cph_Common_AuctionQueryClosed();
handlers[proto.Common.QUERY_LOCKED_TOWN_TRADE_GOODS] =
  new Cph_Common_QUERY_LOCKED_TOWN_TRADE_GOODS();
handlers[proto.Common.RECEIVE_PASS_EVENT_MISSION_REWARD] =
  new Cph_Common_ReceivePassEventMissionReward();
handlers[proto.Common.BUY_PASS_EVENT_EXP] = new Cph_Common_BuyPassEventExp();
handlers[proto.Common.BILLING_COMPLETE_RESERVED_PURCHASE] =
  new Cph_Common_BillingCompleteReservedPurchase();
handlers[proto.Common.BILLING_QUERY_INVEN_PURCHASE_LIST] =
  new Cph_Common_BillingQueryInvenPurchaseList();
handlers[proto.Common.BILLING_RECEIVE_INVEN_PURCHASES] =
  new Cph_Common_BillingReceiveInvenPurchases();
handlers[proto.Common.EVENT_SHOP_BUY] = new Cph_Common_EventShopBuy();
handlers[proto.Common.EVENT_SHOP_GET_PRODUCTS] = new Cph_Common_EventShopGetProducts();
handlers[proto.Common.BILLING_QUERY_LATEST_RESERVED_PURCHASE] =
  new Cph_Common_BillingQueryLatestReservedPurchase();
handlers[proto.Common.REPORT_BAD_CHATTING] = new Cph_Common_ReportBadChatting();
handlers[proto.Common.GET_REPORTED_BAD_CHATTING_LIST] = new Cph_Common_GetReportedBadChattingList();
handlers[proto.Common.CHAT_MUTE_USER] = new Cph_Common_ChatMuteUser();
handlers[proto.Common.CHAT_UNMUTE_USER] = new Cph_Common_ChatUnmuteUser();
handlers[proto.Common.SET_SAIL_WAYPOINT] = new Cph_Common_SetSailWaypoint();
handlers[proto.Common.QUERY_ALL_TOWN_INVESTMENTS] = new Cph_Common_QueryAllTownInvestments();
handlers[proto.Common.REMOVE_SAIL_WAYPOINT] = new Cph_Common_RemoveSailWaypoint();
handlers[proto.Common.QUERY_TRADE_AREA] = new Cph_Common_QueryTradeArea();
handlers[proto.Common.CHANGE_FLEET_PRESET_NAME] = new Cph_Common_ChangeFleetPresetName();
handlers[proto.Common.LOAD_FLEET_PRESET] = new Cph_Common_LoadFleetPreset();
handlers[proto.Common.ADD_MATE_EXP_USE_ITEM] = new Cph_Common_AddMateExpUseItem();
handlers[proto.Common.LOCK_SHIP] = new Cph_Common_LockShip();
handlers[proto.Common.EVENT_GET_MINI_BOARD_GAME] = new Cph_Common_EventGetMiniBoardGame();
handlers[proto.Common.EVENT_PLAY_MINI_BOARD_GAME] = new Cph_Common_EventPlayMiniBoardGame();
handlers[proto.Common.HIDE_EQUIP_SLOTS] = new Cph_Common_HideEquipSlots();
handlers[proto.Common.BILLING_STEAM_PURCHASE_INIT_TXN] =
  new Cph_Common_BillingSteamPurchaseInitTxn();
handlers[proto.Common.QUERY_CRAZE_EVENT_BUDGET] = new Cph_Common_QueryCrazeEventBudget();
handlers[proto.Common.SAVE_FLEET_PRESET] = new Cph_Common_SaveFleetPreset();
handlers[proto.Common.DELETE_FLEET_PRESET] = new Cph_Common_DeleteFleetPreset();
handlers[proto.Common.FLEET_DISPATCH_START] = new Cph_Common_FleetDispatch_Start();
handlers[proto.Common.FLEET_DISPATCH_CANCEL] = new Cph_Common_FleetDispatch_Cancel();
handlers[proto.Common.FLEET_DISPATCH_END] = new Cph_Common_FleetDispatch_End();
handlers[proto.Common.FLEET_DISPATCH_REWARD_RECEIVE] =
  new Cph_Common_FleetDispatch_Reward_Receive();
handlers[proto.Common.FLEET_DISPATCH_SLOT_OPEN] = new Cph_Common_FleetDispatch_Slot_Open();
handlers[proto.Common.FLEET_DISPATCH_DECREASE_EXPIRE_TIME] =
  new Cph_Common_FleetDispatchDecreasExpireTime();
handlers[proto.Common.FLEET_DISPATCH_REWARD_CHOICE] = new Cph_Common_FleetDispatchRewardChoice();
handlers[proto.Common.FLEET_DISPATCH_QUERY_IN_PROGRESS_STATE] =
  new Cph_Common_FleetDispatchQueryInProgreesState();
handlers[proto.Common.GET_WORLD_RANKING] = new Cph_Common_GetWorldRanking();
handlers[proto.Common.MATES_COMPLETE_AWAKEN] = new Cph_Common_MatesCompleteAwaken();
handlers[proto.Common.MATES_COMPLETE_LEARN_PASSIVE] = new Cph_Common_MatesCompleteLearnPassive();
handlers[proto.Common.TOGGLE_BATTLE_CONTINUOUS] = new Cph_Common_ToggleBattleContinuous();
handlers[proto.Common.UPDATE_BATTLE_CONTINUOUS_RESULT] =
  new Cph_Common_UpdateBattleContinuousResult();
handlers[proto.Common.TRANSLATE_CHAT] = new Cph_Common_TranslateChat();
handlers[proto.Common.BUY_CHAT_TRANSLATION_COUNT] = new Cph_Common_BuyChatTranslationCount();
handlers[proto.Common.BUY_ATTENDANCE] = new Cph_Common_BuyAttendance();
handlers[proto.Common.RECEIVE_DAILY_SUBSCRIPTION_REWARD] =
  new Cph_Common_receiveDailySubscriptionReward();
handlers[proto.Common.PUBLISH_TOAST_MESSAGE_CS] = new Cph_Common_PublishToastMessage();
handlers[proto.Common.MATE_START_TRAINING] = new Cph_Common_MateStartTraining();
handlers[proto.Common.MATES_COMPLETE_TRAINING] = new Cph_Common_MatesCompleteTraining();
handlers[proto.Common.MATE_COMPLETE_TRAINING_IMMEDIATELY] =
  new Cph_Common_MateCompleteTrainingImmediately();
handlers[proto.Common.MATE_REDUCE_TRAINING_TIME] = new Cph_Common_MateReduceTrainingTime();
handlers[proto.Common.MATE_USE_TRAINING_POINTS] = new Cph_Common_MateUseTrainingPoints();
handlers[proto.Common.MATE_RESET_TRAINING_POINTS] = new Cph_Common_MateResetTrainingPoints();
handlers[proto.Common.OPEN_HOT_SPOT_PRODUCT] = new Cph_Common_OpenHotSpotProduct();
handlers[proto.Common.GET_WORLD_EVENT_RANKING] = new Cph_Common_GetWorldEventRanking();
handlers[proto.Common.RECEIVE_EVENT_RANKING_MISSION_REWARD] =
  new Cph_Common_ReceiveEventRankingMissionReward();
handlers[proto.Common.RECEIVE_EVENT_RANKING_REWARD] = new Cph_Common_ReceiveEventRankingReward();
handlers[proto.Common.SHIP_SLOT_ITEM_LOCK] = new Cph_Common_ShipSlotItemLock();
handlers[proto.Common.RECEIVE_DISCOVERY_REWARD] = new Cph_Common_ReceiveDiscoveryReward();
handlers[proto.Common.GET_SAILING_DIARIES] = new Cph_Common_GetSailingDiaries();
handlers[proto.Common.EQUIP_MATE_ILLUST] = new Cph_Common_EquipMateIllust();
handlers[proto.Common.RECEIVE_FISH_SIZE_REWARDS] = new Cph_Common_ReceiveFishSizeRewards();
handlers[proto.Common.QUERY_ALL_INVESTED_TOWNS_GUILD_SHARE_POINTS] =
  new Cph_Common_QueryAllInvestedTownsGuildSharePoints();
handlers[proto.Common.QUERY_GUILD_TOWNS] = new Cph_Common_QueryGuildTowns();
handlers[proto.Common.QUERY_GUILD_INVESTMENT_USER_SCORES] =
  new Cph_Common_QueryGuildInvestmentUserScores();
handlers[proto.Common.UPDATE_HOT_TIME_BUFF] = new Cph_Common_UpdateHotTimeBuff();
handlers[proto.Common.QUERY_TOWNS_FIRST_GUILD_SHARE_POINT] =
  new Cph_Common_QueryTownsFirstGuildSharePoint();
handlers[proto.Common.CHANGE_USER_TITLE] = new Cph_Common_ChangeUserTitle();
handlers[proto.Common.EQUIP_BATTLE_QUICK_SKILL] = new Cph_Common_EquipBattleQuickSkill();
handlers[proto.Common.QUERY_NATION_ELECTION_SYNC] = new Cph_Common_QueryNationElectionSync();
handlers[proto.Common.REGISTER_NATION_ELECTION_CANDIDATE] =
  new Cph_Common_RegisterNationElectionCandidate();
handlers[proto.Common.MODIFY_NATION_ELECTION_CANDIDATE_DATA] =
  new Cph_Common_ModifyNationElectionCandidate();
handlers[proto.Common.VOTE_TO_NATION_ELECTION_CANDIDATE] =
  new Cph_Common_VoteToNationElectionCandidate();
handlers[proto.Common.APPLY_FLEET_PRESET] = new Cph_Common_ApplyFleetPreset();
handlers[proto.Common.SHIP_BUILDING_CANCEL] = new Cph_Common_ShipBuildingCancel();
handlers[proto.Common.RECEIVE_NATION_ELECTION_VOTE_REWARD] =
  new Cph_Common_ReceiveNationElectionVoteReward();
handlers[proto.Common.RECEIVE_NATION_GOAL_PROMISE_REWARD] =
  new Cph_Common_ReceiveNationGoalPromiseReward();
handlers[proto.Common.CHANGE_CARGO_PRESET_NAME] = new Cph_Common_ChangeCargoPresetName();
handlers[proto.Common.CHANGE_EQUIPMENT_TO_COSTUME] = new Cph_Common_ChangeEquipmentToCostume();
handlers[proto.Common.CHANGE_CARGO_PRESET_ID_IN_FLEET_PRESET] =
  new Cph_Common_ChangeCargoPresetIdInFleetPreset();
handlers[proto.Common.SHIP_BUILDING_TERMS_INFO] = new Cph_Common_ShipBuildingTermsInfo();
handlers[proto.Common.REMOTE_CREATE_SHIP] = new Cph_Common_RemoteCreateShip();
handlers[proto.Common.UPGRADE_NATION_POLICY_STEP] = new Cph_Common_UpgradeNationPolicyStep();
handlers[proto.Common.DONATE_TO_NATION_BUDGET] = new Cph_Common_DonateToNationBudget();
handlers[proto.Common.QUEST_STATE_CHANGE] = new Cph_Common_QuestStateChange();
handlers[proto.Common.BUY_NATION_SUPPORT_SHOP] = new Cph_Common_BuyNationSupportShop();
handlers[proto.Common.RECEIVE_NATION_SUPPORT_SHOP_REWARD] =
  new Cph_Common_ReceiveNationSupportShopReward();
handlers[proto.Common.GET_NATION_WEEKLY_DONATION_RANKS] =
  new Cph_Common_GetNationWeeklyDonationRanks();
handlers[proto.Common.RECEIVE_NATION_WEEKLY_DONATION_RANK_REWARD] =
  new Cph_Common_ReceiveNationWeeklyDonationRankReward();
handlers[proto.Common.ENABLE_LAST_WEEK_INVESTMENT_REWARD] =
  new Cph_Common_EnableLastWeekInvestmentReward();
handlers[proto.Common.RECEIVE_LAST_WEEK_INVESTMENT_REWARD] =
  new Cph_Common_ReceiveLastWeekInvestmentReward();
handlers[proto.Common.JOIN_TO_NATION_CABINET_APPLICANT] =
  new Cph_Common_JoinToNationCabinetApplicants();
handlers[proto.Common.APPOINT_NATION_CABINET_MEMBER] = new Cph_Common_AppointNationCabinetMember();
handlers[proto.Common.DISMISS_NATION_CABINET_MEMBER] = new Cph_Common_DismissNationCabinetMember();
handlers[proto.Common.WRITE_NATION_NOTICE] = new Cph_Common_WriteNationNotice();
handlers[proto.Common.SET_NATION_WAGE_RATE] = new Cph_Common_SetNationWageRate();
handlers[proto.Common.LOAD_NATION_CABINET_APPLICANTS] =
  new Cph_Common_LoadNationCabinetApplicants();
handlers[proto.Common.WRITE_NATION_PRIME_MINISTER_THOUGHT] =
  new Cph_Common_WriteNationPrimeMinisterThought();
handlers[proto.Common.SET_OPTION_FLEET_PRESET] = new Cph_Common_SetOptionFleetPreset();
handlers[proto.Common.GET_OPENED_FLEET_PRESET] = new Cph_Common_GetOpenedFleetPresets();
handlers[proto.Common.SET_REPRESENTED_MATE] = new Cph_Common_SetRepresentedMate();
handlers[proto.Common.QUEST_REG_UTCTIME] = new Cph_Common_QuestRegUtcTime();
handlers[proto.Common.GET_MARKET_EVENT_INFO] = new Cph_Common_GetMarketEventInfo();
handlers[proto.Common.SET_SIDEKICK_MATE] = new Cph_Common_SetSidekickMate();
handlers[proto.Common.SET_SIDEKICK_PET] = new Cph_Common_SetSidekickPet();
handlers[proto.Common.RECEIVE_EVENT_RANKING_GUILD_REWARD] =
  new Cph_Common_ReceiveEventRankingGuildReward();
handlers[proto.Common.SET_FAVORITE_MATE] = new Cph_Common_SetFavoriteMate();
handlers[proto.Common.GET_MY_BLIND_BID_INVEN] = new Cph_Common_GetMyBlindBidInven();
handlers[proto.Common.GET_BLIND_BID_COMPLETED_PRODUCTS] =
  new Cph_Common_GetBlindBidCompletedProducts();
handlers[proto.Common.TRY_BLIND_BID] = new Cph_Common_TryBlindBid();
handlers[proto.Common.RECEIVE_BLIND_BID_WINNER_REWARD] =
  new Cph_Common_ReceiveBlindBidWinnerReward();
handlers[proto.Common.BLIND_BID_TICKET_BUY] = new Cph_Common_BlindBidTicketBuy();
handlers[proto.Common.REFUND_BLIND_BID] = new Cph_Common_RefundBlindBid();
handlers[proto.Common.MATE_START_TRANSCENDENCE] = new Cph_Common_MateStartTranscendence();
handlers[proto.Common.MATE_COMPLETE_TRANSCENDENCE] = new Cph_Common_MatesCompleteTranscendence();
handlers[proto.Common.MATE_COMPLETE_TRANSCENDENCE_IMMEDIATELY] =
  new Cph_Common_MateCompleteTranscendenceImmediately();
handlers[proto.Common.GET_INFINITE_LIGHT_HOUSE_FRIEND_CLEARED_INFO] =
  new Cph_Common_GetInfiniteLighthouseFriendClearedInfo();
handlers[proto.Common.SWEEP_INFINITE_LIGHT_HOUSE_STAGE] =
  new Cph_Common_SweepInfiniteLighthouseStage();
handlers[proto.Common.GET_USER_FIRST_FLEET_INFOS] = new Cph_Common_GetUserFirstFleetInfos();
handlers[proto.Common.GET_USER_FIRST_FLEET_INFO_BY_NAME] =
  new Cph_Common_GetUserFirstFleetInfoByName();
handlers[proto.Common.SET_OPTION_FRIENDLY_BATTLE] = new Cph_Common_SetOptionfriendlyBattle();
handlers[proto.Common.QUERY_UNPOPULAR_EVENT] = new Cph_Common_QueryUnpopularEvent();
handlers[proto.Common.RESEARCH_START] = new Cph_Common_ResearchStart();
handlers[proto.Common.RESEARCH_CANCEL] = new Cph_Common_ResearchCancel();
handlers[proto.Common.RESEARCH_RESET] = new Cph_Common_ResearchReset();
handlers[proto.Common.RESEARCH_REPORT] = new Cph_Common_ResearchReport();
handlers[proto.Common.GET_INVESTMENT_SEASON_RANKING] = new Cph_Common_GetInvestmentSeasonRanking();
handlers[proto.Common.CLEAR_REENTRY_COOLDOWN] = new Cph_Common_ClearReentryCooldown();
handlers[proto.Common.GET_CLASH_PAGE] = new Cph_Common_GetClashPage();
handlers[proto.Common.RECEIVE_CLASH_REWARD] = new Cph_Common_ReceiveClashReward();
handlers[proto.Common.GET_MY_INVESTMENT_SEASON_NATION_RANKING_SCORE] =
  new Cph_Common_GetMyInvestmentSeasonNationRankingScore();
handlers[proto.Common.CHECK_LAST_SEASON_INVESTMENT_RANK_REWARDABLE] =
  new Cph_Common_CheckLastSeasonInvestmentRankRewardable();
handlers[proto.Common.RECEIVE_LAST_SEASON_INVESTMENT_RANK_REWARD] =
  new Cph_Common_ReceiveLastSeasonInvestmentRankReward();
handlers[proto.Common.AUTO_CHANGE_ITEMS_TRY] = new Cph_Common_AutoChangeItemsTry();
handlers[proto.Common.AUTO_CHANGE_ITEMS_HISTORY_GET] = new Cph_Common_AutoChangeItemsHistoryGet();
handlers[proto.Common.AUTO_CHANGE_ITEMS_HISTORY_RESET] =
  new Cph_Common_AutoChangeItemsHistoryReset();

// BattleReward packets.
handlers[proto.BattleReward.RECEIVE_BATTLE_REWARD] = new Cph_BattleReward_ReceiveBattleReward();
handlers[proto.BattleReward.LEAVE] = new Cph_BattleReward_Leave();
handlers[proto.BattleReward.ENTER] = new Cph_BattleReward_Enter();
handlers[proto.BattleReward.LOAD_COMPLETE] = new Cph_BattleReward_LoadComplete();

// LandExploreReward packets
handlers[proto.LandExploreReward.OPEN_BOX] = new Cph_LandExploreReward_OpenBox();
handlers[proto.LandExploreReward.RECEIVE_ENTER] = new Cph_LandExploreReward_ReceiveEnter();
handlers[proto.LandExploreReward.RECEIVE_LEAVE] = new Cph_LandExploreReward_ReceiveLeave();
handlers[proto.LandExploreReward.RECEIVE] = new Cph_LandExploreReward_Receive();

// Etc packets.
handlers[proto.Etc.PING_CS] = new Cph_Etc_Ping();
handlers[proto.Etc.SET_CLIENT_BACKGROUND_STATE_CS] = new Cph_Etc_SetClientBackgroundState();

// Admin packets.
handlers[proto.Admin.TELEPORT_TOWN] = new Cph_Admin_TeleportTown();
handlers[proto.Admin.TELEPORT_TO_USER] = new Cph_Admin_TeleportToUser();

// Dev peckets.
handlers[proto.Dev.GIVE_MATE_EQUIPMENT] = new Cph_Dev_GiveMateEquipment();
handlers[proto.Dev.ADD_POINT] = new Cph_Dev_AddPoint();
handlers[proto.Dev.ADD_MATE] = new Cph_Dev_AddMate();
handlers[proto.Dev.SET_SHIP_SAILOR] = new Cph_Dev_SetShipSailor();
handlers[proto.Dev.ADD_SHIP] = new Cph_Dev_AddShip();
handlers[proto.Dev.UPDATE_NATION] = new Cph_Dev_UpdateNation();
handlers[proto.Dev.UPDATE_NATION_INTIMACY] = new Cph_Dev_UpdateNationIntimacy();
handlers[proto.Dev.SET_NATION] = new Cph_Dev_SetNation();
handlers[proto.Dev.UPDATE_REPUTATION] = new Cph_Dev_UpdateReputation();
handlers[proto.Dev.INVOKE_EVENT] = new Cph_Dev_InvokeEvent();
handlers[proto.Dev.INVOKE_NATION_INTIMACY_UPDATE_JOB] = new Cph_Dev_InvokeNationIntimacyUpdateJob();
handlers[proto.Dev.REMOVE_POINT] = new Cph_Dev_RemovePoint();
handlers[proto.Dev.SET_POINT] = new Cph_Dev_SetPoint();
handlers[proto.Dev.SET_SHIP_DURABILITY] = new Cph_Dev_SetShipDurability();
handlers[proto.Dev.REMOVE_ALL_SHIP_CARGOS] = new Cph_Dev_RemoveAllShipCargos();
handlers[proto.Dev.SET_FIRST_SHIP_CARGO] = new Cph_Dev_SetFirstShipCargo();
handlers[proto.Dev.ADD_ITEM] = new Cph_Dev_AddItem();
handlers[proto.Dev.ADD_PARTS] = new Cph_Dev_AddParts();
handlers[proto.Dev.SET_DEVELOPMENT_LEVEL] = new Cph_Dev_SetDevelopmentLevel();
handlers[proto.Dev.SET_MATE_FAME] = new Cph_Dev_SetMateFame();
handlers[proto.Dev.SET_MATE_ROYAL_TITLE] = new Cph_Dev_SetMateRoyalTitle();
handlers[proto.Dev.ADD_DIRECT_MAIL_CS] = new Cph_Dev_AddDirectMail();
handlers[proto.Dev.ADD_INSTALLMENT_SAVINGS_LAST_DEPOSIT_TIME] =
  new Cph_Dev_AddInstallmentSavingsLastDepositTime();
handlers[proto.Dev.SET_USER_LEVEL] = new Cph_Dev_SetUserLevel();
handlers[proto.Dev.SET_USER_KARMA] = new Cph_Dev_SetUserKarma();
handlers[proto.Dev.SET_USER_COMPANY_JOB] = new Cph_Dev_SetUserCompanyJob();
handlers[proto.Dev.SET_MATE_INJERY] = new Cph_Dev_SetMateInjury();
handlers[proto.Dev.QUEST_SET_FLAGS] = new Cph_Dev_QuestSetFlags();
handlers[proto.Dev.QUEST_SET_REGISTER] = new Cph_Dev_QuestSetRegister();
handlers[proto.Dev.QUEST_SET_TEMP_REGISTER] = new Cph_Dev_QuestSetTempRegister();
handlers[proto.Dev.QUEST_SET_ACCUM] = new Cph_Dev_QuestSetAccum();
handlers[proto.Dev.QUEST_SET_COMPLETED] = new Cph_Dev_QuestSetCompleted();
handlers[proto.Dev.QUEST_SET_NODE_IDX] = new Cph_Dev_QuestSetNodeIdx();
handlers[proto.Dev.PREDICT_DISASTER] = new Cph_Dev_PredictDisaster();
handlers[proto.Dev.GENERATE_DISASTER] = new Cph_Dev_GenerateDisaster();
handlers[proto.Dev.RESOLVE_DISASTER] = new Cph_Dev_ResolveDisaster();
handlers[proto.Dev.UNLIMITED_INVEST] = new Cph_Dev_UnlimitedInvest();
handlers[proto.Dev.SET_IGNORE_NPC_ENCOUNT_CS] = new Cph_Dev_SetIgnoreNpcEncount();
handlers[proto.Dev.STAT_DUMP] = new Cph_Dev_StatDump();
handlers[proto.Dev.STAT_SET] = new Cph_Dev_StatSet();
handlers[proto.Dev.ADD_NEAR_SPAWNER] = new Cph_Dev_AddNearSpawner();
handlers[proto.Dev.WORLD_BUFF_ADD_CS] = new Cph_Dev_WorldBuffAdd();
handlers[proto.Dev.WORLD_BUFF_REM_CS] = new Cph_Dev_WorldBuffRem();
handlers[proto.Dev.DISCONNECT_SERVER_CS] = new Cph_Dev_DisconnectServer();
handlers[proto.Dev.ENCOUNT_NPC_ATT_CHOICE] = new Cph_Dev_EncountNpcAttChoice();
handlers[proto.Dev.ENCOUNT_NPC_DEF_CHOICE] = new Cph_Dev_EncountNpcDevChoice();
handlers[proto.Dev.CHANGE_NPC_ATTACK_RADIUS] = new Cph_Dev_ChangeNpcAttackRadius();
handlers[proto.Dev.TRADE_DUMP] = new Cph_Dev_TradeDump();
handlers[proto.Dev.CHANGE_NPC_TICK_PER_SEC] = new Cph_Dev_ChangeNpcTickPerSec();
handlers[proto.Dev.REMOVE_NEAR_SPAWNER] = new Cph_Dev_RemoveNearSpawner();
handlers[proto.Dev.SET_LOYALTY] = new Cph_Dev_SetLoyalty();
handlers[proto.Dev.RESET_PUB_MATES] = new Cph_Dev_ResetPubMates();
handlers[proto.Dev.ADD_ALL_MATES] = new Cph_Dev_AddAllMates();
handlers[proto.Dev.GET_NPC_LOCATION_BY_OCEAN_NPC_AREA_SPAWNER] =
  new Cph_Dev_GetNpcLocationByOceanNpcAreaSpawner();
handlers[proto.Dev.QUEST_FORCE_EXEC] = new Cph_Dev_QuestForceExec();
handlers[proto.Dev.ADD_OCEAN_DOODAD_NEAR_SPAWNER] = new Cph_Dev_AddOceanDoodadNearSpawner();
handlers[proto.Dev.REMOVE_OCEAN_DOODAD_NEAR_SPAWNER] = new Cph_Dev_RemoveOceanDoodadNearSpawner();
handlers[proto.Dev.SET_LOCAL_NPC_SPAWN] = new Cph_Dev_SetLocalNpcSpawn();
handlers[proto.Dev.SET_LOCAL_DOODAD_SPAWN] = new Cph_Dev_SetLocalDoodadSpawn();
handlers[proto.Dev.SET_DISASTER_LUCK] = new Cph_Dev_SetDisasterLuck();
handlers[proto.Dev.PREDICT_PROTECTION] = new Cph_Dev_PredictProtection();
handlers[proto.Dev.GENERATE_PROTECTION] = new Cph_Dev_GenerateProtection();
handlers[proto.Dev.RESOLVE_PROTECTION] = new Cph_Dev_ResolveProtection();
handlers[proto.Dev.TELEPORT_TO_LOCATION_CS] = new Cph_Dev_TeleportToLocation();
handlers[proto.Dev.SET_MATE_LEVEL] = new Cph_Dev_SetMateLevel();
handlers[proto.Dev.ADD_USER_DATA_NPC_SPAWNER] = new Cph_Dev_AddUserDataNpcSpawner();
handlers[proto.Dev.REVEAL_ALL_WORLD_MAP_TILES] = new Cph_Dev_RevealAllWorldMapTiles();
handlers[proto.Dev.DISCOVER_ALL_TOWNS] = new Cph_Dev_DiscoverAllTowns();
handlers[proto.Dev.ADD_ALL_TOWNS_TO_QUESTION_PLACE] = new Cph_Dev_AddAllTownsToQuestionPlace();
handlers[proto.Dev.WORLD_PASSIVE_ADD_CS] = new Cph_Dev_WorldPassiveAdd();
handlers[proto.Dev.WORLD_PASSIVE_REM_CS] = new Cph_Dev_WorldPassiveRem();
handlers[proto.Dev.DEBUFF_IMMUNE_CS] = new Cph_Dev_DebuffImmune();
handlers[proto.Dev.DISASTER_IMMUNE_CS] = new Cph_Dev_DisasterImmune();
handlers[proto.Dev.SHOW_DISASTER_STAT_CS] = new Cph_Dev_ShowDisasterStat();
handlers[proto.Dev.SET_TRADE_GOODS_BREED_SUCCESS_CS] = new Cph_Dev_SetTradeGoodsBreedSuccess();
handlers[proto.Dev.DISCOVER] = new Cph_Dev_Discover();
handlers[proto.Dev.BATTLE_RESUME_FOR_USER_ID] = new Cph_Dev_BattleResumeForUserId();
handlers[proto.Dev.GIVE_ALL_MATE_EQUIPMENTS] = new Cph_Dev_GiveAllMateEquipments();
handlers[proto.Dev.DISCOVER_ALL] = new Cph_Dev_DiscoverAll();
handlers[proto.Dev.ATTACK_TO_ME_CS] = new Cph_Dev_AttackToMe();
handlers[proto.Dev.ADD_BATTLE_FORMATION] = new Cph_Dev_AddBattleFormation();
handlers[proto.Dev.SPECIAL_STAT_DUMP] = new Cph_Dev_SpecialStatDump();
handlers[proto.Dev.SET_MATE_AWAKEN] = new Cph_Dev_SetMateAwaken();
handlers[proto.Dev.PUB_STAFF_RESET] = new Cph_Dev_PubStaffReset();
handlers[proto.Dev.SET_TOWN_NATION_SHARE_POINT_CS] = new Cph_Dev_SetTownNationSharePoint();
handlers[proto.Dev.I_AM_MAYOR_CS] = new Cph_Dev_IAmMayor();
handlers[proto.Dev.CHANGE_MAYOR_TAX] = new Cph_Dev_ChangeMayorTax();
handlers[proto.Dev.EASY_LANGUAGE] = new Cph_Dev_EasyLanguage();
handlers[proto.Dev.CHANGE_SPAWNED_LOCAL_NPC_NUM_CS] = new Cph_Dev_ChangeSpawnedLocalNpcNum();
handlers[proto.Dev.SET_SAILING_DAYS_ONOFF_CS] = new Cph_Dev_SetSailingDaysOnOff();
handlers[proto.Dev.RESET_COLLECTION] = new Cph_Dev_ResetCollection();
handlers[proto.Dev.SET_MATE_TALK_WAIT_TIME] = new Cph_Dev_SetMateTalkWaitTime();
handlers[proto.Dev.LOAD] = new Cph_Dev_Load();
handlers[proto.Dev.RESET_PALACE_ROYAL_ORDER] = new Cph_Dev_ResetPalaceRoyalOrder();
handlers[proto.Dev.QUEST_SET_GLOBAL_REGISTER] = new Cph_Dev_QuestSetGlobalRegister();
handlers[proto.Dev.UNLOCK_ALL_MATE_AWAKEN_AND_SKILL] = new Cph_Dev_UnlockAllMateAwakenAndSkill();
handlers[proto.Dev.RESET_GUILD_LEFT_TIME_TEMPORARILY] = new Cph_Dev_ResetGuildLeftTimeTemporarily();
handlers[proto.Dev.RESET_GUILD_RAID_OPEN_TIME] = new Cph_Dev_ResetGuildRaidOpenTime();
handlers[proto.Dev.RESET_GUILD_RAID_BATTLE_N_REWARD_HISTORY] =
  new Cph_Dev_ResetGuildRaidBattleNRewardHistory();
handlers[proto.Dev.SET_GUILD_UPGRADE_POPUP] = new Cph_Dev_SetGuildUpgradePopup();
handlers[proto.Dev.SET_ARENA_SCORE] = new Cph_Dev_SetArenaScore();
handlers[proto.Dev.SET_ARENA_TICKET_COUNT] = new Cph_Dev_SetArenaTicketCount();
handlers[proto.Dev.SET_ARENA_TICKET_BOUGHT_COUNT] = new Cph_Dev_SetArenaTicketBoughtCount();
handlers[proto.Dev.SET_ARENA_MATCH_LIST_REFRESH_COUNT] =
  new Cph_Dev_SetArenaMatchListRefreshCount();
handlers[proto.Dev.ADD_GUILD_POINT] = new Cph_Dev_AddGuildPoint();
handlers[proto.Dev.RESET_ARENA_DATA] = new Cph_Dev_ResetArenaData();
handlers[proto.Dev.RESET_CASH_SHOP_DAILY_PRODUCTS] = new Cph_Dev_ResetCashShopDailyProducts();
handlers[proto.Dev.SET_LEADER_MATE_SWITCH_COUNT] = new Cph_Dev_SetLeaderMateSwitchCount();
handlers[proto.Dev.QUEST_SET_ADMIN_PAUSE] = new Cph_Dev_QuestSetAdminPause();
handlers[proto.Dev.GENERATE_ARENA_DUMMY_USERS] = new Cph_Dev_GenerateArenaDummyUsers();
handlers[proto.Dev.SET_SHIP_BUILD_LEVEL] = new Cph_Dev_SetShipBuildLevel();
handlers[proto.Dev.SET_USER_SHIP_BUILD_LEVEL] = new Cph_Dev_SetUserShipBuildLevel();
handlers[proto.Dev.SET_VILLAGE_FRIENDSHIP] = new Cph_Dev_SetVillageFriendship();
handlers[proto.Dev.SET_DEBUG_MSG_FOR_ENCOUNT_BY_NPC_CS] = new Cph_Dev_SetDebugMsgForEncountByNpc();
handlers[proto.Dev.SHOW_SPAWNED_NPC_COUNT_CS] = new Cph_Dev_ShowSpawnedNpcCount();
handlers[proto.Dev.INIT_EXPLORE_TICKET] = new Cph_Dev_InitExploreTicket();
handlers[proto.Dev.RESET_GUILD_SHOP] = new Cph_Dev_ResetGuildShop();
handlers[proto.Dev.SET_EVENT_PAGE_PRODUCT] = new Cph_Dev_SetEventPageProduct();
handlers[proto.Dev.SET_PASS_EVENT_EXP] = new Cph_Dev_SetPassEventExp();
handlers[proto.Dev.ESTIMATED_SAILING_TIME] = new Cph_Dev_EstimatedSailingTime();
handlers[proto.Dev.FIXED_SPEED_CS] = new Cph_Dev_FixedSpeed();
handlers[proto.Dev.DISABLE_SPEED_HACK_CS] = new Cph_Dev_DisableHackSpeed();
handlers[proto.Dev.SET_EXPLORE_TIME_CHECK] = new Cph_Dev_SetExploreTimeCheck();
handlers[proto.Dev.RESET_GUILD_DATE] = new Cph_Dev_ResetGuildDate();
handlers[proto.Dev.SET_SHIP_LIFE] = new Cph_Dev_SetShipLife();
handlers[proto.Dev.ADD_ALL_SHIPS] = new Cph_Dev_AddAllShips();
handlers[proto.Dev.ADD_ALL_PARTS] = new Cph_Dev_AddAllParts();
handlers[proto.Dev.NOTICE_RAID] = new Cph_Dev_NoticeRaid();
handlers[proto.Dev.ADD_RAID_DAMAGE] = new Cph_Dev_AddRaidDamage();
// handlers[proto.Dev.KILL_RAID_BOSS] = new Cph_Dev_KillRaidBoss();
handlers[proto.Dev.GET_RAID_STATE] = new Cph_Dev_GetRaidState();
handlers[proto.Dev.SET_RAID_SCHEDULE_END] = new Cph_Dev_SetRaidShcheduleEnd();
handlers[proto.Dev.SET_TRADE_POINT] = new Cph_Dev_SetTradePoint();
handlers[proto.Dev.RESET_WORLD_SKILL] = new Cph_Dev_ResetWorldSkill();
handlers[proto.Dev.SET_NATION_LAST_UPDATE_TIME] = new Cph_Dev_SetNationLastUpdateTime();
handlers[proto.Dev.QUEST_RESET_ALL_DAILY_LIMIT_COMPLETED_COUNT] =
  new Cph_Dev_QuestResetAllDailyLimitCompletedCount();
handlers[proto.Dev.SET_MATE_STATE] = new Cph_Dev_SetMateState();
handlers[proto.Dev.ADD_FLEET] = new Cph_Dev_AddFleet();
handlers[proto.Dev.SET_SHIP_SAILMASTERY_LEVEL] = new Cph_Dev_SetShipSailMasteryLevel();
handlers[proto.Dev.MAKE_DIPATCH_END] = new Cph_Dev_MakeDispatchEnd();
handlers[proto.Dev.ADD_ALL_ITEMS] = new Cph_Dev_AddAllItems();
handlers[proto.Dev.RESET_WAYPOINT_SUPPLY_TICKET] = new Cph_Dev_ResetWaypointSupplyTicket();
handlers[proto.Dev.SET_SHIP_SAILMASTERY_EXP] = new Cph_Dev_SetShipSailMasteryExp();
handlers[proto.Dev.RESET_FOR_TIME_TRAVEL] = new Cph_Dev_ResetForTimeTravel();
handlers[proto.Dev.UNLINK_COMPANY] = new Cph_Dev_UnlinkCompany();
handlers[proto.Dev.CLEAR_DISCOVER] = new Cph_Dev_ClearDiscover();
handlers[proto.Dev.REMOVE_ALL_ITEMS] = new Cph_Dev_RemoveAllItems();
handlers[proto.Dev.REMOVE_UNEQUIP_EQUIPMENTS] = new Cph_Dev_RemoveUnequipEquipments();
handlers[proto.Dev.REMOVE_UNEQUIP_PARTS] = new Cph_Dev_RemoveUnequipParts();
handlers[proto.Dev.ADD_RELEASED_MATES] = new Cph_Dev_AddReleasedMates();
handlers[proto.Dev.ADD_ALL_MATES_AWAKEN] = new Cph_Dev_AddAllMatesAwaken();
handlers[proto.Dev.SET_ALL_MATES_LEVEL] = new Cph_Dev_SetAllMatesLevel();
handlers[proto.Dev.RECOVER_CHAT_TRANSLATION] = new Cph_Dev_RecoverChatTranslation();
handlers[proto.Dev.SHOW_DISPATCH_ACTION_RESULT_STAT_CS] =
  new Cph_Dev_ShowDispatchActionResultStat();
handlers[proto.Dev.CHANGE_GUILD_SYNTHESIS_PROB_GREAT_SUCCESS] =
  new Cph_Dev_ChangeGuildSynthesisProbGreatSuccess();
handlers[proto.Dev.RESET_GUILD_RAID_TICKET] = new Cph_Reset_Guild_Raid_Ticket();
handlers[proto.Dev.SET_GUILD_RAID_SCHEDULE_END] = new Cph_Dev_SetGuildRaidShcheduleEnd();
handlers[proto.Dev.ADD_GUILD_RAID_DAMAGE] = new Cph_Dev_AddGulidRaidDamage();
handlers[proto.Dev.ADD_GUILD_RESOURCE] = new Cph_Dev_AddGuildResource();
handlers[proto.Dev.ADD_SHIP_CAMOUFLAGE] = new Cph_Dev_AddShipCamouflage();
handlers[proto.Dev.ADD_MATE_ILLUST] = new Cph_Dev_AddMateIllust();
handlers[proto.Dev.SET_USER_LAST_LOGIN_TIME_DAYS_AGO] = new Cph_Dev_SetUserLastLoginTimeDaysAgo();
handlers[proto.Dev.SET_USER_ATTENDANCE] = new Cph_Dev_SetUserAttendance();
handlers[proto.Dev.ADD_USER_TITLE] = new Cph_Dev_AddUserTitle();
handlers[proto.Dev.RESET_SESSION_RANKING] = new Cph_Dev_ResetSessionRanking();
handlers[proto.Dev.REMOVE_NATION_ELECTION_CANDIDATE] = new Cph_Dev_RemoveNationElectionCandidate();
handlers[proto.Dev.RESERVE_MAYOR_TRADE_EVENT] = new Cph_Dev_ReserveMayorTradeEvent();
handlers[proto.Dev.DELETE_MAYOR_TRADE_EVENT] = new Cph_Dev_DeleteMayorTradeEvent();
handlers[proto.Dev.CHANGE_NATION_SESSIONID] = new Cph_Dev_ChangeNationSessionId();
handlers[proto.Dev.RECEIVE_PRODUCTS] = new Cph_Dev_ReceiveProducts();
handlers[proto.Dev.SET_SWEEP_TICKET] = new Cph_Dev_SetSweepTicket();
handlers[proto.Dev.RESET_EXCHANGE_HISTORY] = new Cph_Dev_ResetExchangeHistory();
handlers[proto.Dev.SET_VILLAGE_EVENT] = new Cph_Dev_SetVillageEvent();
handlers[proto.Dev.SET_NATION_POLICY] = new Cph_Dev_SetNationPolicy();
handlers[proto.Dev.SET_NATION_BUDGET] = new Cph_Dev_SetNationBudget();
handlers[proto.Dev.DELETE_ALL_DIRECT_MAILS] = new Cph_Dev_DeleteAllDirectMails();
handlers[proto.Dev.GET_NATION_ACCUMULATED_TAX] = new Cph_Dev_GetNationAccumulatedTax();
handlers[proto.Dev.SET_QUEST_PASS] = new Cph_Dev_SetQuestPass();
handlers[proto.Dev.I_AM_PRIME_MINISTER] = new Cph_Dev_IAmNationPrimeMinister();
handlers[proto.Dev.RESET_NATION_CABINET_LAST_APPOINTED_TIMES] =
  new Cph_Dev_ResetNationCabinetLastAppointedTimes();
handlers[proto.Dev.RESET_MAYOR_REMOTE_INVEST] = new Cph_Dev_ResetMayorRemoteInvest();
handlers[proto.Dev.RESET_REMOTE_INVEST_COUNT] = new Cph_Dev_ResetRemoteInvestCount();
handlers[proto.Dev.ADD_PET] = new Cph_Dev_AddPet();
handlers[proto.Dev.RESET_BLACK_MARKET_REFRESH_COUNT] = new Cph_Dev_ResetBlackMarketRefreshCount();
handlers[proto.Dev.OPEN_HOT_SPOT_BUYABLE_PRODUCTS] = new Cph_Dev_OpenBuyableHotSpotProducts();
handlers[proto.Dev.TEST] = new Cph_Dev_Test();
handlers[proto.Dev.RESET_HOT_SPOT_PRODUCTS_HISTORY] = new Cph_Dev_ResetHotSpotProductsHistory();
handlers[proto.Dev.CHANGE_INFINITE_LIGHTHOUSE_CLEARED_INFO_SESSION] =
  new Cph_Dev_ChangeInfiniteLighthouseClearedInfoSession();
handlers[proto.Dev.ADD_ALL_PUB_MATES] = new Cph_Dev_AddAllPubMates();
handlers[proto.Dev.CLEAR_INFINITE_LIGHTHOUSE_STAGE] = new Cph_Dev_ClearInfiniteLighthouseStage();
handlers[proto.Dev.RESET_MY_FIRST_FLEET_INFO] = new Cph_Dev_ResetMyFirstFleetInfo();
handlers[proto.Dev.RESERVE_UNPOPULAR_TRADE_EVENT] = new Cph_Dev_ReserveUnpopularTradeEvent();
handlers[proto.Dev.DELETE_UNPOPULAR_TRADE_EVENT] = new Cph_Dev_DeleteUnpopularTradeEvent();
handlers[proto.Dev.UPDATE_RESEARCH] = new Cph_Dev_UpdateResearch();
handlers[proto.Dev.SET_CLASH_SCORE_AND_WINSTREAK] = new Cph_Dev_SetClashScoreAndWinStreak();
handlers[proto.Dev.ACTIVE_CLASH_BATTLE_RECORD] = new Cph_Dev_ActiveClashBattleRecord();
handlers[proto.Dev.GENERATE_TOWN_USER_WEEKLY_INVESTMENT_SCORE] =
  new Cph_Dev_GenerateTownUserWeeklyInvestmentScore();
handlers[proto.Dev.SET_TUTORIAL_CRAZE_EVENT_BUDGET] = new Cph_Dev_SetTutorialCrazeEventBudget();
handlers[proto.Dev.TRY_FORCE_ADD_CRAZE_EVENT] = new Cph_Dev_TryForceAddCrazeEvent();
handlers[proto.Dev.SET_MANUFACTURE_LEVEL] = new Cph_Dev_SetManufactureLevel();

// Temp packets.
handlers[proto.Temp.BUTTON_LOG_CS] = new Cph_Temp_ButtonLog(); // [TEMP] CBT 까지 사용 후 제거

// guild packets
handlers[proto.Guild.CHECK_DUPLICATE_NAME] = new Cph_Guild_CheckForDuplicatesName();
handlers[proto.Guild.CREATE] = new Cph_Guild_Create();
handlers[proto.Guild.DISBAND] = new Cph_Guild_Disband();
handlers[proto.Guild.SHOW_LIST] = new Cph_Guild_ShowList();
handlers[proto.Guild.JOIN] = new Cph_Guild_Join();
handlers[proto.Guild.JOIN_CANCEL] = new Cph_Guild_JoinCancel();
handlers[proto.Guild.LEAVE] = new Cph_Guild_Leave();
handlers[proto.Guild.GET_MY_GUILD_INFO] = new Cph_Guild_GetMyGuildInfo();
handlers[proto.Guild.CHECKED_UPGRADE_POPUP] = new Cph_Guild_CheckedUpgradePopup();
handlers[proto.Guild.GET_DETAIL_INFO] = new Cph_Guild_GetDetailInfo();
handlers[proto.Guild.GET_LIGHT_INFO] = new Cph_Guild_GetLightInfo();
handlers[proto.Guild.CRAFT] = new Cph_Guild_Craft();
handlers[proto.Guild.PICK_UP_DAILY_REWARD] = new Cph_Guild_PickupDailyReward();
handlers[proto.Guild.GUILD_RECEIVE_WEEKLY_REWARD_MAIL] = new Cph_Guild_ReceiveWeeklyRewardMail();
handlers[proto.Guild.CRAFT_CREATE] = new Cph_Guild_Craft_Create();
handlers[proto.Guild.CRAFT_RECEIVE] = new Cph_Guild_Craft_Receive();
handlers[proto.Guild.CRAFT_DECREASE_EXPIRE_TIME] = new Cph_Guild_CraftDecreaseExpireTime();
handlers[proto.Guild.CRAFT_COMPLETE_EXPIRE_TIME] = new Cph_Guild_CraftCompleteExpireTime();
handlers[proto.Guild.SYNTHESIS_CREATE] = new Cph_Guild_Synthesis_Create();
handlers[proto.Guild.SYNTHESIS_RECEIVE] = new Cph_Guild_Synthesis_Receive();
handlers[proto.Guild.SYNTHESIS_DECREASE_EXPIRE_TIME] = new Cph_Guild_SynthesisDecreaseExpireTime();
handlers[proto.Guild.SYNTHESIS_COMPLETE_EXPIRE_TIME] = new Cph_Guild_SynthesisCompleteExpireTime();
handlers[proto.Guild.DONATE] = new Cph_Guild_Donate();
handlers[proto.Guild.BUY_DONATION_COUNT] = new Cph_Guild_BuyDonationCount();

handlers[proto.Guild.SELECT_GUILD_BUFF_CATEGORY] = new Cph_Guild_SelectGuildBuffCategory();
handlers[proto.Guild.LEARN_GUILD_BUFF] = new Cph_Guild_LearnGuildBuff();
handlers[proto.Guild.REGISTER_GUILD_BUFF_ITEM_FOR_UPGRADE] = new Cph_Guild_RegisterGuildBuffItem();
handlers[proto.Guild.RAID_OPEN] = new Cph_Guild_RaidOpen();

handlers[proto.Guild.RAID_GET_INFO] = new Cph_Guild_RaidGetInfo();
handlers[proto.Guild.RAID_GET_RANKING_PAGE] = new Cph_Guild_RaidGetRankingPage();
handlers[proto.Guild.RAID_PICKUP_REWARD] = new Cph_Guild_RaidPickupReward();
handlers[proto.Guild.RAID_BUY_TICKET] = new Cph_Guild_RaidBuyTicket();
handlers[proto.Guild.RAID_GET_PREV_RANKING] = new Cph_Guild_RaidGetPrevRanking();
handlers[proto.Guild.MANAGING_ACCEPT_JOIN] = new Cph_Guild_ManagingAcceptJoining();
handlers[proto.Guild.MANAGING_REFUSE_JOIN] = new Cph_Guild_ManagingRefuseToJoin();
handlers[proto.Guild.MANAGING_CHANGE_MEMBER_GRADE] = new Cph_Guild_ManagingChangeMemberGrade();
handlers[proto.Guild.MANAGING_KICK_MEMBER] = new Cph_Guild_ManagingKickMember();
handlers[proto.Guild.MANAGING_CHANGE_INFO] = new Cph_Guild_ManagingChangeInfo();
handlers[proto.Guild.MANAGING_DELEGATE_MASTER] = new Cph_Guild_ManagingDelegateMaster();

handlers[proto.Guild.GET_GULID_SHOP_SYNC_DATA] = new Cph_Guild_GetGuildShopSyncData();
handlers[proto.Guild.BUY_GULID_SHOP_PRODUCT] = new Cph_Guild_BuyGuildShopProduct();
handlers[proto.Guild.BUY_GUILD_RAID_BUFF] = new Cph_Guild_BuyGuildRaidBuff();

// BattleLobby packets.
handlers[proto.BattleLobby.LEAVE] = new Cph_BattleLobby_Leave();
handlers[proto.BattleLobby.ENTER] = new Cph_BattleLobby_Enter();

// raid packets.
handlers[proto.Raid.GET_RAID_INFO] = new Cph_Raid_GetInfo();
handlers[proto.Raid.GET_RANKING_PAGE] = new Cph_Raid_GetRankingPage();
handlers[proto.Raid.PICKUP_REWARD] = new Cph_Raid_PickupReward();
handlers[proto.Raid.PICK_RAID_BOSS] = new Cph_Raid_PickRaidBoss();
handlers[proto.Raid.GET_REWARD_STATES] = new Cph_Raid_GetRewardStates();
handlers[proto.Raid.PICK_RAID_BOSS_CANCEL] = new Cph_Raid_PickRaidBossCancel();

// friend packets.
handlers[proto.Friend.REQUEST_FRIEND] = new Cph_Friend_RequestFriend();
handlers[proto.Friend.CANCEL_FRIEND_REQUEST] = new Cph_Friend_CancelFriendRequest();
handlers[proto.Friend.DELETE_FRIEND] = new Cph_Friend_DeleteFriend();
handlers[proto.Friend.ACCEPT_FRIEND_REQUEST] = new Cph_Friend_AcceptFriendRequest();
handlers[proto.Friend.DENY_FRIEND_REQUEST] = new Cph_Friend_DenyFriendRequest();
handlers[proto.Friend.SEND_POINT] = new Cph_Friend_SendPoint();
handlers[proto.Friend.PICKUP_POINT] = new Cph_Friend_PickupPoint();

// manufacture packets.
handlers[proto.Manufacture.MANUFACTURE_START] = new Cph_Manufacture_Start();
handlers[proto.Manufacture.MANUFACTURE_RECEIVE] = new Cph_Manufacture_Receive();
handlers[proto.Manufacture.MANUFACTURE_DECREASE_EXPIRE_TIME] = new Cph_Manufacture_Decrease_Expire_Time();
handlers[proto.Manufacture.MANUFACTURE_COMPLETE_EXPIRE_TIME] = new Cph_Manufacture_Complete_Expire_Time();
handlers[proto.Manufacture.MANUFACTURE_UNLOCK_RECIPE] = new Cph_Manufacture_UnlockRecipe();
handlers[proto.Manufacture.MANUFACTURE_END] = new Cph_Manufacture_End();

// ----------------------------------------------------------------------------
// packetsForBackToLastGameState
// gameState 는 유저의 현재 상태를 나타내며 로그인 시 NONE(0)으로 초기화 되며
// 유저의 마지막 상태까지 진입을 시켜주기 위해 lastGameState 가 존재하고
// lastGameState 이 gameState 와 동일해지는 시점에 NONE(0)으로 초기화 됩니다.
// 유저의 마지막 상태까지 진입을 하는데 사용되는 packet 을
// packetsForBackToLastGameState 에 정의 해둔다.
// ----------------------------------------------------------------------------
const packetsForBackToLastGameState = {
  [proto.Auth.ENTER_WORLD]: true,
  [proto.Auth.APP_GUARD_CHECK_CS]: true,
  [proto.Town.ENTER]: true,
  [proto.Town.LOAD_COMPLETE]: true,
  [proto.Ocean.ARRIVE]: true,
  [proto.Ocean.ENTER]: true,
  [proto.Ocean.LAND_ENTER]: true,
  [proto.Ocean.ARRIVE_VILLAGE]: true,
  [proto.Ocean.VILLAGE_ENTER]: true,
  [proto.Ocean.LOAD_COMPLETE]: true,
  [proto.Ocean.SALVAGE_ENTER]: true,
  [proto.Battle.LOAD_COMPLETE]: true,
  [proto.Battle.RESUME]: true,
  [proto.Battle.START]: true,
  [proto.Battle.START_CHALLENGE]: true,
  [proto.Battle.START_ARENA]: true,
  [proto.Battle.START_RAID]: true,
  [proto.Duel.START]: true,
  [proto.Duel.RESUME]: true,
  [proto.BattleReward.ENTER]: true,
  [proto.BattleReward.LOAD_COMPLETE]: true,
  [proto.LandExploreReward.RECEIVE_ENTER]: true,
  [proto.BattleLobby.ENTER]: true,
  [proto.Battle.CANCEL]: true,
  [proto.Ocean.ENTER_CONTINUOUS_SWEEP_REWARD]: true,
};

// ------------------------------------------------------------------------------------------------
// packetsCanBeCalledAlways
// 로그인 후 lastGameState 까지 복구가 되지 않아도 사용될 수 있는 packet
// ------------------------------------------------------------------------------------------------
const packetsCanBeCalledAlways = {
  [proto.Town.MOVE_CS]: true,
  [proto.Town.BANK_WITHDRAW_INSTALLMENT_SAVINGS]: true,
  [proto.Ocean.MOVE_CS]: true,
  [proto.Ocean.END_AUTO_SAILING]: true,
  [proto.Ocean.REVEAL_REGION]: true,
  [proto.Ocean.INSPECT_TOWN]: true,
  [proto.Ocean.ADD_QUESTION_PLACE]: true,
  [proto.Ocean.INSPECT_VILLAGE]: true,
  [proto.Ocean.REVEAL_WORLD_MAP_TILE]: true,
  [proto.Raid.GET_RAID_INFO]: true,
  [proto.Raid.GET_REWARD_STATES]: true,
};

// ------------------------------------------------------------------------------------------------
// Pulblic functions.
// ---------------------------------------------------------------------------------------------------

export const exec = (user: User, packet: CPacket): Promise<any> => {
  const handler = handlers[packet.type];
  const packetType = packet.type;

  if (!handler) {
    mlog.error('Unknown packet type.', {
      userId: user.userId,
      packetType: packetType,
    });

    throw new MError('unknown-packet-type', MErrorCode.UNKNOWN_PACKET_TYPE, packet.type);
  }

  const rawLastGameState = user.userState.getRawLastGameState();

  if (rawLastGameState) {
    // 로그인 후 lastGameState 까지 복구가 안 된 상황. 복구가 완료된 경우 rawLastGameState 값은 NONE(0)

    // 로그인 후 lastGameState 까지 복구가 안 된 상황에서는 아래 조건에 맞지 않는 경우
    // 서버로 요청할 수 없다.
    if (
      !packetsForBackToLastGameState[packetType] &&
      !Object.values(proto.Common).includes(packetType) &&
      !Object.values(proto.Etc).includes(packetType) &&
      !Object.values(proto.Temp).includes(packetType) &&
      !Object.values(proto.Admin).includes(packetType) &&
      !Object.values(proto.Dev).includes(packetType) &&
      !packetsCanBeCalledAlways[packetType]
    ) {
      throw new MError(
        'can-not-be-used-packet-while-back-to-last-game-state',
        MErrorCode.CANNOT_BE_USED_PACKET_WHILE_BACK_TO_LAST_GAME_STATE,
        {
          packetType: packet.type,
          lastGameState: rawLastGameState,
        }
      );
    }
  }

  // 게임 상태 검증.
  if (!handler.testGameState(user)) {
    // mconf.invalidGameStateTolerance 에 지정된 milisec 이내에 잘못된 상태의 패킷이 오는 경우엔
    // 접속 종료가 되지 않도록, 너그러운 에러 처리를 한다.
    if (user.userState.canTolerateInvalidateGameState()) {
      throw new MError('invalid-game-state-tolerated.', MErrorCode.INVALID_GAME_STATE_TOLERATED, {
        packetType: packet.type,
        gameState: user.userState.getGameState(),
        lastGameState: user.userState.getLastGameState(),
        enterState: user.userState.getGameEnterState(),
        bEncounting: user.userEncount.getEncountState() ? true : false,
        bFriendlyEncounting: user.userFriendlyEncount.getEncountState() ? true : false,
        clashState: user.userClash.getMatchingState(),
      });
    }

    // 그 외의 경우엔 치명적 에러 처리.
    throw new MError('invalid-game-state.', MErrorCode.INVALID_GAME_STATE, {
      packetType: packet.type,
      gameState: user.userState.getGameState(),
      lastGameState: user.userState.getLastGameState(),
      enterState: user.userState.getGameEnterState(),
      bEncounting: user.userEncount.getEncountState() ? true : false,
      bFriendlyEncounting: user.userFriendlyEncount.getEncountState() ? true : false,
      clashState: user.userClash.getMatchingState(),
    });
  }

  return handler.exec(user, packet);
};
