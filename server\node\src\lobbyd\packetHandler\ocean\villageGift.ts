// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import { Container } from 'typedi';
import _, { min } from 'lodash';

import * as mutil from '../../../motiflib/mutil';
import * as cmsEx from '../../../cms/ex';
import { LobbyService } from '../../server';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import cms from '../../../cms';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { CashPayment, PointChange } from '../../userPoints';
import Ship, { ShipCargoChange, ShipCargoNub } from '../../ship';
import { Sync } from '../../type/sync';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import * as formula from '../../../formula';
import tuVillageGift from '../../../mysqllib/txn/tuVillageGift';
import { ClientPacketHandler } from '../index';
import { BuffSync } from '../../userBuffs';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'village_gift';
const add_rsn = null;

interface TradeGoods {
  cmsId: number;
  count: number;
}

interface RequestBody {
  villageCmsId: number;
  consumeDucat: number;
  bPermitExchange?: boolean;
  tradeGoods: TradeGoods[];
}

// ----------------------------------------------------------------------------
export class Cph_Ocean_VillageGift implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { villageCmsId, consumeDucat, bPermitExchange, tradeGoods } = body;

    const villageCms = cms.Village[villageCmsId];
    if (!villageCms) {
      throw new MError('invalid-village-cms-id', MErrorCode.INVALID_REQ_BODY_VILLAGE_GIFT, {
        body,
      });
    }

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureGameState(GAME_STATE.IN_OCEAN_VILLAGE);
    const fleetStat = user.companyStat.getFleetStat(cmsEx.FirstFleetIndex);

    // 우호도 100 이하이면  동작 불가
    const conditionFriendship: number = user.userVillage.getCurVillageFriendship();
    if (!user.userVillage.canProcessVillage(conditionFriendship)) {
      throw new MError(
        'process-village-enough-friendship',
        MErrorCode.NOT_ENOUGH_VILLAGE_FRIENDSHIP,
        {
          friendship: conditionFriendship,
        }
      );
    }

    const curTimeUtc = mutil.curTimeUtc();
    const timeCalculatedVillage = user.userVillage.getVillageWithTimeCalc(villageCmsId, curTimeUtc);
    if (timeCalculatedVillage.friendship > cms.Const.BuyVillageFriendShipLimitGrade.value) {
      throw new MError(
        'limit-increase-friendship-with-gift',
        MErrorCode.LIMIT_INCREASE_FRIENDSHIP_WITH_GIFT,
        {
          cmsId: villageCmsId,
          friendship: timeCalculatedVillage.friendship,
        }
      );
    }
    let increaseFriendship: number;
    let pointChanges: PointChange[];
    let cashPayments: CashPayment[];
    let cargoChanges: ShipCargoChange[];
    if (consumeDucat) {
      if (!_.isInteger(consumeDucat) || consumeDucat <= 0) {
        throw new MError('invalid-ducat', MErrorCode.INVALID_DUCAT_VILLAGE_FRIENDSHIP, { body });
      }

      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [{ cmsId: cmsEx.DucatPointCmsId, cost: consumeDucat }],
        bPermitExchange,
        { itemId: rsn },
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;

      increaseFriendship = formula.GetFriendshipToDucat(villageCms.lv, consumeDucat);
      let statVal =
        fleetStat.get(cmsEx.STAT_TYPE.BONUS_FRIENDSHIP_POINT) +
        fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.BONUS_FRIENDSHIP_POINT);

      statVal = Math.max(0, statVal);

      increaseFriendship += Math.floor((increaseFriendship * statVal) / 1000);
    } else {
      if (tradeGoods.length <= 0) {
        throw new MError('empty-trade-goods', MErrorCode.EMPTY_TRADE_GOODS_VILLAGE_FRIENDSHIP, {
          body,
        });
      }
      cargoChanges = _buildShipCargoChanges(
        [...user.userFleets.clone().getShips().values()],
        _.cloneDeep(tradeGoods)
      );
      increaseFriendship = formula.GetFriendshipToTradeGoods(villageCms, tradeGoods);
      const statVal =
        fleetStat.get(cmsEx.STAT_TYPE.BONUS_FRIENDSHIP_POINT) +
        fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.BONUS_FRIENDSHIP_POINT);
      increaseFriendship += Math.floor((increaseFriendship * statVal) / 1000);
    }

    const village = user.userVillage.getVillage(villageCmsId);
    // increaseFriendship 값은  0 이 될 수 있다.
    const newFriendship = Math.min(
      village.friendship + increaseFriendship,
      cms.Const.VillageFriendshipMaxValue.value
    );

    const maximumFriendship =
      village.maximumFriendship < newFriendship ? newFriendship : village.maximumFriendship;

    const buffSync: BuffSync = {
      sync: {},
    };
    return user.userPoints
      .tryConsumeCashs(cashPayments, buffSync.sync, user, { user, rsn, add_rsn })
      .then(() => {
        const { userDbConnPoolMgr } = Container.get(LobbyService);
        return tuVillageGift(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          newFriendship,
          maximumFriendship,
          villageCmsId,
          pointChanges,
          cargoChanges,
          []
        );
      })
      .then(() => {
        const oldFriendship = village.friendship;
        user.userVillage.updateCurVillageFriendship(newFriendship);
        _.merge<Sync, Sync>(buffSync.sync, {
          add: {
            villages: {
              [villageCmsId]: {
                friendship: newFriendship,
                maximumFriendship: maximumFriendship,
              },
            },
          },
        });

        if (cargoChanges && cargoChanges.length > 0) {
          user.userFleets.applyCargoChanges(cargoChanges, user, { user, rsn, add_rsn }, buffSync);
        }

        if (pointChanges) {
          _.merge<Sync, Sync>(
            buffSync.sync,
            user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
          );
        }
        const villageFriendshipCms = cmsEx.getVillageFriendshipCms(village.friendship);
        user.glog('village_friendship', {
          rsn,
          add_rsn,
          village_id: villageCmsId,
          village_name: villageCms.name,
          cv: newFriendship - oldFriendship, // 우호도 변동 수치
          rv: newFriendship,
          friendship_type: villageFriendshipCms ? villageFriendshipCms.name : null,
        });

        return user.sendJsonPacket(packet.seqNum, packet.type, buffSync);
      });
  }
}

function _buildShipCargoChanges(shipList: Ship[], tradeGoodsList: TradeGoods[]): ShipCargoChange[] {
  const shipCargoChanges: ShipCargoChange[] = [];
  for (const tradeGoods of tradeGoodsList) {
    shipCargoChanges.push(..._consumeTradeGoods(shipList, tradeGoods));
  }
  return shipCargoChanges;
}

function _consumeTradeGoods(shipList: Ship[], tradeGoods: TradeGoods): ShipCargoChange[] {
  const shipCargoChanges: ShipCargoChange[] = [];
  for (const ship of shipList) {
    if (tradeGoods.count === 0) {
      break;
    }

    const shipCargoNub: ShipCargoNub = ship.getCargo(tradeGoods.cmsId);
    if (shipCargoNub.quantity === 0) {
      continue;
    }

    const consumeCount = Math.min(shipCargoNub.quantity, tradeGoods.count);

    const prePointInvested: number = ship.getCargoPointInvested(shipCargoNub.cmsId);
    shipCargoNub.pointInvested = _calcPointInvested(
      shipCargoNub.quantity,
      prePointInvested,
      consumeCount
    );

    tradeGoods.count -= consumeCount;
    shipCargoNub.quantity -= consumeCount;

    shipCargoChanges.push({
      shipId: ship.nub.id,
      cmsId: shipCargoNub.cmsId,
      quantity: shipCargoNub.quantity,
      pointInvested: shipCargoNub.pointInvested,
    });
  }

  // 선박에  남은 교역품이 있다는 말은 유저 보유 수 보다  더 많은  수량을 클라이언트 가  보내면 안됨
  if (tradeGoods.count > 0) {
    throw new MError('empty-trade-goods', MErrorCode.OVER_CONSUME_TRADE_GOODS, {
      tradeGoods,
    });
  }
  return shipCargoChanges;
}

// u_ship_cargos 교역품 pointInvest ===  quantity * 교역품 price
function _calcPointInvested(
  oldQuentity: number,
  oldPointInvsted: number,
  quantityToUnLoad: number
): number {
  if (oldQuentity === quantityToUnLoad) {
    return 0;
  }

  const pointInvestedPer = Math.floor(oldPointInvsted / oldQuentity);
  return oldPointInvsted - pointInvestedPer * quantityToUnLoad;
}
