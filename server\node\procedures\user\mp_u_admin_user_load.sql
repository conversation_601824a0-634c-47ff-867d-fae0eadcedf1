CREATE PROCEDURE `mp_u_admin_user_load`(
  IN inUserId INT,
  IN inCurTImeUtc INT
)
label_body:BEGIN

  SELECT
      u_users.name AS name,
      u_users.lang,
      u_states.gameState,
      u_states.lastGameState,
      u_states.isOnline,
      u_soft_data.lastTownCmsId,
      u_soft_data.arrivalTownCmsId,
      u_users.nationCmsId,
      u_users.companyJobCmsId,
      u_soft_data.exp,
      u_soft_data.level,
      u_soft_data.isSlowdownEventChecked,
      u_soft_data.lastCompletedQuestCmsIdForRequest,
      u_users.lastRewardedAchievementPointCmsId,
      u_users.noviceSupplyCount,
      UNIX_TIMESTAMP(u_users.lastNoviceSupplyTimeUtc) AS lastNoviceSupplyTimeUtc,
      u_users.energy,
      UNIX_TIMESTAMP(u_users.lastUpdateEnergyTimeUtc) AS lastUpdateEnergyTimeUtc,
      u_users.palaceRoyalOrderCmsId,
      u_users.palaceRoyalOrderRnds,
      UNIX_TIMESTAMP(u_users.lastRoyalOrderCompletedTimeUtc) AS lastRoyalOrderCompletedTimeUtc,
      u_users.contractedCollectorTownBuildingCmsId,
      u_users.guildId,
      u_soft_data.tradeResetCount,
      UNIX_TIMESTAMP(u_soft_data.lastTradeResetTimeUtc) AS lastTradeResetTimeUtc,
      u_soft_data.karma,
      UNIX_TIMESTAMP(u_soft_data.lastKarmaUpdateTimeUtc) AS lastKarmaUpdateTimeUtc,
      u_soft_data.usedFreeTurnTakebackCount,
      u_soft_data.usedFreePhaseTakebackCount,
      UNIX_TIMESTAMP(u_soft_data.lastFreeTakebackUpdateTimeUtc) AS lastFreeTakebackUpdateTimeUtc,
      u_soft_data.quickModeCount,
      UNIX_TIMESTAMP(u_soft_data.lastQuickModeCountUpdateTimeUtc) AS lastQuickModeCountUpdateTimeUtc,
      UNIX_TIMESTAMP(u_users.lastCompanyJobUpdateTimeUtc) AS lastCompanyJobUpdateTimeUtc,
      u_users.countryIp,
      u_users.firstMateCmsId,
      u_users.westShipBuildLevel,
      u_users.westShipBuildExp,
      u_users.orientShipBuildLevel,
      u_users.orientShipBuildExp,
      u_users.representedMateCmsId,
      u_users.manufacturePoint,
      UNIX_TIMESTAMP(u_users.lastUpdateManufacturePointTimeUtc) AS lastUpdateManufacturePointTimeUtc
    FROM
      u_users, u_states, u_soft_data
    WHERE
      u_users.id = inUserId
      AND u_states.userId = inUserId
      AND u_soft_data.userId = inUserId;

  SELECT
      cmsId,
      loyalty,
      stateFlags,
      adventureExp,
      adventureLevel,
      tradeExp,
      tradeLevel,
      battleExp,
      battleLevel,
      adventureFame,
      tradeFame,
      battleFame,
      royalTitle,
      UNIX_TIMESTAMP(injuryExpireTimeUtc) AS injuryExpireTimeUtc,
      UNIX_TIMESTAMP(lastTalkTimeUtc) AS lastTalkTimeUtc,
      awakenLv,
      UNIX_TIMESTAMP(awakenTimeUtc) AS awakenTimeUtc,
      colorSkin,
      colorEye,
      colorHairAcc1,
      colorHairAcc2,
      colorHair,
      colorBody1,
      colorBody2,
      colorFaceAcc1,
      colorFaceAcc2,
      colorFaceAcc3,
      colorCape1,
      colorCape2,
      colorCape3,
      breastSize,
      trainingGrade,
      trainingPoints,
      equippedIllustCmsId

    FROM
      u_mates
    WHERE
      userId = inUserId;

  SELECT
      id,
      cmsId,
      dye1,
      dye2,
      dye3,
      dye4,
      dye5,
      dye6,
      equippedMateCmsId,
      isBound,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      enchantLevel
    FROM
      u_mate_equipments
    WHERE
      userId = inUserId;

  SELECT
      slot,
      cmsId
    FROM
      u_sidekick_mates
    WHERE
      userId = inUserId AND cmsId > 0;

  SELECT
      cmsId,
      value
    FROM
      u_points
    WHERE
      userId = inUserId;

  SELECT
      accumPoint,
      accumRate,
      accumCount,
      lastCmsId,
      UNIX_TIMESTAMP(lastDepositTimeUtc) AS lastDepositTimeUtc
    FROM
      u_installment_savings
    WHERE
      userId = inUserId;

  SELECT
      fleetIndex,
      battleFormationCmsId
    FROM
      u_fleets
    WHERE
      userId = inUserId;

  SELECT
      shipId,
      shipCmsId,
      rndStats,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      townCmsId
    FROM
      u_ship_buildings
    WHERE
      userId = inUserId;

  SELECT
      id,
      cmsId,
      assignment,
      fleetIndex,
      formationIndex,
      sailor,
      durability,
      permanentDamage,
      name,
      life,
      isLocked,
      rndStats,
      enchantedStatType1,
      enchantedStatValue1,
      enchantedStatType2,
      enchantedStatValue2,
      enchantedStatType3,
      enchantedStatValue3,
      enchantResult,
      enchantCount,
      rndStats,
      isBound,
      guid
    FROM
      u_ships
    WHERE
      userId = inUserId;

  SELECT
      shipId,
      slotIndex,
      mateCmsId,
      isLocked,
      shipSlotItemId
    FROM
      u_ship_slots
    WHERE
      userId = inUserId;

   SELECT
      shipId,
      cmsId,
      quantity,
      pointInvested
    FROM
      u_ship_cargos
    WHERE
      userId = inUserId AND quantity > 0;

  SELECT
      cmsId,
      count,
      unboundCount
    FROM
      u_items
    WHERE
      userId = inUserId AND (count > 0 OR unboundCount > 0);

  SELECT
      cmsId,
      level,
      exp,
      sailMasteryLevel,
      sailMasteryExp
    FROM
      u_ship_blueprints
    WHERE
      userId = inUserId;

  SELECT
      shipBlueprintCmsId,
      slotIndex,
      shipSlotCmsId
    FROM
      u_ship_blueprint_slots
    WHERE
      userId = inUserId;

  SELECT
      nationCmsId,
      reputation,
      UNIX_TIMESTAMP(updateTimeUtc) AS updateTimeUtc
    FROM
      u_reputations
    WHERE
      userId = inUserId;

  SELECT
      mateCmsId,
      intimacy,
      isMet
    FROM
      u_unemployed_mates
    WHERE
      userId = inUserId;

  SELECT
      id,
      cmsId,
      state,
      UNIX_TIMESTAMP(createTimeUtc) AS createTimeUtc,
      UNIX_TIMESTAMP(readTimeUtc) AS readTimeUtc,
      UNIX_TIMESTAMP(deleteTimeUtc) AS deleteTimeUtc,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      bShouldSetExpirationWhenReceiveAttachment,
      title,
      titleFormatValue,
      body,
      bodyFormatValue,
      attachment
    FROM
      u_direct_mails
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_quest_completion_fields
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      nodeIdx,
      blockId,
      state,
      blockState,
      UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
      uflags,
      lflags,
      r0, r1, r2, r3, r4, r5, r6, r7, r8, r9,
      userLevel,
      rnd1, rnd2, rnd3,
      accum1, accum2, accum3, accum4, accum5,
      requestSlotIdx,
      requestNationCmsId,
      startTotalSailedDays,
      isPaused,
      isAdminPaused
    FROM
      u_quest_contexts
    WHERE
      userId = inUserId;

  SELECT
      mateEquipmentId
    FROM
      u_mate_equipment_last_ids
    WHERE
      userId = inUserId;

  SELECT
      shipId
    FROM
      u_ship_last_ids
    WHERE
      userId = inUserId;

  SELECT
      directMailId
    FROM
      u_direct_mail_last_ids
    WHERE
      userId = inUserId;

  SELECT
      groupNo,
      cmsId,
      targetId,
      sourceType,
      sourceId,
      stack,
      UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
      UNIX_TIMESTAMP(endTimeUtc) AS endTimeUtc
    FROM
      u_world_buffs
    WHERE
      userId = inUserId;

  SELECT
      type,
      cmsId,
      quantity,
      receivedQuantity
    FROM
      u_battle_rewards
    WHERE
      userId = inUserId;

  SELECT
      losses,
      multiPvpLoss
    FROM
      u_game_over_losses
    WHERE
      userId = inUserId;

  SELECT
    daysForLoyaltyDecrease,
    daysForTownReset,
    totalSailedDays
  FROM
    u_sailings
  WHERE
    userId = inUserId;

  SELECT
      insuranceCmsId,
      unpaidTradeGoods,
      unpaidShip,
      unpaidSailor,
      unpaidDucat
    FROM
      u_insurances
    WHERE
      userId = inUserId;

  SELECT
      type,
      expanded
    FROM
      u_slot_expansions
    WHERE
      userId = inUserId;

  SELECT
      UNIX_TIMESTAMP(lastBlackMarketResetTimeUtc) AS lastBlackMarketResetTimeUtc,
      blackMarketResetCount
    FROM
      u_black_markets
    WHERE
      userId = inUserId;

  SELECT
      collectionCmsId,
      slotIndex,
      stack
    FROM
      u_collections
    WHERE
      userId = inUserId;

  SELECT
      water,
      food,
      lumber,
      ammo,
      tradeGoods,
      any
    FROM
      u_cargo_load_presets
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_discoveries
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      count,
      isRewarded
    FROM
      u_achievements
    WHERE
      userId = inUserId;

  SELECT
      category,
      UNIX_TIMESTAMP(lastResetTimeUtc) AS lastResetTimeUtc,
      cmsId1,
      count1,
      isRewarded1,
      cmsId2,
      count2,
      isRewarded2,
      cmsId3,
      count3,
      isRewarded3,
      cmsId4,
      count4,
      isRewarded4,
      cmsId5,
      count5,
      isRewarded5,
      cmsId6,
      count6,
      isRewarded6,
      cmsId7,
      count7,
      isRewarded7,
      cmsId8,
      count8,
      isRewarded8,
      cmsId9,
      count9,
      isRewarded9,
      cmsId10,
      count10,
      isRewarded10,
      isCategoryRewarded
    FROM
      u_tasks
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      target,
      count
    FROM
      u_contents_terms_progresses
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      UNIX_TIMESTAMP(expirationTimeUtc) AS expirationTimeUtc
    FROM
      u_tax_free_permits
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_revealed_world_map_tiles
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      sailor,
      overDraftedSailor,
      isInvested,
      isTradedGoods,
      arrestState
    FROM
      u_town_states
    WHERE
      userId = inUserId;

  SELECT
      idx,
      UNIX_TIMESTAMP(purchaseTimeUtc) AS purchaseTimeUtc
    FROM
      u_request_slots
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      amount,
      UNIX_TIMESTAMP(lastBuyingTimeUtc) AS lastBuyingTimeUtc
    FROM
      u_cash_shop_restricted_products
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
      UNIX_TIMESTAMP(endTimeUtc) AS endTimeUtc
    FROM
      u_cash_shop_fixed_term_products
    WHERE
      userId = inUserId;

  SELECT
      cashShopCmsId,
      accum
    FROM
      u_cash_shop_gacha_box_guarantees
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_reported_discoveries
    WHERE
      userId = inUserId;

  SELECT
      hash,
      data,
      UNIX_TIMESTAMP(createTimeUtc) AS createTimeUtc
    FROM
      u_last_reported_discoveries
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_sound_packs
    WHERE
      userId = inUserId;

  SELECT
      g0, g1, g2, g3, g4, g5, g6, g7, g8, g9
    FROM
      u_quest_global_registers
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_question_places
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_reported_world_map_tiles
    WHERE
      userId = inUserId;

  SELECT
      discoveryBox,
      boxes+0 as boxes,
      result,
      gainItem
    FROM
      u_land_explore_results
    WHERE
      userId = inUserId;

  SELECT
      sailPatternCmsId,
      color1,
      color2,
      color3
    FROM
      u_ship_sail_patterns
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_sail_crests
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_sail_pattern_colors
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_body_1_colors
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_body_2_colors
    WHERE
      userId = inUserId;

  SELECT
      sailPatternCmsId,
      sailCrestCmsId,
      bodyColor1,
      bodyColor2,
      bodyColorTint,
      camouflageCmsId
    FROM
      u_ship_customizings
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      friendship,
      UNIX_TIMESTAMP(lastDepartureTimeUtc) AS lastDepartureTimeUtc
    FROM
      u_villages
    WHERE
      userId = inUserId;

  SELECT
      mateCmsId,
      passiveCmsId,
      equipIndex
    FROM
      u_mate_passives
    WHERE
      userId = inUserId;

  SELECT
      mateCmsId,
      passiveCmsId,
      UNIX_TIMESTAMP(learnTimeUtc) AS learnTimeUtc
    FROM
      u_mate_passive_learnings
    WHERE
      userId = inUserId;

  SELECT
      questItemId
    FROM
      u_quest_item_last_ids
    WHERE
      userId = inUserId;

  SELECT
      id,
      itemCmsId,
      questCmsId,
      questRnds
    FROM
      u_quest_items
    WHERE
      userId = inUserId;

  SELECT
      eventPageCmsId,
      accum,
      consecutive,
      UNIX_TIMESTAMP(lastAttendanceTimeUtc) AS lastAttendanceTimeUtc,
      lastRewardedConsecutiveAttendanceCmsId
    FROM
      u_attendances
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      clearedDifficulty,
      clearedMissionField
    FROM
      u_challenges
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_mate_equipment_colors
    WHERE
      userId = inUserId;

  SELECT
      shipSlotItemId
    FROM
      u_ship_slot_item_last_ids
    WHERE
      userId = inUserId;

  SELECT
      id,
      shipSlotCmsId,
      isBound,
      isLocked,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      enchantLevel
    FROM
      u_ship_slot_items
    WHERE
      userId = inUserId;

  SELECT
      battleEndResult
    FROM
      u_battles
    WHERE
      userId = inUserId;

  SELECT
      cmsId
    FROM
      u_battle_formations
    WHERE
      userId = inUserId;

  SELECT
      townCmsId,
      intimacy,
      unlockInterest,
      dailyTalkCount,
      UNIX_TIMESTAMP(lastNominationRefreshTimeUtc) AS lastNominationRefreshTimeUtc,
      UNIX_TIMESTAMP(questBlockTimeUtc) AS questBlockTimeUtc
    FROM
      u_pub_staffs
    WHERE
      userId = inUserId;

  SELECT
      month,
      value
    FROM
      u_mileages
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      UNIX_TIMESTAMP(expiredTimeUtc) AS expiredTimeUtc,
      isEquipped
    FROM
      u_user_titles
    WHERE
      userId = inUserId;
    
  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_event_page_products
    WHERE
      userId = inUserId;
  
  SELECT
    offset,
    idxField+0 as idxField
  FROM
    u_ship_camouflages
  WHERE
    userId = inUserId;

  SELECT
    subSlotType,
    shipSlotItemId
  FROM
    u_costume_ship_slots
  WHERE
    userId = inUserId;

  SELECT
    cmsId,
    popupCount,
    UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
    UNIX_TIMESTAMP(coolTimeUtc) AS coolTimeUtc
  FROM
      u_hot_spots
  WHERE
      userId = inUserId;

  SELECT
    castingExp,
    castingLevel,
    cookingExp,
    cookingLevel,
    sewingExp,
    sewingLevel,
    handmadeExp,
    handmadeLevel,
    medicineExp,
    medicineLevel
  FROM
    u_manufacture_exp
  WHERE
    userId = inUserId;      
    
END
