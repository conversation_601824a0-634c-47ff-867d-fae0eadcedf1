// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MError, MErrorCode } from '../../motiflib/merror';
import { ItemChange } from '../../lobbyd/userInven';
import cms from '../../cms';
import { ITEM_TYPE } from '../../cms/itemDesc';

export const spName = 'mp_u_items_batch_update';
export const errorCode = MErrorCode.ITEM_UPDATE_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  itemChanges: ItemChange[]
): Promise<void> {
  return spFunction(connection, userId, JSON.stringify(itemChanges))
    .then((qr) => {
      // Check if the operation was successful
      if (qr.rows[0][0]['affectedRows'] < 0) {
        throw new MError(`${spName} failed`, errorCode, { itemChanges });
      }
    })
    .catch(catchHandler);
} 