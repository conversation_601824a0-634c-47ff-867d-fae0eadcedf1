// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';
import assert from 'assert';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { CHARACTER_TYPE } from '../../../cms/ex';
import * as formula from '../../../formula';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import tuRecruitMate from '../../../mysqllib/txn/tuRecruitMate';
import { MateEquipmentNub } from '../../../motiflib/model/lobby';
import Mate, { MateUtil } from '../../mate';
import { LobbyService } from '../../server';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { MateNub } from '../../../motiflib/model/lobby';
import { Sync, Resp } from '../../type/sync';
import { AccumulateParam } from '../../userAchievement';
import { TownManager } from '../../townManager';
import { MateDesc } from '../../../cms/mateDesc';
import { BuffSync } from '../../userBuffs';
import { ClientPacketHandler } from '../index';
import UserPoints, { PointConsumptionCostParam } from '../../userPoints';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import mconf from '../../../motiflib/mconf';
import { needMateRecruitingTermsCheck } from '../../../cms/mateRecruitingGroupDesc';

// ----------------------------------------------------------------------------
// 여관에서 항해사 고용.
// ----------------------------------------------------------------------------

const rsn = 'pub_recruit_mate';
const add_rsn = null;

enum MATE_RECRUITING_INDEX {
  NEGOTIATION = 1,
  IMMEDIATELY = 2,
}

interface RequestBody {
  mateCmsId: number;
  recruitIndex: number;
  bPermitExchange?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Town_PubRecruitMate implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureInTown();

    const body: RequestBody = packet.bodyObj;
    const { mateCmsId, recruitIndex, bPermitExchange } = body;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    const townMates = user.userMates.getCurTownMates(user.userTown);

    // Check mate.
    const mateCms = cms.Mate[mateCmsId];
    const townMate = townMates.mates[mateCmsId];
    if (!mateCms || !townMate) {
      throw new MError('invalid-mate-cms-id', MErrorCode.INVALID_MATE_CMS_ID, {
        mateCmsId,
      });
    }

    // if (recruitIndex === MATE_RECRUITING_INDEX.IMMEDIATELY && mconf.binaryCode === 'GL') {
    //   throw new MError('invalid-binary-code', MErrorCode.PUB_RECRUIT_MATE_INVALID_BINARY_CODE, {
    //     userId: user.userId,
    //     binaryCode: mconf.binaryCode,
    //   });
    // }

    // Check time. 서버 클라간 시간이 정확히 같지 않기 때문에 100초의 여유를 둔다.
    const curTimeUtc = mutil.curTimeUtc();
    const curLastUpdateTimeUtc = townMates.lastUpdateTimeUtc;
    const mateResetSeconds = cms.Const.RecruitResetCycleSec.value;
    if (curLastUpdateTimeUtc + mateResetSeconds <= curTimeUtc - 100) {
      throw new MError('time-is-expired', MErrorCode.PUB_MATE_TIME_EXPIRED, {
        mateCmsId,
        curLastUpdateTimeUtc,
        curTimeUtc,
        diff: curTimeUtc - curLastUpdateTimeUtc,
      });
    }

    // https://jira.line.games/browse/UWO-7925
    // https://jira.line.games/browse/UWO-19699 특권 보너스,
    // cms.MateRecruitingGroup.isMustAppear 이 true 또는 mustAppearEvent 조건 만족인 경우
    // cms.MateRecruitingGroup.contentsTerms 을 검사해야됨
    const townCms = cms.Town[user.userTown.getTownCmsId()];
    const mateRecruitingGroupCms = cmsEx.getMateRecruitingGroupByGroupAndMateCmsId(
      townCms.mateRecruitingGroup,
      mateCmsId
    );
    const eventPageProducts = user.userCashShop.getEventPageProducts();
    if (
      !mateRecruitingGroupCms ||
      (needMateRecruitingTermsCheck(mateRecruitingGroupCms, eventPageProducts, curTimeUtc) &&
        !user.userContentsTerms.isValidContentsTerms(mateRecruitingGroupCms.contentsTerms, user))
    ) {
      throw new MError('contents-terms-invalid', MErrorCode.CONTENTS_TERMS_INVALID, {
        mateCmsId,
        townCmsId: townCms.id,
        mateRecruitingGroupCms,
        contentsTerms: mateRecruitingGroupCms.contentsTerms,
      });
    }

    // Calculate price.
    const mateRecruitingCms = mateCms.mateRecruiting;
    const recruitPoint = mateRecruitingCms.recruitPoint[recruitIndex - 1];
    const oldIsRenegotiation: boolean = townMate.isRenegotiation;
    let price = recruitPoint.Val;
    if (recruitPoint.Id === cmsEx.DucatPointCmsId) {
      price = formula.CalcPubMateRecruitingPrice(
        recruitPoint.Val,
        oldIsRenegotiation,
        cms.Const.MateRecruitingRetrySale.value,
        getRecruitMateStatVal(user, townCms.id, mateCms)
      );
    }

    const pointCost: PointConsumptionCostParam = { cmsId: recruitPoint.Id, cost: price };
    const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
      [pointCost],
      bPermitExchange,
      { itemId: rsn },
      true
    );

    const constCms = cms.Const;
    const oldIntimacy = user.userMates.getUnemployedMateIntimacy(mateCmsId);
    const oldIntimacyGrade = cmsEx.getUnemployedMateIntimacyGrade(oldIntimacy);

    let intimacy = oldIntimacy;

    const mate: MateNub = Mate.defaultNub(mateCmsId);

    const bNegotiation = recruitPoint.Id === cmsEx.DucatPointCmsId;
    // "고용 협상"/"즉시 고용(캐시)" 여부

    const probabilityAndLoyalty = mateRecruitingCms.closeness[oldIntimacyGrade - 1];
    const probability = probabilityAndLoyalty.Ratio;
    // probability 10000 means 100%.

    // Do gacha.
    let isSucceeded = false;
    if (bNegotiation) {
      user.userContentsTerms.ensureContentsTerms(mateRecruitingCms.contentsTerms, user);

      if (mateRecruitingCms.isGemOnly) {
        throw new MError('only-can-recruit-by-cash', MErrorCode.ONLY_CAN_RECRUIT_BY_CASH, {
          body,
        });
      }

      // 협상 대기 중인 상태인지 확인
      // 서버 클라간 시간이 정확히 같지 않기 때문에, 얼마간의 여유를 둔다.
      if (
        townMate.negoWaitExpirationTimeUtc &&
        curTimeUtc < townMate.negoWaitExpirationTimeUtc - cms.Const.TimeCostFreeTimeSec.value
      ) {
        throw new MError(
          'wait-time-has-not-expired',
          MErrorCode.MATE_RECRUITING_NEGO_WAIT_EXPIRATION_TIME_NOT_YET_EXPIRED,
          {
            mateCmsId,
            curTimeUtc,
            negoWaitExpirationTimeUtc: townMate.negoWaitExpirationTimeUtc,
            diff: curTimeUtc - townMate.negoWaitExpirationTimeUtc,
          }
        );
      }

      const rand = Math.floor(Math.random() * 10000);
      if (rand < probability) {
        isSucceeded = true;
      }
      mate.loyalty = probabilityAndLoyalty.Loyalty;
    } else {
      // If user use cash, it always succeeds.
      isSucceeded = true;
      mate.loyalty = cms.Const.RecruitCashLoyalty.value;
    }

    // If mate is admiral, loyalty is null.
    if (mateCms.character.charType === CHARACTER_TYPE.LEADERABLE_MATE) {
      mate.loyalty = null;
      mate.royalTitle = cmsEx.getAdmiralByMateCmsId(mateCmsId).startRoyalTitelId;
    }

    // Calculate renegotiation if gacha is failed.
    let isRenegotiation = false;
    let renegotiationCount = townMate.renegotiationCount;
    if (!isSucceeded && townMate.renegotiationCount < constCms.MateRecruitingRetryCount.value) {
      const rand = Math.floor(Math.random() * 100);
      if (rand < constCms.MateRecruitingRetryPer.value) {
        isRenegotiation = true;
        renegotiationCount++;
      }
    }

    // 협상 했을 때, 고용 실패이고 재협상이 아닌 경우 다음 협상 대기시간 적용
    let newNegoWaitExpirationTimeUtc: number | undefined;
    if (bNegotiation && !isSucceeded && !isRenegotiation) {
      assert(mateRecruitingCms.failResetCycleSec !== undefined);
      newNegoWaitExpirationTimeUtc = curTimeUtc + mateRecruitingCms.failResetCycleSec;
    }

    if (!isSucceeded) {
      const maxIntimacyGrade = cms.Const.MateRecruitingClosenessCountMax.value;
      const maxIntimacyValue = cms.Const['MateRecruitngClosenessCount' + maxIntimacyGrade].value;
      const val = mateRecruitingCms.recruitFailClosenessVal ?? 0;
      intimacy = mutil.clamp(intimacy + val, 0, maxIntimacyValue);
    }

    // 항해사 기본 장비 지급
    let mateEquipsToAdd: MateEquipmentNub[];
    if (isSucceeded && mateCms.CEquipId && mateCms.CEquipId.length > 0) {
      mateEquipsToAdd = [];
      for (let i = 0; i < mateCms.CEquipId.length; i++) {
        const cequipCmsId = mateCms.CEquipId[i];
        const mateEquipmentNub = user.userMates.buildMateEquipmentNub(
          cequipCmsId,
          mateCmsId,
          1,
          0,
          curTimeUtc
        );
        mateEquipmentNub.id += i;
        mateEquipsToAdd.push(mateEquipmentNub);
      }
    }

    // 인연 연대기 클리어 시 항해사 효과 추가.
    let addedPassives: { mateCmsId: number; passiveCmsId: number }[];
    if (isSucceeded) {
      addedPassives = MateUtil.getRelationChronicleLearnablePassives(
        user.userMates,
        user.questManager,
        mateCms.id
      );
    }

    const townCmsId = user.userTown.getTownCmsId();

    const resp: BuffSync = {
      sync: {},
    };

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return user.userPoints
      .tryConsumeCashs(pcChanges.cashPayments, resp.sync, user, {
        user,
        rsn,
        add_rsn,
        exchangeHash,
      })
      .then(() => {
        return tuRecruitMate(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          pcChanges.pointChanges,
          townCmsId,
          mateCmsId,
          isSucceeded,
          intimacy,
          townMate.index,
          renegotiationCount,
          isRenegotiation ? 1 : 0,
          newNegoWaitExpirationTimeUtc !== undefined
            ? newNegoWaitExpirationTimeUtc
            : townMate.negoWaitExpirationTimeUtc,
          mate,
          mateEquipsToAdd,
          addedPassives
        );
      })
      .then(() => {
        // glog
        user.glog('pub_mate_recruit', {
          rsn,
          add_rsn,

          town_id: user.userTown.getTownCmsId(),
          town_name: townCms.name,
          mate_id: mateCmsId,
          mate_name: displayNameUtil.getMateDisplayName(mateCmsId),
          friendship: oldIntimacy, // 협상 전 친밀도
          friendship_grade: oldIntimacyGrade, // 협상 전 친밀도 등급
          success_rate: probability / 10000, // [0, 1] ex) 0.123
          flag: bNegotiation ? 0 : 1, // 고용 협상: 0, 즉시 고용: 1
          is_success: isSucceeded ? 1 : 0, // 실패: 0, 성공: 1
          reset_time:
            newNegoWaitExpirationTimeUtc !== undefined // 실패시. (단위: 초)
              ? mateRecruitingCms.failResetCycleSec
              : null,
          pr_data: [{ type: pointCost.cmsId, amt: pointCost.cost }],
          is_discount: oldIsRenegotiation ? 1 : 0, // "재협상(협상 할인)" 상태였는지
          exchange_hash: exchangeHash,
        });
        // --

        _.merge<Sync, Sync>(
          resp.sync,
          user.userPoints.applyPointChanges(pcChanges.pointChanges, { user, rsn, add_rsn })
        );

        if (isSucceeded) {
          user.userMates.addNewMate(mate, user.companyStat, user, { user, rsn, add_rsn }, resp);

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              mates: {
                [mate.cmsId]: user.userMates.getMate(mate.cmsId).getNub(),
              },
            },
          });
          resp.sync.remove = {
            towns: {
              [townCmsId]: { myPubMates: { mates: [mateCmsId.toString()] } },
            },
            unemployedMates: [mateCmsId.toString()],
          };
          delete townMates.mates[mateCmsId];
          user.userMates.deleteUnemployedMate(mateCmsId);

          if (addedPassives) {
            for (const elem of addedPassives) {
              const userMate = user.userMates.getMate(elem.mateCmsId);
              userMate.addPassive(elem.passiveCmsId, 0, resp.sync);
            }
          }
        } else {
          _.merge<Sync, Sync>(resp.sync, {
            add: {
              unemployedMates: {
                [mateCmsId]: {
                  mateCmsId,
                  intimacy,
                },
              },
              towns: {
                [townCmsId]: {
                  myPubMates: {
                    mates: {
                      [mateCmsId]: {
                        isRenegotiation,
                        renegotiationCount,
                      },
                    },
                  },
                },
              },
            },
          });

          user.userMates.setUnemployedMateIntimacy(mateCmsId, intimacy, {
            user,
            rsn,
            add_rsn,
          });
          townMate.isRenegotiation = isRenegotiation;
          townMate.renegotiationCount = renegotiationCount;

          if (newNegoWaitExpirationTimeUtc !== undefined) {
            townMate.negoWaitExpirationTimeUtc = newNegoWaitExpirationTimeUtc;
            _.merge<Sync, Sync>(resp.sync, {
              add: {
                towns: {
                  [townCmsId]: {
                    myPubMates: {
                      mates: {
                        [mateCmsId]: {
                          negoWaitExpirationTimeUtc: newNegoWaitExpirationTimeUtc,
                        },
                      },
                    },
                  },
                },
              },
            });
          }
        }

        if (mateEquipsToAdd) {
          for (const elem of mateEquipsToAdd) {
            _.merge<Sync, Sync>(
              resp.sync,
              user.userMates.addMateEquipment(elem, { user, rsn, add_rsn })
            );
            if (elem.equippedMateCmsId) {
              user.userMates.equipMateEquipment(
                elem.equippedMateCmsId,
                elem.id,
                user.companyStat,
                user.userPassives,
                user.userFleets,
                user.userSailing,
                user.userTriggers,
                user.userBuffs,
                resp.sync,
                { user, rsn, add_rsn }
              );
            }
          }
        }

        // accumulate achievement
        const accums: AccumulateParam[] = [];
        if (isSucceeded) {
          accums.push(
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SUCCESS_RECRUIT_MATE,
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_MATE,
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_NATION_MATE,
              targets: [mateCms.nationId],
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_MATE,
              targets: [mateCmsId],
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
              targets: [mateCmsId],
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
              targets: [mateCmsId],
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
              targets: [mateCmsId],
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
              targets: [mateCmsId],
              addedValue: 3,
            }
          );

          // language
          for (let i = 1; i <= cmsEx.getMateHighestLanguageLevel(mateCmsId); i++) {
            const accum: AccumulateParam = {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.MATE_LANGUAGE_LEVEL,
              targets: [i],
              addedValue: 1,
            };
            accums.push(accum);
          }
        } else {
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.FAIL_RECRUIT_MATE,
            addedValue: 1,
          });
        }

        // admiral
        if (isSucceeded && mateCms.character.charType === CHARACTER_TYPE.LEADERABLE_MATE) {
          accums.push(
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_ADMIRAL,
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_ADMIRAL,
              targets: [mateCmsId],
              addedValue: 1,
            }
          );

          // royal title
          const mateRoyalTitle = mate.royalTitle;
          let targets = cmsEx.getAchievementTermsTargets(cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE, 0);

          if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
            for (const target of targets) {
              if (mateRoyalTitle < target) {
                break;
              }
              if (target !== mateRoyalTitle) {
                continue;
              }

              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE,
                targets: [target],
                addedValue: 1,
              });
            }
          }

          targets = cmsEx.getAchievementTermsTargets(
            cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
            1
          );
          if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
            for (const target of targets) {
              if (mateRoyalTitle < target) {
                break;
              }
              if (target !== mateRoyalTitle) {
                continue;
              }

              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
                targets: [mateCmsId, target],
                addedValue: 1,
              });
            }
          }
        }

        return user.userAchievement.accumulate(accums, user, resp.sync, { user, rsn, add_rsn });
      })
      .then(() => {
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, resp);
      });
  }
}

/*
  항구의 언어를 보유하고 있지 않더라도 여관에서 고용할 항해사가
  선단에 있는 항해사의 언어를 보유하고 있다면 항구에 언어가 보유한 것과 동일한 효과 적용
  https://wiki.line.games/pages/viewpage.action?pageId=76090095
*/
function getRecruitMateStatVal(user: User, townCmsId: number, mateCms: MateDesc): number {
  const townManager = Container.get(TownManager);
  const languageIds = new Set<number>(townManager.getLanguages(townCmsId));

  mateCms.language.forEach((lang) => {
    languageIds.add(lang.Id);
  });

  const langLevel = user.userFleets
    .getFleet(cmsEx.FirstFleetIndex)
    .getHighestLanguageLevel([...languageIds], user.userMates);
  return cmsEx.getRecruitMateBuildingBuffStat(langLevel);
}
