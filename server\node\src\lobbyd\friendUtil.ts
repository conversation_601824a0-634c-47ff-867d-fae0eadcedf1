// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import cms from '../cms';
import { User } from './user';
import { MError, MErrorCode } from '../motiflib/merror';
import { FRIEND_NOTIFICATION_TYPE } from '../motiflib/model/lobby';
import { curTimeUtc } from '../motiflib/mutil';
import { Sync } from './type/sync';
import { Container } from 'typedi/Container';
import { LobbyService } from './server';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import { getUserDbShardId } from '../mysqllib/mysqlUtil';
import { CalcContentsResetTimePassingCount } from '../formula';
import UserFriends, { FriendNub, FriendPointNub, FRIEND_STATE } from './userFriends';
import { GuildUtil } from './guildUtil';
import * as mutil from '../motiflib/mutil';
import puFriendLoad, { Result } from '../mysqllib/sp/puFriendLoad';
import puFriendAdd from '../mysqllib/sp/puFriendAdd';
import puFriendDelete from '../mysqllib/sp/puFriendDelete';
import puFriendUpdate from '../mysqllib/sp/puFriendUpdate';
import puFriendPointUpdate from '../mysqllib/sp/puFriendPointUpdate';
import puFriendPointLoad from '../mysqllib/sp/puFriendPointLoad';
import { EnergyChange } from './userEnergy';
import tuPickupFriendPoint from '../mysqllib/txn/tuPickupFriendPoint';
import { getUserLightInfos, UserLightInfo } from '../motiflib/userCacheRedisHelper';
import tuDeleteFriend from '../mysqllib/txn/tuDeleteFriend';
import { Promise } from 'bluebird';
import { __ } from 'ramda';
import { DbConnPoolManager } from '../mysqllib/pool';
import mhttp from '../motiflib/mhttp';

const rsn = 'friend_point';
const add_rsn = null;

export namespace FriendUtil {
  //=========================================================================================================
  // 친구요청 신청하기.
  // 최대 인원 및 DB 에러등으로 친구요청이 불가능하거나, 상대방의 친구요청받기가 불가능할 경우 친구요청은 실패.
  //=========================================================================================================
  export function requestFriend(user: User, friendUserId: number): Promise<Sync> {
    const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } =
      Container.get(LobbyService);
    const worldConfg = mconf.getWorldConfig();
    const userFriends: UserFriends = user.userFriends;
    const now = mutil.curTimeUtc();

    //------------------------------------------------------------------------------------------------
    // number타입 체크를 안할경우, 바로 아래에 있는 본인여부 조건에서 무조건 통과가 되기때문에 무결성 체크필요.
    //------------------------------------------------------------------------------------------------
    if (typeof friendUserId !== 'number') {
      throw new MError('invalid-other-userid', MErrorCode.INVALID_OTHER_USER_ID, {
        friendUserId,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 본인에게 친구 요청 불가능.
    //------------------------------------------------------------------------------------------------
    if (user.userId === friendUserId) {
      throw new MError('cannot-request-yourself', MErrorCode.CANNOT_REQUEST_YOURSELF, {
        friendUserId,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 최대 친구 수 검사.
    //------------------------------------------------------------------------------------------------
    if (userFriends.getCountByState(FRIEND_STATE.ESTABLISHED) >= cms.Const.FriendLimit.value) {
      throw new MError('the-number-of-friends-is-full', MErrorCode.NUMBER_OF_FRIENDS_IS_FULL, {
        friendUserId,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 최대 친구요청 수 검사.
    //------------------------------------------------------------------------------------------------
    if (userFriends.getCountByState(FRIEND_STATE.REQUEST) >= cms.Const.FriendRequestLimit.value) {
      throw new MError('the-number-of-requests-is-full', MErrorCode.NUMBER_OF_REQUESTS_IS_FULL, {
        friendUserId,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 대상이 친구상태 or 요청상태 or 요청받은상태에서는 실패.
    //------------------------------------------------------------------------------------------------
    if (userFriends.friends[friendUserId]) {
      const state: FRIEND_STATE = userFriends.friends[friendUserId].state;

      // 이미 친구상태?
      if (state === FRIEND_STATE.ESTABLISHED) {
        throw new MError('already-friend', MErrorCode.ALREADY_FRIEND, {
          friendUserId,
        });
      }
      // 요청을 보냈거나, 받은상태?
      if (state === FRIEND_STATE.REQUEST || state === FRIEND_STATE.RECEIVED) {
        throw new MError('friend-request-is-already-registered', MErrorCode.ALREADY_REGISTERED, {
          friendUserId,
          state,
        });
      }
    }

    // 차단유저 확인.
    const volanteUserId: string = user.userId.toString();
    return mhttp.chatd
      .getMuteUserIds(volanteUserId)
      .then((result) => {
        const idx = result.findIndex((blockedUser: number) => friendUserId === blockedUser);
        if (idx !== -1) {
          throw new MError('you-have-blocked-the-user', MErrorCode.BLOCKED_USER, {
            friendUserId,
          });
        }

        return mhttp.chatd.getMuteUserIds(friendUserId.toString()).then((result) => {
          const idx = result.findIndex((blockedUser: number) => user.userId === blockedUser);
          if (idx !== -1) {
            throw new MError('you-have-been-blocked-by-the-user', MErrorCode.BLOCKED_BY_TARGET, {
              friendUserId,
            });
          }
        });
      })
      .then(() => {
        return getUserLightInfos(
          [friendUserId],
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
          //------------------------------------------------------------------------------------------------
          // 존재하지 않는 유저는 실패.
          //------------------------------------------------------------------------------------------------
          if (!userLightInfos[friendUserId]) {
            throw new MError('cannot-find-the-user', MErrorCode.CANNOT_FIND_USER, {
              friendUserId,
            });
          }

          //------------------------------------------------------------------------------------------------
          // 상대방이친구요청을 받을 수 있는지 확인.
          //------------------------------------------------------------------------------------------------
          return puFriendLoad(
            userDbConnPoolMgr.getPoolByShardId(getUserDbShardId(friendUserId)),
            friendUserId
          )
            .then((results: Result[]) => {
              //------------------------------------------------------------------------------------------------
              // 상대방이 친구 수가 꽉찬경우.
              //------------------------------------------------------------------------------------------------
              const numFriends = results.filter(
                (elem) => elem.state === FRIEND_STATE.ESTABLISHED
              ).length;
              if (numFriends >= cms.Const.FriendLimit.value) {
                throw new MError(
                  'recipient-friends-are-full',
                  MErrorCode.OTHER_USER_FRIENDS_ARE_FULL,
                  {
                    friendUserId,
                  }
                );
              }

              //------------------------------------------------------------------------------------------------
              // 상대방이 친구요청을 더이상 받을 수 없는 경우
              //------------------------------------------------------------------------------------------------
              const numReceiveds = results.filter(
                (elem) => elem.state === FRIEND_STATE.RECEIVED
              ).length;
              if (numReceiveds >= cms.Const.FriendAcceptLimit.value) {
                throw new MError(
                  'recipient-Accepts-are-full',
                  MErrorCode.OTHER_USER_ACCEPTS_ARE_FULL,
                  {
                    friendUserId,
                  }
                );
              }

              //------------------------------------------------------------------------------------------------
              // 이미 등록되어있는지 확인.
              //------------------------------------------------------------------------------------------------
              const friend = results.find((elem) => elem.friendUserId === user.userId);
              if (friend) {
                throw new MError('already-registered', MErrorCode.ALREADY_REGISTERED, {
                  friendUserId,
                  state: friend.state,
                });
              }
            })
            .then(() => {
              return puFriendAdd(
                userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
                user.userId,
                friendUserId,
                FRIEND_STATE.REQUEST,
                now
              )
                .then(() => {
                  return puFriendAdd(
                    userDbConnPoolMgr.getPoolByShardId(getUserDbShardId(friendUserId)),
                    friendUserId,
                    user.userId,
                    FRIEND_STATE.RECEIVED,
                    now
                  ).catch((e1) => {
                    // 롤백....
                    return puFriendDelete(
                      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
                      user.userId,
                      friendUserId
                    )
                      .catch((e2) => {
                        // 롤백에 실패할 경우 로그를 남겨주도록 하자.
                        mlog.error('[friend-request] rollback-query-error', {
                          userId: user.userId,
                          friendUserId,
                          error: e2.message,
                          stack: e2.stack,
                        });
                      })
                      .finally(() => {
                        throw e1;
                      });
                  });
                })
                .catch((e1) => {
                  throw new MError(
                    '[friend-request] faild-query',
                    MErrorCode.FRIEND_REQUEST_QUERY_ERROR,
                    {
                      userId: user.userId,
                      friendUserId,
                      error: e1.message,
                      stack: e1.stack,
                    }
                  );
                });
            })
            .then(() => {
              //------------------------------------------------------------------------------------------------
              // 상대방에게 친구요청 알림 전달(상대방이 온라인일 경우)
              //------------------------------------------------------------------------------------------------
              return notifyOnlineFriends(
                user.userId,
                user.userName,
                [friendUserId],
                FRIEND_NOTIFICATION_TYPE.REQUESTED,
                now
              );
            })
            .then(() => {
              return getUserLightInfos(
                [friendUserId],
                userCacheRedis,
                userRedis,
                guildRedis,
                townRedis,
                userDbConnPoolMgr,
                worldConfg.mysqlUserDb.shardFunction
              ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
                const userLightInfo = userLightInfos[friendUserId];
                if (userLightInfo) {
                  user.glog('friend_request', {
                    rsn: 'friend_request',
                    add_rsn: null,
                    friend_nid: userLightInfo.pubId,
                    friend_gameUserId: friendUserId,
                    type: 1, // 신청 1: 취소 : 0
                  });
                }
                return;
              });
            })
            .then(() => {
              const nub: FriendNub = {
                friendUserId: friendUserId,
                state: FRIEND_STATE.REQUEST,
                regTimeUtc: now,
              };
              return userFriends.updateFriend(nub);
            });
        });
      });
  }

  //=========================================================================================================
  // 친구요청 취소,거절 및  친구제거시 양쪽 유저의 정보를 삭제합니다.
  //=========================================================================================================
  export function cancelFriend(
    user: User,
    friendUserId: number,
    notiType: FRIEND_NOTIFICATION_TYPE
  ): Promise<Sync> {
    const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } =
      Container.get(LobbyService);
    const worldConfg = mconf.getWorldConfig();
    const userFriends: UserFriends = user.userFriends;

    //------------------------------------------------------------------------------------------------
    // 등록된 정보에 없을 경우 실패.
    //------------------------------------------------------------------------------------------------
    if (!userFriends.friends[friendUserId]) {
      throw new MError('cannot-find-the-user', MErrorCode.CANNOT_FIND_USER, {
        friendUserId,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 취소타입에 따라, 상태가 일치한지 확인.
    // 즉, 요청취소는 요청상태이어야하며, 요청거절은 요청받은 상태이어야한다.
    //------------------------------------------------------------------------------------------------
    if (notiType === FRIEND_NOTIFICATION_TYPE.CANCELED) {
      if (userFriends.friends[friendUserId].state !== FRIEND_STATE.REQUEST) {
        throw new MError('invalid-friend-state', MErrorCode.INVALID_FRIEND_STATE, {
          friendUserId,
          state: userFriends.friends[friendUserId].state,
          notiType,
        });
      }
    } else if (notiType === FRIEND_NOTIFICATION_TYPE.DENIED) {
      if (userFriends.friends[friendUserId].state !== FRIEND_STATE.RECEIVED) {
        throw new MError('invalid-friend-state', MErrorCode.INVALID_FRIEND_STATE, {
          friendUserId,
          state: userFriends.friends[friendUserId].state,
          notiType,
        });
      }
    }

    //------------------------------------------------------------------------------------------------
    // 양쪽모두 친구삭제한다.일단 요청자 부터..
    //------------------------------------------------------------------------------------------------
    return puFriendDelete(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      friendUserId
    )
      .then(() => {
        // 상대방도 삭제.
        return puFriendDelete(
          userDbConnPoolMgr.getPoolByShardId(getUserDbShardId(friendUserId)),
          friendUserId,
          user.userId
        ).catch((e) => {
          // 삭제는 양쪽 모두 완전한 동기화 대신 실패하더라도, 요청자는 그대로 삭제해주고 로그를 남기는 방식으로 처리.
          // 양쪽 모두 완벽히 삭제가 안되더라도, 한쪽유저만 삭제할 수 있으면 그대로 처리해 주도록 한다.
          // 시스템 에러등으로 삭제가 안될 경우 유저입장에서는 답답할 수 있기 때문이다.
          mlog.error('[friend] failed-to-cancel-both-of-friends', {
            userId: user.userId,
            friendUserId,
            notiType: FRIEND_NOTIFICATION_TYPE[notiType],
            nub: userFriends.friends[friendUserId],
            error: e.message,
            stack: mconf.isDev ? undefined : e.stack,
          });
        });
      })

      .then(() => {
        return notifyOnlineFriends(
          user.userId,
          user.userName,
          [friendUserId],
          notiType,
          curTimeUtc()
        );
      })
      .then(() => {
        return getUserLightInfos(
          [friendUserId],
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
          const userLightInfo = userLightInfos[friendUserId];
          if (userLightInfo) {
            if (notiType === FRIEND_NOTIFICATION_TYPE.DENIED) {
              user.glog('friend_accept', {
                rsn: 'friend_accept',
                add_rsn: null,
                friend_nid: userLightInfo.pubId,
                friend_gameUserId: friendUserId,
                type: 0, // 수락 :1 거절: 0
              });
            } else if (notiType === FRIEND_NOTIFICATION_TYPE.CANCELED) {
              user.glog('friend_request', {
                rsn: 'friend_request',
                add_rsn: null,
                friend_nid: userLightInfo.pubId,
                friend_gameUserId: friendUserId,
                type: 0, // 신청 1: 취소 : 0
              });
            }
          }
          return;
        });
      })
      .then(() => {
        return userFriends.cancelFriend(friendUserId);
      });
  }

  //=========================================================================================================
  // 친구요청 취소,거절 및  친구제거시 양쪽 유저의 정보를 삭제합니다.
  //=========================================================================================================
  export function deleteFriend(user: User, friendUserId: number): Promise<Sync> {
    const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } =
      Container.get(LobbyService);
    const worldConfg = mconf.getWorldConfig();
    const userFriends: UserFriends = user.userFriends;

    //------------------------------------------------------------------------------------------------
    // 등록된 정보에 없을 경우 실패.
    //------------------------------------------------------------------------------------------------
    if (!userFriends.friends[friendUserId]) {
      throw new MError('cannot-find-the-user', MErrorCode.CANNOT_FIND_USER, {
        friendUserId,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 실제 친구상태인지 확인.
    //------------------------------------------------------------------------------------------------
    if (userFriends.friends[friendUserId].state !== FRIEND_STATE.ESTABLISHED) {
      throw new MError('invalid-friend-state', MErrorCode.INVALID_FRIEND_STATE, {
        friendUserId,
        state: userFriends.friends[friendUserId].state,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 양쪽모두 친구삭제한다.일단 요청자 부터..
    //------------------------------------------------------------------------------------------------
    return tuDeleteFriend(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      friendUserId,
      0
    )
      .then(() => {
        // 상대방도 삭제.
        return tuDeleteFriend(
          userDbConnPoolMgr.getPoolByShardId(getUserDbShardId(friendUserId)),
          friendUserId,
          user.userId,
          0
        ).catch((e) => {
          // 삭제는 양쪽 모두 완전한 동기화 대신 실패하더라도, 요청자는 그대로 삭제해주고 로그를 남기는 방식으로 처리.
          // 양쪽 모두 완벽히 삭제가 안되더라도, 한쪽유저만 삭제할 수 있으면 그대로 처리해 주도록 한다.
          // 시스템 에러등으로 삭제가 안될 경우 유저입장에서는 답답할 수 있기 때문.
          mlog.error('[friend] failed-to-delete-both-of-friends', {
            userId: user.userId,
            friendUserId,
            error: e.message,
            stack: mconf.isDev ? undefined : e.stack,
          });
        });
      })

      .then(() => {
        return notifyOnlineFriends(
          user.userId,
          user.userName,
          [friendUserId],
          FRIEND_NOTIFICATION_TYPE.DELETED,
          curTimeUtc()
        );
      })
      .then(() => {
        return getUserLightInfos(
          [friendUserId],
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
          const userLightInfo = userLightInfos[friendUserId];
          if (userLightInfo) {
            user.glog('friend_delete', {
              rsn: 'friend_delete',
              add_rsn: null,
              friend_nid: userLightInfo.pubId,
              friend_gameUserId: friendUserId,
            });
          }
          return;
        });
      })
      .then(() => {
        return userFriends.deleteFriend(friendUserId);
      });
  }

  //=========================================================================================================
  // 친구요청을  수락하여 서로 친구관계를 등록합니다.
  //=========================================================================================================
  export function acceptFriend(user: User, friendUserId: number): Promise<Sync> {
    const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } =
      Container.get(LobbyService);
    const worldConfg = mconf.getWorldConfig();
    const now = curTimeUtc();
    const userFriends: UserFriends = user.userFriends;
    const friendNub: FriendNub = userFriends.friends[friendUserId];

    //------------------------------------------------------------------------------------------------
    // 등록된 정보에 없을 경우 실패.
    //------------------------------------------------------------------------------------------------
    if (!userFriends.friends[friendUserId]) {
      throw new MError('cannot-find-the-user', MErrorCode.CANNOT_FIND_USER, {
        friendUserId,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 요청받은 상태인지 확인.
    //------------------------------------------------------------------------------------------------
    if (userFriends.friends[friendUserId].state !== FRIEND_STATE.RECEIVED) {
      throw new MError('invalid-friend-state', MErrorCode.INVALID_FRIEND_STATE, {
        friendUserId,
        state: userFriends.friends[friendUserId].state,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 최대 친구 수 검사.
    //------------------------------------------------------------------------------------------------
    if (userFriends.getCountByState(FRIEND_STATE.ESTABLISHED) >= cms.Const.FriendLimit.value) {
      throw new MError('friends-are-full', MErrorCode.NUMBER_OF_FRIENDS_IS_FULL, {
        friendUserId,
      });
    }

    return mhttp.chatd
      .getMuteUserIds(user.userId.toString())
      .then((result) => {
        const idx = result.findIndex((blockedUser: number) => friendUserId === blockedUser);
        if (idx !== -1) {
          throw new MError('you-have-blocked-the-user', MErrorCode.BLOCKED_USER, {
            friendUserId,
          });
        }

        return mhttp.chatd.getMuteUserIds(friendUserId.toString()).then((result) => {
          const idx = result.findIndex((blockedUser: number) => user.userId === blockedUser);
          if (idx !== -1) {
            throw new MError('you-have-been-blocked-by-the-user', MErrorCode.BLOCKED_BY_TARGET, {
              friendUserId,
            });
          }
        });
      })
      .then(() => {
        return puFriendLoad(
          userDbConnPoolMgr.getPoolByShardId(getUserDbShardId(friendUserId)),
          friendUserId
        )
          .then((results: Result[]) => {
            //------------------------------------------------------------------------------------------------
            //상대방의 친구 수가 꽉찬경우.
            //------------------------------------------------------------------------------------------------
            const numFriends = results.filter(
              (elem) => elem.state === FRIEND_STATE.ESTABLISHED
            ).length;
            if (numFriends >= cms.Const.FriendLimit.value) {
              throw new MError('other-user-are-full', MErrorCode.OTHER_USER_FRIENDS_ARE_FULL, {
                friendUserId,
              });
            }

            //------------------------------------------------------------------------------------------------
            // 상대방이 나를 요청했는지 확인.
            //------------------------------------------------------------------------------------------------
            const friend = results.find((elem) => elem.friendUserId == user.userId);
            if (!friend) {
              throw new MError('cannot-find-the-user', MErrorCode.CANNOT_FIND_USER, {
                friendUserId,
              });
            }

            //------------------------------------------------------------------------------------------------
            // 상대방이 요청상태가 아닐 경우 실패.
            //------------------------------------------------------------------------------------------------
            if (friend.state !== FRIEND_STATE.REQUEST) {
              throw new MError('invalid-friend-state', MErrorCode.INVALID_FRIEND_STATE, {
                friendUserId,
                state: friend.state,
              });
            }
          })
          .then(() => {
            // 요청자의 친구요청 추가
            return puFriendUpdate(
              userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
              user.userId,
              friendUserId,
              FRIEND_STATE.ESTABLISHED,
              now
            )
              .then(() => {
                // 수신자의 친구수신 추기.
                return puFriendUpdate(
                  userDbConnPoolMgr.getPoolByShardId(getUserDbShardId(friendUserId)),
                  friendUserId,
                  user.userId,
                  FRIEND_STATE.ESTABLISHED,
                  now
                ).catch((e1) => {
                  // 롤백....
                  return puFriendDelete(
                    userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
                    user.userId,
                    friendUserId
                  ).catch((e2) => {
                    mlog.error('[friend-accept] friend rollback query error', {
                      userId: user.userId,
                      recipientUserId: friendUserId,
                      error: e2.message,
                      stack: e2.stack,
                    });
                    throw e1;
                  });
                });
              })
              .catch((e1) => {
                throw new MError(
                  '[friend-accept] faild-query',
                  MErrorCode.FRIEND_ACCEPT_QUERY_ERROR,
                  {
                    userId: user.userId,
                    recipientUserId: friendUserId,
                    error: e1.message,
                    stack: e1.stack,
                  }
                );
              });
          })
          .then(() => {
            return notifyOnlineFriends(
              user.userId,
              user.userName,
              [friendUserId],
              FRIEND_NOTIFICATION_TYPE.ACCEPTED,
              now
            );
          })
          .then(() => {
            return getUserLightInfos(
              [friendUserId],
              userCacheRedis,
              userRedis,
              guildRedis,
              townRedis,
              userDbConnPoolMgr,
              worldConfg.mysqlUserDb.shardFunction
            ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
              const userLightInfo = userLightInfos[friendUserId];
              if (userLightInfo) {
                user.glog('friend_accept', {
                  rsn: 'friend_accept',
                  add_rsn: null,
                  friend_nid: userLightInfo.pubId,
                  friend_gameUserId: friendUserId,
                  type: 1, // 수락:1 거절:2
                });
              }
              return;
            });
          })
          .then(() => {
            friendNub.state = FRIEND_STATE.ESTABLISHED;
            friendNub.regTimeUtc = now;
            return userFriends.updateFriend(friendNub);
          });
      });
  }

  export function notifyOnlineFriends(
    userId: number,
    userName: string,
    friendUserIds: number[],
    notiType: FRIEND_NOTIFICATION_TYPE,
    timeUtc: number
  ) {
    GuildUtil.getLobbyUrlsByUserIds(friendUserIds)
      .then((rets) => {
        rets.forEach((elem) => {
          const packet = {
            userIds: elem.userIds,
            notiType,
            friendUserId: userId,
            friendUserName: userName,
            timeUtc,
          };

          // 친구알림에 실패나도 게임지장엔 영향이 없으므로 로그만  남겨주자.
          elem.lobbyApi.sendFriendNotification(packet).catch((e) => {
            mlog.error('failed-to-send-friendNotification', {
              packet,
              error: e.message,
              stack: e.stack,
            });
          });
        });
      })
      .catch((e) => {
        mlog.error('an-error-occured', {
          error: e.message,
          stack: e.stack,
        });
      });
  }

  //=========================================================================================================
  // 친구가 보낸 포인트를 수령.
  //=========================================================================================================
  export function pickupPoint(user: User, friendUserId: number): Promise<Sync> {
    const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } =
      Container.get(LobbyService);
    const worldConfg = mconf.getWorldConfig();

    const now = curTimeUtc();
    const userFriends: UserFriends = user.userFriends;
    const friendNub: FriendNub = userFriends.friends[friendUserId];

    //------------------------------------------------------------------------------------------------
    // 친구상태에서만 전달 가능하다.
    //------------------------------------------------------------------------------------------------
    if (!friendNub || friendNub.state !== FRIEND_STATE.ESTABLISHED) {
      throw new MError('no-friend', MErrorCode.NO_FRIEND, {
        friendUserId,
        state: friendNub ? friendNub.state : null,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 하루동안 받을 수 있는 횟수 체크.
    //------------------------------------------------------------------------------------------------
    const pickupCount = userFriends.getPickupCountToday(now);
    if (pickupCount >= cms.Const.FriendAP.value) {
      throw new MError('exceeding-daily-pickup-limit', MErrorCode.EXCEEDING_DAILY_PICKUP_LIMIT, {
        friendUserId,
        pickupCount,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 당일 보낸 포인트만 받을 수있다.
    //------------------------------------------------------------------------------------------------
    const friendPointNub: FriendPointNub = _.cloneDeep(userFriends.getFriendPoint(friendUserId));
    const passingCount = CalcContentsResetTimePassingCount(
      now,
      friendPointNub.lastRecvDate,
      cms.ContentsResetHour.FriendAPReset.hour
    );
    if (passingCount > 0) {
      throw new MError('not-received-point-today', MErrorCode.NOT_RECEIVED_POINT_TODAY, {
        friendUserId,
        lastRecvDate: friendPointNub.lastRecvDate,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 오늘날짜로 이미 픽업?
    //------------------------------------------------------------------------------------------------
    if (friendPointNub.pickup) {
      throw new MError('already-pick-up', MErrorCode.ALREADY_PICK_UP, {
        friendUserId,
        friendPointNub,
      });
    }

    friendPointNub.pickup = 1;
    friendPointNub.totalReceivedPts++;

    const energyChange: EnergyChange = user.userEnergy.buildEnergyChangeWithConsume(
      now,
      user.level,
      user.level,
      -cms.Const.FriendAPSendCount.value,
      false
    );

    return tuPickupFriendPoint(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      energyChange,
      [friendPointNub]
    )
      .then(() => {
        return getUserLightInfos(
          [friendUserId],
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
          // 순수 친구포인트로 획득한 수치만 로그에 쌓는다.
          let prevEnergy = user.userEnergy.rawEnergy; // 업데이트 전 현재 포인트.
          user.glog('friend_point', {
            flag: 2,
            target_nid: userLightInfos[friendUserId] ? userLightInfos[friendUserId].pubId : null,
            target_gameUserId: friendUserId,
            cv: cms.Const.FriendAPSendCount.value,
            rv: prevEnergy + cms.Const.FriendAPSendCount.value,
          });
        });
      })
      .then(() => {
        const sync: Sync = {};
        _.merge<Sync, Sync>(
          sync,
          user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn })
        );
        _.merge<Sync, Sync>(sync, userFriends.updateFriendPoint(friendPointNub));

        mlog.verbose('pickup-friend-point', {
          userId: user.userId,
          friendPointNub,
          energyPoint: energyChange.energy,
        });
        return sync;
      });
  }

  //=========================================================================================================
  // 친구가 보낸 포인트를 수령.
  //=========================================================================================================
  export function pickupPointAll(user: User): Promise<Sync> {
    const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } =
      Container.get(LobbyService);
    const worldConfg = mconf.getWorldConfig();
    const userFriends: UserFriends = user.userFriends;
    const now = curTimeUtc();

    const pickupCount = userFriends.getPickupCountToday(now);
    const ableToPickupNum = cms.Const.FriendAP.value - pickupCount; // 실제 수령받을 수 있는 갯수.

    // 업데이트할 객체 목록.
    const changePointNubs: FriendPointNub[] = [];

    _.forOwn(userFriends.friends, (elem) => {
      if (changePointNubs.length >= ableToPickupNum) {
        return;
      }
      if (elem.state !== FRIEND_STATE.ESTABLISHED) {
        return;
      }

      const friendPointNub: FriendPointNub = _.cloneDeep(
        userFriends.getFriendPoint(elem.friendUserId)
      );
      const passingCount = CalcContentsResetTimePassingCount(
        now,
        friendPointNub.lastRecvDate,
        cms.ContentsResetHour.FriendAPReset.hour
      );
      if (passingCount > 0) {
        return;
      }

      if (friendPointNub.pickup) {
        return;
      }

      friendPointNub.pickup = 1;
      friendPointNub.totalReceivedPts++;

      changePointNubs.push(friendPointNub);
    });

    // no get points.
    if (changePointNubs.length === 0) {
      return Promise.resolve({});
    }
    const addedPoint: number = -cms.Const.FriendAPSendCount.value * changePointNubs.length;
    const energyChange: EnergyChange = user.userEnergy.buildEnergyChangeWithConsume(
      now,
      user.level,
      user.level,
      addedPoint,
      false
    );

    return tuPickupFriendPoint(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      energyChange,
      changePointNubs
    )
      .then(() => {
        const friendUserIds = changePointNubs.map((elem) => elem.friendUserId);
        return getUserLightInfos(
          friendUserIds,
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
          /*
             전체 수령시 array형식으로 한번에 보낼경우 라인게임즈측에서 추적이 어렵다는 
             의견이 있어서 어쩔 수없이 개별적으로 각각 획득한 양을 로그로 남기도록 함.
          */

          // 순수 친구포인트로 획득한 수치만 로그에 쌓는다.
          let curEnergy = user.userEnergy.rawEnergy; // 업데이트 전 현재 포인트.
          _.forOwn(userLightInfos, (elem) => {
            curEnergy += cms.Const.FriendAPSendCount.value;

            user.glog('friend_point', {
              flag: 2,
              target_nid: elem.pubId,
              target_gameUserId: elem.userId,
              cv: cms.Const.FriendAPSendCount.value,
              rv: curEnergy,
            });
          });
        });
      })
      .then(() => {
        const sync: Sync = {};

        _.merge<Sync, Sync>(
          sync,
          user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn })
        );
        changePointNubs.forEach((elem) => {
          _.merge<Sync, Sync>(sync, userFriends.updateFriendPoint(elem));
        });

        mlog.verbose('pickup-friend-point-all', {
          userId: user.userId,
          pickNum: changePointNubs.length,
          energyPoint: energyChange.energy,
        });

        return sync;
      });
  }

  //=========================================================================================================
  // 친구에게 포인트 주기.
  //=========================================================================================================
  export function sendPoint(user: User, friendUserId: number): Promise<Sync> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userFriends: UserFriends = user.userFriends;
    const now = curTimeUtc();

    //------------------------------------------------------------------------------------------------
    // 친구상태에서만 전달 가능하다.
    //------------------------------------------------------------------------------------------------
    const friendNub: FriendNub = userFriends.friends[friendUserId];
    if (!friendNub || friendNub.state !== FRIEND_STATE.ESTABLISHED) {
      throw new MError('no-friend', MErrorCode.NO_FRIEND, {
        friendUserId,
        state: friendNub ? friendNub.state : null,
      });
    }

    //------------------------------------------------------------------------------------------------
    // 하루에 한번만 가능.
    //------------------------------------------------------------------------------------------------
    let friendPointNub: FriendPointNub = userFriends.getFriendPoint(friendUserId);
    const passingCount = CalcContentsResetTimePassingCount(now, friendPointNub.lastSendDate, 0);
    if (passingCount === 0) {
      throw new MError('already-sent-to-friend', MErrorCode.ALREADY_SENT_TO_FRIEND, {
        friendUserId,
        lastSendDate: friendPointNub.lastSendDate,
      });
    }

    friendPointNub = _.cloneDeep(friendPointNub);
    return _sendPointToFriend(user, userDbConnPoolMgr, friendPointNub, now);
  }

  //=========================================================================================================
  // 모든 친구에게 포인트 주기.
  //=========================================================================================================
  export function sendPointAll(user: User): Promise<Sync> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const userFriends: UserFriends = user.userFriends;
    const now = curTimeUtc();

    //------------------------------------------------------------------------------------------------
    // 포인트 전송가능한 유저만 긁어 모은다.
    //------------------------------------------------------------------------------------------------
    const candidates: FriendPointNub[] = [];
    _.forOwn(userFriends.friends, (elem) => {
      // 친구관계이며, 오늘 전달하지 않는 유저만 모은다.
      if (elem.state === FRIEND_STATE.ESTABLISHED) {
        const friendPointNub: FriendPointNub = userFriends.getFriendPoint(elem.friendUserId);
        if (
          CalcContentsResetTimePassingCount(
            now,
            friendPointNub.lastSendDate,
            cms.ContentsResetHour.FriendAPReset.hour
          ) > 0
        ) {
          candidates.push(friendPointNub);
        }
      }
    });

    return Promise.reduce(
      candidates,
      (sync: Sync, friendPointNub: FriendPointNub) => {
        return _sendPointToFriend(user, userDbConnPoolMgr, friendPointNub, now)
          .then((_sync: Sync) => {
            _.merge(sync, _sync);
            return sync;
          })
          .catch((e) => {
            mlog.error('[friend-send-point-all] an-error-occured-while-sending-point', {
              userId: user.userId,
              error: e.message,
              stack: e.stack,
            });
            return sync;
          });
      },
      {}
    ).then((sync) => {
      return sync;
    });
  }

  //=========================================================================================================
  // 친구에게 포인트 주기.
  //=========================================================================================================

  function _sendPointToFriend(
    user: User,
    userDbConnPoolMgr: DbConnPoolManager,
    friendPointNub: FriendPointNub,
    curTimeUtc: number
  ): Promise<any> {
    const friendUserId: number = friendPointNub.friendUserId;
    const friendUserShardId: number = getUserDbShardId(friendUserId);
    return puFriendPointLoad(
      userDbConnPoolMgr.getPoolByShardId(friendUserShardId),
      friendUserId,
      user.userId
    )
      .then((result) => {
        let friendPointNubForReceiver: FriendPointNub;
        if (!result) {
          friendPointNubForReceiver = {
            friendUserId: user.userId,
            lastSendDate: 1,
            lastRecvDate: 1,
            pickup: 0,
            totalReceivedPts: 0,
          };
        } else {
          friendPointNubForReceiver = {
            friendUserId: user.userId,
            lastSendDate: parseInt(result.lastSendDate, 10),
            lastRecvDate: parseInt(result.lastRecvDate, 10),
            pickup: result.pickup,
            totalReceivedPts: result.totalReceivedPts,
          };
        }

        //------------------------------------------------------------------------------------------------
        // 하루에 한번만 줄 수 있다.
        //------------------------------------------------------------------------------------------------
        const passingCount: number = CalcContentsResetTimePassingCount(
          curTimeUtc,
          friendPointNubForReceiver.lastRecvDate,
          cms.ContentsResetHour.FriendAPReset.hour
        );
        if (passingCount === 0) {
          throw new MError('already-received-point', MErrorCode.ALREADY_RECEIVED_POINT, {
            friendUserId,
            sendDate: friendPointNub.lastSendDate,
            recvDate: friendPointNubForReceiver.lastRecvDate,
          });
        }
        return friendPointNubForReceiver;
      })
      .then((friendPointNubForReceiver: FriendPointNub) => {
        const sendTimeForRollback: number = friendPointNub.lastSendDate;
        friendPointNub.lastSendDate = curTimeUtc;
        friendPointNubForReceiver.pickup = 0;
        friendPointNubForReceiver.lastRecvDate = curTimeUtc;

        return _sendPointIndividually(
          user,
          userDbConnPoolMgr,
          friendPointNub,
          friendPointNubForReceiver,
          sendTimeForRollback
        );
      });
  }

  //=========================================================================================================
  // 친구에게 포인트 주기.
  //=========================================================================================================
  function _sendPointIndividually(
    user: User,
    userDbConnPoolMgr: DbConnPoolManager,
    pointNubForSender: FriendPointNub,
    pointNubForReciever: FriendPointNub,
    rollbackDate: number
  ): Promise<Sync> {
    // 모든 검사가 끝났으면,  전달유저 먼저 업데이트.
    return puFriendPointUpdate(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      pointNubForSender.friendUserId,
      pointNubForSender.lastSendDate,
      pointNubForSender.lastRecvDate,
      pointNubForSender.pickup,
      pointNubForSender.totalReceivedPts
    )
      .then(() => {
        // 받는유저 업데이트.
        const otherUserShardId: number = getUserDbShardId(pointNubForSender.friendUserId);
        return puFriendPointUpdate(
          userDbConnPoolMgr.getPoolByShardId(otherUserShardId),
          pointNubForSender.friendUserId,
          pointNubForReciever.friendUserId,
          pointNubForReciever.lastSendDate,
          pointNubForReciever.lastRecvDate,
          pointNubForReciever.pickup,
          pointNubForReciever.totalReceivedPts
        ).catch((e1) => {
          // 롤백 처리...
          return puFriendPointUpdate(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId,
            pointNubForSender.friendUserId,
            rollbackDate,
            pointNubForSender.lastRecvDate,
            pointNubForSender.pickup,
            pointNubForSender.totalReceivedPts
          )
            .catch((e2) => {
              mlog.error('[friend-send-point] rollback-query-error', {
                userId: user.userId,
                friendUserId: pointNubForSender.friendUserId,
                error: e2.message,
                stack: e2.stack,
              });
            })
            .finally(() => {
              throw e1;
            });
        });
      })
      .then(() => {
        notifyOnlineFriends(
          user.userId,
          user.userName,
          [pointNubForSender.friendUserId],
          FRIEND_NOTIFICATION_TYPE.RECV_POINT,
          curTimeUtc()
        );
      })
      .then(() => {
        const { userDbConnPoolMgr, userCacheRedis, userRedis, guildRedis, townRedis } =
          Container.get(LobbyService);
        const worldConfg = mconf.getWorldConfig();

        return getUserLightInfos(
          [pointNubForSender.friendUserId],
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        ).then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
          const userLightInfo = userLightInfos[pointNubForSender.friendUserId];
          user.glog('friend_point', {
            flag: 1,
            target_nid: userLightInfo ? userLightInfo.pubId : null,
            target_gameUserId: userLightInfo ? userLightInfo.userId : null,
            cv: 0,
            rv: user.userEnergy.rawEnergy,
          });
        });
      })
      .then(() => {
        return user.userFriends.updateFriendPoint(pointNubForSender);
      })
      .catch((e1) => {
        throw new MError(
          '[friend-send-point] failed-to-query-puFriendPointUpdate',
          MErrorCode.FRIEND_REQUEST_QUERY_ERROR,
          {
            userId: user.userId,
            SenderNub: pointNubForSender,
            ReceiverNub: pointNubForReciever,
            error: e1.message,
            stack: e1.stack,
          }
        );
      });
  }
}
