// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mhttp from '../../../motiflib/mhttp';
import mlog from '../../../motiflib/mlog';
import {
  CHANNEL_TYPE,
  getAliasName,
  VolanteUserSession,
} from '../../../motiflib/mhttp/chatApiClient';
import { MErrorCode } from '../../../motiflib/merrorCode';
import { MError } from '../../../motiflib/merror';
import cms from '../../../cms';
import { ClientPacketHandler } from '../index';
import { ChatErrorCode } from '../../../motiflib/mhttp/chatApiErrors';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

interface RequestBody {
  off: number[];
}

// ----------------------------------------------------------------------------
export class Cph_Common_ChatJoinChannel implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;
    const { off } = reqBody;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const userIdStr = user.userId.toString();

    // 들어가야 될 채널들
    const channelsToJoin: { [channelType: number]: string } = {};
    const channelAliases: { [channelName: string]: string } = {};

    // 시스템 채널
    if (shouldJoin(CHANNEL_TYPE.SYSTEM, off)) {
      channelsToJoin[CHANNEL_TYPE.SYSTEM] = 'SYSTEM';
      channelAliases[CHANNEL_TYPE.SYSTEM] = getAliasName(CHANNEL_TYPE.SYSTEM);
    }

    // 월드 채널
    if (shouldJoin(CHANNEL_TYPE.WORLD, off)) {
      channelsToJoin[CHANNEL_TYPE.WORLD] = 'WORLD';
      channelAliases['WORLD'] = getAliasName(CHANNEL_TYPE.WORLD);
    }

    // 국가 채널
    if (shouldJoin(CHANNEL_TYPE.NATION, off) && user.nationCmsId) {
      const channelName = user.nationCmsId.toString();
      channelsToJoin[CHANNEL_TYPE.NATION] = channelName;
      channelAliases[user.nationCmsId.toString()] = getAliasName(CHANNEL_TYPE.NATION, channelName);
    }

    // 길드
    if (shouldJoin(CHANNEL_TYPE.GUILD, off) && user.userGuild.guildId) {
      const channelName = `GUILD_${user.userGuild.guildId}`;
      channelsToJoin[CHANNEL_TYPE.GUILD] = channelName;
      channelAliases[channelName] = getAliasName(CHANNEL_TYPE.GUILD, channelName);
    }

    // 지역 채널
    if (shouldJoin(CHANNEL_TYPE.REGION, off)) {
      if (user.userState.isInTown()) {
        const townCmsId = user.userTown.getLastTownCmsId();
        if (!townCmsId) {
          throw new MError('no-town-cms-id-to-join', MErrorCode.LG_VOLANTE_ERROR, { townCmsId });
        }
        const channelName = townCmsId.toString();
        channelsToJoin[CHANNEL_TYPE.REGION] = channelName;
        channelAliases[townCmsId.toString()] = getAliasName(CHANNEL_TYPE.REGION, channelName);
      } else if (user.userState.isInOcean()) {
        const channelName = user.userSailing.getSailState().region.id.toString();
        channelsToJoin[CHANNEL_TYPE.REGION] = channelName;
        channelAliases[channelName] = getAliasName(CHANNEL_TYPE.REGION, channelName);
      }
    }

    return mhttp.chatd
      .getSessions(userIdStr)
      .then((ret: VolanteUserSession) => {
        const promises = [];
        for (const channel of ret.session.channels) {
          if (cms.Town[channel] || cms.Region[channel]) {
            if (channelsToJoin[CHANNEL_TYPE.REGION]) {
              if (channel !== channelsToJoin[CHANNEL_TYPE.REGION]) {
                // 이미 입장해 있는 지역 채널과 다르면 나가고 들어간다.
                promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
                promises.push(
                  mhttp.chatd.channelJoin(channelsToJoin[CHANNEL_TYPE.REGION], userIdStr)
                );
              }
              delete channelsToJoin[CHANNEL_TYPE.REGION];
            } else {
              promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
            }
          } else if (cms.Nation[channel]) {
            if (channelsToJoin[CHANNEL_TYPE.NATION]) {
              if (channel !== channelsToJoin[CHANNEL_TYPE.NATION]) {
                // 이미 입장해 있는 국가 채널과 다르면 나가고 들어간다.
                promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
                promises.push(
                  mhttp.chatd.channelJoin(channelsToJoin[CHANNEL_TYPE.NATION], userIdStr)
                );
              }
              delete channelsToJoin[CHANNEL_TYPE.NATION];
            } else {
              promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
            }
          } else if (channel === 'SYSTEM') {
            if (channelsToJoin[CHANNEL_TYPE.SYSTEM]) {
              delete channelsToJoin[CHANNEL_TYPE.SYSTEM];
            } else {
              promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
            }
          } else if (channel === 'WORLD') {
            if (channelsToJoin[CHANNEL_TYPE.WORLD]) {
              delete channelsToJoin[CHANNEL_TYPE.WORLD];
            } else {
              promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
            }
          } else if (channel.includes('GUILD_')) {
            if (channelsToJoin[CHANNEL_TYPE.GUILD]) {
              if (channel !== channelsToJoin[CHANNEL_TYPE.GUILD]) {
                promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
                promises.push(joinGuildChannel(channelsToJoin[CHANNEL_TYPE.GUILD], userIdStr));
              }
              delete channelsToJoin[CHANNEL_TYPE.GUILD];
            } else {
              promises.push(mhttp.chatd.channelLeave(channel, userIdStr));
            }
          }
        }

        _.forOwn(channelsToJoin, (channel) => {
          if (channel.includes('GUILD_')) {
            promises.push(joinGuildChannel(channelsToJoin[CHANNEL_TYPE.GUILD], userIdStr));
          } else {
            promises.push(mhttp.chatd.channelJoin(channel, userIdStr));
          }
        });

        return Promise.all(promises);
      })
      .then(() => {
        return user.sendJsonPacket(packet.seqNum, packet.type, { channelAliases, success: true });
      })
      .catch((err: Error | MError) => {
        if (err instanceof MError) {
          if (err.mcode === MErrorCode.LG_VOLANTE_INVALID_SESSION) {
            return user.sendJsonPacket(packet.seqNum, packet.type, { success: false });
          }
          // 기타 정의된 에러
          throw err;
        }

        throw new MError(err.message, MErrorCode.INTERNAL_ERROR);
      });
  }
}

function shouldJoin(channel: CHANNEL_TYPE, offChannels: CHANNEL_TYPE[]): boolean {
  return offChannels.findIndex((elem) => elem === channel) === -1;
}

function joinGuildChannel(channelName: string, userIdStr: string) {
  return mhttp.chatd.channelJoin(channelName, userIdStr).catch((err) => {
    const errcode = err.extra?.volanteErrorCode || undefined;
    if (!errcode) {
      throw err;
    }

    if (errcode === ChatErrorCode.CHANNEL_NOT_EXIST) {
      mlog.error('[CHAT] Guild channel is not exist, so create new channel.', {
        userId: userIdStr,
        channelName,
      });
      return mhttp.chatd.createGuildChannel(channelName, userIdStr, true).then(() => {
        return joinGuildChannel(channelName, userIdStr);
      });
    } else if (errcode === ChatErrorCode.NOT_ALLOW_USER) {
      mlog.error('[CHAT] Not allowed from guild chat channel.', {
        userId: userIdStr,
        channelName,
      });
      return mhttp.chatd.allowGuildChannel(channelName, userIdStr).then(() => {
        return joinGuildChannel(channelName, userIdStr);
      });
    } else {
      throw err;
    }
  });
}
