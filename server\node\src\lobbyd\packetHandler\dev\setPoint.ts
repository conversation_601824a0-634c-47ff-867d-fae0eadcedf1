// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import tuChangeUserPoint from '../../../mysqllib/txn/tuChangeUserPoint';
import { LobbyService } from '../../server';
import { Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Dev } from '../../../proto/lobby/proto';
import { EnergyChange } from '../../userEnergy';
import { curTimeUtc } from '../../../motiflib/mutil';
import puUserUpdateEnergy from '../../../mysqllib/sp/puUserUpdateEnergy';
import { isCash } from '../../../cms/pointDesc';
import { ClientPacketHandler } from '../index';
import { ManufacturePointChange } from '../../userManufacture';
import puUserUpdateManufacturePoint from '../../../mysqllib/sp/puUserUpdateManufacturePoint';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------
const rsn = 'dev_set_point';
const add_rsn = null;

// ----------------------------------------------------------------------------
export class Cph_Dev_SetPoint implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body = packet.bodyObj;

    const cmsId = body.cmsId;
    const amount = body.amount;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.ensureCheatAccessLevel(Dev.SET_POINT);

    const pointValue = amount;
    if (!Number.isInteger(pointValue)) {
      throw new MError('invalid-amount', MErrorCode.INVALID_REQ_BODY_DEV_SET_POINT, {
        reqBody: body,
      });
    }

    if (cmsId === cmsEx.EnergyPointCmsId) {
      if (pointValue < 0) {
        throw new MError('invalid-amount', MErrorCode.INVALID_REQ_BODY_DEV_SET_POINT, {
          reqBody: body,
        });
      }
      const energyChange: EnergyChange = {
        energy: pointValue,
        lastUpdateTimeUtc: curTimeUtc(),
      };

      const sync: Sync = {};
      const { userDbConnPoolMgr } = Container.get(LobbyService);
      return puUserUpdateEnergy(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        energyChange.energy,
        energyChange.lastUpdateTimeUtc
      ).then(() => {
        _.merge<Sync, Sync>(sync, user.userEnergy.applyEnergyChange(energyChange, null));
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    } else if (cmsId === cmsEx.ManufacturePointCmsId) {
      if (pointValue < 0) {
        throw new MError('invalid-amount', MErrorCode.INVALID_REQ_BODY_DEV_SET_POINT, {
          reqBody: body,
        });
      }

      const manufacturePointChange: ManufacturePointChange = {
        point: Math.min(pointValue, cms.Const.MaxManufacturePoint.value),
        lastUpdatePointTimeUtc: curTimeUtc(),
      };

      const sync: Sync = {};
      const { userDbConnPoolMgr } = Container.get(LobbyService);
      return puUserUpdateManufacturePoint(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        manufacturePointChange.point,
        manufacturePointChange.lastUpdatePointTimeUtc
      ).then(() => {
        _.merge<Sync, Sync>(sync, user.userManufacture.applyPointChange(manufacturePointChange, null));
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    } else if (isCash(cmsId)) {
      return _setCash(cmsId, amount, user, packet);
    } else if (cmsId === cmsEx.CashShopMileage) {
      throw new MError(
        'not-supported-use-add-point-insted',
        MErrorCode.INVALID_REQ_BODY_DEV_SET_POINT,
        {
          reqBody: body,
        }
      );
    } else {
      const pointCms = cms.Point[cmsId];
      if (!pointCms) {
        throw new MError('no-key-in-point-cms', MErrorCode.NO_KEY_IN_CMS, {
          cmsId,
        });
      }
      if (pointValue > pointCms.hardCap) {
        throw new MError('exceeds-hard-cap', MErrorCode.EXCEEDS_HARD_CAP, {
          cmsId,
          pointValue,
        });
      }
      if (pointValue < 0) {
        mlog.info('requested to apply negative point', {
          rsn,
          userId: user.userId,
          cmsId,
          newPoint: pointValue,
        });
      }

      return tuChangeUserPoint(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        cmsId,
        pointValue
      ).then(() => {
        const sync: Sync = {};
        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyPointChanges([{ cmsId, value: pointValue }], null)
        );

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    }
  }
}

function _setCash(cmsId: number, value: number, user: User, packet: CPacket): Promise<any> {
  const cur = user.userPoints.getPoint(cmsId);
  return Promise.resolve()
    .then(() => {
      if (value < cur) {
        return user.userPoints.consumeCash(cmsId, cur - value, { itemId: rsn }, user, null);
      } else {
        return user.userPoints.addCash(cmsId, value - cur, rsn, user, null);
      }
    })
    .then(() => {
      const sync: Sync = {
        add: {
          points: {
            [cmsId]: {
              cmsId,
              value: user.userPoints.getPoint(cmsId),
            },
          },
        },
      };

      return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
    });
}
