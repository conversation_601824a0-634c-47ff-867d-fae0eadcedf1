// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import { withTxn } from '../mysqlUtil';
import { MError, MErrorCode } from '../../motiflib/merror';
import { EnergyChange } from '../../lobbyd/userEnergy';
import puUserUpdateEnergy from '../sp/puUserUpdateEnergy';
import puAttendanceDelete from '../sp/puAttendanceDelete';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../lobbyd/user';
import puSoftDataUpdateKarma from '../sp/puSoftDataUpdateKarma';
import puDirectMailLastIdUpdate from '../sp/puDirectMailLastIdUpdate';
import puDirectMailCreate from '../sp/puDirectMailCreate';
import puUserUpdateLastReceiveHotTimeUtc from '../sp/puUserUpdateLastReceiveHotTimeUtc';
import { UserReputationChange } from '../../lobbyd/userReputation';
import puReputationUpdate from '../sp/puReputationUpdate';
import { GAME_STATE } from '../../motiflib/model/lobby/gameState';
import puStateUpdateGameStateLastGameState from '../sp/puStateUpdateGameStateLastGameState';
import puEventMissionDelete from '../sp/puEventMissionDelete';
import { WeeklyEventChange } from '../../lobbyd/userAchievement';
import puWeeklyEventCreate from '../sp/puWeeklyEventCreate';
import puWeeklyEventDelete from '../sp/puWeeklyEventDelete';
import puPassEventDelete from '../sp/puPassEventDelete';
import puPassEventMissionDelete from '../sp/puPassEventMissionDelete';
import { Shield } from '../../lobbyd/userShield';
import puShieldUpdate from '../sp/puShieldUpdate';
import puUserUpdateExploreTicket from '../sp/puUserUpdateExploreTicket';
import { ExploreQuickModeChange, ExploreTicketChange } from '../../lobbyd/userExplore';
import puUserUpdateExploreQuickMode from '../sp/puUserUpdateExploreQuickMode';
import puEventShopRestrictedProductDelete from '../sp/puEventShopRestrictedProductDelete';
import { PendingDirectMail } from '../../motiflib/model/town';
import puDirectMailPendingCreate from '../sp/puDirectMailPendingCreate';
import puMileageUpdateIsExpirationNotified from '../sp/puMileageUpdateIsExpirationNotified';
import puEventGameDelete from '../sp/puEventGameDelete';
import { WaypointSupplyTicketChange } from '../../lobbyd/userSailWaypoints';
import puSoftDataUpdateWaypointSupplyTicket from '../sp/puSoftDataUpdateWaypointSupplyTicket';
import { ChatTranslationChange } from '../../lobbyd/userChatTranslationCount';
import puChatTranslationCountUpdate from '../sp/puChatTranslationCountUpdate';
import puQuestGroupRegenTimesDelete from '../sp/puQuestGroupRegenTimesDelete';
import puHotSpotProductsDelete from '../sp/puHotSpotProductsDelete';
import { HotSpotProduct, OpenDurationProduct } from '../../lobbyd/userCashShop';
import puCashShopRestrictedProductDelete from '../sp/puCashShopRestrictedProductDelete';
import puCashShopOpenDurationProductUpdate from '../sp/puCashShopOpenDurationProductUpdate';
import puUserTitleDelete from '../sp/puUserTitleDelete';
import { ShipSlotChange } from '../../lobbyd/ship';
import puMateEquipmentEquip from '../sp/puMateEquipmentEquip';
import puShipSlotUpdateIsLockedAndShipSlotItemId from '../sp/puShipSlotUpdateIsLockedAndShipSlotItemId';
import puShipCustomizingUpdateCostumeShipSlotItemId from '../sp/puShipCustomizingUpdateCostumeShipSlotItemId';
import { CostumeShipSlotChange } from '../../lobbyd/userFleets';
import puCostumeShipSlotUpdate from '../sp/puCostumeShipSlotUpdate';
import puHotSpotProductsUpdate from '../sp/puHotSpotProductsUpdate';
import { MailCreatingParams } from '../../motiflib/mailBuilder';
import { ManufacturePointChange } from '../../lobbyd/userManufacture';
import puUserUpdateManufacturePoint from '../sp/puUserUpdateManufacturePoint';

function queryImpl(
  connection,
  userId: number,
  energyChange: EnergyChange,
  manufacturePointChange: ManufacturePointChange,
  karmaChange: KarmaChange,
  waypointSupplyTicketChange: WaypointSupplyTicketChange,
  eventPageCmsIdsToDelete: number[],
  weeklyEventChanges: WeeklyEventChange[],
  passEventEventPageCmsIdsToDelete: number[],
  attendanceEventPageCmsIdsToDelete: number[],
  newMails: MailCreatingParams[],
  lastReceiveHotTimeUtc: number,
  reputationChanges: UserReputationChange[],
  lastGameState: GAME_STATE,
  shieldChanges: Shield[],
  exploreTicketChange: ExploreTicketChange,
  exploreQuickModeChange: ExploreQuickModeChange,
  expiredEventPageCmsIdsOfEventShop: number[],
  newPendingDirectMails: PendingDirectMail[],
  notifiedMileageMonth: number,
  gameEventPageCmsIdsToDelete: number[],
  chatTranslationChange: ChatTranslationChange,
  expiredQuestGroupIds: number[],
  hotSpotProductCmsIdsToDelete: number[],
  resetPopupCountHotSpotProducts: HotSpotProduct[],
  restrictedProductsToDelete: number[],
  openDurationProducts: OpenDurationProduct[],
  expiredEquipmentIds: number[],
  shipSlotChanges: ShipSlotChange[],
  costumeShipSlotChanges: CostumeShipSlotChange[],
  curTimeUtc: number
) {
  return Promise.resolve()
    .then(() => {
      if (energyChange) {
        return puUserUpdateEnergy(
          connection,
          userId,
          energyChange.energy,
          energyChange.lastUpdateTimeUtc
        );
      }
      return null;
    }).then(() => {
      if (manufacturePointChange) {
        return puUserUpdateManufacturePoint(
          connection,
          userId,
          manufacturePointChange.point,
          manufacturePointChange.lastUpdatePointTimeUtc
        );
      }
      return null;
    })
    .then(() => {
      if (lastReceiveHotTimeUtc) {
        return puUserUpdateLastReceiveHotTimeUtc(connection, userId, lastReceiveHotTimeUtc);
      }
      return null;
    })
    .then(() => {
      if (exploreTicketChange) {
        return puUserUpdateExploreTicket(
          connection,
          userId,
          exploreTicketChange.usedTicketCount,
          exploreTicketChange.lastUpdateTimeUtc
        );
      }
      return null;
    })
    .then(() => {
      if (exploreQuickModeChange) {
        return puUserUpdateExploreQuickMode(
          connection,
          userId,
          exploreQuickModeChange.usedQuickModeCount,
          exploreQuickModeChange.lastUpdateTimeUtc
        );
      }
      return null;
    })
    .then(() => {
      if (lastGameState !== undefined) {
        return puStateUpdateGameStateLastGameState(
          connection,
          userId,
          GAME_STATE.NONE,
          lastGameState
        );
      }
      return null;
    })
    .then(() => {
      if (karmaChange) {
        return puSoftDataUpdateKarma(connection, userId, karmaChange);
      }
      return null;
    })
    .then(() => {
      if (waypointSupplyTicketChange) {
        return puSoftDataUpdateWaypointSupplyTicket(
          connection,
          userId,
          waypointSupplyTicketChange.ticketUsedCount,
          waypointSupplyTicketChange.lastUpdateTimeUtc
        );
      }
      return null;
    })
    .then(() => {
      if (notifiedMileageMonth) {
        return puMileageUpdateIsExpirationNotified(connection, userId, notifiedMileageMonth, 1);
      }
      return null;
    })
    .then(() => {
      const promises = [];
      for (const elem of shieldChanges) {
        promises.push(puShieldUpdate(connection, userId, elem));
      }
      return Promise.all(promises);
    })
    .then(() => {
      if (reputationChanges && reputationChanges.length > 0) {
        const promises = [];
        for (const elem of reputationChanges) {
          promises.push(
            puReputationUpdate(
              connection,
              userId,
              elem.nationCmsId,
              elem.reputation,
              elem.updateTimeUtc
            )
          );
        }

        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      const promises = [];
      if (expiredEquipmentIds && expiredEquipmentIds.length > 0) {
        for (const id of expiredEquipmentIds) {
          promises.push(puMateEquipmentEquip(connection, userId, 0, id));
        }
      }
      return Promise.all(promises);
    })
    .then(() => {
      const promises = [];
      if (shipSlotChanges && shipSlotChanges.length > 0) {
        for (const change of shipSlotChanges) {
          promises.push(puShipSlotUpdateIsLockedAndShipSlotItemId(connection, userId, change));
        }
      }
      return Promise.all(promises);
    })
    .then(() => {
      const promises = [];
      if (restrictedProductsToDelete && restrictedProductsToDelete.length > 0) {
        for (const cmsId of restrictedProductsToDelete) {
          promises.push(puCashShopRestrictedProductDelete(connection, userId, cmsId));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      // 임시 코드 EVENT_SHOP_GET_PRODUCTS 패킷 사용 시 삭제 필요
      const promises = [];
      if (expiredEventPageCmsIdsOfEventShop && expiredEventPageCmsIdsOfEventShop.length > 0) {
        for (const cmsId of expiredEventPageCmsIdsOfEventShop) {
          promises.push(puEventShopRestrictedProductDelete(connection, userId, cmsId));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (eventPageCmsIdsToDelete && eventPageCmsIdsToDelete.length > 0) {
        const promises = [];
        for (const cmsId of eventPageCmsIdsToDelete) {
          promises.push(puWeeklyEventDelete(connection, userId, cmsId));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (weeklyEventChanges && weeklyEventChanges.length > 0) {
        const promises = [];
        for (const change of weeklyEventChanges) {
          promises.push(
            puWeeklyEventCreate(
              connection,
              userId,
              change.eventPageCmsId,
              change.weeklyEventStartTimeUtc
            )
          );
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (eventPageCmsIdsToDelete && eventPageCmsIdsToDelete.length > 0) {
        const promises = [];
        for (const cmsId of eventPageCmsIdsToDelete) {
          promises.push(puEventMissionDelete(connection, userId, cmsId));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (passEventEventPageCmsIdsToDelete && passEventEventPageCmsIdsToDelete.length > 0) {
        const promises = [];
        for (const cmsId of passEventEventPageCmsIdsToDelete) {
          promises.push(puPassEventDelete(connection, userId, cmsId));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (passEventEventPageCmsIdsToDelete && passEventEventPageCmsIdsToDelete.length > 0) {
        const promises = [];
        for (const cmsId of passEventEventPageCmsIdsToDelete) {
          promises.push(puPassEventMissionDelete(connection, userId, cmsId));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (attendanceEventPageCmsIdsToDelete && attendanceEventPageCmsIdsToDelete.length > 0) {
        const promises = [];
        for (const cmsId of attendanceEventPageCmsIdsToDelete) {
          promises.push(puAttendanceDelete(connection, userId, cmsId));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (newPendingDirectMails) {
        const promises = [];
        for (const elem of newPendingDirectMails) {
          promises.push(puDirectMailPendingCreate(connection, elem));
        }
        return Promise.all(promises);
      }
      return null;
    })
    .then(() => {
      if (newMails && newMails.length > 0) {
        return puDirectMailLastIdUpdate(connection, userId, newMails[newMails.length - 1].id);
      }
      return null;
    })
    .then(() => {
      if (newMails && newMails.length > 0) {
        const promises = [];
        for (const mail of newMails) {
          promises.push(puDirectMailCreate(connection, userId, mail));
        }
        return Promise.all(promises);
      }
      return null;
    })
    .then(() => {
      if (gameEventPageCmsIdsToDelete && gameEventPageCmsIdsToDelete.length > 0) {
        const promises = [];
        for (const eventPageCmsId of gameEventPageCmsIdsToDelete) {
          promises.push(puEventGameDelete(connection, userId, eventPageCmsId));
        }
        return Promise.all(promises);
      }
      return null;
    })
    .then(() => {
      if (chatTranslationChange) {
        return puChatTranslationCountUpdate(
          connection,
          userId,
          chatTranslationChange.freeCount,
          chatTranslationChange.lastDailyChargeTimeUtc
        );
      }
      return null;
    })
    .then(() => {
      if (expiredQuestGroupIds && expiredQuestGroupIds.length > 0) {
        const promises = [];
        for (const groupId of expiredQuestGroupIds) {
          promises.push(puQuestGroupRegenTimesDelete(connection, userId, groupId));
        }
        return Promise.all(promises);
      }
    })
    .then(() => {
      if (hotSpotProductCmsIdsToDelete && hotSpotProductCmsIdsToDelete.length > 0) {
        const promises = [];
        for (const hotSpotCmsId of hotSpotProductCmsIdsToDelete) {
          promises.push(puHotSpotProductsDelete(connection, userId, hotSpotCmsId));
        }
        return Promise.all(promises);
      }
    })
    .then(() => {
      if (resetPopupCountHotSpotProducts && resetPopupCountHotSpotProducts.length > 0) {
        const promises = [];
        for (const product of resetPopupCountHotSpotProducts) {
          promises.push(
            puHotSpotProductsUpdate(
              connection,
              userId,
              product.cmsId,
              product.popupCount,
              product.expireTimeUtc,
              product.coolTimeUtc,
              product.lastResetTimeUtc
            )
          );
        }
        return Promise.all(promises);
      }
    })
    .then(() => {
      if (openDurationProducts && openDurationProducts.length > 0) {
        const promises = [];
        for (const product of openDurationProducts) {
          promises.push(puCashShopOpenDurationProductUpdate(connection, userId, product));
        }
        return Promise.all(promises);
      }
    })
    .then(() => {
      return puUserTitleDelete(connection, userId, curTimeUtc);
    })
    .then(() => {
      const promises = [];
      if (costumeShipSlotChanges && costumeShipSlotChanges.length > 0) {
        for (const change of costumeShipSlotChanges) {
          promises.push(
            puCostumeShipSlotUpdate(connection, userId, change.slotSubType, change.shipSlotItemId)
          );
        }
      }
      return Promise.all(promises);
    })
    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.ENTER_WORLD_POST_PROCESS_TXN_ERROR);
      }
    });
}

export default function tuEnterWorldPostProcess(
  dbConnPool: Pool,
  userId: number,
  energyChange: EnergyChange,
  manufacturePointChange: ManufacturePointChange,
  karmaChange: KarmaChange,
  waypointSupplyTicketChange: WaypointSupplyTicketChange,
  eventPageCmsIdsToDelete: number[],
  weeklyEventChanges: WeeklyEventChange[],
  passEventEventPageCmsIdsToDelete: number[],
  attendanceEventPageCmsIdsToDelete: number[],
  newMails: MailCreatingParams[],
  lastReceiveHotTimeUtc: number,
  reputationChanges: UserReputationChange[],
  lastGameState: GAME_STATE,
  shieldChanges: Shield[],
  exploreTicketChange: ExploreTicketChange,
  exploreQuickModeChange: ExploreQuickModeChange,
  expiredEventPageCmsIdsOfEventShop: number[],
  newPendingDirectMails: PendingDirectMail[],
  notifiedMileageMonth: number,
  gameEventPageCmsIdsToDelete: number[],
  chatTranslationChange: ChatTranslationChange,
  expiredQuestGroupIds: number[],
  hotSpotProductCmsIdsToDelete: number[],
  resetPopupCountHotSpotProducts: HotSpotProduct[],
  restrictedProductsToDelete: number[],
  openDurationProducts: OpenDurationProduct[],
  expiredEquipmentIds: number[],
  shipSlotChanges: ShipSlotChange[],
  costumeShipSlotChanges: CostumeShipSlotChange[],
  curTimeUtc: number
) {
  return withTxn(dbConnPool, __filename, (connection: PoolConnection) => {
    return queryImpl(
      connection,
      userId,
      energyChange,
      manufacturePointChange,
      karmaChange,
      waypointSupplyTicketChange,
      eventPageCmsIdsToDelete,
      weeklyEventChanges,
      passEventEventPageCmsIdsToDelete,
      attendanceEventPageCmsIdsToDelete,
      newMails,
      lastReceiveHotTimeUtc,
      reputationChanges,
      lastGameState,
      shieldChanges,
      exploreTicketChange,
      exploreQuickModeChange,
      expiredEventPageCmsIdsOfEventShop,
      newPendingDirectMails,
      notifiedMileageMonth,
      gameEventPageCmsIdsToDelete,
      chatTranslationChange,
      expiredQuestGroupIds,
      hotSpotProductCmsIdsToDelete,
      resetPopupCountHotSpotProducts,
      restrictedProductsToDelete,
      openDurationProducts,
      expiredEquipmentIds,
      shipSlotChanges,
      costumeShipSlotChanges,
      curTimeUtc
    );
  });
}
