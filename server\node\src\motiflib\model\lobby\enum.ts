// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
export enum SHIP_ASSIGNMENT {
  DOCK = 0,
  FLEET = 1,
  CAPTURED = 2,
}

// ----------------------------------------------------------------------------
// 오션에서 퇴장 원인.
// ----------------------------------------------------------------------------
export enum OCEAN_ZONE_LEAVE_REASON {
  UNKNOWN = 0,
  DISCONNECTED_SERVER, // 로비서버의 소켓 종료
  ZONE_EXIST_WHEN_ENTERING_ZONE, // 존입장 시 기존 존이 있어서 기존 존에서 퇴장.
  DUPLICATE_USER_IN_ZONE, // oceanZone에서 유저 중복으로 인한 퇴장
  LOGOUT, // 로그아웃
  ARRIVE_TO_TOWN, // 마을 이동으로 인한 퇴장
  CHANGED_OCEAN_ZONE, // 권역변경으로 인한 퇴장
  TELEPORT_TO_USER, // 명령어
  TELEPORT_TO_TOWN, // 명령어
}

export enum PayloadFlag {
  Encrypt = 1,
  Compress = 1 << 1,
  Binary = 1 << 2,
}

// ----------------------------------------------------------------------------
// 오프라인항해봇 종료 원인.
// ----------------------------------------------------------------------------
export enum OFFLINE_SAILING_BOT_END_REASON {
  UNKNOWN = 0,
  DISCONNECTED_SERVER, // 로비서버의 소켓 종료
  LOGOUT, // 로그아웃
  ARRIVE_TO_DESTINATION, // 목적지 도착
}

// 길드 가입 방법
export enum GUILD_JOIN_TYPE {
  FREE = 1, // 즉시가입.
  NORMAL = 2, // 신청가입.
}

// 길드 가입 조건
export enum GUILD_JOIN_CONDITION {
  NONE = 0,
  COMBAT_POWER = 1,
  COMPANY_LEVEL = 2,
}

// 길드 가입 국가 조건
export enum GUILD_NATION_CONDITION {
  NONE = 0,
  SAME_NATION = 1,
}

// 길드원 등급
export enum GUILD_MEMBER_GRADE {
  MASTER = 1,
  A = 2,
  B = 3,
  C = 4,

  // highest/lowest in normal members
  HIGHEST = A,
  LOWEST = C,
}

// 상회원 등급에 따라 기능 사용권한이 분류.
export enum GUILD_MEMBER_GRADE_ACCESS_CATEGORY {
  // 상회 알림 기능
  GUILD_NOTICE = 1,

  // 상회원관리(강퇴,가입승인or거절)
  MEMBER_MANAGE = 2,

  // 상회토벌 열기
  RAID_OPEN = 3,

  // 버프 구매,강화, 성향변경
  BUFF_MANAGING = 4,
}

// 자동알림 정보
export enum GUILD_AUTO_NOTIFICATION_TYPE {
  GUILD_LEVEL_UP = 1,
  JOIN_NEW_MEMBER = 2,
  LEAVE = 3,
  KICKED = 4,
  DELEGATION = 5,
  GUILD_BUFF_LEARN_FIRST = 6, // 상회 효과 습득
  GUILD_BUFF_UPGRADE = 7, // 상회 효과 강화
  CHANGE_GUILD_BUFF_CATEGORY = 8, // 상회 효과 카테고리 변경
}

// 길드 pub_sub 이벤트목록
export enum GUILD_PUBSUB_EVENT {
  MEMBER_LOGIN = 1,
  MEMBER_LOGOUT = 2,
  MEMBER_JOIN = 3,
  MEMBER_LEAVE = 4,
  MEMBER_KICK = 5,
  CHANGE_GUILD_INFO = 6,
  CHANGE_MEMBER_GRADE = 7,
  DELEGATE_MASTER = 8,
}

// 길드원에게 전송할 메일 cms id
export enum GUILD_MAIL {
  JOIN = 92003001,
  KICKED = 92003002,
  DELEGATE = 92003003,
  EXTERNAL_DONATION = 92100244,
}

// 길드 길드포인트 구분
export enum GUILD_POINT_CATEGORY {
  ATTENDANCE = 1, // 출석
  BATTLE = 2, // 전투승리(npc)
  TRADE = 3, // 교역
  EXPLORE = 4, // 탐험
  PLUNDER = 5, // 약탈(pvp승리) **** 기획에서 제외됨.
  CRAFT = 6, // 제작
  INVEST = 7, // 투자
  UNION_QUEST_COMPLETE = 8, // 조합의뢰 완료
  SYNTHESIS = 9, // 합성
  MAX = 10,
}

// 길드 자동안내 만료시간.(sec)
export const GUILD_AUTO_NOTIFICATION_EXPIRE_TIME = 60 * 60 * 24; // a day.

// 월드당 길드아이디 범위.
// 1번월드는 100001 부터 id 발급
// 2번월드느 200001 부터 id 발급
// 3번월드는 300001 부터 id 발급
export const GuildIdRangePerWorld = 100000;

// 길드 제작 종류
export enum GUILD_CRAFT_CATEGORY {
  NORMAL = 1, // 일반제작
  SPECIAL = 2, // 특수 제작
}

// 자동항해 목적지 타입
export enum AUTO_SAIL_DEST_TYPE {
  NONE = 0, // 없음
  TOWN = 1, // 항구
  VILLAGE = 2, // 마을
}

// 레이드 상태
export enum RAID_STATE {
  INPROGRESS = 1, // 진행 중
  CLOSING = 2, // 정산 중
  REWARD = 3, // 보상 기간
}

// 레이드 알림 메세지 타입
export enum RAID_NOTICE_TYPE {
  APPEARANCE = 1, // 등장
  DISAPPEARANCE = 2, // 퇴장
  RANKING_COMPLETED = 3, // 순위 정산 완료
}

// 길드 레이드 상태
export enum GUILD_RAID_STATE {
  NONE = 0, // 미진행 (아직 1회도 진행 안된 경우)
  INPROGRESS = 1, // 진행 중
  CHALLENGING = 2, // 도전 진행 중 (INPROGRESS 상태에서 보스처치시 남은시간동안 도전상태가 된다. )
  CLOSING = 3, // 정산 중
  SUCCESS = 4, // 토벌 성공
  FAILED = 5, //  토벌 실패
}

// 길드 레이드 알림 메세지 타입
export enum GUILD_RAID_NOTICE_TYPE {
  APPEARANCE = 1, // 등장
  DISAPPEARANCE = 2, // 퇴장
  KILL_BOSS = 3, // 보스 토벌
  LEFT_HP = 4, // 남은 HP
  RANKING_COMPLETED = 5, // 순위 정산 완료
}

//  길드 레이드 랭킹 범주
export enum GUILD_RAID_RANKING_CATEGORY {
  ACCUMULATED_DMG = 1, // 레이드 종료시까지 누적데미지
  BEST_DMG = 2, // 한번의 전투에 가한 최대 데미지
}

//  레이드 랭킹 범주
export enum RAID_RANKING_CATEGORY {
  ACCUMULATED_DMG = 1, // 레이드 종료시까지 누적데미지
  // NATION_DMG = 2, // 레이드 종료시까지 국가 별 누적 데미지.

  MIN = ACCUMULATED_DMG,
  MAX = ACCUMULATED_DMG,
}

// 레이드 공격 결과
export enum RAID_HIT_TYPE {
  NO_ATTACK = 0, // 피해량이 없을 경우
  FIRST = 1, // 첫타
  LAST = 2, // 막타
  NORMAL = 3, // 일반
}

// 1:친구요청 2: 수락 3:거절 4:친구삭제 5:친구로그인 6:친구로그아웃
// 친구 알림 정보
export enum FRIEND_NOTIFICATION_TYPE {
  REQUESTED = 0, // 친구요청 받음
  CANCELED = 1, // 친구요청 취소됨
  ACCEPTED = 2, // 친구요청 수락받음
  DENIED = 3, // 친구요청 거부받음
  DELETED = 4, // 상대방이 친구삭제
  LOGIN = 5, // 친구 로그인
  LOGOUT = 6, // 친구 로그아웃
  RECV_POINT = 7, // 친구에게서 포인트 받음.
}

/**
 * fleetDispatchs 의 fleetIndex에 오브젝트가 없는경우 - 미파견
 * fleetDispatchs 의 fleetIndex에 오브젝트가 있고 endTimeUtc 가 만료전- 파견진행중
 * fleetDispatchs 의 fleetIndex에 오브젝트가 있고 endTimeUtc 가 만료이고 rewards 가 없는경우 - 완료(결과확인)가능
 * fleetDispatchs 의 fleetIndex에 오브젝트가 있고 endTimeUtc 가 만료이고 rewards 가 있는경우 - 보상수령가능
 */
export enum FleetDispatchState {
  NONE = 0,
  IN_PROGRESS = 1,
  REWARD = 2,
}

// ----------------------------------------------------------------------------
// 전투 모드 타입 Enum
// ! 수정 시, 클라이언트 VaBattleCommon.lua 에 BattleType 같이 수정
// ! 수정 시, WorldBuff 의 modeBitFlag 값의 비트 인덱스로 사용중이므로 같이 수정
// ----------------------------------------------------------------------------
export enum BattleType { //battletype으로 변경
  Encount = 1,
  Challenge = 2,
  Arena = 3,
  Raid = 4,
  GuildRaid = 5,
  InfiniteLighthouse = 6,
  Friendly = 7,
  Clash = 8,
}

// 강화 성공여부 (클라 전송용)
export enum ENCHANT_RESULT {
  SUCCESS = 1,
  KEEP = 2,
  DESTROY = 3,
}

export enum ManufactureRoomState {
  NONE = 0,
  IN_PROGRESS = 1,
  REWARD = 2,
}