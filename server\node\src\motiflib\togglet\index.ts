// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

// import ToggletClient from './toggletClient';
import mconf from '../mconf';
import { getLocalIp, getPublicIp } from './ip';
import { getAppInfo } from '../appInfo';

// let togglet: ToggletClient | undefined;

async function startTogglet(): Promise<void> {
  // if (togglet) {
  //   stopTogglet();
  // }

  // const appInfo = getAppInfo();

  // const environment = appInfo.environment;

  // const serverBuildBranch = appInfo.gitBranch
  // const serverHostname = appInfo.hostname;
  // const serverCommitDate = appInfo.gitCommitDate;
  // const serverCommitHash = appInfo.gitCommitHash;

  // const serverExternalAddress = await getPublicIp();
  // const serverInternalAddress = getLocalIp();

  // const apiSettings = {
  //   environments: {
  //     development: {
  //       apiUrl: 'https://us.app.unleash-hosted.com/usii0012/api/',
  //       accessToken: 'uwo:development.ac6e32d9cfc0537aa6685e31c506a3d0f9cbf4addc48f89e76e2b1be',
  //     },
  //     qa: {
  //       apiUrl: 'https://us.app.unleash-hosted.com/usii0012/api/',
  //       accessToken: 'uwo:qa.a9e90e3aa41efb4f0912289215f440dd14de0d40133f8b507c566c40',
  //     },
  //     production: {
  //       apiUrl: 'https://us.app.unleash-hosted.com/usii0012/api/',
  //       accessToken: 'uwo:production.14f3a09ad071bd7959e699928e52d7a627ec6286e17d8fa4ec3bc311',
  //     },
  //   }
  // };

  // const apiUrl = apiSettings.environments[environment].apiUrl;
  // const accessToken = apiSettings.environments[environment].accessToken;
  // const refreshIntervalInSec = 10;

  // togglet = new ToggletClient({
  //   apiUrl,
  //   accessToken,
  //   environment,
  //   appName: appInfo.type,
  //   refreshIntervalInSec,
  //   defaultContext: {
  //     appName: appInfo.type,
  //     environment,
  //     properties: {
  //       serverHostname,
  //       serverBuildBranch,
  //       serverCommitDate,
  //       serverCommitHash,
  //       serverExternalAddress,
  //       serverInternalAddress,
  //       binaryCode: mconf.binaryCode,
  //       countryCode: mconf.countryCode,
  //       serverType: process.name,
  //       worldId: mconf.worldId,
  //     },
  //   },
  // });

  // await togglet.start();
}

function stopTogglet() {
  // if (togglet) {
  //   togglet.stop();
  //   togglet = undefined;
  // }
}

export { /*togglet, */startTogglet, stopTogglet };