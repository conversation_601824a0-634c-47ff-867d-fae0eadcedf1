// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { SPLAT } from 'triple-beam';
import { inspect } from 'util';
import { createLogger, format, Logger, transports, addColors } from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import mconf from './mconf';
import { MError } from './merror';
import path from 'path';

const { combine, colorize, timestamp, printf, json } = format;

const MLogLevels = {
  levels: {
    emerg: 0,
    alert: 1,
    error: 2,
    warn: 3,
    info: 4,
    verbose: 5,
    debug: 6,
  },
  colors: {
    emerg: 'magenta',
    alert: 'blue',
    error: 'red',
    warn: 'yellow',
    info: 'green',
    verbose: 'cyan',
    debug: 'grey',
  },
};

const stringifiedJsonConsolePrintFormat = printf((info) => {
  try {
    return `${info.timestamp} - ${info.level}: ${info.message} ${
      info[SPLAT] ? JSON.stringify(info[SPLAT]) : ''
    }`;
  } catch (err) {
    return `${info.timestamp} - ${info.level}: ${info.message} ${info[SPLAT]}`;
  }
});

const newConsolePrintFormat = printf((info) => {
  const data = info[SPLAT] ? info[SPLAT][0] : null;
  return `${info.timestamp} ${info.level}: ${info.message} ${
    data
      ? inspect(data, {
          depth: parseInt(mconf.get('log.console.inspectDepth'), 10),
          colors: true,
          maxArrayLength: 10,
        })
      : ''
  }`;
});

/**
 * Warp Winston createLogger
 * @param filename 로테이션 파일의 prefix
 * @param savedir 파일 저장소 디렉터리
 */
function newLogger(filename: string, savedir: string): Logger {
  const consolePrintFormat = mconf.get<boolean>('log.console.bUseStringifiedJsonPrintFormat')
    ? stringifiedJsonConsolePrintFormat
    : newConsolePrintFormat;
  const mlogger = createLogger({
    levels: MLogLevels.levels,
    transports: [
      new transports.Console({
        level: mconf.get('log.console.level') || 'verbose',
        handleExceptions: false,
        format: combine(
          colorize(),
          timestamp({
            //format: 'MM/DD HH:mm:ss.SSS',
            format: 'MM/DD HH:mm:ss',
          }),
          // timestampColorize({ color: 'gray' }),
          consolePrintFormat
        ),
      }),

      new DailyRotateFile({
        //maxFiles: '30d',
        //maxSize: '20m',
        dirname: savedir,
        filename: filename,
        level: mconf.get('log.file.level') || 'info',
        // extension: '.json',
        handleExceptions: false,
        datePattern: mconf.get('log.file.datePattern') || 'YYYY-MM-DD-HH',
        format: combine(timestamp(), json()),
      }),
    ],
  });

  addColors(MLogLevels.colors);

  return mlogger;
}

declare type JsonLike = { [key: string]: any };
export class MLog {
  logger: Logger;

  constructor(savedir: string = './log') {
    savedir = path.join(mconf.log.baseDir, savedir);
    let filename;
    if (mconf.log.bAppIdName) {
      filename = mconf.appId;
    } else {
      filename = process.name;
    }
    this.logger = newLogger(filename, savedir);
  }

  get level(): string {
    return this.logger.level;
  }

  emerg(message: string, data?: JsonLike): void {
    this.logger.emerg(message, data);
  }

  fatal(message: string, data?: JsonLike): void {
    this.logger.alert(message, data);
  }

  alert(message: string, data?: JsonLike): void {
    this.logger.alert(message, data);
  }

  error(message: string, data?: JsonLike): void {
    this.logger.error(message, data);
  }

  info(message: string, data?: JsonLike): void {
    this.logger.info(message, data);
  }

  verbose(message: string, data?: JsonLike): void {
    this.logger.verbose(message, data);
  }

  trace(message: string, data?: JsonLike): void {
    this.logger.verbose(message, data);
  }

  debug(message: string, data?: JsonLike): void {
    this.logger.debug(message, data);
  }

  exception(message: string, error: Error | MError): void {
    if (error instanceof MError) {
      this.logger.error(message, error);
    } else {
      this.logger.error(message, {
        error: error.message,
        stack: error.stack,
      });
    }
  }

  warn(message: string, data?: JsonLike): void {
    this.logger.warn(message, data);
  }

  setConsoleLevel(newLevel: string): void {
    this.logger.transports[0].level = newLevel;
  }

  setFileLevel(newLevel: string): void {
    this.logger.transports[1].level = newLevel;
  }
}

const logger = new MLog();
export default logger;
