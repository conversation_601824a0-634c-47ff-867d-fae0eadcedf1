// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import glogFactory from './glog';
import * as cmsEx from '../cms/ex';
import { CEquipDesc } from '../cms/cEquipDesc';
import { MateEquipmentNub } from './model/lobby';
import mconf from './mconf';
import mlog from './mlog';
import { formatEventTime, sdoGLog, SdoGLogFromGLog } from './sdoGLogs';

export enum OCEAN_DOODAD_RESULT_FLAG {
  NONE = 0, // 특정한 리워드가 없는 경우
  REWARD = 1, // 리워드가 있는 경우
  BUFF = 2, // 버프
  DISASTER = 3, // 재해
}

export interface PrData {
  type: number;
  amt: number;
}

export interface CostData {
  type: string;
  id: number;
  amt: number;
}

export interface TradeData {
  ship_idx: number;
  ship_id: number;
  ship_uid: number;
  ship_guid: string;
  id: number;
  category: number;
  name: string;
  quantity: number;
  is_speciality: number;
  /**
   * 원가(낱개)
   */
  subprice: number;
  /**
   * 최종가(낱개)
   */
  price: number;
  total: number;
}

export interface RewardData {
  type: string;
  id: number;
  uid: number;
  amt: number;
}

enum GLOG_TIME_REDUCTION_TYPE {
  SHIP_BUILD = 1,
  PUB_MATE_NEGO = 2,
  PUB_MY_MATE_SPONSOR = 3,
  MATE_AWAKEN = 4,
  MATE_LEARN_PASSIVE = 5,
  GUILD_CRAFT = 6,
  SYNTHESIS_CRAFT = 7,
  MATE_TRAINING = 8,
}

interface TimeReductionData {
  rsn: string;
  add_rsn: string | null;

  type: GLOG_TIME_REDUCTION_TYPE;
  old_duration: number;
  cur_duration: number;
  pr_data: PrData[] | null;
  cost_data: CostData[] | null;
  ship_build_data: {
    town_id: number;
    town_name: string;
    ship_id: number;
    ship_uid: number;
    ship_guid: string;
    ship_name: string;
  } | null;
  mate_recruit_data: {
    town_id: number;
    town_name: string;
    mate_id: number;
    mate_name: string;
  } | null;
  mate_support_data: {
    town_id: number;
    town_name: string;
    mate_id: number;
    mate_name: string;
  } | null;
  mate_awaken_data: {
    mate_id: number;
    mate_name: string;
    awakenLv: number; // 예정 승급 레벨
  } | null;
  mate_passive_data: {
    mate_id: number;
    mate_name: string;
    skill_id: number;
    skill_name: string;
  } | null;
  guild_craft_data: {
    type: number;
    craft_slot_idx: number;
    product_id: number;
    product_name: string;
  } | null;
  guild_synthesis_data: {
    synthesis_slot_idx: number;
    product_id: number;
    product_name: string;
  } | null;
  mate_promotion_data: {
    mate_id: number;
    mate_name: string;
    promotionGrade: number;
  } | null;

  exchange_hash?: string | null;
}

type TimeReductionDataCommonKey =
  | 'rsn'
  | 'add_rsn'
  | 'old_duration'
  | 'cur_duration'
  | 'pr_data'
  | 'cost_data'
  | 'exchange_hash';

export interface GLogShipData {
  id: number;
  name: string;
  uid: number;
  guid: string;
  lv: number;
  size: number;
  captain_id: number;
  captain_name: number;
  durability: number;
  max_durability: number;
  sailor: number;
  max_sailor: number;
  cargo: number;
  max_cargo: number;
  stat: { type: string; val: number }[];
  special_stat: number[];
  fleet_idx: number;
  ship_idx: number;
  max_life: number;
  life: number;
  enchant_cnt: number;
  max_enchant_cnt: number;
  enchant_stat: { type: string; val: number }[];
}

export const glogShipStats = [
  cmsEx.STAT_TYPE.SHIP_LIFE,
  cmsEx.STAT_TYPE.SHIP_MIN_SAILOR,
  cmsEx.STAT_TYPE.SHIP_MAX_SAILOR,
  cmsEx.STAT_TYPE.SHIP_MAX_DURABILITY,
  cmsEx.STAT_TYPE.SHIP_HOLD,
  cmsEx.STAT_TYPE.NONCOMBAT_ICE_BREAKING,
  cmsEx.STAT_TYPE.NONCOMBAT_BREAK_THROUGH,
  cmsEx.STAT_TYPE.SHIP_OAR_POWER,
  cmsEx.STAT_TYPE.SHIP_WAVE_RESISTANCE,
  cmsEx.STAT_TYPE.SHIP_VERTICAL_SAIL,
  cmsEx.STAT_TYPE.SHIP_HORIZONTAL_SAIL,
  cmsEx.STAT_TYPE.SHIP_GUNPORTS,
  cmsEx.STAT_TYPE.BATTLE_HUMAN_ATTACK,
  cmsEx.STAT_TYPE.BATTLE_RAMMING_POWER,
  cmsEx.STAT_TYPE.BATTLE_2ND_CANNON_DEFENSE,
  cmsEx.STAT_TYPE.BATTLE_2ND_MELEE_DEFENSE,
  cmsEx.STAT_TYPE.BATTLE_2ND_RAMMING_DEFENSE,
  cmsEx.STAT_TYPE.BATTLE_REPAIR_AMOUNT,
  cmsEx.STAT_TYPE.BATTLE_HEAL_AMOUNT,
  cmsEx.STAT_TYPE.SHIP_ACCELERATION,
  cmsEx.STAT_TYPE.SHIP_ANGULARPOWER,
];

export interface GLogShipCabinData {
  slot_idx: number;
  slot_name: string;
  mate_id: number;
  mate_name: string;
}

export interface GLogShipPartsData {
  slot_idx: number;
  slot_type: number;
  parts_id: number;
  parts_name: string;
}

export interface GLogPartsData {
  id: number;
  name: string;
  uid: number;
  slot_type: string;
  grade: number;
}

export interface GLogEquipData {
  id: number;
  name: string;
  uid: number;
  type: string;
  grade: number;
}

export interface GLogMountEquipData {
  equip_id: number;
  equip_name: string;
  equip_uid: number;
  equip_type: number;
  equip_grade: number;
  equip_color1: number;
  equip_color2: number;
}
export interface GLogShipExpData {
  ship_id: number;
  ship_name: string;
  ship_uid: number;
  ship_guid: string;
  exp: number;
}

export interface SmuggleData {
  ship_idx: number;
  ship_id: number;
  ship_uid: number;
  ship_guid: string;
  id: number;
  category: number;
  name: string;
  quantity: number;
  subprice: number;
  price: number;
  total: number;
}

export function MakeShipBuildTimeReductionData(
  collection: Pick<TimeReductionData, TimeReductionDataCommonKey | 'ship_build_data'>
): TimeReductionData {
  return {
    rsn: collection.rsn,
    add_rsn: collection.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.SHIP_BUILD,
    old_duration: collection.old_duration,
    cur_duration: collection.cur_duration,
    pr_data: collection.pr_data,
    cost_data: collection.cost_data,
    ship_build_data: collection.ship_build_data,
    mate_recruit_data: null,
    mate_support_data: null,
    mate_awaken_data: null,
    mate_passive_data: null,
    guild_craft_data: null,
    guild_synthesis_data: null,
    mate_promotion_data: null,
    exchange_hash: collection.exchange_hash ? collection.exchange_hash : null,
  };
}

export function MakePubMateNegoTimeReductionData(
  data: Pick<TimeReductionData, TimeReductionDataCommonKey | 'mate_recruit_data'>
): TimeReductionData {
  return {
    rsn: data.rsn,
    add_rsn: data.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.PUB_MATE_NEGO,
    old_duration: data.old_duration,
    cur_duration: data.cur_duration,
    pr_data: data.pr_data,
    cost_data: data.cost_data,
    ship_build_data: null,
    mate_recruit_data: data.mate_recruit_data,
    mate_support_data: null,
    mate_awaken_data: null,
    mate_passive_data: null,
    guild_craft_data: null,
    guild_synthesis_data: null,
    mate_promotion_data: null,
    exchange_hash: data.exchange_hash ? data.exchange_hash : null,
  };
}

export function MakePubMyMateSponsorTimeReductionData(
  data: Pick<TimeReductionData, TimeReductionDataCommonKey | 'mate_support_data'>
): TimeReductionData {
  return {
    rsn: data.rsn,
    add_rsn: data.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.PUB_MY_MATE_SPONSOR,
    old_duration: data.old_duration,
    cur_duration: data.cur_duration,
    pr_data: data.pr_data,
    cost_data: data.cost_data,
    ship_build_data: null,
    mate_recruit_data: null,
    mate_support_data: data.mate_support_data,
    mate_awaken_data: null,
    mate_passive_data: null,
    guild_craft_data: null,
    guild_synthesis_data: null,
    mate_promotion_data: null,
    exchange_hash: data.exchange_hash ? data.exchange_hash : null,
  };
}

export function MakeMateAwakenTimeReductionData(
  data: Pick<TimeReductionData, TimeReductionDataCommonKey | 'mate_awaken_data'>
): TimeReductionData {
  return {
    rsn: data.rsn,
    add_rsn: data.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.MATE_AWAKEN,
    old_duration: data.old_duration,
    cur_duration: data.cur_duration,
    pr_data: data.pr_data,
    cost_data: data.cost_data,
    ship_build_data: null,
    mate_recruit_data: null,
    mate_support_data: null,
    mate_awaken_data: data.mate_awaken_data,
    mate_passive_data: null,
    guild_craft_data: null,
    guild_synthesis_data: null,
    mate_promotion_data: null,
    exchange_hash: data.exchange_hash ? data.exchange_hash : null,
  };
}

export function MakeMateLearnPassiveTimeReductionData(
  data: Pick<TimeReductionData, TimeReductionDataCommonKey | 'mate_passive_data'>
): TimeReductionData {
  return {
    rsn: data.rsn,
    add_rsn: data.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.MATE_LEARN_PASSIVE,
    old_duration: data.old_duration,
    cur_duration: data.cur_duration,
    pr_data: data.pr_data,
    cost_data: data.cost_data,
    ship_build_data: null,
    mate_recruit_data: null,
    mate_support_data: null,
    mate_awaken_data: null,
    mate_passive_data: data.mate_passive_data,
    guild_craft_data: null,
    guild_synthesis_data: null,
    mate_promotion_data: null,
    exchange_hash: data.exchange_hash ? data.exchange_hash : null,
  };
}

export function MakeGuildCraftTimeReductionData(
  collection: Pick<TimeReductionData, TimeReductionDataCommonKey | 'guild_craft_data'>
): TimeReductionData {
  return {
    rsn: collection.rsn,
    add_rsn: collection.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.GUILD_CRAFT,
    old_duration: collection.old_duration,
    cur_duration: collection.cur_duration,
    pr_data: collection.pr_data,
    cost_data: collection.cost_data,
    ship_build_data: null,
    mate_recruit_data: null,
    mate_support_data: null,
    mate_awaken_data: null,
    mate_passive_data: null,
    guild_craft_data: collection.guild_craft_data,
    guild_synthesis_data: null,
    mate_promotion_data: null,
    exchange_hash: collection.exchange_hash ? collection.exchange_hash : null,
  };
}

export function MakeGuildSynthesisTimeReductionData(
  collection: Pick<TimeReductionData, TimeReductionDataCommonKey | 'guild_synthesis_data'>
): TimeReductionData {
  return {
    rsn: collection.rsn,
    add_rsn: collection.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.SYNTHESIS_CRAFT,
    old_duration: collection.old_duration,
    cur_duration: collection.cur_duration,
    pr_data: collection.pr_data,
    cost_data: collection.cost_data,
    ship_build_data: null,
    mate_recruit_data: null,
    mate_support_data: null,
    mate_awaken_data: null,
    mate_passive_data: null,
    guild_craft_data: null,
    guild_synthesis_data: collection.guild_synthesis_data,
    mate_promotion_data: null,
    exchange_hash: collection.exchange_hash ? collection.exchange_hash : null,
  };
}

export function MakeMateTrainingTimeReductionData(
  collection: Pick<TimeReductionData, TimeReductionDataCommonKey | 'mate_promotion_data'>
) {
  return {
    rsn: collection.rsn,
    add_rsn: collection.add_rsn,

    type: GLOG_TIME_REDUCTION_TYPE.MATE_TRAINING,
    old_duration: collection.old_duration,
    cur_duration: collection.cur_duration,
    pr_data: collection.pr_data,
    cost_data: collection.cost_data,
    ship_build_data: null,
    mate_recruit_data: null,
    mate_support_data: null,
    mate_awaken_data: null,
    mate_passive_data: null,
    guild_craft_data: null,
    guild_synthesis_data: null,
    mate_promotion_data: collection.mate_promotion_data,
    exchange_hash: collection.exchange_hash ? collection.exchange_hash : null,
  };
}

export function MakeMountEquipGLogData(
  equipmentNub: MateEquipmentNub,
  equipCms: CEquipDesc
): GLogMountEquipData | null {
  if (!equipmentNub) {
    return null;
  }
  return {
    equip_id: equipCms.id,
    equip_name: equipCms.name,
    equip_uid: equipmentNub.id,
    equip_type: equipCms.type,
    equip_grade: equipCms.grade,
    equip_color1: equipmentNub.dye1 ? equipmentNub.dye1 : null,
    equip_color2: equipmentNub.dye2 ? equipmentNub.dye1 : null,
  };
}

export const glogInstance = glogFactory(process.name);

export default function glog(collection: string, data: any) {
  if (mconf.isDev) {
    mlog.info(`[GLOG] collection=${collection}:`, data);
  }

  glogInstance.data(collection, { data });

  // convert to SDO GLog
  // if (mconf.isSDO) {
  //   const log = new SdoGLogFromGLog({
  //     event_name: `__________________${collection}_glog`,

  //     event_time: formatEventTime(),
  //     game_id: parseInt(mconf.sdo.appId),
  //     area_id: mconf.sdo.areaId,
  //     group_id: mconf.sdo.groupId,

  //     mid: data.gnid || undefined,
  //     game_user_id: data.gameUserId || undefined,

  //     ...data,
  //   });
  //   sdoGLog(log);
  // }
}
