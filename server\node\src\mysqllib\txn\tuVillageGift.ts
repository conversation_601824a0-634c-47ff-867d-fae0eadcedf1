// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import { withTxn } from '../mysqlUtil';
import { MError, MErrorCode } from '../../motiflib/merror';
import { Village } from '../../lobbyd/type/sync';
import puVillageFriendshipUpdate from '../sp/puVillageFriendshipUpdate';
import { PointChange } from '../../lobbyd/userPoints';
import { ShipCargoChange } from '../../lobbyd/ship';
import puPointUpdate from '../sp/puPointUpdate';
import puShipCargoUpdate from '../sp/puShipCargoUpdate';
import { ItemChange } from '../../lobbyd/userInven';
import puItemUpdate from '../sp/puItemUpdate';

function queryImpl(
  connection: PoolConnection,
  userId: number,
  friendship: number,
  maximumFriendship: number,
  villageCmsId: number,
  pointChanges: PointChange[],
  cargoChanges: ShipCargoChange[],
  itemChanges: ItemChange[]
) {
  return Promise.resolve()
    .then(() => {
      if (!pointChanges || pointChanges.length === 0) {
        return;
      }

      const promises = [];
      for (const change of pointChanges) {
        promises.push(puPointUpdate(connection, userId, change));
      }
      return Promise.all(promises);
    })
    .then(() => {
      if (!cargoChanges || cargoChanges.length === 0) {
        return;
      }
      const promises = [];
      for (const cargo of cargoChanges) {
        promises.push(puShipCargoUpdate(connection, userId, cargo));
      }
      return Promise.all(promises);
    })
    .then(() => {
      if (!itemChanges || itemChanges.length === 0) {
        return;
      }
      const promises = [];
      for (const itemChange of itemChanges) {
        promises.push(puItemUpdate(
              connection,
              userId,
              itemChange.cmsId,
              itemChange.count,
              itemChange.unboundCount
            )
          );
      }
      return Promise.all(promises);
    })
    .then(() => {
      return puVillageFriendshipUpdate(
        connection,
        userId,
        villageCmsId,
        friendship,
        maximumFriendship
      );
    })
    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.VILLAGE_GIFT_TXN_ERROR);
      }
    });
}

export default function (
  dbConnPool: Pool,
  userId: number,
  friendship: number,
  maximumFriendship: number,
  villageCmsId: number,
  pointChanges: PointChange[],
  cargoChanges: ShipCargoChange[],
  itemChanges: ItemChange[]
) {
  return withTxn(dbConnPool, __filename, (connection: PoolConnection) => {
    return queryImpl(
      connection,
      userId,
      friendship,
      maximumFriendship,
      villageCmsId,
      pointChanges,
      cargoChanges,
      itemChanges
    );
  });
}
