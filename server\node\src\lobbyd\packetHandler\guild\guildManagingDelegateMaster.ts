// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { <PERSON><PERSON><PERSON><PERSON>etHandler } from '../index';
import Container from 'typedi';
import { LobbyService } from '../../server';
import {
  GuildData,
  GuildMemberNub,
  GUILD_AUTO_NOTIFICATION_TYPE,
  GUILD_MEMBER_GRADE,
} from '../../../motiflib/model/lobby';
import { GuildUtil, GuildLogUtil } from '../../guildUtil';
import { Sync, UserLightInfo } from '../../type/sync';
import { curTimeUtc } from '../../../motiflib/mutil';
import { onGuildPublish } from '../../guildPubsub';
import cms from '../../../cms';
import mconf from '../../../motiflib/mconf';
import { SdoGLogEvents } from '../../../motiflib/sdoGLogs.generated';
import { FirstFleetIndex } from '../../../cms/ex';

const rsn = 'guild_member_change';
const add_rsn = null;

// ----------------------------------------------------------------------------
interface RequestBody {
  delegationUserId: number;
}

/**
 * 길드장 위임(길드장 전용)
 */
// ----------------------------------------------------------------------------
export class Cph_Guild_ManagingDelegateMaster implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { guildRedis } = Container.get(LobbyService);

    const userId: number = user.userId;

    const guildId: number = user.userGuild.guildId;
    if (!guildId) {
      throw new MError('there-is-no-guild-joined.', MErrorCode.GUILD_NOT_JOINED, {
        userId: user.userId,
      });
    }

    if (user.userId === body.delegationUserId) {
      throw new MError('can-not-delegate-to-self.', MErrorCode.GUILD_CANNOT_DELEGATE_TO_SELF, {
        userId: user.userId,
      });
    }

    let sync: Sync;
    let oldMaster: GuildMemberNub;
    let newMaster: GuildMemberNub;
    let guildData: GuildData;
    let userLightInfos: { [userId: number]: UserLightInfo };

    let newMasterNid = null;
    let newMasterNationCmsId = null;
    let newMasterOldGrade = null;

    //======================================================================================================
    return (
      GuildUtil.GetGuildDataWithMemberLightInfo(user, guildId)
        .then((result) => {
          if (!result) {
            throw new MError('can-not-find-guild.', MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS, {
              userId: user.userId,
              guildId,
            });
          }

          guildData = result.guildData;
          userLightInfos = result.userLightInfos;

          GuildUtil.ensureMaster(user, guildId, guildData.members);

          oldMaster = guildData.members[user.userId];
          newMaster = guildData.members[body.delegationUserId];
          if (!newMaster) {
            throw new MError(
              'user-to-delegate-is-not-member-in-the-guild.',
              MErrorCode.GUILD_NOT_MEMBER,
              {
                userId: user.userId,
                guildId,
                delegationUserId: body.delegationUserId,
              }
            );
          }

          newMasterNid = userLightInfos[body.delegationUserId].pubId;
          newMasterNationCmsId = userLightInfos[body.delegationUserId].nationCmsId;
          newMasterOldGrade = newMaster.grade;

          newMaster.grade = GUILD_MEMBER_GRADE.MASTER;
          return guildRedis['updateGuildMember'](
            guildId,
            body.delegationUserId,
            JSON.stringify(newMaster)
          );
        })
        //======================================================================================================
        // 기존 길드장은  C등급으로 멤버 업데이트
        //======================================================================================================
        .then(() => {
          oldMaster.grade = GUILD_MEMBER_GRADE.C;
          return guildRedis['updateGuildMember'](guildId, userId, JSON.stringify(oldMaster));
        })

        //======================================================================================================
        // 자동알림 업데이트
        //======================================================================================================
        .then(() => {
          const userLightInfo = userLightInfos[body.delegationUserId];
          if (userLightInfo) {
            guildData.guild.autoNotificationType = GUILD_AUTO_NOTIFICATION_TYPE.DELEGATION;
            guildData.guild.autoNotificationParam1 = userLightInfo.name;
            guildData.guild.autoNotificationRegTimeUtc = curTimeUtc();
            return guildRedis['updateGuild'](guildId, JSON.stringify(guildData.guild));
          }
        })
        //======================================================================================================
        // 길드원들에게 길드장 위임 알림
        //======================================================================================================
        .then(() => {
          sync = {
            add: {
              userGuild: {
                guild: {
                  autoNotificationType: guildData.guild.autoNotificationType,
                  autoNotificationParam1: guildData.guild.autoNotificationParam1,
                  autoNotificationParam2: guildData.guild.autoNotificationParam2,

                  members: {
                    [userId]: {
                      grade: oldMaster.grade,
                    },
                    [body.delegationUserId]: {
                      grade: newMaster.grade,
                    },
                  },
                },
              },
            },
          };
          onGuildPublish(guildData, userLightInfos, [user.userId], sync);
        })
        //======================================================================================================
        //길드 이름,엠블렘,컬러 변경 시 필드 주변 유저들에게도 동기화가 필요하다.
        //======================================================================================================
        .then(() => {
          user.userGuild.updateGuildAppearance(
            user,
            guildData.guild.guildName,
            guildData.members[user.userId].grade,
            guildData.guild.emblemImageCmsId,
            guildData.guild.emblemColorCmsId,
            guildData.guild.emblemBorderCmsId
          );
        })

        //======================================================================================================
        // 응답
        //======================================================================================================
        .then(() => {
          gLog_guild_member_change(
            user,
            newMasterNid,
            newMasterNationCmsId,
            2, // 위임
            newMasterOldGrade,
            newMaster.grade,
            guildId,
            guildData,
            userLightInfos
          );

          user.sendJsonPacket(packet.seqNum, packet.type, { sync });
        })
    );
  }
}

function gLog_guild_member_change(
  user: User,
  member_nid: string,
  member_nation: number,
  type: number,
  old_grade: number,
  cur_grade: number,
  guildId: number,
  guildData: GuildData,
  userLightInfos: { [userId: number]: UserLightInfo }
) {
  const guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);

  let change_nation: string = null;
  if (member_nation) {
    const nationCms = cms.Nation[member_nation];
    change_nation = nationCms ? nationCms.name : null;
  }

  user.glog('guild_member_change', {
    rsn,
    add_rsn,
    change_nid: member_nid,
    change_nation,
    type,
    old_grade,
    cur_grade,
    guild_data,
  });

    if (mconf.isSDO) {
    SdoGLogEvents.uwo_guild_member_glog({
      mid: user.accountId,
      character_id: user.pubId,
      character_name: user.userName,
      character_level: user.level,
      power: user.companyStat.getFleetStat(FirstFleetIndex).getCombatPower(),
      channel_id: user.channel,
      sub_channel_id: user.subChannel,
      platform: user.platform,
      device_id: user.udid,
      ip: user.countryIp,
      port: 0,
      guild_id: guildId.toString(),
      guild_level: guild_data.lv,
      member_num: guild_data.members,
      change_reason: rsn,
      is_positive: 1,
      guild_name: guildData.guild.guildName,
    });
  }
}
