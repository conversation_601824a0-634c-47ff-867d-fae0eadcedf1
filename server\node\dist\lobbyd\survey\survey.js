"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Survey = void 0;
const mailBuilder_1 = require("../../motiflib/mailBuilder");
const mutil = __importStar(require("../../motiflib/mutil"));
const tuDevAddDirectMail_1 = __importDefault(require("../../mysqllib/txn/tuDevAddDirectMail"));
const Container_1 = require("typedi/Container");
const server_1 = require("../server");
const userManager_1 = require("../userManager");
const proto = __importStar(require("../../proto/lobby/proto"));
const mconf_1 = __importDefault(require("../../motiflib/mconf"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const json5_1 = __importDefault(require("json5"));
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const axios_1 = __importDefault(require("axios"));
const SURVEY_MAIL_ID = 92999000;
class Survey {
    static loadSurveryDefinitions() {
        const filename = path_1.default.resolve(path_1.default.join(__dirname, '..', '..', '..', 'config', 'survey.json5'));
        try {
            const json = fs_1.default.readFileSync(filename, 'utf8');
            this.surveyDefs = json5_1.default.parse(json);
        }
        catch (error) {
            mlog_1.default.error('Failed to load survey definitions!', {
                filename,
                errMsg: error.message,
            });
        }
    }
    static conditionalRequestSurvey(user) {
        if (!this.surveyDefs) {
            Survey.loadSurveryDefinitions();
        }
        if (!this.surveyDefs) {
            return;
        }
        mlog_1.default.info('.............conditionalRequestSurvey', {
            userId: user.userId,
            level: user.level,
            createTimeUtc: user.createTimeUtc,
            surveyDefs: this.surveyDefs,
        });
        const now = mutil.curTimeUtc();
        const signupTimeUtc = user.createTimeUtc;
        const daysSinceSignup = Math.floor((now - signupTimeUtc) / 86400);
        for (const survey of this.surveyDefs.surveys) {
            let isConditionMet = true;
            for (const condition of survey.conditions) {
                if (condition.level && user.level < condition.level) {
                    isConditionMet = false;
                    break;
                }
                if (condition.daysSinceSignup && daysSinceSignup < condition.daysSinceSignup) {
                    isConditionMet = false;
                    break;
                }
            }
            if (isConditionMet) {
                this.requestServey(survey, user);
                break;
            }
        }
    }
    static async requestServey(survey, user) {
        // TODO 이미 요청한 경우에는 하지 말아야함.
        const baseSurveyUrl = Survey.surveyDefs.baseSurveyUrl;
        const surveyId = survey.surveyId;
        const roleId = user.pubId;
        const gpId = user.accountId;
        const level = user.level;
        const areaGroup = mconf_1.default.sdo.areaId;
        const channel = user.channel;
        const surveyUrl = `${baseSurveyUrl}/${surveyId}?roleId=${roleId}&gpId=${gpId}&level=${level}&areaGroup=${areaGroup}&channel=${channel}`;
        await Survey.sendMail(SURVEY_MAIL_ID, survey.title, survey.body + '\n' + `<hyperlink color="#1717daff" action="${surveyUrl}">${Survey.surveyDefs.linkCaption}</>`, null, user);
    }
    static async completeSurvey(surveyId, user) {
        if (!this.surveyDefs) {
            return;
        }
        const survey = this.surveyDefs.surveys.find((survey) => survey.surveyId === surveyId);
        if (!survey) {
            return;
        }
        const baseJoinedUrl = Survey.surveyDefs.baseJoinedUrl;
        // const ptId = user.accountId;
        const roleId = user.pubId;
        const level = user.level;
        const areaGroup = mconf_1.default.sdo.areaId;
        const channel = user.channel;
        const key = '123';
        // const joinedUrl = `${baseJoinedUrl}/${surveyId}?roleId=${roleId}&ptId=${ptId}&level=${level}&areaGroup=${areaGroup}&channel=${channel}`;
        const joinedUrl = `${baseJoinedUrl}/${surveyId}?roleId=${roleId}&level=${level}&areaGroup=${areaGroup}&channel=${channel}`;
        const timestamp = Math.floor(mutil.getLocalSecs());
        const sign = Survey.md5(`key=${key}&surveyId=${surveyId}&roleId=${roleId}&timestamp=${timestamp}`);
        const joinedUrlWithSign = `${joinedUrl}&timestamp=${timestamp}&sign=${sign}`;
        const response = await axios_1.default.get(joinedUrlWithSign);
        const json = response.data;
        if (json.code !== 0) {
            mlog_1.default.error('Failed to complete survey!', {
                userId: user.userId,
                surveyId,
                code: json.code,
                message: json.message,
            });
            return;
        }
        else {
            const attachments = survey.rewards ? JSON.stringify(survey.rewards.map((reward) => ({
                Type: reward.type,
                Id: reward.cmsId,
                Quantity: reward.value,
            }))) : null;
            await Survey.sendMail(SURVEY_MAIL_ID, Survey.surveyDefs.rewardMail.title, Survey.surveyDefs.rewardMail.body, attachments, user);
            mlog_1.default.info('Survey completed!', {
                userId: user.userId,
                surveyId,
            });
        }
    }
    static md5(input) {
        const crypto = require('node:crypto');
        return crypto.createHash('md5').update(input).digest('hex');
    }
    static async sendMail(mailCmsId, title, body, attachments, user) {
        const now = mutil.curTimeUtc();
        const expireTimeUtc = null;
        const newMail = new mailBuilder_1.BuilderMailCreateParams(user.userMails.generateNewDirectMailId(), mailCmsId, now, expireTimeUtc, 0, title, null, body, null, attachments).getParam();
        const { userDbConnPoolMgr } = Container_1.Container.get(server_1.LobbyService);
        // TODO: 개발용 기능인것 같은데, 변경해야할까?
        await (0, tuDevAddDirectMail_1.default)(userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()), user.userId, newMail);
        user.userMails.addDirectMail(newMail, null);
        const userManager = Container_1.Container.get(userManager_1.UserManager);
        await userManager.sendJsonPacketToUser(user.userId, proto.Common.NEW_MAIL_SC, {
            sync: {
                add: {
                    userDirectMails: {
                        [newMail.id]: user.userMails.getDirectMailSyncData(newMail.id),
                    },
                },
            }
        });
    }
}
exports.Survey = Survey;
//# sourceMappingURL=survey.js.map