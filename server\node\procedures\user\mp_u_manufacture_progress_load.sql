CREATE PROCEDURE `mp_u_manufacture_progress_load`(
  IN inUserId INT
)
label_body:BEGIN
  SELECT
    roomCmsId,
    slot,
    recipeId,
    UNIX_TIMESTAMP(startTimeUtc) as startTimeUtc,
    UNIX_TIMESTAMP(completionTimeUtc) as completionTimeUtc,
    progressType,
    mateCmsIds,
    successRate,
    greatSuccessRate
  FROM
    u_manufacture_progress
  WHERE
    userId = inUserId
  ORDER BY roomCmsId, slot;
END 