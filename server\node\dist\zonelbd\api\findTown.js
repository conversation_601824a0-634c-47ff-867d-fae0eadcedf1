"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const typedi_1 = require("typedi");
const merror_1 = require("../../motiflib/merror");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const server_1 = require("../server");
const cmsEx = __importStar(require("../../cms/ex"));
module.exports = async (req, res) => {
    const { townCmsId, maxUsersPerChannel } = req.body;
    const townLbRedis = typedi_1.Container.get(server_1.ZonelbService).get(cmsEx.ZoneType.TOWN);
    return townLbRedis['findTown'](townCmsId, maxUsersPerChannel)
        .then((resp) => {
        res.json(resp);
    })
        .catch((err) => {
        mlog_1.default.warn('/townlbd/findTown error', { err: err.message });
        if (err instanceof merror_1.MError) {
            throw err;
        }
        else {
            throw new merror_1.MError(err.message, merror_1.MErrorCode.TOWN_ZONE_FIND_ERROR, err.message);
        }
    });
};
//# sourceMappingURL=findTown.js.map