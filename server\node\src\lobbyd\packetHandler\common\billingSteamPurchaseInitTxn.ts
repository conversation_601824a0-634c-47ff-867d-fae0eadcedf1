// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { MError, MErrorCode } from '../../../motiflib/merror';

import mhttp from '../../../motiflib/mhttp';
import { LGBillingCode } from '../../../motiflib/mhttp/linegamesBillingApiClient';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { C<PERSON><PERSON><PERSON>etHand<PERSON> } from '../index';

// ----------------------------------------------------------------------------
// 단순히 LG Billing Server API 로 이어줌.
// ----------------------------------------------------------------------------

interface RequestBody {
  orderId: number;
  steamLangauge: string;
  steamCurrency: string;
  steamItemInfos: {
    steamItemId: number;
    steamQty: number;
    steamAmount: number;
    steamDescription: string;
  }[];
}

interface ResponseBody {
  billingApiResp: unknown; // 빌링 서버 API 의 response
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingSteamPurchaseInitTxn implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    if (user.storeCode !== LGBillingCode.APP_STORE_CD.STEAM) {
      // user.storeCode 는 엄밀히 LGBillingCode 의 대역대(?)는 아니지만..
      throw new MError('not-in-steam', MErrorCode.INVALID_REQUEST, { storeCode: user.storeCode });
    }

    const reqBody: RequestBody = packet.bodyObj;
    const { orderId, steamLangauge, steamCurrency, steamItemInfos } = reqBody;

    if (orderId === undefined) {
      throw new MError(
        'orderId-expected',
        MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN,
        { reqBody }
      );
    }
    if (steamLangauge === undefined) {
      throw new MError(
        'steamLangauge-expected',
        MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN,
        { reqBody }
      );
    }
    if (steamCurrency === undefined) {
      throw new MError(
        'steamCurrency-expected',
        MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN,
        { reqBody }
      );
    }
    if (!Array.isArray(steamItemInfos)) {
      throw new MError(
        'steamItemInfos-array-expected',
        MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN,
        { reqBody }
      );
    }
    for (const elem of steamItemInfos) {
      if (
        !Number.isInteger(elem.steamItemId) ||
        !Number.isInteger(elem.steamQty) ||
        !Number.isInteger(elem.steamAmount)
      ) {
        throw new MError(
          'invalid-steamItemInfo',
          MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN,
          { reqBody }
        );
      }
      if (typeof elem.steamDescription !== 'string') {
        throw new MError(
          'steamItemInfos-steamDescription-string-expected',
          MErrorCode.INVALID_REQ_BODY_BILLING_STEAM_PURCHASE_INIT_TXN,
          { reqBody }
        );
      }
    }

    return Promise.resolve()
      .then(() => {
        return mhttp.lgbillingd.steamPurchaseInitTxn(
          orderId,
          user.steamUserId,
          user.steamAppId,
          steamLangauge,
          steamCurrency,
          steamItemInfos
        );
      })
      .then((billingApiResp) => {
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          billingApiResp,
        });
      });
  }
}
