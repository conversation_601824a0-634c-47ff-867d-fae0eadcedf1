// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import cms from '../../../cms';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../index';
import { Resp, Sync } from '../../type/sync';
import mlog from '../../../motiflib/mlog';
import { ManufactureProgress } from '../../userManufacture';
import { ManufactureRecipeDesc } from '../../../cms/manufactureRecipeDesc';
import { ManufactureExpLevelChange } from '../../../motiflib/model/lobby';
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import { RewardData } from '../../../motiflib/gameLog';
import tuManufactureDelete from '../../../mysqllib/txn/tuManufactureDelete';
import { LobbyService } from '../../server';

// ----------------------------------------------------------------------------
// 제조 완료 처리 (이후 보상 패킷 처리 별도)
// ----------------------------------------------------------------------------

const rsn = 'manufacture_end';
const add_rsn = null;

interface RequestBody {
  roomId: number;
  slot: number;
}

// ----------------------------------------------------------------------------
export class Cph_Manufacture_End implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;
    const { roomId, slot } = reqBody;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // [조건체크]
    /**
     *  제조실 진행 정보 확인
        슬롯 진행 정보 확인
        레시피 정보 확인
        제조 완료 시간 체크
     */
    // 제조실 진행 정보 확인
    const roomProgress: ManufactureProgress = user.userManufacture.getRoom(roomId);
    if (!roomProgress) {
      throw new MError(
        'not-in-progress-manufacture-room',
        MErrorCode.NOT_IN_PROGRESS_MANUFACTURE_ROOM,
        {
          roomId,
          slot,
        }
      );
    }

    // 슬롯 진행 정보 확인
    const slotData = roomProgress[slot];
    if (!slotData) {
      throw new MError(
        'not-in-progress-manufacture-slot',
        MErrorCode.NOT_IN_PROGRESS_MANUFACTURE_SLOT,
        {
          roomId,
          slot,
        }
      );
    }

    // 레시피 정보 확인
    const recipeCms: ManufactureRecipeDesc = cms.ManufactureRecipe[slotData.recipeId];
    if (!recipeCms) {
      throw new MError(
        'invalid-manufacture-recipe-cms-id',
        MErrorCode.INVALID_MANUFACTURE_RECIPE_CMS_ID,
        {
          roomId,
          slot,
          recipeId: slotData.recipeId,
        }
      );
    }

    // 제조 완료 시간 체크
    const curTimeUtc = mutil.curTimeUtc();
    if (curTimeUtc < slotData.completionTimeUtc) {
      throw new MError(
        'manufacture-not-over',
        MErrorCode.MANUFACTURE_NOT_FINISHED_YET,
        {
          roomId,
          slot,
          curTimeUtc,
          completionTimeUtc: slotData.completionTimeUtc,
        }
      );
    }

    // 생산 완료 처리 - Sync 정보만 세팅
    /**
     *  기존 생산 정보를 그대로 sync에 전달
        completionTimeUtc가 현재 시간보다 작거나 같으면 생산 완료 상태로 인식
        실제 보상 처리는 manufactureReceive.ts에서 담당
     */
    const sync: Sync = {
      add: {},
      remove: {},
    };

    return Promise.resolve()
      .then(() => {
        // 생산 완료 상태를 클라이언트에 알리기 위한 sync 정보
        // completionTimeUtc가 현재 시간보다 작거나 같으면 생산 완료 상태
        _.merge<Sync, Sync>(sync, {
          add: {
            manufacture: {
              roomInfo: {
                [roomId]: {
                  [slot]: {
                    slot,
                    recipeId: slotData.recipeId,
                    startTimeUtc: slotData.startTimeUtc,
                    completionTimeUtc: slotData.completionTimeUtc,
                    resultType: slotData.resultType,
                    mateCmsIds: slotData.mateCmsIds,
                    successRate: slotData.successRate,
                    greatSuccessRate: slotData.greatSuccessRate,
                    extra: slotData.extra,
                  },
                },
              },
            },
          },
        });
        return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
      });
  }
}
