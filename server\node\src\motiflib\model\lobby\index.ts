// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
export * from './enum';
import { Dictionary } from 'lodash';
import {
  BUILDING_TYPE,
  DEVELOPMENT_TYPE,
  JOB_TYPE,
  NATION_PROMISE_CONDITION_CMS_ID,
} from '../../../cms/ex';
import { RELIGION_TYPE } from '../../../cms/religionDesc';
import { Raid } from '../../../lobbyd/raidManager';
import {
  GUILD_RAID_STATE,
  RAID_HIT_TYPE,
  GUILD_RAID_RANKING_CATEGORY,
  RAID_STATE,
  RAID_RANKING_CATEGORY,
} from './enum';
import { NPC_INTERACTION_FUNCTION_TYPE } from '../../../cms/npcInteractionDesc';
import { CLASH_NOTICE_TYPE } from '../../../lobbyd/clash';
import { MANUFACTURE_TYPE } from '../../../cms/manufactureExpDesc';

export interface TownInfo {
  cmsId: number;
  buildingType: BUILDING_TYPE;
  religionType: RELIGION_TYPE;
  npcInteractionFunctionType: NPC_INTERACTION_FUNCTION_TYPE;
  channelId: string;
  url: string;
  queryInProgress: any;
}

export interface MateEquipmentNub {
  id: number;
  cmsId: number;
  equippedMateCmsId: number;
  dye1?: number;
  dye2?: number;
  dye3?: number;
  dye4?: number;
  dye5?: number;
  dye6?: number;
  isBound: number;
  isCostume: number;
  expireTimeUtc?: number;
  enchantLv: number;
}

export interface MatePassive {
  passiveCmsId: number;
  equipIndex: number;
}

export interface MatePassiveLearning {
  passiveCmsId: number;
  learnTimeUtc: number;
}

export interface MateNub {
  cmsId: number;
  loyalty: number | null;
  stateFlags: number;
  adventureExp: number;
  adventureLevel: number;
  tradeExp: number;
  tradeLevel: number;
  battleExp: number;
  battleLevel: number;
  adventureFame: number;
  tradeFame: number;
  battleFame: number;
  royalTitle: number;
  injuryExpireTimeUtc?: number;
  injuryImmuneTimeUtc?: number;
  lastTalkTimeUtc?: number;
  awakenLv: number;
  awakenTimeUtc: number | null; // 승급 중 인지 플래그 역할도 함.
  equipments: MateEquipmentNub[];
  passives: { [passiveCmsId: number]: MatePassive };
  passiveLearnings: { [passiveCmsId: number]: MatePassiveLearning };
  colorSkin?: number;
  colorEye?: number;
  colorHairAcc1?: number;
  colorHairAcc2?: number;
  colorHair?: number;
  colorBody1?: number;
  colorBody2?: number;
  colorFaceAcc1?: number;
  colorFaceAcc2?: number;
  colorFaceAcc3?: number;
  colorCape1?: number;
  colorCape2?: number;
  colorCape3?: number;
  breastSize?: number;
  isHideHat?: number;
  isHideCape?: number;
  isHideFace?: number;
  trainingGrade?: number;
  trainingExpiredTimeUtc?: number;
  trainingPoints?: { [type: number]: number };
  illusts?: { [cmsId: number]: boolean };
  equippedIllustCmsId?: number;
  isFavorite?: number;
  isTranscended?: number;
  transcendExpiredTimeUtc?: number;
}

// 대표 항해사용 장착 정보
export interface MateEquipmentView {
  id: number;
  cmsId: number;
  dye1: number;
  dye2: number;
  dye3: number;
  dye4: number;
  dye5: number;
  dye6: number;
  isCostume: number;
  expireTimeUtc?: number;
}

// 마을에서 보여지는 대표 항해사용 외형 정보
export interface MateView {
  cmsId?: number;
  equipments?: MateEquipmentView[];
  colorSkin?: number;
  colorEye?: number;
  colorHairAcc1?: number;
  colorHairAcc2?: number;
  colorHair?: number;
  colorBody1?: number;
  colorBody2?: number;
  colorFaceAcc1?: number;
  colorFaceAcc2?: number;
  colorFaceAcc3?: number;
  colorCape1?: number;
  colorCape2?: number;
  colorCape3?: number;
  breastSize?: number;
  isHideHat?: number;
  isHideCape?: number;
  isHideFace?: number;
  equippedIllustCmsId?: number;
}

export interface PetView {
  cmsId?: number;
}

export interface WorldBuffActiveEffectCompany {}
export interface WorldBuffActiveEffectFleet {}
export interface WorldBuffActiveEffectShip {
  durability?: number;
  sailor?: number;
  cargos?: { [cargoCmsId: number]: number };
}
export interface WorldBuffActiveEffectMate {
  loyalty: number;
  stateFlags: number;
}

export interface WorldBuffActiveEffectDesc {
  buffCmsId?: number;
  waeCmsId?: number;
}

export interface WorldBuffActiveEffect {
  company?: WorldBuffActiveEffectCompany;
  fleets?: { [fleetIndex: number]: WorldBuffActiveEffectFleet };
  ships?: { [shipId: number]: WorldBuffActiveEffectShip };
  mates?: { [mateCmsId: number]: WorldBuffActiveEffectMate };
  desc?: WorldBuffActiveEffectDesc;
}

export interface NationIntimacyEdge {
  smallerNationCmsId: number;
  largerNationCmsId: number;
  intimacyValue: number;
}

// for town_invested
export interface TownInvestedPubMsg {
  townCmsId: number;
  developmentType: DEVELOPMENT_TYPE;
  oldDevelopmentLevel: number;
  sessionId: number;
  updateTimeUtc: number;
  bUsersInGover?: boolean;
}

// for town_mayor_tax_changed
export interface TownMayorTaxChangedPubMsgElem {
  nationCmsId: number;
  tax: number;
}
export interface TownMayorTaxChangedPubMsg {
  changes: { [townCmsId: number]: TownMayorTaxChangedPubMsgElem[] };
  curTimeUtc: number;
}
export interface TownMayorTaxChangedPubMsgForTownd {
  townCmsId: number;
  changes: TownMayorTaxChangedPubMsgElem[];
}

// for dev_town_nation_share_point_changed
export interface DevTownNationSharePointChangedPubMsg {
  townCmsId: number;
  nationCmsId: number;
  nationSharePoint: number;
  updateTimeUtc: number;
}

// for town_mayor_changed
export interface TownMayorChangedPubMsg {
  townCmsId: number;
  mayorUserId: number;
  mayorUserName: string;
  mayorNationCmsId: number;
  updateTimeUtc: number;
}

export interface CrazeEventBudgetPubMsg {
  townCmsId: number;
  budget?: number;
  usedBudget?: number;
}

export interface UnpopularPubMsg {
  townCmsId: number;
  createSessionId?: number;
  totalQuantity?: number;
  usedQuantity?: number;
}

export interface ClashOpponentFoundNoticeMsg {
  userId: number;
  expiredTimeUtc: number;
}

export interface ClashBattleDecisionNoticeMsg {
  userId: number;
  opponentUserId: number;
  noticeType: CLASH_NOTICE_TYPE;
}

// ----------------------------------------------------------------------------
// 오프라인 항해 중 전투 보상 정보.
// ----------------------------------------------------------------------------
export interface OfflineBattleHistories {
  addedUserExp?: number;
  addedDucat?: number;
  fameGain?: number;

  mates?: {
    mateCmsId: number;
    addedExp: number;
  }[];

  rewards?: {
    type: number;
    cmsId: number;
    quantity: number;
  }[];

  battleEndTimeUtc?: number; // 전투종료시간
}

// ----------------------------------------------------------------------------
// 월드 버프 정보.
// ----------------------------------------------------------------------------
export interface WorldBuffSmall {
  cmsId: number; // WorldBuff.json ID
  stack: number;
}

export interface GuildData {
  // 길드 정보
  guild: GuildNub;

  // 소속 길드원 / 가입 신청자 목록
  members: { [userId: number]: GuildMemberNub };
  applicants: { [userId: number]: GuildApplicantNub };

  // 소속 길드원의 일일/주간/누적 길드포인트
  dailyGuildPoints: { [userId: number]: GuildPointNub };
  weeklyGuildPoints: { [userId: number]: GuildPointNub };
  accumGuildPoints: { [userId: number]: GuildPointNub };

  // 소속 길드원의 수령받은 일일 보상 idx
  pickedDailyRewardIdxs: { [userId: number]: number[] };

  // 주간 랭킹 보상 대상자.
  weeklyRewardUsers: {
    [userId: number]: {
      ranking: number; //주간 기여도 순위
    };
  };

  // 길드레벨업을 확인한 길드원 목록.
  guildUpgradePopupCheckedUsers: { [userId: number]: boolean };

  // 상회 자원(기부금으로 모인 수량)
  resources: {
    [guildResourceCmsId: number]: number;
  };

  // 기부 정보 (주간, 누적)
  weeklyGuildDonations: { [userId: number]: number };
  accumGuildDonations: { [userId: number]: number };

  // 마지막 상회 토벌 오픈 날짜. 이 시간으로 다음 상회토벌이 오픈가능한지 확인.
  lastOpenTimeRaids: { [guildRaidCmsId: number]: number };

  // 상회 토벌 클리어 난이도 정보
  clearDifficultyRaids: { [groupNo: number]: number };

  // 습득한 상회 버프 목록
  learnedGuildBuffCmsIds: {
    [guildBuffCmsId: number]: boolean;
  };

  // 버프 아이템 등록 정보
  registeredGuildBuffItems: {
    [guildBuffCmsId: number]: {
      [itemId: number]: {
        amount: number; //
      };
    };
  };

  // 상회토벌 버프.(회차가 지나면 삭제된다.)
  bossRaidBuffs: {
    [guildBossRaidCmsId: number]: {
      bossRaidBuffCmsId: number;
      level: number;
      userId: number; // 버프 구매자}
    }[];
  };
}

// 길드 정보
export interface GuildNub {
  createdDateTimeUtc: number; // 창단일(utc)

  guildName: string; // 길드명
  introduction: string | null; // 길드 소개글
  masterNotice: string | null; // 길드장 공지글

  // 자동알림 정보
  autoNotificationType: number | null; // 자동알림 <타입으로 분류>
  autoNotificationParam1: string | null; // 자동알림 파라미터1
  autoNotificationParam2: string | null; // 자동알림 파라미터2
  autoNotificationRegTimeUtc: number;

  dormantMasterCheckTimeUtc: number; // 휴면 상회장 검사날짜

  // 가입 정보
  joinType: number; // 가입타입 (1:즉시가입 2:신청가입) GUILD_JOIN_TYPE 참고
  joinCondition: number; // 가입 조건 타입 (0:없음, 1:전투력, 2:선단레벨) GUILD_JOIN_CONDITION 참고
  joinConditionValue: number; // 가입조건 값
  nationCondition: number; // 국가 조건 (0:없음 1:동일국가)
  nationCmsId: number; // 길드국가

  // 길드 레벨 정보
  grade: number; // 등급
  exp: number; // 경험치

  // 문양 정보
  emblemImageCmsId: number; // 문양id
  emblemColorCmsId: number; // 문양색상
  emblemBorderCmsId: number; // 테투리

  // 구성원 등급 별칭
  grade_alias_1: string | null;
  grade_alias_2: string | null;
  grade_alias_3: string | null;
  grade_alias_4: string | null;

  dailyGuildPointResetTimeUtc: number;
  weeklyGuildPointResetTimeUtc: number;

  // 선택된 버프 카테고리
  selectedBuffCategory: JOB_TYPE;
}

// 길드원 정보
export interface GuildMemberNub {
  userId: number;
  grade: number;
  joinDate: number;
  guildPointUpdateTimeUtc: number; // 마지막으로 기여도 갱신 시간 (지난주 주간랭킹 집계 시 동점일 경우 시간순으로 랭킹산정)
  donations: {
    [guildDonationCmsId: number]: {
      count: number; // 기부 횟수
      buyCount: number; // 추가 기부 기회 구매 횟수
      lastDonationTime: number; // 마지막 기부 날짜.
    };
  };

  // 상회 아이템 기부 점수
  registeredItemScore: number;
}

// 길드 가입 신청자 정보
export interface GuildApplicantNub {
  userId: number;
  regTimeUtc: number;
}

export interface GuildPointNub {
  [category: number]: number; // GUILD_POINT_CATEGORY : value
}

export interface GuildAppearance {
  guildName: string;
  grade: number; // 길드원 등급
  emblemImageCmsId: number;
  emblemColorCmsId: number;
  emblemBorderCmsId: number;
}

export interface NationCabinetAppearance {
  cabinetCmsId: number;
}

export interface NationShareRatePerRegion {
  [regionCmsId: number]: {
    [nationCmsId: number]: number; //  nation : share(점유율 1000%단위)
  };
}

export interface GuildCraftProgress {
  cmsId: number; // guildCraft CMS id
  slot: number;
  startTimeUtc: number;
  completionTimeUtc: number;
}

export interface GuildSynthesisProgress {
  cmsId: number; // guildCraft CMS id
  slot: number;
  startTimeUtc: number;
  completionTimeUtc: number;
}

// 레이드 정보 요청시 전달할 정보 목록
// 수정 시 클라이언트 RaidDefine.lua에도 수정할 것.
export interface RaidInfo {
  // BossRaidCms id
  raidCmsId: number;

  // 레이드 상태(진행중 or 성공 or 실패)
  state: RAID_STATE;

  accumDamage: number;
  // // 전체 HP
  // maxHp: number;

  // // 남은 HP
  // curHp: number;

  // // 스폰시점의 HP
  // openingHp: number;

  // 레이트 상태 변경 까지 남은 시간
  leftTime: number;

  // 해당 유저가 전투당 가한 데미지
  damagePerBattle: number[];

  // 막타 유저 정보( 토벌성공시에만 전달)
  // finisher?: {
  //   userId: number;
  //   name: string;
  //   nation: number;
  // };

  // 해당 레이드의 랭킹 정보
  rankings: RaidRanking[];

  // 보상 CMS 아이디
  rewards: { [category: number]: { rewardCmsId: number; mailCmsId: number }[] };

  // 도전가능 여부
  // challengeable: boolean;

  // 202403xx 패치 이후 모든 세션 중 최고 데미지.
  allTimeHighDamage: number;

  pickedRaidBosses: { bossCmsId: number; state: RAID_REWARD_STATE }[];

  accum1stNationCmsId: number;
}

export enum RAID_REWARD_STATE {
  NONE = 0,
  REWARDABLE = 1,
  RECEIVED = 2,
}

export interface RaidRanking {
  // 누적데미지, 단일 최고데미지 등 분류
  category?: GUILD_RAID_RANKING_CATEGORY | RAID_RANKING_CATEGORY;

  // 순수 순위로 표기할지 %로 표기할지 여부.
  bPercent: boolean;

  // 나의 랭킹 (rankingDisplayPctRank 밖의 등수는 퍼센트로 표시)
  myRanking: number;

  // 나의 스코어
  myScore: number;

  // TOP랭커
  topRankers: RaidRanker[];

  // 보스에 참여한 전체 인원
  numOfRankers?: number;
}

export interface RaidRankingEx extends RaidRanking {
  rewardStamp: number;
  accum1stNationCmsId: number;
}

export interface RaidRanker {
  ranking: number;
  userId?: number;
  name?: string;
  mateCmsId?: number;
  nation: number;
  score: number;
}

export interface RaidProgressNub {
  sessionId: number; // 레이드 회차
  stateSeq: number; // 상태가 변경된 번호
  stateBeginAt: number; // 상태변경 시간
  stateEndAt: number; // 상태가 종료될 시간
  state: RAID_STATE; // 진행 중, 정산, 보상
}

export interface RaidNub {
  accumDamage: number; // 모든 유저한테 받은 누적 데미지.
}

export interface RaidBattleResult {
  actualDamage: number;
  accumulatedDamage: number; // 유저의 보스에게 입힌 누적피해량
  oldRanking: number; // 이전랭킹
  newRanking: number; // 현재랭킹
  rankingPct: number; // 랭킹 백분율
  leftTimeSec: number; // 남은시간(초)

  bossAccumulatedDamage?: number; // 현재 세션에서 보스가 받은 데미지 총합. (일반 토벌용)

  maxHp?: number; // 최대HP (길드 토벌용)
  curHp?: number; // 남은HP (길드 토벌용)
  hitResult?: RAID_HIT_TYPE; // 길드 토벌용

  // glog용
  guild_data?: {
    nid: string;
    name: string;
    emblemBorder;
    emblemColor: number;
    emblemImage;
    accept_type: number;
    pl_limit: number;
    power_limit: number;
    nation: string;
    lv: number;
    members: number;
    guild_id: number;
  };
}

export interface GuildRaidProgressNub {
  guildId: number; // guild Id
  cmsId: number; // BossRaid CMS id
  stateSeq: number; // 상태가 변경된 번호
  stateBeginAt: number; // 상태변경 시간
  stateEndAt: number; // 상태가 종료될 시간
  state: GUILD_RAID_STATE; // 진행중, 성공, 종료
  maxHp: number; // 최대HP
  curHp: number; // 남은HP
}

// 상회토벌  정보 요청시 전달할 정보 목록
// 수정 시 클라이언트 RaidDefine.lua에도 수정할 것.
export interface GuildRaidInfo {
  // GuildBossRaidCms id
  guildBossRaidCmsId: number;

  // 레이드 상태(진행중 or 성공 or 실패)
  state: GUILD_RAID_STATE;

  // 전체 HP
  maxHp: number;

  // 남은 HP
  curHp: number;

  // 레이트 상태 변경 까지 남은 시간
  leftTime: number;

  // 해당 유저가 전투당 가한 데미지
  damagePerBattle: number[];

  // 해당 레이드의 랭킹 정보
  rankings: RaidRanking[];

  // 보상가능 여부
  rewardable: boolean | undefined;

  // 보상 CMS 아이디
  rewardCmsIds: number[];
}

export interface ManufactureExpLevelChange {
  type: MANUFACTURE_TYPE;
  exp: number;
  level: number;
  oldLevel: number;
}

// ----------------------------------------------------------------------------
// pub-sub messages
// ----------------------------------------------------------------------------
export interface NationElectionRegisterCandidatePubMsg {
  nationCmsId: number;
  electionSessionId: number;
  candidateUserId: number;
  updateTimeUtc: number;
}

export interface NationElectionCandidatesModifiedPubMsg {
  nationCmsId: number;
  electionSessionId: number;
  candidateUserIds: number[];
  updateTimeUtc: number;
}

export interface NationElectionVotedToCandidatePubMsg {
  nationCmsId: number;
  electionSessionId: number;
  candidateUserId: number;
  updateTimeUtc: number;
}

export interface NationElectionVotesUpdatedPubMsg {
  nationCmsId: number;
  electionSessionId: number;
  candidateUserId: number;
  updateTimeUtc: number;
}

export interface NationElectionRemovedCandidatesPubMsg {
  nationCmsId: number;
  electionSessionId: number;
  candidateUserIds: number[];
  updateTimeUtc: number;
}

export interface NationCabinetMemberAppointedPubMsg {
  nationCmsId: number;
  sessionId: number;
  memberUserId: number;
  updateTimeUtc: number;
}

export interface NationCabinetMemberWageRateChangedPubMsg {
  nationCmsId: number;
  sessionId: number;
  memberUserId: number;
  updateTimeUtc: number;
}

export interface NationCabinetMembersRemovedPubMsg {
  nationCmsId: number;
  sessionId: number;
  memberUserIds: number[];
  updateTimeUtc: number;
}

export interface NationNoticeUpdatedPubMsg {
  nationCmsId: number;
  sessionId: number;
  noticeStr: string;
  updateTimeUtc: number;
}

export interface VillageEventUpdatedPubMsg {}

export interface VillageStorageUpdatedPubMsg {
  curSessionId: number;
  nextSessionId: number;
}
export interface NationGoalPromiseChangedPubMsg {
  nationCmsId: number;
  cabinetSessionId: number;
  updateTimeUtc: number;
}

export interface NationPromiseConditionChangedParam {
  conditionCmsId: NATION_PROMISE_CONDITION_CMS_ID;
  target: number; // 체크할 이벤트가 발생한 대상정보
}

export interface NationGoalPromiseChangedPubConsumeMsg {
  nationCmsId: number;
  cabinetSessionId: number;
  updateTimeUtc: number;
  changedParams: NationPromiseConditionChangedParam[];
}

export interface NationBudgetUpdatedPubMsgEntry {
  nationCmsId: number;
  budget: number;
}
export interface NationBudgetUpdatedPubMsg {
  nations: NationBudgetUpdatedPubMsgEntry[];
  updateTimeUtc: number;
}

export interface NationPoliciesUpdatedPubMsgEntry {
  nationCmsId: number;
  budget?: number;
  cabinetSessionId: number;
  policies: { group: number; step: number }[];
}

export interface NationPoliciesUpdatedPubMsg {
  nations: NationPoliciesUpdatedPubMsgEntry[];
  updateTimeUtc: number;
}

export interface NationPowerUpdatedPubConsumeMsg {
  powers: { [nationCmsId: string]: number };
  updateTimeUtc: number;
  nationTowns: Dictionary<string[]>;
}

export interface NationSupportShopPurchasedPubMsg {
  nationCmsId: number;
  cabinetSessionId: number;
  updateTimeUtc: number;
}

// (치트용)모든 직책 및 임명 쿨타임 정보 리로드 요청
export interface DevNationCabinetMembersAllReloadPubMsg {
  nationCmsId: number;
  sessionId: number;
  updateTimeUtc: number;
}

export interface NationCabinetMemberThoughtChangedPubMsg {
  nationCmsId: number;
  sessionId: number;
  memberUserId: number;
  updateTimeUtc: number;
}

export interface BoughtWebShopProductPubMsg {
  userId: number;
  orderId: string;
  cashShopCmsId: number;
}
