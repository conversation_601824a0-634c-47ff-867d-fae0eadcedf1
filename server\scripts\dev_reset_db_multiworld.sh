#!/bin/bash


pushd ../node

# 1번 월드 초기화 
yarn db-drop uwo_user_00
yarn db-create uwo_user_00
yarn db-drop uwo_user_01
yarn db-create uwo_user_01

yarn db-drop uwo_auth
yarn db-create uwo_auth
yarn db-drop uwo_world
yarn db-create uwo_world

# 2번 월드 초기화 
yarn db-drop uwo_user_02
yarn db-create uwo_user_02
yarn db-drop uwo_user_03
yarn db-create uwo_user_03
yarn db-drop uwo_world2
yarn db-create uwo_world2

# 3번 월드 초기화 
yarn db-drop uwo_user_04
yarn db-create uwo_user_04
yarn db-drop uwo_user_05
yarn db-create uwo_user_05
yarn db-drop uwo_world3
yarn db-create uwo_world3

yarn mig
yarn mig-world-1
yarn mig-world-2
popd

for i in {1..100}
do
  redis-cli -n $i flushdb
done
