import * as fs from 'fs-extra';
import * as path from 'path';
import { RemapRecord } from '../analyzer/remapDataSummaryAnalyzer';
import { WorldShardMapping, AnalysisResult, RedisKeyInfo } from '../types';
import { DatabaseConfigReader } from '../utils/databaseConfigReader';
import { getAllRedisKeyPatterns } from '../config/redisKeyPatterns';

export class RedisScriptGenerator {
  private databaseConfigReader: DatabaseConfigReader;

  constructor() {
    this.databaseConfigReader = new DatabaseConfigReader();
  }

  /**
   * Redis 스크립트 및 Lua 파일 생성
   */
  async generateRedisScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult,
    targetWorldId?: string
  ): Promise<void> {
    console.log('🔴 Redis 스크립트 생성 시작...');

    // 월드별로 레코드 그룹화
    const worldGroups = new Map<string, RemapRecord[]>();
    records.forEach(record => {
      if (!worldGroups.has(record.gameServerId)) {
        worldGroups.set(record.gameServerId, []);
      }
      worldGroups.get(record.gameServerId)!.push(record);
    });

    // 타겟 월드가 지정된 경우 필터링
    let filteredWorldGroups = worldGroups;
    if (targetWorldId) {
      filteredWorldGroups = new Map();
      if (worldGroups.has(targetWorldId)) {
        filteredWorldGroups.set(targetWorldId, worldGroups.get(targetWorldId)!);
      }
      console.log(`🎯 타겟 월드로 필터링: ${targetWorldId}`);
    }

    // 전역 Redis 스크립트 생성 (userCacheRedis)
    await this.generateGlobalRedisScript(records, outputDir, codebaseAnalysisResult);

    // 월드별 Redis 스크립트 생성 (월드별로 적용 대상이 있을 경우에만)
    for (const [worldId, worldRecords] of filteredWorldGroups) {
      await this.generateWorldRedisScript(worldId, worldRecords, outputDir, codebaseAnalysisResult);
    }

    // 마스터 Redis 업데이트 스크립트 생성
    await this.generateMasterRedisScript(filteredWorldGroups, outputDir);

    console.log('✅ Redis 스크립트 생성 완료');
  }

  /**
   * 전역 Redis 스크립트만 생성
   */
  async generateGlobalRedisScriptOnly(
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('🔴 전역 Redis 스크립트 생성 시작...');
    await this.generateGlobalRedisScript(records, outputDir, codebaseAnalysisResult);
    console.log('✅ 전역 Redis 스크립트 생성 완료');
  }

  /**
   * 특정 월드의 Redis 스크립트만 생성
   */
  async generateWorldRedisScriptOnly(
    worldId: string,
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    await this.generateWorldRedisScript(worldId, records, outputDir, codebaseAnalysisResult);
  }

  /**
   * 전역 Redis 스크립트 생성 (userCacheRedis)
   */
  private async generateGlobalRedisScript(
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    // userCacheRedis는 전역으로 사용되므로 모든 레코드를 대상으로 함
    const globalRedisData = this.analyzeGlobalRedisData(records, codebaseAnalysisResult);

    if (globalRedisData.length === 0) {
      console.log('⚠️ 전역 Redis 업데이트 대상이 없습니다.');
      return;
    }

    // 데이터베이스 설정 읽기
    await this.databaseConfigReader.loadConfig();

    // userCacheRedis는 전역 설정 (sharedConfig)에서 가져오기
    const userCacheRedisConfig = this.databaseConfigReader.getRedisConfig('userCacheRedis');

    if (!userCacheRedisConfig) {
      throw new Error('❌ sharedConfig.userCacheRedis 설정을 찾을 수 없습니다.');
    }

    // Redis 인스턴스별로 분리된 Lua 스크립트 생성
    const luaDir = path.join(outputDir, 'lua');
    await fs.ensureDir(luaDir);

    await this.generateSeparatedRedisScripts(globalRedisData, luaDir, outputDir, userCacheRedisConfig);
  }

  /**
   * 월드별 Redis 스크립트 생성
   */
  private async generateWorldRedisScript(
    worldId: string,
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    // 월드별 Redis 데이터 분석
    const worldRedisData = this.analyzeWorldRedisData(records, codebaseAnalysisResult);
    console.log(`🔍 ${worldId} 월드 Redis 데이터 분석 결과: ${worldRedisData.length}개`);

    if (worldRedisData.length === 0) {
      console.log(`⚠️ ${worldId} 월드의 Redis 업데이트 대상이 없습니다.`);
      return;
    }

    // 데이터베이스 설정 읽기
    await this.databaseConfigReader.loadConfig();
    const worldConfig = this.databaseConfigReader.getWorldDatabaseConfig(worldId);

    if (!worldConfig) {
      console.log(`⚠️ ${worldId} 월드의 데이터베이스 설정을 찾을 수 없습니다.`);
      return;
    }

    // 월드별 lua 폴더 생성: lua/{worldId}/
    const worldLuaDir = path.join(outputDir, 'lua', worldId.toLowerCase());
    await fs.ensureDir(worldLuaDir);

    // 월드별 Lua 스크립트 생성
    await this.generateWorldLuaScript(worldId, worldRedisData, worldLuaDir);

    // 월드별 Redis Shell 스크립트 생성
    await this.generateWorldRedisShellScript(worldId, outputDir, worldConfig);
  }

  /**
   * 전역 Redis 데이터 분석
   */
  private analyzeGlobalRedisData(
    records: RemapRecord[],
    codebaseAnalysisResult: AnalysisResult
  ): any[] {
    // 전역 Redis에서 업데이트해야 할 데이터 분석
    // 분석된 Redis 키 패턴에서 전역 데이터 찾기 (userCache, auth, order 인스턴스)
    const globalRedisInstances = ['userCache', 'auth', 'order'];
    const globalRedisKeys = codebaseAnalysisResult.redisKeys?.filter((key: any) =>
      globalRedisInstances.includes(key.redisInstance)
    ) || [];

    // 전역 Redis (userCacheRedis)는 분석된 키가 없어도 기본적인 사용자 캐시 업데이트 수행
    if (globalRedisKeys.length === 0) {
      console.log('⚠️ 분석된 전역 Redis 키가 없지만 기본 사용자 캐시 업데이트를 수행합니다.');
    } else {
      console.log(`✅ 분석된 전역 Redis 키 ${globalRedisKeys.length}개를 사용합니다.`);
      globalRedisKeys.forEach((key: any) => {
        console.log(`  - ${key.keyPattern} (${key.redisInstance}, ${key.keyType})`);
      });
    }

    // GNID/NID 매핑 생성
    const gnidMap = new Map<string, string>();
    const nidMap = new Map<string, string>();
    records.forEach(record => {
      gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
      nidMap.set(record.uwo_Nid, record.uwogl_Nid);
    });

    return [{
      type: 'global',
      gnidMap,
      nidMap,
      keys: globalRedisKeys
    }];
  }

  /**
   * 월드별 Redis 데이터 분석
   */
  private analyzeWorldRedisData(
    records: RemapRecord[],
    codebaseAnalysisResult: AnalysisResult
  ): any[] {
    // 월드별 Redis에서 업데이트해야 할 데이터 분석 (월드별 Redis 인스턴스들)
    // 전역 Redis 인스턴스들: userCache, auth, order
    const globalRedisInstances = ['userCache', 'auth', 'order'];
    const worldRedisKeys = codebaseAnalysisResult.redisKeys?.filter((key: any) =>
      !globalRedisInstances.includes(key.redisInstance)
    ) || [];

    // 분석된 Redis 키가 없으면 빈 배열 반환 (불필요한 스크립트 생성 방지)
    if (worldRedisKeys.length === 0) {
      console.log('⚠️ 분석된 월드별 Redis 키가 없어 스크립트를 생성하지 않습니다.');
      return [];
    }

    // GNID/NID 매핑 생성
    const gnidMap = new Map<string, string>();
    const nidMap = new Map<string, string>();
    records.forEach(record => {
      gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
      nidMap.set(record.uwo_Nid, record.uwogl_Nid);
    });

    return [{
      type: 'world',
      gnidMap,
      nidMap,
      keys: worldRedisKeys
    }];
  }

  /**
   * 전역 Lua 스크립트 생성
   */
  private async generateGlobalLuaScript(
    globalRedisData: any[],
    luaDir: string
  ): Promise<void> {
    const luaScript = this.generateGlobalLuaContent(globalRedisData);
    const luaFilePath = path.join(luaDir, 'update_global_cache.lua');
    await fs.writeFile(luaFilePath, luaScript, 'utf8');
    console.log(`📝 전역 Lua 스크립트 생성: ${luaFilePath}`);
  }

  /**
   * 월드별 Lua 스크립트 생성
   */
  private async generateWorldLuaScript(
    worldId: string,
    worldRedisData: any[],
    worldLuaDir: string
  ): Promise<void> {
    const luaScript = this.generateWorldLuaContent(worldId, worldRedisData);
    const luaFilePath = path.join(worldLuaDir, `update_${worldId.toLowerCase()}_cache.lua`);
    await fs.writeFile(luaFilePath, luaScript, 'utf8');
    console.log(`📝 ${worldId} 월드 Lua 스크립트 생성: ${luaFilePath}`);
  }

  /**
   * 전역 Lua 스크립트 내용 생성
   */
  private generateGlobalLuaContent(globalRedisData: any[]): string {
    let lua = `-- =====================================================
-- UWO GNID/NID 리맵핑 전역 Redis Lua 스크립트
-- 대상: userCacheRedis (전역)
-- 생성일시: ${new Date().toISOString()}
-- =====================================================
-- 주의: 데이터베이스는 redis-cli의 -n 옵션으로 지정됩니다.

local updated_count = 0
local error_count = 0

`;

    // Redis 인스턴스별로 그룹화
    const redisInstanceGroups = new Map<string, any[]>();

    globalRedisData.forEach(data => {
      if (data.type === 'global') {
        const redisKeys = data.keys || [];

        // Redis 인스턴스별로 키들을 그룹화
        redisKeys.forEach((keyInfo: any) => {
          const instance = keyInfo.redisInstance;
          if (!redisInstanceGroups.has(instance)) {
            redisInstanceGroups.set(instance, []);
          }
          redisInstanceGroups.get(instance)!.push({
            keyInfo,
            gnidMap: data.gnidMap,
            nidMap: data.nidMap
          });
        });

        // 분석된 키가 없는 경우 모든 전역 Redis 인스턴스에 대해 기본 스크립트 생성
        if (redisKeys.length === 0) {
          const globalInstances = ['userCache', 'auth', 'order'];
          globalInstances.forEach(instance => {
            redisInstanceGroups.set(instance, [{
              keyInfo: null,
              gnidMap: data.gnidMap,
              nidMap: data.nidMap
            }]);
          });
        }
      }
    });

    // 각 Redis 인스턴스별로 스크립트 생성 안내
    lua += `-- 이 스크립트는 여러 Redis 인스턴스를 처리합니다.\n`;
    lua += `-- 실제로는 인스턴스별로 분리된 스크립트를 사용해야 합니다.\n\n`;

    // 임시로 모든 인스턴스의 스크립트를 하나에 포함 (실제로는 분리해야 함)
    redisInstanceGroups.forEach((keyGroups, instance) => {
      lua += `-- ${instance} Redis 인스턴스 업데이트\n`;
      keyGroups.forEach(group => {
        if (group.keyInfo) {
          lua += this.generateKeyPatternUpdateScript(group.keyInfo, group.gnidMap, group.nidMap);
        } else {
          lua += this.generateDefaultGlobalUpdateScript(group.gnidMap, group.nidMap, instance);
        }
      });
      lua += '\n';
    });

    lua += `-- 결과 반환
return {updated_count, error_count}
`;

    return lua;
  }

  /**
   * 키 패턴에 따른 업데이트 스크립트 생성 (리맵 테이블 + 루프 방식)
   */
  private generateKeyPatternUpdateScript(keyInfo: any, gnidMap: Map<string, string>, nidMap: Map<string, string>): string {
    let script = '';
    const keyPattern = keyInfo.keyPattern;
    const keyType = keyInfo.keyType;

    // 키 패턴 분석
    if (this.hasMultiplePlaceholders(keyPattern)) {
      // 다중 플레이스홀더 패턴 - SCAN을 사용한 패턴 매칭
      script += `-- 다중 플레이스홀더 키 패턴 업데이트: ${keyPattern}\n`;
      script += this.generateMultiPlaceholderUpdateScript(keyPattern, gnidMap, nidMap);
    } else if (keyPattern === 'prologueGnids:{worldId}') {
      // 특수 패턴: prologueGnids:{worldId} - GNID를 멤버로 사용하는 ZSET
      script += `-- 특수 패턴 Sorted Set 키 업데이트: ${keyPattern}\n`;
      script += this.generateZSetUpdateScript(keyPattern, gnidMap);
    } else if (keyPattern === 'deletionPubIds') {
      // 특수 패턴: deletionPubIds - NID를 값으로 사용하는 List
      script += `-- 특수 패턴 List 키 업데이트: ${keyPattern}\n`;
      script += this.generateListUpdateScript(keyPattern, nidMap);
    } else if (keyPattern.includes('{nid}')) {
      // NID 기반 키 패턴 - RENAME 사용 (리맵 테이블 방식)
      script += `-- NID 키 패턴 업데이트: ${keyPattern}\n`;
      script += this.generateRenameScript(keyPattern, '{nid}', nidMap);
    } else if (keyPattern.includes('{gnid}') || keyPattern.includes('{accountId}')) {
      // GNID/AccountId 기반 키 패턴 - RENAME 사용 (리맵 테이블 방식)
      script += `-- GNID 키 패턴 업데이트: ${keyPattern}\n`;
      const placeholder = keyPattern.includes('{gnid}') ? '{gnid}' : '{accountId}';
      script += this.generateRenameScript(keyPattern, placeholder, gnidMap);
    } else if (keyPattern.includes('{userId}')) {
      // UserId 기반 키 패턴 - Hash 필드값 변경 (리맵 테이블 + SCAN 방식)
      script += `-- UserId Hash 필드 업데이트: ${keyPattern}\n`;
      // 키 패턴 정보에서 hashFields 추출
      const keyInfo = getAllRedisKeyPatterns().find((info: RedisKeyInfo) => info.keyPattern === keyPattern);
      const hashFields = keyInfo?.hashFields;
      script += this.generateHashFieldUpdateScript(keyPattern, nidMap, hashFields);
    } else if (keyType === 'list') {
      // List 타입 키 패턴 - 리스트 내 값 업데이트
      script += `-- List 키 업데이트: ${keyPattern}\n`;
      script += this.generateListUpdateScript(keyPattern, nidMap);
    } else if (keyType === 'zset') {
      // Sorted Set 타입 키 패턴 - 멤버 업데이트
      script += `-- Sorted Set 키 업데이트: ${keyPattern}\n`;
      if (keyPattern.includes('{gnid}') || keyPattern.includes('{accountId}')) {
        script += this.generateZSetUpdateScript(keyPattern, gnidMap);
      } else if (keyPattern.includes('{nid}') || keyPattern.includes('{pubId}')) {
        script += this.generateZSetUpdateScript(keyPattern, nidMap);
      }
    }

    script += '\n';
    return script;
  }

  /**
   * 기본 전역 업데이트 스크립트 (분석된 키가 없는 경우, 리맵 테이블 방식)
   */
  private generateDefaultGlobalUpdateScript(gnidMap: Map<string, string>, nidMap: Map<string, string>, instance: string): string {
    let script = `-- 기본 ${instance} Redis 키 패턴 업데이트 (분석된 키 패턴 없음)\n`;
    script += `-- GNID Map size: ${gnidMap.size}, NID Map size: ${nidMap.size}\n`;

    // 인스턴스별로 적절한 패턴만 처리
    if (instance === 'userCache') {
      // userCache 인스턴스 패턴들
      if (gnidMap.size > 0) {
        // account:{accountId} 패턴
        script += this.generateRenameScript('account:{accountId}', '{accountId}', gnidMap);
      }

      if (nidMap.size > 0) {
        // user:{userId} Hash 필드
        const keyInfo = getAllRedisKeyPatterns().find((info: RedisKeyInfo) => info.keyPattern === 'user:{userId}');
        const hashFields = keyInfo?.hashFields;
        script += this.generateHashFieldUpdateScript('user:{userId}', nidMap, hashFields);
      }
    } else if (instance === 'order') {
      // order 인스턴스 패턴들
      if (gnidMap.size > 0) {
        // prologueGnids:{worldId} 패턴 (Sorted Set)
        script += '-- prologueGnids 패턴 처리 시작\n';
        script += this.generateZSetUpdateScript('prologueGnids:{worldId}', gnidMap);
        script += '-- prologueGnids 패턴 처리 완료\n';
      }
    } else if (instance === 'auth') {
      // auth 인스턴스 패턴들
      if (nidMap.size > 0) {
        // deletionPubIds 패턴 (List)
        script += this.generateListUpdateScript('deletionPubIds', nidMap);
      }
    }

    return script;
  }

  /**
   * 월드별 Lua 스크립트 내용 생성
   */
  private generateWorldLuaContent(worldId: string, worldRedisData: any[]): string {
    // 분석된 Redis 데이터가 없으면 빈 스크립트 반환
    if (worldRedisData.length === 0) {
      return `-- =====================================================
-- UWO GNID/NID 리맵핑 월드별 Redis Lua 스크립트
-- 대상: ${worldId} 월드 Redis
-- 생성일시: ${new Date().toISOString()}
-- =====================================================
-- 주의: 이 월드에는 업데이트할 Redis 데이터가 없습니다.

local updated_count = 0
local error_count = 0

-- 결과 반환
return {updated_count, error_count}
`;
    }

    let lua = `-- =====================================================
-- UWO GNID/NID 리맵핑 월드별 Redis Lua 스크립트
-- 대상: ${worldId} 월드 Redis
-- 생성일시: ${new Date().toISOString()}
-- =====================================================
-- 주의: 데이터베이스는 redis-cli의 -n 옵션으로 지정됩니다.

local updated_count = 0
local error_count = 0

`;

    worldRedisData.forEach(data => {
      lua += `-- ${worldId} 월드 GNID 업데이트\n`;
      data.gnidMap.forEach((newGnid: string, oldGnid: string) => {
        lua += `-- 월드 플레이어 데이터 업데이트: ${oldGnid} -> ${newGnid}
local world_player_keys = redis.call('KEYS', 'world:${worldId}:player:${oldGnid}:*')
for i = 1, #world_player_keys do
    local old_key = world_player_keys[i]
    local new_key = string.gsub(old_key, '${oldGnid}', '${newGnid}')
    local value = redis.call('DUMP', old_key)
    if value then
        redis.call('RESTORE', new_key, 0, value)
        redis.call('DEL', old_key)
        updated_count = updated_count + 1
    else
        error_count = error_count + 1
    end
end

`;
      });

      lua += `-- ${worldId} 월드 NID 업데이트\n`;
      data.nidMap.forEach((newNid: string, oldNid: string) => {
        lua += `-- 월드 캐릭터 데이터 업데이트: ${oldNid} -> ${newNid}
local world_char_keys = redis.call('KEYS', 'world:${worldId}:character:${oldNid}:*')
for i = 1, #world_char_keys do
    local old_key = world_char_keys[i]
    local new_key = string.gsub(old_key, '${oldNid}', '${newNid}')
    local value = redis.call('DUMP', old_key)
    if value then
        redis.call('RESTORE', new_key, 0, value)
        redis.call('DEL', old_key)
        updated_count = updated_count + 1
    else
        error_count = error_count + 1
    end
end

`;
      });
    });

    lua += `-- 결과 반환
return {updated_count, error_count}
`;

    return lua;
  }

  /**
   * 전역 Redis Shell 스크립트 생성
   */
  private async generateGlobalRedisShellScript(outputDir: string, userCacheRedisConfig: any): Promise<void> {
    const script = `#!/bin/bash

# =====================================================
# UWO GNID/NID 리맵핑 전역 Redis 업데이트 스크립트
# 대상: userCacheRedis (전역)
# =====================================================

set -e

# 스크립트 디렉토리
SCRIPT_DIR="$(cd "$(dirname "\${BASH_SOURCE[0]}")" && pwd)"

echo "🔴 전역 Redis 캐시 업데이트 시작..."

# userCacheRedis 업데이트
echo "📦 userCacheRedis 업데이트 중..."
if [ -f "\$SCRIPT_DIR/lua/update_global_cache.lua" ]; then
    redis-cli -h "${userCacheRedisConfig.host}" -p "${userCacheRedisConfig.port}" -n "${userCacheRedisConfig.db}" --eval "\$SCRIPT_DIR/lua/update_global_cache.lua"
    echo "✅ userCacheRedis 업데이트 완료"
else
    echo "⚠️ lua/update_global_cache.lua 파일을 찾을 수 없습니다."
fi

echo "🎉 전역 Redis 캐시 업데이트 완료!"
`;

    const scriptPath = path.join(outputDir, 'update_global_redis.sh');
    await fs.writeFile(scriptPath, script, 'utf8');
    await fs.chmod(scriptPath, 0o755);
    console.log(`📝 전역 Redis Shell 스크립트 생성: ${scriptPath}`);
  }

  /**
   * 월드별 Redis Shell 스크립트 생성
   */
  private async generateWorldRedisShellScript(worldId: string, outputDir: string, worldConfig: any): Promise<void> {
    // 월드별 Redis 인스턴스들 중에서 사용자 관련 인스턴스 찾기
    // 일반적으로 userCache나 town 등이 있을 수 있음
    const userRedisConfig = worldConfig.redisInstances?.userCache || worldConfig.redisInstances?.town;

    if (!userRedisConfig) {
      console.log(`⚠️ ${worldId} 월드의 Redis 설정을 찾을 수 없습니다.`);
      return;
    }

    const script = `#!/bin/bash

# =====================================================
# UWO GNID/NID 리맵핑 ${worldId} 월드 Redis 업데이트 스크립트
# =====================================================

set -e

# 스크립트 디렉토리
SCRIPT_DIR="$(cd "$(dirname "\${BASH_SOURCE[0]}")" && pwd)"

echo "🔴 ${worldId} 월드 Redis 캐시 업데이트 시작..."

# ${worldId} 월드 Redis 업데이트
echo "🌍 ${worldId} 월드 Redis 업데이트 중..."
if [ -f "\$SCRIPT_DIR/lua/${worldId.toLowerCase()}/update_${worldId.toLowerCase()}_cache.lua" ]; then
    redis-cli -h "${userRedisConfig.host}" -p "${userRedisConfig.port}" -n "${userRedisConfig.db}" --eval "\$SCRIPT_DIR/lua/${worldId.toLowerCase()}/update_${worldId.toLowerCase()}_cache.lua"
    echo "✅ ${worldId} 월드 Redis 업데이트 완료"
else
    echo "⚠️ lua/${worldId.toLowerCase()}/update_${worldId.toLowerCase()}_cache.lua 파일을 찾을 수 없습니다."
fi

echo "🎉 ${worldId} 월드 Redis 캐시 업데이트 완료!"
`;

    const scriptPath = path.join(outputDir, `update_${worldId.toLowerCase()}_redis.sh`);
    await fs.writeFile(scriptPath, script, 'utf8');
    await fs.chmod(scriptPath, 0o755);
    console.log(`📝 ${worldId} 월드 Redis Shell 스크립트 생성: ${scriptPath}`);
  }

  /**
   * 마스터 Redis 업데이트 스크립트 생성
   */
  private async generateMasterRedisScript(
    worldGroups: Map<string, RemapRecord[]>,
    outputDir: string
  ): Promise<void> {
    let script = `#!/bin/bash

# =====================================================
# UWO GNID/NID 리맵핑 마스터 Redis 업데이트 스크립트
# =====================================================

set -e

echo "🔴 UWO Redis 캐시 업데이트 시작..."

# 전역 Redis 업데이트
echo "📦 전역 Redis 캐시 업데이트..."
./update_global_redis.sh

`;

    // 월드별 Redis 업데이트 추가
    for (const worldId of worldGroups.keys()) {
      script += `# ${worldId} 월드 Redis 업데이트
echo "🌍 ${worldId} 월드 Redis 캐시 업데이트..."
./update_${worldId.toLowerCase()}_redis.sh

`;
    }

    script += `echo "🎉 모든 Redis 캐시 업데이트 완료!"
`;

    const scriptPath = path.join(outputDir, 'update_all_redis.sh');
    await fs.writeFile(scriptPath, script, 'utf8');
    await fs.chmod(scriptPath, 0o755);
    console.log(`📝 마스터 Redis Shell 스크립트 생성: ${scriptPath}`);
  }

  /**
   * Redis 인스턴스별로 분리된 스크립트 생성
   */
  private async generateSeparatedRedisScripts(
    globalRedisData: any[],
    luaDir: string,
    outputDir: string,
    userCacheRedisConfig: any
  ): Promise<void> {
    console.log(`🔍 전역 Redis 데이터 개수: ${globalRedisData.length}`);

    // Redis 인스턴스별로 그룹화
    const redisInstanceGroups = new Map<string, any[]>();

    globalRedisData.forEach(data => {
      if (data.type === 'global') {
        const redisKeys = data.keys || [];

        // Redis 인스턴스별로 키들을 그룹화
        redisKeys.forEach((keyInfo: any) => {
          const instance = keyInfo.redisInstance;
          if (!redisInstanceGroups.has(instance)) {
            redisInstanceGroups.set(instance, []);
          }
          redisInstanceGroups.get(instance)!.push({
            keyInfo,
            gnidMap: data.gnidMap,
            nidMap: data.nidMap
          });
        });

        // 분석된 키가 없는 경우 모든 전역 Redis 인스턴스에 대해 기본 스크립트 생성
        if (redisKeys.length === 0) {
          const globalInstances = ['userCache', 'auth', 'order'];
          globalInstances.forEach(instance => {
            redisInstanceGroups.set(instance, [{
              keyInfo: null,
              gnidMap: data.gnidMap,
              nidMap: data.nidMap
            }]);
          });
        }
      }
    });

    console.log(`🔍 Redis 인스턴스 그룹 개수: ${redisInstanceGroups.size}`);
    redisInstanceGroups.forEach((keyGroups, instance) => {
      console.log(`  - ${instance}: ${keyGroups.length}개 키 그룹`);
    });

    // 각 Redis 인스턴스별로 Lua 파일 생성
    for (const [instance, keyGroups] of redisInstanceGroups) {
      await this.generateInstanceLuaScript(instance, keyGroups, luaDir);
    }

    // 통합 Shell 스크립트 생성
    await this.generateSeparatedRedisShellScript(redisInstanceGroups, outputDir, userCacheRedisConfig);
  }

  /**
   * 특정 Redis 인스턴스용 Lua 스크립트 생성
   */
  private async generateInstanceLuaScript(
    instance: string,
    keyGroups: any[],
    luaDir: string
  ): Promise<void> {
    let lua = `-- =====================================================
-- UWO GNID/NID 리맵핑 ${instance} Redis Lua 스크립트
-- 생성일시: ${new Date().toISOString()}
-- =====================================================
-- 주의: 데이터베이스는 redis-cli의 -n 옵션으로 지정됩니다.

local updated_count = 0
local error_count = 0

`;

    // 모든 키 그룹에서 사용되는 GNID/NID 맵 수집
    const allGnidMaps = new Map<string, string>();
    const allNidMaps = new Map<string, string>();

    keyGroups.forEach(group => {
      group.gnidMap.forEach((newValue: string, oldValue: string) => {
        allGnidMaps.set(oldValue, newValue);
      });
      group.nidMap.forEach((newValue: string, oldValue: string) => {
        allNidMaps.set(oldValue, newValue);
      });
    });

    // 파일 시작 부분에 리맵 테이블 한 번만 생성
    lua += this.generateRemapTables(allGnidMaps, allNidMaps);

    // 각 키 그룹에 대해 스크립트 생성 (리맵 테이블 재사용)
    keyGroups.forEach(group => {
      if (group.keyInfo) {
        lua += this.generateKeyPatternUpdateScriptWithoutTables(group.keyInfo, group.gnidMap, group.nidMap);
      } else {
        lua += this.generateDefaultGlobalUpdateScriptWithoutTables(group.gnidMap, group.nidMap, instance);
      }
    });

    lua += `-- 결과 반환
return {updated_count, error_count}
`;

    const luaFilePath = path.join(luaDir, `update_${instance}_cache.lua`);
    await fs.writeFile(luaFilePath, lua, 'utf8');
    console.log(`📝 ${instance} Redis Lua 스크립트 생성: ${luaFilePath}`);
  }

  /**
   * 분리된 Redis Shell 스크립트 생성
   */
  private async generateSeparatedRedisShellScript(
    redisInstanceGroups: Map<string, any[]>,
    outputDir: string,
    userCacheRedisConfig: any
  ): Promise<void> {
    // 데이터베이스 설정 읽기
    await this.databaseConfigReader.loadConfig();

    let script = `#!/bin/bash

# =====================================================
# UWO GNID/NID 리맵핑 전역 Redis 업데이트 스크립트 (분리된 인스턴스별)
# =====================================================

set -e

# 스크립트 디렉토리
SCRIPT_DIR="$(cd "$(dirname "\${BASH_SOURCE[0]}")" && pwd)"

echo "🔴 전역 Redis 캐시 업데이트 시작..."

`;

    // 각 Redis 인스턴스별로 업데이트 명령 추가
    for (const [instance, keyGroups] of redisInstanceGroups) {
      // 인스턴스별 Redis 설정 가져오기
      const redisConfig = await this.getRedisConfigForInstance(instance, userCacheRedisConfig);
      console.log(`🔧 ${instance} Redis 설정: ${redisConfig.host}:${redisConfig.port} -n ${redisConfig.db}`);

      script += `# ${instance} Redis 인스턴스 업데이트
echo "📦 ${instance} Redis 업데이트 중..."
if [ -f "\$SCRIPT_DIR/lua/update_${instance}_cache.lua" ]; then
    redis-cli -h "${redisConfig.host}" -p "${redisConfig.port}" -n "${redisConfig.db}" --eval "\$SCRIPT_DIR/lua/update_${instance}_cache.lua"
    echo "✅ ${instance} Redis 업데이트 완료"
else
    echo "⚠️ lua/update_${instance}_cache.lua 파일을 찾을 수 없습니다."
fi

`;
    }

    script += `echo "🎉 전역 Redis 캐시 업데이트 완료!"
`;

    const scriptPath = path.join(outputDir, 'update_global_redis.sh');
    await fs.writeFile(scriptPath, script, 'utf8');
    await fs.chmod(scriptPath, 0o755);
    console.log(`📝 전역 Redis Shell 스크립트 생성: ${scriptPath}`);
  }

  /**
   * Redis 인스턴스별 설정 가져오기 (database.json5에서 실제 설정 읽기)
   */
  private async getRedisConfigForInstance(instance: string, defaultConfig: any): Promise<any> {
    // userCache는 기본 설정 사용
    if (instance === 'userCache') {
      return defaultConfig;
    }

    // 다른 인스턴스들은 database.json5에서 실제 설정 읽기
    try {
      await this.databaseConfigReader.loadConfig();

      // 인스턴스별 Redis 설정 매핑
      const redisInstanceMap: { [key: string]: string } = {
        'auth': 'authRedis',
        'order': 'orderRedis',
        'monitor': 'monitorRedis',
        'globalMatch': 'globalMatchRedis',
        'globalBattleLog': 'globalBattleLogRedis'
      };

      const configKey = redisInstanceMap[instance];
      if (configKey) {
        const config = this.databaseConfigReader.getRedisConfig(configKey);
        if (config) {
          return {
            host: config.host,
            port: config.port,
            db: config.db
          };
        }
      }

      console.log(`⚠️ ${instance} Redis 인스턴스 설정을 찾을 수 없어 기본 설정을 사용합니다.`);
      return defaultConfig;
    } catch (error) {
      console.log(`⚠️ Redis 설정 읽기 실패, 기본 설정 사용: ${error}`);
      return defaultConfig;
    }
  }

  /**
   * RENAME 기반 스크립트 생성 (리맵 테이블 + 루프 방식, 에러 처리 포함)
   */
  private generateRenameScript(keyPattern: string, placeholder: string, remapMap: Map<string, string>): string {
    let script = '';

    // 리맵 테이블 생성
    script += `-- 리맵 테이블 생성\n`;
    script += `local remap_table = {\n`;
    remapMap.forEach((newValue: string, oldValue: string) => {
      script += `    ["${oldValue}"] = "${newValue}",\n`;
    });
    script += `}\n\n`;

    // 루프를 통한 일괄 처리
    script += `-- 리맵 테이블을 순회하며 키 업데이트\n`;
    script += `for old_value, new_value in pairs(remap_table) do\n`;
    script += `    local old_key = "${keyPattern.replace(placeholder, '" .. old_value .. "')}"\n`;
    script += `    local new_key = "${keyPattern.replace(placeholder, '" .. new_value .. "')}"\n`;
    script += `    if redis.call('EXISTS', old_key) == 1 then\n`;
    script += `        local ok, err = pcall(function()\n`;
    script += `            return redis.call('RENAME', old_key, new_key)\n`;
    script += `        end)\n`;
    script += `        if ok then\n`;
    script += `            updated_count = updated_count + 1\n`;
    script += `        else\n`;
    script += `            error_count = error_count + 1\n`;
    script += `        end\n`;
    script += `    end\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * Hash 필드 업데이트 스크립트 생성 (리맵 테이블 + SCAN 방식, 특정 필드만 처리)
   */
  private generateHashFieldUpdateScript(keyPattern: string, nidMap: Map<string, string>, hashFields?: string[]): string {
    let script = '';
    const keyName = keyPattern.replace('{userId}', '*');

    // 리맵 테이블 생성
    script += `-- NID 리맵 테이블 생성\n`;
    script += `local nid_remap_table = {\n`;
    nidMap.forEach((newNid: string, oldNid: string) => {
      script += `    ["${oldNid}"] = "${newNid}",\n`;
    });
    script += `}\n\n`;

    // SCAN을 사용하여 키 수집
    script += `-- SCAN을 사용하여 ${keyName} 패턴의 모든 키 수집\n`;
    script += `local user_keys = {}\n`;
    script += `local cursor = "0"\n`;
    script += `repeat\n`;
    script += `    local result = redis.call('SCAN', cursor, 'MATCH', '${keyName}', 'COUNT', 1000)\n`;
    script += `    cursor = result[1]\n`;
    script += `    local keys = result[2]\n`;
    script += `    for i = 1, #keys do\n`;
    script += `        table.insert(user_keys, keys[i])\n`;
    script += `    end\n`;
    script += `until cursor == "0"\n\n`;

    // 특정 필드만 처리하는 효율적인 방식
    if (hashFields && hashFields.length > 0) {
      script += `-- 특정 필드만 효율적으로 업데이트\n`;
      script += `for i = 1, #user_keys do\n`;
      script += `    local key = user_keys[i]\n`;
      script += `    if redis.call('TYPE', key).ok == 'hash' then\n`;
      script += `        local updated = false\n`;

      // 각 필드별로 직접 처리
      hashFields.forEach(field => {
        script += `        -- ${field} 필드 처리\n`;
        script += `        local old_value = redis.call('HGET', key, '${field}')\n`;
        script += `        if old_value then\n`;
        script += `            local new_value = nid_remap_table[old_value]\n`;
        script += `            if new_value then\n`;
        script += `                local ok, err = pcall(function()\n`;
        script += `                    return redis.call('HSET', key, '${field}', new_value)\n`;
        script += `                end)\n`;
        script += `                if ok then\n`;
        script += `                    updated = true\n`;
        script += `                else\n`;
        script += `                    error_count = error_count + 1\n`;
        script += `                end\n`;
        script += `            end\n`;
        script += `        end\n`;
      });

      script += `        if updated then\n`;
      script += `            updated_count = updated_count + 1\n`;
      script += `        end\n`;
      script += `    end\n`;
      script += `end\n\n`;
    } else {
      // 기존 방식 (모든 필드 검사) - 하위 호환성
      script += `-- 모든 필드 검사 (비효율적 - 하위 호환성용)\n`;
      script += `for i = 1, #user_keys do\n`;
      script += `    local key = user_keys[i]\n`;
      script += `    if redis.call('TYPE', key).ok == 'hash' then\n`;
      script += `        local fields = redis.call('HGETALL', key)\n`;
      script += `        local updated = false\n`;
      script += `        for j = 1, #fields, 2 do\n`;
      script += `            local field = fields[j]\n`;
      script += `            local value = fields[j + 1]\n`;
      script += `            local new_value = nid_remap_table[value]\n`;
      script += `            if new_value then\n`;
      script += `                local ok, err = pcall(function()\n`;
      script += `                    return redis.call('HSET', key, field, new_value)\n`;
      script += `                end)\n`;
      script += `                if ok then\n`;
      script += `                    updated = true\n`;
      script += `                else\n`;
      script += `                    error_count = error_count + 1\n`;
      script += `                end\n`;
      script += `            end\n`;
      script += `        end\n`;
      script += `        if updated then\n`;
      script += `            updated_count = updated_count + 1\n`;
      script += `        end\n`;
      script += `    end\n`;
      script += `end\n\n`;
    }

    return script;
  }

  /**
   * 리맵 테이블 생성 (파일당 한 번만)
   */
  private generateRemapTables(gnidMap: Map<string, string>, nidMap: Map<string, string>): string {
    let script = '';

    // GNID 리맵 테이블 생성
    if (gnidMap.size > 0) {
      script += `-- GNID 리맵 테이블 (파일 전체에서 사용)\n`;
      script += `local gnid_remap_table = {\n`;
      gnidMap.forEach((newValue: string, oldValue: string) => {
        script += `    ["${oldValue}"] = "${newValue}",\n`;
      });
      script += `}\n\n`;
    }

    // NID 리맵 테이블 생성
    if (nidMap.size > 0) {
      script += `-- NID 리맵 테이블 (파일 전체에서 사용)\n`;
      script += `local nid_remap_table = {\n`;
      nidMap.forEach((newValue: string, oldValue: string) => {
        script += `    ["${oldValue}"] = "${newValue}",\n`;
      });
      script += `}\n\n`;
    }

    return script;
  }

  /**
   * 키 패턴 업데이트 스크립트 생성 (리맵 테이블 재사용)
   */
  private generateKeyPatternUpdateScriptWithoutTables(keyInfo: any, gnidMap: Map<string, string>, nidMap: Map<string, string>): string {
    let script = '';
    const keyPattern = keyInfo.keyPattern;
    const keyType = keyInfo.keyType;

    // 키 패턴 분석
    if (this.hasMultiplePlaceholders(keyPattern)) {
      // 다중 플레이스홀더 패턴 - SCAN을 사용한 패턴 매칭 (기존 테이블 재사용)
      script += `-- 다중 플레이스홀더 키 패턴 업데이트: ${keyPattern}\n`;
      script += this.generateMultiPlaceholderUpdateScriptWithoutTable(keyPattern);
    } else if (keyPattern === 'prologueGnids:{worldId}') {
      // 특수 패턴: prologueGnids:{worldId} - GNID를 멤버로 사용하는 ZSET
      script += `-- 특수 패턴 Sorted Set 키 업데이트: ${keyPattern}\n`;
      script += this.generateZSetUpdateScriptWithoutTable(keyPattern, 'gnid_remap_table');
    } else if (keyPattern === 'deletionPubIds') {
      // 특수 패턴: deletionPubIds - NID를 값으로 사용하는 List
      script += `-- 특수 패턴 List 키 업데이트: ${keyPattern}\n`;
      script += this.generateListUpdateScriptWithoutTable(keyPattern);
    } else if (keyPattern.includes('{nid}')) {
      // NID 기반 키 패턴 - RENAME 사용 (기존 테이블 재사용)
      script += `-- NID 키 패턴 업데이트: ${keyPattern}\n`;
      script += this.generateRenameScriptWithoutTable(keyPattern, '{nid}', 'nid_remap_table');
    } else if (keyPattern.includes('{gnid}') || keyPattern.includes('{accountId}')) {
      // GNID/AccountId 기반 키 패턴 - RENAME 사용 (기존 테이블 재사용)
      script += `-- GNID 키 패턴 업데이트: ${keyPattern}\n`;
      const placeholder = keyPattern.includes('{gnid}') ? '{gnid}' : '{accountId}';
      script += this.generateRenameScriptWithoutTable(keyPattern, placeholder, 'gnid_remap_table');
    } else if (keyPattern.includes('{userId}')) {
      // UserId 기반 키 패턴 - Hash 필드값 변경 (기존 테이블 재사용)
      script += `-- UserId Hash 필드 업데이트: ${keyPattern}\n`;
      // 키 패턴 정보에서 hashFields 추출
      const keyInfo = getAllRedisKeyPatterns().find((info: RedisKeyInfo) => info.keyPattern === keyPattern);
      const hashFields = keyInfo?.hashFields;
      script += this.generateHashFieldUpdateScriptWithoutTable(keyPattern, hashFields);
    } else if (keyType === 'list') {
      // List 타입 키 패턴 - 리스트 내 값 업데이트 (기존 테이블 재사용)
      script += `-- List 키 업데이트: ${keyPattern}\n`;
      script += this.generateListUpdateScriptWithoutTable(keyPattern);
    } else if (keyType === 'zset') {
      // Sorted Set 타입 키 패턴 - 멤버 업데이트 (기존 테이블 재사용)
      script += `-- Sorted Set 키 업데이트: ${keyPattern}\n`;
      if (keyPattern.includes('{gnid}') || keyPattern.includes('{accountId}')) {
        script += this.generateZSetUpdateScriptWithoutTable(keyPattern, 'gnid_remap_table');
      } else if (keyPattern.includes('{nid}') || keyPattern.includes('{pubId}')) {
        script += this.generateZSetUpdateScriptWithoutTable(keyPattern, 'nid_remap_table');
      }
    } else if (keyType === 'string' && !keyPattern.includes('{')) {
      // 고정 키 이름이지만 string 타입인 경우 (값 자체가 NID일 수 있음)
      script += `-- String 키 값 업데이트: ${keyPattern}\n`;
      script += this.generateStringValueUpdateScriptWithoutTable(keyPattern);
    }

    script += '\n';
    return script;
  }

  /**
   * 기본 업데이트 스크립트 생성 (리맵 테이블 재사용)
   */
  private generateDefaultGlobalUpdateScriptWithoutTables(gnidMap: Map<string, string>, nidMap: Map<string, string>, instance: string): string {
    let script = `-- 기본 ${instance} Redis 키 패턴 업데이트 (분석된 키 패턴 없음, 기존 테이블 재사용)\n`;
    script += `-- Instance: ${instance}, GNID Map size: ${gnidMap.size}, NID Map size: ${nidMap.size}\n`;

    // 인스턴스별로 적절한 패턴만 처리 (기존 테이블 재사용)
    if (instance === 'userCache') {
      // userCache 인스턴스 패턴들
      if (gnidMap.size > 0) {
        // account:{accountId} 패턴
        script += this.generateRenameScriptWithoutTable('account:{accountId}', '{accountId}', 'gnid_remap_table');
      }

      if (nidMap.size > 0) {
        // user:{userId} Hash 필드
        const keyInfo = getAllRedisKeyPatterns().find((info: RedisKeyInfo) => info.keyPattern === 'user:{userId}');
        const hashFields = keyInfo?.hashFields;
        script += this.generateHashFieldUpdateScriptWithoutTable('user:{userId}', hashFields);
      }
    } else if (instance === 'order') {
      // order 인스턴스 패턴들
      if (gnidMap.size > 0) {
        // prologueGnids:{worldId} 패턴 (Sorted Set)
        script += this.generateZSetUpdateScriptWithoutTable('prologueGnids:{worldId}', 'gnid_remap_table');
      }
    } else if (instance === 'auth') {
      // auth 인스턴스 패턴들
      if (nidMap.size > 0) {
        // deletionPubIds 패턴 (List)
        script += this.generateListUpdateScriptWithoutTable('deletionPubIds');
      }
    }

    return script;
  }

  /**
   * RENAME 스크립트 생성 (기존 리맵 테이블 재사용, 에러 처리 포함)
   */
  private generateRenameScriptWithoutTable(keyPattern: string, placeholder: string, tableName: string): string {
    let script = '';

    // 기존 리맵 테이블을 사용한 루프 처리
    script += `-- 리맵 테이블을 순회하며 키 업데이트\n`;
    script += `for old_value, new_value in pairs(${tableName}) do\n`;
    script += `    local old_key = "${keyPattern.replace(placeholder, '" .. old_value .. "')}"\n`;
    script += `    local new_key = "${keyPattern.replace(placeholder, '" .. new_value .. "')}"\n`;
    script += `    if redis.call('EXISTS', old_key) == 1 then\n`;
    script += `        local ok, err = pcall(function()\n`;
    script += `            return redis.call('RENAME', old_key, new_key)\n`;
    script += `        end)\n`;
    script += `        if ok then\n`;
    script += `            updated_count = updated_count + 1\n`;
    script += `        else\n`;
    script += `            error_count = error_count + 1\n`;
    script += `        end\n`;
    script += `    end\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * Hash 필드 업데이트 스크립트 생성 (기존 리맵 테이블 재사용, 특정 필드만 처리)
   */
  private generateHashFieldUpdateScriptWithoutTable(keyPattern: string, hashFields?: string[]): string {
    let script = '';
    const keyName = keyPattern.replace('{userId}', '*');

    // SCAN을 사용하여 키 수집
    script += `-- SCAN을 사용하여 ${keyName} 패턴의 모든 키 수집\n`;
    script += `local user_keys = {}\n`;
    script += `local cursor = "0"\n`;
    script += `repeat\n`;
    script += `    local result = redis.call('SCAN', cursor, 'MATCH', '${keyName}', 'COUNT', 1000)\n`;
    script += `    cursor = result[1]\n`;
    script += `    local keys = result[2]\n`;
    script += `    for i = 1, #keys do\n`;
    script += `        table.insert(user_keys, keys[i])\n`;
    script += `    end\n`;
    script += `until cursor == "0"\n\n`;

    // 특정 필드만 처리하는 효율적인 방식
    if (hashFields && hashFields.length > 0) {
      script += `-- 특정 필드만 효율적으로 업데이트 (기존 리맵 테이블 사용)\n`;
      script += `for i = 1, #user_keys do\n`;
      script += `    local key = user_keys[i]\n`;
      script += `    if redis.call('TYPE', key).ok == 'hash' then\n`;
      script += `        local updated = false\n`;

      // 각 필드별로 직접 처리
      hashFields.forEach(field => {
        script += `        -- ${field} 필드 처리\n`;
        script += `        local old_value = redis.call('HGET', key, '${field}')\n`;
        script += `        if old_value then\n`;
        script += `            local new_value = nid_remap_table[old_value]\n`;
        script += `            if new_value then\n`;
        script += `                local ok, err = pcall(function()\n`;
        script += `                    return redis.call('HSET', key, '${field}', new_value)\n`;
        script += `                end)\n`;
        script += `                if ok then\n`;
        script += `                    updated = true\n`;
        script += `                else\n`;
        script += `                    error_count = error_count + 1\n`;
        script += `                end\n`;
        script += `            end\n`;
        script += `        end\n`;
      });

      script += `        if updated then\n`;
      script += `            updated_count = updated_count + 1\n`;
      script += `        end\n`;
      script += `    end\n`;
      script += `end\n\n`;
    } else {
      // 기존 방식 (모든 필드 검사) - 하위 호환성
      script += `-- 모든 필드 검사 (비효율적 - 하위 호환성용)\n`;
      script += `for i = 1, #user_keys do\n`;
      script += `    local key = user_keys[i]\n`;
      script += `    if redis.call('TYPE', key).ok == 'hash' then\n`;
      script += `        local fields = redis.call('HGETALL', key)\n`;
      script += `        local updated = false\n`;
      script += `        for j = 1, #fields, 2 do\n`;
      script += `            local field = fields[j]\n`;
      script += `            local value = fields[j + 1]\n`;
      script += `            local new_value = nid_remap_table[value]\n`;
      script += `            if new_value then\n`;
      script += `                local ok, err = pcall(function()\n`;
      script += `                    return redis.call('HSET', key, field, new_value)\n`;
      script += `                end)\n`;
      script += `                if ok then\n`;
      script += `                    updated = true\n`;
      script += `                else\n`;
      script += `                    error_count = error_count + 1\n`;
      script += `                end\n`;
      script += `            end\n`;
      script += `        end\n`;
      script += `        if updated then\n`;
      script += `            updated_count = updated_count + 1\n`;
      script += `        end\n`;
      script += `    end\n`;
      script += `end\n\n`;
    }

    return script;
  }

  /**
   * List 타입 키 업데이트 스크립트 생성 (에러 처리 포함)
   */
  private generateListUpdateScript(keyPattern: string, nidMap: Map<string, string>): string {
    let script = '';

    // 고정 키 이름 (deletionPubIds 같은 경우)
    const keyName = keyPattern;

    script += `-- List 키 ${keyName} 업데이트\n`;
    script += `if redis.call('EXISTS', '${keyName}') == 1 then\n`;
    script += `    if redis.call('TYPE', '${keyName}').ok == 'list' then\n`;
    script += `        local list_length = redis.call('LLEN', '${keyName}')\n`;
    script += `        for i = 0, list_length - 1 do\n`;
    script += `            local value = redis.call('LINDEX', '${keyName}', i)\n`;
    script += `            -- 기존 NID 리맵 테이블에서 새 값 찾기\n`;
    script += `            local new_value = nid_remap_table[value]\n`;
    script += `            if new_value then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    return redis.call('LSET', '${keyName}', i, new_value)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated_count = updated_count + 1\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `    end\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * List 타입 키 업데이트 스크립트 생성 (기존 리맵 테이블 재사용)
   */
  private generateListUpdateScriptWithoutTable(keyPattern: string): string {
    let script = '';

    // 고정 키 이름 (deletionPubIds 같은 경우)
    const keyName = keyPattern;

    script += `-- List 키 ${keyName} 업데이트\n`;
    script += `if redis.call('EXISTS', '${keyName}') == 1 then\n`;
    script += `    if redis.call('TYPE', '${keyName}').ok == 'list' then\n`;
    script += `        local list_length = redis.call('LLEN', '${keyName}')\n`;
    script += `        for i = 0, list_length - 1 do\n`;
    script += `            local value = redis.call('LINDEX', '${keyName}', i)\n`;
    script += `            -- 기존 NID 리맵 테이블에서 새 값 찾기\n`;
    script += `            local new_value = nid_remap_table[value]\n`;
    script += `            if new_value then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    return redis.call('LSET', '${keyName}', i, new_value)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated_count = updated_count + 1\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `    end\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * String 타입 키 값 업데이트 스크립트 생성 (기존 리맵 테이블 재사용)
   */
  private generateStringValueUpdateScriptWithoutTable(keyPattern: string): string {
    let script = '';

    // 고정 키 이름
    const keyName = keyPattern;

    script += `-- String 키 ${keyName} 값 업데이트\n`;
    script += `if redis.call('EXISTS', '${keyName}') == 1 then\n`;
    script += `    if redis.call('TYPE', '${keyName}').ok == 'string' then\n`;
    script += `        local value = redis.call('GET', '${keyName}')\n`;
    script += `        -- 기존 NID 리맵 테이블에서 새 값 찾기\n`;
    script += `        local new_value = nid_remap_table[value]\n`;
    script += `        if new_value then\n`;
    script += `            local ok, err = pcall(function()\n`;
    script += `                return redis.call('SET', '${keyName}', new_value)\n`;
    script += `            end)\n`;
    script += `            if ok then\n`;
    script += `                updated_count = updated_count + 1\n`;
    script += `            else\n`;
    script += `                error_count = error_count + 1\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `    end\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * Sorted Set 업데이트 스크립트 생성 (리맵 테이블 방식)
   */
  private generateZSetUpdateScript(keyPattern: string, remapMap: Map<string, string>): string {
    let script = '';

    // 리맵 테이블 생성
    const tableType = keyPattern.includes('{gnid}') || keyPattern.includes('{accountId}') ? 'GNID' : 'NID';
    script += `-- ${tableType} 리맵 테이블 생성\n`;
    script += `local ${tableType.toLowerCase()}_remap_table = {\n`;
    remapMap.forEach((newValue: string, oldValue: string) => {
      script += `    ["${oldValue}"] = "${newValue}",\n`;
    });
    script += `}\n\n`;

    // SCAN을 사용하여 키 수집
    const scanPattern = keyPattern.replace(/\{[^}]+\}/g, '*');
    script += `-- SCAN을 사용하여 ${scanPattern} 패턴의 모든 키 수집\n`;
    script += `local zset_keys = {}\n`;
    script += `local cursor = "0"\n`;
    script += `repeat\n`;
    script += `    local result = redis.call('SCAN', cursor, 'MATCH', '${scanPattern}', 'COUNT', 1000)\n`;
    script += `    cursor = result[1]\n`;
    script += `    local keys = result[2]\n`;
    script += `    for i = 1, #keys do\n`;
    script += `        table.insert(zset_keys, keys[i])\n`;
    script += `    end\n`;
    script += `until cursor == "0"\n\n`;

    // 수집된 키들에 대해 Sorted Set 멤버 업데이트
    script += `-- 수집된 키들에 대해 Sorted Set 멤버 업데이트\n`;
    script += `for i = 1, #zset_keys do\n`;
    script += `    local key = zset_keys[i]\n`;
    script += `    if redis.call('TYPE', key).ok == 'zset' then\n`;
    script += `        -- 모든 멤버와 스코어 가져오기\n`;
    script += `        local members = redis.call('ZRANGE', key, 0, -1, 'WITHSCORES')\n`;
    script += `        local updated = false\n`;
    script += `        for j = 1, #members, 2 do\n`;
    script += `            local member = members[j]\n`;
    script += `            local score = members[j + 1]\n`;
    script += `            -- 리맵 테이블에서 새 값 찾기\n`;
    script += `            local new_member = ${tableType.toLowerCase()}_remap_table[member]\n`;
    script += `            if new_member then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    -- 기존 멤버 제거\n`;
    script += `                    redis.call('ZREM', key, member)\n`;
    script += `                    -- 새 멤버 추가 (동일한 스코어)\n`;
    script += `                    return redis.call('ZADD', key, score, new_member)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated = true\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `        if updated then\n`;
    script += `            updated_count = updated_count + 1\n`;
    script += `        end\n`;
    script += `    end\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * Sorted Set 업데이트 스크립트 생성 (기존 리맵 테이블 재사용)
   */
  private generateZSetUpdateScriptWithoutTable(keyPattern: string, tableVariable: string): string {
    let script = '';

    // SCAN을 사용하여 키 수집
    const scanPattern = keyPattern.replace(/\{[^}]+\}/g, '*');
    script += `-- SCAN을 사용하여 ${scanPattern} 패턴의 모든 키 수집\n`;
    script += `local zset_keys = {}\n`;
    script += `local cursor = "0"\n`;
    script += `repeat\n`;
    script += `    local result = redis.call('SCAN', cursor, 'MATCH', '${scanPattern}', 'COUNT', 1000)\n`;
    script += `    cursor = result[1]\n`;
    script += `    local keys = result[2]\n`;
    script += `    for i = 1, #keys do\n`;
    script += `        table.insert(zset_keys, keys[i])\n`;
    script += `    end\n`;
    script += `until cursor == "0"\n\n`;

    // 수집된 키들에 대해 Sorted Set 멤버 업데이트 (기존 테이블 사용)
    script += `-- 수집된 키들에 대해 Sorted Set 멤버 업데이트 (기존 ${tableVariable} 사용)\n`;
    script += `for i = 1, #zset_keys do\n`;
    script += `    local key = zset_keys[i]\n`;
    script += `    if redis.call('TYPE', key).ok == 'zset' then\n`;
    script += `        -- 모든 멤버와 스코어 가져오기\n`;
    script += `        local members = redis.call('ZRANGE', key, 0, -1, 'WITHSCORES')\n`;
    script += `        local updated = false\n`;
    script += `        for j = 1, #members, 2 do\n`;
    script += `            local member = members[j]\n`;
    script += `            local score = members[j + 1]\n`;
    script += `            -- 기존 리맵 테이블에서 새 값 찾기\n`;
    script += `            local new_member = ${tableVariable}[member]\n`;
    script += `            if new_member then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    -- 기존 멤버 제거\n`;
    script += `                    redis.call('ZREM', key, member)\n`;
    script += `                    -- 새 멤버 추가 (동일한 스코어)\n`;
    script += `                    return redis.call('ZADD', key, score, new_member)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated = true\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `        if updated then\n`;
    script += `            updated_count = updated_count + 1\n`;
    script += `        end\n`;
    script += `    end\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * 다중 플레이스홀더 패턴인지 확인
   */
  private hasMultiplePlaceholders(keyPattern: string): boolean {
    const placeholders = keyPattern.match(/\{[^}]+\}/g) || [];
    return placeholders.length > 1;
  }

  /**
   * 다중 플레이스홀더 키 패턴 업데이트 스크립트 생성
   */
  private generateMultiPlaceholderUpdateScript(keyPattern: string, gnidMap: Map<string, string>, nidMap: Map<string, string>): string {
    let script = '';

    // 어떤 플레이스홀더가 포함되어 있는지 확인
    const hasNid = keyPattern.includes('{nid}');
    const hasGnid = keyPattern.includes('{gnid}') || keyPattern.includes('{accountId}');

    if (hasNid) {
      // NID 기반 다중 플레이스홀더 처리
      script += this.generateMultiPlaceholderNidUpdateScript(keyPattern, nidMap);
    } else if (hasGnid) {
      // GNID 기반 다중 플레이스홀더 처리
      script += this.generateMultiPlaceholderGnidUpdateScript(keyPattern, gnidMap);
    }

    return script;
  }

  /**
   * NID 기반 다중 플레이스홀더 업데이트 스크립트 생성
   */
  private generateMultiPlaceholderNidUpdateScript(keyPattern: string, nidMap: Map<string, string>): string {
    let script = '';

    // 리맵 테이블을 순회하며 각 NID에 대해 SCAN 수행
    script += `-- NID 리맵 테이블을 순회하며 다중 플레이스홀더 키 업데이트\n`;
    script += `for old_nid, new_nid in pairs(nid_remap_table) do\n`;

    // SCAN 패턴 생성 (예: townUserWeeklyInvestmentReport:old_nid:*)
    const scanPattern = keyPattern.replace('{nid}', '" .. old_nid .. "').replace(/\{[^}]+\}/g, '*');
    script += `    local scan_pattern = "${scanPattern}"\n`;
    script += `    local cursor = "0"\n`;
    script += `    repeat\n`;
    script += `        local result = redis.call('SCAN', cursor, 'MATCH', scan_pattern, 'COUNT', 1000)\n`;
    script += `        cursor = result[1]\n`;
    script += `        local keys = result[2]\n`;
    script += `        for i = 1, #keys do\n`;
    script += `            local old_key = keys[i]\n`;

    // 새로운 키 생성 (old_nid를 new_nid로 교체)
    script += `            local new_key = string.gsub(old_key, ":" .. old_nid .. ":", ":" .. new_nid .. ":")\n`;
    script += `            if redis.call('EXISTS', old_key) == 1 then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    return redis.call('RENAME', old_key, new_key)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated_count = updated_count + 1\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `    until cursor == "0"\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * GNID 기반 다중 플레이스홀더 업데이트 스크립트 생성
   */
  private generateMultiPlaceholderGnidUpdateScript(keyPattern: string, gnidMap: Map<string, string>): string {
    let script = '';

    // GNID 리맵 테이블을 순회하며 각 GNID에 대해 SCAN 수행
    script += `-- GNID 리맵 테이블을 순회하며 다중 플레이스홀더 키 업데이트\n`;
    script += `for old_gnid, new_gnid in pairs(gnid_remap_table) do\n`;

    // SCAN 패턴 생성
    const placeholder = keyPattern.includes('{gnid}') ? '{gnid}' : '{accountId}';
    const scanPattern = keyPattern.replace(placeholder, '" .. old_gnid .. "').replace(/\{[^}]+\}/g, '*');
    script += `    local scan_pattern = "${scanPattern}"\n`;
    script += `    local cursor = "0"\n`;
    script += `    repeat\n`;
    script += `        local result = redis.call('SCAN', cursor, 'MATCH', scan_pattern, 'COUNT', 1000)\n`;
    script += `        cursor = result[1]\n`;
    script += `        local keys = result[2]\n`;
    script += `        for i = 1, #keys do\n`;
    script += `            local old_key = keys[i]\n`;

    // 새로운 키 생성 (old_gnid를 new_gnid로 교체)
    script += `            local new_key = string.gsub(old_key, ":" .. old_gnid .. ":", ":" .. new_gnid .. ":")\n`;
    script += `            if redis.call('EXISTS', old_key) == 1 then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    return redis.call('RENAME', old_key, new_key)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated_count = updated_count + 1\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `    until cursor == "0"\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * 다중 플레이스홀더 키 패턴 업데이트 스크립트 생성 (기존 리맵 테이블 재사용)
   */
  private generateMultiPlaceholderUpdateScriptWithoutTable(keyPattern: string): string {
    let script = '';

    // 어떤 플레이스홀더가 포함되어 있는지 확인
    const hasNid = keyPattern.includes('{nid}');
    const hasGnid = keyPattern.includes('{gnid}') || keyPattern.includes('{accountId}');

    if (hasNid) {
      // NID 기반 다중 플레이스홀더 처리 (기존 nid_remap_table 사용)
      script += this.generateMultiPlaceholderNidUpdateScriptWithoutTable(keyPattern);
    } else if (hasGnid) {
      // GNID 기반 다중 플레이스홀더 처리 (기존 gnid_remap_table 사용)
      script += this.generateMultiPlaceholderGnidUpdateScriptWithoutTable(keyPattern);
    }

    return script;
  }

  /**
   * NID 기반 다중 플레이스홀더 업데이트 스크립트 생성 (기존 리맵 테이블 재사용)
   */
  private generateMultiPlaceholderNidUpdateScriptWithoutTable(keyPattern: string): string {
    let script = '';

    // 기존 NID 리맵 테이블을 순회하며 각 NID에 대해 SCAN 수행
    script += `-- 기존 NID 리맵 테이블을 순회하며 다중 플레이스홀더 키 업데이트\n`;
    script += `for old_nid, new_nid in pairs(nid_remap_table) do\n`;

    // SCAN 패턴 생성 (예: townUserWeeklyInvestmentReport:old_nid:*)
    const scanPattern = keyPattern.replace('{nid}', '" .. old_nid .. "').replace(/\{[^}]+\}/g, '*');
    script += `    local scan_pattern = "${scanPattern}"\n`;
    script += `    local cursor = "0"\n`;
    script += `    repeat\n`;
    script += `        local result = redis.call('SCAN', cursor, 'MATCH', scan_pattern, 'COUNT', 1000)\n`;
    script += `        cursor = result[1]\n`;
    script += `        local keys = result[2]\n`;
    script += `        for i = 1, #keys do\n`;
    script += `            local old_key = keys[i]\n`;

    // 새로운 키 생성 (old_nid를 new_nid로 교체)
    script += `            local new_key = string.gsub(old_key, ":" .. old_nid .. ":", ":" .. new_nid .. ":")\n`;
    script += `            if redis.call('EXISTS', old_key) == 1 then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    return redis.call('RENAME', old_key, new_key)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated_count = updated_count + 1\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `    until cursor == "0"\n`;
    script += `end\n\n`;

    return script;
  }

  /**
   * GNID 기반 다중 플레이스홀더 업데이트 스크립트 생성 (기존 리맵 테이블 재사용)
   */
  private generateMultiPlaceholderGnidUpdateScriptWithoutTable(keyPattern: string): string {
    let script = '';

    // 기존 GNID 리맵 테이블을 순회하며 각 GNID에 대해 SCAN 수행
    script += `-- 기존 GNID 리맵 테이블을 순회하며 다중 플레이스홀더 키 업데이트\n`;
    script += `for old_gnid, new_gnid in pairs(gnid_remap_table) do\n`;

    // SCAN 패턴 생성
    const placeholder = keyPattern.includes('{gnid}') ? '{gnid}' : '{accountId}';
    const scanPattern = keyPattern.replace(placeholder, '" .. old_gnid .. "').replace(/\{[^}]+\}/g, '*');
    script += `    local scan_pattern = "${scanPattern}"\n`;
    script += `    local cursor = "0"\n`;
    script += `    repeat\n`;
    script += `        local result = redis.call('SCAN', cursor, 'MATCH', scan_pattern, 'COUNT', 1000)\n`;
    script += `        cursor = result[1]\n`;
    script += `        local keys = result[2]\n`;
    script += `        for i = 1, #keys do\n`;
    script += `            local old_key = keys[i]\n`;

    // 새로운 키 생성 (old_gnid를 new_gnid로 교체)
    script += `            local new_key = string.gsub(old_key, ":" .. old_gnid .. ":", ":" .. new_gnid .. ":")\n`;
    script += `            if redis.call('EXISTS', old_key) == 1 then\n`;
    script += `                local ok, err = pcall(function()\n`;
    script += `                    return redis.call('RENAME', old_key, new_key)\n`;
    script += `                end)\n`;
    script += `                if ok then\n`;
    script += `                    updated_count = updated_count + 1\n`;
    script += `                else\n`;
    script += `                    error_count = error_count + 1\n`;
    script += `                end\n`;
    script += `            end\n`;
    script += `        end\n`;
    script += `    until cursor == "0"\n`;
    script += `end\n\n`;

    return script;
  }
}
