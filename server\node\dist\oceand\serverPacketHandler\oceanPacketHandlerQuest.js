"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const merror_1 = require("../../motiflib/merror");
const typedi_1 = __importDefault(require("typedi"));
const fleetManager_1 = require("../fleetManager");
const cms_1 = __importDefault(require("../../cms"));
const ex_1 = require("../../cms/ex");
const oceanEntityInstanceManager_1 = require("../oceanEntityInstanceManager");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const oceanDoodadEntity_1 = require("../oceanDoodadEntity");
const cmsEx = __importStar(require("../../cms/ex"));
const gl_matrix_1 = require("gl-matrix");
const OC = __importStar(require("../../cms/oceanCoordinate"));
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
router.on(protocol_1.Protocol.LB2OC_REQ_SPAWN_OCEAN_DOODAD, async (req, res) => {
    const userId = req.userId;
    const doodadCmsId = req.doodadCmsId;
    const radius = req.radius;
    let location = req.location;
    const degrees = req.degrees;
    const isGlobal = req.isGlobal;
    const spawnType = req.spawnType;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const oceanDoodadCms = cms_1.default.OceanDoodad[doodadCmsId];
    if (!oceanDoodadCms) {
        throw new merror_1.MError('invalid doodadCmsId!', merror_1.MErrorCode.INVALID_OCEAN_DOODAD_CMS_ID, {
            userId,
            doodadCmsId,
        });
    }
    if (spawnType == cmsEx.QUEST_SPAWN_OCEAN_DOODAD_TYPE.CURRENT_LOCATION_RADIUS) {
        location = userFleet.getLocation();
    }
    if (0 < radius) {
        //spawn within radius
        const vec = gl_matrix_1.vec2.random(gl_matrix_1.vec2.create(), Math.random() * radius);
        const spawnAreaUe = OC.LatLon2Ue(location.latitude, location.longitude);
        location = OC.Ue2LatLon(vec[0] + spawnAreaUe.x, vec[1] + spawnAreaUe.y);
    }
    let oceanDoodad;
    if (!isGlobal) {
        const param = {
            baseOceanDoodadParam: { doodadCmsId, location, degrees },
            spawndCellId: '',
            type: oceanDoodadEntity_1.LOCAL_DOODAD_TYPE.QUEST,
        };
        oceanDoodad = userFleet.spawnLocalNormalDoodad(param);
    }
    else {
        mlog_1.default.error('unsupported request yet...can not create manually global ocean doodad', {
            userId,
            doodadCmsId,
        });
    }
    if (!oceanDoodad) {
        throw new merror_1.MError('can not create oceanDoodad!', merror_1.MErrorCode.INTERNAL_ERROR, {
            userId,
            doodadCmsId,
        });
    }
    const sendPacket = new protocol_1.Protocol.OC2LB_RES_SPAWN_OCEAN_DOODAD();
    sendPacket.doodadId = oceanDoodad.id;
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_REQ_DESPAWN_OCEAN_DOODAD, async (req, res) => {
    const userId = req.userId;
    const doodadId = req.doodadId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const foundDoodad = userFleet.findLocalDoodad(doodadId);
    /*
     호출 case
     1) 로컬고정 스폰인 경우 doodadSpawnLogic 코드에서 contentsTerms 를 만족 못할 시
     2) ActivateOceanDoodad 패킷
     양쪽 모두 패킷이 오더라도 늦게 도착한 패킷은 doodad 없으므로 return 처리
    */
    if (!foundDoodad) {
        mlog_1.default.info('can not find oceanDoodad', {
            userId,
            doodadId,
        });
        const sendPacket = new protocol_1.Protocol.OC2LB_RES_DESPAWN_OCEAN_DOODAD();
        sendPacket.doodadId = doodadId;
        res.send(sendPacket);
        return;
    }
    const entityInsMng = typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager);
    entityInsMng.deleteDoodad(foundDoodad);
    const sendPacket = new protocol_1.Protocol.OC2LB_RES_DESPAWN_OCEAN_DOODAD();
    sendPacket.doodadId = foundDoodad.id;
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_REQ_OCEAN_DOODAD_INFO, async (req, res) => {
    const userId = req.userId;
    const doodadId = req.doodadId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const grid = userFleet.getGrid();
    if (!grid) {
        throw new merror_1.MError('User not in a grid.', merror_1.MErrorCode.USER_NOT_IN_GRID, {
            userId: req.userId,
        });
    }
    const doodadEntity = grid.findVisibleDoodad(doodadId);
    if (!doodadEntity) {
        throw new merror_1.MError('can not find oceanDoodad!', merror_1.MErrorCode.FAILED_TO_FIND_OCEAN_DOODAD, {
            userId,
            doodadId,
        });
    }
    const sendPacket = new protocol_1.Protocol.OC2LB_RES_OCEAN_DOODAD_INFO();
    sendPacket.doodad = doodadEntity.getMySyncData();
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_REQ_QUEST_SPAWN_LOCAL_NPC, async (req, res) => {
    const userId = req.userId;
    const npcCmsId = req.npcCmsId;
    const radius = req.radius;
    let location = req.location;
    const degrees = req.degrees;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const npcCms = (0, ex_1.getOceanNpcCms)()[npcCmsId];
    if (!npcCms) {
        throw new merror_1.MError('invalid npcCmsId!', merror_1.MErrorCode.INVALID_OCEAN_NPC_CMS_ID, {
            userId,
            npcCmsId,
        });
    }
    if (!location) {
        location = userFleet.getLocation();
    }
    if (0 < radius) {
        //spawn within radius
        const vec = gl_matrix_1.vec2.random(gl_matrix_1.vec2.create(), Math.random() * radius);
        const spawnAreaUe = OC.LatLon2Ue(location.latitude, location.longitude);
        location = OC.Ue2LatLon(vec[0] + spawnAreaUe.x, vec[1] + spawnAreaUe.y);
    }
    const npcId = userFleet
        .getLocalNpcSpawnEntry()
        .spawnQuestNpc(userFleet, npcCmsId, location, degrees);
    if (!npcId) {
        throw new merror_1.MError('can not create quest local npc!', merror_1.MErrorCode.INTERNAL_ERROR, {
            userId,
            npcCmsId,
        });
    }
    const sendPacket = new protocol_1.Protocol.OC2LB_RES_QUEST_SPAWN_LOCAL_NPC();
    sendPacket.npcId = npcId;
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_REQ_QUEST_DESPAWN_LOCAL_NPC, async (req, res) => {
    const userId = req.userId;
    const npcId = req.npcId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    userFleet.getLocalNpcSpawnEntry().despawnQuestNpc(userFleet, npcId);
    const sendPacket = new protocol_1.Protocol.OC2LB_RES_QUEST_DESPAWN_LOCAL_NPC();
    sendPacket.npcId = npcId;
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_NTF_CHANGE_QUEST_SINGLE_MODE, async (req, res) => {
    const userId = req.userId;
    const bQuestSingleMode = req.bQuestSingleMode;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    // userFleet의 FleetData에 bQuestSingleMode적용
    userFleet.setQuestSingleMode(bQuestSingleMode);
    // 주변 유저들에게 통보.
    const sendPacket = new protocol_1.Protocol.OC2CL_NTF_NET_USER_CHANGE_QUEST_SINGLE_MODE();
    sendPacket.userId = userFleet.userId;
    sendPacket.bQuestSingleMode = bQuestSingleMode;
    userFleet.sendBroadCastAdjacentGrids(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_NTF_CHANGE_QUEST_NPC_MOVE_AI, async (req, res) => {
    const { userId, npcId, freeRoamingRate, pathRoamingRate, anchorRate } = req;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    userFleet
        .getLocalNpcSpawnEntry()
        .setQuestNpcMoveAi(userFleet, npcId, freeRoamingRate, pathRoamingRate, anchorRate);
});
router.on(protocol_1.Protocol.LB2OC_NTF_CHANGE_QUEST_NPC_ATTACK_AI, async (req, res) => {
    const { userId, npcId, matePersonalityCmsId } = req;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    userFleet.getLocalNpcSpawnEntry().setQuestNpcAttackAi(userFleet, npcId, matePersonalityCmsId);
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerQuest.js.map