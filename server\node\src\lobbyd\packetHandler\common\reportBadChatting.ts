// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Client<PERSON><PERSON>etHandler } from '../index';
import { getUserLightInfoByName } from '../../../motiflib/userCacheRedisHelper';
import { UserLightInfo } from '../../type/sync';

// ----------------------------------------------------------------------------
// 다른플레이어의 불건전 채팅을 신고.
// ----------------------------------------------------------------------------

const BadChattingReason = {
  ['CHAT_OBSCENE']: '음란',
  ['CHAT_ABUSE']: '욕설',
  ['CHAT_SPAMMING']: '도배',
  ['NICKNAME']: '닉네임',
};

interface RequestBody {
  targetUserName: string; // 불건전한 채팅메세지를 사용한 유저.
  chatMsg: string; // 메세지내용.
  reasonCd: string; // 사유
}

// ----------------------------------------------------------------------------
export class Cph_Common_ReportBadChatting implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const reqBody: RequestBody = packet.bodyObj;
    const { targetUserName, chatMsg, reasonCd } = reqBody;

    const addInfo = BadChattingReason[reasonCd];

    return getUserLightInfoByName(targetUserName).then((userLightInfo: UserLightInfo) => {
      if (!userLightInfo) {
        // 유저조회가 안될 경우 실패메세지 전달.
        throw new MError('not-found-target-user-by-name', MErrorCode.NOT_FOUND_BAD_CHATTING_USER, {
          targetUserName,
        });
      }

      return mhttp.lgd.reportBadChat(user.userId, 0, reasonCd, chatMsg, addInfo).then((ret) => {
        user.sendJsonPacket(packet.seqNum, 0, { resultCode: MErrorCode.OK });
      });
    });
  }
}
