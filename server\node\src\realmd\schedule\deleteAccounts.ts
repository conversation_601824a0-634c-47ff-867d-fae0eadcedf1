// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import schedule from 'node-schedule';
import Container from 'typedi';
import mhttp from '../../motiflib/mhttp';
import { Promise as promise } from 'bluebird';
import _ from 'lodash';

import mlog from '../../motiflib/mlog';
import * as mutil from '../../motiflib/mutil';
import { getUserDbShardId } from '../../mysqllib/mysqlUtil';
import puDeleteUser from '../../mysqllib/sp/puDeleteUser';
import puUserLoadName from '../../mysqllib/sp/puUserLoadName';
import pwDeleteUser from '../../mysqllib/sp/pwDeleteUser';
import { RealmService } from '../server';
import { ArenaUtil } from '../../motiflib/model/lobby/arenaUtil';
import cms from '../../cms';
import { GetFullWeeksUsingLocalTime } from '../../formula';
import puFriendLoad from '../../mysqllib/sp/puFriendLoad';
import puFriendDelete from '../../mysqllib/sp/puFriendDelete';
import puHealth from '../../mysqllib/sp/puHealth';
import { RANKING_CMS_ID } from '../../cms/rankingDesc';

// 디비에서 탈퇴한 유저 정보 삭제
// https://developer.line.games/pages/viewpage.action?pageId=18186624

function _delete(
  pubId: string,
  successNidList: string[],
  failNidList: {
    nid: string;
    reasonCd: string;
  }[]
): Promise<void> {
  const app = Container.get(RealmService);
  const {
    userDbConnPoolMgr,
    worldDbConnPool,
    userRedis,
    arenaRedis,
    authRedis,
    townRedis,
    rankingRedis,
  } = app;
  let userIds: number[] = [];
  const names: string[] = [];

  let step = 0;
  const curTimeUtc = mutil.curTimeUtc();
  return mhttp.authd
    .getUserIdsByPubId(pubId)
    .then((ret) => {
      step = 1;
      if (!ret) {
        return null;
      }
      userIds = ret;
    })
    .then(() => {
      step = 2;
      // 상대방 친구목록에서 탈퇴유저 제거
      const promises = [];
      for (const userId of userIds) {
        const userDbShardId = getUserDbShardId(userId);
        // 먼저 유저의 친구 목록을 조회.
        promises.push(
          puFriendLoad(userDbConnPoolMgr.getPoolByShardId(userDbShardId), userId)
            .then((friends) => {
              const delFriendPromises = [];
              if (friends) {
                // 상대방의 친구목록에서 탈퇴유저를 제거.
                friends.forEach((elem) => {
                  const friendUserDbShardId = getUserDbShardId(elem.friendUserId);
                  delFriendPromises.push(
                    puFriendDelete(
                      userDbConnPoolMgr.getPoolByShardId(friendUserDbShardId),
                      elem.friendUserId,
                      userId
                    ).catch((err) => {
                      mlog.error('puFriendDelete at the deleteAccounts is failed', {
                        pubId,
                        userId,
                        friendUserDbShardId,
                        err: err.message,
                        stack: err.stack,
                      });
                    })
                  );
                });
              }

              return Promise.all(delFriendPromises);
            })
            .catch((err) => {
              mlog.error('puFriendLoad at the deleteAccounts is failed', {
                pubId,
                userId,
                err: err.message,
                stack: err.stack,
              });
            })
        );
      }
      return Promise.all(promises);
    })
    .then(() => {
      step = 3;
      const promises = [];
      for (const userId of userIds) {
        const userDbShardId = getUserDbShardId(userId);
        promises.push(
          puUserLoadName(userDbConnPoolMgr.getPoolByShardId(userDbShardId), userId).then((name) => {
            if (name) {
              names.push(name);
            }
          })
        );
      }
      return Promise.all(promises);
    })
    .then(() => {
      step = 4;
      // user db 에서 삭제.
      const promises = [];
      for (const userId of userIds) {
        mlog.info('Deleting user at user db...', { pubId, userId });
        const userDbShardId = getUserDbShardId(userId);
        promises.push(puDeleteUser(userDbConnPoolMgr.getPoolByShardId(userDbShardId), userId));
      }
      return Promise.all(promises);
    })
    .then(() => {
      step = 5;
      // world db 에서 삭제.
      const promises = [];
      for (const userId of userIds) {
        mlog.info('Deleting user at world db...', { pubId, userId });
        promises.push(pwDeleteUser(worldDbConnPool.getPool(), userId));
      }
      return Promise.all(promises);
    })
    .then(() => {
      step = 6;
      // authd 에 요청.
      mlog.info('Deleting user at auth db...', { pubId });
      return mhttp.authd.deleteAccount(pubId);
    })
    .then(() => {
      step = 7;
      // user redis
      if (names.length > 0) {
        mlog.info('Deleting user at user redis...', { pubId, userIds, names });
        return userRedis['deleteUsers'](JSON.stringify(names));
      }
    })
    .then(() => {
      step = 8;
      // town redis
      const weeklySessionId = GetFullWeeksUsingLocalTime(
        curTimeUtc,
        cms.Define.InvestmentWeeklySessionPivotDay
      );
      const townCmsIds = JSON.stringify(Object.keys(cms.Town));

      for (const userId of userIds) {
        mlog.info('Deleting user at town redis...', { pubId, userId });

        return townRedis['deleteUser'](userId, townCmsIds, weeklySessionId);
      }
    })
    .then(() => {
      step = 9;
      // arena redis
      const sessionId = ArenaUtil.getCurrentSessionId(curTimeUtc);
      const promises = [];
      for (const userId of userIds) {
        mlog.info('Deleting user at arena redis...', { pubId, userId });
        promises.push(arenaRedis['removeRevokeUser'](userId, sessionId));
      }
      return Promise.all(promises);
    })
    .then(() => {
      step = 10;
      // authRedis deletionPubIds 에서 제거
      return authRedis['deleteDeletionPubId'](pubId);
    })
    .then(() => {
      step = 11;
      // rankingRedis 에서 제거
      const promises = [];
      for (const userId of userIds) {
        mlog.info('Deleting user at ranking redis...', { pubId, userId });
        promises.push(rankingRedis['removeUserAllRankings'](userId));
      }
      return Promise.all(promises);
    })
    .then(() => {
      step = 12;
      successNidList.push(pubId);
    })
    .catch((err) => {
      mlog.alert('deleteAccounts is failed', {
        pubId,
        step,
        err: err.message,
        stack: err.stack,
      });
      failNidList.push({ nid: pubId, reasonCd: 'NOT_EXIST_DATA' });
    });
}

const delay = (ms) => {
  return new Promise((resolve) =>
    setTimeout(() => {
      resolve(ms);
    }, ms)
  );
};

// 간혈적 timeout 예방.
function knock(): Promise<any> {
  const app = Container.get(RealmService);
  const { userDbConnPoolMgr } = app;
  const promises = [];
  _.forOwn(userDbConnPoolMgr.getDbConnPools(), (conn) => {
    for (let i = 0; i < 10; i++) {
      promises.push(puHealth(conn.getPool()));
    }
  });
  return Promise.all(promises).finally(() => {
    return mhttp.authd.knock();
  });
}

export function start(): schedule.Job {
  mlog.info('Scheduling job deleteAccounts...');

  //const cron = '*/30 * * * *';
  const cron = '* * * * *'; // for test every minute
  const job = schedule.scheduleJob(cron, (fireDate: Date) => {
    mlog.info(`Starting job deleteAccounts for ${fireDate}...`);

    const successNidList: string[] = [];
    const failNidList: {
      nid: string;
      reasonCd: string;
    }[] = [];

    const app = Container.get(RealmService);
    const { authRedis } = app;

    return authRedis['getDeletionPubId']().then((ret) => {
      if (ret) {
        const pubIds = JSON.parse(ret);
        return knock()
          .then(() => {
            return promise.each(pubIds, (pubId: string) => {
              return _delete(pubId, successNidList, failNidList).then(() => {
                return delay(1000);
              });
            });
          })
          .then(() => {
            mlog.info('Account is deleted.', { successNidList, failNidList });
            return mhttp.lgd.reportAccountDeletionResult(successNidList, failNidList);
          });
      }
    });
  });

  return job;
}
