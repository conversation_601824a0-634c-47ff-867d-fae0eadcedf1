DROP TABLE IF EXISTS `u_manufacture_progress`;
CREATE TABLE `u_manufacture_progress` (
  `userId` int NOT NULL,
  `roomCmsId` int NOT NULL,
  `slot` smallint NOT NULL,
  `recipeId` int NOT NULL,
  `startTimeUtc` datetime NOT NULL,
  `completionTimeUtc` datetime NOT NULL,
  `isGreatSuccess` tinyint(1) NOT NULL DEFAULT '0',
  `mateCmsIds` json DEFAULT NULL,
  PRIMARY KEY (`userId`, `roomCmsId`, `slot`),
  KEY `IDX_u_manufacture_progress__userId` (`userId`),
  KEY `IDX_u_manufacture_progress__roomCmsId` (`roomCmsId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;