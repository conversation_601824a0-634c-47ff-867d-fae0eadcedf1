// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import assert from 'assert';
import _ from 'lodash';
import { Container } from 'typedi';
import cms from '../cms';
import * as cmsEx from '../cms/ex';
import { RELIGION_BUFF_TYPE, WorldTargetType, ZoneType } from '../cms/ex';
import mconf from '../motiflib/mconf';
import { MError, MErrorCode } from '../motiflib/merror';
import mlog from '../motiflib/mlog';
import { OceanBuffSyncNub } from '../motiflib/model/ocean';
import { curTimeUtc } from '../motiflib/mutil';
import { CompanyStat } from '../motiflib/stat/companyStat';
import puWorldBuffCreate from '../mysqllib/sp/puWorldBuffCreate';
import puWorldBuffDelete from '../mysqllib/sp/puWorldBuffDelete';
import puWorldBuffUpdate from '../mysqllib/sp/puWorldBuffUpdate';
import * as proto from '../proto/lobby/proto';
import { Protocol } from '../proto/oceand-lobbyd/protocol';
import { LobbyService } from './server';
import { All, Sync } from './type/sync';
import { LoginInfo, User } from './user';
import { WorldActiveEffectApplier } from './worldActiveEffectApplier';
import UserFleets from './userFleets';
import { WorldBuffDesc, WORLD_BUFF_TICK_MODE_TYPE } from '../cms/worldBuffDesc';
import { UserManager } from './userManager';
import UserMates from './userMates';
import * as mutil from '../motiflib/mutil';
import { OCEAN_DOODAD_RESULT_FLAG } from '../motiflib/gameLog';
import { GAME_ENTER_STATE, GAME_STATE, GsUtil } from '../motiflib/model/lobby/gameState';
import { WorldBuffActiveEffect, WorldBuffSmall } from '../motiflib/model/lobby';
import { getGLogCoordinate } from '../cms/oceanCoordinate';
import { _PassiveSync } from './userPassives';
import Ship from './ship';
import * as displayNameUtil from '../motiflib/displayNameUtil';
import { stat } from 'fs';
import { SAIL_SPEED_STATS } from '../motiflib/model/ocean/sailingSpeed';
import { BattleType } from '../motiflib/model/lobby';
import { BattleUtil } from './battleUtil';

// ----------------------------------------------------------------------------
// 월드 버프 Nub 오브젝트.  (DB 데이터 그대로)
//
// 'groupNo': WorldBuff CMS 테이블의 'groupNo' 값.
//   cmsId 로 유추 가능하지만, 가지고 있는게 비용 대비 여러가지 편리하다.
// 'cmsId': WorldBuff 테이블의 CMS ID.
// 'targetId': 대상 ID.  이 값은 WorldBuff 테이블의, 'buffTargetType' 값에 따라서,
//   다르게 해석이 된다.
// 'sourceType': 버프 출처.
// 'stack': 중첩 개수.
// 'startTimeUtc': 버프 시작 시간.
// 'endTimeUtc': 버프 종료 시간.  (오프라인에도 지속시간 차감이 되는게 기획 의도.)
//
// ----------------------------------------------------------------------------

export interface WorldBuffNub {
  groupNo: number;
  cmsId: number;
  targetId: number;
  sourceType: cmsEx.WorldBuffSourceType;
  sourceId: number;
  stack: number;
  startTimeUtc: number;
  endTimeUtc: number;
  createdFromDB?: boolean; // 로그인시 DB에서 생성된 버프여부.
}

// ----------------------------------------------------------------------------
// 월드 버프.
//
// 'id': 유저에 한해서, 고유한 버프 ID.
// 'lastTickTimeUtc': 최근 tick 이 발동한 UTC 시간.
// 'nub': DB row 형태를 그대로 유지하는 버프 struct.
//
// ----------------------------------------------------------------------------
export interface WorldBuff {
  id?: number;
  lastTickTimeUtc: number;

  nub: WorldBuffNub;
}

// ----------------------------------------------------------------------------
// 클라에게 전달되는 버프 업데이트 패킷 body 인터페이스.
// ----------------------------------------------------------------------------

interface _BuffRemoveInfo {
  cmsId: number;
  targetId: number;
}

export interface BuffSync {
  sync?: Sync;
  removed?: _BuffRemoveInfo[]; // 삭제된 버프 정보들.

  activeEffects?: WorldBuffActiveEffect[]; // 버프로 발동한 액티브 효과들.
}

function _buffSyncMergeCustomizer(objValue: any, srcValue: any): any {
  if (_.isArray(objValue)) {
    return objValue.concat(srcValue);
  }
}

function _isEmptyBuffSync(buffSync: BuffSync): boolean {
  // added, updated, removed 는 딱히 체크 할 필요가 없다. ('sync' 만으로 판단 가능)
  if (!_.isEmpty(buffSync.sync)) {
    return false;
  }

  if (buffSync.activeEffects && buffSync.activeEffects.length > 0) {
    return false;
  }

  return true;
}

// ----------------------------------------------------------------------------
// 월드버프 변경 내역. ('updated' 에 추가된 버프도 첨부된)
// ----------------------------------------------------------------------------

// ----------------------------------------------------------------------------
// 월드버프 관련 헬퍼 함수들 모음.
// ----------------------------------------------------------------------------
export namespace WorldBuffUtil {
  // --------------------------------------------------------------------------
  // Religion CMS 테이블의, 'religionBuffType' 값을, 월드버프의 소스타입으로 변환.
  // --------------------------------------------------------------------------
  export function getSourceTypeFromReligionBuffType(
    religionBuffType: number
  ): cmsEx.WorldBuffSourceType {
    switch (religionBuffType) {
      case RELIGION_BUFF_TYPE.PRAYER:
        return cmsEx.WorldBuffSourceType.PRAYER;
      case RELIGION_BUFF_TYPE.DONATION:
        return cmsEx.WorldBuffSourceType.DONATION;
    }

    return null;
  }

  // --------------------------------------------------------------------------
  export function makeNub(
    buffCms: any,
    targetId: number,
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number,
    startTimeUtc: number
  ): WorldBuffNub {
    // duration 이 없는 버프는, 만료되지 않는 버프.
    const endTimeUtc = buffCms.duration ? startTimeUtc + buffCms.duration : cmsEx.TheEndTimeUtc;

    const wbNub: WorldBuffNub = {
      groupNo: buffCms.groupNo,
      cmsId: buffCms.id,
      targetId,
      sourceType,
      sourceId,
      stack: 1,
      startTimeUtc: startTimeUtc,
      endTimeUtc,
    };

    return wbNub;
  }

  // --------------------------------------------------------------------------
  export function makeNubWithCustomEndTime(
    buffCms: any,
    targetId: number,
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number,
    startTimeUtc: number,
    endTimeUtc: number
  ): WorldBuffNub {
    const wbNub: WorldBuffNub = {
      groupNo: buffCms.groupNo,
      cmsId: buffCms.id,
      targetId,
      sourceType,
      sourceId,
      stack: 1,
      startTimeUtc: startTimeUtc,
      endTimeUtc,
    };

    return wbNub;
  }

  // --------------------------------------------------------------------------
  export function makeNubWithCustomStackAndEndTime(
    buffCms: any,
    targetId: number,
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number,
    stack: number,
    startTimeUtc: number,
    endTimeUtc: number
  ): WorldBuffNub {
    const wbNub: WorldBuffNub = {
      groupNo: buffCms.groupNo,
      cmsId: buffCms.id,
      targetId,
      sourceType,
      sourceId,
      stack,
      startTimeUtc: startTimeUtc,
      endTimeUtc,
    };

    return wbNub;
  }

  // --------------------------------------------------------------------------
  // type에 따른 cms 반환 ex> cms.CashShop
  export function getSourceNameOfCmsByWorldBuffSourceType(
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number
  ): string {
    switch (sourceType) {
      case cmsEx.WorldBuffSourceType.PRAYER:
      case cmsEx.WorldBuffSourceType.DONATION:
        return cms.Religion[sourceId] ? cms.Religion[sourceId].typeName : null;
      case cmsEx.WorldBuffSourceType.USER_ITEM:
      case cmsEx.WorldBuffSourceType.POINT:
        return cms.Item[sourceId] ? displayNameUtil.getItemDisplayName(cms.Item[sourceId]) : null;
      case cmsEx.WorldBuffSourceType.DISASTER: // 재해 명칭
        return cms.OceanDisaster[sourceId] ? cms.OceanDisaster[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT:
        return null; // sourceId 구분 불가.
      case cmsEx.WorldBuffSourceType.HUNGER:
        return null; // sourceId 구분 불가.
      case cmsEx.WorldBuffSourceType.CASH_SHOP_BUY_WITHOUT_PURCHASE:
        return cms.CashShop[sourceId]
          ? displayNameUtil.getCashShopProductDisplayName(cms.CashShop[sourceId])
          : null;
      case cmsEx.WorldBuffSourceType.OCEAN_DOODAD_EFFECT:
        return cms.OceanDoodad[sourceId] ? cms.OceanDoodad[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.CHEAT:
        return null; // sourceId 구분 불가.
      case cmsEx.WorldBuffSourceType.PROTECTION:
        return cms.OceanProtection[sourceId] ? cms.OceanProtection[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.WORLD_TILE:
        return cms.WorldTile[sourceId] ? cms.WorldTile[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.MATE_SET:
        return cms.MateSet[sourceId] ? cms.MateSet[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.COMPANY_JOB:
        return cms.CompanyJob[sourceId] ? cms.CompanyJob[sourceId].desc : null;
      case cmsEx.WorldBuffSourceType.WEATHER:
        return cms.Weather[sourceId] ? cms.Weather[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.BATTLE_FORMATION:
        return cms.BattleFormation[sourceId] ? cms.BattleFormation[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.MATE_PASSIVE:
        return cms.WorldPassive[sourceId] ? cms.WorldPassive[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.WORLD_SKILL:
        return cms.WorldSkill[sourceId] ? cms.WorldSkill[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.ADDED_TICK_BUFF:
        return cms.WorldBuff[sourceId] ? cms.WorldBuff[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.COLLECTION_BUFF:
        return cms.Collection[sourceId] ? cms.Collection[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.ANXIETY:
        return cms.WorldBuff[sourceId] ? cms.WorldBuff[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.REVOLT:
        return cms.WorldBuff[sourceId] ? cms.WorldBuff[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.TOWING:
        return cms.WorldBuff[sourceId] ? cms.WorldBuff[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.LIVE_EVENT:
        return cms.LiveEvent[sourceId] ? cms.LiveEvent[sourceId].name : null;
      case cmsEx.WorldBuffSourceType.PET:
        return cms.Pet[sourceId] ? cms.Pet[sourceId].name : null;
    }
  }

  // --------------------------------------------------------------------------
  export function isDBSave(sourceType: cmsEx.WorldBuffSourceType): boolean {
    switch (sourceType) {
      case cmsEx.WorldBuffSourceType.DISASTER:
      case cmsEx.WorldBuffSourceType.OCEAN_DOODAD_EFFECT: // 해양오브젝트에의한 버프는 휘발성이다
      case cmsEx.WorldBuffSourceType.PROTECTION:
      case cmsEx.WorldBuffSourceType.WORLD_TILE:
      case cmsEx.WorldBuffSourceType.WEATHER:
      case cmsEx.WorldBuffSourceType.OCCUPIED_NATION:
      case cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT:
      case cmsEx.WorldBuffSourceType.HUNGER:
      case cmsEx.WorldBuffSourceType.BATTLE_FORMATION:
      case cmsEx.WorldBuffSourceType.MATE_PASSIVE:
      case cmsEx.WorldBuffSourceType.COLLECTION_BUFF:
      case cmsEx.WorldBuffSourceType.ANXIETY:
      case cmsEx.WorldBuffSourceType.REVOLT:
      case cmsEx.WorldBuffSourceType.COMPANY_LEVEL:
      case cmsEx.WorldBuffSourceType.TOWING:
      case cmsEx.WorldBuffSourceType.LIVE_EVENT:
      case cmsEx.WorldBuffSourceType.HOT_TIME_BUFF:
      case cmsEx.WorldBuffSourceType.USER_TITLE:
      case cmsEx.WorldBuffSourceType.GUILD_BUFF:
      case cmsEx.WorldBuffSourceType.NATION_EFFECT_PROMISE:
      case cmsEx.WorldBuffSourceType.PET:
      case cmsEx.WorldBuffSourceType.SMUGGLE:
      case cmsEx.WorldBuffSourceType.CLASH_BUFF:
        return false;
      default:
        return true;
    }
  }

  // --------------------------------------------------------------------------
  export function doesNeedToUpdateToClient(sourceType: cmsEx.WorldBuffSourceType): boolean {
    switch (sourceType) {
      case cmsEx.WorldBuffSourceType.OCEAN_DOODAD_EFFECT: // 해양오브젝트에의한 버프는 스킵한다
        return false;
      default:
        return true;
    }
  }
}

// ----------------------------------------------------------------------------
// 월드 버프 컨테이너.
//
// 워드 버프의 개별 대상 마다 존재하며,
// 버프의 그룹 / 스택 관리를 책임진다.
// ----------------------------------------------------------------------------

export class WorldBuffContainer {
  private _userBuffs: UserBuffs;
  private _worldBuffs: {
    [groupNo: number]: WorldBuff;
  };

  // --------------------------------------------------------------------------
  constructor(userBuffs: UserBuffs) {
    this._userBuffs = userBuffs;
    this._worldBuffs = {};
  }

  // --------------------------------------------------------------------------
  addBuffOnLogin(wb: WorldBuff): void {
    if (this._worldBuffs[wb.nub.groupNo]) {
      mlog.error('[buffmgr] Duplicate buff group on login!', {
        userId: this._userBuffs.getUserId(),
        groupNo: wb.nub.groupNo,
        cmsId: wb.nub.cmsId,
      });
      return;
    }

    this._worldBuffs[wb.nub.groupNo] = wb;
  }

  // --------------------------------------------------------------------------
  addBuffOnInit(newWb: WorldBuff): void {
    if (!this._worldBuffs[newWb.nub.groupNo]) {
      this._worldBuffs[newWb.nub.groupNo] = newWb;
      return;
    }

    const curWb = this._worldBuffs[newWb.nub.groupNo];
    const curBuffCms = cms.WorldBuff[curWb.nub.cmsId];
    const maxStack = curBuffCms.maxStack; // 1 이상의 값으로 보장 됨.
    if (curWb.nub.stack >= maxStack) {
      mlog.error('[buffmgr] buff-can-not-increase-stack-anymore-on-init!', {
        userId: this._userBuffs.getUserId(),
        groupNo: curWb.nub.groupNo,
        curBuffCmsId: curWb.nub.cmsId,
        stack: curWb.nub.stack,
        newBuffCmsId: newWb.nub.cmsId,
      });
    }
    curWb.nub.stack++;
  }

  // --------------------------------------------------------------------------
  // [WARN] 버프 데이터 홀더를 그대로 넘기는 함수.
  // 절대로 이 함수로 얻어온 데이터에 write 를 하지 않도록 주의!
  // --------------------------------------------------------------------------
  getBuffTable(): { [groupNo: number]: WorldBuff } {
    return this._worldBuffs;
  }

  // --------------------------------------------------------------------------
  getBuffByCmsId(cmsId: number): WorldBuff {
    for (const [, wb] of Object.entries(this._worldBuffs)) {
      if (wb.nub.cmsId === cmsId) {
        return wb;
      }
    }
    return null;
  }

  // --------------------------------------------------------------------------
  getBuffByGroup(groupNo: number): WorldBuff {
    return this._worldBuffs[groupNo];
  }

  // --------------------------------------------------------------------------
  getBuffsBySourceType(sourceType: cmsEx.WorldBuffSourceType): WorldBuff[] {
    const buffs: WorldBuff[] = [];
    for (const [, wb] of Object.entries(this._worldBuffs)) {
      if (wb.nub.sourceType === sourceType) {
        buffs.push(wb);
      }
    }
    return buffs;
  }

  // --------------------------------------------------------------------------
  getBuffsBySourceTypeAndSourceId(
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number
  ): WorldBuff[] {
    const buffs: WorldBuff[] = [];
    for (const [groupNo, wb] of Object.entries(this._worldBuffs)) {
      if (wb.nub.sourceType === sourceType && wb.nub.sourceId === sourceId) {
        buffs.push(wb);
      }
    }
    return buffs;
  }

  // --------------------------------------------------------------------------
  addBuff(wb: WorldBuff, user: User, buffSync?: BuffSync, isLoading?: boolean): void {
    const newBuffCms = cms.WorldBuff[wb.nub.cmsId];
    const groupNo = newBuffCms.groupNo;
    const curBuff = this._worldBuffs[groupNo];

    // 만약 같은 그룹의 버프가 없는 상태면, 추가하고 끝.
    if (!curBuff) {
      this._worldBuffs[groupNo] = wb;
      this._userBuffs.onBuffAdded(wb, user, buffSync, isLoading);
      return;
    }

    // 같은 그룹의 버프가 이미 있으면...
    const maxStack = newBuffCms.maxStack; // 1 이상의 값으로 보장 됨.
    const curBuffCms = cms.WorldBuff[curBuff.nub.cmsId];
    const curOrderNo = curBuffCms.groupOrder;
    const newOrderNo = newBuffCms.groupOrder;

    // 중첩이 불가능 한 경우, orderNo 비교해서, 크면 덮어쓰고, 낮으면 그냥 리턴.
    if (maxStack === 1) {
      if (curOrderNo > newOrderNo) {
        // 새로운 버프의 orderNo 가 낮으므로 무시.
        return;
      }

      // 기존 버프 갱신.
      curBuff.nub = wb.nub;
      this._userBuffs.onBuffUpdated(curBuff, user, buffSync);
      return;
    }

    // 현재 최대 스택이면 지속시간 갱신만 하고 리턴.
    if (curBuff.nub.stack >= maxStack) {
      curBuff.nub = wb.nub;
      this._userBuffs.onBuffUpdated(curBuff, user, buffSync);
      return;
    }

    // 스택 + 1.  (그리고 지속시간 갱신)
    const newStack = curBuff.nub.stack + 1;
    curBuff.nub = wb.nub;
    curBuff.nub.stack = newStack;
    this._userBuffs.onBuffUpdated(curBuff, user, buffSync);
  }

  // --------------------------------------------------------------------------
  // 이전 아이템 사용 로직을 유지하기 위해 임시로, 아이템 사용시 무조건 새로운 버프로 덮어쓴다.
  // --------------------------------------------------------------------------
  tempUpdateBuffWithItem(inBuff: WorldBuff, user: User, buffSync?: BuffSync): void {
    const buffCms = cms.WorldBuff[inBuff.nub.cmsId];
    const groupNo = buffCms.groupNo;
    const curBuff = this._worldBuffs[groupNo];

    if (curBuff) {
      // 이미 같은 그룹의 버프가 있으면 기존 버프를 갱신.
      curBuff.nub = inBuff.nub;
      this._userBuffs.onBuffUpdated(curBuff, user, buffSync);
      return;
    }

    // 없으면 추가.
    this._worldBuffs[groupNo] = inBuff;
    this._userBuffs.onBuffAdded(inBuff, user, buffSync);
  }

  // --------------------------------------------------------------------------
  removeBuffByGroupNo(
    groupNo: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    const deleted = this._worldBuffs[groupNo];
    if (!deleted) {
      mlog.warn('[buff] Cannot remove buff by groupNo', {
        userId: this._userBuffs.getUserId(),
        groupNo,
      });
      return;
    }

    delete this._worldBuffs[groupNo];
    this._userBuffs.onBuffRemoved(deleted, user, glogRsn, glogAddRsn, buffSync);
  }

  // --------------------------------------------------------------------------
  removeBuffsBySourceType(
    sourceType: cmsEx.WorldBuffSourceType,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null
  ): void {
    const buffSync: BuffSync = {
      sync: {},
    };

    for (const [groupNo, wb] of Object.entries(this._worldBuffs)) {
      if (wb.nub.sourceType === sourceType) {
        delete this._worldBuffs[groupNo];
        this._userBuffs.onBuffRemoved(wb, user, glogRsn, glogAddRsn, buffSync);
      }
    }

    if (!_.isEmpty(buffSync.sync)) {
      user.sendJsonPacket(0, proto.Common.BUFF_UPDATE_SC, buffSync);
    }
  }

  // --------------------------------------------------------------------------
  removeBuffsBySourceTypeAndSourceId(
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null
  ): void {
    const buffSync: BuffSync = {
      sync: {},
    };

    for (const [groupNo, wb] of Object.entries(this._worldBuffs)) {
      if (wb.nub.sourceType === sourceType && wb.nub.sourceId === sourceId) {
        delete this._worldBuffs[groupNo];
        this._userBuffs.onBuffRemoved(wb, user, glogRsn, glogAddRsn, buffSync);
      }
    }

    if (!_.isEmpty(buffSync.sync)) {
      user.sendJsonPacket(0, proto.Common.BUFF_UPDATE_SC, buffSync);
    }
  }

  // --------------------------------------------------------------------------
  removeAll(user: User, glogRsn: string, glogAddRsn: string = null, buffSync?: BuffSync): void {
    _.forOwn(this._worldBuffs, (wb) => {
      this._userBuffs.onBuffRemoved(wb, user, glogRsn, glogAddRsn, buffSync);
    });
    this._worldBuffs = {};
  }

  // --------------------------------------------------------------------------
  getReligionBuff(religionBuffType: RELIGION_BUFF_TYPE): WorldBuff {
    const sourceType = WorldBuffUtil.getSourceTypeFromReligionBuffType(religionBuffType);

    for (const [groupNo, wb] of Object.entries(this._worldBuffs)) {
      if (wb.nub.sourceType === sourceType) {
        return wb;
      }
    }

    return null;
  }

  // --------------------------------------------------------------------------
  gatherOceanSync(nubs: OceanBuffSyncNub[]): void {
    _.forOwn(this._worldBuffs, (wb) => {
      nubs.push({
        cmsId: wb.nub.cmsId,
        sourceType: wb.nub.sourceType,
        targetId: wb.nub.targetId,
        stack: wb.nub.stack,
        endTimeUtc: wb.nub.endTimeUtc,
      });
    });
  }

  // --------------------------------------------------------------------------
  hasWpeInBuff(attr: number): boolean {
    for (const [groupNo, wb] of Object.entries(this._worldBuffs)) {
      const worldbuffCms = cms.WorldBuff[wb.nub.cmsId];
      if (worldbuffCms && worldbuffCms.statEffect) {
        for (const se of worldbuffCms.statEffect) {
          if (se.Id === attr) {
            return true;
          }
        }
      }
    }
    return false;
  }

  // --------------------------------------------------------------------------
  tick(curTimeUtc: number, user: User): void {
    // tick 마다 하나의 패킷으로 동기화.
    const buffSync: BuffSync = {
      sync: {},
      activeEffects: [],
    };

    // 만료된 버프들 목록.
    const expiredBuffs: WorldBuff[] = [];

    _.forOwn(this._worldBuffs, (wb) => {
      if (curTimeUtc > wb.nub.endTimeUtc) {
        expiredBuffs.push(wb);
      }
    });

    if (expiredBuffs.length > 0) {
      for (const expiredBuff of expiredBuffs) {
        delete this._worldBuffs[expiredBuff.nub.groupNo];
        this._userBuffs.onBuffRemoved(expiredBuff, user, 'tick_duration_over', null, buffSync);
      }
    }

    // 활성화 된 버프들 틱.
    const worldBuffNubs: WorldBuffNub[] = [];
    for (const [id, wb] of Object.entries(this._worldBuffs)) {
      this._userBuffs.onBuffTick(curTimeUtc, wb, worldBuffNubs, user, buffSync);
    }

    for (const wb of worldBuffNubs) {
      user.userBuffs.addSingleBuff(
        wb.cmsId,
        wb.targetId,
        wb.sourceId,
        wb.sourceType,
        user,
        buffSync
      );
    }

    if (!_isEmptyBuffSync(buffSync)) {
      user.sendJsonPacket<BuffSync>(0, proto.Common.BUFF_UPDATE_SC, buffSync);
    }
  }
}

// ----------------------------------------------------------------------------
// User buff manager.
// ----------------------------------------------------------------------------
export class UserBuffs {
  private _userId;

  // 선단 버프.
  private _companyBuffContainer: WorldBuffContainer;

  // 함대 버프. (fleet index - 1 으로 인덱싱)
  private _fleetBuffContainers: {
    [fleetIndex: number]: WorldBuffContainer;
  };

  // 선박 버프. (shipId 로 인덱싱)
  private _shipBuffContainers: {
    [shipId: number]: WorldBuffContainer;
  };

  // 항해사 버프. (mateCmsId 로 인덱싱)
  private _mateBuffContainers: {
    [mateCmsId: number]: WorldBuffContainer;
  };

  // ID 생성기. (DB에 저장되는 정보가 아니라, 메모리상에서, 각 버프를 고유한 ID로 관리하기 위한)
  private _idGen: number;

  // 마지막으로 버프가 추가 또는 삭제된 시간
  private _lastAddOrRemoveTimeUtc: number;

  // --------------------------------------------------------------------------
  constructor() {
    this._companyBuffContainer = new WorldBuffContainer(this);

    this._fleetBuffContainers = {};

    this._shipBuffContainers = {};

    this._mateBuffContainers = {};

    this._idGen = 0;

    this._lastAddOrRemoveTimeUtc = 0;
  }

  // --------------------------------------------------------------------------
  getUserId(): number {
    return this._userId;
  }

  // --------------------------------------------------------------------------
  getLastAddOrRemoveTimeUtc(): number {
    return this._lastAddOrRemoveTimeUtc;
  }

  // --------------------------------------------------------------------------
  private _getNextId(): number {
    return ++this._idGen;
  }

  // --------------------------------------------------------------------------
  // DB로부터 받아온 버프가 실제 유효한지에 대한 검증
  // --------------------------------------------------------------------------
  private _isValid(
    user: User,
    wb: {
      groupNo: number;
      cmsId: number;
      targetId: number;
      sourceType: number;
      sourceId: number;
      stack: number;
      startTimeUtc: string;
      endTimeUtc: string;
    }
  ): boolean {
    // cms에 등록 유무
    const worldBuff: WorldBuffDesc = cms.WorldBuff[wb.cmsId];
    if (!worldBuff) {
      mlog.warn('buffCmsId-does-not-exist-in-cms-at-UserBuff.initWithLoginInfo', {
        userId: this._userId,
        worldBuff: wb,
      });
      return false;
    }
    // target 존재 유무
    if (WorldTargetType.SHIP === worldBuff.buffTargetType) {
      // 선박이 없을 경우
      if (!user.userFleets.getShip(wb.targetId)) {
        mlog.warn('buffTarget(ship)-does-not-exist-at-UserBuff.initWithLoginInfo', {
          userId: this._userId,
          worldBuff: wb,
        });
        return false;
      }
    } else if (WorldTargetType.MATE === worldBuff.buffTargetType) {
      // 항해사가 없을 경우
      if (!user.userMates.getMate(wb.targetId)) {
        mlog.warn('buffTarget(mate)-does-not-exist-at-UserBuff.initWithLoginInfo', {
          userId: this._userId,
          worldBuff: wb,
        });
        return false;
      }
    }

    return true;
  }
  // --------------------------------------------------------------------------
  // 로그인시에 버프와 패시브들을 초기화 한다.
  // 패시브의 특성상, 이 전에 유저의 함대들과 항해사들의 정보가 "먼저" 초기화 된
  // 상태이어야만 한다.
  // --------------------------------------------------------------------------
  initWithLoginInfo(loginInfo: LoginInfo, user: User): void {
    const app = Container.get(LobbyService);

    this._userId = loginInfo.userId;

    // 적용받는 버프리스트
    let validWorldBuffs: WorldBuff[] = [];

    for (const elem of loginInfo.worldBuffs) {
      if (!this._isValid(user, elem)) {
        // 유효하지않는 버프는 로그남기고 완전제거. 원인은 _isValid()함수내에 로그로 남김.
        puWorldBuffDelete(
          app.userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          this._userId,
          elem.groupNo,
          elem.targetId
        )
          .then(() => {
            mlog.error('invalid-buff-has-been-deleted-from-GameDB', {
              userId: this._userId,
              worldBuff: elem,
            });
          })
          .catch((e) => {
            mlog.error('[buff] buff delete query error at initWithLoginInfo', {
              userId: this._userId,
              cmsId: elem.cmsId,
              error: e.message,
            });
          });

        continue;
      }

      validWorldBuffs.push({
        id: this._getNextId(),
        lastTickTimeUtc: 0,
        nub: {
          groupNo: elem.groupNo,
          cmsId: elem.cmsId,
          targetId: elem.targetId,
          sourceType: elem.sourceType,
          sourceId: elem.sourceId,
          startTimeUtc: parseInt(elem.startTimeUtc, 10),
          endTimeUtc: parseInt(elem.endTimeUtc, 10),
          stack: elem.stack,
          createdFromDB: true,
        },
      });
    }

    this._initBuffsOnLogin(validWorldBuffs);
  }

  // --------------------------------------------------------------------------
  // 외부로부터 받아온 버프들를 초기세팅한다.
  // initWithLoginInfo함수와 다른 점은 동일한 버프가 추가될 경우 stack을 증가시켜준다.
  // --------------------------------------------------------------------------
  initOtherBuffs(wbNubs: WorldBuffNub[]): void {
    wbNubs.map((wbNub) => {
      const worldBuff: WorldBuff = {
        id: this._getNextId(),
        lastTickTimeUtc: 0,
        nub: wbNub,
      };
      const buffCms = cms.WorldBuff[worldBuff.nub.cmsId];
      const targetType = buffCms.buffTargetType;
      const targetId = worldBuff.nub.targetId;

      if (targetType === WorldTargetType.COMPANY) {
        this._companyBuffContainer.addBuffOnLogin(worldBuff);
      } else if (targetType === WorldTargetType.FLEET) {
        if (targetId < 1 || targetId > cms.Define.MaxFleetShipCount) {
          mlog.error('[userBuff] Invalid target ID!', {
            userId: this._userId,
            cmsId: worldBuff.nub.cmsId,
            targetType,
            targetId,
          });
          return;
        }

        const fleetBuffContainer = this._getFleetBuffContainer(targetId);
        fleetBuffContainer.addBuffOnInit(worldBuff);
      } else if (targetType === WorldTargetType.SHIP) {
        const shipBuffContainer = this._getShipBuffContainer(targetId);
        shipBuffContainer.addBuffOnInit(worldBuff);
      } else if (targetType === WorldTargetType.MATE) {
        const mateBuffContainer = this._getMateBuffContainer(targetId);
        mateBuffContainer.addBuffOnInit(worldBuff);
      } else {
        mlog.error('Invalid buff target type!', {
          userId: this._userId,
          buffCmsId: buffCms.id,
          targetType,
        });
      }
    });
  }

  // --------------------------------------------------------------------------
  _initBuffsOnLogin(worldBuffs: WorldBuff[]): void {
    for (const worldBuff of worldBuffs) {
      const buffCms = cms.WorldBuff[worldBuff.nub.cmsId];
      const targetType = buffCms.buffTargetType;
      const targetId = worldBuff.nub.targetId;

      // 대상 타입에 따라 적용.
      if (targetType === WorldTargetType.COMPANY) {
        this._companyBuffContainer.addBuffOnLogin(worldBuff);
      } else if (targetType === WorldTargetType.FLEET) {
        // 혹시 모를 상황에 대비..
        if (targetId < 1 || targetId > cms.Define.MaxFleetShipCount) {
          mlog.error('[userBuff] Invalid target ID!', {
            userId: this._userId,
            cmsId: worldBuff.nub.cmsId,
            targetType,
            targetId,
          });
          continue;
        }

        const fleetBuffContainer = this._getFleetBuffContainer(targetId);
        fleetBuffContainer.addBuffOnLogin(worldBuff);
      } else if (targetType === WorldTargetType.SHIP) {
        const shipBuffContainer = this._getShipBuffContainer(targetId);
        shipBuffContainer.addBuffOnLogin(worldBuff);
      } else if (targetType === WorldTargetType.MATE) {
        const mateBuffContainer = this._getMateBuffContainer(targetId);
        mateBuffContainer.addBuffOnLogin(worldBuff);
      } else {
        mlog.error('Invalid buff target type!', {
          userId: this._userId,
          buffCmsId: buffCms.id,
          targetType,
        });
      }
    }
  }

  // --------------------------------------------------------------------------
  getOceanSync(): OceanBuffSyncNub[] {
    const nubs: OceanBuffSyncNub[] = [];

    this._companyBuffContainer.gatherOceanSync(nubs);

    _.forOwn(this._fleetBuffContainers, (fbc) => {
      fbc.gatherOceanSync(nubs);
    });

    _.forOwn(this._shipBuffContainers, (sbc) => {
      sbc.gatherOceanSync(nubs);
    });

    _.forOwn(this._mateBuffContainers, (mbc) => {
      mbc.gatherOceanSync(nubs);
    });

    return nubs;
  }
  // --------------------------------------------------------------------------
  hasBuffByCmsId(targetId: number, cmsId: number): boolean {
    let buff = null;

    const buffTargetType = cms.WorldBuff[cmsId].buffTargetType;
    switch (buffTargetType) {
      case cmsEx.WorldTargetType.COMPANY:
        buff = this.getCompanyBuffByCmsId(cmsId);
        break;
      case cmsEx.WorldTargetType.FLEET:
        buff = this.getFleetBuffByCmsId(targetId, cmsId);
        break;
      case cmsEx.WorldTargetType.SHIP:
        buff = this.getShipBuffByCmsId(targetId, cmsId);
        break;
      case cmsEx.WorldTargetType.MATE:
        buff = this.getMateBuffByCmsId(targetId, cmsId);
        break;
    }

    if (buff) {
      return true;
    }
    return false;
  }

  // --------------------------------------------------------------------------
  private _getFleetBuffContainer(fleetIndex: number): WorldBuffContainer {
    if (mutil.isNotANumber(fleetIndex)) {
      throw new MError('Invalid fleet index for world buff container.', MErrorCode.INTERNAL_ERROR, {
        userId: this._userId,
        fleetIndex,
      });
    }

    if (!this._fleetBuffContainers[fleetIndex]) {
      this._fleetBuffContainers[fleetIndex] = new WorldBuffContainer(this);
    }

    return this._fleetBuffContainers[fleetIndex];
  }

  // --------------------------------------------------------------------------
  private _getShipBuffContainer(shipId: number): WorldBuffContainer {
    if (mutil.isNotANumber(shipId)) {
      throw new MError('Invalid ship ID for world buff container.', MErrorCode.INTERNAL_ERROR, {
        userId: this._userId,
        shipId,
      });
    }

    if (!this._shipBuffContainers[shipId]) {
      this._shipBuffContainers[shipId] = new WorldBuffContainer(this);
    }

    return this._shipBuffContainers[shipId];
  }

  // --------------------------------------------------------------------------
  private _getMateBuffContainer(mateCmsId: number): WorldBuffContainer {
    if (mutil.isNotANumber(mateCmsId)) {
      throw new MError('Invalid mate ID for world buff container.', MErrorCode.INTERNAL_ERROR, {
        userId: this._userId,
        mateCmsId,
      });
    }

    if (!this._mateBuffContainers[mateCmsId]) {
      this._mateBuffContainers[mateCmsId] = new WorldBuffContainer(this);
    }

    return this._mateBuffContainers[mateCmsId];
  }

  // --------------------------------------------------------------------------
  getCompanyBuffTable(): { [groupNo: number]: WorldBuff } {
    return this._companyBuffContainer.getBuffTable();
  }

  // --------------------------------------------------------------------------
  getFleetBuffTable(fleetIndex: number): { [groupNo: number]: WorldBuff } {
    return this._getFleetBuffContainer(fleetIndex).getBuffTable();
  }

  // --------------------------------------------------------------------------
  getShipBuffTable(shipId: number): { [groupNo: number]: WorldBuff } {
    return this._getShipBuffContainer(shipId).getBuffTable();
  }

  // --------------------------------------------------------------------------
  getMateBuffTable(mateCmsId: number): { [groupNo: number]: WorldBuff } {
    return this._getMateBuffContainer(mateCmsId).getBuffTable();
  }

  // --------------------------------------------------------------------------
  getCompanyBuffByCmsId(cmsId: number): WorldBuff {
    return this._companyBuffContainer.getBuffByCmsId(cmsId);
  }

  // --------------------------------------------------------------------------
  getFleetBuffByCmsId(fleetIndex: number, cmsId: number): WorldBuff {
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    return fleetBuffContainer.getBuffByCmsId(cmsId);
  }

  // --------------------------------------------------------------------------
  getShipBuffByCmsId(shipId: number, cmsId: number): WorldBuff {
    const shipBuffContainer = this._getShipBuffContainer(shipId);
    return shipBuffContainer.getBuffByCmsId(cmsId);
  }

  // --------------------------------------------------------------------------
  getMateBuffByCmsId(mateCmsId: number, cmsId: number): WorldBuff {
    const mateBuffContainer = this._getMateBuffContainer(mateCmsId);
    return mateBuffContainer.getBuffByCmsId(cmsId);
  }

  // --------------------------------------------------------------------------
  getCompanyBuffByGroup(groupNo: number): WorldBuff {
    // getCompanyBuffByGroup,getShipBuffByGroup 등 따로만들지 말고,
    // buffCms의 targetType을 기준을 찾아와서 하도록 하자 .
    return this._companyBuffContainer.getBuffByGroup(groupNo);
  }

  // --------------------------------------------------------------------------
  getCompanyBuffsBySourceType(sourceType: cmsEx.WorldBuffSourceType): WorldBuff[] {
    return this._companyBuffContainer.getBuffsBySourceType(sourceType);
  }

  // --------------------------------------------------------------------------
  getShipBuffByGroup(shipId: number, groupNo: number): WorldBuff {
    const shipBuffContainer = this._getShipBuffContainer(shipId);
    return shipBuffContainer.getBuffByGroup(groupNo);
  }

  // --------------------------------------------------------------------------
  getFleetBuffByGroup(fleetIndex: number, groupNo: number): WorldBuff {
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    return fleetBuffContainer.getBuffByGroup(groupNo);
  }

  // --------------------------------------------------------------------------
  getFleetBuffsBySourceType(
    fleetIndex: number,
    sourceType: cmsEx.WorldBuffSourceType
  ): WorldBuff[] {
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    return fleetBuffContainer.getBuffsBySourceType(sourceType);
  }

  // --------------------------------------------------------------------------
  getFleetBuffsBySourceTypeAndSourceId(
    fleetIndex: number,
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number
  ): WorldBuff[] {
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    return fleetBuffContainer.getBuffsBySourceTypeAndSourceId(sourceType, sourceId);
  }

  // --------------------------------------------------------------------------
  _addBuffImpl(wbNub: WorldBuffNub, targetType: number, user: User, buffSync?: BuffSync): void {
    if (user.userDevParams.debuffImmune === true) {
      const worldBuffCms = cms.WorldBuff[wbNub.cmsId];
      if (worldBuffCms.iconFrame === 1) {
        mlog.info('[CHEAT DEBUFF IMMUNE] worldbuff Skip', {
          userId: user.userId,
          cmsId: worldBuffCms.id,
        });
        return;
      }
    }
    switch (targetType) {
      case WorldTargetType.COMPANY:
        this.addCompanyBuff(wbNub, user, buffSync);
        break;
      case WorldTargetType.FLEET:
        this.addFleetBuff(wbNub.targetId, wbNub, user, buffSync);
        break;
      case WorldTargetType.SHIP:
        this.addShipBuff(wbNub.targetId, wbNub, user, buffSync);
        break;
      case WorldTargetType.MATE:
        this.addMateBuff(wbNub.targetId, wbNub, user, buffSync);
        break;
      default:
        throw new MError('Invalid buff target type.', MErrorCode.INVALID_BUFF_TARGET_TYPE, {
          userId: this._userId,
          targetType,
          buffCmsId: wbNub.cmsId,
        });
    }
  }

  // --------------------------------------------------------------------------
  addSingleBuff(
    cmsId: number,
    targetId: number,
    sourceId: number,
    sourceType: number,
    user: User,
    buffSync?: BuffSync
  ) {
    const buffCms = cms.WorldBuff[cmsId];
    if (!buffCms) {
      throw new MError('Invalid buff cms ID', MErrorCode.INVALID_BUFF_CMS_ID, {
        userId: this._userId,
        cmsId,
      });
    }

    const wbNub = WorldBuffUtil.makeNub(buffCms, targetId, sourceType, sourceId, curTimeUtc());
    this._addBuffImpl(wbNub, buffCms.buffTargetType, user, buffSync);
  }

  // --------------------------------------------------------------------------
  addSingleBuffByWBNub(wbNub: WorldBuffNub, user: User, buffSync?: BuffSync) {
    const buffCms = cms.WorldBuff[wbNub.cmsId];
    if (!buffCms) {
      throw new MError('Invalid buff cms ID', MErrorCode.INVALID_BUFF_CMS_ID, {
        userId: this._userId,
        cmsId: wbNub.cmsId,
      });
    }

    this._addBuffImpl(wbNub, buffCms.buffTargetType, user, buffSync);
  }

  // --------------------------------------------------------------------------
  removeBuff(
    cmsId: number,
    targetId: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    const buffCms = cms.WorldBuff[cmsId];
    if (!buffCms) {
      throw new MError('Invalid buff cms ID', MErrorCode.INVALID_BUFF_CMS_ID, {
        userId: this._userId,
        cmsId,
      });
    }

    this.removeBuffByGroupNo(
      buffCms.groupNo,
      buffCms.buffTargetType,
      targetId,
      user,
      glogRsn,
      glogAddRsn,
      buffSync
    );
  }

  // --------------------------------------------------------------------------
  addCompanyBuff(wbNub: WorldBuffNub, user: User, buffSync?: BuffSync): void {
    const wb: WorldBuff = {
      id: this._getNextId(),
      lastTickTimeUtc: 0,
      nub: wbNub,
    };

    this._companyBuffContainer.addBuff(wb, user, buffSync);
  }

  // --------------------------------------------------------------------------
  addFleetBuff(
    fleetIndex: number,
    wbNub: WorldBuffNub,
    user: User,
    buffSync?: BuffSync,
    isLoading?: boolean
  ): void {
    const wb: WorldBuff = {
      id: this._getNextId(),
      lastTickTimeUtc: 0,
      nub: wbNub,
    };

    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    fleetBuffContainer.addBuff(wb, user, buffSync, isLoading);
  }

  // --------------------------------------------------------------------------
  addShipBuff(shipId: number, wbNub: WorldBuffNub, user: User, buffSync?: BuffSync): void {
    const wb: WorldBuff = {
      id: this._getNextId(),
      lastTickTimeUtc: 0,
      nub: wbNub,
    };

    const shipBuffContainer = this._getShipBuffContainer(shipId);
    shipBuffContainer.addBuff(wb, user, buffSync);
  }

  // --------------------------------------------------------------------------
  addMateBuff(mateCmsId: number, wbNub: WorldBuffNub, user: User, buffSync?: BuffSync): void {
    const wb: WorldBuff = {
      id: this._getNextId(),
      lastTickTimeUtc: 0,
      nub: wbNub,
    };

    const mateBuffContainer = this._getMateBuffContainer(mateCmsId);
    mateBuffContainer.addBuff(wb, user, buffSync);
  }

  // --------------------------------------------------------------------------
  addDisasterBuff(
    shipId: number,
    disasterCmsId: number,
    buffCmsId: number,
    startTimeUtc: number,
    user: User,
    isLoading?: boolean
  ): void {
    const buffCms = cms.WorldBuff[buffCmsId];
    const wbNub: WorldBuffNub = {
      groupNo: buffCms.groupNo,
      cmsId: buffCmsId,
      targetId: shipId,
      sourceType: cmsEx.WorldBuffSourceType.DISASTER,
      sourceId: disasterCmsId,
      stack: 1,
      startTimeUtc,
      endTimeUtc: cmsEx.TheEndTimeUtc,
    };

    const wb: WorldBuff = {
      id: this._getNextId(),
      lastTickTimeUtc: 0,
      nub: wbNub,
    };

    const shipBuffContainer = this._getShipBuffContainer(shipId);
    shipBuffContainer.addBuff(wb, user, undefined, isLoading);
  }

  // --------------------------------------------------------------------------
  removeDisasterBuff(
    shipId: number,
    buffCmsId: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null
  ): void {
    // 딱히 재해 관련 처리가 필요 없어보이는데..
    const buffCms = cms.WorldBuff[buffCmsId];
    const shipBuffContainer = this._getShipBuffContainer(shipId);
    shipBuffContainer.removeBuffByGroupNo(buffCms.groupNo, user, glogRsn, glogAddRsn);
  }

  // --------------------------------------------------------------------------
  addProtectionBuff(
    fleetIndex: number,
    protectionCmsId: number,
    buffCmsId: number,
    startTimeUtc: number,
    user: User,
    isLoading?: boolean
  ): void {
    const buffCms = cms.WorldBuff[buffCmsId];
    const wbNub: WorldBuffNub = {
      groupNo: buffCms.groupNo,
      cmsId: buffCmsId,
      targetId: fleetIndex,
      sourceType: cmsEx.WorldBuffSourceType.PROTECTION,
      sourceId: protectionCmsId,
      stack: 1,
      startTimeUtc,
      endTimeUtc: cmsEx.TheEndTimeUtc,
    };

    this.addFleetBuff(fleetIndex, wbNub, user, undefined, isLoading);
  }

  // --------------------------------------------------------------------------
  removeProtectionBuff(fleetIndex: number, buffCmsId: number, user: User): void {
    const buffCms = cms.WorldBuff[buffCmsId];

    this.removeFleetBuffByGroupNo(fleetIndex, buffCms.groupNo, user, 'remove_protection');
  }

  // --------------------------------------------------------------------------
  // 이전 아이템 사용 로직을 유지하기 위해 임시로, 아이템 사용시 무조건 새로운 버프로 덮어쓴다.
  // (항상 1번 함대)
  // --------------------------------------------------------------------------
  tempUpdateBuffWithItem(wbNub: WorldBuffNub, user: User, buffSync?: BuffSync): void {
    const wb: WorldBuff = {
      id: this._getNextId(),
      lastTickTimeUtc: 0,
      nub: wbNub,
    };

    const fleetBuffContainer = this._getFleetBuffContainer(cmsEx.FirstFleetIndex);
    fleetBuffContainer.tempUpdateBuffWithItem(wb, user, buffSync);
  }

  // --------------------------------------------------------------------------
  removeBuffByGroupNo(
    groupNo: number,
    targetType: number,
    targetId: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    switch (targetType) {
      case WorldTargetType.COMPANY:
        this.removeCompanyBuffByGroupNo(groupNo, user, glogRsn, glogAddRsn, buffSync);
        break;
      case WorldTargetType.FLEET:
        this.removeFleetBuffByGroupNo(targetId, groupNo, user, glogRsn, glogAddRsn, buffSync);
        break;
      case WorldTargetType.SHIP:
        this.removeShipBuffByGroupNo(targetId, groupNo, user, glogRsn, glogAddRsn, buffSync);
        break;
      case WorldTargetType.MATE:
        this.removeMateBuffByGroupNo(targetId, groupNo, user, glogRsn, glogAddRsn, buffSync);
        break;
      default:
        throw new MError('Invalid buff target type. (rem)', MErrorCode.INVALID_BUFF_TARGET_TYPE, {
          userId: this._userId,
          targetType,
        });
    }
  }

  // --------------------------------------------------------------------------
  removeCompanyBuffByGroupNo(
    groupNo: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    this._companyBuffContainer.removeBuffByGroupNo(groupNo, user, glogRsn, glogAddRsn, buffSync);
  }

  // --------------------------------------------------------------------------
  removeFleetBuffByGroupNo(
    fleetIndex: number,
    groupNo: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    fleetBuffContainer.removeBuffByGroupNo(groupNo, user, glogRsn, glogAddRsn, buffSync);
  }

  // --------------------------------------------------------------------------
  removeShipBuffByGroupNo(
    shipId: number,
    groupNo: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    const shipBuffContainer = this._getShipBuffContainer(shipId);
    shipBuffContainer.removeBuffByGroupNo(groupNo, user, glogRsn, glogAddRsn, buffSync);
  }

  // --------------------------------------------------------------------------
  removeMateBuffByGroupNo(
    mateCmsId: number,
    groupNo: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    const mateBuffContainer = this._getMateBuffContainer(mateCmsId);
    mateBuffContainer.removeBuffByGroupNo(groupNo, user, glogRsn, glogAddRsn, buffSync);
  }

  // --------------------------------------------------------------------------
  removeCompanyBuffsBySourceType(
    sourceType: cmsEx.WorldBuffSourceType,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null
  ): void {
    this._companyBuffContainer.removeBuffsBySourceType(sourceType, user, glogRsn, glogAddRsn);
  }

  // --------------------------------------------------------------------------
  removeFleetBuffsBySourceType(
    fleetIndex: number,
    sourceType: cmsEx.WorldBuffSourceType,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null
  ): void {
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    fleetBuffContainer.removeBuffsBySourceType(sourceType, user, glogRsn, glogAddRsn);
  }

  // --------------------------------------------------------------------------
  removeBuffsBySourceType(
    sourceType: cmsEx.WorldBuffSourceType,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null
  ): void {
    const f = (c: WorldBuffContainer) => {
      c.removeBuffsBySourceType(sourceType, user, glogRsn, glogAddRsn);
    };

    // 선단 버프.
    f(this._companyBuffContainer);

    // 함대 버프
    _.forOwn(this._fleetBuffContainers, f);
    // 선박 버프
    _.forOwn(this._shipBuffContainers, f);
    // 항해사 버프
    _.forOwn(this._mateBuffContainers, f);
  }

  // --------------------------------------------------------------------------
  removeBuffsBySourceTypeAndSourceId(
    sourceType: cmsEx.WorldBuffSourceType,
    sourceId: number,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null
  ): void {
    const f = (c: WorldBuffContainer) => {
      c.removeBuffsBySourceTypeAndSourceId(sourceType, sourceId, user, glogRsn, glogAddRsn);
    };

    // 선단 버프.
    f(this._companyBuffContainer);

    // 함대 버프
    _.forOwn(this._fleetBuffContainers, f);
    // 선박 버프
    _.forOwn(this._shipBuffContainers, f);
    // 항해사 버프
    _.forOwn(this._mateBuffContainers, f);
  }

  // --------------------------------------------------------------------------
  getFleetReligionBuff(fleetIndex: number, religionBuffType: RELIGION_BUFF_TYPE): WorldBuff {
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIndex);
    return fleetBuffContainer.getReligionBuff(religionBuffType);
  }

  // --------------------------------------------------------------------------
  getFleetBattleParamWorldBuffs(fleetIdx: number, battleType: BattleType): WorldBuffSmall[] {
    const bpWorldBuffs: WorldBuffSmall[] = [];

    // 전투에서 의미 있는 함대의 버프들만 모은다.
    // 자식 선박들에게 전가될 battleBuffId 가 있거나,
    // 자식 선박들에게 전가될 childStatEffect 가 있거나,
    // 자식 선박들에게 전가될 childSpecStatExpModifer 가 있는 경우.
    // && WorldBuff.modeBitFlag 에서 허용하는 전투모드
    const fleetBuffContainer = this._getFleetBuffContainer(fleetIdx);
    const fleetBuffTable = fleetBuffContainer.getBuffTable();
    _.forOwn(fleetBuffTable, (wb: WorldBuff) => {
      // battleFormation은 ocean/loadComplete 나 ocean/arrive할때 버프를 추가했다 뺐다 하기 때문에 제외
      if (wb.nub.sourceType === cmsEx.WorldBuffSourceType.BATTLE_FORMATION) {
        return;
      }

      const wbCms = cms.WorldBuff[wb.nub.cmsId];
      if (
        (wbCms.battleBuffId ||
          (wbCms.childStatEffect && wbCms.childStatEffect.length > 0) ||
          wbCms.childSpecStatExpModifer) &&
        BattleUtil.isApplicableGameMode(battleType, wbCms.id, wbCms.modeBitFlag)
      ) {
        bpWorldBuffs.push({
          cmsId: wb.nub.cmsId,
          stack: wb.nub.stack,
        });
      }
    });

    return bpWorldBuffs;
  }

  // --------------------------------------------------------------------------
  getShipBattleParamWorldBuffs(shipId: number, battleType: BattleType): WorldBuffSmall[] {
    const bpWorldBuffs: WorldBuffSmall[] = [];

    const shipBuffContainer = this._getShipBuffContainer(shipId);
    const shipBuffTable = shipBuffContainer.getBuffTable();
    _.forOwn(shipBuffTable, (wb: WorldBuff) => {
      const wbCms = cms.WorldBuff[wb.nub.cmsId];
      if (BattleUtil.isApplicableGameMode(battleType, wbCms.id, wbCms.modeBitFlag)) {
        bpWorldBuffs.push({
          cmsId: wb.nub.cmsId,
          stack: wb.nub.stack,
        });
      }
    });

    return bpWorldBuffs;
  }

  // --------------------------------------------------------------------------
  getSyncData(): All {
    const sync: All = {
      worldBuffs: {},
    };

    // Buffs.
    const companyBuffTable = this._companyBuffContainer.getBuffTable();
    for (const [groupNo, wb] of Object.entries(companyBuffTable)) {
      sync.worldBuffs[wb.id] = wb.nub;
    }

    for (const [fleetIndex, buffMgr] of Object.entries(this._fleetBuffContainers)) {
      for (const [groupNo, wb] of Object.entries(buffMgr.getBuffTable())) {
        sync.worldBuffs[wb.id] = wb.nub;
      }
    }

    for (const [shipId, buffMgr] of Object.entries(this._shipBuffContainers)) {
      for (const [groupNo, wb] of Object.entries(buffMgr.getBuffTable())) {
        sync.worldBuffs[wb.id] = wb.nub;
      }
    }

    for (const [mateCmsId, buffMgr] of Object.entries(this._mateBuffContainers)) {
      for (const [groupNo, wb] of Object.entries(buffMgr.getBuffTable())) {
        sync.worldBuffs[wb.id] = wb.nub;
      }
    }

    return sync;
  }

  // --------------------------------------------------------------------------
  hasWorldPassiveEffectBuff(type: WorldTargetType, targetId: number, attr: number): boolean {
    let buffContainer: WorldBuffContainer = null;
    switch (type) {
      case WorldTargetType.COMPANY:
        buffContainer = this._companyBuffContainer;
        break;
      case WorldTargetType.FLEET:
        buffContainer = this._getFleetBuffContainer(targetId);
        break;
      case WorldTargetType.SHIP:
        buffContainer = this._getShipBuffContainer(targetId);
        break;
    }

    if (!buffContainer) {
      return false;
    }
    return buffContainer.hasWpeInBuff(attr);
  }

  // --------------------------------------------------------------------------
  tick(curTimeUtc: number, user: User): void {
    if (user.userState.isInTownBattle() || user.userState.isInOceanBattle()) {
      return;
    }
    if (user.userState.getGameEnterState() === GAME_ENTER_STATE.ENTERING) {
      return;
    }

    this._companyBuffContainer.tick(curTimeUtc, user);

    _.forOwn(this._fleetBuffContainers, (fleetBuffContainer) => {
      fleetBuffContainer.tick(curTimeUtc, user);
    });

    _.forOwn(this._shipBuffContainers, (shipBuffContainer) => {
      shipBuffContainer.tick(curTimeUtc, user);
    });

    _.forOwn(this._mateBuffContainers, (mateBuffContainer) => {
      mateBuffContainer.tick(curTimeUtc, user);
    });
  }

  // --------------------------------------------------------------------------
  private _updateStatBuff(
    wb: WorldBuff,
    companyStat: CompanyStat,
    userFleets: UserFleets,
    userMates: UserMates
  ): void {
    const wbGroupNo = wb.nub.groupNo;
    const stack = wb.nub.stack;
    const buffCms = cms.WorldBuff[wb.nub.cmsId];

    if (buffCms.buffTargetType === WorldTargetType.COMPANY) {
      companyStat.chg_UpdateWorldBuff(wbGroupNo, wb.nub.cmsId, stack);
    } else if (buffCms.buffTargetType === WorldTargetType.FLEET) {
      const fleetStat = companyStat.getFleetStat(wb.nub.targetId);
      fleetStat.chg_UpdateWorldBuff(wbGroupNo, wb.nub.cmsId, stack);
    } else if (buffCms.buffTargetType === WorldTargetType.SHIP) {
      const ship = userFleets.getShip(wb.nub.targetId);
      const shipStat = ship.getStat(companyStat);
      shipStat.chg_UpdateWorldBuff(wbGroupNo, wb.nub.cmsId, stack);
    } else if (buffCms.buffTargetType === WorldTargetType.MATE) {
      const mate = userMates.getMate(wb.nub.targetId);
      if (mate) {
        const mateStat = mate.getStat(companyStat);
        mateStat.chg_UpdateWorldBuff(wbGroupNo, wb.nub.cmsId, stack);
      }
    } else {
      assert(false);
    }
  }

  // --------------------------------------------------------------------------
  private _removeBuffFromStat(
    wb: WorldBuff,
    companyStat: CompanyStat,
    userFleets: UserFleets,
    userMates: UserMates
  ): void {
    const wbGroupNo = wb.nub.groupNo;
    const buffCms = cms.WorldBuff[wb.nub.cmsId];

    if (buffCms.buffTargetType === WorldTargetType.COMPANY) {
      companyStat.chg_RemoveWorldBuff(wbGroupNo);
    } else if (buffCms.buffTargetType === WorldTargetType.FLEET) {
      const fleetStat = companyStat.getFleetStat(wb.nub.targetId);
      fleetStat.chg_RemoveWorldBuff(wbGroupNo);
    } else if (buffCms.buffTargetType === WorldTargetType.SHIP) {
      const ship = userFleets.getShip(wb.nub.targetId);
      const shipStat = ship.getStat(companyStat);
      shipStat.chg_RemoveWorldBuff(wbGroupNo);
    } else if (buffCms.buffTargetType === WorldTargetType.MATE) {
      const mate = userMates.getMate(wb.nub.targetId);
      const mateStat = mate.getStat(companyStat);
      mateStat.chg_RemoveWorldBuff(wbGroupNo);
    } else {
      assert(false);
    }
  }

  // --------------------------------------------------------------------------
  private _syncUpdateInOcean(
    wb: WorldBuff,
    gameState: GAME_STATE,
    gameEnterState: GAME_ENTER_STATE
  ): void {
    if (!GsUtil.isInOcean(gameState)) {
      return;
    }
    if (gameEnterState === GAME_ENTER_STATE.ENTERING) {
      return;
    }
    const sendp = new Protocol.LB2OC_NTF_BUFF_SYNC_UPDATE();
    sendp.userId = this._userId;
    sendp.obsn = {
      cmsId: wb.nub.cmsId,
      sourceType: wb.nub.sourceType,
      targetId: wb.nub.targetId,
      stack: wb.nub.stack,
      endTimeUtc: wb.nub.endTimeUtc,
    };

    const userManager = Container.get(UserManager);
    const user = userManager.getUserByUserId(this._userId);
    if (user) {
      const { tcpClientSessionManager } = Container.get(LobbyService);

      const url = user.getZoneInfo(ZoneType.OCEAN);
      if (url) {
        tcpClientSessionManager.send(url, sendp);
      }
    }
  }

  // --------------------------------------------------------------------------
  private _syncRemoveInOcean(
    wb: WorldBuff,
    gameState: GAME_STATE,
    gameEnterState: GAME_ENTER_STATE
  ): void {
    if (!GsUtil.isInOcean(gameState)) {
      return;
    }
    if (gameEnterState === GAME_ENTER_STATE.ENTERING) {
      return;
    }

    const sendp = new Protocol.LB2OC_NTF_BUFF_SYNC_REMOVE();
    sendp.userId = this._userId;
    sendp.buffCmsId = wb.nub.cmsId;
    sendp.targetId = wb.nub.targetId;
    sendp.sourceType = wb.nub.sourceType;

    const userManager = Container.get(UserManager);
    const user = userManager.getUserByUserId(this._userId);
    if (user) {
      const { tcpClientSessionManager } = Container.get(LobbyService);

      tcpClientSessionManager.send(user.getZoneInfo(ZoneType.OCEAN), sendp);
    }
  }

  // --------------------------------------------------------------------------
  // glog functions
  // --------------------------------------------------------------------------
  private _glogOceanDoodadBuff(wb: WorldBuff, user: User) {
    const location = user.getCurLocation();
    const coordinates = getGLogCoordinate(location);
    user.glog('ocean_doodad', {
      rsn: 'ocean_doodad_triggered',
      add_rsn: null,
      region_id: user.getCurRegionCmsId(),
      coordinates,
      id: wb.nub.sourceId,
      name: cms.OceanDoodad[wb.nub.sourceId].name,
      result_flag: `${OCEAN_DOODAD_RESULT_FLAG[OCEAN_DOODAD_RESULT_FLAG.BUFF]}`,
      reward_data: null,
      buff_id: wb.nub.cmsId,
      quest_id: null,
      disaster_id: null,
    });
  }

  // --------------------------------------------------------------------------
  // BI에 문의 결과 등록시점이 유저의 액션에 의해 명확한 것들만 우선 남기도록 처리 (220627)
  // 차후 등록 시점이 로그인시와 해양 입장/퇴장시 인 것들은 따로 분리할지 BI에서 가이드 받을 예정
  // --------------------------------------------------------------------------
  private _isBuffGlogSourceType(wb: WorldBuff): boolean {
    switch (wb.nub.sourceType) {
      case cmsEx.WorldBuffSourceType.PRAYER:
      case cmsEx.WorldBuffSourceType.DONATION:
      case cmsEx.WorldBuffSourceType.USER_ITEM:
      case cmsEx.WorldBuffSourceType.DISASTER:
      case cmsEx.WorldBuffSourceType.CASH_SHOP_BUY_WITHOUT_PURCHASE:
      case cmsEx.WorldBuffSourceType.POINT:
      case cmsEx.WorldBuffSourceType.CHEAT:
      case cmsEx.WorldBuffSourceType.PROTECTION:
      case cmsEx.WorldBuffSourceType.WORLD_TILE:
      case cmsEx.WorldBuffSourceType.WEATHER:
      case cmsEx.WorldBuffSourceType.WORLD_SKILL:
      case cmsEx.WorldBuffSourceType.COLLECTION_BUFF:
      case cmsEx.WorldBuffSourceType.REVOLT:
      case cmsEx.WorldBuffSourceType.TOWING:
      case cmsEx.WorldBuffSourceType.OCEAN_DOODAD_EFFECT: // 해양오브젝트에의한 버프는 휘발성이다
        return true;

      case cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT: // 해양 입장/퇴장 시점형
      case cmsEx.WorldBuffSourceType.HUNGER: // 해양 입장/퇴장 시점형
      case cmsEx.WorldBuffSourceType.COMPANY_JOB: // 로그인 시점형
      case cmsEx.WorldBuffSourceType.COMPANY_LEVEL: // 로그인 시점형
      case cmsEx.WorldBuffSourceType.OCCUPIED_NATION: // 해양 입장/퇴장 시점형
      case cmsEx.WorldBuffSourceType.MATE_INJURY: // 해양 입장/퇴장 시점형
      case cmsEx.WorldBuffSourceType.ANXIETY: // 해양 입장/퇴장 시점형
      case cmsEx.WorldBuffSourceType.BATTLE_FORMATION: // 해양 입장/퇴장 시점형
      case cmsEx.WorldBuffSourceType.MATE_PASSIVE: // 해양 입장/퇴장 시점형
      case cmsEx.WorldBuffSourceType.ADDED_TICK_BUFF: // 틱주기
      case cmsEx.WorldBuffSourceType.MATE_SLOWDOWN: // 로그인 시점형
      case cmsEx.WorldBuffSourceType.LIVE_EVENT: // 로그인 시점형
        return false;

      default:
        return false;
    }
  }

  // --------------------------------------------------------------------------
  private _glogBuffAdded(wb: WorldBuff, user: User) {
    if (!this._isBuffGlogSourceType(wb)) {
      return;
    }

    // 별도의 추가로그 남기기(해양오브젝트에의한 버프는 휘발성이다)
    if (cmsEx.WorldBuffSourceType.OCEAN_DOODAD_EFFECT === wb.nub.sourceType) {
      this._glogOceanDoodadBuff(wb, user);
    }

    // 버프 로그
    const glog_rsn = `world_buff_added_from_${cmsEx.WorldBuffSourceType[wb.nub.sourceType]}`;
    const buffCms = cms.WorldBuff[wb.nub.cmsId];
    user.glog('buff', {
      rsn: glog_rsn.toLowerCase(),
      add_rsn: null,
      flag: 1, // 생성
      id: wb.nub.cmsId,
      source_type: wb.nub.sourceType,
      source_id: wb.nub.sourceId,
      duration: buffCms.duration ? buffCms.duration : null,
      target_type: buffCms.buffTargetType,
      target_id: wb.nub.targetId,
      effect_id: buffCms.initializeEffectId ? buffCms.initializeEffectId : null,
      effect_value: buffCms.initializeEffectVal ? buffCms.initializeEffectVal : null,
    });

    return;
  }

  // --------------------------------------------------------------------------
  private _glogBuffRemoved(
    wb: WorldBuff,
    user: User,
    buffCms: WorldBuffDesc,
    glogRsn: string,
    glogAddRsn: string = null
  ) {
    if (!this._isBuffGlogSourceType(wb)) {
      return;
    }

    // 버프 로그
    // 치트의 경우 glog 기록 안함.
    if (glogRsn) {
      user.glog('buff', {
        rsn: glogRsn,
        add_rsn: glogAddRsn,
        flag: 3, // 소멸
        id: wb.nub.cmsId,
        source_type: wb.nub.sourceType,
        source_id: wb.nub.sourceId,
        duration: buffCms.duration ? buffCms.duration : null,
        target_type: buffCms.buffTargetType,
        target_id: wb.nub.targetId,
        effect_id: buffCms.finalizeEffectId ? buffCms.finalizeEffectId : null,
        effect_value: buffCms.finalizeEffectVal ? buffCms.finalizeEffectVal : null,
      });
    }
  }

  // --------------------------------------------------------------------------
  // 버프가 추가되었을때 호출
  // --------------------------------------------------------------------------
  onBuffAdded(wb: WorldBuff, user: User, buffSync?: BuffSync, isLoading?: boolean): void {
    // mlog.verbose('[buff] Buff added.', {
    //   userId: this._userId,
    //   cmsId: wb.nub.cmsId,
    //   targetId: wb.nub.targetId,
    // });

    // 타겟에 본 버프의 "statEffect" 를 더한다.
    this._updateStatBuff(wb, user.companyStat, user.userFleets, user.userMates);

    // Sync.
    const body: BuffSync = {
      sync: {
        add: {
          worldBuffs: {
            [wb.id]: wb.nub,
          },
        },
      },
    };

    // Update DB. (async)
    const buffCms = cms.WorldBuff[wb.nub.cmsId];
    if (WorldBuffUtil.isDBSave(wb.nub.sourceType)) {
      if (buffCms.saveToDb) {
        const app = Container.get(LobbyService);
        const userDbConnPoolMgr = app.userDbConnPoolMgr;
        puWorldBuffCreate(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          this._userId,
          wb.nub
        ).catch((e) => {
          mlog.error('[buff] buff create query error', {
            userId: this._userId,
            cmsId: wb.nub.cmsId,
            error: e.message,
            stack: mconf.isDev ? undefined : e.stack,
          });
        });
      }
    }

    // initializeEffect 처리.
    if (
      !isLoading &&
      WorldActiveEffectApplier.applicable(
        user,
        buffCms.id,
        buffCms.initializeEffectId,
        buffCms.initializeEffectVal,
        wb.nub.sourceType
      )
    ) {
      WorldActiveEffectApplier.applyBuffEffect(
        user,
        buffCms.buffTargetType,
        wb.nub.targetId,
        wb.nub,
        buffCms.initializeEffectId,
        buffCms.initializeEffectVal,
        buffSync
      );
    }

    // buffSync 를 동기화 하는 경우엔, 결과를 누적만 시키고,
    // 그 외의 경우엔, 유저에게 동기화 패킷을 보낸다.
    if (buffSync) {
      _.mergeWith<BuffSync, BuffSync>(buffSync, body, _buffSyncMergeCustomizer);
    } else {
      user.sendJsonPacket<BuffSync>(0, proto.Common.BUFF_UPDATE_SC, body);
    }

    this._lastAddOrRemoveTimeUtc = mutil.curTimeUtc();

    this._updateSailSpeedIfSpeedBuff(user, wb);

    this._syncUpdateInOcean(wb, user.userState.getGameState(), user.userState.getGameEnterState());

    // 항해 일지에는 굶주림 버프의 시작/종료만 남긴다
    if (wb.nub.sourceType === cmsEx.WorldBuffSourceType.HUNGER) {
      user.userSailingDiaries.addDiaryOceanBuffStart(user, wb.nub.cmsId);
    }

    // glog
    this._glogBuffAdded(wb, user);
  }

  // --------------------------------------------------------------------------
  // 버프가 업데이트 되었을때 호출
  // --------------------------------------------------------------------------
  onBuffUpdated(wb: WorldBuff, user: User, buffSync?: BuffSync): void {
    // mlog.verbose('[buff] Buff updated.', {
    //   userId: this._userId,
    //   cmsId: wb.nub.cmsId,
    //   targetId: wb.nub.targetId,
    // });

    // 타겟에 본 버프의 "statEffect" 를 업데이트.
    this._updateStatBuff(wb, user.companyStat, user.userFleets, user.userMates);

    if (WorldBuffUtil.doesNeedToUpdateToClient(wb.nub.sourceType)) {
      // Sync.
      const body: BuffSync = {
        sync: {
          add: {
            worldBuffs: {
              [wb.id]: wb.nub,
            },
          },
        },
      };

      if (buffSync) {
        _.mergeWith<BuffSync, BuffSync>(buffSync, body, _buffSyncMergeCustomizer);
      } else {
        user.sendJsonPacket(0, proto.Common.BUFF_UPDATE_SC, body);
      }
    }

    // Update DB. (async)
    // 재해로 인한 버프는, 재해 로직이 관리한다.
    const buffCms = cms.WorldBuff[wb.nub.cmsId];
    if (WorldBuffUtil.isDBSave(wb.nub.sourceType)) {
      if (buffCms.saveToDb) {
        const app = Container.get(LobbyService);
        puWorldBuffUpdate(
          app.userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          this._userId,
          wb.nub
        ).catch((e) => {
          mlog.error('[buff] buff update query error', {
            userId: this._userId,
            cmsId: wb.nub.cmsId,
            error: e.message,
            stack: mconf.isDev ? undefined : e.stack,
          });
        });
      }
    }

    this._lastAddOrRemoveTimeUtc = mutil.curTimeUtc();

    this._updateSailSpeedIfSpeedBuff(user, wb);

    this._syncUpdateInOcean(wb, user.userState.getGameState(), user.userState.getGameEnterState());
  }

  // --------------------------------------------------------------------------
  // 버프가 제거되었을때 호출
  // --------------------------------------------------------------------------
  onBuffRemoved(
    wb: WorldBuff,
    user: User,
    glogRsn: string,
    glogAddRsn: string = null,
    buffSync?: BuffSync
  ): void {
    // mlog.verbose('[buff] Buff removed.', {
    //   userId: this._userId,
    //   cmsId: wb.nub.cmsId,
    //   targetId: wb.nub.targetId,
    // });

    // 타겟에 본 버프의 "statEffect" 를 제거.
    this._removeBuffFromStat(wb, user.companyStat, user.userFleets, user.userMates);

    // Sync.
    // 버프가 제거된 경우에는, 해당 버프의 추가 정보를 알려준다.
    // (sync data 에서 지워진 데이터를 알 수가 없음)
    const body: BuffSync = {
      sync: {
        remove: {
          worldBuffs: {
            [wb.id]: true,
          },
        },
      },
      removed: [
        {
          cmsId: wb.nub.cmsId,
          targetId: wb.nub.targetId,
        },
      ],
    };

    if (buffSync) {
      _.mergeWith<BuffSync, BuffSync>(buffSync, body, _buffSyncMergeCustomizer);
    } else {
      user.sendJsonPacket(0, proto.Common.BUFF_UPDATE_SC, body);
    }

    const buffCms = cms.WorldBuff[wb.nub.cmsId];

    // Update DB. (async)
    let deleteToDB = false;
    if (wb.nub.createdFromDB) {
      deleteToDB = true;
    } else {
      if (WorldBuffUtil.isDBSave(wb.nub.sourceType)) {
        if (buffCms.saveToDb) {
          deleteToDB = true;
        }
      }
    }

    if (deleteToDB) {
      const app = Container.get(LobbyService);
      puWorldBuffDelete(
        app.userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        this._userId,
        wb.nub.groupNo,
        wb.nub.targetId
      ).catch((e) => {
        mlog.error('[buff] buff delete query error', {
          userId: this._userId,
          cmsId: wb.nub.cmsId,
          error: e.message,
        });
      });
    }

    // finalizeEffectId 처리.]
    if (
      WorldActiveEffectApplier.applicable(
        user,
        buffCms.id,
        buffCms.finalizeEffectId,
        buffCms.finalizeEffectVal,
        wb.nub.sourceType
      )
    ) {
      WorldActiveEffectApplier.applyBuffEffect(
        user,
        buffCms.buffTargetType,
        wb.nub.targetId,
        wb.nub,
        buffCms.finalizeEffectId,
        buffCms.finalizeEffectVal,
        buffSync
      );
    }

    this._syncRemoveInOcean(wb, user.userState.getGameState(), user.userState.getGameEnterState());

    this._lastAddOrRemoveTimeUtc = mutil.curTimeUtc();

    this._updateSailSpeedIfSpeedBuff(user, wb);

    // 항해 일지에는 굶주림 버프의 시작/종료만 남긴다
    if (wb.nub.sourceType === cmsEx.WorldBuffSourceType.HUNGER) {
      user.userSailingDiaries.addDiaryOceanBuffEnd(user, wb.nub.cmsId);
    }

    this._glogBuffRemoved(wb, user, buffCms, glogRsn, glogAddRsn);
  }

  // --------------------------------------------------------------------------
  _tickActiveEffect(secondsElapsed: number, wb: WorldBuff, user: User, buffSync: BuffSync): void {
    // 틱을 수행해야 하는지 판단.
    const buffCms = cms.WorldBuff[wb.nub.cmsId];
    switch (buffCms.tickModeType) {
      case WORLD_BUFF_TICK_MODE_TYPE.ALL:
        break;
      case WORLD_BUFF_TICK_MODE_TYPE.TOWN:
        if (!user.userState.isInTown()) {
          return;
        }
        if (user.userState.getGameEnterState() === GAME_ENTER_STATE.ENTERING) {
          return;
        }
        break;
      case WORLD_BUFF_TICK_MODE_TYPE.OCEAN:
        // TODO jaykay: only ocean?
        if (user.userState.getGameState() !== GAME_STATE.IN_OCEAN) {
          return;
        }
        break;
      default:
        // 기타의 값이 들어오면, 틱을 돌지 않는 버프로 간주한다.
        return;
    }

    if (user.questManager.bSingleMode) {
      // 퀘스트 싱글모드에서 재해로 인한 디버프 이펙트 적용은 안한다.
      if (wb.nub.sourceType === cmsEx.WorldBuffSourceType.DISASTER) {
        return;
      }

      // 퀘스트 싱글모드에서 굶주림 디버프 이펙트(물빵고갈로 인한 피해) 적용은 안한다.
      if (buffCms.id === cms.Const.HungerId.value) {
        return;
      }
    }

    // 이펙트들을 돌면서 버프가 활성화 된 이후 지나간 시간을 기반으로 틱 발동.
    for (const tae of buffCms.tickActiveEffect) {
      if (!tae.Value || !tae.Id) {
        continue;
      }

      if (secondsElapsed % tae.Interval !== 0) {
        continue;
      }

      WorldActiveEffectApplier.applyBuffEffect(
        user,
        buffCms.buffTargetType,
        wb.nub.targetId,
        wb.nub,
        tae.Id,
        tae.Value,
        buffSync
      );
    }
  }

  onRemoveShip(user: User, shipId: number, sync: Sync): void {
    const glogRsn = 'remove_ship';
    const glogAddRsn = undefined;
    const shipBuffContainer = this._getShipBuffContainer(shipId);

    const buffSync: BuffSync = {
      sync: {},
    };

    shipBuffContainer.removeAll(user, glogRsn, glogAddRsn, buffSync);
    _.merge<Sync, Sync>(sync, buffSync.sync);
  }

  // --------------------------------------------------------------------------
  private _updateSailSpeedIfSpeedBuff(user: User, wb: WorldBuff): void {
    const gameState: GAME_STATE = user.userState.getGameState();
    if (!GsUtil.isInOcean(gameState)) {
      return;
    }

    const gameEnterState: GAME_ENTER_STATE = user.userState.getGameEnterState();
    if (gameEnterState === GAME_ENTER_STATE.ENTERING) {
      return;
    }

    const sailState = user.userSailing.getSailState();
    if (!sailState) {
      return;
    }

    const worldBuffCms = cms.WorldBuff[wb.nub.cmsId];
    if (!worldBuffCms) {
      return;
    }

    // 항해속도에 반영되는 스탯이 있는 버프는 즉시 이동속도를 업데이트한다.
    const statEffectList = worldBuffCms.statEffect;
    if (statEffectList) {
      for (const stat of statEffectList) {
        if (SAIL_SPEED_STATS[stat.Id]) {
          sailState.updateSailSpeed(user, true);
          return;
        }
      }
    }
  }

  _tickAddBuff(secondsElapsed: number, wb: WorldBuff, addBuffs: WorldBuffNub[], user: User): void {
    // 추가 버프가 있어야한다.
    const buffCms = cms.WorldBuff[wb.nub.cmsId];
    if (!buffCms.addBuff || buffCms.addBuff.length === 0) {
      return;
    }

    // 항해모드에서만 적용.
    if (user.userState.getGameState() !== GAME_STATE.IN_OCEAN) {
      return;
    }

    //
    for (const ab of buffCms.addBuff) {
      if (secondsElapsed % ab.Interval !== 0) {
        continue;
      }
      if (ab.rate < mutil.randIntInc(1, 1000)) {
        continue;
      }

      const wbNub = WorldBuffUtil.makeNub(
        cms.WorldBuff[ab.id],
        wb.nub.targetId,
        cmsEx.WorldBuffSourceType.ADDED_TICK_BUFF,
        wb.nub.cmsId,
        curTimeUtc()
      );

      addBuffs.push(wbNub);
    }
  }

  // --------------------------------------------------------------------------
  _tickable(curTimeUtc: number, wb: WorldBuff, user: User): boolean {
    const buffCms = cms.WorldBuff[wb.nub.cmsId];

    // 임시로 정수형이 아닌 utc 값이 있는지 체크 (기존 코드에서 floor 가 되고 있었음.)
    if (!_.isInteger(wb.nub.startTimeUtc)) {
      throw new MError('Buff start time is not an integer.', MErrorCode.INTERNAL_ERROR, {
        userId: user.userId,
        wb,
      });
    }

    if (!buffCms.tickActiveEffect || buffCms.tickActiveEffect.length === 0) {
      return false;
    }

    const tickModeType = buffCms.tickModeType;
    if (!tickModeType) {
      return false;
    }

    return true;
  }
  // --------------------------------------------------------------------------
  // 버프 1개를 틱.
  // --------------------------------------------------------------------------
  onBuffTick(
    curTimeUtc: number,
    wb: WorldBuff,
    addBuffs: WorldBuffNub[],
    user: User,
    buffSync: BuffSync
  ): void {
    const buffCms = cms.WorldBuff[wb.nub.cmsId];
    // 임시로 정수형이 아닌 utc 값이 있는지 체크 (기존 코드에서 floor 가 되고 있었음.)
    if (!_.isInteger(wb.nub.startTimeUtc)) {
      throw new MError('Buff start time is not an integer.', MErrorCode.INTERNAL_ERROR, {
        userId: user.userId,
        wb,
      });
    }

    if (!buffCms.tickActiveEffect || buffCms.tickActiveEffect.length === 0) {
      return;
    }

    const tickModeType = buffCms.tickModeType;
    if (!tickModeType) {
      return;
    }

    const secondsElapsed = curTimeUtc - wb.nub.startTimeUtc;
    if (secondsElapsed < 1) {
      return;
    }

    this._tickActiveEffect(secondsElapsed, wb, user, buffSync);

    // 확률에 의한 추가버프를 넣어준다.
    this._tickAddBuff(secondsElapsed, wb, addBuffs, user);
  }
}
