CREATE PROCEDURE `mp_u_points_batch_update`(
  IN inUserId INT,
  IN inPointChanges JSON
)
label_body:BEGIN
  DECLARE i INT DEFAULT 0;
  DECLARE point_count INT;
  DECLARE current_cms_id INT;
  DECLARE current_value BIGINT;
  
  -- JSON 배열의 길이를 가져옴
  SET point_count = JSON_LENGTH(inPointChanges);
  
  -- 각 포인트 변경사항을 순회하며 처리
  WHILE i < point_count DO
    -- JSON에서 cmsId와 value를 추출
    SET current_cms_id = JSON_EXTRACT(inPointChanges, CONCAT('$[', i, '].cmsId'));
    SET current_value = JSON_EXTRACT(inPointChanges, CONCAT('$[', i, '].value'));
    
    -- 포인트 업데이트 실행
    INSERT INTO u_points
    (
      userId,
      cmsId,
      value
    )
    VALUES 
    (
      inUserId,
      current_cms_id,
      current_value
    ) 
    ON DUPLICATE KEY UPDATE value = current_value;
    
    SET i = i + 1;
  END WHILE;
  
  SELECT ROW_COUNT() as affectedRows;
END 