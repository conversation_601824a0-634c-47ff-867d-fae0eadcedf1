import { AuthApiClient } from './authApiClient';
import { ConfigApiClient } from './configApiClient';
import { SailApiClient } from './sailApiClient';
import { ProxyOceanApiClient } from './oceanApiClient';
import { ProxyLobbyApiClient } from './lobbyApiClient';
import { ProxyTownApiClient } from './townApiClient';
import { ZonelbApiClient } from './zonelbApiClient';
import { RealmApiClient } from './realmApiClient';
import { NavApiClient } from './navApiClient';
import { ChatApiClient } from './chatApiClient';
import { LineGamesApiClient } from './linegamesApiClient';
import mconf, { World } from '../mconf';
import { LineGamesLogApiClient } from './linegamesLogApiClient';
import { LineGamesBillingApiClient } from './linegamesBillingApiClient';
import { LineGamesPayApiClient } from './linegamesPayApiClient';

export { LobbyGroup } from './lobbyApiClient';

// 모든 서버에 configd 설정은 있어야 한다.
const configd = new ConfigApiClient(mconf.http.configd.url, mconf.http.configd.timeout);
const authd = new AuthApiClient();
const oceanpx = new ProxyOceanApiClient();
const townpx = new ProxyTownApiClient();
const lobbypx = new ProxyLobbyApiClient();
const saild = new SailApiClient();
const zonelbd = new ZonelbApiClient();
const realmd = new RealmApiClient();
const lgd = new LineGamesApiClient();
const lglogd = new LineGamesLogApiClient();
const lgbillingd = new LineGamesBillingApiClient();
const chatd = new ChatApiClient();
const navid = new NavApiClient();
const lgpayd = new LineGamesPayApiClient();

// 각 월드에 소속된 http목록. admind에서 사용.
const worldHttp: {
  [worldId: string]: {
    lgbillingd: LineGamesBillingApiClient;
    saild: SailApiClient;
    zonelbd: ZonelbApiClient;
    realmd: RealmApiClient;
    chatd: ChatApiClient;
    lgpayd: LineGamesPayApiClient;
  };
} = {};
export default {
  configd,
  authd,
  oceanpx,
  townpx,
  lobbypx,
  saild,
  zonelbd,
  realmd,
  lgd,
  lglogd,
  lgbillingd,
  chatd,
  navid,
  lgpayd,
  worldHttp,

  init() {
    if (mconf.http.authd) {
      authd.init(mconf.http.authd.url, mconf.http.authd.timeout);
    }
    if (mconf.http.saild) {
      saild.init(mconf.http.saild.url, mconf.http.saild.timeout);
    }
    if (mconf.http.zonelbd) {
      zonelbd.init(mconf.http.zonelbd.url, mconf.http.zonelbd.timeout);
    }
    if (mconf.http.realmd) {
      realmd.init(mconf.http.realmd.url, mconf.http.realmd.timeout);
    }
    if (mconf.http.lgd) {
      lgd.init(mconf.http.lgd.url, mconf.http.lgd.timeout);
    }
    if (mconf.http.lglogd) {
      lglogd.init(mconf.http.lglogd.url, mconf.http.lglogd.timeout);
    }
    if (mconf.http.lgbillingd) {
      lgbillingd.init(mconf.http.lgbillingd.url, mconf.http.lgbillingd.timeout);
      lgbillingd.setAuthPassword(mconf.http.lgbillingd.authPwd);
    }
    if (mconf.http.chatd) {
      chatd.init(mconf.http.chatd.url, mconf.http.chatd.timeout);
      chatd.setSalt(mconf.http.chatd.salt);
    }
    if (mconf.http.navid) {
      navid.init(mconf.http.navid.url, mconf.http.navid.timeout);
    }
    if (mconf.http.lgpayd) {
      lgpayd.init(mconf.http.lgpayd.url, mconf.http.lgpayd.timeout);
      lgpayd.setAuthPassword(mconf.http.lgpayd.authPwd);
    }

    mconf.worlds.forEach((world: World) => {
      let chatd: ChatApiClient;
      if (world.http.chatd) {
        chatd = new ChatApiClient();
        chatd.init(world.http.chatd.url, world.http.chatd.timeout);
        chatd.setSalt(world.http.chatd.salt);
      }
      let lgbillingd: LineGamesBillingApiClient;
      if (world.http.lgbillingd) {
        lgbillingd = new LineGamesBillingApiClient();
        lgbillingd.init(world.http.lgbillingd.url, world.http.lgbillingd.timeout);
        lgbillingd.setAuthPassword(world.http.lgbillingd.authPwd);
      }

      let saild: SailApiClient;
      if (world.http.saild) {
        saild = new SailApiClient();
        saild.init(world.http.saild.url, world.http.saild.timeout);
      }

      let zonelbd: ZonelbApiClient;
      if (world.http.zonelbd) {
        zonelbd = new ZonelbApiClient();
        zonelbd.init(world.http.zonelbd.url, world.http.zonelbd.timeout);
      }

      let realmd: RealmApiClient;
      if (world.http.realmd) {
        realmd = new RealmApiClient();
        realmd.init(world.http.realmd.url, world.http.realmd.timeout);
      }

      let lgpayd: LineGamesPayApiClient;
      if (world.http.lgpayd) {
        lgpayd = new LineGamesPayApiClient();
        lgpayd.init(world.http.lgpayd.url, world.http.lgpayd.timeout);
        lgpayd.setAuthPassword(world.http.lgpayd.authPwd);
      }
      worldHttp[world.id] = {
        chatd,
        lgbillingd,
        saild,
        zonelbd,
        realmd,
        lgpayd,
      };
    });
  },
};
