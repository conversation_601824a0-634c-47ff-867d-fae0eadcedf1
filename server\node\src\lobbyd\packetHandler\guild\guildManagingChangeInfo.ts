// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../index';
import Container from 'typedi';
import { LobbyService } from '../../server';
import {
  GuildData,
  GUI<PERSON>_JOIN_CONDITION,
  GUILD_JOIN_TYPE,
  GUILD_MEMBER_GRADE,
  GUILD_NATION_CONDITION,
  GUILD_MEMBER_GRADE_ACCESS_CATEGORY,
} from '../../../motiflib/model/lobby';
import { GuildSync, UserLightInfo } from '../../type/sync';
import _ from 'lodash';
import mhttp from '../../../motiflib/mhttp';
import { isNotANumber } from '../../../motiflib/mutil';
import { onGuildPublish } from '../../guildPubsub';
import cms from '../../../cms';
import { GuildLogUtil, GuildUtil } from '../../guildUtil';
import * as mutilLanguage from '../../../motiflib/mutilLanguage';

const rsn = 'guild_change';
const add_rsn = 'by_master';

// ----------------------------------------------------------------------------
interface RequestBody {
  guildName?: string; // 길드명
  introduction?: string; // 길드 소개글
  masterNotice?: string; // 길드장 공지글
  joinType?: number; // 가입타입
  joinCondition?: number; // 가입 조건 타입
  joinConditionValue?: number; // 가입조건 값
  nationCondition?: number; // 국가 필터
  emblemImageCmsId?: number; // 문양id
  emblemColorCmsId?: number; // 테투리
  emblemBorderCmsId?: number;
  grade_alias_1?: string;
  grade_alias_2?: string;
  grade_alias_3?: string;
  grade_alias_4?: string;
}

/**
 * 길드 정보 변경(길드장 전용)
 */
// ----------------------------------------------------------------------------
export class Cph_Guild_ManagingChangeInfo implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;
    const { guildRedis } = Container.get(LobbyService);

    // 상회장 관리 https://wiki.line.games/pages/viewpage.action?pageId=83573760
    const guildId: number = user.userGuild.guildId;
    if (!guildId) {
      throw new MError('there-is-no-guild-joined.', MErrorCode.GUILD_NOT_JOINED, {
        userId: user.userId,
      });
    }

    let guildSync: GuildSync = {};
    let guildData: GuildData;
    let guildData_old: GuildData;
    let userLightInfos: { [userId: number]: UserLightInfo };

    const itemNamesForBadWordChecking = [
      'introduction',
      'masterNotice',
      'grade_alias_1',
      'grade_alias_2',
      'grade_alias_3',
      'grade_alias_4'];
    for (const itemName of itemNamesForBadWordChecking) {
      const itemValue = reqBody[itemName];
      const hasBadWord = itemValue ? await mhttp.platformApi.hasBadWord(itemValue) : false; 
      if (hasBadWord) {
        return user.sendJsonPacket(packet.seqNum, packet.type, {
          errCode: MErrorCode.HAS_BAD_WORD,
          badWordField: itemName,
        });
      }
    }

    //======================================================================================================
    return (
      GuildUtil.GetGuildDataWithMemberLightInfo(user, guildId)
        .then((result) => {
          if (!result) {
            throw new MError('cannot-find-guild.', MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS, {
              userId: user.userId,
              guildId,
            });
          }

          guildData = result.guildData;
          guildData_old = _.cloneDeep(guildData);
          userLightInfos = result.userLightInfos;

          //======================================================================================================
          // 길드명 변경
          //======================================================================================================
          return Promise.resolve().then(() => {
            if (reqBody.guildName && reqBody.guildName !== guildData.guild.guildName) {
              // 길드장만 변경 가능.
              GuildUtil.ensureMaster(user, guildId, guildData.members);

              mutilLanguage.ensureValidName(
                guildData.guild.guildName,
                reqBody.guildName,
                cms.Const.GuildNameMinimum.value,
                cms.Const.GuildNameMaximum.value
              );
              //
              return mhttp.platformApi
                .hasBadWord(reqBody.guildName)
                .then((bHas) => {
                  if (bHas) {
                    throw new MError('has-bad-word', MErrorCode.GUILD_NAME_HAS_BAD_WORD, {
                      userId: user.userId,
                      guildId: user.userGuild.guildId,
                      name: reqBody.guildName,
                    });
                  }
                  return guildRedis['getGuildIdByName'](reqBody.guildName);
                })

                .then((guildId) => {
                  if (guildId > 0) {
                    throw new MError(
                      'guild-name-already-exists',
                      MErrorCode.GUILD_NAME_ALREADY_EXISTS,
                      {
                        userId: user.userId,
                      }
                    );
                  }
                  guildData.guild.guildName = reqBody.guildName;
                  _.merge(guildSync, { guildName: reqBody.guildName });
                });
            }
          });
        })

        //======================================================================================================
        // 소개글 변경
        //======================================================================================================
        .then(() => {
          if (reqBody.introduction !== null && reqBody.introduction !== undefined) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            GuildUtil.ensureGuildIntroductionLength(user, reqBody.introduction);

            guildData.guild.introduction = reqBody.introduction;
            _.merge(guildSync, {
              introduction: reqBody.introduction,
            });
          }
          if (reqBody.masterNotice !== null && reqBody.masterNotice !== undefined) {
            GuildUtil.ensureGuildMemberAccess(
              GUILD_MEMBER_GRADE_ACCESS_CATEGORY.GUILD_NOTICE,
              user,
              guildId,
              guildData
            );

            guildData.guild.masterNotice = reqBody.masterNotice;
            _.merge(guildSync, {
              masterNotice: reqBody.masterNotice,
            });
          }
        })

        //======================================================================================================
        // 가입방법 변경
        //======================================================================================================
        .then(() => {
          if (isNotANumber(reqBody.joinType)) {
            return;
          }
          // 길드장만 변경 가능.
          GuildUtil.ensureMaster(user, guildId, guildData.members);

          // 자유가입 -> 신청가입으로 변경 시 신청리스트가 남아있을 경우 에러전달.
          if (
            reqBody.joinType === GUILD_JOIN_TYPE.FREE &&
            guildData.guild.joinType === GUILD_JOIN_TYPE.NORMAL
          ) {
            if (_.keys(guildData.applicants).length > 0) {
              throw new MError('applicant-exists', MErrorCode.GUILD_APPLICANT_EXISTS, {
                userId: user.userId,
                guildId: user.userGuild,
                numOfApplicants: _.keys(guildData.applicants).length,
              });
            }
          }

          guildData.guild.joinType = reqBody.joinType;
          _.merge(guildSync, {
            joinType: reqBody.joinType,
          });
        })
        //======================================================================================================
        // 가입 조건 변경
        //======================================================================================================
        .then(() => {
          if (isNotANumber(reqBody.joinCondition)) {
            return;
          }

          // 길드장만 변경 가능.
          GuildUtil.ensureMaster(user, guildId, guildData.members);

          guildData.guild.joinCondition = reqBody.joinCondition;

          _.merge(guildSync, {
            joinCondition: reqBody.joinCondition,
          });
        })
        //======================================================================================================
        // 가입 조건값 변경
        //======================================================================================================
        .then(() => {
          if (isNotANumber(reqBody.joinConditionValue)) {
            return;
          }
          // 길드장만 변경 가능.
          GuildUtil.ensureMaster(user, guildId, guildData.members);

          guildData.guild.joinConditionValue = reqBody.joinConditionValue;

          _.merge(guildSync, {
            joinConditionValue: reqBody.joinConditionValue,
          });
        })
        //======================================================================================================
        // 국가 조건 변경.
        //======================================================================================================
        .then(() => {
          if (isNotANumber(reqBody.nationCondition)) {
            return;
          }

          // 길드장만 변경 가능.
          GuildUtil.ensureMaster(user, guildId, guildData.members);

          if (reqBody.nationCondition === GUILD_NATION_CONDITION.NONE) {
            guildData.guild.nationCondition = reqBody.nationCondition;
            guildData.guild.nationCmsId = 0;

            _.merge(guildSync, {
              nationCondition: reqBody.nationCondition,
              nationCmsId: 0,
            });

            return;
          }
          // 동일 국가조건으로 바꿀 경우 길드원이 모두 길드장과 동일한 국가이어야 한다.
          if (reqBody.nationCondition === GUILD_NATION_CONDITION.SAME_NATION) {
            const memberUserIds = [];
            _.forOwn(guildData.members, (v, k) => {
              memberUserIds.push(parseInt(k, 10));
            });

            _.forOwn(userLightInfos, (userLightInfo: UserLightInfo) => {
              if (user.nationCmsId !== userLightInfo.nationCmsId) {
                throw new MError(
                  'there-is-other-nation-in-guild-members',
                  MErrorCode.GUILD_EXISTS_OTHER_NATION_IN_GUILD_MEMBERS,
                  {
                    userId: user.userId,
                    guildId,
                    masterNationCmsId: user.nationCmsId,
                    memberNationCmsId: userLightInfo.nationCmsId,
                    memberUserId: userLightInfo.userId,
                  }
                );
              }
            });

            guildData.guild.nationCondition = reqBody.nationCondition;
            guildData.guild.nationCmsId = user.nationCmsId;
            _.merge(guildSync, {
              nationCondition: reqBody.nationCondition,
              nationCmsId: user.nationCmsId,
            });
          }
        })
        //======================================================================================================
        // 문양 및 색 변경
        //======================================================================================================
        .then(() => {
          if (!isNotANumber(reqBody.emblemImageCmsId)) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            if (reqBody.emblemImageCmsId > 0) {
              GuildUtil.ensureEmblemImage(user, guildData.guild.grade, reqBody.emblemImageCmsId);
            }

            guildData.guild.emblemImageCmsId = reqBody.emblemImageCmsId;
            _.merge(guildSync, {
              emblemImageCmsId: reqBody.emblemImageCmsId,
            });
          }
          if (!isNotANumber(reqBody.emblemColorCmsId)) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            if (reqBody.emblemColorCmsId > 0) {
              GuildUtil.ensureEmblemColor(user, reqBody.emblemColorCmsId);
            }
            guildData.guild.emblemColorCmsId = reqBody.emblemColorCmsId;
            _.merge(guildSync, {
              emblemColorCmsId: reqBody.emblemColorCmsId,
            });
          }
          if (!isNotANumber(reqBody.emblemBorderCmsId)) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            if (reqBody.emblemBorderCmsId > 0) {
              GuildUtil.ensureEmblemBorder(user, reqBody.emblemBorderCmsId);
            }
            guildData.guild.emblemBorderCmsId = reqBody.emblemBorderCmsId;
            _.merge(guildSync, {
              emblemBorderCmsId: reqBody.emblemBorderCmsId,
            });
          }
        })
        //======================================================================================================
        // 등급명  변경
        //======================================================================================================
        .then(() => {
          if (reqBody.grade_alias_1) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            guildData.guild.grade_alias_1 = reqBody.grade_alias_1;
            _.merge(guildSync, {
              grade_alias_1: reqBody.grade_alias_1,
            });
          }
          if (reqBody.grade_alias_2) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            guildData.guild.grade_alias_2 = reqBody.grade_alias_2;
            _.merge(guildSync, {
              grade_alias_2: reqBody.grade_alias_2,
            });
          }
          if (reqBody.grade_alias_3) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            guildData.guild.grade_alias_3 = reqBody.grade_alias_3;
            _.merge(guildSync, {
              grade_alias_3: reqBody.grade_alias_3,
            });
          }
          if (reqBody.grade_alias_4) {
            // 길드장만 변경 가능.
            GuildUtil.ensureMaster(user, guildId, guildData.members);

            guildData.guild.grade_alias_4 = reqBody.grade_alias_4;
            _.merge(guildSync, {
              grade_alias_4: reqBody.grade_alias_4,
            });
          }
        })
        //======================================================================================================
        // 길드 레디스 업데이트
        //======================================================================================================
        .then(() => {
          return guildRedis['updateGuild'](guildId, JSON.stringify(guildData.guild));
        })

        //======================================================================================================
        // 멤버들에게 변경 알림
        //======================================================================================================
        .then(() => {
          onGuildPublish(guildData, userLightInfos, [user.userId], {
            add: {
              userGuild: {
                guild: guildSync,
              },
            },
          });
        })
        //======================================================================================================
        //길드 이름,엠블렘,컬러 변경 시 필드 주변 유저들에게도 동기화가 필요하다.
        //======================================================================================================
        .then(() => {
          user.userGuild.updateGuildAppearance(
            user,
            guildData.guild.guildName,
            GUILD_MEMBER_GRADE.MASTER, // 길드장만 길드정보 변경할 수 있으므로...
            guildData.guild.emblemImageCmsId,
            guildData.guild.emblemColorCmsId,
            guildData.guild.emblemBorderCmsId
          );
        })

        //======================================================================================================
        // 응답
        //======================================================================================================
        .then(() => {
          gLog_guildChange(user, guildId, guildData_old, guildData, userLightInfos);

          return user.sendJsonPacket(packet.seqNum, packet.type, {
            sync: {
              add: {
                userGuild: {
                  guild: guildSync,
                },
              },
            },
          });
        })
    );
  }
}

function gLog_guildChange(
  user: User,
  guildId: number,
  guildData_old: GuildData,
  guildData: GuildData,
  userLightInfos: { [userId: number]: UserLightInfo }
) {
  const cur_guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);
  const old_guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData_old, userLightInfos);

  let nation: string = null;
  if (user.nationCmsId) {
    const nationCms = cms.Nation[user.nationCmsId];
    nation = nationCms ? nationCms.name : null;
  }

  user.glog('guild_change', {
    nation,
    rsn,
    add_rsn,
    cur_guild_data,
    old_guild_data,
  });
}
