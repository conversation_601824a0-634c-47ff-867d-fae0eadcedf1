// ------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ------------------------------------------------------------------------------------------------

import R from 'ramda';
import { Container } from 'typedi';
import { Promise as promise, using } from 'bluebird';
import { PoolConnection } from 'promise-mysql';
import _ from 'lodash';

import cms from '../../cms';
import { LobbyService } from '../server';
import * as query from '../../mysqllib/query';
import { getSqlConnection } from '../../mysqllib/mysqlUtil';
import * as cmsEx from '../../cms/ex';
import * as sync from '../type/sync';
import { UserChangeSpec } from './commonChangeSpec';
import mlog from '../../motiflib/mlog';
import * as mutil from '../../motiflib/mutil';
import Mate, { MateExpChange } from '../mate';
import { CASH_SHOP_PRODUCT_TYPE } from '../../cms/cashShopDesc';
import { User } from '../user';
import UserPoints, { CashGain, CashPayment, Mileage } from '../userPoints';
import { UserInven } from '../userInven';
import UserMates from '../userMates';
import { UserReputation } from '../userReputation';
import UserFleets from '../userFleets';
import UserMails from '../userMails';
import { CompanyStat } from '../../motiflib/stat/companyStat';
import UserAchievement, { AccumulateParam, TaskCountChange } from '../userAchievement';
import UserCashShop, { DailySubscription } from '../userCashShop';
import { UserQuestManager } from '../quest';
import UserSailing, { GameOverLosses } from '../userSailing';
import UserAttendance from '../userAttendance';
import UserState from '../userState';
import UserShipBlueprints from '../userShipBlueprints';
import UserBattle, { BattleQuickModeChange } from '../userBattle';
import { BattleReward } from '../battleResult';
import { UserChallenges } from '../userChallenges';
import { RewardData } from '../../motiflib/gameLog';
import { REWARD_TYPE } from '../../cms/rewardDesc';
import { UserEncount } from '../encount';
import { UserEnergy } from '../userEnergy';
import UserShield from '../userShield';
import UserTrade from '../userTrade';
import { isCash } from '../../cms/pointDesc';
import { BuffSync } from '../userBuffs';
import { UserVillage } from '../userVillage';
import UserDiscovery from '../userDiscovery';
import { EventPageType } from '../../cms/eventPageDesc';
import UserPassEvent, { PassEventMission } from '../userPassEvent';
import { DISCOVERY_GROUP, DISCOVERY_TYPE } from '../../cms/DiscoveryDesc';
import { recordEventBetweenTwoNations } from '../nationManager';
import UserExplore from '../userExplore';
import { GuildLogUtil } from '../guildUtil';
import { UserTradeArea } from '../userTradeArea';
import { UserPets } from '../userPets';
import { UserManufacture } from '../userManufacture';

import puUserUpdateEnergy from '../../mysqllib/sp/puUserUpdateEnergy';
import puSoftDataUpdateExpLevel from '../../mysqllib/sp/puSoftDataUpdateExpLevel';
import puPointUpdate from '../../mysqllib/sp/puPointUpdate';
import puItemUpdate from '../../mysqllib/sp/puItemUpdate';
import puMateCreate from '../../mysqllib/sp/puMateCreate';
import puMateUpdateStateFlags from '../../mysqllib/sp/puMateUpdateStateFlags';
import puMateUpdateFame from '../../mysqllib/sp/puMateUpdateFame';
import puMateUpdateExpLevel from '../../mysqllib/sp/puMateUpdateExpLevel';
import puMateEquipmentUpdateLastId from '../../mysqllib/sp/puMateEquipmentUpdateLastId';
import puMateEquipmentCreate from '../../mysqllib/sp/puMateEquipmentCreate';
import puMateEquipmentDelete from '../../mysqllib/sp/puMateEquipmentDelete';
import puShipUpdateLastId from '../../mysqllib/sp/puShipUpdateLastId';
import puShipCreate from '../../mysqllib/sp/puShipCreate';
import puShipDelete from '../../mysqllib/sp/puShipDelete';
import puShipSlotsDelete from '../../mysqllib/sp/puShipSlotsDelete';
import puShipCargoUpdate from '../../mysqllib/sp/puShipCargoUpdate';
import puShipCargosDelete from '../../mysqllib/sp/puShipCargosDelete';
import puUserUpdateNation from '../../mysqllib/sp/puUserUpdateNation';
import puReputationUpdate from '../../mysqllib/sp/puReputationUpdate';
import puMateUpdateLoyalties from '../../mysqllib/sp/puMateUpdateLoyalties';
import puSailingUpdateDays from '../../mysqllib/sp/puSailingUpdateDays';
import puDirectMailUpdateState from '../../mysqllib/sp/puDirectMailUpdateState';
import puGameOverLossUpdate from '../../mysqllib/sp/puGameOverLossUpdate';
import puTaskUpdateIsRewarded from '../../mysqllib/sp/puTaskUpdateIsRewarded';
import puTaskUpdateIsCategoryRewarded from '../../mysqllib/sp/puTaskUpdateIsCategoryRewarded';
import puAchievementUpdateIsRewarded from '../../mysqllib/sp/puAchievementUpdateIsRewarded';
import puUserUpdateLastRewardedAchievementPointCmsId from '../../mysqllib/sp/puUserUpdateLastRewardedAchievementPointCmsId';
import puBattleRewardUpdate from '../../mysqllib/sp/puBattleRewardUpdate';
import puInsuranceUpdateCmsId from '../../mysqllib/sp/puInsuranceUpdateCmsId';
import puShipUpdateSailor from '../../mysqllib/sp/puShipUpdateSailor';
import puUserUpdatePalaceRoyalOrderCmsIdAndRnds from '../../mysqllib/sp/puUserUpdatePalaceRoyalOrderCmsIdAndRnds';
import puCashShopRestrictedProductDelete from '../../mysqllib/sp/puCashShopRestrictedProductDelete';
import puCashShopRestrictedProductUpdate from '../../mysqllib/sp/puCashShopRestrictedProductUpdate';
import puCashShopFixedTermProductDelete from '../../mysqllib/sp/puCashShopFixedTermProductDelete';
import puCashShopFixedTermProductUpdate from '../../mysqllib/sp/puCashShopFixedTermProductUpdate';
import puStateUpdateGameState from '../../mysqllib/sp/puStateUpdateGameState';
import puSoftDataUpdateLastTownCmsId from '../../mysqllib/sp/puSoftDataUpdateLastTownCmsId';
import puDirectMailUpdateExpireTimeUtc from '../../mysqllib/sp/puDirectMailUpdateExpireTimeUtc';
import puShipBlueprintUpdateLevel from '../../mysqllib/sp/puShipBlueprintUpdateLevel';
import puCashShopGachaBoxGuaranteeUpdate from '../../mysqllib/sp/puCashShopGachaBoxGuaranteeUpdate';
import puSoundPackUpdate from '../../mysqllib/sp/puSoundPackUpdate';
import puMateUpdateLoyaltiesAndStateFlags from '../../mysqllib/sp/puMateUpdateLoyaltiesAndStateFlags';
import puUnemployedMateUpdateIsMet from '../../mysqllib/sp/puUnemployedMateUpdateIsMet';
import puUnemployedMateUpdateIntimacy from '../../mysqllib/sp/puUnemployedMateUpdateIntimacy';
import puAttendanceUpdate from '../../mysqllib/sp/puAttendanceUpdate';
import puDirectMailLastIdUpdate from '../../mysqllib/sp/puDirectMailLastIdUpdate';
import puDirectMailCreate from '../../mysqllib/sp/puDirectMailCreate';
import puShipSlotUpdate from '../../mysqllib/sp/puShipSlotUpdate';
import puShipSlotItemLastIdUpdate from '../../mysqllib/sp/puShipSlotItemLastIdUpdate';
import puShipSlotItemCreate from '../../mysqllib/sp/puShipSlotItemCreate';
import puSoftDataUpdateFreeTakeback from '../../mysqllib/sp/puSoftDataUpdateFreeTakeback';
import puSoftDataUpdateQuickMode from '../../mysqllib/sp/puSoftDataUpdateQuickMode';
import puShipUpdateDurability from '../../mysqllib/sp/puShipUpdateDurability';
import puInsuranceUpdateUnpaid from '../../mysqllib/sp/puInsuranceUpdateUnpaid';
import puQuestContextUpdateAllFlags from '../../mysqllib/sp/puQuestContextUpdateAllFlags';
import puChallengeUpdate from '../../mysqllib/sp/puChallengeUpdate';
import puQuestContextDelete from '../../mysqllib/sp/puQuestContextDelete';
import puBattleUpdateBattleEndResult from '../../mysqllib/sp/puBattleUpdateBattleEndResult';
import puUnemployedMateDelete from '../../mysqllib/sp/puUnemployedMateDelete';
import puShipUpdateFormationIndex from '../../mysqllib/sp/puShipUpdateFormationIndex';
import puQuestItemLastIdUpdate from '../../mysqllib/sp/puQuestItemLastIdUpdate';
import puQuestItemCreate from '../../mysqllib/sp/puQuestItemCreate';
import puShipSlotItemDelete from '../../mysqllib/sp/puShipSlotItemDelete';
import puTownStateDeleteAll from '../../mysqllib/sp/puTownStateDeleteAll';
import puLineMailUpdateState from '../../mysqllib/sp/puLineMailUpdateState';
import puStateUpdateGameStateLastGameState from '../../mysqllib/sp/puStateUpdateGameStateLastGameState';
import puShieldUpdate from '../../mysqllib/sp/puShieldUpdate';
import puTaxFreePermitUpdateExpirationTimeUtc from '../../mysqllib/sp/puTaxFreePermitUpdateExpirationTimeUtc';
import puVillageUpdate from '../../mysqllib/sp/puVillageUpdate';
import puSoftDataUpdateArrivalTownCmsId from '../../mysqllib/sp/puSoftDataUpdateArrivalTownCmsId';
import puEventMissionUpdateIsRewarded from '../../mysqllib/sp/puEventMissionUpdateIsRewarded';
import puMateUpdateInjuryExpireTimeUtc from '../../mysqllib/sp/puMateUpdateInjuryExpireTimeUtc';
import puShipUpdateLife from '../../mysqllib/sp/puShipUpdateLife';
import puContributionShopRestrictedProductDelete from '../../mysqllib/sp/puContributionShopRestrictedProductDelete';
import puContributionShopRestrictedProductUpdate from '../../mysqllib/sp/puContributionShopRestrictedProductUpdate';
import puDiscoveryAdd from '../../mysqllib/sp/puDiscoveryAdd';
import puMileageDelete from '../../mysqllib/sp/puMileageDelete';
import puMileageUpdate from '../../mysqllib/sp/puMileageUpdate';
import puPassEventUpdate from '../../mysqllib/sp/puPassEventUpdate';
import puPassEventMissionUpdateCountAndIsRewarded from '../../mysqllib/sp/puPassEventMissionUpdateCountAndIsRewarded';
import puUserUpdateExploreTicket from '../../mysqllib/sp/puUserUpdateExploreTicket';
import puGuildShopRestrictedProductDelete from '../../mysqllib/sp/puGuildShopRestrictedProductDelete';
import puGuildShopRestrictedProductUpdate from '../../mysqllib/sp/puGuildShopRestrictedProductUpdate';
import puUserUpdateExploreQuickMode from '../../mysqllib/sp/puUserUpdateExploreQuickMode';
import puTradeAreaUpdate from '../../mysqllib/sp/puTradeAreaUpdate';
import puUserUpdateShipBuild from '../../mysqllib/sp/puUserUpdateShipBuild';
import puEventGameUpdate from '../../mysqllib/sp/puEventGameUpdate';
import puShipBlueprintUpdateSailMasteryExpLevel from '../../mysqllib/sp/puShipBlueprintUpdateSailMasteryExpLevel';
import puFleetUpdateDispatchReduceLifeRemain from '../../mysqllib/sp/puFleetUpdateDispatchReduceLifeRemain';
import puFleetDispatchUpdateRewrd from '../../mysqllib/sp/puFleetDispatchUpdateRewrd';
import puFleetDispatchUpdateStateEndView from '../../mysqllib/sp/puFleetDispatchUpdateStateEndView';
import puFleetDispatchDelete from '../../mysqllib/sp/puFleetDispatchDelete';
import puDailySubscriptionUpdate from '../../mysqllib/sp/puDailySubscriptionUpdate';
import { UserGuild } from '../userGuild';
import puGuildSynthesisProgressDelete from '../../mysqllib/sp/puGuildSynthesisProgressDelete';
import puGuildSynthesisProgressUpdate from '../../mysqllib/sp/puGuildSynthesisProgressUpdate';
import puHotSpotProductsUpdate from '../../mysqllib/sp/puHotSpotProductsUpdate';
import UserEventRanking from '../userEventRanking';
import puEventRankingRewardUpdate from '../../mysqllib/sp/puEventRankingRewardUpdate';
import UserDiscoveryReward from '../userDiscoveryReward';
import puDiscoveryRewardUpdate from '../../mysqllib/sp/puDiscoveryRewardUpdate';
import puSoftDataUpdateKarma from '../../mysqllib/sp/puSoftDataUpdateKarma';
import puUserUpdatePalaceRoyalTitleOrderCmsIdAndRnds from '../../mysqllib/sp/puUserUpdatePalaceRoyalTitleOrderCmsIdAndRnds';
import puShipCamouflageUpdate from '../../mysqllib/sp/puShipCamouflageUpdate';
import puGameOverLossUpdateMultiPvpLoss from '../../mysqllib/sp/puGameOverLossUpdateMultiPvpLoss';
import { FishCatch, UserFishing } from '../userFishing';
import puFishCatchUpdate from '../../mysqllib/sp/puFishesUpdate';
import { UserTitle, UserTitles } from '../userTitles';
import puUserTitleUpdate from '../../mysqllib/sp/puUserTitleUpdate';
import Ship, { ShipSlotChange } from '../ship';
import UserSweepTicket from '../userSweepTicket';
import puSweepTicketUpdate from '../../mysqllib/sp/puSweepTicketUpdate';
import puMatePassiveCreate from '../../mysqllib/sp/puMatePassiveCreate';
import { PubStaff } from '../userTown';
import puTownPubStaffUpdate from '../../mysqllib/sp/puTownPubStaffUpdate';
import puUserUpdateLastRoyalOrderCompletedTimeUtc from '../../mysqllib/sp/puUserUpdateLastRoyalOrderCompletedTimeUtc';
import puPetCreate from '../../mysqllib/sp/puPetCreate';
import puShipComposeUpdate from '../../mysqllib/sp/puShipComposeUpdate';
import { UserSmuggle } from '../userSmuggle';
import puLastPaidSmuggleEnterTownCmsIdUpdate from '../../mysqllib/sp/puLastPaidSmuggleEnterTownCmsIdUpdate';
import puShipBlueprintUpdate from '../../mysqllib/sp/puShipBlueprintUpdate';
import { UserInfiniteLighthouse } from '../userInfiniteLighthouse';
import { GetMonthlyContentsResetCount } from '../../formula';
import puInfiniteLighthouseUpdate from '../../mysqllib/sp/puInfiniteLighthouseUpdate';
import puTaskUpdateCount from '../../mysqllib/sp/puTaskUpdateCount';
import { BattleRedisHelper } from '../battleRedisHelper';
import { BattleType } from '../../motiflib/model/lobby';
import { UserClash } from '../userClash';
import { ClashSession } from '../clash';
import { Reentry, UserReentry } from '../userReentry';
import puReentryUpdate from '../../mysqllib/sp/puReentryUpdate';
import puClashUpdate from '../../mysqllib/sp/puClashUpdate';
import { MailCreatingParams } from '../../motiflib/mailBuilder';
import puItemUpdate_8 from '../../mysqllib/sp/puItemUpdate_8';
import puUserUpdateManufacturePoint from '../../mysqllib/sp/puUserUpdateManufacturePoint';
import { ManufactureExpLevelChange } from '../../motiflib/model/lobby';
import puManufactureExpLevelUpdate from '../../mysqllib/sp/puManufactureExpLevelUpdate';
import puManufactureProgressDelete from '../../mysqllib/sp/puManufactureProgressDelete';

export enum CHANGE_TASK_REASON {
  RECEIVE_DIRECT_MAIL_REWARD,
  RECEIVE_TASK_REWARD,
  COMPLETE_TASK_IMMEDIATELY,
  RECEIVE_TASK_CATEGORY_REWARD,
  RECEIVE_ACHIEVEMENT_REWARD,
  RECEIVE_ACHIEVEMENT_POINT_REWARD,
  RECEIVE_BATTLE_REWARD,
  ATTENDANCE,
  ARRIVE_IN_TOWN,
  QUEST_REWARD,
  QUEST_PAYMENT,
  RESOLVE_DISASTER,
  DEV_UPDATE_REPUTATION,
  DEV_SET_NATION,
  DEV_INVOKE_EVENT,
  SELECT_NATION,
  CHANGE_NATION,
  PAY_INSURANCE,
  CASH_SHOP_BUY_WITHOUT_PURCHASE_REWARD_FIXED,
  CASH_SHOP_BUY_WITHOUT_PURCHASE_BUFF,
  CASH_SHOP_BUY_WITHOUT_PURCHASE_GACHA_BOX,
  CASH_SHOP_GET_GACHA_BOX_GUARANTEE,
  COMPLETE_PROLOGUE,
  TOUCH_OCEAN_DOODAD,
  BATTLE_END,
  BATTLE_END_RAID,
  BATTLE_END_CHALLENGE,
  DISMANTLE_SHIP,
  CASH_SHOP_BUY_WITHOUT_PURCHASE_ENCOUNT_SHIELD,
  RECEIVE_LINE_MAIL_REWARD,
  MANTIC_SWAP_PIECE,
  FISHING_REWARD,
  VILLAGE_EXPLORE,
  RECEIVE_EVENT_MISSION_REWARD,
  RECEIVE_EVENT_MISSION_COMPLETE_REWARD,
  PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
  LAND_EXPLORE,
  RECEIVE_PASS_EVENT_MISSION_REWARD,
  BUY_GUILD_SHOP_PRODUCT,
  RECEIVE_TRADE_AREA_REWARD,
  USE_ITEM,
  PLAY_EVENT_GAME,
  FLEET_DISPATCH_END,
  FLEET_DISPATCH_RECEIVE_REWARD,
  BUY_ATTENDANCE,
  RECEIVE_DAILY_SUBSCRIPTION_REWARD,
  GUILD_SYNTHESIS_REWARD,
  CASH_SHOP_BUY_WITHOUT_PURCHASE_DAILY_SUBSCRIPTION,
  CASH_SHOP_BUY_WITHOUT_HOT_SPOT,
  BATTLE_END_MULTI_PVP,
  RECEIVE_EVENT_RANKING_MISSION_REWARD,
  RECEIVE_DISCOVERY_REWARD,
  RECEIVE_FISH_SIZE_REWARDS,
  RECEIVE_FRIENDSHIP_FIRST_REWARD,
  RECEIVE_FRIENDSHIP_WEEKLY_REWARD,
  SWEEP_LOCAL_NPC,
  RECEIVE_NATION_SUPPORT_SHOP_REWARD,
  SALVAGE_GAME_END,
  SHIP_COMPOSE,
  BATTLE_END_INFINITE_LIGHTHOUSE,
  BATTLE_END_FRIENDLY,
  BATTLE_END_CLASH,
  AUTO_CHANGE_ITEMS,
  DEV_SET_MANUFACTURE_LEVEL,
  MANUFACTURE_RECEIVE,
}

export enum CHANGE_TASK_RESULT {
  OK = 0,
  NOTHING = 1, // Ok, but nothing to receive.
  EXCEEDS_LOAD_RATIO = 2, // 전투 보상에서 적재 비율 초과 시 유저 선택으로 수령.
  EXCEEDS_POINT_HARD_CAP = 3, // Ok, but nothing to receive.
  OK_MAX = 3,

  ITEM_MAX_HAVABLE = 100,
  INVEN_FULL = 101,
  CARGO_FULL = 102, // 보상으로 보급품, 교역품 수령 시 적재 비율 또는 선창 최대치 만큼 수령으로 변경되어 사용 안함.
  MATE_EQUIP_MAX_HAVABLE = 103,
  MATE_EQUIP_FULL = 104,
  DOCK_FULL = 105,
  CAPTURED_FULL = 106,
  ENCOUNT_SHIELD_FULL = 107,
  DUPLICATE_MATE = 108, //* 어느 순간 사용 안하게 돼서 참조하고 있는 곳이 없는 듯한데 확인 필요.
  SHIP_SLOT_INVEN_FULL = 109,
  LOCKED_SHIP = 110,

  // FULL_MAX 이하는 tryData 에 정상적이지 않은 변경이 발생되지 않았음이 보장되어야 된다.
  // 예를 들어 inven 에 넣기 전에 가능한지 여부를 판단한 후 문제 없을 경우 tryData 에 추가한다.
  FULL_MAX = 199,

  ALREADY_RECEIVED_TASK_REWARD = 200,
  ALREADY_RECEIVED_TASK_CATEGORY_REWARD = 201,
  ALREADY_RECEIVED_ACHIEVEMENT_REWARD = 202,
  ALREADY_RECEIVED_ACHIEVEMENT_POINT_REWARD = 203,

  NOT_ENOUGH_POINT = 204,
  NOT_ENOUGH_ITEM = 205,
  NOT_ENOUGH_CARGO = 206,
  NOT_ENOUGH_ENERGY = 207,
  NOT_ENOUGH_SHIELD = 208,

  ALREADY_BOUGHT = 209,
  ALREADY_RECEIVED_EVENT_MISSION_REWARD = 210,

  ALREADY_RECEIVED_DAILY_SUBSCRIPTION_PURCHASE_REWARD = 211,
  ALREADY_RECEIVED_DAILY_SUBSCRIPTION_DAILY_REWARD = 212,

  NOT_OPEN_HOT_SPOT_PRODUCT = 213,
  ALREADY_RECEIVED_EVENT_RANKING_MISSION_REWARD = 214,
  ALREADY_RECEIVED_DISCOVERY_REWARD = 215,
  ALREADY_RECEIVED_FISH_SIZE_REWARD = 216,
  ENOUGH_FISH_BIG_CATCH_SIZE_REWARD = 217,

  NOT_ENOUGH_SHIP_COUNT = 218,
  ALREADY_COMPOSE_MAX_COUNT = 219,
  NOT_MATCHED_MATERIAL_SHIP_CMS_ID = 220,
  COMPOSE_SHIP_ONLY_IN_DOCK = 221,
  CAN_NOT_COMPOSE_LOCKED_SHIP = 222,
  INVALID_SHIP_ID = 223,
  NOT_ENOUGH_MANUFACTURE_POINT = 224,

  NOT_IMPLEMENTED = 300,
  INTERNAL_ERROR = 301, // Bugs.
  CMS_ERROR = 302, // 잘못된 기획 테이블 값.
}

// ------------------------------------------------------------------------------------------------
// "Try data" to keep track of accumulative changes to the original state.
// Each field is cloned from the User's field when needed.
// ------------------------------------------------------------------------------------------------
export interface TryData {
  userExp?: number;
  userLevel?: number;
  nationCmsId?: number;
  lastUpdateNationTimeUtc?: number;
  points?: UserPoints;
  inven?: UserInven;
  mates?: UserMates;
  reputation?: UserReputation;
  fleets?: UserFleets; // fleets 를 통해 얻어지고 내부에서 사용되는 mate 의 경우 clone 된 mate 가 아님.
  shipBlueprints?: UserShipBlueprints;
  mails?: UserMails;
  stats?: CompanyStat;
  achievements?: UserAchievement;
  state?: UserState;
  cashShop?: UserCashShop;
  questManager?: UserQuestManager;
  sailing?: UserSailing;
  attendance?: UserAttendance;
  passEvent?: UserPassEvent;
  battle?: UserBattle;
  challenge?: UserChallenges;
  encount?: UserEncount;
  energy?: UserEnergy;
  shield?: UserShield;
  trade?: UserTrade;
  village?: UserVillage;
  userDiscovery?: UserDiscovery;
  explore?: UserExplore;
  tradeArea?: UserTradeArea;
  userGuild?: UserGuild;
  userEventRanking?: UserEventRanking;
  userDiscoveryReward?: UserDiscoveryReward;
  userFishing?: UserFishing;
  userTitles?: UserTitles;
  userSweepTicket?: UserSweepTicket;
  userPets?: UserPets;
  userSmuggle?: UserSmuggle;
  userInfiniteLighthouse?: UserInfiniteLighthouse;
  userClash?: UserClash;
  userReentry?: UserReentry;
  userManufacture?: UserManufacture;
}

export function cloneTryData(ori: TryData): TryData {
  const clone: TryData = {
    userExp: ori.userExp ? ori.userExp : undefined,
    userLevel: ori.userLevel ? ori.userLevel : undefined,
    nationCmsId: ori.nationCmsId ? ori.nationCmsId : undefined,
    lastUpdateNationTimeUtc: ori.lastUpdateNationTimeUtc ? ori.lastUpdateNationTimeUtc : undefined,
    points: ori.points ? ori.points.clone() : undefined,
    inven: ori.inven ? ori.inven.clone() : undefined,
    mates: ori.mates ? ori.mates.clone() : undefined,
    reputation: ori.reputation ? ori.reputation.clone() : undefined,
    fleets: ori.fleets ? ori.fleets.clone() : undefined,
    shipBlueprints: ori.shipBlueprints ? ori.shipBlueprints.clone() : undefined,
    mails: ori.mails ? ori.mails.clone() : undefined,
    stats: ori.stats ? ori.stats.clone() : undefined,
    achievements: ori.achievements ? ori.achievements.clone() : undefined,
    state: ori.state ? ori.state.clone() : undefined,
    cashShop: ori.cashShop ? ori.cashShop.clone() : undefined,
    questManager: ori.questManager ? ori.questManager.clone() : undefined,
    sailing: ori.sailing ? ori.sailing.clone() : undefined,
    attendance: ori.attendance ? ori.attendance.clone() : undefined,
    battle: ori.battle ? ori.battle.clone() : undefined,
    challenge: ori.challenge ? ori.challenge.clone() : undefined,
    encount: ori.encount ? ori.encount.clone() : undefined,
    energy: ori.energy ? ori.energy.clone() : undefined,
    shield: ori.shield ? ori.shield.clone() : undefined,
    trade: ori.trade ? ori.trade.clone() : undefined,
    village: ori.village ? ori.village.clone() : undefined,
    userDiscovery: ori.userDiscovery ? ori.userDiscovery.clone() : undefined,
    tradeArea: ori.tradeArea ? ori.tradeArea.clone() : undefined,
    userGuild: ori.userGuild ? ori.userGuild.clone() : undefined,
    userTitles: ori.userTitles ? ori.userTitles.clone() : undefined,
    userSweepTicket: ori.userSweepTicket ? ori.userSweepTicket.clone() : undefined,
    userPets: ori.userPets ? ori.userPets.clone() : undefined,
    userClash: ori.userClash ? ori.userClash.clone() : undefined,
    userReentry: ori.userReentry ? ori.userReentry.clone() : undefined,
    userManufacture: ori.userManufacture ? ori.userManufacture.clone() : undefined
  };

  return clone;
}

// ------------------------------------------------------------------------------------------------
// Changes.
// ------------------------------------------------------------------------------------------------
export interface Changes {
  // TODO: sync 자료형을 changes 에서 사용함으로써 명시적이지 않은 부분이 있음. 개선 필요할까?
  syncAdd: sync.All;
  syncRemove: any;

  cashPayments?: CashPayment[];
  cashGains?: CashGain[];

  daysForLoyaltyDecrease?: number;
  daysForTownReset?: number;
  nationEvents?: [number, number, number][];
  mateExp?: { [jobType: number]: MateExpChange[] };
  mateLoyalty?: { mateCmsId: number; value: number }[];
  lastMateEquipmentId?: number;
  lastShipId?: number;
  actualGainExp?: { [jobType: number]: number }; // 실제로 획득한 경험치
  totalGainFames?: { [jobType: number]: number }; // 획득 명성 ( 최대치 보정하지 않은, 업적 용도 )
  bResetTownState?: boolean;
  sailedDays?: number;
  addedSoundPacks?: number[];
  arrivalTownCmsId?: number;
  newMail?: MailCreatingParams[];
  lastShipSlotItemId?: number;
  battleIdForLogEnd?: string;
  gameOverLosses?: GameOverLosses;
  multiPvpLoss?: any;
  shipIdsToDismantle?: number[]; // 분해할 선박 ids
  firedSailors?: number; // 해고 선원 수
  lastQuestItemId?: number;
  addedPalaceTaxFreePermitCounts?: { [taxFreePermitCmsId: number]: number }; // ActualGain 을 위한 것
  bResetPalaceRoyalOrder?: boolean; // 칙명 초기화
  unequippedShipSlotItemIds?: number[]; // 선박 해체시  부품id 저장 (부품 창고 이동)
  discoveryCmsIds?: number[]; // discovery cms ids
  landExploreCmsId?: number; // 패킷 land_explore 호출 (업적 용도)
  mileages?: Mileage[];
  injuryDuration?: { [mateCmsId: number]: number };
  addedArenaTicket?: number;
  passEvents?: {
    [eventPageCmsId: number]: {
      eventPageCmsId: number;
      expChange?: { exp: number; level: number; addedExp: number };
      lastDailyResetTimeUtc?: number | null;
    };
  };
  passEventMissions?: {
    [eventPageCmsId: number]: {
      [eventMissionCmsId: number]: {
        eventPageCmsId: number;
        eventMissionCmsId: number;
        // 변경되는 게 있을 때만 설정
        count?: number;
        repeatedRewardReceiveCount?: number;
        isRewarded?: 0 | 1;
      };
    };
  };
  fleetDispatchLifeRemains?: { [fleetIndex: number]: number };
  bpSailMasteryforGlog?: {
    [cmsId: number]: {
      cmsId: number;
      oldLevel: number;
      oldExp: number;
      level: number;
      exp: number;
      addedExp: number;
    };
  };
  dailySubscriptions?: {
    [cmsId: number]: {
      dailySubscription: DailySubscription;
    };
  };

  addedEventRankingRewardIdx?: { [cmsId: number]: number[] };
  addedDiscoveryReward?: number[];

  addedShipCamouflages?: number[];

  addedFishCatchRewards?: { [cmsId: number]: FishCatch };

  addedUserTitles?: { [cmsId: number]: UserTitle };

  sweepTicket?: { count: number; buyCount: number };

  itemGainAccumParams?: { targets?: number[]; addedValue: number }[];

  addedMatePassives?: { mateCmsId: number; passiveCmsId: number }[];

  pubStaffChanges?: PubStaff[];

  questDropForGlogs?: { questCmsId: number; step: number }[];

  // actualGain에 point의 획득량만 넣기 위한 변수
  // mileages는 시간에 따라 값이 변하기 때문에 제외
  pointsGainAmount?: { [cmsId: number]: number };

  // glog에 포인트 소모량을 전달하기 위한 변수(음수로 저장)
  pointsConsumeAmountForGlog?: { [cmsId: number]: number };

  // energy의 소모량을 저장하기 위한 변수
  // 업적에서 사용
  energyConsumeAmount?: number;

  clash?: ClashSession;
  reentrys?: { [type: number]: Reentry };
  manufactureExpLevel?: { [type: number]: ManufactureExpLevelChange };
}

// ------------------------------------------------------------------------------------------------
// ActualGain.
// LURewardPopupWidget:_ConvertToRewardFixed.lua 의 주석과 내용이 일치해야 됨.
// ------------------------------------------------------------------------------------------------
export interface ActualGain {
  points?: { [cmsId: number]: number };
  items?: { [cmsId: number]: number };
  departSupplies?: { [cmsId: number]: number };
  tradeGoods?: { [cmsId: number]: number };
  smuggleGoods?: { [cmsId: number]: number };
  mateEquips?: { [cmsId: number]: number };
  mateEquipIds?: { [cmsId: number]: number[] };
  ships?: { [cmsId: number]: number };
  shipIds?: { [cmsId: number]: number[] };
  mates?: number[]; // No duplicate mates.
  sailors?: { [shipId: number]: number };
  shipBlueprints?: {
    [shipBlueprintCmsId: number]: {
      level?: number;
      exp?: number;
      sailMasteryLevel?: number;
      sailMasteryExp?: number;
    };
  };
  cashShopGachaBoxGuaranteeAccum?: { [cashShopCmsId: number]: number };
  soundPacks?: number[];
  shipSlotItems?: { [cmsId: number]: number };
  shipSlotItemIds?: { [cmsId: number]: number[] };
  questItems?: { [cmsId: number]: number };
  questItemsIds?: { [cmsId: number]: number[] };
  shields?: { [cmsId: number]: { nonPurchaseCount?: number; purchaseCount?: number } };

  admiralFame?: { [jobType: number]: number }; // 제독이 얻은 명성
  userExp?: number; // 선단이 얻은 경험치
  userLevel?: number; // 선단이 얻은 레벨(ex 2->4가 된 경우 2)
  mateExp?: { [mateCmsId: number]: { [jobType: number]: number } }; // 항해사가 얻은 경험치
  exp?: { [jobType: number]: number }; // 실제로 얻은 경험치
  mateIntimacy?: { [mateCmsId: number]: number };
  mateLoyalty?: { [mateCmsId: number]: number };

  taxFreePermits?: { [taxFreePermit: number]: number }; // 얻은 면세증 개수
  discoverCmsIds?: number[];

  arenaTicket?: number;

  westShipBuildExp?: number; // 얻은 서양 건조 경험치.
  orientShipBuildExp?: number; // 얻은 동양 건조 경험치.
  bpSailMasteryforGlog?: {
    [cmsId: number]: {
      cmsId: number;
      oldLevel: number;
      oldExp: number;
      level: number;
      exp: number;
      addedExp: number;
    };
  };

  freeSweepTicket?: number;
  buySweepTicket?: number;

  pets?: number[]; // no duplicate pets

  manufactureExp?: { [type: number]: number };
}

// ------------------------------------------------------------------------------------------------
// UserChangeTask
// ------------------------------------------------------------------------------------------------
export class UserChangeTask {
  private readonly _user: User;
  private _reason: CHANGE_TASK_REASON;
  private _rsn: string; // glog 변경 사유
  private _addRsn: string; // glog 추가적인 변경 사유

  // "Try" data to keep track of accumulative changes to the original data.
  private _tryData: TryData;

  // Changes to be commited. (Both to DB and to memory.)
  private _changes: Changes;

  // Actual amounts gained by this reward. (For non-fixed rewards.)
  private _actualGain: ActualGain;

  private _exchangeHash: string;

  private _spec: UserChangeSpec;

  // @addRsn glog 추가적인 변경 사요
  constructor(
    user: User,
    reason: CHANGE_TASK_REASON,
    spec: UserChangeSpec = null,
    addRsn: string = null
  ) {
    this._user = user;
    this._reason = reason;
    this._spec = spec;
    this._rsn = CHANGE_TASK_REASON[this._reason].toLowerCase();
    this._addRsn = addRsn;

    this._tryData = {};
    this._changes = {
      syncAdd: {},
      syncRemove: {},
    };
    this._actualGain = {};
  }

  setSpec(spec: UserChangeSpec): void {
    this._spec = spec;
  }

  trySpec(): CHANGE_TASK_RESULT {
    if (!this._spec) {
      return CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }
    return this._spec.accumulate(this._user, this._tryData, this._changes);
  }

  getTaskReason(): CHANGE_TASK_REASON {
    return this._reason;
  }

  // Apply the changes and return 'Sync'.
  apply(): Promise<sync.Sync> {
    const sync: sync.Sync = {
      add: this._changes.syncAdd,
      remove: this._changes.syncRemove,
    };

    // Update.
    return this._applyCashPayments()
      .then(() => {
        return this._applyToRDB();
      })
      .then(() => {
        return this._applyToRedis(); // TODO: 실패할 경우 롤백 처리.
      })
      .then(() => {
        return this._applyCashGains();
      })
      .then(() => {
        // calc actual gain.
        this._calcActualGain();

        // glog before apply to user
        this.glogBeforeApplyToUser();

        // Update memory.
        this._applyToUser(sync);

        // Build sync.
        this._buildSync(sync);

        // glog
        this.glog();

        return this._accumulateAchievements(sync);
      }) as Promise<sync.Sync>;
  }

  private _applyCashPayments(): Promise<any> {
    const cashPayments = this._changes.cashPayments;
    if (!cashPayments || cashPayments.findIndex((elem) => isCash(elem.cmsId)) === -1) {
      return Promise.resolve();
    }

    const sync: sync.Sync = {};
    // cash 는 user 에 직접 업데이트 한다.
    const exchangeHash = UserPoints.generateExchangeHash(this._user.userId);
    return this._user.userPoints
      .tryConsumeCashs(cashPayments, sync, this._user, {
        rsn: this._rsn,
        add_rsn: this._addRsn,
        user: this._user,
        exchangeHash,
      })
      .then(() => {
        this._exchangeHash = exchangeHash;

        if (sync.add) {
          _.merge<sync.All, sync.All>(this._changes.syncAdd, sync.add);
        }
      });
  }

  private _applyCashGains(): Promise<any> {
    const cashGains = this._changes.cashGains;
    if (!cashGains || cashGains.findIndex((elem) => isCash(elem.cmsId)) === -1) {
      return Promise.resolve();
    }

    const sync: sync.Sync = {};
    // cash 는 user 에 직접 업데이트 한다.
    return this._user.userPoints
      .tryAddCashs(cashGains, sync, this._user, {
        rsn: this._rsn,
        add_rsn: this._addRsn,
        user: this._user,
      })
      .then(() => {
        if (sync.add) {
          const actualGain = this._actualGain;
          if (!actualGain.points) {
            actualGain.points = {};
          }
          for (const elem of cashGains) {
            if (!actualGain.points[elem.cmsId]) {
              actualGain.points[elem.cmsId] = 0;
            }
            actualGain.points[elem.cmsId] += elem.amount;
          }

          _.merge<sync.All, sync.All>(this._changes.syncAdd, sync.add);
        }
      });
  }

  // Updates the DB data with the 'changes'.
  // [WARN] Refer to the TABLE_ACCESS_ORDER.json5 when building the query chain.
  protected _applyToRDB(): Promise<any> {
    const app = Container.get(LobbyService);
    const userDbPool = app.userDbConnPoolMgr.getPoolByShardId(this._user.getUserDbShardId());

    const queryChain = [];
    this._buildQueryChain(queryChain);

    // Go.
    return using(getSqlConnection(userDbPool), async (connection: PoolConnection) => {
      try {
        await connection.beginTransaction();
        const result = await promise.each(queryChain, (queryExec) => {
          return queryExec(connection);
        });
        return connection.commit().return(result);
      } catch (err) {
        return connection.rollback().throw(err);
      }
    });
  }

  protected _buildQueryChain(queryChain: any[]) {
    const userId = this._user.userId;

    // u_users
    if (this._changes.syncAdd.user) {
      if (this._changes.syncAdd.user.lastUpdateEnergyTimeUtc !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puUserUpdateEnergy(
            dbConn,
            userId,
            this._changes.syncAdd.points[cmsEx.EnergyPointCmsId].value,
            this._changes.syncAdd.user.lastUpdateEnergyTimeUtc
          );
        });
      }
      if (this._changes.syncAdd.user.lastUpdateManufacturePointTimeUtc !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puUserUpdateManufacturePoint(
            dbConn,
            userId,
            this._changes.syncAdd.points[cmsEx.ManufacturePointCmsId].value,
            this._changes.syncAdd.user.lastUpdateManufacturePointTimeUtc
          );
        });
      }
      if (this._changes.bResetPalaceRoyalOrder) {
        queryChain.push(
          (dbConn: query.Connection) => {
            return puUserUpdatePalaceRoyalOrderCmsIdAndRnds(dbConn, userId, null, null);
          },
          (dbConn: query.Connection) => {
            return puUserUpdatePalaceRoyalTitleOrderCmsIdAndRnds(dbConn, userId, null, null);
          }
        );
      }
      if (this._changes.syncAdd.user.nationCmsId !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puUserUpdateNation(
            dbConn,
            userId,
            this._changes.syncAdd.user.nationCmsId,
            this._changes.syncAdd.user.lastUpdateNationTimeUtc
          );
        });
      }
      if (this._changes.syncAdd.user.lastTicketCountUpdateTimeUtc !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puUserUpdateExploreTicket(
            dbConn,
            userId,
            this._changes.syncAdd.user.usedExploreTicketCount,
            this._changes.syncAdd.user.lastTicketCountUpdateTimeUtc
          );
        });
      }
      if (this._changes.syncAdd.user.lastExploreQuickModeCountUpdateTimeUtc != undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puUserUpdateExploreQuickMode(
            dbConn,
            userId,
            this._changes.syncAdd.user.usedExploreQuickModeCount,
            this._changes.syncAdd.user.lastExploreQuickModeCountUpdateTimeUtc
          );
        });
      }

      if (
        this._changes.syncAdd.user.westShipBuildLevel !== undefined &&
        this._changes.syncAdd.user.westShipBuildExp !== undefined &&
        this._changes.syncAdd.user.orientShipBuildLevel !== undefined &&
        this._changes.syncAdd.user.orientShipBuildExp !== undefined
      ) {
        queryChain.push((dbConn: query.Connection) => {
          return puUserUpdateShipBuild(
            dbConn,
            userId,
            this._changes.syncAdd.user.westShipBuildLevel,
            this._changes.syncAdd.user.westShipBuildExp,
            this._changes.syncAdd.user.orientShipBuildLevel,
            this._changes.syncAdd.user.orientShipBuildExp
          );
        });
      }

      if (this._changes.syncAdd.user.lastRoyalOrderCompletedTimeUtc) {
        queryChain.push((dbConn: query.Connection) => {
          return puUserUpdateLastRoyalOrderCompletedTimeUtc(
            dbConn,
            userId,
            this._changes.syncAdd.user.lastRoyalOrderCompletedTimeUtc
          );
        });
      }

      if (this._changes.syncAdd.user.lastPaidSmuggleEnterTownCmsId !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puLastPaidSmuggleEnterTownCmsIdUpdate(
            dbConn,
            userId,
            this._changes.syncAdd.user.lastPaidSmuggleEnterTownCmsId
          );
        });
      }
    }

    // u_states
    if (this._changes.syncAdd.user) {
      if (
        this._changes.syncAdd.user.gameState !== undefined &&
        this._changes.syncAdd.user.lastGameState !== undefined
      ) {
        queryChain.push((dbConn: query.Connection) => {
          return puStateUpdateGameStateLastGameState(
            dbConn,
            userId,
            this._changes.syncAdd.user.gameState,
            this._changes.syncAdd.user.lastGameState
          );
        });
      } else if (this._changes.syncAdd.user.gameState !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puStateUpdateGameState(dbConn, userId, this._changes.syncAdd.user.gameState);
        });
      } else if (this._changes.syncAdd.user.lastGameState !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puStateUpdateGameState(dbConn, userId, this._changes.syncAdd.user.lastGameState);
        });
      }
    }

    // u_soft_data
    if (
      this._changes.syncAdd.user &&
      this._changes.syncAdd.user.exp !== undefined &&
      this._changes.syncAdd.user.level !== undefined
    ) {
      // exp, level 는 항상 같이 변경되어야 된다. 둘 중 하나만 값이 들어가 있으면 안 됨
      queryChain.push((dbConn: query.Connection) => {
        return puSoftDataUpdateExpLevel(
          dbConn,
          userId,
          this._changes.syncAdd.user.exp,
          this._changes.syncAdd.user.level
        );
      });
    }
    if (this._changes.syncAdd.user && this._changes.syncAdd.user.lastTownCmsId !== undefined) {
      queryChain.push((dbConn: query.Connection) => {
        return puSoftDataUpdateLastTownCmsId(
          dbConn,
          userId,
          this._changes.syncAdd.user.lastTownCmsId
        );
      });
    }
    if (this._changes.arrivalTownCmsId !== undefined) {
      queryChain.push((dbConn: query.Connection) => {
        return puSoftDataUpdateArrivalTownCmsId(dbConn, userId, this._changes.arrivalTownCmsId);
      });
    }
    if (
      this._changes.syncAdd.user &&
      this._changes.syncAdd.user.usedFreeTurnTakebackCount !== undefined &&
      this._changes.syncAdd.user.usedFreePhaseTakebackCount !== undefined &&
      this._changes.syncAdd.user.lastFreeTakebackUpdateTimeUtc !== undefined
    ) {
      // usedFreeTurnTakebackCount, usedFreePhaseTakebackCount, lastFreeTakebackUpdateTimeUtc 는 항상 같이 변경되어야 된다.
      // 모두 값이 들어가야됨.
      queryChain.push((dbConn: query.Connection) => {
        return puSoftDataUpdateFreeTakeback(
          dbConn,
          userId,
          this._changes.syncAdd.user.usedFreeTurnTakebackCount,
          this._changes.syncAdd.user.usedFreePhaseTakebackCount,
          this._changes.syncAdd.user.lastFreeTakebackUpdateTimeUtc
        );
      });
    }

    if (
      this._changes.syncAdd.user &&
      this._changes.syncAdd.user.quickModeCount !== undefined &&
      this._changes.syncAdd.user.lastQuickModeCountUpdateTimeUtc !== undefined
    ) {
      const quickModeChange: BattleQuickModeChange = {
        quickModeCount: this._changes.syncAdd.user.quickModeCount,
        updateTimeUtc: this._changes.syncAdd.user.lastQuickModeCountUpdateTimeUtc,
      };
      queryChain.push((dbConn: query.Connection) => {
        return puSoftDataUpdateQuickMode(dbConn, userId, quickModeChange);
      });
    }

    // u_points
    if (this._changes.syncAdd.points) {
      _.forOwn(this._changes.syncAdd.points, (elem) => {
        if (elem.cmsId === cmsEx.EnergyPointCmsId || isCash(elem.cmsId)) {
          return;
        }

        if (this._user.userPoints.getPoint(elem.cmsId) !== elem.value) {
          queryChain.push((dbConn: query.Connection) => {
            return puPointUpdate(dbConn, userId, {
              cmsId: elem.cmsId,
              value: elem.value,
            });
          });
        }
      });
    }
    if (this._changes.mileages) {
      for (const elem of this._changes.mileages) {
        if (elem.value === 0) {
          queryChain.push((dbConn: query.Connection) => {
            return puMileageDelete(dbConn, userId, elem.month);
          });
        } else {
          queryChain.push((dbConn: query.Connection) => {
            return puMileageUpdate(dbConn, userId, elem.month, elem.value);
          });
        }
      }
    }

    // u_items
    if (this._changes.syncAdd.items) {
      let params: { cmsId: number; count: number; unboundCount: number }[] = [];

      for (const key in this._changes.syncAdd.items) {
        const elem = this._changes.syncAdd.items[key];
        params.push({ cmsId: elem.cmsId, count: elem.count, unboundCount: elem.unboundCount });

        // 8개씩 묶어서 처리
        if (params.length === 8) {
          const copiedParams = _.cloneDeep(params);
          queryChain.push((dbConn: query.Connection) => {
            return puItemUpdate_8(
              dbConn,
              userId,
              copiedParams[0].cmsId,
              copiedParams[0].count,
              copiedParams[0].unboundCount,
              copiedParams[1].cmsId,
              copiedParams[1].count,
              copiedParams[1].unboundCount,
              copiedParams[2].cmsId,
              copiedParams[2].count,
              copiedParams[2].unboundCount,
              copiedParams[3].cmsId,
              copiedParams[3].count,
              copiedParams[3].unboundCount,
              copiedParams[4].cmsId,
              copiedParams[4].count,
              copiedParams[4].unboundCount,
              copiedParams[5].cmsId,
              copiedParams[5].count,
              copiedParams[5].unboundCount,
              copiedParams[6].cmsId,
              copiedParams[6].count,
              copiedParams[6].unboundCount,
              copiedParams[7].cmsId,
              copiedParams[7].count,
              copiedParams[7].unboundCount
            );
          });
          params = []; // 배열 초기화
        }
      }

      // 남은 아이템 처리
      if (params.length > 0) {
        // Fill missing parameters with nulls or appropriate default values
        while (params.length < 8) {
          params.push({ cmsId: null, count: null, unboundCount: null });
        }

        const copiedParams = _.cloneDeep(params);
        queryChain.push((dbConn: query.Connection) => {
          return puItemUpdate_8(
            dbConn,
            userId,
            copiedParams[0]?.cmsId ?? null,
            copiedParams[0]?.count ?? null,
            copiedParams[0]?.unboundCount ?? null,
            copiedParams[1]?.cmsId ?? null,
            copiedParams[1]?.count ?? null,
            copiedParams[1]?.unboundCount ?? null,
            copiedParams[2]?.cmsId ?? null,
            copiedParams[2]?.count ?? null,
            copiedParams[2]?.unboundCount ?? null,
            copiedParams[3]?.cmsId ?? null,
            copiedParams[3]?.count ?? null,
            copiedParams[3]?.unboundCount ?? null,
            copiedParams[4]?.cmsId ?? null,
            copiedParams[4]?.count ?? null,
            copiedParams[4]?.unboundCount ?? null,
            copiedParams[5]?.cmsId ?? null,
            copiedParams[5]?.count ?? null,
            copiedParams[5]?.unboundCount ?? null,
            copiedParams[6]?.cmsId ?? null,
            copiedParams[6]?.count ?? null,
            copiedParams[6]?.unboundCount ?? null,
            copiedParams[7]?.cmsId ?? null,
            copiedParams[7]?.count ?? null,
            copiedParams[7]?.unboundCount ?? null
          );
        });
      }
    }

    // u_quest_item_last_ids, u_quest_items
    if (this._changes.lastQuestItemId !== undefined) {
      queryChain.push((dbConn: query.Connection) => {
        return puQuestItemLastIdUpdate(dbConn, userId, this._changes.lastQuestItemId);
      });

      // new quest item
      for (
        let i = this._user.userInven.itemInven.getLastQuestItemId() + 1;
        i <= this._changes.lastQuestItemId;
        i++
      ) {
        queryChain.push((dbConn: query.Connection) => {
          const questItem = this._changes.syncAdd.questItems[i];
          return puQuestItemCreate(dbConn, userId, {
            id: questItem.id,
            cmsId: questItem.cmsId,
            questCmsId: questItem.questCmsId,
            questRnds: questItem.questRnds,
          });
        });
      }
    }

    // u_shields
    if (this._changes.syncAdd.shields) {
      _.forOwn(this._changes.syncAdd.shields, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puShieldUpdate(dbConn, userId, {
            cmsId: elem.cmsId,
            dailyFreeCount: elem.dailyFreeCount,
            lastDailyChargeTimeUtc: elem.lastDailyChargeTimeUtc,
            nonPurchaseCount: elem.nonPurchaseCount,
            purchaseCount: elem.purchaseCount,
            isActivated: elem.isActivated,
          });
        });
      });
    }

    // u_challenges
    if (this._changes.syncAdd.challenges) {
      _.forOwn(this._changes.syncAdd.challenges, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puChallengeUpdate(dbConn, userId, elem);
        });
      });
    }

    // u_ship_blueprints
    if (this._changes.syncAdd.shipBlueprints) {
      _.forOwn(this._changes.syncAdd.shipBlueprints, (elem) => {
        if (
          elem.level !== undefined &&
          elem.exp !== undefined &&
          elem.sailMasteryLevel !== undefined &&
          elem.sailMasteryExp !== undefined
        ) {
          queryChain.push((dbConn: query.Connection) => {
            return puShipBlueprintUpdate(
              dbConn,
              userId,
              elem.cmsId,
              elem.level,
              elem.exp,
              elem.sailMasteryLevel,
              elem.sailMasteryExp
            );
          });
        } else {
          if (elem.level !== undefined && elem.exp !== undefined) {
            queryChain.push((dbConn: query.Connection) => {
              return puShipBlueprintUpdateLevel(dbConn, userId, elem.cmsId, elem.level, elem.exp);
            });
          }

          if (elem.sailMasteryLevel !== undefined && elem.sailMasteryExp !== undefined) {
            queryChain.push((dbConn: query.Connection) => {
              return puShipBlueprintUpdateSailMasteryExpLevel(
                dbConn,
                userId,
                elem.cmsId,
                elem.sailMasteryLevel,
                elem.sailMasteryExp
              );
            });
          }
        }
      });
    }

    // u_reputations
    if (this._changes.syncAdd.reputations) {
      _.forOwn(this._changes.syncAdd.reputations, (elem, nationCmsIdStr) => {
        queryChain.push((dbConn: query.Connection) => {
          return puReputationUpdate(
            dbConn,
            userId,
            parseInt(nationCmsIdStr, 10),
            elem.reputation,
            elem.updateTimeUtc
          );
        });
      });
    }

    // u_quest_contexts
    if (this._changes.syncAdd.questData && this._changes.syncAdd.questData.contexts) {
      _.forOwn(this._changes.syncAdd.questData.contexts, (ctx) => {
        // flags
        // uflags, lflags 는 항상 같이 변경되어야 된다. 둘 중 하나만 값이 들어가 있으면 안 됨
        if (ctx.uflags !== undefined && ctx.lflags !== undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puQuestContextUpdateAllFlags(dbConn, userId, ctx.cmsId, ctx.uflags, ctx.lflags);
          });
        }
      });
    }
    if (this._changes.syncRemove.questData && this._changes.syncRemove.questData.contexts) {
      _.forOwn(this._changes.syncRemove.questData.contexts, (elem, questCmsIdStr) => {
        if (elem === true) {
          // drop quest
          queryChain.push((dbConn: query.Connection) => {
            return puQuestContextDelete(dbConn, userId, parseInt(questCmsIdStr, 10));
          });
        }
      });
    }

    // u_sailing
    if (
      this._changes.daysForLoyaltyDecrease ||
      this._changes.daysForTownReset ||
      (this._changes.syncAdd.user && this._changes.syncAdd.user.totalSailedDays)
    ) {
      queryChain.push((dbConn: query.Connection) => {
        return puSailingUpdateDays(
          dbConn,
          userId,
          this._changes.daysForLoyaltyDecrease === undefined
            ? this._user.userSailing.daysForLoyaltyDecrease
            : this._changes.daysForLoyaltyDecrease,
          this._changes.daysForTownReset === undefined
            ? this._user.userSailing.daysForTownReset
            : this._changes.daysForTownReset,
          this._changes.syncAdd.user && this._changes.syncAdd.user.totalSailedDays !== undefined
            ? this._changes.syncAdd.user.totalSailedDays
            : this._user.userSailing.totalSailedDays
        );
      });
    }

    // u_insurances
    if (this._changes.syncAdd.insurance) {
      const insuranceChange = this._changes.syncAdd.insurance;
      // unpaid 는 항상 모든 값이 존재해야 됨.
      if (
        insuranceChange.unpaidDucat !== undefined &&
        insuranceChange.unpaidSailor !== undefined &&
        insuranceChange.unpaidShip !== undefined &&
        insuranceChange.unpaidTradeGoods !== undefined
      ) {
        queryChain.push((dbConn: query.Connection) => {
          return puInsuranceUpdateUnpaid(dbConn, userId, insuranceChange);
        });
      } else if (insuranceChange.insuranceCmsId !== undefined) {
        queryChain.push((dbConn: query.Connection) => {
          return puInsuranceUpdateCmsId(dbConn, userId, insuranceChange.insuranceCmsId);
        });
      }
    }

    // u_town_states
    if (this._changes.bResetTownState) {
      queryChain.push((dbConn: query.Connection) => {
        return puTownStateDeleteAll(dbConn, userId);
      });
    }

    // u_mates
    if (this._changes.syncAdd.mates) {
      _.forOwn(this._changes.syncAdd.mates, (mate) => {
        // create
        if (!this._user.userMates.getMate(mate.cmsId)) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateCreate(dbConn, userId, mate.cmsId, mate.loyalty, mate.royalTitle);
          });

          // 항해사가 새로 만들어지는 경우에는
          // - stateFlags
          // - 명성
          // - 충성도
          // - 부상 시간
          // 업데이트를 하지 않는다.
          return;
        }

        // loyalty, stateFlags
        if (mate.loyalty !== undefined && mate.loyalty !== null && mate.stateFlags !== undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateUpdateLoyaltiesAndStateFlags(dbConn, userId, [mate]);
          });
        } else if (mate.loyalty !== undefined && mate.loyalty !== null) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateUpdateLoyalties(dbConn, userId, [
              {
                mateCmsId: mate.cmsId,
                value: mate.loyalty,
              },
            ]);
          });
        } else if (mate.stateFlags !== undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateUpdateStateFlags(dbConn, userId, mate.cmsId, mate.stateFlags);
          });
        }

        if (mate.injuryExpireTimeUtc !== undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateUpdateInjuryExpireTimeUtc(
              dbConn,
              userId,
              mate.cmsId,
              mate.injuryExpireTimeUtc
            );
          });
        }

        // adventure fame
        if (mate.adventureFame) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateUpdateFame(
              dbConn,
              userId,
              mate.cmsId,
              cmsEx.JOB_TYPE.ADVENTURE,
              mate.adventureFame
            );
          });
        }

        // trade fame
        if (mate.tradeFame) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateUpdateFame(
              dbConn,
              userId,
              mate.cmsId,
              cmsEx.JOB_TYPE.TRADE,
              mate.tradeFame
            );
          });
        }

        // battle fame
        if (mate.battleFame) {
          queryChain.push((dbConn: query.Connection) => {
            return puMateUpdateFame(
              dbConn,
              userId,
              mate.cmsId,
              cmsEx.JOB_TYPE.BATTLE,
              mate.battleFame
            );
          });
        }
      });
    }

    // 항해사 경험치, 충성도 update 는 다수의 쿼리가 발생할 수 있기 때문에 따로 모아서 처리.
    if (this._changes.mateExp) {
      _.forOwn(this._changes.mateExp, (changes, jobtypeStr) => {
        queryChain.push((dbConn: query.Connection) => {
          return puMateUpdateExpLevel(dbConn, userId, parseInt(jobtypeStr, 10), changes);
        });
      });
    }
    if (this._changes.mateLoyalty && this._changes.mateLoyalty.length > 0) {
      queryChain.push((dbConn: query.Connection) => {
        return puMateUpdateLoyalties(dbConn, userId, this._changes.mateLoyalty);
      });
    }

    // u_mate_equipment_last_ids, u_mate_equipments
    if (this._changes.lastMateEquipmentId) {
      queryChain.push((dbConn: query.Connection) => {
        return puMateEquipmentUpdateLastId(dbConn, userId, this._changes.lastMateEquipmentId);
      });

      for (
        let i = this._user.userMates.getLastMateEquipmentId() + 1;
        i <= this._changes.lastMateEquipmentId;
        i++
      ) {
        const equip = this._changes.syncAdd.mateEquipments[i];
        queryChain.push((dbConn: query.Connection) => {
          return puMateEquipmentCreate(
            dbConn,
            userId,
            i,
            equip.cmsId,
            equip.isBound,
            equip.expireTimeUtc,
            equip.enchantLv,
            equip.equippedMateCmsId ? equip.equippedMateCmsId : 0
          );
        });
      }
    }

    if (this._changes.syncRemove.mateEquipments) {
      for (const mateEquipIdStr of this._changes.syncRemove.mateEquipments) {
        queryChain.push((dbConn: query.Connection) => {
          return puMateEquipmentDelete(dbConn, userId, parseInt(mateEquipIdStr, 10));
        });
      }
    }

    // u_mate_passives
    if (this._changes.addedMatePassives) {
      for (const elem of this._changes.addedMatePassives) {
        queryChain.push((dbConn: query.Connection) => {
          return puMatePassiveCreate(dbConn, userId, elem.mateCmsId, elem.passiveCmsId);
        });
      }
    }

    // u_unemployed_mates
    if (this._changes.syncAdd.unemployedMates) {
      _.forOwn(this._changes.syncAdd.unemployedMates, (mate) => {
        if (mate.intimacy === undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puUnemployedMateUpdateIsMet(dbConn, userId, mate.mateCmsId, mate.isMet);
          });
        } else {
          queryChain.push((dbConn: query.Connection) => {
            return puUnemployedMateUpdateIntimacy(dbConn, userId, mate.mateCmsId, mate.intimacy);
          });
        }
      });
    }
    if (this._changes.syncRemove.unemployedMates) {
      _.forOwn(this._changes.syncRemove.unemployedMates, (elem, mateCmsIdStr) => {
        if (elem === true) {
          queryChain.push((dbConn: query.Connection) => {
            return puUnemployedMateDelete(dbConn, userId, parseInt(mateCmsIdStr, 10));
          });
        }
      });
    }

    // u_fleets
    if (this._changes.fleetDispatchLifeRemains) {
      _.forOwn(this._changes.fleetDispatchLifeRemains, (remain, fleetIndexStr) => {
        queryChain.push((dbConn: query.Connection) => {
          return puFleetUpdateDispatchReduceLifeRemain(
            dbConn,
            userId,
            parseInt(fleetIndexStr, 10),
            remain
          );
        });
      });
    }

    // u_ship_last_ids, u_ships
    if (this._changes.lastShipId) {
      queryChain.push((dbConn: query.Connection) => {
        return puShipUpdateLastId(dbConn, userId, this._changes.lastShipId);
      });

      // new ship
      for (let i = this._user.userFleets.getLastShipId() + 1; i <= this._changes.lastShipId; i++) {
        queryChain.push((dbConn: query.Connection) => {
          const ship = this._changes.syncAdd.ships[i];
          return puShipCreate(dbConn, userId, {
            id: ship.id,
            cmsId: ship.cmsId,
            assignment: ship.assignment,
            fleetIndex: ship.fleetIndex,
            formationIndex: ship.formationIndex,
            durability: ship.durability,
            sailor: ship.sailor,
            name: ship.name,
            life: ship.life,
            enchantedStats: ship.enchantedStats,
            enchantResult: ship.enchantResult,
            enchantCount: ship.enchantCount,
            rndStats: ship.rndStats,
            isBound: ship.isBound,
            guid: ship.guid,
          });
        });
      }
    }

    _.forOwn(this._changes.syncAdd.ships, (ship) => {
      // sailor
      if (ship.sailor !== undefined && this._user.userFleets.getShip(ship.id)) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipUpdateSailor(dbConn, userId, ship.id, ship.sailor);
        });
      }

      // durability
      if (ship.durability !== undefined && this._user.userFleets.getShip(ship.id)) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipUpdateDurability(dbConn, userId, ship.id, ship.durability);
        });
      }

      // formationIndex
      if (ship.formationIndex !== undefined && this._user.userFleets.getShip(ship.id)) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipUpdateFormationIndex(dbConn, userId, ship.id, ship.formationIndex);
        });
      }

      // life
      if (ship.life !== undefined && this._user.userFleets.getShip(ship.id)) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipUpdateLife(dbConn, userId, ship.id, ship.life);
        });
      }
    });

    // 선박 삭제
    if (this._changes.syncRemove.ships) {
      for (const shipIdStr of this._changes.syncRemove.ships) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipDelete(dbConn, userId, parseInt(shipIdStr, 10));
        });
      }
    }

    // 선박 분해로 인한 선박 삭제
    if (this._changes.shipIdsToDismantle) {
      for (const shipId of this._changes.shipIdsToDismantle) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipDelete(dbConn, userId, shipId);
        });
      }
    }

    // u_ship_slots
    if (this._changes.syncAdd.ships) {
      const shipSlotChangesForLog: ShipSlotChange[] = [];

      _.forOwn(this._changes.syncAdd.ships, (ship) => {
        if (ship.slots) {
          _.forOwn(ship.slots, (slot) => {
            const shipSlotChange: ShipSlotChange = {
              shipId: ship.id,
              slotIndex: slot.slotIndex,
              mateCmsId: slot.mateCmsId,
              isLocked: slot.isLocked,
              shipSlotItemId: slot.shipSlotItemId,
            };
            shipSlotChangesForLog.push(shipSlotChange);

            queryChain.push((dbConn: query.Connection) => {
              return puShipSlotUpdate(dbConn, userId, ship.id, {
                slotIndex: slot.slotIndex,
                mateCmsId: slot.mateCmsId,
                isLocked: slot.isLocked,
                shipSlotItemId: slot.shipSlotItemId,
              });
            });
          });
        }
      });

      // u_ship_slots shipSlotItemId 컬럼 체크용 로그
      mlog.info('shipSlotChange userChangeTask', { userId, shipSlotChange: shipSlotChangesForLog });
    }

    // 선박 삭제
    if (this._changes.syncRemove.ships) {
      for (const shipIdStr of this._changes.syncRemove.ships) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipSlotsDelete(dbConn, userId, parseInt(shipIdStr, 10));
        });
      }
    }

    // 선박 분해로 인한 선박 삭제
    if (this._changes.shipIdsToDismantle) {
      for (const shipId of this._changes.shipIdsToDismantle) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipSlotsDelete(dbConn, userId, shipId);
        });
      }
    }

    // u_ship_cargos
    if (this._changes.syncAdd.ships) {
      _.forOwn(this._changes.syncAdd.ships, (ship) => {
        if (ship.cargos) {
          _.forOwn(ship.cargos, (cargo) => {
            const userShip = this._user.userFleets.getShip(ship.id);
            if (userShip) {
              const curCargo = userShip.getCargo(cargo.cmsId);
              if (
                curCargo.quantity === cargo.quantity &&
                curCargo.pointInvested === cargo.pointInvested
              ) {
                // mlog.warn('[TEMP] userChangeTask._applyToRDB: no change', {
                //   userId: this._user.userId,
                //   shipId: ship.id,
                //   cargo,
                // });
                return;
              }
            }
            queryChain.push((dbConn: query.Connection) => {
              return puShipCargoUpdate(dbConn, userId, {
                shipId: ship.id,
                cmsId: cargo.cmsId,
                quantity: cargo.quantity,
                pointInvested: cargo.pointInvested,
              });
            });
          });
        }
      });
    }

    // 선박 삭제
    if (this._changes.syncRemove.ships) {
      for (const shipIdStr of this._changes.syncRemove.ships) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipCargosDelete(dbConn, userId, parseInt(shipIdStr, 10));
        });
      }
    }

    // 선박 분해로 인한 선박 삭제
    if (this._changes.shipIdsToDismantle) {
      for (const shipId of this._changes.shipIdsToDismantle) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipCargosDelete(dbConn, userId, shipId);
        });
      }
    }

    // u_ship_slot_item_last_ids, u_ship_slot_items
    if (this._changes.lastShipSlotItemId) {
      queryChain.push((dbConn: query.Connection) => {
        return puShipSlotItemLastIdUpdate(dbConn, userId, this._changes.lastShipSlotItemId);
      });

      // new ship slot item
      for (
        let i = this._user.userInven.getLastShipSlotItemId() + 1;
        i <= this._changes.lastShipSlotItemId;
        i++
      ) {
        queryChain.push((dbConn: query.Connection) => {
          const shipSlotItem = this._changes.syncAdd.shipSlotItems[i];
          return puShipSlotItemCreate(
            dbConn,
            userId,
            shipSlotItem.id,
            shipSlotItem.shipSlotCmsId,
            shipSlotItem.isBound,
            shipSlotItem.isLocked,
            shipSlotItem.expireTimeUtc,
            shipSlotItem.enchantLv
          );
        });
      }
    }
    if (this._changes.syncRemove.shipSlotItems) {
      for (const idStr of this._changes.syncRemove.shipSlotItems) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipSlotItemDelete(dbConn, userId, parseInt(idStr, 10));
        });
      }
    }

    // u_tax_free_permits
    if (this._changes.syncAdd.taxFreePermits) {
      _.forOwn(this._changes.syncAdd.taxFreePermits, (taxFreePermit) => {
        queryChain.push((dbConn: query.Connection) => {
          return puTaxFreePermitUpdateExpirationTimeUtc(
            dbConn,
            userId,
            taxFreePermit.cmsId,
            taxFreePermit.expirationTimeUtc
          );
        });
      });
    }

    // u_battles
    if (this._changes.syncAdd.battleEndResult) {
      queryChain.push((dbConn: query.Connection) => {
        return puBattleUpdateBattleEndResult(
          dbConn,
          userId,
          JSON.stringify(this._changes.syncAdd.battleEndResult)
        );
      });
    }

    // u_battle_rewards
    if (this._changes.syncAdd.battleRewards) {
      for (const reward of this._changes.syncAdd.battleRewards) {
        queryChain.push((dbConn: query.Connection) => {
          return puBattleRewardUpdate(dbConn, userId, reward);
        });
      }
    }

    // u_game_over_losses
    if (this._changes.syncRemove.gameOverLosses) {
      queryChain.push((dbConn: query.Connection) => {
        return puGameOverLossUpdate(dbConn, userId, null);
      });
    }

    if (this._changes.gameOverLosses) {
      queryChain.push((dbConn: query.Connection) => {
        return puGameOverLossUpdate(dbConn, userId, JSON.stringify(this._changes.gameOverLosses));
      });
    }

    if (this._changes.syncRemove.multiPvpLoss) {
      queryChain.push((dbConn: query.Connection) => {
        return puGameOverLossUpdateMultiPvpLoss(dbConn, userId, null);
      });
    }

    if (this._changes.multiPvpLoss) {
      queryChain.push((dbConn: query.Connection) => {
        return puGameOverLossUpdateMultiPvpLoss(
          dbConn,
          userId,
          JSON.stringify(this._changes.multiPvpLoss)
        );
      });
    }

    // u_cash_shop_restricted_products
    if (this._changes.syncRemove.cashShopRestrictedProducts) {
      for (const cmsIdStr of this._changes.syncRemove.cashShopRestrictedProducts) {
        queryChain.push((dbConn: query.Connection) => {
          return puCashShopRestrictedProductDelete(dbConn, userId, parseInt(cmsIdStr, 10));
        });
      }
    }
    if (this._changes.syncAdd.cashShopRestrictedProducts) {
      _.forOwn(this._changes.syncAdd.cashShopRestrictedProducts, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puCashShopRestrictedProductUpdate(dbConn, userId, {
            cmsId: elem.cmsId,
            amount: elem.amount,
            lastBuyingTimeUtc: elem.lastBuyingTimeUtc,
          });
        });
      });
    }

    //u_cash_shop_fixed_term_products
    if (this._changes.syncRemove.cashShopFixedTermProducts) {
      for (const cmsIdStr of this._changes.syncRemove.cashShopFixedTermProducts) {
        queryChain.push((dbConn: query.Connection) => {
          return puCashShopFixedTermProductDelete(dbConn, userId, parseInt(cmsIdStr, 10));
        });
      }
    }
    if (this._changes.syncAdd.cashShopFixedTermProducts) {
      _.forOwn(this._changes.syncAdd.cashShopFixedTermProducts, (elem) => {
        // mlog.warn('[TEMP] puCashShopFixedTermProductUpdate', {
        //   old: this._user.userCashShop.getFixedTermProduct(elem.cmsId, mutil.curTimeUtc()),
        //   new: {
        //     cmsId: elem.cmsId,
        //     startTimeUtc: elem.startTimeUtc,
        //     endTimeUtc: elem.endTimeUtc,
        //   },
        // });
        queryChain.push((dbConn: query.Connection) => {
          return puCashShopFixedTermProductUpdate(dbConn, userId, {
            cmsId: elem.cmsId,
            startTimeUtc: elem.startTimeUtc,
            endTimeUtc: elem.endTimeUtc,
          });
        });
      });
    }

    // u_cash_shop_gacha_box_guarantees
    if (this._changes.syncAdd.cashShopGachaBoxGuarantees) {
      _.forOwn(this._changes.syncAdd.cashShopGachaBoxGuarantees, (elem, cmsIdStr) => {
        queryChain.push((dbConn: query.Connection) => {
          return puCashShopGachaBoxGuaranteeUpdate(dbConn, userId, parseInt(cmsIdStr, 10), elem);
        });
      });
    }

    // u_sound_packs
    if (this._changes.syncAdd.soundPacks) {
      _.forOwn(this._changes.syncAdd.soundPacks, (idxField, offsetStr) => {
        queryChain.push((dbConn: query.Connection) => {
          return puSoundPackUpdate(dbConn, userId, parseInt(offsetStr, 10), idxField);
        });
      });
    }

    // u_ship_camouflages
    if (this._changes.addedShipCamouflages) {
      for (const cmsId of this._changes.addedShipCamouflages) {
        queryChain.push((dbConn: query.Connection) => {
          return puShipCamouflageUpdate(dbConn, userId, Math.floor(cmsId / 32), cmsId % 32);
        });
      }
    }

    // u_contribution_shop_restricted_product
    if (this._changes.syncRemove.contributionShopRestrictedProducts) {
      for (const cmsIdStr of this._changes.syncRemove.contributionShopRestrictedProducts) {
        queryChain.push((dbConn: query.Connection) => {
          return puContributionShopRestrictedProductDelete(dbConn, userId, parseInt(cmsIdStr, 10));
        });
      }
    }
    if (this._changes.syncAdd.contributionShopRestrictedProducts) {
      _.forOwn(this._changes.syncAdd.contributionShopRestrictedProducts, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puContributionShopRestrictedProductUpdate(dbConn, userId, {
            cmsId: elem.cmsId,
            amount: elem.amount,
            lastBuyingTimeUtc: elem.lastBuyingTimeUtc,
          });
        });
      });
    }

    // u_guild_shop_restricted_product
    if (this._changes.syncRemove.guildShopRestrictedProducts) {
      for (const cmsIdStr of this._changes.syncRemove.guildShopRestrictedProducts) {
        queryChain.push((dbConn: query.Connection) => {
          return puGuildShopRestrictedProductDelete(dbConn, userId, parseInt(cmsIdStr, 10));
        });
      }
    }
    if (this._changes.syncAdd.guildShopRestrictedProducts) {
      _.forOwn(this._changes.syncAdd.guildShopRestrictedProducts, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puGuildShopRestrictedProductUpdate(dbConn, userId, {
            cmsId: elem.cmsId,
            amount: elem.amount,
            lastBuyingTimeUtc: elem.lastBuyingTimeUtc,
          });
        });
      });
    }

    // u_discoveries
    if (this._changes.discoveryCmsIds && this._changes.discoveryCmsIds.length > 0) {
      for (const cmsId of this._changes.discoveryCmsIds) {
        queryChain.push((dbConn: query.Connection) => {
          return puDiscoveryAdd(dbConn, userId, Math.floor(cmsId / 32), cmsId % 32);
        });
      }
    }

    // u_villages
    _.forOwn(this._changes.syncAdd.villages, (village) => {
      queryChain.push((dbConn: query.Connection) => {
        return puVillageUpdate(dbConn, userId, village);
      });
    });

    // u_achievements
    if (this._changes.syncAdd.achievements) {
      _.forOwn(this._changes.syncAdd.achievements, (elem) => {
        // is rewarded
        if (elem.isRewarded === 1) {
          queryChain.push((dbConn: query.Connection) => {
            return puAchievementUpdateIsRewarded(
              dbConn,
              this._user.userId,
              elem.cmsId,
              elem.isRewarded
            );
          });
        }
      });
    }

    if (
      this._changes.syncAdd.user &&
      this._changes.syncAdd.user.lastRewardedAchievementPointCmsId !== undefined
    ) {
      queryChain.push((dbConn: query.Connection) => {
        return puUserUpdateLastRewardedAchievementPointCmsId(
          dbConn,
          this._user.userId,
          this._changes.syncAdd.user.lastRewardedAchievementPointCmsId
        );
      });
    }

    // u_tasks
    if (this._changes.syncAdd.tasks) {
      _.forOwn(this._changes.syncAdd.tasks, (categoryTasks, categoryStr) => {
        let taskCountChange: TaskCountChange;
        _.forOwn(categoryTasks.tasks, (task) => {
          //  task is rewarded
          if (task.isRewarded === 1) {
            queryChain.push((dbConn: query.Connection) => {
              return puTaskUpdateIsRewarded(
                dbConn,
                userId,
                parseInt(categoryStr, 10),
                task.index,
                task.isRewarded
              );
            });
          }

          if (task.count > 0) {
            if (!taskCountChange) {
              taskCountChange = {
                category: parseInt(categoryStr, 10),
                cmsIds: [],
                counts: [],
                indexes: [],
              };
            }

            taskCountChange.cmsIds.push(task.cmsId);
            taskCountChange.counts.push(task.count);
            taskCountChange.indexes.push(task.index);
          }
        });

        if (taskCountChange) {
          queryChain.push((dbConn: query.Connection) => {
            return puTaskUpdateCount(dbConn, userId, taskCountChange);
          });
        }

        // task category is rewarded
        if (categoryTasks.isCategoryRewarded === 1) {
          queryChain.push((dbConn: query.Connection) => {
            return puTaskUpdateIsCategoryRewarded(
              dbConn,
              this._user.userId,
              parseInt(categoryStr, 10),
              categoryTasks.isCategoryRewarded
            );
          });
        }
      });
    }

    // u_event_missions
    if (this._changes.syncAdd.eventPages) {
      _.forOwn(this._changes.syncAdd.eventPages, (eventPage, eventPageCmsId) => {
        _.forOwn(eventPage.eventMissions, (eventMission, eventMissionCmsId) => {
          queryChain.push((dbConn: query.Connection) => {
            return puEventMissionUpdateIsRewarded(
              dbConn,
              this._user.userId,
              parseInt(eventPageCmsId, 10),
              parseInt(eventMissionCmsId, 10),
              eventMission.isRewarded
            );
          });
        });
      });
    }

    // u_pass_events
    if (this._changes.passEvents) {
      _.forOwn(this._changes.passEvents, (passEvent) => {
        const passEventPageCms = cms.EventPage[passEvent.eventPageCmsId];
        if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
          mlog.error('u_pass_events is updated with non pass eventPageCms!', {
            userId,
            eventPageCmsId: passEventPageCms.id,
            eventPageType: passEventPageCms.type,
            rsn: this._rsn,
            addRsn: this._addRsn,
          });
        }

        const expChange =
          passEvent.expChange ?? this._user.userPassEvent.getPassEventExpLevel(passEventPageCms);
        const lastDailyResetTimeUtc: number | null =
          passEvent.lastDailyResetTimeUtc !== undefined
            ? passEvent.lastDailyResetTimeUtc
            : this._user.userPassEvent.getLastDailyResetTimeUtc(passEvent.eventPageCmsId);
        queryChain.push((dbConn: query.Connection) => {
          return puPassEventUpdate(
            dbConn,
            this._user.userId,
            passEvent.eventPageCmsId,
            expChange.exp,
            expChange.level,
            lastDailyResetTimeUtc
          );
        });
      });
    }

    // u_pass_event_missions
    if (this._changes.passEventMissions) {
      _.forOwn(this._changes.passEventMissions, (changes, strEventPageCmsId) => {
        const passEventPageCms = cms.EventPage[strEventPageCmsId];
        if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
          mlog.error('u_pass_event_missions is updated with non pass eventPageCms!', {
            userId,
            eventPageCmsId: passEventPageCms.id,
            eventPageType: passEventPageCms.type,
            rsn: this._rsn,
            addRsn: this._addRsn,
          });
        }
        const userEventMissions = this._user.userPassEvent.getPassEventMissions(
          passEventPageCms.id
        );
        _.forOwn(changes, (elem) => {
          const userEventMission = userEventMissions[elem.eventMissionCmsId];
          const userCount = userEventMission ? userEventMission.count : 0;
          const userRepeatedReceiveRewardCount = userEventMission
            ? userEventMission.repeatedRewardReceiveCount
            : 0;
          const userIsRewarded = userEventMission ? userEventMission.isRewarded : 0;
          queryChain.push((dbConn: query.Connection) => {
            return puPassEventMissionUpdateCountAndIsRewarded(
              dbConn,
              this._user.userId,
              elem.eventPageCmsId,
              elem.eventMissionCmsId,
              elem.count !== undefined ? elem.count : userCount,
              elem.repeatedRewardReceiveCount !== undefined
                ? elem.repeatedRewardReceiveCount
                : userRepeatedReceiveRewardCount,
              elem.isRewarded !== undefined ? elem.isRewarded : userIsRewarded
            );
          });
        });
      });
    }

    // u_attendances
    if (this._changes.syncAdd.attendances) {
      _.forOwn(this._changes.syncAdd.attendances, (attendance) => {
        queryChain.push((dbConn: query.Connection) => {
          return puAttendanceUpdate(dbConn, this._user.userId, {
            eventPageCmsId: attendance.eventPageCmsId,
            accum: attendance.accum,
            consecutive: attendance.consecutive,
            lastAttendanceTimeUtc: attendance.lastAttendanceTimeUtc,
            lastRewardedConsecutiveAttendanceCmsId:
              attendance.lastRewardedConsecutiveAttendanceCmsId,
            startTimeUtc: attendance.startTimeUtc,
            endTimeUtc: attendance.endTimeUtc,
          });
        });
      });
    }

    // u_pub_staffs
    if (this._changes.pubStaffChanges) {
      _.forEach(this._changes.pubStaffChanges, (change) => {
        queryChain.push((dbConn: query.Connection) => {
          return puTownPubStaffUpdate(
            dbConn,
            this._user.userId,
            change.townCmsId,
            change.intimacy,
            change.unlockInterest,
            change.dailyTalkCount,
            change.lastNominationRefreshTimeUtc,
            change.questBlockTimeUtc
          );
        });
      });
    }

    // u_daily_subscriptions
    if (this._changes.syncAdd.dailySubscriptions) {
      _.forOwn(this._changes.syncAdd.dailySubscriptions, (ds) => {
        queryChain.push((dbConn: query.Connection) => {
          return puDailySubscriptionUpdate(dbConn, this._user.userId, {
            cmsId: ds.cmsId,
            createTimeUtc: ds.createTimeUtc,
            expireTimeUtc: ds.expireTimeUtc,
            lastReceiveTimeUtc: ds.lastReceiveTimeUtc,
          });
        });
      });
    }

    // u_direct_mail_last_ids
    if (this._changes.newMail) {
      let newLastId;
      for (const elem of this._changes.newMail) {
        if (!this._user.userMails.getDirectMail(elem.id)) {
          if (newLastId) {
            newLastId = Math.max(newLastId, elem.id);
          } else {
            newLastId = elem.id;
          }
        }
      }
      if (newLastId) {
        queryChain.push((dbConn: query.Connection) => {
          return puDirectMailLastIdUpdate(dbConn, userId, newLastId);
        });
      }
    }

    // u_direct_mails
    if (this._changes.newMail) {
      for (const elem of this._changes.newMail) {
        queryChain.push((dbConn: query.Connection) => {
          return puDirectMailCreate(dbConn, userId, elem);
        });
      }
    }
    if (this._changes.syncAdd.userDirectMails) {
      _.forOwn(this._changes.syncAdd.userDirectMails, (elem) => {
        if (elem.state !== undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puDirectMailUpdateState(dbConn, userId, [elem.id], elem.state);
          });
        }
        if (elem.expireTimeUtc !== undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puDirectMailUpdateExpireTimeUtc(dbConn, userId, elem.id, elem.expireTimeUtc);
          });
        }
      });
    }

    // u_line_mails
    if (this._changes.syncAdd.lineMails) {
      _.forOwn(this._changes.syncAdd.lineMails, (elem) => {
        if (elem.state !== undefined) {
          queryChain.push((dbConn: query.Connection) => {
            return puLineMailUpdateState(dbConn, userId, elem.id, elem.state);
          });
        }
      });
    }

    // arena
    if (this._changes.addedArenaTicket) {
      this._user.userArena.changeTicketCount(
        mutil.curTimeUtc(),
        this._user,
        this._changes.addedArenaTicket,
        { user: this._user, rsn: this._rsn, add_rsn: this._addRsn },
        this._changes.syncAdd
      );
      queryChain.push(() => {
        // 같은 트랜젝션이 아니긴 하지만 예외적으로 queryChain 안에 넣어둔다.
        return this._user.userArena.saveDirtyInArena();
      });
    }

    // u_trade_area
    if (this._changes.syncAdd.tradeArea) {
      _.forOwn(this._changes.syncAdd.tradeArea, (tradeArea) => {
        queryChain.push((dbConn: query.Connection) => {
          return puTradeAreaUpdate(dbConn, this._user.userId, tradeArea);
        });
      });
    }

    // u_event_games
    if (this._changes.syncAdd.eventGames) {
      _.forOwn(this._changes.syncAdd.eventGames, (eventGame) => {
        _.forOwn(eventGame, (elem) => {
          queryChain.push((dbConn: query.Connection) => {
            return puEventGameUpdate(
              dbConn,
              this._user.userId,
              elem.eventPageCmsId,
              elem.eventGameCmsId,
              elem.position,
              JSON.stringify(elem.extra)
            );
          });
        });
      });
    }

    // u_fleet_dispatches
    if (this._changes.syncAdd.fleetDispatches) {
      _.forOwn(this._changes.syncAdd.fleetDispatches, (dispatch) => {
        mlog.verbose('[YMK] changeTask preparing DB query', {
          userId: this._user.userId,
          dispatch,
        });

        if (dispatch.endView) {
          queryChain.push((dbConn: query.Connection) => {
            return puFleetDispatchUpdateStateEndView(
              dbConn,
              this._user.userId,
              dispatch.fleetIndex,
              dispatch.state,
              JSON.stringify(dispatch.endView)
            );
          });
        }
        if (dispatch.rewards) {
          queryChain.push((dbConn: query.Connection) => {
            return puFleetDispatchUpdateRewrd(
              dbConn,
              this._user.userId,
              dispatch.fleetIndex,
              JSON.stringify(dispatch.rewards),
              JSON.stringify(dispatch.lostRewards),
              dispatch.lostRewardIdCount
            );
          });
        }
      });
    }

    if (this._changes.syncRemove.fleetDispatches) {
      _.forOwn(this._changes.syncRemove.fleetDispatches, (elem, fleetIndexStr) => {
        if (elem === true) {
          queryChain.push((dbConn: query.Connection) => {
            return puFleetDispatchDelete(dbConn, this._user.userId, parseInt(fleetIndexStr, 10));
          });
        }
      });
    }

    // u_guild_synthesis_progress
    if (
      this._changes.syncRemove.userGuild &&
      this._changes.syncRemove.userGuild.synthesisProgresses
    ) {
      _.forOwn(this._changes.syncRemove.userGuild.synthesisProgresses, (elem, slotNoStr) => {
        if (elem === true) {
          queryChain.push((dbConn: query.Connection) => {
            return puGuildSynthesisProgressDelete(
              dbConn,
              this._user.userId,
              parseInt(slotNoStr, 10)
            );
          });
        }
      });
    }
    if (this._changes.syncAdd.userGuild && this._changes.syncAdd.userGuild.synthesisProgresses) {
      _.forOwn(this._changes.syncAdd.userGuild.synthesisProgresses, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puGuildSynthesisProgressUpdate(
            dbConn,
            userId,
            elem.slot,
            elem.cmsId,
            elem.startTimeUtc,
            elem.completionTimeUtc
          );
        });
      });
    }

    // u_hot_spots
    if (this._changes.syncAdd.hotSpotProducts) {
      _.forOwn(this._changes.syncAdd.hotSpotProducts, (hotSpotProduct) => {
        queryChain.push((dbConn: query.Connection) => {
          return puHotSpotProductsUpdate(
            dbConn,
            this._user.userId,
            hotSpotProduct.cmsId,
            hotSpotProduct.popupCount,
            hotSpotProduct.expireTimeUtc,
            hotSpotProduct.coolTimeUtc,
            hotSpotProduct.lastResetTimeUtc
          );
        });
      });
    }

    if (this._changes.addedEventRankingRewardIdx) {
      _.forOwn(this._changes.addedEventRankingRewardIdx, (rewardIdxArr, eventPageCmsIdStr) => {
        const eventPageCmsId = parseInt(eventPageCmsIdStr);
        for (const rewardIdx of rewardIdxArr) {
          queryChain.push((dbConn: query.Connection) => {
            return puEventRankingRewardUpdate(
              dbConn,
              userId,
              eventPageCmsId,
              Math.floor(rewardIdx / 32),
              rewardIdx % 32
            );
          });
        }
      });
    }

    if (this._changes.addedDiscoveryReward) {
      for (const cmsId of this._changes.addedDiscoveryReward) {
        queryChain.push((dbConn: query.Connection) => {
          return puDiscoveryRewardUpdate(dbConn, userId, Math.floor(cmsId / 32), cmsId % 32);
        });
      }
    }

    // u_battle_rewards
    if (this._changes.syncAdd.user && !mutil.isNotANumber(this._changes.syncAdd.user.karma)) {
      queryChain.push((dbConn: query.Connection) => {
        const karma = this._changes.syncAdd.user.karma;
        const lastUpdateTimeUtc = this._changes.syncAdd.user.lastKarmaUpdateTimeUtc;
        return puSoftDataUpdateKarma(dbConn, userId, { karma, lastUpdateTimeUtc });
      });
    }

    // u_fishes
    if (this._changes.addedFishCatchRewards) {
      _.forOwn(this._changes.addedFishCatchRewards, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puFishCatchUpdate(dbConn, userId, elem.fishId, elem.maxSize, elem.isRewarded);
        });
      });
    }

    // u_user_titles
    if (this._changes.addedUserTitles) {
      _.forOwn(this._changes.addedUserTitles, (elem) => {
        queryChain.push((dbConn: query.Connection) => {
          return puUserTitleUpdate(
            dbConn,
            userId,
            elem.cmsId,
            elem.expiredTimeUtc,
            elem.isEquipped
          );
        });
      });
    }

    // u_sweep_tickets
    if (this._changes.sweepTicket) {
      queryChain.push((dbConn: query.Connection) => {
        return puSweepTicketUpdate(
          dbConn,
          userId,
          this._changes.sweepTicket.count,
          this._changes.sweepTicket.buyCount
        );
      });
    }

    // u_pets
    if (this._changes.syncAdd.pets) {
      _.forOwn(this._changes.syncAdd.pets, (pet) => {
        if (!this._user.userPets.hasPet(pet.cmsId)) {
          queryChain.push((dbConn: query.Connection) => {
            return puPetCreate(dbConn, userId, pet.cmsId);
          });

          // 현재 펫이 새로 만들어지는 경우에는 별도의 업데이트를 하지 않는다
          return;
        }

        // todo. 펫에 대한 변경사항이 있는 경우 처리
      });
    }

    //u_ship_compose
    if (this._changes.syncAdd.shipCompose) {
      _.forOwn(this._changes.syncAdd.shipCompose, (shipComposeInfo, groupId) => {
        queryChain.push((dbConn: query.Connection) => {
          return puShipComposeUpdate(
            dbConn,
            userId,
            parseInt(groupId, 10),
            shipComposeInfo.turn,
            shipComposeInfo.rewardedCount
          );
        });
      });
    }

    //u_infinite_light_house
    if (this._changes.syncAdd.clearedInfiniteLighthouseStages) {
      _.forOwn(
        this._changes.syncAdd.clearedInfiniteLighthouseStages,
        (stageClearedInfos, sessionIdStr) => {
          const sessionId = parseInt(sessionIdStr, 10);
          _.forOwn(stageClearedInfos, (clearInfo, stageNumStr) => {
            const stageNum = parseInt(stageNumStr, 10);
            queryChain.push((dbConn: query.Connection) => {
              return puInfiniteLighthouseUpdate(
                dbConn,
                userId,
                stageNum,
                sessionId,
                clearInfo.usedTurn
              );
            });
          });
        }
      );
    }

    // u_reentrys
    if (this._changes.reentrys) {
      _.forOwn(this._changes.reentrys, (reentry, _) => {
        queryChain.push((dbConn: query.Connection) => {
          return puReentryUpdate(dbConn, userId, reentry);
        });
      });
    }

    // u_clashs
    if (this._changes.clash) {
      queryChain.push((dbConn: query.Connection) => {
        return puClashUpdate(dbConn, userId, this._changes.clash);
      });
    }

    // u_manufacture_exp
    if (this._changes.manufactureExpLevel) {
      queryChain.push((dbConn: query.Connection) => {
        return puManufactureExpLevelUpdate(
          dbConn,
          userId,
          Object.values(this._changes.manufactureExpLevel)
        );
      });
    }

    // u_manufacture_progress deletion
    if (this._changes.syncRemove.manufacture && this._changes.syncRemove.manufacture.roomInfo) {
      for (const roomId in this._changes.syncRemove.manufacture.roomInfo) {
        const roomInfo = this._changes.syncRemove.manufacture.roomInfo[roomId];
        for (const slot in roomInfo) {
          if (roomInfo[slot] === true) {
            queryChain.push((dbConn: query.Connection) => {
              return puManufactureProgressDelete(dbConn, userId, parseInt(roomId, 10), parseInt(slot, 10));
            });
          }
        }
      }
    }

    return queryChain;
  }

  protected _applyToRedis() {
    const promises = this.convertToRedisQueries(this._user);
    if (promises.length !== 0) {
      return Promise.all(R.map((p) => p(), promises));
    }
  }

  // -------------------------------------------------------------------------------------------------
  // 유저의 변경 기록을 Redis 쿼리 목록으로 변환한다.
  // -------------------------------------------------------------------------------------------------
  convertToRedisQueries(user: User): (() => Promise<void>)[] {
    const promises: (() => Promise<void>)[] = [];
    const { nationRedis, userCacheRedis, battleLogRedis, townRedis } = Container.get(LobbyService);

    if (this._changes.syncAdd.user && this._changes.syncAdd.user.level) {
      promises.push(() =>
        userCacheRedis['setUserLevel'](user.userId, this._changes.syncAdd.user.level)
      );
    }

    if (this._changes.syncAdd.user && this._changes.syncAdd.user.nationCmsId) {
      promises.push(() =>
        nationRedis['changeNationPopulation'](
          user.nationCmsId,
          this._changes.syncAdd.user.nationCmsId
        )
      );
      promises.push(() =>
        userCacheRedis['setUserNationCmsId'](user.userId, this._changes.syncAdd.user.nationCmsId)
      );
    }

    if (this._changes.nationEvents) {
      for (const [cmsId1, cmsId2, nationDiplomacyCmsId] of this._changes.nationEvents) {
        promises.push(() =>
          recordEventBetweenTwoNations(
            nationRedis,
            cmsId1,
            cmsId2,
            nationDiplomacyCmsId,
            this._user.userId
          )
        );
      }
    }

    if (this._changes.battleIdForLogEnd !== undefined) {
      const battleType: BattleType = user.userBattle.getBattleInfo().battleParam.battleType;
      const battleRedis = BattleRedisHelper.getBattleRedisByBattleType(battleType);
      promises.push(() =>
        battleRedis['logEnd'](
          user.userId,
          this._changes.battleIdForLogEnd,
          user.userBattle.getBattleInfo().multiId
        )
      );
    }

    return promises;
  }

  getActualGain(): ActualGain {
    return this._actualGain;
  }

  getExchangeHash(): string {
    return this._exchangeHash ? this._exchangeHash : null;
  }

  private _calcActualGain() {
    const user = this._user;
    const changes = this._changes;
    const actualGain = this._actualGain;
    // mlog.verbose('[TEMP] dump changes:', JSON.stringify(this._changes));

    //  u_users, u_soft_data
    if (changes.syncAdd.user) {
      // user ship building exp
      if (
        changes.syncAdd.user.westShipBuildExp !== undefined &&
        changes.syncAdd.user.westShipBuildExp > user.userShipBlueprints.westShipBuildExp
      ) {
        actualGain.westShipBuildExp =
          changes.syncAdd.user.westShipBuildExp - user.userShipBlueprints.westShipBuildExp;
      }

      if (
        changes.syncAdd.user.orientShipBuildExp !== undefined &&
        changes.syncAdd.user.orientShipBuildExp > user.userShipBlueprints.orientShipBuildExp
      ) {
        actualGain.orientShipBuildExp =
          changes.syncAdd.user.orientShipBuildExp - user.userShipBlueprints.orientShipBuildExp;
      }

      // exp
      if (changes.syncAdd.user.exp !== undefined) {
        actualGain.userExp = changes.syncAdd.user.exp - user.exp;
      }
      if (changes.syncAdd.user.level !== undefined && changes.syncAdd.user.level > user.level) {
        actualGain.userLevel = changes.syncAdd.user.level - user.level;
      }
    }

    // u_points
    if (changes.pointsGainAmount) {
      _.forOwn(changes.pointsGainAmount, (value, cmsIdStr) => {
        const cmsId = parseInt(cmsIdStr, 10);
        if (isCash(cmsId)) {
          return;
        }
        if (!actualGain.points) {
          actualGain.points = {};
        }

        actualGain.points[cmsId] = value;
      });
    }
    if (changes.mileages) {
      const curTimeUtc = mutil.curTimeUtc(); // 정확한 gain을 얻기 위해서는 changeTask 가 curTimeUtc를 가지고 있어야 될 필요가 있음.
      const old = user.userPoints.getMileage(curTimeUtc);
      const cur = this._tryData.points.getMileage(curTimeUtc);
      if (!actualGain.points) {
        actualGain.points = {};
      }
      actualGain.points[cmsEx.CashShopMileage] = cur - old;
    }

    // u_items
    if (changes.syncAdd.items) {
      _.forOwn(changes.syncAdd.items, (item) => {
        if (!actualGain.items) {
          actualGain.items = {};
        }

        const amount =
          item.count + item.unboundCount - user.userInven.itemInven.getCount(item.cmsId);
        actualGain.items[item.cmsId] = amount;
      });
    }

    // u_quest_items
    if (this._changes.syncAdd.questItems) {
      _.forOwn(changes.syncAdd.questItems, (item) => {
        if (!actualGain.questItems) {
          actualGain.questItems = {};
          actualGain.questItemsIds = {};
        }
        if (!actualGain.questItems[item.cmsId]) {
          actualGain.questItems[item.cmsId] = 0;
          actualGain.questItemsIds[item.cmsId] = [];
        }

        actualGain.questItems[item.cmsId]++;
        actualGain.questItemsIds[item.cmsId].push(item.id);
      });
    }

    // u_shields
    if (this._changes.syncAdd.shields) {
      _.forOwn(this._changes.syncAdd.shields, (elem) => {
        const oldShield = user.userShield.getShield(elem.cmsId);
        if (!oldShield || elem.nonPurchaseCount !== oldShield.nonPurchaseCount) {
          _.merge<ActualGain, ActualGain>(actualGain, {
            shields: {
              [elem.cmsId]: {
                nonPurchaseCount:
                  elem.nonPurchaseCount - (oldShield ? oldShield.nonPurchaseCount : 0),
              },
            },
          });
        }
        if (!oldShield || elem.purchaseCount !== oldShield.purchaseCount) {
          _.merge<ActualGain, ActualGain>(actualGain, {
            shields: {
              [elem.cmsId]: {
                purchaseCount: elem.purchaseCount - (oldShield ? oldShield.purchaseCount : 0),
              },
            },
          });
        }
      });
    }

    // u_ship_blueprints
    if (this._changes.syncAdd.shipBlueprints) {
      _.forOwn(this._changes.syncAdd.shipBlueprints, (elem) => {
        if (!actualGain.shipBlueprints) {
          actualGain.shipBlueprints = {};
        }
        if (elem.level !== undefined && elem.exp !== undefined) {
          _.merge<ActualGain, ActualGain>(actualGain, {
            shipBlueprints: {
              [elem.cmsId]: {
                level: elem.level,
                exp: elem.exp,
              },
            },
          });
        }

        if (elem.sailMasteryLevel !== undefined && elem.sailMasteryExp !== undefined) {
          _.merge<ActualGain, ActualGain>(actualGain, {
            shipBlueprints: {
              [elem.cmsId]: {
                sailMasteryLevel: elem.sailMasteryLevel,
                sailMasteryExp: elem.sailMasteryExp,
              },
            },
          });
        }
      });
    }

    if (changes.bpSailMasteryforGlog) {
      _.forOwn(changes.bpSailMasteryforGlog, (bpElem) => {
        _.merge<ActualGain, ActualGain>(actualGain, {
          bpSailMasteryforGlog: {
            [bpElem.cmsId]: {
              cmsId: bpElem.cmsId,
              oldLevel: bpElem.oldLevel,
              oldExp: bpElem.oldExp,
              level: bpElem.level,
              exp: bpElem.exp,
              addedExp: bpElem.addedExp,
            },
          },
        });
      });
    }

    // u_mates
    if (changes.syncAdd.mates) {
      _.forOwn(changes.syncAdd.mates, (mate) => {
        const userMate = user.userMates.getMate(mate.cmsId);

        // create
        if (!userMate) {
          if (!actualGain.mates) {
            actualGain.mates = [];
          }
          actualGain.mates.push(mate.cmsId);
          return;
        }

        // loyalty
        if (mate.loyalty !== undefined && mate.loyalty !== null) {
          _.merge<ActualGain, ActualGain>(actualGain, {
            mateLoyalty: {
              [mate.cmsId]: mate.loyalty - userMate.getLoyalty(),
            },
          });
        }

        // fame
        if (mate.adventureFame || mate.tradeFame || mate.battleFame) {
          actualGain.admiralFame = {};
        }
        if (mate.adventureFame) {
          actualGain.admiralFame[cmsEx.JOB_TYPE.ADVENTURE] =
            mate.adventureFame - userMate.getFame(cmsEx.JOB_TYPE.ADVENTURE);
        }
        if (mate.tradeFame) {
          actualGain.admiralFame[cmsEx.JOB_TYPE.TRADE] =
            mate.tradeFame - userMate.getFame(cmsEx.JOB_TYPE.TRADE);
        }
        if (mate.battleFame) {
          actualGain.admiralFame[cmsEx.JOB_TYPE.BATTLE] =
            mate.battleFame - userMate.getFame(cmsEx.JOB_TYPE.BATTLE);
        }
      });
    }

    if (changes.mateExp) {
      let bChangeExp = false;
      _.forOwn(changes.mateExp, (changes, jobtypeStr) => {
        if (!actualGain.mateExp) {
          actualGain.mateExp = {};
        }

        const jobType = parseInt(jobtypeStr, 10);
        for (const change of changes) {
          bChangeExp = true;
          if (!actualGain.mateExp[change.mateCmsId]) {
            actualGain.mateExp[change.mateCmsId] = {};
          }
          actualGain.mateExp[change.mateCmsId][jobtypeStr] =
            change.exp - user.userMates.getMate(change.mateCmsId).getExp(jobType);
        }
      });
      // acture gain exp
      actualGain.exp = bChangeExp ? changes.actualGainExp : undefined;
    }

    // u_mate_equipments
    if (changes.lastMateEquipmentId) {
      if (!actualGain.mateEquips) {
        actualGain.mateEquips = {};
        actualGain.mateEquipIds = {};
      }

      for (
        let i = user.userMates.getLastMateEquipmentId() + 1;
        i <= changes.lastMateEquipmentId;
        i++
      ) {
        const e = changes.syncAdd.mateEquipments[i];
        if (!actualGain.mateEquips[e.cmsId]) {
          actualGain.mateEquips[e.cmsId] = 1;
          actualGain.mateEquipIds[e.cmsId] = [e.id];
        } else {
          actualGain.mateEquips[e.cmsId]++;
          actualGain.mateEquipIds[e.cmsId].push(e.id);
        }
      }
    }

    // u_unemployed_mates
    if (this._changes.syncAdd.unemployedMates) {
      _.forOwn(this._changes.syncAdd.unemployedMates, (mate) => {
        if (mate.intimacy !== undefined) {
          _.merge<ActualGain, ActualGain>(actualGain, {
            mateIntimacy: {
              [mate.mateCmsId]:
                mate.intimacy - user.userMates.getUnemployedMateIntimacy(mate.mateCmsId),
            },
          });
        }
      });
    }
    if (this._changes.syncRemove.unemployedMates) {
      _.forOwn(this._changes.syncRemove.unemployedMates, (elem, mateCmsIdStr) => {
        if (elem === true) {
          user.userMates.deleteUnemployedMate(parseInt(mateCmsIdStr, 10));
        }
      });
    }

    // u_ships, u_ship_slots, u_ship_cargos
    if (changes.syncAdd.ships) {
      _.forOwn(changes.syncAdd.ships, (ship) => {
        // create
        const userShip = user.userFleets.getShip(ship.id);
        if (!userShip) {
          if (!actualGain.ships) {
            actualGain.ships = {};
            actualGain.shipIds = {};
          }

          if (!actualGain.ships[ship.cmsId]) {
            actualGain.ships[ship.cmsId] = 1;
            actualGain.shipIds[ship.cmsId] = [ship.id];
          } else {
            actualGain.ships[ship.cmsId]++;
            actualGain.shipIds[ship.cmsId].push(ship.id);
          }
        }

        // sailor
        if (ship.sailor !== undefined) {
          let oldSailor = 0;
          if (userShip) {
            oldSailor = userShip.getSailor();
          }
          if (ship.sailor !== oldSailor) {
            if (!actualGain.sailors) {
              actualGain.sailors = {};
            }
            actualGain.sailors[ship.id] = ship.sailor - oldSailor;
          }
        }

        // cargo
        if (ship.cargos) {
          _.forOwn(ship.cargos, (cargo) => {
            const supplyCms = cms.DepartSupply[cargo.cmsId];
            let oldQuantity = 0;
            if (userShip) {
              oldQuantity = userShip.getCargoQuantity(cargo.cmsId);
            }

            if (supplyCms) {
              if (!actualGain.departSupplies) {
                actualGain.departSupplies = {};
              }

              if (!actualGain.departSupplies[cargo.cmsId]) {
                actualGain.departSupplies[cargo.cmsId] = cargo.quantity - oldQuantity;
              } else {
                actualGain.departSupplies[cargo.cmsId] += cargo.quantity - oldQuantity;
              }
            } else if (cms.TradeGoods[cargo.cmsId]) {
              if (!actualGain.tradeGoods) {
                actualGain.tradeGoods = {};
              }

              if (!actualGain.tradeGoods[cargo.cmsId]) {
                actualGain.tradeGoods[cargo.cmsId] = cargo.quantity - oldQuantity;
              } else {
                actualGain.tradeGoods[cargo.cmsId] += cargo.quantity - oldQuantity;
              }
            } else if (cms.SmuggleGoods[cargo.cmsId]) {
              if (!actualGain.smuggleGoods) {
                actualGain.smuggleGoods = {};
              }

              if (!actualGain.smuggleGoods[cargo.cmsId]) {
                actualGain.smuggleGoods[cargo.cmsId] = cargo.quantity - oldQuantity;
              } else {
                actualGain.smuggleGoods[cargo.cmsId] += cargo.quantity - oldQuantity;
              }
            }
          });
        }
      });
    }

    // u_ship_slot_items
    if (changes.lastShipSlotItemId) {
      // new ship slot item
      for (
        let i = this._user.userInven.getLastShipSlotItemId() + 1;
        i <= this._changes.lastShipSlotItemId;
        i++
      ) {
        const shipSlotItem = this._changes.syncAdd.shipSlotItems[i];
        if (!actualGain.shipSlotItems) {
          actualGain.shipSlotItems = {};
          actualGain.shipSlotItemIds = {};
        }
        if (!actualGain.shipSlotItems[shipSlotItem.shipSlotCmsId]) {
          actualGain.shipSlotItems[shipSlotItem.shipSlotCmsId] = 0;
          actualGain.shipSlotItemIds[shipSlotItem.shipSlotCmsId] = [];
        }
        actualGain.shipSlotItems[shipSlotItem.shipSlotCmsId]++;
        actualGain.shipSlotItemIds[shipSlotItem.shipSlotCmsId].push(shipSlotItem.id);
      }
    }

    // u_tax_free_permits
    if (changes.addedPalaceTaxFreePermitCounts) {
      if (!actualGain.taxFreePermits) {
        actualGain.taxFreePermits = {};
      }
      const taxFreePermits = actualGain.taxFreePermits;
      _.forOwn(changes.addedPalaceTaxFreePermitCounts, (count, cmsIdStr) => {
        taxFreePermits[cmsIdStr] = count;
      });
    }

    // u_cash_shop_gacha_box_guarantees
    if (changes.syncAdd.cashShopGachaBoxGuarantees) {
      _.forOwn(changes.syncAdd.cashShopGachaBoxGuarantees, (elem, cmsIdStr) => {
        if (elem === -1) {
          return;
        }

        const curAccum = user.userCashShop.getGachaBoxGuaranteeAccum(parseInt(cmsIdStr, 10));
        if (curAccum === -1) {
          return;
        }

        if (!actualGain.cashShopGachaBoxGuaranteeAccum) {
          actualGain.cashShopGachaBoxGuaranteeAccum = {};
        }
        actualGain.cashShopGachaBoxGuaranteeAccum[cmsIdStr] = curAccum ? elem - curAccum : elem;
      });
    }

    // u_sound_packs
    if (changes.addedSoundPacks) {
      actualGain.soundPacks = changes.addedSoundPacks;
    }

    // u_discoveries
    if (changes.discoveryCmsIds) {
      actualGain.discoverCmsIds = changes.discoveryCmsIds;
    }

    // arena
    if (changes.addedArenaTicket) {
      actualGain.arenaTicket = changes.addedArenaTicket;
    }

    if (changes.sweepTicket) {
      const addedFreeSweepTicket: number = changes.sweepTicket.count - user.userSweepTicket.count;
      if (addedFreeSweepTicket > 0) {
        actualGain.freeSweepTicket = addedFreeSweepTicket;
      }

      const addedBuySweepTicket: number =
        changes.sweepTicket.buyCount - user.userSweepTicket.buyCount;
      if (addedBuySweepTicket > 0) {
        actualGain.buySweepTicket = addedBuySweepTicket;
      }
    }

    // u_pets
    if (changes.syncAdd.pets) {
      _.forOwn(changes.syncAdd.pets, (pet) => {
        const userPet = user.userPets.getPet(pet.cmsId);
        if (!userPet) {
          if (!actualGain.pets) {
            actualGain.pets = [];
          }
          actualGain.pets.push(pet.cmsId);
        }
      });
    }

    // u_manufacture_exp
    if (changes.manufactureExpLevel) {
      if (!actualGain.manufactureExp) {
        actualGain.manufactureExp = {};
      }
      const manufactureExp = actualGain.manufactureExp;
      _.forOwn(changes.manufactureExpLevel, (change, typeStr) => {
        const type = parseInt(typeStr, 10);
        manufactureExp[type] = change.exp - user.userManufacture.getExp(type);
      });
    }
  }

  // user 에 적용한다. 추가로 changes.sync 를 사용하지 않는 경우에 sync 에 변경 사항을 머지하기도 한다.
  protected _applyToUser(sync: sync.Sync) {
    const user = this._user;
    const buffSync: BuffSync = { sync: {} };

    // u_users
    if (this._changes.syncAdd.user) {
      // energy
      if (this._changes.syncAdd.user.lastUpdateEnergyTimeUtc !== undefined) {
        user.userEnergy.applyEnergyChange(
          {
            energy: this._changes.syncAdd.points[cmsEx.EnergyPointCmsId].value,
            lastUpdateTimeUtc: this._changes.syncAdd.user.lastUpdateEnergyTimeUtc,
          },
          null
        );
      }

      // manufacture point
      if (this._changes.syncAdd.user.lastUpdateManufacturePointTimeUtc !== undefined) {
        user.userManufacture.applyPointChange(
          {
            point: this._changes.syncAdd.points[cmsEx.ManufacturePointCmsId].value,
            lastUpdatePointTimeUtc: this._changes.syncAdd.user.lastUpdateManufacturePointTimeUtc,
          },
          null
        );
      }

      // nation cms id
      if (this._changes.syncAdd.user.nationCmsId !== undefined) {
        user.nationCmsId = this._changes.syncAdd.user.nationCmsId;
        user.lastUpdateNationTimeUtc = this._changes.syncAdd.user.lastUpdateNationTimeUtc;
      }

      // palace royal order
      if (this._changes.bResetPalaceRoyalOrder) {
        user.questManager.setPalaceRoyalOrderCmsId(null);
        user.questManager.setPalaceRoyalOrderRnds(undefined);
        user.questManager.setPalaceRoyalTitleOrderCmsId(null);
        user.questManager.setPalaceRoyalTitleOrderRnds(undefined);
      }

      // explore_ticket
      if (
        this._changes.syncAdd.user.usedExploreTicketCount !== undefined ||
        this._changes.syncAdd.user.lastTicketCountUpdateTimeUtc !== undefined
      ) {
        user.userExplore.applyTicketChange(
          {
            usedTicketCount: this._changes.syncAdd.user.usedExploreTicketCount,
            lastUpdateTimeUtc: this._changes.syncAdd.user.lastTicketCountUpdateTimeUtc,
          },
          {
            user: this._user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
            maxQuickModeCount: user.userExplore.getMaxTicketCount(user),
          }
        );
      }

      // explore_quick_mode
      if (
        this._changes.syncAdd.user.usedExploreQuickModeCount !== undefined ||
        this._changes.syncAdd.user.lastExploreQuickModeCountUpdateTimeUtc !== undefined
      ) {
        user.userExplore.applyQuickModeChange(
          {
            usedQuickModeCount: this._changes.syncAdd.user.usedExploreQuickModeCount,
            lastUpdateTimeUtc: this._changes.syncAdd.user.lastExploreQuickModeCountUpdateTimeUtc,
          },
          {
            user: this._user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
            maxQuickModeCount: user.userExplore.getMaxQuickModeCount(user),
          }
        );
      }

      // user ship building exp
      if (
        this._changes.syncAdd.user.westShipBuildExp !== undefined &&
        this._changes.syncAdd.user.westShipBuildLevel !== undefined &&
        this._changes.syncAdd.user.orientShipBuildExp !== undefined &&
        this._changes.syncAdd.user.orientShipBuildLevel !== undefined
      ) {
        user.userShipBlueprints.setUserShipBuildingExpLevel({
          westShipBuildExp: this._changes.syncAdd.user.westShipBuildExp,
          westShipBuildLevel: this._changes.syncAdd.user.westShipBuildLevel,
          orientShipBuildExp: this._changes.syncAdd.user.orientShipBuildExp,
          orientShipBuildLevel: this._changes.syncAdd.user.orientShipBuildLevel,
        });
      }

      if (this._changes.syncAdd.user.lastRoyalOrderCompletedTimeUtc) {
        user.questManager.setLastRoyalOrderCompletedTimeUtc(
          this._changes.syncAdd.user.lastRoyalOrderCompletedTimeUtc
        );
      }

      if (this._changes.syncAdd.user.lastPaidSmuggleEnterTownCmsId) {
        user.userSmuggle.lastPaidSmuggleEnterTownCmsId =
          this._changes.syncAdd.user.lastPaidSmuggleEnterTownCmsId;
      }
    }

    // u_states
    if (this._changes.syncAdd.user) {
      if (
        this._changes.syncAdd.user.gameState !== undefined ||
        this._changes.syncAdd.user.lastGameState !== undefined ||
        this._changes.syncAdd.user.gameEnterState !== undefined
      ) {
        user.userState.applyGameStateChange(
          {
            gameState: this._changes.syncAdd.user.gameState,
            lastGameState: this._changes.syncAdd.user.lastGameState,
            gameEnterState: this._changes.syncAdd.user.gameEnterState,
          },
          { user }
        );
      }
    }

    // u_soft_data
    if (
      this._changes.syncAdd.user &&
      (this._changes.syncAdd.user.exp !== undefined ||
        this._changes.syncAdd.user.level !== undefined)
    ) {
      if (
        this._changes.syncAdd.user.exp !== undefined ||
        this._changes.syncAdd.user.level !== undefined
      ) {
        user.setExpLevel(
          this._changes.syncAdd.user.exp,
          this._changes.syncAdd.user.level,
          this._rsn,
          this._addRsn,
          buffSync
        );
      }
    }
    if (this._changes.syncAdd.user && this._changes.syncAdd.user.lastTownCmsId !== undefined) {
      user.userTown.setLastTownCmsId(this._changes.syncAdd.user.lastTownCmsId);
    }
    if (this._changes.arrivalTownCmsId != undefined) {
      user.userTown.arrivalTownCmsId = this._changes.arrivalTownCmsId;
    }
    if (
      this._changes.syncAdd.user &&
      this._changes.syncAdd.user.usedFreeTurnTakebackCount !== undefined &&
      this._changes.syncAdd.user.usedFreePhaseTakebackCount !== undefined &&
      this._changes.syncAdd.user.lastFreeTakebackUpdateTimeUtc !== undefined
    ) {
      user.userBattle.applyFreeTakebackChange({
        usedFreeTurnTakebackCount: this._changes.syncAdd.user.usedFreeTurnTakebackCount,
        usedFreePhaseTakebackCount: this._changes.syncAdd.user.usedFreePhaseTakebackCount,
        updateTimeUtc: this._changes.syncAdd.user.lastFreeTakebackUpdateTimeUtc,
      });
    }
    if (
      this._changes.syncAdd.user &&
      this._changes.syncAdd.user.quickModeCount !== undefined &&
      this._changes.syncAdd.user.lastQuickModeCountUpdateTimeUtc !== undefined
    ) {
      user.userBattle.applyQuickModeChange({
        quickModeCount: this._changes.syncAdd.user.quickModeCount,
        updateTimeUtc: this._changes.syncAdd.user.lastQuickModeCountUpdateTimeUtc,
      });
    }

    // u_points
    if (this._changes.syncAdd.points) {
      _.forOwn(this._changes.syncAdd.points, (point) => {
        if (point.cmsId === cmsEx.EnergyPointCmsId || isCash(point.cmsId)) {
          return;
        }
        user.userPoints.setPointForChangeTask(point.cmsId, point.value);
      });
    }
    if (this._changes.mileages) {
      _.merge<sync.Sync, sync.Sync>(
        sync,
        user.userPoints.applyMileageChanges(this._changes.mileages, {
          user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
        })
      );
    }

    // u_items.
    if (this._changes.syncAdd.items) {
      _.forOwn(this._changes.syncAdd.items, (item) => {
        user.userInven.itemInven.applyItemChange(
          {
            cmsId: item.cmsId,
            count: item.count,
            unboundCount: item.unboundCount,
            bIsAccum: false,
          },
          null,
          {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          }
        );
      });
    }

    // u_quest_items
    _.forOwn(this._changes.syncAdd.questItems, (item) => {
      user.userInven.itemInven.addQuestItem({
        id: item.id,
        cmsId: item.cmsId,
        questCmsId: item.questCmsId,
        questRnds: item.questRnds,
      });
    });

    // u_shields
    _.forOwn(this._changes.syncAdd.shields, (elem) => {
      user.userShield.applyShieldChange(
        {
          cmsId: elem.cmsId,
          dailyFreeCount: elem.dailyFreeCount,
          lastDailyChargeTimeUtc: elem.lastDailyChargeTimeUtc,
          nonPurchaseCount: elem.nonPurchaseCount,
          purchaseCount: elem.purchaseCount,
          isActivated: elem.isActivated,
        },
        { user, rsn: this._rsn, add_rsn: this._addRsn }
      );
    });

    // u_challenges
    if (this._changes.syncAdd.challenges) {
      _.forOwn(this._changes.syncAdd.challenges, (elem) => {
        user.userChallenges.updateChallenge(elem);
      });
    }

    // u_ship_blueprints
    if (this._changes.syncAdd.shipBlueprints) {
      _.forOwn(this._changes.syncAdd.shipBlueprints, (elem) => {
        if (elem.level !== undefined && elem.exp !== undefined) {
          if (!user.userShipBlueprints.getUserShipBlueprint(elem.cmsId)) {
            user.userShipBlueprints.createUserShipBlueprint(
              user.companyStat,
              elem.cmsId,
              elem.level,
              elem.exp,
              1,
              0,
              { user, rsn: this._rsn, add_rsn: this._addRsn },
              sync
            );
          } else {
            user.userShipBlueprints.setUserShipBlueprintLevel(
              elem.cmsId,
              elem.level,
              elem.exp,
              user.companyStat,
              sync
            );
          }
        }

        if (elem.sailMasteryLevel !== undefined && elem.sailMasteryExp !== undefined) {
          user.userShipBlueprints.setUserShipBlueprintSailMastery(
            elem.cmsId,
            elem.sailMasteryLevel,
            elem.sailMasteryExp,
            user.companyStat,
            sync
          );
        }
      });
    }

    // u_reputations
    if (this._changes.syncAdd.reputations) {
      _.forOwn(this._changes.syncAdd.reputations, (elem, nationCmsIdStr) => {
        user.userReputation.set(
          parseInt(nationCmsIdStr, 10),
          {
            reputation: elem.reputation,
            updateTimeUtc: elem.updateTimeUtc,
          },
          { user, rsn: this._rsn, add_rsn: this._addRsn }
        );
      });
    }

    // u_quest_contexts
    if (this._changes.syncAdd.questData && this._changes.syncAdd.questData.contexts) {
      _.forOwn(this._changes.syncAdd.questData.contexts, (ctx) => {
        // flags
        if (ctx.uflags !== undefined && ctx.lflags !== undefined) {
          user.questManager.applyCtxChange({
            cmsId: ctx.cmsId,
            uflags: ctx.uflags,
            lflags: ctx.lflags,
          });
        }
      });
    }
    if (this._changes.syncRemove.questData && this._changes.syncRemove.questData.contexts) {
      _.forOwn(this._changes.syncRemove.questData.contexts, (elem, questCmsIdStr) => {
        if (elem === true) {
          // drop quest
          user.questManager.dropQuest(parseInt(questCmsIdStr, 10));
        }
      });
    }

    // u_sailing
    if (this._changes.daysForLoyaltyDecrease || this._changes.daysForTownReset) {
      user.userSailing.applySailingChange({
        daysForLoyaltyDecrease:
          this._changes.daysForLoyaltyDecrease === undefined
            ? this._user.userSailing.daysForLoyaltyDecrease
            : this._changes.daysForLoyaltyDecrease,
        daysForTownReset:
          this._changes.daysForTownReset === undefined
            ? this._user.userSailing.daysForTownReset
            : this._changes.daysForTownReset,
      });
    }
    if (this._changes.syncAdd.user && this._changes.syncAdd.user.totalSailedDays !== undefined) {
      user.userSailing.totalSailedDays = this._changes.syncAdd.user.totalSailedDays;
    }

    // u_insurances
    if (this._changes.syncAdd.insurance) {
      const insuranceChange = this._changes.syncAdd.insurance;
      if (insuranceChange.insuranceCmsId !== undefined) {
        user.userPoints.setInsuranceCmsId(insuranceChange.insuranceCmsId);
      }
      if (
        insuranceChange.unpaidDucat !== undefined &&
        insuranceChange.unpaidSailor !== undefined &&
        insuranceChange.unpaidShip !== undefined &&
        insuranceChange.unpaidTradeGoods !== undefined
      ) {
        user.userPoints.applyInsuranceUnpaidChange({
          unpaidDucat: insuranceChange.unpaidDucat,
          unpaidSailor: insuranceChange.unpaidSailor,
          unpaidShip: insuranceChange.unpaidShip,
          unpaidTradeGoods: insuranceChange.unpaidTradeGoods,
        });
      }
    }

    // u_mates
    if (this._changes.syncAdd.mates) {
      _.forOwn(this._changes.syncAdd.mates, (mate) => {
        // create
        if (!user.userMates.getMate(mate.cmsId)) {
          user.userMates.addNewMate(
            {
              cmsId: mate.cmsId,
              loyalty: mate.loyalty,
              stateFlags: mate.stateFlags ? mate.stateFlags : 0,
              adventureExp: mate.adventureExp ? mate.adventureExp : 0,
              adventureLevel: mate.adventureLevel ? mate.adventureLevel : 1,
              tradeExp: mate.tradeExp ? mate.tradeExp : 0,
              tradeLevel: mate.tradeLevel ? mate.tradeLevel : 1,
              battleExp: mate.battleExp ? mate.battleExp : 0,
              battleLevel: mate.battleLevel ? mate.battleLevel : 1,
              adventureFame: mate.adventureFame ? mate.adventureFame : 0,
              tradeFame: mate.tradeFame ? mate.tradeFame : 0,
              battleFame: mate.battleFame ? mate.battleFame : 0,
              royalTitle: mate.royalTitle ? mate.royalTitle : 0,
              awakenLv: mate.awakenLv ? mate.awakenLv : 0,
              awakenTimeUtc: null,
              equipments: [],
              passives: {},
              passiveLearnings: {},
              isFavorite: mate.isFavorite,
            },
            user.companyStat,
            user,
            {
              user,
              rsn: this._rsn,
              add_rsn: this._addRsn,
            },
            buffSync
          );

          return;
        }

        const userMate = user.userMates.getMate(mate.cmsId);

        if (mate.loyalty !== undefined && mate.loyalty !== null) {
          userMate.setLoyalty(mate.loyalty, user.companyStat, user, {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          });
        }

        //* 부상이 발동되었을 때만 플래그를 syncAdd 에 넣어줘야한다. ( 버프가 중복 적용될 수 있음 )
        if ((mate.stateFlags & cmsEx.MATE_STATE_FLAG.INJURY) === cmsEx.MATE_STATE_FLAG.INJURY) {
          userMate.setInjuryImmuneTimeUtc(null);
          userMate.addInjuryBuff(
            user,
            { user, rsn: this._rsn, duration: this._changes.injuryDuration[mate.cmsId] },
            buffSync
          );
        }
        if (mate.injuryExpireTimeUtc !== undefined) {
          userMate.setInjuryExpireTimeUtc(mate.injuryExpireTimeUtc);
        }

        if (mate.stateFlags !== undefined) {
          userMate.removeSlowdownBuffIfNeeded(user, { user, rsn: this._rsn }, null);
          userMate.setStateFlags(mate.stateFlags, user.companyStat);
        }

        if (mate.adventureFame) {
          userMate.setFame(cmsEx.JOB_TYPE.ADVENTURE, mate.adventureFame, { user, rsn: this._rsn });
        }
        if (mate.tradeFame) {
          userMate.setFame(cmsEx.JOB_TYPE.TRADE, mate.tradeFame, { user, rsn: this._rsn });
        }
        if (mate.battleFame) {
          userMate.setFame(cmsEx.JOB_TYPE.BATTLE, mate.battleFame, { user, rsn: this._rsn });
        }
      });
    }

    if (this._changes.mateExp) {
      _.forOwn(this._changes.mateExp, (changes, jobtypeStr) => {
        user.userMates.applyMateExpLevelChanges(user, changes, parseInt(jobtypeStr, 10), {
          user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
        });
      });
    }
    if (this._changes.mateLoyalty) {
      for (const elem of this._changes.mateLoyalty) {
        user.userMates.getMate(elem.mateCmsId).setLoyalty(elem.value, user.companyStat, user, {
          user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
        });
      }
    }

    // u_mate_equipment_last_ids, u_mate_equipments
    if (this._changes.lastMateEquipmentId) {
      for (
        let i = user.userMates.getLastMateEquipmentId() + 1;
        i <= this._changes.lastMateEquipmentId;
        i++
      ) {
        const e = this._changes.syncAdd.mateEquipments[i];
        user.userMates.addMateEquipment(
          {
            id: e.id,
            cmsId: e.cmsId,
            equippedMateCmsId: e.equippedMateCmsId ? e.equippedMateCmsId : 0,
            isBound: e.isBound,
            isCostume: e.isCostume,
            expireTimeUtc: e.expireTimeUtc,
            enchantLv: e.enchantLv,
          },
          {
            user: this._user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          }
        );
        if (e.equippedMateCmsId) {
          user.userMates.equipMateEquipment(
            e.equippedMateCmsId,
            e.id,
            user.companyStat,
            user.userPassives,
            user.userFleets,
            user.userSailing,
            user.userTriggers,
            user.userBuffs,
            sync,
            { user, rsn: this._rsn, add_rsn: this._addRsn }
          );
        }
      }
    }

    if (this._changes.syncRemove.mateEquipments) {
      for (const mateEquipIdStr of this._changes.syncRemove.mateEquipments) {
        user.userMates.removeMateEquipment(
          parseInt(mateEquipIdStr, 10),
          user.companyStat,
          user.userPassives,
          user.userFleets,
          user.userSailing,
          user.userTriggers,
          user.userBuffs,
          {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          },
          sync
        );
      }
    }

    // u_mate_passives
    if (this._changes.addedMatePassives) {
      for (const elem of this._changes.addedMatePassives) {
        const userMate: Mate = user.userMates.getMate(elem.mateCmsId);
        userMate.addPassive(elem.passiveCmsId, 0, sync);
      }
    }

    // u_fleets
    if (this._changes.fleetDispatchLifeRemains) {
      _.forOwn(this._changes.fleetDispatchLifeRemains, (remain, fleetIndexStr) => {
        const fleet = user.userFleets.getFleet(parseInt(fleetIndexStr, 10));
        fleet.setDispatchReduceLifeRemain(remain);
      });
    }

    // u_ships, u_ship_slots, u_ship_cargos
    if (this._changes.syncAdd.ships) {
      _.forOwn(this._changes.syncAdd.ships, (ship) => {
        // create
        if (!user.userFleets.getShip(ship.id)) {
          user.userFleets.addNewShip(
            {
              id: ship.id,
              cmsId: ship.cmsId,
              assignment: ship.assignment,
              fleetIndex: ship.fleetIndex,
              formationIndex: ship.formationIndex,
              durability: ship.durability,
              sailor: ship.sailor,
              name: ship.name,
              life: ship.life,
              isLocked: ship.isLocked,
              slots: {},
              cargos: {},
              permanentDamage: 0,
              enchantedStats: ship.enchantedStats ? ship.enchantedStats : [],
              enchantResult: ship.enchantResult ? ship.enchantResult : null,
              enchantCount: ship.enchantCount ? ship.enchantCount : 0,
              rndStats: ship.rndStats,
              battleQuickSkills: Ship.defaultBattleQuickSkills(),
              isBound: ship.isBound,
              guid: ship.guid,
            },
            user.companyStat,
            user.userShipBlueprints,
            user.userMates,
            user.userFleets,
            user.userInven,
            user.userBuffs,
            user.userPassives,
            user.userCollection,
            user.questManager,
            user.userSailing,
            user.userTriggers,
            user.userNation,
            user.userResearch,
            { user, rsn: this._rsn, add_rsn: this._addRsn },
            sync
          );
        }

        const userShip = user.userFleets.getShip(ship.id);
        // durability
        if (ship.durability !== undefined) {
          userShip.setDurability(ship.durability, user, {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          });
        }
        // sailor
        // 난파 되었을때 수명이 0이 되었다면 내구도를 다시 세팅하는 부분이 있어서 내구도먼저 세팅하고 선원을 세팅해줘야됨
        if (ship.sailor !== undefined) {
          userShip.setSailor(ship.sailor, user, false, {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          });
        }
        // formationIndex
        if (ship.formationIndex !== undefined) {
          userShip.setFormationIndex(ship.formationIndex, user.companyStat);
        }
        // life
        if (ship.life !== undefined) {
          userShip.setLife(
            ship.life,
            { user, rsn: this._rsn, add_rsn: this._addRsn },
            user.companyStat
          );
        }

        if (ship.slots) {
          _.forOwn(ship.slots, (slot) => {
            // change task 를 사용해서 항해사는 unmount 시키는 경우는 없다고 한다. 발생하면 경고 로그를 남기도록.
            if (slot.mateCmsId === 0) {
              mlog.warn('Unmounting a mate with a change task!', {
                userId: user.userId,
                reason: this._reason,
                rsn: this._rsn,
                addRsn: this._addRsn,
                shipId: userShip.nub.id,
                slot,
              });
            }

            userShip.setSlotMate(
              {
                slotIndex: slot.slotIndex,
                mateCmsId: slot.mateCmsId,
                isLocked: slot.isLocked,
                shipSlotItemId: slot.shipSlotItemId,
              },
              user.userShipBlueprints,
              user.userMates,
              user.userFleets,
              user.companyStat,
              user.userPassives,
              user.questManager,
              user.userSailing,
              user.userTriggers,
              user.userBuffs,
              false, // bOnLogin
              sync
            );
          });
        }

        if (ship.cargos) {
          _.forOwn(ship.cargos, (cargo) => {
            userShip.applyCargoChange(
              {
                cmsId: cargo.cmsId,
                quantity: cargo.quantity,
                pointInvested: cargo.pointInvested,
              },
              user,
              {
                user,
                rsn: this._rsn,
                add_rsn: this._addRsn,
              },
              buffSync
            );
          });
        }
      });
    }

    if (this._changes.syncRemove.ships) {
      for (const shipIdStr of this._changes.syncRemove.ships) {
        user.userFleets.deleteShip(
          parseInt(shipIdStr, 10),
          user,
          user.userMates,
          user.companyStat,
          user.userFleets,
          user.userShipBlueprints,
          user.userInven,
          user.userPassives,
          user.questManager,
          user.userSailing,
          user.userTriggers,
          user.userBuffs,
          {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          },
          sync
        );
      }
    }

    if (this._changes.shipIdsToDismantle) {
      for (const shipId of this._changes.shipIdsToDismantle) {
        user.userFleets.deleteShip(
          shipId,
          user,
          user.userMates,
          user.companyStat,
          user.userFleets,
          user.userShipBlueprints,
          user.userInven,
          user.userPassives,
          user.questManager,
          user.userSailing,
          user.userTriggers,
          user.userBuffs,
          {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          },
          sync
        );
      }
    }
    if (
      this._changes.unequippedShipSlotItemIds &&
      this._changes.unequippedShipSlotItemIds.length > 0
    ) {
      for (const shipSlotItemId of this._changes.unequippedShipSlotItemIds) {
        user.userInven.unequipShipSlotItem(shipSlotItemId);
      }
    }

    // u_ship_slot_items
    if (this._changes.lastShipSlotItemId) {
      // new ship slot item
      let i = this._user.userInven.getLastShipSlotItemId() + 1;
      for (; i <= this._changes.lastShipSlotItemId; i++) {
        const shipSlotItem = this._changes.syncAdd.shipSlotItems[i];
        user.userInven.addShipSlotItemForSync(shipSlotItem, {
          user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
        });
      }
    }
    if (this._changes.syncRemove.shipSlotItems) {
      for (const idStr of this._changes.syncRemove.shipSlotItems) {
        user.userInven.removeShipSlotItem(parseInt(idStr, 10), {
          user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
        });
      }
    }

    //u_tax_free_permits
    if (this._changes.syncAdd.taxFreePermits) {
      _.forOwn(this._changes.syncAdd.taxFreePermits, (taxFreePermit) => {
        // 면세증 만료 기간이 줄어드는 operator가 추가되면 glog하는 부분 확인 필요
        user.userTrade.setTaxFreePermitExpirationWithGlogForAdding(
          taxFreePermit.cmsId,
          taxFreePermit.expirationTimeUtc,
          {
            user,
            rsn: this._rsn,
            add_rsn: this._addRsn,
          },
          null /** pr_data */
        );
      });
    }

    // u_battles
    if (this._changes.syncAdd.battleEndResult) {
      user.userBattle.setBattleEndResult(this._changes.syncAdd.battleEndResult);
    }

    // u_battle_rewards
    if (this._changes.syncAdd.battleRewards) {
      const userBattleRewards = user.userBattle.getBattleRewards();
      if (_.isEmpty(userBattleRewards)) {
        // battleEnd 에서 battleRewards 가 set 되는 경우
        const battleRewardsObj = this._changes.syncAdd.battleRewards.reduce<{
          [type: number]: { [id: number]: BattleReward };
        }>((acc, cur) => {
          return _.merge(acc, {
            [cur.Type]: {
              [cur.Id]: cur,
            },
          });
        }, {});

        user.userBattle.setBattleRewards(battleRewardsObj);
      } else {
        // receive 에서 보상을 일부 받는 경우
        for (const elem of this._changes.syncAdd.battleRewards) {
          _.merge<
            { [type: number]: { [id: number]: BattleReward } },
            { [type: number]: { [id: number]: sync.BattleReward } }
          >(userBattleRewards, {
            [elem.Type]: {
              [elem.Id]: elem,
            },
          });
        }
      }
    }

    // u_game_over_losses
    if (this._changes.syncRemove.gameOverLosses) {
      user.userSailing.emptyGameOverLosses();
    }
    if (this._changes.gameOverLosses) {
      user.userSailing.setGameOverLosses(this._changes.gameOverLosses);
    }

    if (this._changes.syncRemove.multiPvpLoss) {
      user.userSailing.setMultiPvpLoss(null);
    }
    if (this._changes.multiPvpLoss) {
      user.userSailing.setMultiPvpLoss(this._changes.multiPvpLoss);
    }

    // u_cash_shop_restricted_products
    if (this._changes.syncRemove.cashShopRestrictedProducts) {
      user.userCashShop.deleteRestrictedProducts(
        this._changes.syncRemove.cashShopRestrictedProducts
      );
    }
    if (this._changes.syncAdd.cashShopRestrictedProducts) {
      _.forOwn(this._changes.syncAdd.cashShopRestrictedProducts, (elem) => {
        user.userCashShop.setRestrictedProduct({
          cmsId: elem.cmsId,
          amount: elem.amount,
          lastBuyingTimeUtc: elem.lastBuyingTimeUtc,
        });
      });
    }

    // u_cash_shop_fixed_term_products
    if (this._changes.syncRemove.cashShopFixedTermProducts) {
      user.userCashShop.deleteFixedTermProducts(this._changes.syncRemove.cashShopFixedTermProducts);
    }
    if (this._changes.syncAdd.cashShopFixedTermProducts) {
      _.forOwn(this._changes.syncAdd.cashShopFixedTermProducts, (elem) => {
        user.userCashShop.setFixedTermProduct({
          cmsId: elem.cmsId,
          startTimeUtc: elem.startTimeUtc,
          endTimeUtc: elem.endTimeUtc,
        });
      });
    }

    // u_cash_shop_gacha_box_guarantees
    if (this._changes.syncAdd.cashShopGachaBoxGuarantees) {
      _.forOwn(this._changes.syncAdd.cashShopGachaBoxGuarantees, (elem, cmsIdStr) => {
        user.userCashShop.setGachaBoxGuaranteeAccum(parseInt(cmsIdStr, 10), elem);
      });
    }

    // u_sound_packs
    if (this._changes.addedSoundPacks) {
      for (const elem of this._changes.addedSoundPacks) {
        user.userCashShop.addSoundPack(elem);
      }
    }

    // u_ship_camouflages
    if (this._changes.addedShipCamouflages) {
      for (const cmsId of this._changes.addedShipCamouflages) {
        user.userFleets.addShipCamouflage(user, cmsId, {
          user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
          exchangeHash: this._exchangeHash,
        });
      }
    }

    // u_contribution_shop_restricted_product
    if (this._changes.syncRemove.contributionShopRestrictedProducts) {
      user.userContributionShop.deleteRestrictedProducts(
        this._changes.syncRemove.contributionShopRestrictedProducts as string[]
      );
    }
    if (this._changes.syncAdd.contributionShopRestrictedProducts) {
      _.forOwn(this._changes.syncAdd.contributionShopRestrictedProducts, (elem) => {
        user.userContributionShop.setRestrictedProduct({
          cmsId: elem.cmsId,
          amount: elem.amount,
          lastBuyingTimeUtc: elem.lastBuyingTimeUtc,
        });
      });
    }

    // u_contribution_shop_restricted_product
    if (this._changes.syncRemove.guildShopRestrictedProducts) {
      user.userGuildShop.deleteRestrictedProducts(
        this._changes.syncRemove.guildShopRestrictedProducts as string[]
      );
    }
    if (this._changes.syncAdd.guildShopRestrictedProducts) {
      _.forOwn(this._changes.syncAdd.guildShopRestrictedProducts, (elem) => {
        user.userGuildShop.setRestrictedProduct({
          cmsId: elem.cmsId,
          amount: elem.amount,
          lastBuyingTimeUtc: elem.lastBuyingTimeUtc,
        });
      });
    }

    // u_discoveries
    if (this._changes.discoveryCmsIds) {
      for (const cmsId of this._changes.discoveryCmsIds) {
        user.userDiscovery.discover(cmsId, user);
      }
    }

    // u_villages
    _.forOwn(this._changes.syncAdd.villages, (village) => {
      user.userVillage.setVillage({
        cmsId: village.cmsId,
        friendship: village.friendship,
        recruitedSailor: village.recruitedSailor,
        lastDepartureTimeUtc: village.lastDepartureTimeUtc,
        lastRecruitedSailorTimeUtc: village.lastRecruitedSailorTimeUtc,
        friendshipFirstRewardReceiveBitflag: village.friendshipFirstRewardReceiveBitflag,
        lastReceiveFriendshipWeeklyRewardGrade: village.lastReceiveFriendshipWeeklyRewardGrade,
        lastReceiveFriendshipWeeklyRewardTimeUtc: village.lastReceiveFriendshipWeeklyRewardTimeUtc,
        lastPlunderTimeUtc: village.lastPlunderTimeUtc,
        maximumFriendship: village.maximumFriendship,
      });
    });

    // u_achievements
    if (this._changes.syncAdd.achievements) {
      _.forOwn(this._changes.syncAdd.achievements, (elem) => {
        // is rewarded
        if (elem.isRewarded === 1) {
          user.userAchievement.setAchievementRewarded(elem.cmsId);
        }
      });
    }

    if (
      this._changes.syncAdd.user &&
      this._changes.syncAdd.user.lastRewardedAchievementPointCmsId !== undefined
    ) {
      // point is rewarded
      user.userAchievement.lastRewardedAchievementPointCmsId =
        this._changes.syncAdd.user.lastRewardedAchievementPointCmsId;
    }

    // u_tasks
    if (this._changes.syncAdd.tasks) {
      _.forOwn(this._changes.syncAdd.tasks, (categoryTasks, categoryStr) => {
        _.forOwn(categoryTasks.tasks, (task) => {
          //  is rewarded
          if (task.isRewarded === 1) {
            user.userAchievement.setTaskRewarded(parseInt(categoryStr, 10), task.cmsId);
          }

          if (task.count > 0) {
            user.userAchievement.setTaskCount(parseInt(categoryStr, 10), task.cmsId, task.count);
          }
        });

        if (categoryTasks.isCategoryRewarded === 1) {
          user.userAchievement.setTaskCategoryRewarded(parseInt(categoryStr, 10));
        }
      });
    }

    // u_event_missions
    if (this._changes.syncAdd.eventPages) {
      _.forOwn(this._changes.syncAdd.eventPages, (eventPage, strEventPageCmsId) => {
        const eventPageCmsId = parseInt(strEventPageCmsId, 10);
        _.forOwn(eventPage.eventMissions, (eventMission, strEventMissionCmsId) => {
          const eventMissionCmsId = parseInt(strEventMissionCmsId, 10);
          const userEventMission = user.userAchievement.getEventMission(
            eventPageCmsId,
            eventMissionCmsId
          );
          if (userEventMission) {
            userEventMission.isRewarded = eventMission.isRewarded;
          } else {
            // 카운팅 개념을 사용하지 않고, isRewarded 만 사용하는 형태의 CMS.EventMission 도 있음.
            // DB, 메모리에 없을 수 있는 것 참고.
            user.userAchievement.setEventMission(eventPageCmsId, eventMissionCmsId, 0, 1);
          }
        });

        const userEventMissionRewardCount =
          user.userAchievement.getEventMissionRewardCount(eventPageCmsId);
        const eventPageCms = cms.EventPage[eventPageCmsId];
        const cmsEventMissionRewardCount = cmsEx.getEventMissionCount(eventPageCms.groupRef);
        // 유저가 보유한 보상수령 갯수하고  cms 미션 갯수와 동일거나 크면 모든 보상 수령 확인
        if (userEventMissionRewardCount >= cmsEventMissionRewardCount) {
          user.userAchievement.setEventPageComplete(eventPageCmsId, true);
          eventPage.bCompleted = true;
          eventPage.eventMissions = undefined;
        }
      });
    }

    // u_pass_events
    if (this._changes.passEvents) {
      _.forOwn(this._changes.passEvents, (passEvent, strEventPageCmsId) => {
        const passEventPageCms = cms.EventPage[strEventPageCmsId];
        if (passEvent.expChange !== undefined) {
          // exp 와 level 은 모두 있거나 모두 없어야함.
          user.userPassEvent.setPassEventExpLevel(
            passEventPageCms,
            passEvent.expChange.exp,
            passEvent.expChange.level,
            { user, rsn: this._rsn, add_rsn: this._addRsn, pr_data: null }
          );
        }
        if (passEvent.lastDailyResetTimeUtc !== undefined) {
          user.userPassEvent.setPassEventLastDailyResetTimeUtc(
            passEventPageCms.id,
            passEvent.lastDailyResetTimeUtc
          );
        }
      });
    }

    // u_pass_event_missions
    if (this._changes.passEventMissions) {
      _.forOwn(this._changes.passEventMissions, (changes, strEventPageCmsId) => {
        const eventPageCms = cms.EventPage[strEventPageCmsId];

        const params: PassEventMission[] = [];
        const userEventMissions = user.userPassEvent.getPassEventMissions(eventPageCms.id);
        _.forOwn(changes, (elem) => {
          const userEventMission = userEventMissions[elem.eventMissionCmsId];
          const userCount = userEventMission ? userEventMission.count : 0;
          const userRepeatedReceiveRewardCount = userEventMission
            ? userEventMission.repeatedRewardReceiveCount
            : 0;
          const userIsRewarded = userEventMission ? userEventMission.isRewarded : 0;
          params.push({
            eventMissionCmsId: elem.eventMissionCmsId,
            count: elem.count !== undefined ? elem.count : userCount,
            repeatedRewardReceiveCount:
              elem.repeatedRewardReceiveCount !== undefined
                ? elem.repeatedRewardReceiveCount
                : userRepeatedReceiveRewardCount,
            isRewarded: elem.isRewarded !== undefined ? elem.isRewarded : userIsRewarded,
          });
        });
        user.userPassEvent.applyPassEventMissions(eventPageCms, params);
      });
    }

    // u_attendances
    if (this._changes.syncAdd.attendances) {
      _.forOwn(this._changes.syncAdd.attendances, (attendance) => {
        user.userAttendance.setAttendance({
          eventPageCmsId: attendance.eventPageCmsId,
          accum: attendance.accum,
          consecutive: attendance.consecutive,
          lastAttendanceTimeUtc: attendance.lastAttendanceTimeUtc,
          lastRewardedConsecutiveAttendanceCmsId: attendance.lastRewardedConsecutiveAttendanceCmsId,
          startTimeUtc: attendance.startTimeUtc,
          endTimeUtc: attendance.endTimeUtc,
        });
      });
    }

    if (this._changes.pubStaffChanges) {
      _.forEach(this._changes.pubStaffChanges, (change) => {
        user.userTown.setPubStaff(this._user, change, this._rsn, this._addRsn);
      });
    }

    // u_daily_subscriptions
    if (this._changes.syncAdd.dailySubscriptions) {
      _.forOwn(this._changes.syncAdd.dailySubscriptions, (ds) => {
        user.userCashShop.setDailySubscription({
          cmsId: ds.cmsId,
          createTimeUtc: ds.createTimeUtc,
          expireTimeUtc: ds.expireTimeUtc,
          lastReceiveTimeUtc: ds.lastReceiveTimeUtc,
        });
      });
    }

    // u_direct_mails
    if (this._changes.newMail) {
      for (const mail of this._changes.newMail) {
        user.userMails.addDirectMail(mail, { user });
      }
    }
    if (this._changes.syncAdd.userDirectMails) {
      _.forOwn(this._changes.syncAdd.userDirectMails, (elem) => {
        if (elem.state !== undefined) {
          user.userMails.setDirectMailState(elem.id, elem.state);
        }
        if (elem.expireTimeUtc !== undefined) {
          user.userMails.setDirectMailExpireTimeUtc(elem.id, elem.expireTimeUtc);
        }
      });
    }

    // u_line_mails
    if (this._changes.syncAdd.lineMails) {
      _.forOwn(this._changes.syncAdd.lineMails, (elem) => {
        if (elem.state !== undefined) {
          user.userMails.setLineMailState(elem.id, elem.state);
        }
      });
    }

    // u_trade_area
    if (this._changes.syncAdd.tradeArea) {
      _.forOwn(this._changes.syncAdd.tradeArea, (tradeArea) => {
        user.userTradeArea.applyChange(tradeArea, {
          user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
        });
      });
    }

    if (this._changes.syncAdd.eventGames) {
      _.forOwn(this._changes.syncAdd.eventGames, (eventGame) => {
        _.forOwn(eventGame, (elem) => {
          user.userEventGames.applyChange(elem, { user, rsn: this._rsn, add_rsn: this._addRsn });
        });
      });
    }

    // u_fleet_dispatches
    if (this._changes.syncAdd.fleetDispatches) {
      _.forOwn(this._changes.syncAdd.fleetDispatches, (dpSync) => {
        const dispatch = user.userFleetDispatch.getDispatch(dpSync.fleetIndex);
        if (dispatch) {
          if (dpSync.endView) {
            dispatch.state = dpSync.state;
            dispatch.endView = dpSync.endView;
          }

          if (dpSync.rewards) {
            dispatch.rewards = dpSync.rewards;
            dispatch.lostRewards = dpSync.lostRewards;
            dispatch.lostRewardIdCount = dpSync.lostRewardIdCount;
          }
        }
      });
    }

    if (this._changes.syncRemove.fleetDispatches) {
      _.forOwn(this._changes.syncRemove.fleetDispatches, (elem, fleetIndexStr) => {
        if (elem === true) {
          user.userFleetDispatch.removeDispatch(parseInt(fleetIndexStr, 10));
        }
      });
    }

    // etc.

    // bGameOver
    if (this._changes.syncAdd.user && this._changes.syncAdd.user.bGameOver !== undefined) {
      if (this._changes.syncAdd.user.bGameOver) {
        // TODO
      } else {
        user.userSailing.resetGameOver(user, false);
      }
    }

    // reset town state
    if (this._changes.bResetTownState) {
      user.userTown.resetAllUserTownStates();
    }

    // guild synthesis
    if (
      this._changes.syncRemove.userGuild &&
      this._changes.syncRemove.userGuild.synthesisProgresses
    ) {
      _.forOwn(
        this._changes.syncRemove.userGuild.synthesisProgresses,
        (elem: boolean, slotNoStr: string) => {
          if (elem === true) {
            user.userGuild.removeGulidSynthesisProgress(parseInt(slotNoStr, 10));
          }
        }
      );
    }

    if (this._changes.syncAdd.userGuild && this._changes.syncAdd.userGuild.synthesisProgresses) {
      _.forOwn(this._changes.syncAdd.userGuild.synthesisProgresses, (elem) => {
        user.userGuild.applyGulidSynthesisProgress({
          cmsId: elem.cmsId,
          slot: elem.slot,
          startTimeUtc: elem.startTimeUtc,
          completionTimeUtc: elem.completionTimeUtc,
        });
      });
    }

    if (this._changes.syncAdd.hotSpotProducts) {
      _.forOwn(this._changes.syncAdd.hotSpotProducts, (elem) => {
        user.userCashShop.setHotSpotProduct({
          cmsId: elem.cmsId,
          popupCount: elem.popupCount,
          expireTimeUtc: elem.expireTimeUtc,
          coolTimeUtc: elem.coolTimeUtc,
          lastResetTimeUtc: elem.lastResetTimeUtc,
        });
      });
    }

    if (this._changes.addedEventRankingRewardIdx) {
      _.forOwn(this._changes.addedEventRankingRewardIdx, (rewardIdxArr, eventPageCmsId) => {
        for (const rewardIdx of rewardIdxArr) {
          user.userEventRanking.addRewardIdx(parseInt(eventPageCmsId, 10), rewardIdx);
        }
      });
    }

    if (this._changes.addedDiscoveryReward) {
      for (const cmsId of this._changes.addedDiscoveryReward) {
        user.userDiscoveryReward.addReward(cmsId);
      }
    }

    if (this._changes.syncAdd.user && !mutil.isNotANumber(this._changes.syncAdd.user.karma)) {
      const karma = this._changes.syncAdd.user.karma;
      const lastUpdateTimeUtc = this._changes.syncAdd.user.lastKarmaUpdateTimeUtc;
      user.applyKarma(
        { karma, lastUpdateTimeUtc },
        { user, rsn: this._rsn, add_rsn: this._addRsn }
      );
    }

    if (this._changes.addedFishCatchRewards) {
      _.forOwn(this._changes.addedFishCatchRewards, (elem) => {
        user.userFishing.setFishCatch(elem);
      });
    }

    if (this._changes.addedUserTitles) {
      const curTimeUtc: number = mutil.curTimeUtc();
      _.forOwn(this._changes.addedUserTitles, (elem) => {
        if (elem.isEquipped) {
          user.userTitles.equipUserTitle(user, elem, curTimeUtc, buffSync);
          user.userTitles.notifyCurTitleChanged(user, curTimeUtc);
        } else {
          user.userTitles.setUserTitle(elem, buffSync);
        }
      });
    }

    if (this._changes.sweepTicket) {
      user.userSweepTicket.applyTicketCount(
        this._changes.sweepTicket.count,
        this._changes.sweepTicket.buyCount,
        {
          user: this._user,
          rsn: this._rsn,
          add_rsn: this._addRsn,
          exchangeHash: this.getExchangeHash(),
        },
        buffSync.sync
      );
    }

    if (this._changes.syncAdd.pets) {
      _.forOwn(this._changes.syncAdd.pets, (srcPet) => {
        if (user.userPets.hasPet(srcPet.cmsId)) {
          const petNub = user.userPets.getPet(srcPet.cmsId);
          if (srcPet.sidekickSlot !== undefined) {
            petNub.sidekickSlot = srcPet.sidekickSlot;
          }
        } else {
          user.userPets.addNewPet(srcPet.cmsId);
        }
      });
    }

    if (this._changes.syncAdd.shipCompose) {
      _.forOwn(this._changes.syncAdd.shipCompose, (shipComposeInfo, groupIdStr) => {
        const groupId = parseInt(groupIdStr, 10);
        user.userFleets.setShipCompose(groupId, shipComposeInfo);
      });
    }

    if (this._changes.syncAdd.clearedInfiniteLighthouseStages) {
      _.forOwn(
        this._changes.syncAdd.clearedInfiniteLighthouseStages,
        (stageClearedInfos, sessionIdStr) => {
          const sessionId = parseInt(sessionIdStr, 10);
          _.forOwn(stageClearedInfos, (clearInfo, stageNumStr) => {
            const stageNum = parseInt(stageNumStr, 10);
            user.userInfiniteLighthouse.setClearStageInfo(sessionId, stageNum, clearInfo.usedTurn);
          });
        }
      );
    }

    if (this._changes.reentrys) {
      _.forOwn(this._changes.reentrys, (reentry) => {
        user.userReentry.applyReentry(reentry, sync);
      });
    }

    if (this._changes.clash) {
      user.userClash.applySession(this._changes.clash, sync);
    }

    // merge buff sync
    if (buffSync.sync.add) {
      _.merge<sync.All, sync.All>(this._changes.syncAdd, buffSync.sync.add);
    }
    if (buffSync.sync.remove) {
      _.merge<sync.All, sync.All>(this._changes.syncRemove, buffSync.sync.remove);
    }

    // u_manufacture_exp
    if (this._changes.manufactureExpLevel) { 
      const syncExp = user.userManufacture.applyExpLevelChange(Object.values(this._changes.manufactureExpLevel), {
        user,
        rsn: this._rsn,
        add_rsn: this._addRsn,
      });
      _.merge<sync.Sync, sync.Sync>(sync, syncExp);
    }
  }

  protected _buildSync(sync: sync.Sync): void {
    // u_users
    if (this._changes.bResetPalaceRoyalOrder) {
      _.merge<sync.Sync, sync.Sync>(sync, {
        remove: {
          user: {
            palaceRoyalOrderCmsId: true,
            palaceRoyalTitleOrderCmsId: true,
            palaceRoyalOrderRnds: true,
            palaceRoyalTitleOrderRnds: true,
          },
        },
      });
    }

    // Mate exp.
    if (this._changes.mateExp) {
      if (sync.add.mates === undefined) {
        sync.add.mates = {};
      }

      _.forOwn(this._changes.mateExp, (changes, jobTypeStr) => {
        for (const change of changes) {
          if (!sync.add.mates[change.mateCmsId]) {
            sync.add.mates[change.mateCmsId] = {};
          }

          const jobType = parseInt(jobTypeStr, 10);
          switch (jobType) {
            case cmsEx.JOB_TYPE.ADVENTURE:
              sync.add.mates[change.mateCmsId].adventureExp = change.exp;
              sync.add.mates[change.mateCmsId].adventureLevel = change.level;
              break;
            case cmsEx.JOB_TYPE.TRADE:
              sync.add.mates[change.mateCmsId].tradeExp = change.exp;
              sync.add.mates[change.mateCmsId].tradeLevel = change.level;
              break;
            case cmsEx.JOB_TYPE.BATTLE:
              sync.add.mates[change.mateCmsId].battleExp = change.exp;
              sync.add.mates[change.mateCmsId].battleLevel = change.level;
              break;
          }
        }
      });
    }

    // mate loyalty
    if (this._changes.mateLoyalty) {
      for (const elem of this._changes.mateLoyalty) {
        _.merge<sync.Sync, sync.Sync>(sync, {
          add: {
            mates: {
              [elem.mateCmsId]: {
                loyalty: elem.value,
              },
            },
          },
        });
      }
    }

    // u_ships
    if (this._changes.shipIdsToDismantle) {
      if (!this._changes.syncRemove.ships) {
        this._changes.syncRemove.ships = [];
      }
      for (const shipId of this._changes.shipIdsToDismantle) {
        this._changes.syncRemove.ships.push(shipId.toString());
      }
    }

    // u_ship_slots
    if (
      this._changes.unequippedShipSlotItemIds &&
      this._changes.unequippedShipSlotItemIds.length > 0
    ) {
      if (!this._changes.syncRemove.shipSlotItems) {
        this._changes.syncRemove.shipSlotItems = {};
      }
      for (const shipSlotItemId of this._changes.unequippedShipSlotItemIds) {
        this._changes.syncRemove.shipSlotItems[shipSlotItemId] = [
          'equippedShipId',
          'equippedShipSlotIdx',
        ];
      }
    }

    // battle rewards
    if (this._changes.syncAdd.battleRewards) {
      sync.remove.battleRewards = true;
      sync.add.battleRewards = [];
      const battleRewards = this._user.userBattle.getBattleRewards();
      for (const type of Object.keys(battleRewards)) {
        for (const id of Object.keys(battleRewards[type])) {
          if (battleRewards[type][id].Quantity !== 0) {
            sync.add.battleRewards.push(battleRewards[type][id]);
          }
        }
      }
    }

    // u_pass_events
    if (this._changes.passEvents) {
      // 다른데서 먼저 만들어지지는 않을 듯?
      sync.add.passEvents = {};

      _.forOwn(this._changes.passEvents, (passEvent, strEventPageCmsId) => {
        const passEventPageCms = cms.EventPage[strEventPageCmsId];

        const syncPassEvent: sync.PassEvent = {
          eventPageCmsId: passEventPageCms.id,
        };

        if (passEvent.expChange !== undefined) {
          syncPassEvent.exp = passEvent.expChange.exp;
          syncPassEvent.level = passEvent.expChange.level;
        }
        if (passEvent.lastDailyResetTimeUtc !== undefined) {
          syncPassEvent.lastDailyResetTimeUtc = passEvent.lastDailyResetTimeUtc;
          if (passEvent.lastDailyResetTimeUtc === null) {
            // null 로 설정할 일이 있을지 모르겠어서 구현없이 에러로그만.
            mlog.error('not implemented to set lastDailyResetTimeUtc to null', {
              userId: this._user.userId,
              rsn: this._rsn,
              addRsn: this._addRsn,
            });
          }
        }

        sync.add.passEvents[passEventPageCms.id] = syncPassEvent;
      });
    }

    // u_pass_event_missions
    if (this._changes.passEventMissions) {
      _.forOwn(this._changes.passEventMissions, (passEventMissions, strEventPageCmsId) => {
        const passEventPageCms = cms.EventPage[strEventPageCmsId];
        const syncEventMissions: sync.PassEvent['eventMissions'] = {};

        _.forOwn(passEventMissions, (eventMission) => {
          syncEventMissions[eventMission.eventMissionCmsId] = {
            eventMissionCmsId: eventMission.eventMissionCmsId,
            count: eventMission.count,
            repeatedRewardReceiveCount: eventMission.repeatedRewardReceiveCount,
            isRewarded: eventMission.isRewarded,
          };
        });

        _.merge<sync.Sync, sync.Sync>(sync, {
          add: {
            passEvents: {
              [passEventPageCms.id]: {
                eventPageCmsId: passEventPageCms.id,
                eventMissions: syncEventMissions,
              },
            },
          },
        });

        if (this._user.userPassEvent.isPassEventCompleted(passEventPageCms.id)) {
          // 변하는 시점에만 보낼 수 있으면 좋을지? 일단 완료된 이후에 액션이 없을 듯 해서..
          sync.add.passEvents[passEventPageCms.id].bCompleted = true;

          // 완료된 상태면 eventMissions 내역을 sync 로 보내줄 필요도 없긴함.
        }
      });
    }

    if (this._changes.pubStaffChanges) {
      for (const change of this._changes.pubStaffChanges) {
        _.merge<sync.Sync, sync.Sync>(sync, {
          add: {
            towns: {
              [change.townCmsId]: {
                myPubStaffIntimacy: change,
              },
            },
          },
        });
      }

      _.forOwn(this._user.userTown.getPubStaffs(), (pubStaff) => {
        _.merge<sync.Sync, sync.Sync>(sync, {
          add: {
            pubStaffIntimacy: {
              [pubStaff.pubStaffCmsId]: pubStaff.intimacy,
            },
          },
        });
      });
    }

    // u_direct_mails
    if (this._changes.newMail) {
      for (const mail of this._changes.newMail) {
        _.merge<sync.Sync, sync.Sync>(sync, {
          add: {
            userDirectMails: {
              [mail.id]: this._user.userMails.getDirectMailSyncData(mail.id),
            },
          },
        });
      }
    }

    // gameOverLosses
    if (this._changes.gameOverLosses) {
      sync.add.gameOverLosses = this._changes.gameOverLosses;
    }

    // multiPvpLoss
    if (this._changes.multiPvpLoss) {
      sync.add.multiPvpLoss = this._changes.multiPvpLoss;
    }

    // u_discoveries
    if (this._changes.discoveryCmsIds && this._changes.discoveryCmsIds.length > 0) {
      const discoverySync: { [offset: number]: number } = {};
      for (const cmsId of this._changes.discoveryCmsIds) {
        const offset = Math.floor(cmsId / 32);
        const idxField = (discoverySync[offset] | (1 << cmsId % 32)) >>> 0;
        discoverySync[offset] = idxField;
      }
      sync.add.discoveries = discoverySync;
    }

    // u_event_ranking_received_rewards
    if (this._changes.addedEventRankingRewardIdx) {
      if (sync.add.eventRanking === undefined) {
        sync.add.eventRanking = {};
      }

      _.forOwn(this._changes.addedEventRankingRewardIdx, (_, eventPageCmsIdStr) => {
        const eventPageCmsId = parseInt(eventPageCmsIdStr, 10);
        sync.add.eventRanking[eventPageCmsId] = {
          receivedRewards: this._user.userEventRanking.getReceivedRewards(eventPageCmsId),
        };
      });
    }

    // u_discovery_rewards
    if (this._changes.addedDiscoveryReward) {
      sync.add.discoveryRewards = this._user.userDiscoveryReward.getReceivedRewards();
    }

    // u_ship_camouflages
    if (this._changes.addedShipCamouflages) {
      sync.add.shipCamouflages = this._user.userFleets.getShipCamouflages();
    }

    // u_fishes
    if (this._changes.addedFishCatchRewards) {
      if (sync.add.fishCatch === undefined) {
        sync.add.fishCatch = {};
      }

      _.forOwn(this._changes.addedFishCatchRewards, (elem) => {
        sync.add.fishCatch[elem.fishId] = this._user.userFishing.getFishCatch(elem.fishId);
      });
    }

    // u_manufacture_exp
    if (this._changes.manufactureExpLevel) {
      if (sync.add.manufacture.expLevel === undefined) {
        sync.add.manufacture.expLevel = {};
      }
      const manufactureExpLevelSync = sync.add.manufacture.expLevel;
      _.forOwn(this._changes.manufactureExpLevel, (change, typeStr) => {
        const type = parseInt(typeStr, 10);
        manufactureExpLevelSync[type] = {
          type,
          exp: change.exp,
          level: change.level,
        };
      });
    }
  }

  protected _accumulateAchievements(sync: sync.Sync): Promise<sync.Sync> {
    const user = this._user;
    const actualGain = this._actualGain;
    const changes = this._changes;
    const accums: AccumulateParam[] = [];

    // u_soft_data
    if (actualGain.userLevel) {
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.COMPANY_LEVEL,
        addedValue: actualGain.userLevel,
      });
    }

    // u_ship_blueprints
    if (this._changes.syncAdd.shipBlueprints) {
      _.forOwn(this._changes.syncAdd.shipBlueprints, (elem) => {
        if (elem.level !== undefined) {
          if (elem.level > 1) {
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.TRY_UPDATE_SHIP_BLUEPRINT,
              addedValue: 1,
            });

            const bpShips = user.userFleets.getShipsByBP(elem.cmsId);
            if (bpShips.length > 0) {
              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SHIP,
                targets: [elem.level],
                addedValue: bpShips.length,
              });

              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP,
                targets: [bpShips[0].getNub().cmsId, elem.level],
                addedValue: bpShips.length,
              });
            }
          } else {
            // 설계도가 해금됨
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.UNLOCK_SHIP_BLUEPRINT,
              addedValue: 1,
            });
          }
        }
      });
    }

    // u_mates
    if (actualGain.mates && actualGain.mates.length > 0) {
      for (const mateCmsId of actualGain.mates) {
        for (let i = 1; i <= cmsEx.getMateHighestLanguageLevel(mateCmsId); i++) {
          const accum: AccumulateParam = {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.MATE_LANGUAGE_LEVEL,
            targets: [i],
            addedValue: 1,
          };
          accums.push(accum);
        }

        const mateCms = cms.Mate[mateCmsId];
        const userMate = user.userMates.getMate(mateCmsId);
        accums.push(
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_MATE,
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_NATION_MATE,
            targets: [mateCms.nationId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_MATE,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
            targets: [mateCmsId],
            addedValue: 3,
          }
        );

        if (mateCms.character.charType === cmsEx.CHARACTER_TYPE.LEADERABLE_MATE) {
          accums.push(
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_ADMIRAL,
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_ADMIRAL,
              targets: [mateCmsId],
              addedValue: 1,
            }
          );

          const mateRoyalTitle = userMate.getRoyalTitle();
          let targets = cmsEx.getAchievementTermsTargets(cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE, 0);

          if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
            for (const target of targets) {
              if (mateRoyalTitle < target) {
                break;
              }
              if (target !== mateRoyalTitle) {
                continue;
              }

              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE,
                targets: [target],
                addedValue: 1,
              });
            }
          }

          targets = cmsEx.getAchievementTermsTargets(
            cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
            1
          );
          if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
            for (const target of targets) {
              if (mateRoyalTitle < target) {
                break;
              }
              if (target !== mateRoyalTitle) {
                continue;
              }

              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
                targets: [mateCmsId, target],
                addedValue: 1,
              });
            }
          }
        }
      }
    }
    if (changes.mateExp) {
      const mateLevels: {
        [mateCmsId: number]: { [jobType: number]: { old: number; new: number } };
      } = {};
      _.forOwn(changes.mateExp, (changes, jobtypeStr) => {
        const jobType = parseInt(jobtypeStr, 10);
        for (const change of changes) {
          if (change.level === change.oldLevel) {
            continue;
          }

          switch (jobType) {
            case cmsEx.JOB_TYPE.ADVENTURE:
              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
                targets: [change.mateCmsId],
                addedValue: change.level - change.oldLevel,
              });
              break;
            case cmsEx.JOB_TYPE.TRADE:
              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
                targets: [change.mateCmsId],
                addedValue: change.level - change.oldLevel,
              });
              break;
            case cmsEx.JOB_TYPE.BATTLE:
              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
                targets: [change.mateCmsId],
                addedValue: change.level - change.oldLevel,
              });
              break;
          }

          if (!mateLevels[change.mateCmsId]) {
            mateLevels[change.mateCmsId] = {};
          }
          mateLevels[change.mateCmsId][jobtypeStr] = {
            old: change.oldLevel,
            new: change.level,
          };
        }
      });

      _.forOwn(mateLevels, (levels, mateCmsIdStr) => {
        const mateCmsId = parseInt(mateCmsIdStr, 10);
        let oldLv = 0;
        let newLv = 0;
        const userMate = user.userMates.getMate(mateCmsId);
        for (const elem in cmsEx.JOB_TYPE) {
          const jobType = parseInt(elem, 10);
          if (mutil.isNotANumber(jobType) || jobType === 0) {
            continue;
          }
          if (levels[jobType]) {
            oldLv += levels[jobType].old;
            newLv += levels[jobType].new;
          } else {
            oldLv += userMate.getLevel(jobType);
            newLv += userMate.getLevel(jobType);
          }
        }

        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
          targets: [mateCmsId],
          addedValue: newLv - oldLv,
        });
      });
    }

    if (changes.totalGainFames) {
      _.forOwn(changes.totalGainFames, (fame, jobTypeStr) => {
        if (!Number.isInteger(fame) || fame <= 0) {
          const termsName = cmsEx.ACHIEVEMENT_TERMS[cmsEx.ACHIEVEMENT_TERMS.GAIN_FAME];
          mlog.error(`[ACHIEVEMENT_TERMS.${termsName}] positive integer expected`, {
            userId: user.userId,
            reason: this._reason,
            rsn: this._rsn,
            addRsn: this._addRsn,
          });
          return;
        }
        const jobType = parseInt(jobTypeStr, 10);
        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_FAME,
          targets: [jobType],
          addedValue: fame,
        });
      });
    }

    // u_ships
    if (actualGain.ships) {
      _.forOwn(actualGain.ships, (count, shipCmsIdStr) => {
        const shipCmsId = parseInt(shipCmsIdStr, 10);

        accums.push(
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SHIP,
            addedValue: count,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_SHIP,
            targets: [shipCmsId],
            addedValue: count,
          }
        );

        const shipCms = cms.Ship[shipCmsId];
        const userBP = user.userShipBlueprints.getUserShipBlueprint(shipCms.shipBlueprintId);
        const bpLv = userBP ? userBP.level : 1;

        for (let i = 1; i <= bpLv; i++) {
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SHIP,
            targets: [i],
            addedValue: count,
          });
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP,
            targets: [shipCmsId, i],
            addedValue: count,
          });
        }
      });
    }
    // 선박 분해
    if (changes.shipIdsToDismantle) {
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DISMANTLE_SHIP,
        addedValue: changes.shipIdsToDismantle.length,
      });
    }
    // 선원 해고
    if (changes.firedSailors) {
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.FIRE_SAILOR,
        addedValue: changes.firedSailors,
      });
    }

    // u_cash_shop_fixed_term_products
    if (this._changes.syncAdd.cashShopFixedTermProducts) {
      // quest pass 구입에 따른 ACHIEVEMENT_TERMS.COMPLETE_QUEST_NODE
      // hint: cms Achievement.needQuestPassId
      _.forOwn(this._changes.syncAdd.cashShopFixedTermProducts, (elem) => {
        const cashShopCms = cms.CashShop[elem.cmsId];
        if (cashShopCms.productType !== CASH_SHOP_PRODUCT_TYPE.QUEST_PASS) {
          return;
        }
        const questPassCms = cms.QuestPass[cashShopCms.questPassId];
        for (const questCmsId of questPassCms.questId) {
          const questCtx = user.questManager.getContext(questCmsId);
          if (!questCtx) {
            continue;
          }
          const questCms = cms.Quest[questCmsId];
          for (let i = 0; i < questCtx.nodeIdx; i++) {
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.COMPLETE_QUEST_NODE,
              targets: [questCmsId, questCms.nodes[i]],
              addedValue: 1,
            });
          }
        }
      });
    }

    // sailedDays
    if (changes.sailedDays) {
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SAILING_DAYS,
        addedValue: changes.sailedDays,
      });
    }

    // discovery
    if (changes.landExploreCmsId) {
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.LAND_EXPLORE,
        addedValue: 1,
      });

      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_LAND_EXPLORE,
        targets: [changes.landExploreCmsId],
        addedValue: 1,
      });
      // 부모 landExploreCmsId 가 존재하면 추가 accum
      if (cms.LandExplore[changes.landExploreCmsId].inheritLandFeature) {
        const inheritCmsId = cms.LandExplore[changes.landExploreCmsId].inheritLandFeature;
        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_LAND_EXPLORE,
          targets: [inheritCmsId],
          addedValue: 1,
        });
      }
    }
    if (changes.discoveryCmsIds && changes.discoveryCmsIds.length > 0) {
      for (const cmsId of changes.discoveryCmsIds) {
        const discoveryCms = cms.Discovery[cmsId];
        accums.push(
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DISCOVER_SPECIFIC_CATEGORY,
            targets: [discoveryCms.discoveryType],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DISCOVER,
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DISCOVER_SPECIFIC,
            targets: [discoveryCms.id],
            addedValue: 1,
          }
        );
      }
    }

    // 에너지 소모(사용) 업적 관련 갱신을 한다
    if (changes.energyConsumeAmount) {
      const consumeEnergy = changes.energyConsumeAmount;
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.USE_ENERGY,
        addedValue: consumeEnergy * -1,
      });
    }

    if (this._changes.itemGainAccumParams && this._changes.itemGainAccumParams.length > 0) {
      for (const accumParams of this._changes.itemGainAccumParams) {
        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ACCUMULATED_ITEM_COUNT,
          targets: accumParams.targets,
          addedValue: accumParams.addedValue,
        });
      }
    }

    return user.userAchievement.accumulate(accums, user, sync, {
      user,
      rsn: this._rsn,
      add_rsn: this._addRsn,
    });
  }

  protected glogBeforeApplyToUser() {
    // pr_data 때문에 로그 남기는 위치를 각 api 로 옮김
    // if (this._changes.syncAdd.user && this._changes.syncAdd.user.nationCmsId !== undefined) {
    //   glog(
    //     'company_nation',
    //     {
    //       rsn: this._rsn,
    //       add_rsn: this._addRsn,
    //       old_nation: this._user.nationCmsId,
    //       cur_nation: this._changes.syncAdd.user.nationCmsId,
    //     },
    //     this._user
    //   );
    // }

    // 리워드로 선박 지급될 때 이름이 주어진 경우.
    _.forOwn(this._changes.syncAdd.ships, (ship) => {
      if (!ship.name) {
        return;
      }

      if (this._user.userFleets.getShip(ship.id)) {
        return;
      }

      const shipCms = cms.Ship[ship.cmsId];
      this._user.glog('ship_nickname', {
        rsn: this._rsn,
        add_rsn: this._addRsn,
        ship_id: shipCms.id,
        ship_name: shipCms.name,
        ship_uid: ship.id,
        ship_guid: ship.guid,
        old_nick: shipCms.name,
        cur_nick: ship.name,
      });
    });
  }

  protected glog() {
    const actualGain = this._actualGain;
    const user = this._user;

    if (actualGain.points) {
      if (actualGain.points[cmsEx.DucatPointCmsId]) {
        user.glog('common_gamemoney', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          fcv: actualGain.points[cmsEx.DucatPointCmsId],
          frv: user.userPoints.getPoint(cmsEx.DucatPointCmsId),
          pcv: 0,
          prv: 0,
          sk: user.storeCode,
        });
      }
      if (actualGain.points[cmsEx.BlueGemPointCmsId]) {
        user.glog('common_bluegem', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          fcv: actualGain.points[cmsEx.BlueGemPointCmsId],
          frv: user.userPoints.getPoint(cmsEx.BlueGemPointCmsId),
          pcv: 0,
          prv: 0,
          sk: user.storeCode,
        });
      }
      if (actualGain.points[cmsEx.EnergyPointCmsId]) {
        user.glog('energy', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          cv: actualGain.points[cmsEx.EnergyPointCmsId],
          rv: user.userEnergy.rawEnergy,
        });
      }
      if (actualGain.points[cmsEx.ContributionPointCmsId]) {
        user.glog('contribution_point', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          cv: actualGain.points[cmsEx.ContributionPointCmsId],
          rv: user.userPoints.getPoint(cmsEx.ContributionPointCmsId),
        });
      }
      if (actualGain.points[cmsEx.GuildCoinCmsId]) {
        GuildLogUtil.gLog_GuildPoint(
          user,
          this._rsn,
          this._addRsn,
          actualGain.points[cmsEx.GuildCoinCmsId],
          user.userPoints.getPoint(cmsEx.GuildCoinCmsId)
        );
      }
      if (actualGain.points[cmsEx.ManufacturePointCmsId]) {
        user.glog('manufacture_point', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          cv: actualGain.points[cmsEx.ManufacturePointCmsId],
          rv: user.userManufacture.point,
        });
      }
    }

    if (this._changes.pointsConsumeAmountForGlog) {
      const pointsConsumed = this._changes.pointsConsumeAmountForGlog;
      if (pointsConsumed[cmsEx.DucatPointCmsId]) {
        user.glog('common_gamemoney', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          fcv: pointsConsumed[cmsEx.DucatPointCmsId],
          frv: user.userPoints.getPoint(cmsEx.DucatPointCmsId),
          pcv: 0,
          prv: 0,
          sk: user.storeCode,
        });
      }
      if (pointsConsumed[cmsEx.BlueGemPointCmsId]) {
        user.glog('common_bluegem', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          fcv: pointsConsumed[cmsEx.BlueGemPointCmsId],
          frv: user.userPoints.getPoint(cmsEx.BlueGemPointCmsId),
          pcv: 0,
          prv: 0,
          sk: user.storeCode,
        });
      }
      if (pointsConsumed[cmsEx.EnergyPointCmsId]) {
        user.glog('energy', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          cv: pointsConsumed[cmsEx.EnergyPointCmsId],
          rv: user.userEnergy.rawEnergy,
        });
      }
      if (pointsConsumed[cmsEx.ContributionPointCmsId]) {
        user.glog('contribution_point', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          cv: pointsConsumed[cmsEx.ContributionPointCmsId],
          rv: user.userPoints.getPoint(cmsEx.ContributionPointCmsId),
        });
      }
      if (pointsConsumed[cmsEx.GuildCoinCmsId]) {
        GuildLogUtil.gLog_GuildPoint(
          user,
          this._rsn,
          this._addRsn,
          pointsConsumed[cmsEx.GuildCoinCmsId],
          user.userPoints.getPoint(cmsEx.GuildCoinCmsId)
        );
      }
      if (pointsConsumed[cmsEx.ManufacturePointCmsId]) {
        user.glog('manufacture_point', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          cv: pointsConsumed[cmsEx.ManufacturePointCmsId],
          rv: user.userManufacture.point,
        });
      }
    }

    if (actualGain.discoverCmsIds && actualGain.discoverCmsIds.length > 0) {
      for (const discoveryCmsId of actualGain.discoverCmsIds) {
        const discoveryCms = cms.Discovery[discoveryCmsId];
        const discovery_data = {
          category: DISCOVERY_GROUP[discoveryCms.discoveryGroup],
          subgroup: DISCOVERY_TYPE[discoveryCms.discoveryType],
          id: discoveryCms.id,
          name: discoveryCms.name,
        };
        user.glog('discovery_book', {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          category: discovery_data.category,
          subgroup: discovery_data.subgroup,
          id: discovery_data.id,
          name: discovery_data.name,
          grade: discoveryCms.discoveryGrade,
          reward_data: null,
        });
      }
    }

    if (this._changes.questDropForGlogs && this._changes.questDropForGlogs.length > 0) {
      for (const questDropForGlog of this._changes.questDropForGlogs) {
        const questCms = cms.Quest[questDropForGlog.questCmsId];
        const step = questDropForGlog.step;
        const questNodeCmsId = questCms.nodes[step];
        const questNodeCms = cms.QuestNode[questNodeCmsId];

        const collection =
          questCms.category === cmsEx.QUEST_CATEGORY.SCENARIO ||
          questCms.category === cmsEx.QUEST_CATEGORY.RELATIONSHIP
            ? 'admiral_story'
            : 'quest';

        user.glog(collection, {
          rsn: this._rsn,
          add_rsn: this._addRsn,
          category: questCms.category,
          flag: 3, // 1: 수락, 2: 노드 완료, 3: 포기
          id: questCms.id,
          name: questCms.name,
          step,
          progress: step + '/' + questCms.nodes.length,
          reward_data: null,
          pr_data: collection === 'admiral_story' ? null : undefined,
          chapter: questNodeCms?.name ?? null,
          quest_node_id: questNodeCms?.id ?? null,
          script_name: questNodeCms?.script ?? null,
        });
      }
    }
  }

  // uid 가 필요한 타입이 추가될 경우 cashShopBuyWithoutPurchase.ts getActualGain 도 같이 수정해줘야 된다.
  static covertActualGainToGLogRewardData(gain: ActualGain): RewardData[] {
    const ret: RewardData[] = [];
    if (gain.points) {
      _.forOwn(gain.points, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.POINT],
          id: parseInt(cmsIdStr, 10),
          uid: null,
          amt: amount,
        });
      });
    }
    if (gain.items) {
      _.forOwn(gain.items, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.ITEM],
          id: parseInt(cmsIdStr, 10),
          uid: null,
          amt: amount,
        });
      });
    }
    if (gain.departSupplies) {
      _.forOwn(gain.departSupplies, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.DEPART_SUPPLY],
          id: parseInt(cmsIdStr, 10),
          uid: null,
          amt: amount,
        });
      });
    }
    if (gain.tradeGoods) {
      _.forOwn(gain.tradeGoods, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.TRADE_GOODS],
          id: parseInt(cmsIdStr, 10),
          uid: null,
          amt: amount,
        });
      });
    }
    if (gain.smuggleGoods) {
      _.forOwn(gain.smuggleGoods, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.SMUGGLE_GOODS],
          id: parseInt(cmsIdStr, 10),
          uid: null,
          amt: amount,
        });
      });
    }
    if (gain.mateEquips) {
      const mateEquipIds = _.cloneDeep(gain.mateEquipIds);
      _.forOwn(gain.mateEquips, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        const cmsId = parseInt(cmsIdStr, 10);
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.MATE_EQUIP],
          id: cmsId,
          uid: mateEquipIds[cmsId].pop(),
          amt: amount,
        });
      });
    }
    if (gain.ships) {
      const shipIds = _.cloneDeep(gain.shipIds);
      _.forOwn(gain.ships, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        const cmsId = parseInt(cmsIdStr, 10);
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.SHIP],
          id: cmsId,
          uid: shipIds[cmsId].pop(),
          amt: amount,
        });
      });
    }
    if (gain.mates) {
      for (const mateCmsId of gain.mates) {
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.MATE],
          id: mateCmsId,
          uid: null,
          amt: 1,
        });
      }
    }
    if (gain.sailors) {
      let sum = 0;
      _.forOwn(gain.sailors, (amount) => {
        sum += amount;
      });
      if (sum > 0) {
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.SAILOR],
          id: null,
          uid: null,
          amt: sum,
        });
      }
    }
    // TODO level, exp 전달해야 하는거같은데
    if (gain.shipBlueprints) {
      _.forOwn(gain.shipBlueprints, (elem, cmsIdStr) => {
        if (elem.level !== 1) {
          return;
        }
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.SHIP_BLUEPRINT],
          id: parseInt(cmsIdStr, 10),
          uid: null,
          amt: 1,
        });
      });
    }
    if (gain.soundPacks) {
      for (const cmsId of gain.soundPacks) {
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.SOUND_PACK],
          id: cmsId,
          uid: null,
          amt: 1,
        });
      }
    }
    if (gain.shipSlotItems) {
      const shipSlotItemIds = _.cloneDeep(gain.shipSlotItemIds);
      _.forOwn(gain.shipSlotItems, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        const cmsId = parseInt(cmsIdStr, 10);
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.SHIP_SLOT_ITEM],
          id: cmsId,
          uid: shipSlotItemIds[cmsId].pop(),
          amt: amount,
        });
      });
    }
    if (gain.questItems) {
      const questItemsIds = _.cloneDeep(gain.questItemsIds);
      _.forOwn(gain.questItems, (amount, cmsIdStr) => {
        if (amount <= 0) {
          return;
        }
        const cmsId = parseInt(cmsIdStr, 10);
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.QUEST_ITEM],
          id: cmsId,
          uid: questItemsIds[cmsId].pop(),
          amt: amount,
        });
      });
    }
    if (gain.taxFreePermits) {
      _.forOwn(gain.taxFreePermits, (count, cmsIdStr) => {
        const cmsId = parseInt(cmsIdStr, 10);
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.TAX_FREE_PERMIT],
          id: cmsId,
          uid: null,
          amt: count,
        });
      });
    }
    if (gain.admiralFame) {
      _.forOwn(gain.admiralFame, (value, jobTypeStr) => {
        const jobType = parseInt(jobTypeStr, 10);
        let rewardType: REWARD_TYPE;
        switch (jobType) {
          case cmsEx.JOB_TYPE.ADVENTURE:
            rewardType = REWARD_TYPE.ADVENTURE_FAME;
            break;
          case cmsEx.JOB_TYPE.BATTLE:
            rewardType = REWARD_TYPE.BATTLE_FAME;
            break;
          case cmsEx.JOB_TYPE.TRADE:
            rewardType = REWARD_TYPE.TRADE_FAME;
            break;
        }

        ret.push({
          type: REWARD_TYPE[rewardType],
          id: null,
          uid: null,
          amt: value,
        });
      });
    }
    if (gain.pets) {
      for (const petCmsId of gain.pets) {
        ret.push({
          type: REWARD_TYPE[REWARD_TYPE.PET],
          id: petCmsId,
          uid: null,
          amt: 1,
        });
      }
    }

    return ret;
  }
}
