"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const oceanUserFleet_1 = require("../oceanUserFleet");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const merror_1 = require("../../motiflib/merror");
const typedi_1 = __importDefault(require("typedi"));
const oceanZoneManager_1 = require("../oceanZoneManager");
const fleetManager_1 = require("../fleetManager");
const enum_1 = require("../../motiflib/model/lobby/enum");
const enum_2 = require("../../motiflib/model/ocean/enum");
// 클라와 계산차를 허용하는 unreal unit.
//
// 배의 최대속도는 21 노트.  (3241 uu / sec)
// 1초당 동기화 되기 때문에, 최대 이 거리만큼, 실제 유저의 위치와 차이가 날 수 있다.
const MarginOfErrorUu = 3241;
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
router.on(protocol_1.Protocol.LB2OC_REQ_ENTER, async (req, res) => {
    const { oceanZoneCmsId, channelId, userId, accountId, pubId, lobbyUrl, userFleetSyncDataForOcean, bDevIgnoreEncount, } = req;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const zoneManager = typedi_1.default.get(oceanZoneManager_1.OceanZoneManager);
    const userFleet = fleetManager.get(userId);
    if (userFleet) {
        mlog_1.default.warn('user already in ocean', { userId });
        const curZone = userFleet.getCurrentZone();
        if (curZone) {
            curZone.removeUserFleet(userId, enum_1.OCEAN_ZONE_LEAVE_REASON.ZONE_EXIST_WHEN_ENTERING_ZONE);
        }
    }
    const fleet = new oceanUserFleet_1.OceanUserFleet(userId, accountId, pubId, lobbyUrl, userFleetSyncDataForOcean);
    fleet.setIgnoringNpcEncount(bDevIgnoreEncount);
    return zoneManager.getOrCreate(channelId, oceanZoneCmsId).then((zone) => {
        if (!zone) {
            throw new merror_1.MError('failed-to-enter-ocean1', merror_1.MErrorCode.CANNOT_ENTER_OCEAN_ZONE);
        }
        if (!zone.addUserFleet(userId, fleet)) {
            throw new merror_1.MError('failed-to-enter-ocean2', merror_1.MErrorCode.CANNOT_ENTER_OCEAN_ZONE);
        }
        fleetManager.set(userId, fleet);
        if (req.isLoaded) {
            fleet.setLoaded();
        }
        const respPacket = new protocol_1.Protocol.OC2LB_RES_ENTER();
        respPacket.channelId = zone.channelId;
        respPacket.userId = userId;
        res.send(respPacket);
    });
});
router.on(protocol_1.Protocol.LB2OC_REQ_LEAVE, async (req, res) => {
    const { channelId, userId, reason } = req;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const zoneManager = typedi_1.default.get(oceanZoneManager_1.OceanZoneManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('invalid userId', merror_1.MErrorCode.INVALID_USER_ID, { userId });
    }
    const zone = zoneManager.get(channelId);
    if (!zone) {
        throw new merror_1.MError('invalid ocean zone channelId', merror_1.MErrorCode.INVALID_OCEAN_ZONE_CHANNEL_ID, {
            channelId,
        });
    }
    zone.removeUserFleet(userId, reason);
    fleetManager.delete(userId);
    const respPacket = new protocol_1.Protocol.OC2LB_RES_LEAVE();
    respPacket.userId = userId;
    res.send(respPacket);
});
router.on(protocol_1.Protocol.LB2OC_REQ_LOAD_COMPLETE, async (req, res) => {
    const { userId, bFromBattleOrDuel, karma, lastKarmaUpdateTimeUtc } = req;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('no agent in ocean zone', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setLoaded();
    fleet.getLocalNpcSpawnEntry().resume();
    fleet.setStateAndBroadCast(enum_2.OCEAN_USER_STATE.SAIL, bFromBattleOrDuel);
    const curKarma = fleet.getKarma();
    if (curKarma !== karma) {
        const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_CHANGE_KARMA();
        packet.userId = userId;
        packet.karma = karma;
        packet.lastKarmaUpdateTimeUtc = lastKarmaUpdateTimeUtc;
        fleet.sendBroadCastAdjacentGrids(packet);
    }
    const respPacket = new protocol_1.Protocol.OC2LB_RES_LOAD_COMPLETE();
    respPacket.userId = userId;
    res.send(respPacket);
    // mlog.debug('/enter resp');
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerEnterOrLeave.js.map