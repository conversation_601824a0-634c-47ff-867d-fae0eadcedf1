"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const resolveLocalDotJson5_1 = require("../motiflib/resolveLocalDotJson5");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const os = __importStar(require("os"));
const JSON5 = __importStar(require("json5"));
const uuid_1 = require("uuid");
let temporaryFiles = [];
function deleteAllTemporaryFiles() {
    for (const file of temporaryFiles) {
        try {
            fs.unlinkSync(file);
        }
        catch (e) {
            console.error(`deleteAllTemporaryFiles error: ${e.message}`);
        }
    }
    temporaryFiles = [];
}
let createDatabaseCreateJsonFilePath = '';
let migrateJsonFilePath = '';
function createDatabaseCreateJsonFile(mysqlConnectionConfig) {
    const json = {
        default: {
            host: mysqlConnectionConfig.host,
            port: mysqlConnectionConfig.port,
            user: mysqlConnectionConfig.user,
            password: mysqlConnectionConfig.password,
            driver: 'mysql',
            multipleStatements: true,
            supportBigNumbers: true,
            bigNumberStrings: true,
        }
    };
    const tmpConfigPath = path.join(os.tmpdir(), (0, uuid_1.v4)() + '.json');
    fs.writeFileSync(tmpConfigPath, JSON.stringify(json, null, 2));
    temporaryFiles.push(tmpConfigPath);
    return tmpConfigPath;
}
function createDatabaseMigrationJsonFile(migrationDatabaseConfig) {
    const json = {};
    json[migrationDatabaseConfig.auth.database] = {
        host: migrationDatabaseConfig.auth.host,
        port: migrationDatabaseConfig.auth.port,
        user: migrationDatabaseConfig.auth.user,
        password: migrationDatabaseConfig.auth.password,
        database: migrationDatabaseConfig.auth.database,
        driver: 'mysql',
        multipleStatements: true,
        supportBigNumbers: true,
        bigNumberStrings: true,
    };
    json[migrationDatabaseConfig.world.database] = {
        host: migrationDatabaseConfig.world.host,
        port: migrationDatabaseConfig.world.port,
        user: migrationDatabaseConfig.world.user,
        password: migrationDatabaseConfig.world.password,
        database: migrationDatabaseConfig.world.database,
        driver: 'mysql',
        multipleStatements: true,
        supportBigNumbers: true,
        bigNumberStrings: true,
    };
    for (const userShard of migrationDatabaseConfig.userShards) {
        json[userShard.database] = {
            host: userShard.host,
            port: userShard.port,
            user: userShard.user,
            password: userShard.password,
            database: userShard.database,
            driver: 'mysql',
            multipleStatements: true,
            supportBigNumbers: true,
            bigNumberStrings: true,
        };
    }
    const tmpConfigPath = path.join(os.tmpdir(), (0, uuid_1.v4)() + '.json');
    fs.writeFileSync(tmpConfigPath, JSON.stringify(json, null, 2));
    temporaryFiles.push(tmpConfigPath);
    return tmpConfigPath;
}
function makeRecreateDatabaseCmd(mysqlConnectionConfig) {
    const cmd = `./node_modules/.bin/db-migrate --config ${createDatabaseCreateJsonFilePath} --env default db:drop ${mysqlConnectionConfig.database}\n` +
        `./node_modules/.bin/db-migrate --config ${createDatabaseCreateJsonFilePath} --env default db:create ${mysqlConnectionConfig.database}\n`;
    return cmd;
}
function makeMigrateCmd(migrationDatabaseConfig) {
    let cmd = '';
    cmd += `./node_modules/.bin/db-migrate up:auth --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.auth.database}\n`;
    cmd += `./node_modules/.bin/db-migrate up:world --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.world.database}\n`;
    for (const userShard of migrationDatabaseConfig.userShards) {
        cmd += `./node_modules/.bin/db-migrate up:user --config ${migrateJsonFilePath} --env ${userShard.database}\n`;
    }
    cmd += `node -r source-map-support/register dist/spapply --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.auth.database} --input procedures/auth\n`;
    cmd += `node -r source-map-support/register dist/spapply --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.world.database} --input procedures/world\n`;
    for (const userShard of migrationDatabaseConfig.userShards) {
        cmd += `node -r source-map-support/register dist/spapply --config ${migrateJsonFilePath} --env ${userShard.database} --input procedures/user\n`;
    }
    return cmd;
}
//----------------------------------------------------------------------------
const overrideBinaryCode = process.argv[2];
const binaryCode = (overrideBinaryCode || (0, resolveLocalDotJson5_1.getOverridedBinaryCode)()).toLowerCase();
let localDotJson5Filename = 'local.json5';
if (binaryCode === 'cn') {
    localDotJson5Filename = 'local.cn.json5';
}
localDotJson5Filename = path.resolve(path.join(__dirname, '..', '..', 'service_layout', localDotJson5Filename));
if (!fs.existsSync(localDotJson5Filename)) {
    console.error(`localDotJson5Filename not exists: ${localDotJson5Filename}`);
    process.exit(1);
}
const localJson = JSON5.parse(fs.readFileSync(localDotJson5Filename, 'utf8'));
// auth db
const authDbHost = localJson.sharedConfig.mysqlAuthDb.host;
const authDbPort = localJson.sharedConfig.mysqlAuthDb.port;
const authDbUser = localJson.sharedConfig.mysqlAuthDb.user;
const authDbPassword = localJson.sharedConfig.mysqlAuthDb.password;
const authDbDatabase = localJson.sharedConfig.mysqlAuthDb.database;
if (!authDbHost || !authDbPort || !authDbUser || !authDbPassword || !authDbDatabase) {
    console.error(`authDbHost or authDbPort or authDbUser or authDbPassword or authDbDatabase not found in ${localDotJson5Filename}`);
    process.exit(1);
}
// 첫번째 월드만
const world = localJson.world.worlds[0];
if (!world) {
    console.error(`world not found in ${localDotJson5Filename}`);
    process.exit(1);
}
// world db
const worldDbHost = world.mysqlWorldDb.host;
const worldDbPort = world.mysqlWorldDb.port;
const worldDbUser = world.mysqlWorldDb.user;
const worldDbPassword = world.mysqlWorldDb.password;
const worldDbDatabase = world.mysqlWorldDb.database;
if (!worldDbHost || !worldDbPort || !worldDbUser || !worldDbPassword || !worldDbDatabase) {
    console.error(`worldDbHost or worldDbPort or worldDbUser or worldDbPassword or worldDbDatabase not found in ${localDotJson5Filename}`);
    process.exit(1);
}
// user db shards
const userDbShards = [];
for (const shard of world.mysqlUserDb.shards) {
    const sqlDefaultCfg = world.mysqlUserDb.sqlDefaultCfg;
    userDbShards.push({
        host: sqlDefaultCfg.host,
        port: sqlDefaultCfg.port,
        user: sqlDefaultCfg.user,
        password: sqlDefaultCfg.password,
        database: shard.sqlCfg.database,
    });
}
// redis 설정들 수집
const redisConfigs = new Set();
// sharedConfig의 Redis들
const sharedConfig = localJson.sharedConfig;
// pubsub redis들 (db 없음)
if (sharedConfig.authPubsubRedis) {
    redisConfigs.add(`${sharedConfig.authPubsubRedis.host}:${sharedConfig.authPubsubRedis.port}`);
}
if (sharedConfig.globalPubsubRedis) {
    redisConfigs.add(`${sharedConfig.globalPubsubRedis.host}:${sharedConfig.globalPubsubRedis.port}`);
}
if (sharedConfig.configPubsubRedis) {
    redisConfigs.add(`${sharedConfig.configPubsubRedis.host}:${sharedConfig.configPubsubRedis.port}`);
}
// db가 있는 Redis들
const redisWithDbConfigs = [
    'monitorRedis', 'orderRedis', 'authRedis', 'userCacheRedis',
    'globalMatchRedis', 'globalBattleLogRedis'
];
for (const configName of redisWithDbConfigs) {
    if (sharedConfig[configName] && sharedConfig[configName].redisCfg) {
        const cfg = sharedConfig[configName].redisCfg;
        redisConfigs.add(`${cfg.host}:${cfg.port}:${cfg.db || 0}`);
    }
}
// world의 Redis들
if (world.worldPubsubRedis) {
    redisConfigs.add(`${world.worldPubsubRedis.host}:${world.worldPubsubRedis.port}`);
}
if (world.guildPubsubRedis) {
    redisConfigs.add(`${world.guildPubsubRedis.host}:${world.guildPubsubRedis.port}`);
}
const worldRedisWithDbConfigs = [
    'townRedis', 'nationRedis', 'collectorRedis', 'sailRedis', 'guildRedis',
    'arenaRedis', 'raidRedis', 'rankingRedis', 'userRedis', 'townLbRedis',
    'oceanLbRedis', 'blindBidRedis'
];
for (const configName of worldRedisWithDbConfigs) {
    if (world[configName] && world[configName].redisCfg) {
        const cfg = world[configName].redisCfg;
        redisConfigs.add(`${cfg.host}:${cfg.port}:${cfg.db || 0}`);
    }
}
// auctionRedis는 shards 구조
if (world.auctionRedis && world.auctionRedis.shards) {
    for (const shard of world.auctionRedis.shards) {
        if (shard.redisCfg) {
            redisConfigs.add(`${shard.redisCfg.host}:${shard.redisCfg.port}:${shard.redisCfg.db || 0}`);
        }
    }
}
// configd의 configRedis
if (localJson.configd && localJson.configd.common && localJson.configd.common.configRedis) {
    const cfg = localJson.configd.common.configRedis.redisCfg;
    redisConfigs.add(`${cfg.host}:${cfg.port}:${cfg.db || 0}`);
}
// authd의 battleLogRedis
if (localJson.authd && localJson.authd.common && localJson.authd.common.battleLogRedis) {
    const cfg = localJson.authd.common.battleLogRedis.redisCfg;
    redisConfigs.add(`${cfg.host}:${cfg.port}:${cfg.db || 0}`);
}
createDatabaseCreateJsonFilePath = createDatabaseCreateJsonFile({
    host: authDbHost,
    port: authDbPort,
    user: authDbUser,
    password: authDbPassword,
    database: authDbDatabase,
});
migrateJsonFilePath = createDatabaseMigrationJsonFile({
    auth: {
        host: authDbHost,
        port: authDbPort,
        user: authDbUser,
        password: authDbPassword,
        database: authDbDatabase,
    },
    world: {
        host: worldDbHost,
        port: worldDbPort,
        user: worldDbUser,
        password: worldDbPassword,
        database: worldDbDatabase,
    },
    userShards: userDbShards,
});
let cmd = '';
// auth db
cmd += makeRecreateDatabaseCmd({
    host: authDbHost,
    port: authDbPort,
    user: authDbUser,
    password: authDbPassword,
    database: authDbDatabase,
});
// world db
cmd += makeRecreateDatabaseCmd({
    host: worldDbHost,
    port: worldDbPort,
    user: worldDbUser,
    password: worldDbPassword,
    database: worldDbDatabase,
});
// user db list
for (const userDb of userDbShards) {
    cmd += makeRecreateDatabaseCmd(userDb);
}
// migration
cmd += makeMigrateCmd({
    auth: {
        host: authDbHost,
        port: authDbPort,
        user: authDbUser,
        password: authDbPassword,
        database: authDbDatabase,
    },
    world: {
        host: worldDbHost,
        port: worldDbPort,
        user: worldDbUser,
        password: worldDbPassword,
        database: worldDbDatabase,
    },
    userShards: userDbShards,
});
// redis - 모든 Redis 인스턴스에 대해 flushdb 실행
for (const redisConfig of redisConfigs) {
    const parts = redisConfig.split(':');
    const host = parts[0];
    const port = parts[1];
    const db = parts[2];
    if (db !== undefined) {
        // db가 지정된 경우 해당 db에 대해 flushdb
        cmd += `redis-cli -h ${host} -p ${port} -n ${db} flushdb\n`;
    }
    else {
        // pubsub redis의 경우 db 0에 대해 flushdb
        // cmd += `redis-cli -h ${host} -p ${port} -n 0 flushdb\n`;
    }
}
console.log(cmd);
// execute!
// const output = childProcess.execSync(cmd, {
//   env: process.env,
//   maxBuffer: 10000 * 2014,
// });
// console.log(output.toString());
// delete temporary files
deleteAllTemporaryFiles();
//# sourceMappingURL=resetAll.js.map