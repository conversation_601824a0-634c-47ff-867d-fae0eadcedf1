"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const childProcess = __importStar(require("child_process"));
const resolveLocalDotJson5_1 = require("../motiflib/resolveLocalDotJson5");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const os = __importStar(require("os"));
const JSON5 = __importStar(require("json5"));
const uuid_1 = require("uuid");
let temporaryFiles = [];
function deleteAllTemporaryFiles() {
    for (const file of temporaryFiles) {
        try {
            fs.unlinkSync(file);
        }
        catch (e) {
            console.error(`deleteAllTemporaryFiles error: ${e.message}`);
        }
    }
    temporaryFiles = [];
}
let createDatabaseCreateJsonFilePath = '';
let migrateJsonFilePath = '';
function createDatabaseCreateJsonFile(mysqlConnectionConfig) {
    const json = {
        default: {
            host: mysqlConnectionConfig.host,
            port: mysqlConnectionConfig.port,
            user: mysqlConnectionConfig.user,
            password: mysqlConnectionConfig.password,
            driver: 'mysql',
            multipleStatements: true,
            supportBigNumbers: true,
            bigNumberStrings: true,
        }
    };
    const tmpConfigPath = path.join(os.tmpdir(), (0, uuid_1.v4)() + '.json');
    fs.writeFileSync(tmpConfigPath, JSON.stringify(json, null, 2));
    temporaryFiles.push(tmpConfigPath);
    return tmpConfigPath;
}
function createDatabaseMigrationJsonFile(migrationDatabaseConfig) {
    const json = {};
    json[migrationDatabaseConfig.auth.database] = {
        host: migrationDatabaseConfig.auth.host,
        port: migrationDatabaseConfig.auth.port,
        user: migrationDatabaseConfig.auth.user,
        password: migrationDatabaseConfig.auth.password,
        database: migrationDatabaseConfig.auth.database,
        driver: 'mysql',
        multipleStatements: true,
        supportBigNumbers: true,
        bigNumberStrings: true,
    };
    json[migrationDatabaseConfig.world.database] = {
        host: migrationDatabaseConfig.world.host,
        port: migrationDatabaseConfig.world.port,
        user: migrationDatabaseConfig.world.user,
        password: migrationDatabaseConfig.world.password,
        database: migrationDatabaseConfig.world.database,
        driver: 'mysql',
        multipleStatements: true,
        supportBigNumbers: true,
        bigNumberStrings: true,
    };
    for (const userShard of migrationDatabaseConfig.userShards) {
        json[userShard.database] = {
            host: userShard.host,
            port: userShard.port,
            user: userShard.user,
            password: userShard.password,
            database: userShard.database,
            driver: 'mysql',
            multipleStatements: true,
            supportBigNumbers: true,
            bigNumberStrings: true,
        };
    }
    const tmpConfigPath = path.join(os.tmpdir(), (0, uuid_1.v4)() + '.json');
    fs.writeFileSync(tmpConfigPath, JSON.stringify(json, null, 2));
    temporaryFiles.push(tmpConfigPath);
    return tmpConfigPath;
}
function makeRecreateDatabaseCmd(mysqlConnectionConfig) {
    const cmd = `./node_modules/.bin/db-migrate --config ${createDatabaseCreateJsonFilePath} --env default db:drop ${mysqlConnectionConfig.database}\n` +
        `./node_modules/.bin/db-migrate --config ${createDatabaseCreateJsonFilePath} --env default db:create ${mysqlConnectionConfig.database}\n`;
    return cmd;
}
function makeMigrateCmd(migrationDatabaseConfig) {
    let cmd = '';
    cmd += `./node_modules/.bin/db-migrate up:auth --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.auth.database}\n`;
    cmd += `./node_modules/.bin/db-migrate up:world --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.world.database}\n`;
    for (const userShard of migrationDatabaseConfig.userShards) {
        cmd += `./node_modules/.bin/db-migrate up:user --config ${migrateJsonFilePath} --env ${userShard.database}\n`;
    }
    cmd += `node -r source-map-support/register dist/spapply --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.auth.database} --input procedures/auth\n`;
    cmd += `node -r source-map-support/register dist/spapply --config ${migrateJsonFilePath} --env ${migrationDatabaseConfig.world.database} --input procedures/world\n`;
    for (const userShard of migrationDatabaseConfig.userShards) {
        cmd += `node -r source-map-support/register dist/spapply --config ${migrateJsonFilePath} --env ${userShard.database} --input procedures/user\n`;
    }
    return cmd;
}
//----------------------------------------------------------------------------
const overrideBinaryCode = process.argv[2];
const binaryCode = (overrideBinaryCode || (0, resolveLocalDotJson5_1.getOverridedBinaryCode)()).toLowerCase();
let localDotJson5Filename = 'local.json5';
if (binaryCode === 'cn') {
    localDotJson5Filename = 'local.cn.json5';
}
localDotJson5Filename = path.resolve(path.join(__dirname, '..', '..', 'service_layout', localDotJson5Filename));
if (!fs.existsSync(localDotJson5Filename)) {
    console.error(`localDotJson5Filename not exists: ${localDotJson5Filename}`);
    process.exit(1);
}
const localJson = JSON5.parse(fs.readFileSync(localDotJson5Filename, 'utf8'));
// auth db
const authDbHost = localJson.sharedConfig.mysqlAuthDb.host;
const authDbPort = localJson.sharedConfig.mysqlAuthDb.port;
const authDbUser = localJson.sharedConfig.mysqlAuthDb.user;
const authDbPassword = localJson.sharedConfig.mysqlAuthDb.password;
const authDbDatabase = localJson.sharedConfig.mysqlAuthDb.database;
if (!authDbHost || !authDbPort || !authDbUser || !authDbPassword || !authDbDatabase) {
    console.error(`authDbHost or authDbPort or authDbUser or authDbPassword or authDbDatabase not found in ${localDotJson5Filename}`);
    process.exit(1);
}
// 첫번째 월드만
const world = localJson.world.worlds[0];
if (!world) {
    console.error(`world not found in ${localDotJson5Filename}`);
    process.exit(1);
}
// world db
const worldDbHost = world.mysqlWorldDb.host;
const worldDbPort = world.mysqlWorldDb.port;
const worldDbUser = world.mysqlWorldDb.user;
const worldDbPassword = world.mysqlWorldDb.password;
const worldDbDatabase = world.mysqlWorldDb.database;
if (!worldDbHost || !worldDbPort || !worldDbUser || !worldDbPassword || !worldDbDatabase) {
    console.error(`worldDbHost or worldDbPort or worldDbUser or worldDbPassword or worldDbDatabase not found in ${localDotJson5Filename}`);
    process.exit(1);
}
// user db shards
const userDbShards = [];
for (const shard of world.mysqlUserDb.shards) {
    const sqlDefaultCfg = world.mysqlUserDb.sqlDefaultCfg;
    userDbShards.push({
        host: sqlDefaultCfg.host,
        port: sqlDefaultCfg.port,
        user: sqlDefaultCfg.user,
        password: sqlDefaultCfg.password,
        database: shard.sqlCfg.database,
    });
}
// redis
const authRedis = localJson.sharedConfig.authRedis;
const redisHost = authRedis.redisCfg.host;
const redisPort = authRedis.redisCfg.port;
if (!redisHost || !redisPort) {
    console.error(`redisHost or redisPort not found in ${localDotJson5Filename}`);
    process.exit(1);
}
createDatabaseCreateJsonFilePath = createDatabaseCreateJsonFile({
    host: authDbHost,
    port: authDbPort,
    user: authDbUser,
    password: authDbPassword,
    database: authDbDatabase,
});
migrateJsonFilePath = createDatabaseMigrationJsonFile({
    auth: {
        host: authDbHost,
        port: authDbPort,
        user: authDbUser,
        password: authDbPassword,
        database: authDbDatabase,
    },
    world: {
        host: worldDbHost,
        port: worldDbPort,
        user: worldDbUser,
        password: worldDbPassword,
        database: worldDbDatabase,
    },
    userShards: userDbShards,
});
let cmd = '';
// auth db
cmd += makeRecreateDatabaseCmd({
    host: authDbHost,
    port: authDbPort,
    user: authDbUser,
    password: authDbPassword,
    database: authDbDatabase,
});
// world db
cmd += makeRecreateDatabaseCmd({
    host: worldDbHost,
    port: worldDbPort,
    user: worldDbUser,
    password: worldDbPassword,
    database: worldDbDatabase,
});
// user db list
for (const userDb of userDbShards) {
    cmd += makeRecreateDatabaseCmd(userDb);
}
// migration
cmd += makeMigrateCmd({
    auth: {
        host: authDbHost,
        port: authDbPort,
        user: authDbUser,
        password: authDbPassword,
        database: authDbDatabase,
    },
    world: {
        host: worldDbHost,
        port: worldDbPort,
        user: worldDbUser,
        password: worldDbPassword,
        database: worldDbDatabase,
    },
    userShards: userDbShards,
});
// redis
cmd += `redis-cli -h ${redisHost} -p ${redisPort} flushall\n`;
console.log(cmd);
// execute!
const output = childProcess.execSync(cmd, {
    env: process.env,
    maxBuffer: 10000 * 2014,
});
console.log(output.toString());
// delete temporary files
deleteAllTemporaryFiles();
//# sourceMappingURL=resetAll.js.map