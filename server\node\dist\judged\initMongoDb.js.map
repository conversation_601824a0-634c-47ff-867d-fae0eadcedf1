{"version": 3, "file": "initMongoDb.js", "sourceRoot": "", "sources": ["../../src/judged/initMongoDb.ts"], "names": [], "mappings": ";AAAA,+EAA+E;AAC/E,4DAA4D;AAC5D,+EAA+E;;;;;AAK/E,4DAAoC;AACpC,8DAAsC;AACtC,8DAAsC;AACtC,wEAAqE;AAGrE,6EAA6E;AAC7E,KAAK,UAAU,iBAAiB,CAAC,WAA8B,EAAE,UAA2B;IAC1F,mDAAmD;IAEnD,OAAO,WAAW;SACf,QAAQ,CAAC,UAAU,CAAC;SACpB,IAAI,CAAC,CAAC,KAA0B,EAAE,EAAE;QACnC,OAAO,KAAK,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YACxC,OAAO,KAAK,CAAC,aAAa,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QACb,cAAI,CAAC,IAAI,CAAC,yEAAyE,EAAE;YACnF,UAAU;YACV,GAAG;SACJ,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,6EAA6E;AAC7E,KAAK,UAAU,IAAI;IACjB,iBAAiB;IACjB,eAAK,CAAC,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC;IAE3B,MAAM,eAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IAE5B,MAAM,UAAU,GAAG,eAAK,CAAC,gBAAgB,CAAC;IAC1C,UAAU,CAAC,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;IAE7C,MAAM,SAAS,GAAsB,IAAI,qCAAiB,EAAE,CAAC;IAC7D,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAE3B,MAAM,iBAAiB,CAAC,SAAS,yBAAiC,CAAC;IACnE,MAAM,iBAAiB,CAAC,SAAS,yBAAiC,CAAC;IACnE,MAAM,iBAAiB,CAAC,SAAS,uBAA+B,CAAC;IACjE,MAAM,iBAAiB,CAAC,SAAS,wBAAgC,CAAC;IAClE,MAAM,iBAAiB,CAAC,SAAS,gCAAwC,CAAC;IAC1E,MAAM,iBAAiB,CAAC,SAAS,8BAAsC,CAAC;IACxE,MAAM,iBAAiB,CAAC,SAAS,6BAAqC,CAAC;AACzE,CAAC;AAED,6EAA6E;AAC7E,IAAI,EAAE;KACH,IAAI,CAAC,GAAG,EAAE;IACT,cAAI,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IAC1D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;KACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;IACb,cAAI,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;IAC1D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}