// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import _ from 'lodash';

import { withTxn } from '../mysqlUtil';
import puMateCreate from '../sp/puMateCreate';
import puShipCreate from '../sp/puShipCreate';
import puShipSlotUpdateMateCmsIdAndIsLocked from '../sp/puShipSlotUpdateMateCmsIdAndIsLocked';
import * as cmsEx from '../../cms/ex';
import { ShipNub } from '../../lobbyd/ship';
import puPointUpdate from '../sp/puPointUpdate';
import puShipUpdateLastId from '../sp/puShipUpdateLastId';
import puQuestContextCreate from '../sp/puQuestContextCreate';
import puDirectMailCreate from '../sp/puDirectMailCreate';
import puDirectMailLastIdUpdate from '../sp/puDirectMailLastIdUpdate';
import puStateUpdateGameState from '../sp/puStateUpdateGameState';
import puInsuranceUpdateCmsId from '../sp/puInsuranceUpdateCmsId';
import { MError, MErrorCode } from '../../motiflib/merror';
import puRevealedWorldMapTilesAdd from '../sp/puRevealedWorldMapTilesAdd';
import puShipSailPatternUpdate from '../sp/puShipSailPatternUpdate';
import puShipSailCrestUpdate from '../sp/puShipSailCrestUpdate';
import puShipSailPatternColorUpdate from '../sp/puShipSailPatternColorUpdate';
import puShipBody1ColorUpdate from '../sp/puShipBody1ColorUpdate';
import puShipBody2ColorUpdate from '../sp/puShipBody2ColorUpdate';
import { PointChange } from '../../lobbyd/userPoints';
import puSoftDataUpdateArrivalTownCmsId from '../sp/puSoftDataUpdateArrivalTownCmsId';
import puMateEquipmentColorUpdate from '../sp/puMateEquipmentColorUpdate';
import { MateEquipmentNub } from '../../motiflib/model/lobby';
import puMateEquipmentUpdateLastId from '../sp/puMateEquipmentUpdateLastId';
import puUserUpdateFirstMateCmsId from '../sp/puUserUpdateFirstMateCmsId';
import puDiscoveryAdd from '../sp/puDiscoveryAdd';
import puMateEquipmentAndDyeCreate from '../sp/puMateEquipmentAndDyeCreate';
import { EnergyChange } from '../../lobbyd/userEnergy';
import puUserUpdateEnergy from '../sp/puUserUpdateEnergy';
import puShipCustomizingUpdateSailPatternCmsId from '../sp/puShipCustomizingUpdateSailPatternCmsId';
import puStateUpdateGameStateLastGameState from '../sp/puStateUpdateGameStateLastGameState';
import { GameStateChange } from '../../lobbyd/userState';
import puStateUpdateLastGameState from '../sp/puStateUpdateLastGameState';
import puBattleFormationCreate from '../sp/puBattleFormationCreate';
import puFleetUpdateBattleFormationCmsId from '../sp/puFleetUpdateBattleFormationCmsId';
import ShipBlueprint from '../../lobbyd/shipBlueprint';
import puShipBlueprintUpdateLevel from '../sp/puShipBlueprintUpdateLevel';
import puRevealedRegionsAdd from '../sp/puRevealedRegionsAdd';
import puArenaUpdateTicketCount from '../sp/puArenaUpdateTicketCount';
import { BattleFormation } from '../../lobbyd/userBattleFormations';
import puBattleFormationUpdate from '../sp/puBattleFormationUpdate';
import { MailCreatingParams } from '../../motiflib/mailBuilder';
import { ManufacturePointChange } from '../../lobbyd/userManufacture';
import puUserUpdateManufacturePoint from '../sp/puUserUpdateManufacturePoint';

function queryImpl(
  connection: PoolConnection,
  userId: number,
  curTimeUtc: number,
  mateCmsId: number,
  defaultShipBlueprints: ShipBlueprint[],
  ships: ShipNub[],
  pointChanges: PointChange[],
  energyChange: EnergyChange,
  manufacturePointChange: ManufacturePointChange,
  defaultMails: MailCreatingParams[],
  quests: { questCmsId: number; r0: number }[],
  arrivalTownCmsId: number,
  defaultInsuranceCmsId: number,
  royaltitle: number,
  userLevel: number,
  worldMapTiles: number[],
  regions: number[],
  defaultShipSailPatterns: number[],
  defaultShipSailCrests: { [offset: number]: number },
  defaultShipSailPatternColors: { [offset: number]: number },
  defaultShipBody1Colors: { [offset: number]: number },
  defaultShipBody2Colors: { [offset: number]: number },
  defaultMateEquipmentColors: { [offset: number]: number },
  defaultEquipSailPatternCmsId: number,
  mateEquipsToAdd: MateEquipmentNub[],
  defaultDiscoveries: number[],
  gameStateChange: GameStateChange,
  battleFormation: BattleFormation,
  fleetIndex: number,
  nationCmsId: number,
  defaultArenaTickets: number
) {
  return puUserUpdateFirstMateCmsId(connection, userId, mateCmsId, nationCmsId)
    .then(() => {
      if (energyChange) {
        return puUserUpdateEnergy(
          connection,
          userId,
          energyChange.energy,
          energyChange.lastUpdateTimeUtc
        );
      }
      return null;
    })
    .then(() => {
      if (manufacturePointChange) {
        return puUserUpdateManufacturePoint(
          connection,
          userId,
          manufacturePointChange.point,
          manufacturePointChange.lastUpdatePointTimeUtc
        );
      }
      return null;
    })
    .then(() => {
      if (gameStateChange) {
        if (
          gameStateChange.gameState !== undefined &&
          gameStateChange.lastGameState !== undefined
        ) {
          return puStateUpdateGameStateLastGameState(
            connection,
            userId,
            gameStateChange.gameState,
            gameStateChange.lastGameState
          );
        } else if (gameStateChange.gameState !== undefined) {
          return puStateUpdateGameState(connection, userId, gameStateChange.gameState);
        } else if (gameStateChange.lastGameState !== undefined) {
          return puStateUpdateLastGameState(connection, userId, gameStateChange.lastGameState);
        }
      }

      return null;
    })
    .then(() => {
      return puSoftDataUpdateArrivalTownCmsId(connection, userId, arrivalTownCmsId);
    })
    .then(() => {
      if (pointChanges) {
        const promises = [];
        for (const point of pointChanges) {
          promises.push(puPointUpdate(connection, userId, point));
        }
        return Promise.all(promises);
      }
    })
    .then(() => {
      if (defaultShipBlueprints && defaultShipBlueprints.length > 0) {
        const promises = [];
        for (const blueprint of defaultShipBlueprints) {
          promises.push(
            puShipBlueprintUpdateLevel(
              connection,
              userId,
              blueprint.cmsId,
              blueprint.level,
              blueprint.exp
            )
          );
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (quests) {
        const promises = [];
        for (const quest of quests) {
          promises.push(
            puQuestContextCreate(
              connection,
              userId,
              quest.questCmsId,
              curTimeUtc,
              quest.r0,
              userLevel,
              null,
              null,
              null,
              0
            )
          );
        }
        return Promise.all(promises);
      }

      return [];
    })
    .then(() => {
      if (defaultInsuranceCmsId !== undefined) {
        return puInsuranceUpdateCmsId(connection, userId, defaultInsuranceCmsId);
      }

      return null;
    })
    .then(() => {
      return puMateCreate(connection, userId, mateCmsId, null, royaltitle);
    })
    .then(() => {
      if (mateEquipsToAdd) {
        return puMateEquipmentUpdateLastId(
          connection,
          userId,
          mateEquipsToAdd[mateEquipsToAdd.length - 1].id
        );
      }
      return null;
    })
    .then(() => {
      if (mateEquipsToAdd) {
        const promises = [];
        for (const elem of mateEquipsToAdd) {
          promises.push(
            puMateEquipmentAndDyeCreate(
              connection,
              userId,
              elem.id,
              elem.cmsId,
              elem.dye1,
              elem.dye2,
              elem.dye3,
              elem.dye4,
              elem.dye5,
              elem.dye6,
              elem.expireTimeUtc,
              elem.enchantLv,
              elem.equippedMateCmsId
            )
          );
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      const promises = [];
      _.forOwn(defaultMateEquipmentColors, (idxField, offsetStr) => {
        promises.push(
          puMateEquipmentColorUpdate(connection, userId, parseInt(offsetStr, 10), idxField)
        );
      });
      return Promise.all(promises);
    })
    .then(() => {
      return puBattleFormationUpdate(connection, userId, battleFormation);
    })
    .then(() => {
      return puFleetUpdateBattleFormationCmsId(
        connection,
        userId,
        fleetIndex,
        battleFormation.cmsId
      );
    })
    .then(() => {
      if (ships && ships.length > 0) {
        return puShipUpdateLastId(connection, userId, ships[ships.length - 1].id);
      }
      return null;
    })
    .then(() => {
      if (ships && ships.length > 0) {
        const promises = [];
        for (let i = 0; i < ships.length; i++) {
          promises.push(puShipCreate(connection, userId, ships[i]));
        }
        return Promise.all(promises);
      }

      return [];
    })
    .then(() => {
      if (ships && ships.length > 0) {
        const promises = [];
        for (let i = 0; i < ships.length; i++) {
          promises.push(
            puShipSlotUpdateMateCmsIdAndIsLocked(
              connection,
              userId,
              ships[i].id,
              ships[i].slots[cmsEx.ShipSlotIndexCaptainRoom]
            )
          );
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      return puShipCustomizingUpdateSailPatternCmsId(
        connection,
        userId,
        defaultEquipSailPatternCmsId
      );
    })
    .then(() => {
      const promises = [];
      for (const cmsId of defaultShipSailPatterns) {
        promises.push(puShipSailPatternUpdate(connection, userId, cmsId, null, null, null));
      }
      return Promise.all(promises);
    })
    .then(() => {
      const promises = [];
      _.forOwn(defaultShipSailCrests, (idxField, offsetStr) => {
        promises.push(puShipSailCrestUpdate(connection, userId, parseInt(offsetStr, 10), idxField));
      });
      return Promise.all(promises);
    })
    .then(() => {
      const promises = [];
      _.forOwn(defaultShipSailPatternColors, (idxField, offsetStr) => {
        promises.push(
          puShipSailPatternColorUpdate(connection, userId, parseInt(offsetStr, 10), idxField)
        );
      });
      return Promise.all(promises);
    })
    .then(() => {
      const promises = [];
      _.forOwn(defaultShipBody1Colors, (idxField, offsetStr) => {
        promises.push(
          puShipBody1ColorUpdate(connection, userId, parseInt(offsetStr, 10), idxField)
        );
      });
      return Promise.all(promises);
    })
    .then(() => {
      const promises = [];
      _.forOwn(defaultShipBody2Colors, (idxField, offsetStr) => {
        promises.push(
          puShipBody2ColorUpdate(connection, userId, parseInt(offsetStr, 10), idxField)
        );
      });
      return Promise.all(promises);
    })
    .then(() => {
      if (defaultDiscoveries) {
        const promises = [];
        for (const elem of defaultDiscoveries) {
          promises.push(puDiscoveryAdd(connection, userId, Math.floor(elem / 32), elem % 32));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (worldMapTiles) {
        const promises = [];
        for (const id of worldMapTiles) {
          promises.push(
            puRevealedWorldMapTilesAdd(connection, userId, Math.floor(id / 32), id % 32)
          );
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (regions) {
        const promises = [];
        for (const id of regions) {
          promises.push(puRevealedRegionsAdd(connection, userId, Math.floor(id / 32), id % 32));
        }
        return Promise.all(promises);
      }
      return [];
    })
    .then(() => {
      if (defaultMails && defaultMails.length > 0) {
        return puDirectMailLastIdUpdate(
          connection,
          userId,
          defaultMails[defaultMails.length - 1].id
        );
      }

      return null;
    })
    .then(() => {
      if (defaultMails && defaultMails.length > 0) {
        const promises = [];
        for (const mail of defaultMails) {
          promises.push(puDirectMailCreate(connection, userId, mail));
        }
        return Promise.all(promises);
      }

      return [];
    })
    .then(() => {
      if (defaultArenaTickets) {
        return puArenaUpdateTicketCount(connection, userId, defaultArenaTickets, curTimeUtc);
      }
    })
    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.CREATE_FIRST_MATE_TXN_ERROR);
      }
    });
}

export default function tuCreateFirstMate(
  dbConnPool: Pool,
  userId: number,
  curTimeUtc: number,
  mateCmsId: number,
  defaultShipBlueprints: ShipBlueprint[],
  ships: ShipNub[],
  pointChanges: PointChange[],
  energyChange: EnergyChange,
  manufacturePointChange: ManufacturePointChange,
  defaultMails: MailCreatingParams[],
  quests: { questCmsId: number; r0: number }[],
  arrivalTownCmsId: number,
  defaultInsuranceCmsId: number,
  royaltitle: number,
  userLevel: number,
  worldMapTiles: number[],
  regions: number[],
  defaultShipSailPatterns: number[],
  defaultShipSailCrests: { [offset: number]: number },
  defaultShipSailPatternColors: { [offset: number]: number },
  defaultShipBody1Colors: { [offset: number]: number },
  defaultShipBody2Colors: { [offset: number]: number },
  defaultMateEquipmentColors: { [offset: number]: number },
  defaultEquipSailPatternCmsId: number,
  mateEquipsToAdd: MateEquipmentNub[],
  defaultDiscoveries: number[],
  gameStateChange: GameStateChange,
  battleFormation: BattleFormation,
  fleetIndex: number,
  nationCmsId: number,
  defaultArenaTickets: number
) {
  return withTxn(dbConnPool, __filename, (connection: PoolConnection) => {
    return queryImpl(
      connection,
      userId,
      curTimeUtc,
      mateCmsId,
      defaultShipBlueprints,
      ships,
      pointChanges,
      energyChange,
      manufacturePointChange,
      defaultMails,
      quests,
      arrivalTownCmsId,
      defaultInsuranceCmsId,
      royaltitle,
      userLevel,
      worldMapTiles,
      regions,
      defaultShipSailPatterns,
      defaultShipSailCrests,
      defaultShipSailPatternColors,
      defaultShipBody1Colors,
      defaultShipBody2Colors,
      defaultMateEquipmentColors,
      defaultEquipSailPatternCmsId,
      mateEquipsToAdd,
      defaultDiscoveries,
      gameStateChange,
      battleFormation,
      fleetIndex,
      nationCmsId,
      defaultArenaTickets
    );
  });
}
