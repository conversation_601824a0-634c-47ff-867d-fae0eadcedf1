"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JudgeUtil = void 0;
const child_process = __importStar(require("child_process"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
var JudgeUtil;
(function (JudgeUtil) {
    // ----------------------------------------------------------------------------------------------
    // Retrieve "revision" from battle param.
    // ----------------------------------------------------------------------------------------------
    function extractBpRevision(bp) {
        let revision = bp.patchRevision;
        if (!revision || revision === 'Unknown') {
            revision = bp.revision;
        }
        if (!revision) {
            return undefined;
        }
        revision = revision.substr(0, 10); // short hash
        return revision;
    }
    JudgeUtil.extractBpRevision = extractBpRevision;
    // ------------------------------------------------------------------------------------------------
    // spawnExec
    // https://wiki.line.games/pages/viewpage.action?pageId=73883712
    // 비동기적으로 Sub Process 를 실행 시킴.
    // ------------------------------------------------------------------------------------------------
    function spawnExec(path, cmd) {
        //console.log(`exec [${cmd}]...`);
        return new Promise((resolve, reject) => {
            const child = child_process.exec(cmd, {
                cwd: path,
            });
            // subProcess 와의 통신간에 buffer 크기에 따라 'data' 이벤트가 여러번 호출될수 있음.
            let outPutBuff = '';
            child.stdout.on('data', (buffer) => {
                if (!outPutBuff) {
                    outPutBuff = buffer.toString();
                }
                else {
                    outPutBuff += buffer.toString();
                }
                // console.log(buffer.toString());
            });
            child.stderr.on('data', (buffer) => {
                console.log(buffer.toString());
            });
            child.on('error', (error) => {
                reject(error);
            });
            child.on('close', (exitCode) => {
                const procExitInfo = {
                    output: outPutBuff,
                    exitCode,
                };
                resolve(procExitInfo);
            });
        });
    }
    JudgeUtil.spawnExec = spawnExec;
    // ------------------------------------------------------------------------------------------------
    // extractVbmLogInfo
    // vbmLog 와 vbmStackTrace 추출.
    // ------------------------------------------------------------------------------------------------
    function extractVbmLogInfo(outPutBuff, vbmLogInfo) {
        const outputLines = outPutBuff.split('\n');
        let outputLinesIndex = 0;
        const outputLogLines = [];
        let bOutputLog = false;
        const outputErrorLines = [];
        let bOutputError = false;
        while (outputLinesIndex < outputLines.length) {
            let searchIndex = outputLines[outputLinesIndex].indexOf('[LBattleValidationMain] Start');
            if (searchIndex != -1) {
                bOutputLog = true;
            }
            if (!bOutputLog) {
                ++outputLinesIndex;
                continue;
            }
            outputLogLines.push(outputLines[outputLinesIndex]);
            searchIndex = outputLines[outputLinesIndex].indexOf('stack traceback');
            if (searchIndex != -1) {
                bOutputError = true;
            }
            if (bOutputError) {
                outputErrorLines.push(outputLines[outputLinesIndex]);
            }
            ++outputLinesIndex;
        }
        // 특정 기호 문자 제거.
        if (outputLogLines.length > 0) {
            vbmLogInfo.vbmLog = outputLogLines.toString().replace(/\t/g, '');
        }
        if (outputErrorLines.length > 0) {
            vbmLogInfo.vbmStackTrace = outputErrorLines.toString().replace(/\t/g, '');
        }
    }
    JudgeUtil.extractVbmLogInfo = extractVbmLogInfo;
    // --------------------------------------------------------------------------
    // 검증 결과 유죄 판결을 받은 케이스 저장.
    // --------------------------------------------------------------------------
    async function saveConvicted(mongoDbConn, suspectDoc, // mongoose.Document
    vbmLogInfo, exitCode) {
        return mongoDbConn
            .getModel(2 /* BATTLE_CONVICT */)
            .then((model) => {
            const txnLog = JSON.parse(suspectDoc.actionLogs);
            const battleParam = JSON.parse(txnLog[0]).battleParam;
            const convictDoc = {
                countryCode: suspectDoc.countryCode,
                worldId: suspectDoc.worldId,
                userId: suspectDoc.userId,
                battleId: suspectDoc.battleId,
                battleParam: battleParam,
                actionLogs: suspectDoc.actionLogs,
                battleResult: suspectDoc.battleResult,
                reasonForSuspicion: suspectDoc.reasonForSuspicion,
                exitCode,
                vbmLog: vbmLogInfo.vbmLog,
                vbmStackTrace: vbmLogInfo.vbmStackTrace,
            };
            // 비동기로 insert.
            model.create(convictDoc).catch((err) => {
                mlog_1.default.error('Failed to create convict doc.', {
                    countryCode: suspectDoc.countryCode,
                    worldId: suspectDoc.worldId,
                    userId: suspectDoc.userId,
                    battleId: suspectDoc.battleId,
                    exitCode,
                    name: err.name,
                    message: err.message,
                });
            });
        });
    }
    JudgeUtil.saveConvicted = saveConvicted;
    // --------------------------------------------------------------------------
    // 검증 실패한 doc 저장. (장애, 에러 등)
    // --------------------------------------------------------------------------
    async function saveGhost(mongoDbConn, suspectDoc, ghostReason) {
        return mongoDbConn.getModel(3 /* BATTLE_GHOST */).then((model) => {
            const ghostDoc = {
                countryCode: suspectDoc.countryCode,
                worldId: suspectDoc.worldId,
                userId: suspectDoc.userId,
                battleId: suspectDoc.battleId,
                actionLogs: suspectDoc.actionLogs,
                battleResult: suspectDoc.battleResult,
                reasonForSuspicion: suspectDoc.reasonForSuspicion,
                ghostReason,
            };
            // 비동기로 insert.
            model
                .create(ghostDoc)
                .then(() => {
                mlog_1.default.info('Ghost battle log.', {
                    countryCode: suspectDoc.countryCode,
                    worldId: suspectDoc.worldId,
                    userId: suspectDoc.userId,
                    battleId: suspectDoc.battleId,
                });
            })
                .catch((err) => {
                mlog_1.default.error('Failed to create ghost doc.', {
                    countryCode: suspectDoc.countryCode,
                    worldId: suspectDoc.worldId,
                    userId: suspectDoc.userId,
                    battleId: suspectDoc.battleId,
                    name: err.name,
                    message: err.message,
                });
            });
        });
    }
    JudgeUtil.saveGhost = saveGhost;
    // --------------------------------------------------------------------------
    // 전투 취소에 대한 검증 결과 유죄 판결을 받은 케이스 저장.
    // --------------------------------------------------------------------------
    async function saveCancelConvicted(mongoDbConn, cancelDoc, // mongoose.Document
    vbmLogInfo) {
        return mongoDbConn
            .getModel(5 /* BATTLE_CANCEL_CONVICT */)
            .then((model) => {
            const txnLog = JSON.parse(cancelDoc.actionLogs);
            const battleParam = JSON.parse(txnLog[0]).battleParam;
            const convictDoc = {
                countryCode: cancelDoc.countryCode,
                worldId: cancelDoc.worldId,
                userId: cancelDoc.userId,
                battleId: cancelDoc.battleId,
                actionLogs: cancelDoc.actionLogs,
                battleError: cancelDoc.battleError,
                reason: cancelDoc.reason,
                vbmLog: vbmLogInfo.vbmLog,
            };
            // 비동기로 insert.
            model.create(convictDoc).catch((err) => {
                mlog_1.default.error('Failed to create convict doc. (c)', {
                    countryCode: cancelDoc.countryCode,
                    worldId: cancelDoc.worldId,
                    userId: cancelDoc.userId,
                    battleId: cancelDoc.battleId,
                    name: err.name,
                    message: err.message,
                });
            });
        });
    }
    JudgeUtil.saveCancelConvicted = saveCancelConvicted;
    // --------------------------------------------------------------------------
    // 전투 취소에 대한 검증이 실패한 doc 저장. (장애, 에러 등)
    // --------------------------------------------------------------------------
    async function saveCancelGhost(mongoDbConn, cancelSuspectDoc, ghostReason) {
        return mongoDbConn
            .getModel(6 /* BATTLE_CANCEL_GHOST */)
            .then((model) => {
            const ghostDoc = {
                countryCode: cancelSuspectDoc.countryCode,
                worldId: cancelSuspectDoc.worldId,
                userId: cancelSuspectDoc.userId,
                battleId: cancelSuspectDoc.battleId,
                actionLogs: cancelSuspectDoc.actionLogs,
                battleError: cancelSuspectDoc.battleError,
                reason: cancelSuspectDoc.reason,
                ghostReason,
            };
            // 비동기로 insert.
            model
                .create(ghostDoc)
                .then(() => {
                mlog_1.default.info('Ghost battle cancel log.', {
                    countryCode: cancelSuspectDoc.countryCode,
                    worldId: cancelSuspectDoc.worldId,
                    userId: cancelSuspectDoc.userId,
                    battleId: cancelSuspectDoc.battleId,
                });
            })
                .catch((err) => {
                mlog_1.default.error('Failed to create ghost doc. (c)', {
                    countryCode: cancelSuspectDoc.countryCode,
                    worldId: cancelSuspectDoc.worldId,
                    userId: cancelSuspectDoc.userId,
                    battleId: cancelSuspectDoc.battleId,
                    name: err.name,
                    message: err.message,
                });
            });
        });
    }
    JudgeUtil.saveCancelGhost = saveCancelGhost;
})(JudgeUtil = exports.JudgeUtil || (exports.JudgeUtil = {}));
//# sourceMappingURL=judgeUtil.js.map