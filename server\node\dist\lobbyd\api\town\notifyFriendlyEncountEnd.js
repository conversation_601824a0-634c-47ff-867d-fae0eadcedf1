"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const proto = __importStar(require("../../../proto/lobby/proto"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const userManager_1 = require("../../userManager");
const typedi_1 = require("typedi");
const userFriendlyEncount_1 = require("../../userFriendlyEncount");
const mutil = __importStar(require("../../../motiflib/mutil"));
module.exports = async (req, res) => {
    mlog_1.default.debug('api-/common/friendNotification req -', req.body);
    const { targetUserId } = req.body.packet;
    let encountResult = req.body.packet.encountResult;
    const curTimeUtc = mutil.curTimeUtc();
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    const user = userManager.getUserByUserId(targetUserId);
    if (!user) {
        return res.json({ encountResult: userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_NOT_FOUND_USER });
    }
    const encountState = user.userFriendlyEncount.getEncountState();
    if (!encountState) {
        return res.json({ encountResult: userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_HAS_NOT_ENCOUNTING });
    }
    if (encountState.encountResult !== userFriendlyEncount_1.FriendlyEncountResult.WAITING_FOR_CHOICE) {
        return res.json({ encountResult: userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_ALREADY_PROCESSING });
    }
    const sync = {};
    if (encountResult === userFriendlyEncount_1.FriendlyEncountResult.START_BATTLE) {
        encountResult = userFriendlyEncount_1.FriendlyEncountUtil.checkFriendlyBattle(user, true);
    }
    const result = await user.userFriendlyEncount.endEncount(user, encountResult, curTimeUtc, sync, 'friendly_encount_end_sc');
    res.json({ encountResult: result.encountResult });
    user.sendJsonPacket(0, proto.Town.FRIENDLY_ENCOUNT_END_SC, {
        sync,
        battleParam: result.battleParam,
        encountResult: result.encountResult,
    });
};
//# sourceMappingURL=notifyFriendlyEncountEnd.js.map