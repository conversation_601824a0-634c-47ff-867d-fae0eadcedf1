{"name": "uwo-remap-tool", "version": "1.0.0", "description": "UWO GNID/NID Remapping Tool", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node --enable-source-maps dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "analyze": "node dist/index.js analyze", "summary": "node dist/index.js summary", "run": "node dist/index.js run", "backup": "node dist/index.js backup", "restore": "node dist/index.js restore", "list-backups": "node dist/index.js list-backups", "verify": "node dist/index.js verify", "init": "node dist/index.js init", "extract-schema": "node dist/index.js extract-schema", "generate": "node dist/index.js generate", "win:summary": "node dist/index.js summary -f", "win:run": "node dist/index.js run -f", "win:verify": "node dist/index.js verify -f"}, "bin": {"uwo-remap": "./dist/index.js"}, "keywords": ["uwo", "database", "redis", "migration", "remapping", "gnid", "nid", "accountId", "pubId"], "author": "UWO Development Team", "license": "MIT", "dependencies": {"@types/archiver": "^6.0.3", "@types/ioredis": "^4.28.10", "@types/luaparse": "^0.2.12", "@types/unzipper": "^0.10.11", "archiver": "^7.0.1", "chalk": "^4.1.2", "commander": "^11.1.0", "csv-parse": "^6.1.0", "csv-parser": "^3.0.0", "fs-extra": "^11.1.1", "glob": "^10.3.10", "inquirer": "^8.2.6", "ioredis": "^5.6.1", "json5": "^2.2.3", "lodash": "^4.17.21", "luaparse": "^0.3.1", "moment": "^2.29.4", "mysql2": "^3.14.2", "node-sql-parser": "^5.3.10", "ora": "^5.4.1", "progress": "^2.0.3", "unzipper": "^0.12.3", "winston": "^3.11.0"}, "devDependencies": {"@types/csv-parse": "^1.1.12", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.6", "@types/json5": "^0.0.30", "@types/lodash": "^4.14.200", "@types/node": "^20.8.10", "@types/progress": "^2.0.7", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "^8.52.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.8.3"}, "engines": {"node": ">=16.0.0"}}