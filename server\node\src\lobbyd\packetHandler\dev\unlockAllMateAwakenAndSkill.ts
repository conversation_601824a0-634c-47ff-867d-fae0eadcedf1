// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import { LobbyService } from '../../server';
import { Sync } from '../../type/sync';
import mlog from '../../../motiflib/mlog';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import * as cmsEx from '../../../cms/ex';
import { Dev } from '../../../proto/lobby/proto';
import { ClientPacketHandler } from '../index';
import Mate, { MateExpChange, MateUtil } from '../../mate';
import tuDevUnlockAwakenAndSkill from '../../../mysqllib/txn/tuDevUnlockAwakenAndSkill';
import { AccumulateParam } from '../../userAchievement';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { MATE_TRAINING_GRADE } from '../../../cms/mateTrainingDesc';

// ----------------------------------------------------------------------------
// 계정에서 보유하고 있는 모든 항해사의 등급 최대 + 모든 스킬 언락 치트
// ----------------------------------------------------------------------------

const rsn = 'dev_unlock_all_mate_awaken_and_skill';
const add_rsn = null;

// ----------------------------------------------------------------------------
export class Cph_Dev_UnlockAllMateAwakenAndSkill implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const { userDbConnPoolMgr, userCacheRedis } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.ensureCheatAccessLevel(Dev.UNLOCK_ALL_MATE_AWAKEN_AND_SKILL);

    const changeAwakenLvAndTrainingGrade: {
      mateCmsId: number;
      awakenLv: number;
      trainingGrade: MATE_TRAINING_GRADE;
    }[] = [];
    const changeAdventureExpLevel: MateExpChange[] = [];
    const changeTradeExpLevel: MateExpChange[] = [];
    const changeBattleExpLevel: MateExpChange[] = [];
    const addMatePassive: {
      mateCmsId: number;
      passiveCmsId: number;
    }[] = [];
    const removeMatePassive: {
      mateCmsId: number;
      passiveCmsId: number;
    }[] = [];

    const maxJobLevel =
      cms.Const['MateAwaken' + cms.Const.AwakenMaxLv.value + 'DevelopmentLv'].value;
    const maxAwakenLv = cms.Const.AwakenMaxLv.value;
    let oldSumLv: number;
    _.forOwn(_.values(user.userMates.getMates()), (mate: Mate) => {
      oldSumLv =
        mate.getLevel(cmsEx.JOB_TYPE.ADVENTURE) +
        mate.getLevel(cmsEx.JOB_TYPE.TRADE) +
        mate.getLevel(cmsEx.JOB_TYPE.BATTLE);

      _.forOwn(
        cmsEx.getMateLearnablePassiveElemTable(mate.getNub().cmsId),
        (_, strPassiveCmsId) => {
          const passiveCmsId = parseInt(strPassiveCmsId, 10);
          // 보유하지 않는 스킬을 추가
          if (!mate.getPassive(passiveCmsId)) {
            addMatePassive.push({
              mateCmsId: mate.getNub().cmsId,
              passiveCmsId,
            });
          }

          if (mate.isLearningPassive(passiveCmsId)) {
            removeMatePassive.push({
              mateCmsId: mate.getNub().cmsId,
              passiveCmsId,
            });
          }
        }
      );

      changeAwakenLvAndTrainingGrade.push({
        mateCmsId: mate.getNub().cmsId,
        awakenLv: maxAwakenLv,
        trainingGrade: MATE_TRAINING_GRADE.MAX,
      });

      changeAdventureExpLevel.push({
        mateCmsId: mate.getNub().cmsId,
        oldLevel: mate.getLevel(cmsEx.JOB_TYPE.ADVENTURE),
        level: maxJobLevel,
        exp: cms.CharacterExp[maxJobLevel - 1].accumulateExp[cmsEx.JOB_TYPE.ADVENTURE - 1],
      });
      changeTradeExpLevel.push({
        mateCmsId: mate.getNub().cmsId,
        oldLevel: mate.getLevel(cmsEx.JOB_TYPE.TRADE),
        level: maxJobLevel,
        exp: cms.CharacterExp[maxJobLevel - 1].accumulateExp[cmsEx.JOB_TYPE.TRADE - 1],
      });
      changeBattleExpLevel.push({
        mateCmsId: mate.getNub().cmsId,
        oldLevel: mate.getLevel(cmsEx.JOB_TYPE.BATTLE),
        level: maxJobLevel,
        exp: cms.CharacterExp[maxJobLevel - 1].accumulateExp[cmsEx.JOB_TYPE.BATTLE - 1],
      });
    });

    const sync: Sync = {};
    return tuDevUnlockAwakenAndSkill(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      changeAwakenLvAndTrainingGrade,
      changeAdventureExpLevel,
      changeTradeExpLevel,
      changeBattleExpLevel,
      addMatePassive,
      removeMatePassive
    )
      .then(() => {
        // Update cache and build resp sync.
        for (const change of changeAwakenLvAndTrainingGrade) {
          const userMate = user.userMates.getMate(change.mateCmsId);
          userMate.setAwakenLv(change.awakenLv, user.companyStat);
          _.merge<Sync, Sync>(sync, {
            add: {
              mates: {
                [change.mateCmsId]: {
                  awakenLv: change.awakenLv,
                },
              },
            },
          });

          userMate.setAwakenTimeUtc(null);
          // sync data, null 관련해서 remove 로 제거
          _.merge<Sync, Sync>(sync, {
            remove: {
              mates: {
                [change.mateCmsId]: {
                  awakenTimeUtc: true,
                },
              },
            },
          });

          userMate.setTrainingGrade(change.trainingGrade, user.companyStat);
          _.merge<Sync, Sync>(sync, {
            add: {
              mates: {
                [change.mateCmsId]: {
                  trainingGrade: change.trainingGrade,
                },
              },
            },
          });

          const collectionSync: Sync = {};
          user.userCollection.registerMate(user, change.mateCmsId, change.awakenLv, collectionSync);
          _.merge<Sync, Sync>(sync, collectionSync);
        }

        // update adventure exp, level
        const accums: AccumulateParam[] = [];

        _.merge<Sync, Sync>(
          sync,
          user.userMates.applyMateExpLevelChanges(
            user,
            changeAdventureExpLevel,
            cmsEx.JOB_TYPE.ADVENTURE,
            null
          )
        );
        for (const change of changeAdventureExpLevel) {
          if (change.level > change.oldLevel) {
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
              targets: [change.mateCmsId],
              addedValue: change.level - change.oldLevel,
            });
          }
        }

        // update trade exp, level
        _.merge<Sync, Sync>(
          sync,
          user.userMates.applyMateExpLevelChanges(
            user,
            changeTradeExpLevel,
            cmsEx.JOB_TYPE.TRADE,
            null
          )
        );

        for (const change of changeTradeExpLevel) {
          if (change.level > change.oldLevel) {
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
              targets: [change.mateCmsId],
              addedValue: change.level - change.oldLevel,
            });
          }
        }

        // update battle exp, level
        _.merge<Sync, Sync>(
          sync,
          user.userMates.applyMateExpLevelChanges(
            user,
            changeBattleExpLevel,
            cmsEx.JOB_TYPE.BATTLE,
            null
          )
        );
        for (const change of changeBattleExpLevel) {
          if (change.level > change.oldLevel) {
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
              targets: [change.mateCmsId],
              addedValue: change.level - change.oldLevel,
            });
          }
        }

        // remove passive learning
        for (const change of removeMatePassive) {
          const mate = user.userMates.getMate(change.mateCmsId);
          mate.removePassiveLearning(change.passiveCmsId);
          _.merge<Sync, Sync>(sync, {
            remove: {
              mates: {
                [mate.getNub().cmsId]: {
                  passiveLearnings: { [change.passiveCmsId]: true },
                },
              },
            },
          });
        }

        for (const { mateCmsId, passiveCmsId } of addMatePassive) {
          const userMate = user.userMates.getMate(mateCmsId);
          userMate.addPassive(passiveCmsId, 0, sync);
        }

        _.forOwn(_.values(user.userMates.getMates()), (mate: Mate) => {
          const newSumLv =
            mate.getLevel(cmsEx.JOB_TYPE.ADVENTURE) +
            mate.getLevel(cmsEx.JOB_TYPE.TRADE) +
            mate.getLevel(cmsEx.JOB_TYPE.BATTLE);

          if (newSumLv > oldSumLv) {
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
              targets: [mate.getNub().cmsId],
              addedValue: newSumLv - oldSumLv,
            });
          }
        });

        userCacheRedis['setUserLeaderMateAwakenLevel'](user.userId, maxAwakenLv).catch((err) => {
          mlog.error('userCacheRedis setUserLeaderMateAwakenLevel is failed.', {
            userId: user.userId,
            newAwakenLv: maxAwakenLv,
            err: err.message,
          });
        });

        return user.userAchievement.accumulate(accums, user, sync, null);
      })
      .then(() => {
        return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
      });
  }
}
