// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import moment from 'moment';

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import { AdminService } from '../../server';
import { CommonResponseBody } from '../../adminCommon';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { WorldBuffSourceType } from '../../../cms/ex';
import { WorldBuffUtil } from '../../../lobbyd/userBuffs';
import { GAME_STATE, GsUtil } from '../../../motiflib/model/lobby/gameState';
import paAdminLoadAccountIdPubIdUserIdByName from '../../../mysqllib/sp/paAdminLoadAccountIdPubIdUserIdByName';
import paAdminAccountLoadAllColumns from '../../../mysqllib/sp/paAdminAccountLoadAllColumns';
import puAdminUserLoad from '../../../mysqllib/sp/puAdminUserLoad';
import { DbConnPoolManager } from '../../../mysqllib/pool';
import paAdminLoadAccountIdPubIdWorldIdNameByUserId from '../../../mysqllib/sp/paAdminLoadAccountIdPubIdWorldIdNameByUserId';
import { UserEnergy } from '../../../lobbyd/userEnergy';
import { User } from '../../../lobbyd/user';
import { UserManufacture } from '../../../lobbyd/userManufacture';

interface RequestBody {
  gameServerId: string;
  gameUserId?: string;
  nick?: string;
}

interface ResponseBody extends CommonResponseBody {
  data?: {
    nid?: string;
    gnid?: string;
    userName?: string;
    gameServerId?: string;
    gameUserId?: number;
    countryIp?: string;
    isOnline?: number;
    loginPlatform?: string; // 로그인 플랫폼(페이스북/구글)
    createDate?: string; // 계정 생성 일시
    accessLevel?: number; // 계정상태
    lastLoginDate?: string; // 최근 접속 일시
    lastLogoutDate?: string; // 마지막 접속 일시
    points?: { [name: string]: number }; // 보유 재화
    nation?: string;
    level?: number;
    exp?: string;
    energy?: number;
    manufacturePoint?: number;
    companyJob?: string;
    town?: string; // 도시에 있을 경우 현재 도시
    oceanLocation?: { latitude: number; longitude: number }; // 해상일 경우 현재 위치
    installmentSavings?: {
      accumPoint: number;
      accumRate: number;
      accumCount: number;
      last: string;
      lastDepositDate: string;
    }; // 적금 현황
    insurance?: {
      name: string;
      unpaidTradeGoods: number;
      unpaidShip: number;
      unpaidSailor: number;
      unpaidDucat: number;
    }; // 보험 현황
    taxFreePermits?: {
      name: string;
      expirationDate: string;
    }[]; // 면세증 현황
    worldBuffs?: {
      buffName: string;
      sourceType: string;
      sourceName: string;
      stack: number;
      targetId: number;
      startDate: string;
      endDate: string;
    }[]; // 활성화 중인 효과
    firstMate?: string; // 최초 선택 제독
    // 누적 결제 건수
    // 누적 결제 금액
    clientVersion?: string; // 게임 버전
    manufactureExpLevel?: {
      castingExp: number;
      castingLevel: number;
      cookingExp: number;
      cookingLevel: number;
      sewingExp: number;
      sewingLevel: number;
      handmadeExp: number;
      handmadeLevel: number;
      medicineExp: number;
      medicineLevel: number;
    };
  };
}

export = (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /user/loadUser', { body: req.body });

  const curTimeUtc = mutil.curTimeUtc();
  const { gameUserId }: RequestBody = req.body;
  let { nick, gameServerId: worldId }: RequestBody = req.body;
  let userDbConnPoolMgr: DbConnPoolManager;

  if (nick && !worldId) {
    throw new MError('no gameServerId', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }

  if (!gameUserId && !nick) {
    throw new MError('no gameUserId no nick', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }

  const { authDbConnPool, userDbConnPoolMgrs, serviceLayoutMgr, sailRedises, userCacheRedis } =
    Container.get(AdminService);
  const sailRedis = sailRedises[worldId];

  const timezone = serviceLayoutMgr.getWorldCfg(worldId).timezone;

  const resp: ResponseBody = {
    isSuccess: true,
  };

  let userId: number;
  let accountId: string;
  let nid: string;
  return Promise.resolve()
    .then(() => {
      // load userId, pubId, accountId.
      if (gameUserId) {
        return paAdminLoadAccountIdPubIdWorldIdNameByUserId(
          authDbConnPool.getPool(),
          parseInt(gameUserId, 10),
          worldId
        );
      } else {
        return paAdminLoadAccountIdPubIdUserIdByName(authDbConnPool.getPool(), nick, worldId);
      }
    })
    .then((result) => {
      if (!result) {
        throw new MError('no user', MErrorCode.ADMIN_USER_NOT_FOUND, req.body);
      }

      accountId = result.accountId;
      nid = result.pubId;
      nick = nick || result.name;
      userId = result.userId;
      worldId = result.worldId;

      userDbConnPoolMgr = userDbConnPoolMgrs[worldId];
      if (!userDbConnPoolMgr) {
        throw new MError('no gameServerId in service layout', MErrorCode.ADMIN_INVALID_PARAMETER, {
          worldId,
        });
      }

      resp.data = {
        nid,
        gnid: accountId,
        gameServerId: worldId,
        gameUserId: userId,
      };

      // load from auth db
      return paAdminAccountLoadAllColumns(authDbConnPool.getPool(), result.accountId);
    })
    .then((result) => {
      resp.data.accessLevel = result.accessLevel;
      resp.data.lastLoginDate = moment(
        new Date(parseInt(result.lastLoginTimeUtc, 10) * 1000)
      ).format('YYYY-MM-DD HH:mm:ss');
      resp.data.loginPlatform = result.loginPlatform;
      resp.data.createDate = moment(new Date(parseInt(result.createTimeUtc, 10) * 1000)).format(
        'YYYY-MM-DD HH:mm:ss'
      );
      resp.data.clientVersion = result.clientVersion;

      // load from user db
      return puAdminUserLoad(
        userDbConnPoolMgr.getPoolByUserIdAndShardFuncName(
          userId,
          serviceLayoutMgr.getUserDbShardFunction(worldId),
          worldId
        ),
        userId,
        curTimeUtc
      );
    })
    .then((result) => {
      let energyChange = UserEnergy.buildEnergyChange(
        result.energy,
        parseInt(result.lastUpdateEnergyTimeUtc, 10),
        curTimeUtc,
        result.level,
        result.level
      );
      if (!energyChange) {
        energyChange = {
          energy: result.energy,
          lastUpdateTimeUtc: parseInt(result.lastUpdateEnergyTimeUtc, 10),
        };
      }
      resp.data.isOnline = result.isOnline;
      resp.data.energy = energyChange.energy;

      resp.data.manufacturePoint = UserManufacture.getCurrentPoint(
        result.manufacturePoint,
        parseInt(result.lastUpdateManufacturePointTimeUtc, 10),
        curTimeUtc,
        timezone
      );

      resp.data.userName = result.name;
      resp.data.countryIp = result.countryIp;
      resp.data.points = {};
      for (const elem of result.points) {
        if (elem.cmsId === cmsEx.EnergyPointCmsId) {
          resp.data.energy = elem.value;
          continue;
        }
        const pointCms = cms.Point[elem.cmsId];
        resp.data.points[pointCms.name] = elem.value;
      }
      resp.data.nation = cms.Nation[result.nationCmsId]
        ? cms.Nation[result.nationCmsId].name
        : null;
      resp.data.level = result.level;
      const companyExpCms = cms.CompanyExp[result.level];
      resp.data.exp = `${result.exp}/${companyExpCms.accumulateExp}`;
      resp.data.companyJob = cms.CompanyJob[result.companyJobCmsId]
        ? cms.CompanyJob[result.companyJobCmsId].desc
        : null;
      if (
        GsUtil.isInTown(
          result.gameState === GAME_STATE.NONE ? result.lastGameState : result.gameState
        )
      ) {
        resp.data.town = cms.Town[result.lastTownCmsId].name;
      }
      resp.data.installmentSavings = {
        accumPoint: parseInt(result.installmentSavings.accumPoint, 10),
        accumRate: result.installmentSavings.accumRate / 1000,
        accumCount: result.installmentSavings.accumCount,
        last: cms.BankInstallmentSavings[result.installmentSavings.lastCmsId]
          ? cms.BankInstallmentSavings[result.installmentSavings.lastCmsId].name
          : null,
        lastDepositDate: moment(
          new Date(parseInt(result.installmentSavings.lastDepositTimeUtc, 10) * 1000)
        ).format('YYYY-MM-DD HH:mm:ss'),
      };
      const insuranceCms = cms.BankInsurance[result.insurance.insuranceCmsId];
      if (insuranceCms) {
        resp.data.insurance = {
          name: mutil.stringFormat(insuranceCms.name, insuranceCms.nameFormatTexts),
          unpaidTradeGoods: parseInt(result.insurance.unpaidTradeGoods, 10),
          unpaidShip: parseInt(result.insurance.unpaidShip, 10),
          unpaidSailor: parseInt(result.insurance.unpaidSailor, 10),
          unpaidDucat: parseInt(result.insurance.unpaidDucat, 10),
        };
      }
      resp.data.taxFreePermits = [];
      if (result.firstMateCmsId) {
        resp.data.firstMate = displayNameUtil.getMateDisplayName(result.firstMateCmsId);
      }

      for (const elem of result.taxFreePermits) {
        resp.data.taxFreePermits.push({
          name: cms.TaxFreePermit[elem.cmsId]
            ? displayNameUtil.getTaxFreePermitDisplayName(cms.TaxFreePermit[elem.cmsId])
            : null,
          expirationDate: moment(new Date(parseInt(elem.expirationTimeUtc, 10) * 1000)).format(
            'YYYY-MM-DD HH:mm:ss'
          ),
        });
      }
      resp.data.worldBuffs = [];
      for (const elem of result.worldBuffs) {
        const worldBuffCms = cms.WorldBuff[elem.cmsId];
        resp.data.worldBuffs.push({
          buffName: mutil.stringFormat(worldBuffCms.name, [worldBuffCms.nameFormatTexts]),
          sourceType: WorldBuffSourceType[elem.sourceType],
          sourceName: WorldBuffUtil.getSourceNameOfCmsByWorldBuffSourceType(
            elem.sourceType,
            elem.sourceId
          ),
          stack: elem.stack,
          targetId: elem.targetId,
          startDate: moment(new Date(parseInt(elem.startTimeUtc, 10) * 1000)).format(
            'YYYY-MM-DD HH:mm:ss'
          ),
          endDate: moment(new Date(parseInt(elem.endTimeUtc, 10) * 1000)).format(
            'YYYY-MM-DD HH:mm:ss'
          ),
        });
      }

      resp.data.manufactureExpLevel = {
        castingExp: result.manufactureExpLevel.castingExp,
        castingLevel: result.manufactureExpLevel.castingLevel,
        cookingExp: result.manufactureExpLevel.cookingExp,
        cookingLevel: result.manufactureExpLevel.cookingLevel,
        sewingExp: result.manufactureExpLevel.sewingExp,
        sewingLevel: result.manufactureExpLevel.sewingLevel,
        handmadeExp: result.manufactureExpLevel.handmadeExp,
        handmadeLevel: result.manufactureExpLevel.handmadeLevel,
        medicineExp: result.manufactureExpLevel.medicineExp,
        medicineLevel: result.manufactureExpLevel.medicineLevel,
      };

      // 해상에 있을 경우 sail id 로드 (위치를 얻기 위해)
      if (
        GsUtil.isInOcean(
          result.gameState === GAME_STATE.NONE ? result.lastGameState : result.gameState
        )
      ) {
        return sailRedis['getSailingId'](userId);
      }

      return null;
    })
    .then((sailId) => {
      // 해상에 있을 경우 위치 로드
      if (sailId) {
        return sailRedis['loadSailing'](userId, sailId);
      }

      return null;
    })
    .then((sailing) => {
      if (sailing) {
        resp.data.oceanLocation = JSON.parse(sailing).location;
      }

      return userCacheRedis['getUser'](userId);
    })
    .then((ret) => {
      const redisRet = JSON.parse(ret);
      if (redisRet && redisRet.lastLogoutTimeUtc) {
        resp.data.lastLogoutDate = moment(
          new Date(parseInt(redisRet.lastLogoutTimeUtc, 10) * 1000)
        ).format('YYYY-MM-DD HH:mm:ss');
      }

      mlog.info('[TX] /user/loadUser', { body: resp });
      res.json(resp);
    });
};
