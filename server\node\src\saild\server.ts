// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import bodyParser from 'body-parser';
import express from 'express';
import http from 'http';
import morgan from 'morgan';
import path from 'path';
import Container, { Service } from 'typedi';

import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import * as tcp from '../tcplib';
import { MRedisConnPool } from '../redislib/connPool';
import Pubsub from '../redislib/pubsub';
import { DBConnPool, DbConnPoolManager } from '../mysqllib/pool';
import * as saildPubsub from './sailPubsub';
import { registerBehaviorNodes } from './offlineSailingBotBehaviorCommonNode';
import { OfflineSailingManager } from './offlineSailingManager';
import cms, { load as loadCms } from '../cms';
import stoppable from 'stoppable';
import { Segments } from '../tcplib/shared/segments';
import * as Sentry from '@sentry/node';
import 'express-async-errors';
import { PushLocalizeLookupTable } from '../motiflib/model/cmsKeyGroup';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

//////

// -------------------------------------------------------------------------------------------------
// Api interfaces.
// -------------------------------------------------------------------------------------------------

@Service()
export class SailService {
  userCacheRedis: MRedisConnPool;
  sailRedis: MRedisConnPool;
  monitorRedis: MRedisConnPool;
  worldDbConnPool: DBConnPool;

  authPubsub: Pubsub;
  bMaintenance: boolean = false;
  lastMaintenanceTick: number = 0;

  pushLocalizeLookupTable: PushLocalizeLookupTable;

  async init() {
    await startTogglet();

    // Init mysql connection pool.
    this.worldDbConnPool = Container.of('world').get(DBConnPool);
    await this.worldDbConnPool.init(mconf.mysqlWorldDb);

    // Init user cache redis pool.
    this.userCacheRedis = Container.of('user-cache-redis').get(MRedisConnPool);
    await this.userCacheRedis.init('user-cache-redis', mconf.userCacheRedis);

    // Init sail redis pool.
    this.sailRedis = Container.of('sail-redis').get(MRedisConnPool);
    await this.sailRedis.init('sail-redis', mconf.sailRedis);

    this.monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
    await this.monitorRedis.init('monitor-redis', mconf.monitorRedis);

    // Init redis pubsub.
    this.authPubsub = Container.of('pubsub-auth').get(Pubsub);
    this.authPubsub.init(mconf.authPubsubRedis);

    saildPubsub.init();

    this.pushLocalizeLookupTable = new PushLocalizeLookupTable(cms);
  }

  async destroy() {
    await this.authPubsub.quit();
    await this.worldDbConnPool.destroy();
    await this.userCacheRedis.destroy();
    await this.sailRedis.destroy();
    await this.monitorRedis.destroy();

    stopTogglet();
  }
}

// -------------------------------------------------------------------------------------------------
// Module variables.
// -------------------------------------------------------------------------------------------------
// Main express app.
const app = express();

app.disable('x-powered-by');
app.disable('etag');
app.disable('content-type');

const server = stoppable(http.createServer(app));
server.keepAliveTimeout = 0;

export const tcpServer = tcp.server();
tcpServer.routeEx(__dirname, './serverPacketHandler');
tcp.logger.setMoudle(mlog); // Setup tcp log writer

let stopping = false;
let offSailStartDelayTimeout: NodeJS.Timer = null;
// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

function sailReqLog(tokens, req, res) {
  if (req.url === '/health') {
    return;
  }

  mlog.info('saild-req', {
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  });
  return null;
}

async function closeServer() {
  return new Promise((resolve, reject) => {
    server.stop((err) => {
      if (err) return reject(err);
      resolve(null);
    });
  });
}

export async function stopServer() {
  try {
    mlog.info('stopping server ...');

    tcpServer.dispose();

    if (offSailStartDelayTimeout) {
      clearTimeout(offSailStartDelayTimeout);
      offSailStartDelayTimeout = null;
    }
    await closeServer();

    const offlineSailingManager = Container.get(OfflineSailingManager);
    offlineSailingManager.stopTick();

    const app = Container.get(SailService);
    await app.destroy();

    mlog.info('server stopped');
    process.exitCode = 0;
  } catch (error) {
    mlog.error('graceful shutdown failed', { error: error.message });
    process.exit(1);
  }
}

// -------------------------------------------------------------------------------------------------
// Public functions.
// -------------------------------------------------------------------------------------------------

export async function start() {
  try {
    await mhttp.configd.registerInstance(
      process.env.WORLD_ID ? process.env.WORLD_ID : mconf.instance.worldId,
      mconf.appInstanceId,
      mconf.hostname
    );

    mutil.initSentry();

    // Init http clients.
    mhttp.init();

    // Init cms.
    loadCms();

    const appService = Container.get(SailService);
    await appService.init();

    const bindAddress = mconf.apiService.bindAddress;
    const port = mconf.apiService.port;

    app.use(morgan(sailReqLog));
    app.use(bodyParser.json());
    mutil.registerHealthCheck(app);
    mutil.registerGarbageCollector(app);

    await dirAsApi.register(app, path.join(__dirname, 'api'));
    app.use(expressError.middleware);

    const onDisconnected = (segment: Segments): void => {
      const disconnectedServerURL = segment.get('url');

      const offlineSailingManager = Container.get(OfflineSailingManager);
      const jobs = offlineSailingManager.getAllJobs();
      jobs.forEach((job) => {
        if (job.getBot().conn.getLobbydUrl() === disconnectedServerURL) {
          // 접속종료된 로비서버와 연결된 job을 종료한다
          job.close();

          mlog.warn('offSailBot was destroyed by disconnected lobbyd', {
            userId: job.userId,
          });
        }
      });
    };

    const tcpconf = mconf.apiService.tcpServer;
    tcpServer.start(tcpconf.port, tcpconf.ip, onDisconnected);

    //register behaviorTree nodes
    registerBehaviorNodes();

    // Init user tick.
    const offSailStartDelay = mutil.randIntInc(1, 30000); // 최대 30초 후에 시작
    offSailStartDelayTimeout = setTimeout(async () => {
      // 서버 점검 여부 체크
      Promise.resolve()
        .then(() => {
          return mhttp.lgd
            .isWorldInMaintenance(mconf.worldId)
            .then((ret) => {
              mlog.info('init bMaintenance state ', {
                new: ret,
              });

              appService.bMaintenance = ret;
            })
            .catch((error) => {
              mlog.error('failed to check maintenance', {
                error: error.message,
                extra: error.extra,
              });
            });
        })
        .then(() => {
          if (!stopping) {
            const offlineSailingManager = Container.get(OfflineSailingManager);
            offlineSailingManager.startTick();
          }
        });
    }, offSailStartDelay);

    server.listen(port, bindAddress, () => {
      mlog.info('start listening ...', { bindAddress, port });
    });

    // config final sync
    const beforeVer = mconf.layoutVersion;
    await mhttp.configd.sync(beforeVer, isStopping, stop).then(() => {
      if (beforeVer < mconf.layoutVersion) {
        // do something
      }
    });
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
}

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;
  await stopServer();
}
