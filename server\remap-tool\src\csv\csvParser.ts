import fs from 'fs-extra';
import csv from 'csv-parser';
import { RemapData } from '../types';

export class CsvParser {
  async parseRemapCsv(filePath: string): Promise<RemapData[]> {
    if (!await fs.pathExists(filePath)) {
      throw new Error(`CSV file not found: ${filePath}`);
    }

    const results: RemapData[] = [];
    const errors: string[] = [];

    return new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row: any) => {
          try {
            const remapData = this.parseRow(row, results.length + 1);
            if (remapData) {
              results.push(remapData);
            }
          } catch (error) {
            errors.push(`Line ${results.length + 1}: ${error}`);
          }
        })
        .on('end', () => {
          if (errors.length > 0) {
            reject(new Error(`CSV parsing errors:\n${errors.join('\n')}`));
          } else {
            console.log(`CSV parsing completed: ${results.length} records`);
            resolve(results);
          }
        })
        .on('error', (error) => {
          reject(new Error(`CSV file reading error: ${error.message}`));
        });
    });
  }

  private parseRow(row: any, _lineNumber: number): RemapData | null {
    const requiredColumns = ['seq', 'uwo_Gnid', 'uwo_Nid', 'uwogl_Gnid', 'uwogl_Nid', 'gameServerId'];
    const missingColumns = requiredColumns.filter(col => !(col in row));

    if (missingColumns.length > 0) {
      throw new Error(`Missing required columns: ${missingColumns.join(', ')}`);
    }

    const seq = this.parseNumber(row.seq, 'seq');
    const uwo_Gnid = this.parseString(row.uwo_Gnid, 'uwo_Gnid');
    const uwo_Nid = this.parseString(row.uwo_Nid, 'uwo_Nid');
    const uwogl_Gnid = this.parseString(row.uwogl_Gnid, 'uwogl_Gnid');
    const uwogl_Nid = this.parseString(row.uwogl_Nid, 'uwogl_Nid');
    const gameServerId = this.parseString(row.gameServerId, 'gameServerId');

    if (!uwo_Gnid && !uwo_Nid && !uwogl_Gnid && !uwogl_Nid) {
      return null;
    }

    return {
      seq,
      uwo_Gnid,
      uwo_Nid,
      uwogl_Gnid,
      uwogl_Nid,
      gameServerId,
    };
  }

  private parseNumber(value: any, fieldName: string): number {
    if (value === undefined || value === null || value === '') {
      throw new Error(`${fieldName} is empty`);
    }

    const num = Number(value);
    if (isNaN(num)) {
      throw new Error(`${fieldName} is not a valid number: ${value}`);
    }

    return num;
  }

  private parseString(value: any, fieldName: string): string {
    if (value === undefined || value === null) {
      throw new Error(`${fieldName} is empty`);
    }

    const str = String(value).trim();
    if (str === '') {
      throw new Error(`${fieldName} is empty`);
    }

    return str;
  }

  async validateRemapData(remapData: RemapData[]): Promise<void> {
    const errors: string[] = [];

    if (remapData.length === 0) {
      throw new Error('Remap data is empty');
    }

    const seqDuplicates = this.findDuplicates(remapData.map(d => d.seq.toString()));
    if (seqDuplicates.length > 0) {
      errors.push(`Duplicate seq values found: ${seqDuplicates.join(', ')}`);
    }

    const uwoKeys = remapData.map(d => `${d.uwo_Gnid}:${d.uwo_Nid}`);
    const uwoDuplicates = this.findDuplicates(uwoKeys);
    if (uwoDuplicates.length > 0) {
      errors.push(`Duplicate (uwo_Gnid, uwo_Nid) combinations found: ${uwoDuplicates.join(', ')}`);
    }

    const uwoglKeys = remapData.map(d => `${d.uwogl_Gnid}:${d.uwogl_Nid}`);
    const uwoglDuplicates = this.findDuplicates(uwoglKeys);
    if (uwoglDuplicates.length > 0) {
      errors.push(`Duplicate (uwogl_Gnid, uwogl_Nid) combinations found: ${uwoglDuplicates.join(', ')}`);
    }

    for (let i = 0; i < remapData.length; i++) {
      const data = remapData[i];
      const lineNumber = i + 1;

      if (!data) {
        continue;
      }

      if (!this.isValidId(data.uwo_Gnid)) {
        errors.push(`Line ${lineNumber}: Invalid uwo_Gnid format: ${data.uwo_Gnid}`);
      }

      if (!this.isValidId(data.uwo_Nid)) {
        errors.push(`Line ${lineNumber}: Invalid uwo_Nid format: ${data.uwo_Nid}`);
      }

      if (!this.isValidId(data.uwogl_Gnid)) {
        errors.push(`Line ${lineNumber}: Invalid uwogl_Gnid format: ${data.uwogl_Gnid}`);
      }

      if (!this.isValidId(data.uwogl_Nid)) {
        errors.push(`Line ${lineNumber}: Invalid uwogl_Nid format: ${data.uwogl_Nid}`);
      }

      if (!this.isValidGameServerId(data.gameServerId)) {
        errors.push(`Line ${lineNumber}: Invalid gameServerId format: ${data.gameServerId}`);
      }

      if (data.uwo_Gnid === data.uwogl_Gnid) {
        errors.push(`Line ${lineNumber}: uwo_Gnid and uwogl_Gnid are identical: ${data.uwo_Gnid}`);
      }

      if (data.uwo_Nid === data.uwogl_Nid) {
        errors.push(`Line ${lineNumber}: uwo_Nid and uwogl_Nid are identical: ${data.uwo_Nid}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Data validation errors:\n${errors.join('\n')}`);
    }

    console.log(`Data validation completed: ${remapData.length} records`);
  }

  filterByWorld(remapData: RemapData[], worldId: string): RemapData[] {
    const filtered = remapData.filter(data => data.gameServerId === worldId);
    console.log(`World ${worldId} filtering result: ${filtered.length} records`);
    return filtered;
  }

  private findDuplicates<T>(values: T[]): T[] {
    const seen = new Set<T>();
    const duplicates = new Set<T>();

    for (const value of values) {
      if (seen.has(value)) {
        duplicates.add(value);
      } else {
        seen.add(value);
      }
    }

    return Array.from(duplicates);
  }

  private isValidId(id: string): boolean {
    return /^[a-zA-Z0-9_-]+$/.test(id) && id.length > 0 && id.length <= 64;
  }

  private isValidGameServerId(gameServerId: string): boolean {
    return /^[a-zA-Z0-9-]+$/.test(gameServerId) && gameServerId.length > 0 && gameServerId.length <= 32;
  }

  async writeRemapCsv(filePath: string, remapData: RemapData[]): Promise<void> {
    const headers = ['seq', 'uwo_Gnid', 'uwo_Nid', 'uwogl_Gnid', 'uwogl_Nid', 'gameServerId'];
    const csvContent = [
      headers.join(','),
      ...remapData.map(data => [
        data.seq,
        data.uwo_Gnid,
        data.uwo_Nid,
        data.uwogl_Gnid,
        data.uwogl_Nid,
        data.gameServerId,
      ].join(','))
    ].join('\n');

    await fs.writeFile(filePath, csvContent, 'utf-8');
    console.log(`CSV file creation completed: ${filePath}`);
  }

  generateStatistics(remapData: RemapData[]): {
    totalRecords: number;
    worldCounts: Record<string, number>;
    uniqueOldGnids: number;
    uniqueOldNids: number;
    uniqueNewGnids: number;
    uniqueNewNids: number;
  } {
    const worldCounts: Record<string, number> = {};
    const oldGnids = new Set<string>();
    const oldNids = new Set<string>();
    const newGnids = new Set<string>();
    const newNids = new Set<string>();

    for (const data of remapData) {
      worldCounts[data.gameServerId] = (worldCounts[data.gameServerId] || 0) + 1;

      oldGnids.add(data.uwo_Gnid);
      oldNids.add(data.uwo_Nid);
      newGnids.add(data.uwogl_Gnid);
      newNids.add(data.uwogl_Nid);
    }

    return {
      totalRecords: remapData.length,
      worldCounts,
      uniqueOldGnids: oldGnids.size,
      uniqueOldNids: oldNids.size,
      uniqueNewGnids: newGnids.size,
      uniqueNewNids: newNids.size,
    };
  }
}
