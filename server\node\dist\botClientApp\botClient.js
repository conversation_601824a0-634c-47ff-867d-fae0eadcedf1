"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BotClient = void 0;
const gameState_1 = require("../motiflib/model/lobby/gameState");
const botConnection_1 = __importDefault(require("./botConnection"));
const botConnection_2 = require("./botConnection");
const proto = __importStar(require("../proto/lobby/proto"));
const botConf_1 = __importDefault(require("./botConf"));
const botConst = __importStar(require("./const"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const botLogic_1 = require("./botLogic");
const lodash_1 = __importStar(require("lodash"));
const cmsEx = __importStar(require("../cms/ex"));
const enum_1 = require("../motiflib/model/auth/enum");
const botEnums_1 = require("./botEnums");
const OC = __importStar(require("../cms/oceanCoordinate"));
const formula_1 = require("../formula");
const reqSenders = __importStar(require("./botReqSenders"));
const bot_1 = require("../motiflib/model/bot");
const mutil = __importStar(require("../motiflib/mutil"));
const gl_matrix_1 = require("gl-matrix");
const ex_1 = require("../cms/ex");
const cms_1 = __importDefault(require("../cms"));
const formula = __importStar(require("../formula"));
const behaviortree_1 = require("behaviortree");
const townUserState_1 = require("../townd/townUserState");
const botUtils_1 = require("./botUtils");
const scenarioInfiniteLoopTownAndOcean = __importStar(require("./botScenarios/botScenarioInfiniteLoopTownAndOcean"));
const scenarioInfiniteLoopReconnect = __importStar(require("./botScenarios/botScenarioInfiniteLoopReconnect"));
const scenarioChangeToOceanAndStayForever = __importStar(require("./botScenarios/botScenarioChangeToOceanAndStayForever"));
const scenarioOceanRandomMove = __importStar(require("./botScenarios/botScenarioOceanRandomMove"));
const scenarioOfflineSailing = __importStar(require("./botScenarios/botScenarioOfflineSailing"));
const scenarioGoverInvestActor = __importStar(require("./botScenarios/botScenarioGoverInvestActor"));
const scenarioTownRandomMove = __importStar(require("./botScenarios/botScenarioTownRandomMove"));
const scenarioGoverInvestAllTowns = __importStar(require("./botScenarios/botScenarioGoverInvestAllTowns"));
const scenarioTownInfiniteSetSidekickMate = __importStar(require("./botScenarios/botScenarioTownInfiniteSetSidekickMate"));
const lobby_1 = require("../motiflib/model/lobby");
// ----------------------------------------------------------------------------
// Constants.
// ----------------------------------------------------------------------------
class BehaviorRequest {
    constructor() {
        this.actionType = 0;
        this.requiredPacketTypes = [];
        this.result = 0;
    }
    getRequiredPacketTypes() {
        return this.requiredPacketTypes;
    }
    setRequiredPacketTypes(values) {
        this.requiredPacketTypes = values;
    }
    get getActionType() {
        return this.actionType;
    }
    set setActionType(val) {
        this.actionType = val;
    }
    get getResult() {
        return this.result;
    }
    set setResult(val) {
        this.result = val;
    }
    reset() {
        this.setActionType = botEnums_1.BehaviorActionType.None;
        this.setResult = botEnums_1.BehaviorRequestResultType.Success;
        this.requiredPacketTypes = [];
    }
}
class BotClient {
    constructor(idx) {
        this._userId = 0;
        this.curRequest = new BehaviorRequest();
        this._moveState = botEnums_1.BotMoveState.MoveStopped;
        this.lastTickTimeInMs = 0;
        this.sailSpeed = 0.0;
        this._sailId = undefined;
        this._isEncountOccurred = false;
        this._battleId = undefined;
        this._battleParam = undefined;
        this._isScenarioInitiated = false;
        this._lastLogoutTimeUtc = 0;
        this._nextDestinationTownCmsId = 0;
        this._townMovePacketRecvCount = 0;
        this._lastPingTimeUtc = 0;
        this._lastSidekickSetTickTimeInMs = 0;
        this._isSidekickMateSet = false;
        // User info.
        const pubIdInt = botConf_1.default.pubIdStart + idx;
        this.pubId = pubIdInt.toString();
        this.userName = botConf_1.default.userNamePrefix + this.pubId;
        this.enterWorldToken = '';
        // Socket.
        this._conn = new botConnection_1.default(this.pubId, this.userName);
        // State.
        this.botMode = botEnums_1.BotMode.LOGIN_MODE;
        this.loginPhase = botEnums_1.LoginPhase.UNAUTHORIZED;
        // sync Data
        this.syncAll = {};
        // behavior tree setting
        this.behaviorStat = {
            waitCount: 0,
            waitMax: 0,
        };
        this.initScenario();
        // Move state.
        this.resetMoveState();
    }
    getConn() {
        return this._conn;
    }
    setConn(value) {
        this._conn = value;
    }
    get SyncAll() {
        return this.syncAll;
    }
    getMoveState() {
        return this._moveState;
    }
    setMoveState(value) {
        this._moveState = value;
    }
    getLogoutTimeUtc() {
        return this._lastLogoutTimeUtc;
    }
    setLastLogoutTimeUtc(value) {
        this._lastLogoutTimeUtc = value;
    }
    getSailId() {
        return this._sailId;
    }
    setSailId(value) {
        this._sailId = value;
    }
    //----------------------------------------------------------
    getGameState() {
        if (this.syncAll && this.syncAll.user) {
            return this.syncAll.user.gameState;
        }
        return gameState_1.GAME_STATE.NONE;
    }
    //----------------------------------------------------------
    getRawLastGameState() {
        if (this.syncAll && this.syncAll.user) {
            return this.syncAll.user.lastGameState;
        }
        return gameState_1.GAME_STATE.NONE;
    }
    //----------------------------------------------------------
    // getLastGameState
    // gameState 는 유저의 현재 상태를 나타내며 로그인 시 NONE(0)으로 초기화 된다.
    // 하지만 유저의 마지막 상태를 유지 시켜줘야 될 필요가 있기에 lastGameState 가 존재하고
    // lastGameState 이 gameState 와 동일해지는 시점에 lastGameState 은 NONE 으로 초기화 된다.
    // 따라서 lastGameState 값이 NONE 일 경우 lastGameState 값은 gameState 을 통해 얻어오면 된다.
    //----------------------------------------------------------
    getLastGameState() {
        const lastGameState = this.getRawLastGameState();
        if (lastGameState !== gameState_1.GAME_STATE.NONE) {
            return lastGameState;
        }
        else {
            return this.getGameState();
        }
        return undefined;
    }
    //----------------------------------------------------------
    getGameEnterState() {
        if (this.syncAll && this.syncAll.user) {
            return this.syncAll.user.gameEnterState;
        }
        return gameState_1.GAME_ENTER_STATE.LOAD_COMPLETED;
    }
    //----------------------------------------------------------
    getTownUserState() {
        if (this.syncAll && this.syncAll.user) {
            return this.syncAll.user.townUserState;
        }
        return townUserState_1.TOWN_USER_STATE.NONE;
    }
    //----------------------------------------------------------
    isInTown() {
        return gameState_1.GsUtil.isInTown(this.getLastGameState());
    }
    //----------------------------------------------------------
    isInOcean() {
        return gameState_1.GsUtil.isInOcean(this.getLastGameState());
    }
    isInOceanBattle() {
        const gameState = this.getLastGameState();
        const gameEnterState = this.getGameEnterState();
        return (gameState >= gameState_1.GAME_STATE.OCEAN_BATTLE_REWARD_MIN &&
            gameState <= gameState_1.GAME_STATE.OCEAN_BATTLE_REWARD_MAX);
    }
    //----------------------------------------------------------
    isWrecked() {
        if (this.SyncAll.user) {
            return this.SyncAll.user.bGameOver;
        }
        return false;
    }
    //----------------------------------------------------------
    isShipNeededToRepair() {
        if (this.SyncAll.ships) {
            let bNeedRepair = false;
            Object.values(this.SyncAll.ships).forEach((ship) => {
                if (ship.durability === 0) {
                    bNeedRepair = true;
                }
            });
            return bNeedRepair;
        }
        return false;
    }
    //----------------------------------------------------------
    getShipsNeededToRepair() {
        if (this.SyncAll.ships) {
            const repairableShipIds = [
                ...Object.keys(this.SyncAll.ships).filter((shipId) => {
                    const ship = this.SyncAll.ships[shipId];
                    return ship.durability === 0;
                }),
            ];
            return repairableShipIds.map((shipIdStr) => parseInt(shipIdStr, 10));
        }
        return [];
    }
    //----------------------------------------------------------
    isShipNeededSailor() {
        if (this.SyncAll.ships) {
            let bNeedSailor = false;
            Object.values(this.SyncAll.ships).forEach((ship) => {
                if (ship.sailor === 0) {
                    bNeedSailor = true;
                }
            });
            return bNeedSailor;
        }
        return false;
    }
    //----------------------------------------------------------
    getShipsNeededSailor() {
        if (this.SyncAll.ships) {
            const targetShipIds = [
                ...Object.keys(this.SyncAll.ships).filter((shipId) => {
                    const ship = this.SyncAll.ships[shipId];
                    return ship.sailor === 0;
                }),
            ];
            return targetShipIds.map((shipIdStr) => parseInt(shipIdStr, 10));
        }
        return [];
    }
    //----------------------------------------------------------
    getPoint(cmsId) {
        if (this.SyncAll.points) {
            return this.SyncAll.points[cmsId] ? this.SyncAll.points[cmsId].value : 0;
        }
        return 0;
    }
    //----------------------------------------------------------
    isAbleToOceanMove() {
        if (this.isWrecked()) {
            return false;
        }
        if (this.getLastGameState() !== gameState_1.GAME_STATE.IN_OCEAN) {
            return false;
        }
        return true;
    }
    //----------------------------------------------------------
    getCurTownCmsId() {
        if (this.isInTown()) {
            if (this.syncAll.user && this.syncAll.user.lastTownCmsId) {
                return this.syncAll.user.lastTownCmsId;
            }
        }
        return 0;
    }
    //----------------------------------------------------------
    getTownInvestmentAccumPoint(townCmsId) {
        if (this.syncAll.towns && this.syncAll.towns[townCmsId]) {
            return this.syncAll.towns[townCmsId].myInvestmentAccumPoint
                ? this.syncAll.towns[townCmsId].myInvestmentAccumPoint
                : 0;
        }
        return 0;
    }
    //-----------------------------------------------------------
    // 미적용 코드. 봇에서 국가 순위가 필요한 경우 사용예정.
    getNationPowerRanks() {
        const arrNations = [];
        lodash_1.default.forOwn(this.syncAll.nations, (nation, natinCmsIdStr) => {
            arrNations.push({
                cmsId: parseInt(natinCmsIdStr, 10),
                power: nation.power,
                population: nation.population,
                numTown: nation.numTowns,
            });
        });
        arrNations.sort((a, b) => {
            if (a.power !== b.power) {
                return a.power - b.power;
            }
            if (a.population !== b.population) {
                return b.population - a.population;
            }
            if (a.numTowns !== b.numTowns) {
                return a.numTowns - b.numTowns;
            }
            return a.cmsId - b.cmsId;
        });
        const sortedNations = {};
        for (let idx = 0; idx < arrNations.length; idx++) {
            const nation = arrNations[idx];
            sortedNations[nation.cmsId] = idx + 1;
        }
        return sortedNations;
    }
    //----------------------------------------------------------
    getNationPowerRank(targetNationCmsId) {
        const sortedNationsRanks = this.getNationPowerRanks();
        return sortedNationsRanks[targetNationCmsId];
    }
    //----------------------------------------------------------
    isInTownBuilding(buildingType) {
        if (this.getTownUserState() === townUserState_1.TOWN_USER_STATE.IN_BUILDING) {
            if (this.syncAll.user &&
                this.syncAll.user.buildingType &&
                buildingType === this.syncAll.user.buildingType) {
                return true;
            }
        }
        return false;
    }
    //----------------------------------------------------------
    getNationCmsId() {
        if (this.syncAll.user && this.syncAll.user.nationCmsId) {
            return this.syncAll.user.nationCmsId;
        }
        return 0;
    }
    //----------------------------------------------------------
    calcScenarioGoverInvestAllTownsMinDucat() {
        const minDucat = this.calcGoverInvestPoint(this._nextDestinationTownCmsId);
        return minDucat;
    }
    //----------------------------------------------------------
    calcScenarioMinDucat() {
        switch (botEnums_1.ScenarioType[botConf_1.default.scenarioTypeName]) {
            case botEnums_1.ScenarioType.GoverInvestAllTowns:
                return this.calcScenarioGoverInvestAllTownsMinDucat();
            default:
                return botConf_1.default.point.minDucatAmount;
        }
    }
    //----------------------------------------------------------
    _getProbableNations() {
        const totalPower = (0, lodash_1.sumBy)([...Object.entries(this.SyncAll.nations)], ([_, nation]) => nation.power);
        const totalPopulation = (0, lodash_1.sumBy)([...Object.entries(this.SyncAll.nations)], ([_, nation]) => nation.population);
        if (totalPower === 0 || totalPopulation === 0) {
            return undefined;
        }
        const size = botLogic_1.selectableNationCmsIds.length;
        const probableNations = [];
        for (const key of Object.keys(this.SyncAll.nations)) {
            const nation = this.SyncAll.nations[key];
            const rate = (nation.power * size) / totalPower + (nation.population * size) / totalPopulation;
            if (rate < 2) {
                probableNations.push({ nationCmsId: parseInt(key, 10), rate });
            }
        }
        probableNations.sort(function (a, b) {
            return a.rate - b.rate;
        });
        return probableNations;
    }
    //----------------------------------------------------------
    getProbableNation() {
        const probableNations = this._getProbableNations();
        if (probableNations.length === 0) {
            // 발생하면 안됨
            return 0;
        }
        return probableNations[0].nationCmsId;
    }
    //----------------------------------------------------------
    isNeedMateExist() {
        // 항해사들을 검색해서 대상항해사 존재여부확인
        if (this.syncAll.mates) {
            for (const mate of Object.values(this.syncAll.mates)) {
                if (mate.cmsId === botConf_1.default.needMateCmsId) {
                    return true;
                }
            }
        }
        return false;
    }
    //----------------------------------------------------------
    resetBehaviorStat() {
        this.behaviorStat.waitCount = 0;
        this.behaviorStat.waitMax = 0;
        this._isScenarioInitiated = false;
        this._nextDestinationTownCmsId = 0;
        this.resetMoveState();
    }
    //----------------------------------------------------------
    resetMoveState() {
        this.curMoveStateIndex = 0;
        this.moveStates = [];
        if (botConf_1.default.movePositions) {
            for (let idx = 0; idx < botConf_1.default.movePositions.length; ++idx) {
                const pos = botConf_1.default.movePositions[idx];
                this.moveStates.push({
                    x: pos.x,
                    y: pos.y,
                    degrees: 0,
                    speed: 3,
                });
            }
        }
        // this.moveStates.push({
        //   x: -678000,
        //   y: -185800,
        //   degrees: 0,
        //   speed: 3,
        // });
        // this.moveStates.push({
        //   x: -650500,
        //   y: -183100,
        //   degrees: 15,
        //   speed: 800,
        // });
        // this.moveStates.push({
        //   x: -617700,
        //   y: -174100,
        //   degrees: 0,
        //   speed: 0,
        // });
        // this.moveStates.push({
        //   x: -619400,
        //   y: -174200,
        //   degrees: -177,
        //   speed: 800,
        // });
        // this.moveStates.push({
        //   x: -683900,
        //   y: -178500,
        //   degrees: 180,
        //   speed: 783,
        // });
        // this.moveStates.push({
        //   x: -671800,
        //   y: -177500,
        //   degrees: -3,
        //   speed: 761,
        // });
        // this.moveStates.push({
        //   x: -650600,
        //   y: -190000,
        //   degrees: 0,
        //   speed: 0,
        // });
        this._townMovePacketRecvCount = 0;
        // ocean move data
        this._moveState = botEnums_1.BotMoveState.MoveStopped;
        this.oceanTargetLoc = { latitude: 0, longitude: 0 };
        this.oceanCurrentLocation = { latitude: 0, longitude: 0 };
    }
    //----------------------------------------------------------
    getBehaviorStat() {
        return this.behaviorStat;
    }
    //----------------------------------------------------------
    getBehaviorRequestResult() {
        return this.curRequest.getResult;
    }
    //----------------------------------------------------------
    setEnterWorldToken(token) {
        this.enterWorldToken = token;
    }
    //----------------------------------------------------------
    isDisconnected() {
        return this._conn.isDisconnected();
    }
    //----------------------------------------------------------
    close() {
        if (this._conn) {
            this._conn.disconnect();
            this.setLastLogoutTimeUtc(mutil.curTimeUtc());
        }
    }
    //----------------------------------------------------------
    reconnect() {
        mlog_1.default.info('reconnect..................................', { pubId: this.pubId });
        this.botMode = botEnums_1.BotMode.LOGIN_MODE;
        this._conn.setState(botConnection_2.State.INITIALIZED);
        this.loginPhase = botEnums_1.LoginPhase.UNAUTHORIZED;
        this._conn.clearPacketQueue();
        this.syncAll = {};
        this.initScenario();
        //[todo] release any game play context
        this.resetBehaviorStat();
    }
    initScenario() {
        mlog_1.default.info(`applying [${botConf_1.default.scenarioTypeName}] ...`);
        switch (botEnums_1.ScenarioType[botConf_1.default.scenarioTypeName]) {
            case botEnums_1.ScenarioType.InfiniteLoopTownAndOcean:
                this.scenarioBehaviorTree = scenarioInfiniteLoopTownAndOcean.createScenario(this);
                break;
            case botEnums_1.ScenarioType.InfiniteLoopReconnect:
                this.scenarioBehaviorTree = scenarioInfiniteLoopReconnect.createScenario(this);
                break;
            case botEnums_1.ScenarioType.ChangeToOceanAndStayForever:
                this.scenarioBehaviorTree = scenarioChangeToOceanAndStayForever.createScenario(this);
                break;
            case botEnums_1.ScenarioType.OceanRandomMove:
                this.scenarioBehaviorTree = scenarioOceanRandomMove.createScenario(this);
                break;
            case botEnums_1.ScenarioType.OfflineSailing:
                this.scenarioBehaviorTree = scenarioOfflineSailing.createScenario(this);
                break;
            case botEnums_1.ScenarioType.GoverInvestActor:
                this.scenarioBehaviorTree = scenarioGoverInvestActor.createScenario(this);
                break;
            case botEnums_1.ScenarioType.TownRandomMove:
                this.scenarioBehaviorTree = scenarioTownRandomMove.createScenario(this);
                break;
            case botEnums_1.ScenarioType.GoverInvestAllTowns:
                this.scenarioBehaviorTree = scenarioGoverInvestAllTowns.createScenario(this);
                break;
            case botEnums_1.ScenarioType.TownInfiniteSetSidekickMate:
                this.scenarioBehaviorTree = scenarioTownInfiniteSetSidekickMate.createScenario(this);
                break;
            default:
                mlog_1.default.warn(`not found [${botConf_1.default.scenarioTypeName}] ...applying default scenario`);
                this.scenarioBehaviorTree = scenarioInfiniteLoopTownAndOcean.createScenario(this);
                break;
        }
    }
    //----------------------------------------------------------
    getCheatTeleportToLocation() {
        switch (botEnums_1.ScenarioType[botConf_1.default.scenarioTypeName]) {
            case botEnums_1.ScenarioType.InfiniteLoopTownAndOcean:
                return botConf_1.default.oceanRandomMoveCenterLocation;
            case botEnums_1.ScenarioType.InfiniteLoopReconnect:
                return botConf_1.default.oceanRandomMoveCenterLocation;
            case botEnums_1.ScenarioType.ChangeToOceanAndStayForever:
                return botConf_1.default.oceanRandomMoveCenterLocation;
            default:
                return botConf_1.default.oceanRandomMoveCenterLocation;
        }
    }
    //----------------------------------------------------------
    tick() {
        if (this._conn.isDisconnected()) {
            return;
        }
        try {
            switch (this.botMode) {
                case botEnums_1.BotMode.LOGIN_MODE:
                    this._tickLoginMode();
                    break;
                case botEnums_1.BotMode.SCENARIO_MODE:
                    this._tickScenarioMode();
                    break;
                default:
                    // invalid mode
                    break;
            }
        }
        catch (err) {
            mlog_1.default.error('tick exception', {
                userId: this._userId,
                err,
            });
            if (!this._conn.isDisconnected()) {
                this.close();
            }
            return;
        }
    }
    //----------------------------------------------------------
    ping() {
        var _a, _b;
        if ((_a = botConf_1.default.ping) === null || _a === void 0 ? void 0 : _a.disable) {
            return;
        }
        const curTime = mutil.curTimeUtc();
        const pingInterval = ((_b = botConf_1.default.ping) === null || _b === void 0 ? void 0 : _b.interval) || 60;
        if (this._lastPingTimeUtc + pingInterval < curTime) {
            this._lastPingTimeUtc = curTime;
            // mlog.info('sending ping', {
            //   userId: this._userId,
            // });
            //send ping
            reqSenders.sendPing(this);
        }
    }
    //----------------------------------------------------------
    // Login Mode Processes
    //----------------------------------------------------------
    _tickLoginMode() {
        //handle login mode packets
        this.handleLoginModePacket();
        switch (this.loginPhase) {
            case botEnums_1.LoginPhase.UNAUTHORIZED:
                this.tickOnUnauthorized();
                break;
            case botEnums_1.LoginPhase.AUTHRIZING:
                break;
            case botEnums_1.LoginPhase.CHECKING_ORDER:
                this.tickCheckingOrder();
                break;
            case botEnums_1.LoginPhase.AUTHORIZED:
                this.tickOnAuthorized();
                break;
            case botEnums_1.LoginPhase.LOGGING_IN:
                this.tickOnLoggingIn();
                break;
            case botEnums_1.LoginPhase.AFTER_LOGGED_IN:
                this.tickOnAfterLoggedIn();
                break;
            case botEnums_1.LoginPhase.MAP_LOADING:
                break;
            case botEnums_1.LoginPhase.MAP_LOADING_COMPLETE:
                // end of login mode. changing to scenario mode
                this.botMode = botEnums_1.BotMode.SCENARIO_MODE;
                break;
            default:
                break;
        }
    }
    //----------------------------------------------------------
    // botClient의 LoginMode용 패킷핸들러는 이곳에 추가.
    handleLoginModePacket() {
        const packet = this._conn.popPacket();
        if (!packet || !packet.type) {
            return;
        }
        if (!this.updateSyncLoginMode(packet)) {
            this.close();
            return;
        }
        switch (packet.type) {
            case proto.Auth.HELLO:
                this.onRecvHello(packet);
                break;
            case proto.Auth.ENTER_WORLD:
                this.onRecvEnterWorld(packet);
                break;
            case proto.Auth.CHANGE_NAME:
                this.onRecvSetName();
                break;
            case proto.Auth.COMPLETE_PROLOGUE:
                this.onRecvCompletePrologue();
                break;
            case proto.Common.CREATE_FIRST_MATE:
                this.onRecvSetFirstMate();
                break;
            case proto.Common.CHANGE_COMPANY_JOB:
                this.onRecvChangeCompanyJob(packet);
                break;
            case proto.Town.ENTER:
                this.onRecvTownEnter(packet);
                break;
            case proto.Town.LOAD_COMPLETE:
                this.onRecvTownLoadComplete();
                break;
            case proto.Town.ENTER_BUILDING:
                this.onRecvTownEnterBuilding(packet);
                break;
            case proto.Town.DEPART_DEPART:
                this.onRecvTownDepartDepart(packet);
                break;
            case proto.Ocean.ENTER:
                this.onRecvOceanEnter(packet);
                break;
            case proto.Ocean.LOAD_COMPLETE:
                this.onRecvOceanLoadComplete();
                break;
            default:
                // mlog.info('handlePacket unhandled packet', {
                //   userId: this._userId,
                //   packetType: packet.type,
                // });
                break;
        }
    }
    //----------------------------------------------------------
    // 항상 최신의 sync data를 업데이트 받아놓는다.
    updateSyncLoginMode(packet) {
        try {
            if (packet.body) {
                const resBody = JSON.parse(packet.body);
                if (resBody.sync && resBody.sync.add) {
                    const prevGameState = this.getLastGameState();
                    lodash_1.default.merge(this.syncAll, resBody.sync.add);
                    return true;
                }
                if (resBody.errCode) {
                    mlog_1.default.error('received error', {
                        errCode: resBody.errCode,
                        errMessage: resBody.errMessage ? resBody.errMessage : null,
                    });
                    return false;
                }
            }
            return true;
        }
        catch (err) {
            mlog_1.default.error('updateSyncLoginMode error', {
                userId: this._userId,
                err,
            });
            return false;
        }
    }
    //----------------------------------------------------------
    tickOnUnauthorized() {
        this.loginPhase = botEnums_1.LoginPhase.AUTHRIZING;
        mlog_1.default.info('requesting auth/getWorlds', { pubId: this.pubId, userName: this.userName });
        botLogic_1.botApiConnection
            .getWorlds({
            platform: enum_1.PLATFORM.DEV,
            gnidSessionToken: this.userName,
        })
            .then((res) => {
            if (res.worlds.length <= 0) {
                throw new Error('getWorlds - world list is empty');
            }
            this._conn.setLobbydAddr(res.worlds[0].address, res.worlds[0].port);
            mlog_1.default.info('received auth/getWorlds reply', {
                pubId: this.pubId,
                userName: this.userName,
            });
            return botLogic_1.botApiConnection.login({
                worldId: res.worlds[0].worldId,
                platform: enum_1.PLATFORM.DEV,
                revision: '0.0.1',
                sessionToken: this.userName,
            });
        })
            .then((res) => {
            mlog_1.default.info('received auth/login reply', { pubId: this.pubId, userName: this.userName });
            this.setEnterWorldToken(res.enterWorldToken);
            if (res.errorCd) {
                mlog_1.default.info('login error.. restarting login phase', {
                    pubId: this.pubId,
                    userName: this.userName,
                    errorCd: res.errorCd,
                });
                this.loginPhase = botEnums_1.LoginPhase.UNAUTHORIZED;
                return;
            }
            if (res.orderWait > 0) {
                this.loginPhase = botEnums_1.LoginPhase.CHECKING_ORDER;
            }
            else {
                this.loginPhase = botEnums_1.LoginPhase.AUTHORIZED;
            }
        })
            .catch((err) => {
            mlog_1.default.error(err);
            mlog_1.default.info('restarting login phase', {
                pubId: this.pubId,
                userName: this.userName,
            });
            this.loginPhase = botEnums_1.LoginPhase.UNAUTHORIZED;
        });
    }
    //----------------------------------------------------------
    tickCheckingOrder() {
        mlog_1.default.info('requesting auth/checkOrder', { pubId: this.pubId, userName: this.userName });
        botLogic_1.botApiConnection
            .checkOrder({
            checkKey: this.userName,
            enterWorldToken: this.enterWorldToken,
        })
            .then((res) => {
            mlog_1.default.info('received auth/checkOrder reply', {
                pubId: this.pubId,
                userName: this.userName,
                orderWait: res.orderWait,
            });
            if (res.errorCd) {
                mlog_1.default.info('checkOrder error.. restarting login phase', {
                    pubId: this.pubId,
                    userName: this.userName,
                    errorCd: res.errorCd,
                });
                this.loginPhase = botEnums_1.LoginPhase.UNAUTHORIZED;
                return;
            }
            if (res.enterWorldToken) {
                this.setEnterWorldToken(res.enterWorldToken);
            }
            if (res.orderWait === 0) {
                this.loginPhase = botEnums_1.LoginPhase.AUTHORIZED;
            }
        })
            .catch((err) => {
            mlog_1.default.error(err);
            mlog_1.default.info('restarting login phase', {
                pubId: this.pubId,
                userName: this.userName,
            });
            this.loginPhase = botEnums_1.LoginPhase.UNAUTHORIZED;
        });
    }
    //----------------------------------------------------------
    tickOnAuthorized() {
        this.loginPhase = botEnums_1.LoginPhase.LOGGING_IN;
        this._conn.reconnect();
    }
    //----------------------------------------------------------
    tickOnLoggingIn() {
        if (gameState_1.GAME_STATE.TOWN_MIN > this.getLastGameState()) {
            return;
        }
        mlog_1.default.info('logged-in', { pubId: this.pubId });
        this.loginPhase = botEnums_1.LoginPhase.AFTER_LOGGED_IN;
    }
    //----------------------------------------------------------
    // 로그인완료 시점. 이곳에서 필요한 분기를 시킨다.
    //----------------------------------------------------------
    tickOnAfterLoggedIn() {
        // [todo] act differently based on the game state
        if (this.isInTown()) {
            this.startTownEnterProcess();
        }
        else if (this.isInOcean()) {
            this.startOceanEnterProcess();
        }
    }
    //----------------------------------------------------------
    startTownEnterProcess() {
        this.loginPhase = botEnums_1.LoginPhase.MAP_LOADING;
        reqSenders.sendTownEnter(this);
    }
    //----------------------------------------------------------
    startOceanEnterProcess() {
        this.loginPhase = botEnums_1.LoginPhase.MAP_LOADING;
        reqSenders.sendOceanEnter(this);
    }
    //----------------------------------------------------------
    // login handlers
    //---------------------------------------------------
    onRecvHello(packet) {
        // First take the double quotes from both ends of the 'body'.
        const peerPublicKey = packet.body.replace(/"/g, '');
        this._conn.getCryptoCtx.computeSecret(Buffer.from(peerPublicKey, 'hex'));
        // Now send login.
        const body = {
            enterWorldToken: this.enterWorldToken,
            isDevLogin: 1,
            sessionToken: this.userName,
            lang: 'ko',
            reconnect: 0,
            deviceType: 'Windows',
            isTestBot: 1,
            lparam: '{}',
        };
        const loginPacket = {
            type: proto.Auth.ENTER_WORLD,
            seqNum: 0,
            body: JSON.stringify(body),
        };
        this._conn.sendJsonPacket(loginPacket, NaN);
    }
    //----------------------------------------------------------
    onRecvEnterWorld(packet) {
        const resBody = JSON.parse(packet.body);
        if (resBody.kickReason) {
            mlog_1.default.info('onRecvEnterWorld kicked from server', {
                userId: this._userId,
            });
            this.close();
            return;
        }
        const user = resBody.sync.add.user;
        this.pubId = user.pubId ? user.pubId : this.pubId;
        this._userId = user.userId ? user.userId : 0;
        this._conn.setUserId(this._userId);
        if (resBody.sailId) {
            this.setSailId(resBody.sailId);
        }
        mlog_1.default.info('onRecvEnterWorld', {
            pubId: this.pubId,
            userName: this.userName,
            userId: this._userId,
        });
        const gamestate = this.getLastGameState();
        switch (gamestate) {
            case gameState_1.GAME_STATE.ACCOUNT_CREATED:
                const body = {
                    name: this.userName,
                };
                const loginPacket = {
                    type: proto.Auth.CHANGE_NAME,
                    seqNum: 0,
                    body: JSON.stringify(body),
                };
                this._conn.sendJsonPacket(loginPacket, NaN);
                break;
            case gameState_1.GAME_STATE.NAME_SET:
                this.onRecvSetName();
                break;
            case gameState_1.GAME_STATE.TUTORIAL_COMPLETED:
                this.onRecvCompletePrologue();
                break;
            case gameState_1.GAME_STATE.FIRST_MATE_CREATED:
                if (0 == user.companyJobCmsId) {
                    this.onRecvSetFirstMate();
                }
                else {
                    //this.setState(State.CHANGE_COMPANY_JOB);
                }
                break;
            default:
                if (this.isInTown() || this.isInOcean()) {
                    this.loginPhase = botEnums_1.LoginPhase.AFTER_LOGGED_IN;
                    break;
                }
                mlog_1.default.info('onRecvEnterWorld unhandled gamestate', {
                    userId: this._userId,
                    gamestaste: user.gameState,
                });
                break;
        }
    }
    //----------------------------------------------------------
    onRecvSetName() {
        const body = {
            admiralCmsId: botConf_1.default.admiralCmsId,
        };
        const loginPacket = {
            type: proto.Auth.COMPLETE_PROLOGUE,
            seqNum: 0,
            body: JSON.stringify(body),
        };
        this._conn.sendJsonPacket(loginPacket, NaN);
    }
    //----------------------------------------------------------
    onRecvCompletePrologue() {
        const body = {
            admiralCmsId: botConf_1.default.admiralCmsId,
            //cNationPopRank: 0,
        };
        const loginPacket = {
            type: proto.Common.CREATE_FIRST_MATE,
            seqNum: 0,
            body: JSON.stringify(body),
        };
        this._conn.sendJsonPacket(loginPacket, NaN);
    }
    //----------------------------------------------------------
    onRecvSetFirstMate() {
        const body = {
            companyJobCmsId: botConf_1.default.companyJobCmsId,
        };
        const loginPacket = {
            type: proto.Common.CHANGE_COMPANY_JOB,
            seqNum: 0,
            body: JSON.stringify(body),
        };
        this._conn.sendJsonPacket(loginPacket, NaN);
    }
    //----------------------------------------------------------
    onRecvChangeCompanyJob(packet) {
        //this.setState(State.CHANGE_COMPANY_JOB);
    }
    //----------------------------------------------------------
    onRecvTownEnter(packet) {
        const body = JSON.parse(packet.body);
        if (body.errCode) {
            mlog_1.default.error('received error', {
                errCode: body.errCode,
                errMessage: body.errMessage ? body.errMessage : null,
            });
            this.close();
            return;
        }
        mlog_1.default.info('entered-town', { userId: this._userId });
        reqSenders.sendTownLoadComplete(this);
        this.curMoveStateIndex = 0;
    }
    //----------------------------------------------------------
    onRecvTownLoadComplete() {
        this.loginPhase = botEnums_1.LoginPhase.MAP_LOADING_COMPLETE;
        mlog_1.default.info('loadcomplete-town', { userId: this._userId });
        // const packet = {
        //   seqNum: 0,
        //   type: proto.Town.MOVE_CS,
        //   body: this.moveStates[this.curMoveStateIndex],
        // };
        // this._conn.sendJsonPacket(packet, NaN);
        // this.curMoveStateIndex++;
        // if (this.curMoveStateIndex >= this.moveStates.length) {
        //   this.curMoveStateIndex = 0;
        // }
        // [todo] prerequistic game state is IN_TOWN_BUILDING
        // const rand = Math.floor(Math.random() * 1000);
        // if (rand < botConst.probabilityOfDeparting) {
        //   mlog.info('try-to-depart', { userId: this._userId });
        //   // Discard all received packets.
        //   this.conn.clearPacketQueue();
        //   this.phase = Phase.DEPARTING;
        //   const body = {};
        //   const packet = {
        //     seqNum: 0,
        //     type: proto.Town.DEPART_DEPART,
        //     body: JSON.stringify(body),
        //   };
        //   this.conn.sendJsonPacket(packet, NaN);
        // }
    }
    //----------------------------------------------------------
    onRecvTownEnterBuilding(packet) {
        const body = JSON.parse(packet.body);
        if (body.errCode) {
            mlog_1.default.error('received error', {
                errCode: body.errCode,
                errMessage: body.errMessage ? body.errMessage : null,
            });
            this.close();
            return;
        }
        mlog_1.default.info('entered-town buillding', { userId: this._userId });
        const sendbody = {};
        const sendpacket = {
            seqNum: 0,
            type: proto.Town.DEPART_DEPART,
            body: JSON.stringify(sendbody),
        };
        this._conn.sendJsonPacket(sendpacket, NaN);
    }
    //----------------------------------------------------------
    onRecvTownDepartDepart(packet) {
        const body = JSON.parse(packet.body);
        if (body.errCode) {
            mlog_1.default.error('received error', {
                errCode: body.errCode,
                errMessage: body.errMessage ? body.errMessage : null,
            });
            this.close();
            return;
        }
        mlog_1.default.info('departed-town', { userId: this._userId });
        const sendbody = {};
        const sendpacket = {
            seqNum: 0,
            type: proto.Ocean.ENTER,
            body: JSON.stringify(sendbody),
        };
        this._conn.sendJsonPacket(sendpacket, NaN);
    }
    //----------------------------------------------------------
    onRecvOceanEnter(packet) {
        const body = JSON.parse(packet.body);
        if (body.errCode) {
            mlog_1.default.error('received error', {
                errCode: body.errCode,
                errMessage: body.errMessage ? body.errMessage : null,
            });
            this.close();
            return;
        }
        this.sailSpeed = body.sailSpeed * 0.2;
        this.oceanCurrentLocation = body.location;
        mlog_1.default.info('entered-ocean', { userId: this._userId, loc: body.location });
        reqSenders.sendOceanLoadComplete(this);
    }
    //----------------------------------------------------------
    onRecvOceanLoadComplete() {
        this.loginPhase = botEnums_1.LoginPhase.MAP_LOADING_COMPLETE;
        mlog_1.default.info('loadcomplete-ocean', { userId: this._userId });
    }
    //----------------------------------------------------------
    // Scenario Mode 함수들
    //----------------------------------------------------------
    _tickScenarioMode() {
        //handle scenario mode packets
        this.handleScenarioModePacket();
        this.tickOnPlaySecenario();
        this.ping();
    }
    //----------------------------------------------------------
    // botClient의 ScenarioMode용 패킷핸들러는 이곳에 추가.
    handleScenarioModePacket() {
        for (let k = 0; k < 5; k++) {
            const packet = this._conn.popPacket();
            if (!packet) {
                return;
            }
            if (!packet.type) {
                continue;
            }
            let ret = true;
            try {
                if (packet.body) {
                    const resBody = JSON.parse(packet.body);
                    if (!this.updateSyncScenarioMode(resBody)) {
                        this.close();
                        return;
                    }
                    // update latest request's state
                    if (resBody.errCode) {
                        ret = 0 == resBody.errCode ? true : false;
                    }
                }
            }
            catch (error) {
                mlog_1.default.error('handleScenarioModePacket failed', {
                    userId: this._userId,
                    error: error.message,
                });
                this.close();
                return;
            }
            this.updateBehaviorRequest(packet.type, ret);
            // add individual packet handlers here
            switch (packet.type) {
                case proto.Town.USER_MOVE_SC:
                    this.onRecvTownMove(packet);
                    break;
                case proto.Ocean.ARRIVE:
                    this.onRecvArrive(packet);
                    break;
                case proto.Ocean.ADD_DISASTER_SC:
                case proto.Ocean.REMOVE_DISASTER_SC:
                    this.onRecvUpdateDisaster(packet);
                    break;
                case proto.Ocean.ENCOUNT_BY_NPC_SC:
                    this.onRecvEncountByNpcSc(packet);
                    break;
                case proto.Ocean.ENCOUNT_BY_NET_USER_SC:
                    this.onRecvEncountByNetUser(packet);
                    break;
                case proto.Ocean.WRECK_FLEET_SC:
                    this.onRecvWreckFleetSc(packet);
                    break;
                case proto.Ocean.ENCOUNT_END:
                    this.onRecvEncountEnd(packet);
                    break;
                case proto.Battle.RESUME:
                    this.onRecvBattleResume(packet);
                    break;
                case proto.Battle.END:
                    this.onRecvBattleEnd(packet);
                    break;
                case proto.Ocean.GAME_OVER_RESURRECT:
                    this.onRecvOceanResurrect(packet);
                    break;
                case proto.Ocean.TELELPORT_TO_LOCATION_SC:
                    this.onRecvTeleportToLocation(packet);
                    break;
                case proto.Ocean.NET_USER_MOVE_SC:
                    this.onRecvNetUserMove(packet);
                    break;
                case proto.Common.SELECT_NATION:
                    this.onRecvSelectNation(packet);
                    break;
                case proto.Common.SET_SIDEKICK_MATE:
                    this.onRecvSetSidekickMate(packet);
                    break;
                case proto.Ocean.UPDATE_SAIL_SPEED_SC:
                    this.onRecvUpdateSailSpeedSc(packet);
                    break;
                default:
                    if (!bot_1.ResponseTypesNotToLog.includes(packet.type)) {
                        // mlog.info('handleScenarioModePacket unhandled packet', {
                        //   userId: this._userId,
                        //   packetType: packet.type,
                        //   typeStr: proto.toString(packet.type),
                        // });
                    }
                    break;
            }
        }
    }
    //----------------------------------------------------------
    // 항상 최신의 sync data를 업데이트 받아놓는다.
    updateSyncScenarioMode(resBody) {
        if (resBody.sync && resBody.sync.add) {
            lodash_1.default.merge(this.syncAll, resBody.sync.add);
            if (this.SyncAll.sailing && this.SyncAll.sailing.sailId) {
                this.setSailId(this.SyncAll.sailing.sailId);
            }
            return true;
        }
        if (resBody.errCode) {
            mlog_1.default.error('received error', {
                errCode: resBody.errCode,
                errMessage: resBody.errMessage ? resBody.errMessage : null,
            });
            return false;
        }
        return true;
    }
    //----------------------------------------------------------
    updateBehaviorRequest(packetType, isSuccess) {
        let requiredPacketTypes = this.curRequest.getRequiredPacketTypes();
        if (0 == requiredPacketTypes.length) {
            return;
        }
        let found = false;
        let k = 0;
        for (; k < requiredPacketTypes.length; k++) {
            if (packetType == requiredPacketTypes[k]) {
                found = true;
                break;
            }
        }
        if (!found) {
            return;
        }
        if (!isSuccess) {
            //anyone fails all fail
            this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Failure;
            mlog_1.default.error('failed request', { userId: this._userId, packetType });
        }
        else {
            //if last packet is success, it's request is complete
            if (k == requiredPacketTypes.length - 1) {
                this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Success;
            }
            else {
                //send next request packet
                const nextPacketType = requiredPacketTypes[k + 1];
                if (!nextPacketType) {
                    return;
                }
                const func = reqSenders.sendMap[nextPacketType];
                if (func) {
                    mlog_1.default.info('next request', {
                        userId: this._userId,
                        nextPacketType,
                        typeStr: proto.toString(nextPacketType),
                        func,
                    });
                    func(this);
                }
            }
        }
    }
    //----------------------------------------------------------
    tickOnPlaySecenario() {
        // [todo] act based on gamestate
        this.scenarioBehaviorTree.step();
        // mlog.info('tickOnPlaySecenario', {
        //   userId: this._userId,
        //   lastResult: this.scenarioBehaviorTree.lastResult,
        //   lastRundData: this.scenarioBehaviorTree.lastRundData,
        // });
        if (this.scenarioBehaviorTree.lastResult === behaviortree_1.SUCCESS ||
            this.scenarioBehaviorTree.lastResult === behaviortree_1.FAILURE) {
            this.curRequest.reset();
        }
        //console.log(this.scenarioBehaviorTree.lastRundData);
    }
    //----------------------------------------------------------
    onRecvArrive(packet) {
        //const body = JSON.parse(packet.body);
        mlog_1.default.info('arrived', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvUpdateDisaster(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('update-disaster', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvEncountByNpcSc(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-by-npc', { userId: this._userId });
        //set encout flag
        //this.isEncountOccurred = true;
    }
    //----------------------------------------------------------
    onRecvEncountByNetUser(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-by-user', { userId: this._userId });
        //set encout flag
        //this.isEncountOccurred = true;
    }
    //----------------------------------------------------------
    onRecvWreckFleetSc(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('WreckFleetSc', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvEncountEnd(packet) {
        const body = JSON.parse(packet.body);
        this._battleParam = body.battleParam;
        mlog_1.default.info('EncountEnd', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvBattleResume(packet) {
        const body = JSON.parse(packet.body);
        if (body.txnLog) {
            const battleParam = JSON.parse(body.txnLog[0]).battleParam;
            //this._battleParam = battleParam;
            mlog_1.default.info('BattleResume updated battleParam', { userId: this._userId });
        }
        mlog_1.default.info('BattleResume ok', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvBattleEnd(packet) {
        //const body: sync.Resp = JSON.parse(packet.body);
        const body = JSON.parse(packet.body);
        mlog_1.default.info('onRecvBattleEnd', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvOceanResurrect(packet) {
        const body = JSON.parse(packet.body);
        const location = body.location;
        this.oceanCurrentLocation = location;
        mlog_1.default.info('onRecvOceanResurrect', { userId: this._userId, location });
    }
    //----------------------------------------------------------
    onRecvTeleportToLocation(packet) {
        const body = JSON.parse(packet.body);
        this.oceanCurrentLocation = { latitude: body.latitude, longitude: body.longitude };
        mlog_1.default.info('onRecvTeleportToLocation', {
            userId: this._userId,
            location: this.oceanCurrentLocation,
        });
    }
    //----------------------------------------------------------
    onRecvTownMove(packet) {
        this._townMovePacketRecvCount++;
        if (this._townMovePacketRecvCount % 20 === 0) {
            mlog_1.default.info('onRecvTownMove', {
                userId: this._userId,
                townMovePacketRecvCount: this._townMovePacketRecvCount,
            });
        }
    }
    //----------------------------------------------------------
    onRecvNetUserMove(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('onRecvNetUserMove', {
            userId: this._userId,
            otherUserId: body.userId,
            location: body.location,
        });
    }
    //----------------------------------------------------------
    onRecvSelectNation(packet) {
        const body = JSON.parse(packet.body);
        if (body.bRestricted) {
            mlog_1.default.warn('onRecvSelectNation got restricted', { userId: this._userId });
            return;
        }
        mlog_1.default.info('onRecvSelectNation', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvSetSidekickMate(packet) {
        // 요청이 성공한 경우 설정과 해제를 번갈아가며 한다.
        this._isSidekickMateSet = !this._isSidekickMateSet;
        mlog_1.default.info('onRecvSetSidekickMate', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvUpdateSailSpeedSc(packet) {
        const body = JSON.parse(packet.body);
        this.sailSpeed = body.s * 0.2;
        mlog_1.default.info('onRecvUpdateSailSpeedSc', { userId: this._userId, sailSpeed: this.sailSpeed });
    }
    //----------------------------------------------------------
    calcMove(curTickInMs) {
        if (curTickInMs < this.lastTickTimeInMs + botConf_1.default.randomMoveTickInterval) {
            return {
                location: undefined,
                sailingSpeed: undefined,
                degrees: undefined,
            };
        }
        let elapsedSec;
        if (this.lastTickTimeInMs === 0) {
            elapsedSec = 1;
        }
        else {
            elapsedSec = (curTickInMs - this.lastTickTimeInMs) / 1000;
        }
        this.lastTickTimeInMs = curTickInMs;
        let sailingSpeed = this.sailSpeed;
        const remainingD = OC.CalcWorldDistance(this.oceanCurrentLocation, this.oceanTargetLoc);
        const degrees = OC.CalcWorldDegrees(this.oceanCurrentLocation, this.oceanTargetLoc);
        const movedDistance = sailingSpeed * elapsedSec;
        const npcUePos = OC.Location2Ue(this.oceanCurrentLocation);
        const updatedPosX = npcUePos.x + Math.cos((0, formula_1.toRadians)(degrees)) * movedDistance;
        const updatedPosY = npcUePos.y + Math.sin((0, formula_1.toRadians)(degrees)) * movedDistance;
        let updatedLocation = OC.Ue2LatLon(updatedPosX, updatedPosY);
        if (movedDistance >= remainingD) {
            updatedLocation = this.oceanTargetLoc;
            sailingSpeed = 0;
        }
        return {
            location: updatedLocation,
            sailingSpeed,
            degrees,
        };
    }
    //----------------------------------------------------------
    //----------------------------------------------------------
    // BehaviorTree 함수들
    //----------------------------------------------------------
    //----------------------------------------------------------
    _btWaitSet(count) {
        this.behaviorStat.waitCount = 0;
        this.behaviorStat.waitMax = count;
    }
    //----------------------------------------------------------
    btTickWait(count) {
        if (this.behaviorStat.waitCount < 0) {
            this._btWaitSet(count);
            return 0;
        }
        else {
            this.behaviorStat.waitCount++;
            if (this.behaviorStat.waitCount >= this.behaviorStat.waitMax) {
                this.behaviorStat.waitCount = -1;
                return 1;
            }
            return 0;
        }
    }
    //----------------------------------------------------------
    btTickWaitRandom(maxCount) {
        const val = Math.floor(Math.random() * maxCount) + 1;
        if (this.behaviorStat.waitCount < 0) {
            this._btWaitSet(val);
            return 0;
        }
        else {
            this.behaviorStat.waitCount++;
            if (this.behaviorStat.waitCount >= this.behaviorStat.waitMax) {
                this.behaviorStat.waitCount = -1;
                return 1;
            }
            return 0;
        }
    }
    //----------------------------------------------------------
    btIsInTown() {
        return this.isInTown();
    }
    //----------------------------------------------------------
    btIsInOcean() {
        return this.isInOcean();
    }
    //----------------------------------------------------------
    btIsInOceanLoading() {
        return (this.getLastGameState() === gameState_1.GAME_STATE.IN_OCEAN &&
            this.getGameEnterState() === gameState_1.GAME_ENTER_STATE.LOADING);
    }
    //----------------------------------------------------------
    btIsInOceanBattle() {
        mlog_1.default.info('btIsInOceanBattle', { userId: this._userId });
        return this.isInOceanBattle();
    }
    //----------------------------------------------------------
    // btIsNeedBattleResume() {
    //   mlog.info('btIsNeedBattleResume', { userId: this._userId });
    //   return this.isInOceanBattle(true) && !this._battleParam && this._battleId;
    // }
    //----------------------------------------------------------
    btIsWrecked() {
        return this.isWrecked();
    }
    //----------------------------------------------------------
    _btStartChangeToTown() {
        mlog_1.default.info('btStartChangeToTown', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([
            proto.Ocean.ARRIVE,
            proto.Town.ENTER,
            proto.Town.LOAD_COMPLETE,
        ]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.ChangeToTown;
        reqSenders.sendOceanArrive(this);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btChangeToTown() {
        if (botEnums_1.BehaviorActionType.ChangeToTown != this.curRequest.getActionType) {
            //init
            this._btStartChangeToTown();
            return botEnums_1.BehaviorRequestResultType.Pending;
        }
        else {
            //update
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btChangeToTown fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    _btStartChangeToOcean() {
        mlog_1.default.info('btStartChangeToOcean', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([
            proto.Town.ENTER_BUILDING,
            proto.Town.DEPART_DEPART,
            proto.Ocean.ENTER,
            proto.Ocean.LOAD_COMPLETE,
        ]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.ChangeToOcean;
        reqSenders.sendTownEnterBuilding(this);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btChangeToOcean() {
        if (botEnums_1.BehaviorActionType.ChangeToOcean != this.curRequest.getActionType) {
            //init
            return this._btStartChangeToOcean();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btChangeToOcean fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    _btStartMakeOceanLoadComplete() {
        mlog_1.default.info('_btStartMakeOceanLoadComplete', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Ocean.LOAD_COMPLETE]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.MakeOceanLoadComplete;
        reqSenders.sendOceanLoadComplete(this);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btMakeOceanLoadComplete() {
        if (botEnums_1.BehaviorActionType.MakeOceanLoadComplete != this.curRequest.getActionType) {
            //init
            return this._btStartMakeOceanLoadComplete();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btMakeOceanLoadComplete fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    _btStartCheatTeleportTown() {
        mlog_1.default.info('_btStartCheatTeleportTown', { userId: this._userId });
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.CheatTeleportTown;
        this.curRequest.setRequiredPacketTypes([proto.Admin.TELEPORT_TOWN]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        reqSenders.sendCheatTeleportTown(this, this._nextDestinationTownCmsId);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btCheatTeleportTown() {
        if (botEnums_1.BehaviorActionType.CheatTeleportTown != this.curRequest.getActionType) {
            //init
            return this._btStartCheatTeleportTown();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btCheatTeleportTown fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    _btStartCheatTeleportToLocation() {
        mlog_1.default.info('_btStartCheatTeleportToLocation', { userId: this._userId });
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.CheatTeleportToLocation;
        this.curRequest.setRequiredPacketTypes([
            proto.Dev.TELEPORT_TO_LOCATION_CS,
            proto.Ocean.TELELPORT_TO_LOCATION_SC,
        ]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        reqSenders.sendCheatTeleportToLocation(this, this.getCheatTeleportToLocation());
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btCheatTeleportToLocation() {
        if (botEnums_1.BehaviorActionType.CheatTeleportToLocation != this.curRequest.getActionType) {
            //init
            return this._btStartCheatTeleportToLocation();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btCheatTeleportToLocation fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    btReconnect() {
        mlog_1.default.info('btReconnect', { userId: this._userId });
        this.reconnect();
    }
    //----------------------------------------------------------
    btIsMoveStateStopped() {
        mlog_1.default.info('btIsMoveStateStopped', { userId: this._userId });
        return botEnums_1.BotMoveState.MoveStopped === this.getMoveState();
    }
    //----------------------------------------------------------
    btSetOceanRandomMovePos() {
        const centerLoc = botConf_1.default.oceanRandomMoveCenterLocation;
        const range = botConf_1.default.randomMoveRange;
        const vec = gl_matrix_1.vec2.random(gl_matrix_1.vec2.create(), Math.random() * range);
        const spawnAreaUe = OC.LatLon2Ue(centerLoc.latitude, centerLoc.longitude);
        this.oceanTargetLoc = OC.Ue2LatLon(vec[0] + spawnAreaUe.x, vec[1] + spawnAreaUe.y);
        mlog_1.default.info('SetOceanRandomMovePos', {
            userId: this._userId,
            curLoc: this.oceanCurrentLocation,
            targetLoc: this.oceanTargetLoc,
        });
        // 특정한 지점을 목적지로 설정한다.
        return botEnums_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    _btStartOceanMoveToTargetPos() {
        mlog_1.default.info('_btStartOceanMoveToTargetPos', {
            userId: this._userId,
            curLoc: this.oceanCurrentLocation,
            targetLoc: this.oceanTargetLoc,
        });
        this.setMoveState(botEnums_1.BotMoveState.Moving);
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.OceanMoveToTargetPos;
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btOceanMoveToTargetPos() {
        if (botEnums_1.BehaviorActionType.OceanMoveToTargetPos != this.curRequest.getActionType) {
            //init
            return this._btStartOceanMoveToTargetPos();
        }
        else {
            // 이동가능 상태 체크
            if (!this.isAbleToOceanMove()) {
                return botEnums_1.BehaviorRequestResultType.Failure;
            }
            const curTimeInMs = Date.now();
            const segmentResult = this.calcMove(curTimeInMs);
            if (!segmentResult.location) {
                return botEnums_1.BehaviorRequestResultType.Pending;
            }
            const newLocation = segmentResult.location;
            const degrees = segmentResult.degrees;
            this.oceanCurrentLocation = newLocation;
            const packetBody = {
                latitude: newLocation.latitude,
                longitude: newLocation.longitude,
                speed: segmentResult.sailingSpeed,
                degrees,
                t: Math.floor(curTimeInMs) - 1,
            };
            reqSenders.sendOceanMove(this, packetBody);
            // 목표지점에 도달했는지 체크
            if (!OC.isLocationCloseEnough(newLocation, this.oceanTargetLoc)) {
                return botEnums_1.BehaviorRequestResultType.Pending;
            }
            this.setMoveState(botEnums_1.BotMoveState.MoveStopped);
            mlog_1.default.info('btOceanMoveToTargetPos fin', { userId: this._userId });
            return botEnums_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    _btStartResurrect() {
        mlog_1.default.info('_btStartResurrect', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Ocean.GAME_OVER_RESURRECT]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.Resurrect;
        reqSenders.sendOceanGameOverResurrect(this);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btResurrect() {
        if (botEnums_1.BehaviorActionType.Resurrect != this.curRequest.getActionType) {
            //init
            return this._btStartResurrect();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btResurrect fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    btIsScenarioInitiated() {
        return this._isScenarioInitiated;
    }
    //----------------------------------------------------------
    btSetScenarioInitiated() {
        this._isScenarioInitiated = true;
        mlog_1.default.info('btSetScenarioInitiated fin', { userId: this._userId });
        return botEnums_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    btIsPointDucatSufficient() {
        const curPoint = this.getPoint(cmsEx.DucatPointCmsId);
        const minDucat = this.calcScenarioMinDucat();
        if (curPoint > minDucat) {
            return true;
        }
        return false;
    }
    //----------------------------------------------------------
    _btStartCheatSetPointDucat() {
        mlog_1.default.info('_btStartCheatSetPointDucat', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Dev.SET_POINT]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.CheatSetPointDucat;
        const ducat = this.calcScenarioMinDucat() * botConst.CheatSetPointDucatRate;
        reqSenders.sendSetPoint(this, cmsEx.DucatPointCmsId, ducat);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btCheatSetPointDucat() {
        if (botEnums_1.BehaviorActionType.CheatSetPointDucat != this.curRequest.getActionType) {
            return this._btStartCheatSetPointDucat();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btCheatSetPointDucat fin', { userId: this._userId });
            return ret;
        }
    }
    //----------------------------------------------------------
    btIsPointBluegemSufficient() {
        const curPoint = this.getPoint(cmsEx.BlueGemPointCmsId);
        const minBluegemAmount = parseInt(botConf_1.default.point.minBluegemAmount, 10);
        if (curPoint >= minBluegemAmount) {
            return true;
        }
        mlog_1.default.info('btIsPointBluegemSufficient false', {
            userId: this._userId,
            curPoint,
            minBluegemAmount,
        });
        return false;
    }
    //----------------------------------------------------------
    _btStartCheatSetPointBluegem() {
        mlog_1.default.info('_btStartCheatSetPointBluegem', { userId: this._userId });
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.CheatSetPointBluegem;
        this.curRequest.setRequiredPacketTypes([proto.Dev.SET_POINT]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        reqSenders.sendSetPoint(this, cmsEx.BlueGemPointCmsId, botConf_1.default.point.bluegemAmount);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btCheatSetPointBluegem() {
        if (botEnums_1.BehaviorActionType.CheatSetPointBluegem != this.curRequest.getActionType) {
            return this._btStartCheatSetPointBluegem();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btCheatSetPointBluegem fin', { userId: this._userId });
            return ret;
        }
    }
    //----------------------------------------------------------
    // 항해사 추가 치트키 호출
    _btStartCheatAddMate() {
        mlog_1.default.info('_btStartCheatAddMate', { userId: this._userId });
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.CheatAddMate;
        this.curRequest.setRequiredPacketTypes([proto.Dev.ADD_MATE]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        reqSenders.sendAddMate(this, botConf_1.default.needMateCmsId);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btCheatAddMate() {
        if (botEnums_1.BehaviorActionType.CheatAddMate != this.curRequest.getActionType) {
            return this._btStartCheatAddMate();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btCheatAddMate fin', { userId: this._userId });
            return ret;
        }
    }
    //----------------------------------------------------------
    btSetNextDestinationTown() {
        for (let k = 0; k < botConf_1.default.destinationTownCmsIds.length; k++) {
            if (this._nextDestinationTownCmsId !== botConf_1.default.destinationTownCmsIds[k]) {
                this._nextDestinationTownCmsId = botConf_1.default.destinationTownCmsIds[k];
                break;
            }
        }
        mlog_1.default.info('btSetNextDestinationTown fin', {
            userId: this._userId,
            nextDestinationTown: this._nextDestinationTownCmsId,
        });
        return botEnums_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    btSetNextInvestDestTown() {
        const size = botLogic_1.investableTownCmsIds.length;
        if (this._nextDestinationTownCmsId === 0) {
            // 순회 시작할 마을을 선택
            const curTownCmsId = this.getCurTownCmsId();
            let idx = 0;
            if (curTownCmsId) {
                idx = botLogic_1.investableTownCmsIds.findIndex((cmsId) => cmsId === curTownCmsId);
                if (idx === -1) {
                    // 대상 마을중에 있지않은 경우는 대상마을중 하나로 셋팅
                    idx = this._userId % size;
                    // mlog.info('btSetNextInvestDestTown step1', {
                    //   userId: this._userId,
                    //   idx,
                    // });
                }
                else {
                    // 현재 위치가 대상리스트중에 하나인 경우는 다음 인덱스에서 시작
                    idx = ++idx % size;
                    // mlog.info('btSetNextInvestDestTown step2', {
                    //   userId: this._userId,
                    //   idx,
                    // });
                }
            }
            else {
                // 최초에는 userId에 따른 위치에서 시작
                idx = this._userId % size;
                // mlog.info('btSetNextInvestDestTown step3', {
                //   userId: this._userId,
                //   idx,
                // });
            }
            this._nextDestinationTownCmsId = botLogic_1.investableTownCmsIds[idx];
        }
        else {
            // 현재 마을의 다음 마을로 이동
            let idx = botLogic_1.investableTownCmsIds.findIndex((cmsId) => cmsId === this._nextDestinationTownCmsId);
            if (idx === -1) {
                mlog_1.default.error('btSetNextInvestDestTown failed..cant find current cmsId', {
                    userId: this._userId,
                    nextDestinationTown: this._nextDestinationTownCmsId,
                });
                return botEnums_1.BehaviorRequestResultType.Failure;
            }
            idx = ++idx % size;
            // mlog.info('btSetNextInvestDestTown step4', {
            //   userId: this._userId,
            //   idx,
            // });
            this._nextDestinationTownCmsId = botLogic_1.investableTownCmsIds[idx];
        }
        // [TEMP] DEBUG
        //this._nextDestinationTownCmsId = 11000009;
        mlog_1.default.info('btSetNextInvestDestTown fin', {
            userId: this._userId,
            nextDestinationTown: this._nextDestinationTownCmsId,
        });
        return botEnums_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    _bStarttBeginAutoSailing() {
        mlog_1.default.info('_bStarttBeginAutoSailing', { userId: this._userId });
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.BeginAutoSailing;
        this.curRequest.setRequiredPacketTypes([proto.Ocean.BEGIN_AUTO_SAILING]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        const destPath = botConf_1.default.autoSailingPaths[this._nextDestinationTownCmsId];
        if (!destPath) {
            return botEnums_1.BehaviorRequestResultType.Failure;
        }
        reqSenders.sendBeginAutoSailing(this, this._nextDestinationTownCmsId, lobby_1.AUTO_SAIL_DEST_TYPE.TOWN, destPath);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btBeginAutoSailing() {
        if (botEnums_1.BehaviorActionType.BeginAutoSailing != this.curRequest.getActionType) {
            return this._bStarttBeginAutoSailing();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btBeginAutoSailing fin', { userId: this._userId });
            return ret;
        }
    }
    //----------------------------------------------------------
    btLogout() {
        mlog_1.default.info('btLogout fin', {
            userId: this._userId,
            reconnectWaitTimeUtc: botConf_1.default.reconnectWaitTimeSec,
        });
        //접속을 종료시키므로 더이상 behaviorTree 스텝도 진행되지 않는다.
        this.close();
        return botEnums_1.BehaviorRequestResultType.Failure;
    }
    //----------------------------------------------------------
    btIsInDestTown() {
        const curTownCmsId = this.getCurTownCmsId();
        if (curTownCmsId === this._nextDestinationTownCmsId) {
            return true;
        }
        mlog_1.default.info('btIsInDestTown false', {
            userId: this._userId,
            curTownCmsId,
            nextDestinationTownCmsId: this._nextDestinationTownCmsId,
        });
        return false;
    }
    //----------------------------------------------------------
    btIsInDestBuilding() {
        const destinationBuildingType = botConf_1.default.destinationBuildingType.toString();
        if (this.isInTownBuilding(ex_1.BUILDING_TYPE[destinationBuildingType])) {
            return true;
        }
        mlog_1.default.info('btIsInDestBuilding false', {
            userId: this._userId,
            destinationBuildingType,
        });
        return false;
    }
    //----------------------------------------------------------
    _btStartEnterDestBuilding() {
        mlog_1.default.info('_btStartEnterDestBuilding', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Town.ENTER_BUILDING]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.EnterBuilding;
        reqSenders.sendTownEnterBuilding(this);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btEnterDestBuilding() {
        if (botEnums_1.BehaviorActionType.EnterBuilding != this.curRequest.getActionType) {
            //init
            return this._btStartEnterDestBuilding();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btEnterDestBuilding fin', { userId: this._userId, ret });
            return ret;
        }
    }
    calcGoverInvestPoint(townCmsId) {
        if (botConf_1.default.applyIvestPoint) {
            return parseInt(botConf_1.default.applyIvestPoint);
        }
        // 스트레스 테스트 환경이 아닌 경우 실제 유저와 같은 로직으로 계산
        const constCms = cms_1.default.Const;
        const myInvestmentAccumPoint = this.getTownInvestmentAccumPoint(townCmsId);
        const minMax = formula.GetInvestableMinMaxPoint(constCms.InvestVal1.value, constCms.InvestVal2.value, constCms.InvestVal3.value, constCms.InvestMaxVal.value, constCms.InvestMinLimit.value, myInvestmentAccumPoint);
        mlog_1.default.info('calcGoverInvestPoint', {
            userId: this._userId,
            myInvestmentAccumPoint,
            min: minMax.min,
            max: minMax.max,
        });
        if (minMax) {
            return Math.floor((minMax.min + minMax.max) / 2);
        }
        return 0;
    }
    //----------------------------------------------------------
    _btStartGoverGetSyncData() {
        mlog_1.default.info('_btStartGoverGetSyncData', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Town.GOVER_GET_SYNC_DATA]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.GoverGetSyncData;
        reqSenders.sendGoverGetSyncData(this);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btGoverGetSyncData() {
        if (botEnums_1.BehaviorActionType.GoverGetSyncData != this.curRequest.getActionType) {
            //init
            return this._btStartGoverGetSyncData();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btGoverGetSyncData fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    _btStartGoverInvest() {
        mlog_1.default.info('_btStartGoverInvest', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Town.GOVER_INVEST]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.GoverInvest;
        const investType = mutil.randIntInc(ex_1.DEVELOPMENT_TYPE.industry, ex_1.DEVELOPMENT_TYPE.max - 1);
        const investPoint = this.calcGoverInvestPoint(this._nextDestinationTownCmsId);
        if (!investPoint) {
            mlog_1.default.info('_btStartGoverInvest investPoint calc failed', {
                userId: this._userId,
                DestinationTownCmsId: this._nextDestinationTownCmsId,
            });
            return botEnums_1.BehaviorRequestResultType.Failure;
        }
        reqSenders.sendGoverInvest(this, investType, investPoint);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btGoverInvest() {
        if (botEnums_1.BehaviorActionType.GoverInvest != this.curRequest.getActionType) {
            //init
            return this._btStartGoverInvest();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btGoverInvest fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    btIsNationSelected() {
        if (this.getNationCmsId()) {
            return true;
        }
        mlog_1.default.info('btIsNationSelected false', {
            userId: this._userId,
        });
        return false;
    }
    //----------------------------------------------------------
    _btStartSelectNation() {
        mlog_1.default.info('_btStartSelectNation', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Common.SELECT_NATION]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.SelectNation;
        //const choiceIdx = mutil.randIntInc(0, selectableNationCmsIds.length - 1);
        const size = botLogic_1.selectableNationCmsIds.length;
        const choiceIdx = this._userId % size;
        const nationCmsId = botLogic_1.selectableNationCmsIds[choiceIdx];
        reqSenders.sendSelectNation(this, nationCmsId);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btSelectNation() {
        if (botEnums_1.BehaviorActionType.SelectNation != this.curRequest.getActionType) {
            //init
            return this._btStartSelectNation();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btSelectNation fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    _btStartSelectProbableNation() {
        mlog_1.default.info('_btStartSelectProbableNation', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Common.SELECT_NATION]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.SelectProbableNation;
        // 가장 비중이 작은 국가를 선택한다
        const nationCmsId = this.getProbableNation();
        if (!nationCmsId) {
            mlog_1.default.warn('[_btStartSelectProbableNation] getProbableNation failed', {
                userId: this._userId,
            });
            return botEnums_1.BehaviorRequestResultType.Failure;
        }
        reqSenders.sendSelectNation(this, nationCmsId);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btSelectProbableNation() {
        if (botEnums_1.BehaviorActionType.SelectProbableNation != this.curRequest.getActionType) {
            //init
            return this._btStartSelectProbableNation();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btSelectProbableNation fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    btIsShipNeededToRepair() {
        if (!this.isShipNeededToRepair()) {
            return false;
        }
        mlog_1.default.info('btIsShipNeededToRepair', {
            userId: this._userId,
        });
        return true;
    }
    //----------------------------------------------------------
    _btRepairShipsInTown() {
        mlog_1.default.info('_btRepairShipsInTown', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Town.SHIPYARD_REPAIR]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.RepairShipsInTown;
        const shipIds = this.getShipsNeededToRepair();
        const curTownCmsId = this.getCurTownCmsId();
        if (!curTownCmsId || !shipIds) {
            mlog_1.default.info('_btRepairShipsInTown failed', { userId: this._userId });
            return botEnums_1.BehaviorRequestResultType.Failure;
        }
        reqSenders.sendShipyardRepair(this, curTownCmsId, shipIds);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btRepairShipsInTown() {
        if (botEnums_1.BehaviorActionType.RepairShipsInTown != this.curRequest.getActionType) {
            //init
            return this._btRepairShipsInTown();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btRepairShipsInTown fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    btIsShipNeededSailor() {
        if (!this.isShipNeededSailor()) {
            return false;
        }
        mlog_1.default.info('btIsShipNeededSailor', {
            userId: this._userId,
        });
        return true;
    }
    //----------------------------------------------------------
    _btDraftSailorInTown() {
        mlog_1.default.info('_btRepairShipsInTown', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Town.PUB_DRAFT_SAILOR]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.DraftSailorInTown;
        const shipIds = this.getShipsNeededSailor();
        const curTownCmsId = this.getCurTownCmsId();
        if (!curTownCmsId || !shipIds) {
            mlog_1.default.info('_btDraftSailorInTown failed', { userId: this._userId });
            return botEnums_1.BehaviorRequestResultType.Failure;
        }
        reqSenders.sendDraftSailorInTown(this, curTownCmsId, shipIds);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btDraftSailorInTown() {
        if (botEnums_1.BehaviorActionType.DraftSailorInTown != this.curRequest.getActionType) {
            //init
            return this._btDraftSailorInTown();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btDraftSailorInTown fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    _btStartTownRandomMove() {
        mlog_1.default.info('_btStartTownRandomMove', {
            userId: this._userId,
            curMoveStateIndex: this.curMoveStateIndex,
        });
        this.setMoveState(botEnums_1.BotMoveState.Moving);
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.TownRandomMove;
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btTownRandomMove() {
        if (botEnums_1.BehaviorActionType.TownRandomMove != this.curRequest.getActionType) {
            //init
            return this._btStartTownRandomMove();
        }
        else {
            // 이동가능 상태 체크
            if (!this.isInTown()) {
                return botEnums_1.BehaviorRequestResultType.Failure;
            }
            const curTimeInMs = Date.now();
            if (curTimeInMs < this.lastTickTimeInMs + botConf_1.default.randomMoveTickInterval) {
                return botEnums_1.BehaviorRequestResultType.Pending;
            }
            this.lastTickTimeInMs = curTimeInMs;
            reqSenders.sendTownMove(this, this.moveStates[this.curMoveStateIndex]);
            this.curMoveStateIndex++;
            if (this.curMoveStateIndex >= this.moveStates.length) {
                this.curMoveStateIndex = 0;
            }
            // 현재 무한 이동
            return botEnums_1.BehaviorRequestResultType.Pending;
            // this.setMoveState(BotMoveState.MoveStopped);
            // mlog.info('btTownRandomMove fin', { userId: this._userId });
            // return BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    btIsNeedMateExist() {
        if (this.isNeedMateExist()) {
            return true;
        }
        mlog_1.default.info('btIsNeedMateExist false', {
            userId: this._userId,
        });
        return false;
    }
    //----------------------------------------------------------
    btCheatAddNeedMate() {
        // needMateCmsId 항해사가 없으면 추가
        reqSenders.sendAddMate(this, botConf_1.default.needMateCmsId);
        return botEnums_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    _btStartTownInfiniteSetSidekickMate(mateCmsId) {
        mlog_1.default.info('_btStartTownInfiniteSetSidekickMate', { userId: this._userId });
        this.curRequest.setRequiredPacketTypes([proto.Common.SET_SIDEKICK_MATE]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.TownSetSidekickMate;
        reqSenders.sendSetSidekickMate(this, 1, mateCmsId);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    // 수행항해사의 설정/해제를 무한반복한다.
    btTownInfiniteSetSidekickMate() {
        if (botEnums_1.BehaviorActionType.TownSetSidekickMate != this.curRequest.getActionType) {
            //init
            const mateCmsId = this._determineSidekickMateSet();
            return this._btStartTownInfiniteSetSidekickMate(mateCmsId);
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            // 무한 반복
            const curTimeInMs = Date.now();
            if (curTimeInMs <
                this._lastSidekickSetTickTimeInMs + botConf_1.default.infiniteSetSidekickMateTickInterval) {
                return botEnums_1.BehaviorRequestResultType.Pending;
            }
            this._lastSidekickSetTickTimeInMs = curTimeInMs;
            mlog_1.default.info('btTownInfiniteSetSidekickMate repeating..', {
                userId: this._userId,
                ret,
            });
            // 현재 설정상태체크
            const mateCmsId = this._determineSidekickMateSet();
            return this._btStartTownInfiniteSetSidekickMate(mateCmsId);
        }
    }
    _determineSidekickMateSet() {
        if (this._isSidekickMateSet) {
            // 설정상태이면 해제한다.
            return 0;
        }
        else {
            // 설정상태가 아니면 설정한다.
            return botConf_1.default.needMateCmsId;
        }
    }
    //----------------------------------------------------------
    _btStartCheatEasylanguage() {
        mlog_1.default.info('_btStartCheatEasylanguage', { userId: this._userId });
        this.curRequest.setActionType = botEnums_1.BehaviorActionType.CheatEasylanguage;
        this.curRequest.setRequiredPacketTypes([proto.Dev.EASY_LANGUAGE]);
        this.curRequest.setResult = botEnums_1.BehaviorRequestResultType.Pending;
        const townCms = cms_1.default.Town[this._nextDestinationTownCmsId];
        if (!townCms) {
            mlog_1.default.error('_btStartCheatEasylanguage invalid nextDestinationTownCmsId', {
                userId: this._userId,
                nextDestinationTownCmsId: this._nextDestinationTownCmsId,
            });
            return botEnums_1.BehaviorRequestResultType.Failure;
        }
        if (townCms.languageId.length === 0) {
            mlog_1.default.info('_btStartCheatEasylanguage fin languageId empty', { userId: this._userId });
            return botEnums_1.BehaviorRequestResultType.Success;
        }
        reqSenders.sendCheatEasylanguage(this, botConst.BuildingContentsTermsLanguageLevel);
        return botEnums_1.BehaviorRequestResultType.Pending;
    }
    //----------------------------------------------------------
    btCheatEasylanguage() {
        if (botEnums_1.BehaviorActionType.CheatEasylanguage != this.curRequest.getActionType) {
            //init
            return this._btStartCheatEasylanguage();
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (botEnums_1.BehaviorRequestResultType.Success !== ret) {
                return ret;
            }
            mlog_1.default.info('btCheatEasylanguage fin', { userId: this._userId, ret });
            return ret;
        }
    }
    //----------------------------------------------------------
    btWaitForInvestClose() {
        const townInvestCloseTime = botUtils_1.BotUtil.getTownInvestCloseTime(botLogic_1.layoutData);
        if (townInvestCloseTime !== -1) {
            const d = new Date();
            const curMin = d.getMinutes();
            const startTime = botUtils_1.BotUtil.normalizeMinutes(townInvestCloseTime - 1);
            const endTime = botUtils_1.BotUtil.normalizeMinutes(townInvestCloseTime + 1);
            if (botUtils_1.BotUtil.isInBetweenMinutes(curMin, startTime, endTime)) {
                //mlog.info('[TEMP] btWaitForInvestClose pending', { userId: this._userId });
                return botEnums_1.BehaviorRequestResultType.Pending;
            }
        }
        mlog_1.default.info('btWaitForInvestClose fin', { userId: this._userId });
        return botEnums_1.BehaviorRequestResultType.Success;
    }
}
exports.BotClient = BotClient;
exports.default = BotClient;
//# sourceMappingURL=botClient.js.map