"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.State = exports.Const = void 0;
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
const net = __importStar(require("net"));
//import * as Queue from 'fifo';
const queue_fifo_1 = __importDefault(require("queue-fifo"));
const botCryptoContext_1 = __importDefault(require("./botCryptoContext"));
const proto = __importStar(require("../proto/lobby/proto"));
const mutil = __importStar(require("../motiflib/mutil"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const bot_1 = require("../motiflib/model/bot");
const lobby_1 = require("../motiflib/model/lobby");
const csNet_1 = require("../proto/csNet");
// ----------------------------------------------------------------------------
// Constants.
// ----------------------------------------------------------------------------
var Const;
(function (Const) {
    Const[Const["HeaderLen"] = 4] = "HeaderLen";
    Const[Const["RecvBufSize"] = 32768] = "RecvBufSize";
    Const[Const["BinaryPacketHeadLen"] = 4] = "BinaryPacketHeadLen";
    Const[Const["BinaryPayloadFlags"] = 4] = "BinaryPayloadFlags";
    Const[Const["TownMoveTxPacketSize"] = 20] = "TownMoveTxPacketSize";
    Const[Const["TownMoveRxPacketSize"] = 24] = "TownMoveRxPacketSize";
    Const[Const["OceanMoveTxPacketSize"] = 36] = "OceanMoveTxPacketSize";
    Const[Const["OceanNetUserMoveRxPacketSize"] = 32] = "OceanNetUserMoveRxPacketSize";
})(Const = exports.Const || (exports.Const = {}));
var State;
(function (State) {
    State[State["INITIALIZED"] = 0] = "INITIALIZED";
    State[State["CONNECTING"] = 1] = "CONNECTING";
    State[State["CONNECTED"] = 2] = "CONNECTED";
    State[State["EXCHANGING_KEYS"] = 3] = "EXCHANGING_KEYS";
    State[State["LOGGING_IN"] = 4] = "LOGGING_IN";
    State[State["LOGGED_IN"] = 5] = "LOGGED_IN";
    State[State["USER_NAME_SET"] = 6] = "USER_NAME_SET";
    State[State["FIRST_MATE_SET"] = 7] = "FIRST_MATE_SET";
    State[State["CHANGE_COMPANY_JOB"] = 8] = "CHANGE_COMPANY_JOB";
    State[State["DISCONNECTED"] = 10] = "DISCONNECTED";
    State[State["ERROR"] = 100] = "ERROR";
})(State = exports.State || (exports.State = {}));
// ----------------------------------------------------------------------------
// BotConnection object.
// ----------------------------------------------------------------------------
class BotConnection {
    constructor(inPubId, userName) {
        this._userId = 0;
        // User.
        this.pubId = inPubId;
        this.userName = userName;
        // Socket.
        this.sock = null;
        this.recvBuf = Buffer.allocUnsafe(Const.RecvBufSize);
        this.bytesReceived = 0;
        this.setLobbydAddr('', 0);
        // Encryption.
        this.cryptoCtx = new botCryptoContext_1.default();
        // State.
        this.state = State.INITIALIZED;
        // Received packet queue.
        this.packetQueue = new queue_fifo_1.default();
    }
    get getCryptoCtx() {
        return this.cryptoCtx;
    }
    getUserId() {
        return this._userId;
    }
    setUserId(value) {
        this._userId = value;
    }
    connect() {
        if (this.sock) {
            return;
        }
        this.sock = net.connect({ host: this.lobbydHost, port: this.lobbydPort }, () => {
            mlog_1.default.verbose('connected to lobbyd', {
                pubId: this.pubId,
            });
            this.setState(State.CONNECTED);
            this.onConnected();
            this.sock.on('data', (data) => {
                mlog_1.default.debug('SOCK EVT data', { pubId: this.pubId });
                this.onRecv(data);
            });
            this.sock.on('end', () => {
                mlog_1.default.verbose('SOCK EVT end', { pubId: this.pubId });
                this.disconnect();
            });
            this.sock.on('close', () => {
                mlog_1.default.verbose('SOCK EVT close', { pubId: this.pubId });
                this.disconnect();
            });
        });
        this.sock.on('error', (err) => {
            mlog_1.default.verbose('SOCK EVT error', { pubId: this.pubId, error: err.message });
            this.disconnect();
        });
    }
    setLobbydAddr(addr, port) {
        this.lobbydHost = addr;
        this.lobbydPort = port;
    }
    disconnect() {
        if (this.state === State.DISCONNECTED) {
            return;
        }
        mlog_1.default.verbose('bot-con disconnect');
        if (this.sock) {
            this.sock.destroy();
            this.sock = null;
        }
        this.setState(State.DISCONNECTED);
    }
    reconnect() {
        return Promise.resolve()
            .then(() => {
            this.disconnect();
            this.state = State.INITIALIZED;
            return this.connect();
        })
            .catch((err) => {
            mlog_1.default.error('reconnect failed..', {
                userId: this._userId,
                message: err.message,
            });
        });
    }
    isLoginProcessCompleted() {
        return this.state === State.CHANGE_COMPANY_JOB;
    }
    isDisconnected() {
        return this.state === State.DISCONNECTED;
    }
    clearPacketQueue() {
        this.packetQueue.clear();
    }
    popPacket() {
        return this.packetQueue.dequeue();
    }
    sendJsonPacket(packet, payloadFlags) {
        if (mutil.isNotANumber(payloadFlags)) {
            payloadFlags = lobby_1.PayloadFlag.Compress | lobby_1.PayloadFlag.Encrypt;
        }
        const packetJson = JSON.stringify(packet);
        const packetBuf = Buffer.from(packetJson, 'utf-8');
        if (proto.Etc.PING_CS !== packet.type) {
            mlog_1.default.info('sending packet', {
                userId: this._userId,
                packetType: packet.type,
                typeStr: proto.toString(packet.type),
                packetJson,
            });
        }
        return Promise.resolve()
            .then(() => {
            if (payloadFlags & lobby_1.PayloadFlag.Compress) {
                return mutil.compress(packetBuf);
            }
            return packetBuf;
        })
            .then((buf) => {
            if (payloadFlags & lobby_1.PayloadFlag.Encrypt) {
                return this.cryptoCtx.encrypt(buf);
            }
            return buf;
        })
            .then((buf) => {
            const packetSize = buf.byteLength;
            const sendBuf = Buffer.alloc(Const.HeaderLen + packetSize);
            csNet_1.CsNet.writePacketHeader(sendBuf, packetSize, payloadFlags);
            sendBuf.fill(buf, Const.HeaderLen);
            return this.sock.write(sendBuf);
        })
            .catch((err) => {
            mlog_1.default.error('failed to send', {
                error: err.message,
                packetType: packet.type,
            });
            if (!this.isDisconnected()) {
                this.setState(State.ERROR);
            }
        });
    }
    sendBinaryPacket(packetType, bufBody) {
        return Promise.resolve()
            .then(() => {
            const packetSize = bufBody.byteLength;
            const sendBuf = Buffer.alloc(Const.HeaderLen + packetSize);
            csNet_1.CsNet.writePacketHeader(sendBuf, packetSize, Const.BinaryPayloadFlags);
            sendBuf.fill(bufBody, Const.HeaderLen);
            return this.sock.write(sendBuf);
        })
            .catch((err) => {
            mlog_1.default.error('failed to send', {
                error: err.message,
                packetType,
            });
            if (!this.isDisconnected()) {
                this.setState(State.ERROR);
            }
        });
    }
    onConnected() {
        // Send hello.
        this.setState(State.EXCHANGING_KEYS);
        this.cryptoCtx.init();
        const packet = {
            type: proto.Auth.HELLO,
            seqNum: 0,
            body: JSON.stringify({
                key: this.cryptoCtx.publicKey,
            }),
        };
        this.sendJsonPacket(packet, 0);
    }
    // private onRecv(data) {
    //   // Old code.
    //   // const payload = this.readPayload(data);
    //   // if (!payload) {
    //   //   return;
    //   // }
    //   // return this.processPayload(payload);
    //   const payloads = this.readPayloads(data);
    //   if (payloads.length === 0) {
    //     return;
    //   }
    //   return (Promise as any).reduce(
    //     payloads,
    //     (_accum, payload) => {
    //       return this.processPayload(payload);
    //     },
    //     null
    //   );
    // }
    onRecv(data) {
        // Old code.
        // const payload = this.readPayload(data);
        // if (!payload) {
        //   return;
        // }
        // return this.processPayload(payload);
        const payloads = this.readPayloads(data);
        if (payloads.length === 0) {
            return;
        }
        payloads.forEach((payload) => {
            return this.processPayload(payload);
        });
    }
    processPayload(payload) {
        return this.parsePacket(payload)
            .then((packet) => {
            if (!packet) {
                // 현재 binary packet들은 처리안하고 있음
                return;
            }
            if (!bot_1.ResponseTypesNotToLog.includes(packet.type)) {
                mlog_1.default.info('packet received', {
                    userId: this._userId,
                    // packet,
                    typeStr: proto.toString(packet.type),
                });
            }
            return this.packetQueue.enqueue(packet);
            /////////////////////////////
            // below codes moved to botClient
        })
            .catch((err) => {
            mlog_1.default.error('onRecv failed', {
                pubId: this.pubId,
                userId: this._userId,
                error: err.message,
            });
            console.error(err.stack);
            this.disconnect();
        });
    }
    calcFreeRecvBufSize() {
        const totalSize = this.recvBuf.length;
        if (this.bytesReceived >= totalSize) {
            return 0;
        }
        return totalSize - this.bytesReceived;
    }
    readPayloads(buf) {
        const freeSize = this.calcFreeRecvBufSize();
        if (freeSize < buf.byteLength) {
            mlog_1.default.error('readPayload recvBuff overflow', {
                userId: this._userId,
                incommingBytesLength: buf.byteLength,
                freeSize,
            });
            this.disconnect();
            return [];
        }
        // First copy everything into the recv buffer.
        buf.copy(this.recvBuf, this.bytesReceived, 0, buf.byteLength);
        this.bytesReceived += buf.byteLength;
        // Read as many payloads as we can.
        const payloads = [];
        while (true) {
            if (this.bytesReceived < Const.HeaderLen) {
                // Need to read more.
                break;
            }
            const { payloadSize, payloadFlags } = csNet_1.CsNet.readPacketHeader(this.recvBuf);
            if (this.bytesReceived < Const.HeaderLen + payloadSize) {
                // Need to read more.
                break;
            }
            // We have enough to read to make a packet.
            // Copy the payload to separate buffer,
            // and discard the data read from the recv buffer.
            const numBytesToDiscard = Const.HeaderLen + payloadSize;
            const numBytesRemaining = this.bytesReceived - numBytesToDiscard;
            const payloadBuffer = Buffer.alloc(payloadSize);
            this.recvBuf.copy(payloadBuffer, 0, Const.HeaderLen, numBytesToDiscard);
            this.recvBuf.copy(this.recvBuf, 0, numBytesToDiscard, numBytesToDiscard + numBytesRemaining);
            this.bytesReceived = numBytesRemaining;
            // mlog.debug('[TEMP] readPayload', {
            //   pubId: this.pubId,
            //   bytesReceived: this.bytesReceived,
            // });
            // payloads.push(Buffer.from(payloadBuffer.buffer, 4));
            // const offset = (payloadFlags & PayloadFlag.Compress) ? 4 : 0;
            const payload = {
                flags: payloadFlags,
                buffer: null,
            };
            if (payloadFlags & lobby_1.PayloadFlag.Compress) {
                // + 4 for 'uncompressed size' of the payload.
                payload.buffer = Buffer.from(payloadBuffer.buffer, 4);
            }
            else {
                payload.buffer = payloadBuffer;
            }
            payloads.push(payload);
        }
        return payloads;
    }
    parsePacket(payload) {
        const flags = payload.flags;
        const payloadBuf = payload.buffer;
        return Promise.resolve()
            .then(() => {
            // Decrypt if necessary.
            if (flags & lobby_1.PayloadFlag.Encrypt) {
                return this.cryptoCtx.decrypt(payloadBuf);
            }
            return payloadBuf;
        })
            .then((buf) => {
            // Unzip.
            if (flags & lobby_1.PayloadFlag.Compress) {
                return mutil.uncompress(buf);
            }
            return buf;
        })
            .then((buf) => {
            // Return parsed packet.
            if (flags & lobby_1.PayloadFlag.Binary) {
                return this.parseBinaryPacket(buf);
            }
            return JSON.parse(buf);
        });
    }
    parseBinaryPacket(buf) {
        const type = buf.readInt32LE(0);
        if (proto.Town.USER_MOVE_SC === type) {
            return parseMoveInTown(buf);
        }
        else {
            // const packetBody = PacketFactory.Create(type);
            // const bodyBuf = buf.subarray(Const.BinaryPacketHeadLen);
            // const bodyReader = SmartBuffer.fromBuffer(bodyBuf);
            // packetBody.deserialize(bodyReader);
            // const packet = {
            //   seqNum: 0,
            //   type,
            //   body: packetBody,
            // };
            // mlog.info('[TEMP] parsed binary packet', { typeStr: proto.toString(type) });
            return null;
        }
    }
    setState(newState) {
        // mlog.debug('state change', {
        //   oldState: this.state,
        //   newState,
        // });
        this.state = newState;
    }
}
function parseMoveInTown(buf) {
    let pos = 4;
    const userId = buf.readInt32LE(pos);
    pos += 4;
    const x = buf.readInt32LE(pos);
    pos += 4;
    const y = buf.readInt32LE(pos);
    pos += 4;
    const degrees = buf.readInt32LE(pos);
    pos += 4;
    const speed = buf.readInt32LE(pos);
    const packet = {
        seqNum: 0,
        type: proto.Town.USER_MOVE_SC,
        userId,
        x,
        y,
        degrees,
        speed,
    };
    // mlog.verbose('received move-in-town - ', JSON.stringify(packet));
    return packet;
}
exports.default = BotConnection;
//# sourceMappingURL=botConnection.js.map