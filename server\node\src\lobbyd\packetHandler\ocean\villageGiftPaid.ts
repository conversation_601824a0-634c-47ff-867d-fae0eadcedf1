// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import { Container } from 'typedi';
import _, { min } from 'lodash';

import * as mutil from '../../../motiflib/mutil';
import * as cmsEx from '../../../cms/ex';
import { LobbyService } from '../../server';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import cms from '../../../cms';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { CashPayment, PointChange } from '../../userPoints';
import Ship, { ShipCargoChange, ShipCargoNub } from '../../ship';
import { Sync } from '../../type/sync';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import * as formula from '../../../formula';
import tuVillageGift from '../../../mysqllib/txn/tuVillageGift';
import { ClientPacketHandler } from '../index';
import { BuffSync } from '../../userBuffs';
import { ItemChange } from '../../userInven';
import { AccumulateParam } from '../../userAchievement';

/**
 * 마을 우호도 유료 회복. 
 * ! 티켓 우선 소모 -> 블루젬 소모. Ducat은 villageGift.ts 
 */

const rsn = 'village_gift_paid';
const add_rsn = null;

interface RequestBody {
  villageCmsId: number;
  bTicket?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Ocean_VillageGift_Paid implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { villageCmsId, bTicket } = body;

    // ! 참고 : villageGift.ts
    // ! 여기는 우호도 전용 티켓 과 블루젬으로 올려주는 기능
    // 데이터 체크
    const villageCms = cms.Village[villageCmsId];
    if (!villageCms) {
      throw new MError('invalid-village-cms-id', MErrorCode.INVALID_REQ_BODY_VILLAGE_GIFT, {
        body,
      });
    }

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureGameState(GAME_STATE.IN_OCEAN_VILLAGE);
    const fleetStat = user.companyStat.getFleetStat(cmsEx.FirstFleetIndex);

    // 우호도 100 이하이면  동작 불가
    const conditionFriendship: number = user.userVillage.getCurVillageFriendship();
    if (!user.userVillage.canProcessVillage(conditionFriendship)) {
      throw new MError(
        'process-village-enough-friendship',
        MErrorCode.NOT_ENOUGH_VILLAGE_FRIENDSHIP,
        {
          friendship: conditionFriendship,
        }
      );
    }

    // 우호도 grade 체크
    const village = user.userVillage.getVillage(villageCmsId);
    const currentFriendship = village.friendship; // 현재 우호도
    const villageFriendshipCms = cmsEx.getVillageFriendshipCms(conditionFriendship);
    if (!villageFriendshipCms) {
      throw new MError('invalid-villagefriendship-cms-id', MErrorCode.INVALID_REQ_BODY_VILLAGE_GIFT, {
        body,
      });
    }
    // 음 grade 체크 하드코딩인데..
    if (villageFriendshipCms.grade >= 6 
      || currentFriendship >= cms.Const.VillageFriendshipMaxValue.value) {
        throw new MError('max-grade-villagefriendship', MErrorCode.INVALID_REQ_BODY_VILLAGE_GIFT, {
        body,
      });
    }

    let itemChanges: ItemChange[] = [];
    let pointChanges: PointChange[];
    let cashPayments: CashPayment[];

    if (bTicket) {
      // 1. 티켓 소모먼저 체크
      const itemCms = cms.Item[cms.Const.BuyFriendshipPriceItemId.value];
      if (!itemCms) {
        throw new MError('invalid-item-cms-id', MErrorCode.NO_KEY_IN_CMS, {
          itemCmsId: cms.Const.BuyFriendshipPriceItemId.value,
        });
      }
      const itemChange = user.userInven.itemInven.buildItemChange(itemCms.id, -1, true);
      itemChanges.push(itemChange);

    } else {
      // 1. 블루젬 소모 및 체크
      const gemCost = cms.Const.BuyFriendshipPricePointDefault.value;
      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [{ cmsId: cmsEx.BlueGemPointCmsId, cost: gemCost }],
        true, // 나중에 혹시 문제되면 bPermitChange request.body에서 받도록 처리 필요.
        { itemId: rsn },
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;
    }

    // 2. 우호도 max
    const maximumFriendship = cms.Const.VillageFriendshipMaxValue.value; // 티켓이나 블루젬으로 max까지 올리자.

    const buffSync: BuffSync = {
      sync: {},
    };
    const accums: AccumulateParam[] = [];

    return user.userPoints
      .tryConsumeCashs(cashPayments, buffSync.sync, user, { user, rsn, add_rsn })
      .then(() => {
        const { userDbConnPoolMgr } = Container.get(LobbyService);
        return tuVillageGift(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          maximumFriendship,
          maximumFriendship,
          villageCmsId,
          pointChanges,
          [], // 빈 배열로 명시.
          itemChanges
        );
      })
      .then(() => {
        user.userVillage.updateCurVillageFriendship(maximumFriendship);
        _.merge<Sync, Sync>(buffSync.sync, {
          add: {
            villages: {
              [villageCmsId]: {
                friendship: maximumFriendship,
                maximumFriendship: maximumFriendship,
              },
            },
          },
        });

        if (pointChanges) {
          _.merge<Sync, Sync>(
            buffSync.sync,
            user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
          );
        }

        if (itemChanges) {
          for (const item of itemChanges) {
            _.merge<Sync, Sync>(
              buffSync.sync,
              user.userInven.itemInven.applyItemChange(item, accums, { user, rsn, add_rsn })
            );
          }
        }

        const villageFriendshipCms = cmsEx.getVillageFriendshipCms(village.friendship);
        user.glog('village_friendship', {
          rsn,
          add_rsn,
          village_id: villageCmsId,
          village_name: villageCms.name,
          cv: maximumFriendship - currentFriendship, // 우호도 변동 수치
          rv: maximumFriendship,
          friendship_type: villageFriendshipCms ? villageFriendshipCms.name : null,
        });

        return user.sendJsonPacket(packet.seqNum, packet.type, buffSync);
      });
  }
}
