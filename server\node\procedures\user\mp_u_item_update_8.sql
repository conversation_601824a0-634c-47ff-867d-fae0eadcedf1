CREATE PROCEDURE `mp_u_item_update_8`(
  IN inUserId INT,
  IN inCmsId0 INT, IN inCount0 INT, IN inUnboundCount0 INT,
  IN inCmsId1 INT, IN inCount1 INT, IN inUnboundCount1 INT,
  IN inCmsId2 INT, IN inCount2 INT, IN inUnboundCount2 INT,
  IN inCmsId3 INT, IN inCount3 INT, IN inUnboundCount3 INT,
  IN inCmsId4 INT, IN inCount4 INT, IN inUnboundCount4 INT,
  IN inCmsId5 INT, IN inCount5 INT, IN inUnboundCount5 INT,
  IN inCmsId6 INT, IN inCount6 INT, IN inUnboundCount6 INT,
  IN inCmsId7 INT, IN inCount7 INT, IN inUnboundCount7 INT
)
label_body:BEGIN

  IF inCmsId0 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId0,
      inCount0,
      inUnboundCount0
    )
    ON DUPLICATE KEY UPDATE count = inCount0, unboundCount = inUnboundCount0;
  END IF;

  IF inCmsId1 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId1,
      inCount1,
      inUnboundCount1
    )
    ON DUPLICATE KEY UPDATE count = inCount1, unboundCount = inUnboundCount1;
  END IF;

  IF inCmsId2 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId2,
      inCount2,
      inUnboundCount2
    )
    ON DUPLICATE KEY UPDATE count = inCount2, unboundCount = inUnboundCount2;
  END IF;

  IF inCmsId3 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId3,
      inCount3,
      inUnboundCount3
    )
    ON DUPLICATE KEY UPDATE count = inCount3, unboundCount = inUnboundCount3;
  END IF;

  IF inCmsId4 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId4,
      inCount4,
      inUnboundCount4
    )
    ON DUPLICATE KEY UPDATE count = inCount4, unboundCount = inUnboundCount4;
  END IF;

  IF inCmsId5 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId5,
      inCount5,
      inUnboundCount5
    )
    ON DUPLICATE KEY UPDATE count = inCount5, unboundCount = inUnboundCount5;
  END IF;

  IF inCmsId6 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId6,
      inCount6,
      inUnboundCount6
    )
    ON DUPLICATE KEY UPDATE count = inCount6, unboundCount = inUnboundCount6;
  END IF;

  IF inCmsId7 IS NOT NULL THEN
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES
    (
      inUserId,
      inCmsId7,
      inCount7,
      inUnboundCount7
    )
    ON DUPLICATE KEY UPDATE count = inCount7, unboundCount = inUnboundCount7;
  END IF;

  SELECT ROW_COUNT();
END