import { RedisManager } from './redisManager';
import { RedisKeyInfo, RemapData, RedisUpdateResult, BatchOptions } from '../types';

/**
 * Redis 업데이트 엔진
 */
export class RedisUpdateEngine {
  private redisManager: RedisManager;
  private batchOptions: BatchOptions;
  private currentWorldId?: string;

  constructor(redisManager: RedisManager, batchOptions: BatchOptions = {
    batchSize: 50,
    delayBetweenBatches: 500,
    maxRetries: 3,
    retryDelay: 2000,
  }) {
    this.redisManager = redisManager;
    this.batchOptions = batchOptions;
  }

  /**
   * 모든 Redis 키 업데이트
   */
  async updateAllKeys(
    redisKeys: RedisKeyInfo[],
    remapData: RemapData[],
    worldId?: string,
    dryRun: boolean = false
  ): Promise<RedisUpdateResult[]> {
    const results: RedisUpdateResult[] = [];

    // 월드별 필터링
    const filteredRemapData = worldId
      ? remapData.filter(data => data.gameServerId === worldId)
      : remapData;

    if (filteredRemapData.length === 0) {
      console.log('업데이트할 Redis 데이터가 없습니다.');
      return results;
    }

    console.log(`${filteredRemapData.length}개 레코드로 ${redisKeys.length}개 Redis 키 패턴 업데이트 시작`);

    // Redis 인스턴스별로 그룹화
    const keysByInstance = this.groupKeysByInstance(redisKeys);

    // worldId가 없는 경우 모든 월드에 대해 처리
    if (!worldId) {
      const worldIds = this.getAvailableWorldIds(remapData);

      for (const currentWorldId of worldIds) {
        this.currentWorldId = currentWorldId;
        const worldFilteredData = remapData.filter(data => data.gameServerId === currentWorldId);

        console.log(`Redis 월드별 처리: ${currentWorldId} (${worldFilteredData.length}개 레코드)`);

        for (const [instanceName, keys] of keysByInstance) {
          for (const keyInfo of keys) {
            const result = dryRun
              ? await this.simulateKeyPatternUpdate(keyInfo, worldFilteredData, instanceName)
              : await this.updateKeyPattern(keyInfo, worldFilteredData, instanceName);
            results.push(result);
          }
        }
      }
    } else {
      // worldId가 지정된 경우 기존 로직 사용
      if (filteredRemapData.length > 0 && filteredRemapData[0]?.gameServerId) {
        this.currentWorldId = filteredRemapData[0].gameServerId;
      }

      for (const [instanceName, keys] of keysByInstance) {
        for (const keyInfo of keys) {
          const result = dryRun
            ? await this.simulateKeyPatternUpdate(keyInfo, filteredRemapData, instanceName)
            : await this.updateKeyPattern(keyInfo, filteredRemapData, instanceName);
          results.push(result);
        }
      }
    }

    const totalUpdated = results.reduce((sum, r) => sum + r.updatedKeys, 0);
    console.log(`Redis 업데이트 완료: 총 ${totalUpdated}개 키 업데이트`);

    return results;
  }

  /**
   * 리맵 데이터에서 사용 가능한 월드 ID 목록 추출
   */
  private getAvailableWorldIds(remapData: RemapData[]): string[] {
    const worldIds = new Set<string>();
    remapData.forEach(data => {
      if (data.gameServerId) {
        worldIds.add(data.gameServerId);
      }
    });
    return Array.from(worldIds);
  }

  /**
   * 키를 인스턴스별로 그룹화
   */
  private groupKeysByInstance(redisKeys: RedisKeyInfo[]): Map<string, RedisKeyInfo[]> {
    const grouped = new Map<string, RedisKeyInfo[]>();

    for (const keyInfo of redisKeys) {
      const instanceName = keyInfo.redisInstance;
      if (!grouped.has(instanceName)) {
        grouped.set(instanceName, []);
      }
      grouped.get(instanceName)!.push(keyInfo);
    }

    return grouped;
  }

  /**
   * 특정 키 패턴 업데이트
   */
  private async updateKeyPattern(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<RedisUpdateResult> {
    const startTime = Date.now();

    try {
      let updatedKeys = 0;

      // AccountId/Gnid 관련 키 업데이트
      if (keyInfo.usesAccountId || keyInfo.usesGnid) {
        const accountUpdates = await this.updateAccountIdKeys(keyInfo, remapData, instanceName);
        updatedKeys += accountUpdates;
      }

      // PubId/Nid 관련 키 업데이트
      if (keyInfo.usesPubId || keyInfo.usesNid) {
        const pubIdUpdates = await this.updatePubIdKeys(keyInfo, remapData, instanceName);
        updatedKeys += pubIdUpdates;
      }

      // UserId 관련 키 업데이트 (필요한 경우)
      if (keyInfo.usesUserId) {
        const userIdUpdates = await this.updateUserIdKeys(keyInfo, remapData, instanceName);
        updatedKeys += userIdUpdates;
      }

      // 정적 분석을 통한 키 타입 결정
      const keyType = this.getStaticKeyType(keyInfo, updatedKeys);

      return {
        keyPattern: keyInfo.keyPattern,
        updatedKeys,
        success: true,
        executionTime: Date.now() - startTime,
        keyType: keyType,
        instanceName: instanceName,
      };
    } catch (error) {
      // 에러가 발생해도 정의된 키 타입 사용
      const keyType = this.getStaticKeyType(keyInfo, 0);

      return {
        keyPattern: keyInfo.keyPattern,
        updatedKeys: 0,
        success: false,
        error: String(error),
        executionTime: Date.now() - startTime,
        keyType: keyType,
        instanceName: instanceName,
      };
    }
  }

  /**
   * AccountId 관련 키 업데이트
   */
  private async updateAccountIdKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    // 특별한 키 패턴 처리
    if (keyInfo.keyPattern === 'prologueGnids:{worldId}') {
      return await this.updatePrologueGnidsKeys(remapData, instanceName);
    }

    for (const data of remapData) {
      // 기존 키 패턴에서 실제 키 찾기
      const oldKeys = await this.findKeysWithAccountId(keyInfo.keyPattern, data.uwo_Gnid, instanceName);

      for (const oldKey of oldKeys) {
        const newKey = this.replaceAccountIdInKey(oldKey, data.uwo_Gnid, data.uwogl_Gnid);

        if (oldKey !== newKey) {
          const success = await this.moveKey(oldKey, newKey, instanceName);
          if (success) {
            updatedCount++;
          }
        }
      }
    }

    return updatedCount;
  }

  /**
   * PubId 관련 키 업데이트
   */
  private async updatePubIdKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    // 특별한 키 패턴 처리
    if (keyInfo.keyPattern === 'deletionPubIds') {
      return await this.updateDeletionPubIdsKey(remapData, instanceName);
    }

    if (keyInfo.keyPattern === 'userIdsByName') {
      return await this.updateUserIdsByNameKey(remapData, instanceName);
    }

    // user:{userId} 키의 특별 처리 (Hash 내부 pubId 필드 값 변경)
    if (keyInfo.keyPattern === 'user:{userId}') {
      return await this.updateUserHashKeys(keyInfo, remapData, instanceName);
    }

    // Hash 필드로 userId를 사용하는 키들 처리
    if (this.isHashFieldKey(keyInfo.keyPattern)) {
      return await this.updateHashFieldKeys(keyInfo, remapData, instanceName);
    }

    // Set/Sorted Set 멤버로 userId를 사용하는 키들 처리
    if (this.isSetMemberKey(keyInfo.keyPattern)) {
      return await this.updateSetMemberKeys(keyInfo, remapData, instanceName);
    }

    for (const data of remapData) {
      // 기존 키 패턴에서 실제 키 찾기
      const oldKeys = await this.findKeysWithPubId(keyInfo.keyPattern, data.uwo_Nid, instanceName);

      for (const oldKey of oldKeys) {
        const newKey = this.replacePubIdInKey(oldKey, data.uwo_Nid, data.uwogl_Nid);

        if (oldKey !== newKey) {
          const success = await this.moveKey(oldKey, newKey, instanceName);
          if (success) {
            updatedCount++;
          }
        }
      }
    }

    return updatedCount;
  }

  /**
   * UserId 관련 키 업데이트
   */
  private async updateUserIdKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    // UserId는 일반적으로 AccountId와 동일하게 처리
    return await this.updateAccountIdKeys(keyInfo, remapData, instanceName);
  }

  /**
   * 키 패턴 업데이트 시뮬레이션 (드라이런용)
   */
  private async simulateKeyPatternUpdate(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<RedisUpdateResult> {
    const startTime = Date.now();

    console.log(`[DEBUG] 시뮬레이션 시작: ${keyInfo.keyPattern} (인스턴스: ${instanceName})`);

    try {
      let keyCount = 0;

      // AccountId/Gnid 관련 키 카운트
      if (keyInfo.usesAccountId || keyInfo.usesGnid) {
        console.log(`[DEBUG] AccountId/Gnid 키 패턴 처리: ${keyInfo.keyPattern}`);
        for (const data of remapData) {
          const keys = await this.findKeysWithAccountId(keyInfo.keyPattern, data.uwo_Gnid, instanceName);
          keyCount += keys.length;
          console.log(`[DEBUG] AccountId ${data.uwo_Gnid} -> ${keys.length}개 키 발견`);
        }
      }

      // PubId/Nid 관련 키 카운트
      if (keyInfo.usesPubId || keyInfo.usesNid) {
        console.log(`[DEBUG] PubId/Nid 키 패턴 처리: ${keyInfo.keyPattern}`);
        for (const data of remapData) {
          const keys = await this.findKeysWithPubId(keyInfo.keyPattern, data.uwo_Nid, instanceName);
          keyCount += keys.length;
          console.log(`[DEBUG] PubId ${data.uwo_Nid} -> ${keys.length}개 키 발견`);
        }
      }

      // 정적 분석을 통한 키 타입 결정
      const keyType = this.getStaticKeyType(keyInfo, keyCount);

      console.log(`[DEBUG] 시뮬레이션 완료: ${keyInfo.keyPattern} -> 총 ${keyCount}개 키 (타입: ${keyType})`);

      return {
        keyPattern: keyInfo.keyPattern,
        updatedKeys: keyCount,
        success: true,
        executionTime: Date.now() - startTime,
        keyType: keyType,
        instanceName: instanceName,
      };
    } catch (error) {
      console.error(`[DEBUG] 시뮬레이션 오류: ${keyInfo.keyPattern}`, error);
      return {
        keyPattern: keyInfo.keyPattern,
        updatedKeys: 0,
        success: false,
        error: String(error),
        executionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Redis 인스턴스 이름을 실제 클라이언트 이름으로 변환
   */
  private resolveRedisInstanceName(instanceName: string, worldId?: string): string {
    // 공유 인스턴스들 (월드 ID 없음)
    const sharedInstances = ['auth', 'monitor', 'order', 'globalMatch', 'globalBattleLog'];

    if (sharedInstances.includes(instanceName)) {
      return instanceName;
    }

    // 이미 월드 ID가 포함된 인스턴스 이름인지 확인
    if (instanceName.includes('-')) {
      // 이미 완전한 인스턴스 이름 (예: 'UWO-GL-01-userCache')
      return instanceName;
    }

    // 월드별 인스턴스들 (월드 ID 필요)
    if (worldId) {
      return `${worldId}-${instanceName}`;
    }

    // 현재 처리 중인 월드 ID 사용
    if (this.currentWorldId) {
      return `${this.currentWorldId}-${instanceName}`;
    }

    // 기본 월드 ID 사용 (첫 번째 월드)
    const defaultWorldId = this.redisManager.getWorldIds()[0];
    return `${defaultWorldId}-${instanceName}`;
  }

  /**
   * 정적 분석을 통한 키 타입 결정
   */
  private getStaticKeyType(keyInfo: RedisKeyInfo, keyCount: number): string {
    // 이미 분석된 타입이 있으면 사용 (정의된 패턴에서 온 경우)
    if (keyInfo.keyType && keyInfo.keyType !== 'unknown') {
      return keyInfo.keyType;
    }

    // 키가 없으면 'none' 반환 (정의된 타입이 없는 경우만)
    if (keyCount === 0) {
      return 'none';
    }

    // 정적 분석을 통한 타입 추론
    return this.inferKeyTypeFromPattern(keyInfo.keyPattern);
  }

  /**
   * 키 패턴을 기반으로 타입 추론
   */
  private inferKeyTypeFromPattern(keyPattern: string): string {
    // 코드베이스 분석 결과를 기반으로 한 정적 타입 매핑
    const staticTypes: Record<string, string> = {
      // Hash 타입 - HSET, HGET, HMSET, HMGET 명령어 사용 패턴
      'account:{accountId}': 'hash',
      'user:{userId}': 'hash',
      'guildMember:{guildId}': 'hash',
      'pickedDailyRewardIdx:{guildId}': 'hash',
      'nationCabinet:{nationCmsId}:{sessionId}': 'hash',
      'userIdsByName': 'hash',
      'inProgress': 'hash',

      // Sorted Set 타입 - ZADD, ZREM, ZSCORE 명령어 사용 패턴
      'prologueGnids:{worldId}': 'zset',

      // List 타입 - RPUSH, LRANGE, LREM 명령어 사용 패턴
      'deletionPubIds': 'list',

      // String 타입 - SET 명령어 사용 패턴
      'townUserWeeklyInvestmentReport:{nid}': 'string',
      'eu:{userId}': 'string'
    };

    return staticTypes[keyPattern] || 'unknown';
  }

  /**
   * AccountId가 포함된 키 찾기 (타입 정보 포함)
   */
  private async findKeysWithAccountId(
    keyPattern: string,
    accountId: string,
    instanceName: string,
    worldId?: string
  ): Promise<string[]> {
    // 특별한 키 패턴 처리
    if (keyPattern === 'prologueGnids:{worldId}') {
      return await this.findPrologueGnidsKeys(accountId, instanceName);
    }

    // 인스턴스 이름을 실제 클라이언트 이름으로 변환 (월드 ID 전달)
    const resolvedInstanceName = this.resolveRedisInstanceName(instanceName, worldId);

    // 키 패턴을 실제 검색 패턴으로 변환
    const searchPattern = this.createSearchPattern(keyPattern, accountId, 'account');
    const keys = await this.redisManager.findKeys(resolvedInstanceName, searchPattern);

    console.log(`AccountId 키 검색: ${keyPattern} -> ${searchPattern} -> ${keys.length}개 키 발견`);

    // 정확한 키만 반환 (패턴이 정확히 매치되는 경우)
    if (searchPattern === `account:${accountId}`) {
      return keys.filter(key => key === `account:${accountId}`);
    }

    return keys;
  }

  /**
   * PubId가 포함된 키 찾기
   */
  private async findKeysWithPubId(
    keyPattern: string,
    pubId: string,
    instanceName: string,
    worldId?: string
  ): Promise<string[]> {
    // 특별한 키 패턴 처리
    if (keyPattern === 'deletionPubIds') {
      return await this.findDeletionPubIdsKeys(pubId, instanceName);
    }

    if (keyPattern === 'userIdsByName') {
      return await this.findUserIdsByNameKeys(pubId, instanceName);
    }

    // 인스턴스 이름을 실제 클라이언트 이름으로 변환 (월드 ID 전달)
    const resolvedInstanceName = this.resolveRedisInstanceName(instanceName, worldId);

    // 키 패턴을 실제 검색 패턴으로 변환
    const searchPattern = this.createSearchPattern(keyPattern, pubId, 'pub');
    const keys = await this.redisManager.findKeys(resolvedInstanceName, searchPattern);

    console.log(`PubId 키 검색: ${keyPattern} -> ${searchPattern} -> ${keys.length}개 키 발견`);

    // 정확한 키만 반환 (패턴이 정확히 매치되는 경우)
    if (searchPattern === `user:${pubId}` || searchPattern === `eu:${pubId}`) {
      return keys.filter(key => key === searchPattern);
    }

    return keys;
  }

  /**
   * prologueGnids 키에서 특정 gnid를 포함하는 키 찾기
   */
  private async findPrologueGnidsKeys(
    gnid: string,
    instanceName: string
  ): Promise<string[]> {
    try {
      const keys = await this.redisManager.findKeys(instanceName, 'prologueGnids:*');
      const matchingKeys: string[] = [];

      for (const key of keys) {
        // Sorted Set에서 해당 gnid가 member로 존재하는지 확인
        const score = await this.redisManager.getClient(instanceName).zscore(key, gnid);
        if (score !== null) {
          matchingKeys.push(key);
        }
      }

      return matchingKeys;
    } catch (error) {
      console.error('prologueGnids 키 검색 실패:', error);
      return [];
    }
  }

  /**
   * deletionPubIds 키에서 특정 pubId를 포함하는지 확인
   */
  private async findDeletionPubIdsKeys(
    pubId: string,
    instanceName: string
  ): Promise<string[]> {
    try {
      const key = 'deletionPubIds';
      const keyExists = await this.redisManager.keyExists(instanceName, key);

      if (keyExists) {
        // List에서 해당 pubId가 존재하는지 확인
        const values = await this.redisManager.getClient(instanceName).lrange(key, 0, -1);
        if (values.includes(pubId)) {
          return [key];
        }
      }

      return [];
    } catch (error) {
      console.error('deletionPubIds 키 검색 실패:', error);
      return [];
    }
  }

  /**
   * userIdsByName 키에서 특정 userId를 값으로 가지는지 확인
   */
  private async findUserIdsByNameKeys(
    userId: string,
    instanceName: string
  ): Promise<string[]> {
    try {
      const key = 'userIdsByName';
      const keyExists = await this.redisManager.keyExists(instanceName, key);

      if (keyExists) {
        // Hash에서 해당 userId가 값으로 존재하는지 확인
        const hashData = await this.redisManager.getClient(instanceName).hgetall(key);
        for (const value of Object.values(hashData)) {
          if (value === userId) {
            return [key];
          }
        }
      }

      return [];
    } catch (error) {
      console.error('userIdsByName 키 검색 실패:', error);
      return [];
    }
  }

  /**
   * 검색 패턴 생성
   */
  private createSearchPattern(keyPattern: string, id: string, idType: 'account' | 'pub'): string {
    // 키 패턴에서 ID 부분을 실제 ID로 치환하여 검색 패턴 생성
    let pattern = keyPattern;

    if (idType === 'account') {
      // accountId, gnid 등을 실제 값으로 치환
      pattern = pattern
        .replace(/\{accountId\}/gi, id)
        .replace(/\{gnid\}/gi, id)
        .replace(/\{account\}/gi, id);
    } else {
      // pubId, nid, userId 등을 실제 값으로 치환
      pattern = pattern
        .replace(/\{pubId\}/gi, id)
        .replace(/\{nid\}/gi, id)
        .replace(/\{userId\}/gi, id)
        .replace(/\{pub\}/gi, id);
    }

    // 다른 플레이스홀더들은 와일드카드로 변환
    pattern = pattern.replace(/\{[^}]+\}/g, '*');

    return pattern;
  }

  /**
   * 키에서 AccountId 교체
   */
  private replaceAccountIdInKey(key: string, oldAccountId: string, newAccountId: string): string {
    // 정확한 매칭을 위해 구분자를 고려한 교체
    return key
      .replace(new RegExp(`\\b${this.escapeRegExp(oldAccountId)}\\b`, 'g'), newAccountId)
      .replace(new RegExp(`account:${this.escapeRegExp(oldAccountId)}`, 'g'), `account:${newAccountId}`)
      .replace(new RegExp(`gnid:${this.escapeRegExp(oldAccountId)}`, 'g'), `gnid:${newAccountId}`);
  }

  /**
   * 키에서 PubId 교체
   */
  private replacePubIdInKey(key: string, oldPubId: string, newPubId: string): string {
    // 정확한 매칭을 위해 구분자를 고려한 교체
    return key
      .replace(new RegExp(`\\b${this.escapeRegExp(oldPubId)}\\b`, 'g'), newPubId)
      .replace(new RegExp(`user:${this.escapeRegExp(oldPubId)}`, 'g'), `user:${newPubId}`)
      .replace(new RegExp(`nid:${this.escapeRegExp(oldPubId)}`, 'g'), `nid:${newPubId}`)
      .replace(new RegExp(`pubId:${this.escapeRegExp(oldPubId)}`, 'g'), `pubId:${newPubId}`)
      .replace(new RegExp(`eu:${this.escapeRegExp(oldPubId)}`, 'g'), `eu:${newPubId}`)
      .replace(new RegExp(`bl:${this.escapeRegExp(oldPubId)}`, 'g'), `bl:${newPubId}`);
  }

  /**
   * 정규식에서 특수문자 이스케이프
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Hash 필드로 userId/pubId를 사용하는 키인지 확인
   */
  private isHashFieldKey(keyPattern: string): boolean {
    const hashFieldPatterns = [
      'guildMember:',
      'pickedDailyRewardIdx:',
      'nationCabinet:',
      'inProgress'
      // user:{userId}는 별도 메서드에서 처리
    ];

    return hashFieldPatterns.some(pattern =>
      keyPattern.includes(pattern) || keyPattern === pattern
    );
  }

  /**
   * Set/Sorted Set 멤버로 userId를 사용하는 키인지 확인
   */
  private isSetMemberKey(keyPattern: string): boolean {
    const setMemberPatterns = [
      'nationCabinetApplicantUserIds:',
      'nationWeeklyDonationRank:',
      'nationWeeklyDonationRankRewardedUsers:'
    ];

    return setMemberPatterns.some(pattern => keyPattern.includes(pattern));
  }

  /**
   * 키 이동 (이름 변경)
   */
  private async moveKey(oldKey: string, newKey: string, instanceName: string): Promise<boolean> {
    try {
      // 새 키가 이미 존재하는지 확인
      const newKeyExists = await this.redisManager.keyExists(instanceName, newKey);
      if (newKeyExists) {
        console.warn(`새 키가 이미 존재합니다: ${newKey}`);
        return false;
      }

      // 기존 키가 존재하는지 확인
      const oldKeyExists = await this.redisManager.keyExists(instanceName, oldKey);
      if (!oldKeyExists) {
        return false; // 기존 키가 없으면 이동할 필요 없음
      }

      // 키 이름 변경
      await this.redisManager.renameKey(instanceName, oldKey, newKey);
      return true;
    } catch (error) {
      console.error(`키 이동 실패: ${oldKey} -> ${newKey}`, error);
      return false;
    }
  }

  /**
   * prologueGnids 키 업데이트 (Sorted Set에서 gnid member 변경)
   */
  private async updatePrologueGnidsKeys(
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    try {
      // 모든 월드의 prologueGnids 키 찾기
      const keys = await this.redisManager.findKeys(instanceName, 'prologueGnids:*');

      console.log(`prologueGnids 키 검색: ${keys.length}개 키 발견`);

      for (const key of keys) {
        for (const data of remapData) {
          // 기존 gnid가 Sorted Set에 있는지 확인
          const score = await this.redisManager.getClient(instanceName).zscore(key, data.uwo_Gnid);

          if (score !== null) {
            // 기존 gnid 제거하고 새 gnid 추가
            await this.redisManager.getClient(instanceName).zrem(key, data.uwo_Gnid);
            await this.redisManager.getClient(instanceName).zadd(key, score, data.uwogl_Gnid);
            updatedCount++;
            console.log(`prologueGnids 업데이트: ${key} - ${data.uwo_Gnid} -> ${data.uwogl_Gnid}`);
          }
        }
      }
    } catch (error) {
      console.error('prologueGnids 키 업데이트 실패:', error);
    }

    return updatedCount;
  }

  /**
   * deletionPubIds 키 업데이트 (List에서 nid 값 변경)
   */
  private async updateDeletionPubIdsKey(
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    try {
      const key = 'deletionPubIds';
      const keyExists = await this.redisManager.keyExists(instanceName, key);

      if (keyExists) {
        // List의 모든 값 가져오기
        const values = await this.redisManager.getClient(instanceName).lrange(key, 0, -1);

        // 새로운 값들로 교체
        const newValues = values.map((value: string) => {
          for (const data of remapData) {
            if (value === data.uwo_Nid) {
              updatedCount++;
              return data.uwogl_Nid;
            }
          }
          return value;
        });

        // 기존 List 삭제하고 새 값들로 재생성
        if (updatedCount > 0) {
          await this.redisManager.getClient(instanceName).del(key);
          if (newValues.length > 0) {
            await this.redisManager.getClient(instanceName).rpush(key, ...newValues);
          }
          console.log(`deletionPubIds 업데이트: ${updatedCount}개 항목 변경`);
        }
      }
    } catch (error) {
      console.error('deletionPubIds 키 업데이트 실패:', error);
    }

    return updatedCount;
  }

  /**
   * userIdsByName 키 업데이트 (Hash에서 userId 값 변경)
   */
  private async updateUserIdsByNameKey(
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    try {
      const key = 'userIdsByName';
      const keyExists = await this.redisManager.keyExists(instanceName, key);

      if (keyExists) {
        // Hash의 모든 필드-값 가져오기
        const hashData = await this.redisManager.getClient(instanceName).hgetall(key);

        for (const [field, value] of Object.entries(hashData)) {
          for (const data of remapData) {
            if (value === data.uwo_Nid) {
              await this.redisManager.getClient(instanceName).hset(key, field, data.uwogl_Nid);
              updatedCount++;
              console.log(`userIdsByName 업데이트: ${field} - ${data.uwo_Nid} -> ${data.uwogl_Nid}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('userIdsByName 키 업데이트 실패:', error);
    }

    return updatedCount;
  }

  /**
   * user:{userId} 키의 Hash 필드 값 업데이트
   */
  private async updateUserHashKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    try {
      // 모든 user:* 키 찾기
      const keys = await this.redisManager.findKeys(instanceName, 'user:*');
      console.log(`user:{userId} 키 검색: user:* -> ${keys.length}개 키 발견`);

      for (const key of keys) {
        // user:{userId} 키는 정적 분석으로 Hash 타입임을 확인
        const hashData = await this.redisManager.getClient(instanceName).hgetall(key);

        for (const data of remapData) {
          // pubId 필드 값 변경
          if (hashData.pubId === data.uwo_Nid) {
            await this.redisManager.getClient(instanceName).hset(key, 'pubId', data.uwogl_Nid);
            updatedCount++;
            console.log(`user Hash pubId 업데이트: ${key} - pubId: ${data.uwo_Nid} -> ${data.uwogl_Nid}`);
          }

          // accountId 필드 값 변경 (있는 경우)
          if (hashData.accountId === data.uwo_Gnid) {
            await this.redisManager.getClient(instanceName).hset(key, 'accountId', data.uwogl_Gnid);
            updatedCount++;
            console.log(`user Hash accountId 업데이트: ${key} - accountId: ${data.uwo_Gnid} -> ${data.uwogl_Gnid}`);
          }

          // 다른 필드에서 ID가 값으로 사용되는 경우
          for (const [field, value] of Object.entries(hashData)) {
            if (typeof value === 'string') {
              let newValue = value;
              let updated = false;

              // pubId/nid 교체
              if (value.includes(data.uwo_Nid)) {
                newValue = newValue.replace(new RegExp(this.escapeRegExp(data.uwo_Nid), 'g'), data.uwogl_Nid);
                updated = true;
              }

              // accountId/gnid 교체
              if (value.includes(data.uwo_Gnid)) {
                newValue = newValue.replace(new RegExp(this.escapeRegExp(data.uwo_Gnid), 'g'), data.uwogl_Gnid);
                updated = true;
              }

              if (updated) {
                await this.redisManager.getClient(instanceName).hset(key, field, newValue);
                updatedCount++;
                console.log(`user Hash ${field} 필드 값 업데이트: ${key} - ID 교체`);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('user:{userId} 키 업데이트 실패:', error);
    }

    return updatedCount;
  }

  /**
   * Hash 필드로 userId를 사용하는 키들 업데이트
   */
  private async updateHashFieldKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    try {
      // 키 패턴에서 실제 키들 찾기
      const searchPattern = this.createHashFieldSearchPattern(keyInfo.keyPattern);
      const keys = await this.redisManager.findKeys(instanceName, searchPattern);

      console.log(`Hash 필드 키 검색: ${searchPattern} -> ${keys.length}개 키 발견`);

      for (const key of keys) {
        const hashData = await this.redisManager.getClient(instanceName).hgetall(key);

        for (const [field, value] of Object.entries(hashData)) {
          for (const data of remapData) {
            // 필드명이 userId인 경우 (필드명 자체가 ID)
            if (field === data.uwo_Nid) {
              // 필드명 변경 (기존 필드 삭제 후 새 필드 생성)
              await this.redisManager.getClient(instanceName).hdel(key, field);
              await this.redisManager.getClient(instanceName).hset(key, data.uwogl_Nid, value);
              updatedCount++;
              console.log(`Hash 필드명 업데이트: ${key} - 필드 ${data.uwo_Nid} -> ${data.uwogl_Nid}`);
            }

            // pubId 필드 값 변경 (user:{userId} 키의 pubId 필드)
            if (field === 'pubId' && value === data.uwo_Nid) {
              await this.redisManager.getClient(instanceName).hset(key, field, data.uwogl_Nid);
              updatedCount++;
              console.log(`Hash pubId 필드 값 업데이트: ${key} - pubId: ${data.uwo_Nid} -> ${data.uwogl_Nid}`);
            }

            // accountId 필드 값 변경
            if (field === 'accountId' && value === data.uwo_Gnid) {
              await this.redisManager.getClient(instanceName).hset(key, field, data.uwogl_Gnid);
              updatedCount++;
              console.log(`Hash accountId 필드 값 업데이트: ${key} - accountId: ${data.uwo_Gnid} -> ${data.uwogl_Gnid}`);
            }

            // 값에 ID가 포함된 경우 (문자열 내부에 ID가 포함)
            if (typeof value === 'string') {
              let newValue = value;
              let updated = false;

              // pubId/nid 교체
              if (value.includes(data.uwo_Nid)) {
                newValue = newValue.replace(new RegExp(this.escapeRegExp(data.uwo_Nid), 'g'), data.uwogl_Nid);
                updated = true;
              }

              // accountId/gnid 교체
              if (value.includes(data.uwo_Gnid)) {
                newValue = newValue.replace(new RegExp(this.escapeRegExp(data.uwo_Gnid), 'g'), data.uwogl_Gnid);
                updated = true;
              }

              if (updated) {
                await this.redisManager.getClient(instanceName).hset(key, field, newValue);
                updatedCount++;
                console.log(`Hash 값 내용 업데이트: ${key} - ${field} 값에서 ID 교체`);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Hash 필드 키 업데이트 실패:', error);
    }

    return updatedCount;
  }

  /**
   * Hash 필드 키를 위한 검색 패턴 생성
   */
  private createHashFieldSearchPattern(keyPattern: string): string {
    // 모든 플레이스홀더를 와일드카드로 변환
    return keyPattern.replace(/\{[^}]+\}/g, '*');
  }

  /**
   * Set/Sorted Set 멤버로 userId를 사용하는 키들 업데이트
   */
  private async updateSetMemberKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<number> {
    let updatedCount = 0;

    try {
      // 키 패턴에서 실제 키들 찾기
      const searchPattern = this.createHashFieldSearchPattern(keyInfo.keyPattern);
      const keys = await this.redisManager.findKeys(instanceName, searchPattern);

      console.log(`Set/Sorted Set 키 검색: ${searchPattern} -> ${keys.length}개 키 발견`);

      for (const key of keys) {
        // 정적 분석을 통해 키 타입 결정
        const keyType = this.inferKeyTypeFromPattern(keyInfo.keyPattern);

        if (keyType === 'zset') {
          // Sorted Set 처리
          for (const data of remapData) {
            const score = await this.redisManager.getClient(instanceName).zscore(key, data.uwo_Nid);
            if (score !== null) {
              await this.redisManager.getClient(instanceName).zrem(key, data.uwo_Nid);
              await this.redisManager.getClient(instanceName).zadd(key, score, data.uwogl_Nid);
              updatedCount++;
              console.log(`Sorted Set 멤버 업데이트: ${key} - ${data.uwo_Nid} -> ${data.uwogl_Nid}`);
            }
          }
        } else if (keyType === 'set') {
          // Set 처리
          for (const data of remapData) {
            const isMember = await this.redisManager.getClient(instanceName).sismember(key, data.uwo_Nid);
            if (isMember) {
              await this.redisManager.getClient(instanceName).srem(key, data.uwo_Nid);
              await this.redisManager.getClient(instanceName).sadd(key, data.uwogl_Nid);
              updatedCount++;
              console.log(`Set 멤버 업데이트: ${key} - ${data.uwo_Nid} -> ${data.uwogl_Nid}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('Set/Sorted Set 멤버 키 업데이트 실패:', error);
    }

    return updatedCount;
  }

  // /**
  //  * 해시 필드 업데이트
  //  */
  // private async updateHashFields(
  //   key: string,
  //   remapData: RemapData[],
  //   instanceName: string
  // ): Promise<number> {
  //   let updatedFields = 0;

  //   try {
  //     const hashData = await this.redisManager.getHashAll(instanceName, key);

  //     for (const [field, value] of Object.entries(hashData)) {
  //       let newValue = value;
  //       let updated = false;

  //       // AccountId 교체
  //       for (const data of remapData) {
  //         if (value.includes(data.uwo_Gnid)) {
  //           newValue = newValue.replace(new RegExp(data.uwo_Gnid, 'g'), data.uwogl_Gnid);
  //           updated = true;
  //         }
  //         if (value.includes(data.uwo_Nid)) {
  //           newValue = newValue.replace(new RegExp(data.uwo_Nid, 'g'), data.uwogl_Nid);
  //           updated = true;
  //         }
  //       }

  //       if (updated) {
  //         await this.redisManager.setHashField(instanceName, key, field, newValue);
  //         updatedFields++;
  //       }
  //     }
  //   } catch (error) {
  //     console.error(`해시 필드 업데이트 실패: ${key}`, error);
  //   }

  //   return updatedFields;
  // }

  /**
   * 드라이런 모드 - 실제 업데이트 없이 영향받을 키 수만 계산
   */
  async dryRun(
    redisKeys: RedisKeyInfo[],
    remapData: RemapData[],
    worldId?: string
  ): Promise<RedisUpdateResult[]> {
    const results: RedisUpdateResult[] = [];

    const filteredRemapData = worldId
      ? remapData.filter(data => data.gameServerId === worldId)
      : remapData;

    console.log(`Redis 드라이런 모드: ${filteredRemapData.length}개 레코드로 ${redisKeys.length}개 키 패턴 분석`);

    const keysByInstance = this.groupKeysByInstance(redisKeys);

    // worldId가 없는 경우 모든 월드에 대해 처리
    if (!worldId) {
      const worldIds = this.getAvailableWorldIds(remapData);

      for (const currentWorldId of worldIds) {
        this.currentWorldId = currentWorldId;
        const worldFilteredData = remapData.filter(data => data.gameServerId === currentWorldId);

        console.log(`Redis 드라이런 월드별 분석: ${currentWorldId} (${worldFilteredData.length}개 레코드)`);

        for (const [instanceName, keys] of keysByInstance) {
          for (const keyInfo of keys) {
            const result = await this.analyzeKeyImpact(keyInfo, worldFilteredData, instanceName);
            results.push(result);
          }
        }
      }
    } else {
      // worldId가 지정된 경우 기존 로직 사용
      if (filteredRemapData.length > 0 && filteredRemapData[0]?.gameServerId) {
        this.currentWorldId = filteredRemapData[0].gameServerId;
      }

      for (const [instanceName, keys] of keysByInstance) {
        for (const keyInfo of keys) {
          const result = await this.analyzeKeyImpact(keyInfo, filteredRemapData, instanceName);
          results.push(result);
        }
      }
    }

    return results;
  }

  /**
   * 키 영향도 분석
   */
  private async analyzeKeyImpact(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<RedisUpdateResult> {
    const startTime = Date.now();

    try {
      let keyCount = 0;

      // AccountId/Gnid 관련 키 카운트
      if (keyInfo.usesAccountId || keyInfo.usesGnid) {
        for (const data of remapData) {
          const keys = await this.findKeysWithAccountId(keyInfo.keyPattern, data.uwo_Gnid, instanceName);
          keyCount += keys.length;
        }
      }

      // PubId/Nid 관련 키 카운트
      if (keyInfo.usesPubId || keyInfo.usesNid) {
        for (const data of remapData) {
          const keys = await this.findKeysWithPubId(keyInfo.keyPattern, data.uwo_Nid, instanceName);
          keyCount += keys.length;
        }
      }

      // 정적 분석을 통한 키 타입 결정
      const keyType = this.getStaticKeyType(keyInfo, keyCount);

      return {
        keyPattern: keyInfo.keyPattern,
        updatedKeys: keyCount,
        success: true,
        executionTime: Date.now() - startTime,
        keyType: keyType,
        instanceName: instanceName,
      };
    } catch (error) {
      // 에러가 발생해도 정의된 키 타입 사용
      const keyType = this.getStaticKeyType(keyInfo, 0);

      return {
        keyPattern: keyInfo.keyPattern,
        updatedKeys: 0,
        success: false,
        error: String(error),
        executionTime: Date.now() - startTime,
        keyType: keyType,
        instanceName: instanceName,
      };
    }
  }

  /**
   * 백업 생성
   */
  async createBackup(
    redisKeys: RedisKeyInfo[],
    remapData: RemapData[],
    worldId?: string
  ): Promise<Record<string, Record<string, string>>> {
    const backup: Record<string, Record<string, string>> = {};

    const filteredRemapData = worldId
      ? remapData.filter(data => data.gameServerId === worldId)
      : remapData;

    const keysByInstance = this.groupKeysByInstance(redisKeys);

    for (const [instanceName, keys] of keysByInstance) {
      backup[instanceName] = {};

      for (const keyInfo of keys) {
        // AccountId 관련 키 백업
        if (keyInfo.usesAccountId || keyInfo.usesGnid) {
          for (const data of filteredRemapData) {
            const keysToBackup = await this.findKeysWithAccountId(keyInfo.keyPattern, data.uwo_Gnid, instanceName);
            for (const key of keysToBackup) {
              const value = await this.redisManager.getValue(instanceName, key);
              if (value !== null) {
                backup[instanceName][key] = value;
              }
            }
          }
        }

        // PubId 관련 키 백업
        if (keyInfo.usesPubId || keyInfo.usesNid) {
          for (const data of filteredRemapData) {
            const keysToBackup = await this.findKeysWithPubId(keyInfo.keyPattern, data.uwo_Nid, instanceName);
            for (const key of keysToBackup) {
              const value = await this.redisManager.getValue(instanceName, key);
              if (value !== null) {
                backup[instanceName][key] = value;
              }
            }
          }
        }
      }
    }

    return backup;
  }
}
