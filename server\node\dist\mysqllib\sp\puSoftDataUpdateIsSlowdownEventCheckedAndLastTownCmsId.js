"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorCode = exports.spName = void 0;
const query = __importStar(require("../query"));
const merror_1 = require("../../motiflib/merror");
exports.spName = 'mp_u_soft_data_update_is_slowdown_event_checked_last_town_cms_id';
exports.errorCode = merror_1.MErrorCode.SOFT_DATA_UPDATE_IS_SLOWDOWN_EVENT_CHECKED_AND_LAST_TOWN_CMS_ID_QUERY_ERROR;
const spFunction = query.generateSPFunction(exports.spName);
const catchHandler = query.generateMErrorRejection(exports.errorCode);
function default_1(connection, userId, isSlowdownEventChecked, townCmsId) {
    return spFunction(connection, userId, isSlowdownEventChecked, townCmsId)
        .then(() => {
        return;
    })
        .catch(catchHandler);
}
exports.default = default_1;
//# sourceMappingURL=puSoftDataUpdateIsSlowdownEventCheckedAndLastTownCmsId.js.map