// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _, { isInteger } from 'lodash';
import { Container } from 'typedi';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import * as formula from '../../../formula';
import * as mutil from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { Sync, Resp } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ItemChange, ItemInven } from '../../userInven';
import { ClientPacketHandler } from '../index';
import { ItemDesc, ITEM_TYPE } from '../../../cms/itemDesc';
import { CostData, MakeManufactureTimeReductionData } from '../../../motiflib/gameLog';
import tuManufactureDecreaseExpireTime from '../../../mysqllib/txn/tuManufactureDecreaseExpireTime';
import { AccumulateParam } from '../../userAchievement';

// ----------------------------------------------------------------------------
// 제조 가속권 사용
// ----------------------------------------------------------------------------

const rsn = 'manufacture_decrease_expire_time';
const add_rsn = null;

interface RequestBody {
  roomCmsId: number;
  slot: number;
  itemCmsId: number; // 제조 가속권 itemId
  count: number; // 아이템 사용 갯수
}

// ----------------------------------------------------------------------------
export class Cph_Manufacture_Decrease_Expire_Time implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() { }

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { roomCmsId, slot, itemCmsId, count } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const itemInven: ItemInven = user.userInven.itemInven;

    if (!isInteger(count) || count <= 0) {
      throw new MError('invalid-count', MErrorCode.MANUFACTURE_DECREASE_INVALID_COUNT, {
        body,
      });
    }

    const roomProgress = user.userManufacture.getRoom(roomCmsId);
    if (!roomProgress) {
      throw new MError(
        'not-in-progress-manufacture-room',
        MErrorCode.NOT_IN_PROGRESS_MANUFACTURE_ROOM,
        {
          userId: user.userId,
          roomCmsId,
          body,
        }
      );
    }

    const slotData = roomProgress[slot];
    if (!slotData) {
      throw new MError(
        'not-in-progress-manufacture-slot',
        MErrorCode.NOT_IN_PROGRESS_MANUFACTURE_SLOT,
        {
          userId: user.userId,
          roomCmsId,
          slot,
          body,
        }
      );
    }

    const itemCms: ItemDesc = cms.Item[itemCmsId];
    if (!itemCms) {
      throw new MError('invalid-item-cms-id', MErrorCode.MANUFACTURE_INVALID_ITEM_CMS_ID, {
        userId: user.userId,
        body,
      });
    }

    if (!itemCms.timeCostValMin || itemCms.timeCostValMin <= 0) {
      throw new MError(
        'invalid-manufacture-time-cost-val-min',
        MErrorCode.INVALID_MANUFACTURE_TIME_COST_VAL_MIN,
        {
          body,
        }
      );
    }

    const curTimeUtc: number = mutil.curTimeUtc();

    // 클라, 서버 시간 오차 10초 여유를 둔다
    const completionTimeUtc: number = slotData.completionTimeUtc + 10;

    // 남은 생산 시간에 최대 사용 가능한 갯수를 구함.
    const maxCount = Math.ceil(
      (completionTimeUtc - curTimeUtc) / (itemCms.timeCostValMin * formula.SECONDS_PER_MINUTE)
    );

    // 최대 사용 가능 갯수보다 많은 count 값이 요청으로 왔을 경우 throw.
    if (count > maxCount) {
      throw new MError('over-use-item', MErrorCode.OVER_USE_ITEM_COUNT, {
        itemCmsId,
        timeCostValMin: itemCms.timeCostValMin,
        maxCount,
        count,
      });
    }

    const itemChange: ItemChange = itemInven.buildItemChange(itemCmsId, -count, true);
    const cost_data: CostData[] = [
      {
        id: itemCmsId,
        amt: count,
        type: ITEM_TYPE[itemCms.type],
      },
    ];

    const decreaseSec: number = itemCms.timeCostValMin * formula.SECONDS_PER_MINUTE * count;

    const updatedCompletionTimeUtc = slotData.completionTimeUtc - decreaseSec;

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const sync: Sync = {};
    const accums: AccumulateParam[] = [];
    // Create a modified version of slotData with updated completion time
    const updatedSlotData = {
      ...slotData,
      completionTimeUtc: updatedCompletionTimeUtc
    };
    
    return tuManufactureDecreaseExpireTime(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      { [slot]: updatedSlotData }, // ManufactureProgress object with updated completion time
      roomCmsId,
      slot,
      [], // PointChange
      [itemChange]
    )
      .then(() => {
        const { product_id, product_name } = this.buildManufactureCommonLog(
          slotData.recipeId
        );
        user.glog(
          'time_reduction',
          MakeManufactureTimeReductionData({
            rsn,
            add_rsn,
            old_duration: Math.max(completionTimeUtc - curTimeUtc, 0),
            cur_duration: Math.max(updatedCompletionTimeUtc - curTimeUtc, 0),
            pr_data: null,
            cost_data,
            manufacture_data: {
              manufacture_slot_idx: slot,
              product_id,
              product_name,
            },
          })
        );

        // itemInven update
        _.merge<Sync, Sync>(
          sync,
          itemInven.applyItemChange(itemChange, accums, {
            user,
            rsn,
            add_rsn,
          })
        );

        slotData.completionTimeUtc = updatedCompletionTimeUtc;

        _.merge<Sync, Sync>(sync, {
          add: {
            manufacture: {
              roomInfo: {
                [roomCmsId]: {
                  [slot]: {
                    slot,
                    recipeId: slotData.recipeId,
                    startTimeUtc: slotData.startTimeUtc,
                    completionTimeUtc: slotData.completionTimeUtc,
                    resultType: slotData.resultType,
                    mateCmsIds: slotData.mateCmsIds,
                    successRate: slotData.successRate,
                    greatSuccessRate: slotData.greatSuccessRate,
                    extra: slotData.extra
                  },
                }
              },
            },
          },
        });

        if (accums.length > 0) {
          return user.userAchievement.accumulate(accums, user, sync, { user, rsn, add_rsn });
        }
      })
      .then(() => {
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }

  private buildManufactureCommonLog(recipeId: number): { product_id: number; product_name: string } {
    const recipeCms = cms.ManufactureRecipe[recipeId];
    if (!recipeCms) {
      return {
        product_id: recipeId,
        product_name: `Unknown Recipe ${recipeId}`,
      };
    }
    return {
      product_id: recipeId,
      product_name: `Recipe ${recipeId}`,
    };
  }
}