import mysql from 'mysql2/promise';
import fs from 'fs-extra';
import path from 'path';
import JSON5 from 'json5';
import { DatabaseTableInfo, DatabaseColumnInfo, DatabaseIndexInfo } from '../types';
import { DatabaseManager } from '../database/databaseManager';
import { RemapToolConfig } from '../types';

export interface ExtractedSchema {
  database: string;
  tables: DatabaseTableInfo[];
  extractedAt: string;
}

export class SchemaExtractor {
  private dbManager: DatabaseManager;
  private connection: mysql.Connection | null = null;

  constructor(private config: RemapToolConfig) {
    this.dbManager = new DatabaseManager(config);
  }

  async connect(databaseType: 'auth' | 'world' | 'user', shardKey?: string): Promise<void> {
    await this.dbManager.initialize();

    if (databaseType === 'auth') {
      this.connection = this.dbManager.getAuthConnection();
    } else if (databaseType === 'world') {
      if (this.config.worlds.length === 0) throw new Error('No world database configured');
      const firstWorld = this.config.worlds[0];
      if (!firstWorld) throw new Error('No world configuration found');
      this.connection = this.dbManager.getWorldConnection(firstWorld.id);
    } else if (databaseType === 'user') {
      if (this.config.worlds.length === 0) throw new Error('No world database configured');
      const firstWorld = this.config.worlds[0];
      if (!firstWorld) throw new Error('No world configuration found');
      const shardId = shardKey ? parseInt(shardKey) : 0;
      this.connection = this.dbManager.getUserShardConnection(firstWorld.id, shardId);
    }

    if (!this.connection) {
      throw new Error(`Failed to get ${databaseType} database connection`);
    }
  }

  async disconnect(): Promise<void> {
    if (this.dbManager) {
      await this.dbManager.cleanup();
    }
    this.connection = null;
  }

  async extractSchema(databaseType: 'auth' | 'world' | 'user', databaseName: string): Promise<ExtractedSchema> {
    console.log(`📊 Starting schema extraction: ${databaseName} (${databaseType})`);

    const tables = await this.extractTables(databaseType, databaseName);

    console.log(`✅ Schema extraction completed: ${tables.length} tables`);

    return {
      database: databaseName,
      tables,
      extractedAt: new Date().toISOString()
    };
  }

  private async extractTables(databaseType: 'auth' | 'world' | 'user', databaseName: string): Promise<DatabaseTableInfo[]> {
    if (!this.connection) throw new Error('Database connection not established');

    const [rows] = await this.connection.execute(`
      SELECT TABLE_NAME, TABLE_COMMENT, ENGINE, TABLE_COLLATION
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = ?
      AND TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `, [databaseName]);

    const tables: DatabaseTableInfo[] = [];

    for (const row of rows as any[]) {
      const tableName = row.TABLE_NAME;

      if (!this.isValidTableName(tableName, databaseType)) {
        console.log(`[Filtered] Invalid table prefix: ${tableName} (${databaseType} database)`);
        continue;
      }

      console.log(`  📋 Extracting table: ${tableName}`);

      const columns = await this.extractColumns(tableName, databaseName);
      const indexes = await this.extractIndexes(tableName, databaseName);

      const columnsArray = Array.from(columns.values());
      const primaryKeys = await this.extractPrimaryKeys(tableName, databaseName);

      const table: DatabaseTableInfo = {
        tableName,
        database: databaseType,
        columns: columnsArray,
        primaryKeys,
        indexes,
        hasAccountId: this.hasColumnInArray(columnsArray, ['accountId', 'gnid']),
        hasPubId: this.hasColumnInArray(columnsArray, ['pubId', 'nid']),
        shardingRequired: databaseType === 'user',
        filePath: `extracted:${databaseName}.${tableName}`,
        comment: row.TABLE_COMMENT || undefined,
        engine: row.ENGINE || undefined,
        collation: row.TABLE_COLLATION || undefined
      };

      tables.push(table);
    }

    return tables;
  }

  private isValidTableName(tableName: string, database: 'auth' | 'world' | 'user'): boolean {
    const lowerTableName = tableName.toLowerCase();

    switch (database) {
      case 'auth':
        return lowerTableName.startsWith('a_');
      case 'world':
        return lowerTableName.startsWith('w_');
      case 'user':
        return lowerTableName.startsWith('u_');
      default:
        return false;
    }
  }

  private async extractColumns(tableName: string, databaseName: string): Promise<Map<string, DatabaseColumnInfo>> {
    if (!this.connection) throw new Error('Database connection not established');

    const [rows] = await this.connection.execute(`
      SELECT
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT,
        COLUMN_TYPE,
        COLUMN_COMMENT,
        EXTRA,
        ORDINAL_POSITION,
        COLUMN_KEY
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY ORDINAL_POSITION
    `, [databaseName, tableName]);

    const columns = new Map<string, DatabaseColumnInfo>();

    for (const row of rows as any[]) {
      const columnInfo: DatabaseColumnInfo = {
        columnName: row.COLUMN_NAME,
        dataType: row.DATA_TYPE,
        isNullable: row.IS_NULLABLE === 'YES',
        isPrimaryKey: row.COLUMN_KEY === 'PRI',
        isIndexed: row.COLUMN_KEY !== '',
        relatedTo: this.determineColumnRelation(row.COLUMN_NAME, tableName),
        defaultValue: row.COLUMN_DEFAULT,
        fullType: row.COLUMN_TYPE,
        comment: row.COLUMN_COMMENT || undefined,
        extra: row.EXTRA || undefined,
        position: row.ORDINAL_POSITION
      };

      columns.set(row.COLUMN_NAME, columnInfo);
    }

    return columns;
  }

  private async extractIndexes(tableName: string, databaseName: string): Promise<DatabaseIndexInfo[]> {
    if (!this.connection) {
      throw new Error('Database connection not established');
    }

    const [rows] = await this.connection.execute(`
      SELECT
        INDEX_NAME,
        COLUMN_NAME,
        NON_UNIQUE,
        SEQ_IN_INDEX,
        INDEX_TYPE,
        INDEX_COMMENT
      FROM INFORMATION_SCHEMA.STATISTICS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
      ORDER BY INDEX_NAME, SEQ_IN_INDEX
    `, [databaseName, tableName]);

    const indexMap = new Map<string, DatabaseIndexInfo>();

    for (const row of rows as any[]) {
      const indexName = row.INDEX_NAME;

      if (!indexMap.has(indexName)) {
        indexMap.set(indexName, {
          indexName: indexName,
          columns: [],
          isUnique: row.NON_UNIQUE === 0,
          isPrimary: indexName === 'PRIMARY',
          type: row.INDEX_TYPE,
          comment: row.INDEX_COMMENT || undefined
        });
      }

      const index = indexMap.get(indexName)!;
      index.columns.push(row.COLUMN_NAME);
    }

    return Array.from(indexMap.values());
  }

  private async extractPrimaryKeys(tableName: string, databaseName: string): Promise<string[]> {
    if (!this.connection) {
      throw new Error('Database connection not established');
    }

    const [rows] = await this.connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND CONSTRAINT_NAME = 'PRIMARY'
      ORDER BY ORDINAL_POSITION
    `, [databaseName, tableName]);

    return (rows as any[]).map(row => row.COLUMN_NAME);
  }

  private hasColumnInArray(columns: DatabaseColumnInfo[], columnNames: string[]): boolean {
    const hasRelatedColumn = columns.some(col =>
      columnNames.includes(col.relatedTo)
    );

    if (hasRelatedColumn) {
      return true;
    }

    return columnNames.some(name =>
      columns.some(col => col.columnName.toLowerCase() === name.toLowerCase())
    );
  }

  private determineColumnRelation(columnName: string, tableName: string): 'accountId' | 'pubId' | 'none' {
    const lowerName = columnName.toLowerCase();
    const lowerTableName = tableName.toLowerCase();

    if (lowerName === 'accountid' || lowerName === 'gnid') {
      return 'accountId';
    }
    if (lowerName === 'pubid' || lowerName === 'nid') {
      return 'pubId';
    }

    if (lowerTableName === 'a_accounts' && lowerName === 'id') {
      return 'accountId';
    }

    return 'none';
  }
}

export async function saveSchemaToFile(schema: ExtractedSchema, outputDir: string): Promise<string> {
  await fs.ensureDir(outputDir);

  const fileName = `schema-${schema.database}.json5`;
  const filePath = path.join(outputDir, fileName);

  const schemaContent = JSON5.stringify(schema, null, 2);
  await fs.writeFile(filePath, schemaContent, 'utf8');

  console.log(`💾 Schema saved: ${filePath}`);
  return filePath;
}
