// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import mlog from '../../../motiflib/mlog';
import tuReduceMyMateSponsorWaitTime from '../../../mysqllib/txn/tuReduceMyMateSponsorWaitTime';
import { <PERSON>lient<PERSON><PERSON>etHandler } from '../index';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { SECONDS_PER_MINUTE } from '../../../formula';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import { LobbyService } from '../../server';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Sync, Resp } from '../../type/sync';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import { ItemChange } from '../../userInven';
import { CostData, MakePubMyMateSponsorTimeReductionData } from '../../../motiflib/gameLog';
import { ITEM_TYPE } from '../../../cms/itemDesc';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import mconf from '../../../motiflib/mconf';
import { needMateRecruitingTermsCheck } from '../../../cms/mateRecruitingGroupDesc';
import { AccumulateParam } from '../../userAchievement';

// ----------------------------------------------------------------------------
// 여관-동료 후원 대기 시간을 초기화 한다.
// ----------------------------------------------------------------------------

const rsn = 'pub_reduce_my_mate_sponsor_wait_time';
const add_rsn = null;

interface RequestBody {
  mateCmsId: number;
  itemCmsId: number;
  count: number;
}

// ----------------------------------------------------------------------------
export class Cph_Town_PubReduceMyMateSponsorWaitTime implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureInTown();

    const body: RequestBody = packet.bodyObj;
    const { mateCmsId, itemCmsId, count } = body;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    // 여관-동료 메뉴는 잠금되지 않음
    // user.userContentsTerms.ensureBuildingContentsUnlock(
    //   cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID,
    //   user
    // );
    // if (mconf.binaryCode === 'GL') {
    //   throw new MError(
    //     'invalid-binary-code',
    //     MErrorCode.PUB_REDUCE_MY_MATE_SPONSOR_WAIT_TIME_INVALID_BINARY_CODE,
    //     {
    //       userId: user.userId,
    //       binaryCode: mconf.binaryCode,
    //     }
    //   );
    // }

    if (!count || !_.isInteger(count)) {
      throw new MError('invalid-count', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
        count,
      });
    }
    const itemCms = cms.Item[itemCmsId];
    if (!itemCms) {
      throw new MError('invalid-item-cms-id', MErrorCode.NO_KEY_IN_CMS, {
        itemCmsId,
      });
    }

    const buildingMenuCmsId = cmsEx.BUILDING_MENU_CMS_ID.PUB_MY_MATE;
    if (
      itemCms.timeCostValMin === undefined ||
      !itemCms.timeCostBuildingMenu?.includes(buildingMenuCmsId)
    ) {
      throw new MError('invalid-item', MErrorCode.INVALID_ITEM, {
        itemCmsId,
      });
    }

    const townCmsId = user.userTown.getTownCmsId();
    const curTimeUtc = mutil.curTimeUtc();
    const townMyMates = user.userMates.getCurTownMyMates(user.userTown);

    const mateCms = cms.Mate[mateCmsId];
    const townMyMate = townMyMates.mates[mateCmsId];

    if (!mateCms) {
      throw new MError('invalid-mate-cms-id', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
        mateCmsId,
      });
    }

    if (!townMyMate) {
      throw new MError('not-found-mate', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
        mateCmsId,
      });
    }

    // 서버 클라간 시간이 정확히 같지 않기 때문에 100초의 여유를 둔다.
    const curLastUpdateTimeUtc = townMyMates.lastUpdateTimeUtc;
    const mateResetSeconds = cms.Const.RecruitResetCycleSec.value;
    if (curLastUpdateTimeUtc + mateResetSeconds <= curTimeUtc - 100) {
      throw new MError('pub-my-mates-expired', MErrorCode.PUB_MY_MATE_TIME_EXPIRED, {
        mateCmsId,
        curLastUpdateTimeUtc,
        curTimeUtc,
        diff: curTimeUtc - curLastUpdateTimeUtc,
      });
    }

    // https://jira.line.games/browse/UWO-7925
    // https://jira.line.games/browse/UWO-19699 특권 보너스,
    // cms.MateRecruitingGroup.isMustAppear 이 true 또는 mustAppearEvent 조건 만족인 경우
    // cms.MateRecruitingGroup.contentsTerms 을 검사해야됨
    const townCms = cms.Town[user.userTown.getTownCmsId()];
    const mateRecruitingGroupCms = cmsEx.getMateRecruitingGroupByGroupAndMateCmsId(
      townCms.mateRecruitingGroup,
      mateCmsId
    );
    const eventPageProducts = user.userCashShop.getEventPageProducts();
    if (
      !mateRecruitingGroupCms ||
      (needMateRecruitingTermsCheck(mateRecruitingGroupCms, eventPageProducts, curTimeUtc) &&
        !user.userContentsTerms.isValidContentsTerms(mateRecruitingGroupCms.contentsTerms, user))
    ) {
      throw new MError('contents-terms-invalid', MErrorCode.CONTENTS_TERMS_INVALID, {
        mateCmsId,
        townCmsId: townCms.id,
        mateRecruitingGroupCms,
        contentsTerms: mateRecruitingGroupCms.contentsTerms,
      });
    }

    // 대기 중이 아닌 경우
    if (!townMyMate.sponsorableTimeUtc || curTimeUtc >= townMyMate.sponsorableTimeUtc) {
      throw new MError(
        'not-waiting-for-sponsorship',
        MErrorCode.NOT_IN_MY_MATE_SPONSORSHIP_WAIT_TIME,
        {
          mateCmsId,
          sponsorableTimeUtc: townMyMate.sponsorableTimeUtc,
          curTimeUtc,
        }
      );
    }

    // 가속 아이템
    const itemChange: ItemChange = user.userInven.itemInven.buildItemChange(
      itemCmsId,
      -count,
      true
    );
    const cost_data: CostData[] = [
      {
        id: itemCmsId,
        amt: count,
        type: ITEM_TYPE[itemCms.type],
      },
    ];
    if (itemChange.count < 0) {
      throw new MError('not-enough-item', MErrorCode.NOT_ENOUGH_ITEM, {
        itemChange,
      });
    }

    // 클라 SyncData, null 관련해서 만료 처리를 현재 시간으로 보내줌..
    const reduceSecOfOnce = itemCms.timeCostValMin * SECONDS_PER_MINUTE;
    const oldRemainingTimeSec = townMyMate.sponsorableTimeUtc - curTimeUtc;

    // 최대 사용 가능한 갯수를 구함
    const maxCount: number = Math.ceil(oldRemainingTimeSec / reduceSecOfOnce);
    if (count > maxCount) {
      throw new MError('over-userd-reduce-item', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
        itemCmsId,
        count,
        maxCount,
      });
    }

    const newSponsorableTimeUtc: number = Math.max(
      townMyMate.sponsorableTimeUtc - count * reduceSecOfOnce,
      curTimeUtc
    );

    const resp: Resp = { sync: {} };
    const accums: AccumulateParam[] = [];

    return Promise.resolve()
      .then(() => {
        return tuReduceMyMateSponsorWaitTime(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          undefined,
          itemChange,
          townCmsId,
          townMyMate.index,
          newSponsorableTimeUtc
        );
      })
      .then(() => {
        user.glog(
          'time_reduction',
          MakePubMyMateSponsorTimeReductionData({
            rsn,
            add_rsn,

            old_duration: townMyMate.sponsorableTimeUtc - curTimeUtc,
            cur_duration: newSponsorableTimeUtc - curTimeUtc,
            pr_data: null,
            cost_data,
            mate_support_data: {
              town_id: townCmsId,
              town_name: townCms.name,
              mate_id: mateCmsId,
              mate_name: displayNameUtil.getMateDisplayName(mateCmsId),
            },
          })
        );

        _.merge<Sync, Sync>(
          resp.sync,
          user.userInven.itemInven.applyItemChange(itemChange, accums, {
            user,
            rsn,
            add_rsn,
          })
        );

        townMyMate.sponsorableTimeUtc = newSponsorableTimeUtc;
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            towns: {
              [townCmsId]: {
                myPubMyMates: {
                  mates: {
                    [mateCmsId]: {
                      sponsorableTimeUtc: newSponsorableTimeUtc,
                    },
                  },
                },
              },
            },
          },
        });

        if (accums.length > 0) {
          return user.userAchievement.accumulate(accums, user, resp.sync, { user, rsn, add_rsn });
        }
      })
      .then(() => {
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, resp);
      });
  }
}
