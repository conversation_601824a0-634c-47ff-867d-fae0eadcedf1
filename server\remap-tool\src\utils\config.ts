import fs from 'fs-extra';
import path from 'path';
import JSON5 from 'json5';
import { DatabaseConfigs, RedisConfigs } from '../types';
import { getLogger } from './logger';

export class ConfigLoader {
  private configDir: string;
  private logger = getLogger();

  constructor(configDir: string = './config') {
    this.configDir = configDir;
  }

  async loadConfig(): Promise<DatabaseConfigs> {
    const configPath = path.join(this.configDir, 'database.json5');

    if (!await fs.pathExists(configPath)) {
      throw new Error(`Configuration file not found: ${configPath}`);
    }

    try {
      const configContent = await fs.readFile(configPath, 'utf8');
      const config = JSON5.parse(configContent) as DatabaseConfigs;
      this.validateDatabaseConfig(config);
      this.logger.info(`Configuration loaded: ${configPath}`);
      return config;
    } catch (error) {
      this.logger.error(`Failed to load configuration: ${configPath}`, error);
      throw error;
    }
  }

  private validateDatabaseConfig(config: DatabaseConfigs): void {
    if (!config.sharedConfig || !config.worlds) {
      throw new Error('Database configuration is incomplete. sharedConfig and worlds are required.');
    }

    this.validateSharedConfig(config.sharedConfig);

    if (!Array.isArray(config.worlds) || config.worlds.length === 0) {
      throw new Error('worlds must be a non-empty array.');
    }

    config.worlds.forEach((world, index) => {
      this.validateWorldConfig(world, `worlds[${index}]`);
    });
  }

  private validateSharedConfig(config: any): void {
    if (!config.mysqlAuthDb) {
      throw new Error('sharedConfig.mysqlAuthDb is not configured.');
    }
    this.validateSingleDatabaseConfig(config.mysqlAuthDb, 'sharedConfig.mysqlAuthDb');

    const requiredRedis = [
      'authRedis',
      'monitorRedis',
      'orderRedis',
      'globalMatchRedis',
      'globalBattleLogRedis'
    ];
    for (const redisName of requiredRedis) {
      if (!config[redisName]) {
        throw new Error(`sharedConfig.${redisName} is not configured.`);
      }
    }
  }

  private validateWorldConfig(world: any, name: string): void {
    if (!world.id || typeof world.id !== 'string') {
      throw new Error(`${name}.id is not configured or is not a string.`);
    }

    if (!world.mysqlUserDb) {
      throw new Error(`${name}.mysqlUserDb is not configured.`);
    }

    if (!world.mysqlWorldDb) {
      throw new Error(`${name}.mysqlWorldDb is not configured.`);
    }
    this.validateSingleDatabaseConfig(world.mysqlWorldDb, `${name}.mysqlWorldDb`);

    const userDb = world.mysqlUserDb;
    if (!userDb.sqlDefaultCfg) {
      throw new Error(`${name}.mysqlUserDb.sqlDefaultCfg is not configured.`);
    }
    this.validateSingleDatabaseConfig(userDb.sqlDefaultCfg, `${name}.mysqlUserDb.sqlDefaultCfg`);

    if (!Array.isArray(userDb.shards) || userDb.shards.length === 0) {
      throw new Error(`${name}.mysqlUserDb.shards must be a non-empty array.`);
    }

    userDb.shards.forEach((shard: any, shardIndex: number) => {
      if (typeof shard.shardId !== 'number') {
        throw new Error(`${name}.mysqlUserDb.shards[${shardIndex}].shardId must be a number.`);
      }
      if (!shard.sqlCfg || !shard.sqlCfg.database) {
        throw new Error(`${name}.mysqlUserDb.shards[${shardIndex}].sqlCfg.database is not configured.`);
      }
    });
  }

  private validateSingleDatabaseConfig(config: any, name: string): void {
    const requiredFields = [
      'host',
      'port',
      'user',
      'password',
      'database'
    ];

    for (const field of requiredFields) {
      if (!config[field]) {
        throw new Error(`${name}.${field} is not configured.`);
      }
    }

    if (typeof config.port !== 'number' || config.port <= 0 || config.port > 65535) {
      throw new Error(`${name}.port must be a number between 1-65535.`);
    }
  }

  private validateRedisConfig(config: RedisConfigs): void {
    const requiredConnections = [
      'userCache',
      'monitor',
      'order',
      'nation',
      'auth'
    ];

    for (const connectionName of requiredConnections) {
      if (!config[connectionName as keyof RedisConfigs]) {
        throw new Error(`${connectionName} is missing in Redis configuration.`);
      }

      this.validateSingleRedisConfig(config[connectionName as keyof RedisConfigs], connectionName);
    }
  }

  private validateSingleRedisConfig(config: any, name: string): void {
    const requiredFields = ['host', 'port', 'db'];

    for (const field of requiredFields) {
      if (config[field] === undefined || config[field] === null) {
        throw new Error(`${name}.${field} is not configured.`);
      }
    }

    if (typeof config.port !== 'number' || config.port <= 0 || config.port > 65535) {
      throw new Error(`${name}.port must be a number between 1-65535.`);
    }

    if (typeof config.db !== 'number' || config.db < 0 || config.db > 255) {
      throw new Error(`${name}.db must be a number between 0-255.`);
    }
  }

  async checkConfigFiles(): Promise<{ database: boolean; redis: boolean }> {
    const databasePath = path.join(this.configDir, 'database.json5');

    return {
      database: await fs.pathExists(databasePath),
      redis: true,
    };
  }

  async copyExampleConfigs(): Promise<void> {
    const databaseExamplePath = path.join(this.configDir, 'database.example.json5');
    const databasePath = path.join(this.configDir, 'database.json5');

    if (await fs.pathExists(databaseExamplePath) && !await fs.pathExists(databasePath)) {
      await fs.copy(databaseExamplePath, databasePath);
      this.logger.info(`Example database configuration file copied: ${databasePath}`);
    }
  }
}
