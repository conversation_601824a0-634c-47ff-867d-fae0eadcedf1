module.exports = {
  apps: [
    /* configd */
    {
      name: 'configd',
      cwd: '../node',
      script: 'dist/configd/configd.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* authd */
    {
      name: 'authd',
      cwd: '../node',
      script: 'dist/authd/authd.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* lobbyd */
    {
      name: 'lobbyd1',
      cwd: '../node',
      script: 'dist/lobbyd/lobbyd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-01',
        UWO_APP_INSTANCE_ID: 0
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      // shutdown_with_message: true, // Windows OS
      kill_timeout: 80000, // 80 seconds
      exp_backoff_restart_delay: 500,
    },

    {
      name: 'lobbyd2',
      cwd: '../node',
      script: 'dist/lobbyd/lobbyd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-02',
        UWO_APP_INSTANCE_ID: 1
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      // shutdown_with_message: true, // Windows OS
      kill_timeout: 80000, // 80 seconds
      exp_backoff_restart_delay: 500,
    },
    {
      name: 'lobbyd3',
      cwd: '../node',
      script: 'dist/lobbyd/lobbyd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-03',
        UWO_APP_INSTANCE_ID: 2,
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      // shutdown_with_message: true, // Windows OS
      kill_timeout: 80000, // 80 seconds
      exp_backoff_restart_delay: 500,
    },

    /* oceand */
    {
      name: 'oceand1',
      cwd: '../node',
      script: 'dist/oceand/oceand.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-01',
        UWO_APP_INSTANCE_ID: 0
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    {
      name: 'oceand2',
      cwd: '../node',
      script: 'dist/oceand/oceand.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-02',
        UWO_APP_INSTANCE_ID: 1
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },
    {
      name: 'oceand3',
      cwd: '../node',
      script: 'dist/oceand/oceand.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-03',
        UWO_APP_INSTANCE_ID: 2,
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* saild */
    {
      name: 'saild1',
      cwd: '../node',
      script: 'dist/saild/saild.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-01',
        UWO_APP_INSTANCE_ID: 0
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    {
      name: 'saild2',
      cwd: '../node',
      script: 'dist/saild/saild.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-02',
        UWO_APP_INSTANCE_ID: 1
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },
    {
      name: 'saild3',
      cwd: '../node',
      script: 'dist/saild/saild.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-03',
        UWO_APP_INSTANCE_ID: 2,
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* townd */
    {
      name: 'townd1',
      cwd: '../node',
      script: 'dist/townd/townd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-01',
        UWO_APP_INSTANCE_ID: 0
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    {
      name: 'townd2',
      cwd: '../node',
      script: 'dist/townd/townd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-02',
        UWO_APP_INSTANCE_ID: 1
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },
    {
      name: 'townd3',
      cwd: '../node',
      script: 'dist/townd/townd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-03',
        UWO_APP_INSTANCE_ID: 2,
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* zonelbd */
    {
      name: 'zonelbd1',
      cwd: '../node',
      script: 'dist/zonelbd/zonelbd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-01',
        UWO_APP_INSTANCE_ID: 0
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    {
      name: 'zonelbd2',
      cwd: '../node',
      script: 'dist/zonelbd/zonelbd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-02',
        UWO_APP_INSTANCE_ID: 1
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },
    {
      name: 'zonelbd3',
      cwd: '../node',
      script: 'dist/zonelbd/zonelbd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-03',
        UWO_APP_INSTANCE_ID: 2,
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* realmd */
    {
      name: 'realmd1',
      cwd: '../node',
      script: 'dist/realmd/realmd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-01',
        UWO_APP_INSTANCE_ID: 0
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    {
      name: 'realmd2',
      cwd: '../node',
      script: 'dist/realmd/realmd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-02',
        UWO_APP_INSTANCE_ID: 1
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },
    {
      name: 'realmd3',
      cwd: '../node',
      script: 'dist/realmd/realmd.js',
      env: {
        NODE_ENV: 'development',
        WORLD_ID: 'UWO-GL-03',
        UWO_APP_INSTANCE_ID: 2,
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },
  ] /* apps */

};
