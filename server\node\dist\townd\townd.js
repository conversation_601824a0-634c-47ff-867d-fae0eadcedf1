"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
// Set process name.
process.name = 'townd';
const mainEntry = __importStar(require("../motiflib/mainEntry"));
mainEntry.start();
require("reflect-metadata");
const server = __importStar(require("./server"));
server.start();
const finalizers_1 = require("../motiflib/finalizers");
['SIGTERM', 'SIGINT', 'SIGHUP', 'SIGQUIT'].forEach((signal) => {
    process.on(signal, async () => {
        mainEntry.stop(signal);
        (0, finalizers_1.cleanupFinalizers)();
        await server.stop();
    });
});
//# sourceMappingURL=townd.js.map