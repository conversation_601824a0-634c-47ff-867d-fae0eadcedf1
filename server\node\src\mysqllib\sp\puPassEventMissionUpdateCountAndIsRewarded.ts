// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MError, MErrorCode } from '../../motiflib/merror';
import mconf from '../../motiflib/mconf';
import mlog from '../../motiflib/mlog';

export const spName = 'mp_u_pass_event_mission_update_count_and_is_rewarded';
export const errorCode = MErrorCode.PASS_EVENT_MISSION_UPDATE_COUNT_AND_IS_REWARDED_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  eventPageCmsId: number,
  eventMissionCmsId: number,
  count: number,
  repeatedRewardReceiveCount: number,
  isRewarded: number
): Promise<void> {
  return spFunction(
    connection,
    userId,
    eventPageCmsId,
    eventMissionCmsId,
    count,
    repeatedRewardReceiveCount,
    isRewarded
  )
    .then((qr) => {
      if (qr.rows[0][0]['ROW_COUNT()'] !== '1' && qr.rows[0][0]['ROW_COUNT()'] !== '2') {
        mlog.error(spName + ' is failed', {
          userId,
          eventPageCmsId,
          eventMissionCmsId,
          count,
          repeatedRewardReceiveCount,
          isRewarded,
        });
      }
      return;
    })
    .catch(catchHandler);
}
