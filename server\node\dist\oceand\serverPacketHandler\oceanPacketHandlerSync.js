"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const merror_1 = require("../../motiflib/merror");
const typedi_1 = require("typedi");
const fleetManager_1 = require("../fleetManager");
const enum_1 = require("../../motiflib/model/ocean/enum");
const server_1 = require("../server");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const router = tcp.createPacketRouter();
// ----------------------------------------------------------------------------
// 로비서버와 연결 완료
// ----------------------------------------------------------------------------
router.on(protocol_1.Protocol.LB2OC_REQ_CONNECTED, async (req, res) => {
    const { oceanSyncManager } = typedi_1.Container.get(server_1.OceanService);
    const packet = new protocol_1.Protocol.OC2LB_RES_CONNECTED();
    // 월드데이터(국가랭킹,우호도 등..)가 필요시 요청.
    packet.bNeedWorldSyncData = oceanSyncManager.needWorldSyncData();
    res.send(packet);
});
// ----------------------------------------------------------------------------
// 월드데이터 업데이트
// ----------------------------------------------------------------------------
router.on(protocol_1.Protocol.LB2OC_NTF_WORLD_SYNC_DATA, async (req, res) => {
    const { oceanSyncManager } = typedi_1.Container.get(server_1.OceanService);
    // 국가별 우호도 업데이트
    if (req.nationIntimacies) {
        oceanSyncManager.setNationIntimacies(req.nationIntimacies);
    }
    // 권역 점유율 업데이트
    if (req.nationShareRatePerRegion) {
        oceanSyncManager.setNationShareRate(req.nationShareRatePerRegion);
    }
    if (req.raidDoodadSpawnOnOff !== undefined) {
        oceanSyncManager.setRaidDoodadSpawnOnOff(req.raidDoodadSpawnOnOff);
    }
});
// ----------------------------------------------------------------------------
// 항해중 버프의 추가/업데이트.
// ----------------------------------------------------------------------------
router.on(protocol_1.Protocol.LB2OC_NTF_BUFF_SYNC_UPDATE, async (req, res) => {
    const userId = req.userId;
    const buffNub = req.obsn;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        mlog_1.default.warn('User not found in this oceand! (buff update)', { userId });
        return;
    }
    fleet.updateBuff(buffNub);
    if (fleet.shouldNotifyOcean(buffNub.sourceType)) {
        const packet = new protocol_1.Protocol.OC2CL_NTF_BUFF_SYNC_UPDATE();
        packet.userId = fleet.userId;
        packet.obsn = buffNub;
        fleet.sendBroadCastAdjacentGrids(packet);
    }
});
// ----------------------------------------------------------------------------
// 항해중 버프의 삭제.
// ----------------------------------------------------------------------------
router.on(protocol_1.Protocol.LB2OC_NTF_BUFF_SYNC_REMOVE, async (req, res) => {
    const userId = req.userId;
    const buffCmsId = req.buffCmsId;
    const targetId = req.targetId;
    const sourceType = req.sourceType;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        mlog_1.default.warn('User not found in this oceand! (buff remove)', { userId });
        return;
    }
    fleet.removeBuff(buffCmsId, targetId);
    if (fleet.shouldNotifyOcean(sourceType)) {
        const packet = new protocol_1.Protocol.OC2CL_NTF_BUFF_SYNC_REMOVE();
        packet.userId = fleet.userId;
        packet.buffCmsId = buffCmsId;
        packet.targetId = targetId;
        fleet.sendBroadCastAdjacentGrids(packet);
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_BATTLE_OR_DUEL_START, async (req, res) => {
    const userId = req.userId;
    const bDuel = req.bDuel;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.getLocalNpcSpawnEntry().pause();
    const newOceanUserState = bDuel ? enum_1.OCEAN_USER_STATE.DUEL : enum_1.OCEAN_USER_STATE.BATTLE;
    fleet.setStateAndBroadCast(newOceanUserState, true);
});
router.on(protocol_1.Protocol.LB2OC_NTF_BATTLE_OR_DUEL_RESUME, async (req, res) => {
    const userId = req.userId;
    const bDuel = req.bDuel;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.getLocalNpcSpawnEntry().pause();
    const newOceanUserState = bDuel ? enum_1.OCEAN_USER_STATE.DUEL : enum_1.OCEAN_USER_STATE.BATTLE;
    fleet.setStateAndBroadCast(newOceanUserState, true);
});
router.on(protocol_1.Protocol.LB2OC_NTF_BATTLE_OR_DUEL_END, async (req, res) => {
    const userId = req.userId;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_CHANGE_LAND_ANCHOR, async (req, res) => {
    const userId = req.userId;
    const bLandAnchoring = req.bLandAnchoring;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setLandAnchoring(bLandAnchoring);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_CHANGE_LAND_ANCHOR();
    packet.userId = userId;
    packet.bLandAnchoring = bLandAnchoring;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_VILLAGE_ENTER, async (req, res) => {
    const userId = req.userId;
    const bVillageEnter = req.bVillageEnter;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setVillageEnter(bVillageEnter);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_VILLAGE_ENTER();
    packet.userId = userId;
    packet.bVillageEnter = bVillageEnter;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_CHANGE_CUSTOMIZE, async (req, res) => {
    const { userId, shipCustomizing, shipSailPattern } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setCustomizing(shipCustomizing, shipSailPattern);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_CHANGE_CUSTOMIZE();
    packet.userId = userId;
    packet.shipCustomizing = shipCustomizing;
    packet.shipSailPattern = shipSailPattern;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_UPDATE_USER_GAMEOVER, async (req, res) => {
    const { userId, bGameOver } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setGameOver(bGameOver);
});
router.on(protocol_1.Protocol.LB2OC_NTF_SYNC_FLEET_STAT, async (req, res) => {
    const { userId, wpes } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    wpes.forEach((elem) => {
        fleet.setWpe(elem.id, elem.value);
    });
});
router.on(protocol_1.Protocol.LB2OC_NTF_SYNC_COMPANY_LEVEL, async (req, res) => {
    const { userId, companyLevel } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setCompanyLevel(companyLevel);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_CHANGE_COMPANY_LEVEL();
    packet.userId = userId;
    packet.companyLevel = companyLevel;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_SYNC_KARMA, async (req, res) => {
    const { userId, karma, lastKarmaUpdateTimeUtc } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setKarma(karma, lastKarmaUpdateTimeUtc);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_CHANGE_KARMA();
    packet.userId = userId;
    packet.karma = karma;
    packet.lastKarmaUpdateTimeUtc = lastKarmaUpdateTimeUtc;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_GUILD_LEAVE, async (req, res) => {
    const { userId } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setGuildAppearance(null);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_GUILD_LEAVE();
    packet.userId = userId;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_GUILD_UPDATE, async (req, res) => {
    const { userId, guild } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setGuildAppearance(guild);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_GUILD_UPDATE();
    packet.userId = userId;
    packet.guild = guild;
    fleet.sendBroadCastAdjacentGrids(packet);
});
// 국가 관직 정보 업데이트
router.on(protocol_1.Protocol.LB2OC_NTF_NATION_CABINET_UPDATE, async (req, res) => {
    const { userId, nationCabinet } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        mlog_1.default.warn('User not found in this oceand!', { userId });
        return;
    }
    fleet.setNationCabinetAppearance(nationCabinet);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_NATION_CABINET_UPDATE();
    packet.userId = userId;
    packet.nationCabinet = nationCabinet;
    fleet.sendBroadCastAdjacentGrids(packet);
});
// 국가 관직 정보 삭제
router.on(protocol_1.Protocol.LB2OC_NTF_NATION_CABINET_LEAVE, async (req, res) => {
    const { userId } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        mlog_1.default.warn('User not found in this oceand!', { userId });
        return;
    }
    fleet.setNationCabinetAppearance(null);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_NATION_CABINET_LEAVE();
    packet.userId = userId;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_TRAINING_GRADE, async (req, res) => {
    const { userId, admiralTrainingGrade } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setTrainingGrade(admiralTrainingGrade);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_ILLUST, async (req, res) => {
    const { userId, admiralIllustCmsId } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setIllust(admiralIllustCmsId);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_CHANGE_USER_TITLE, async (req, res) => {
    const { userId, userTitle } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setUserTitle(userTitle);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_CHANGE_USER_TITLE();
    packet.userId = userId;
    packet.userTitle = userTitle;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_CHANGE_REPRESENTED_MATE, async (req, res) => {
    const { userId, representedMateCmsId, representedMateIllustCmsId } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    // zoomInUser 요청시에만 필요하므로 브로드캐스트하지 않음.
    fleet.setRepresentedMate(representedMateCmsId, representedMateIllustCmsId);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_SALVAGE_ENTER, async (req, res) => {
    const userId = req.userId;
    const bSalvageEnter = req.bSalvageEnter;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setSalvageEnter(bSalvageEnter);
    const packet = new protocol_1.Protocol.OC2CL_NTF_NET_USER_SALVAGE_ENTER();
    packet.userId = userId;
    packet.bSalvageEnter = bSalvageEnter;
    fleet.sendBroadCastAdjacentGrids(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_IS_TRANSCENDED, async (req, res) => {
    const { userId, admiralIsTranscended } = req;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setIsTranscended(admiralIsTranscended);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_USER_CONTINUOUS_SWEEP_REWARD_ENTER_OR_LEAVE, async (req, res) => {
    const userId = req.userId;
    const bEnterSweepReward = req.bEnterSweepReward;
    const fleetManager = typedi_1.Container.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const newOceanUserState = bEnterSweepReward
        ? enum_1.OCEAN_USER_STATE.SWEEP_REWARD
        : enum_1.OCEAN_USER_STATE.SAIL;
    fleet.setStateAndBroadCast(newOceanUserState, true);
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerSync.js.map