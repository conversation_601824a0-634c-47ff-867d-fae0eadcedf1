// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mhttp from '../../../motiflib/mhttp';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../index';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

/**
 * 볼란테 (채팅)서버에 클라이언트가 접속하기 전에 사전 준비해야 될 작업을 여기서 진행합니다.
 */
// ----------------------------------------------------------------------------
export class Cph_Common_ChatInit implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    if (!user.userState.isCompanyCreated()) {
      throw new MError(
        'company-is-not-created-yet',
        MErrorCode.TRY_INIT_CHAT_BEFORE_COMPANY_CREATED,
        {
          gameState: user.userState.getGameState(),
        }
      );
    }

    const { userName, nationCmsId } = user;
    const { guildId } = user.userGuild;
    const guildName = user.userGuild.getGuildName();
    if (!userName) {
      throw new MError('no-user-name', MErrorCode.TRY_INIT_CHAT_BEFORE_COMPANY_CREATED, {
        gameState: user.userState.getGameState(),
      });
    }
    const volanteId = user.userId.toString();
    const volanteToken = mhttp.chatd.getIdToken(volanteId);
    const extraData = { nationCmsId, guildId, guildName };

    return mhttp.chatd
      .createUserIfNotExists(volanteId, userName)
      .then(() => mhttp.chatd.updateUser(volanteId, userName, extraData))
      .then((channels) =>
        user.sendJsonPacket(packet.seqNum, packet.type, { volanteToken, channels })
      );
  }
}
