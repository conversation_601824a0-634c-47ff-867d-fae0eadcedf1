import { <PERSON>Buffer } from 'smart-buffer';
import {
  OCEAN_ZONE_LEAVE_REASON,
  NationIntimacyEdge,
  GuildAppearance,
  NationShareRatePerRegion,
  NationCabinetAppearance,
} from '../../motiflib/model/lobby';
import { EncountResult, EncountTargetType } from '../../motiflib/model/ocean/enum';
import {
  Location,
  NpcFleetSyncData,
  OceanBuffSyncNub,
  UserFleetSyncData,
  OceanFleetData,
  UserFleetSyncDataForOcean,
  ZoomInNpcDesc,
  OceanDoodadSyncData,
  ShipCustomizing,
  ShipSailPattern,
  ZoomInUserDesc,
  ReinforcementFleet,
  AnchorInfo,
} from '../../motiflib/model/ocean';
import { PacketFactory } from '../../tcplib';
import { EnumPacketOption, IPacket } from '../../tcplib/shared/basic-packets';
import * as proto from '../lobby/proto';
import './smart-buffer-ex';
import { RequiredEncountChoiceStats } from '../../motiflib/model/ocean';
import { MError, MErrorCode } from '../../motiflib/merror';
import { SEA_AREA_TYPE } from '../../cms/ex';
import mconf from '../../motiflib/mconf';
import { MIPacket } from '../ssNet';
import _ from 'lodash';
import { UserTitle } from '../../lobbyd/userTitles';
import mlog from '../../motiflib/mlog';

export namespace Protocol {
  export enum EnumPacket {
    // tslint:disable
    // 클라이언트에게 Relay패킷으로 통해 다이렉트로 보내는 패킷
    PT_OC2CL_NTF_ADD_FLEET = proto.Ocean.ADD_FLEET_SC,
    PT_OC2CL_NTF_REMOVE_OCEAN_ENTITY = proto.Ocean.REMOVE_OCEAN_ENTITY_SC,
    PT_OC2CL_NTF_NET_USER_MOVE = proto.Ocean.NET_USER_MOVE_SC,
    PT_OC2CL_NTF_NPC_SPAWN = proto.Ocean.NPC_SPAWN_SC,
    PT_OC2CL_NTF_NPC_DESPAWN = proto.Ocean.NPC_DESPAWN_SC,
    PT_OC2CL_NTF_UPDATE_ZONE_SYNC_DATA = proto.Ocean.UPDATE_ZONE_SYNC_DATA_SC,
    PT_OC2CL_NTF_BUFF_SYNC_UPDATE = proto.Ocean.BUFF_SYNC_UPDATE_SC,
    PT_OC2CL_NTF_BUFF_SYNC_REMOVE = proto.Ocean.BUFF_SYNC_REMOVE_SC,
    PT_OC2CL_NTF_NPC_FLLET_MOVE = proto.Ocean.NPC_FLEET_MOVE_SC,
    PT_OC2CL_NTF_NPC_FLLET_STOP = proto.Ocean.NPC_FLEET_STOP_SC,
    PT_OC2CL_NTF_NPC_FLEET_MOVE_SYNC = proto.Ocean.NPC_FLEET_MOVE_SYNC_SC,
    PT_OC2CL_NTF_DOODAD_SPAWN = proto.Ocean.DOODAD_SPAWN_SC,
    PT_OC2CL_NTF_DOODAD_DESPAWN = proto.Ocean.DOODAD_DESPAWN_SC,
    PT_OC2CL_NTF_NET_USER_CHANGE_STATE = proto.Ocean.NET_USER_CHANGE_STATE_SC,
    PT_OC2CL_NTF_NET_USER_CHANGE_QUEST_SINGLE_MODE = proto.Ocean
      .NET_USER_CHANGE_QUEST_SINGLE_MODE_SC,
    PT_OC2CL_NTF_DOODAD_SYNC_UPDATE = proto.Ocean.DOODAD_SYNC_UPDATE_SC,
    PT_OC2CL_NTF_NET_USER_CHANGE_LAND_ANCHOR = proto.Ocean.NET_USER_CHANGE_LAND_ANCHOR_SC,
    PT_OC2CL_NTF_NET_USER_VILLAGE_ENTER = proto.Ocean.NET_USER_VILLAGE_ENTER_SC,
    PT_OC2CL_NTF_NET_USER_CHANGE_CUSTOMIZE = proto.Ocean.NET_USER_CHANGE_CUSTOMIZE_SC,
    PT_OC2CL_NTF_WORLD_MAP_NPC_LOCATION = proto.Ocean.WORLD_MAP_NPC_LOCATION_SC,
    PT_OC2CL_NTF_NET_USER_TELEPORT_TO_LOCATION = proto.Ocean.NET_USER_TELEPORT_TO_LOCATION_SC,
    PT_OC2CL_NTF_SHOW_EMOTICON_INSTANT = proto.Ocean.SHOW_EMOTICON_INSTANT_SC,
    PT_OC2CL_NTF_NET_USER_CHANGE_COMPANY_LEVEL = proto.Ocean.NET_USER_CHANGE_COMPANY_LEVEL_SC,
    PT_OC2CL_NTF_NET_USER_CHANGE_KARMA = proto.Ocean.NET_USER_CHANGE_KARMA_SC,
    PT_OC2CL_NTF_NET_USER_GUILD_LEAVE = proto.Ocean.NET_USER_GUILD_LEAVE_SC,
    PT_OC2CL_NTF_NET_USER_GUILD_UPDATE = proto.Ocean.NET_USER_GUILD_UPDATE_SC,

    PT_OC2CL_NTF_SERVER_DEBUG_MSG = proto.Dev.SERVER_DEBUG_MSG_SC,
    PT_OC2CL_NTF_ELITE_NPC_SPAWN_NOTIFICATION = proto.Ocean.ELITE_NPC_SPAWN_NOTIFICATION_SC,

    PT_OC2CL_NTF_NET_USER_CHANGE_USER_TITLE = proto.Ocean.NET_USER_CHANGE_USER_TITLE_SC,

    PT_OC2CL_NTF_DELEGATE_NAVI_QUERY = proto.Ocean.DELEGATE_NAVI_QUERY_SC,

    PT_OC2CL_NTF_NET_USER_NATION_CABINET_LEAVE = proto.Ocean.NET_USER_NATION_CABINET_LEAVE_SC,
    PT_OC2CL_NTF_NET_USER_NATION_CABINET_UPDATE = proto.Ocean.NET_USER_NATION_CABINET_UPDATE_SC,

    PT_OC2CL_NTF_NET_USER_SALVAGE_ENTER = proto.Ocean.NET_USER_SALVAGE_ENTER_SC,
    //---------------------------------------------------------------------------
    // 요청 -> 응답 패킷 리스트
    //---------------------------------------------------------------------------
    PT_LB2OC_REQ_ENTER = 30001,
    PT_OC2LB_RES_ENTER = 30002,

    PT_LB2OC_REQ_LEAVE = 30003,
    PT_OC2LB_RES_LEAVE = 30004,

    // 로딩 완료 요청
    PT_LB2OC_REQ_LOAD_COMPLETE = 30005,
    PT_OC2LB_RES_LOAD_COMPLETE = 30006,

    // 유저의 인카운트 요청
    PT_LB2OC_REQ_ENCOUNT_BY_USER = 30007,
    PT_OC2LB_RES_ENCOUNT_BY_USER = 30008,

    // 인카운트 종료
    PT_LB2OC_REQ_ENCOUNT_END = 30009,
    PT_OC2LB_RES_ENCOUNT_END = 30010,

    // 유저 함대 이동 (비용절감을 위해 응답은 없다.)
    PT_LB2OC_NTF_OCEAN_MOVE = 30011,

    // 로비서버의 유저 인카운트 상태를 초기화
    PT_OC2LB_NTF_USER_ENCOUNT_STATE_CLEAR = 30012,

    // NPC 인카운트 발생
    PT_OC2LB_REQ_ENCOUNT_BY_NPC = 30013,
    PT_LB2OC_RES_ENCOUNT_BY_NPC = 30014,

    // 인카운트 무시
    PT_LB2OC_NTF_DEV_IGNORE_NPC_ENCOUNT = 30015,
    // 근접한 위치에 NPC 스폰
    PT_LB2OC_NTF_DEV_ADD_NEAR_SPAWNER = 30016,
    PT_LB2OC_NTF_DEV_REMOVE_NEAR_SPAWNER = 30017,

    // 동기화
    PT_LB2OC_NTF_BUFF_SYNC_UPDATE = 30100, // 버프 생성
    PT_LB2OC_NTF_BUFF_SYNC_REMOVE = 30101, // 버프 해제

    PT_LB2OC_NTF_DEV_NPC_ATTACK_RADIUS = 30102, // npc 인카운트 범위 변경(dev)
    PT_LB2OC_NTF_DEV_NPC_TICK_PER_SEC = 30103,

    PT_LB2OC_REQ_ZOOM_IN_USER = 30104,
    PT_OC2LB_RES_ZOOM_IN_USER = 30105,

    PT_LB2OC_REQ_ZOOM_IN_NPC = 30106,
    PT_OC2LB_RES_ZOOM_IN_NPC = 30107,

    PT_LB2OC_REQ_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER = 30108,
    PT_OC2LB_RES_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER = 30109,

    PT_LB2OC_REQ_SPAWN_OCEAN_DOODAD = 30111,
    PT_OC2LB_RES_SPAWN_OCEAN_DOODAD = 30112,

    PT_LB2OC_REQ_DESPAWN_OCEAN_DOODAD = 30113,
    PT_OC2LB_RES_DESPAWN_OCEAN_DOODAD = 30114,

    // 근접한 위치에 Doodad(해양오브젝트) 스폰
    PT_LB2OC_NTF_DEV_ADD_OCEAN_DOODAD_NEAR_SPAWNER = 30115,
    PT_LB2OC_NTF_DEV_REMOVE_OCEAN_DOODAD_NEAR_SPAWNER = 30116,
    PT_LB2OC_NTF_SET_LOCAL_NPC_SPAWN = 30117,

    PT_LB2OC_NTF_BATTLE_OR_DUEL_START = 30118,
    PT_LB2OC_NTF_BATTLE_OR_DUEL_RESUME = 30119,
    PT_LB2OC_NTF_BATTLE_OR_DUEL_END = 30120,

    PT_LB2OC_REQ_OCEAN_DOODAD_INFO = 30121,
    PT_OC2LB_RES_OCEAN_DOODAD_INFO = 30122,

    PT_LB2OC_NTF_SPAWN_OCEAN_LOCAL_DOODAD = 30123,

    PT_LB2OC_REQ_QUEST_SPAWN_LOCAL_NPC = 30124,
    PT_OC2LB_RES_QUEST_SPAWN_LOCAL_NPC = 30125,
    PT_LB2OC_REQ_QUEST_DESPAWN_LOCAL_NPC = 30126,
    PT_OC2LB_RES_QUEST_DESPAWN_LOCAL_NPC = 30127,

    PT_LB2OC_NTF_CHANGE_QUEST_SINGLE_MODE = 30128,

    //현재 해양에 존재하는 유저의 로컬 오션두다드 수량 업데이트(영구타입은 제외)
    PT_OC2LB_NTF_UPDATE_OCEAN_LOCAL_DOODAD_NUM = 30129,

    //해양오브젝트의 효과를 발동시킨다
    PT_OC2LB_NTF_APPLY_OCEAN_DOODAD_EFFECT = 30130,

    PT_LB2OC_NTF_SET_LOCAL_DOODAD_SPAWN = 30131,

    // 인카운트 취소 통보
    PT_LB2OC_NTF_CANCEL_ENCOUNT = 30132,

    PT_LB2OC_NTF_NET_USER_CHANGE_LAND_ANCHOR = 30133,
    PT_LB2OC_NTF_NET_USER_CHANGE_CUSTOMIZE = 30134,

    // 유저 함대 텔리포트
    PT_LB2OC_NTF_OCEAN_TELEPORT = 30135,

    PT_LB2OC_NTF_CHANGE_QUEST_NPC_MOVE_AI = 30136,
    PT_LB2OC_NTF_CHANGE_QUEST_NPC_ATTACK_AI = 30137,

    PT_LB2OC_NTF_DEV_ADD_USER_DATA_SPAWNER = 30138,

    PT_OC2LB_REQ_ENCOUNT_BY_NET_USER = 30139,
    PT_LB2OC_RES_ENCOUNT_BY_NET_USER = 30140,

    // deprecated
    PT_OC2LB_REQ_GET_NPC_FLEET_DATA_FOR_USER = 30141,
    PT_LB2OC_RES_GET_NPC_FLEET_DATA_FOR_USER = 30142,

    PT_LB2OC_NTF_UPDATE_USER_GAMEOVER = 30143,

    PT_LB2OC_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER = 30144,
    PT_OC2LB_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER = 30145,

    PT_LB2OC_NTF_SHOW_EMOTICON_INSTANT = 30146,

    PT_LB2OC_NTF_NET_USER_VILLAGE_ENTER = 30147,

    PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_TRAINING_GRADE = 30148,
    PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_ILLUST = 30149,

    PT_LB2OC_NTF_NET_USER_SALVAGE_ENTER = 30150,

    PT_LB2OC_REQ_CONNECTED = 39900,
    PT_OC2LB_RES_CONNECTED = 39901,
    PT_LB2OC_NTF_WORLD_SYNC_DATA = 39902,

    PT_OC2LB_NTF_UPDATE_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_ENTRIES = 39903,
    PT_LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_CHECK_SUCCESS = 39904,

    PT_LB2OC_NTF_DEV_ATTACK_TO_ME = 39905,

    // 유저의 인카운트 선택
    PT_LB2OC_REQ_ENCOUNT_USER_CHOICE = 39906,
    PT_OC2LB_RES_ENCOUNT_USER_CHOICE = 39907,

    // npc의 인카운트 선택
    PT_OC2LB_NTF_ENCOUNT_NPC_CHOICE = 39910,

    // 유저의 인카운트 선택을 상대유저에게 전달
    PT_OC2LB_REQ_ENCOUNT_NET_USER_CHOICE = 39912,
    PT_LB2OC_RES_ENCOUNT_NET_USER_CHOICE = 39913,

    PT_OC2LB_NTF_ENCOUNT_ENEMY_WRECKED = 39914,
    PT_OC2LB_NTF_ENCOUNT_ENEMY_LOGOUT = 39915,

    PT_OC2LB_NTF_ENCOUNT_END_FORCEDLY = 39916,

    //플레이어의 스탯값을 오션과 동기화
    PT_LB2OC_LB2OC_NTF_SYNC_FLEET_STAT = 39917,

    //
    PT_LB2OC_NTF_DEV_LOCAL_NPC_SPAWN_NUM = 39918,

    PT_LB2OC_NTF_SYNC_COMPANY_LEVEL = 39919,
    PT_LB2OC_NTF_SYNC_KARMA = 39920,

    PT_LB2OC_NTF_GUILD_LEAVE = 39922,
    PT_LB2OC_NTF_GUILD_UPDATE = 39923,

    PT_LB2OC_NTF_SERVER_DEBUG_MSG = 39924,

    PT_LB2OC_NTF_BATTLE_LOSE = 39925,

    PT_LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_DESPAWN = 39926,

    PT_LB2OC_NTF_CHANGE_USER_TITLE = 39927,

    PT_LB2OC_NTF_DELEGATE_NAVI_RESULT = 39928,

    PT_LB2OC_NTF_NET_SWEEP_LOCAL_NPC = 39929,
    PT_OC2LB_NTF_NET_SWEEP_LOCAL_NPC = 39930,

    PT_LB2OC_NTF_NATION_CABINET_LEAVE = 39931,
    PT_LB2OC_NTF_NATION_CABINET_UPDATE = 39932,

    PT_LB2OC_NTF_NET_USER_CHANGE_REPRESENTED_MATE = 39933,

    PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_IS_TRANSCENDED = 39934,

    PT_LB2OC_NTF_NET_USER_CONTINUOUS_SWEEP_REWARD = 39935,

    PT_OCEAN_MAX = 39999,
  }

  export class LB2OC_REQ_CONNECTED extends MIPacket {
    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_CONNECTED;
    }

    serialize(bf: SmartBuffer) {}

    deserialize(bf: SmartBuffer) {}
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_CONNECTED, LB2OC_REQ_CONNECTED);

  export class OC2LB_RES_CONNECTED extends MIPacket {
    bNeedWorldSyncData: boolean;
    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_CONNECTED;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt8(this.bNeedWorldSyncData === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.bNeedWorldSyncData = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_CONNECTED, OC2LB_RES_CONNECTED);

  export class LB2OC_NTF_WORLD_SYNC_DATA extends MIPacket {
    nationIntimacies?: NationIntimacyEdge[];
    nationShareRatePerRegion?: NationShareRatePerRegion;
    raidDoodadSpawnOnOff: boolean;
    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_WORLD_SYNC_DATA;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt8(this.nationIntimacies ? 1 : 0);
      if (this.nationIntimacies) {
        bf.writeInt32LE(this.nationIntimacies.length);
        if (this.nationIntimacies.length > 0) {
          this.nationIntimacies.forEach((nationIntimacy) => {
            bf.writeInt32LE(nationIntimacy.smallerNationCmsId);
            bf.writeInt32LE(nationIntimacy.largerNationCmsId);
            bf.writeInt32LE(nationIntimacy.intimacyValue);
          });
        }
      }

      bf.writeInt8(this.nationShareRatePerRegion ? 1 : 0);
      if (this.nationShareRatePerRegion) {
        const len = Object.keys(this.nationShareRatePerRegion).length;
        bf.writeInt32LE(len);
        if (len > 0) {
          _.forOwn(this.nationShareRatePerRegion, (elem, key) => {
            const regionCmsId = parseInt(key, 10);
            bf.writeInt32LE(regionCmsId);
            const nationLen = Object.keys(this.nationShareRatePerRegion[regionCmsId]).length;
            bf.writeInt32LE(nationLen);
            _.forOwn(this.nationShareRatePerRegion[regionCmsId], (share, nationCmsIdStr) => {
              const nationCmsId = parseInt(nationCmsIdStr, 10);
              bf.writeInt32LE(nationCmsId);
              bf.writeInt32LE(share);
            });
          });
        }
      }

      if (this.raidDoodadSpawnOnOff !== undefined) {
        bf.writeInt8(1);
        bf.writeInt8(this.raidDoodadSpawnOnOff ? 1 : 0);
      } else {
        bf.writeInt8(0);
      }
    }

    deserialize(bf: SmartBuffer) {
      if (bf.readInt8()) {
        this.nationIntimacies = [];
        const nationIntimacyLength = bf.readInt32LE();
        for (let i = 0; i < nationIntimacyLength; i++) {
          this.nationIntimacies.push({
            smallerNationCmsId: bf.readInt32LE(),
            largerNationCmsId: bf.readInt32LE(),
            intimacyValue: bf.readInt32LE(),
          });
        }
      }

      if (bf.readInt8()) {
        this.nationShareRatePerRegion = {};
        const nationSharePointPerRegionLength = bf.readInt32LE();
        for (let i = 0; i < nationSharePointPerRegionLength; i++) {
          const regionCmsId = bf.readInt32LE();
          if (!this.nationShareRatePerRegion[regionCmsId]) {
            this.nationShareRatePerRegion[regionCmsId] = {};
          }

          const nationLength = bf.readInt32LE();
          for (let j = 0; j < nationLength; j++) {
            const nationCmsId = bf.readInt32LE();
            const share = bf.readInt32LE();
            _.merge(this.nationShareRatePerRegion[regionCmsId], {
              [nationCmsId]: share,
            });
          }
        }
      }

      if (bf.readInt8() === 1) {
        this.raidDoodadSpawnOnOff = bf.readInt8() === 1 ? true : false;
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_WORLD_SYNC_DATA, LB2OC_NTF_WORLD_SYNC_DATA);

  export class LB2OC_REQ_ENCOUNT_BY_USER extends MIPacket {
    userId: number = 0;
    userKarmaCmsId: number;
    targetNpcId?: string = '';
    targetUserId?: number;

    // pvp용 상대방에게 전달할 정보.
    oceanFleetData: OceanFleetData;
    encountChoiceStats: RequiredEncountChoiceStats;
    ducatPoint: number;
    supplies: { cmsId: number; amount: number }[];
    tradeGoods: { cmsId: number; amount: number }[];
    pvpId: string;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_ENCOUNT_BY_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.userKarmaCmsId);

      if (this.targetNpcId) {
        bf.writeInt8(1);
        bf.writeStringNT(this.targetNpcId);
      } else {
        bf.writeInt8(0);
        bf.writeInt32LE(this.targetUserId);

        bf.writeOceanFleetData(this.oceanFleetData);
        bf.writeRequiredEncountChoiceStats(this.encountChoiceStats);

        bf.writeBigInt64LE(BigInt(this.ducatPoint));

        bf.writeInt8(this.supplies.length);
        this.supplies.forEach((cargo) => {
          bf.writeInt32LE(cargo.cmsId);
          bf.writeInt32LE(cargo.amount);
        });
        bf.writeInt32LE(this.tradeGoods.length);
        this.tradeGoods.forEach((tradeGoods) => {
          bf.writeInt32LE(tradeGoods.cmsId);
          bf.writeInt32LE(tradeGoods.amount);
        });
        bf.writeStringNT(this.pvpId);
      }
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.userKarmaCmsId = bf.readInt32LE();

      if (bf.readInt8() === 1) {
        this.targetNpcId = bf.readStringNT();
      } else {
        this.targetUserId = bf.readInt32LE();
        this.oceanFleetData = bf.readOceanFleetData();
        this.encountChoiceStats = bf.readRequiredEncountChoiceStats();
        this.ducatPoint = Number(bf.readBigInt64LE());

        this.supplies = [];
        const suppliesSize = bf.readInt8();
        for (let i = 0; i < suppliesSize; i++) {
          this.supplies.push({
            cmsId: bf.readInt32LE(),
            amount: bf.readInt32LE(),
          });
        }

        this.tradeGoods = [];
        const tradeGoodsSize = bf.readInt32LE();
        for (let i = 0; i < tradeGoodsSize; i++) {
          this.tradeGoods.push({
            cmsId: bf.readInt32LE(),
            amount: bf.readInt32LE(),
          });
        }
        this.pvpId = bf.readStringNT();
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_ENCOUNT_BY_USER, LB2OC_REQ_ENCOUNT_BY_USER);

  export class OC2LB_RES_ENCOUNT_BY_USER extends MIPacket {
    result: number; // MErrorCode

    targetType: EncountTargetType;
    targetFleetData: OceanFleetData;
    anchorInfo: AnchorInfo;
    reinforcementFleets: ReinforcementFleet[];

    paramType: number; // 0:pveParam 1: pvpParam
    pveParam?: {
      isQuestNpc: boolean;
    };
    pvpParam?: {
      targetAccountId: string;
      targetPubId: string;
      targetDucatPoint: number;
      targetSupplies: { cmsId: number; amount: number }[];
      targetTradeGoods: { cmsId: number; amount: number }[];
      targetKarma: number;
      targetKaramLastUpdateTimeUtc?: number; // 카르마가 최소값인 경우 lastKarmaUpdateTimeUtc 는 사용하지 않음.
      targetKarmaCmsId: number;
      targetNegoDucat: number;
      targetSurrDucat: number;
      targetNegoSuccessRate: number;
      targetEscapeSuccessRate: number;
      bMultiBattle: boolean;
      targetRepresentedMateCmsId: number;
      targetRepresentedMateIllustCmsId: number;
    };

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_ENCOUNT_BY_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.result);

      if (this.result === MErrorCode.OK) {
        bf.writeInt8(this.targetType);
        bf.writeOceanFleetData(this.targetFleetData);
        bf.writeAnchorInfo(this.anchorInfo);
        bf.writeInt8(this.reinforcementFleets.length);
        this.reinforcementFleets.forEach((rf) => bf.writeReinforcementFleet(rf));

        bf.writeInt32LE(this.paramType);
        if (this.paramType === 0) {
          bf.writeInt8(this.pveParam.isQuestNpc === true ? 1 : 0);
        } else if (this.paramType === 1) {
          bf.writeInt8(1);
          bf.writeStringNT(this.pvpParam.targetAccountId);
          bf.writeStringNT(this.pvpParam.targetPubId);
          bf.writeBigInt64LE(BigInt(this.pvpParam.targetDucatPoint));

          bf.writeInt8(this.pvpParam.targetSupplies.length);
          this.pvpParam.targetSupplies.forEach((cargo) => {
            bf.writeInt32LE(cargo.cmsId);
            bf.writeInt32LE(cargo.amount);
          });

          bf.writeInt32LE(this.pvpParam.targetTradeGoods.length);
          this.pvpParam.targetTradeGoods.forEach((tradeGoods) => {
            bf.writeInt32LE(tradeGoods.cmsId);
            bf.writeInt32LE(tradeGoods.amount);
          });

          bf.writeInt32LE(this.pvpParam.targetKarma);
          if (this.pvpParam.targetKaramLastUpdateTimeUtc) {
            bf.writeInt8(1);
            bf.writeInt32LE(this.pvpParam.targetKaramLastUpdateTimeUtc);
          } else {
            bf.writeInt8(0);
          }
          bf.writeInt32LE(this.pvpParam.targetKarmaCmsId);

          bf.writeInt32LE(this.pvpParam.targetNegoDucat);
          bf.writeInt32LE(this.pvpParam.targetSurrDucat);
          bf.writeInt32LE(this.pvpParam.targetNegoSuccessRate);
          bf.writeInt32LE(this.pvpParam.targetEscapeSuccessRate);
          bf.writeInt8(this.pvpParam.bMultiBattle === true ? 1 : 0);
          bf.writeInt32LE(this.pvpParam.targetRepresentedMateCmsId);
          bf.writeInt32LE(this.pvpParam.targetRepresentedMateIllustCmsId);
        }
      }
    }

    deserialize(bf: SmartBuffer) {
      this.result = bf.readInt32LE();

      if (this.result === MErrorCode.OK) {
        this.targetType = bf.readInt8();
        this.targetFleetData = bf.readOceanFleetData();
        this.anchorInfo = bf.readAnchorInfo();
        const size = bf.readInt8();
        this.reinforcementFleets = [];
        for (let i = 0; i < size; i++) {
          this.reinforcementFleets.push(bf.readReinforcementFleet());
        }

        this.paramType = bf.readInt32LE();

        if (this.paramType === 0) {
          const isQuestNpc: boolean = bf.readInt8() === 1 ? true : false;
          this.pveParam = {
            isQuestNpc,
          };
        } else if (this.paramType === 1) {
          const targetAccountId = bf.readStringNT();
          const targetPubId = bf.readStringNT();
          const targetDucatPoint = Number(bf.readBigInt64LE());

          const targetSupplies = [];
          const suppliesSize = bf.readInt8();
          for (let i = 0; i < suppliesSize; i++) {
            targetSupplies.push({
              cmsId: bf.readInt32LE(),
              amount: bf.readInt32LE(),
            });
          }

          const targetTradeGoods = [];
          const tradeGoodsSize = bf.readInt32LE();
          for (let i = 0; i < tradeGoodsSize; i++) {
            targetTradeGoods.push({
              cmsId: bf.readInt32LE(),
              amount: bf.readInt32LE(),
            });
          }

          const targetKarma = bf.readInt32LE();
          let targetKaramLastUpdateTimeUtc;
          if (bf.readInt8() === 1) {
            targetKaramLastUpdateTimeUtc = bf.readInt32LE();
          }
          const targetKarmaCmsId = bf.readInt32LE();
          const targetNegoDucat = bf.readInt32LE();
          const targetSurrDucat = bf.readInt32LE();
          const targetNegoSuccessRate = bf.readInt32LE();
          const targetEscapeSuccessRate = bf.readInt32LE();
          const bMultiBattle = bf.readInt8() === 1 ? true : false;
          const targetRepresentedMateCmsId = bf.readInt32LE();
          const targetRepresentedMateIllustCmsId = bf.readInt32LE();

          this.pvpParam = {
            targetAccountId,
            targetPubId,
            targetDucatPoint,
            targetSupplies,
            targetTradeGoods,
            targetKarma,
            targetKaramLastUpdateTimeUtc,
            targetKarmaCmsId,
            targetNegoDucat,
            targetSurrDucat,
            targetNegoSuccessRate,
            targetEscapeSuccessRate,
            bMultiBattle,
            targetRepresentedMateCmsId,
            targetRepresentedMateIllustCmsId,
          };
        }
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_ENCOUNT_BY_USER, OC2LB_RES_ENCOUNT_BY_USER);

  export class OC2LB_REQ_ENCOUNT_BY_NET_USER extends MIPacket {
    userId: number;
    attackerUserId: number;
    attackerUserKarmaCmsId: number;
    attackerAccountId: string;
    attackerPubId: string;
    anchorInfo: AnchorInfo;
    reinforcementFleets: ReinforcementFleet[];
    // pvp용 상대방에게 전달할 정보.
    oceanFleetData: OceanFleetData;
    encountChoiceStats: RequiredEncountChoiceStats;
    ducatPoint: number;
    supplies: { cmsId: number; amount: number }[];
    tradeGoods: { cmsId: number; amount: number }[];
    bMultiBattle: boolean;
    pvpId: string;
    attackerRepresentedMateCmsId: number;
    attackerRepresentedMateIllustCmsId: number;

    getPacketId() {
      return EnumPacket.PT_OC2LB_REQ_ENCOUNT_BY_NET_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.attackerUserId);
      bf.writeInt32LE(this.attackerUserKarmaCmsId);
      bf.writeStringNT(this.attackerAccountId);
      bf.writeStringNT(this.attackerPubId);
      bf.writeAnchorInfo(this.anchorInfo);
      bf.writeInt8(this.reinforcementFleets.length);
      for (const rf of this.reinforcementFleets) {
        bf.writeReinforcementFleet(rf);
      }

      bf.writeOceanFleetData(this.oceanFleetData);
      bf.writeRequiredEncountChoiceStats(this.encountChoiceStats);
      bf.writeBigInt64LE(BigInt(this.ducatPoint));

      bf.writeInt8(this.supplies.length);
      this.supplies.forEach((cargo) => {
        bf.writeInt32LE(cargo.cmsId);
        bf.writeInt32LE(cargo.amount);
      });
      bf.writeInt32LE(this.tradeGoods.length);
      this.tradeGoods.forEach((tradeGoods) => {
        bf.writeInt32LE(tradeGoods.cmsId);
        bf.writeInt32LE(tradeGoods.amount);
      });
      bf.writeInt8(this.bMultiBattle === true ? 1 : 0);
      bf.writeStringNT(this.pvpId);
      bf.writeInt32LE(this.attackerRepresentedMateCmsId);
      bf.writeInt32LE(this.attackerRepresentedMateIllustCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.attackerUserId = bf.readInt32LE();
      this.attackerUserKarmaCmsId = bf.readInt32LE();
      this.attackerAccountId = bf.readStringNT();
      this.attackerPubId = bf.readStringNT();
      this.anchorInfo = bf.readAnchorInfo();

      this.reinforcementFleets = [];
      const rfCount = bf.readInt8();
      for (let i = 0; i < rfCount; i++) {
        const rf = bf.readReinforcementFleet();
        this.reinforcementFleets.push(rf);
      }

      this.oceanFleetData = bf.readOceanFleetData();
      this.encountChoiceStats = bf.readRequiredEncountChoiceStats();
      this.ducatPoint = Number(bf.readBigInt64LE());

      this.supplies = [];
      const suppliesSize = bf.readInt8();
      for (let i = 0; i < suppliesSize; i++) {
        this.supplies.push({
          cmsId: bf.readInt32LE(),
          amount: bf.readInt32LE(),
        });
      }

      this.tradeGoods = [];
      const tradeGoodsSize = bf.readInt32LE();
      for (let i = 0; i < tradeGoodsSize; i++) {
        this.tradeGoods.push({
          cmsId: bf.readInt32LE(),
          amount: bf.readInt32LE(),
        });
      }
      this.bMultiBattle = bf.readInt8() === 1 ? true : false;
      this.pvpId = bf.readStringNT();

      this.attackerRepresentedMateCmsId = bf.readInt32LE();
      this.attackerRepresentedMateIllustCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_REQ_ENCOUNT_BY_NET_USER,
    OC2LB_REQ_ENCOUNT_BY_NET_USER
  );

  export class LB2OC_RES_ENCOUNT_BY_NET_USER extends MIPacket {
    result: number;

    karmaCmsId: number;
    oceanFleetData: OceanFleetData;
    negoSuccessRate: number;
    escapeSuccessRate: number;
    surrenderCost: number;
    negotiationCost: number;
    ducatPoint: number;
    supplies: { cmsId: number; amount: number }[];
    tradeGoods: { cmsId: number; amount: number }[];

    getPacketId() {
      return EnumPacket.PT_LB2OC_RES_ENCOUNT_BY_NET_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.result);
      if (this.result === MErrorCode.OK) {
        bf.writeInt32LE(this.karmaCmsId);
        bf.writeOceanFleetData(this.oceanFleetData);
        bf.writeInt32LE(this.negoSuccessRate);
        bf.writeInt32LE(this.escapeSuccessRate);
        bf.writeInt32LE(this.surrenderCost);
        bf.writeInt32LE(this.negotiationCost);
        bf.writeBigInt64LE(BigInt(this.ducatPoint));

        bf.writeInt8(this.supplies.length);
        this.supplies.forEach((cargo) => {
          bf.writeInt32LE(cargo.cmsId);
          bf.writeInt32LE(cargo.amount);
        });

        bf.writeInt32LE(this.tradeGoods.length);
        this.tradeGoods.forEach((tradeGoods) => {
          bf.writeInt32LE(tradeGoods.cmsId);
          bf.writeInt32LE(tradeGoods.amount);
        });
      }
    }

    deserialize(bf: SmartBuffer) {
      this.result = bf.readInt32LE();
      if (this.result === MErrorCode.OK) {
        this.karmaCmsId = bf.readInt32LE();
        this.oceanFleetData = bf.readOceanFleetData();
        this.negoSuccessRate = bf.readInt32LE();
        this.escapeSuccessRate = bf.readInt32LE();
        this.surrenderCost = bf.readInt32LE();
        this.negotiationCost = bf.readInt32LE();
        this.ducatPoint = Number(bf.readBigInt64LE());

        this.supplies = [];
        const suppliesSize = bf.readInt8();
        for (let i = 0; i < suppliesSize; i++) {
          this.supplies.push({
            cmsId: bf.readInt32LE(),
            amount: bf.readInt32LE(),
          });
        }

        this.tradeGoods = [];
        const tradeGoodsSize = bf.readInt32LE();
        for (let i = 0; i < tradeGoodsSize; i++) {
          this.tradeGoods.push({
            cmsId: bf.readInt32LE(),
            amount: bf.readInt32LE(),
          });
        }
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_RES_ENCOUNT_BY_NET_USER,
    LB2OC_RES_ENCOUNT_BY_NET_USER
  );

  export class OC2CL_NTF_ADD_FLEET extends MIPacket {
    userId: number = 0;
    userFleetSyncData: UserFleetSyncData = null;

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_ADD_FLEET;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeUserFleetSyncData(this.userFleetSyncData);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.userFleetSyncData = bf.readUserFleetSyncData();
    }

    getLogBodyShrinked(): any {
      return {
        userId: this.userId,
        fleetSync: {
          location: this.userFleetSyncData.location,
          degrees: this.userFleetSyncData.degrees,
        },
      };
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_ADD_FLEET, OC2CL_NTF_ADD_FLEET);

  export class OC2CL_NTF_REMOVE_OCEAN_ENTITY extends MIPacket {
    userIds: number[] = [];
    npcIds: string[] = [];
    doodadIds: string[] = [];

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_REMOVE_OCEAN_ENTITY;
    }
    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userIds.length);
      this.userIds.forEach((userId) => bf.writeInt32LE(userId));

      bf.writeInt32LE(this.npcIds.length);
      this.npcIds.forEach((npcId) => bf.writeStringNT(npcId));

      bf.writeInt32LE(this.doodadIds.length);
      this.doodadIds.forEach((doodadId) => bf.writeStringNT(doodadId));
    }

    deserialize(bf: SmartBuffer) {
      const userSize = bf.readInt32LE();
      for (let i = 0; i < userSize; i++) {
        this.userIds.push(bf.readInt32LE());
      }

      const npcSize = bf.readInt32LE();
      for (let i = 0; i < npcSize; i++) {
        this.npcIds.push(bf.readStringNT());
      }

      const doodadSize = bf.readInt32LE();
      for (let i = 0; i < doodadSize; i++) {
        this.doodadIds.push(bf.readStringNT());
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_REMOVE_OCEAN_ENTITY,
    OC2CL_NTF_REMOVE_OCEAN_ENTITY
  );

  export class OC2CL_NTF_NET_USER_MOVE extends MIPacket {
    userId: number = 0;
    location: Location = null;
    degrees: number = 0;
    speed: number = 0;

    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_MOVE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeLocation(this.location);
      bf.writeInt32LE(this.degrees);
      bf.writeInt32LE(this.speed);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.location = bf.readLocation();
      this.degrees = bf.readInt32LE();
      this.speed = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_NET_USER_MOVE, OC2CL_NTF_NET_USER_MOVE);

  export class OC2CL_NTF_NET_USER_TELEPORT_TO_LOCATION extends MIPacket {
    userId: number = 0;
    location: Location = null;
    degrees: number = 0;
    speed: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_TELEPORT_TO_LOCATION;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeLocation(this.location);
      bf.writeInt32LE(this.degrees);
      bf.writeInt32LE(this.speed);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.location = bf.readLocation();
      this.degrees = bf.readInt32LE();
      this.speed = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_TELEPORT_TO_LOCATION,
    OC2CL_NTF_NET_USER_TELEPORT_TO_LOCATION
  );

  export class OC2CL_NTF_NPC_FLEET_MOVE extends MIPacket {
    npcId: string = '';
    path: Location[] = [];
    linearSpd: number;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NPC_FLLET_MOVE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.npcId);
      bf.writeInt32LE(this.path.length);
      for (const location of this.path) {
        bf.writeLocation(location);
      }

      bf.writeInt32LE(this.linearSpd);
    }

    deserialize(bf: SmartBuffer) {
      this.npcId = bf.readStringNT();

      const pathCount = bf.readInt32LE();
      for (let i = 0; i < pathCount; i++) {
        this.path.push(bf.readLocation());
      }
      this.linearSpd = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_NPC_FLLET_MOVE, OC2CL_NTF_NPC_FLEET_MOVE);

  export class OC2CL_NTF_NPC_FLEET_STOP extends MIPacket {
    npcId: string = '';
    location: Location = null;
    degrees: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NPC_FLLET_STOP;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.npcId);
      bf.writeLocation(this.location);
      bf.writeInt32LE(this.degrees);
    }

    deserialize(bf: SmartBuffer) {
      this.npcId = bf.readStringNT();
      this.location = bf.readLocation();
      this.degrees = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_NPC_FLLET_STOP, OC2CL_NTF_NPC_FLEET_STOP);

  export class OC2CL_NTF_NPC_FLEET_MOVE_SYNC extends MIPacket {
    npcId: string = '';
    linearSpd: number = 0;
    progress: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NPC_FLEET_MOVE_SYNC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.npcId);
      bf.writeInt32LE(this.linearSpd);
      bf.writeDoubleLE(this.progress);
    }

    deserialize(bf: SmartBuffer) {
      this.npcId = bf.readStringNT();
      this.linearSpd = bf.readInt32LE();
      this.progress = bf.readDoubleLE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NPC_FLEET_MOVE_SYNC,
    OC2CL_NTF_NPC_FLEET_MOVE_SYNC
  );

  export class OC2CL_NTF_NPC_SPAWN extends MIPacket {
    npcs: { [key: string]: NpcFleetSyncData } = {};

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NPC_SPAWN;
    }

    serialize(bf: SmartBuffer) {
      let pos = bf.writeOffset;
      bf.writeInt32LE(0, pos);
      let count = 0;
      for (const onfId in this.npcs) {
        count++;
        bf.writeStringNT(onfId);
        bf.writeNpcFleetSyncData(this.npcs[onfId]);
      }

      bf.writeInt32LE(count, pos);

      pos = bf.writeOffset;
      bf.writeInt32LE(0, pos);
    }

    deserialize(bf: SmartBuffer) {
      this.npcs = {};
      let count = bf.readInt32LE();
      for (let i = 0; i < count; i++) {
        const onfId = bf.readStringNT();
        this.npcs[onfId] = bf.readNpcFleetSyncData();
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_NPC_SPAWN, OC2CL_NTF_NPC_SPAWN);

  export class OC2CL_NTF_NPC_DESPAWN extends MIPacket {
    npcIds: string[] = [];

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NPC_DESPAWN;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.npcIds.length);
      this.npcIds.forEach((npcId) => bf.writeStringNT(npcId));
    }

    deserialize(bf: SmartBuffer) {
      const length = bf.readInt32LE();
      for (let i = 0; i < length; i++) {
        this.npcIds.push(bf.readStringNT());
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_NPC_DESPAWN, OC2CL_NTF_NPC_DESPAWN);

  export class OC2CL_NTF_UPDATE_ZONE_SYNC_DATA extends MIPacket {
    users: { [userId: number]: UserFleetSyncData } = {};
    npcs: { [npcId: string]: NpcFleetSyncData } = {};
    doodads: { [doodadId: string]: OceanDoodadSyncData } = {};

    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }
    /*
    assign(osd: OceanSyncData) {
      this.npcs = osd.npcs;
      this.users = osd.users;
      this.doodads = osd.doodads;
    }
    */

    getOptions() {
      return EnumPacketOption.Compressed;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_UPDATE_ZONE_SYNC_DATA;
    }

    serialize(bf: SmartBuffer) {
      bf.writeOceanSyncData({ users: this.users, npcs: this.npcs, doodads: this.doodads });
    }

    deserialize(bf: SmartBuffer) {
      const osd = bf.readOceanSyncData();
      this.users = osd.users;
      this.npcs = osd.npcs;
      this.doodads = osd.doodads;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_UPDATE_ZONE_SYNC_DATA,
    OC2CL_NTF_UPDATE_ZONE_SYNC_DATA
  );

  export class LB2OC_REQ_ENCOUNT_END extends MIPacket {
    userId: number = 0;
    bAttack: boolean = false;
    encountResult: EncountResult;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_ENCOUNT_END;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeUInt8(this.bAttack ? 1 : 0);
      bf.writeInt32LE(this.encountResult);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.bAttack = bf.readUInt8() ? true : false;
      this.encountResult = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_ENCOUNT_END, LB2OC_REQ_ENCOUNT_END);

  export class OC2LB_RES_ENCOUNT_END extends MIPacket {
    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_ENCOUNT_END;
    }

    serialize(bf: SmartBuffer) {}
    deserialize(bf: SmartBuffer) {}
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_ENCOUNT_END, OC2LB_RES_ENCOUNT_END);

  export class LB2OC_REQ_ENTER extends MIPacket {
    oceanZoneCmsId: number = 0;
    channelId: string = '';
    userId: number = 0;
    accountId: string = '';
    pubId: string = '';
    lobbyUrl: string = '';
    userFleetSyncDataForOcean: UserFleetSyncDataForOcean = null;
    bDevIgnoreEncount: boolean = false;
    isLoaded: boolean = false;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_ENTER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.oceanZoneCmsId);
      bf.writeStringNT(this.channelId);
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.accountId);
      bf.writeStringNT(this.pubId);
      bf.writeStringNT(this.lobbyUrl);
      bf.writeUserFleetSyncDataForOcean(this.userFleetSyncDataForOcean);
      bf.writeUInt8(this.bDevIgnoreEncount ? 1 : 0);
      bf.writeUInt8(this.isLoaded ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.oceanZoneCmsId = bf.readInt32LE();
      this.channelId = bf.readStringNT();
      this.userId = bf.readInt32LE();
      this.accountId = bf.readStringNT();
      this.pubId = bf.readStringNT();
      this.lobbyUrl = bf.readStringNT();
      this.userFleetSyncDataForOcean = bf.readUserFleetSyncDataForOcean();
      this.bDevIgnoreEncount = bf.readUInt8() === 1 ? true : false;
      this.isLoaded = bf.readUInt8() === 1 ? true : false;
    }

    getLogBodyShrinked(): any {
      return {
        userId: this.userId,
        lobbyUrl: this.lobbyUrl,
        fleetSync: {
          location: this.userFleetSyncDataForOcean.location,
          degrees: this.userFleetSyncDataForOcean.degrees,
        },
      };
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_ENTER, LB2OC_REQ_ENTER);

  export class OC2LB_RES_ENTER extends MIPacket {
    channelId: string = '';
    userId: number = 0;
    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_ENTER;
    }
    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.channelId);
      bf.writeInt32LE(this.userId);
    }
    deserialize(bf: SmartBuffer) {
      this.channelId = bf.readStringNT();
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_ENTER, OC2LB_RES_ENTER);

  export class LB2OC_REQ_LEAVE extends MIPacket {
    channelId: string = '';
    userId: number = 0;
    reason: OCEAN_ZONE_LEAVE_REASON = OCEAN_ZONE_LEAVE_REASON.UNKNOWN;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_LEAVE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.channelId);
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.reason);
    }

    deserialize(bf: SmartBuffer) {
      this.channelId = bf.readStringNT();
      this.userId = bf.readInt32LE();
      this.reason = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_LEAVE, LB2OC_REQ_LEAVE);

  export class OC2LB_RES_LEAVE extends MIPacket {
    userId: number = 0;
    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_LEAVE;
    }
    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
    }
    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_LEAVE, OC2LB_RES_LEAVE);

  export class LB2OC_REQ_LOAD_COMPLETE extends MIPacket {
    userId: number = 0;
    bFromBattleOrDuel: boolean = false;
    karma: number;
    lastKarmaUpdateTimeUtc: number;
    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_LOAD_COMPLETE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt8(this.bFromBattleOrDuel ? 1 : 0);
      bf.writeInt32LE(this.karma);
      bf.writeInt32LE(this.lastKarmaUpdateTimeUtc);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.bFromBattleOrDuel = bf.readInt8() ? true : false;
      this.karma = bf.readInt32LE();
      this.lastKarmaUpdateTimeUtc = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_LOAD_COMPLETE, LB2OC_REQ_LOAD_COMPLETE);

  export class OC2LB_RES_LOAD_COMPLETE extends MIPacket {
    userId: number = 0;
    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_LOAD_COMPLETE;
    }
    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
    }
    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_LOAD_COMPLETE, OC2LB_RES_LOAD_COMPLETE);

  export class LB2OC_NTF_OCEAN_MOVE extends MIPacket {
    userId: number = 0;
    location: Location = { latitude: 0, longitude: 0 };
    degrees: number = 0;
    speed: number = 0;

    debugTrace(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_OCEAN_MOVE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeLocation(this.location);
      bf.writeInt32LE(this.degrees);
      bf.writeInt32LE(this.speed);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.location = bf.readLocation();
      this.degrees = bf.readInt32LE();
      this.speed = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_OCEAN_MOVE, LB2OC_NTF_OCEAN_MOVE);

  export class LB2OC_NTF_OCEAN_TELEPORT extends MIPacket {
    userId: number = 0;
    location: Location = { latitude: 0, longitude: 0 };
    degrees: number = 0;

    debugTrace(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_OCEAN_TELEPORT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeLocation(this.location);
      bf.writeInt32LE(this.degrees);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.location = bf.readLocation();
      this.degrees = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_OCEAN_TELEPORT, LB2OC_NTF_OCEAN_TELEPORT);

  export class LB2OC_NTF_DEV_IGNORE_NPC_ENCOUNT extends MIPacket {
    userId: number = 0;
    bFlag: boolean = false;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_IGNORE_NPC_ENCOUNT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeUInt8(this.bFlag ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.bFlag = bf.readUInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_IGNORE_NPC_ENCOUNT,
    LB2OC_NTF_DEV_IGNORE_NPC_ENCOUNT
  );

  export class OC2LB_REQ_ENCOUNT_BY_NPC extends MIPacket {
    userId: number = 0;
    npcId: string;
    isQuestNpc: boolean;
    choiceStats: RequiredEncountChoiceStats; //인카운트 선택 확률에 필요한 스탯 정보
    npcFleetData: OceanFleetData;
    anchorInfo: AnchorInfo;
    reinforcementFleets: ReinforcementFleet[] = [];

    getLogBodyShrinked(): any {
      return {
        userId: this.userId,
        npcId: this.npcId,
      };
    }
    getPacketId() {
      return EnumPacket.PT_OC2LB_REQ_ENCOUNT_BY_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.npcId);
      bf.writeUInt8(this.isQuestNpc ? 1 : 0);
      bf.writeRequiredEncountChoiceStats(this.choiceStats);
      bf.writeOceanFleetData(this.npcFleetData);
      bf.writeAnchorInfo(this.anchorInfo);
      bf.writeInt8(this.reinforcementFleets.length);
      for (const rf of this.reinforcementFleets) {
        bf.writeReinforcementFleet(rf);
      }
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.npcId = bf.readStringNT();
      this.isQuestNpc = bf.readUInt8() ? true : false;
      this.choiceStats = bf.readRequiredEncountChoiceStats();
      this.npcFleetData = bf.readOceanFleetData();
      this.anchorInfo = bf.readAnchorInfo();
      const size = bf.readInt8();
      for (let i = 0; i < size; i++) {
        const rf = bf.readReinforcementFleet();
        this.reinforcementFleets.push(rf);
      }
    }
  }

  PacketFactory.Register(EnumPacket.PT_OC2LB_REQ_ENCOUNT_BY_NPC, OC2LB_REQ_ENCOUNT_BY_NPC);

  export class LB2OC_RES_ENCOUNT_BY_NPC extends MIPacket {
    userId: number = 0;
    npcId: string = '';
    result: number;

    getPacketId() {
      return EnumPacket.PT_LB2OC_RES_ENCOUNT_BY_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.npcId);
      bf.writeInt32LE(this.result);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.npcId = bf.readStringNT();
      this.result = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_RES_ENCOUNT_BY_NPC, LB2OC_RES_ENCOUNT_BY_NPC);

  export class LB2OC_NTF_DEV_ADD_NEAR_SPAWNER extends MIPacket {
    userId: number = 0;
    npcCmsId: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_ADD_NEAR_SPAWNER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.npcCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.npcCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_ADD_NEAR_SPAWNER,
    LB2OC_NTF_DEV_ADD_NEAR_SPAWNER
  );

  export class LB2OC_NTF_DEV_REMOVE_NEAR_SPAWNER extends MIPacket {
    userId: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_REMOVE_NEAR_SPAWNER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_REMOVE_NEAR_SPAWNER,
    LB2OC_NTF_DEV_REMOVE_NEAR_SPAWNER
  );

  export class OC2LB_NTF_USER_ENCOUNT_STATE_CLEAR extends MIPacket {
    userId: number = 0;
    npcId: string;
    result: number;

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_USER_ENCOUNT_STATE_CLEAR;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.npcId);
      bf.writeInt32LE(this.result);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.npcId = bf.readStringNT();
      this.result = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_USER_ENCOUNT_STATE_CLEAR,
    OC2LB_NTF_USER_ENCOUNT_STATE_CLEAR
  );

  export class LB2OC_NTF_BUFF_SYNC_UPDATE extends MIPacket {
    userId: number = 0;
    obsn: OceanBuffSyncNub = null;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_BUFF_SYNC_UPDATE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeOceanBuffSyncNub(this.obsn);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.obsn = bf.readOceanBuffSyncNub();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_BUFF_SYNC_UPDATE, LB2OC_NTF_BUFF_SYNC_UPDATE);

  export class LB2OC_NTF_BUFF_SYNC_REMOVE extends MIPacket {
    userId: number = 0;
    buffCmsId: number = 0;
    targetId: number = 0;
    sourceType: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_BUFF_SYNC_REMOVE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.buffCmsId);
      bf.writeInt32LE(this.targetId);
      bf.writeInt32LE(this.sourceType);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.buffCmsId = bf.readInt32LE();
      this.targetId = bf.readInt32LE();
      this.sourceType = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_BUFF_SYNC_REMOVE, LB2OC_NTF_BUFF_SYNC_REMOVE);

  export class OC2CL_NTF_BUFF_SYNC_UPDATE extends MIPacket {
    userId: number = 0;
    obsn: OceanBuffSyncNub = null;

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_BUFF_SYNC_UPDATE;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeOceanBuffSyncNub(this.obsn);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.obsn = bf.readOceanBuffSyncNub();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_BUFF_SYNC_UPDATE, OC2CL_NTF_BUFF_SYNC_UPDATE);

  export class OC2CL_NTF_BUFF_SYNC_REMOVE extends MIPacket {
    userId: number = 0;
    buffCmsId: number = 0;
    targetId: number = 0;

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_BUFF_SYNC_REMOVE;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.buffCmsId);
      bf.writeInt32LE(this.targetId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.buffCmsId = bf.readInt32LE();
      this.targetId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_BUFF_SYNC_REMOVE, OC2CL_NTF_BUFF_SYNC_REMOVE);

  export class LB2OC_NTF_DEV_NPC_ATTACK_RADIUS extends MIPacket {
    userId: number = 0;
    radius: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_NPC_ATTACK_RADIUS;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.radius);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.radius = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_NPC_ATTACK_RADIUS,
    LB2OC_NTF_DEV_NPC_ATTACK_RADIUS
  );

  export class LB2OC_NTF_DEV_NPC_TICK_PER_SEC extends MIPacket {
    tickPerSec: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_NPC_TICK_PER_SEC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.tickPerSec);
    }

    deserialize(bf: SmartBuffer) {
      this.tickPerSec = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_NPC_TICK_PER_SEC,
    LB2OC_NTF_DEV_NPC_TICK_PER_SEC
  );

  export class LB2OC_REQ_ZOOM_IN_NPC extends MIPacket {
    userId: number = 0;
    targetNpcId: string = '';
    interactableDistance: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_ZOOM_IN_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.targetNpcId);
      bf.writeInt32LE(this.interactableDistance);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.targetNpcId = bf.readStringNT();
      this.interactableDistance = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_ZOOM_IN_NPC, LB2OC_REQ_ZOOM_IN_NPC);

  export class OC2LB_RES_ZOOM_IN_NPC extends MIPacket {
    zoominNpcDesc: ZoomInNpcDesc;
    targetDistance: number;

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_ZOOM_IN_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeZoominNpcDesc(this.zoominNpcDesc);
      bf.writeUInt32LE(this.targetDistance);
    }

    deserialize(bf: SmartBuffer) {
      this.zoominNpcDesc = bf.readZoominNpcDesc();
      this.targetDistance = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_ZOOM_IN_NPC, OC2LB_RES_ZOOM_IN_NPC);

  export class LB2OC_REQ_ZOOM_IN_USER extends MIPacket {
    userId: number = 0;
    targetUserId: number = 0;
    interactableDistance: number = 0;
    seaAreaType: SEA_AREA_TYPE;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_ZOOM_IN_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.targetUserId);
      bf.writeInt32LE(this.interactableDistance);
      bf.writeInt8(this.seaAreaType);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.targetUserId = bf.readInt32LE();
      this.interactableDistance = bf.readInt32LE();
      this.seaAreaType = bf.readInt8();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_ZOOM_IN_USER, LB2OC_REQ_ZOOM_IN_USER);

  export class OC2LB_RES_ZOOM_IN_USER extends MIPacket {
    zoominUserDesc: ZoomInUserDesc;
    encountable: boolean;
    targetDistance: number;

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_ZOOM_IN_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeZoominUserDesc(this.zoominUserDesc);
      bf.writeInt8(this.encountable ? 1 : 0);
      bf.writeUInt32LE(this.targetDistance);
    }

    deserialize(bf: SmartBuffer) {
      this.zoominUserDesc = bf.readZoominUserDesc();
      this.encountable = bf.readInt8() === 1 ? true : false;
      this.targetDistance = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_ZOOM_IN_USER, OC2LB_RES_ZOOM_IN_USER);

  export class LB2OC_REQ_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER extends MIPacket {
    userId: number;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_REQ_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER,
    LB2OC_REQ_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER
  );

  export class OC2LB_RES_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER extends MIPacket {
    npcs: { npcCmsId: number; latitude: number; longitude: number }[] = [];

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.npcs.length);
      this.npcs.forEach((npc) => {
        bf.writeInt32LE(npc.npcCmsId);
        bf.writeDoubleLE(npc.latitude);
        bf.writeDoubleLE(npc.longitude);
      });
    }

    deserialize(bf: SmartBuffer) {
      const size = bf.readInt32LE();
      for (let i = 0; i < size; i++) {
        this.npcs.push({
          npcCmsId: bf.readInt32LE(),
          latitude: bf.readDoubleLE(),
          longitude: bf.readDoubleLE(),
        });
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_RES_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER,
    OC2LB_RES_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER
  );

  export class OC2CL_NTF_DOODAD_SPAWN extends MIPacket {
    doodads: { [key: string]: OceanDoodadSyncData } = {};

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_DOODAD_SPAWN;
    }

    serialize(bf: SmartBuffer) {
      let pos = bf.writeOffset;
      bf.writeInt32LE(0, pos);
      let count = 0;
      for (const onfId in this.doodads) {
        count++;
        bf.writeStringNT(onfId);
        bf.writeOceanDoodadSyncData(this.doodads[onfId]);
      }

      bf.writeInt32LE(count, pos);

      pos = bf.writeOffset;
      bf.writeInt32LE(0, pos);
    }

    deserialize(bf: SmartBuffer) {
      this.doodads = {};
      let count = bf.readInt32LE();
      for (let i = 0; i < count; i++) {
        const onfId = bf.readStringNT();
        this.doodads[onfId] = bf.readOceanDoodadSyncData();
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_DOODAD_SPAWN, OC2CL_NTF_DOODAD_SPAWN);

  export class OC2CL_NTF_DOODAD_DESPAWN extends MIPacket {
    doodadIds: string[] = [];

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_DOODAD_DESPAWN;
    }

    serialize(bf: SmartBuffer) {
      let pos = bf.writeOffset;
      bf.writeInt32LE(0, pos);
      let count = 0;
      for (const doodadId of this.doodadIds) {
        count++;
        bf.writeStringNT(doodadId);
      }

      bf.writeInt32LE(count, pos);
    }

    deserialize(bf: SmartBuffer) {
      this.doodadIds = [];
      let count = bf.readInt32LE();
      for (let i = 0; i < count; i++) {
        const onfId = bf.readStringNT();
        this.doodadIds.push(onfId);
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_DOODAD_DESPAWN, OC2CL_NTF_DOODAD_DESPAWN);

  export class LB2OC_REQ_SPAWN_OCEAN_DOODAD extends MIPacket {
    userId: number = 0;
    doodadCmsId: number = 0;
    radius: number = 0;
    location: Location = { latitude: 0, longitude: 0 };
    degrees: number = 0;
    isGlobal: boolean = false; //기본적으로 로컬객체
    spawnType: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_SPAWN_OCEAN_DOODAD;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.doodadCmsId);
      bf.writeInt32LE(this.radius);
      bf.writeLocation(this.location);
      bf.writeInt32LE(this.degrees);
      bf.writeUInt8(this.isGlobal ? 1 : 0);
      bf.writeUInt8(this.spawnType);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadCmsId = bf.readInt32LE();
      this.radius = bf.readInt32LE();
      this.location = bf.readLocation();
      this.degrees = bf.readInt32LE();
      this.isGlobal = bf.readUInt8() ? true : false;
      this.spawnType = bf.readUInt8();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_SPAWN_OCEAN_DOODAD, LB2OC_REQ_SPAWN_OCEAN_DOODAD);

  export class OC2LB_RES_SPAWN_OCEAN_DOODAD extends MIPacket {
    doodadId: string = '';

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_SPAWN_OCEAN_DOODAD;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.doodadId);
    }

    deserialize(bf: SmartBuffer) {
      this.doodadId = bf.readStringNT();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_SPAWN_OCEAN_DOODAD, OC2LB_RES_SPAWN_OCEAN_DOODAD);

  export class LB2OC_REQ_DESPAWN_OCEAN_DOODAD extends MIPacket {
    userId: number = 0;
    doodadId: string = '';

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_DESPAWN_OCEAN_DOODAD;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.doodadId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadId = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_REQ_DESPAWN_OCEAN_DOODAD,
    LB2OC_REQ_DESPAWN_OCEAN_DOODAD
  );

  export class OC2LB_RES_DESPAWN_OCEAN_DOODAD extends MIPacket {
    doodadId: string = '';

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_DESPAWN_OCEAN_DOODAD;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.doodadId);
    }

    deserialize(bf: SmartBuffer) {
      this.doodadId = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_RES_DESPAWN_OCEAN_DOODAD,
    OC2LB_RES_DESPAWN_OCEAN_DOODAD
  );

  export class LB2OC_NTF_DEV_ADD_OCEAN_DOODAD_NEAR_SPAWNER extends MIPacket {
    userId: number = 0;
    doodadCmsId: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_ADD_OCEAN_DOODAD_NEAR_SPAWNER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.doodadCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_ADD_OCEAN_DOODAD_NEAR_SPAWNER,
    LB2OC_NTF_DEV_ADD_OCEAN_DOODAD_NEAR_SPAWNER
  );

  export class LB2OC_NTF_DEV_REMOVE_OCEAN_DOODAD_NEAR_SPAWNER extends MIPacket {
    userId: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_REMOVE_OCEAN_DOODAD_NEAR_SPAWNER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_REMOVE_OCEAN_DOODAD_NEAR_SPAWNER,
    LB2OC_NTF_DEV_REMOVE_OCEAN_DOODAD_NEAR_SPAWNER
  );

  export class LB2OC_NTF_SET_LOCAL_NPC_SPAWN extends MIPacket {
    userId: number;
    flag: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_SET_LOCAL_NPC_SPAWN;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.flag);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.flag = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_SET_LOCAL_NPC_SPAWN,
    LB2OC_NTF_SET_LOCAL_NPC_SPAWN
  );

  export class LB2OC_NTF_BATTLE_OR_DUEL_START extends MIPacket {
    userId: number;
    bDuel: boolean;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_BATTLE_OR_DUEL_START;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt8(this.bDuel === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.bDuel = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_BATTLE_OR_DUEL_START,
    LB2OC_NTF_BATTLE_OR_DUEL_START
  );

  export class LB2OC_NTF_BATTLE_OR_DUEL_RESUME extends MIPacket {
    userId: number;
    bDuel: boolean;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_BATTLE_OR_DUEL_RESUME;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt8(this.bDuel === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.bDuel = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_BATTLE_OR_DUEL_RESUME,
    LB2OC_NTF_BATTLE_OR_DUEL_RESUME
  );

  export class LB2OC_NTF_BATTLE_OR_DUEL_END extends MIPacket {
    userId: number;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_BATTLE_OR_DUEL_END;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_BATTLE_OR_DUEL_END, LB2OC_NTF_BATTLE_OR_DUEL_END);

  export class OC2CL_NTF_NET_USER_CHANGE_STATE extends MIPacket {
    userId: number;
    state: number;
    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_STATE;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.state);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.state = bf.readInt32LE();
    }
  }

  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_STATE,
    OC2CL_NTF_NET_USER_CHANGE_STATE
  );

  export class LB2OC_REQ_OCEAN_DOODAD_INFO extends MIPacket {
    userId: number = 0;
    doodadId: string = '';

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_OCEAN_DOODAD_INFO;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.doodadId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadId = bf.readStringNT();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_REQ_OCEAN_DOODAD_INFO, LB2OC_REQ_OCEAN_DOODAD_INFO);

  export class OC2LB_RES_OCEAN_DOODAD_INFO extends MIPacket {
    doodad: OceanDoodadSyncData = null;

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_OCEAN_DOODAD_INFO;
    }

    serialize(bf: SmartBuffer) {
      bf.writeOceanDoodadSyncData(this.doodad);
    }

    deserialize(bf: SmartBuffer) {
      this.doodad = bf.readOceanDoodadSyncData();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_RES_OCEAN_DOODAD_INFO, OC2LB_RES_OCEAN_DOODAD_INFO);

  export class LB2OC_NTF_SPAWN_OCEAN_LOCAL_DOODAD extends MIPacket {
    userId: number = 0;
    doodadCmsId: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_SPAWN_OCEAN_LOCAL_DOODAD;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.doodadCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_SPAWN_OCEAN_LOCAL_DOODAD,
    LB2OC_NTF_SPAWN_OCEAN_LOCAL_DOODAD
  );

  export class LB2OC_REQ_QUEST_SPAWN_LOCAL_NPC extends MIPacket {
    userId: number = 0;
    npcCmsId: number = 0;
    radius: number = 0;
    location: Location | null = { latitude: 0, longitude: 0 };
    degrees: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_QUEST_SPAWN_LOCAL_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.npcCmsId);
      bf.writeInt32LE(this.radius);
      if (this.location) {
        bf.writeInt8(1);
        bf.writeLocation(this.location);
      } else {
        bf.writeInt8(0);
      }
      bf.writeInt32LE(this.degrees);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.npcCmsId = bf.readInt32LE();
      this.radius = bf.readInt32LE();

      if (bf.readInt8() === 1) {
        this.location = bf.readLocation();
      } else {
        this.location = null;
      }

      this.degrees = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_REQ_QUEST_SPAWN_LOCAL_NPC,
    LB2OC_REQ_QUEST_SPAWN_LOCAL_NPC
  );

  export class OC2LB_RES_QUEST_SPAWN_LOCAL_NPC extends MIPacket {
    npcId: string = '';

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_QUEST_SPAWN_LOCAL_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.npcId);
    }

    deserialize(bf: SmartBuffer) {
      this.npcId = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_RES_QUEST_SPAWN_LOCAL_NPC,
    OC2LB_RES_QUEST_SPAWN_LOCAL_NPC
  );

  export class LB2OC_REQ_QUEST_DESPAWN_LOCAL_NPC extends MIPacket {
    userId: number = 0;
    npcId: string = '';

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_QUEST_DESPAWN_LOCAL_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeStringNT(this.npcId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.npcId = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_REQ_QUEST_DESPAWN_LOCAL_NPC,
    LB2OC_REQ_QUEST_DESPAWN_LOCAL_NPC
  );

  export class OC2LB_RES_QUEST_DESPAWN_LOCAL_NPC extends MIPacket {
    npcId: string = '';

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_QUEST_DESPAWN_LOCAL_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.npcId);
    }

    deserialize(bf: SmartBuffer) {
      this.npcId = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_RES_QUEST_DESPAWN_LOCAL_NPC,
    OC2LB_RES_QUEST_DESPAWN_LOCAL_NPC
  );

  export class LB2OC_NTF_CHANGE_QUEST_SINGLE_MODE extends MIPacket {
    userId: number = 0;
    bQuestSingleMode: boolean = false;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_CHANGE_QUEST_SINGLE_MODE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bQuestSingleMode === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bQuestSingleMode = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_CHANGE_QUEST_SINGLE_MODE,
    LB2OC_NTF_CHANGE_QUEST_SINGLE_MODE
  );

  export class OC2CL_NTF_NET_USER_CHANGE_QUEST_SINGLE_MODE extends MIPacket {
    userId: number = 0;
    bQuestSingleMode: boolean = false;

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_QUEST_SINGLE_MODE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bQuestSingleMode === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bQuestSingleMode = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_QUEST_SINGLE_MODE,
    OC2CL_NTF_NET_USER_CHANGE_QUEST_SINGLE_MODE
  );

  export class OC2LB_NTF_UPDATE_OCEAN_LOCAL_DOODAD_NUM extends MIPacket {
    userId: number = 0;
    localDoodadNum: number = 0;

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_UPDATE_OCEAN_LOCAL_DOODAD_NUM;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.localDoodadNum);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.localDoodadNum = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_UPDATE_OCEAN_LOCAL_DOODAD_NUM,
    OC2LB_NTF_UPDATE_OCEAN_LOCAL_DOODAD_NUM
  );

  export class OC2CL_NTF_DOODAD_SYNC_UPDATE extends MIPacket {
    doodadId: string = '';
    state: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_DOODAD_SYNC_UPDATE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeStringNT(this.doodadId);
      bf.writeInt8(this.state);
    }

    deserialize(bf: SmartBuffer) {
      this.doodadId = bf.readStringNT();
      this.state = bf.readInt8();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_DOODAD_SYNC_UPDATE, OC2CL_NTF_DOODAD_SYNC_UPDATE);

  export class OC2LB_NTF_APPLY_OCEAN_DOODAD_EFFECT extends MIPacket {
    userId: number = 0;
    doodadCmsId: number = 0;

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_APPLY_OCEAN_DOODAD_EFFECT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.doodadCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_APPLY_OCEAN_DOODAD_EFFECT,
    OC2LB_NTF_APPLY_OCEAN_DOODAD_EFFECT
  );

  export class LB2OC_NTF_SET_LOCAL_DOODAD_SPAWN extends MIPacket {
    userId: number;
    flag: number = 0;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_SET_LOCAL_DOODAD_SPAWN;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.flag);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.flag = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_SET_LOCAL_DOODAD_SPAWN,
    LB2OC_NTF_SET_LOCAL_DOODAD_SPAWN
  );

  export class LB2OC_NTF_CANCEL_ENCOUNT extends MIPacket {
    userId: number;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_CANCEL_ENCOUNT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_CANCEL_ENCOUNT, LB2OC_NTF_CANCEL_ENCOUNT);

  export class LB2OC_NTF_NET_USER_CHANGE_LAND_ANCHOR extends MIPacket {
    userId: number = 0;
    bLandAnchoring: boolean = false;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_LAND_ANCHOR;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bLandAnchoring === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bLandAnchoring = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_LAND_ANCHOR,
    LB2OC_NTF_NET_USER_CHANGE_LAND_ANCHOR
  );

  export class OC2CL_NTF_NET_USER_CHANGE_LAND_ANCHOR extends MIPacket {
    userId: number = 0;
    bLandAnchoring: boolean = false;

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_LAND_ANCHOR;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bLandAnchoring === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bLandAnchoring = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_LAND_ANCHOR,
    OC2CL_NTF_NET_USER_CHANGE_LAND_ANCHOR
  );

  export class LB2OC_NTF_NET_USER_VILLAGE_ENTER extends MIPacket {
    userId: number = 0;
    bVillageEnter: boolean = false;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_VILLAGE_ENTER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bVillageEnter === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bVillageEnter = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_VILLAGE_ENTER,
    LB2OC_NTF_NET_USER_VILLAGE_ENTER
  );

  export class OC2CL_NTF_NET_USER_VILLAGE_ENTER extends MIPacket {
    userId: number = 0;
    bVillageEnter: boolean = false;

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_VILLAGE_ENTER;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bVillageEnter === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bVillageEnter = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_VILLAGE_ENTER,
    OC2CL_NTF_NET_USER_VILLAGE_ENTER
  );

  //**********************************************************************************
  export class LB2OC_NTF_NET_USER_SALVAGE_ENTER extends MIPacket {
    userId: number = 0;
    bSalvageEnter: boolean = false;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_SALVAGE_ENTER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bSalvageEnter === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bSalvageEnter = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_SALVAGE_ENTER,
    LB2OC_NTF_NET_USER_SALVAGE_ENTER
  );

  export class OC2CL_NTF_NET_USER_SALVAGE_ENTER extends MIPacket {
    userId: number = 0;
    bSalvageEnter: boolean = false;

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_SALVAGE_ENTER;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bSalvageEnter === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bSalvageEnter = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_SALVAGE_ENTER,
    OC2CL_NTF_NET_USER_SALVAGE_ENTER
  );
  //**********************************************************************************

  export class LB2OC_NTF_NET_USER_CHANGE_CUSTOMIZE extends MIPacket {
    userId: number = 0;
    shipCustomizing: ShipCustomizing = {};
    shipSailPattern: ShipSailPattern = {};

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_CUSTOMIZE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeShipCustomizing(this.shipCustomizing);
      bf.writeShipSailPattern(this.shipSailPattern);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.shipCustomizing = bf.readShipCustomizing();
      this.shipSailPattern = bf.readShipSailPattern();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_CUSTOMIZE,
    LB2OC_NTF_NET_USER_CHANGE_CUSTOMIZE
  );

  export class OC2CL_NTF_NET_USER_CHANGE_CUSTOMIZE extends MIPacket {
    userId: number = 0;
    shipCustomizing: ShipCustomizing = {};
    shipSailPattern: ShipSailPattern = {};

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_CUSTOMIZE;
    }

    sendableToBot(): boolean {
      return false;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeShipCustomizing(this.shipCustomizing);
      bf.writeShipSailPattern(this.shipSailPattern);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.shipCustomizing = bf.readShipCustomizing();
      this.shipSailPattern = bf.readShipSailPattern();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_CUSTOMIZE,
    OC2CL_NTF_NET_USER_CHANGE_CUSTOMIZE
  );

  export class OC2CL_NTF_WORLD_MAP_NPC_LOCATION extends MIPacket {
    npcs: { npcId: string; npcCmsId: number; location: Location; degrees: number }[] = [];

    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }
    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_WORLD_MAP_NPC_LOCATION;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.npcs.length);
      this.npcs.forEach((npc) => {
        bf.writeStringNT(npc.npcId);
        bf.writeUInt32LE(npc.npcCmsId);
        bf.writeLocation(npc.location);
        bf.writeUInt32LE(npc.degrees);
      });
    }

    deserialize(bf: SmartBuffer) {
      const length = bf.readUInt32LE();
      for (let i = 0; i < length; i++) {
        this.npcs.push({
          npcId: bf.readStringNT(),
          npcCmsId: bf.readUInt32LE(),
          location: bf.readLocation(),
          degrees: bf.readUInt32LE(),
        });
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_WORLD_MAP_NPC_LOCATION,
    OC2CL_NTF_WORLD_MAP_NPC_LOCATION
  );

  export class LB2OC_NTF_CHANGE_QUEST_NPC_MOVE_AI extends MIPacket {
    userId: number;
    npcId: string;
    freeRoamingRate: number;
    pathRoamingRate: number;
    anchorRate: number;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_CHANGE_QUEST_NPC_MOVE_AI;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeStringNT(this.npcId);
      bf.writeUInt32LE(this.freeRoamingRate);
      bf.writeUInt32LE(this.pathRoamingRate);
      bf.writeUInt32LE(this.anchorRate);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.npcId = bf.readStringNT();
      this.freeRoamingRate = bf.readUInt32LE();
      this.pathRoamingRate = bf.readUInt32LE();
      this.anchorRate = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_CHANGE_QUEST_NPC_MOVE_AI,
    LB2OC_NTF_CHANGE_QUEST_NPC_MOVE_AI
  );

  export class LB2OC_NTF_CHANGE_QUEST_NPC_ATTACK_AI extends MIPacket {
    userId: number;
    npcId: string;
    matePersonalityCmsId: number;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_CHANGE_QUEST_NPC_ATTACK_AI;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeStringNT(this.npcId);
      bf.writeUInt32LE(this.matePersonalityCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.npcId = bf.readStringNT();
      this.matePersonalityCmsId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_CHANGE_QUEST_NPC_ATTACK_AI,
    LB2OC_NTF_CHANGE_QUEST_NPC_ATTACK_AI
  );

  export class LB2OC_NTF_DEV_ADD_USER_DATA_SPAWNER extends MIPacket {
    userId: number = 0;
    userFleetData: OceanFleetData;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_ADD_USER_DATA_SPAWNER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeOceanFleetData(this.userFleetData);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.userFleetData = bf.readOceanFleetData();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_ADD_USER_DATA_SPAWNER,
    LB2OC_NTF_DEV_ADD_USER_DATA_SPAWNER
  );

  export class OC2LB_REQ_GET_NPC_FLEET_DATA_FOR_USER extends MIPacket {
    userId: number;

    getPacketId() {
      return EnumPacket.PT_OC2LB_REQ_GET_NPC_FLEET_DATA_FOR_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_REQ_GET_NPC_FLEET_DATA_FOR_USER,
    OC2LB_REQ_GET_NPC_FLEET_DATA_FOR_USER
  );

  export class LB2OC_RES_GET_NPC_FLEET_DATA_FOR_USER extends MIPacket {
    userFleetData: OceanFleetData;

    getPacketId() {
      return EnumPacket.PT_LB2OC_RES_GET_NPC_FLEET_DATA_FOR_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeOceanFleetData(this.userFleetData);
    }

    deserialize(bf: SmartBuffer) {
      this.userFleetData = bf.readOceanFleetData();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_RES_GET_NPC_FLEET_DATA_FOR_USER,
    LB2OC_RES_GET_NPC_FLEET_DATA_FOR_USER
  );

  export class LB2OC_NTF_UPDATE_USER_GAMEOVER extends MIPacket {
    userId: number;
    bGameOver: boolean;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_UPDATE_USER_GAMEOVER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32BE(this.userId);
      bf.writeInt8(this.bGameOver ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32BE();
      this.bGameOver = bf.readInt8() ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_UPDATE_USER_GAMEOVER,
    LB2OC_NTF_UPDATE_USER_GAMEOVER
  );

  export class LB2OC_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER extends MIPacket {
    offlineUserId: number;
    battleResult: string;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.offlineUserId);
      bf.writeStringNT(this.battleResult);
    }

    deserialize(bf: SmartBuffer) {
      this.offlineUserId = bf.readUInt32LE();
      this.battleResult = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER,
    LB2OC_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER
  );

  export class OC2LB_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER extends MIPacket {
    offlineUserId: number;
    battleResult: string;

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.offlineUserId);
      bf.writeStringNT(this.battleResult);
    }

    deserialize(bf: SmartBuffer) {
      this.offlineUserId = bf.readUInt32LE();
      this.battleResult = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER,
    OC2LB_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER
  );

  export class LB2OC_NTF_SHOW_EMOTICON_INSTANT extends MIPacket {
    userId: number = 0;
    emoticonCmsId: number;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_SHOW_EMOTICON_INSTANT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.emoticonCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.emoticonCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_SHOW_EMOTICON_INSTANT,
    LB2OC_NTF_SHOW_EMOTICON_INSTANT
  );

  export class OC2CL_NTF_SHOW_EMOTICON_INSTANT extends MIPacket {
    userId: number = 0;
    emoticonCmsId: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_SHOW_EMOTICON_INSTANT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.emoticonCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.emoticonCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_SHOW_EMOTICON_INSTANT,
    OC2CL_NTF_SHOW_EMOTICON_INSTANT
  );

  export class OC2LB_NTF_UPDATE_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_ENTRIES extends MIPacket {
    userId: number = 0;
    oceanDoodadCmsId: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_UPDATE_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_ENTRIES;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.oceanDoodadCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.oceanDoodadCmsId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_UPDATE_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_ENTRIES,
    OC2LB_NTF_UPDATE_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_ENTRIES
  );

  export class LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_CHECK_SUCCESS extends MIPacket {
    userId: number = 0;
    doodadCmsIds: number[] = [];

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_CHECK_SUCCESS;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      let pos = bf.writeOffset;
      bf.writeInt32LE(0, pos);
      let count = 0;
      for (const cmsId of this.doodadCmsIds) {
        count++;
        bf.writeInt32LE(cmsId);
      }

      bf.writeInt32LE(count, pos);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadCmsIds = [];
      let count = bf.readInt32LE();
      for (let i = 0; i < count; i++) {
        const onfId = bf.readInt32LE();
        this.doodadCmsIds.push(onfId);
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_CHECK_SUCCESS,
    LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_CHECK_SUCCESS
  );

  export class LB2OC_NTF_DEV_ATTACK_TO_ME extends MIPacket {
    userId: number = 0;
    flag: boolean = false;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_ATTACK_TO_ME;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt8(this.flag ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.flag = bf.readInt8() ? true : false;
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_DEV_ATTACK_TO_ME, LB2OC_NTF_DEV_ATTACK_TO_ME);

  export class LB2OC_REQ_ENCOUNT_USER_CHOICE extends MIPacket {
    userId: number = 0;
    choice: number = 0;
    encountResult: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_REQ_ENCOUNT_USER_CHOICE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.choice);
      bf.writeInt32LE(this.encountResult);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.choice = bf.readInt32LE();
      this.encountResult = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_REQ_ENCOUNT_USER_CHOICE,
    LB2OC_REQ_ENCOUNT_USER_CHOICE
  );

  export class OC2LB_RES_ENCOUNT_USER_CHOICE extends MIPacket {
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_RES_ENCOUNT_USER_CHOICE;
    }

    serialize(bf: SmartBuffer) {}

    deserialize(bf: SmartBuffer) {}
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_RES_ENCOUNT_USER_CHOICE,
    OC2LB_RES_ENCOUNT_USER_CHOICE
  );

  export class OC2LB_NTF_ENCOUNT_NPC_CHOICE extends MIPacket {
    targetUserId: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_ENCOUNT_NPC_CHOICE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.targetUserId);
    }

    deserialize(bf: SmartBuffer) {
      this.targetUserId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2LB_NTF_ENCOUNT_NPC_CHOICE, OC2LB_NTF_ENCOUNT_NPC_CHOICE);

  export class OC2LB_REQ_ENCOUNT_NET_USER_CHOICE extends MIPacket {
    userId: number = 0;
    targetChoice: number = 0;
    encountResult: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_REQ_ENCOUNT_NET_USER_CHOICE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.targetChoice);
      bf.writeInt32LE(this.encountResult);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.targetChoice = bf.readInt32LE();
      this.encountResult = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_REQ_ENCOUNT_NET_USER_CHOICE,
    OC2LB_REQ_ENCOUNT_NET_USER_CHOICE
  );

  export class LB2OC_RES_ENCOUNT_NET_USER_CHOICE extends MIPacket {
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_RES_ENCOUNT_NET_USER_CHOICE;
    }

    serialize(bf: SmartBuffer) {}

    deserialize(bf: SmartBuffer) {}
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_RES_ENCOUNT_NET_USER_CHOICE,
    LB2OC_RES_ENCOUNT_NET_USER_CHOICE
  );

  export class OC2LB_NTF_ENCOUNT_ENEMY_WRECKED extends MIPacket {
    userId: number;
    wreckedUserId: number;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_ENCOUNT_ENEMY_WRECKED;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.wreckedUserId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.wreckedUserId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_ENCOUNT_ENEMY_WRECKED,
    OC2LB_NTF_ENCOUNT_ENEMY_WRECKED
  );

  export class OC2LB_NTF_ENCOUNT_ENEMY_LOGOUT extends MIPacket {
    userId: number;
    logoutUserId: number;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_ENCOUNT_ENEMY_LOGOUT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.logoutUserId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.logoutUserId = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_ENCOUNT_ENEMY_LOGOUT,
    OC2LB_NTF_ENCOUNT_ENEMY_LOGOUT
  );

  export class OC2LB_NTF_ENCOUNT_END_FORCEDLY extends MIPacket {
    userId: number;
    bBattle: boolean;
    reason: number; // 1:상대방 난파  2:상대방 로그아웃
    encountResult: number;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_ENCOUNT_END_FORCEDLY;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt8(this.bBattle ? 1 : 0);
      bf.writeInt32LE(this.reason);
      bf.writeInt32LE(this.encountResult);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.bBattle = bf.readInt8() ? true : false;
      this.reason = bf.readInt32LE();
      this.encountResult = bf.readInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_ENCOUNT_END_FORCEDLY,
    OC2LB_NTF_ENCOUNT_END_FORCEDLY
  );

  export class LB2OC_NTF_SYNC_FLEET_STAT extends MIPacket {
    userId: number = 0;
    wpes: { id: number; value: number }[] = [];
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_LB2OC_NTF_SYNC_FLEET_STAT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);

      bf.writeUInt8(this.wpes.length);
      this.wpes.forEach((elem) => {
        bf.writeInt32LE(elem.id);
        bf.writeInt32LE(elem.value);
      });
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();

      const wpeSize = bf.readUInt8();
      for (let i = 0; i < wpeSize; i++) {
        this.wpes.push({
          id: bf.readInt32LE(),
          value: bf.readInt32LE(),
        });
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_LB2OC_NTF_SYNC_FLEET_STAT, LB2OC_NTF_SYNC_FLEET_STAT);

  export class LB2OC_NTF_DEV_LOCAL_NPC_SPAWN_NUM extends MIPacket {
    userId: number = 0;
    on: boolean = false;
    occupyingBattle: number = 0;
    occupyingTrade: number = 0;
    occupyingExploration: number = 0;
    occupiedBattle: number = 0;
    occupiedTrade: number = 0;
    occupiedExploration: number = 0;
    randomBattle: number = 0;
    randomTrade: number = 0;
    randomExploration: number = 0;
    pirateBattle: number = 0;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DEV_LOCAL_NPC_SPAWN_NUM;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt8(this.on ? 1 : 0);
      bf.writeUInt32LE(this.occupyingBattle);
      bf.writeUInt32LE(this.occupyingTrade);
      bf.writeUInt32LE(this.occupyingExploration);
      bf.writeUInt32LE(this.occupiedBattle);
      bf.writeUInt32LE(this.occupiedTrade);
      bf.writeUInt32LE(this.occupiedExploration);
      bf.writeUInt32LE(this.randomBattle);
      bf.writeUInt32LE(this.randomTrade);
      bf.writeUInt32LE(this.randomExploration);
      bf.writeUInt32LE(this.pirateBattle);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.on = bf.readInt8() ? true : false;
      this.occupyingBattle = bf.readUInt32LE();
      this.occupyingTrade = bf.readUInt32LE();
      this.occupyingExploration = bf.readUInt32LE();
      this.occupiedBattle = bf.readUInt32LE();
      this.occupiedTrade = bf.readUInt32LE();
      this.occupiedExploration = bf.readUInt32LE();
      this.randomBattle = bf.readUInt32LE();
      this.randomTrade = bf.readUInt32LE();
      this.randomExploration = bf.readUInt32LE();
      this.pirateBattle = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DEV_LOCAL_NPC_SPAWN_NUM,
    LB2OC_NTF_DEV_LOCAL_NPC_SPAWN_NUM
  );

  export class LB2OC_NTF_SYNC_COMPANY_LEVEL extends MIPacket {
    userId: number = 0;
    companyLevel: number = 0;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_SYNC_COMPANY_LEVEL;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeUInt32LE(this.companyLevel);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.companyLevel = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_SYNC_COMPANY_LEVEL, LB2OC_NTF_SYNC_COMPANY_LEVEL);
  export class OC2CL_NTF_NET_USER_CHANGE_COMPANY_LEVEL extends MIPacket {
    userId: number = 0;
    companyLevel: number = 0;

    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_COMPANY_LEVEL;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeUInt32LE(this.companyLevel);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.companyLevel = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_COMPANY_LEVEL,
    OC2CL_NTF_NET_USER_CHANGE_COMPANY_LEVEL
  );

  export class LB2OC_NTF_SYNC_KARMA extends MIPacket {
    userId: number;
    karma: number;
    lastKarmaUpdateTimeUtc: number;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_SYNC_KARMA;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeUInt32LE(this.karma);
      if (!isNaN(this.lastKarmaUpdateTimeUtc)) {
        bf.writeUInt8(1);
        bf.writeUInt32LE(this.lastKarmaUpdateTimeUtc);
      } else {
        bf.writeUInt8(0);
      }
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.karma = bf.readUInt32LE();
      if (bf.readUInt8() === 1) {
        this.lastKarmaUpdateTimeUtc = bf.readUInt32LE();
      }
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_SYNC_KARMA, LB2OC_NTF_SYNC_KARMA);

  export class OC2CL_NTF_NET_USER_CHANGE_KARMA extends MIPacket {
    userId: number;
    karma: number;
    lastKarmaUpdateTimeUtc: number;

    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_KARMA;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeUInt32LE(this.karma);
      if (!isNaN(this.lastKarmaUpdateTimeUtc)) {
        bf.writeUInt8(1);
        bf.writeUInt32LE(this.lastKarmaUpdateTimeUtc);
      } else {
        bf.writeUInt8(0);
      }
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.karma = bf.readUInt32LE();
      if (bf.readUInt8() === 1) {
        this.lastKarmaUpdateTimeUtc = bf.readUInt32LE();
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_KARMA,
    OC2CL_NTF_NET_USER_CHANGE_KARMA
  );

  export class OC2CL_NTF_NET_USER_GUILD_UPDATE extends MIPacket {
    userId: number;
    guild: GuildAppearance;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_GUILD_UPDATE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeGuildAppearance(this.guild);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.guild = bf.readGuildAppearance();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_GUILD_UPDATE,
    OC2CL_NTF_NET_USER_GUILD_UPDATE
  );

  export class LB2OC_NTF_GUILD_LEAVE extends MIPacket {
    userId: number;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_GUILD_LEAVE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_GUILD_LEAVE, LB2OC_NTF_GUILD_LEAVE);
  export class OC2CL_NTF_NET_USER_GUILD_LEAVE extends MIPacket {
    userId: number;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_GUILD_LEAVE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_GUILD_LEAVE,
    OC2CL_NTF_NET_USER_GUILD_LEAVE
  );

  export class LB2OC_NTF_GUILD_UPDATE extends MIPacket {
    userId: number;
    guild: GuildAppearance;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_GUILD_UPDATE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeGuildAppearance(this.guild);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.guild = bf.readGuildAppearance();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_GUILD_UPDATE, LB2OC_NTF_GUILD_UPDATE);

  export class LB2OC_NTF_NATION_CABINET_UPDATE extends MIPacket {
    userId: number;
    nationCabinet: NationCabinetAppearance;
    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }
    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NATION_CABINET_UPDATE;
    }
    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeNationCabinetAppearance(this.nationCabinet);
    }
    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.nationCabinet = bf.readNationCabinetAppearance();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NATION_CABINET_UPDATE,
    LB2OC_NTF_NATION_CABINET_UPDATE
  );

  export class OC2CL_NTF_NET_USER_NATION_CABINET_UPDATE extends MIPacket {
    userId: number;
    nationCabinet: NationCabinetAppearance;
    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }
    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_NATION_CABINET_UPDATE;
    }
    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeNationCabinetAppearance(this.nationCabinet);
    }
    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.nationCabinet = bf.readNationCabinetAppearance();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_NATION_CABINET_UPDATE,
    OC2CL_NTF_NET_USER_NATION_CABINET_UPDATE
  );

  export class LB2OC_NTF_NATION_CABINET_LEAVE extends MIPacket {
    userId: number;
    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }
    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NATION_CABINET_LEAVE;
    }
    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
    }
    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NATION_CABINET_LEAVE,
    LB2OC_NTF_NATION_CABINET_LEAVE
  );

  export class OC2CL_NTF_NET_USER_NATION_CABINET_LEAVE extends MIPacket {
    userId: number;
    debugTrace(): boolean {
      return false;
    }
    sendableToBot(): boolean {
      return false;
    }
    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_NATION_CABINET_LEAVE;
    }
    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
    }
    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_NATION_CABINET_LEAVE,
    OC2CL_NTF_NET_USER_NATION_CABINET_LEAVE
  );

  export class LB2OC_NTF_SERVER_DEBUG_MSG extends MIPacket {
    userId: number = 0;
    on: boolean = false;
    type: number = 0; // 1: 인카운트불가 원인 2:로컬npc스폰

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_SERVER_DEBUG_MSG;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt8(this.on ? 1 : 0);
      bf.writeInt32LE(this.type);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.on = bf.readInt8() ? true : false;
      this.type = bf.readInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_SERVER_DEBUG_MSG, LB2OC_NTF_SERVER_DEBUG_MSG);

  export class OC2CL_NTF_SERVER_DEBUG_MSG extends MIPacket {
    userId: number;
    msg: string;
    extra: string;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_SERVER_DEBUG_MSG;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeStringNT(this.msg);
      bf.writeStringNT(this.extra);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.msg = bf.readStringNT();
      this.extra = bf.readStringNT();
    }
  }
  PacketFactory.Register(EnumPacket.PT_OC2CL_NTF_SERVER_DEBUG_MSG, OC2CL_NTF_SERVER_DEBUG_MSG);

  export class LB2OC_NTF_BATTLE_LOSE extends MIPacket {
    userId: number;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_BATTLE_LOSE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(EnumPacket.PT_LB2OC_NTF_BATTLE_LOSE, LB2OC_NTF_BATTLE_LOSE);

  export class OC2CL_NTF_ELITE_NPC_SPAWN_NOTIFICATION extends MIPacket {
    npcCmsId: number;
    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_ELITE_NPC_SPAWN_NOTIFICATION;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.npcCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.npcCmsId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_ELITE_NPC_SPAWN_NOTIFICATION,
    OC2CL_NTF_ELITE_NPC_SPAWN_NOTIFICATION
  );

  export class LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_TRAINING_GRADE extends MIPacket {
    userId: number;
    admiralTrainingGrade: number;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_TRAINING_GRADE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeUInt32LE(this.admiralTrainingGrade);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.admiralTrainingGrade = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_TRAINING_GRADE,
    LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_TRAINING_GRADE
  );

  export class LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_ILLUST extends MIPacket {
    userId: number;
    admiralIllustCmsId: number;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_ILLUST;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeUInt32LE(this.admiralIllustCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.admiralIllustCmsId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_ILLUST,
    LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_ILLUST
  );

  export class LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_DESPAWN extends MIPacket {
    userId: number = 0;
    doodadCmsIds: number[] = [];

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_DESPAWN;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      let pos = bf.writeOffset;
      bf.writeInt32LE(0, pos);
      let count = 0;
      for (const cmsId of this.doodadCmsIds) {
        count++;
        bf.writeInt32LE(cmsId);
      }

      bf.writeInt32LE(count, pos);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.doodadCmsIds = [];
      let count = bf.readInt32LE();
      for (let i = 0; i < count; i++) {
        const onfId = bf.readInt32LE();
        this.doodadCmsIds.push(onfId);
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_DESPAWN,
    LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_DESPAWN
  );

  export class LB2OC_NTF_NET_CHANGE_USER_TITLE extends MIPacket {
    userId: number;
    userTitle: UserTitle;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_CHANGE_USER_TITLE;
    }

    serialize(bf: SmartBuffer): void {
      bf.writeInt32LE(this.userId);
      if (this.userTitle) {
        bf.writeInt8(1);
        bf.writeUserTitle(this.userTitle);
      } else {
        bf.writeInt8(0);
      }
    }

    deserialize(bf: SmartBuffer): void {
      this.userId = bf.readInt32LE();
      if (bf.readInt8()) {
        this.userTitle = bf.readUserTitle();
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_CHANGE_USER_TITLE,
    LB2OC_NTF_NET_CHANGE_USER_TITLE
  );

  export class OC2CL_NTF_NET_CHANGE_USER_TITLE extends MIPacket {
    userId: number;
    userTitle: UserTitle = null;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_USER_TITLE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      if (this.userTitle) {
        bf.writeInt8(1);
        bf.writeUserTitle(this.userTitle);
      } else {
        bf.writeInt8(0);
      }
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      if (bf.readInt8()) {
        this.userTitle = bf.readUserTitle();
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_NET_USER_CHANGE_USER_TITLE,
    OC2CL_NTF_NET_CHANGE_USER_TITLE
  );

  export class LB2OC_NTF_DELEGATE_NAVI_RESULT extends MIPacket {
    userId: number;
    queryResult: {
      npcId: string;
      path: { x: number; y: number }[];
    }[];

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_DELEGATE_NAVI_RESULT;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);

      bf.writeUInt16LE(this.queryResult.length);
      this.queryResult.forEach((query) => {
        bf.writeStringNT(query.npcId);

        const pathLen = query.path.length;
        bf.writeInt16LE(pathLen);

        query.path.forEach((waypoint) => {
          bf.writeInt32LE(waypoint.x);
          bf.writeInt32LE(waypoint.y);
        });
      });
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      const queryLen = bf.readUInt16LE();

      this.queryResult = [];

      for (let i = 0; i < queryLen; i++) {
        const npcId: string = bf.readStringNT();
        const pathLen: number = bf.readUInt16LE();

        const path: {
          x: number;
          y: number;
        }[] = [];

        for (let j = 0; j < pathLen; j++) {
          path.push({
            x: bf.readInt32LE(),
            y: bf.readInt32LE(),
          });
        }

        this.queryResult.push({
          npcId: npcId,
          path: path,
        });
      }
    }
  }

  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_DELEGATE_NAVI_RESULT,
    LB2OC_NTF_DELEGATE_NAVI_RESULT
  );

  export class OC2CL_NTF_DELEGATE_NAVI_QUERY extends MIPacket {
    userId: number;
    querys: {
      npcId: string;
      fromX: number;
      fromY: number;
      toX: number;
      toY: number;
    }[];

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2CL_NTF_DELEGATE_NAVI_QUERY;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);

      bf.writeUInt16LE(this.querys.length);
      this.querys.forEach((q) => {
        bf.writeStringNT(q.npcId);
        bf.writeInt32LE(q.fromX);
        bf.writeInt32LE(q.fromY);
        bf.writeInt32LE(q.toX);
        bf.writeInt32LE(q.toY);
      });
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();

      this.querys = [];
      const len = bf.readUInt16LE();
      for (let i = 0; i < len; i++) {
        this.querys.push({
          npcId: bf.readStringNT(),
          fromX: bf.readInt32LE(),
          fromY: bf.readInt32LE(),
          toX: bf.readInt32LE(),
          toY: bf.readInt32LE(),
        });
      }
    }
  }

  PacketFactory.Register(
    EnumPacket.PT_OC2CL_NTF_DELEGATE_NAVI_QUERY,
    OC2CL_NTF_DELEGATE_NAVI_QUERY
  );

  export class LB2OC_NTF_NET_SWEEP_LOCAL_NPC extends MIPacket {
    userId: number;
    userCombatPower: number;
    npcId: string;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_SWEEP_LOCAL_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.userId);
      bf.writeInt32LE(this.userCombatPower);
      bf.writeStringNT(this.npcId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readInt32LE();
      this.userCombatPower = bf.readInt32LE();
      this.npcId = bf.readStringNT();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_SWEEP_LOCAL_NPC,
    LB2OC_NTF_NET_SWEEP_LOCAL_NPC
  );

  export class OC2LB_NTF_NET_SWEEP_LOCAL_NPC extends MIPacket {
    result: number;
    oceanFleetData: OceanFleetData;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_OC2LB_NTF_NET_SWEEP_LOCAL_NPC;
    }

    serialize(bf: SmartBuffer) {
      bf.writeInt32LE(this.result);
      if (this.oceanFleetData) {
        bf.writeInt8(1);
        bf.writeOceanFleetData(this.oceanFleetData);
      } else {
        bf.writeInt8(0);
      }
    }

    deserialize(bf: SmartBuffer) {
      this.result = bf.readInt32LE();
      if (bf.readInt8()) {
        this.oceanFleetData = bf.readOceanFleetData();
      }
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_OC2LB_NTF_NET_SWEEP_LOCAL_NPC,
    OC2LB_NTF_NET_SWEEP_LOCAL_NPC
  );

  export class LB2OC_NTF_NET_USER_CHANGE_REPRESENTED_MATE extends MIPacket {
    userId: number;
    representedMateCmsId: number;
    representedMateIllustCmsId: number;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_REPRESENTED_MATE;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeUInt32LE(this.representedMateCmsId);
      bf.writeUInt32LE(this.representedMateIllustCmsId);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.representedMateCmsId = bf.readUInt32LE();
      this.representedMateIllustCmsId = bf.readUInt32LE();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_REPRESENTED_MATE,
    LB2OC_NTF_NET_USER_CHANGE_REPRESENTED_MATE
  );

  export class LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_IS_TRANSCENDED extends MIPacket {
    userId: number;
    admiralIsTranscended: number;

    debugTrace(): boolean {
      return false;
    }

    sendableToBot(): boolean {
      return false;
    }

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_IS_TRANSCENDED;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeUInt8(this.admiralIsTranscended);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.admiralIsTranscended = bf.readUInt8();
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_IS_TRANSCENDED,
    LB2OC_NTF_NET_USER_CHANGE_ADMIRAL_IS_TRANSCENDED
  );

  //**********************************************************************************
  export class LB2OC_NTF_NET_USER_CONTINUOUS_SWEEP_REWARD_ENTER_OR_LEAVE extends MIPacket {
    userId: number = 0;
    bEnterSweepReward: boolean = false;

    getPacketId() {
      return EnumPacket.PT_LB2OC_NTF_NET_USER_CONTINUOUS_SWEEP_REWARD;
    }

    serialize(bf: SmartBuffer) {
      bf.writeUInt32LE(this.userId);
      bf.writeInt8(this.bEnterSweepReward === true ? 1 : 0);
    }

    deserialize(bf: SmartBuffer) {
      this.userId = bf.readUInt32LE();
      this.bEnterSweepReward = bf.readInt8() === 1 ? true : false;
    }
  }
  PacketFactory.Register(
    EnumPacket.PT_LB2OC_NTF_NET_USER_CONTINUOUS_SWEEP_REWARD,
    LB2OC_NTF_NET_USER_CONTINUOUS_SWEEP_REWARD_ENTER_OR_LEAVE
  );
}
