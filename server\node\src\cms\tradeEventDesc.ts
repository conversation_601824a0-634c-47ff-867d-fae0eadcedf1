// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import cms from '../cms';
import { TRADE_EVENT_TYPE } from '../motiflib/model/town';
import { TRADE_GOODS_CATEGORY } from './tradeGoodsDesc';

export interface TradeEvent {
  eventType: TRADE_EVENT_TYPE;
  expirationSession: number;
  percent?: number;
}

export interface TradeEventDesc {
  id: number;
  name: string;
  tradeEventType: TRADE_EVENT_TYPE;
  majorTrendHour: number;
  tradeEventTypeTimeMin: number;
  tradeEventTypeTimeMax: number;
  majorTrendAURate: number;
  majorTrendBudgetValue: number;

  getTradeGoodsDefaultValue: number;
  getTradeGoods: { Category: number; Value: number }[];
  villageFriendshipDefaultValue: number;
  villageFriendship: { Category: number; Value: number }[];
  villageStorageDefaultValue: number;
  villageStorage: { Category: number; Value: number }[];
  nagotiationRate: number;
  eventLimit: number;
  tradeEventValueMin: number;
  tradeEventValueMax: number;
  marketEventPriceType: number;
  marketEventPriceValue: number;
  majorTrendTradeGoodsCategory: TRADE_GOODS_CATEGORY[];
  marketEventTypeRequirementWeek: number;
  marketEventCultureAble: number[];
}

export function createTradeEventSessionId(curTimeUtc: number) {
  return Math.floor(curTimeUtc / cms.Define.TownTradeScheduleIntervalMin / 60);
}
export function convertToTimeUtc(sessionId: number) {
  return sessionId * cms.Define.TownTradeScheduleIntervalMin * 60;
}
