"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorCode = exports.spName = void 0;
const query = __importStar(require("../query"));
const merror_1 = require("../../motiflib/merror");
exports.spName = 'mp_u_ship_customizing_update_sail_crest_cms_id';
exports.errorCode = merror_1.MErrorCode.SHIP_CUSTOMIZING_UPDATE_SAIL_CREST_CMS_ID_QUERY_ERROR;
const spFunction = query.generateSPFunction(exports.spName);
const catchHandler = query.generateMErrorRejection(exports.errorCode);
function default_1(connection, userId, cmsId) {
    return spFunction(connection, userId, cmsId)
        .then((qr) => {
        if (qr.rows[0][0]['ROW_COUNT()'] !== '1' && qr.rows[0][0]['ROW_COUNT()'] !== '2') {
            throw new merror_1.MError(exports.spName + ' is failed', exports.errorCode, {
                cmsId,
            });
        }
        return;
    })
        .catch(catchHandler);
}
exports.default = default_1;
//# sourceMappingURL=puShipCustomizingUpdateSailCrestCmsId.js.map