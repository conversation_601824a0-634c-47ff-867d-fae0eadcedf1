// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MErrorCode, MError } from '../../motiflib/merror';
import { ManufactureProgress } from '../../lobbyd/userManufacture';

export const spName = 'mp_u_manufacture_progress_update';
export const errorCode = MErrorCode.MANUFACTURE_PROGRESS_UPDATE_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  roomCmsId: number,
  slot: number,
  slotData: ManufactureProgress[number] | null
): Promise<void> {
  // 음 혹시나 여기 null로 해두긴 했는데 호출하는 곳에서 이미 null 체크를 하므로 걸릴일은 없다..
  // 걸리면... 음.... 
  if (!slotData) {
    throw new MError('slotData is null', errorCode, {
      userId,
      roomCmsId,
      slot,
    });
  }

  return spFunction(connection, userId, roomCmsId, slot, slotData.recipeId, slotData.startTimeUtc, slotData.completionTimeUtc, slotData.resultType, JSON.stringify(slotData.mateCmsIds), slotData.successRate, slotData.greatSuccessRate, slotData.extra)
    .then((qr) => {
      if (qr.rows[0][0]['ROW_COUNT()'] === '0') {
        throw new MError(spName + ' is failed', errorCode, {
          userId,
          roomCmsId,
          slot,
          slotData,
        });
      }
      return;
    })
    .catch(catchHandler);
} 