CREATE PROCEDURE `mp_u_items_batch_update`(
  IN inUserId INT,
  IN inItemChanges JSON
)
label_body:BEGIN
  DECLARE i INT DEFAULT 0;
  DECLARE item_count INT;
  DECLARE current_cms_id INT;
  DECLARE current_count INT;
  DECLARE current_unbound_count INT;
  
  -- JSON 배열의 길이를 가져옴
  SET item_count = JSON_LENGTH(inItemChanges);
  
  -- 각 아이템 변경사항을 순회하며 처리
  WHILE i < item_count DO
    -- JSON에서 cmsId, count, unboundCount를 추출
    SET current_cms_id = JSON_EXTRACT(inItemChanges, CONCAT('$[', i, '].cmsId'));
    SET current_count = JSON_EXTRACT(inItemChanges, CONCAT('$[', i, '].count'));
    SET current_unbound_count = JSON_EXTRACT(inItemChanges, CONCAT('$[', i, '].unboundCount'));
    
    -- 아이템 업데이트 실행
    INSERT INTO u_items
    (
      userId,
      cmsId,
      count,
      unboundCount
    )
    VALUES 
    (
      inUserId,
      current_cms_id,
      current_count,
      current_unbound_count
    ) 
    ON DUPLICATE KEY UPDATE 
      count = current_count,
      unboundCount = current_unbound_count;
    
    SET i = i + 1;
  END WHILE;
  
  SELECT ROW_COUNT() as affectedRows;
END 