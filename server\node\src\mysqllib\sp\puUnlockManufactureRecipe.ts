// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MErrorCode, MError } from '../../motiflib/merror';

export const spName = 'mp_u_manufacture_recipe_unlock';
export const errorCode = MErrorCode.MANUFACTURE_UNLOCK_RECIPE_TXN_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  recipeCmsId: number,
  unlockTimeUtc?: Date
): Promise<void> {
  // If unlockTimeUtc is provided, we need to modify the stored procedure call
  // For now, we'll use the existing procedure which uses NOW()
  return spFunction(connection, userId, recipeCmsId)
    .then((qr) => {
      if (qr.rows[0][0]['ROW_COUNT()'] === '0') {
        throw new MError(spName + ' is failed', errorCode, {
          userId,
          recipeCmsId,
          unlockTimeUtc,
        });
      }
      return;
    })
    .catch(catchHandler);
} 