import Redis from 'ioredis';
import { RemapToolConfig, RedisInstanceConfig } from '../types';

export class RedisManager {
  private clients: Map<string, Redis> = new Map();
  private config: RemapToolConfig;

  constructor(config: RemapToolConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    console.log('Initializing Redis connections...');

    try {
      await this.initializeSharedRedis();

      for (const world of this.config.worlds) {
        await this.initializeWorldRedis(world.id);
      }

      console.log('All Redis connections successfully initialized.');
    } catch (error) {
      await this.cleanup();
      throw new Error(`Redis initialization failed: ${error}`);
    }
  }

  private async initializeSharedRedis(): Promise<void> {
    const sharedConfig = this.config.sharedConfig;

    await this.createAndConnectClient('auth', sharedConfig.authRedis);
    await this.createAndConnectClient('monitor', sharedConfig.monitorRedis);
    await this.createAndConnectClient('order', sharedConfig.orderRedis);

    if (sharedConfig.globalMatchRedis) {
      await this.createAndConnectClient('globalMatch', sharedConfig.globalMatchRedis);
    }

    if (sharedConfig.globalBattleLogRedis) {
      await this.createAndConnectClient('globalBattleLog', sharedConfig.globalBattleLogRedis);
    }
  }

  private async initializeWorldRedis(worldId: string): Promise<void> {
    const world = this.config.worlds.find(w => w.id === worldId);
    if (!world) {
      throw new Error(`World ${worldId} not found`);
    }

    const redisInstances = [
      { name: 'userCache', config: world.userCacheRedis },
      { name: 'town', config: world.townRedis },
      { name: 'nation', config: world.nationRedis },
      { name: 'collector', config: world.collectorRedis },
      { name: 'sail', config: world.sailRedis },
      { name: 'guild', config: world.guildRedis },
      { name: 'arena', config: world.arenaRedis },
      { name: 'raid', config: world.raidRedis },
      { name: 'ranking', config: world.rankingRedis },
      { name: 'user', config: world.userRedis },
      { name: 'townLb', config: world.townLbRedis },
      { name: 'oceanLb', config: world.oceanLbRedis },
      { name: 'blindBid', config: world.blindBidRedis },
    ];

    for (const instance of redisInstances) {
      const clientName = `${worldId}-${instance.name}`;
      await this.createAndConnectClient(clientName, instance.config);
    }
  }

  private async createAndConnectClient(name: string, config: RedisInstanceConfig): Promise<void> {
    const redisOptions: any = {
      host: config.redisCfg.host,
      port: config.redisCfg.port,
      db: config.redisCfg.db,
      connectTimeout: 10000,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      enableReadyCheck: false,
    };

    if (config.redisCfg.password) {
      redisOptions.password = config.redisCfg.password;
    }

    const client = new Redis(redisOptions);

    client.on('error', (error) => {
      console.error(`Redis ${name} connection error:`, error);
    });

    client.on('connect', () => {
      // console.log(`Redis ${name} connected successfully`);
    });

    client.on('close', () => {
      // console.log(`Redis ${name} disconnected`);
    });

    await client.connect();
    await client.ping();

    this.clients.set(name, client);
  }

  getClient(instanceName: string): Redis {
    const client = this.clients.get(instanceName);
    if (!client) {
      throw new Error(`Redis instance '${instanceName}' not found`);
    }
    return client;
  }

  getWorldClient(worldId: string, instanceType: string): any {
    const clientName = `${worldId}-${instanceType}`;
    return this.getClient(clientName);
  }

  getAuthClient(): any {
    return this.getClient('auth');
  }

  getMonitorClient(): any {
    return this.getClient('monitor');
  }

  getOrderClient(): any {
    return this.getClient('order');
  }

  getUserCacheClient(worldId: string): any {
    return this.getWorldClient(worldId, 'userCache');
  }

  getNationClient(worldId: string): any {
    return this.getWorldClient(worldId, 'nation');
  }

  getAllClients(): Map<string, any> {
    return new Map(this.clients);
  }

  async findKeys(instanceName: string, pattern: string): Promise<string[]> {
    const client = this.getClient(instanceName);
    const keys: string[] = [];

    const stream = client.scanStream({
      match: pattern,
      count: 1024
    });

    return new Promise((resolve, reject) => {
      stream.on('data', (resultKeys: string[]) => {
        keys.push(...resultKeys);
      });

      stream.on('end', () => {
        resolve(keys);
      });

      stream.on('error', (error) => {
        reject(error);
      });
    });
  }

  async keyExists(instanceName: string, key: string): Promise<boolean> {
    const client = this.getClient(instanceName);
    const result = await client.exists(key);
    return result === 1;
  }

  async getValue(instanceName: string, key: string): Promise<string | null> {
    const client = this.getClient(instanceName);
    return await client.get(key);
  }

  async setValue(instanceName: string, key: string, value: string): Promise<void> {
    const client = this.getClient(instanceName);
    await client.set(key, value);
  }

  async deleteKey(instanceName: string, key: string): Promise<boolean> {
    const client = this.getClient(instanceName);
    const result = await client.del(key);
    return result === 1;
  }

  async renameKey(instanceName: string, oldKey: string, newKey: string): Promise<void> {
    const client = this.getClient(instanceName);
    await client.rename(oldKey, newKey);
  }

  async getHashField(instanceName: string, key: string, field: string): Promise<string | null> {
    const client = this.getClient(instanceName);
    return await client.hget(key, field);
  }

  async setHashField(instanceName: string, key: string, field: string, value: string): Promise<void> {
    const client = this.getClient(instanceName);
    await client.hset(key, field, value);
  }

  async getHashAll(instanceName: string, key: string): Promise<Record<string, string>> {
    const client = this.getClient(instanceName);
    return await client.hgetall(key);
  }

  async executeLuaScript(
    instanceName: string,
    script: string,
    keys: string[] = [],
    args: string[] = []
  ): Promise<any> {
    const client = this.getClient(instanceName);
    return await client.eval(script, keys.length, ...keys, ...args);
  }

  async checkConnections(): Promise<Record<string, boolean>> {
    const result: Record<string, boolean> = {};

    for (const [name, client] of this.clients) {
      try {
        await client.ping();
        result[name] = true;
      } catch {
        result[name] = false;
      }
    }

    return result;
  }

  async isConnected(instanceName: string): Promise<boolean> {
    try {
      const client = this.getClient(instanceName);
      await client.ping();
      return true;
    } catch {
      return false;
    }
  }

  async backupKeys(instanceName: string, keyPattern: string): Promise<Record<string, string>> {
    const client = this.getClient(instanceName);
    const keys = await this.findKeys(instanceName, keyPattern);
    const backup: Record<string, string> = {};

    for (const key of keys) {
      const value = await client.get(key);
      if (value !== null) {
        backup[key] = value;
      }
    }

    return backup;
  }

  async restoreKeys(instanceName: string, backup: Record<string, string>): Promise<void> {
    const client = this.getClient(instanceName);
    const pipeline = client.multi();

    for (const [key, value] of Object.entries(backup)) {
      pipeline.set(key, value);
    }

    await pipeline.exec();
  }

  getWorldIds(): string[] {
    return this.config.worlds.map(world => world.id);
  }

  getInstanceNames(): string[] {
    return Array.from(this.clients.keys());
  }

  async cleanup(): Promise<void> {
    console.log('Cleaning up Redis connections...');

    const cleanupPromises = Array.from(this.clients.values()).map(async (client) => {
      try {
        await client.quit();
      } catch (error) {
        console.warn('Error during Redis connection cleanup:', error);
      }
    });

    await Promise.all(cleanupPromises);
    this.clients.clear();

    console.log('All Redis connections cleaned up.');
  }

  private async copyDatabase(name: string, config: RedisInstanceConfig, targetDb: number) {
    const sourceClient = await this.transientCreateAndConnectClient(name, config);
    const targetClient = await this.transientCreateAndConnectClient(name, { ...config, redisCfg: { ...config.redisCfg, db: targetDb } });

    console.log(`📦 Copying from DB ${name}:${config.redisCfg.db} to DB ${name}:${targetDb}`);

    await targetClient.flushdb();
    console.log(`✅ DB ${targetDb} flushed`);

    const keys: string[] = [];
    const stream = sourceClient.scanStream();
    stream.on('data', (resultKeys: string[]) => {
      keys.push(...resultKeys);
    });

    await new Promise((resolve, reject) => {
      stream.on('end', resolve);
      stream.on('error', reject);
    });

    console.log(`🔑 ${keys.length} keys found`);

    for (const key of keys) {
      const dumped = await sourceClient.dump(key);
      const ttl = await sourceClient.pttl(key);

      if (!dumped) {
        console.warn(`⚠️ Failed to dump key: ${key}`);
        continue;
      }

      const safeTtl = ttl >= 0 ? ttl : 0;

      try {
        if (safeTtl > 0) {
          await targetClient.restore(key, ttl, dumped, 'REPLACE');
        } else {
          await targetClient.restore(key, 0, dumped, 'REPLACE');
        }
        console.log(`✅ Copied: ${key} (TTL: ${safeTtl}ms)`);
      } catch (e) {
        console.error(`❌ Failed to restore key: ${key}`, e);
      }
    }

    await sourceClient.quit();
    await targetClient.quit();

    console.log('🏁 Done!');
  }

  private async transientCreateAndConnectClient(name: string, config: RedisInstanceConfig): Promise<Redis> {
    const redisOptions: any = {
      host: config.redisCfg.host,
      port: config.redisCfg.port,
      db: config.redisCfg.db,
      connectTimeout: 10000,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      enableReadyCheck: false,
    };

    if (config.redisCfg.password) {
      redisOptions.password = config.redisCfg.password;
    }

    const client = new Redis(redisOptions);

    client.on('error', (error: any) => {
      console.error(`Redis ${name} connection error:`, error);
    });

    client.on('connect', () => {
      console.log(`Redis ${name} connected successfully`);
    });

    client.on('close', () => {
      console.log(`Redis ${name} disconnected`);
    });

    await client.connect();
    await client.ping();

    return client;
  }
}
