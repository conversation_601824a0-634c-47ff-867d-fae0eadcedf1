import { DatabaseManager } from './databaseManager';
import { DatabaseTableInfo, RemapData, DatabaseUpdateResult, BatchOptions } from '../types';
import mysql from 'mysql2/promise';

export class DatabaseUpdateEngine {
  private dbManager: DatabaseManager;
  private batchOptions: BatchOptions;

  constructor(dbManager: DatabaseManager, batchOptions: BatchOptions = {
    batchSize: 100,
    delayBetweenBatches: 1000,
    maxRetries: 3,
    retryDelay: 5000,
  }) {
    this.dbManager = dbManager;
    this.batchOptions = batchOptions;
  }

  async updateAllTables(
    tables: DatabaseTableInfo[],
    remapData: RemapData[],
    worldId?: string
  ): Promise<DatabaseUpdateResult[]> {
    const results: DatabaseUpdateResult[] = [];

    const filteredRemapData = worldId
      ? remapData.filter(data => data.gameServerId === worldId)
      : remapData;

    if (filteredRemapData.length === 0) {
      console.log('No data to update.');
      return results;
    }

    console.log(`Starting update of ${tables.length} tables with ${filteredRemapData.length} records`);

    const authTables = tables.filter(t => t.database === 'auth');
    const worldTables = tables.filter(t => t.database === 'world');
    const userTables = tables.filter(t => t.database === 'user');

    for (const table of authTables) {
      const result = await this.updateAuthTable(table, filteredRemapData);
      results.push(result);
    }

    for (const table of worldTables) {
      if (worldId) {
        const result = await this.updateWorldTable(table, filteredRemapData, worldId);
        results.push(result);
      } else {
        const worldIds = [...new Set(filteredRemapData.map(d => d.gameServerId))];
        for (const wId of worldIds) {
          const worldData = filteredRemapData.filter(d => d.gameServerId === wId);
          const result = await this.updateWorldTable(table, worldData, wId);
          results.push(result);
        }
      }
    }

    for (const table of userTables) {
      if (worldId) {
        const result = await this.updateUserTable(table, filteredRemapData, worldId);
        results.push(result);
      } else {
        const worldIds = [...new Set(filteredRemapData.map(d => d.gameServerId))];
        for (const wId of worldIds) {
          const worldData = filteredRemapData.filter(d => d.gameServerId === wId);
          const result = await this.updateUserTable(table, worldData, wId);
          results.push(result);
        }
      }
    }

    const totalAffected = results.reduce((sum, r) => sum + Number(r.affectedRows), 0);
    console.log(`Database update completed: ${totalAffected} rows affected`);

    return results;
  }

  private async updateAuthTable(
    table: DatabaseTableInfo,
    remapData: RemapData[]
  ): Promise<DatabaseUpdateResult> {
    const startTime = Date.now();

    try {
      const pool = this.dbManager.getAuthConnection();

      if (!await this.dbManager.tableExists(pool, table.tableName)) {
        return {
          tableName: `auth.${table.tableName}`,
          affectedRows: 0,
          success: false,
          error: 'Table does not exist',
          executionTime: Date.now() - startTime,
        };
      }

      let totalAffected = 0;

      if (remapData.length === 0) {
        return {
          tableName: `auth.${table.tableName}`,
          affectedRows: 0,
          success: true,
          executionTime: Date.now() - startTime,
        };
      }

      if (table.hasAccountId) {
        const accountIdColumns = table.columns.filter(c => c.relatedTo === 'accountId');
        for (const column of accountIdColumns) {
          const affected = await this.updateColumnInBatches(
            pool,
            table.tableName,
            column.columnName,
            remapData.map(d => ({ old: d.uwo_Gnid, new: d.uwogl_Gnid }))
          );
          totalAffected += Number(affected) || 0;
        }
      }

      if (table.hasPubId) {
        const pubIdColumns = table.columns.filter(c => c.relatedTo === 'pubId');
        for (const column of pubIdColumns) {
          const affected = await this.updateColumnInBatches(
            pool,
            table.tableName,
            column.columnName,
            remapData.map(d => ({ old: d.uwo_Nid, new: d.uwogl_Nid }))
          );
          totalAffected += Number(affected) || 0;
        }
      }

      return {
        tableName: `auth.${table.tableName}`,
        affectedRows: totalAffected,
        success: true,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        tableName: `auth.${table.tableName}`,
        affectedRows: 0,
        success: false,
        error: String(error),
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async updateWorldTable(
    table: DatabaseTableInfo,
    remapData: RemapData[],
    worldId: string
  ): Promise<DatabaseUpdateResult> {
    const startTime = Date.now();

    try {
      const pool = this.dbManager.getWorldConnection(worldId);

      if (!await this.dbManager.tableExists(pool, table.tableName)) {
        return {
          tableName: `world-${worldId}.${table.tableName}`,
          affectedRows: 0,
          success: false,
          error: 'Table does not exist',
          executionTime: Date.now() - startTime,
        };
      }

      let totalAffected = 0;

      if (remapData.length === 0) {
        return {
          tableName: `world-${worldId}.${table.tableName}`,
          affectedRows: 0,
          success: true,
          executionTime: Date.now() - startTime,
        };
      }

      if (table.hasAccountId) {
        const accountIdColumns = table.columns.filter(c => c.relatedTo === 'accountId');
        for (const column of accountIdColumns) {
          const affected = await this.updateColumnInBatches(
            pool,
            table.tableName,
            column.columnName,
            remapData.map(d => ({ old: d.uwo_Gnid, new: d.uwogl_Gnid }))
          );
          totalAffected += Number(affected) || 0;
        }
      }

      if (table.hasPubId) {
        const pubIdColumns = table.columns.filter(c => c.relatedTo === 'pubId');
        for (const column of pubIdColumns) {
          const affected = await this.updateColumnInBatches(
            pool,
            table.tableName,
            column.columnName,
            remapData.map(d => ({ old: d.uwo_Nid, new: d.uwogl_Nid }))
          );
          totalAffected += Number(affected) || 0;
        }
      }

      return {
        tableName: `world-${worldId}.${table.tableName}`,
        affectedRows: totalAffected,
        success: true,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        tableName: `world-${worldId}.${table.tableName}`,
        affectedRows: 0,
        success: false,
        error: String(error),
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async updateUserTable(
    table: DatabaseTableInfo,
    remapData: RemapData[],
    worldId: string
  ): Promise<DatabaseUpdateResult> {
    const startTime = Date.now();
    let totalAffected = 0;
    const errors: string[] = [];

    try {
      if (remapData.length === 0) {
        return {
          tableName: `user-${worldId}.${table.tableName}`,
          affectedRows: 0,
          success: true,
          executionTime: Date.now() - startTime,
        };
      }

      const worldShards = this.dbManager.getWorldUserShardConnections(worldId);

      if (worldShards.size === 0) {
        return {
          tableName: `user-${worldId}.${table.tableName}`,
          affectedRows: 0,
          success: false,
          error: `Cannot find user shards for world ${worldId}`,
          executionTime: Date.now() - startTime,
        };
      }

      const shardResults: DatabaseUpdateResult[] = [];

      for (const [shardId, pool] of worldShards) {
        const shardStartTime = Date.now();
        let shardAffected = 0;
        const shardErrors: string[] = [];

        try {
          if (!await this.dbManager.tableExists(pool, table.tableName)) {
            console.warn(`Table does not exist: user-${worldId}-shard-${shardId}.${table.tableName}`);
            shardResults.push({
              tableName: `user-${worldId}_${shardId.toString().padStart(2, '0')}.${table.tableName}`,
              affectedRows: 0,
              success: true,
              executionTime: Date.now() - shardStartTime,
            });
            continue;
          }

          if (table.hasAccountId) {
            const accountIdColumns = table.columns.filter(c => c.relatedTo === 'accountId');
            for (const column of accountIdColumns) {
              const affected = await this.updateColumnInBatches(
                pool,
                table.tableName,
                column.columnName,
                remapData.map(d => ({ old: d.uwo_Gnid, new: d.uwogl_Gnid }))
              );
              shardAffected += Number(affected) || 0;
              totalAffected += Number(affected) || 0;
            }
          }

          if (table.hasPubId) {
            const pubIdColumns = table.columns.filter(c => c.relatedTo === 'pubId');
            for (const column of pubIdColumns) {
              const affected = await this.updateColumnInBatches(
                pool,
                table.tableName,
                column.columnName,
                remapData.map(d => ({ old: d.uwo_Nid, new: d.uwogl_Nid }))
              );
              shardAffected += Number(affected) || 0;
              totalAffected += Number(affected) || 0;
            }
          }
        } catch (error) {
          shardErrors.push(`${error}`);
          errors.push(`Shard ${shardId}: ${error}`);
        }

        const shardResult: DatabaseUpdateResult = {
          tableName: `user-${worldId}_${shardId.toString().padStart(2, '0')}.${table.tableName}`,
          affectedRows: shardAffected,
          success: shardErrors.length === 0,
          executionTime: Date.now() - shardStartTime,
        };

        if (shardErrors.length > 0) {
          shardResult.error = shardErrors.join('; ');
        }

        shardResults.push(shardResult);
      }

      // 기존 통합 결과도 유지 (하위 호환성)
      const result: DatabaseUpdateResult = {
        tableName: `user-${worldId}.${table.tableName}`,
        affectedRows: totalAffected,
        success: errors.length === 0,
        executionTime: Date.now() - startTime,
      };

      if (errors.length > 0) {
        result.error = errors.join('; ');
      }

      // 샤드별 결과를 추가 속성으로 포함
      (result as any).shardResults = shardResults;

      return result;
    } catch (error) {
      return {
        tableName: `user-${worldId}.${table.tableName}`,
        affectedRows: totalAffected,
        success: false,
        error: String(error),
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async updateColumnInBatches(
    pool: mysql.Pool,
    tableName: string,
    columnName: string,
    mappings: Array<{ old: string; new: string }>
  ): Promise<number> {
    if (mappings.length === 0) return 0;

    const updateQueries: string[] = [];

    for (let i = 0; i < mappings.length; i += this.batchOptions.batchSize) {
      const batch = mappings.slice(i, i + this.batchOptions.batchSize);
      const query = this.generateUpdateQuery(tableName, columnName, batch);
      if (query && query.trim()) {
        updateQueries.push(query);
      }
    }

    console.log(`Generated ${updateQueries.length} update queries`);

    let totalAffected = 0;
    for (let i = 0; i < updateQueries.length; i++) {
      const query = updateQueries[i];

      if (!query || !query.trim()) {
        console.warn(`Skipping empty query: ${i + 1}/${updateQueries.length}`);
        continue;
      }

      let retries = 0;
      while (retries < this.batchOptions.maxRetries) {
        try {
          console.log(`Executing query (${i + 1}/${updateQueries.length}): ${tableName}.${columnName}`);
          const result = await this.dbManager.executeQuery(pool, query) as mysql.ResultSetHeader;
          totalAffected += Number(result.affectedRows) || 0;
          break;
        } catch (error) {
          retries++;
          if (retries >= this.batchOptions.maxRetries) {
            throw error;
          }
          console.warn(`Query execution failed, retry ${retries}/${this.batchOptions.maxRetries}: ${error}`);
          await this.delay(this.batchOptions.retryDelay);
        }
      }

      if (i + 1 < updateQueries.length) {
        await this.delay(this.batchOptions.delayBetweenBatches);
      }
    }

    return totalAffected;
  }

  private generateUpdateQuery(
    tableName: string,
    columnName: string,
    mappings: Array<{ old: string; new: string }>
  ): string {
    if (mappings.length === 0) return '';

    const escapedMappings = mappings.map(m => ({
      old: mysql.escape(m.old).slice(1, -1),
      new: mysql.escape(m.new).slice(1, -1)
    }));

    const caseWhenClauses = escapedMappings.map(m =>
      `WHEN '${m.old}' THEN '${m.new}'`
    ).join(' ');

    const oldValues = escapedMappings.map(m => `'${m.old}'`).join(', ');

    const query = `
      UPDATE ${tableName}
      SET ${columnName} = CASE ${columnName}
        ${caseWhenClauses}
        ELSE ${columnName}
      END
      WHERE ${columnName} IN (${oldValues})
    `.trim();

    return query;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async dryRun(
    tables: DatabaseTableInfo[],
    remapData: RemapData[],
    worldId?: string
  ): Promise<DatabaseUpdateResult[]> {
    const results: DatabaseUpdateResult[] = [];

    const filteredRemapData = worldId
      ? remapData.filter(data => data.gameServerId === worldId)
      : remapData;

    console.log(`Dry run mode: analyzing ${tables.length} tables with ${filteredRemapData.length} records`);

    // worldId가 없는 경우 모든 월드에 대해 처리
    if (!worldId) {
      const worldIds = this.getAvailableWorldIds(remapData);

      for (const table of tables) {
        if (table.database === 'auth') {
          // auth 테이블은 월드와 무관하게 한 번만 처리
          const result = await this.analyzeTableImpact(table, filteredRemapData, undefined);
          results.push(result);
        } else {
          // world, user 테이블은 각 월드별로 처리
          for (const currentWorldId of worldIds) {
            const worldFilteredData = remapData.filter(data => data.gameServerId === currentWorldId);
            const result = await this.analyzeTableImpact(table, worldFilteredData, currentWorldId);
            results.push(result);
          }
        }
      }
    } else {
      // worldId가 지정된 경우 기존 로직 사용
      for (const table of tables) {
        const result = await this.analyzeTableImpact(table, filteredRemapData, worldId);
        results.push(result);
      }
    }

    return results;
  }

  /**
   * 리맵 데이터에서 사용 가능한 월드 ID 목록 추출
   */
  private getAvailableWorldIds(remapData: RemapData[]): string[] {
    const worldIds = new Set<string>();
    remapData.forEach(data => {
      if (data.gameServerId) {
        worldIds.add(data.gameServerId);
      }
    });
    return Array.from(worldIds);
  }

  private async analyzeTableImpact(
    table: DatabaseTableInfo,
    remapData: RemapData[],
    worldId?: string
  ): Promise<DatabaseUpdateResult> {
    const startTime = Date.now();

    try {
      let pool: mysql.Pool;
      let tablePrefix: string;

      switch (table.database) {
        case 'auth':
          pool = this.dbManager.getAuthConnection();
          tablePrefix = 'auth';
          break;
        case 'world':
          if (!worldId) {
            throw new Error('World ID is required');
          }
          pool = this.dbManager.getWorldConnection(worldId);
          tablePrefix = `world-${worldId}`;
          break;
        case 'user':
          if (!worldId) {
            throw new Error('World ID is required');
          }
          // User 테이블의 경우 샤드별로 처리
          return await this.simulateUserTableUpdate(table, remapData, worldId);
        default:
          throw new Error(`Unknown database type: ${table.database}`);
      }

      if (!await this.dbManager.tableExists(pool, table.tableName)) {
        return {
          tableName: `${tablePrefix}.${table.tableName}`,
          affectedRows: 0,
          success: false,
          error: 'Table does not exist',
          executionTime: Date.now() - startTime,
        };
      }

      let totalCount = 0;

      if (table.hasAccountId && remapData.length > 0) {
        const accountIdColumns = table.columns.filter(c => c.relatedTo === 'accountId');
        for (const column of accountIdColumns) {
          const oldValues = remapData.map(d => `'${d.uwo_Gnid}'`).join(', ');
          const countQuery = `SELECT COUNT(*) as count FROM ${table.tableName} WHERE ${column.columnName} IN (${oldValues})`;
          const result = await this.dbManager.executeQuery(pool, countQuery) as mysql.RowDataPacket[];
          totalCount += Number(result[0]?.count) || 0;
        }
      }

      if (table.hasPubId && remapData.length > 0) {
        const pubIdColumns = table.columns.filter(c => c.relatedTo === 'pubId');
        for (const column of pubIdColumns) {
          const oldValues = remapData.map(d => `'${d.uwo_Nid}'`).join(', ');
          const countQuery = `SELECT COUNT(*) as count FROM ${table.tableName} WHERE ${column.columnName} IN (${oldValues})`;
          const result = await this.dbManager.executeQuery(pool, countQuery) as mysql.RowDataPacket[];
          totalCount += Number(result[0]?.count) || 0;
        }
      }

      return {
        tableName: `${tablePrefix}.${table.tableName}`,
        affectedRows: totalCount,
        success: true,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        tableName: `${table.database}.${table.tableName}`,
        affectedRows: 0,
        success: false,
        error: String(error),
        executionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * User 테이블 업데이트 시뮬레이션 (샤드별)
   */
  private async simulateUserTableUpdate(
    table: DatabaseTableInfo,
    remapData: RemapData[],
    worldId: string
  ): Promise<DatabaseUpdateResult> {
    const startTime = Date.now();

    try {
      if (remapData.length === 0) {
        return {
          tableName: `user-${worldId}.${table.tableName}`,
          affectedRows: 0,
          success: true,
          executionTime: Date.now() - startTime,
        };
      }

      const worldShards = this.dbManager.getWorldUserShardConnections(worldId);
      if (worldShards.size === 0) {
        return {
          tableName: `user-${worldId}.${table.tableName}`,
          affectedRows: 0,
          success: false,
          error: `Cannot find user shards for world ${worldId}`,
          executionTime: Date.now() - startTime,
        };
      }

      const shardResults: DatabaseUpdateResult[] = [];
      let totalCount = 0;
      const errors: string[] = [];

      for (const [shardId, pool] of worldShards) {
        const shardStartTime = Date.now();
        let shardCount = 0;
        const shardErrors: string[] = [];

        try {
          if (!await this.dbManager.tableExists(pool, table.tableName)) {
            console.warn(`Table does not exist: user-${worldId}-shard-${shardId}.${table.tableName}`);
            shardResults.push({
              tableName: `user-${worldId}_${shardId.toString().padStart(2, '0')}.${table.tableName}`,
              affectedRows: 0,
              success: true,
              executionTime: Date.now() - shardStartTime,
            });
            continue;
          }

          if (table.hasAccountId && remapData.length > 0) {
            const accountIdColumns = table.columns.filter(c => c.relatedTo === 'accountId');
            for (const column of accountIdColumns) {
              const oldValues = remapData.map(d => `'${d.uwo_Gnid}'`).join(', ');
              const countQuery = `SELECT COUNT(*) as count FROM ${table.tableName} WHERE ${column.columnName} IN (${oldValues})`;
              const result = await this.dbManager.executeQuery(pool, countQuery) as mysql.RowDataPacket[];
              const count = Number(result[0]?.count) || 0;
              shardCount += count;
              totalCount += count;
            }
          }

          if (table.hasPubId && remapData.length > 0) {
            const pubIdColumns = table.columns.filter(c => c.relatedTo === 'pubId');
            for (const column of pubIdColumns) {
              const oldValues = remapData.map(d => `'${d.uwo_Nid}'`).join(', ');
              const countQuery = `SELECT COUNT(*) as count FROM ${table.tableName} WHERE ${column.columnName} IN (${oldValues})`;
              const result = await this.dbManager.executeQuery(pool, countQuery) as mysql.RowDataPacket[];
              const count = Number(result[0]?.count) || 0;
              shardCount += count;
              totalCount += count;
            }
          }
        } catch (error) {
          shardErrors.push(`${error}`);
          errors.push(`Shard ${shardId}: ${error}`);
        }

        const shardResult: DatabaseUpdateResult = {
          tableName: `user-${worldId}_${shardId.toString().padStart(2, '0')}.${table.tableName}`,
          affectedRows: shardCount,
          success: shardErrors.length === 0,
          executionTime: Date.now() - shardStartTime,
        };

        if (shardErrors.length > 0) {
          shardResult.error = shardErrors.join('; ');
        }

        shardResults.push(shardResult);
      }

      // 기존 통합 결과도 유지 (하위 호환성)
      const result: DatabaseUpdateResult = {
        tableName: `user-${worldId}.${table.tableName}`,
        affectedRows: totalCount,
        success: errors.length === 0,
        executionTime: Date.now() - startTime,
      };

      if (errors.length > 0) {
        result.error = errors.join('; ');
      }

      // 샤드별 결과를 추가 속성으로 포함
      (result as any).shardResults = shardResults;

      return result;
    } catch (error) {
      return {
        tableName: `user-${worldId}.${table.tableName}`,
        affectedRows: 0,
        success: false,
        error: `Failed to simulate user table update: ${error}`,
        executionTime: Date.now() - startTime,
      };
    }
  }
}
