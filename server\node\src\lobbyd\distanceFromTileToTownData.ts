// ------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ------------------------------------------------------------------------------------------------

import { Service } from 'typedi';
import fs from 'fs';
import * as util from 'util';
import _ from 'lodash';
import assert from 'assert';

class UE4FloatArrayReader {
  numBits: number;
  data: Buffer;

  constructor(stream: Buffer, start?: number) {
    let offset = start ? start : 0;
    this.numBits = stream.readInt32LE(offset);
    this.data = stream.slice(offset + 4);
  }

  getAt(index: number): number {
    if (index >= this.numBits) {
      return 0;
    }
    return this.data.readFloatLE(index * 4);
  }
}

@Service()
export class DistanceFromTileToTownData {
  private _minX: number = 0;
  private _minY: number = 0;
  private _tileSizeX: number = 0;
  private _tileSizeY: number = 0;
  private _tileCountX: number = 0;
  private _tileCountY: number = 0;
  private _minTownCmsId: number = 0;
  private _maxTownCmsId: number = 0;
  private _bitArray: UE4FloatArrayReader = null;

  /**
   * @see LuaNavMesh.cpp // void ALuaNavMesh::ExportDistancesFromTileToNavPoint(...)
   */
  static async fromFile(path: string): Promise<DistanceFromTileToTownData> {
    const readFile = util.promisify(fs.readFile);
    return readFile(path).then((chunk) => {
      const output = new DistanceFromTileToTownData();
      let offset: number = 0;
      output._minX = chunk.readFloatLE(offset);
      output._minY = chunk.readFloatLE((offset += 4));
      output._tileSizeX = chunk.readFloatLE((offset += 4));
      output._tileSizeY = chunk.readFloatLE((offset += 4));
      output._tileCountX = chunk.readInt32LE((offset += 4));
      output._tileCountY = chunk.readInt32LE((offset += 4));
      output._minTownCmsId = chunk.readInt32LE((offset += 4));
      output._maxTownCmsId = chunk.readInt32LE((offset += 4));
      output._bitArray = new UE4FloatArrayReader(chunk, (offset += 4));

      //TODO? townCmsId 대역대/타일 대역대에 변동이 생기는 경우 파일을 새로 추출해야하는 걸 알릴 필요가 있음.
      return output;
    });
  }

  getDistance(tileX: number, tileY: number, townCmsId: number): number {
    this._ensureTileInRange(tileX, tileY);
    this._ensureQueryableTownCmsId(townCmsId);
    const distanceOrErr = this._getDistanceOrErr(tileX, tileY, townCmsId);
    assert(
      distanceOrErr >= 0, // 음수인 경우 에러
      `[distanceFromTileToTownData] failed to get distance. err: ${distanceOrErr}. (${tileX}, ${tileY}) -> ${townCmsId}`
    );
    return distanceOrErr;
  }

  /**
   * @returns number 언리얼단위, '길찾기 거리'가 제대로 안뽑힌 경우 음수
   */
  private _getDistanceOrErr(tileX: number, tileY: number, townCmsId: number): number {
    const townSize = this._maxTownCmsId - this._minTownCmsId + 1;
    const idx =
      townCmsId - this._minTownCmsId + tileY * this._tileCountX * townSize + tileX * townSize;
    return this._bitArray.getAt(idx);
  }

  findNearestTownCmsIdFromTile(
    tileX: number,
    tileY: number,
    predicate: (townCmsId: number) => boolean = (townCmsId) => true
  ): number | undefined {
    this._ensureTileInRange(tileX, tileY);

    let minDistance = Number.MAX_SAFE_INTEGER;
    let nearestTownCmsId: number | undefined;

    this._forEachTownDistance(tileX, tileY, (townCmsId, distance) => {
      if (distance < 0) {
        // '길찾기 거리'가 제대로 안뽑힌 경우
        return;
      }

      if (distance < minDistance && predicate(townCmsId)) {
        nearestTownCmsId = townCmsId;
        minDistance = distance;
      }
    });

    return nearestTownCmsId;
  }

  /**
   * @param tileXtileY 250 * 100 https://wiki.line.games/pages/viewpage.action?pageId=3309962
   * @param callbackFn distance: 언리얼 단위
   * 거리 계산이 안 된 경우 음수
   * (해당 타일이 이동불가/타일-타운 길찾기 실패/townCmsId에 해당하는 타운이 배치되지 않음 등)
   */
  forEachTownDistance(
    tileX: number,
    tileY: number,
    callbackFn: (townCmsId: number, distance: number) => void
  ) {
    this._ensureTileInRange(tileX, tileY);
    this._forEachTownDistance(tileX, tileY, (townCmsId, distance) =>
      callbackFn(townCmsId, distance)
    );
  }

  private _ensureTileInRange(tileX: number, tileY: number) {
    if (!this._isTileInRange(tileX, tileY)) {
      assert(false, `[distanceFromTileToTownData] out of range coordinates(${tileX}, ${tileY})`);
    }
  }

  private _isTileInRange(tileX: number, tileY: number) {
    return _.inRange(tileX, 0, this._tileCountX) && _.inRange(tileY, 0, this._tileCountY);
  }

  private _ensureQueryableTownCmsId(townCmsId: number): void {
    if (!Number.isInteger(townCmsId)) {
      assert.fail(`[distanceFromTileToTownData] townCmsId integer expected: ${townCmsId}`);
    }
    const bInRange = this._minTownCmsId <= townCmsId && townCmsId <= this._maxTownCmsId;
    if (!bInRange) {
      assert.fail(
        `[distanceFromTileToTownData] out of range townCmsId: ${townCmsId}` +
          ` (min: ${this._minTownCmsId}, max: ${this._maxTownCmsId})`
      );
    }
  }

  private _forEachTownDistance(
    tileX: number,
    tileY: number,
    callbackFn: (townCmsId: number, distance: number) => void
  ) {
    const townCount = this._maxTownCmsId - this._minTownCmsId + 1;
    const firstIdx = tileY * this._tileCountX * townCount + tileX * townCount;
    for (let i = 0; i < townCount; ++i) {
      const townCmsId = this._minTownCmsId + i;
      const dis = this._bitArray.getAt(firstIdx + i);
      callbackFn(townCmsId, dis);
    }
  }

  /**
   * @param predicate true면 Map에 저장, 기본 콜백은 distance >= 0
   */
  private _makeDistanceMap(
    tileX: number,
    tileY: number,
    predicate: (townCmsId: number, distance: number) => boolean = (_townCmsId, distance) =>
      distance >= 0
  ): Map<number, number> {
    const map = new Map<number, number>();
    this._forEachTownDistance(tileX, tileY, (townCmsId, distance) => {
      if (predicate(townCmsId, distance)) {
        map.set(townCmsId, distance);
      }
    });
    return map;
  }

  /**
   * 데이터를 제대로 읽었는지 확인하기 위해 CSV 파일로 내보낸다.
   * @param path 저장할 파일 이름
   */
  async exportToCSV(path: string): Promise<void> {
    interface Tile {
      x: number;
      y: number;
      distances: Map<number, number>;
    }
    const tiles: Tile[] = [];
    for (let tileY = 0; tileY < this._tileCountY; ++tileY) {
      for (let tileX = 0; tileX < this._tileCountX; ++tileX) {
        const distances = this._makeDistanceMap(tileX, tileY, () => true);
        tiles.push({
          x: tileX,
          y: tileY,
          distances,
        });
      }
    }

    const out: string[] = [];

    out.push(`minX: ${this._minX}\n`);
    out.push(`minY: ${this._minY}\n`);
    out.push(`tileSizeX: ${this._tileSizeX}\n`);
    out.push(`tileSizeY: ${this._tileSizeY}\n`);
    out.push(`tileCountX: ${this._tileCountX}\n`);
    out.push(`tileCountY: ${this._tileCountY}\n`);
    out.push(
      [
        `negative distance means`,
        `-1: failed to project point at tile origin`,
        `-2: failed to find path tile to town`,
        `-3: not in CMS.TownLocation`,
      ].join(',')
    );
    out.push('\n');
    out.push('\n');

    for (let townCmsId = this._minTownCmsId; townCmsId <= this._maxTownCmsId; ++townCmsId) {
      out.push(`,${townCmsId}`);
    }
    out.push('\n');

    tiles.forEach((tile) => {
      out.push(`X: ${tile.x} / Y: ${tile.y}`);
      for (let townCmsId = this._minTownCmsId; townCmsId <= this._maxTownCmsId; ++townCmsId) {
        out.push(`,${tile.distances.get(townCmsId)}`);
      }
      out.push('\n');
    });

    const writeFile = util.promisify(fs.writeFile);
    await writeFile(path, out.join(''), { encoding: 'utf-8' });
  }
}
