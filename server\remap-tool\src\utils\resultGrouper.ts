import { RemapResult, GroupedRemapResult, WorldResult, DatabaseUpdateResult, RedisUpdateResult } from '../types';

/**
 * 리맵 결과를 월드별로 그룹핑하는 유틸리티
 */
export class ResultGrouper {
  /**
   * 기존 RemapResult를 GroupedRemapResult로 변환
   */
  static groupByWorld(result: RemapResult): GroupedRemapResult {
    const sharedDatabaseResults: DatabaseUpdateResult[] = [];
    const sharedRedisResults: RedisUpdateResult[] = [];
    const worldResultsMap = new Map<string, WorldResult>();

    // 데이터베이스 결과 그룹핑
    for (const dbResult of result.databaseResults) {
      if (this.isSharedDatabase(dbResult.tableName)) {
        sharedDatabaseResults.push(dbResult);
      } else {
        const worldId = this.extractWorldIdFromTableName(dbResult.tableName);
        if (worldId) {
          if (!worldResultsMap.has(worldId)) {
            worldResultsMap.set(worldId, {
              worldId,
              databaseResults: [],
              redisResults: []
            });
          }
          worldResultsMap.get(worldId)!.databaseResults.push(dbResult);
        }
      }
    }

    // 데이터베이스 결과에서 월드 ID 목록 추출
    const worldIds = Array.from(worldResultsMap.keys());

    // Redis 결과를 간단하게 분류 (후처리 방식)
    const sharedKeys = new Set<string>();
    let worldSpecificResults: RedisUpdateResult[] = [];

    for (const redisResult of result.redisResults) {
      if (this.isSharedRedisInstance(redisResult.instanceName)) {
        // 공유 인스턴스 - 중복 제거
        const keyId = `${redisResult.keyPattern}:${redisResult.instanceName}`;
        if (!sharedKeys.has(keyId)) {
          sharedKeys.add(keyId);
          sharedRedisResults.push(redisResult);
        }
      } else {
        // 월드별 인스턴스 수집
        worldSpecificResults.push(redisResult);
      }
    }

    // 월드별 인스턴스를 월드 순서대로 분배
    let currentWorldIndex = 0;
    for (let i = 0; i < worldSpecificResults.length; i++) {
      const redisResult = worldSpecificResults[i];
      if (!redisResult) continue;

      const targetWorldId = worldIds[currentWorldIndex] || 'UWO-GL-01';

      if (!worldResultsMap.has(targetWorldId)) {
        worldResultsMap.set(targetWorldId, {
          worldId: targetWorldId,
          databaseResults: [],
          redisResults: []
        });
      }
      const worldResult = worldResultsMap.get(targetWorldId);
      if (worldResult) {
        worldResult.redisResults.push(redisResult);
      }

      // 3개마다 다음 월드로 이동 (키 패턴 수에 따라 조정)
      if ((i + 1) % 3 === 0) {
        currentWorldIndex = (currentWorldIndex + 1) % worldIds.length;
      }
    }

    return {
      success: result.success,
      totalProcessed: result.totalProcessed,
      sharedResults: {
        databaseResults: sharedDatabaseResults,
        redisResults: sharedRedisResults
      },
      worldResults: Array.from(worldResultsMap.values()).sort((a, b) => a.worldId.localeCompare(b.worldId)),
      errors: result.errors,
      warnings: result.warnings,
      startTime: result.startTime,
      endTime: result.endTime,
      duration: result.duration
    };
  }

  /**
   * 공유 데이터베이스인지 확인
   */
  private static isSharedDatabase(tableName: string): boolean {
    return tableName.startsWith('auth.');
  }

  /**
   * 테이블 이름에서 월드 ID 추출
   */
  private static extractWorldIdFromTableName(tableName: string): string | null {
    // world-UWO-GL-01.table_name 또는 user-UWO-GL-01.table_name 형태
    const match = tableName.match(/^(?:world|user)-([^.]+)\./);
    return match ? match[1] || null : null;
  }

  /**
   * 공유 Redis 인스턴스인지 확인
   */
  private static isSharedRedisInstance(instanceName?: string): boolean {
    if (!instanceName) return false;
    const sharedInstances = ['auth', 'monitor', 'order', 'globalMatch', 'globalBattleLog'];
    return sharedInstances.includes(instanceName);
  }
}
