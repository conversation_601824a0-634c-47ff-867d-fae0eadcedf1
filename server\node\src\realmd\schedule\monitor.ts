// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import schedule from 'node-schedule';
import Container from 'typedi';
import { RealmService } from '../server';
import { Promise as promise } from 'bluebird';

import mlog from '../../motiflib/mlog';
import cms from '../../cms';
import * as cmsEx from '../../cms/ex';
import * as mutil from '../../motiflib/mutil';
import { GetFullWeeksUsingLocalTime } from '../../formula';

export function startForServerGroup(): schedule.Job {
  const cron = `*/5 * * * *`;
  const job = schedule.scheduleJob(cron, () => {
    const promises = [userCount(), waitingCount()];
    return Promise.all(promises);
  });

  return job;
}

export function startForWorld(): schedule.Job {
  const cron = `59 * * * *`;
  const job = schedule.scheduleJob(cron, () => {
    const promises = [nationalPop(), collectorRank()];
    return Promise.all(promises);
  });

  return job;
}

function userCount() {
  const app = Container.get(RealmService);
  const { monitorRedis } = app;
  return monitorRedis['getUserCount']()
    .then((ret) => {
      const extra = JSON.parse(ret);
      extra.customInt1 = extra.user?.total || 0;
      mlog.info('User count: ', extra);
    })
    .catch(() => {
      // Do nothing.
    });
}

function waitingCount() {
  const app = Container.get(RealmService);
  const { orderRedis } = app;
  return orderRedis['getWaitingCount']()
    .then((ret) => {
      const extra = JSON.parse(ret);
      extra.customInt1 = extra.total || 0;
      mlog.info('waiting count: ', extra);
    })
    .catch(() => {
      // Do nothing.
    });
}

function nationalPop() {
  const app = Container.get(RealmService);
  const { nationRedis } = app;
  return nationRedis['getAllPop']()
    .then((ret) => {
      const pop = JSON.parse(ret);
      mlog.info('National pop: ', pop);
    })
    .catch(() => {
      // Do nothing.
    });
}

function collectorRank() {
  const app = Container.get(RealmService);
  const { collectorRedis } = app;

  const townBuildingCmsIds = cmsEx.getCollectorTownBuildingCmsIds();
  const curTimeUtc = mutil.curTimeUtc();
  const sessionId = GetFullWeeksUsingLocalTime(
    curTimeUtc,
    cms.Define.CollectorWeeklySessionPivotDay
  );
  return promise
    .reduce(
      townBuildingCmsIds,
      (_, townBuildingCmsId) => {
        return collectorRedis['loadCollectorRankForLog'](townBuildingCmsId, sessionId).then(
          (ret) => {
            mlog.info('Collector rank.', { townBuildingCmsId, sessionId, rank: JSON.parse(ret) });
          }
        );
      },
      {}
    )
    .then(() => {
      return collectorRedis['loadCollectorGlobalRankForLog'](
        sessionId,
        cmsEx.getMaxGlobalRanking()
      );
    })
    .then((ret) => {
      mlog.info('Collector global rank.', { sessionId, rank: JSON.parse(ret) });
    })
    .catch(() => {
      // Do nothing.
    });
}
