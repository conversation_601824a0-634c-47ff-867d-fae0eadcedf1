// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { <PERSON>lient<PERSON><PERSON>etHandler } from '../index';
import { Container } from 'typedi/Container';
import { LobbyService } from '../../server';
import { GuildData, GUILD_MEMBER_GRADE } from '../../../motiflib/model/lobby';
import { GuildUtil, GuildLogUtil } from '../../guildUtil';
import { Sync, UserLightInfo } from '../../type/sync';
import { onGuildPublish } from '../../guildPubsub';
import cms from '../../../cms';
import mconf from '../../../motiflib/mconf';
import { SdoGLogEvents } from '../../../motiflib/sdoGLogs.generated';
import { FirstFleetIndex } from '../../../cms/ex';

const rsn = 'guild_member_change';
const add_rsn = null;

// ----------------------------------------------------------------------------
interface RequestBody {
  memberUserId: number;
  isPromoted: number; //
}

/**
 * 길드원 등급 변경(길드장 전용)
 */
// ----------------------------------------------------------------------------
export class Cph_Guild_ManagingChangeMemberGrade implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { memberUserId, isPromoted } = body;
    const { guildRedis } = Container.get(LobbyService);
    const { userId } = user;

    const guildId: number = user.userGuild.guildId;
    if (!guildId) {
      throw new MError('there-is-no-guild-joined.', MErrorCode.GUILD_NOT_JOINED, {
        userId: user.userId,
      });
    }
    let updatedGrade;
    let sync: Sync;

    let guildData: GuildData;
    let userLightInfos: { [userId: number]: UserLightInfo };

    let memberNid = null;
    let memberNationCmsId = null;
    let memberOldGrade = null;

    //======================================================================================================
    return (
      GuildUtil.GetGuildDataWithMemberLightInfo(user, guildId)
        .then((result) => {
          if (!result) {
            throw new MError('can-not-find-guild.', MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS, {
              userId: user.userId,
              guildId,
            });
          }

          guildData = result.guildData;
          userLightInfos = result.userLightInfos;
          //
          GuildUtil.ensureMaster(user, guildId, guildData.members);

          if (user.userId === memberUserId) {
            throw new MError(
              'cannot-change-grade-to-self',
              MErrorCode.GUILD_FAILED_SELF_GRADE_CHANGE,
              {
                userId: user.userId,
                guildId,
              }
            );
          }
          const member = guildData.members[memberUserId];
          if (!member) {
            throw new MError('not-exists-guild-member-in-redis', MErrorCode.GUILD_NOT_MEMBER, {
              userId,
              guildId,
              memberUserId,
            });
          }

          memberNid = userLightInfos[memberUserId].pubId;
          memberNationCmsId = userLightInfos[memberUserId].nationCmsId;
          memberOldGrade = member.grade;

          if (isPromoted) {
            if (member.grade <= GUILD_MEMBER_GRADE.HIGHEST) {
              throw new MError(
                'the-guild-member-can-not-promote-anymore',
                MErrorCode.GUILD_MEMBER_CANNOT_PROMOTE,
                {
                  userId,
                  guildId,
                  memberUserId,
                }
              );
            }
            member.grade--;
          } else {
            if (member.grade >= GUILD_MEMBER_GRADE.LOWEST) {
              throw new MError(
                'the-guild-member-can-not-demote-anymore',
                MErrorCode.GUILD_MEMBER_CANNOT_DEMOTE,
                {
                  userId,
                  guildId,
                  memberUserId,
                }
              );
            }
            member.grade++;
          }

          updatedGrade = member.grade;
          return guildRedis['updateGuildMember'](guildId, memberUserId, JSON.stringify(member));
        })
        //======================================================================================================
        // 나머지 기존 길드원에게 알려준다.
        //======================================================================================================
        .then(() => {
          sync = {
            add: {
              userGuild: {
                guild: {
                  members: {
                    [memberUserId]: {
                      grade: updatedGrade,
                    },
                  },
                },
              },
            },
          };

          onGuildPublish(guildData, userLightInfos, [user.userId], sync);
        })

        //======================================================================================================
        // 응답
        //======================================================================================================
        .then(() => {
          gLog_guild_member_change(
            user,
            memberNid,
            memberNationCmsId,
            isPromoted ? 0 : 1,
            memberOldGrade,
            updatedGrade,
            guildId,
            guildData,
            userLightInfos
          );
          user.sendJsonPacket(packet.seqNum, packet.type, { sync });
        })
    );
  }
}

function gLog_guild_member_change(
  user: User,
  member_nid: string,
  member_nation: number,
  type: number,
  old_grade: number,
  cur_grade: number,
  guildId: number,
  guildData: GuildData,
  userLightInfos: { [userId: number]: UserLightInfo }
) {
  const guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);

  let change_nation: string = null;
  if (member_nation) {
    const nationCms = cms.Nation[member_nation];
    change_nation = nationCms ? nationCms.name : null;
  }

  user.glog('guild_member_change', {
    rsn,
    add_rsn,
    change_nid: member_nid,
    change_nation,
    type,
    old_grade,
    cur_grade,
    guild_data,
  });

  if (mconf.isSDO) {
    SdoGLogEvents.uwo_guild_member_glog({
      mid: user.accountId,
      character_id: user.pubId,
      character_name: user.userName,
      character_level: user.level,
      power: user.companyStat.getFleetStat(FirstFleetIndex).getCombatPower(),
      channel_id: user.channel,
      sub_channel_id: user.subChannel,
      platform: user.platform,
      device_id: user.udid,
      ip: user.countryIp,
      port: 0,
      guild_id: guildId.toString(),
      guild_level: guild_data.lv,
      member_num: guild_data.members,
      change_reason: rsn,
      is_positive: 1,
      guild_name: guildData.guild.guildName,
    });
  }
}
