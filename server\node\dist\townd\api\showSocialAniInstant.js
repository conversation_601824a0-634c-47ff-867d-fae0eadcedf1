"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
const merror_1 = require("../../motiflib/merror");
const libTown = __importStar(require("../libTown"));
module.exports = async (req, res) => {
    const { userId, socialAniCmsId } = req.body;
    let townUser = libTown.getTownUser(userId);
    if (!townUser) {
        throw new merror_1.MError('/town/showSocialAniInstant user not found', merror_1.MErrorCode.TOWN_USER_NOT_FOUND);
    }
    return libTown.getTownZone(townUser.curTownCmsId, townUser.curTownChannelId).then((townZone) => {
        libTown.showSocialAniInstant(townZone, townUser, socialAniCmsId);
        res.end();
    });
};
//# sourceMappingURL=showSocialAniInstant.js.map