"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stop = exports.start = exports.stopServer = exports.tcpServer = exports.TownService = void 0;
const body_parser_1 = __importDefault(require("body-parser"));
const express_1 = __importDefault(require("express"));
require("express-async-errors");
const http_1 = __importDefault(require("http"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const typedi_1 = require("typedi");
const cms = __importStar(require("../cms"));
const dirAsApi = __importStar(require("../motiflib/directoryAsApi"));
const expressError = __importStar(require("../motiflib/expressError"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const slackNotifier_1 = require("../motiflib/slackNotifier");
const pubsub_1 = __importDefault(require("../redislib/pubsub"));
const townPubsub = __importStar(require("./townPubsub"));
const cmsEx = __importStar(require("../cms/ex"));
const townPerfmonManager_1 = require("./townPerfmonManager");
const packetPerfmon_1 = require("../motiflib/packetPerfmon");
const stoppable_1 = __importDefault(require("stoppable"));
const tcp = __importStar(require("../tcplib"));
const libTown = __importStar(require("./libTown"));
const Sentry = __importStar(require("@sentry/node"));
// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('uncaught Exception', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('unhandled Rejection', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------
let TownService = class TownService {
    constructor() { }
    async init() {
        this.worldPubsub = typedi_1.Container.of('pubsub-world').get(pubsub_1.default);
        this.worldPubsub.init(mconf_1.default.getWorldConfig().worldPubsubRedis);
        townPubsub.init(this.worldPubsub);
    }
    async destroy() {
        await this.worldPubsub.quit();
        this._stopPing();
    }
    startPing() {
        if (stopping) {
            return;
        }
        mlog_1.default.info('startPing', { pingInterval });
        this._pingInterval = setInterval(() => {
            const curTimeUtc = mutil.curTimeUtc();
            this._updateServerdPing(mconf_1.default.apiService.url, curTimeUtc);
        }, pingInterval);
    }
    _stopPing() {
        if (this._pingInterval) {
            clearInterval(this._pingInterval);
        }
    }
    async _updateServerdPing(appServiceUrl, curDate) {
        try {
            if (stopping) {
                return;
            }
            const resp = await mhttp_1.default.zonelbd.updateServerdPing({
                appServiceUrl,
                curDate,
                zoneType: cmsEx.ZoneType.TOWN,
            });
            if (resp.bStop) {
                Sentry.captureMessage('updateServerdPing signaled to stop. begin stopping townd server');
                mlog_1.default.warn('updateServerdPing signaled to stop. begin stopping server');
                stop();
            }
            else {
                // mlog.info('updateServerdPing succeeded.');
            }
        }
        catch (err) {
            mlog_1.default.warn(err.message);
        }
    }
};
TownService = __decorate([
    (0, typedi_1.Service)(),
    __metadata("design:paramtypes", [])
], TownService);
exports.TownService = TownService;
// Main townd app.
const townApp = (0, express_1.default)();
townApp.disable('x-powered-by');
townApp.disable('etag');
townApp.disable('content-type');
const townServer = (0, stoppable_1.default)(http_1.default.createServer(townApp));
townServer.keepAliveTimeout = 0;
exports.tcpServer = tcp.server();
exports.tcpServer.routeEx(__dirname, './serverPacketHandler');
tcp.logger.setMoudle(mlog_1.default); // Setup tcp log writer
let stopping = false;
let pingInterval = 2000;
// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------
morgan_1.default.token('mcode', (_req, res) => res.mcode || 0);
function townReqLog(tokens, req, res) {
    const netPerfmon = typedi_1.Container.get(packetPerfmon_1.PacketPerfmon);
    const unitPacketStat = {
        packetId: 0,
        packetIdStr: req.url,
        size: parseInt(tokens['res'](req, res, 'content-length'), 10),
        duration: parseFloat(tokens['response-time'](req, res)),
    };
    netPerfmon.addApiRecvStat(packetPerfmon_1.PacketRecvType.SERVER_API_RECEIVED, unitPacketStat);
    if (req.url === '/move' ||
        req.url === '/enterBuilding' ||
        req.url === '/leaveBuilding' ||
        req.url === '/health') {
        return;
    }
    mlog_1.default.info('townd-req', {
        url: tokens['url'](req, res),
        status: tokens['status'](req, res),
        'response-time': tokens['response-time'](req, res),
        mcode: tokens['mcode'](req, res),
    });
    return null;
}
async function closeServer() {
    return new Promise((resolve, reject) => {
        townServer.stop((err) => {
            if (err)
                return reject(err);
            resolve(null);
        });
    });
}
async function stopServer() {
    try {
        mlog_1.default.info('stopping server ...');
        exports.tcpServer.dispose();
        await unregisterServerd(mconf_1.default.apiService.url);
        // town service
        const townService = typedi_1.Container.get(TownService);
        await townService.destroy();
        const townPerfmonManager = typedi_1.Container.get(townPerfmonManager_1.TownPerfmonManager);
        townPerfmonManager.stopPerfmonTick();
        await closeServer();
        await mutil.sleep(1001); // timeout of 1000ms exceeded
        mlog_1.default.info('server stopped');
        process.exitCode = 0;
    }
    catch (error) {
        mlog_1.default.error('graceful shutdown failed', { error: error.message });
        process.exit(1);
    }
}
exports.stopServer = stopServer;
async function unregisterServerd(appServiceUrl) {
    try {
        await mhttp_1.default.zonelbd.unregisterServerd({ appServiceUrl, zoneType: cmsEx.ZoneType.TOWN });
    }
    catch (err) {
        mlog_1.default.warn('Failed to unregister.', { err: err.message });
        if (stopping) {
            return;
        }
        await mutil.sleep(1001); // timeout of 1000ms exceeded
        await unregisterServerd(appServiceUrl);
    }
}
async function registerServerd(appServiceUrl, curDate) {
    try {
        if (stopping) {
            return;
        }
        const resp = await mhttp_1.default.zonelbd.registerServerd({
            appServiceUrl,
            curDate,
            zoneType: cmsEx.ZoneType.TOWN,
        });
        pingInterval = resp.pingInterval;
        mlog_1.default.info('registerServerd succeeded', { pingInterval, curDate });
    }
    catch (err) {
        mlog_1.default.warn('Failed to register.', { err: err.message });
        if (stopping) {
            return;
        }
        await mutil.sleep(1001); // timeout of 1000ms exceeded
        await registerServerd(appServiceUrl, mutil.curTimeUtc());
    }
}
// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
async function start() {
    try {
        await mhttp_1.default.configd.registerInstance(process.env.WORLD_ID ? process.env.WORLD_ID : mconf_1.default.instance.worldId, mconf_1.default.appInstanceId, mconf_1.default.hostname);
        mutil.initSentry();
        // Init http clients.
        mhttp_1.default.init();
        cms.load();
        // town service
        const townService = typedi_1.Container.get(TownService);
        await townService.init();
        const bindAddress = mconf_1.default.apiService.bindAddress;
        const port = mconf_1.default.apiService.port;
        townApp.use((0, morgan_1.default)(townReqLog));
        townApp.use(body_parser_1.default.json());
        mutil.registerHealthCheck(townApp);
        mutil.registerGarbageCollector(townApp);
        await dirAsApi.register(townApp, path_1.default.join(__dirname, 'api'));
        townApp.use(expressError.middleware);
        const onDisconnected = (segment) => {
            const disconnectedServerURL = segment.get('url');
            mlog_1.default.info('disconnected from lobbyd:', { url: disconnectedServerURL });
            // 접속종료된 로비서버와 연결된 유저를 제거한다.
            const townUsers = libTown.getTownUserAll();
            townUsers.forEach((townUser) => {
                if (disconnectedServerURL === townUser.lobbyUrl) {
                    mlog_1.default.warn('disconnect lobbyd user', {
                        userId: townUser.userId,
                    });
                    const townZone = townUser.getCurrentZone();
                    if (townZone) {
                        libTown.leaveTownZone(townZone, townUser);
                    }
                }
            });
        };
        const tcpconf = mconf_1.default.apiService.tcpServer;
        exports.tcpServer.start(tcpconf.port, tcpconf.ip, onDisconnected);
        const townPerfmonManager = typedi_1.Container.get(townPerfmonManager_1.TownPerfmonManager);
        townPerfmonManager.startPerfmonTick();
        townServer.listen(port, bindAddress, () => {
            mlog_1.default.info('start listening ...', { bindAddress, port });
        });
        // config final sync
        const beforeVer = mconf_1.default.layoutVersion;
        await mhttp_1.default.configd.sync(beforeVer, isStopping, stop).then(() => {
            if (beforeVer < mconf_1.default.layoutVersion) {
                // do something
            }
        });
        await unregisterServerd(mconf_1.default.apiService.url);
        await registerServerd(mconf_1.default.apiService.url, mutil.curTimeUtc());
        townService.startPing();
        // start ping tick
    }
    catch (error) {
        mlog_1.default.error('failed to start', { error: error.message, extra: error.extra });
        mlog_1.default.error(error.stack);
        const slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
        await slackNotifier.notify({ username: process.name, text: error.message });
        process.exit(1);
    }
}
exports.start = start;
function isStopping() {
    return stopping;
}
async function stop() {
    if (stopping) {
        return;
    }
    stopping = true;
    await stopServer();
    process.exit(0);
}
exports.stop = stop;
//# sourceMappingURL=server.js.map