"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const minimist_1 = __importDefault(require("minimist"));
const mysql = __importStar(require("promise-mysql"));
const util_1 = require("util");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const mkdirp_1 = __importDefault(require("mkdirp"));
async function extract(connectionOption, databaseName, outputDirectory) {
    console.log(`Connecting to '${connectionOption.host}:${connectionOption.port}'`);
    const dbConn = await mysql.createConnection(connectionOption);
    console.log(`Querying procedures for '${databaseName}' database`);
    const rows = await dbConn.query(`
      SELECT ROUTINE_NAME
        FROM INFORMATION_SCHEMA.ROUTINES
       WHERE ROUTINE_SCHEMA = '${databaseName}'
         AND ROUTINE_TYPE = 'PROCEDURE';
    `);
    console.log(`Making output directory '${outputDirectory}'`);
    await (0, util_1.promisify)(mkdirp_1.default)(outputDirectory);
    for (const row of rows) {
        const procedureName = row.ROUTINE_NAME;
        console.log(`Querying create procedure '${databaseName}.${procedureName}'`);
        const createProcedureRows = await dbConn.query(`
        SHOW CREATE PROCEDURE ${databaseName}.${procedureName};
      `);
        const createProcedureRow = createProcedureRows[0];
        console.log(`Writing procedure file '${outputDirectory}/${procedureName}.sql'`);
        await (0, util_1.promisify)(fs_1.default.writeFile)(`${outputDirectory}/${procedureName}.sql`, createProcedureRow['Create Procedure']);
    }
    await dbConn.end();
}
function main() {
    const argv = (0, minimist_1.default)(process.argv.slice(2));
    const configPath = argv.config || path_1.default.join('config', 'default.json');
    console.log(`Loading config from '${configPath}'`);
    // --config 로 설정 파일 지정
    const config = JSON.parse(fs_1.default.readFileSync(configPath, { encoding: 'utf-8' }));
    // --env 로 설정 파일 내의 환경 지정
    const environment = argv.env || process.env.NODE_ENV || 'development';
    const connectionOption = config[environment];
    const databaseName = connectionOption.database;
    const outputDirectory = argv.output || 'procedures';
    return extract(connectionOption, databaseName, outputDirectory);
}
main()
    .then(() => {
    console.log('Finished');
    process.exit(0);
})
    .catch((err) => {
    console.error(err.message);
    process.exit(-1);
});
//# sourceMappingURL=index.js.map