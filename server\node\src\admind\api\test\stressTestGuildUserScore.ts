// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import mlog from '../../../motiflib/mlog';
import { AdminService } from '../../server';
import { CommonResponseBody } from '../../adminCommon';
import * as mutil from '../../../motiflib/mutil';
import * as formula from '../../../formula/index';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as cmsEx from '../../../cms/ex';

/**
 * @api {post} /test/stressTestGuildUserScore
 * @apiName stressTestGuildUserScore
 * @apiGroup Test
 * @apiDescription
 * Stress test for guild user score calculation.
 * 내부 테스트용으로만 사용되는 API로, 외부에서 사용하지 않도록 한다.
 * master branch에서만 사용되며, qa branch에는 적용하지 않는다.
 * 호출시 대상 월드의 townRedis 에 있는 모든 데이터가 삭제된다.
 *
 * curl -X POST http://localhost:11000/test/stressTestGuildUserScore
 */

interface RequestBody {
  passCode: string;
  passCode2: string;
}

interface ResponseBody extends CommonResponseBody {}

export = async (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /test/stressTestGuildUserScore', { body: req.body });

  let { passCode, passCode2 }: RequestBody = req.body;

  if (passCode !== 'motiftlwms' || passCode2 !== 'gksmddlqhdngktk') {
    throw new MError('invalid passCodes', MErrorCode.ADMIN_INVALID_PARAMETER, {
      passCode,
      passCode2,
    });
  }

  const { townRedises } = Container.get(AdminService);

  const pointToInvestWithItem = 123456;

  const worldId = 'UWO-GL-01';
  const guildId = 100;
  const townCmsId = 11000003;
  const townRedis = townRedises[worldId];
  const developmentName: string = 'test';
  const investNationDucat = 0;
  const userNationCmsId = 10000001;
  const curTimeUtc = 0;
  const defaultDevelopment = 1;
  const maxDevelopmentLevel = 10;
  const addedDevelopment = 0;
  let curSessionId = 1;
  const timeScore = 10;
  const scoreToAdd = Math.floor(pointToInvestWithItem / cms.Const.InvestCompanyPointPer.value);
  const guildScoreToAdd = guildId
    ? Math.floor(pointToInvestWithItem / cms.Const.InvestGuildPointPer.value)
    : 0;

  const guildUserIds = [1, 2]; // 길드 유저들

  const constCms = cms.Const;

  TownInvestmentUtil.buildDefault();

  const { defaultDevelopments, maxDevelopmentLevels, defaultNations, defaultNationSharePoints } =
    TownInvestmentUtil.param;

  // 상회원인 A, B 두 유저가 계속 투자를 하고 있다고 가정한다.
  // 각 루프 중간에 deleteTownGuildUserScore 에 대한 주사위를 굴려서 성공하면 중단한다

  // 항상 테스트 전에 flushdb를 해준다.
  await townRedis['flushdb']();
  mlog.info(`************ ${worldId}: townRedis flushed ************`);

  const MAX_INVEST_LOOP = 100;
  const MAX_SHARE_LOOP = 10;
  const MAX_CLOSE_LOOP = 5;

  const investCompanyPointMinusPer = [cms.Const.InvestCompanyPointMinusPer1.value];
  for (let i = 1; i <= 5; i++) {
    investCompanyPointMinusPer.push(cms.Const[`InvestCompanyPointMinusPer${i}`].value);
  }

  const totalLoopCount = MAX_INVEST_LOOP * MAX_SHARE_LOOP * MAX_CLOSE_LOOP;

  for (let closeLoop = 0; closeLoop < MAX_CLOSE_LOOP; closeLoop++) {
    for (let shareLoop = 0; shareLoop < MAX_SHARE_LOOP; shareLoop++) {
      for (let investLoop = 0; investLoop < MAX_INVEST_LOOP; investLoop++) {
        // goverInvest * N
        for (const userId of guildUserIds) {
          const ret = await townRedis['goverInvest'](
            userId, // 1
            townCmsId,
            developmentName,
            pointToInvestWithItem,
            userNationCmsId, // 5
            curTimeUtc,
            defaultDevelopment,
            JSON.stringify(cms.TownDevelopExp),
            maxDevelopmentLevel,
            cms.Point[cmsEx.DucatPointCmsId].hardCap, // 10
            addedDevelopment,
            curSessionId,
            cms.Define.InvestmentSocreMultiplier,
            timeScore,
            cms.Define.MaxInvestmentScore, // 15
            scoreToAdd,
            false, //bUnOccupiableTown
            false, // bIsMayerRemoteInvest
            guildId,
            guildScoreToAdd, // 20
            investNationDucat,
            0 // createTimeUtc
          );

          const myInvestmentAccumPoint = parseInt(ret[0], 10);
          const myInvestmentScore = parseInt(ret[1], 10);
          const myInvestmentRank = parseInt(ret[2], 10) + 1;
          const myGuildInvestmentScore = parseInt(ret[3], 10);
          const myGuildInvestmentRank = parseInt(ret[4], 10) + 1;
          const myAllGuildInvestmentScore = parseInt(ret[5], 10);
          const myAllGuildInvestmentRank = parseInt(ret[6], 10) + 1;

          const retGuildUserScoresStr = await townRedis['loadGuildUserScore'](townCmsId, guildId);

          const retGuildInvestStr = await townRedis['loadInvestmentDevelopmentInvestScore'](
            townCmsId,
            cms.Const.InvestAccumPointMaxDisplayCount.value,
            curSessionId
          );

          const guildInvestments = getTownGuildInvestmentsSyncData(retGuildInvestStr[3]); // 길드 투자 점수

          mlog.verbose('invest result', {
            userId,
            myInvestmentAccumPoint,
            myInvestmentScore,
            myInvestmentRank,
            myGuildInvestmentScore,
            myGuildInvestmentRank,
            myAllGuildInvestmentScore,
            myAllGuildInvestmentRank,
            guildUserScores: JSON.parse(retGuildUserScoresStr),
            guildInvestments,
          });
        }

        if (shareLoop > 1) {
          const deletedUserId = randomDeleteOneUser(guildUserIds, 1); // 길드 유저들 중 랜덤으로 한명을 삭제한다
          if (0 < deletedUserId) {
            mlog.info(`********** deleted userId: ${deletedUserId} **********`);

            await townRedis['deleteTownGuildUserScore'](
              JSON.stringify([townCmsId]),
              guildId,
              deletedUserId,
              cms.Const.InvestGuildPointPer.value,
              cms.Const.InvestGuildSharePointPer.value
            );

            await dumpCurrentInvestmentData(townCmsId, guildId, worldId, curSessionId);
          }
        }
      } // end of investLoop

      const bNaturalDecreaseSp = shareLoop === MAX_SHARE_LOOP ? true : false;

      // applyTownInvestToNationSharePoint
      const retApplySharePointStr = await townRedis['applyTownInvestToNationSharePoint'](
        JSON.stringify([townCmsId]),
        // 타국이 투자 할수 없는 townCmsIds
        JSON.stringify({}),
        // 항구 기본 투자 레벨
        JSON.stringify(defaultDevelopments),
        // 항구 최대 투자 레벨
        JSON.stringify(maxDevelopmentLevels),
        // 항구 기본 국가
        JSON.stringify(defaultNations), // 5
        // 항구 국가 기본 점유율
        JSON.stringify(defaultNationSharePoints),

        JSON.stringify(cms.TownDevelopExp),
        constCms.InvestDevelopExpMinusPer.value,
        constCms.InvestNationSharePointPer.value,
        constCms.DefaultInvestMinusSharePoint.value, // 10
        constCms.InvestMinusPer.value,
        constCms.InvestMinusPerLimit.value,

        curSessionId,
        cms.Define.InvestmentSocreMultiplier,
        bNaturalDecreaseSp, // 15

        constCms.InvestMinusSharePointMaxPer.value,
        constCms.InvestNpcIncreaseSharePointMaxPer.value,

        cms.Const.InvestGuildSharePointPer.value
      );

      const newNationSharePoints = JSON.parse(retApplySharePointStr[0]);
      const newDevelopmentLevels = JSON.parse(retApplySharePointStr[1]);
      const userScore = JSON.parse(retApplySharePointStr[2]);

      const retGuildSharePointsStr = await townRedis['loadDevelopSharePoint'](townCmsId);
      const guildSharePoints = getTownGuildSharePointsSyncData(
        retGuildSharePointsStr[3],
        retGuildSharePointsStr[4],
        townCmsId
      );

      mlog.verbose('applySharePoint result', {
        newNationSharePoints,
        newDevelopmentLevels,
        userScore,
        guildSharePoints,
      });
    } // end of shareLoop

    const retCloseInvestmentSessionStr = await townRedis['closeInvestmentSession'](
      townCmsId,
      curSessionId,
      JSON.stringify(investCompanyPointMinusPer),
      cms.Define.InvestmentSocreMultiplier,
      cms.Const.InvestGuildPointMinusPer.value
    );

    const bHandled = parseInt(retCloseInvestmentSessionStr[0]);
    mlog.verbose('closeInvestmentSession result', { bHandled });

    curSessionId++;
  } // end of closeLoop

  // ----------------------------------------
  // 결과 검증
  await dumpCurrentInvestmentData(townCmsId, guildId, worldId, curSessionId);

  mlog.info('[TX] /test/stressTestGuildUserScore', {
    curSessionId,
    totalLoopCount,
    townCmsId,
    guildId,
  });

  const resp: ResponseBody = {
    isSuccess: true,
  };
  res.json(resp);
};

// -----------------------------------------------------------------------------------------------
// -----------------------------------------------------------------------------------------------
// -----------------------------------------------------------------------------------------------
interface InvestmentParam {
  townCmsIdsStr: string;
  developmentLevelNamesStr: string;
  regionIdsStr: string;
  townCmsIds: number[];
  unoccupiableTownCmsIds: number[];
  defaultDevelopments: { [townCmsId: number]: { [type: string]: number } };
  maxDevelopmentLevels: { [townCmsId: number]: number };
  bIsUnInvestableTownByForeigner: { [townCmsId: number]: boolean };
  defaultNations: { [townCmsId: number]: number };
  defaultNationSharePoints: { [townCmsId: number]: number };
}
namespace TownInvestmentUtil {
  export const param: InvestmentParam = {
    townCmsIdsStr: null,
    developmentLevelNamesStr: null,
    regionIdsStr: null,
    townCmsIds: [],
    unoccupiableTownCmsIds: [],
    defaultDevelopments: {},
    maxDevelopmentLevels: {},
    bIsUnInvestableTownByForeigner: {},
    defaultNations: {},
    defaultNationSharePoints: {},
  };

  // -----------------------------------------------------------------------------------------------
  export function buildDefault() {
    const regionIds = new Set<number>();

    _.forOwn(cms.Town, (elem) => {
      if (elem.ownType === cmsEx.TOWN_OWN_TYPE.UNOCCUPIABLE) {
        param.unoccupiableTownCmsIds.push(elem.id);
      } else {
        param.townCmsIds.push(elem.id);
        if (elem.RegionId) {
          regionIds.add(elem.RegionId);
        }
      }
    });
    param.townCmsIdsStr = JSON.stringify(param.townCmsIds);
    param.regionIdsStr = JSON.stringify(Array.from(regionIds));

    for (const key of Object.keys(cms.Town)) {
      const townCms = cms.Town[key];
      const townCmsIdStr = key;

      // Build defaultDevelopments
      const defaultDevelopment: { [type: string]: number } = {};
      const expCms = cms.TownDevelopExp;
      const maxDevelopmentLevel = cms.Const['MaxDevelopLvOfTownSize' + townCms.townSize].value;
      if (townCms.industryExp !== 0) {
        defaultDevelopment['industry'] = townCms.industryExp;
        for (let i = 1; i <= maxDevelopmentLevel; i++) {
          if (townCms.industryExp < expCms[i].accumulateExp) {
            defaultDevelopment['industryLevel'] = i;
            break;
          }
        }
        if (defaultDevelopment['industryLevel'] === undefined) {
          defaultDevelopment['industryLevel'] = maxDevelopmentLevel;
        }
      }
      if (townCms.commerceExp !== 0) {
        defaultDevelopment['commerce'] = townCms.commerceExp;
        for (let i = 1; i <= maxDevelopmentLevel; i++) {
          if (townCms.commerceExp < expCms[i].accumulateExp) {
            defaultDevelopment['commerceLevel'] = i;
            break;
          }
        }
        if (defaultDevelopment['commerceLevel'] === undefined) {
          defaultDevelopment['commerceLevel'] = maxDevelopmentLevel;
        }
      }
      if (townCms.armoryExp !== 0) {
        defaultDevelopment['armory'] = townCms.armoryExp;
        for (let i = 1; i <= maxDevelopmentLevel; i++) {
          if (townCms.armoryExp < expCms[i].accumulateExp) {
            defaultDevelopment['armoryLevel'] = i;
            break;
          }
        }
        if (defaultDevelopment['armoryLevel'] === undefined) {
          defaultDevelopment['armoryLevel'] = maxDevelopmentLevel;
        }
      }
      param.defaultDevelopments[townCmsIdStr] = defaultDevelopment;
      param.maxDevelopmentLevels[townCmsIdStr] = maxDevelopmentLevel;

      param.bIsUnInvestableTownByForeigner[townCmsIdStr] = false;
      param.defaultNations[townCmsIdStr] = townCms.nationId;
      param.defaultNationSharePoints[townCmsIdStr] = townCms.sharePoint;
    }

    const developmentLevelNames: string[] = [];
    for (let i = cmsEx.DEVELOPMENT_TYPE.none + 1; i < cmsEx.DEVELOPMENT_TYPE.max; i++) {
      developmentLevelNames.push(cmsEx.DEVELOPMENT_TYPE[i] + 'Level');
    }
    param.developmentLevelNamesStr = JSON.stringify(developmentLevelNames);
  }
}

interface TownGuildSharePoint {
  townCmsId?: number;
  guildId?: number;
  value?: number;
  preInvestment?: number;
}

function getTownGuildSharePointsSyncData(
  guildSharePointstStr: string,
  preTownGuildInvestmentsStr: string,
  townCmsId: number
): TownGuildSharePoint[] {
  const guildSharePoints = JSON.parse(guildSharePointstStr);
  const preTownGuildInvestments = JSON.parse(preTownGuildInvestmentsStr);
  const ret: TownGuildSharePoint[] = [];
  for (const key of Object.keys(guildSharePoints)) {
    const guildId = parseInt(guildSharePoints[key].guildId, 10);
    const value = parseInt(guildSharePoints[key].value, 10);
    let preInvestment = 0;
    if (preTownGuildInvestments[guildId]) {
      preInvestment = parseInt(preTownGuildInvestments[guildId], 10);
    }
    ret.push({
      townCmsId,
      guildId,
      value,
      preInvestment,
    });
  }

  return ret;
}

function getTownGuildInvestmentsSyncData(redisResultStr: string): {
  [guildId: number]: number;
} {
  const redisResult = JSON.parse(redisResultStr);
  const ret: { [guild: number]: number } = {};
  for (const key of Object.keys(redisResult)) {
    const guildId = parseInt(key, 10);
    ret[guildId] = parseInt(redisResult[key], 10);
  }

  return ret;
}

// 인자로 전달되는 guildUserIds:number[] 중 랜덤으로 한명을 삭제한다
function _deleteRandomUserId(guildUserIds: number[]): number {
  const randomIndex = Math.floor(Math.random() * guildUserIds.length);
  const userId = guildUserIds[randomIndex];
  guildUserIds.splice(randomIndex, 1);
  return userId;
}

// 유저를 삭제할지 여부의 100분률 랜덤을 결정한다
function _determineDeleteUser(prob100: number): boolean {
  const randomNum = Math.floor(Math.random() * 100);
  return randomNum < prob100;
}

// 가끔 유저를 삭제할지 여부의 100분률 랜덤을 결정한다
function randomDeleteOneUser(guildUserIds: number[], prob100: number): number {
  if (guildUserIds.length === 0) {
    return 0;
  }

  if (_determineDeleteUser(prob100)) {
    return _deleteRandomUserId(guildUserIds);
  }
  return 0;
}

async function dumpCurrentInvestmentData(
  townCmsId: number,
  guildId: number,
  worldId: string,
  curSessionId: number
) {
  const { townRedises } = Container.get(AdminService);
  const townRedis = townRedises[worldId];
  const retGuildUserScoresStr = await townRedis['loadGuildUserScore'](townCmsId, guildId);
  const guildUserScores: { userdId: number; score: number }[] = JSON.parse(retGuildUserScoresStr);

  // guildUserScores 를 순회하면서 score의 합을 구한다.
  const totalGuildUserScore = _.reduce(
    guildUserScores,
    (sum, elem) => {
      return sum + elem.score;
    },
    0
  );

  const expectedDocat = Math.floor(totalGuildUserScore * cms.Const.InvestGuildPointPer.value);
  const expectedGuildSharePoints = Math.ceil(
    expectedDocat / cms.Const.InvestGuildSharePointPer.value
  );

  const retGuildSharePointsStr = await townRedis['loadDevelopSharePoint'](townCmsId);
  const guildSharePoints = getTownGuildSharePointsSyncData(
    retGuildSharePointsStr[3],
    retGuildSharePointsStr[4],
    townCmsId
  );

  mlog.info('dumpCurrentInvestmentData', {
    curSessionId,
    guildId,
    guildSharePoints,
    guildUserScores: JSON.parse(retGuildUserScoresStr),
    totalGuildUserScore,
    expectedDocat,
    expectedGuildSharePoints,
  });
}
