// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import tuChangeUserPoint from '../../../mysqllib/txn/tuChangeUserPoint';
import { LobbyService } from '../../server';
import { Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Dev } from '../../../proto/lobby/proto';
import { curTimeUtc } from '../../../motiflib/mutil';
import { EnergyChange } from '../../userEnergy';
import puUserUpdateEnergy from '../../../mysqllib/sp/puUserUpdateEnergy';
import { ClientPacketHandler } from '../index';
import { isCash } from '../../../cms/pointDesc';
import tuUpdateMileage from '../../../mysqllib/txn/tuUpdateMileage';
import puUserUpdateManufacturePoint from '../../../mysqllib/sp/puUserUpdateManufacturePoint';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'dev_remove_point';
const add_rsn = null;

// ----------------------------------------------------------------------------
export class Cph_Dev_RemovePoint implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body = packet.bodyObj;

    const cmsId = body.cmsId;
    const amount = body.amount;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.ensureCheatAccessLevel(Dev.REMOVE_POINT);

    if (!Number.isInteger(amount) || amount === 0) {
      throw new MError('invalid-amount', MErrorCode.INVALID_REQ_BODY_DEV_REMOVE_POINT, {
        reqBody: body,
      });
    }

    const now = curTimeUtc();
    if (cmsId === cmsEx.EnergyPointCmsId) {
      const energyChange: EnergyChange = user.userEnergy.buildEnergyChangeWithConsume(
        now,
        user.level,
        user.level,
        amount,
        true
      );

      const sync: Sync = {};
      const { userDbConnPoolMgr } = Container.get(LobbyService);
      return puUserUpdateEnergy(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        energyChange.energy,
        energyChange.lastUpdateTimeUtc
      ).then(() => {
        _.merge<Sync, Sync>(sync, user.userEnergy.applyEnergyChange(energyChange, null));
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    } else if (cmsId === cmsEx.ManufacturePointCmsId) {
      const [success, pointChange] = user.userManufacture.buildPointChange(now, -amount);
      if(!success){
        throw new MError('fail-to-build-manufacture-point-change',
          MErrorCode.INVALID_REQ_BODY_DEV_ADD_POINT, {
          reqBody: body,
        });
      }
      
      const sync: Sync = {};
      const { userDbConnPoolMgr } = Container.get(LobbyService);
      return puUserUpdateManufacturePoint(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        pointChange.point,
        pointChange.lastUpdatePointTimeUtc
      ).then(() => {
        _.merge<Sync, Sync>(sync, user.userManufacture.applyPointChange(pointChange, null));
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    } else if (isCash(cmsId)) {
      return _removeCash(cmsId, amount, user, packet);
    } else if (cmsId === cmsEx.CashShopMileage) {
      const mileageChanges = user.userPoints.buildMileageChanges(-amount, now);
      return tuUpdateMileage(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        mileageChanges
      ).then(() => {
        const sync = user.userPoints.applyMileageChanges(mileageChanges, null);
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    } else {
      const pointCms = cms.Point[cmsId.toString()];
      if (!pointCms) {
        throw new MError('no-key-in-point-cms', MErrorCode.NO_KEY_IN_CMS, { cmsId });
      }

      const oldPoint = user.userPoints.getPoint(cmsId);
      const pointValue = oldPoint - amount;
      if (pointValue > pointCms.hardCap) {
        throw new MError('exceeds-hard-cap', MErrorCode.EXCEEDS_HARD_CAP, { cmsId, pointValue });
      }
      if (pointValue < 0) {
        mlog.info('requested to apply negative point', {
          rsn,
          userId: user.userId,
          cmsId,
          oldPoint,
          newPoint: pointValue,
        });
      }

      return tuChangeUserPoint(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        cmsId,
        pointValue
      ).then(() => {
        const sync: Sync = {};
        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyPointChanges([{ cmsId, value: pointValue }], null)
        );

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    }
  }
}

function _removeCash(cmsId: number, amount: number, user: User, packet: CPacket): Promise<any> {
  return Promise.resolve()
    .then(() => {
      if (amount > 0) {
        return user.userPoints.consumeCash(cmsId, amount, { itemId: rsn }, user, null);
      } else {
        return user.userPoints.addCash(cmsId, -amount, rsn, user, null);
      }
    })
    .then(() => {
      const sync: Sync = {
        add: {
          points: {
            [cmsId]: {
              cmsId,
              value: user.userPoints.getPoint(cmsId),
            },
          },
        },
      };

      return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
    });
}
