{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-14T04:21:07.907Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-14T04:21:07.910Z"}
{"level":"info","message":"[Session] socket disposed, B8HW0De5","timestamp":"2025-08-14T04:21:07.910Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-14T04:21:07.911Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-14T04:21:08.031Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-14T04:21:08.032Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-14T04:21:08.812Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-14T04:21:08.812Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"socket hang up","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-14T04:21:09.024Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-14T04:21:09.025Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-14T04:21:09.025Z"}
