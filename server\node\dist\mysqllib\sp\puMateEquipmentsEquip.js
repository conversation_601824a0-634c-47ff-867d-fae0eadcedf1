"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorCode = exports.spName = void 0;
const lodash_1 = __importDefault(require("lodash"));
const query = __importStar(require("../query"));
const merror_1 = require("../../motiflib/merror");
exports.spName = 'mp_u_mate_equipmnet_update_equipped_mate_cms_id_';
exports.errorCode = merror_1.MErrorCode.BATTLE_REWARD_UPDATE_QUERY_ERROR;
const catchHandler = query.generateMErrorRejection(exports.errorCode);
function makePromise(connection, userId, mateCmsId, inEquipIds, count) {
    const ids = [];
    for (let i = 0; i < count; i++) {
        const id = inEquipIds.shift();
        ids.push(id);
    }
    const spFunction = query.generateSPFunction(exports.spName + count);
    return spFunction(connection, userId, mateCmsId, ...ids);
}
function default_1(connection, userId, mateCmsId, inEquipIds) {
    const equipIds = lodash_1.default.cloneDeep(inEquipIds);
    const promises = [];
    while (equipIds.length > 0) {
        if (equipIds.length >= 4) {
            promises.push(makePromise(connection, userId, mateCmsId, equipIds, 4));
        }
        else if (equipIds.length >= 1) {
            promises.push(makePromise(connection, userId, mateCmsId, equipIds, 1));
        }
    }
    return Promise.all(promises)
        .then(() => {
        return;
    })
        .catch(catchHandler);
}
exports.default = default_1;
//# sourceMappingURL=puMateEquipmentsEquip.js.map