// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import mconf from '../mconf';
import { BaseApiClient } from './baseApiClient';
import { Result as taLoadLastLobbyOfOnlineUsersResult } from '../../mysqllib/txn/taLoadLastLobbyOfOnlineUsers';
import { Result as paWorldUserLoadUserIdByNameAndWorldIdResult } from '../../mysqllib/sp/paWorldUserLoadUserIdByNameAndWorldId';

interface GetUserLastWorldIdRequestBody {
  sessionToken: string; // accountId
}

export interface GetUserLastWorldIdResponse {
  lastWorldId: string;
}

export interface EnterWorldResult {
  bValidEnterWorldToken: boolean;
  accountId: string;
  pubId: string; // (lg:nid, editor:pubId)
  countryCreated: string;
  userId: number;
  accessLevel: number;
  revision: string;
  patchRevision: string;
  kickReason?: number;
}

/**
 * rest client
 */
export class AuthApiClient extends BaseApiClient {
  constructor() {
    super();
  }

  getUserLastWorldId(req: GetUserLastWorldIdRequestBody): Promise<GetUserLastWorldIdResponse> {
    return this.post('/getUserLastWorldId', req);
  }

  enterWorld(
    isDevLogin: boolean,
    sessionToken: string,
    worldId: number,
    enterWorldToken: string,
    reconnect: number
  ): Promise<EnterWorldResult> {
    const body = {
      isDevLogin,
      sessionToken,
      worldId,
      enterWorldToken,
      lobbyId: mconf.appId,
      reconnect,
    };
    return this.post('/enterWorld', body);
  }

  logout(accountId: string): Promise<void> {
    const body = { accountId };
    return this.post('/logout', body).then(() => undefined);
  }

  changeUserName(
    userId: number,
    name: string,
    worldId: string,
    preoccupancyCode?: string
  ): Promise<boolean> {
    const body = { userId, name, worldId, preoccupancyCode };
    return this.post('/changeUserName', body).then((ret) => <boolean>ret.bIsDuplicated);
  }

  changeUserNation(userId: number, nationCmsId: number): Promise<void> {
    const body = { userId, nationCmsId };
    return this.post('/changeUserNation', body).then(() => undefined);
  }

  getUserIdByName(
    name: string,
    worldId: string
  ): Promise<paWorldUserLoadUserIdByNameAndWorldIdResult> {
    const body = { name, worldId };
    return this.post('/getUserIdByName', body).then(
      (ret: paWorldUserLoadUserIdByNameAndWorldIdResult) => ret
    );
  }

  getUserIdsByPubId(pubId: string): Promise<number[]> {
    const body = { pubId };
    return this.post('/getUserIdsByPubId', body).then((ret) => <number[]>ret.userIds);
  }

  getLastLobbyOfOnlineUsers(userIds: number[]): Promise<taLoadLastLobbyOfOnlineUsersResult> {
    const body = { userIds };
    return this.post('/getLastLobbyOfOnlineUsers', body).then(
      (ret: taLoadLastLobbyOfOnlineUsersResult) => ret
    );
  }

  deleteAccount(pubId: string): Promise<void> {
    const body = { pubId };
    return this.post('/deleteAccount', body).then(() => undefined);
  }

  changeUserIdForDevLoad(
    userIdToLoad: number,
    myPubId: string,
    userDbLength: number
  ): Promise<number> {
    const body = { userIdToLoad, myPubId, userDbLength };
    return this.post('/changeUserIdForDevLoad', body).then((ret) => <number>ret.userId);
  }

  // 간혈적인 timeout 방지.
  knock(): Promise<void> {
    return this.get('/health')
      .then(() => {
        return;
      })
      .catch(() => {
        // Do nothing.
      });
  }
}
