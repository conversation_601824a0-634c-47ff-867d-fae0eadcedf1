"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const typedi_1 = __importDefault(require("typedi"));
const moment_1 = __importDefault(require("moment"));
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const merror_1 = require("../../../motiflib/merror");
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const enum_1 = require("../../../motiflib/model/auth/enum");
const mutil = __importStar(require("../../../motiflib/mutil"));
const pool_1 = require("../../../mysqllib/pool");
const taGetWorld_1 = __importDefault(require("../../../mysqllib/txn/taGetWorld"));
const gameLog_1 = __importDefault(require("../../../motiflib/gameLog"));
const connPool_1 = require("../../../redislib/connPool");
const worldStateDesc_1 = require("../../../cms/worldStateDesc");
const worldState_1 = require("../../worldState");
const mysqlReqRepCounter_1 = require("../../../mysqllib/mysqlReqRepCounter");
const worldNameMapping_1 = __importDefault(require("../../worldNameMapping"));
module.exports = (req, res) => {
    // [TEMP] production 환경에서 에디터 로그인 가능하게 하는 코드
    // const platform = req.body.platform !== undefined ? req.body.platform : mconf.platform;
    const platform = mconf_1.default.isDev && req.body.platform !== undefined ? req.body.platform : mconf_1.default.platform;
    mlog_1.default.info('[RX] /getWorlds', { body: req.body, platform: enum_1.PLATFORM[platform] });
    const { gnidSessionToken, deviceType, osv, dm, deviceLang, lineLang, v, sk, country_ip, os_modulation, emulator, loading_time, loginPlatform, } = req.body;
    let resp = {
        worlds: undefined,
        lastWorldId: undefined,
    };
    const reqRepCounter = typedi_1.default.get(mysqlReqRepCounter_1.MysqlReqRepCounter);
    if (reqRepCounter.isLimitOver()) {
        resp.errorCd = merror_1.MErrorCode.AUTH_LOGIN_FAILED_WITH_SERVER_BUSY;
        const limit = reqRepCounter.getLimitCount();
        const cnt = reqRepCounter.getCount();
        mlog_1.default.warn(`request-respons-count over`, { cnt, limit });
        res.status(503).json(resp);
        return;
    }
    let accountId; // (lg:gnid, editor:로그인 창에서 입력하는 아이디)
    let registeredGameServerList;
    return Promise.resolve()
        .then(() => {
        if (platform === enum_1.PLATFORM.LINE) {
            return mhttp_1.default.platformApi.getNidAndServerInfoByGnid(gnidSessionToken);
        }
        return null;
    })
        .then((result) => {
        if (platform === enum_1.PLATFORM.LINE) {
            accountId = result.gnid;
            registeredGameServerList = result.registeredGameServerList;
        }
        else if (platform === enum_1.PLATFORM.DEV) {
            accountId = gnidSessionToken;
            const krPattern = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/;
            const blankPattern = /[\s]/g;
            if (krPattern.test(accountId) || blankPattern.test(accountId)) {
                throw new merror_1.MError('invalid-id', merror_1.MErrorCode.AUTH_INVALID_PUB_ID, req.body);
            }
        }
        const dbConnPool = typedi_1.default.get(pool_1.DBConnPool);
        return (0, taGetWorld_1.default)(dbConnPool.getPool(), accountId, mutil.curTimeUtc());
    })
        .then((result) => {
        resp.worlds = result.worlds || [];
        resp.lastWorldId = result.lastWorldId;
        // get current users per world
        const monitorRedis = typedi_1.default.of('monitor-redis').get(connPool_1.MRedisConnPool);
        return monitorRedis['getUserCount']().then((retuserCount) => {
            const userCount = JSON.parse(retuserCount);
            // TODO: 리팩토링 필요함. (라인인 경우 registeredGameServerList 위주로)
            for (const worldInfo of mconf_1.default.worlds) {
                let order = 0;
                if (platform === enum_1.PLATFORM.LINE) {
                    order = registeredGameServerList.findIndex((item) => item.gameServerId === worldInfo.id);
                    if (order === -1) {
                        continue;
                    }
                }
                if (worldInfo.disabled) {
                    continue;
                }
                let elem = resp.worlds.find((elem) => elem.worldId === worldInfo.id);
                if (!elem) {
                    // 만들어진 선단이 없는 월드인 경우
                    elem = {
                        worldId: worldInfo.id,
                        worldName: (0, worldNameMapping_1.default)(worldInfo.id), // world name mapping(NEW FEATURE)
                    };
                    resp.worlds.push(elem);
                }
                else {
                    elem.worldName = (0, worldNameMapping_1.default)(worldInfo.id); // world name mapping(NEW FEATURE)
                }
                elem.order = order;
                elem.address = worldInfo.address;
                elem.port = worldInfo.port;
                elem.bIsNonPK = worldInfo.bIsNonPK;
                // world state
                if (userCount.user.world[elem.worldId]) {
                    const curUsers = userCount.user.world[elem.worldId];
                    const wsId = worldState_1.WorldStateUtil.getWorldState(curUsers, mconf_1.default.maxUsersPerWorld);
                    elem.worldStateCmsId = wsId;
                }
                else {
                    // 아직 유저가 한명도 입장안했거나 월드가 켜져있지 않는 상황
                    // [todo] 실제 월드의 상태 체크필요
                    elem.worldStateCmsId = worldStateDesc_1.WORLD_STATE_TYPE_ID.COMFORTABLE;
                }
            }
            // 라인일 경우 정렬
            if (platform === enum_1.PLATFORM.LINE) {
                resp.worlds.sort((a, b) => a.order - b.order);
            }
            // glog
            (0, gameLog_1.default)('common_gnid_login', {
                _time: (0, moment_1.default)(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                os: deviceType ? deviceType.toUpperCase() : null,
                osv,
                dm,
                lang: deviceLang,
                lang_game: lineLang,
                v,
                sk,
                platform: loginPlatform,
                country_ip,
                os_modulation,
                emulator,
                loading_time,
                gnid: accountId,
            });
            mlog_1.default.info('[TX] /getWorlds', { body: resp });
            res.json(resp);
        });
    })
        .catch((error) => {
        mlog_1.default.error('/getWorlds failed', {
            msg: error.message,
        });
        if (error instanceof merror_1.MError) {
            throw new merror_1.MError(`'/getWorlds' api err`, error.mcode, error.message);
        }
        throw new merror_1.MError(error.message, merror_1.MErrorCode.AUTH_GET_WORLD_ERR, undefined, error.stack);
    });
};
//# sourceMappingURL=getWorlds.js.map