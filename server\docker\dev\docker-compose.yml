version: "3"

services:
  redis:
    image: redis
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    restart: always

  mysql:
    image: mysql:8.0
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/initdb.sql:/docker-entrypoint-initdb.d/initdb.sql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: "dev123$$"
      MYSQL_USER: motif_dev
      MYSQL_PASSWORD: "dev123$$"
    command: mysqld --default-authentication-plugin=mysql_native_password
    restart: always

  rabbitmq:
    image: rabbitmq:3-management
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    restart: always
    # environment:
    #   RABBITMQ_VM_MEMORY_HIGH_WATERMARK: '256MiB'

  mongodb:
    image: mongo:4.2.18
    volumes:
      - mongodb_data:/var/lib/mongodb
    ports:
      - "27017:27017"
    restart: always

  redis_2:
    image: redis
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - redis_data_2:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6380:6379"
    restart: always

  mysql_2:
    image: mysql:8.0
    volumes:
      - mysql_data_2:/var/lib/mysql
      - ./mysql/initdb.sql:/docker-entrypoint-initdb.d/initdb.sql
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: "dev123$$"
      MYSQL_USER: motif_dev
      MYSQL_PASSWORD: "dev123$$"
    restart: always

  rabbitmq_2:
    image: rabbitmq:3-management
    volumes:
      - rabbitmq_data_2:/var/lib/rabbitmq
    ports:
      - "5673:5672"
      - "15673:15672"
    restart: always
    # environment:
    #   RABBITMQ_VM_MEMORY_HIGH_WATERMARK: '256MiB'

  mongodb_2:
    image: mongo:4.2.18
    volumes:
      - mongodb_data_2:/var/lib/mongodb
    ports:
      - "27018:27017"
    restart: always

volumes:
  redis_data:
  mysql_data:
  rabbitmq_data:
  mongodb_data:
  redis_data_2:
  mysql_data_2:
  rabbitmq_data_2:
  mongodb_data_2:
