// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import cms from '../cms';
import { getOceanNpcCms } from '../cms/ex';
import { LoginInfo, User } from './user';
import * as sync from './type/sync';
import {
  GuildPointNub,
  GuildData,
  GuildMemberNub,
  GUILD_POINT_CATEGORY,
  GuildAppearance,
  GuildCraftProgress,
  GuildSynthesisProgress,
} from '../motiflib/model/lobby';
import { UserLightInfo } from '../motiflib/userCacheRedisHelper';
import { curTimeUtc, isNotANumber } from '../motiflib/mutil';
import { Sync } from './type/sync';
import { Container } from 'typedi/Container';
import { LobbyService } from './server';
import mlog from '../motiflib/mlog';
import mhttp from '../motiflib/mhttp';

import { OceanNpcDesc, OCEAN_NPC_TYPE } from '../cms/oceanNpcDesc';
import { GuildLogUtil, GuildUtil } from './guildUtil';
import { RANKING_CMS_ID } from '../cms/rankingDesc';
import { EventRankingUtil } from './userEventRanking';

export namespace GuildPubsub {}

export namespace GuildPointMission {
  function _add(origin: GuildPointNub, category: number, v: number): GuildPointNub {
    if (!origin) {
      origin = {};
    }
    if (!origin[category]) {
      origin[category] = 0;
    }
    origin[category] += v;
    return origin;
  }

  export function save(
    user: User,
    guildData: GuildData,
    userLightInfos: { [userId: number]: UserLightInfo },
    category: GUILD_POINT_CATEGORY,
    addGP: number
  ): Promise<Sync> {
    const { guildRedis, rankingManager } = Container.get(LobbyService);

    const guildId: number = user.userGuild.guildId;

    const dgp: GuildPointNub = _add(guildData.dailyGuildPoints[user.userId], category, addGP);
    const wgp: GuildPointNub = _add(guildData.weeklyGuildPoints[user.userId], category, addGP);
    const agp: GuildPointNub = _add(guildData.accumGuildPoints[user.userId], category, addGP);

    guildData.dailyGuildPoints[user.userId] = dgp;
    guildData.weeklyGuildPoints[user.userId] = wgp;
    guildData.accumGuildPoints[user.userId] = agp;

    const guildMember: GuildMemberNub = guildData.members[user.userId];
    guildMember.guildPointUpdateTimeUtc = curTimeUtc();

    const sync: Sync = {};

    // 라인게임즈 로그용
    let nation: string = null;
    let hardCap = 0;
    let rsn;
    const nationCms = cms.Nation[user.nationCmsId];
    nation = nationCms ? nationCms.name : null;
    const grade = guildData.members[user.userId].grade;

    if (category === GUILD_POINT_CATEGORY.ATTENDANCE) {
      hardCap = cms.Const.GuildAttendancePerPoint.value;
      rsn = 'exp_by_attendance';
    } else if (category === GUILD_POINT_CATEGORY.BATTLE) {
      hardCap = cms.Const.GuildPointBattleMaxLimit.value;
      rsn = 'exp_by_battle';
    } else if (category === GUILD_POINT_CATEGORY.TRADE) {
      hardCap = cms.Const.GuildTradeMaxLimit.value;
      rsn = 'exp_by_trade';
    } else if (category === GUILD_POINT_CATEGORY.EXPLORE) {
      hardCap = cms.Const.GuildPointExploreMaxLimit.value;
      rsn = 'exp_by_explore';
    } else if (category === GUILD_POINT_CATEGORY.PLUNDER) {
      hardCap = cms.Const.GuildPointPVPMaxLimit.value;
      rsn = 'exp_by_plunder';
    } else if (category === GUILD_POINT_CATEGORY.CRAFT) {
      hardCap = cms.Const.GuildPointCraftMaxLimit.value;
      rsn = 'exp_by_craft';
    } else if (category === GUILD_POINT_CATEGORY.INVEST) {
      hardCap = cms.Const.GuildInvestMaxLimit.value;
      rsn = 'exp_by_invest';
    } else if (category === GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE) {
      hardCap = cms.Const.GuildPointUnionRequestMaxLimit.value;
      rsn = 'exp_by_union_quest_complete';
    } else if (category === GUILD_POINT_CATEGORY.SYNTHESIS) {
      hardCap = cms.Const.GuildPointUnionRequestMaxLimit.value;
      rsn = 'exp_by_synthesis';
    }
    // 레디스에 길드포인트 업데이트
    return (
      guildRedis['updateGuildPoint'](
        user.userId,
        guildId,
        JSON.stringify(dgp),
        JSON.stringify(wgp),
        JSON.stringify(agp)
      )
        .then(() => {
          return guildRedis['updateGuildMember'](guildId, user.userId, JSON.stringify(guildMember));
        })
        .then(() => {
          _.merge(sync, {
            add: {
              userGuild: {
                guild: {
                  members: {
                    [user.userId]: {
                      dailyGuildPoints: {
                        [category]: dgp[category],
                      },
                      weeklyGuildPoints: {
                        [category]: wgp[category],
                      },
                      accumGuildPoints: {
                        [category]: agp[category],
                      },
                    },
                  },
                },
              },
            },
          });

          const guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);
          user.glog('guild_active_point', {
            rsn: 'guild_active_point',
            add_rsn: null,
            nation,
            grade,
            category,
            category_cv: addGP,
            category_progress: `${dgp[category]}/${hardCap}`,
            daily_point: convertGuildPointToLog(dgp),
            weekly_point: convertGuildPointToLog(wgp),
            total_point: convertGuildPointToLog(agp),
            guild_data,
          });
        })
        // 길드경험치
        .then(() => {
          return GuildUtil.updateExp(
            user,
            guildId,
            guildData,
            userLightInfos,
            Math.floor(addGP),
            rsn,
            null
          );
        })
        .then((syncResult: Sync) => {
          _.merge(sync, syncResult);

          // 기여도 월드 랭킹 업데이트
          let score: number = 0;
          _.forOwn(guildData.accumGuildPoints, (agp) => {
            _.forOwn(agp, (value) => (score += value));
          });

          rankingManager.updateRanking(
            RANKING_CMS_ID.GUILD_CONTRIBUTION,
            guildId,
            score,
            user.userId
          );
          return sync;
        })
    );
  }

  // 출석 길드포인트 등록 후 길드정보를 반환.
  export function getGuildInfoAndAttendance(user: User, bAttendance: boolean) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    let guildData: GuildData;
    let userLightInfos: { [userId: number]: UserLightInfo };

    const sync: Sync = {};
    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId)
      .then((result) => {
        guildData = result.guildData;
        userLightInfos = result.userLightInfos;

        if (bAttendance === true) {
          let curDailyPoint = 0;
          if (guildData.dailyGuildPoints[user.userId]) {
            const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
            if (dailyPoints[GUILD_POINT_CATEGORY.ATTENDANCE]) {
              curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.ATTENDANCE];
            }
          }

          let addedValue = 0;
          if (curDailyPoint < cms.Const.GuildPointAttendanceMaxLimit.value) {
            addedValue = cms.Const.GuildAttendancePerPoint.value;
            if (curDailyPoint + addedValue > cms.Const.GuildPointAttendanceMaxLimit.value) {
              addedValue = cms.Const.GuildPointAttendanceMaxLimit.value - curDailyPoint;
            }
          }

          if (!isNotANumber(addedValue) && addedValue > 0) {
            return save(
              user,
              guildData,
              userLightInfos,
              GUILD_POINT_CATEGORY.ATTENDANCE,
              addedValue
            );
          }
        }
      })
      .then(() => {
        _.merge(sync, GuildUtil.buildGuildSyncDataAll(user, guildData, userLightInfos));
        _.merge(sync, {
          add: {
            userGuild: {
              guild: {
                lastResetTimeUtc: curTimeUtc(),
              },
            },
          },
        });
        return sync;
      });
  }

  // PVE 전투 승리 길드포인트 추가.
  export function processBattleWin(user: User, npcCmsId: number) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }
    const npcCms: OceanNpcDesc = getOceanNpcCms()[npcCmsId];
    if (!npcCms) {
      mlog.error('invalid-ocean-npc-cms-id-at-processBattleWin', {
        userId: user.userId,
        npcCmsId,
      });
      return Promise.resolve({});
    }

    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then(
      (result) => {
        const guildData: GuildData = result.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.BATTLE]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.BATTLE];
          }
        }

        let addedValue = 0;
        if (
          npcCms.OceanNpcType === OCEAN_NPC_TYPE.LOCAL ||
          npcCms.OceanNpcType === OCEAN_NPC_TYPE.EVENT
        ) {
          addedValue = cms.Const.GuildBattlePerPoint1.value;
        } else if (npcCms.OceanNpcType === OCEAN_NPC_TYPE.ELITE) {
          addedValue = cms.Const.GuildBattlePerPoint2.value;
        }

        if (curDailyPoint + addedValue > cms.Const.GuildPointBattleMaxLimit.value) {
          addedValue = cms.Const.GuildPointBattleMaxLimit.value - curDailyPoint;
        }
        if (isNotANumber(addedValue) || addedValue === 0) {
          return {};
        }
        return save(
          user,
          result.guildData,
          result.userLightInfos,
          GUILD_POINT_CATEGORY.BATTLE,
          addedValue
        );
      }
    );
  }

  // PVP 전투 승리 길드포인트 추가.
  export function processPvpPlunder(user: User) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then(
      (result) => {
        const guildData: GuildData = result.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.PLUNDER]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.PLUNDER];
          }
        }

        let addedValue = 0;
        if (curDailyPoint < cms.Const.GuildPointPVPMaxLimit.value) {
          addedValue = cms.Const.GuildPVPPerPoint.value;
          if (curDailyPoint + addedValue > cms.Const.GuildPointPVPMaxLimit.value) {
            addedValue = cms.Const.GuildPointPVPMaxLimit.value - curDailyPoint;
          }
        }
        if (isNotANumber(addedValue) || addedValue === 0) {
          return {};
        }

        return save(
          user,
          result.guildData,
          result.userLightInfos,
          GUILD_POINT_CATEGORY.PLUNDER,
          addedValue
        );
      }
    );
  }

  // 탐험 길드포인트 추가.
  export function processExplore(user: User) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then(
      (result) => {
        const guildData: GuildData = result.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.EXPLORE]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.EXPLORE];
          }
        }

        let addedValue = 0;
        if (curDailyPoint < cms.Const.GuildPointExploreMaxLimit.value) {
          addedValue = cms.Const.GuildExplorePerPoint.value;
          if (curDailyPoint + addedValue > cms.Const.GuildPointExploreMaxLimit.value) {
            addedValue = cms.Const.GuildPointExploreMaxLimit.value - curDailyPoint;
          }
        }
        if (isNotANumber(addedValue) || addedValue === 0) {
          return {};
        }

        return save(
          user,
          result.guildData,
          result.userLightInfos,
          GUILD_POINT_CATEGORY.EXPLORE,
          addedValue
        );
      }
    );
  }

  // 교역 길드포인트 추가.
  export function processTrade(user: User, profit: number) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    if (profit <= 0) {
      return Promise.resolve({});
    }

    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then(
      (result) => {
        const guildData: GuildData = result.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.TRADE]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.TRADE];
          }
        }
        let addedValue = 0;
        if (curDailyPoint < cms.Const.GuildTradeMaxLimit.value) {
          const m = profit / cms.Const.GuildTradeBaseDucat.value;

          addedValue = cms.Const.GuildTradePerPoint.value * m;
          addedValue = Math.floor(addedValue * 100) / 100;
          if (curDailyPoint + addedValue > cms.Const.GuildTradeMaxLimit.value) {
            addedValue = cms.Const.GuildTradeMaxLimit.value - curDailyPoint;
          }
        }

        if (!addedValue) {
          return {};
        }

        return save(user, guildData, result.userLightInfos, GUILD_POINT_CATEGORY.TRADE, addedValue);
      }
    );
  }

  // 제작 길드포인트 추가.
  export function processCraft(
    user: User,
    pts: number,
    guild?: { guildData: GuildData; userLightInfos: { [userId: number]: UserLightInfo } }
  ) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    return Promise.resolve()
      .then(() => {
        if (!guild) {
          return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId);
        }
        return guild;
      })
      .then((guild) => {
        const guildData: GuildData = guild.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.CRAFT]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.CRAFT];
          }
        }

        let addedValue = 0;
        if (curDailyPoint < cms.Const.GuildPointCraftMaxLimit.value) {
          addedValue = pts;
          if (curDailyPoint + addedValue > cms.Const.GuildPointCraftMaxLimit.value) {
            addedValue = cms.Const.GuildPointCraftMaxLimit.value - curDailyPoint;
          }
        }

        if (isNotANumber(addedValue) || addedValue === 0) {
          return {};
        }

        return save(user, guildData, guild.userLightInfos, GUILD_POINT_CATEGORY.CRAFT, addedValue);
      });
  }

  // 합성 길드포인트 추가.
  export function processSynthesis(
    user: User,
    pts: number,
    guild?: { guildData: GuildData; userLightInfos: { [userId: number]: UserLightInfo } }
  ) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    return Promise.resolve()
      .then(() => {
        if (!guild) {
          return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId);
        }
        return guild;
      })
      .then((guild) => {
        const guildData: GuildData = guild.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.SYNTHESIS]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.SYNTHESIS];
          }
        }

        let addedValue = 0;
        if (curDailyPoint < cms.Const.GuildPointSynthesisMaxLimit.value) {
          addedValue = pts;
          if (curDailyPoint + addedValue > cms.Const.GuildPointSynthesisMaxLimit.value) {
            addedValue = cms.Const.GuildPointSynthesisMaxLimit.value - curDailyPoint;
          }
        }

        if (isNotANumber(addedValue) || addedValue === 0) {
          return {};
        }

        return save(
          user,
          guildData,
          guild.userLightInfos,
          GUILD_POINT_CATEGORY.SYNTHESIS,
          addedValue
        );
      });
  }

  // 투자 길드포인트 추가.
  export function processGoverInvest(user: User, investPoints: number) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    if (investPoints <= 0) {
      return Promise.resolve({});
    }

    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then(
      (result) => {
        const guildData: GuildData = result.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.INVEST]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.INVEST];
          }
        }
        let addedValue = 0;
        if (curDailyPoint < cms.Const.GuildInvestMaxLimit.value) {
          const m = investPoints / cms.Const.GuildInvestBaseDucat.value;

          addedValue = Math.floor(cms.Const.GuildInvestPerPoint.value * m);
          if (curDailyPoint + addedValue > cms.Const.GuildInvestMaxLimit.value) {
            addedValue = cms.Const.GuildInvestMaxLimit.value - curDailyPoint;
          }
        }

        if (!addedValue) {
          return {};
        }

        return save(
          user,
          guildData,
          result.userLightInfos,
          GUILD_POINT_CATEGORY.INVEST,
          addedValue
        );
      }
    );
  }

  // 조합의뢰 완료 길드포인트 추가.
  export function processUnionQuestComplete(user: User) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then(
      (result) => {
        const guildData: GuildData = result.guildData;

        let curDailyPoint = 0;
        if (guildData.dailyGuildPoints[user.userId]) {
          const dailyPoints: GuildPointNub = guildData.dailyGuildPoints[user.userId];
          if (dailyPoints[GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE]) {
            curDailyPoint = dailyPoints[GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE];
          }
        }

        let addedValue = 0;
        if (curDailyPoint < cms.Const.GuildPointUnionRequestMaxLimit.value) {
          addedValue = cms.Const.GuildUnionRequestPerPoint.value;
          if (curDailyPoint + addedValue > cms.Const.GuildPointUnionRequestMaxLimit.value) {
            addedValue = cms.Const.GuildPointUnionRequestMaxLimit.value - curDailyPoint;
          }
        }
        if (isNotANumber(addedValue) || addedValue === 0) {
          return {};
        }

        return save(
          user,
          result.guildData,
          result.userLightInfos,
          GUILD_POINT_CATEGORY.UNION_QUEST_COMPLETE,
          addedValue
        );
      }
    );
  }

  export function processDev(user: User, category: number, value: number) {
    if (!user.userGuild.guildId) {
      return Promise.resolve({});
    }

    return GuildUtil.GetGuildDataWithMemberLightInfo(user, user.userGuild.guildId).then(
      (result) => {
        // 진행도는 제외
        return save(user, result.guildData, result.userLightInfos, category, value);
      }
    );
  }
}

export class UserGuild {
  guildId: number = 0;
  lastGuildLeftTimeUtc: number = 0;

  // 가입 대기중인 길드 목록
  waitingJoinGuildIds: {
    [guildId: number]: {
      regTimeUtc: number;
    };
  } = {};

  // 가입된 길드 캐시정보 (마을 및 오션에서 캐릭터 상단에 표시될 길드정보)
  guildApperance: GuildAppearance = {
    guildName: '',
    grade: 0,
    emblemImageCmsId: 0,
    emblemColorCmsId: 0,
    emblemBorderCmsId: 0,
  };

  // 진행 중인 상회제작 아이템
  guildCraftProgresses: { [slot: number]: GuildCraftProgress } = {};

  // 진행 중인 합성 아이템
  guildSynthesisProgresses: { [slot: number]: GuildSynthesisProgress } = {};

  constructor() {}

  clone(): UserGuild {
    const c = new UserGuild();
    c.cloneSet(
      this.guildId,
      this.lastGuildLeftTimeUtc,
      _.cloneDeep(this.waitingJoinGuildIds),
      _.cloneDeep(this.guildApperance),
      _.cloneDeep(this.guildCraftProgresses),
      _.cloneDeep(this.guildSynthesisProgresses)
    );
    return c;
  }
  cloneSet(
    guildId: number,
    lastGuildLeftTimeUtc: number,
    waitingJoinGuildIds: {
      [guildId: number]: {
        regTimeUtc: number;
      };
    },
    guildApperance: GuildAppearance,
    guildCraftProgresses: { [slot: number]: GuildCraftProgress },
    guildSynthesisProgresses: { [slot: number]: GuildSynthesisProgress }
  ): void {
    this.guildId = guildId;
    this.lastGuildLeftTimeUtc = lastGuildLeftTimeUtc;
    this.waitingJoinGuildIds = waitingJoinGuildIds;
    this.guildApperance = guildApperance;
    this.guildCraftProgresses = guildCraftProgresses;
    this.guildSynthesisProgresses = guildSynthesisProgresses;
  }

  initWithLoginInfo(loginInfo: LoginInfo) {
    this.guildId = loginInfo.guildId;
    this.lastGuildLeftTimeUtc = parseInt(loginInfo.lastGuildLeftTimeUtc, 10);

    loginInfo.waitingJoinGuilds.forEach((guild) => {
      this.waitingJoinGuildIds[guild.guildId] = {
        regTimeUtc: parseInt(guild.regTimeUtc, 10),
      };
    });

    loginInfo.guildCraftProgresses.forEach((craft) => {
      this.guildCraftProgresses[craft.slot] = {
        cmsId: craft.guildCraftCmsId,
        slot: craft.slot,
        startTimeUtc: parseInt(craft.startTimeUtc, 10),
        completionTimeUtc: parseInt(craft.completionTimeUtc),
      };
    });

    loginInfo.guildSynthesisProgresses.forEach((synthesis) => {
      this.guildSynthesisProgresses[synthesis.slot] = {
        cmsId: synthesis.guildSynthesisCmsId,
        slot: synthesis.slot,
        startTimeUtc: parseInt(synthesis.startTimeUtc, 10),
        completionTimeUtc: parseInt(synthesis.completionTimeUtc),
      };
    });
  }

  getSyncData(): sync.All {
    const craftProgresses: {
      [slot: number]: {
        cmsId?: number;
        slot?: number;
        startTimeUtc?: number;
        completionTimeUtc?: number;
      };
    } = {};

    _.forOwn(this.guildCraftProgresses, (elem) => {
      craftProgresses[elem.slot] = {
        cmsId: elem.cmsId,
        slot: elem.slot,
        startTimeUtc: elem.startTimeUtc,
        completionTimeUtc: elem.completionTimeUtc,
      };
    });

    const synthesisProgresses: {
      [slot: number]: {
        cmsId?: number;
        slot?: number;
        startTimeUtc?: number;
        completionTimeUtc?: number;
      };
    } = {};

    _.forOwn(this.guildSynthesisProgresses, (elem) => {
      synthesisProgresses[elem.slot] = {
        cmsId: elem.cmsId,
        slot: elem.slot,
        startTimeUtc: elem.startTimeUtc,
        completionTimeUtc: elem.completionTimeUtc,
      };
    });

    const sync: sync.All = {
      userGuild: {
        waitingJoinGuildIds: this.waitingJoinGuildIds,
        leftGuildTimeUtc: this.lastGuildLeftTimeUtc,
        craftProgresses,
        synthesisProgresses,
      },
    };

    return sync;
  }
  getGuildName(): string {
    return this.guildApperance.guildName;
  }

  setGuildAppearance(ga: GuildAppearance) {
    _.merge(this.guildApperance, ga);
  }

  joinGuild(user: User, id: number, guildData: GuildData): Sync {
    this.guildId = id;
    this.waitingJoinGuildIds = {};
    this.guildApperance = {
      guildName: guildData.guild.guildName,
      grade: guildData.members[user.userId].grade,
      emblemImageCmsId: guildData.guild.emblemImageCmsId,
      emblemColorCmsId: guildData.guild.emblemColorCmsId,
      emblemBorderCmsId: guildData.guild.emblemBorderCmsId,
    };

    // 채팅 채널 생성
    const userIdStr = user.userId.toString();
    const channelName = `GUILD_${id}`;
    mhttp.chatd
      .existChannel(channelName)
      .then((ret) => {
        if (!ret) {
          return mhttp.chatd.createGuildChannel(channelName, userIdStr, false);
        }
        return null;
      })
      .then(() => {
        return mhttp.chatd.allowGuildChannel(channelName, userIdStr);
      })
      .then(() => {
        return mhttp.chatd.channelJoin(channelName, userIdStr);
      })
      .catch((err) => {
        mlog.error('mhttp.chatd joinGuild is failed.', {
          err: err.message,
          userId: user.userId,
          guildId: id,
        });
      });

    user.onGuildJoinOrChange();
    return {
      remove: {
        userGuild: {
          waitingJoinGuildIds: true,
        },
      },
    };
  }
  updateGuildAppearance(
    user: User,
    guildName?: string,
    grade?: number,
    emblemImageCmsId?: number,
    emblemColorCmsId?: number,
    emblemBorderCmsId?: number
  ) {
    let isChanged = false;
    if (guildName && this.guildApperance.guildName !== guildName) {
      isChanged = true;
      _.merge(this.guildApperance, { guildName });
    }

    if (!isNotANumber(grade) && this.guildApperance.grade !== grade) {
      isChanged = true;
      _.merge(this.guildApperance, { grade });
    }

    if (
      !isNotANumber(emblemImageCmsId) &&
      this.guildApperance.emblemImageCmsId !== emblemImageCmsId
    ) {
      isChanged = true;
      _.merge(this.guildApperance, { emblemImageCmsId });
    }
    if (
      !isNotANumber(emblemColorCmsId) &&
      this.guildApperance.emblemColorCmsId !== emblemColorCmsId
    ) {
      isChanged = true;
      _.merge(this.guildApperance, { emblemColorCmsId });
    }
    if (
      !isNotANumber(emblemBorderCmsId) &&
      this.guildApperance.emblemBorderCmsId !== emblemBorderCmsId
    ) {
      isChanged = true;
      _.merge(this.guildApperance, { emblemBorderCmsId });
    }

    if (isChanged) {
      user.onGuildJoinOrChange();
    }
  }

  leaveGuild(
    user: User,
    bShouldDeleteChattingChannel: boolean,
    bShouldDisallowChattingChannel: boolean
  ): Sync {
    const guildId = this.guildId;
    this.guildId = 0;
    this.lastGuildLeftTimeUtc = curTimeUtc();

    this.guildApperance = {
      guildName: '',
      grade: 0,
      emblemImageCmsId: 0,
      emblemColorCmsId: 0,
      emblemBorderCmsId: 0,
    };

    const removeEventPageCmsIds: number[] = EventRankingUtil.removeGuildUser(
      user.userId,
      guildId,
      curTimeUtc()
    );

    const userIdStr = user.userId.toString();
    const channelName = `GUILD_${guildId}`;
    mhttp.chatd
      .channelLeave(channelName, userIdStr)
      .then(() => {
        if (bShouldDisallowChattingChannel) {
          return mhttp.chatd.disallowGuildChannel(channelName, userIdStr);
        }
        return null;
      })
      .then(() => {
        if (bShouldDeleteChattingChannel) {
          return mhttp.chatd.deleteChannel(channelName);
        }
        return null;
      })
      .catch((err) => {
        mlog.error('mhttp.chatd leaveGuild is failed.', {
          err: err.message,
          userId: user.userId,
          guildId,
        });
      });

    user.onGuildLeave();
    const sync: Sync = {
      remove: {
        userGuild: {
          guild: true,
        },
      },
      add: {
        userGuild: {
          leftGuildTimeUtc: this.lastGuildLeftTimeUtc,
        },
      },
    };

    for (const eventPageCmsId of removeEventPageCmsIds) {
      _.merge<Sync, Sync>(sync, {
        add: {
          eventRanking: {
            [eventPageCmsId]: {
              guildUserScore: 0,
            },
          },
        },
      });
    }
    return sync;
  }

  addWaitingJoinGuild(guildId: number, regTimeUtc: number): Sync {
    this.waitingJoinGuildIds[guildId] = {
      regTimeUtc,
    };
    return {
      add: {
        userGuild: {
          waitingJoinGuildIds: {
            [guildId]: {
              regTimeUtc,
            },
          },
        },
      },
    };
  }

  removeWaitingJoinGuild(guildId: number): Sync {
    delete this.waitingJoinGuildIds[guildId];
    return {
      remove: {
        userGuild: {
          waitingJoinGuildIds: {
            [guildId]: true,
          },
        },
      },
    };
  }

  getGuildAppearance(): GuildAppearance | null {
    if (!this.guildId) {
      return null;
    }
    return this.guildApperance;
  }

  applyGulidSynthesisProgress(gsp: GuildSynthesisProgress) {
    this.guildSynthesisProgresses[gsp.slot] = gsp;
  }

  removeGulidSynthesisProgress(slotNo: number) {
    delete this.guildSynthesisProgresses[slotNo];
  }
}

const convertGuildPointToLog = (gp: GuildPointNub) => {
  const result: {
    [category: string]: number;
  } = {};

  for (let i = 1; i < GUILD_POINT_CATEGORY.MAX; i++) {
    result[`k_${i}`] = gp[i] || 0;
  }

  return result;
};
