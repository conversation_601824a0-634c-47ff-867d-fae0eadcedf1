// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import { Container } from 'typedi';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { Resp, Sync } from '../../type/sync';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../index';
import { LobbyService } from '../../server';
import { ChatTranslationChange } from '../../userChatTranslationCount';
import tuUpdateChatTranslationCount from '../../../mysqllib/txn/tuUpdateChatTranslationCount';
import cms from '../../../cms';
import UserPoints, { PointConsumptionCostParam } from '../../userPoints';
import { RedGemPointCmsId } from '../../../cms/ex';

const rsn = 'buy_chat_translation';
const add_rsn = null;
// ----------------------------------------------------------------------------
// 연속 전투 여부 저장
// ----------------------------------------------------------------------------

interface RequestBody {
  bPermitExchange: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BuyChatTranslationCount implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;
    const { bPermitExchange } = reqBody;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // 무제한 번역 이후로는 더이상 이 패킷을 요청할 수 없다.
    throw new MError(
      'You-can-no-longer-send-this-packet-after-an-unlimited-number-of-translations',
      MErrorCode.CANNOT_USE_TRANSLATION_PACKET
    );
    /*
      테스트용 
    cms.Const.ChatTranslateBuyOnOff.value = true;
    cms.Const.ChatTranslateBuyPointId.value = RedGemPointCmsId;
    cms.Const.ChatTranslateBuyPointValue.value = 100;
    */

    if (!cms.Const.ChatTranslateBuyOnOff.value) {
      throw new MError('cannot-buy-chat-translation', MErrorCode.CANNOT_BUY_IT_BECAUSE_CMS_BUY_OFF);
    }

    const chatTranslationChange: ChatTranslationChange =
      user.userChatTranslationCount.buildChatTranslationChange();

    if (chatTranslationChange.freeCount > 0) {
      throw new MError('there-are-still-amounts-left', MErrorCode.THERE_ARE_STILL_AMOUNTS_LEFT, {
        amount: chatTranslationChange.freeCount,
      });
    }

    const pointCosts: PointConsumptionCostParam[] = [
      {
        cmsId: cms.Const.ChatTranslateBuyPointId.value,
        cost: cms.Const.ChatTranslateBuyPointValue.value,
      },
    ];

    chatTranslationChange.freeCount++;
    const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
      pointCosts,
      bPermitExchange,
      { itemId: rsn },
      true
    );

    let sync: Sync = {};
    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return user.userPoints
      .tryConsumeCashs(pcChanges.cashPayments, sync, user, {
        user,
        rsn,
        add_rsn,
        exchangeHash,
      })
      .then(() => {
        const { userDbConnPoolMgr } = Container.get(LobbyService);
        return tuUpdateChatTranslationCount(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          pcChanges.pointChanges,
          chatTranslationChange
        );
      })
      .then(() => {
        mlog.info('buy-chat-translation', {
          userId: user.userId,
          pcChanges,
        });

        user.userPoints.applyPointChanges(pcChanges.pointChanges, { user, rsn, add_rsn });
        sync = _.merge(sync, {
          add: user.userChatTranslationCount.applyTranslationNub(chatTranslationChange),
        });

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, {
          sync,
        });
      });
  }
}
