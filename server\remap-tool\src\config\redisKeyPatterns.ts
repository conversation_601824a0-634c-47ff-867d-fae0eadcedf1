import { RedisKeyInfo } from '../types';

/**
 * 분석된 Redis 키 패턴 정의
 * 실제 코드베이스 분석을 통해 발견된 gnid/nid 관련 키 패턴들
 */
export const REDIS_KEY_PATTERNS: RedisKeyInfo[] = [
  // AccountId/Gnid 관련 패턴들
  {
    keyPattern: 'account:{accountId}',
    redisInstance: 'userCache',
    usesAccountId: true,
    usesPubId: false,
    usesUserId: false,
    usesGnid: true,
    usesNid: false,
    description: '계정 정보 저장 (토큰, 월드ID, 주문ID, 하트비트 등)',
    keyType: 'hash',
    keyCount: 0,
  },
  {
    keyPattern: 'prologueGnids:{worldId}',
    redisInstance: 'order',
    usesAccountId: false,
    usesPubId: false,
    usesUserId: false,
    usesGnid: true,
    usesNid: false,
    description: '월드별 프롤로그 상태 유저 관리 (Sorted Set, gnid를 member로 사용)',
    keyType: 'zset',
    keyCount: 0,
  },

  // 제외된 패턴: user:{userId} - userId는 게임서버 내부 ID이므로 키 자체는 리맵핑 대상 아님
  // 단, Hash 내부의 pubId 필드는 리맵핑 대상이지만 별도 처리 필요
  {
    keyPattern: 'deletionPubIds',
    redisInstance: 'auth',
    usesAccountId: false,
    usesPubId: true,
    usesUserId: false,
    usesGnid: false,
    usesNid: true,
    description: '삭제 대상 pubId 목록 관리 (List)',
    keyType: 'list',
    keyCount: 0,
  },
  {
    keyPattern: 'townUserWeeklyInvestmentReport:{nid}:{lang}',
    redisInstance: 'userCache',
    usesAccountId: false,
    usesPubId: true,
    usesUserId: false,
    usesGnid: false,
    usesNid: true,
    description: '도시 주간 투자 보고서 (nid 및 언어별 캐시)',
    keyType: 'string',
    keyCount: 0,
  },
  {
    keyPattern: 'prologueGnids:{worldId}',
    redisInstance: 'order',
    usesAccountId: false,
    usesPubId: false,
    usesUserId: false,
    usesGnid: true,
    usesNid: false,
    description: '프롤로그 상태 GNID 목록 (월드별 Sorted Set)',
    keyType: 'zset',
    keyCount: 0,
  },
  {
    keyPattern: 'account:{accountId}',
    redisInstance: 'userCache',
    usesAccountId: true,
    usesPubId: false,
    usesUserId: false,
    usesGnid: false,
    usesNid: false,
    description: '계정 정보 캐시 (AccountId 기반 Hash)',
    keyType: 'hash',
    keyCount: 0,
  },
  {
    keyPattern: 'deletionPubIds',
    redisInstance: 'auth',
    usesAccountId: false,
    usesPubId: true,
    usesUserId: false,
    usesGnid: false,
    usesNid: false,
    description: '삭제 대상 PubId 목록 (List)',
    keyType: 'list',
    keyCount: 0,
  },
  {
    keyPattern: 'user:{userId}',
    redisInstance: 'userCache',
    usesAccountId: false,
    usesPubId: true,
    usesUserId: true,
    usesGnid: false,
    usesNid: true,
    description: '유저 캐시 정보 (Hash 내부 pubId 필드 값 변경)',
    keyType: 'hash',
    keyCount: 0,
    hashFields: ['pubId'], // 업데이트할 특정 필드들
  },
  // 제외된 패턴: userIdsByName - Hash key는 name, value는 userId이므로 gnid/nid와 무관

  // Guild 관련 패턴들 - 제외 (키 자체에 gnid/nid 없음, field로만 사용)
  // guildMember:{guildId} - Hash field로 userId 사용하지만 리맵핑 대상 아님
  // pickedDailyRewardIdx:{guildId} - Hash field로 userId 사용하지만 리맵핑 대상 아님

  // Nation 관련 패턴들
  // {
  //   keyPattern: 'nationUserRewardedGoalPromiseCmsIds:{nationCmsId}:{userId}:{sessionId}',
  //   redisInstance: 'nation',
  //   usesAccountId: false,
  //   usesPubId: true,
  //   usesUserId: true,
  //   usesGnid: false,
  //   usesNid: true,
  //   description: '국가 목표 공약 보상 수령 여부 (키에 userId 포함)',
  // },

  // 제외된 Nation 패턴들 - 키 자체에 gnid/nid 없음
  // nationCabinetApplicantUserIds:{nationCmsId}:{sessionId} - Set member로 userId 사용하지만 리맵핑 대상 아님
  // nationWeeklyDonationRank:{nationCmsId}:{sessionId} - Sorted Set member로 userId 사용하지만 리맵핑 대상 아님
  // nationWeeklyDonationRankRewardedUsers:{nationCmsId}:{sessionId} - Set member로 userId 사용하지만 리맵핑 대상 아님
  // nationCabinet:{nationCmsId}:{sessionId} - Hash field로 userId 사용하지만 리맵핑 대상 아님

  // Battle Log 관련 패턴들
  // {
  //   keyPattern: 'bl:{userId}:{battleId}',
  //   redisInstance: 'globalBattleLog',
  //   usesAccountId: false,
  //   usesPubId: true,
  //   usesUserId: true,
  //   usesGnid: false,
  //   usesNid: true,
  //   description: '진행 중인 전투 로그 (키에 userId 포함)',
  // },
  // {
  //   keyPattern: 'canceled:{userId}:{battleId}',
  //   redisInstance: 'globalBattleLog',
  //   usesAccountId: false,
  //   usesPubId: true,
  //   usesUserId: true,
  //   usesGnid: false,
  //   usesNid: true,
  //   description: '취소된 전투 로그 (키에 userId 포함)',
  // },

  // 제외된 Battle Log 패턴들
  // inProgress - Hash field로 userId 사용하지만 키 자체에는 ID 없음, 리맵핑 대상 아님

  // User 관련 기타 패턴들
  // {
  //   keyPattern: 'eu:{userId}',
  //   redisInstance: 'userCache',
  //   usesAccountId: false,
  //   usesPubId: true,
  //   usesUserId: true,
  //   usesGnid: false,
  //   usesNid: true,
  //   description: '회피 가능한 조우 유저 목록',
  // },
  // {
  //   keyPattern: 'questDailyLimit:expireTimeUtc:{userId}',
  //   redisInstance: 'user',
  //   usesAccountId: false,
  //   usesPubId: true,
  //   usesUserId: true,
  //   usesGnid: false,
  //   usesNid: true,
  //   description: '퀘스트 일일 제한 만료 시간 (키에 userId 포함)',
  // },
  // {
  //   keyPattern: 'questDailyLimit:completedCounts:{userId}',
  //   redisInstance: 'user',
  //   usesAccountId: false,
  //   usesPubId: true,
  //   usesUserId: true,
  //   usesGnid: false,
  //   usesNid: true,
  //   description: '퀘스트 일일 제한 완료 횟수 (키에 userId 포함)',
  // },

  // // 추가 패턴들 (실제 분석에서 발견된 패턴들)
  // {
  //   keyPattern: 'nationUserRewardedGoalPromiseCmsIds:{nationCmsId}:{userId}',
  //   redisInstance: 'nation',
  //   usesAccountId: false,
  //   usesPubId: true,
  //   usesUserId: true,
  //   usesGnid: false,
  //   usesNid: true,
  //   description: '국가 목표 공약 보상 수령 여부 (sessionId 없는 버전, 키에 userId 포함)',
  // },
];

/**
 * Redis 인스턴스별 키 패턴 그룹화
 */
export function getKeyPatternsByInstance(): Map<string, RedisKeyInfo[]> {
  const grouped = new Map<string, RedisKeyInfo[]>();

  for (const keyInfo of REDIS_KEY_PATTERNS) {
    const instanceName = keyInfo.redisInstance;
    if (!grouped.has(instanceName)) {
      grouped.set(instanceName, []);
    }
    grouped.get(instanceName)!.push(keyInfo);
  }

  return grouped;
}

/**
 * AccountId/Gnid 관련 키 패턴만 필터링
 */
export function getAccountIdKeyPatterns(): RedisKeyInfo[] {
  return REDIS_KEY_PATTERNS.filter(key => key.usesAccountId || key.usesGnid);
}

/**
 * PubId/Nid 관련 키 패턴만 필터링
 */
export function getPubIdKeyPatterns(): RedisKeyInfo[] {
  return REDIS_KEY_PATTERNS.filter(key => key.usesPubId || key.usesNid);
}

/**
 * 특정 Redis 인스턴스의 키 패턴만 필터링
 */
export function getKeyPatternsByInstanceName(instanceName: string): RedisKeyInfo[] {
  return REDIS_KEY_PATTERNS.filter(key => key.redisInstance === instanceName);
}

/**
 * 모든 정의된 Redis 키 패턴 반환
 */
export function getAllRedisKeyPatterns(): RedisKeyInfo[] {
  return [...REDIS_KEY_PATTERNS];
}
