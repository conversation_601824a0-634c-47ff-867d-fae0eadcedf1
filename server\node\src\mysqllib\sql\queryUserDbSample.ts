// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

// import * as query from '../query';
// import { MErrorCode } from '../../motiflib/merror';
// import { AutoChangeItemsHistoryRow } from '../sp/puAutoChangeItemsHistoryLoad';

// export async function queryUserDbHistoryInsert(
//   connection: query.Connection,
//   userId: number,
//   historyJsonString: string,
//   curTimeUtc: number
// ): Promise<void> {
//   try {
//     const sql = `
//       START TRANSACTION;
//       DELETE FROM u_auto_change_items_history WHERE userId = ?;
//       INSERT INTO u_auto_change_items_history (userId, history, createdAt)
//       VALUES (?, ?, FROM_UNIXTIME(?));
//       SELECT * FROM u_auto_change_items_history WHERE userId = ?;
//       COMMIT;
//     `;
//     const values = [userId, userId, historyJsonString, curTimeUtc, userId];
//     result:
//     [
//       OkPacket {},
//       OkPacket {},
//       OkPacket {},
//       [RowDataPacket {}],
//       OkPacket {}
//     ]
//     await query.querySqlAsync(connection, sql, values);
//   } catch (err) {
//     throw query.generateMErrorRejection(MErrorCode.AUTO_CHANGE_ITEMS_HISTORY_INSERT_ERROR);
//   }
// }

// export async function queryUserDbHistoryLoad(
//   connection: query.Connection,
//   userId: number
// ): Promise<AutoChangeItemsHistoryRow[]> {
//   try {
//     const sql = `
//     SELECT * FROM u_auto_change_items_history WHERE userId = ?;
//     `;
//     const values = [userId, userId];
//     result:
//     [
//       RowDataPacket {},
//       RowDataPacket {},
//     ]

//     const sql = `
//     SELECT * FROM u_auto_change_items_history WHERE userId = ?;
//     SELECT * FROM u_auto_change_items_history WHERE userId = ?;
//     `;
//     const values = [userId, userId];
//     result:
//     [
//       [
//         RowDataPacket {},
//         RowDataPacket {},
//       ],
//       [
//         RowDataPacket {},
//         RowDataPacket {},
//       ],
//     ]

//     const qr = await query.querySqlAsync(connection, sql, values);
//     return qr.rows[0];
//   } catch (err) {
//     throw query.generateMErrorRejection(MErrorCode.AUTO_CHANGE_ITEMS_HISTORY_LOAD_ERROR);
//   }
// }

// export async function queryUserDbHistoryDelete(
//   connection: query.Connection,
//   userId: number
// ): Promise<void> {
//   try {
//     const sql = `DELETE FROM u_auto_change_items_history WHERE userId = ?;`;
//     const values = [userId];
//     reuslt:
//     [
//       OkPacket{}
//     ]

//     await query.querySqlAsync(connection, sql, values);



//   } catch (err) {
//     throw query.generateMErrorRejection(MErrorCode.AUTO_CHANGE_ITEMS_HISTORY_DELETE_ERROR);
//   }
// }

// query.ts
// export async function querySqlAsync(connection: Connection, sql: string, args: any[])
// : Promise<mysqlUtil.QueryResult> {
//   const apmTxn = mapm.startTxn(`QUERY.${sql}.${args.join(',')}`, 'query');
//   try {
//     const result = await mysqlUtil.querySqlAsync(connection, mysql.format(sql, args));
//     return result;
//   }
//   catch (err) {
//     mapm.endTxn(apmTxn, false);
//     throw err;
//   }
//   finally {
//     mapm.endTxn(apmTxn);
//   }
// }


// mysqlUtil.ts
// export async function querySqlAsync(connection: Connection, sql: string)
// : Promise<QueryResult> {
//   return connection.query(sql).then((rows) => {
//     mlog.debug('mysql query result', {
//       sql,
//       rows,
//     });
//     return reduceRows(rows); << 주의!!
//   });
// }
