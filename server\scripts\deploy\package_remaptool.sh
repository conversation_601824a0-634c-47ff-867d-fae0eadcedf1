#!/bin/bash

GIT_DIR=$1
BRANCH=$2
GIT_REV=$3

if [ -z "$GIT_DIR" ]; then
  GIT_DIR="../../../.git"
fi

if [ -z "$BRANCH" ]; then
  BRANCH="master"
fi

# # Reset to head.
# git reset --hard HEAD

# # Checkout the specified branch.
# git checkout $BRANCH
# git reset --hard origin/$BRANCH

if [ -z "$GIT_REV" ] || [ $GIT_REV == "latest" ]; then
  GIT_REV=`git rev-parse HEAD`
fi

# Make a package name.
DATESTR=$(date '+%Y-%m-%d.%H%M%S')
GITHASH="${GIT_REV:0:10}"
PACKAGE_NAME="uwo-remap-tool.$DATESTR"

PACKAGE_ARTIFACT_PATH="package_artifacts/$PACKAGE_NAME"

# Print packaging info.
echo ""
echo "========================================================================="
echo "="
echo "= Packaging UWO remap-tool..."
echo "="
echo "= git_dir: $GIT_DIR"
echo "= branch: $BRANCH"
echo "= git_rev: $GIT_REV"
echo "= package_name: $PACKAGE_NAME"
echo "="
echo "========================================================================="
echo ""

# Archive folders needed for running servers.
git --git-dir=$GIT_DIR archive --prefix=server/ --output $PACKAGE_ARTIFACT_PATH.zip --format=zip $GIT_REV:server

echo "Packaging successful!"
echo ""
