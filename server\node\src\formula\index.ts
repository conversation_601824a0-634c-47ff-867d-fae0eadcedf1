// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import R from 'ramda';
import _ from 'lodash';
import assert from 'assert';

import cms from '../cms';
import * as cmsEx from '../cms/ex';
import { getOceanNpcCms } from '../cms/ex';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import { CashShopDesc } from '../cms/cashShopDesc';
import { VillageDesc } from '../cms/villageDesc';
import { MError, MErrorCode } from '../motiflib/merror';
import * as mutil from '../motiflib/mutil';
import { EarthScale } from '../cms/oceanCoordinate';
import { EventMissionDesc } from '../cms/eventMissionDesc';
import { PreferenceItem } from '../cms/itemDesc';
import { KARMA_TYPE, KarmaDesc } from '../cms/karmaDesc';
import { FleetStat } from '../motiflib/stat/fleetStat';
import { SmuggleDesc } from '../cms/smuggleDesc';
import { SmuggleGoodsDesc } from '../cms/smuggleGoodsDesc';
import { REWARD_TYPE, RewardCmsElem } from '../cms/rewardDesc';
import { RewardSeasonElemDesc } from '../cms/rewardSeasonItemsDesc';

// Client's formula is C:\work\uwo\game\lua\formula\Formula.lua
// Functions of both scripts should be the same.

// -----------------------------------------------------------------------------
export function HasContentsResetTimePassed(
  curTimeUtc: number,
  lastContentsTimeUtc: number,
  contentsResetHour: number,
  timezone?: number
) {
  const timeZoneOffset = mutil.getTimezoneOffset(timezone) * 60;
  const localTime = curTimeUtc - timeZoneOffset;
  const curDays = Math.floor(localTime / 86400);
  const curHours = Math.floor(localTime / 3600) % 24;
  let lastContentResetDays = curDays;

  if (curHours < contentsResetHour) {
    lastContentResetDays = lastContentResetDays - 1;
  }

  const lastContentResetTime = lastContentResetDays * 86400 + contentsResetHour * 3600;
  if (lastContentsTimeUtc - timeZoneOffset >= lastContentResetTime) {
    return false;
  }
  return true;
}

// -----------------------------------------------------------------------------
export function CalcContentsResetTimePassingCount(
  curTimeUtc: number,
  lastContentsTimeUtc: number,
  contentsResetHour: number,
  timezone?: number
) {
  const timeZoneOffset = mutil.getTimezoneOffset(timezone) * 60;
  const localTime = curTimeUtc - timeZoneOffset;
  const curDays = Math.floor(localTime / 86400);
  const curHours = Math.floor(localTime / 3600) % 24;
  let lastContentResetDays = curDays;

  if (curHours < contentsResetHour) {
    lastContentResetDays = lastContentResetDays - 1;
  }

  const lastContentResetTime = lastContentResetDays * 86400 + contentsResetHour * 3600;
  if (lastContentsTimeUtc - timeZoneOffset >= lastContentResetTime) {
    return 0;
  }

  const tickCount = Math.ceil(
    (lastContentResetTime + timeZoneOffset - lastContentsTimeUtc) / 86400
  );

  // mlog.warn('[TEMP] last daily contents reset time: ', {
  //   lastContentResetTime: new Date((lastContentResetTime + timeZoneOffset) * 1000).toString(),
  //   lastContentsTimeUtc: new Date(lastContentsTimeUtc * 1000).toString(),
  //   tickCount,
  // });

  return tickCount;
}

// -----------------------------------------------------------------------------
export function HasWeeklyContentsResetTimePassed(
  curTimeUtc: number,
  lastContentsTimeUtc: number,
  contentsResetHour: number,
  contentsResetDay: number,
  timezone?: number
) {
  const timeZoneOffsetSec = mutil.getTimezoneOffset(timezone) * 60;
  const curDateObj = new Date(curTimeUtc * 1000);

  // local 기준, 1970년1월1일0시 이후 몇번째 날인지.
  const curLocalDays = Math.floor((curTimeUtc - timeZoneOffsetSec) / 86400);
  // local hours
  const curLocalHours = mutil.getLocalHours(curDateObj, timezone);
  // local day
  const curLocalDayOfWeek = mutil.getLocalDay(curDateObj, timezone);

  // local 기준, 마지막으로 리셋한 날이 1970년1월1일0시 이후 몇번째 날인지.
  let localContentResetDays: number;

  if (curLocalDayOfWeek === contentsResetDay && curLocalHours >= contentsResetHour) {
    localContentResetDays = curLocalDays;
  } else if (curLocalDayOfWeek === contentsResetDay) {
    localContentResetDays = curLocalDays - 7;
  } else if (curLocalDayOfWeek > contentsResetDay) {
    localContentResetDays = curLocalDays - curLocalDayOfWeek + contentsResetDay;
  } else {
    localContentResetDays = curLocalDays - 7 + contentsResetDay - curLocalDayOfWeek;
  }

  const contentsResetTimeUtc =
    localContentResetDays * 86400 + contentsResetHour * 3600 + timeZoneOffsetSec;

  if (lastContentsTimeUtc >= contentsResetTimeUtc) {
    return false;
  }
  return true;
}

// -----------------------------------------------------------------------------
export function HasMonthlyContentsResetTimePassed(
  curTimeUtc: number,
  lastContentsTimeUtc: number,
  contentsResetHour: number,
  contentsResetDate: number,
  timezone?: number
) {
  // 지난 달의 날수를 반환. ex) 28, 29, 30, 31
  function getNumDaysInPreMonth(curDateObj: Date): number {
    const curMonth = curDateObj.getMonth();
    if (curMonth > 1) {
      // month 0 ~ 11 , day = 0  지난달 마지막 일 전달
      return mutil.getLocalDate(
        new Date(mutil.getLocalFullYear(curDateObj), curMonth, 0),
        timezone
      );
    } else {
      // 12월, 1월은 31일
      return 31;
    }
  }

  const timeZoneOffsetSec = mutil.getTimezoneOffset(timezone) * 60;
  const curDateObj = new Date(curTimeUtc * 1000);

  // local 기준, 1970년1월1일0시 이후 몇번째 날인지.
  const curLocalDays = Math.floor((curTimeUtc - timeZoneOffsetSec) / 86400);
  // local hours
  const curLocalHours = mutil.getLocalHours(curDateObj, timezone);
  // local date
  const curLocalDateOfMonth = mutil.getLocalDate(curDateObj, timezone);

  // local 기준, 마지막으로 리셋한 날이 1970년1월1일0시 이후 몇번째 날인지.
  let localContentResetDays: number;
  // 지난 달의 날수. ex) 28, 29, 30, 31
  const numDaysInPreMonth = getNumDaysInPreMonth(curDateObj);

  if (curLocalDateOfMonth === contentsResetDate && curLocalHours >= contentsResetHour) {
    // 리셋 날짜와 현재 로컬 날짜가 같고 현재 로컬 시가 리셋시보다 크거나 같은 경우. 즉 오늘 리셋된 경우.
    localContentResetDays = curLocalDays;
  } else if (curLocalDateOfMonth === contentsResetDate) {
    // 리셋 날짜와 현재 로컬 날짜가 같지만 리셋시간 아직 안 지난 경우. 지난 달 리셋 날짜가 마지막 리셋 날짜.
    localContentResetDays = curLocalDays - numDaysInPreMonth;
  } else if (curLocalDateOfMonth > contentsResetDate) {
    // 이번 달에 이미 리셋되었고 리셋 날짜가 지난 경우.
    localContentResetDays = curLocalDays - curLocalDateOfMonth + contentsResetDate;
  } else {
    // 지난 달에 리셋된 경우.
    localContentResetDays =
      curLocalDays - numDaysInPreMonth + contentsResetDate - curLocalDateOfMonth;
  }

  const contentsResetTimeUtc =
    localContentResetDays * 86400 + contentsResetHour * 3600 + timeZoneOffsetSec;

  if (lastContentsTimeUtc >= contentsResetTimeUtc) {
    return false;
  }
  return true;
}

// -----------------------------------------------------------------------------
export function isContentsTimeUtcInHours(
  contentsTimeUtc: number,
  startHour: number,
  endHour: number,
  correctionSec?: number // 지연으로 인한 추가 보정 시간. (second)
) {
  correctionSec = correctionSec ? correctionSec : 0;

  const startDate = getDateOfSpecificLocalHourOfToday(contentsTimeUtc, startHour);
  const startTimeUtc = Math.floor(startDate.getTime() / 1000);

  const expireDate = getDateOfSpecificLocalHourOfToday(contentsTimeUtc, endHour);
  const endTimeUtc = Math.floor(expireDate.getTime() / 1000) + correctionSec;

  if (contentsTimeUtc >= startTimeUtc && contentsTimeUtc < endTimeUtc) {
    return true;
  }

  return false;
}

// -----------------------------------------------------------------------------
export function GetNextContentResetTimeUtc(curTimeUtc: number, contentsResetHour: number) {
  const localTime = curTimeUtc - mutil.getTimezoneOffset() * 60;
  const curDays = Math.floor(localTime / 86400);
  const curHours = Math.floor(localTime / 3600) % 24;
  let newContentResetDays = curDays;

  // 오늘 리셋되었고, 내일 리셋이 필요한 경우
  if (curHours >= contentsResetHour) {
    newContentResetDays = newContentResetDays + 1;
  }

  const nextContentResetTimeUtc = newContentResetDays * 86400 + contentsResetHour * 3600;

  return nextContentResetTimeUtc + mutil.getTimezoneOffset() * 60;
}

// -----------------------------------------------------------------------------
export function GetNextContentResetTimeByAddDays(
  curTimeUtc: number,
  addDays: number,
  contentsResetHour: number
) {
  assert(addDays >= 0);

  return GetNextContentResetTimeUtc(curTimeUtc, contentsResetHour) + addDays * SECONDS_PER_DAY;
}

// -----------------------------------------------------------------------------
export function GetNextContentsResetTimeByWeekly(
  curTimeUtc: number,
  contentsResetHour: number,
  contentsResetDay: number
) {
  const timeZoneOffsetSec = mutil.getTimezoneOffset() * 60;
  const curDateObj = new Date(curTimeUtc * 1000);

  // local 기준, 1970년1월1일0시 이후 몇번째 날인지.
  const curLocalDays = Math.floor((curTimeUtc - timeZoneOffsetSec) / 86400);
  // local hours
  const curLocalHours = mutil.getLocalHours(curDateObj);
  // local day
  const curLocalDayOfWeek = mutil.getLocalDay(curDateObj);

  let newContentResetDays: number;
  if (curLocalDayOfWeek === contentsResetDay && curLocalHours < contentsResetHour) {
    // 오늘 리셋이 필요한 경우
    newContentResetDays = curLocalDays;
  } else if (curLocalDayOfWeek < contentsResetDay) {
    // 이번주에 리셋 되야할 경우
    newContentResetDays = curLocalDays - curLocalDayOfWeek + contentsResetDay;
  } else {
    // 이번주는 리셋되었고, 다음 주에 리셋 되야할 경우
    newContentResetDays = curLocalDays - curLocalDayOfWeek + 7 + contentsResetDay;
  }

  const nextContentsResetTimeUtc =
    newContentResetDays * 86400 + contentsResetHour * 3600 + timeZoneOffsetSec;

  return nextContentsResetTimeUtc;
}

// -----------------------------------------------------------------------------
export function GetNextContentsResetTimeByMonthly(
  curTimeUtc: number,
  contentsResetHour: number,
  contentsResetDate: number
) {
  const timeZoneOffsetSec = mutil.getTimezoneOffset() * 60;
  const curDateObj = new Date(curTimeUtc * 1000);

  // local 기준, 1970년1월1일0시 이후 몇번째 날인지.
  const curLocalDays = Math.floor((curTimeUtc - timeZoneOffsetSec) / 86400);
  // local hours, 현재 시간 반환
  const curLocalHours = mutil.getLocalHours(curDateObj);
  // local date, 현재 날짜 반환
  const curLocalDateOfMonth = mutil.getLocalDate(curDateObj);
  // 현재 월의 일 수 반환
  const numDaysInCurMonth = mutil.getLocalDate(
    new Date(mutil.getLocalFullYear(curDateObj), curDateObj.getMonth() + 1, 0)
  );

  let newContentResetDays: number;
  if (curLocalDateOfMonth === contentsResetDate && curLocalHours < contentsResetHour) {
    // 오늘 리셋이 필요한 경우
    newContentResetDays = curLocalDays;
  } else if (curLocalDateOfMonth < contentsResetDate) {
    // 이번 달에 리셋이 되야할 경우
    newContentResetDays = curLocalDays - curLocalDateOfMonth + contentsResetDate;
  } else {
    // 다음 달에 리셋이 필요한 경우
    newContentResetDays =
      curLocalDays - curLocalDateOfMonth + numDaysInCurMonth + contentsResetDate;
  }

  const nextContentsResetTimeUtc =
    newContentResetDays * 86400 + contentsResetHour * 3600 + timeZoneOffsetSec;

  return nextContentsResetTimeUtc;
}

// -----------------------------------------------------------------------------
// local time 기준으로 contentsResetHour 를 지났으면 현재 요일, 그렇지 않으면 이전 요일을 반환
// @return 0: sunday, 1: monday
export function GetDayForContentsResetHourUsingLocalTime(
  curTimeUtc: number,
  contentsResetHour: number
): number {
  const SATURDAY = 6;
  const curDateObj = new Date(curTimeUtc * 1000);
  const localHour = mutil.getLocalHours(curDateObj);
  const localDay = mutil.getLocalDay(curDateObj);
  if (localHour < contentsResetHour) {
    const prevDay = localDay - 1;
    return prevDay < 0 ? SATURDAY : prevDay;
  }
  return localDay;
}

// -----------------------------------------------------------------------------
// local time 기준으로 1970년 1월1일 기준으로 몇 번째 주인지 반환
// 1970년 1월 1일 목요일의 반환 값은 0
// @pivotDay 기준이 되는 요일. 0: sunday, 1: monday
export function GetFullWeeksUsingLocalTime(
  curTimeUtc: number,
  pivotDay: number,
  inTimezone?: number
) {
  const timeZoneOffset = mutil.getTimezoneOffset(inTimezone) * 60;
  let pivotDayFactor;
  if (pivotDay <= 4) {
    pivotDayFactor = SECONDS_PER_DAY * (4 - pivotDay);
  } else {
    pivotDayFactor = SECONDS_PER_DAY * (4 - pivotDay + 7);
  }

  const modifiedLocalTimeUtc = curTimeUtc - timeZoneOffset + pivotDayFactor;

  return Math.floor(modifiedLocalTimeUtc / SECONDS_PER_WEEK);
}

// -----------------------------------------------------------------------------
export function GetElapssedMillisecFromThisWeek(pivotDay: number): number {
  const curDate = new Date();
  const curDay = mutil.getLocalDay(curDate);

  let pivotDateTime = new Date(
    mutil.getLocalFullYear(curDate),
    mutil.getLocalMonth(curDate),
    mutil.getLocalDate(curDate)
  ).getTime();

  mlog.verbose('[TEMP] GetElapssedMillisecFromThisWeek: 1', {
    curDay,
    pivotDay,
    curDate,
    pivotDateTime,
  });

  if (curDay > pivotDay) {
    pivotDateTime -= (curDay - pivotDay) * SECONDS_PER_DAY * 1000;
  } else if (curDay < pivotDay) {
    pivotDateTime -= (curDay - pivotDay + 7) * SECONDS_PER_DAY * 1000;
  }

  mlog.verbose('[TEMP] GetElapssedMillisecFromThisWeek: 2', {
    curDay,
    pivotDay,
    curDate,
    pivotDateTime,
  });

  return curDate.getTime() - mutil.convertLocalDateToUtcDate(new Date(pivotDateTime)).getTime();
}

// -----------------------------------------------------------------------------
export function GetElapssedDaysFromCmsDateStr(cmsDateStr: string, curTimeUtc: number) {
  const curDate = new Date(curTimeUtc * 1000);
  const tmpPivotDate = mutil.newDateByCmsDateStr(cmsDateStr);
  const pivotDate = new Date(
    mutil.getLocalFullYear(tmpPivotDate),
    mutil.getLocalMonth(tmpPivotDate),
    mutil.getLocalDate(tmpPivotDate)
  ); // 인자가 local time 라는 문제가 있음

  const elapsedMiliSecconds =
    mutil.convertUtcDateToTmpLocalDate(curDate).getTime() - pivotDate.getTime();
  const elapsedDays = Math.floor(elapsedMiliSecconds / SECONDS_PER_DAY / 1000);

  return elapsedDays;
}

// -----------------------------------------------------------------------------
// 이벤트 마지막 교환 시간
// -----------------------------------------------------------------------------
export function getUtcOfLastEventExchangeTime(endDateStr: string, remainHour: number): number {
  const endDate: Date = mutil.newDateByCmsDateStr(endDateStr);
  const endDateTimeUtc = endDate.getTime() / 1000;

  const addedTimeSecs = remainHour ? remainHour * SECONDS_PER_HOUR : 0;
  return endDateTimeUtc + addedTimeSecs;
}

// -----------------------------------------------------------------------------
// 오늘 특정 local 시간에 해당되는 Date 반환
export function getDateOfSpecificLocalHourOfToday(curTimeUtc: number, targetHour: number): Date {
  const curDate = new Date(curTimeUtc * 1000);
  const tmpLocalDate = mutil.convertUtcDateToTmpLocalDate(curDate);
  const tmpLocalDateZeroMin = new Date(
    tmpLocalDate.getUTCFullYear(),
    tmpLocalDate.getUTCMonth(),
    tmpLocalDate.getUTCDate(),
    targetHour
  );

  return mutil.convertLocalDateToUtcDate(tmpLocalDateZeroMin);
}

/*
  이벤트 과제가 오픈되고, 유저가 처음 로그인 한 이후에 실제 시간으로 하루가 지날 때마다 다음 날의 과제가 오픈됨
  7일임무에서 유저 로그인한 시간 기준 (userWeeklyStartTimeUtc)
  해당 임무 오픈날자(eventMissionCms.val == 2 (2일차))
*/
export function IsTodayWeeklyMission(
  curTimeUtc: number,
  userWeeklyEventStartTimeUtc: number,
  contentsResetHour: number,
  eventMissionCms: EventMissionDesc
): boolean {
  const timeZoneOffset = mutil.getTimezoneOffset() * 60;
  userWeeklyEventStartTimeUtc -= timeZoneOffset;

  // 첫째날 이벤트 이면 현재시간
  if (eventMissionCms.val === 1) {
    if (curTimeUtc - timeZoneOffset >= userWeeklyEventStartTimeUtc) {
      return true;
    } else {
      return false;
    }
  }

  // 둘째날부터 00:00:00  시간에 해당 이벤트가 오픈된다
  const eventDays = Math.floor(userWeeklyEventStartTimeUtc / 86400);
  const eventHours = Math.floor(userWeeklyEventStartTimeUtc / 3600) % 24;
  let eventMissionStartDays = eventDays + (eventMissionCms.val - 1);

  if (eventHours < contentsResetHour) {
    eventMissionStartDays = eventMissionStartDays - 1;
  }

  const eventMissionStartTime = eventMissionStartDays * 86400 + contentsResetHour * 3600;
  if (curTimeUtc - timeZoneOffset >= eventMissionStartTime) {
    return true;
  }
  return false;
}

// -----------------------------------------------------------------------------
// 지금까지 경과한 주(week)를 리턴
export function GetWeeklyContentsResetCount(
  curTimeUtc: number,
  contentsResetMin: number,
  contentsResetHour: number,
  contentsResetDay: number
): number {
  const timeZoneOffsetSec = mutil.getTimezoneOffset() * 60;
  const localDate = new Date((curTimeUtc - timeZoneOffsetSec) * 1000);

  // 금일 경과시간
  const elapsedSinceToday = localDate.getTime() % (SECONDS_PER_DAY * 1000);

  // 금일 시작날짜
  const todayStartDate = new Date(localDate.getTime() - elapsedSinceToday);

  // 이번 주 시작날짜
  const thisWeekStartTime = new Date(
    todayStartDate.getTime() - todayStartDate.getUTCDay() * SECONDS_PER_DAY * 1000
  );

  // 이번 주 리셋시간
  const thisWeekResetTime =
    thisWeekStartTime.getTime() +
    contentsResetDay * (SECONDS_PER_DAY * 1000) +
    contentsResetHour * (SECONDS_PER_HOUR * 1000) +
    contentsResetMin * (SECONDS_PER_MINUTE * 1000);

  // +1은 최초시작은 1주차부터라서
  let weeks = Math.floor(thisWeekResetTime / (SECONDS_PER_WEEK * 1000)) + 1;
  if (localDate.getTime() <= thisWeekResetTime) {
    weeks -= 1;
  }
  return weeks;
}

// -----------------------------------------------------------------------------
export function GetThisWeekTimeUtc(
  curTimeUtc: number,
  contentsDay: number,
  contentsHour: number,
  contentsMin: number
) {
  const timeZoneOffsetSec = mutil.getTimezoneOffset() * 60;
  const localDate = new Date((curTimeUtc - timeZoneOffsetSec) * 1000);

  // 금일 경과시간
  const elapsedSinceToday = localDate.getTime() % (SECONDS_PER_DAY * 1000);

  // 금일 시작날짜
  const todayStartDate = new Date(localDate.getTime() - elapsedSinceToday);

  // 이번 주 시작날짜
  const thisWeekStartTime = new Date(
    todayStartDate.getTime() - todayStartDate.getUTCDay() * SECONDS_PER_DAY * 1000
  );

  // 이번 주 리셋 시간
  let thisWeekTimeUtc =
    thisWeekStartTime.getTime() / 1000 +
    contentsDay * SECONDS_PER_DAY +
    contentsHour * SECONDS_PER_HOUR +
    contentsMin * SECONDS_PER_MINUTE;

  thisWeekTimeUtc += timeZoneOffsetSec;

  return thisWeekTimeUtc;
}

// -----------------------------------------------------------------------------
export function GetMonthlyContentsResetCount(
  curTimeUtc: number,
  contentsResetHour: number,
  contentsResetDate: number
) {
  const timeZoneOffsetSec = mutil.getTimezoneOffset() * 60;
  const localDate = new Date((curTimeUtc - timeZoneOffsetSec) * 1000);

  const startYear = START_DATE.getFullYear();
  const startMonth = START_DATE.getMonth();

  const localYear = localDate.getFullYear();
  const localMonth = localDate.getMonth();
  const localDay = localDate.getDate();
  const localHour = localDate.getHours();

  let elapsedMonths = (localYear - startYear) * 12 + (localMonth - startMonth);
  if (
    localDay < contentsResetDate ||
    (localDay === contentsResetDate && localHour < contentsResetHour)
  ) {
    elapsedMonths -= 1;
  }

  return elapsedMonths;
}

// -----------------------------------------------------------------------------
export function CalculateSailorDraftCost(
  userLevel: number,
  draftableSailor: number,
  draftSailorCount: number,
  overDraftedSailorCount: number,
  defaultSailorGold: number,
  sailorDraftingPriceModifierStat: number = 1000
) {
  // 일반 모집비용 계산
  const normalSailorToDraft = Math.min(draftableSailor, draftSailorCount);
  let normalPrice = normalSailorToDraft * defaultSailorGold;

  // 긴급 모집비용 계산
  const exceededSailorToDraft = draftSailorCount - normalSailorToDraft + overDraftedSailorCount;
  let overDraftPrice = exceededSailorToDraft * defaultSailorGold;

  const overCharge =
    (((exceededSailorToDraft + 1) * exceededSailorToDraft) / 2 -
      1 -
      (((overDraftedSailorCount + 2) * (overDraftedSailorCount + 1)) / 2 - 1)) *
      defaultSailorGold +
    defaultSailorGold;

  // https://wiki.line.games/pages/viewpage.action?pageId=28605582
  // 긴급모집 비용은 특정 선단 LV까지 할인
  const overChargeRate = Math.min(
    Math.min(cms.Const.SailorSaleCompanyLv.value - userLevel, 0) /
      -cms.Const.SailorSaleCompanyLv.value,
    1
  );
  overDraftPrice += Math.ceil(overCharge * overChargeRate);

  // 모집 비용은 일반 모집비용 + 긴급모집 비용 이다
  let ret = normalPrice + overDraftPrice;
  return Math.ceil((ret * sailorDraftingPriceModifierStat) / 1000);
}

// -----------------------------------------------------------------------------
export function GetInvestableMinMaxPoint(
  investVal1: number,
  investVal2: number,
  investVal3: number,
  investMaxVal: number,
  investMinLimit: number,
  accumPoint: number
) {
  const min = Math.min(
    investVal1 + investVal2 * Math.floor(accumPoint / investVal3),
    investMinLimit
  );
  const max = min * investMaxVal;
  return { min, max };
}

// -----------------------------------------------------------------------------
export function CalcSpareItemCountForShipRepair(shipTier: number) {
  return Math.ceil(
    cms.Const['ShipAddBuffClearItemCount'].value * cms.Const['ShipTierVariable' + shipTier].value
  );
}

// -----------------------------------------------------------------------------
export function CalcShipBlueprintBaseStat(statCms) {
  const ret = {};
  for (const stat of statCms) {
    ret[stat.Type] = stat.Val;
  }
  return ret;
}

// -----------------------------------------------------------------------------
// 부락에서 두캇으로  우호도 리턴
export function GetFriendshipToDucat(villageLevel: number, ducat: number): number {
  const conditionDucat: number = cms.Const.VillageFriendshipDucat.value * villageLevel * 100;
  const increaseFriendship = Math.floor(ducat / conditionDucat);
  return increaseFriendship;
}

// -----------------------------------------------------------------------------
// 부락에서 교역품으로  우호도 리턴
export function GetFriendshipToTradeGoods(
  village: VillageDesc,
  tradeGoods: { cmsId: number; count: number }[]
): number {
  let sumDucat: number = 0;

  // 좋아하는 무역품만  우호도를 올리면  기본 무역품을 줄 필요가 잇나?
  for (const elem of tradeGoods) {
    const tradeGoodsCms = cms.TradeGoods[elem.cmsId];
    // 부족 이 원하지 않는  교역품은 에러
    if (tradeGoodsCms.tradeGoodsCategory !== village.favorTradeGoodsCategory) {
      throw new MError('not-same-tradegoods-category', MErrorCode.NOT_SAME_TRADEGOODS_CATEGORY, {
        tradeGoods,
        village,
      });
    }
    sumDucat += tradeGoodsCms.basisDucat * elem.count * 10;
  }

  const increaseFriendship = Math.floor(
    sumDucat / (cms.Const.VillageFriendshipDucat.value * village.lv * 100)
  );
  return increaseFriendship;
}

// -----------------------------------------------------------------------------
export function CalcShipBlueprintSlotStat(bpSlotCms, shipSlotCms, userSlots, StatTypeNone) {
  const ret = {};
  for (const slot of bpSlotCms) {
    let slotCmsId = slot.Id;
    if (userSlots[slot.Index]) {
      slotCmsId = userSlots[slot.Index].shipSlotCmsId;
    }
    const slotStatCms = shipSlotCms[slotCmsId.toString()].stat;
    for (const stat of slotStatCms) {
      if (stat.Type === StatTypeNone) {
        continue;
      }
      if (!ret[stat.Type]) {
        ret[stat.Type] = 0;
      }
      ret[stat.Type] += stat.Val;
    }
  }
  return ret;
}

// -----------------------------------------------------------------------------
export function CalcShipBlueprintLevelStat(bpLvCms, level, StatTypeNone) {
  const ret = {};
  for (let i = 0; i <= level; i++) {
    const cms = bpLvCms[i.toString()];
    if (!cms) {
      continue;
    }
    for (const stat of cms.stat) {
      if (stat.Type === StatTypeNone) {
        continue;
      }
      if (ret[stat.Type] === undefined) {
        ret[stat.Type] = 0;
      }
      ret[stat.Type] += stat.Val;
    }
  }
  return ret;
}

// -----------------------------------------------------------------------------
export function CalcFleetHoldStat(shipHoldStats) {
  let sum = 0;
  for (const shipId of Object.keys(shipHoldStats)) {
    sum += shipHoldStats[shipId];
  }
  return sum;
}

// -----------------------------------------------------------------------------
// TODO] stat/fleetStat.ts 적용되면 이건 없애자.
export function CalcFleetBuildingStatByLanguage(
  languageLevel: number,
  buildingBuffCms,
  languageContentsTermsType: number
) {
  const stats: { [statType: number]: number } = {};
  for (const key of Object.keys(buildingBuffCms)) {
    const cms = buildingBuffCms[key];
    if (
      cms.contentsTermsId === languageContentsTermsType &&
      languageLevel === cms.contentsTermsCount
    ) {
      stats[cms.statType] = cms.statVal;
    }
  }
  return stats;
}

// -----------------------------------------------------------------------------
// 구매 원가 계산.
export function CalcTradeGoodsFirstPrice(
  basePrice: number,
  pricePercent: number,
  townCmsPercentPrice: number
): number {
  const price = Math.ceil((basePrice * pricePercent * townCmsPercentPrice) / 10000);
  return price;
}

// -----------------------------------------------------------------------------
export function CalcTradeGoodsPrice(
  basePrice: number,
  pricePercent: number,
  townCmsPercentPrice: number,
  buildingBuffStat: number,
  wpeAdd: number,
  statPct: number,
  tax: number
): number {
  const price = CalcTradeGoodsFirstPrice(basePrice, pricePercent, townCmsPercentPrice);

  const basePct = buildingBuffStat - statPct;
  const unlimitedBasePrice = Math.floor((price * basePct) / 1000) - wpeAdd;
  const minBasePrice = Math.ceil(price * cms.Const.TradeGoodsPriceDiscountLimitValue.value * 0.01);
  const base = Math.max(unlimitedBasePrice, minBasePrice);

  const final = Math.floor(base + base * tax);
  return final;
}

// -----------------------------------------------------------------------------
export function CalcTradeGoodsSaleFirstPrice(
  basePrice: number,
  pricePercent: number,
  bIsSellInTown: boolean,
  disCountRateIfSellingInTown: number
): number {
  // TODO jaykay (기본 가격 * 도시 가치 배수 * 권역 가치 배수 * 시세 * ( 1 - 관세 ))
  let price = (basePrice * pricePercent) / 100;
  if (bIsSellInTown) {
    price *= disCountRateIfSellingInTown / 100;
  }
  price = Math.floor(price);

  return price;
}

// -----------------------------------------------------------------------------
export function CalcTradeGoodsSalePrice(
  basePrice: number,
  pricePercent: number,
  bIsSellInTown: boolean,
  disCountRateIfSellingInTown: number,
  buildingBuffStat: number,
  pct: number,
  wpeAdd: number
): number {
  let price = (basePrice * pricePercent) / 100;
  if (bIsSellInTown) {
    price *= disCountRateIfSellingInTown / 100;
  }
  price = Math.floor(price);

  let totalPct = buildingBuffStat;
  if (pct) {
    totalPct += pct;
  }

  return Math.floor((price * totalPct) / 1000) + wpeAdd;
}

// -----------------------------------------------------------------------------
export function CalcBankWithdrawalFee(
  withdrawalAmount: number,
  bankWithdrawPerCms: number,
  withdrawlFeeModifierStat: number
): number {
  return Math.round((withdrawalAmount * bankWithdrawPerCms * withdrawlFeeModifierStat) / 1000);
}

// -----------------------------------------------------------------------------
// https://wiki.line.games/pages/viewpage.action?pageId=********
export function CalcDepartSupplyPrice(
  quantity: number,
  price: number,
  supplyPriceModifierStat: number,
  multiplyWpeStat: number,
  nationRankBonus: number
): number {
  let ret = quantity * Math.ceil((price * supplyPriceModifierStat) / 1000);
  if (nationRankBonus !== undefined) {
    ret -= Math.ceil((ret * nationRankBonus) / 1000);
  }
  ret = Math.ceil(ret * (1 + multiplyWpeStat / 1000));
  return ret;
}

// -----------------------------------------------------------------------------
export function CalcShipyardShipPrice(price: number, shipPriceModifierStat: number): number {
  return Math.ceil((price * shipPriceModifierStat) / 1000);
}

// -----------------------------------------------------------------------------
export function CalcShipyardRepairingPrice(
  ShipRepairGoldCms: number,
  durabilityValue: number,
  ShipRepairSizePerCms: number,
  repairingPriceModifierStat: number = 1000
): number {
  return Math.ceil(
    (Math.ceil(ShipRepairGoldCms * durabilityValue * ShipRepairSizePerCms) *
      repairingPriceModifierStat) /
      1000
  );
}

// -----------------------------------------------------------------------------
/*
  // 자재 갯수에 따라 내구도 수치 리턴
  // ! 현재 사용하지 않는 패킷 호출 함수
export function CalcShipDurabilityToRepairByLumber(lumber: number, shipSize: number): number {
  return Math.floor(lumber * cms.Const['ShipSize' + shipSize + 'RepairResourcesValue'].value);
}
*/

// -----------------------------------------------------------------------------
export function CalcShipRepairingLumberCost(durability: number, shipSize: number): number {
  return Math.ceil(
    ((durability * cms.Const.BattleRestoreCostForDurability.value) / 1000) *
      cms.Const['ShipRepairSizePer' + shipSize].value
  );
}

// -----------------------------------------------------------------------------
export function CalcShipyardShipSalePrice(
  price: number,
  shipSalePriceModifierStat: number
): number {
  return Math.ceil((price * shipSalePriceModifierStat) / 1000);
}

// -----------------------------------------------------------------------------
export function CalcTownDraftableSailor(
  cmsValue: number,
  draftableSailorAdditionStat: number
): number {
  return cmsValue + draftableSailorAdditionStat;
}

// -----------------------------------------------------------------------------
export function CalcPubMateRecruitingPrice(
  basePrice: number,
  bIsRenegotiation: boolean,
  mateRecruitingRetrySaleCms: number,
  mateRecruitingPriceModifierStat: number
): number {
  let price = basePrice;
  if (bIsRenegotiation) {
    price -= (basePrice * mateRecruitingRetrySaleCms) / 100;
  }
  price = Math.ceil((price * mateRecruitingPriceModifierStat) / 1000);
  return price;
}

// -----------------------------------------------------------------------------
export function CalcShopBuyingPrice(base: number, stat: number) {
  const price = Math.floor(base * stat * 0.001);
  return price;
}

// -----------------------------------------------------------------------------
export function CalcShopSellingPrice(base: number, stat: number) {
  const price = Math.floor(base * stat * 0.001);
  return price;
}

// -----------------------------------------------------------------------------
export function FoodOrWaterConsumePerDay(
  sailors: number,
  mates: number,
  foodPerSailor: number,
  foodPerMate: number,
  wpeAdded: number,
  wpeAddedPct: number
): number {
  const base = Math.ceil(sailors * foodPerSailor + mates * foodPerMate);
  const final = base + wpeAdded + (base * wpeAddedPct) / 1000;

  // 최소 1은 되야한다.
  return Math.max(Math.floor(final), 1);
}

// -----------------------------------------------------------------------------
export function FoodOrWaterConsumePerDay__(
  sailors: number,
  foodPerSailor: number,
  wpeAdded: number,
  wpeAddedPct: number, // 월드패시브 이펙트 75310003 or  75310003
  shipCount: number
): number {
  // ( 탑승 중인 선원 수 + 운용중인 선박 수) * 물_식량_소모_상수
  const base = (sailors + shipCount) * foodPerSailor;
  let final = (1 + wpeAddedPct / 1000) * base + wpeAdded;

  // 소수점 두자리까지는 보존.
  final = Math.floor(final * 100) / 100;
  // 최소 1은 되야한다.
  return Math.max(final, 1);
}

// -----------------------------------------------------------------------------
export function RemainSailDay(
  water: number,
  food: number,
  waterConsumePerDay: number,
  foodConsumePerDay: number
): number {
  return Math.floor(Math.min(water / waterConsumePerDay, food / foodConsumePerDay));
}

// -----------------------------------------------------------------------------
export function CalcPubDrinkPrice(price: number, priceModifierStat: number): number {
  return Math.ceil((price * priceModifierStat) / 1000);
}

// -----------------------------------------------------------------------------
export function CalcPubAddedIntimacyByDrink(cmsValue: number, additionStat: number): number {
  return cmsValue + additionStat;
}

// -----------------------------------------------------------------------------
export function CalcShipyardUpgradeBlueprint(
  blueprintLevel: number,
  blueprintPointValue: number,
  shipSize: number
): number {
  if (blueprintLevel === 1) {
    return blueprintPointValue;
  } else {
    return (
      Math.ceil(
        (Math.pow(
          cms.Const['ShipEnchantPer' + shipSize].value,
          blueprintLevel / cms.Const.ShipEnchantGoldPer.value
        ) *
          cms.Const.ShipEnchantGold.value) /
          100
      ) * 100
    );
  }
}

// -----------------------------------------------------------------------------
export function CalcTradeGoodsSellingExp(
  totalProfit: number,
  tradeExpLogBase: number,
  famousProfit: number,
  nationRankBonus: number,
  bIsSmuggle: boolean
): number {
  const constTradeBenefitExp = bIsSmuggle
    ? cms.Const.SmuggleExp.value
    : cms.Const.TradeBenefitExp.value;
  let nomalExp = 1;
  let famousExp = 0;

  if (famousProfit > 0) {
    famousExp = Math.floor(
      (((famousProfit * constTradeBenefitExp) / tradeExpLogBase) *
        cms.Const.TradeSpecialitiesExpRatio.value) /
        1000
    );
  }

  if (totalProfit > 0) {
    nomalExp = Math.floor((totalProfit * constTradeBenefitExp) / tradeExpLogBase);
  }

  let totalExp = nomalExp + famousExp;
  if (nationRankBonus) {
    totalExp = Math.floor((totalExp * nationRankBonus) / 1000);
  }
  return totalExp;
}

// -----------------------------------------------------------------------------
export function Log(base: number, x: number): number {
  return Math.log(x) / Math.log(base);
}

// -----------------------------------------------------------------------------
export function CalcReligionPrice(base: number, stat: number): number {
  return Math.ceil((base * stat) / 1000);
}

// -----------------------------------------------------------------------------
export function CanGetTownTradePriceOnWorldMap(
  distance: number,
  stat: number,
  constValue: number
): boolean {
  return distance <= stat * constValue;
}

// https://gist.github.com/tommyettinger/46a874533244883189143505d203312c
// export function SrandMulberry32 (seed: number) {
//   return function () {
//     let t = seed += 0x6D2B79F5;
//     t = Math.imul(t ^ t >>> 15, t | 1);
//     t ^= t + Math.imul(t ^ t >>> 7, t | 61);
//     return ((t ^ t >>> 14) >>> 0) / 4294967296;
//   };
// }

// -----------------------------------------------------------------------------
// http://www.pcg-random.org/posts/bob-jenkins-small-prng-passes-practrand.html
export function Srand(seed: number) {
  function jsf() {
    const e = s[0] - ((s[1] << 27) | (s[1] >>> 5));
    s[0] = s[1] ^ ((s[2] << 17) | (s[2] >>> 15));
    s[1] = s[2] + s[3];
    s[2] = s[3] + e;
    s[3] = s[0] + e;
    let ret = (s[3] >>> 0) / 4294967296;
    return Math.round(ret * 1000000000000) / 1000000000000;
  }
  seed >>>= 0;
  const s = [0xf1ea5eed, seed, seed, seed];
  for (let i = 0; i < 20; i++) {
    jsf();
  }
  return jsf;
}

// -----------------------------------------------------------------------------
let secondsPerGameMonth;
function GetSecondsPerGameMonth(seasonTimeScale: number): number {
  if (secondsPerGameMonth === undefined) {
    secondsPerGameMonth = (86400 / seasonTimeScale) * 30;
  }
  return secondsPerGameMonth;
}

// -----------------------------------------------------------------------------
// @return 1~12
export function GetGameMonth(seasonTimeScale: number, curTimeUtc: number): number {
  const gameMonths = Math.floor(curTimeUtc / GetSecondsPerGameMonth(seasonTimeScale));
  return (gameMonths % 12) + 1;
}

// -----------------------------------------------------------------------------
// @return base 0
export function GetGameYear(seasonTimeScale: number, curTimeUtc: number): number {
  const gameMonths = Math.floor(curTimeUtc / GetSecondsPerGameMonth(seasonTimeScale));
  return Math.floor(gameMonths / 12);
}

// -----------------------------------------------------------------------------
// It's for weather.
// return 1 ~ OceanTimeScale
export function GetOceanDay(
  seasonTimeScale: number,
  oceanTimeScale: number,
  curTimeUtc: number
): number {
  const factor = GetSecondsPerGameMonth(seasonTimeScale);
  return Math.floor(((curTimeUtc % factor) / factor) * oceanTimeScale) + 1;
}

// -----------------------------------------------------------------------------
// 클라이언트 루아의 _G.GetOceanTimeUtc 같은 함수.
export function GetOceanTimeUtc(oceanTimeScale: number, curTimeUtc: number): number {
  return (curTimeUtc * oceanTimeScale) % SECONDS_PER_DAY;
}

// -----------------------------------------------------------------------------
// Weather cms cache.
const cachedWeather: { [weatherTileCmsId: number]: { [yearMonth: string]: number[] } } = [];
export function GetCurWeatherCmsId(weatherTileCmsId: number, curTimeUtc: number): number {
  const constSeasonTimeScale = cms.Const.SeasonTimeScale.value;
  const constOceanTimeScale = cms.Const.OceanTimeScale.value;
  const year = GetGameYear(constSeasonTimeScale, curTimeUtc);
  const month = GetGameMonth(constSeasonTimeScale, curTimeUtc);
  const day = GetOceanDay(constSeasonTimeScale, constOceanTimeScale, curTimeUtc);
  const weatherTileClimateCmsId = cms.WeatherTile[weatherTileCmsId].weathertileclimateId;
  const climateIdIndex = R.clamp(0, 11, month - 1);
  const climateCmsId = cms.WeatherTileClimate[weatherTileClimateCmsId].climateId[climateIdIndex];
  const climateWeathersCms = cms.Climate[climateCmsId].weather;

  if (cachedWeather[weatherTileCmsId] === undefined) {
    cachedWeather[weatherTileCmsId] = {};
  }

  const yearMonth = year + ':' + month;
  if (cachedWeather[weatherTileCmsId][yearMonth] === undefined) {
    cachedWeather[weatherTileCmsId] = {};
    cachedWeather[weatherTileCmsId][yearMonth] = [];

    let totalRatio = 0;
    for (const elem of climateWeathersCms) {
      totalRatio += elem.Prob;
    }
    const seed = parseInt(
      (weatherTileCmsId % cmsEx.firstWeatherTileCmsId) + year.toString() + month,
      10
    );
    const rand = Srand(seed);

    for (let i = 0; i < constOceanTimeScale; i++) {
      const rnd = Math.floor(rand() * totalRatio);
      let curSumRatio = 0;
      for (const elem of climateWeathersCms) {
        curSumRatio += elem.Prob;
        if (rnd < curSumRatio) {
          cachedWeather[weatherTileCmsId][yearMonth].push(elem.Id);
          break;
        }
      }
    }
  }

  return cachedWeather[weatherTileCmsId][yearMonth][day - 1];
}

// -----------------------------------------------------------------------------
export function CalcRecoverInjuryCost(
  constFactor: number,
  gradeFactor: number,
  remainingSeconds: number
): number {
  return Math.ceil(constFactor * gradeFactor * Math.ceil(remainingSeconds / 60));
}

// -----------------------------------------------------------------------------
export function CalcIncreaseLoyaltyCost(
  base: number,
  baseMultiplier: number,
  increasedLoyalty: number,
  gradeFactor: number,
  levels: number[],
  loyaltyPayPerConst: number,
  loyaltyMaxLvConst: number
): number {
  let sumLevels = 0;
  for (const level of levels) {
    sumLevels += level;
  }

  const levelAvr = sumLevels / levels.length;
  const levelFactor = Math.floor(levelAvr) * ((levelAvr - 1) / loyaltyPayPerConst);

  let cost = ((base * baseMultiplier) / 100000) * increasedLoyalty * (gradeFactor + levelFactor);

  if (levelAvr < loyaltyMaxLvConst) {
    cost = Math.ceil(cost * (levelAvr / loyaltyMaxLvConst));
  } else {
    cost = Math.ceil(cost);
  }

  return cost;
}

// -----------------------------------------------------------------------------
export function CalcMateInjuryDuration(
  adventureLv: number,
  tradeLv: number,
  battleLv: number,
  wpeAddedInjuryPct: number
): number {
  // 항해사 Lv에 따른 부상 시간 보정
  const levelAvr = _.mean([adventureLv, tradeLv, battleLv]);
  const injuryCorrection = levelAvr / cms.Const.InjuryCharacterMaxLV.value;

  const injuryTimeMin = Math.ceil(cms.Const.InjuryTimeMin.value * injuryCorrection);
  const injuryTimeMax = Math.ceil(cms.Const.InjuryTimeMax.value * injuryCorrection);
  let duration = mutil.randIntInc(injuryTimeMin, injuryTimeMax);

  // 천분율
  duration += Math.ceil((duration * wpeAddedInjuryPct) / 1000);
  return Math.max(duration, cms.Const.InjuryFinalTimeMinSec.value);
}

// -----------------------------------------------------------------------------
export function CalcTradeFameFromSellFamouseTradeGoods(
  profit: number,
  constTradeSpeciality: number,
  constTradeSpecialitiesFame: number
): number {
  return Math.floor((profit * constTradeSpeciality) / constTradeSpecialitiesFame);
}

// -----------------------------------------------------------------------------
function _CalcDucatExchangeRatio1(ducat: number): number {
  const constCms = cms.Const;
  const ratio =
    constCms.DucatRedgemExchangeVal.value *
    Math.pow(
      constCms.DucatRedgemExchangePer.value / 100,
      Log(10, ducat / constCms.DucatRedgemExchangeDividePer.value)
    );

  mlog.verbose('CalcDucatExchangeRatio1', {
    ducat,
    ratio,
    DucatRedgemExchangeVal: constCms.DucatRedgemExchangeVal.value,
    DucatRedgemExchangePer: constCms.DucatRedgemExchangePer.value,
    DucatRedgemExchangeDividePer: constCms.DucatRedgemExchangeDividePer.value,
    DucatRedgemExchangeValMax: constCms.DucatRedgemExchangeValMax.value,
  });

  if (ratio >= constCms.DucatRedgemExchangeValMax.value) {
    return constCms.DucatRedgemExchangeValMax.value;
  }
  return ratio;
}

// -----------------------------------------------------------------------------
function _CalcDucatExchangeRatio2(ducat: number): number {
  const constCms = cms.Const;
  const ratio =
    constCms.DucatRedgemExchangeVal2.value *
    Math.pow(
      constCms.DucatRedgemExchangePer2.value / 100,
      Log(10, ducat / constCms.DucatRedgemExchangeDividePer2.value)
    );

  mlog.verbose('CalcDucatExchangeRatio2', {
    ducat,
    ratio,
    DucatRedgemExchangeVal2: constCms.DucatRedgemExchangeVal2.value,
    DucatRedgemExchangePer2: constCms.DucatRedgemExchangePer2.value,
    DucatRedgemExchangeDividePer2: constCms.DucatRedgemExchangeDividePer2.value,
    DucatRedgemExchangeValMax2: constCms.DucatRedgemExchangeValMax2.value,
  });

  if (ratio >= constCms.DucatRedgemExchangeValMax2.value) {
    return constCms.DucatRedgemExchangeValMax2.value;
  }
  return ratio;
}

// -----------------------------------------------------------------------------
// 환전 비율 공식 변경(https://wiki.line.games/pages/viewpage.action?pageId=38247197#id-%EC%9E%AC%ED%99%94%ED%99%98%EC%A0%84-%ED%99%98%EC%A0%84%EC%BC%80%EC%9D%B4%EC%8A%A4)
export function CalcDucatExchangeRatio(ducat: number): number {
  const ratio1 = _CalcDucatExchangeRatio1(ducat);
  const ratio2 = _CalcDucatExchangeRatio2(ducat);

  return Math.max(ratio1, ratio2);
}

// -----------------------------------------------------------------------------
export function CalcRandomQuestRewardUserLevelRatio(userLevel: number) {
  return (
    Math.sqrt(userLevel * cms.Const.QuestRewardCompanyLvA.value) +
    Math.log10(userLevel * cms.Const.QuestRewardCompanyLvB.value)
  );
}

// -----------------------------------------------------------------------------
export function CalcCashShopBuffUpgradeCostPerSec(
  newCashShopCms: CashShopDesc,
  oldCashShopCms: CashShopDesc,
  remainingSec: number
): number {
  if (ValidateCashShopBuffDurationTimes(newCashShopCms, oldCashShopCms) === false) {
    throw new MError('not-match-duration-times', MErrorCode.NOT_MATCH_DURATION_TIMES, {
      newCashShopDurationDays: newCashShopCms.durationDays,
      newCashShopDurationHours: newCashShopCms.durationHours,
      oldCashShopDurationDays: oldCashShopCms.durationDays,
      oldCashShopDurationHours: oldCashShopCms.durationHours,
    });
  }

  const newCashShopTime = newCashShopCms.durationDays
    ? newCashShopCms.durationDays
    : newCashShopCms.durationHours;
  const oldCashShopTime = oldCashShopCms.durationDays
    ? oldCashShopCms.durationDays
    : oldCashShopCms.durationHours;
  const secondsPerDayOrHour = newCashShopCms.durationDays ? SECONDS_PER_DAY : SECONDS_PER_HOUR;

  return Math.ceil(
    ((newCashShopCms.salePointVal / newCashShopTime -
      oldCashShopCms.salePointVal / oldCashShopTime) /
      secondsPerDayOrHour) *
      remainingSec
  );
}

// -----------------------------------------------------------------------------
export function CalcCashShopBuffUpgradeCostPerMinute(
  newCashShopCms: CashShopDesc,
  oldCashShopCms: CashShopDesc,
  remainingMin: number
): number {
  if (remainingMin === 0) {
    return cms.Const.CashShopBuffMinPrice.value;
  }

  if (ValidateCashShopBuffDurationTimes(newCashShopCms, oldCashShopCms) === false) {
    throw new MError('not-match-duration-times', MErrorCode.NOT_MATCH_DURATION_TIMES, {
      newCashShopDurationDays: newCashShopCms.durationDays,
      newCashShopDurationHours: newCashShopCms.durationHours,
      oldCashShopDurationDays: oldCashShopCms.durationDays,
      oldCashShopDurationHours: oldCashShopCms.durationHours,
    });
  }

  const newCashShopTime = newCashShopCms.durationDays
    ? newCashShopCms.durationDays
    : newCashShopCms.durationHours;
  const oldCashShopTime = oldCashShopCms.durationDays
    ? oldCashShopCms.durationDays
    : oldCashShopCms.durationHours;
  const minutesPerDayOrHour = newCashShopCms.durationDays ? MINUTES_PER_DAY : MINUTES_PER_HOUR;

  return Math.ceil(
    ((newCashShopCms.salePointVal / newCashShopTime -
      oldCashShopCms.salePointVal / oldCashShopTime) /
      minutesPerDayOrHour) *
      remainingMin
  );
}

function ValidateCashShopBuffDurationTimes(
  newCashShopCms: CashShopDesc,
  oldCashShopCms: CashShopDesc
) {
  if (
    (newCashShopCms.durationDays && newCashShopCms.durationHours) ||
    (oldCashShopCms.durationDays && oldCashShopCms.durationHours)
  ) {
    return false;
  }

  if (
    (newCashShopCms.durationDays && oldCashShopCms.durationDays) ||
    (newCashShopCms.durationHours && oldCashShopCms.durationHours)
  ) {
    return true;
  }

  return false;
}

// -----------------------------------------------------------------------------
export function CalcCashShopQuestPassWithMateCost(
  questPassPrice: number,
  admiralPrice: number
): number {
  return Math.ceil(
    ((questPassPrice + admiralPrice) * (100 - cms.Const.QuestPassDiscountPer.value)) / 100
  );
}

// -----------------------------------------------------------------------------
// https://wiki.line.games/pages/viewpage.action?pageId=55305886
export function CalcAdventureLandExploreRewardDucatPctStat(
  firstStatValue: number,
  companyLevel: number
): number {
  // todo const
  return Math.floor((firstStatValue / 10 / 100) * 0.3 + (companyLevel / 100) * 0.7 * 0.2);
}

// -----------------------------------------------------------------------------
// npc의 교섭 보상
export function CalcNpcNegoReward(npcCmsId: number): number {
  const npcCms = getOceanNpcCms()[npcCmsId];
  return npcCms.negotiateDucatReward;
}

// -----------------------------------------------------------------------------
// 유저가 지불해야하는 항복 비용
export function CalcUserSurrenderCost(curDucat: number, userLevel: number): number {
  curDucat = Math.max(0, curDucat); // 보유량이 음수인 경우 0인 것처럼 처리

  return Math.min(
    Math.floor((curDucat * cms.Const.encountSurrenderDucateRate.value) / 1000),
    userLevel * cms.Const.LossOfDucatByLevel.value
  );
}

// -----------------------------------------------------------------------------
// 유저가 지불해야하는 교섭 비용
export function CalcUserNegoCost(level: number): number {
  const mutiplyPoint = cms.Const.BattleEncounterNegotiateMutiplyPoint1.value;
  return Math.ceil((Math.pow(level, 1.5) * mutiplyPoint) / 1000) * 1000;
}

// -----------------------------------------------------------------------------
// 난파선  부활시  사용되는 재화 비용
export function CalcWreckResurrectionCost(shipCmsId: number): number {
  const cost = cms.Const.OceanWreckresurrectionPointValue.value;
  const shipCms = cms.Ship[shipCmsId];
  return Math.ceil(cost * cms.Const['ShipRepairSizePer' + shipCms.shipSize].value);
}

// -----------------------------------------------------------------------------
// 시간에 따라 줄어든 카르마 계산
export function CalcKarma(
  uncalculatedKarma: number,
  lastUpdateTimeUtc: number,
  curTimeUtc: number
): number {
  const ret = CalcKarmaAndGetDecreaseTimesEx(uncalculatedKarma, lastUpdateTimeUtc, curTimeUtc);
  if (!ret) {
    return uncalculatedKarma;
  }
  return ret.karma;
}

// -----------------------------------------------------------------------------
// 시간에 따라 줄어든 카르마와 감소 횟수 리턴
// 레거시 코드 CalcKarmaAndGetDecreaseTimesEx로 대체 됨.
// https://wiki.line.games/pages/viewpage.action?pageId=45781083
export function CalcKarmaAndGetDecreaseTimes(
  uncalculatedKarma: number,
  lastUpdateTimeUtc: number,
  curTimeUtc: number
): {
  karma: number;
  decreaseTimes: number;
  //* 단순하게 계산한 횟수.(카르마가 최소치인 경우 고려하지 않은 것 유의)
} {
  // 몇 번 감소 되는지 계산.
  const timeElapsing = curTimeUtc - lastUpdateTimeUtc;
  const recycleSecs = cms.Const.KarmaDecreaseHour.value * SECONDS_PER_HOUR;
  const decreaseTimes = Math.floor(timeElapsing / recycleSecs);
  if (decreaseTimes === 0) {
    return null;
  }

  // 감소량 계산.
  let estimatingKarma = uncalculatedKarma;
  let decreaseTimesForCriminal = 0; // 범죄 상태로 차감된 횟수

  // 범죄 상태일 때 계산
  const karmaForCriminal = estimatingKarma - cms.Const.CriminalKarmaCutLine.value;
  if (karmaForCriminal >= 0) {
    const calcDecreaseTimesAndValForCriminal = (decreaseVal: number) => {
      const times = Math.min(
        decreaseTimes,
        karmaForCriminal === 0
          ? 1 // CriminalKarmaCutLine '이상'
          : Math.ceil(karmaForCriminal / decreaseVal)
      );
      return {
        times,
        val: times * decreaseVal,
      };
    };

    // 해적 상태일 때 계산
    const { times, val: decreaseValForCriminal } = calcDecreaseTimesAndValForCriminal(
      cms.Const.PirateKarmaDecreaseValue.value
    );
    // TODO 배신자일 경우 처리.

    decreaseTimesForCriminal = times;
    estimatingKarma = Math.max(
      cms.Const.UserMinKarma.value,
      estimatingKarma - decreaseValForCriminal
    );
  }

  // 일반 상태일 때 계산
  {
    const decreaseTimesForNormal = decreaseTimes - decreaseTimesForCriminal;
    const decreaseValForNormal = decreaseTimesForNormal * cms.Const.KarmaDecreaseValue.value;
    estimatingKarma = Math.max(
      cms.Const.UserMinKarma.value,
      estimatingKarma - decreaseValForNormal
    );
  }

  return {
    karma: estimatingKarma,
    decreaseTimes,
  };
}

// -----------------------------------------------------------------------------
// Get the karma cms table based on the current karma point
// -----------------------------------------------------------------------------
export function getKarmaDescBasedOnKarmaVal(curValue: number): KarmaDesc {
  let myKarmaDesc: KarmaDesc = null;
  _.forOwn(cms.Karma, (karmaDesc) => {
    if (karmaDesc.karmaType === KARMA_TYPE.OTHER_LAND) {
      if (karmaDesc.karmaMinValue <= curValue && karmaDesc.karmaMaxValue >= curValue) {
        myKarmaDesc = karmaDesc;
      }
    }
  });
  return myKarmaDesc;
}

// -----------------------------------------------------------------------------
// 시간에 따라 줄어든 카르마와 감소 횟수 리턴
// https://wiki.line.games/pages/viewpage.action?pageId=124187679
export function CalcKarmaAndGetDecreaseTimesEx(
  uncalculatedKarma: number,
  lastUpdateTimeUtc: number,
  curTimeUtc: number
): {
  karma: number;
  decreaseTimes: number;
} {
  // 몇 번 감소 되는지 계산.
  const timeElapsing = curTimeUtc - lastUpdateTimeUtc;
  const recycleSecs = cms.Const.KarmaDecreaseHour.value * SECONDS_PER_HOUR;
  const decreaseTimes = Math.floor(timeElapsing / recycleSecs);
  if (decreaseTimes === 0) {
    return null;
  }

  // 감소량 계산.
  let myKarmaDesc: KarmaDesc = getKarmaDescBasedOnKarmaVal(uncalculatedKarma);
  if (!myKarmaDesc) {
    return null;
  }

  let decreaseValue: number = myKarmaDesc.natureDecreaseValue * decreaseTimes;
  const newKarma = Math.max(cms.Const.UserMinKarma.value, uncalculatedKarma - decreaseValue);

  return {
    karma: newKarma,
    decreaseTimes,
  };
}

// -----------------------------------------------------------------------------
// Convert angle in radians to degrees
export const toDegrees = (radians: number) => (radians * 180) / Math.PI;
// Convert angle in degrees to radians
export const toRadians = (degrees: number) => (degrees * Math.PI) / 180;

// Conver game hour to real second
export const SECONDS_PER_MINUTE = 60;
export const MINUTES_PER_HOUR = 60;
export const SECONDS_PER_HOUR = SECONDS_PER_MINUTE * MINUTES_PER_HOUR;
export const HOURS_PER_DAY = 24;
export const SECONDS_PER_DAY = SECONDS_PER_HOUR * HOURS_PER_DAY;
export const MINUTES_PER_DAY = MINUTES_PER_HOUR * HOURS_PER_DAY;
export const DAYS_PER_WEEK = 7;
export const SECONDS_PER_WEEK = SECONDS_PER_DAY * DAYS_PER_WEEK;
export const DAYS_PER_MONTH = 30;
export const SECONDS_PER_MONTH = SECONDS_PER_DAY * DAYS_PER_MONTH;
export const MONTHS_PER_YEAR = 12;
export const SECONDS_PER_YEAR = SECONDS_PER_MONTH * MONTHS_PER_YEAR;
export const GameHour2RealSecond = (timeScale: number) => SECONDS_PER_HOUR / timeScale;
export const START_DATE = new Date(0);

export const KNOT2KMPH = 1.852;
export const knot2kmph = (knot: number) => knot * KNOT2KMPH;
export const kmh2knot = (kmh: number) => kmh / KNOT2KMPH;
export const knot2Spd = (earthScale: number, oceanTimeScale: number, knot: number): number => {
  return (knot * KNOT2KMPH * oceanTimeScale) / SECONDS_PER_HOUR / earthScale;
};
export const Spd2Knot = (earthScale: number, oceanTimeScale: number, spd: number): number => {
  return (spd * SECONDS_PER_HOUR * earthScale) / KNOT2KMPH / oceanTimeScale;
};

// -----------------------------------------------------------------------------
export function GetMinKnot(): number {
  return cms.Define.OceanSailingMinKnot;
}

export function GetSoftCapKnot(): number {
  return cms.Define.OceanSailingKnotSoftCap;
}

// -----------------------------------------------------------------------------
// 항해사 전문지식 경험치 계산
// -----------------------------------------------------------------------------
export function SpecialStatExp(
  keyStatVal: number, // 전문지식 타입에 따른 key stat
  mateFortune: number, // 운
  gradeWeight: number, // 등급별 가중치
  jobLevel: number, // 직업 레벨
  jobMult: number // 직업 배수
): number {
  let ssVal =
    (keyStatVal * 0.4 + mateFortune * 0.2) *
    (gradeWeight / 10) *
    (1 + jobLevel / 10) *
    (jobMult / 600);
  return Math.max(0, Math.floor(ssVal));
}

// -----------------------------------------------------------------------------
// 항해사의 기본 스탯에 (Mate 테이블의 'stat') 승급 레벨 적용.
// https://wiki.line.games/pages/viewpage.action?pageId=70950958
// -----------------------------------------------------------------------------
export function CalcMateBaseStat(statVal: number, awakenLevel: number): number {
  const val = statVal / 2 + (statVal / 10) * (awakenLevel + 1);
  return Math.round(val);
}

// -----------------------------------------------------------------------------
// 전투 스탯 계산 공식들.
// -----------------------------------------------------------------------------

// -----------------------------------------------------------------------------
export function CalcBattleCannonAttackStat(
  ssLevelCannon: number,
  numGunport: number,
  captainBattleLevel: number,
  bact: number, // 1022 BATTLE_ADDED_CANNON_ATTACK
  bactPct: number // 1023 BATTLE_ADDED_CANNON_ATTACK_PCT
): number {
  const cannonAttack =
    (captainBattleLevel * 4 * (1 + numGunport * 0.1) + bact) * (1 + bactPct * 0.001);

  return Math.floor(cannonAttack);
}

// -----------------------------------------------------------------------------
export function CalcBattleCannonDefenseStat(
  ssLevelCannon: number,
  captainBattleLevel: number,
  bacd: number, // 1024 BATTLE_ADDED_CANNON_DEFENSE
  bacdPct: number, // 1025 BATTLE_ADDED_CANNON_DEFENSE_PCT
  bscd: number // 1114 BATTLE_SHIP_CANNON_DEFENSE
): number {
  const cannonDefense = (captainBattleLevel * 4 + (bscd + bacd)) * (1 + bacdPct * 0.001);
  return Math.floor(cannonDefense);
}

// -----------------------------------------------------------------------------
export function CalcBattleCannonAccuracyStat(
  ssLevelCannon: number,
  captainBattleLevel: number,
  baca: number, // 1026 BATTLE_ADDED_CANNON_ACCURACY
  bacaPct: number // 1027 BATTLE_ADDED_CANNON_ACCURACY_PCT
): number {
  const val = (captainBattleLevel * 12 + baca) * (2 + bacaPct * 0.001);
  return Math.floor(val);
}

// -----------------------------------------------------------------------------
export function CalcBattleCannonDodgeStat(
  ssLevelCannon: number,
  captainBattleLevel: number,
  bacdr: number, // 1031 BATTLE_ADDED_CANNON_DODGE_RATING
  bacdrPct: number // 1032 BATTLE_ADDED_CANNON_DODGE_RATING_PCT
): number {
  const cannonDodge = (captainBattleLevel * 12 + bacdr) * (1 + bacdrPct * 0.001);
  return Math.floor(cannonDodge);
}

// -----------------------------------------------------------------------------
export function CalcBattleMeleeAttackStat(
  ssLevelMelee: number,
  humanAttack: number, // 1002 BATTLE_HUMAN_ATTACK
  captainBattleLevel: number,
  bama: number, // 1037 BATTLE_ADDED_MELEE_ATTACK
  bamaPct: number, // 1038 BATTLE_ADDED_MELEE_ATTACK
  bca: number // 1122 BATTLE_CEQUIP_ATTACK
): number {
  const meleeAttack =
    ((captainBattleLevel * 9 * (1 + humanAttack * 0.1) + (bama + bca * 5)) *
      (1 + bamaPct * 0.001)) /
    4.5;

  return Math.floor(meleeAttack);
}

// -----------------------------------------------------------------------------
export function CalcBattleMeleeDefenseStat(
  ssLevelMelee: number,
  captainBattleLevel: number,
  bamd: number, // 1039 BATTLE_ADDED_MELEE_DEFENSE
  bamdPct: number, // 1040 BATTLE_ADDED_MELEE_DEFENSE_PCT
  bsmd: number, // 1115 BATTLE_SHIP_MELEE_DEFENSE
  bcd: number // 1123 BATTLE_CEQUIP_DEFENSE
): number {
  const meleeDefense =
    ((captainBattleLevel * 9 + (bsmd + bamd + bcd * 5)) * (1 + bamdPct * 0.001)) / 4.5;
  return Math.floor(meleeDefense);
}

// -----------------------------------------------------------------------------
export function CalcBattleMeleeAccuracyStat(
  ssLevelMelee: number,
  captainBattleLevel: number,
  bama: number, // 1041 BATTLE_ADDED_MELEE_ACCURACY
  bamaPct: number // 1042 BATTLE_ADDED_MELEE_ACCURACY_PCT
): number {
  const val = (captainBattleLevel * 12 + bama / 3.5) * (2 + bamaPct * 0.001);
  return Math.floor(val);
}

// -----------------------------------------------------------------------------
export function CalcBattleMeleeDodgeRatingStat(
  ssLevelMelee: number,
  captainBattleLevel: number,
  bamdr: number, // 1046 BATTLE_ADDED_MELEE_DODGE_RATING
  bamdrPct: number // 1047 BATTLE_ADDED_MELEE_DODGE_RATING_PCT
): number {
  const meleeDodge = (captainBattleLevel * 12 + bamdr) * (1 + bamdrPct * 0.001);
  return Math.floor(meleeDodge);
}

// -----------------------------------------------------------------------------
export function CalcBattleRammingAttackStat(
  ssLevelRamming: number,
  captainBattleLevel: number,
  rammingPower: number, // 1000 BATTLE_RAMMING_POWER
  bara: number, // 1052 BATTLE_ADDED_RAMMING_ATTACK
  baraPct: number // 1053 BATTLE_ADDED_RAMMING_ATTACK
): number {
  const rammingAttack =
    (captainBattleLevel * 4 * (1 + rammingPower * 0.1) + bara) * (1 + baraPct * 0.001);

  return Math.floor(rammingAttack);
}

// -----------------------------------------------------------------------------
export function CalcBattleCannonCritical(
  ssLevelCannon: number,
  captainBattleLevel: number,
  bacc: number, // 1028 (BATTLE_ADDED_CANNON_CRITICAL)
  baccPct: number // 1029 (BATTLE_ADDED_CANNON_CRITICAL_PCT)
): number {
  const val = (captainBattleLevel * 12 + bacc) * (1 + baccPct * 0.0015);
  return Math.floor(val);
}

// -----------------------------------------------------------------------------
export function CalcBattleMeleeCriticalStat(
  ssLevelMelee: number,
  captainBattleLevel: number,
  bamc: number, // 1043 BATTLE_ADDED_MELEE_CRITICAL
  bamcPct: number // 1044 (BATTLE_ADDED_MELEE_CRITICAL_PCT)
): number {
  const val = (captainBattleLevel * 12 + bamc / 2.84) * (1 + bamcPct * 0.0015);
  return Math.floor(val);
}

// -----------------------------------------------------------------------------
export function CalcBattleRammingDefenseStat(
  ssLevelRamming: number,
  captainBattleLevel: number,
  bard: number, // 1086 BATTLE_ADDED_RAMMING_DEFENSE
  bardPct: number, // 1087 BATTLE_ADDED_RAMMING_DEFENSE_PCT
  bsrd: number // 1116 BATTLE_SHIP_RAMMING_DEFENSE
): number {
  const rammingDefense = (captainBattleLevel * 4 + (bsrd + bard)) * (1 + bardPct * 0.001);
  return Math.floor(rammingDefense);
}

// -----------------------------------------------------------------------------
export function CalcBattleRammingAccuracyStat(
  ssLevelRamming: number,
  captainBattleLevel: number,
  bara: number, // 1088 BATTLE_ADDED_RAMMING_ACCURACY
  baraPct: number // 1089 BATTLE_ADDED_RAMMING_ACCURACY_PCT
): number {
  const rammingAccuracy = (captainBattleLevel * 12 + bara) * (1 + baraPct * 0.001);
  return Math.floor(rammingAccuracy);
}

// -----------------------------------------------------------------------------
export function CalcBattleRammingDodgeRatingStat(
  ssLevelRamming: number,
  captainBattleLevel: number,
  bard: number, // 1093 BATTLE_ADDED_RAMMING_DODGE
  bardPct: number // 1093 BATTLE_ADDED_RAMMING_DODGE
): number {
  const rammingDodgeRating = (captainBattleLevel * 12 + bard) * (1 + bardPct * 0.001);
  return Math.floor(rammingDodgeRating);
}

// -----------------------------------------------------------------------------
export function CalcBattleRammingCriticalRatingStat(
  ssLevelRamming: number,
  captainBattleLevel: number,
  barcr: number, // 1090 BATTLE_ADDED_RAMMING_CRITICAL_RATING
  barcrPct: number // 1091 BATTLE_ADDED_RAMMING_CRITICAL_RATING_PCT
): number {
  const rammingCritRating = (captainBattleLevel * 12 + barcr) * (1 + barcrPct * 0.0015);
  return Math.floor(rammingCritRating);
}

// -----------------------------------------------------------------------------
// 전투 종료 후 획득 경험치 계산식
// https://wiki.line.games/pages/viewpage.action?pageId=7898079#id-%EC%BA%90%EB%A6%AD%ED%84%B0%EA%B2%BD%ED%97%98%EC%B9%98-%EC%A0%84%ED%88%AC%EA%B2%BD%ED%97%98%EC%B9%98
// -----------------------------------------------------------------------------

// 기여도 순위로 인한 경험치 보너스
const _ExpRankBonus = [
  0.2, // 1st
  0.15,
  0.1,
  0.05,
  0.03,
  0.02, // 6th
  0,
];

export function CalcBattleExpAmount(
  baseExp: number, // 기본 경험치 (OceanNpc.exp)
  rankNum: number, // 기여도 순위 (0 ~ 6)
  levelDiff: number, // 레벨 차이 (유저레벨 - 대상레벨.  음수 가능)
  jobPenalty: number, // 항해사의 탑승슬롯/직업 패널티 보정 수치. (1 이면 없음)
  nationRankBonus: number // // 국가 순위 보너스 보정 수치 (1000 이면 없음)
): number {
  const rankBonus = rankNum >= _ExpRankBonus.length ? 0 : _ExpRankBonus[rankNum];
  let expAmount = baseExp + baseExp * rankBonus;

  if (levelDiff === 5) {
    expAmount *= 1;
  } else if (levelDiff === 6) {
    expAmount *= 0.95;
  } else if (levelDiff === 7) {
    expAmount *= 0.9;
  } else if (levelDiff === 8) {
    expAmount *= 0.85;
  } else if (levelDiff > 8) {
    expAmount *= 0.8;
  }

  if (nationRankBonus) {
    expAmount = Math.floor((expAmount * nationRankBonus) / 1000);
  }

  expAmount *= jobPenalty;

  return Math.floor(expAmount);
}

// -----------------------------------------------------------------------------
// https://docs.google.com/spreadsheets/d/1gAwXJyN8PwMayIN-8L74xFxAnfJErgo6QO7v9c-NhFQ/edit#gid=1406253444
// 시간 가속 컨텐츠에서 사용하는 즉시 완료 포인트 비용을 계산
// ( 포인트 CMS ID 는 CMS.Const.TimeCostPerMinPointType 참조 )
// -----------------------------------------------------------------------------
export function CalcImmediateCompletionTimeCost(remainingTimeSec: number): number {
  // 일정 시간 이하면 무료
  if (remainingTimeSec <= cms.Const.TimeCostFreeTimeSec.value) {
    return 0;
  }
  // 1분 미만의 초는 버림
  const remainingTimeMin = Math.floor(remainingTimeSec / SECONDS_PER_MINUTE);
  // 시간 비용
  const baseCost = remainingTimeMin * cms.Const.TimeCostPerMinPointVal.value;
  // 할인 비용
  const discount = Math.floor(remainingTimeMin / cms.Const.TimeCostSalePer.value);
  return Math.max(0, baseCost - discount);
}

// -----------------------------------------------------------------------------
// 탁송비용 변경: https://wiki.line.games/pages/viewpage.action?pageId=108519522
export function CalcShipDeliverDistanceCost(distance: number, bpTier: number): number {
  const kmDistance = Math.floor(distance * EarthScale);
  const tierCost = cms.Const['ConsignmentFactorShipSize' + bpTier].value;
  const cost =
    cms.Const.ConsignmentDefaultPayment.value +
    Math.ceil(
      cms.Const.ConsignmentPaymentPerUnit.value * (tierCost / 1000) * Math.ceil(kmDistance / 100)
    );

  return Math.min(cost, cms.Const.ConsignmentMaxPayment.value);
}

// -----------------------------------------------------------------------------
export function CalcLandFeatureRatio(userStat: number, landFeatureStat: number): number {
  const diffStat = userStat - landFeatureStat;
  const ratio =
    1 /
    (cms.Const.AdventureEventFormulaMC.value +
      Math.exp(
        -cms.Const.AdventureEventFormulaSlope.value *
          ((diffStat - cms.Const.AdventureEventFormulaMid.value) /
            cms.Const.AdventureEventFormulaDivider.value)
      ));

  return ratio;
}

// -----------------------------------------------------------------------------
const numToBits: number[] = [0, 1, 1, 2, 1, 2, 2, 3, 1, 2, 2, 3, 2, 3, 3, 4];
export function CalcCountSetBitsRecursive(num: number): number {
  if (num === 0) {
    return 0;
  }

  const nibble = num & 0xf;
  return numToBits[nibble] + CalcCountSetBitsRecursive(num >>> 4);
}

// -----------------------------------------------------------------------------
export function shuffleReportItems(array: PreferenceItem[], seed: number) {
  let currentIndex = array.length;
  let temporaryValue;
  let randomIndex;

  const rand = Srand(seed);
  while (0 !== currentIndex) {
    randomIndex = Math.floor(rand() * currentIndex);
    currentIndex -= 1;
    // And swap it with the current element.
    temporaryValue = array[currentIndex];
    array[currentIndex] = array[randomIndex];
    array[randomIndex] = temporaryValue;
  }
  return array;
}

// -----------------------------------------------------------------------------
export function getLocalDailySessionId(curTimeUtc: number) {
  return Math.floor((curTimeUtc - mutil.getTimezoneOffset() * 60) / SECONDS_PER_DAY);
}

// -----------------------------------------------------------------------------
// 밀수 구매 가능 최대 갯수 계산
export function calcSmuggleBuyableCount(
  fleetStat: FleetStat,
  smuggleCms: SmuggleDesc,
  curTownAvgPercent: number,
  bIsAllyTown: boolean,
  cl: number
) {
  const smuggleGoodsCms = cms.SmuggleGoods[smuggleCms.smuggleGoodsId];
  const addedBuyablePct = fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_PCT); // 밀수품 구매 수량%
  const categorybuyablePct = fleetStat.getWpe(
    cmsEx.SMUGGLE_ADDED_BUYABLE_PCT_PASSIVE_EFFECT[smuggleGoodsCms.smuggleGoodsCategory]
  ); // 대상 밀수품 카테고리 구매 수량%
  const addedBuyable = fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE); // 밀수품 구매 수량
  const categoryBuyable = fleetStat.getWpe(
    cmsEx.SMUGGLE_ADDED_BUYABLE_PASSIVE_EFFECT[smuggleGoodsCms.smuggleGoodsCategory]
  ); // 대상 밀수품 카테고리 구매 수량
  const allyTownWeight = bIsAllyTown ? cms.Const.SmuggleGoodsAllyTownWeight.value : 0; // 동맹항 밀수품 비중
  const clWeight = cms.TownDevelopExp[cl - 1].smuggleGoodsWeight; // 상업 레벨에 따른 가중치

  const avgPercentWeight = Math.max(
    1 + (curTownAvgPercent * cms.Const.AveragePricePercentWeight.value) / 1000 / 100,
    cms.Const.SmuggleTownWeightMinValue.value
  ); // 평균 시세 가중치

  return Math.floor(
    smuggleCms.smuggleGoodsQuantity *
      ((1000 + addedBuyablePct + categorybuyablePct) / 1000) *
      ((1000 + allyTownWeight + clWeight) / 1000) *
      avgPercentWeight +
      addedBuyable +
      categoryBuyable
  );
}

// -----------------------------------------------------------------------------
// 밀수품 1개의 구매가격 계산
export function calcSmuggleBuyPrice(
  fleetStat: FleetStat,
  smuggleGoodsCms: SmuggleGoodsDesc,
  pricePercent: number, // 현재 도시의 시세%
  negoSuccess: number
) {
  const firstPrice = calcSmuggleBuyFirstPrice(smuggleGoodsCms, pricePercent);

  const buyPriceDiscountPct = fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_PCT); // 밀수품 구매 가격 할인율%
  const buyCategoryPriceDiscountPct = fleetStat.getWpe(
    cmsEx.SMUGGLE_BUY_PRICE_PCT_PASSIVE_EFFECT[smuggleGoodsCms.smuggleGoodsCategory]
  ); // 대상 밀수품 카테고리 구매 가격 할인율%

  let negoPct = 0;
  if (negoSuccess > 0) {
    const negoSuccessPct = cms.Const['SmuggleBargainDiscount' + negoSuccess].value;
    const negoSuccessWpePct = fleetStat.getWpe(
      cmsEx.PASSIVE_EFFECT.SMUGGLE_NEGOTIATE_BUY_PRICE_PCT
    );

    negoPct = Math.min(
      negoSuccessPct + negoSuccessWpePct,
      cms.Const.SmuggleBargainDiscountMaxRate.value
    );
  }
  const discountPct = buyPriceDiscountPct + buyCategoryPriceDiscountPct + negoPct;

  const buyPriceDiscount = fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.SMUGGLE_BUY_PRICE); // 밀수품 구매 가격 할인
  const buyCategoryPriceDiscount = fleetStat.getWpe(
    cmsEx.SMUGGLE_BUY_PRICE_PASSIVE_EFFECT[smuggleGoodsCms.smuggleGoodsCategory]
  ); // 대상 밀수품 카테고리 구매 가격 할인
  const discount = buyPriceDiscount + buyCategoryPriceDiscount;

  return Math.ceil((firstPrice * (1000 - discountPct)) / 1000 - discount);
}

// -----------------------------------------------------------------------------
// 구매 원가
export function calcSmuggleBuyFirstPrice(smuggleGoodsCms: SmuggleGoodsDesc, pricePercent: number) {
  const basisDucat = smuggleGoodsCms.basisDucat; // 밀수품 기본 가격
  return Math.ceil((basisDucat * pricePercent) / 100); // 구매 원가
}

// -----------------------------------------------------------------------------
// 밀수품 1개의 판매 가격
export function calcSmuggleSellPrice(
  fleetStat: FleetStat,
  pricePercent: number, // 현재 도시의 시세%
  smuggleGoodsCmsId: number,
  townCmsId: number,
  negoSuccessCount: number
) {
  const smuggleGoodsCms = cms.SmuggleGoods[smuggleGoodsCmsId];

  const firstPrice = calcSmuggleSellFirstPrice(pricePercent, smuggleGoodsCmsId, townCmsId); // 판매 원가

  const salePricePct = fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_PCT); // 판매 가격 할증률%
  const categoryPricePct = fleetStat.getWpe(
    cmsEx.SMUGGLE_SALE_PRICE_PCT_PASSIVE_EFFECT[smuggleGoodsCms.smuggleGoodsCategory]
  ); // 카테고리 구매 가격 할증률%

  let negoPct = 0;
  if (negoSuccessCount > 0) {
    negoPct = Math.min(
      cms.Const['SmuggleBargainPremium' + negoSuccessCount].value +
        fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.SMUGGLE_NEGOTIATE_SALE_PRICE_PCT),
      cms.Const.SmuggleBargainPremiumMaxRate.value
    ); // 성공한 가격 협상 단계 할증률
  }
  const pct = salePricePct + categoryPricePct + negoPct;

  const premium = fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.SMUGGLE_SALE_PRICE); // 밀수품 판매 가격 할증
  const premiumCategory = fleetStat.getWpe(
    cmsEx.SMUGGLE_SALE_PRICE_PASSIVE_EFFECT[smuggleGoodsCms.smuggleGoodsCategory]
  ); // 밀수품 카테고리 판매 가격 할증
  const wpeAdd = premium + premiumCategory;

  return Math.floor((firstPrice * (1000 + pct)) / 1000 + wpeAdd);
}

// -----------------------------------------------------------------------------
// 판매 원가
export function calcSmuggleSellFirstPrice(
  pricePercent: number, // 현재 도시의 시세%
  smuggleGoodsCmsId: number,
  townCmsId: number
) {
  const smuggleGoodsCms = cms.SmuggleGoods[smuggleGoodsCmsId];
  const townCms = cms.Town[townCmsId];

  const distance = cms.SmuggleGoodsTownMatrix[townCmsId][smuggleGoodsCmsId]; // 거리 판매 배율
  const culture =
    smuggleGoodsCms.cultureSellPrices[townCms.CulturalAreaId % cmsEx.CultureAreaCmsBaseCmsId]; // 문화권 할증%

  const basisValue =
    smuggleGoodsCms.basisDucat *
    ((1000 + smuggleGoodsCms.categoryCms.margin + distance) / 1000) *
    ((100 + culture) / 100); // 판매 기본 값어치
  const saleLowPrice = cmsEx.isSellingSmuggleInTown(townCmsId, smuggleGoodsCmsId)
    ? cms.Const.SaleLowPriceConst.value / 100
    : 1; // 해당 도시 판매 여부 상수

  return Math.floor(((basisValue * pricePercent) / 100) * saleLowPrice); // 판매 원가
}

/*
  한국   2시즌 시작날짜: 2025/03/24/00:00:00
  글로벌 2시즌 시작날짜: 2025/04/07/00:00:00
  중국   2시즌 시작날짜: 2026/01/05/00:00:00(임시)

  한국, 글로벌 3시즌 시작날짜: 2025/06/23/00:00:00
  중국        3시즌 시작날짜: 2026/04/06/00:00:00(임시)

  한국, 글로벌 4시즌 시작날짜: 2025/10/27/00:00:00
  중국        4시즌 시작날짜: 2026/07/0?/00:00:00(임시)

*/
export function getInvestSeasonId(curTimeUtc: number, inTimezone?: number): number {
  //const myTest = mutil.newDateByCmsDateStr('2025/10/27/00:00:00').getTime() / 1000;
  const curSessionId = GetFullWeeksUsingLocalTime(
    curTimeUtc,
    cms.Define.InvestmentWeeklySessionPivotDay,
    inTimezone
  );

  let secondSeasonStartTimeUtc;
  if (mconf.binaryCode === 'GL') {
    secondSeasonStartTimeUtc =
      mutil.newDateByCmsDateStr(cms.Define.SecondInvestSeasonStartDateGL, inTimezone).getTime() /
      1000;
  }
  else if (mconf.binaryCode === 'CN') {
    secondSeasonStartTimeUtc =
      mutil.newDateByCmsDateStr(cms.Define.SecondInvestSeasonStartDateCN, inTimezone).getTime() /
      1000;
  } else {
    secondSeasonStartTimeUtc =
      mutil.newDateByCmsDateStr(cms.Define.SecondInvestSeasonStartDateKR, inTimezone).getTime() /
      1000;
  }

  // 1시즌:  2시즌 sessionId 보다 작으면
  if (
    curSessionId <
    GetFullWeeksUsingLocalTime(
      secondSeasonStartTimeUtc,
      cms.Define.InvestmentWeeklySessionPivotDay,
      inTimezone
    )
  ) {
    const cmsId = cmsEx.getFirstInvestSeasonCmsId();
    if (!cms.InvestSeason[cmsId]) {
      throw new MError(
        'not-found-first-invest-season-id',
        MErrorCode.INVALID_INVEST_SEASON_CMS_ID,
        {
          curSessionId,
          cmsId,
        }
      );
    }
    return cmsId;
  }

  // 2시즌:  3시즌 sessionId 보다 작으면
  let thirdSeasonStartTimeUtc: number;
  if (mconf.binaryCode === 'CN') {
    thirdSeasonStartTimeUtc =
      mutil.newDateByCmsDateStr(cms.Define.CommonInvestSeasonStartDateCN, inTimezone).getTime() /
      1000;
  } else {
    thirdSeasonStartTimeUtc =
      mutil.newDateByCmsDateStr(cms.Define.CommonInvestSeasonStartDate, inTimezone).getTime() /
      1000;
  }
  
  if (
    curSessionId <
    GetFullWeeksUsingLocalTime(
      thirdSeasonStartTimeUtc,
      cms.Define.InvestmentWeeklySessionPivotDay,
      inTimezone
    )
  ) {
    const cmsId = cmsEx.getFirstInvestSeasonCmsId() + 1;
    if (!cms.InvestSeason[cmsId]) {
      throw new MError(
        'not-found-second-invest-season-id',
        MErrorCode.INVALID_INVEST_SEASON_CMS_ID,
        {
          curSessionId,
          cmsId,
        }
      );
    }
    return cmsId;
  }

  // 3시즌:  3시즌 + incr ...
  const gapSessionId =
    curSessionId -
    GetFullWeeksUsingLocalTime(
      thirdSeasonStartTimeUtc,
      cms.Define.InvestmentWeeklySessionPivotDay,
      inTimezone
    );
  // 3시즌 부터 시작하기 위한 값
  const incrSeasonId = 2 + Math.floor(gapSessionId / cms.Define.NextInvestSeasonWeeklySessionCnt);

  const cmsId = cmsEx.getFirstInvestSeasonCmsId() + incrSeasonId;
  if (!cms.InvestSeason[cmsId]) {
    throw new MError('not-found-invest-season-id', MErrorCode.INVALID_INVEST_SEASON_CMS_ID, {
      curSessionId,
      gapSessionId,
      cmsId,
    });
  }
  return cmsId;
}

export function convertSessionIdToStartTimeUtc(weeklySessionId: number, pivotDay: number) {
  const myTimeUtc = SECONDS_PER_WEEK * weeklySessionId;
  const timeZoneOffset = mutil.getTimezoneOffset() * 60;

  let pivotDayFactor;
  if (pivotDay <= 4) {
    pivotDayFactor = SECONDS_PER_DAY * (4 - pivotDay);
  } else {
    pivotDayFactor = SECONDS_PER_DAY * (4 - pivotDay + 7);
  }

  return myTimeUtc + timeZoneOffset - pivotDayFactor;
}

// -----------------------------------------------------------------------------
// 기본 시장 관세
export function getDefaultMayorTax(
  targetNationCmsId: number,
  mayorNationCmsId: number,
  townNationCmsId: number
) {
  if (townNationCmsId === targetNationCmsId) {
    return cms.Const.OurNationDefaultTaxRate.value / 1000;
  } else if (mayorNationCmsId && mayorNationCmsId === targetNationCmsId) {
    return cms.Const.MayorNationDefaultTaxRate.value / 1000;
  } else {
    return cms.Const.AnotherNationDefaultTaxRate.value / 1000;
  }
}

// -----------------------------------------------------------------------------
export function exchangeInvestSeasonReward(
  rewardCmsElem: RewardCmsElem,
  curTimeUtc: number,
  timezone?: number,
): RewardCmsElem {
  assert(rewardCmsElem.Type === REWARD_TYPE.REWERD_SEASON_ITEMS, 'invalid-exchangeReward-type');

  const investSeasonCmsId = getInvestSeasonId(curTimeUtc, timezone);

  return exchangeInvestSeasonRewardWithSeasonId(rewardCmsElem, investSeasonCmsId);
}

// -----------------------------------------------------------------------------
export function exchangeInvestSeasonRewardWithSeasonId(
  rewardCmsElem: RewardCmsElem,
  investSeasonCmsId: number
): RewardCmsElem {
  assert(rewardCmsElem.Type === REWARD_TYPE.REWERD_SEASON_ITEMS, 'invalid-exchangeReward-type');

  const rewardElem: RewardSeasonElemDesc = cmsEx.getRewardSeasonElem(
    investSeasonCmsId,
    rewardCmsElem.Id
  );

  if (_.isUndefined(rewardElem)) {
    throw new MError('not-found-key-exchangeReward', MErrorCode.INVALID_INVEST_SEASON_CMS_ID, {
      investSeasonCmsId,
      rewardCmsElem,
    });
  }

  // 기존 시즌보상 아이템(38) 수량은  변경된 보상의 수량으로 교체된다
  const changeRewardCmsElem: RewardCmsElem = _.cloneDeep(rewardCmsElem);
  changeRewardCmsElem.Type = rewardElem.Type;
  changeRewardCmsElem.Id = rewardElem.Id;
  if (changeRewardCmsElem.Quantity) {
    changeRewardCmsElem.Quantity = rewardElem.MinQuantity;
  } else {
    changeRewardCmsElem.MinQuantity = rewardElem.MinQuantity;
    changeRewardCmsElem.MaxQuantity = rewardElem.MaxQuantity;
  }
  mlog.verbose('[exchangeInvest]', {
    cur: rewardCmsElem,
    new: changeRewardCmsElem,
  });
  return changeRewardCmsElem;
}

// -----------------------------------------------------------------------------
export function convertSeasonRankerScoreToPureScore(seasonRankerScore: number): number {
  return Math.floor(seasonRankerScore / cms.Define.InvestmentSeasonRankScoreMultiplier);
}
