#!/bin/bash

CWD="$(dirname "$0")"

if [ ! -f $CWD/_config.sh ]; then
	echo "Please create a custom '_config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/_query.sh ]; then
	echo "Please create a custom '_query.sh' file at '$CWD' directory."
	exit 1
fi

if [ -z "$1" ]
  then
    echo "No argument worldIds"
    exit 1
fi

SECONDS=0

source $CWD/_config.sh
source $CWD/_query.sh

main() 
{

  worldIds=()
  for arg in ${@}
  do
    worldIds+=\"${arg}\"
    if [[ "${@: -1}" != "${arg}" ]]
    then
      worldIds+=","
    fi
  done

  echo "===== ARGS worldIds: " $worldIds
  echo "===== CREATE TABLE integration_duplicate_name"
  q="
    DROP TABLE IF EXISTS integration_duplicate_name;

    CREATE TABLE integration_duplicate_name (
      userId int NOT NULL,
      prevName varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      name varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      PRIMARY KEY (userId)
    ) 
    ENGINE=InnoDB DEFAULT 
    CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"


  echo "===== ALTER a_world_users ADD INDEX name"
  q="
    ALTER TABLE a_world_users
    ADD INDEX IDX_name (name);
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"


  echo "===== INSERT integration_duplicate_name"
  q="
    INSERT INTO integration_duplicate_name(userId, prevName, name)
    SELECT userId, name as prevName, CONCAT(\"N_\", userId) as name
      FROM a_world_users AS A WHERE 
      EXISTS
      (SELECT 1 FROM a_world_users AS B 
        WHERE A.worldId IN (${worldIds})
        AND B.worldId IN (${worldIds}) 
        AND A.name = B.name LIMIT 1, 1
      );
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"


  echo "===== ALTER a_world_users DROP INDEX name"
  q="
    ALTER TABLE a_world_users
    DROP INDEX IDX_name;
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"
}


main "$@"; 

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
exit
