{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T08:37:35.052Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T08:37:35.056Z"}
{"level":"info","message":"[Session] socket disposed, UUdIz7_t","timestamp":"2025-08-22T08:37:35.057Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T08:37:35.057Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T08:37:35.194Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T08:37:35.194Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T08:37:35.414Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T08:37:35.415Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T08:37:35.417Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T08:37:36.420Z"}
{"environment":"development","type":"townd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T08:37:38.855Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T08:38:04.798Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:38:04.808Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T08:38:04.811Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T08:38:05.071Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T08:38:05.072Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T08:38:05.073Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T08:38:05.073Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T08:38:09.102Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T08:38:09.103Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T08:38:09.105Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T08:38:09.112Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T08:38:09.113Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T08:38:09.129Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T08:38:09.230Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.252Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.269Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.281Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.296Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.308Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.325Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.344Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.362Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:38:09.382Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T08:38:09.464Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T08:38:09.465Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T08:38:09.469Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T08:38:09.605Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T08:38:09.607Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T08:38:09.608Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T08:38:09.608Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T08:38:09.611Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T08:38:09.611Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T08:38:09.612Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T08:38:09.612Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T08:38:09.614Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T08:38:09.614Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T08:38:09.615Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T08:38:09.615Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T08:38:09.616Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T08:38:09.618Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T08:38:09.619Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.621Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.621Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.621Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:38:09.622Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.633Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.644Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.653Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.667Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.678Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.702Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.711Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.719Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.734Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.748Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.759Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.768Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.777Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.785Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.792Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.800Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.808Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.816Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.824Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T08:38:09.835Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T08:38:09.836Z"}
{"baseUrl":"http://localhost:10001","body":{"layoutVersion":7},"err":"socket hang up","level":"error","message":"'/sync' exception","timestamp":"2025-08-22T08:38:09.837Z"}
{"msg":"'/sync' api exception","tries":1,"level":"warn","message":"sync try failed","timestamp":"2025-08-22T08:38:09.837Z"}
{"tries":1,"level":"warn","message":"Retry sync.","timestamp":"2025-08-22T08:38:09.837Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:38:10.841Z"}
{"pingInterval":2000,"curDate":1755851890,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T08:38:10.949Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T08:38:10.950Z"}
{"level":"info","message":"[SessionManager] session created: vSnBTsMC, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T08:38:23.170Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T08:38:23.174Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T08:38:23.174Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T08:38:23.175Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T08:45:28.798Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T08:45:28.799Z"}
{"level":"info","message":"[Session] socket disposed, vSnBTsMC","timestamp":"2025-08-22T08:45:28.799Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T08:45:28.799Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T08:45:29.191Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T08:45:29.191Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T08:45:30.145Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T08:45:30.145Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T08:45:30.147Z"}
{"environment":"development","type":"townd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T08:45:34.186Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T08:45:54.947Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:45:54.956Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T08:45:54.958Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T08:45:55.156Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T08:45:55.157Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T08:45:55.158Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T08:45:55.159Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T08:45:58.604Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T08:45:58.605Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T08:45:58.605Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T08:45:58.612Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T08:45:58.613Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T08:45:58.628Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T08:45:58.720Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.744Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.759Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.770Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.783Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.794Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.809Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.825Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.840Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T08:45:58.859Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T08:45:58.939Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T08:45:58.941Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T08:45:58.946Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T08:45:59.085Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T08:45:59.087Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T08:45:59.088Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T08:45:59.088Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T08:45:59.091Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T08:45:59.092Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T08:45:59.092Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T08:45:59.092Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T08:45:59.094Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T08:45:59.095Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T08:45:59.095Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T08:45:59.095Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T08:45:59.096Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T08:45:59.098Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T08:45:59.099Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.101Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.101Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.101Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.101Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.102Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.102Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.102Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.102Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.102Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.102Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.102Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T08:45:59.103Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.112Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.128Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.140Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.152Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.160Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.177Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.186Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.193Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.209Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.220Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.232Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.241Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.251Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.261Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.271Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.281Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.291Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.301Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:45:59.312Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T08:45:59.326Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T08:45:59.327Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:45:59.329Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:45:59.332Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:45:59.332Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:00.334Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:00.334Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:01.336Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:01.336Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:02.338Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:02.338Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:03.341Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:03.341Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:04.343Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:04.343Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:05.346Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:05.346Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:06.349Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:06.349Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:07.352Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:07.352Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:08.354Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:08.354Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T08:46:09.356Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T08:46:09.357Z"}
{"pingInterval":2000,"curDate":1755852370,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T08:46:10.364Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T08:46:10.364Z"}
{"level":"info","message":"[SessionManager] session created: tWYRhuy4, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T08:46:16.070Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T08:46:16.072Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T08:46:16.073Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T08:46:16.073Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T08:47:24.492Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T08:47:24.493Z"}
{"level":"info","message":"[Session] socket disposed, tWYRhuy4","timestamp":"2025-08-22T08:47:24.493Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T08:47:24.493Z"}
{"level":"info","message":"[SessionManager] session created: eK4e6b0p, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T08:48:06.748Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T08:48:06.751Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T08:48:06.751Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T08:48:06.752Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T08:49:00.594Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T08:49:00.595Z"}
{"level":"info","message":"[Session] socket disposed, eK4e6b0p","timestamp":"2025-08-22T08:49:00.595Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T08:49:00.595Z"}
{"level":"info","message":"[SessionManager] session created: wpHEb8lw, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T08:49:43.613Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T08:49:43.615Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T08:49:43.615Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T08:49:43.616Z"}
