{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T04:56:49.777Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T04:56:49.782Z"}
{"level":"info","message":"[Session] socket disposed, j9vVOVGT","timestamp":"2025-08-22T04:56:49.782Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T04:56:50.036Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T04:56:50.036Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T04:56:50.121Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T04:56:50.122Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T04:56:50.123Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T04:56:50.125Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T04:56:50.126Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T04:56:50.126Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T04:56:50.127Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T04:56:50.128Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T04:56:50.128Z"}
{"environment":"development","type":"oceand","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T04:56:52.725Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.469Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.469Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.470Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.470Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.470Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.470Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.470Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.471Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T04:57:21.471Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T04:57:21.479Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T04:57:21.480Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T04:57:21.747Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T04:57:21.748Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T04:57:21.749Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T04:57:21.750Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T04:57:25.457Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T04:57:25.458Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T04:57:25.458Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T04:57:25.465Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T04:57:25.466Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T04:57:25.482Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T04:57:25.571Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.591Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.608Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.622Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.637Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.649Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.666Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.684Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.699Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:25.717Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T04:57:25.798Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T04:57:25.799Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T04:57:25.803Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T04:57:25.937Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T04:57:25.939Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T04:57:25.939Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T04:57:25.940Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T04:57:25.942Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T04:57:25.943Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T04:57:25.943Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T04:57:25.944Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T04:57:25.946Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T04:57:25.947Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T04:57:25.947Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T04:57:25.948Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T04:57:25.949Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T04:57:25.951Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T04:57:25.951Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T04:57:26.050Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T04:57:26.152Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T04:57:26.157Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T04:57:26.157Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T04:57:26.157Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T04:57:26.157Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:26.215Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T04:57:26.226Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T04:57:26.229Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T04:57:26.229Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T04:57:26.231Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T04:57:26.231Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T04:57:26.233Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T04:57:26.234Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755838646,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T04:57:26.326Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T04:57:26.327Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T04:57:27.236Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T04:57:27.236Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T04:57:28.325Z"}
{"pingInterval":2000,"curDate":1755838648,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T04:57:28.329Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T04:58:00.544Z"}
{"level":"info","message":"[SessionManager] session created: uXnJpVuP, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T04:58:00.785Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T04:58:00.787Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T04:58:00.788Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T04:58:00.789Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.791Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.793Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.794Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.794Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.795Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.795Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.796Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T04:58:00.796Z"}
