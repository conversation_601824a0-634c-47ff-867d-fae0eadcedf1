// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import { withTxn } from '../mysqlUtil';
import puItemUpdate from '../sp/puItemUpdate';
import puUnlockManufactureRecipe from '../sp/puUnlockManufactureRecipe';
import { ItemChange } from '../../lobbyd/userInven';
import { MError, MErrorCode } from '../../motiflib/merror';

function queryImpl(
  connection: PoolConnection,
  userId: number,
  recipeCmsId: number,
  itemChange: ItemChange | null,
  unlockTimeUtc?: Date
) {
  return Promise.resolve()
    .then(() => {
      if (itemChange) {
        return puItemUpdate(
          connection,
          userId,
          itemChange.cmsId,
          itemChange.count,
          itemChange.unboundCount
        );
      }
    })
    .then(() => {
      return puUnlockManufactureRecipe(connection, userId, recipeCmsId, unlockTimeUtc);
    })
    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.MANUFACTURE_UNLOCK_RECIPE_TXN_ERROR);
      }
    });
}

export default function (
  dbConnPool: Pool,
  userId: number,
  recipeCmsId: number,
  itemChange: ItemChange | null,
  unlockTimeUtc?: Date
) {
  return withTxn(dbConnPool, __filename, (connection: PoolConnection) => {
    return queryImpl(connection, userId, recipeCmsId, itemChange, unlockTimeUtc);
  });
} 