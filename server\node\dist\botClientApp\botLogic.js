"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stop = exports.start = exports.layoutData = exports.investableTownCmsIds = exports.selectableNationCmsIds = exports.botApiConnection = void 0;
const json5_1 = __importDefault(require("json5"));
const lodash_1 = __importDefault(require("lodash"));
const path_1 = __importDefault(require("path"));
const botClient_1 = __importDefault(require("./botClient"));
const botApiConnection_1 = __importDefault(require("./botApiConnection"));
const botConf_1 = __importDefault(require("./botConf"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const resolveLocalDotJson5_1 = require("../motiflib/resolveLocalDotJson5");
//mconf.append({ log: botConst.log });
const fs_1 = __importDefault(require("fs"));
const botBehaviorCommonNode_1 = require("./botScenarios/botBehaviorCommonNode");
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const merror_1 = require("../motiflib/merror");
const cms_1 = __importStar(require("../cms"));
const townDesc_1 = require("../cms/townDesc");
const Sentry = __importStar(require("@sentry/node"));
// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('uncaught Exception', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('unhandled Rejection', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------
// ----------------------------------------------------------------------------
// Variables.
// ----------------------------------------------------------------------------
let stopping = false;
const allClients = [];
exports.botApiConnection = new botApiConnection_1.default();
exports.selectableNationCmsIds = [];
exports.investableTownCmsIds = [];
exports.layoutData = {};
// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------
function stopImpl() {
    // Wait for all clients to terminate.
    setTimeout(() => {
        mlog_1.default.info('exit');
        process.exit(0);
    }, botConf_1.default.numBots * 50);
}
function tick() {
    if (stopping) {
        stopImpl();
        return;
    }
    allClients.forEach((bc) => {
        setTimeout(() => {
            if (stopping) {
                return;
            }
            if (bc.isDisconnected()) {
                if (bc.getLogoutTimeUtc() + botConf_1.default.reconnectWaitTimeSec > mutil.curTimeUtc()) {
                    return;
                }
                bc.reconnect();
            }
            else {
                bc.tick();
            }
        }, (Math.random() * botConf_1.default.tickIntervalMsec) / 2);
    });
    setTimeout(() => {
        tick();
    }, botConf_1.default.tickIntervalMsec);
}
function load(cfgname) {
    const configFolder = 'config';
    const configFilePath = path_1.default.join(configFolder, cfgname);
    return mutil
        .readFile(configFilePath, 'utf8')
        .then((cfgData) => {
        mlog_1.default.info('config loaded successfully.', { dump: cfgData });
        return json5_1.default.parse(cfgData);
    })
        .catch((err) => {
        mlog_1.default.error('Failed to load config!', {
            configFilePath,
            errMsg: err.message,
        });
        return null;
    });
}
function loadLayout() {
    const ServiceLayoutFolder = 'service_layout';
    const layoutFileName = mconf_1.default.layout || (0, resolveLocalDotJson5_1.resolveLocalDotJson5)();
    const layoutFilePath = path_1.default.join(ServiceLayoutFolder, layoutFileName);
    const fileStr = fs_1.default.readFileSync(layoutFilePath, 'utf8');
    try {
        const layoutData = json5_1.default.parse(fileStr);
        return layoutData;
    }
    catch (err) {
        throw new Error(`failed to parse layout': ${err.message}`);
    }
}
function setSelectableNationCmsIds() {
    const nationCmsIdStr = [
        ...Object.keys(cms_1.default.Nation).filter((nationCmsId) => {
            const nationCms = cms_1.default.Nation[nationCmsId];
            return nationCms.canSelect;
        }),
    ];
    nationCmsIdStr.forEach((elem) => {
        exports.selectableNationCmsIds.push(parseInt(elem));
    });
}
//----------------------------------------------------------
function setInvestableTownCmsIds() {
    for (const key of Object.keys(cms_1.default.Town)) {
        const townCms = cms_1.default.Town[key];
        if (!(0, townDesc_1.IsUnInvestableTownByForeigner)(townCms.ownType)) {
            exports.investableTownCmsIds.push(townCms.id);
        }
    }
    mlog_1.default.verbose('setInvestableTownCmsIds completed', {
        investableTownCmsIds: exports.investableTownCmsIds,
    });
}
// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
const start = async (cfgname) => {
    // load config
    mlog_1.default.info('loading config...', {
        cfgname,
    });
    const loadedData = await load(cfgname);
    if (!loadedData) {
        throw new merror_1.MError('Failed to load initial config!', merror_1.MErrorCode.INTERNAL_ERROR);
    }
    lodash_1.default.merge(botConf_1.default, loadedData.common);
    // merge scenario configurations
    const scenarioConf = loadedData[botConf_1.default.scenarioTypeName];
    if (scenarioConf) {
        lodash_1.default.merge(botConf_1.default, scenarioConf);
    }
    mlog_1.default.info('confirm config...', {
        botConf: botConf_1.default,
    });
    // Init cms.
    (0, cms_1.load)();
    exports.layoutData = loadLayout();
    mlog_1.default.verbose('layoutData loaded ...', { layoutData: exports.layoutData });
    setSelectableNationCmsIds();
    setInvestableTownCmsIds();
    // initialize apic connection to authd
    exports.botApiConnection.init(botConf_1.default.authd, undefined);
    //register behaviorTree nodes
    (0, botBehaviorCommonNode_1.registerBehaviorNodes)();
    // Create clients.
    mlog_1.default.verbose('spawning clients ...', { numBots: botConf_1.default.numBots });
    for (let i = 0; i < botConf_1.default.numBots; ++i) {
        const bc = new botClient_1.default(i);
        allClients.push(bc);
    }
    // Start ticking.
    tick();
};
exports.start = start;
const stop = () => {
    // Close all clients.
    mlog_1.default.info('closing clients ...');
    stopping = true;
    allClients.forEach((bc) => {
        bc.close();
    });
};
exports.stop = stop;
//# sourceMappingURL=botLogic.js.map