// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MError, MErrorCode } from '../../motiflib/merror';
import cms from '../../cms';
import { ITEM_TYPE } from '../../cms/itemDesc';
import mconf from '../../motiflib/mconf';
import mlog from '../../motiflib/mlog';

export const spName = 'mp_u_item_update_8';
export const errorCode = MErrorCode.ITEM_UPDATE_QUERY_ERROR_8;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default async function (
  connection: query.Connection,
  userId: number,
  cmsId0: number, count0: number, unboundCount0: number,
  cmsId1: number, count1: number, unboundCount1: number,
  cmsId2: number, count2: number, unboundCount2: number,
  cmsId3: number, count3: number, unboundCount3: number,
  cmsId4: number, count4: number, unboundCount4: number,
  cmsId5: number, count5: number, unboundCount5: number,
  cmsId6: number, count6: number, unboundCount6: number,
  cmsId7: number, count7: number, unboundCount7: number
): Promise<void> {

  // puItemUpdate 기존 로직대로.
  // 의뢰 아이템은 u_item 테이블에 넣지 않아야 됨.
  // 여기서 검사하는 것이 좋은 방법은 아니지만 item 에 비해 뒤늦게 만들어진 기획이기에 만약을 위해 여기서 검사.
  // 모든 cmsId 값을 검사
  const cmsIds = [cmsId0, cmsId1, cmsId2, cmsId3, cmsId4, cmsId5, cmsId6, cmsId7];
  const hasQuestItem = cmsIds.some(cmsId => 
    cmsId != null && cmsId > 0 && cms.Item[cmsId]?.type === ITEM_TYPE.RANDOM_QUEST
  );

  if (hasQuestItem) {
    throw new MError(
      'quest-item-can-not-insert-to-items',
      MErrorCode.QUEST_ITEM_CAN_NOT_INSERT_TO_ITEMS_TABLE,
      { cmsIds }
    );
  }

  try {
    const qr = await spFunction(connection, userId, 
      cmsId0, count0, unboundCount0,
      cmsId1, count1, unboundCount1,
      cmsId2, count2, unboundCount2,
      cmsId3, count3, unboundCount3,
      cmsId4, count4, unboundCount4,
      cmsId5, count5, unboundCount5,
      cmsId6, count6, unboundCount6,
      cmsId7, count7, unboundCount7
    );
    
    if (qr.rows[0][0]['ROW_COUNT()'] !== '1' && qr.rows[0][0]['ROW_COUNT()'] !== '2') {
      const errorMsg = `${spName} is failed`;
      if (mconf.isDev) {
        throw new MError(errorMsg, errorCode, { cmsIds });
      } else {
        mlog.error(errorMsg, { cmsIds });
      }
    }
  } catch (err) {
    return catchHandler(err);
  }
}
