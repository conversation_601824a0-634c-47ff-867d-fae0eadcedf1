"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SPAWN_STATE = exports.SPAWN_RADIUS_TYPE = exports.WorldTargetType = exports.WorldBuffSourceType = exports.EXPIRE_TYPE = exports.MATE_STATE_FLAG = exports.SHIP_BOTTOM_TYPE = exports.SHIP_MOVE_TYPE = exports.OCEAN_NPC_CMS_ID = exports.NATION_CMS_ID = exports.BUFF_CMS_ID = exports.SUPPLY_CMS_ID = exports.CARGO_LOAD_PRESET_TYPE = exports.SHIP_CARGO_TYPE = exports.ShipSlotIndexMax = exports.ShipCabinSlotIndexMax = exports.ShipSlotIndexCaptainRoom = exports.CHARACTER_TYPE = exports.VICE_PRIME_MINISTER_CMS_ID = exports.PRIME_MINISTER_CMS_ID = exports.BATTLE_SAILOR_MULTIPLIER = exports.INVEST_RANKER_MAIL_CMS_ID = exports.MAYOR_REWARD_MAIL_CMS_ID = exports.REGION_REWARD_MAIL_CMS_ID = exports.SmuggleWorldBuffCmsId = exports.MichaelRank3CmsId = exports.MichaelRank2CmsId = exports.MateSlowdownCmsId = exports.MateInjuryCmsId = exports.TheEndTimeUtc = exports.BattleContentsTermsToContentsTermsOffset = exports.BattleContentsTermsStartingCmsId = exports.ContentsTermsStartingCmsId = exports.TradeGooodsStartingCmsId = exports.RisbonTownCmsId = exports.ResearchPointCmsId = exports.SmuggleShopPointCmsId = exports.SmugglePointCmsId = exports.ArenaTicketCmsId = exports.GuildCoinCmsId = exports.ContributionPointCmsId = exports.PaidRedGemPointCmsId = exports.CashShopGachaBoxTokenCmsId = exports.AchievementPointCmsId = exports.CashShopMileage = exports.EnergyPointCmsId = exports.RedGemPointCmsId = exports.BlueGemPointCmsId = exports.DepositPointCmsId = exports.DucatPointCmsId = void 0;
exports.SHIP_SIZE = exports.SHIP_GRADE_TYPE = exports.SPECIAL_STAT_TARGET_TYPE = exports.POINT_TYPE = exports.CONTENTS_TERMS_OPERATOR = exports.RELIGION_BUFF_TYPE = exports.BUILDING_TYPE = exports.QUEST_CATEGORY = exports.MAIL_TYPE = exports.INVENTORY_TYPE = exports.LevelPropName = exports.ExpPropName = exports.SPECIAL_STAT_JOB_TYPE = exports.JOB_TYPE = exports.isMateTrainingTimeCostItem = exports.isGuildSynthesisTimeCostItem = exports.isGuildCraftTimeCostItem = exports.isMateLearnPassiveTimeCostItem = exports.isMateAwakenTimeCostItem = exports.BUILDING_MENU_CMS_ID = exports.BUILDING_CONTENTS_UNLOCK_CMS_ID = exports.CONTENTS_TERMS_TYPE = exports._BubbleEventCmsId = exports._DisasterWpeIdLinker = exports.DISASTER_CMS_ID_TYPE = exports.CONTENTS_TERMS_CMS_ID = exports.DiscoverChancePassiveEffectByDiscoveryGroup = exports.DiscoverChanceSpecialStatByDiscoveryGroup = exports.PASSIVE_EFFECT = exports.ACTIVE_EFFECT = exports.STAT_TYPE = exports.DISCOVERY_MISSION_DIFFICULTY = exports.SHIP_TEMPLATE_OVERRIDE_CALC_TYPE = exports.SPECIAL_STAT_MODIFIER_TYPE = exports.SPECIAL_STAT_TYPE = exports.EncountChoice = exports.SEA_AREA_TYPE = exports.ECONOMIC_BLOC_TYPE = exports.CULTURE_TYPE = exports.DEVELOPMENT_TYPE = exports.NATION_DIPLOMACY_CMS_ID = exports.TOWN_OWN_TYPE = exports.RoyalTitleBaseCmsId = exports.CultureAreaCmsBaseCmsId = exports.FlagShipFormationIndex = exports.FirstFleetIndex = exports.NoFleetIndex = exports.isFoodOrWater = exports.convertLoadPresetTypeToSupplyCmsId = exports.convertCargoCmsIdToLoadPresetType = void 0;
exports.getTownBuilding = exports.getTownDraftableSailor = exports.getTownTradeGoodsCms = exports.getTownSellingTradeGoodsCmsIds = exports.IsSellInTown = exports.IntimacyModifyValue = exports.getMateReRecruitingForMateGrade = exports.getMateRecruitingGroupByGroupAndMateCmsId = exports.isFilteredByCountryCode = exports.GetMateRecrutingGroupsForPubReRecruiting = exports.GetMateRecrutingGroupsForPubRecruiting = exports.IsBlackMarketShopType = exports.GetShipCargoType = exports.NATION_PROMISE_CONDITION_CMS_ID = exports.NoInsuranceCmsId = exports.SailConsumeSupplies = exports.PUB_GIFT_GRADE = exports.DISCOVERY_GRADE = exports.QUEST_SPAWN_OCEAN_DOODAD_TYPE = exports.ZoneType = exports.GENDER = exports.NATION_RANKING_EFFECT_CMS_ID = exports.firstCulturalAreaCmsId = exports.firstWeatherTileCmsId = exports.firstAchievementPointCmsId = exports.OCCUPY_TYPE = exports.KICK_RESULT_TYPE = exports.KICK_SITUATION_TYPE = exports.COUNTRY_CODE_MASK = exports.QUEST_TERMS = exports.isRandomMissionTargetType = exports.MISSION_TARGET_TYPE = exports.SMUGGLE_SALE_PRICE_PASSIVE_EFFECT = exports.SMUGGLE_SALE_PRICE_PCT_PASSIVE_EFFECT = exports.SMUGGLE_BUY_PRICE_PASSIVE_EFFECT = exports.SMUGGLE_BUY_PRICE_PCT_PASSIVE_EFFECT = exports.SMUGGLE_ADDED_BUYABLE_PASSIVE_EFFECT = exports.SMUGGLE_ADDED_BUYABLE_PCT_PASSIVE_EFFECT = exports.EXCHANGE_GET_GOODS_PCT_PASSIVE_EFFECT = exports.EXCHANGE_PRICE_PCT_PASSIVE_EFFECT = exports.TRADE_PREDICT_PRICE_PASSIVE_EFFECT = exports.TRADE_ADDED_BUYABLE_PCT_PASSIVE_EFFECT = exports.TRADE_ADDED_BUYABLE_PASSIVE_EFFECT = exports.INVEST_DEVELOPMENT_PCT_PASSIVE_EFFECT = exports.INVEST_DEVELOPMENT_PASSIVE_EFFECT = exports.TRADE_EXTRA_CHARGE_PASSIVE_EFFECT = exports.TRADE_DISCOUNT_PASSIVE_EFFECT = exports.TASK_CATEGORY = exports.ACHIEVEMENT_TERMS = exports.STAT_EFFECT_TYPE = void 0;
exports.getContentsTermsIdsOfAchievementTerms = exports.getTask = exports.statName = exports.shipDefaultLife = exports.shipDefaultDurability = exports.getCultureTownCmsIds = exports.getRegionCmsIdsThatHasTown = exports.getRegionTownCmsIds = exports.getTradeSpeciality = exports.getRoyalTitleOrderGroup = exports.getRoyalOrderGroup = exports.getItemQuestGroup = exports.getTownEventRequestGroup = exports.getTownRequestGroup = exports.isMustAppearQuest = exports.collectorThisWeekPreferenceItems = exports.getAdmiralByMateCmsId = exports.getFishGradeCms = exports.getFishDiscoveryCmses = exports.getBlackMarketShopCmses = exports.calcBlueprintExpLevel = exports.getShipBuildExpCmsAddMinRate = exports.getShipBuildMasteryExpCmsAccumulateExp = exports.getMaxBlueprintExp = exports.getMaxBpLevel = exports.getMaxPassEventExp = exports.calcPassEventLevel = exports.getMaxUserExp = exports.calcUserLevel = exports.calcMateLevel = exports.getMaxMateLevel = exports.getDefaultShipBody2Colors = exports.getLandDisasterCmsIds = exports.getExchangeVillageListCmsIdsByVillage = exports.getTradeGoodsCategoryByExchangeVillageList = exports.getExchangeVillageStorageByLevelValue = exports.getVillageExchangeList = exports.getVillageDiscoveryIds = exports.getVillageFriendshipCmsesSortedByGroup = exports.getVillageFriendshipCms = exports.getDefaultShipBody1Colors = exports.getDefaultShipSailPatternColors = exports.getDefaultShipSailCrests = exports.getDefaultPoint = exports.getSortedEventInvestmentRanking = exports.getSortedInvestmentRanking = exports.getMaxGlobalRanking = exports.getSortedDiscoveryGlobalRanking = exports.getSortedDiscoveryCollectorRanking = exports.getCollectorTownBuildingCmsIds = void 0;
exports.getTradeGoodsMarketPriceVolumeBases = exports.convertRewardFixedElemsToCustomAttachmentStr = exports.convertRewardFixedToCustomAttachmentStr = exports.getCurHotTimeBonus = exports.getTownShopCmsIds = exports.getShipEnchantStatRatioEx = exports.getShipEnchant = exports.getShipBlueprintSlotByIndex = exports.getPassiveCommonInfo = exports.getPassiveTargetTypeByGroupId = exports.getPassiveByGroupAndLevel = exports.convertRewardFixedToGLogRewardData = exports.getRandomEquipDyeColors = exports.getDefaultMateEquipmentColors = exports.isEventPageExpired = exports.getEventMissionCount = exports.getEventMissionCmses = exports.getMateCmsIdsOfAdmiralMateSet = exports.getEventPagesForPageType = exports.getNewUserAttendance = exports.getRevivalUserAttendance = exports.getAttendanceLastDay = exports.getAttendance = exports.getAutoMateOrderElem = exports.getMateLearnablePassiveElem = exports.getMateLearnablePassiveElemTable = exports.getTradeByTownAndTradeGoodsCmsId = exports.getMateAwaken = exports.getShipCustomizingCmsName = exports.pickRegionRandomTradeGoodsReward = exports.pickRegionRandomItemReward = exports.getShopCmsIdsByGroup = exports.getSelectableNations = exports.getCashShopCmsByProductCode = exports.getQuestPassCashShopCmsIdByQuestPassCmsId = exports.getQuestPassCashShopCmsIdsByQuestCmsId = exports.getSortedDiscoveryMissionByDifficulty = exports.getItemDiscoveryCmsIds = exports.getCashShopLimitSalesByPointType = exports.getCashShopBoxRatio = exports.getTaxFreePermitCashShopCmsId = exports.isContributionShopPreviousId = exports.isCashShopPreviousId = exports.getAchievementTermsTargets = exports.getMateHighestLanguageLevel = exports.getDrinkMateBuildingBuffStat = exports.getRecruitMateBuildingBuffStat = exports.getTownKick = exports.getSortedReputationArr = exports.getTradeGoodsByCategory = void 0;
exports.getReturnUserCashShopCms = exports.getChoiceBoxGroup = exports.getShipCamouflageType = exports.getConstellationByDiscoveryId = exports.getEventPageRankingGuild = exports.getEventRankingRewardGroup = exports.getEventRankingReceivedRewardIds = exports.getPubStaffQuestLevel = exports.getTownCmsIdForPubStaffQuestCmsId = exports.getMateBuffLiveEvents = exports.getMateTraining = exports.getPassEventMaxLevel = exports.GetAdventureEventRewardGroup = exports.GetLandExploreQuestSpot = exports.getCurEquipType = exports.isCostumeEquipType = exports.TradeGoodsGroupInTownCmsId = exports.GetUnpopularTradeGoodsCandidates = exports.TradeAreaTileGroup = exports.TradeAreaTileDataDispatchGroup = exports.GetLandExploreDispatchGroup = exports.getTradeEventCmsByEvnetType = exports.getSortedTradeEventsByTradeAreaTileData = exports.getTownCmsIdsByTradeAreaTileData = exports.getTradeAreaTileDataByTown = exports.GetBoardGameRewardGroup = exports.GetEventGameGroup = exports.GetTradeComboGroup = exports.isLiveEvent = exports.getBuildingHours = exports.getQuestChapterByNodeIdx = exports.getFirstAdmiralNationCmsIds = exports.getArenaMatchBotByGroup = exports.getArenaTierByGrade = exports.getArenaInitScores = exports.getArenaTierListByPriority = exports.GetShipyardBuilds = exports.getRequiredSyncStatForOcean = exports.getUnemployedMateIntimacyGrade = exports.getIntimacyPctOfMaxLevel = exports.getChangedIntimacyPctOfCurLevel = exports.getIntimacyLevelByIntimacyPoint = exports.addableIntimacyWithinSameLevel = exports.getShipBlueprintCmsFromMaterialBpId = exports.getMateCmsFromAwakenPieceItem = exports.getRewardDropPoolDataInGroup = exports.getMateCmsIdsByAutoPassiveClearQuestId = exports.getMateCmsIdsByAutoFixedPassiveClearQuestId = exports.getDefaultNationPower = exports.getTradeGoodsPricePercentClamps = void 0;
exports.clearCmsCache = exports.getWorldStateCms = exports.getMateTemplateGroupCms = exports.getTradeGoodsCms = exports.getDiscoveryCms = exports.getBossRaidCms = exports.getFishCms = exports.getOceanNpcCms = exports.getTownNpcGlobalCms = exports.getHotTimeBuffCms = exports.getAchievementCms = exports.getRewardSeasonElem = exports.getFirstInvestSeasonCmsId = exports.getOceanStageCmsIdByClash = exports.getRefundInvestItemCmsId = exports.getInfiniteLighthouseGroup = exports.getKarmaLevel = exports.getTownNpcIdByInteractionFunctionType = exports.getTownNpcInteractionCmsByInteractionFunctionType = exports.getNpcShopGroupByTownCmsId = exports.getNpcIdsByTownCmsId = exports.getTownNpcShopGroup = exports.getTownSmuggleGoodsCms = exports.getTownSellingSmuggleGoodsCmsIds = exports.isSellingSmuggleInTown = exports.getShipComposeGroup = exports.getShipSlotEnchantStatRatio = exports.getCEquipEnchantStatRatio = exports.getShipSlotEnchant = exports.getCEquipEnchant = exports.getInvestmentTownCmsIds = exports.getRelationShipChronicleByQuestId = exports.getAdmiralQuestPassIdByMateCmsId = exports.isOpenDurationCashShopCms = void 0;
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
const lodash_1 = __importDefault(require("lodash"));
const assert_1 = __importDefault(require("assert"));
const _1 = __importDefault(require("."));
const CMSConst = __importStar(require("./const"));
const formula = __importStar(require("../formula"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const discoveryRankingDesc_1 = require("./discoveryRankingDesc");
const eventPageDesc_1 = require("./eventPageDesc");
const shopDesc_1 = require("./shopDesc");
const DiscoveryDesc_1 = require("./DiscoveryDesc");
const rewardDesc_1 = require("./rewardDesc");
const tradeGoodsDesc_1 = require("./tradeGoodsDesc");
const cEquipDesc_1 = require("./cEquipDesc");
const mutil = __importStar(require("../motiflib/mutil"));
const formula_1 = require("../formula");
const eventMissionDesc_1 = require("./eventMissionDesc");
const itemDesc_1 = require("./itemDesc");
const oceanCoordinate_1 = require("./oceanCoordinate");
const shipCamouflageDesc_1 = require("./shipCamouflageDesc");
const questPassDesc_1 = require("./questPassDesc");
const enchantStatRatioDesc_1 = require("./enchantStatRatioDesc");
const smuggleGoodsCategoryDesc_1 = require("./smuggleGoodsCategoryDesc");
exports.DucatPointCmsId = 100001;
exports.DepositPointCmsId = 100002;
exports.BlueGemPointCmsId = 100003; // 블루젬
exports.RedGemPointCmsId = 100004; // 레드잼
// 에너지는 cms, syncdata 상에만 point 에 있고 실제로는 UserEnergy 에서 관리됨.
exports.EnergyPointCmsId = 100005;
exports.CashShopMileage = 100006;
exports.AchievementPointCmsId = 100007;
exports.CashShopGachaBoxTokenCmsId = 100008; // 상자 교환권
exports.PaidRedGemPointCmsId = 100009; // 레드잼(유료용)
exports.ContributionPointCmsId = 100010; // 공헌도
exports.GuildCoinCmsId = 100011; // 길드(상회)코인
// 모의전 입장권은 cms, syncdata 상에만 point 에 있고 실제로는 UserArena 에서 관리됨.
exports.ArenaTicketCmsId = 100012; // 모의전 입장권
exports.SmugglePointCmsId = 100014; // 밀수 교역상 이용에 필요한 포인트 (밀수단 문서 조각)
exports.SmuggleShopPointCmsId = 100015; // 밀수단 지부장/단장 상점 이용에 필요한 포인트 (밀수단의 신용 증서)
exports.ResearchPointCmsId = 100020; // 연구 포인트.
exports.RisbonTownCmsId = 11000000;
exports.TradeGooodsStartingCmsId = 24000000;
exports.ContentsTermsStartingCmsId = 81000000;
exports.BattleContentsTermsStartingCmsId = 69400000;
exports.BattleContentsTermsToContentsTermsOffset = exports.ContentsTermsStartingCmsId - exports.BattleContentsTermsStartingCmsId;
// 2036-01-19 03:14:09 (시간 만료가 없다는 의미로 사용됨)
exports.TheEndTimeUtc = 2147483647;
// 항해사 부상 worldbuff cmsId
exports.MateInjuryCmsId = 38010000;
// 항해사 태업 worldbuff cmsId
exports.MateSlowdownCmsId = 38010036;
// 미카엘 2,3급 worldbuff cmsId
exports.MichaelRank2CmsId = 38020531;
exports.MichaelRank3CmsId = 38020532;
// 밀수품 보유시의 worldBuff cmsId
exports.SmuggleWorldBuffCmsId = 38043300; // 밀수
exports.REGION_REWARD_MAIL_CMS_ID = 92900001;
exports.MAYOR_REWARD_MAIL_CMS_ID = 92002001;
exports.INVEST_RANKER_MAIL_CMS_ID = 92002002;
// https://wiki.line.games/pages/viewpage.action?pageId=3310023
// 전투중 사용되는 선원수 수치에 적용.
exports.BATTLE_SAILOR_MULTIPLIER = 10;
exports.PRIME_MINISTER_CMS_ID = 10140001; // 총리
exports.VICE_PRIME_MINISTER_CMS_ID = 10140002; // 부총리
var CHARACTER_TYPE;
(function (CHARACTER_TYPE) {
    CHARACTER_TYPE[CHARACTER_TYPE["MATE"] = 0] = "MATE";
    CHARACTER_TYPE[CHARACTER_TYPE["LEADERABLE_MATE"] = 1] = "LEADERABLE_MATE";
    CHARACTER_TYPE[CHARACTER_TYPE["NPC"] = 2] = "NPC";
})(CHARACTER_TYPE = exports.CHARACTER_TYPE || (exports.CHARACTER_TYPE = {}));
// Ship slot index must be between 1 ~ 15.
exports.ShipSlotIndexCaptainRoom = 1;
exports.ShipCabinSlotIndexMax = 15;
exports.ShipSlotIndexMax = 25;
var SHIP_CARGO_TYPE;
(function (SHIP_CARGO_TYPE) {
    SHIP_CARGO_TYPE[SHIP_CARGO_TYPE["NONE"] = 0] = "NONE";
    SHIP_CARGO_TYPE[SHIP_CARGO_TYPE["SUPPLY"] = 1] = "SUPPLY";
    SHIP_CARGO_TYPE[SHIP_CARGO_TYPE["TRADE_GOODS"] = 2] = "TRADE_GOODS";
})(SHIP_CARGO_TYPE = exports.SHIP_CARGO_TYPE || (exports.SHIP_CARGO_TYPE = {}));
var CARGO_LOAD_PRESET_TYPE;
(function (CARGO_LOAD_PRESET_TYPE) {
    CARGO_LOAD_PRESET_TYPE[CARGO_LOAD_PRESET_TYPE["NONE"] = 0] = "NONE";
    CARGO_LOAD_PRESET_TYPE[CARGO_LOAD_PRESET_TYPE["WATER"] = 1] = "WATER";
    CARGO_LOAD_PRESET_TYPE[CARGO_LOAD_PRESET_TYPE["FOOD"] = 2] = "FOOD";
    CARGO_LOAD_PRESET_TYPE[CARGO_LOAD_PRESET_TYPE["LUMBER"] = 3] = "LUMBER";
    CARGO_LOAD_PRESET_TYPE[CARGO_LOAD_PRESET_TYPE["AMMO"] = 4] = "AMMO";
    CARGO_LOAD_PRESET_TYPE[CARGO_LOAD_PRESET_TYPE["TRADE_GOODS"] = 5] = "TRADE_GOODS";
    CARGO_LOAD_PRESET_TYPE[CARGO_LOAD_PRESET_TYPE["ANY"] = 6] = "ANY";
})(CARGO_LOAD_PRESET_TYPE = exports.CARGO_LOAD_PRESET_TYPE || (exports.CARGO_LOAD_PRESET_TYPE = {}));
var SUPPLY_CMS_ID;
(function (SUPPLY_CMS_ID) {
    SUPPLY_CMS_ID[SUPPLY_CMS_ID["WATER"] = 40600001] = "WATER";
    SUPPLY_CMS_ID[SUPPLY_CMS_ID["FOOD"] = 40600002] = "FOOD";
    SUPPLY_CMS_ID[SUPPLY_CMS_ID["LUMBER"] = 40600003] = "LUMBER";
    SUPPLY_CMS_ID[SUPPLY_CMS_ID["AMMO"] = 40600004] = "AMMO";
})(SUPPLY_CMS_ID = exports.SUPPLY_CMS_ID || (exports.SUPPLY_CMS_ID = {}));
var BUFF_CMS_ID;
(function (BUFF_CMS_ID) {
})(BUFF_CMS_ID = exports.BUFF_CMS_ID || (exports.BUFF_CMS_ID = {}));
// HUNGER = 38010003, // 굶주림 디버프
//ANXIETY = 38001011, // 불안 디버프
var NATION_CMS_ID;
(function (NATION_CMS_ID) {
    NATION_CMS_ID[NATION_CMS_ID["PIRATE"] = 10001002] = "PIRATE";
    NATION_CMS_ID[NATION_CMS_ID["NEUTRAL_NATION"] = 10001001] = "NEUTRAL_NATION";
})(NATION_CMS_ID = exports.NATION_CMS_ID || (exports.NATION_CMS_ID = {}));
var OCEAN_NPC_CMS_ID;
(function (OCEAN_NPC_CMS_ID) {
    OCEAN_NPC_CMS_ID[OCEAN_NPC_CMS_ID["USER_DATA_NPC_CMS_ID"] = 15000010] = "USER_DATA_NPC_CMS_ID";
})(OCEAN_NPC_CMS_ID = exports.OCEAN_NPC_CMS_ID || (exports.OCEAN_NPC_CMS_ID = {}));
var SHIP_MOVE_TYPE;
(function (SHIP_MOVE_TYPE) {
    SHIP_MOVE_TYPE[SHIP_MOVE_TYPE["SAILING_SHIP"] = 1] = "SAILING_SHIP";
    SHIP_MOVE_TYPE[SHIP_MOVE_TYPE["GALLEY"] = 2] = "GALLEY";
})(SHIP_MOVE_TYPE = exports.SHIP_MOVE_TYPE || (exports.SHIP_MOVE_TYPE = {}));
var SHIP_BOTTOM_TYPE;
(function (SHIP_BOTTOM_TYPE) {
    SHIP_BOTTOM_TYPE[SHIP_BOTTOM_TYPE["POINTY"] = 1] = "POINTY";
    SHIP_BOTTOM_TYPE[SHIP_BOTTOM_TYPE["FLAT"] = 2] = "FLAT";
})(SHIP_BOTTOM_TYPE = exports.SHIP_BOTTOM_TYPE || (exports.SHIP_BOTTOM_TYPE = {}));
// 항해사 상태 플래그
var MATE_STATE_FLAG;
(function (MATE_STATE_FLAG) {
    MATE_STATE_FLAG[MATE_STATE_FLAG["SLOWDOWN"] = 1] = "SLOWDOWN";
    MATE_STATE_FLAG[MATE_STATE_FLAG["INJURY"] = 2] = "INJURY";
    MATE_STATE_FLAG[MATE_STATE_FLAG["AWOL"] = 4] = "AWOL";
})(MATE_STATE_FLAG = exports.MATE_STATE_FLAG || (exports.MATE_STATE_FLAG = {}));
// 장비, 부품에 대한 만료 타입(아이템은 미구현)
var EXPIRE_TYPE;
(function (EXPIRE_TYPE) {
    EXPIRE_TYPE[EXPIRE_TYPE["PROVIDE"] = 1] = "PROVIDE";
    EXPIRE_TYPE[EXPIRE_TYPE["RECEIVE"] = 2] = "RECEIVE";
})(EXPIRE_TYPE = exports.EXPIRE_TYPE || (exports.EXPIRE_TYPE = {}));
// ----------------------------------------------------------------------------
// 버프의 발생 출처.
// 추가 시 WorldBuffUtil.getSourceIdCmsByWorldBuffSourceType 수정 필요
// OX는 DB 저장여부
// ----------------------------------------------------------------------------
var WorldBuffSourceType;
(function (WorldBuffSourceType) {
    WorldBuffSourceType[WorldBuffSourceType["INVALID"] = 0] = "INVALID";
    WorldBuffSourceType[WorldBuffSourceType["PRAYER"] = 1] = "PRAYER";
    WorldBuffSourceType[WorldBuffSourceType["DONATION"] = 2] = "DONATION";
    WorldBuffSourceType[WorldBuffSourceType["USER_ITEM"] = 3] = "USER_ITEM";
    WorldBuffSourceType[WorldBuffSourceType["DISASTER"] = 4] = "DISASTER";
    WorldBuffSourceType[WorldBuffSourceType["PROTECT_ENCOUNT"] = 5] = "PROTECT_ENCOUNT";
    WorldBuffSourceType[WorldBuffSourceType["HUNGER"] = 6] = "HUNGER";
    WorldBuffSourceType[WorldBuffSourceType["CASH_SHOP_BUY_WITHOUT_PURCHASE"] = 7] = "CASH_SHOP_BUY_WITHOUT_PURCHASE";
    WorldBuffSourceType[WorldBuffSourceType["OCEAN_DOODAD_EFFECT"] = 8] = "OCEAN_DOODAD_EFFECT";
    WorldBuffSourceType[WorldBuffSourceType["POINT"] = 9] = "POINT";
    WorldBuffSourceType[WorldBuffSourceType["CHEAT"] = 10] = "CHEAT";
    WorldBuffSourceType[WorldBuffSourceType["PROTECTION"] = 11] = "PROTECTION";
    WorldBuffSourceType[WorldBuffSourceType["WORLD_TILE"] = 12] = "WORLD_TILE";
    WorldBuffSourceType[WorldBuffSourceType["MATE_SET"] = 13] = "MATE_SET";
    WorldBuffSourceType[WorldBuffSourceType["COMPANY_JOB"] = 14] = "COMPANY_JOB";
    WorldBuffSourceType[WorldBuffSourceType["WEATHER"] = 15] = "WEATHER";
    WorldBuffSourceType[WorldBuffSourceType["COMPANY_LEVEL"] = 16] = "COMPANY_LEVEL";
    WorldBuffSourceType[WorldBuffSourceType["OCCUPIED_NATION"] = 17] = "OCCUPIED_NATION";
    WorldBuffSourceType[WorldBuffSourceType["MATE_INJURY"] = 18] = "MATE_INJURY";
    WorldBuffSourceType[WorldBuffSourceType["ANXIETY"] = 19] = "ANXIETY";
    WorldBuffSourceType[WorldBuffSourceType["BATTLE_FORMATION"] = 20] = "BATTLE_FORMATION";
    WorldBuffSourceType[WorldBuffSourceType["MATE_PASSIVE"] = 21] = "MATE_PASSIVE";
    WorldBuffSourceType[WorldBuffSourceType["WORLD_SKILL"] = 22] = "WORLD_SKILL";
    WorldBuffSourceType[WorldBuffSourceType["ADDED_TICK_BUFF"] = 23] = "ADDED_TICK_BUFF";
    WorldBuffSourceType[WorldBuffSourceType["COLLECTION_BUFF"] = 24] = "COLLECTION_BUFF";
    WorldBuffSourceType[WorldBuffSourceType["REVOLT"] = 25] = "REVOLT";
    WorldBuffSourceType[WorldBuffSourceType["TOWING"] = 26] = "TOWING";
    WorldBuffSourceType[WorldBuffSourceType["MATE_SLOWDOWN"] = 27] = "MATE_SLOWDOWN";
    WorldBuffSourceType[WorldBuffSourceType["ATTENDANCE"] = 28] = "ATTENDANCE";
    WorldBuffSourceType[WorldBuffSourceType["LIVE_EVENT"] = 29] = "LIVE_EVENT";
    WorldBuffSourceType[WorldBuffSourceType["HOT_TIME_BUFF"] = 30] = "HOT_TIME_BUFF";
    WorldBuffSourceType[WorldBuffSourceType["RETURNER_BUFF"] = 31] = "RETURNER_BUFF";
    WorldBuffSourceType[WorldBuffSourceType["USER_TITLE"] = 32] = "USER_TITLE";
    WorldBuffSourceType[WorldBuffSourceType["GUILD_BUFF"] = 33] = "GUILD_BUFF";
    WorldBuffSourceType[WorldBuffSourceType["NATION_EFFECT_PROMISE"] = 34] = "NATION_EFFECT_PROMISE";
    WorldBuffSourceType[WorldBuffSourceType["BILLING"] = 35] = "BILLING";
    WorldBuffSourceType[WorldBuffSourceType["PET"] = 36] = "PET";
    WorldBuffSourceType[WorldBuffSourceType["SMUGGLE"] = 37] = "SMUGGLE";
    WorldBuffSourceType[WorldBuffSourceType["SEASON_BUFF"] = 38] = "SEASON_BUFF";
    WorldBuffSourceType[WorldBuffSourceType["CLASH_BUFF"] = 39] = "CLASH_BUFF";
})(WorldBuffSourceType = exports.WorldBuffSourceType || (exports.WorldBuffSourceType = {}));
// ----------------------------------------------------------------------------
// 월드 대상 타입 (world buff, world active effect 등에서 사용)
// ----------------------------------------------------------------------------
var WorldTargetType;
(function (WorldTargetType) {
    WorldTargetType[WorldTargetType["INVALID"] = 0] = "INVALID";
    WorldTargetType[WorldTargetType["COMPANY"] = 1] = "COMPANY";
    WorldTargetType[WorldTargetType["FLEET"] = 2] = "FLEET";
    WorldTargetType[WorldTargetType["SHIP"] = 3] = "SHIP";
    WorldTargetType[WorldTargetType["MATE"] = 4] = "MATE";
})(WorldTargetType = exports.WorldTargetType || (exports.WorldTargetType = {}));
// ----------------------------------------------------------------------------
// NPC, Doodad 스폰할 좌표의 타입
// ----------------------------------------------------------------------------
var SPAWN_RADIUS_TYPE;
(function (SPAWN_RADIUS_TYPE) {
    SPAWN_RADIUS_TYPE[SPAWN_RADIUS_TYPE["NONE"] = 0] = "NONE";
    SPAWN_RADIUS_TYPE[SPAWN_RADIUS_TYPE["CIRCLE"] = 1] = "CIRCLE";
    SPAWN_RADIUS_TYPE[SPAWN_RADIUS_TYPE["SQUARE"] = 2] = "SQUARE";
    SPAWN_RADIUS_TYPE[SPAWN_RADIUS_TYPE["REGION"] = 3] = "REGION";
})(SPAWN_RADIUS_TYPE = exports.SPAWN_RADIUS_TYPE || (exports.SPAWN_RADIUS_TYPE = {}));
// ----------------------------------------------------------------------------
// NPC, Doodad 스폰 상태
// ----------------------------------------------------------------------------
var SPAWN_STATE;
(function (SPAWN_STATE) {
    SPAWN_STATE[SPAWN_STATE["START"] = 0] = "START";
    SPAWN_STATE[SPAWN_STATE["SPAWNED"] = 1] = "SPAWNED";
    SPAWN_STATE[SPAWN_STATE["DESPAWN"] = 2] = "DESPAWN";
})(SPAWN_STATE = exports.SPAWN_STATE || (exports.SPAWN_STATE = {}));
function convertCargoCmsIdToLoadPresetType(cmsId) {
    if (cmsId === SUPPLY_CMS_ID.WATER) {
        return CARGO_LOAD_PRESET_TYPE.WATER;
    }
    if (cmsId === SUPPLY_CMS_ID.FOOD) {
        return CARGO_LOAD_PRESET_TYPE.FOOD;
    }
    if (cmsId === SUPPLY_CMS_ID.LUMBER) {
        return CARGO_LOAD_PRESET_TYPE.LUMBER;
    }
    if (cmsId === SUPPLY_CMS_ID.AMMO) {
        return CARGO_LOAD_PRESET_TYPE.AMMO;
    }
    if (_1.default.TradeGoods[cmsId] || _1.default.SmuggleGoods[cmsId]) {
        return CARGO_LOAD_PRESET_TYPE.TRADE_GOODS;
    }
    return CARGO_LOAD_PRESET_TYPE.NONE;
}
exports.convertCargoCmsIdToLoadPresetType = convertCargoCmsIdToLoadPresetType;
function convertLoadPresetTypeToSupplyCmsId(type) {
    switch (type) {
        case CARGO_LOAD_PRESET_TYPE.WATER:
            return SUPPLY_CMS_ID.WATER;
        case CARGO_LOAD_PRESET_TYPE.FOOD:
            return SUPPLY_CMS_ID.FOOD;
        case CARGO_LOAD_PRESET_TYPE.LUMBER:
            return SUPPLY_CMS_ID.LUMBER;
        case CARGO_LOAD_PRESET_TYPE.AMMO:
            return SUPPLY_CMS_ID.AMMO;
        default:
            return 0;
    }
}
exports.convertLoadPresetTypeToSupplyCmsId = convertLoadPresetTypeToSupplyCmsId;
function isFoodOrWater(cmsId) {
    if (cmsId === SUPPLY_CMS_ID.WATER || cmsId === SUPPLY_CMS_ID.FOOD) {
        return true;
    }
    return false;
}
exports.isFoodOrWater = isFoodOrWater;
exports.NoFleetIndex = 0;
exports.FirstFleetIndex = 1;
exports.FlagShipFormationIndex = 1;
exports.CultureAreaCmsBaseCmsId = 12200000;
exports.RoyalTitleBaseCmsId = 21700000;
var TOWN_OWN_TYPE;
(function (TOWN_OWN_TYPE) {
    TOWN_OWN_TYPE[TOWN_OWN_TYPE["FREE_TOWN"] = 0] = "FREE_TOWN";
    TOWN_OWN_TYPE[TOWN_OWN_TYPE["CAPITAL_TOWN"] = 1] = "CAPITAL_TOWN";
    TOWN_OWN_TYPE[TOWN_OWN_TYPE["NATIONAL_TOWN"] = 2] = "NATIONAL_TOWN";
    TOWN_OWN_TYPE[TOWN_OWN_TYPE["UNOCCUPIABLE"] = 3] = "UNOCCUPIABLE";
})(TOWN_OWN_TYPE = exports.TOWN_OWN_TYPE || (exports.TOWN_OWN_TYPE = {}));
// Types of events that occur between two nations
var NATION_DIPLOMACY_CMS_ID;
(function (NATION_DIPLOMACY_CMS_ID) {
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["ATTACK_MY_NATION_FLEET"] = 10200000] = "ATTACK_MY_NATION_FLEET";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["ATTACK_OTHER_NATION_FLEET"] = 10200001] = "ATTACK_OTHER_NATION_FLEET";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["WIN_IN_BATTLE_WITH_MY_NATION_FLEET"] = 10200002] = "WIN_IN_BATTLE_WITH_MY_NATION_FLEET";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["WIN_IN_BATTLE_WITH_OTHER_NATION_FLEET"] = 10200003] = "WIN_IN_BATTLE_WITH_OTHER_NATION_FLEET";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["INVEST_IN_OWN_NATION_TOWN"] = 10200004] = "INVEST_IN_OWN_NATION_TOWN";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["INVEST_IN_OTHER_NATION_TOWN"] = 10200005] = "INVEST_IN_OTHER_NATION_TOWN";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["BUY_AND_SELL_TRADE_GOODS"] = 10200006] = "BUY_AND_SELL_TRADE_GOODS";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["COMPLETE_REQUEST"] = 10200007] = "COMPLETE_REQUEST";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["CHANGE_NATION"] = 10200008] = "CHANGE_NATION";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["CHANGE_TOWN_NATION_BY_INVEST"] = 10200009] = "CHANGE_TOWN_NATION_BY_INVEST";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["SWEEP_MY_NATION_FLEET"] = 10200015] = "SWEEP_MY_NATION_FLEET";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["SWEEP_OTHER_NATION_FLEET"] = 10200016] = "SWEEP_OTHER_NATION_FLEET";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["BUY_AND_SELL_MY_NATION_SMUGGLE_GOODS"] = 10200017] = "BUY_AND_SELL_MY_NATION_SMUGGLE_GOODS";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["BUY_AND_SELL_OTHER_NATION_SMUGGLE_GOODS"] = 10200018] = "BUY_AND_SELL_OTHER_NATION_SMUGGLE_GOODS";
    NATION_DIPLOMACY_CMS_ID[NATION_DIPLOMACY_CMS_ID["REMOTE_INVEST"] = 10200019] = "REMOTE_INVEST";
})(NATION_DIPLOMACY_CMS_ID = exports.NATION_DIPLOMACY_CMS_ID || (exports.NATION_DIPLOMACY_CMS_ID = {}));
// lower case specially.
var DEVELOPMENT_TYPE;
(function (DEVELOPMENT_TYPE) {
    DEVELOPMENT_TYPE[DEVELOPMENT_TYPE["none"] = 0] = "none";
    DEVELOPMENT_TYPE[DEVELOPMENT_TYPE["industry"] = 1] = "industry";
    DEVELOPMENT_TYPE[DEVELOPMENT_TYPE["commerce"] = 2] = "commerce";
    DEVELOPMENT_TYPE[DEVELOPMENT_TYPE["armory"] = 3] = "armory";
    DEVELOPMENT_TYPE[DEVELOPMENT_TYPE["max"] = 4] = "max";
})(DEVELOPMENT_TYPE = exports.DEVELOPMENT_TYPE || (exports.DEVELOPMENT_TYPE = {}));
var CULTURE_TYPE;
(function (CULTURE_TYPE) {
    CULTURE_TYPE[CULTURE_TYPE["HYBRID"] = 0] = "HYBRID";
    CULTURE_TYPE[CULTURE_TYPE["WESTERN"] = 1] = "WESTERN";
    CULTURE_TYPE[CULTURE_TYPE["ORIENTAL"] = 2] = "ORIENTAL";
})(CULTURE_TYPE = exports.CULTURE_TYPE || (exports.CULTURE_TYPE = {}));
var ECONOMIC_BLOC_TYPE;
(function (ECONOMIC_BLOC_TYPE) {
    ECONOMIC_BLOC_TYPE[ECONOMIC_BLOC_TYPE["WESTERN"] = 0] = "WESTERN";
    ECONOMIC_BLOC_TYPE[ECONOMIC_BLOC_TYPE["ORIENTAL"] = 1] = "ORIENTAL";
})(ECONOMIC_BLOC_TYPE = exports.ECONOMIC_BLOC_TYPE || (exports.ECONOMIC_BLOC_TYPE = {}));
// 권역 속성 (안전,위험,무법)
var SEA_AREA_TYPE;
(function (SEA_AREA_TYPE) {
    SEA_AREA_TYPE[SEA_AREA_TYPE["SAFE"] = 1] = "SAFE";
    SEA_AREA_TYPE[SEA_AREA_TYPE["DANGEROUS"] = 2] = "DANGEROUS";
    SEA_AREA_TYPE[SEA_AREA_TYPE["LAWLESS"] = 3] = "LAWLESS";
})(SEA_AREA_TYPE = exports.SEA_AREA_TYPE || (exports.SEA_AREA_TYPE = {}));
// ----------------------------------------------------------------------------
// 인카운트 선택값. (CMSEx.lua 의 ENCOUNT_USER_CHOICE)
// ----------------------------------------------------------------------------
var EncountChoice;
(function (EncountChoice) {
    EncountChoice[EncountChoice["NONE"] = 0] = "NONE";
    EncountChoice[EncountChoice["ATT_ESCAPE_ALLOW"] = 1] = "ATT_ESCAPE_ALLOW";
    EncountChoice[EncountChoice["ATT_ESCAPE_DENY"] = 2] = "ATT_ESCAPE_DENY";
    EncountChoice[EncountChoice["ATT_SURRENDER_ALLOW"] = 3] = "ATT_SURRENDER_ALLOW";
    EncountChoice[EncountChoice["ATT_SURRENDER_DENY"] = 4] = "ATT_SURRENDER_DENY";
    EncountChoice[EncountChoice["ATT_NEGOTIATE_ALLOW"] = 5] = "ATT_NEGOTIATE_ALLOW";
    EncountChoice[EncountChoice["ATT_NEGOTIATE_DELY"] = 6] = "ATT_NEGOTIATE_DELY";
    EncountChoice[EncountChoice["DEF_START_BATTLE"] = 7] = "DEF_START_BATTLE";
    EncountChoice[EncountChoice["DEF_ESCAPE"] = 8] = "DEF_ESCAPE";
    EncountChoice[EncountChoice["DEF_SURRENDER"] = 9] = "DEF_SURRENDER";
    EncountChoice[EncountChoice["DEF_NEGOTIATE"] = 10] = "DEF_NEGOTIATE";
    EncountChoice[EncountChoice["MIN"] = 1] = "MIN";
    EncountChoice[EncountChoice["MAX"] = 10] = "MAX";
    EncountChoice[EncountChoice["ATT_MIN"] = 1] = "ATT_MIN";
    EncountChoice[EncountChoice["ATT_MAX"] = 6] = "ATT_MAX";
    EncountChoice[EncountChoice["DEF_MIN"] = 7] = "DEF_MIN";
    EncountChoice[EncountChoice["DEF_MAX"] = 10] = "DEF_MAX";
})(EncountChoice = exports.EncountChoice || (exports.EncountChoice = {}));
// 전문지식 스탯.
// https://docs.google.com/spreadsheets/d/18L1rhIFwGXAAijb2z7PPwo9aRNbm1CnltKu9fESbpi0/edit#gid=1128321814
var SPECIAL_STAT_TYPE;
(function (SPECIAL_STAT_TYPE) {
    // 전투
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["CANNON"] = 1] = "CANNON";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["RAMMING"] = 2] = "RAMMING";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["SUPPORT"] = 3] = "SUPPORT";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["MELEE"] = 4] = "MELEE";
    // 교역
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["BUY"] = 5] = "BUY";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["SELL"] = 6] = "SELL";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["NEGOTIATE"] = 7] = "NEGOTIATE";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["TRADE"] = 8] = "TRADE";
    // 모험
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["NATURAL_HISTORY"] = 9] = "NATURAL_HISTORY";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["AESTHETICS"] = 10] = "AESTHETICS";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["SCOUT"] = 11] = "SCOUT";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["SUPPLY"] = 12] = "SUPPLY";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["MIN"] = 1] = "MIN";
    SPECIAL_STAT_TYPE[SPECIAL_STAT_TYPE["MAX"] = 12] = "MAX";
})(SPECIAL_STAT_TYPE = exports.SPECIAL_STAT_TYPE || (exports.SPECIAL_STAT_TYPE = {}));
var SPECIAL_STAT_MODIFIER_TYPE;
(function (SPECIAL_STAT_MODIFIER_TYPE) {
    SPECIAL_STAT_MODIFIER_TYPE[SPECIAL_STAT_MODIFIER_TYPE["ADD"] = 1] = "ADD";
    SPECIAL_STAT_MODIFIER_TYPE[SPECIAL_STAT_MODIFIER_TYPE["ADD_PCT"] = 2] = "ADD_PCT";
    SPECIAL_STAT_MODIFIER_TYPE[SPECIAL_STAT_MODIFIER_TYPE["ADD_LEVEL"] = 3] = "ADD_LEVEL";
})(SPECIAL_STAT_MODIFIER_TYPE = exports.SPECIAL_STAT_MODIFIER_TYPE || (exports.SPECIAL_STAT_MODIFIER_TYPE = {}));
// shipTemplate cms overrideStat의 계산 방법
var SHIP_TEMPLATE_OVERRIDE_CALC_TYPE;
(function (SHIP_TEMPLATE_OVERRIDE_CALC_TYPE) {
    SHIP_TEMPLATE_OVERRIDE_CALC_TYPE[SHIP_TEMPLATE_OVERRIDE_CALC_TYPE["SET"] = 1] = "SET";
    SHIP_TEMPLATE_OVERRIDE_CALC_TYPE[SHIP_TEMPLATE_OVERRIDE_CALC_TYPE["ADD"] = 2] = "ADD";
    SHIP_TEMPLATE_OVERRIDE_CALC_TYPE[SHIP_TEMPLATE_OVERRIDE_CALC_TYPE["MULTIPLY"] = 3] = "MULTIPLY";
})(SHIP_TEMPLATE_OVERRIDE_CALC_TYPE = exports.SHIP_TEMPLATE_OVERRIDE_CALC_TYPE || (exports.SHIP_TEMPLATE_OVERRIDE_CALC_TYPE = {}));
var DISCOVERY_MISSION_DIFFICULTY;
(function (DISCOVERY_MISSION_DIFFICULTY) {
    DISCOVERY_MISSION_DIFFICULTY[DISCOVERY_MISSION_DIFFICULTY["EASY"] = 1] = "EASY";
    DISCOVERY_MISSION_DIFFICULTY[DISCOVERY_MISSION_DIFFICULTY["NORMAL"] = 2] = "NORMAL";
    DISCOVERY_MISSION_DIFFICULTY[DISCOVERY_MISSION_DIFFICULTY["HARD"] = 3] = "HARD";
})(DISCOVERY_MISSION_DIFFICULTY = exports.DISCOVERY_MISSION_DIFFICULTY || (exports.DISCOVERY_MISSION_DIFFICULTY = {}));
// UWO stat type doc link
// https://docs.google.com/spreadsheets/d/15gj00RrBf27b3DLg7MeYtZVKBjKKXaTtLkAQnaYjlSk/edit#gid=1903224352
var STAT_TYPE;
(function (STAT_TYPE) {
    STAT_TYPE[STAT_TYPE["NONE"] = 0] = "NONE";
    // Ship. (0 ~ 99)
    STAT_TYPE[STAT_TYPE["SHIP_MIN_SAILOR"] = 1] = "SHIP_MIN_SAILOR";
    STAT_TYPE[STAT_TYPE["SHIP_MAX_SAILOR"] = 2] = "SHIP_MAX_SAILOR";
    STAT_TYPE[STAT_TYPE["SHIP_MAX_DURABILITY"] = 3] = "SHIP_MAX_DURABILITY";
    STAT_TYPE[STAT_TYPE["SHIP_HOLD"] = 4] = "SHIP_HOLD";
    STAT_TYPE[STAT_TYPE["SHIP_GUNPORTS"] = 5] = "SHIP_GUNPORTS";
    STAT_TYPE[STAT_TYPE["SHIP_OAR_POWER"] = 6] = "SHIP_OAR_POWER";
    STAT_TYPE[STAT_TYPE["SHIP_VERTICAL_SAIL"] = 7] = "SHIP_VERTICAL_SAIL";
    STAT_TYPE[STAT_TYPE["SHIP_HORIZONTAL_SAIL"] = 8] = "SHIP_HORIZONTAL_SAIL";
    STAT_TYPE[STAT_TYPE["SHIP_WAVE_RESISTANCE"] = 9] = "SHIP_WAVE_RESISTANCE";
    STAT_TYPE[STAT_TYPE["SHIP_ARMOR"] = 10] = "SHIP_ARMOR";
    STAT_TYPE[STAT_TYPE["SHIP_LIFE"] = 11] = "SHIP_LIFE";
    STAT_TYPE[STAT_TYPE["SHIP_ACCELERATION"] = 12] = "SHIP_ACCELERATION";
    STAT_TYPE[STAT_TYPE["SHIP_ANGULARPOWER"] = 13] = "SHIP_ANGULARPOWER";
    // Mate. (101 ~ 199)
    STAT_TYPE[STAT_TYPE["MATE_COMMAND"] = 101] = "MATE_COMMAND";
    STAT_TYPE[STAT_TYPE["MATE_FORCE"] = 102] = "MATE_FORCE";
    STAT_TYPE[STAT_TYPE["MATE_INTUITION"] = 103] = "MATE_INTUITION";
    STAT_TYPE[STAT_TYPE["MATE_NEGOTIATION"] = 104] = "MATE_NEGOTIATION";
    STAT_TYPE[STAT_TYPE["MATE_KNOWLEDGE"] = 105] = "MATE_KNOWLEDGE";
    STAT_TYPE[STAT_TYPE["MATE_CHARM"] = 106] = "MATE_CHARM";
    STAT_TYPE[STAT_TYPE["MATE_FORTUNE"] = 107] = "MATE_FORTUNE";
    STAT_TYPE[STAT_TYPE["NONCOMBAT_ICE_BREAKING"] = 108] = "NONCOMBAT_ICE_BREAKING";
    STAT_TYPE[STAT_TYPE["NONCOMBAT_BREAK_THROUGH"] = 109] = "NONCOMBAT_BREAK_THROUGH";
    // 전투. (1000 ~ 1999)
    STAT_TYPE[STAT_TYPE["BATTLE_RAMMING_POWER"] = 1000] = "BATTLE_RAMMING_POWER";
    STAT_TYPE[STAT_TYPE["BATTLE_HUMAN_ATTACK"] = 1002] = "BATTLE_HUMAN_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_MOVE_POWER"] = 1004] = "BATTLE_MOVE_POWER";
    STAT_TYPE[STAT_TYPE["BATTLE_ROTATE_POWER"] = 1005] = "BATTLE_ROTATE_POWER";
    STAT_TYPE[STAT_TYPE["BATTLE_ACTION_POWER"] = 1006] = "BATTLE_ACTION_POWER";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_CANNON_ATTACK"] = 1007] = "BATTLE_2ND_CANNON_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_CANNON_DEFENSE"] = 1008] = "BATTLE_2ND_CANNON_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_CANNON_ACCURACY"] = 1009] = "BATTLE_2ND_CANNON_ACCURACY";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_CANNON_DODGE_RATING"] = 1010] = "BATTLE_2ND_CANNON_DODGE_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_CANNON_CRITICAL"] = 1011] = "BATTLE_2ND_CANNON_CRITICAL";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MELEE_ATTACK"] = 1012] = "BATTLE_2ND_MELEE_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MELEE_DEFENSE"] = 1013] = "BATTLE_2ND_MELEE_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MELEE_ACCURACY"] = 1014] = "BATTLE_2ND_MELEE_ACCURACY";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MELEE_DODGE_RATING"] = 1015] = "BATTLE_2ND_MELEE_DODGE_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MELEE_CRITICAL"] = 1016] = "BATTLE_2ND_MELEE_CRITICAL";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_RAMMING_ATTACK"] = 1017] = "BATTLE_2ND_RAMMING_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_REPAIR_AMOUNT"] = 1018] = "BATTLE_REPAIR_AMOUNT";
    STAT_TYPE[STAT_TYPE["BATTLE_REPAIR_CRITICAL_RATING"] = 1019] = "BATTLE_REPAIR_CRITICAL_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_HEAL_AMOUNT"] = 1020] = "BATTLE_HEAL_AMOUNT";
    STAT_TYPE[STAT_TYPE["BATTLE_HEAL_CRITICAL_RATING"] = 1021] = "BATTLE_HEAL_CRITICAL_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_ATTACK"] = 1022] = "BATTLE_ADDED_CANNON_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_ATTACK_PCT"] = 1023] = "BATTLE_ADDED_CANNON_ATTACK_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_DEFENSE"] = 1024] = "BATTLE_ADDED_CANNON_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_DEFENSE_PCT"] = 1025] = "BATTLE_ADDED_CANNON_DEFENSE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_ACCURACY"] = 1026] = "BATTLE_ADDED_CANNON_ACCURACY";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_ACCURACY_PCT"] = 1027] = "BATTLE_ADDED_CANNON_ACCURACY_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_CRITICAL"] = 1028] = "BATTLE_ADDED_CANNON_CRITICAL";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_CRITICAL_PCT"] = 1029] = "BATTLE_ADDED_CANNON_CRITICAL_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_CANNON_CRITICAL_DAMAGE_PCT"] = 1030] = "BATTLE_CANNON_CRITICAL_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_DODGE_RATING"] = 1031] = "BATTLE_ADDED_CANNON_DODGE_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_DODGE_RATING_PCT"] = 1032] = "BATTLE_ADDED_CANNON_DODGE_RATING_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_CANNON_COUNTER_ATTACK_DAMAGE_PCT"] = 1036] = "BATTLE_CANNON_COUNTER_ATTACK_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_ATTACK"] = 1037] = "BATTLE_ADDED_MELEE_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_ATTACK_PCT"] = 1038] = "BATTLE_ADDED_MELEE_ATTACK_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_DEFENSE"] = 1039] = "BATTLE_ADDED_MELEE_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_DEFENSE_PCT"] = 1040] = "BATTLE_ADDED_MELEE_DEFENSE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_ACCURACY"] = 1041] = "BATTLE_ADDED_MELEE_ACCURACY";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_ACCURACY_PCT"] = 1042] = "BATTLE_ADDED_MELEE_ACCURACY_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_CRITICAL"] = 1043] = "BATTLE_ADDED_MELEE_CRITICAL";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_CRITICAL_PCT"] = 1044] = "BATTLE_ADDED_MELEE_CRITICAL_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_MELEE_CRITICAL_DAMAGE_PCT"] = 1045] = "BATTLE_MELEE_CRITICAL_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_DODGE_RATING"] = 1046] = "BATTLE_ADDED_MELEE_DODGE_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_DODGE_RATING_PCT"] = 1047] = "BATTLE_ADDED_MELEE_DODGE_RATING_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_MELEE_COUNTER_ATTACK_DAMAGE_PCT"] = 1051] = "BATTLE_MELEE_COUNTER_ATTACK_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_ATTACK"] = 1052] = "BATTLE_ADDED_RAMMING_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_ATTACK_PCT"] = 1053] = "BATTLE_ADDED_RAMMING_ATTACK_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_RAMMING_COUNTER_ATTACK_DAMAGE_PCT"] = 1055] = "BATTLE_RAMMING_COUNTER_ATTACK_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_REPAIR_AMOUNT"] = 1056] = "BATTLE_ADDED_REPAIR_AMOUNT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_REPAIR_AMOUNT_PCT"] = 1057] = "BATTLE_ADDED_REPAIR_AMOUNT_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_REPAIR_CRITICAL_RATING"] = 1058] = "BATTLE_ADDED_REPAIR_CRITICAL_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_HEAL_AMOUNT"] = 1059] = "BATTLE_ADDED_HEAL_AMOUNT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_HEAL_AMOUNT_PCT"] = 1060] = "BATTLE_ADDED_HEAL_AMOUNT_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_HEAL_CRITICAL_RATING"] = 1061] = "BATTLE_ADDED_HEAL_CRITICAL_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MOVE_POWER"] = 1062] = "BATTLE_ADDED_MOVE_POWER";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MOVE_POWER_PCT"] = 1063] = "BATTLE_ADDED_MOVE_POWER_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_ROTATE_POWER"] = 1064] = "BATTLE_ADDED_ROTATE_POWER";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_ROTATE_POWER_PCT"] = 1065] = "BATTLE_ADDED_ROTATE_POWER_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_ACTION_POWER_RECOVER"] = 1066] = "BATTLE_ADDED_ACTION_POWER_RECOVER";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_ACTION_POWER_RECOVER_PCT"] = 1067] = "BATTLE_ADDED_ACTION_POWER_RECOVER_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DUEL_PREEMPTIVE_STRIKE_PCT"] = 1074] = "BATTLE_DUEL_PREEMPTIVE_STRIKE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_DUEL_HP"] = 1075] = "BATTLE_ADDED_DUEL_HP";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_DUEL_HP_PCT"] = 1076] = "BATTLE_ADDED_DUEL_HP_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DUEL_ATTACK"] = 1077] = "BATTLE_DUEL_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_DUEL_ATTACK_PCT"] = 1078] = "BATTLE_DUEL_ATTACK_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DUEL_DEFENCE"] = 1079] = "BATTLE_DUEL_DEFENCE";
    STAT_TYPE[STAT_TYPE["BATTLE_DUEL_DEFENCE_PCT"] = 1080] = "BATTLE_DUEL_DEFENCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ACTION_POWER_RECOVER"] = 1081] = "BATTLE_ACTION_POWER_RECOVER";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_RAMMING_DEFENSE"] = 1082] = "BATTLE_2ND_RAMMING_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_RAMMING_ACCURACY"] = 1083] = "BATTLE_2ND_RAMMING_ACCURACY";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_RAMMING_DODGE_RATING"] = 1084] = "BATTLE_2ND_RAMMING_DODGE_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_RAMMING_CRITICAL_RATING"] = 1085] = "BATTLE_2ND_RAMMING_CRITICAL_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_DEFENSE"] = 1086] = "BATTLE_ADDED_RAMMING_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_DEFENSE_PCT"] = 1087] = "BATTLE_ADDED_RAMMING_DEFENSE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_ACCURACY"] = 1088] = "BATTLE_ADDED_RAMMING_ACCURACY";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_ACCURACY_PCT"] = 1089] = "BATTLE_ADDED_RAMMING_ACCURACY_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_CRITICAL_RATING"] = 1090] = "BATTLE_ADDED_RAMMING_CRITICAL_RATING";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_CRITICAL_RATING_PCT"] = 1091] = "BATTLE_ADDED_RAMMING_CRITICAL_RATING_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_RAMMING_CRITICAL_DAMAGE_PCT"] = 1092] = "BATTLE_RAMMING_CRITICAL_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_DODGE"] = 1093] = "BATTLE_ADDED_RAMMING_DODGE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_DODGE_PCT"] = 1094] = "BATTLE_ADDED_RAMMING_DODGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_REPAIR_CRITICAL_RATING_PCT"] = 1095] = "BATTLE_ADDED_REPAIR_CRITICAL_RATING_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_REPAIR_CRITICAL_AMOUNT_PCT"] = 1096] = "BATTLE_REPAIR_CRITICAL_AMOUNT_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_HEAL_CRITICAL_RATING_PCT"] = 1097] = "BATTLE_ADDED_HEAL_CRITICAL_RATING_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_HEAL_CRITICAL_AMOUNT_PCT"] = 1098] = "BATTLE_HEAL_CRITICAL_AMOUNT_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_ACTION_POWER"] = 1099] = "BATTLE_ADDED_ACTION_POWER";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_ACTION_POWER_PCT"] = 1100] = "BATTLE_ADDED_ACTION_POWER_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DEBUFF_RESISTANCE_ALL_PCT"] = 1101] = "BATTLE_DEBUFF_RESISTANCE_ALL_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DEBUFF_RESISTANCE_CANNON_PCT"] = 1102] = "BATTLE_DEBUFF_RESISTANCE_CANNON_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DEBUFF_RESISTANCE_SHIP_PCT"] = 1103] = "BATTLE_DEBUFF_RESISTANCE_SHIP_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DEBUFF_RESISTANCE_MOVE_PCT"] = 1104] = "BATTLE_DEBUFF_RESISTANCE_MOVE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DEBUFF_RESISTANCE_SAILOR_PCT"] = 1105] = "BATTLE_DEBUFF_RESISTANCE_SAILOR_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_DAMAGE_BONUS_PCT"] = 1106] = "BATTLE_DAMAGE_BONUS_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ACCURACY_BONUS_PCT"] = 1107] = "BATTLE_ACCURACY_BONUS_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_CRITICAL_RATING_BONUS_PCT"] = 1108] = "BATTLE_CRITICAL_RATING_BONUS_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_CRITICAL_DAMAGE_PCT"] = 1109] = "BATTLE_ADDED_CANNON_CRITICAL_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_CRITICAL_DAMAGE_PCT"] = 1110] = "BATTLE_ADDED_MELEE_CRITICAL_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_CRITICAL_DAMAGE_PCT"] = 1111] = "BATTLE_ADDED_RAMMING_CRITICAL_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_REPAIR_CRITICAL_AMOUNT_PCT"] = 1112] = "BATTLE_ADDED_REPAIR_CRITICAL_AMOUNT_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_HEAL_CRITICAL_AMOUNT_PCT"] = 1113] = "BATTLE_ADDED_HEAL_CRITICAL_AMOUNT_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_SHIP_CANNON_DEFENSE"] = 1114] = "BATTLE_SHIP_CANNON_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_SHIP_MELEE_DEFENSE"] = 1115] = "BATTLE_SHIP_MELEE_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_SHIP_RAMMING_DEFENSE"] = 1116] = "BATTLE_SHIP_RAMMING_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_SHIP_REPAIR_AMOUNT"] = 1117] = "BATTLE_SHIP_REPAIR_AMOUNT";
    STAT_TYPE[STAT_TYPE["BATTLE_SHIP_HEAL_AMOUNT"] = 1118] = "BATTLE_SHIP_HEAL_AMOUNT";
    STAT_TYPE[STAT_TYPE["BATTLE_RAMMING_DISTANCE_BONUS_PCT"] = 1119] = "BATTLE_RAMMING_DISTANCE_BONUS_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MIN_SAILOR"] = 1120] = "BATTLE_2ND_MIN_SAILOR";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MAX_SAILOR"] = 1121] = "BATTLE_2ND_MAX_SAILOR";
    STAT_TYPE[STAT_TYPE["BATTLE_CEQUIP_ATTACK"] = 1122] = "BATTLE_CEQUIP_ATTACK";
    STAT_TYPE[STAT_TYPE["BATTLE_CEQUIP_DEFENSE"] = 1123] = "BATTLE_CEQUIP_DEFENSE";
    STAT_TYPE[STAT_TYPE["BATTLE_CANNON_ADD_TAKE_DAMAGE"] = 1124] = "BATTLE_CANNON_ADD_TAKE_DAMAGE";
    STAT_TYPE[STAT_TYPE["BATTLE_CANNON_ADD_TAKE_DAMAGE_PCT"] = 1125] = "BATTLE_CANNON_ADD_TAKE_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_MELEE_ADD_TAKE_DAMAGE"] = 1126] = "BATTLE_MELEE_ADD_TAKE_DAMAGE";
    STAT_TYPE[STAT_TYPE["BATTLE_MELEE_ADD_TAKE_DAMAGE_PCT"] = 1127] = "BATTLE_MELEE_ADD_TAKE_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_RAMMING_ADD_TAKE_DAMAGE"] = 1128] = "BATTLE_RAMMING_ADD_TAKE_DAMAGE";
    STAT_TYPE[STAT_TYPE["BATTLE_RAMMING_ADD_TAKE_DAMAGE_PCT"] = 1129] = "BATTLE_RAMMING_ADD_TAKE_DAMAGE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_CRITICAL_RATING_RESISTANCE"] = 1130] = "BATTLE_ADDED_CANNON_CRITICAL_RATING_RESISTANCE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_CRITICAL_RATING_RESISTANCE_PCT"] = 1131] = "BATTLE_ADDED_CANNON_CRITICAL_RATING_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_CANNON_CRITICAL_RATING_RESISTANCE"] = 1132] = "BATTLE_2ND_CANNON_CRITICAL_RATING_RESISTANCE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_CANNON_CRITICAL_DAMAGE_RESISTANCE_PCT"] = 1133] = "BATTLE_ADDED_CANNON_CRITICAL_DAMAGE_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_CANNON_CRITICAL_DAMAGE_RESISTANCE_PCT"] = 1134] = "BATTLE_2ND_CANNON_CRITICAL_DAMAGE_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_CRITICAL_RATING_RESISTANCE"] = 1135] = "BATTLE_ADDED_MELEE_CRITICAL_RATING_RESISTANCE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_CRITICAL_RATING_RESISTANCE_PCT"] = 1136] = "BATTLE_ADDED_MELEE_CRITICAL_RATING_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MELEE_CRITICAL_RATING_RESISTANCE"] = 1137] = "BATTLE_2ND_MELEE_CRITICAL_RATING_RESISTANCE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_MELEE_CRITICAL_DAMAGE_RESISTANCE_PCT"] = 1138] = "BATTLE_ADDED_MELEE_CRITICAL_DAMAGE_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_MELEE_CRITICAL_DAMAGE_RESISTANCE_PCT"] = 1139] = "BATTLE_2ND_MELEE_CRITICAL_DAMAGE_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_CRITICAL_RATING_RESISTANCE"] = 1140] = "BATTLE_ADDED_RAMMING_CRITICAL_RATING_RESISTANCE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_CRITICAL_RATING_RESISTANCE_PCT"] = 1141] = "BATTLE_ADDED_RAMMING_CRITICAL_RATING_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_RAMMING_CRITICAL_RATING_RESISTANCE"] = 1142] = "BATTLE_2ND_RAMMING_CRITICAL_RATING_RESISTANCE";
    STAT_TYPE[STAT_TYPE["BATTLE_ADDED_RAMMING_CRITICAL_DAMAGE_RESISTANCE_PCT"] = 1143] = "BATTLE_ADDED_RAMMING_CRITICAL_DAMAGE_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_2ND_RAMMING_CRITICAL_DAMAGE_RESISTANCE_PCT"] = 1144] = "BATTLE_2ND_RAMMING_CRITICAL_DAMAGE_RESISTANCE_PCT";
    STAT_TYPE[STAT_TYPE["BATTLE_SHIP_ACTION_POWER"] = 1145] = "BATTLE_SHIP_ACTION_POWER";
    // 교역 (2000 ~ 2999)
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_TRIGGER_RATING"] = 2001] = "TRADE_NEGOTIATE_TRIGGER_RATING";
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_TRIGGER_RATING_PCT"] = 2002] = "TRADE_NEGOTIATE_TRIGGER_RATING_PCT";
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_SUCCESS_RATING"] = 2003] = "TRADE_NEGOTIATE_SUCCESS_RATING";
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_SUCCESS_RATING_PCT"] = 2004] = "TRADE_NEGOTIATE_SUCCESS_RATING_PCT";
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_COUNT"] = 2005] = "TRADE_NEGOTIATE_COUNT";
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_COUNT_PCT"] = 2006] = "TRADE_NEGOTIATE_COUNT_PCT";
    STAT_TYPE[STAT_TYPE["TRADE_WORLD_MAP_PREDICT_PRICE"] = 2007] = "TRADE_WORLD_MAP_PREDICT_PRICE";
    STAT_TYPE[STAT_TYPE["TOWN_KICK_ARREST_NEGO_SUCCESS_PROBABILITY"] = 2008] = "TOWN_KICK_ARREST_NEGO_SUCCESS_PROBABILITY";
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_BUY_PRICE_PCT"] = 2009] = "TRADE_NEGOTIATE_BUY_PRICE_PCT";
    STAT_TYPE[STAT_TYPE["TRADE_NEGOTIATE_SALE_PRICE_PCT"] = 2010] = "TRADE_NEGOTIATE_SALE_PRICE_PCT";
    STAT_TYPE[STAT_TYPE["TRADE_BUY_PRICE_PCT"] = 2013] = "TRADE_BUY_PRICE_PCT";
    STAT_TYPE[STAT_TYPE["TRADE_SALE_PRICE_PCT"] = 2014] = "TRADE_SALE_PRICE_PCT";
    // 모험 (3000 ~ 3999)
    STAT_TYPE[STAT_TYPE["ADVENTURE_SURVEILLANCE_RANGE"] = 3001] = "ADVENTURE_SURVEILLANCE_RANGE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_DISASTER_PREDICTION_RATE"] = 3002] = "ADVENTURE_DISASTER_PREDICTION_RATE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_SAIL_SPEED"] = 3005] = "ADVENTURE_SAIL_SPEED";
    STAT_TYPE[STAT_TYPE["ADVENTURE_SEARCH_RANGE"] = 3006] = "ADVENTURE_SEARCH_RANGE";
    STAT_TYPE[STAT_TYPE["BONUS_FRIENDSHIP_POINT"] = 3114] = "BONUS_FRIENDSHIP_POINT";
    STAT_TYPE[STAT_TYPE["ADVENTURE_DISCOVER_CHANCE_NATURE"] = 3115] = "ADVENTURE_DISCOVER_CHANCE_NATURE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_DISCOVER_CHANCE_ANIMAL"] = 3116] = "ADVENTURE_DISCOVER_CHANCE_ANIMAL";
    STAT_TYPE[STAT_TYPE["ADVENTURE_DISCOVER_CHANCE_PLANT"] = 3117] = "ADVENTURE_DISCOVER_CHANCE_PLANT";
    STAT_TYPE[STAT_TYPE["ADVENTURE_DISCOVER_CHANCE_ARTIFACT"] = 3118] = "ADVENTURE_DISCOVER_CHANCE_ARTIFACT";
    STAT_TYPE[STAT_TYPE["ADVENTURE_DISCOVER_CHANCE_TREASURE"] = 3119] = "ADVENTURE_DISCOVER_CHANCE_TREASURE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE"] = 3120] = "ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_LAND_EXPLORE_REWARD_DUCAT_GAIN_PCT"] = 3118] = "ADVENTURE_LAND_EXPLORE_REWARD_DUCAT_GAIN_PCT";
    STAT_TYPE[STAT_TYPE["FISHING_SUCCESS_RATE"] = 3131] = "FISHING_SUCCESS_RATE";
    STAT_TYPE[STAT_TYPE["FISHING_GOODFISH_DROP_RATE"] = 3132] = "FISHING_GOODFISH_DROP_RATE";
    STAT_TYPE[STAT_TYPE["TOWN_KICK_ARREST_RUN_AWAY_SUCCESS_PROBABILITY"] = 3141] = "TOWN_KICK_ARREST_RUN_AWAY_SUCCESS_PROBABILITY";
    STAT_TYPE[STAT_TYPE["DISCOVERY_DROP_RATE"] = 3142] = "DISCOVERY_DROP_RATE";
    STAT_TYPE[STAT_TYPE["REPORT_DISCOVERY_GET_DUCAT_INCREASE"] = 3143] = "REPORT_DISCOVERY_GET_DUCAT_INCREASE";
    STAT_TYPE[STAT_TYPE["REPORT_DISCOVERY_GET_FAME_INCREASE"] = 3144] = "REPORT_DISCOVERY_GET_FAME_INCREASE";
    STAT_TYPE[STAT_TYPE["VILLAGE_PLUNDER_REDUCE_FRIENDSHIP_DECREASE"] = 3146] = "VILLAGE_PLUNDER_REDUCE_FRIENDSHIP_DECREASE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_EXPLORE_COMBAT"] = 3148] = "ADVENTURE_EXPLORE_COMBAT";
    STAT_TYPE[STAT_TYPE["ADVENTURE_EXPLORE_GATHERING"] = 3149] = "ADVENTURE_EXPLORE_GATHERING";
    STAT_TYPE[STAT_TYPE["ADVENTURE_EXPLORE_OBSERVATION"] = 3150] = "ADVENTURE_EXPLORE_OBSERVATION";
    STAT_TYPE[STAT_TYPE["ADVENTURE_REDUCE_FOOD_AND_WATER"] = 3151] = "ADVENTURE_REDUCE_FOOD_AND_WATER";
    STAT_TYPE[STAT_TYPE["ADVENTURE_REDUCE_COMBAT_SAILOR_DEATH"] = 3152] = "ADVENTURE_REDUCE_COMBAT_SAILOR_DEATH";
    STAT_TYPE[STAT_TYPE["ADVENTURE_REDUCE_COMBAT_MATE_INJURED"] = 3153] = "ADVENTURE_REDUCE_COMBAT_MATE_INJURED";
    STAT_TYPE[STAT_TYPE["ADVENTURE_INCREASE_HIGH_GRADE_EVENT_SPOT"] = 3154] = "ADVENTURE_INCREASE_HIGH_GRADE_EVENT_SPOT";
    STAT_TYPE[STAT_TYPE["REPORT_RESOURCE_GET_DUCAT_INCREASE"] = 3155] = "REPORT_RESOURCE_GET_DUCAT_INCREASE";
    STAT_TYPE[STAT_TYPE["REPORT_RESOURCE_GET_FAME_INCREASE"] = 3156] = "REPORT_RESOURCE_GET_FAME_INCREASE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_INCREASE_RATE_REWARD_COMBAT"] = 3157] = "ADVENTURE_INCREASE_RATE_REWARD_COMBAT";
    STAT_TYPE[STAT_TYPE["ADVENTURE_INCREASE_RATE_REWARD_OBSERVATION"] = 3158] = "ADVENTURE_INCREASE_RATE_REWARD_OBSERVATION";
    STAT_TYPE[STAT_TYPE["ADVENTURE_INCREASE_RATE_REWARD_GATHER"] = 3159] = "ADVENTURE_INCREASE_RATE_REWARD_GATHER";
    STAT_TYPE[STAT_TYPE["ADVENTURE_REDUCE_PURCHASE_ADVENTURE_ITEM"] = 3160] = "ADVENTURE_REDUCE_PURCHASE_ADVENTURE_ITEM";
    STAT_TYPE[STAT_TYPE["ADVENTURE_GET_COMBAT_STAT_VERSUS_PIRATE"] = 3161] = "ADVENTURE_GET_COMBAT_STAT_VERSUS_PIRATE";
    STAT_TYPE[STAT_TYPE["ADVENTURE_GET_COMBAT_STAT_VERSUS_WILD"] = 3162] = "ADVENTURE_GET_COMBAT_STAT_VERSUS_WILD";
    STAT_TYPE[STAT_TYPE["ADVENTURE_INCREASE_RATE_DISCOVERY"] = 3163] = "ADVENTURE_INCREASE_RATE_DISCOVERY";
    STAT_TYPE[STAT_TYPE["ADVENTURE_INCREASE_GET_FOOD_WATER_WOOD"] = 3164] = "ADVENTURE_INCREASE_GET_FOOD_WATER_WOOD";
    // 건물 (4000 ~ 4999)
    STAT_TYPE[STAT_TYPE["BUILDING_BANK_WITHDRAWAL_FEE_MODIFIER"] = 4001] = "BUILDING_BANK_WITHDRAWAL_FEE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_DEPART_SUPPLY_PRICE_MODIFIER"] = 4002] = "BUILDING_DEPART_SUPPLY_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHIPYARD_SHIP_PRICE_MODIFIER"] = 4003] = "BUILDING_SHIPYARD_SHIP_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHIPYARD_REPAIRING_PRICE_MODIFIER"] = 4004] = "BUILDING_SHIPYARD_REPAIRING_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHIPYARD_SHIP_SALE_PRICE_MODIFIER"] = 4005] = "BUILDING_SHIPYARD_SHIP_SALE_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_PUB_SAILOR_DRAFTING_PRICE_MODIFIER"] = 4006] = "BUILDING_PUB_SAILOR_DRAFTING_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_PUB_MATE_RECRUITING_PRICE_MODIFIER"] = 4007] = "BUILDING_PUB_MATE_RECRUITING_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_RELIGION_DONATION_PRICE_MODIFIER"] = 4008] = "BUILDING_RELIGION_DONATION_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_TRADE_GOODS_PRICE_MODIFIER"] = 4009] = "BUILDING_TRADE_GOODS_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_TRADE_GOODS_SALE_PRICE_MODIFIER"] = 4010] = "BUILDING_TRADE_GOODS_SALE_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_PUB_DRINK_PRICE_MODIFIER"] = 4011] = "BUILDING_PUB_DRINK_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_DRAFTABLE_SAILORS"] = 4012] = "BUILDING_DRAFTABLE_SAILORS";
    STAT_TYPE[STAT_TYPE["BUILDING_MATE_INTIMACY"] = 4013] = "BUILDING_MATE_INTIMACY";
    STAT_TYPE[STAT_TYPE["BUILDING_RELIGION_PRAYER_PRICE_MODIFIER"] = 4014] = "BUILDING_RELIGION_PRAYER_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHOP_EQUIP_BUY_PRICE_MODIFIER"] = 4015] = "BUILDING_SHOP_EQUIP_BUY_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHOP_EQUIP_SELL_PRICE_MODIFIER"] = 4016] = "BUILDING_SHOP_EQUIP_SELL_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHOP_ITEM_BUY_PRICE_MODIFIER"] = 4017] = "BUILDING_SHOP_ITEM_BUY_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHOP_ITEM_SELL_PRICE_MODIFIER"] = 4018] = "BUILDING_SHOP_ITEM_SELL_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHOP_BLACKMARKET_BUY_PRICE_MODIFIER"] = 4019] = "BUILDING_SHOP_BLACKMARKET_BUY_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_MANTIC_FORTUNE_PRICE_MODIFIER"] = 4022] = "BUILDING_MANTIC_FORTUNE_PRICE_MODIFIER";
    STAT_TYPE[STAT_TYPE["BUILDING_SHIPYARD_SHIP_SLOT_ITEM_PRICE_MODIFIER"] = 4023] = "BUILDING_SHIPYARD_SHIP_SLOT_ITEM_PRICE_MODIFIER";
})(STAT_TYPE = exports.STAT_TYPE || (exports.STAT_TYPE = {}));
// WorldActiveEfect
// https://docs.google.com/spreadsheets/d/15gj00RrBf27b3DLg7MeYtZVKBjKKXaTtLkAQnaYjlSk/edit?pli=1#gid=1976171197
var ACTIVE_EFFECT;
(function (ACTIVE_EFFECT) {
    // 1척의 랜덤 선박 적용
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_WATER_RANDOMLY_SELECT_ONE_SHIP"] = 75110002] = "FLEET_CHANGE_WATER_RANDOMLY_SELECT_ONE_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_WATER_PCT_RANDOMLY_SELECT_ONE_SHIP"] = 75110003] = "FLEET_CHANGE_WATER_PCT_RANDOMLY_SELECT_ONE_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_FOOD_RANDOMLY_SELECT_ONE_SHIP"] = 75110004] = "FLEET_CHANGE_FOOD_RANDOMLY_SELECT_ONE_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_FOOD_PCT_RANDOMLY_SELECT_ONE_SHIP"] = 75110005] = "FLEET_CHANGE_FOOD_PCT_RANDOMLY_SELECT_ONE_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_DURABILITY_RANDOMLY_SELECT_ONE_SHIP"] = 75110006] = "FLEET_CHANGE_DURABILITY_RANDOMLY_SELECT_ONE_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_DURABILITY_PCT_RANDOMLY_SELECT_ONE_SHIP"] = 75110007] = "FLEET_CHANGE_DURABILITY_PCT_RANDOMLY_SELECT_ONE_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_SAILOR_RANDOMLY_SELECT_ONE_SHIP"] = 75110009] = "FLEET_CHANGE_SAILOR_RANDOMLY_SELECT_ONE_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_SAILOR_PCT_RANDOMLY_SELECT_ONE_SHIP"] = 75110010] = "FLEET_CHANGE_SAILOR_PCT_RANDOMLY_SELECT_ONE_SHIP";
    // 모든 선박에게 동일 적용
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_WATRER_EQUALLY_ALL_SHIP"] = 75110102] = "FLEET_CHANGE_WATRER_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_WATRER_PCT_EQUALLY_ALL_SHIP"] = 75110103] = "FLEET_CHANGE_WATRER_PCT_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_FOOD_EQUALLY_ALL_SHIP"] = 75110104] = "FLEET_CHANGE_FOOD_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_FOOD_PCT_EQUALLY_ALL_SHIP"] = 75110105] = "FLEET_CHANGE_FOOD_PCT_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_DURABILITY_EQUALLY_ALL_SHIP"] = 75110106] = "FLEET_CHANGE_DURABILITY_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_DURABILITY_PCT_EQUALLY_ALL_SHIP"] = 75110107] = "FLEET_CHANGE_DURABILITY_PCT_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_SAILOR_EQUALLY_ALL_SHIP"] = 75110109] = "FLEET_CHANGE_SAILOR_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_SAILOR_PCT_EQUALLY_ALL_SHIP"] = 75110110] = "FLEET_CHANGE_SAILOR_PCT_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_LOYALTY_EQUALLY_ALL_SHIP"] = 75111026] = "FLEET_CHANGE_LOYALTY_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_LOYALTY_PCT_EQUALLY_ALL_SHIP"] = 75111027] = "FLEET_CHANGE_LOYALTY_PCT_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_REMOVE_DISASTER_EQUALLY_ALL_SHIP"] = 75111028] = "FLEET_REMOVE_DISASTER_EQUALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_REMOVE_MATE_INJURY_EQUALLY_ALL_SHIP"] = 75111038] = "FLEET_REMOVE_MATE_INJURY_EQUALLY_ALL_SHIP";
    // 1번 선박부터 순차적으로 가능한 만큼 적용
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_WATRER_SEQUENTIALLY_ALL_SHIP"] = 75111039] = "FLEET_CHANGE_WATRER_SEQUENTIALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["FLEET_CHANGE_FOOD_SEQUENTIALLY_ALL_SHIP"] = 75111040] = "FLEET_CHANGE_FOOD_SEQUENTIALLY_ALL_SHIP";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_LOYALTY"] = 75110012] = "SHIP_CHANGE_LOYALTY";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_LOYALTY_PCT"] = 75110013] = "SHIP_CHANGE_LOYALTY_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_WATER"] = 75111001] = "SHIP_CHANGE_WATER";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_WATER_PCT"] = 75111002] = "SHIP_CHANGE_WATER_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_FOOD"] = 75111003] = "SHIP_CHANGE_FOOD";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_FOOD_PCT"] = 75111004] = "SHIP_CHANGE_FOOD_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_DURABILITY"] = 75111005] = "SHIP_CHANGE_DURABILITY";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_DURABILITY_PCT"] = 75111006] = "SHIP_CHANGE_DURABILITY_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_SAILOR"] = 75111008] = "SHIP_CHANGE_SAILOR";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_SAILOR_PCT"] = 75111009] = "SHIP_CHANGE_SAILOR_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_COMBUSTIBLE"] = 75111016] = "SHIP_CHANGE_TRADE_GOODS_LIKE_COMBUSTIBLE";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_COMBUSTIBLE_PCT"] = 75111017] = "SHIP_CHANGE_TRADE_GOODS_LIKE_COMBUSTIBLE_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_VALUABLE"] = 75111018] = "SHIP_CHANGE_TRADE_GOODS_LIKE_VALUABLE";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_VALUABLE_PCT"] = 75111019] = "SHIP_CHANGE_TRADE_GOODS_LIKE_VALUABLE_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_SPOILABLE"] = 75111020] = "SHIP_CHANGE_TRADE_GOODS_LIKE_SPOILABLE";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_SPOILABLE_PCT"] = 75111021] = "SHIP_CHANGE_TRADE_GOODS_LIKE_SPOILABLE_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_FALLDEAD"] = 75111022] = "SHIP_CHANGE_TRADE_GOODS_LIKE_FALLDEAD";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_FALLDEAD_PCT"] = 75111023] = "SHIP_CHANGE_TRADE_GOODS_LIKE_FALLDEAD_PCT";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_BREEDING"] = 75111024] = "SHIP_CHANGE_TRADE_GOODS_LIKE_BREEDING";
    ACTIVE_EFFECT[ACTIVE_EFFECT["SHIP_CHANGE_TRADE_GOODS_LIKE_BREEDING_PCT"] = 75111025] = "SHIP_CHANGE_TRADE_GOODS_LIKE_BREEDING_PCT";
})(ACTIVE_EFFECT = exports.ACTIVE_EFFECT || (exports.ACTIVE_EFFECT = {}));
// WorldPassiveEfect
// https://docs.google.com/spreadsheets/d/15gj00RrBf27b3DLg7MeYtZVKBjKKXaTtLkAQnaYjlSk/edit?pli=1#gid=*********
// 월드에서 (타운/항해) 사용되는 효과들인데, stat 과 매우 유사함.
// 하지만, 그 정의가 다른만큼, 추후 기획 요구에 대응하기 위해 스탯과는 일단 분리해둠.
var PASSIVE_EFFECT;
(function (PASSIVE_EFFECT) {
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_ADDED_EXP"] = 75300000] = "COMPANY_ADDED_EXP";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_ADDED_EXP_PCT"] = 75300001] = "COMPANY_ADDED_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ALL_EXP"] = 75300010] = "MATE_ADDED_ALL_EXP";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ALL_EXP_PCT"] = 75300011] = "MATE_ADDED_ALL_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_BATTLE_EXP"] = 75300020] = "MATE_ADDED_BATTLE_EXP";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_BATTLE_EXP_PCT"] = 75300021] = "MATE_ADDED_BATTLE_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_TRADE_EXP"] = 75300030] = "MATE_ADDED_TRADE_EXP";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_TRADE_EXP_PCT"] = 75300031] = "MATE_ADDED_TRADE_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ADVENTURE_EXP"] = 75300040] = "MATE_ADDED_ADVENTURE_EXP";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ADVENTURE_EXP_PCT"] = 75300041] = "MATE_ADDED_ADVENTURE_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_BATTLE_FAME"] = 75300050] = "MATE_ADDED_BATTLE_FAME";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_BATTLE_FAME_PCT"] = 75300051] = "MATE_ADDED_BATTLE_FAME_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_TRADE_FAME"] = 75300060] = "MATE_ADDED_TRADE_FAME";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_TRADE_FAME_PCT"] = 75300061] = "MATE_ADDED_TRADE_FAME_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ADVENTURE_FAME"] = 75300070] = "MATE_ADDED_ADVENTURE_FAME";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ADVENTURE_FAME_PCT"] = 75300071] = "MATE_ADDED_ADVENTURE_FAME_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_GOODS_PRICE"] = 75300080] = "TRADE_GOODS_PRICE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_GOODS_PRICE_PCT"] = 75300081] = "TRADE_GOODS_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_NEGOTIATE_DISCOUNT_RATE"] = 75300090] = "TRADE_NEGOTIATE_DISCOUNT_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_NEGOTIATE_EXTRA_CHARGE_RATE"] = 75300100] = "TRADE_NEGOTIATE_EXTRA_CHARGE_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_TARIFF_RATE"] = 75300110] = "TRADE_TARIFF_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_RESTOCK_DURATION"] = 75300120] = "TRADE_RESTOCK_DURATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_RESTOCK_DURATION_PCT"] = 75300121] = "TRADE_RESTOCK_DURATION_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DEVELOPMENT_COMMERCE"] = 75300130] = "TRADE_DEVELOPMENT_COMMERCE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DEVELOPMENT_COMMERCE_PCT"] = 75300131] = "TRADE_DEVELOPMENT_COMMERCE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DEVELOPMENT_ARMORY"] = 75300132] = "TRADE_DEVELOPMENT_ARMORY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DEVELOPMENT_ARMORY_PCT"] = 75300133] = "TRADE_DEVELOPMENT_ARMORY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DEVELOPMENT_INDUSTRY"] = 75300134] = "TRADE_DEVELOPMENT_INDUSTRY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DEVELOPMENT_INDUSTRY_PCT"] = 75300135] = "TRADE_DEVELOPMENT_INDUSTRY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_FOOD"] = 75300140] = "TRADE_ADDED_BUYABLE_FOOD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_FOOD_PCT"] = 75300141] = "TRADE_ADDED_BUYABLE_FOOD_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_CONDIMENT"] = 75300150] = "TRADE_ADDED_BUYABLE_CONDIMENT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_CONDIMENT_PCT"] = 75300151] = "TRADE_ADDED_BUYABLE_CONDIMENT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_LIVESTOCK"] = 75300160] = "TRADE_ADDED_BUYABLE_LIVESTOCK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_LIVESTOCK_PCT"] = 75300161] = "TRADE_ADDED_BUYABLE_LIVESTOCK_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_MEDICINE"] = 75300170] = "TRADE_ADDED_BUYABLE_MEDICINE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_MEDICINE_PCT"] = 75300171] = "TRADE_ADDED_BUYABLE_MEDICINE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_GENERAL"] = 75300180] = "TRADE_ADDED_BUYABLE_GENERAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_GENERAL_PCT"] = 75300181] = "TRADE_ADDED_BUYABLE_GENERAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_LIQUOR"] = 75300190] = "TRADE_ADDED_BUYABLE_LIQUOR";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_LIQUOR_PCT"] = 75300191] = "TRADE_ADDED_BUYABLE_LIQUOR_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_DYE"] = 75300200] = "TRADE_ADDED_BUYABLE_DYE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_DYE_PCT"] = 75300201] = "TRADE_ADDED_BUYABLE_DYE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_ORE"] = 75300210] = "TRADE_ADDED_BUYABLE_ORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_ORE_PCT"] = 75300211] = "TRADE_ADDED_BUYABLE_ORE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_INDUSTRIAL"] = 75300220] = "TRADE_ADDED_BUYABLE_INDUSTRIAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_INDUSTRIAL_PCT"] = 75300221] = "TRADE_ADDED_BUYABLE_INDUSTRIAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_LUXURY"] = 75300230] = "TRADE_ADDED_BUYABLE_LUXURY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_LUXURY_PCT"] = 75300231] = "TRADE_ADDED_BUYABLE_LUXURY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_TEXTILE"] = 75300240] = "TRADE_ADDED_BUYABLE_TEXTILE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_TEXTILE_PCT"] = 75300241] = "TRADE_ADDED_BUYABLE_TEXTILE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_FABRIC"] = 75300250] = "TRADE_ADDED_BUYABLE_FABRIC";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_FABRIC_PCT"] = 75300251] = "TRADE_ADDED_BUYABLE_FABRIC_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_WEAPON"] = 75300260] = "TRADE_ADDED_BUYABLE_WEAPON";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_WEAPON_PCT"] = 75300261] = "TRADE_ADDED_BUYABLE_WEAPON_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_FIREARM"] = 75300270] = "TRADE_ADDED_BUYABLE_FIREARM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_FIREARM_PCT"] = 75300271] = "TRADE_ADDED_BUYABLE_FIREARM_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_CRAFT"] = 75300280] = "TRADE_ADDED_BUYABLE_CRAFT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_CRAFT_PCT"] = 75300281] = "TRADE_ADDED_BUYABLE_CRAFT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_ART"] = 75300290] = "TRADE_ADDED_BUYABLE_ART";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_ART_PCT"] = 75300291] = "TRADE_ADDED_BUYABLE_ART_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_SPICE"] = 75300300] = "TRADE_ADDED_BUYABLE_SPICE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_SPICE_PCT"] = 75300301] = "TRADE_ADDED_BUYABLE_SPICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_JEWELRY"] = 75300310] = "TRADE_ADDED_BUYABLE_JEWELRY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_JEWELRY_PCT"] = 75300311] = "TRADE_ADDED_BUYABLE_JEWELRY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_AROMA"] = 75300320] = "TRADE_ADDED_BUYABLE_AROMA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_AROMA_PCT"] = 75300321] = "TRADE_ADDED_BUYABLE_AROMA_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_GEM"] = 75300330] = "TRADE_ADDED_BUYABLE_GEM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE_GEM_PCT"] = 75300331] = "TRADE_ADDED_BUYABLE_GEM_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_FOOD"] = 75300340] = "TRADE_DISCOUNT_FOOD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_CONDIMENT"] = 75300350] = "TRADE_DISCOUNT_CONDIMENT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_LIVESTOCK"] = 75300360] = "TRADE_DISCOUNT_LIVESTOCK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_MEDICINE"] = 75300370] = "TRADE_DISCOUNT_MEDICINE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_GENERAL"] = 75300380] = "TRADE_DISCOUNT_GENERAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_LIQUOR"] = 75300390] = "TRADE_DISCOUNT_LIQUOR";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_DYE"] = 75300400] = "TRADE_DISCOUNT_DYE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_ORE"] = 75300410] = "TRADE_DISCOUNT_ORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_INDUSTRIAL"] = 75300420] = "TRADE_DISCOUNT_INDUSTRIAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_LUXURY"] = 75300430] = "TRADE_DISCOUNT_LUXURY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_TEXTILE"] = 75300440] = "TRADE_DISCOUNT_TEXTILE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_FABRIC"] = 75300450] = "TRADE_DISCOUNT_FABRIC";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_WEAPON"] = 75300460] = "TRADE_DISCOUNT_WEAPON";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_FIREARM"] = 75300470] = "TRADE_DISCOUNT_FIREARM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_CRAFT"] = 75300480] = "TRADE_DISCOUNT_CRAFT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_ART"] = 75300490] = "TRADE_DISCOUNT_ART";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_SPICE"] = 75300500] = "TRADE_DISCOUNT_SPICE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_JEWELRY"] = 75300510] = "TRADE_DISCOUNT_JEWELRY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_AROMA"] = 75300520] = "TRADE_DISCOUNT_AROMA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_DISCOUNT_GEM"] = 75300530] = "TRADE_DISCOUNT_GEM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_FOOD"] = 75300540] = "TRADE_EXTRA_CHARGE_FOOD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_CONDIMENT"] = 75300550] = "TRADE_EXTRA_CHARGE_CONDIMENT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_LIVESTOCK"] = 75300560] = "TRADE_EXTRA_CHARGE_LIVESTOCK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_MEDICINE"] = 75300570] = "TRADE_EXTRA_CHARGE_MEDICINE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_GENERAL"] = 75300580] = "TRADE_EXTRA_CHARGE_GENERAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_LIQUOR"] = 75300590] = "TRADE_EXTRA_CHARGE_LIQUOR";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_DYE"] = 75300600] = "TRADE_EXTRA_CHARGE_DYE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_ORE"] = 75300610] = "TRADE_EXTRA_CHARGE_ORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_INDUSTRIAL"] = 75300620] = "TRADE_EXTRA_CHARGE_INDUSTRIAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_LUXURY"] = 75300630] = "TRADE_EXTRA_CHARGE_LUXURY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_TEXTILE"] = 75300640] = "TRADE_EXTRA_CHARGE_TEXTILE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_FABRIC"] = 75300650] = "TRADE_EXTRA_CHARGE_FABRIC";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_WEAPON"] = 75300660] = "TRADE_EXTRA_CHARGE_WEAPON";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_FIREARM"] = 75300670] = "TRADE_EXTRA_CHARGE_FIREARM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_CRAFT"] = 75300680] = "TRADE_EXTRA_CHARGE_CRAFT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_ART"] = 75300690] = "TRADE_EXTRA_CHARGE_ART";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_SPICE"] = 75300700] = "TRADE_EXTRA_CHARGE_SPICE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_JEWELRY"] = 75300710] = "TRADE_EXTRA_CHARGE_JEWELRY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_AROMA"] = 75300720] = "TRADE_EXTRA_CHARGE_AROMA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_EXTRA_CHARGE_GEM"] = 75300730] = "TRADE_EXTRA_CHARGE_GEM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_FOOD"] = 75300740] = "TRADE_PREDICT_PRICE_FOOD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_CONDIMENT"] = 75300750] = "TRADE_PREDICT_PRICE_CONDIMENT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_LIVESTOCK"] = 75300760] = "TRADE_PREDICT_PRICE_LIVESTOCK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_MEDICINE"] = 75300770] = "TRADE_PREDICT_PRICE_MEDICINE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_GENERAL"] = 75300780] = "TRADE_PREDICT_PRICE_GENERAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_LIQUOR"] = 75300790] = "TRADE_PREDICT_PRICE_LIQUOR";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_DYE"] = 75300800] = "TRADE_PREDICT_PRICE_DYE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_ORE"] = 75300810] = "TRADE_PREDICT_PRICE_ORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_INDUSTRIAL"] = 75300820] = "TRADE_PREDICT_PRICE_INDUSTRIAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_LUXURY"] = 75300830] = "TRADE_PREDICT_PRICE_LUXURY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_TEXTILE"] = 75300840] = "TRADE_PREDICT_PRICE_TEXTILE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_FABRIC"] = 75300850] = "TRADE_PREDICT_PRICE_FABRIC";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_WEAPON"] = 75300860] = "TRADE_PREDICT_PRICE_WEAPON";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_FIREARM"] = 75300870] = "TRADE_PREDICT_PRICE_FIREARM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_CRAFT"] = 75300880] = "TRADE_PREDICT_PRICE_CRAFT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_ART"] = 75300890] = "TRADE_PREDICT_PRICE_ART";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_SPICE"] = 75300900] = "TRADE_PREDICT_PRICE_SPICE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_JEWELRY"] = 75300910] = "TRADE_PREDICT_PRICE_JEWELRY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_AROMA"] = 75300920] = "TRADE_PREDICT_PRICE_AROMA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_PREDICT_PRICE_GEM"] = 75300930] = "TRADE_PREDICT_PRICE_GEM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_INVESTMENT_SCORE_PCT"] = 75300950] = "TRADE_INVESTMENT_SCORE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_GOODS_CHANGE_RATE_BREED_PCT"] = 75300960] = "TRADE_GOODS_CHANGE_RATE_BREED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_SAIL_SPEED"] = 75310000] = "ADVENTURE_ADDED_SHIP_SAIL_SPEED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_SAIL_SPEED_PCT"] = 75310001] = "ADVENTURE_ADDED_SHIP_SAIL_SPEED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_CONSUME_WATER"] = 75310002] = "ADVENTURE_ADDED_CONSUME_WATER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_CONSUME_WATER_PCT"] = 75310003] = "ADVENTURE_ADDED_CONSUME_WATER_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_CONSUME_FOOD"] = 75310004] = "ADVENTURE_ADDED_CONSUME_FOOD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_CONSUME_FOOD_PCT"] = 75310005] = "ADVENTURE_ADDED_CONSUME_FOOD_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_SAIL_DAMAGE_RATE"] = 75310006] = "ADVENTURE_ADDED_SHIP_SAIL_DAMAGE_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_KEY_DAMAGE_RATE"] = 75310007] = "ADVENTURE_ADDED_SHIP_KEY_DAMAGE_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_RADAR_RANGE"] = 75310008] = "ADVENTURE_ADDED_RADAR_RANGE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ENCOUNT_RATE"] = 75310009] = "ADVENTURE_ENCOUNT_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_MATE_INJURY_RATE"] = 75310010] = "ADVENTURE_ADDED_MATE_INJURY_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_MATE_INJURY_DURATION"] = 75310011] = "ADVENTURE_ADDED_MATE_INJURY_DURATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_MATE_INJURY_HEAL_RATE"] = 75310012] = "ADVENTURE_ADDED_MATE_INJURY_HEAL_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_FLEET_SAIL_SPEED"] = 75310013] = "ADVENTURE_ADDED_FLEET_SAIL_SPEED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_FLEET_SAIL_SPEED_PCT"] = 75310014] = "ADVENTURE_ADDED_FLEET_SAIL_SPEED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_HORIZONTAL_SAIL"] = 75310015] = "ADVENTURE_ADDED_SHIP_HORIZONTAL_SAIL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_HORIZONTAL_SAIL_PCT"] = 75310016] = "ADVENTURE_ADDED_SHIP_HORIZONTAL_SAIL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_VERTICAL_SAIL"] = 75310017] = "ADVENTURE_ADDED_SHIP_VERTICAL_SAIL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_VERTICAL_SAIL_PCT"] = 75310018] = "ADVENTURE_ADDED_SHIP_VERTICAL_SAIL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_OAR_POWER"] = 75310019] = "ADVENTURE_ADDED_SHIP_OAR_POWER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_OAL_POWER_PCT"] = 75310020] = "ADVENTURE_ADDED_SHIP_OAL_POWER_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_PREDICT_DISASTER"] = 75311000] = "ADVENTURE_PREDICT_DISASTER";
    // 재해 방어 관련
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_RATS"] = 75311001] = "ADVENTURE_DISASTER_DEFENSE_RATS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_FILTH"] = 75311002] = "ADVENTURE_DISASTER_DEFENSE_FILTH";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_SCURVY"] = 75311003] = "ADVENTURE_DISASTER_DEFENSE_SCURVY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_EPIDEMIC"] = 75311004] = "ADVENTURE_DISASTER_DEFENSE_EPIDEMIC";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_DECAY"] = 75311005] = "ADVENTURE_DISASTER_DEFENSE_DECAY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_MALARIA"] = 75311006] = "ADVENTURE_DISASTER_DEFENSE_MALARIA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_FIRE"] = 75311007] = "ADVENTURE_DISASTER_DEFENSE_FIRE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_SEAWEED"] = 75311008] = "ADVENTURE_DISASTER_DEFENSE_SEAWEED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_REEF"] = 75311009] = "ADVENTURE_DISASTER_DEFENSE_REEF";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_FLOODING"] = 75311010] = "ADVENTURE_DISASTER_DEFENSE_FLOODING";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_GREAT_FIRE"] = 75311011] = "ADVENTURE_DISASTER_DEFENSE_GREAT_FIRE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_REVOLT"] = 75311012] = "ADVENTURE_DISASTER_DEFENSE_REVOLT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_FIGHT"] = 75311013] = "ADVENTURE_DISASTER_DEFENSE_FIGHT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_CARGO_THEFT"] = 75311014] = "ADVENTURE_DISASTER_DEFENSE_CARGO_THEFT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_CARGO_DAMAGE"] = 75311015] = "ADVENTURE_DISASTER_DEFENSE_CARGO_DAMAGE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_FRUSTRATION"] = 75311016] = "ADVENTURE_DISASTER_DEFENSE_FRUSTRATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_ANXIETY"] = 75311017] = "ADVENTURE_DISASTER_DEFENSE_ANXIETY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_HOMESICK"] = 75311018] = "ADVENTURE_DISASTER_DEFENSE_HOMESICK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_INSOMNIA"] = 75311019] = "ADVENTURE_DISASTER_DEFENSE_INSOMNIA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_STORM"] = 75311020] = "ADVENTURE_DISASTER_DEFENSE_STORM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_BIG_WAVE"] = 75311021] = "ADVENTURE_DISASTER_DEFENSE_BIG_WAVE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_BLIZZARD"] = 75311022] = "ADVENTURE_DISASTER_DEFENSE_BLIZZARD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_WHIRLWIND"] = 75311023] = "ADVENTURE_DISASTER_DEFENSE_WHIRLWIND";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_WINDLESS"] = 75311024] = "ADVENTURE_DISASTER_DEFENSE_WINDLESS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_MAGNETIC_DISTURBANCE"] = 75311025] = "ADVENTURE_DISASTER_DEFENSE_MAGNETIC_DISTURBANCE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_SIREN"] = 75311026] = "ADVENTURE_DISASTER_DEFENSE_SIREN";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_SHARK"] = 75311027] = "ADVENTURE_DISASTER_DEFENSE_SHARK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_WHALE"] = 75311028] = "ADVENTURE_DISASTER_DEFENSE_WHALE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_HYPOTHERMIA"] = 75311029] = "ADVENTURE_DISASTER_DEFENSE_HYPOTHERMIA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_DEFENSE_FROSTBITE"] = 75311030] = "ADVENTURE_DISASTER_DEFENSE_FROSTBITE";
    // 재해 자연 해결 관련
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_RATS"] = 75311100] = "ADVENTURE_DISASTER_RESOLVE_RATS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_FILTH"] = 75311101] = "ADVENTURE_DISASTER_RESOLVE_FILTH";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_SCURVY"] = 75311102] = "ADVENTURE_DISASTER_RESOLVE_SCURVY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_EPIDEMIC"] = 75311103] = "ADVENTURE_DISASTER_RESOLVE_EPIDEMIC";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_DECAY"] = 75311104] = "ADVENTURE_DISASTER_RESOLVE_DECAY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_MALARIA"] = 75311105] = "ADVENTURE_DISASTER_RESOLVE_MALARIA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_FIRE"] = 75311106] = "ADVENTURE_DISASTER_RESOLVE_FIRE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_SEAWEED"] = 75311107] = "ADVENTURE_DISASTER_RESOLVE_SEAWEED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_FLOODING"] = 75311108] = "ADVENTURE_DISASTER_RESOLVE_FLOODING";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_GREAT_FIRE"] = 75311109] = "ADVENTURE_DISASTER_RESOLVE_GREAT_FIRE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_REVOLT"] = 75311110] = "ADVENTURE_DISASTER_RESOLVE_REVOLT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_FIGHT"] = 75311111] = "ADVENTURE_DISASTER_RESOLVE_FIGHT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_FRUSTRATION"] = 75311112] = "ADVENTURE_DISASTER_RESOLVE_FRUSTRATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_ANXIETY"] = 75311113] = "ADVENTURE_DISASTER_RESOLVE_ANXIETY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_HOMESICK"] = 75311114] = "ADVENTURE_DISASTER_RESOLVE_HOMESICK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_INSOMNIA"] = 75311115] = "ADVENTURE_DISASTER_RESOLVE_INSOMNIA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_STORM"] = 75311116] = "ADVENTURE_DISASTER_RESOLVE_STORM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_BLIZZARD"] = 75311117] = "ADVENTURE_DISASTER_RESOLVE_BLIZZARD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_WINDLESS"] = 75311118] = "ADVENTURE_DISASTER_RESOLVE_WINDLESS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_MAGNETIC_DISTURBANCE"] = 75311119] = "ADVENTURE_DISASTER_RESOLVE_MAGNETIC_DISTURBANCE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_SIREN"] = 75311120] = "ADVENTURE_DISASTER_RESOLVE_SIREN";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_SHARK"] = 75311121] = "ADVENTURE_DISASTER_RESOLVE_SHARK";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_HYPOTHERMIA"] = 75311122] = "ADVENTURE_DISASTER_RESOLVE_HYPOTHERMIA";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_FROSTBITE"] = 75311123] = "ADVENTURE_DISASTER_RESOLVE_FROSTBITE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_REEF"] = 75311124] = "ADVENTURE_DISASTER_RESOLVE_REEF";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_CARGO_THEFT"] = 75311125] = "ADVENTURE_DISASTER_RESOLVE_CARGO_THEFT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_RESOLVE_WHIRLWIND"] = 75311126] = "ADVENTURE_DISASTER_RESOLVE_WHIRLWIND";
    // 인카운트 보호 관련
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_PROTECT_ENCOUNT_FROM_USER"] = 75311150] = "ADVENTURE_PROTECT_ENCOUNT_FROM_USER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_PROTECT_ENCOUNT_FROM_NPC"] = 75311151] = "ADVENTURE_PROTECT_ENCOUNT_FROM_NPC";
    // 재해 발생 확률
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISASTER_GENERATE_PCT"] = 75312000] = "ADVENTURE_DISASTER_GENERATE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SHIELD_ADD_DAILY_COUNT_DISASTER"] = 75312001] = "SHIELD_ADD_DAILY_COUNT_DISASTER";
    // 전투 관련
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_ADDED_MAX_TURN_TAKE_BACK_COUNT"] = 75312002] = "BATTLE_ADDED_MAX_TURN_TAKE_BACK_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_ADDED_MAX_QUICK_MODE_COUNT"] = 75312003] = "BATTLE_ADDED_MAX_QUICK_MODE_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_TURN_TAKE_BACK_COUNT_UNLIMITED"] = 75312004] = "BATTLE_TURN_TAKE_BACK_COUNT_UNLIMITED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_QUICK_MODE_COUNT_UNLIMITED"] = 75312005] = "BATTLE_QUICK_MODE_COUNT_UNLIMITED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_GOODS_SALE_PRICE_PCT"] = 75312094] = "TRADE_GOODS_SALE_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_ADDED_MAX_PHASE_TAKE_BACK_COUNT"] = 75312097] = "BATTLE_ADDED_MAX_PHASE_TAKE_BACK_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_PHASE_TAKE_BACK_COUNT_UNLIMITED"] = 75312098] = "BATTLE_PHASE_TAKE_BACK_COUNT_UNLIMITED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_NO_TOWN_KICK_IF_TOWN_KICK_RESULT_IS_1"] = 75312008] = "ADVENTURE_NO_TOWN_KICK_IF_TOWN_KICK_RESULT_IS_1";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISATER_PROTECT"] = 75312009] = "ADVENTURE_DISATER_PROTECT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_INSURANCE_COST_PCT"] = 75312010] = "ADVENTURE_INSURANCE_COST_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_CHANGE_MATE_LOYALTY_DECREASE_SPEED_PCT"] = 75312011] = "ADVENTURE_CHANGE_MATE_LOYALTY_DECREASE_SPEED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_MATE_ADDED_BATTLE_EXP_COMPANY"] = 75312012] = "BATTLE_MATE_ADDED_BATTLE_EXP_COMPANY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_MATE_ADDED_BATTLE_EXP_PCT_COMPANY"] = 75312013] = "BATTLE_MATE_ADDED_BATTLE_EXP_PCT_COMPANY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_MATE_ADDED_TRADE_EXP_COMPANY"] = 75312014] = "TRADE_MATE_ADDED_TRADE_EXP_COMPANY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_MATE_ADDED_TRADE_EXP_PCT_COMPANY"] = 75312015] = "TRADE_MATE_ADDED_TRADE_EXP_PCT_COMPANY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_MATE_ADDED_ADVENTURE_EXP_COMPANY"] = 75312016] = "ADVENTURE_MATE_ADDED_ADVENTURE_EXP_COMPANY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_MATE_ADDED_ADVENTURE_EXP_PCT_COMPANY"] = 75312017] = "ADVENTURE_MATE_ADDED_ADVENTURE_EXP_PCT_COMPANY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISCOVER_CHANCE_NATURE"] = 75312018] = "ADVENTURE_DISCOVER_CHANCE_NATURE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISCOVER_CHANCE_ANIMAL"] = 75312019] = "ADVENTURE_DISCOVER_CHANCE_ANIMAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISCOVER_CHANCE_PLANT"] = 75312020] = "ADVENTURE_DISCOVER_CHANCE_PLANT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE"] = 75312021] = "ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISCOVER_CHANCE_ARTIFACT"] = 75312022] = "ADVENTURE_DISCOVER_CHANCE_ARTIFACT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_DISCOVER_CHANCE_TREASURE"] = 75312023] = "ADVENTURE_DISCOVER_CHANCE_TREASURE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_GALLEY_SHIP_SAIL_SPEED"] = 75312024] = "ADVENTURE_ADDED_GALLEY_SHIP_SAIL_SPEED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_GALLEY_SHIP_SAIL_SPEED_PCT"] = 75312025] = "ADVENTURE_ADDED_GALLEY_SHIP_SAIL_SPEED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_FLEET_ACCELERATION"] = 75312036] = "ADVENTURE_ADDED_FLEET_ACCELERATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_FLEET_ACCELERATION_PCT"] = 75312037] = "ADVENTURE_ADDED_FLEET_ACCELERATION_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_ACCELERATION"] = 75312038] = "ADVENTURE_ADDED_SHIP_ACCELERATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_ACCELERATION_PCT"] = 75312039] = "ADVENTURE_ADDED_SHIP_ACCELERATION_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_FLEET_ANGULARPOWER"] = 75312040] = "ADVENTURE_ADDED_FLEET_ANGULARPOWER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_FLEET_ANGULARPOWER_PCT"] = 75312041] = "ADVENTURE_ADDED_FLEET_ANGULARPOWER_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_ANGULARPOWER"] = 75312042] = "ADVENTURE_ADDED_SHIP_ANGULARPOWER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_SHIP_ANGULARPOWER_PCT"] = 75312043] = "ADVENTURE_ADDED_SHIP_ANGULARPOWER_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["FISHING_GOODFISH_DROP_RATE"] = 75312051] = "FISHING_GOODFISH_DROP_RATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["REPORT_DISCOVERY_GET_DUCAT_INCREASE"] = 75312054] = "REPORT_DISCOVERY_GET_DUCAT_INCREASE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["REPORT_DISCOVERY_GET_FAME_INCREASE"] = 75312055] = "REPORT_DISCOVERY_GET_FAME_INCREASE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["REPORT_RESOURCE_GET_DUCAT_INCREASE"] = 75312060] = "REPORT_RESOURCE_GET_DUCAT_INCREASE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["REPORT_RESOURCE_GET_FAME_INCREASE"] = 75312061] = "REPORT_RESOURCE_GET_FAME_INCREASE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_REDUCE_COMBAT_MATE_INJURED"] = 75312062] = "ADVENTURE_REDUCE_COMBAT_MATE_INJURED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_REDUCE_COMBAT_SAILOR_DEATH"] = 75312063] = "ADVENTURE_REDUCE_COMBAT_SAILOR_DEATH";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_INCREASE_RATE_REWARD_COMBAT"] = 75312064] = "ADVENTURE_INCREASE_RATE_REWARD_COMBAT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_INCREASE_RATE_REWARD_OBSERVATION"] = 75312065] = "ADVENTURE_INCREASE_RATE_REWARD_OBSERVATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_INCREASE_RATE_REWARD_GATHER"] = 75312066] = "ADVENTURE_INCREASE_RATE_REWARD_GATHER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_REDUCE_FOOD_AND_WATER"] = 75312067] = "ADVENTURE_REDUCE_FOOD_AND_WATER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_INCREASE_HIGH_GRADE_EVENT_SPOT"] = 75312068] = "ADVENTURE_INCREASE_HIGH_GRADE_EVENT_SPOT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_REDUCE_PURCHASE_ADVENTURE_ITEM"] = 75312069] = "ADVENTURE_REDUCE_PURCHASE_ADVENTURE_ITEM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_GET_COMBAT_STAT_VERSUS_PIRATE"] = 75312070] = "ADVENTURE_GET_COMBAT_STAT_VERSUS_PIRATE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_GET_COMBAT_STAT_VERSUS_WILD"] = 75312071] = "ADVENTURE_GET_COMBAT_STAT_VERSUS_WILD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_INCREASE_RATE_DISCOVERY"] = 75312072] = "ADVENTURE_INCREASE_RATE_DISCOVERY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_INCREASE_GET_FOOD_WATER_WOOD"] = 75312073] = "ADVENTURE_INCREASE_GET_FOOD_WATER_WOOD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADD_DAILY_TICKET_COUNT"] = 75312074] = "ADVENTURE_ADD_DAILY_TICKET_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BONUS_FRIENDSHIP_POINT"] = 75312075] = "BONUS_FRIENDSHIP_POINT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["VILLAGE_PLUNDER_REDUCE_FRIENDSHIP_DECREASE"] = 75312076] = "VILLAGE_PLUNDER_REDUCE_FRIENDSHIP_DECREASE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_SEARCH_RANGE"] = 75312077] = "ADVENTURE_SEARCH_RANGE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SHIELD_ADD_DAILY_COUNT_RESURRECTION"] = 75312092] = "SHIELD_ADD_DAILY_COUNT_RESURRECTION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_GOODS_BUY_PRICE_PCT"] = 75312093] = "TRADE_GOODS_BUY_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_ADDED_BUYABLE"] = 75312095] = "TRADE_ADDED_BUYABLE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_NEGOTIATE_TRIGGER_RATING"] = 75312096] = "TRADE_NEGOTIATE_TRIGGER_RATING";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SHIELD_ADD_DAILY_COUNT_RETURN_TO_TOWN"] = 75312099] = "SHIELD_ADD_DAILY_COUNT_RETURN_TO_TOWN";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_MATE_LOYALTY_DECREASE_PREVENT"] = 75312102] = "ADVENTURE_MATE_LOYALTY_DECREASE_PREVENT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_MATE_INJURY_PREVENT"] = 75312103] = "ADVENTURE_MATE_INJURY_PREVENT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_NEGOTIATE_SUCCESS_RATING"] = 75312104] = "TRADE_NEGOTIATE_SUCCESS_RATING";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXPLORE_ADDED_MAX_QUICK_MODE_COUNT"] = 75312105] = "EXPLORE_ADDED_MAX_QUICK_MODE_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_CONTINUOUS_ADDED_COUNT"] = 75312106] = "BATTLE_CONTINUOUS_ADDED_COUNT";
    // 함대안의 항해사 명성.
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_BATTLE_FAME_FLEET_FOR_ORDER"] = 75312107] = "MATE_ADDED_BATTLE_FAME_FLEET_FOR_ORDER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_BATTLE_FAME_PCT_FLEET_FOR_ORDER"] = 75312108] = "MATE_ADDED_BATTLE_FAME_PCT_FLEET_FOR_ORDER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_TRADE_FAME_FLEET_FOR_ORDER"] = 75312109] = "MATE_ADDED_TRADE_FAME_FLEET_FOR_ORDER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_TRADE_FAME_PCT_FLEET_FOR_ORDER"] = 75312110] = "MATE_ADDED_TRADE_FAME_PCT_FLEET_FOR_ORDER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ADVENTURE_FAME_FLEET_FOR_ORDER"] = 75312111] = "MATE_ADDED_ADVENTURE_FAME_FLEET_FOR_ORDER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["MATE_ADDED_ADVENTURE_FAME_PCT_FLEET_FOR_ORDER"] = 75312112] = "MATE_ADDED_ADVENTURE_FAME_PCT_FLEET_FOR_ORDER";
    // 함대안의 항해사 경험치.
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_MATE_ADDED_BATTLE_EXP_FLEET"] = 75312113] = "BATTLE_MATE_ADDED_BATTLE_EXP_FLEET";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BATTLE_MATE_ADDED_BATTLE_EXP_PCT_FLEET"] = 75312114] = "BATTLE_MATE_ADDED_BATTLE_EXP_PCT_FLEET";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_MATE_ADDED_TRADE_EXP_FLEET"] = 75312115] = "TRADE_MATE_ADDED_TRADE_EXP_FLEET";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_MATE_ADDED_TRADE_EXP_PCT_FLEET"] = 75312116] = "TRADE_MATE_ADDED_TRADE_EXP_PCT_FLEET";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_MATE_ADDED_ADVENTURE_EXP_FLEET"] = 75312117] = "ADVENTURE_MATE_ADDED_ADVENTURE_EXP_FLEET";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_MATE_ADDED_ADVENTURE_EXP_PCT_FLEET"] = 75312118] = "ADVENTURE_MATE_ADDED_ADVENTURE_EXP_PCT_FLEET";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_POINT_TO_EARN_DECREASE_PCT"] = 75312119] = "TRADE_POINT_TO_EARN_DECREASE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_ADDED_EXP_WITH_MATES_PCT"] = 75312120] = "COMPANY_ADDED_EXP_WITH_MATES_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_COMBO_INCREASE_MAXIMUM"] = 75312121] = "TRADE_COMBO_INCREASE_MAXIMUM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ALWAYS_ENTER_BLACK_MARKET"] = 75312122] = "ALWAYS_ENTER_BLACK_MARKET";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_FREE_WAYPOINT_SUPPLY"] = 75312123] = "ADVENTURE_FREE_WAYPOINT_SUPPLY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["CAPTURE_SHIP_ADDED_PCT"] = 75312124] = "CAPTURE_SHIP_ADDED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_ADDED_EXPIRE_TIME"] = 75312126] = "COMPANY_FLEET_DISPATCH_ADDED_EXPIRE_TIME";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_ADDED_EXPIRE_TIME_PCT"] = 75312127] = "COMPANY_FLEET_DISPATCH_ADDED_EXPIRE_TIME_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_BATTLE_ADDED_EXPIRE_TIME"] = 75312128] = "COMPANY_FLEET_DISPATCH_BATTLE_ADDED_EXPIRE_TIME";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_BATTLE_ADDED_EXPIRE_TIME_PCT"] = 75312129] = "COMPANY_FLEET_DISPATCH_BATTLE_ADDED_EXPIRE_TIME_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_TRADE_ADDED_EXPIRE_TIME"] = 75312130] = "COMPANY_FLEET_DISPATCH_TRADE_ADDED_EXPIRE_TIME";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_TRADE_ADDED_EXPIRE_TIME_PCT"] = 75312131] = "COMPANY_FLEET_DISPATCH_TRADE_ADDED_EXPIRE_TIME_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_ADVENTURE_ADDED_EXPIRE_TIME"] = 75312132] = "COMPANY_FLEET_DISPATCH_ADVENTURE_ADDED_EXPIRE_TIME";
    PASSIVE_EFFECT[PASSIVE_EFFECT["COMPANY_FLEET_DISPATCH_ADVENTURE_ADDED_EXPIRE_TIME_PCT"] = 75312133] = "COMPANY_FLEET_DISPATCH_ADVENTURE_ADDED_EXPIRE_TIME_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TASK_REWARD_INCREASE"] = 75312135] = "TASK_REWARD_INCREASE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TAX_FREE"] = 75312136] = "TAX_FREE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SHIP_BLUEPRINT_EXP_PCT"] = 75312137] = "SHIP_BLUEPRINT_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SHIP_SAIL_MASTERY_EXP_PCT"] = 75312138] = "SHIP_SAIL_MASTERY_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["LAND_EXPLORE_QUICK_MODE_COUNT_UNLIMITED"] = 75312140] = "LAND_EXPLORE_QUICK_MODE_COUNT_UNLIMITED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ORIENT_USER_BUILDING_EXP_PCT"] = 75312141] = "ORIENT_USER_BUILDING_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["WEST_USER_BUILDING_EXP_PCT"] = 75312142] = "WEST_USER_BUILDING_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ALL_SHIP_BUILDING_EXP_PCT"] = 75312143] = "ALL_SHIP_BUILDING_EXP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TRADE_BUY_ADDED_TRADE_GOODS_QUANTITY_PCT"] = 75312144] = "TRADE_BUY_ADDED_TRADE_GOODS_QUANTITY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["CHANGE_SHIP_BUILDING_TIME_PCT"] = 75312145] = "CHANGE_SHIP_BUILDING_TIME_PCT";
    // 보급품 가격 변경
    PASSIVE_EFFECT[PASSIVE_EFFECT["RELATE_ITEM_RATIO_PCT"] = 75312201] = "RELATE_ITEM_RATIO_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["KARMA_DECREASE"] = 75312204] = "KARMA_DECREASE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["FISHING_BIG_CATCH_ADDED_RELATE_ITEM"] = 75312210] = "FISHING_BIG_CATCH_ADDED_RELATE_ITEM";
    PASSIVE_EFFECT[PASSIVE_EFFECT["BUILDING_DEPART_SUPPLY_PRICE_MULTIPLY"] = 75312212] = "BUILDING_DEPART_SUPPLY_PRICE_MULTIPLY";
    // 물물교환
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_ADDED_COUNT"] = 75312151] = "EXCHANGE_ADDED_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_PCT"] = 75312152] = "EXCHANGE_GET_GOODS_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_FOOD_PCT"] = 75312153] = "EXCHANGE_GET_GOODS_FOOD_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_CONDIMENT_PCT"] = 75312154] = "EXCHANGE_GET_GOODS_CONDIMENT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_LIVESTOCK_PCT"] = 75312155] = "EXCHANGE_GET_GOODS_LIVESTOCK_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_MEDICINE_PCT"] = 75312156] = "EXCHANGE_GET_GOODS_MEDICINE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_GENERAL_PCT"] = 75312157] = "EXCHANGE_GET_GOODS_GENERAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_LIQUOR_PCT"] = 75312158] = "EXCHANGE_GET_GOODS_LIQUOR_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_DYE_PCT"] = 75312159] = "EXCHANGE_GET_GOODS_DYE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_ORE_PCT"] = 75312160] = "EXCHANGE_GET_GOODS_ORE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_INDUSTRIAL_PCT"] = 75312161] = "EXCHANGE_GET_GOODS_INDUSTRIAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_LUXURY_PCT"] = 75312162] = "EXCHANGE_GET_GOODS_LUXURY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_TEXTILE_PCT"] = 75312163] = "EXCHANGE_GET_GOODS_TEXTILE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_FABRIC_PCT"] = 75312164] = "EXCHANGE_GET_GOODS_FABRIC_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_CRAFT_PCT"] = 75312165] = "EXCHANGE_GET_GOODS_CRAFT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_ART_PCT"] = 75312166] = "EXCHANGE_GET_GOODS_ART_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_SPICE_PCT"] = 75312167] = "EXCHANGE_GET_GOODS_SPICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_JEWELRY_PCT"] = 75312168] = "EXCHANGE_GET_GOODS_JEWELRY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_AROMA_PCT"] = 75312169] = "EXCHANGE_GET_GOODS_AROMA_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_GEM_PCT"] = 75312170] = "EXCHANGE_GET_GOODS_GEM_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_WEAPON_PCT"] = 75312171] = "EXCHANGE_GET_GOODS_WEAPON_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_FIREARM_PCT"] = 75312172] = "EXCHANGE_GET_GOODS_FIREARM_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_PCT"] = 75312173] = "EXCHANGE_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_FOOD_PCT"] = 75312174] = "EXCHANGE_PRICE_FOOD_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_CONDIMENT_PCT"] = 75312175] = "EXCHANGE_PRICE_CONDIMENT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_LIVESTOCK_PCT"] = 75312176] = "EXCHANGE_PRICE_LIVESTOCK_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_MEDICINE_PCT"] = 75312177] = "EXCHANGE_PRICE_MEDICINE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_GENERAL_PCT"] = 75312178] = "EXCHANGE_PRICE_GENERAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_LIQUOR_PCT"] = 75312179] = "EXCHANGE_PRICE_LIQUOR_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_DYE_PCT"] = 75312180] = "EXCHANGE_PRICE_DYE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_ORE_PCT"] = 75312181] = "EXCHANGE_PRICE_ORE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_INDUSTRIAL_PCT"] = 75312182] = "EXCHANGE_PRICE_INDUSTRIAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_LUXURY_PCT"] = 75312183] = "EXCHANGE_PRICE_LUXURY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_TEXTILE_PCT"] = 75312184] = "EXCHANGE_PRICE_TEXTILE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_FABRIC_PCT"] = 75312185] = "EXCHANGE_PRICE_FABRIC_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_CRAFT_PCT"] = 75312186] = "EXCHANGE_PRICE_CRAFT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_ART_PCT"] = 75312187] = "EXCHANGE_PRICE_ART_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_SPICE_PCT"] = 75312188] = "EXCHANGE_PRICE_SPICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_JEWELRY_PCT"] = 75312189] = "EXCHANGE_PRICE_JEWELRY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_AROMA_PCT"] = 75312190] = "EXCHANGE_PRICE_AROMA_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_GEM_PCT"] = 75312191] = "EXCHANGE_PRICE_GEM_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_WEAPON_PCT"] = 75312192] = "EXCHANGE_PRICE_WEAPON_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_FIREARM_PCT"] = 75312193] = "EXCHANGE_PRICE_FIREARM_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_NEGO_FRIENDSHIP"] = 75312194] = "EXCHANGE_PRICE_NEGO_FRIENDSHIP";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_NEGO_FRIENDSHIP_PCT"] = 75312195] = "EXCHANGE_PRICE_NEGO_FRIENDSHIP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_NEGO_FRIENDSHIP"] = 75312196] = "EXCHANGE_GET_GOODS_NEGO_FRIENDSHIP";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_NEGO_FRIENDSHIP_PCT"] = 75312197] = "EXCHANGE_GET_GOODS_NEGO_FRIENDSHIP_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_PRICE_NEGO_SUCCESS_PCT"] = 75312198] = "EXCHANGE_PRICE_NEGO_SUCCESS_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["EXCHANGE_GET_GOODS_NEGO_SUCCESS_PCT"] = 75312199] = "EXCHANGE_GET_GOODS_NEGO_SUCCESS_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_GET_WATER_PCT"] = 75312209] = "ADVENTURE_ADDED_GET_WATER_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_ADDED_GET_WATER"] = 75312213] = "ADVENTURE_ADDED_GET_WATER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["REMOTE_SHIP_CREATE_UNLIMITED"] = 75312214] = "REMOTE_SHIP_CREATE_UNLIMITED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["FREE_SHIP_DELIVER"] = 75312215] = "FREE_SHIP_DELIVER";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SWEEP_LOCAL_NPC_TICKET_DECREASE_COUNT"] = 75312216] = "SWEEP_LOCAL_NPC_TICKET_DECREASE_COUNT";
    // 운하
    PASSIVE_EFFECT[PASSIVE_EFFECT["CANAL_USE_DEFAULT_COST_PCT"] = 75312218] = "CANAL_USE_DEFAULT_COST_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["CANAL_USE_TRADE_GOODS_COST_PCT"] = 75312219] = "CANAL_USE_TRADE_GOODS_COST_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["CANAL_USE_FREE_COUNT"] = 75312220] = "CANAL_USE_FREE_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["CANAL_USE_MAX_COUNT"] = 75312221] = "CANAL_USE_MAX_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_EXPLORE_GATHERING"] = 75312223] = "ADVENTURE_EXPLORE_GATHERING";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_EXPLORE_OBSERVATION"] = 75312224] = "ADVENTURE_EXPLORE_OBSERVATION";
    PASSIVE_EFFECT[PASSIVE_EFFECT["ADVENTURE_EXPLORE_COMBAT"] = 75312225] = "ADVENTURE_EXPLORE_COMBAT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["OCEAN_SAILING_KNOT_SOFT_CAP_ADD"] = 75312226] = "OCEAN_SAILING_KNOT_SOFT_CAP_ADD";
    PASSIVE_EFFECT[PASSIVE_EFFECT["REMOTE_INVEST"] = 75312222] = "REMOTE_INVEST";
    // 인양
    // 75312227, 75312228 인게임이 클라 위주로 돌아가기 때문에 해당 값은 서버에선 사용될 수 없음.
    // https://wiki.line.games/pages/viewpage.action?pageId=117505815
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_DEEP_SEA_SCORE"] = 75312227] = "SALVAGE_ADDED_DEEP_SEA_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_SHALLOW_SEA_SCORE"] = 75312228] = "SALVAGE_ADDED_SHALLOW_SEA_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_DEEP_SEA_REWARD_AMOUNT"] = 75312229] = "SALVAGE_ADDED_DEEP_SEA_REWARD_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_SHALLOW_SEA_REWARD_AMOUNT"] = 75312230] = "SALVAGE_ADDED_SHALLOW_SEA_REWARD_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_DEEP_SEA_REWARD_SCORE"] = 75312231] = "SALVAGE_ADDED_DEEP_SEA_REWARD_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_SHALLOW_SEA_REWARD_SCORE"] = 75312232] = "SALVAGE_ADDED_SHALLOW_SEA_REWARD_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_SHALLOW_SEA_LOOP_DAMAGED"] = 75312233] = "SALVAGE_ADDED_SHALLOW_SEA_LOOP_DAMAGED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_REWARD_AMOUNT"] = 75312234] = "SALVAGE_ADDED_REWARD_AMOUNT";
    // 인양-산호초
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CORAL_REEF_SCORE"] = 75312235] = "SALVAGE_ADDED_CORAL_REEF_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CORAL_REEF_REWARD_AMOUNT"] = 75312236] = "SALVAGE_ADDED_CORAL_REEF_REWARD_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CORAL_REEF_REWARD_SCORE"] = 75312237] = "SALVAGE_ADDED_CORAL_REEF_REWARD_SCORE";
    // 인양-대륙봉
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CONTINENTAL_SHELF_SCORE"] = 75312238] = "SALVAGE_ADDED_CONTINENTAL_SHELF_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CONTINENTAL_SHELF_REWARD_AMOUNT"] = 75312239] = "SALVAGE_ADDED_CONTINENTAL_SHELF_REWARD_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CONTINENTAL_SHELF_REWARD_SCORE"] = 75312240] = "SALVAGE_ADDED_CONTINENTAL_SHELF_REWARD_SCORE";
    // 인양-해구
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_TRENCH_SCORE"] = 75312241] = "SALVAGE_ADDED_TRENCH_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_TRENCH_REWARD_AMOUNT"] = 75312242] = "SALVAGE_ADDED_TRENCH_REWARD_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_TRENCH_REWARD_SCORE"] = 75312243] = "SALVAGE_ADDED_TRENCH_REWARD_SCORE";
    // 인양-해령
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_OCEAN_RIDGE_SCORE"] = 75312244] = "SALVAGE_ADDED_OCEAN_RIDGE_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_OCEAN_RIDGE_REWARD_AMOUNT"] = 75312245] = "SALVAGE_ADDED_OCEAN_RIDGE_REWARD_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_OCEAN_RIDGE_REWARD_SCORE"] = 75312246] = "SALVAGE_ADDED_OCEAN_RIDGE_REWARD_SCORE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_DEEP_SEA_LOOP_DAMAGED"] = 75312311] = "SALVAGE_ADDED_DEEP_SEA_LOOP_DAMAGED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CORAL_REEF_LOOP_DAMAGED"] = 75312312] = "SALVAGE_ADDED_CORAL_REEF_LOOP_DAMAGED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_CONTINENTAL_SHELF_DAMAGED"] = 75312313] = "SALVAGE_ADDED_CONTINENTAL_SHELF_DAMAGED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_TRENCH_LOOP_DAMAGED"] = 75312314] = "SALVAGE_ADDED_TRENCH_LOOP_DAMAGED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SALVAGE_ADDED_OCEAN_RIDGE_LOOP_DAMAGED"] = 75312315] = "SALVAGE_ADDED_OCEAN_RIDGE_LOOP_DAMAGED";
    // 밀수
    PASSIVE_EFFECT[PASSIVE_EFFECT["POSSESS_SMUGGLE_GOODS"] = 75312258] = "POSSESS_SMUGGLE_GOODS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_PCT"] = 75312259] = "SMUGGLE_ADDED_BUYABLE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_ILLEGAL_PCT"] = 75312260] = "SMUGGLE_ADDED_BUYABLE_ILLEGAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS_PCT"] = 75312261] = "SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_GREED_PCT"] = 75312262] = "SMUGGLE_ADDED_BUYABLE_GREED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_LUXURY_PCT"] = 75312263] = "SMUGGLE_ADDED_BUYABLE_LUXURY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE"] = 75312264] = "SMUGGLE_ADDED_BUYABLE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_ILLEGAL"] = 75312265] = "SMUGGLE_ADDED_BUYABLE_ILLEGAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS"] = 75312266] = "SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_GREED"] = 75312267] = "SMUGGLE_ADDED_BUYABLE_GREED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_ADDED_BUYABLE_LUXURY"] = 75312268] = "SMUGGLE_ADDED_BUYABLE_LUXURY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_NEGOTIATE_TRIGGER_RATING_PCT"] = 75312269] = "SMUGGLE_NEGOTIATE_TRIGGER_RATING_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_NEGOTIATE_ADDITIONAL_TRIGGER_RATING_PCT"] = 75312270] = "SMUGGLE_NEGOTIATE_ADDITIONAL_TRIGGER_RATING_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_NEGOTIATE_COUNT"] = 75312271] = "SMUGGLE_NEGOTIATE_COUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_NEGOTIATE_SUCCESS_RATING_PCT"] = 75312272] = "SMUGGLE_NEGOTIATE_SUCCESS_RATING_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_NEGOTIATE_BUY_PRICE_PCT"] = 75312273] = "SMUGGLE_NEGOTIATE_BUY_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_NEGOTIATE_SALE_PRICE_PCT"] = 75312274] = "SMUGGLE_NEGOTIATE_SALE_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLING_CHECK_RATING_PCT"] = 75312285] = "SMUGGLING_CHECK_RATING_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_POINT_GAIN_PCT"] = 75312286] = "SMUGGLE_POINT_GAIN_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_POINT_GAIN_AMOUNT_PCT"] = 75312287] = "SMUGGLE_POINT_GAIN_AMOUNT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_POINT_GAIN_AMOUNT"] = 75312288] = "SMUGGLE_POINT_GAIN_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SHOP_POINT_GAIN_AMOUNT_PCT"] = 75312289] = "SMUGGLE_SHOP_POINT_GAIN_AMOUNT_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SHOP_POINT_GAIN_AMOUNT"] = 75312290] = "SMUGGLE_SHOP_POINT_GAIN_AMOUNT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_PCT"] = 75312291] = "SMUGGLE_BUY_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_ILLEGAL_PCT"] = 75312292] = "SMUGGLE_BUY_PRICE_ILLEGAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_FOOLHARDINESS_PCT"] = 75312293] = "SMUGGLE_BUY_PRICE_FOOLHARDINESS_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_GREED_PCT"] = 75312294] = "SMUGGLE_BUY_PRICE_GREED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_LUXURY_PCT"] = 75312295] = "SMUGGLE_BUY_PRICE_LUXURY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE"] = 75312296] = "SMUGGLE_BUY_PRICE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_ILLEGAL"] = 75312297] = "SMUGGLE_BUY_PRICE_ILLEGAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_FOOLHARDINESS"] = 75312298] = "SMUGGLE_BUY_PRICE_FOOLHARDINESS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_GREED"] = 75312299] = "SMUGGLE_BUY_PRICE_GREED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_BUY_PRICE_LUXURY"] = 75312300] = "SMUGGLE_BUY_PRICE_LUXURY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_PCT"] = 75312301] = "SMUGGLE_SALE_PRICE_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_ILLEGAL_PCT"] = 75312302] = "SMUGGLE_SALE_PRICE_ILLEGAL_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_FOOLHARDINESS_PCT"] = 75312303] = "SMUGGLE_SALE_PRICE_FOOLHARDINESS_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_GREED_PCT"] = 75312304] = "SMUGGLE_SALE_PRICE_GREED_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_LUXURY_PCT"] = 75312305] = "SMUGGLE_SALE_PRICE_LUXURY_PCT";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE"] = 75312306] = "SMUGGLE_SALE_PRICE";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_ILLEGAL"] = 75312307] = "SMUGGLE_SALE_PRICE_ILLEGAL";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_FOOLHARDINESS"] = 75312308] = "SMUGGLE_SALE_PRICE_FOOLHARDINESS";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_GREED"] = 75312309] = "SMUGGLE_SALE_PRICE_GREED";
    PASSIVE_EFFECT[PASSIVE_EFFECT["SMUGGLE_SALE_PRICE_LUXURY"] = 75312310] = "SMUGGLE_SALE_PRICE_LUXURY";
    PASSIVE_EFFECT[PASSIVE_EFFECT["TASK_AUTO_CLEAR"] = 75312316] = "TASK_AUTO_CLEAR";
})(PASSIVE_EFFECT = exports.PASSIVE_EFFECT || (exports.PASSIVE_EFFECT = {}));
exports.DiscoverChanceSpecialStatByDiscoveryGroup = {
    [DiscoveryDesc_1.DISCOVERY_GROUP.NATURE]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_NATURE,
    [DiscoveryDesc_1.DISCOVERY_GROUP.ANIMAL]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_ANIMAL,
    [DiscoveryDesc_1.DISCOVERY_GROUP.PLANT]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_PLANT,
    [DiscoveryDesc_1.DISCOVERY_GROUP.ARCHITECTURE]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE,
    [DiscoveryDesc_1.DISCOVERY_GROUP.ARTIFACT]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_ARTIFACT,
    [DiscoveryDesc_1.DISCOVERY_GROUP.TREASURE]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_TREASURE,
};
exports.DiscoverChancePassiveEffectByDiscoveryGroup = {
    [DiscoveryDesc_1.DISCOVERY_GROUP.NATURE]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_NATURE,
    [DiscoveryDesc_1.DISCOVERY_GROUP.ANIMAL]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_ANIMAL,
    [DiscoveryDesc_1.DISCOVERY_GROUP.PLANT]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_PLANT,
    [DiscoveryDesc_1.DISCOVERY_GROUP.ARCHITECTURE]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE,
    [DiscoveryDesc_1.DISCOVERY_GROUP.ARTIFACT]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_ARTIFACT,
    [DiscoveryDesc_1.DISCOVERY_GROUP.TREASURE]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_TREASURE,
};
var CONTENTS_TERMS_CMS_ID;
(function (CONTENTS_TERMS_CMS_ID) {
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["NONE"] = 0] = "NONE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["LANGUAGE"] = 81000000] = "LANGUAGE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["COMPANY_LEVEL"] = 81000001] = "COMPANY_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SHIPS_COUNT"] = 81000002] = "SHIPS_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SHIPS_COUNT_OVER_BP_LEVEL"] = 81000003] = "SHIPS_COUNT_OVER_BP_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_SHIPS_COUNT"] = 81000004] = "SPECIFIC_SHIPS_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_BLUEPRINT_LEVEL"] = 81000005] = "SPECIFIC_BLUEPRINT_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CUR_TOWN_REGION"] = 81000006] = "CUR_TOWN_REGION";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CUR_TOWN"] = 81000007] = "CUR_TOWN";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["VISIT_REGION"] = 81000008] = "VISIT_REGION";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["INVEST_ANY_TOWN_ANY_DEVELOPMENT_TYPE"] = 81000010] = "INVEST_ANY_TOWN_ANY_DEVELOPMENT_TYPE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["INVEST_ANY_TOWN_SPECIPIC_DEVELOPMENT_TYPE"] = 81000011] = "INVEST_ANY_TOWN_SPECIPIC_DEVELOPMENT_TYPE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["INVEST_SPECIPIC_REGION_ANY_DEVELOPMENT_TYPE"] = 81000012] = "INVEST_SPECIPIC_REGION_ANY_DEVELOPMENT_TYPE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["ACCUMULATED_INVEST"] = 81000014] = "ACCUMULATED_INVEST";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["INVEST_SPECIPIC_REGION_SPECIPIC_DEVELOPMENT_TYPE"] = 81000015] = "INVEST_SPECIPIC_REGION_SPECIPIC_DEVELOPMENT_TYPE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["INVEST_POINT_CUR_TOWN_ANY_DEVELOPMENT_TYPE"] = 81000016] = "INVEST_POINT_CUR_TOWN_ANY_DEVELOPMENT_TYPE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["ADMIRALS_COUNT"] = 81000017] = "ADMIRALS_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["HAS_SPECIFIC_ADMIRAL"] = 81000018] = "HAS_SPECIFIC_ADMIRAL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["MATES_COUNT"] = 81000019] = "MATES_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_NATION_MATES_COUNT"] = 81000020] = "SPECIFIC_NATION_MATES_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["HAS_SPECIFIC_MATE"] = 81000021] = "HAS_SPECIFIC_MATE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_MATE_ADVENTURE_LEVEL"] = 81000022] = "SPECIFIC_MATE_ADVENTURE_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_MATE_TRADE_LEVEL"] = 81000023] = "SPECIFIC_MATE_TRADE_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_MATE_BATTLE_LEVEL"] = 81000024] = "SPECIFIC_MATE_BATTLE_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_MATE_SUM_LEVEL"] = 81000025] = "SPECIFIC_MATE_SUM_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["MATES_COUNT_OVER_LOYALTY"] = 81000026] = "MATES_COUNT_OVER_LOYALTY";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_MATE_LOYALTY"] = 81000027] = "SPECIFIC_MATE_LOYALTY";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CUR_TOWN_SPECIPIC_DEVELOPMENT_LEVEL"] = 81000028] = "CUR_TOWN_SPECIPIC_DEVELOPMENT_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CUR_TOWN_SUM_DEVELOPMENT_LEVEL"] = 81000029] = "CUR_TOWN_SUM_DEVELOPMENT_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SEASON"] = 81000030] = "SEASON";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["MONTH"] = 81000031] = "MONTH";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["WEATHER"] = 81000032] = "WEATHER";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_MATE_ROYAL_TITLE"] = 81000033] = "SPECIFIC_MATE_ROYAL_TITLE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_MATE_HIGHEST_LEVEL"] = 81000034] = "SPECIFIC_MATE_HIGHEST_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["REGION_OCCUPIED"] = 81000035] = "REGION_OCCUPIED";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["REGION_COMPLETELY_OCCUPIED"] = 81000036] = "REGION_COMPLETELY_OCCUPIED";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["QUEST_GLOBAL_REGISTER_BIT_FLAG"] = 81000037] = "QUEST_GLOBAL_REGISTER_BIT_FLAG";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["COMPANY_LEVEL_LESS_THAN"] = 81000038] = "COMPANY_LEVEL_LESS_THAN";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["QUEST_NODE"] = 81000039] = "QUEST_NODE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["IS_MATE_IN_FIRST_FLEET"] = 81000040] = "IS_MATE_IN_FIRST_FLEET";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["ADMIRAL_ADVENTURE_LEVEL"] = 81000041] = "ADMIRAL_ADVENTURE_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["COMPARE_ADMIRAL_ROYAL_TITLE"] = 81000042] = "COMPARE_ADMIRAL_ROYAL_TITLE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["IS_CLEAR_QUEST_SCENARIO"] = 81000043] = "IS_CLEAR_QUEST_SCENARIO";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["ALIVE_SHIPS_COUNT_IN_FIRST_FLEET_GREATER_OR_EQUAL"] = 81000044] = "ALIVE_SHIPS_COUNT_IN_FIRST_FLEET_GREATER_OR_EQUAL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["ALIVE_SHIPS_COUNT_IN_FIRST_FLEET_LESS_THAN"] = 81000045] = "ALIVE_SHIPS_COUNT_IN_FIRST_FLEET_LESS_THAN";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["HAS_SPECIFIC_BLUEPRINT"] = 81000046] = "HAS_SPECIFIC_BLUEPRINT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_JOB_MATES_COUNT"] = 81000047] = "SPECIFIC_JOB_MATES_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["REPORTED_WORLD_MAP_TILE_PERCENT"] = 81000048] = "REPORTED_WORLD_MAP_TILE_PERCENT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["QUEST_NODE_EQUAL"] = 81000049] = "QUEST_NODE_EQUAL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["USER_SHIP_BUILD_LEVEL"] = 81000050] = "USER_SHIP_BUILD_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["UNIQUE_GROUP_COMPLETE_QUEST"] = 81000051] = "UNIQUE_GROUP_COMPLETE_QUEST";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["IS_DISCOVERY"] = 81000052] = "IS_DISCOVERY";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["ALWAYS_FAIL"] = 81000053] = "ALWAYS_FAIL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIFIC_ITEM_COUNT"] = 81000054] = "SPECIFIC_ITEM_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["ACHIEVEMENT_COMPLETE"] = 81000056] = "ACHIEVEMENT_COMPLETE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CUR_TOWN_NATION"] = 81000057] = "CUR_TOWN_NATION";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["IS_CUR_TOWN_MY_GUILD"] = 81000058] = "IS_CUR_TOWN_MY_GUILD";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["NOT_HAS_SPECIFIC_ITEM"] = 81000059] = "NOT_HAS_SPECIFIC_ITEM";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["KARMA"] = 81000060] = "KARMA";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CUR_VILLAGE_FRIENDSHIP"] = 81000064] = "CUR_VILLAGE_FRIENDSHIP";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CUR_VILLAGE_EVENT"] = 81000065] = "CUR_VILLAGE_EVENT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["TARGET_TOWN_INDUSTRY_DEVELOPMENT_LEVEL"] = 81000066] = "TARGET_TOWN_INDUSTRY_DEVELOPMENT_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["TARGET_TOWN_COMMERCE_DEVELOPMENT_LEVEL"] = 81000067] = "TARGET_TOWN_COMMERCE_DEVELOPMENT_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["TARGET_TOWN_ARMORY_DEVELOPMENT_LEVEL"] = 81000068] = "TARGET_TOWN_ARMORY_DEVELOPMENT_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["IS_TARGET_TOWN_MY_GUILD"] = 81000069] = "IS_TARGET_TOWN_MY_GUILD";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["INVEST_POINT_TARGET_TOWN_ANY_DEVELOPMENT_TYPE"] = 81000070] = "INVEST_POINT_TARGET_TOWN_ANY_DEVELOPMENT_TYPE";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["LIVE_EVENT_PROGRESS"] = 81000072] = "LIVE_EVENT_PROGRESS";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["IS_TARGET_TOWN_NOT_MY_GUILD"] = 81000073] = "IS_TARGET_TOWN_NOT_MY_GUILD";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["CASH_SHOP_BUY_COUNT"] = 81000074] = "CASH_SHOP_BUY_COUNT";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["PUB_STAFF_INTIMACY"] = 81000075] = "PUB_STAFF_INTIMACY";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["SPECIAL_STAT_LEVEL"] = 81000076] = "SPECIAL_STAT_LEVEL";
    CONTENTS_TERMS_CMS_ID[CONTENTS_TERMS_CMS_ID["KARMA_LEVEL"] = 81000077] = "KARMA_LEVEL";
})(CONTENTS_TERMS_CMS_ID = exports.CONTENTS_TERMS_CMS_ID || (exports.CONTENTS_TERMS_CMS_ID = {}));
var DISASTER_CMS_ID_TYPE;
(function (DISASTER_CMS_ID_TYPE) {
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["WRECK"] = 16100000] = "WRECK";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["RATS"] = 16100001] = "RATS";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["FILTH"] = 16100002] = "FILTH";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["SCURVY"] = 16100003] = "SCURVY";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["EPIDEMIC"] = 16100004] = "EPIDEMIC";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["MALARIA"] = 16100005] = "MALARIA";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["FIRE"] = 16100006] = "FIRE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["SEAWEED"] = 16100007] = "SEAWEED";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["REEF"] = 16100008] = "REEF";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["FLOODING"] = 16100009] = "FLOODING";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["GREAT_FIRE"] = 16100010] = "GREAT_FIRE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["DECAY"] = 16100011] = "DECAY";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["FIGHT"] = 16100012] = "FIGHT";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["REVOLT"] = 16100013] = "REVOLT";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["CARGO_DAMAGE"] = 16100014] = "CARGO_DAMAGE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["CARGO_THEFT"] = 16100015] = "CARGO_THEFT";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["FRUSTRATION"] = 16100016] = "FRUSTRATION";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["HOMESICK"] = 16100017] = "HOMESICK";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["ANXIETY"] = 16100018] = "ANXIETY";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["INSOMNIA"] = 16100019] = "INSOMNIA";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["STORM"] = 16100020] = "STORM";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["BIG_WAVE"] = 16100021] = "BIG_WAVE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["BLIZZARD"] = 16100022] = "BLIZZARD";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["WHIRLWIND"] = 16100023] = "WHIRLWIND";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["WINDLESS"] = 16100024] = "WINDLESS";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["DISTURBANCE"] = 16100025] = "DISTURBANCE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["SIREN"] = 16100026] = "SIREN";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["SHARK"] = 16100027] = "SHARK";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["WHALE"] = 16100028] = "WHALE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["KRAKEN"] = 16100029] = "KRAKEN";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["HYPOTHERMIA"] = 16100030] = "HYPOTHERMIA";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["FROSTBITE"] = 16100031] = "FROSTBITE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["BERMUDA_TRIANGLE"] = 16100032] = "BERMUDA_TRIANGLE";
    DISASTER_CMS_ID_TYPE[DISASTER_CMS_ID_TYPE["HYPOTHERMIA_POLAR"] = 16100040] = "HYPOTHERMIA_POLAR";
})(DISASTER_CMS_ID_TYPE = exports.DISASTER_CMS_ID_TYPE || (exports.DISASTER_CMS_ID_TYPE = {}));
// 재해아이디 종류에 따라 재해방어 능력치 타입과 자연 해결 능력치 타입을 연결 시킨다.
// 재해가 적용되기 전 fleet의 능력치[defenseWpeType]에 따라 방어 성공여부를 판단하며,
// 재해가 적용 후 fleet의의 능력치[resolveWpeType]에 따라 자연해결 성공여부를 판단한다.
exports._DisasterWpeIdLinker = {
    // 난파
    [DISASTER_CMS_ID_TYPE.WRECK]: {
        defenseWpeType: 0,
        resolveWpeType: 0,
    },
    // 쥐
    [DISASTER_CMS_ID_TYPE.RATS]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_RATS,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_RATS,
    },
    // 비위생
    [DISASTER_CMS_ID_TYPE.FILTH]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FILTH,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FILTH,
    },
    // 괴혈병
    [DISASTER_CMS_ID_TYPE.SCURVY]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SCURVY,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SCURVY,
    },
    // 전염병
    [DISASTER_CMS_ID_TYPE.EPIDEMIC]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_EPIDEMIC,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_EPIDEMIC,
    },
    // 말라리아
    [DISASTER_CMS_ID_TYPE.MALARIA]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_MALARIA,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_MALARIA,
    },
    // 화재
    [DISASTER_CMS_ID_TYPE.FIRE]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FIRE,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FIRE,
    },
    // 해초
    [DISASTER_CMS_ID_TYPE.SEAWEED]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SEAWEED,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SEAWEED,
    },
    // 암초
    [DISASTER_CMS_ID_TYPE.REEF]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_REEF,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_REEF,
    },
    // 침수
    [DISASTER_CMS_ID_TYPE.FLOODING]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FLOODING,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FLOODING,
    },
    // 대화재
    [DISASTER_CMS_ID_TYPE.GREAT_FIRE]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_GREAT_FIRE,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_GREAT_FIRE,
    },
    // 부패
    [DISASTER_CMS_ID_TYPE.DECAY]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_DECAY,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_DECAY,
    },
    // 싸움
    [DISASTER_CMS_ID_TYPE.FIGHT]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FIGHT,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FIGHT,
    },
    // 반란
    [DISASTER_CMS_ID_TYPE.REVOLT]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_REVOLT,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_REVOLT,
    },
    // 적재화물 붕괴
    [DISASTER_CMS_ID_TYPE.CARGO_DAMAGE]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_CARGO_DAMAGE,
        resolveWpeType: 0,
    },
    // 적재화물 도난
    [DISASTER_CMS_ID_TYPE.CARGO_THEFT]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_CARGO_THEFT,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_CARGO_THEFT,
    },
    // 욕구불만
    [DISASTER_CMS_ID_TYPE.FRUSTRATION]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FRUSTRATION,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FRUSTRATION,
    },
    // 향수병
    [DISASTER_CMS_ID_TYPE.HOMESICK]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_HOMESICK,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_HOMESICK,
    },
    // 정신 불안
    [DISASTER_CMS_ID_TYPE.ANXIETY]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_ANXIETY,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_ANXIETY,
    },
    // 불면증
    [DISASTER_CMS_ID_TYPE.INSOMNIA]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_INSOMNIA,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_INSOMNIA,
    },
    // 폭풍
    [DISASTER_CMS_ID_TYPE.STORM]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_STORM,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_STORM,
    },
    // 높은 파도
    [DISASTER_CMS_ID_TYPE.BIG_WAVE]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_BIG_WAVE,
        resolveWpeType: 0,
    },
    // 눈보라
    [DISASTER_CMS_ID_TYPE.BLIZZARD]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_BLIZZARD,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_BLIZZARD,
    },
    // 돌풍
    [DISASTER_CMS_ID_TYPE.WHIRLWIND]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_WHIRLWIND,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_WHIRLWIND,
    },
    // 무풍
    [DISASTER_CMS_ID_TYPE.WINDLESS]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_WINDLESS,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_WINDLESS,
    },
    // 자기장 이상
    [DISASTER_CMS_ID_TYPE.DISTURBANCE]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_MAGNETIC_DISTURBANCE,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_MAGNETIC_DISTURBANCE,
    },
    // 세이렌
    [DISASTER_CMS_ID_TYPE.SIREN]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SIREN,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SIREN,
    },
    // 식인상어
    [DISASTER_CMS_ID_TYPE.SHARK]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SHARK,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SHARK,
    },
    // 고래 충돌
    [DISASTER_CMS_ID_TYPE.WHALE]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_WHALE,
        resolveWpeType: 0,
    },
    // 크라켄
    [DISASTER_CMS_ID_TYPE.KRAKEN]: {
        defenseWpeType: 0,
        resolveWpeType: 0,
    },
    [DISASTER_CMS_ID_TYPE.HYPOTHERMIA]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_HYPOTHERMIA,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_HYPOTHERMIA,
    },
    [DISASTER_CMS_ID_TYPE.FROSTBITE]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FROSTBITE,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FROSTBITE,
    },
    // 버뮤다 삼각지대
    [DISASTER_CMS_ID_TYPE.BERMUDA_TRIANGLE]: {
        defenseWpeType: 0,
        resolveWpeType: 0,
    },
    [DISASTER_CMS_ID_TYPE.HYPOTHERMIA_POLAR]: {
        defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_HYPOTHERMIA,
        resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_HYPOTHERMIA,
    },
};
// 재해와 버블이벤트 링크
exports._BubbleEventCmsId = {
    ForShipDurability: 16200000,
    ForMateLoyalty: 16200001,
};
var CONTENTS_TERMS_TYPE;
(function (CONTENTS_TERMS_TYPE) {
    CONTENTS_TERMS_TYPE[CONTENTS_TERMS_TYPE["NONE"] = 0] = "NONE";
    CONTENTS_TERMS_TYPE[CONTENTS_TERMS_TYPE["ACCUMULATION"] = 1] = "ACCUMULATION";
    CONTENTS_TERMS_TYPE[CONTENTS_TERMS_TYPE["CURRENT_VALUE"] = 2] = "CURRENT_VALUE";
})(CONTENTS_TERMS_TYPE = exports.CONTENTS_TERMS_TYPE || (exports.CONTENTS_TERMS_TYPE = {}));
var BUILDING_CONTENTS_UNLOCK_CMS_ID;
(function (BUILDING_CONTENTS_UNLOCK_CMS_ID) {
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["NONE"] = 0] = "NONE";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["LANGUAGE"] = ********] = "LANGUAGE";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["BANK_DEPOSIT_WITHDRAW"] = ********] = "BANK_DEPOSIT_WITHDRAW";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["BANK_DEPOSIT_INSTALLMENT_SAVINGS"] = ********] = "BANK_DEPOSIT_INSTALLMENT_SAVINGS";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["BANK_INSURANCE"] = ********] = "BANK_INSURANCE";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["DEPART_DOCK"] = ********] = "DEPART_DOCK";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["SHIPYARD_CREATE_SHIP"] = ********] = "SHIPYARD_CREATE_SHIP";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["SHIPYARD_CHANGE_BLUEPRINT_SLOT"] = ********] = "SHIPYARD_CHANGE_BLUEPRINT_SLOT";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["SHIPYARD_REPAIR_SHIP"] = ********] = "SHIPYARD_REPAIR_SHIP";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["SHIPYARD_DISMANTLE_SHIP"] = ********] = "SHIPYARD_DISMANTLE_SHIP";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["PUB_DRAFT_SAILOR"] = ********] = "PUB_DRAFT_SAILOR";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["GOVER_INVEST"] = ********] = "GOVER_INVEST";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["GOVER_GET_PRE_SESSION_INVEST"] = 38300011] = "GOVER_GET_PRE_SESSION_INVEST";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["GOVER_PREDICT_SHARE_POINTS"] = 38300012] = "GOVER_PREDICT_SHARE_POINTS";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["PUB_BUY_DRINK"] = 38300013] = "PUB_BUY_DRINK";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["PUB_STAFF"] = 38300014] = "PUB_STAFF";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["SHOP_BUY_EQUIPMENT"] = 38300015] = "SHOP_BUY_EQUIPMENT";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["SHOP_BUY_USER_ITEM"] = 38300016] = "SHOP_BUY_USER_ITEM";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["SHOP_BUY_FROM_BLACK_MARKET"] = 38300017] = "SHOP_BUY_FROM_BLACK_MARKET";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["PALACE_GET_ROYAL_ORDER"] = 38300018] = "PALACE_GET_ROYAL_ORDER";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["PALACE_BUY_TAX_FREE_PERMIT"] = 38300019] = "PALACE_BUY_TAX_FREE_PERMIT";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["CHANGE_NATION"] = 38300020] = "CHANGE_NATION";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["RELIGION_PRAYER"] = 38300021] = "RELIGION_PRAYER";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["RELIGION_DONATION"] = 38300022] = "RELIGION_DONATION";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["COLLECTOR_CONTRACT"] = 38300023] = "COLLECTOR_CONTRACT";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["COLLECTOR_REPORT"] = 38300024] = "COLLECTOR_REPORT";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["UNION_ACCEPT_REQUEST"] = 38300025] = "UNION_ACCEPT_REQUEST";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["TRADE_BUY"] = 38300026] = "TRADE_BUY";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["TRADE_SELL"] = 38300027] = "TRADE_SELL";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["PUB_MANAGE_MATE"] = 38300028] = "PUB_MANAGE_MATE";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["MANTIC_FORTUNE"] = 38300029] = "MANTIC_FORTUNE";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["COLLECTOR_WORLD_MAP"] = 38300031] = "COLLECTOR_WORLD_MAP";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["MANTIC_SWAP_PIECE_MATE"] = 38300032] = "MANTIC_SWAP_PIECE_MATE";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["MANTIC_SWAP_PIECE_SHIP_BLUEPRINT"] = 38300033] = "MANTIC_SWAP_PIECE_SHIP_BLUEPRINT";
    // SHIPYARD_VERIFY_TOW_SHIP = 38300035, // 조선소에서 예인 선박을 선거 슬롯으로 이동시킨다
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT"] = 38300037] = "PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["UNION_ACCEPT_EVENT_REQEUST"] = 38300038] = "UNION_ACCEPT_EVENT_REQEUST";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["MANTIC_SWAP_PIECE_GACHA_TICKET"] = 38300041] = "MANTIC_SWAP_PIECE_GACHA_TICKET";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["MANTIC_SWAP_PIECE_TOOL"] = 38300042] = "MANTIC_SWAP_PIECE_TOOL";
    BUILDING_CONTENTS_UNLOCK_CMS_ID[BUILDING_CONTENTS_UNLOCK_CMS_ID["MANTIC_SWAP_PIECE_TOKEN"] = 38300047] = "MANTIC_SWAP_PIECE_TOKEN";
})(BUILDING_CONTENTS_UNLOCK_CMS_ID = exports.BUILDING_CONTENTS_UNLOCK_CMS_ID || (exports.BUILDING_CONTENTS_UNLOCK_CMS_ID = {}));
var BUILDING_MENU_CMS_ID;
(function (BUILDING_MENU_CMS_ID) {
    BUILDING_MENU_CMS_ID[BUILDING_MENU_CMS_ID["PUB_RECRUITING_MATE"] = 38400001] = "PUB_RECRUITING_MATE";
    BUILDING_MENU_CMS_ID[BUILDING_MENU_CMS_ID["SHIPYARD"] = 38400014] = "SHIPYARD";
    BUILDING_MENU_CMS_ID[BUILDING_MENU_CMS_ID["PUB_MY_MATE"] = 38400002] = "PUB_MY_MATE";
})(BUILDING_MENU_CMS_ID = exports.BUILDING_MENU_CMS_ID || (exports.BUILDING_MENU_CMS_ID = {}));
// 승급 시간 가속권
var MATE_AWAKEN_TIME_COST_ITEM;
(function (MATE_AWAKEN_TIME_COST_ITEM) {
    MATE_AWAKEN_TIME_COST_ITEM[MATE_AWAKEN_TIME_COST_ITEM["A"] = 23500022] = "A";
    MATE_AWAKEN_TIME_COST_ITEM[MATE_AWAKEN_TIME_COST_ITEM["B"] = 23500023] = "B";
    MATE_AWAKEN_TIME_COST_ITEM[MATE_AWAKEN_TIME_COST_ITEM["C"] = 23500024] = "C";
    MATE_AWAKEN_TIME_COST_ITEM[MATE_AWAKEN_TIME_COST_ITEM["D"] = 23500025] = "D";
    MATE_AWAKEN_TIME_COST_ITEM[MATE_AWAKEN_TIME_COST_ITEM["E"] = 23500026] = "E";
    MATE_AWAKEN_TIME_COST_ITEM[MATE_AWAKEN_TIME_COST_ITEM["F"] = 23500027] = "F";
})(MATE_AWAKEN_TIME_COST_ITEM || (MATE_AWAKEN_TIME_COST_ITEM = {}));
// 학습 시간 가속권
var MATE_LEARN_PASSIVE_TIME_COST_ITEM;
(function (MATE_LEARN_PASSIVE_TIME_COST_ITEM) {
    MATE_LEARN_PASSIVE_TIME_COST_ITEM[MATE_LEARN_PASSIVE_TIME_COST_ITEM["A"] = 23500028] = "A";
    MATE_LEARN_PASSIVE_TIME_COST_ITEM[MATE_LEARN_PASSIVE_TIME_COST_ITEM["B"] = 23500029] = "B";
    MATE_LEARN_PASSIVE_TIME_COST_ITEM[MATE_LEARN_PASSIVE_TIME_COST_ITEM["C"] = 23500030] = "C";
    MATE_LEARN_PASSIVE_TIME_COST_ITEM[MATE_LEARN_PASSIVE_TIME_COST_ITEM["D"] = 23500031] = "D";
    MATE_LEARN_PASSIVE_TIME_COST_ITEM[MATE_LEARN_PASSIVE_TIME_COST_ITEM["E"] = 23500032] = "E";
    MATE_LEARN_PASSIVE_TIME_COST_ITEM[MATE_LEARN_PASSIVE_TIME_COST_ITEM["F"] = 23500033] = "F";
})(MATE_LEARN_PASSIVE_TIME_COST_ITEM || (MATE_LEARN_PASSIVE_TIME_COST_ITEM = {}));
// 길드 제작 가속권
var GUILD_CRAFT_TIME_COST_ITEM;
(function (GUILD_CRAFT_TIME_COST_ITEM) {
    GUILD_CRAFT_TIME_COST_ITEM[GUILD_CRAFT_TIME_COST_ITEM["A"] = 23500034] = "A";
    GUILD_CRAFT_TIME_COST_ITEM[GUILD_CRAFT_TIME_COST_ITEM["B"] = 23500035] = "B";
    GUILD_CRAFT_TIME_COST_ITEM[GUILD_CRAFT_TIME_COST_ITEM["C"] = 23500036] = "C";
    GUILD_CRAFT_TIME_COST_ITEM[GUILD_CRAFT_TIME_COST_ITEM["D"] = 23500037] = "D";
    GUILD_CRAFT_TIME_COST_ITEM[GUILD_CRAFT_TIME_COST_ITEM["E"] = 23500038] = "E";
    GUILD_CRAFT_TIME_COST_ITEM[GUILD_CRAFT_TIME_COST_ITEM["F"] = 23500039] = "F";
})(GUILD_CRAFT_TIME_COST_ITEM || (GUILD_CRAFT_TIME_COST_ITEM = {}));
var GUILD_SYNTHESIS_TIME_COST_ITEM;
(function (GUILD_SYNTHESIS_TIME_COST_ITEM) {
    GUILD_SYNTHESIS_TIME_COST_ITEM[GUILD_SYNTHESIS_TIME_COST_ITEM["A"] = 23500046] = "A";
    GUILD_SYNTHESIS_TIME_COST_ITEM[GUILD_SYNTHESIS_TIME_COST_ITEM["B"] = 23500047] = "B";
    GUILD_SYNTHESIS_TIME_COST_ITEM[GUILD_SYNTHESIS_TIME_COST_ITEM["C"] = 23500048] = "C";
    GUILD_SYNTHESIS_TIME_COST_ITEM[GUILD_SYNTHESIS_TIME_COST_ITEM["D"] = 23500049] = "D";
    GUILD_SYNTHESIS_TIME_COST_ITEM[GUILD_SYNTHESIS_TIME_COST_ITEM["E"] = 23500050] = "E";
    GUILD_SYNTHESIS_TIME_COST_ITEM[GUILD_SYNTHESIS_TIME_COST_ITEM["F"] = 23500051] = "F";
})(GUILD_SYNTHESIS_TIME_COST_ITEM || (GUILD_SYNTHESIS_TIME_COST_ITEM = {}));
var MATE_TRAINING_TIEM_COST_ITEM;
(function (MATE_TRAINING_TIEM_COST_ITEM) {
    MATE_TRAINING_TIEM_COST_ITEM[MATE_TRAINING_TIEM_COST_ITEM["A"] = 23700091] = "A";
    MATE_TRAINING_TIEM_COST_ITEM[MATE_TRAINING_TIEM_COST_ITEM["B"] = 23700092] = "B";
    MATE_TRAINING_TIEM_COST_ITEM[MATE_TRAINING_TIEM_COST_ITEM["C"] = 23700093] = "C";
    MATE_TRAINING_TIEM_COST_ITEM[MATE_TRAINING_TIEM_COST_ITEM["D"] = 23700094] = "D";
    MATE_TRAINING_TIEM_COST_ITEM[MATE_TRAINING_TIEM_COST_ITEM["E"] = 23700095] = "E";
    MATE_TRAINING_TIEM_COST_ITEM[MATE_TRAINING_TIEM_COST_ITEM["F"] = 23700096] = "F";
})(MATE_TRAINING_TIEM_COST_ITEM || (MATE_TRAINING_TIEM_COST_ITEM = {}));
function isMateAwakenTimeCostItem(itemCms) {
    return MATE_AWAKEN_TIME_COST_ITEM[itemCms.id] ? true : false;
}
exports.isMateAwakenTimeCostItem = isMateAwakenTimeCostItem;
function isMateLearnPassiveTimeCostItem(itemCms) {
    return MATE_LEARN_PASSIVE_TIME_COST_ITEM[itemCms.id] ? true : false;
}
exports.isMateLearnPassiveTimeCostItem = isMateLearnPassiveTimeCostItem;
function isGuildCraftTimeCostItem(itemCms) {
    return GUILD_CRAFT_TIME_COST_ITEM[itemCms.id] ? true : false;
}
exports.isGuildCraftTimeCostItem = isGuildCraftTimeCostItem;
function isGuildSynthesisTimeCostItem(itemCms) {
    return GUILD_SYNTHESIS_TIME_COST_ITEM[itemCms.id] ? true : false;
}
exports.isGuildSynthesisTimeCostItem = isGuildSynthesisTimeCostItem;
function isMateTrainingTimeCostItem(itemCms) {
    return MATE_TRAINING_TIEM_COST_ITEM[itemCms.id] ? true : false;
}
exports.isMateTrainingTimeCostItem = isMateTrainingTimeCostItem;
var JOB_TYPE;
(function (JOB_TYPE) {
    JOB_TYPE[JOB_TYPE["NONE"] = 0] = "NONE";
    JOB_TYPE[JOB_TYPE["ADVENTURE"] = 1] = "ADVENTURE";
    JOB_TYPE[JOB_TYPE["TRADE"] = 2] = "TRADE";
    JOB_TYPE[JOB_TYPE["BATTLE"] = 3] = "BATTLE";
})(JOB_TYPE = exports.JOB_TYPE || (exports.JOB_TYPE = {}));
var SPECIAL_STAT_JOB_TYPE;
(function (SPECIAL_STAT_JOB_TYPE) {
    SPECIAL_STAT_JOB_TYPE[SPECIAL_STAT_JOB_TYPE["ALL"] = 0] = "ALL";
    SPECIAL_STAT_JOB_TYPE[SPECIAL_STAT_JOB_TYPE["ADVENTURE"] = 1] = "ADVENTURE";
    SPECIAL_STAT_JOB_TYPE[SPECIAL_STAT_JOB_TYPE["TRADE"] = 2] = "TRADE";
    SPECIAL_STAT_JOB_TYPE[SPECIAL_STAT_JOB_TYPE["BATTLE"] = 3] = "BATTLE";
    SPECIAL_STAT_JOB_TYPE[SPECIAL_STAT_JOB_TYPE["ADVENTURE_AND_TRADE"] = 4] = "ADVENTURE_AND_TRADE";
})(SPECIAL_STAT_JOB_TYPE = exports.SPECIAL_STAT_JOB_TYPE || (exports.SPECIAL_STAT_JOB_TYPE = {}));
exports.ExpPropName = {
    [JOB_TYPE.ADVENTURE]: 'adventureExp',
    [JOB_TYPE.TRADE]: 'tradeExp',
    [JOB_TYPE.BATTLE]: 'battleExp',
};
exports.LevelPropName = {
    [JOB_TYPE.ADVENTURE]: 'adventureLevel',
    [JOB_TYPE.TRADE]: 'tradeLevel',
    [JOB_TYPE.BATTLE]: 'battleLevel',
};
var INVENTORY_TYPE;
(function (INVENTORY_TYPE) {
    INVENTORY_TYPE[INVENTORY_TYPE["NONE"] = 0] = "NONE";
    INVENTORY_TYPE[INVENTORY_TYPE["MATE_EQUIPMENT"] = 1] = "MATE_EQUIPMENT";
    INVENTORY_TYPE[INVENTORY_TYPE["USER_ITEM"] = 2] = "USER_ITEM";
    INVENTORY_TYPE[INVENTORY_TYPE["CAPTURED"] = 3] = "CAPTURED";
    INVENTORY_TYPE[INVENTORY_TYPE["SHIP_SLOT_ITEM"] = 5] = "SHIP_SLOT_ITEM";
    INVENTORY_TYPE[INVENTORY_TYPE["SHIP_DOCK"] = 6] = "SHIP_DOCK";
    INVENTORY_TYPE[INVENTORY_TYPE["SHIP_BUILDING"] = 7] = "SHIP_BUILDING";
    INVENTORY_TYPE[INVENTORY_TYPE["GUILD_CRATE"] = 8] = "GUILD_CRATE";
    INVENTORY_TYPE[INVENTORY_TYPE["PRESET"] = 9] = "PRESET";
    INVENTORY_TYPE[INVENTORY_TYPE["SAIL_WAYPOINT"] = 10] = "SAIL_WAYPOINT";
    INVENTORY_TYPE[INVENTORY_TYPE["MATE_EQUIPMENT_COSTUME"] = 11] = "MATE_EQUIPMENT_COSTUME";
    INVENTORY_TYPE[INVENTORY_TYPE["GUILD_SYNTHESIS"] = 12] = "GUILD_SYNTHESIS";
    INVENTORY_TYPE[INVENTORY_TYPE["DISPATCH_PRESET"] = 13] = "DISPATCH_PRESET";
})(INVENTORY_TYPE = exports.INVENTORY_TYPE || (exports.INVENTORY_TYPE = {}));
var MAIL_TYPE;
(function (MAIL_TYPE) {
    MAIL_TYPE[MAIL_TYPE["SYSTEM"] = 0] = "SYSTEM";
    MAIL_TYPE[MAIL_TYPE["CASH"] = 1] = "CASH";
    MAIL_TYPE[MAIL_TYPE["OPERATION"] = 2] = "OPERATION";
    MAIL_TYPE[MAIL_TYPE["GUILD"] = 3] = "GUILD";
})(MAIL_TYPE = exports.MAIL_TYPE || (exports.MAIL_TYPE = {}));
var QUEST_CATEGORY;
(function (QUEST_CATEGORY) {
    QUEST_CATEGORY[QUEST_CATEGORY["NONE"] = 0] = "NONE";
    QUEST_CATEGORY[QUEST_CATEGORY["CONTENTS_BY_LEVEL"] = 1] = "CONTENTS_BY_LEVEL";
    QUEST_CATEGORY[QUEST_CATEGORY["ROYAL_ORDER"] = 2] = "ROYAL_ORDER";
    QUEST_CATEGORY[QUEST_CATEGORY["REQUEST"] = 3] = "REQUEST";
    QUEST_CATEGORY[QUEST_CATEGORY["RANDOM"] = 4] = "RANDOM";
    QUEST_CATEGORY[QUEST_CATEGORY["SCENARIO"] = 5] = "SCENARIO";
    QUEST_CATEGORY[QUEST_CATEGORY["RELATIONSHIP"] = 6] = "RELATIONSHIP";
    QUEST_CATEGORY[QUEST_CATEGORY["CHALLENGE"] = 7] = "CHALLENGE";
    QUEST_CATEGORY[QUEST_CATEGORY["PUB_STAFF"] = 8] = "PUB_STAFF";
    // 이벤트의(한정 탭) 경우 퀘스트 클리어 시 국가 우호도 증가 X, 퀘스트 포기 시 명성 감소 X
    QUEST_CATEGORY[QUEST_CATEGORY["EVENT_REQUEST"] = 9] = "EVENT_REQUEST";
    QUEST_CATEGORY[QUEST_CATEGORY["ADVENTURE"] = 10] = "ADVENTURE";
    QUEST_CATEGORY[QUEST_CATEGORY["NPC"] = 11] = "NPC";
})(QUEST_CATEGORY = exports.QUEST_CATEGORY || (exports.QUEST_CATEGORY = {}));
var BUILDING_TYPE;
(function (BUILDING_TYPE) {
    BUILDING_TYPE[BUILDING_TYPE["NONE"] = 0] = "NONE";
    BUILDING_TYPE[BUILDING_TYPE["PALACE"] = 1] = "PALACE";
    BUILDING_TYPE[BUILDING_TYPE["GOVER"] = 2] = "GOVER";
    BUILDING_TYPE[BUILDING_TYPE["UNION"] = 3] = "UNION";
    BUILDING_TYPE[BUILDING_TYPE["PUB"] = 4] = "PUB";
    BUILDING_TYPE[BUILDING_TYPE["SHOP"] = 5] = "SHOP";
    BUILDING_TYPE[BUILDING_TYPE["SPECIAL"] = 6] = "SPECIAL";
    BUILDING_TYPE[BUILDING_TYPE["RELIGION"] = 7] = "RELIGION";
    BUILDING_TYPE[BUILDING_TYPE["COLLECTOR"] = 8] = "COLLECTOR";
    BUILDING_TYPE[BUILDING_TYPE["BANK"] = 9] = "BANK";
    BUILDING_TYPE[BUILDING_TYPE["TRADE"] = 10] = "TRADE";
    BUILDING_TYPE[BUILDING_TYPE["SHIPYARD"] = 11] = "SHIPYARD";
    BUILDING_TYPE[BUILDING_TYPE["DEPART"] = 12] = "DEPART";
    BUILDING_TYPE[BUILDING_TYPE["MANTIC"] = 13] = "MANTIC";
    BUILDING_TYPE[BUILDING_TYPE["CANAL"] = 14] = "CANAL";
    BUILDING_TYPE[BUILDING_TYPE["EVENT_GOVER"] = 30] = "EVENT_GOVER";
})(BUILDING_TYPE = exports.BUILDING_TYPE || (exports.BUILDING_TYPE = {}));
var RELIGION_BUFF_TYPE;
(function (RELIGION_BUFF_TYPE) {
    RELIGION_BUFF_TYPE[RELIGION_BUFF_TYPE["NONE"] = 0] = "NONE";
    RELIGION_BUFF_TYPE[RELIGION_BUFF_TYPE["PRAYER"] = 1] = "PRAYER";
    RELIGION_BUFF_TYPE[RELIGION_BUFF_TYPE["DONATION"] = 2] = "DONATION";
})(RELIGION_BUFF_TYPE = exports.RELIGION_BUFF_TYPE || (exports.RELIGION_BUFF_TYPE = {}));
var CONTENTS_TERMS_OPERATOR;
(function (CONTENTS_TERMS_OPERATOR) {
    CONTENTS_TERMS_OPERATOR[CONTENTS_TERMS_OPERATOR["EQUAL"] = 1] = "EQUAL";
    CONTENTS_TERMS_OPERATOR[CONTENTS_TERMS_OPERATOR["GREATER_OR_EQUAL"] = 2] = "GREATER_OR_EQUAL";
    CONTENTS_TERMS_OPERATOR[CONTENTS_TERMS_OPERATOR["LESS_THAN"] = 3] = "LESS_THAN";
})(CONTENTS_TERMS_OPERATOR = exports.CONTENTS_TERMS_OPERATOR || (exports.CONTENTS_TERMS_OPERATOR = {}));
var POINT_TYPE;
(function (POINT_TYPE) {
    POINT_TYPE[POINT_TYPE["POINT"] = 0] = "POINT";
    POINT_TYPE[POINT_TYPE["CASH"] = 1] = "CASH";
})(POINT_TYPE = exports.POINT_TYPE || (exports.POINT_TYPE = {}));
var SPECIAL_STAT_TARGET_TYPE;
(function (SPECIAL_STAT_TARGET_TYPE) {
    SPECIAL_STAT_TARGET_TYPE[SPECIAL_STAT_TARGET_TYPE["SHIP"] = 0] = "SHIP";
    SPECIAL_STAT_TARGET_TYPE[SPECIAL_STAT_TARGET_TYPE["FLEET"] = 1] = "FLEET";
})(SPECIAL_STAT_TARGET_TYPE = exports.SPECIAL_STAT_TARGET_TYPE || (exports.SPECIAL_STAT_TARGET_TYPE = {}));
var SHIP_GRADE_TYPE;
(function (SHIP_GRADE_TYPE) {
    SHIP_GRADE_TYPE[SHIP_GRADE_TYPE["C"] = 2] = "C";
    SHIP_GRADE_TYPE[SHIP_GRADE_TYPE["B"] = 3] = "B";
    SHIP_GRADE_TYPE[SHIP_GRADE_TYPE["A"] = 4] = "A";
    SHIP_GRADE_TYPE[SHIP_GRADE_TYPE["S"] = 5] = "S";
})(SHIP_GRADE_TYPE = exports.SHIP_GRADE_TYPE || (exports.SHIP_GRADE_TYPE = {}));
/*
  shipSize 변경이 일어나면
  shipBuildExpCms shipSize 와 관련된 함수를 변경해줘야 한다
*/
var SHIP_SIZE;
(function (SHIP_SIZE) {
    SHIP_SIZE[SHIP_SIZE["SMALL"] = 2] = "SMALL";
    SHIP_SIZE[SHIP_SIZE["MEDIUM"] = 3] = "MEDIUM";
    SHIP_SIZE[SHIP_SIZE["LARGE"] = 4] = "LARGE";
    SHIP_SIZE[SHIP_SIZE["EXTRA_LARGE"] = 5] = "EXTRA_LARGE";
})(SHIP_SIZE = exports.SHIP_SIZE || (exports.SHIP_SIZE = {}));
var STAT_EFFECT_TYPE;
(function (STAT_EFFECT_TYPE) {
    STAT_EFFECT_TYPE[STAT_EFFECT_TYPE["STAT_OPERATOR"] = 0] = "STAT_OPERATOR";
    STAT_EFFECT_TYPE[STAT_EFFECT_TYPE["WORLD_PASSIVE_EFFECT"] = 1] = "WORLD_PASSIVE_EFFECT";
})(STAT_EFFECT_TYPE = exports.STAT_EFFECT_TYPE || (exports.STAT_EFFECT_TYPE = {}));
// https://docs.google.com/spreadsheets/d/1KQRAhLwxmRNXSVGqQ__4AovzNsZJHlh_U9romm1IQYE/edit#gid=0
// 클라의 CMSEx.lua , ACHIEVEMENT_TERMS 참고
// 클라 전투 내부에서 일부 id 를 사용하고 있음.
var ACHIEVEMENT_TERMS;
(function (ACHIEVEMENT_TERMS) {
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["MATE_LANGUAGE_LEVEL"] = 87000000] = "MATE_LANGUAGE_LEVEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["COMPANY_LEVEL"] = 87000001] = "COMPANY_LEVEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUILD_SHIP"] = 87000002] = "BUILD_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DISMANTLE_SHIP"] = 87000003] = "DISMANTLE_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_SHIP"] = 87000004] = "GAIN_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_SPECIFIC_BP_LEVEL_SHIP"] = 87000005] = "GAIN_SPECIFIC_BP_LEVEL_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRY_UPDATE_SHIP_BLUEPRINT"] = 87000006] = "TRY_UPDATE_SHIP_BLUEPRINT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_SPECIFIC_SHIP"] = 87000007] = "GAIN_SPECIFIC_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP"] = 87000008] = "GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["EQUIP_SHIP_SLOT_ITEM"] = 87000009] = "EQUIP_SHIP_SLOT_ITEM";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["CHANGE_SHIP_BLUEPRINT_ROOM_SLOT"] = 87000010] = "CHANGE_SHIP_BLUEPRINT_ROOM_SLOT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["VISIT_REGION"] = 87000011] = "VISIT_REGION";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ENTER_TOWN"] = 87000012] = "ENTER_TOWN";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["INVEST_COUNT"] = 87000013] = "INVEST_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_DEVELOPMENT_TYPE_INVEST_COUNT"] = 87000014] = "SPECIFIC_DEVELOPMENT_TYPE_INVEST_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_REGION_INVEST_COUNT"] = 87000015] = "SPECIFIC_REGION_INVEST_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_TOWN_INVEST_COUNT"] = 87000016] = "SPECIFIC_TOWN_INVEST_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["INVEST_POINT"] = 87000017] = "INVEST_POINT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_REGION_INVEST_POINT"] = 87000018] = "SPECIFIC_REGION_INVEST_POINT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_TOWN_INVEST_POINT"] = 87000019] = "SPECIFIC_TOWN_INVEST_POINT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["INVEST_COUNT_TOWN_OWN_TYPE_3"] = 87000130] = "INVEST_COUNT_TOWN_OWN_TYPE_3";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_DEVELOPMENT_TYPE_INVEST_COUNT_TOWN_OWN_TYPE_3"] = 87000131] = "SPECIFIC_DEVELOPMENT_TYPE_INVEST_COUNT_TOWN_OWN_TYPE_3";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_REGION_INVEST_COUNT_TOWN_OWN_TYPE_3"] = 87000132] = "SPECIFIC_REGION_INVEST_COUNT_TOWN_OWN_TYPE_3";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_TOWN_INVEST_COUNT_TOWN_OWN_TYPE_3"] = 87000133] = "SPECIFIC_TOWN_INVEST_COUNT_TOWN_OWN_TYPE_3";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["INVEST_POINT_TOWN_OWN_TYPE_3"] = 87000134] = "INVEST_POINT_TOWN_OWN_TYPE_3";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_REGION_INVEST_POINT_TOWN_OWN_TYPE_3"] = 87000135] = "SPECIFIC_REGION_INVEST_POINT_TOWN_OWN_TYPE_3";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_TOWN_INVEST_POINT_TOWN_OWN_TYPE_3"] = 87000136] = "SPECIFIC_TOWN_INVEST_POINT_TOWN_OWN_TYPE_3";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GIFT_TO_MATE"] = 87000020] = "GIFT_TO_MATE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_ADMIRAL"] = 87000021] = "GAIN_ADMIRAL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_SPECIFIC_ADMIRAL"] = 87000022] = "GAIN_SPECIFIC_ADMIRAL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_MATE"] = 87000023] = "GAIN_MATE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_SPECIFIC_NATION_MATE"] = 87000024] = "GAIN_SPECIFIC_NATION_MATE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_SPECIFIC_MATE"] = 87000025] = "GAIN_SPECIFIC_MATE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_MATE_ADVENTURE_LEVEL"] = 87000026] = "SPECIFIC_MATE_ADVENTURE_LEVEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_MATE_TRADE_LEVEL"] = 87000027] = "SPECIFIC_MATE_TRADE_LEVEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_MATE_BATTLE_LEVEL"] = 87000028] = "SPECIFIC_MATE_BATTLE_LEVEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_MATE_TOTAL_LEVEL"] = 87000029] = "SPECIFIC_MATE_TOTAL_LEVEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ROYAL_TITLE"] = 87000030] = "ROYAL_TITLE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ROYAL_TITLE_SPECIFIC_ADMIRAL"] = 87000031] = "ROYAL_TITLE_SPECIFIC_ADMIRAL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SUCCESS_RECRUIT_MATE"] = 87000032] = "SUCCESS_RECRUIT_MATE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["FAIL_RECRUIT_MATE"] = 87000033] = "FAIL_RECRUIT_MATE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUY_DRINK"] = 87000034] = "BUY_DRINK";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DRAFT_SAILOR"] = 87000035] = "DRAFT_SAILOR";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["FIRE_SAILOR"] = 87000036] = "FIRE_SAILOR";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_BUY_GOODS_COUNT"] = 87000037] = "TRADE_BUY_GOODS_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_GOODS_COUNT"] = 87000038] = "TRADE_SELL_GOODS_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_BUY_SELL_SPECIFIC_TRADE_GOODS"] = 87000039] = "TRADE_BUY_SELL_SPECIFIC_TRADE_GOODS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_BUY_SELL_FAMOUSE_TRADE_GOODS"] = 87000040] = "TRADE_BUY_SELL_FAMOUSE_TRADE_GOODS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_BUY_SELL_SPECIFIC_FAMOUSE_TRADE_GOODS"] = 87000041] = "TRADE_BUY_SELL_SPECIFIC_FAMOUSE_TRADE_GOODS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_PROFIT"] = 87000042] = "TRADE_PROFIT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SPECIFIC_PROFIT"] = 87000043] = "TRADE_SPECIFIC_PROFIT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUY_SHOP_ITEM"] = 87000044] = "BUY_SHOP_ITEM";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SELL_SHOP_ITEM"] = 87000045] = "SELL_SHOP_ITEM";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUY_BLACK_MARKET_ITEM"] = 87000046] = "BUY_BLACK_MARKET_ITEM";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DEPART"] = 87000047] = "DEPART";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUY_FOOD"] = 87000048] = "BUY_FOOD";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUY_WATER"] = 87000049] = "BUY_WATER";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUY_AMMO"] = 87000050] = "BUY_AMMO";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUY_LUMBER"] = 87000051] = "BUY_LUMBER";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SHIP_REPAIRING_COST"] = 87000052] = "SHIP_REPAIRING_COST";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SAILING_DAYS"] = 87000053] = "SAILING_DAYS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DISASTER"] = 87000054] = "DISASTER";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_DISASTER"] = 87000055] = "SPECIFIC_DISASTER";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DISCOVER_OCEAN_DOODAD"] = 87000056] = "DISCOVER_OCEAN_DOODAD";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DISCOVER_SPECIFIC_CATEGORY"] = 87000057] = "DISCOVER_SPECIFIC_CATEGORY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DISCOVER"] = 87000058] = "DISCOVER";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY"] = 87000059] = "BATTLE_VICTORY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_DEFEAT"] = 87000060] = "BATTLE_DEFEAT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_ACTION"] = 87000061] = "BATTLE_ACTION";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_SINK_ENEMY_SHIP"] = 87000062] = "BATTLE_SINK_ENEMY_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["CAPTURE_SHIP"] = 87000063] = "CAPTURE_SHIP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_GIVE_UP"] = 87000064] = "BATTLE_GIVE_UP";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["USE_ENERGY"] = 87000065] = "USE_ENERGY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY_FROM_SPECIFIC_NPC"] = 87000066] = "BATTLE_VICTORY_FROM_SPECIFIC_NPC";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_DEFEAT_FROM_SPECIFIC_NPC"] = 87000067] = "BATTLE_DEFEAT_FROM_SPECIFIC_NPC";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["COMPLETE_QUEST_NODE"] = 87000068] = "COMPLETE_QUEST_NODE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ENCOUNT_SURRENDER_SUCCESS"] = 87000069] = "ENCOUNT_SURRENDER_SUCCESS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ENCOUNT_NEGOTIATE_SUCCESS"] = 87000070] = "ENCOUNT_NEGOTIATE_SUCCESS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ENCOUNT_ESCAPE_SUCCESS"] = 87000071] = "ENCOUNT_ESCAPE_SUCCESS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["COMPLETE_QUEST"] = 87000072] = "COMPLETE_QUEST";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DISCOVER_SPECIFIC"] = 87000073] = "DISCOVER_SPECIFIC";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_MISSION_ALL_CLEAR"] = 87000075] = "BATTLE_MISSION_ALL_CLEAR";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["PUB_STAFF_INTIMACY_LEVEL_5"] = 87000077] = "PUB_STAFF_INTIMACY_LEVEL_5";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["FISHING_SUCCESS"] = 87000078] = "FISHING_SUCCESS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["CUSTOMIZE_MATE_EQUIPMENT"] = 87000079] = "CUSTOMIZE_MATE_EQUIPMENT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_ESCAPE"] = 87000080] = "BATTLE_ESCAPE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY_IN_DUEL"] = 87000081] = "BATTLE_VICTORY_IN_DUEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["COMPLETE_QUEST_SPECIFIC_LEADER_MATE_SPECIFIC_JOB_TYPE"] = 87000082] = "COMPLETE_QUEST_SPECIFIC_LEADER_MATE_SPECIFIC_JOB_TYPE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_ONE_ATTACK"] = 87000083] = "BATTLE_ONE_ATTACK";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["EQUIP_MATE_EQUIPMENT_SPECIFIC_GRADE_SPECIFIC_TYPE"] = 87000084] = "EQUIP_MATE_EQUIPMENT_SPECIFIC_GRADE_SPECIFIC_TYPE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["COLLECTOR_REPORT_DISCOVERIES"] = 87000085] = "COLLECTOR_REPORT_DISCOVERIES";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["OCEAN_PROTECTION"] = 87000086] = "OCEAN_PROTECTION";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_GREATE_VICTORY_IN_DUEL"] = 87000087] = "BATTLE_GREATE_VICTORY_IN_DUEL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["GAIN_FAME"] = 87000088] = "GAIN_FAME";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_NEGO_WITHOUT_FAILURE"] = 87000089] = "TRADE_NEGO_WITHOUT_FAILURE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["OPEN_CASH_SHOP_GACHA_BOX"] = 87000090] = "OPEN_CASH_SHOP_GACHA_BOX";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["PUB_STAFF_TALKING"] = 87000091] = "PUB_STAFF_TALKING";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["OVERWRITE_SHIP_ENCHANT"] = 87000092] = "OVERWRITE_SHIP_ENCHANT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DEFEAT_ELITE_FLEET"] = 87000093] = "DEFEAT_ELITE_FLEET";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_USE_ORDER_SKILL"] = 87000094] = "BATTLE_USE_ORDER_SKILL";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS"] = 87000098] = "TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_PROFIT_TRADE_EVENT"] = 87000099] = "TRADE_SELL_PROFIT_TRADE_EVENT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_CONSTANT_PRICE_PERCENT_CONSTANT_COUNT"] = 87000100] = "TRADE_SELL_CONSTANT_PRICE_PERCENT_CONSTANT_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_BUY_SELL_SPECIFIC_CATEGORY"] = 87000101] = "TRADE_BUY_SELL_SPECIFIC_CATEGORY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DISCOVERY_SCORE"] = 87000103] = "DISCOVERY_SCORE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["LAND_EXPLORE"] = 87000104] = "LAND_EXPLORE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["UNLOCK_SHIP_BLUEPRINT"] = 87000105] = "UNLOCK_SHIP_BLUEPRINT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ACTIVATE_OCEAN_DOODAD_BY_TOUCH"] = 87000106] = "ACTIVATE_OCEAN_DOODAD_BY_TOUCH";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["COMPLETE_SPECIFIC_CYCLE_TASK_CATEGORY"] = 87000107] = "COMPLETE_SPECIFIC_CYCLE_TASK_CATEGORY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY_FROM_USER"] = 87000108] = "BATTLE_VICTORY_FROM_USER";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY_FROM_USER_SPECIFIC_NATION"] = 87000109] = "BATTLE_VICTORY_FROM_USER_SPECIFIC_NATION";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["KARMA_VALUE"] = 87000110] = "KARMA_VALUE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION_SPECIFIC_JOB_TYPE"] = 87000111] = "BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION_SPECIFIC_JOB_TYPE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION"] = ********] = "BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_VICTORY_FROM_NPC_SPECIFIC_JOB_TYPE"] = ********] = "BATTLE_VICTORY_FROM_NPC_SPECIFIC_JOB_TYPE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_NATION_TOWN_INVEST_POINT"] = ********] = "SPECIFIC_NATION_TOWN_INVEST_POINT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BUILD_SHIP_SPECIFIC_TIER"] = ********] = "BUILD_SHIP_SPECIFIC_TIER";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["DEPOSIT_BANK_INSTALLMENT_SAVINGS"] = ********] = "DEPOSIT_BANK_INSTALLMENT_SAVINGS";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["RECEIVE_SPECIFIC_SHIP_FROM_SHIPYARD"] = ********] = "RECEIVE_SPECIFIC_SHIP_FROM_SHIPYARD";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SAILING_DAYS_FOR_QUEST"] = ********] = "SAILING_DAYS_FOR_QUEST";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BATTLE_DUEL_COUNT"] = ********] = "BATTLE_DUEL_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_POINT"] = ********] = "TRADE_POINT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BOSS_RAID_PARTICIPATE"] = ********] = "BOSS_RAID_PARTICIPATE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BOSS_RAID_DAMAGE"] = ********] = "BOSS_RAID_DAMAGE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BOSS_RAID_LAST_HIT"] = ********] = "BOSS_RAID_LAST_HIT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BOSS_RAID_FIRST_HIT"] = ********] = "BOSS_RAID_FIRST_HIT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["FISHING_BIG_CATCH"] = ********] = "FISHING_BIG_CATCH";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["FISHING_MAX_SIZE"] = 87000142] = "FISHING_MAX_SIZE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SPECIFIC_LAND_EXPLORE"] = 87000145] = "SPECIFIC_LAND_EXPLORE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BOSS_RAID_PARTICIPATE_ANY"] = 87000146] = "BOSS_RAID_PARTICIPATE_ANY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["BOSS_RAID_DAMAGE_ANY"] = 87000147] = "BOSS_RAID_DAMAGE_ANY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_BUY_SPECIFIC_GOODS_COUNT"] = 87000119] = "TRADE_BUY_SPECIFIC_GOODS_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_SPECIFIC_GOODS_COUNT"] = 87000120] = "TRADE_SELL_SPECIFIC_GOODS_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_BUY_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_TOWN"] = 87000121] = "TRADE_BUY_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_TOWN";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_TOWN"] = 87000122] = "TRADE_SELL_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_TOWN";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS_IN_SPECIFIC_TOWN"] = 87000123] = "TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS_IN_SPECIFIC_TOWN";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS_CATEGORY"] = 87000149] = "TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS_CATEGORY";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["ACCUMULATED_ITEM_COUNT"] = 87000150] = "ACCUMULATED_ITEM_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["EXCHANGE_COUNT"] = 87000154] = "EXCHANGE_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["EXCHANGE_COUNT_IN_SPECIFIC_VILLAGE"] = 87000155] = "EXCHANGE_COUNT_IN_SPECIFIC_VILLAGE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["EXCHANGE_SPECIFIC_GOODS_COUNT"] = 87000156] = "EXCHANGE_SPECIFIC_GOODS_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["EXCHANGE_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_VILLAGE"] = 87000157] = "EXCHANGE_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_VILLAGE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["CASH_SHOP_BUY_COUNT"] = 87000158] = "CASH_SHOP_BUY_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SALVAGE_COUNT"] = 87000160] = "SALVAGE_COUNT";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SALVAGE_SCORE"] = 87000161] = "SALVAGE_SCORE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SALVAGE_ACCUM_SCORE"] = 87000162] = "SALVAGE_ACCUM_SCORE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SALVAGE_ACCUM_DECREASE_SCORE"] = 87000163] = "SALVAGE_ACCUM_DECREASE_SCORE";
    ACHIEVEMENT_TERMS[ACHIEVEMENT_TERMS["SALVAGE_SPECIAL_OBJECT_COUNT"] = 87000164] = "SALVAGE_SPECIAL_OBJECT_COUNT";
})(ACHIEVEMENT_TERMS = exports.ACHIEVEMENT_TERMS || (exports.ACHIEVEMENT_TERMS = {}));
var TASK_CATEGORY;
(function (TASK_CATEGORY) {
    TASK_CATEGORY[TASK_CATEGORY["NONE"] = 0] = "NONE";
    TASK_CATEGORY[TASK_CATEGORY["DAILY"] = 1] = "DAILY";
    TASK_CATEGORY[TASK_CATEGORY["WEEKLY"] = 2] = "WEEKLY";
    TASK_CATEGORY[TASK_CATEGORY["MONTHLY"] = 3] = "MONTHLY";
    TASK_CATEGORY[TASK_CATEGORY["MAX"] = 3] = "MAX";
})(TASK_CATEGORY = exports.TASK_CATEGORY || (exports.TASK_CATEGORY = {}));
exports.TRADE_DISCOUNT_PASSIVE_EFFECT = {
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_DISCOUNT_FOOD,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_DISCOUNT_CONDIMENT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_DISCOUNT_LIVESTOCK,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_DISCOUNT_MEDICINE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_DISCOUNT_GENERAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_DISCOUNT_LIQUOR,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_DISCOUNT_DYE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_DISCOUNT_ORE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_DISCOUNT_INDUSTRIAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_DISCOUNT_LUXURY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_DISCOUNT_TEXTILE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_DISCOUNT_FABRIC,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_DISCOUNT_CRAFT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_DISCOUNT_ART,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_DISCOUNT_SPICE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_DISCOUNT_JEWELRY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_DISCOUNT_AROMA,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_DISCOUNT_GEM,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_DISCOUNT_WEAPON,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_DISCOUNT_FIREARM,
};
exports.TRADE_EXTRA_CHARGE_PASSIVE_EFFECT = {
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_FOOD,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_CONDIMENT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_LIVESTOCK,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_MEDICINE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_GENERAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_LIQUOR,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_DYE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_ORE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_INDUSTRIAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_LUXURY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_TEXTILE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_FABRIC,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_CRAFT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_ART,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_SPICE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_JEWELRY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_AROMA,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_GEM,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_WEAPON,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_FIREARM,
};
exports.INVEST_DEVELOPMENT_PASSIVE_EFFECT = {
    [DEVELOPMENT_TYPE.industry]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_INDUSTRY,
    [DEVELOPMENT_TYPE.commerce]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_COMMERCE,
    [DEVELOPMENT_TYPE.armory]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_ARMORY,
};
exports.INVEST_DEVELOPMENT_PCT_PASSIVE_EFFECT = {
    [DEVELOPMENT_TYPE.industry]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_INDUSTRY_PCT,
    [DEVELOPMENT_TYPE.commerce]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_COMMERCE_PCT,
    [DEVELOPMENT_TYPE.armory]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_ARMORY_PCT,
};
exports.TRADE_ADDED_BUYABLE_PASSIVE_EFFECT = {
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FOOD,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CONDIMENT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIVESTOCK,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_MEDICINE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GENERAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIQUOR,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_DYE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ORE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_INDUSTRIAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LUXURY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_TEXTILE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FABRIC,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CRAFT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ART,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_SPICE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_JEWELRY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_AROMA,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GEM,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_WEAPON,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FIREARM,
};
exports.TRADE_ADDED_BUYABLE_PCT_PASSIVE_EFFECT = {
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FOOD_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CONDIMENT_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIVESTOCK_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_MEDICINE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GENERAL_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIQUOR_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_DYE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ORE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_INDUSTRIAL_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LUXURY_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_TEXTILE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FABRIC_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CRAFT_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ART_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_SPICE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_JEWELRY_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_AROMA_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GEM_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_WEAPON_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FIREARM_PCT,
};
exports.TRADE_PREDICT_PRICE_PASSIVE_EFFECT = {
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_FOOD,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_CONDIMENT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_LIVESTOCK,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_MEDICINE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_GENERAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_LIQUOR,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_DYE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_ORE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_INDUSTRIAL,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_LUXURY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_TEXTILE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_FABRIC,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_CRAFT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_ART,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_SPICE,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_JEWELRY,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_AROMA,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_GEM,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_WEAPON,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_FIREARM,
};
exports.EXCHANGE_PRICE_PCT_PASSIVE_EFFECT = {
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.EXCHANGE_PRICE_FOOD_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.EXCHANGE_PRICE_CONDIMENT_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.EXCHANGE_PRICE_LIVESTOCK_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.EXCHANGE_PRICE_MEDICINE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.EXCHANGE_PRICE_GENERAL_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.EXCHANGE_PRICE_LIQUOR_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.EXCHANGE_PRICE_DYE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.EXCHANGE_PRICE_ORE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.EXCHANGE_PRICE_INDUSTRIAL_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.EXCHANGE_PRICE_LUXURY_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.EXCHANGE_PRICE_TEXTILE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.EXCHANGE_PRICE_FABRIC_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.EXCHANGE_PRICE_CRAFT_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.EXCHANGE_PRICE_ART_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.EXCHANGE_PRICE_SPICE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.EXCHANGE_PRICE_JEWELRY_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.EXCHANGE_PRICE_AROMA_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.EXCHANGE_PRICE_GEM_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.EXCHANGE_PRICE_WEAPON_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.EXCHANGE_PRICE_FIREARM_PCT,
};
exports.EXCHANGE_GET_GOODS_PCT_PASSIVE_EFFECT = {
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_FOOD_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_CONDIMENT_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_LIVESTOCK_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_MEDICINE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_GENERAL_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_LIQUOR_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_DYE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_ORE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_INDUSTRIAL_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_LUXURY_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_TEXTILE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_FABRIC_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_CRAFT_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_ART_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_SPICE_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_JEWELRY_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_AROMA_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_GEM_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_WEAPON_PCT,
    [tradeGoodsDesc_1.TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_FIREARM_PCT,
};
exports.SMUGGLE_ADDED_BUYABLE_PCT_PASSIVE_EFFECT = {
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_ILLEGAL_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_GREED_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_LUXURY_PCT,
};
exports.SMUGGLE_ADDED_BUYABLE_PASSIVE_EFFECT = {
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_ILLEGAL,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_GREED,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_LUXURY,
};
exports.SMUGGLE_BUY_PRICE_PCT_PASSIVE_EFFECT = {
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_ILLEGAL_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_FOOLHARDINESS_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_GREED_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_LUXURY_PCT,
};
exports.SMUGGLE_BUY_PRICE_PASSIVE_EFFECT = {
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_ILLEGAL,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_FOOLHARDINESS,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_GREED,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_LUXURY,
};
exports.SMUGGLE_SALE_PRICE_PCT_PASSIVE_EFFECT = {
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_ILLEGAL_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_FOOLHARDINESS_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_GREED_PCT,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_LUXURY_PCT,
};
exports.SMUGGLE_SALE_PRICE_PASSIVE_EFFECT = {
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_ILLEGAL,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_FOOLHARDINESS,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_GREED,
    [smuggleGoodsCategoryDesc_1.SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_LUXURY,
};
// https://wiki.line.games/display/MOTIF/missionTargetType
// https://wiki.line.games/pages/viewpage.action?pageId=38240414
var MISSION_TARGET_TYPE;
(function (MISSION_TARGET_TYPE) {
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_TOWN_BUILDING"] = 1] = "GO_TO_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_OCEAN"] = 2] = "GO_TO_OCEAN";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_OCEAN_NPC"] = 3] = "GO_TO_OCEAN_NPC";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NATION_OCEAN_NPC"] = 4] = "GO_TO_NATION_OCEAN_NPC";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["DELIVERY_ITEM_TO_TOWN_BUILDING"] = 5] = "DELIVERY_ITEM_TO_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["DELIVERY_CEQUIP_TO_TOWN_BUILDING"] = 6] = "DELIVERY_CEQUIP_TO_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["DELIVERY_TRADEGOODS_TO_TOWN_BUILDING"] = 7] = "DELIVERY_TRADEGOODS_TO_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["DELIVERY_ITEM_TO_REGION_OCEAN_NPC"] = 8] = "DELIVERY_ITEM_TO_REGION_OCEAN_NPC";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["DELIVERY_CEQUIP_TO_REGION_OCEAN_NPC"] = 9] = "DELIVERY_CEQUIP_TO_REGION_OCEAN_NPC";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["DELIVERY_TRADEGOODS_TO_REGION_OCEAN_NPC"] = 10] = "DELIVERY_TRADEGOODS_TO_REGION_OCEAN_NPC";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["DELIVERY_POINT_TO_TOWN_BUILDING"] = 11] = "DELIVERY_POINT_TO_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_TOWN_ITEM"] = 14] = "GO_TO_NEAREST_TOWN_ITEM";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_TOWN_CEQUIP"] = 15] = "GO_TO_NEAREST_TOWN_CEQUIP";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_TOWN_TRADEGOODS"] = 16] = "GO_TO_NEAREST_TOWN_TRADEGOODS";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_TOWN_BUILDING"] = 17] = "GO_TO_NEAREST_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_TOWN_EXCEPT"] = 18] = "GO_TO_NEAREST_TOWN_EXCEPT";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_TOWN_BUILDING_EXCEPT"] = 19] = "GO_TO_NEAREST_TOWN_BUILDING_EXCEPT";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_REGION_TOWN_BUILDING"] = 20] = "GO_TO_NEAREST_REGION_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_CULTURAL_AREA_TOWN_BUILDING"] = 21] = "GO_TO_NEAREST_CULTURAL_AREA_TOWN_BUILDING";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_NEAREST_TOWN_MATE"] = 22] = "GO_TO_NEAREST_TOWN_MATE";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["GO_TO_DISCOVERY"] = 23] = "GO_TO_DISCOVERY";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_MIN"] = 101] = "RANDOM_MIN";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_TOWN"] = 101] = "RANDOM_TOWN";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_TOWN_RANDOM_TRADE_GOODS"] = 102] = "RANDOM_TOWN_RANDOM_TRADE_GOODS";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_REGION"] = 103] = "RANDOM_REGION";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_REGION_OCEAN_NPC"] = 104] = "RANDOM_REGION_OCEAN_NPC";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_REGION_LOW_INTIMACY_NATION_OCEAN_NPC"] = 105] = "RANDOM_REGION_LOW_INTIMACY_NATION_OCEAN_NPC";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_REGION_LAND_EXPLORE"] = 106] = "RANDOM_REGION_LAND_EXPLORE";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_TOWN_RANDOM_SHOP_ITEM"] = 107] = "RANDOM_TOWN_RANDOM_SHOP_ITEM";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_REGION_RANDOM_OCEAN_DOODAD"] = 108] = "RANDOM_REGION_RANDOM_OCEAN_DOODAD";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_TOWN_RANDOM_SHOP_MATE_EQUIP"] = 109] = "RANDOM_TOWN_RANDOM_SHOP_MATE_EQUIP";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_TOWN_RANDOM_SHOP_MATE_EQUIP_ONLY_EQUIPABLE"] = 110] = "RANDOM_TOWN_RANDOM_SHOP_MATE_EQUIP_ONLY_EQUIPABLE";
    MISSION_TARGET_TYPE[MISSION_TARGET_TYPE["RANDOM_TOWN_INVESTABLE_BY_TOWN_OWN_TYPE"] = 111] = "RANDOM_TOWN_INVESTABLE_BY_TOWN_OWN_TYPE";
})(MISSION_TARGET_TYPE = exports.MISSION_TARGET_TYPE || (exports.MISSION_TARGET_TYPE = {}));
function isRandomMissionTargetType(type) {
    return type >= MISSION_TARGET_TYPE.RANDOM_MIN;
}
exports.isRandomMissionTargetType = isRandomMissionTargetType;
/**
 * https://wiki.line.games/display/MOTIF/QuestTerms
 */
var QUEST_TERMS;
(function (QUEST_TERMS) {
    // [~86100000] CMS.QuestTerms.type = '현재 상태'
    QUEST_TERMS[QUEST_TERMS["MOVE"] = 86000001] = "MOVE";
    QUEST_TERMS[QUEST_TERMS["DELIVERY"] = 86000002] = "DELIVERY";
    QUEST_TERMS[QUEST_TERMS["REGISTER_EQUAL"] = 86000014] = "REGISTER_EQUAL";
    QUEST_TERMS[QUEST_TERMS["FLAG"] = 86000015] = "FLAG";
    QUEST_TERMS[QUEST_TERMS["LEADER_MATE_ROYAL_TITLE"] = 86000016] = "LEADER_MATE_ROYAL_TITLE";
    QUEST_TERMS[QUEST_TERMS["LEADER_MATE_FAME"] = 86000017] = "LEADER_MATE_FAME";
    QUEST_TERMS[QUEST_TERMS["REGISTER"] = 86000020] = "REGISTER";
    QUEST_TERMS[QUEST_TERMS["MATE"] = 86000021] = "MATE";
    QUEST_TERMS[QUEST_TERMS["DUCAT"] = 86000022] = "DUCAT";
    QUEST_TERMS[QUEST_TERMS["SAILED_DAYS"] = 86000024] = "SAILED_DAYS";
    QUEST_TERMS[QUEST_TERMS["MATE_LANGUAGE_LEVEL"] = 86000025] = "MATE_LANGUAGE_LEVEL";
    QUEST_TERMS[QUEST_TERMS["STAT_OPERATOR"] = 86000026] = "STAT_OPERATOR";
    QUEST_TERMS[QUEST_TERMS["COMPANY_LEVEL"] = 86000027] = "COMPANY_LEVEL";
    QUEST_TERMS[QUEST_TERMS["LEADER_MATE_LEVEL"] = 86000028] = "LEADER_MATE_LEVEL";
    QUEST_TERMS[QUEST_TERMS["COMBAT_POWER"] = 86000029] = "COMBAT_POWER";
    QUEST_TERMS[QUEST_TERMS["SPECIFIC_TIER_MAX_SHIP_BLUEPRINT_LEVEL"] = 86000030] = "SPECIFIC_TIER_MAX_SHIP_BLUEPRINT_LEVEL";
    QUEST_TERMS[QUEST_TERMS["SPECIFIC_SHIP_BLUEPRINT_LEVEL"] = 86000031] = "SPECIFIC_SHIP_BLUEPRINT_LEVEL";
    QUEST_TERMS[QUEST_TERMS["EQUIPPED_MATE_EQUIPMENT_COUNT_OVER_SPECIFIC_GRADE"] = 86000032] = "EQUIPPED_MATE_EQUIPMENT_COUNT_OVER_SPECIFIC_GRADE";
    QUEST_TERMS[QUEST_TERMS["EQUIPPED_SHIP_SLOT_ITEM_COUNT_OVER_SPECIFIC_GRADE"] = 86000033] = "EQUIPPED_SHIP_SLOT_ITEM_COUNT_OVER_SPECIFIC_GRADE";
    QUEST_TERMS[QUEST_TERMS["SHIP_COUNT_OVER_SPECIFIC_TIER_WITH_JOB_TYPE"] = 86000034] = "SHIP_COUNT_OVER_SPECIFIC_TIER_WITH_JOB_TYPE";
    QUEST_TERMS[QUEST_TERMS["MATE_COUNT_OVER_SPECIFIC_GRADE_WITH_JOB_TYPE"] = 86000035] = "MATE_COUNT_OVER_SPECIFIC_GRADE_WITH_JOB_TYPE";
    QUEST_TERMS[QUEST_TERMS["DISCOVERY_COUNT_OVER_SPECIFIC_GRADE"] = 86000036] = "DISCOVERY_COUNT_OVER_SPECIFIC_GRADE";
    QUEST_TERMS[QUEST_TERMS["SPECIFIC_DISCOVERY"] = 86000037] = "SPECIFIC_DISCOVERY";
    QUEST_TERMS[QUEST_TERMS["SPECIFIC_TOWN_COLLECTOR_CONTRACT"] = 86000038] = "SPECIFIC_TOWN_COLLECTOR_CONTRACT";
    QUEST_TERMS[QUEST_TERMS["COLLECTOR_REPORTED_WORLD_MAP_RATE"] = 86000039] = "COLLECTOR_REPORTED_WORLD_MAP_RATE";
    QUEST_TERMS[QUEST_TERMS["SPECIFIC_TRADE_GOODS_COUNT_OVER"] = 86000040] = "SPECIFIC_TRADE_GOODS_COUNT_OVER";
    QUEST_TERMS[QUEST_TERMS["LEADER_MATE_SPECIFIC_CEQUIP_EQUIPPED"] = 86000041] = "LEADER_MATE_SPECIFIC_CEQUIP_EQUIPPED";
    QUEST_TERMS[QUEST_TERMS["QUEST_NODE_CURRENT_INDEX"] = 86000042] = "QUEST_NODE_CURRENT_INDEX";
    QUEST_TERMS[QUEST_TERMS["QUEST_UNIQUE_GROUP_COMPLETE"] = 86000043] = "QUEST_UNIQUE_GROUP_COMPLETE";
    // [86100000~] CMS.QuestTerms.type = '누적', CMS.QuestTerms.achievementTermsId 참고
    QUEST_TERMS[QUEST_TERMS["BATTLE_VICTORY_FROM_SPECIFIC_NPC"] = 86100066] = "BATTLE_VICTORY_FROM_SPECIFIC_NPC";
})(QUEST_TERMS = exports.QUEST_TERMS || (exports.QUEST_TERMS = {}));
var COUNTRY_CODE_MASK;
(function (COUNTRY_CODE_MASK) {
    COUNTRY_CODE_MASK[COUNTRY_CODE_MASK["KOREA"] = 1] = "KOREA";
    COUNTRY_CODE_MASK[COUNTRY_CODE_MASK["KOREA_NON_PK"] = 2] = "KOREA_NON_PK";
    COUNTRY_CODE_MASK[COUNTRY_CODE_MASK["GLOBAL"] = 8] = "GLOBAL";
    COUNTRY_CODE_MASK[COUNTRY_CODE_MASK["GLOBAL_NON_PK"] = 16] = "GLOBAL_NON_PK";
    COUNTRY_CODE_MASK[COUNTRY_CODE_MASK["CHINA"] = 64] = "CHINA";
    COUNTRY_CODE_MASK[COUNTRY_CODE_MASK["CHINA_NON_PK"] = 128] = "CHINA_NON_PK";
})(COUNTRY_CODE_MASK = exports.COUNTRY_CODE_MASK || (exports.COUNTRY_CODE_MASK = {}));
var KICK_SITUATION_TYPE;
(function (KICK_SITUATION_TYPE) {
    KICK_SITUATION_TYPE[KICK_SITUATION_TYPE["ENTER_TOWN"] = 1] = "ENTER_TOWN";
    KICK_SITUATION_TYPE[KICK_SITUATION_TYPE["ENTER_BUILDING"] = 2] = "ENTER_BUILDING";
})(KICK_SITUATION_TYPE = exports.KICK_SITUATION_TYPE || (exports.KICK_SITUATION_TYPE = {}));
var KICK_RESULT_TYPE;
(function (KICK_RESULT_TYPE) {
    KICK_RESULT_TYPE[KICK_RESULT_TYPE["NEGO"] = 1] = "NEGO";
    KICK_RESULT_TYPE[KICK_RESULT_TYPE["KICK"] = 2] = "KICK";
})(KICK_RESULT_TYPE = exports.KICK_RESULT_TYPE || (exports.KICK_RESULT_TYPE = {}));
var OCCUPY_TYPE;
(function (OCCUPY_TYPE) {
    OCCUPY_TYPE[OCCUPY_TYPE["MIN"] = 0] = "MIN";
    OCCUPY_TYPE[OCCUPY_TYPE["OCCUPY"] = 0] = "OCCUPY";
    OCCUPY_TYPE[OCCUPY_TYPE["UNOCCUPIED"] = 1] = "UNOCCUPIED";
    OCCUPY_TYPE[OCCUPY_TYPE["RANDOM"] = 2] = "RANDOM";
    OCCUPY_TYPE[OCCUPY_TYPE["PIRATE"] = 3] = "PIRATE";
    OCCUPY_TYPE[OCCUPY_TYPE["MAX"] = 4] = "MAX";
})(OCCUPY_TYPE = exports.OCCUPY_TYPE || (exports.OCCUPY_TYPE = {}));
exports.firstAchievementPointCmsId = 2000;
exports.firstWeatherTileCmsId = 13300000;
exports.firstCulturalAreaCmsId = 12200000;
var NATION_RANKING_EFFECT_CMS_ID;
(function (NATION_RANKING_EFFECT_CMS_ID) {
    NATION_RANKING_EFFECT_CMS_ID[NATION_RANKING_EFFECT_CMS_ID["SELECT_NATION"] = 10300000] = "SELECT_NATION";
    NATION_RANKING_EFFECT_CMS_ID[NATION_RANKING_EFFECT_CMS_ID["EXP"] = 10300001] = "EXP";
    NATION_RANKING_EFFECT_CMS_ID[NATION_RANKING_EFFECT_CMS_ID["FAME"] = 10300002] = "FAME";
    NATION_RANKING_EFFECT_CMS_ID[NATION_RANKING_EFFECT_CMS_ID["TRADE_TAX"] = 10300003] = "TRADE_TAX";
    NATION_RANKING_EFFECT_CMS_ID[NATION_RANKING_EFFECT_CMS_ID["REWARD_DUCAT_CARGO"] = 10300004] = "REWARD_DUCAT_CARGO";
    NATION_RANKING_EFFECT_CMS_ID[NATION_RANKING_EFFECT_CMS_ID["DISCOUNT_SUPPLY_WHERE_MY_NATION_TOWN"] = 10300005] = "DISCOUNT_SUPPLY_WHERE_MY_NATION_TOWN";
})(NATION_RANKING_EFFECT_CMS_ID = exports.NATION_RANKING_EFFECT_CMS_ID || (exports.NATION_RANKING_EFFECT_CMS_ID = {}));
var GENDER;
(function (GENDER) {
    GENDER[GENDER["MAN"] = 0] = "MAN";
    GENDER[GENDER["WOMAN"] = 1] = "WOMAN";
    GENDER[GENDER["COMMON"] = 2] = "COMMON";
})(GENDER = exports.GENDER || (exports.GENDER = {}));
var ZoneType;
(function (ZoneType) {
    ZoneType[ZoneType["INVALID"] = 0] = "INVALID";
    ZoneType[ZoneType["TOWN"] = 1] = "TOWN";
    ZoneType[ZoneType["OCEAN"] = 2] = "OCEAN";
    ZoneType[ZoneType["MAX_ZONE_TYPE"] = 3] = "MAX_ZONE_TYPE";
})(ZoneType = exports.ZoneType || (exports.ZoneType = {}));
var QUEST_SPAWN_OCEAN_DOODAD_TYPE;
(function (QUEST_SPAWN_OCEAN_DOODAD_TYPE) {
    QUEST_SPAWN_OCEAN_DOODAD_TYPE[QUEST_SPAWN_OCEAN_DOODAD_TYPE["CURRENT_LOCATION_RADIUS"] = 0] = "CURRENT_LOCATION_RADIUS";
    QUEST_SPAWN_OCEAN_DOODAD_TYPE[QUEST_SPAWN_OCEAN_DOODAD_TYPE["DESIGNATED_LOCATION_RADIUS"] = 1] = "DESIGNATED_LOCATION_RADIUS";
})(QUEST_SPAWN_OCEAN_DOODAD_TYPE = exports.QUEST_SPAWN_OCEAN_DOODAD_TYPE || (exports.QUEST_SPAWN_OCEAN_DOODAD_TYPE = {}));
var DISCOVERY_GRADE;
(function (DISCOVERY_GRADE) {
    DISCOVERY_GRADE[DISCOVERY_GRADE["D"] = 1] = "D";
    DISCOVERY_GRADE[DISCOVERY_GRADE["C"] = 2] = "C";
    DISCOVERY_GRADE[DISCOVERY_GRADE["B"] = 3] = "B";
    DISCOVERY_GRADE[DISCOVERY_GRADE["A"] = 4] = "A";
    DISCOVERY_GRADE[DISCOVERY_GRADE["S"] = 5] = "S";
})(DISCOVERY_GRADE = exports.DISCOVERY_GRADE || (exports.DISCOVERY_GRADE = {}));
var PUB_GIFT_GRADE;
(function (PUB_GIFT_GRADE) {
    PUB_GIFT_GRADE[PUB_GIFT_GRADE["C"] = 1] = "C";
    PUB_GIFT_GRADE[PUB_GIFT_GRADE["B"] = 2] = "B";
    PUB_GIFT_GRADE[PUB_GIFT_GRADE["A"] = 3] = "A";
    PUB_GIFT_GRADE[PUB_GIFT_GRADE["S"] = 4] = "S";
})(PUB_GIFT_GRADE = exports.PUB_GIFT_GRADE || (exports.PUB_GIFT_GRADE = {}));
exports.SailConsumeSupplies = [SUPPLY_CMS_ID.WATER, SUPPLY_CMS_ID.FOOD];
exports.NoInsuranceCmsId = 40200001;
var NATION_PROMISE_CONDITION_CMS_ID;
(function (NATION_PROMISE_CONDITION_CMS_ID) {
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["NONE"] = 0] = "NONE";
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["SPECIFIC_REGION_INVEST_POINT"] = 10160001] = "SPECIFIC_REGION_INVEST_POINT";
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["SPECIFIC_TOWN_INVEST_POINT"] = 10160002] = "SPECIFIC_TOWN_INVEST_POINT";
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["SPECIFIC_TOWN_OCCPIED_CURRENTLY"] = 10160003] = "SPECIFIC_TOWN_OCCPIED_CURRENTLY";
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["REGION_PARTIALLY_OCCUPIED"] = 10160004] = "REGION_PARTIALLY_OCCUPIED";
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["REGION_COMPLETELY_OCCUPIED"] = 10160005] = "REGION_COMPLETELY_OCCUPIED";
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["NATION_RANK_ACQUIRED"] = 10160006] = "NATION_RANK_ACQUIRED";
    NATION_PROMISE_CONDITION_CMS_ID[NATION_PROMISE_CONDITION_CMS_ID["NATION_BUDGET_ACQUIRED"] = 10160007] = "NATION_BUDGET_ACQUIRED";
})(NATION_PROMISE_CONDITION_CMS_ID = exports.NATION_PROMISE_CONDITION_CMS_ID || (exports.NATION_PROMISE_CONDITION_CMS_ID = {}));
// ------------------------------------------------------------------------------------------------
function GetShipCargoType(cmsId) {
    if (_1.default.DepartSupply[cmsId]) {
        return SHIP_CARGO_TYPE.SUPPLY;
    }
    else {
        const tradeGoodsCms = _1.default.TradeGoods[cmsId];
        // if (tradeGoodsCms && isFilteredByCountryCode(tradeGoodsCms)) {
        //   // TradeGoods가 필터링되면 NONE 반환
        // } else
        if (tradeGoodsCms || _1.default.SmuggleGoods[cmsId]) {
            return SHIP_CARGO_TYPE.TRADE_GOODS;
        }
    }
    return SHIP_CARGO_TYPE.NONE;
}
exports.GetShipCargoType = GetShipCargoType;
// ------------------------------------------------------------------------------------------------
function IsBlackMarketShopType(shopType) {
    return (shopType === shopDesc_1.SHOP_TYPE.BLACK_MARKET_USER_ITEM ||
        shopType === shopDesc_1.SHOP_TYPE.BLACK_MARKET_MATE_EQUIPMENT);
}
exports.IsBlackMarketShopType = IsBlackMarketShopType;
// ------------------------------------------------------------------------------------------------
// 여관-고용 에서 사용되는 항해사 후보군 cms cache
// ------------------------------------------------------------------------------------------------
const mateRecrutingGroupsForPubRecruit = {};
function GetMateRecrutingGroupsForPubRecruiting(group) {
    if (!mateRecrutingGroupsForPubRecruit[group]) {
        const grpCmses = _GetMateRecruitingGroupsByGroupIdCache()[group];
        if (grpCmses) {
            // 여관-동료에서만 사용되는 것 제외
            const reRecruitFiltered = grpCmses.filter((elem) => !elem.isReRecruit);
            mateRecrutingGroupsForPubRecruit[group] = reRecruitFiltered;
        }
        else {
            mateRecrutingGroupsForPubRecruit[group] = [];
        }
    }
    return mateRecrutingGroupsForPubRecruit[group];
}
exports.GetMateRecrutingGroupsForPubRecruiting = GetMateRecrutingGroupsForPubRecruiting;
// ------------------------------------------------------------------------------------------------
// 여관-동료 에서 사용되는 항해사 후보군 cms cache
// ------------------------------------------------------------------------------------------------
const mateRecrutingGroupsForPubReRecruit = {};
function GetMateRecrutingGroupsForPubReRecruiting(group) {
    if (!mateRecrutingGroupsForPubReRecruit[group]) {
        const grpCmses = _GetMateRecruitingGroupsByGroupIdCache()[group];
        if (grpCmses) {
            mateRecrutingGroupsForPubReRecruit[group] = grpCmses;
        }
        else {
            mateRecrutingGroupsForPubReRecruit[group] = [];
        }
    }
    return mateRecrutingGroupsForPubReRecruit[group];
}
exports.GetMateRecrutingGroupsForPubReRecruiting = GetMateRecrutingGroupsForPubReRecruiting;
function isFilteredByCountryCode(localBitFlag) {
    return (localBitFlag & (1 << mconf_1.default.countryCode)) === 0;
}
exports.isFilteredByCountryCode = isFilteredByCountryCode;
// ------------------------------------------------------------------------------------------------
// 국가 코드가 맞지 않는 것을 걸러내고, 단순히 그룹별로 나누기만 합니다.
// ------------------------------------------------------------------------------------------------
let mateRecruitingGroupByGroupIdCache;
function _GetMateRecruitingGroupsByGroupIdCache() {
    if (!mateRecruitingGroupByGroupIdCache) {
        const grouped = {};
        lodash_1.default.forOwn(_1.default.MateRecruitingGroup, (elem) => {
            const mateCms = _1.default.Mate[elem.mateId];
            if (isFilteredByCountryCode(mateCms.localBitFlag)) {
                return;
            }
            (0, assert_1.default)(elem.group !== undefined);
            (0, assert_1.default)(elem.Ratio !== undefined);
            if (grouped[elem.group]) {
                grouped[elem.group].push(elem);
            }
            else {
                grouped[elem.group] = [elem];
            }
        });
        mateRecruitingGroupByGroupIdCache = grouped;
    }
    return mateRecruitingGroupByGroupIdCache;
}
// ------------------------------------------------------------------------------------------------
// https://motif-hq.slack.com/archives/C74627QGJ/p1602581665055100
let mateRecruitingGroupByGroupAndMateCmsIdCache;
function getMateRecruitingGroupByGroupAndMateCmsId(group, mateCmsId) {
    if (!mateRecruitingGroupByGroupAndMateCmsIdCache) {
        mateRecruitingGroupByGroupAndMateCmsIdCache = {};
        lodash_1.default.forOwn(_1.default.MateRecruitingGroup, (elem) => {
            lodash_1.default.merge(mateRecruitingGroupByGroupAndMateCmsIdCache, {
                [elem.group]: {
                    [elem.mateId]: elem,
                },
            });
        });
    }
    return mateRecruitingGroupByGroupAndMateCmsIdCache[group]
        ? mateRecruitingGroupByGroupAndMateCmsIdCache[group][mateCmsId]
        : undefined;
}
exports.getMateRecruitingGroupByGroupAndMateCmsId = getMateRecruitingGroupByGroupAndMateCmsId;
// ------------------------------------------------------------------------------------------------
let mateReRecruitingCmsByMateGradeCache;
function getMateReRecruitingForMateGrade(mateGrade) {
    if (!mateReRecruitingCmsByMateGradeCache) {
        const dict = {};
        lodash_1.default.forOwn(_1.default.MateReRecruiting, (mateReRecruitingCms) => {
            (0, assert_1.default)(!dict[mateReRecruitingCms.mateGrade]);
            dict[mateReRecruitingCms.mateGrade] = mateReRecruitingCms;
        });
        mateReRecruitingCmsByMateGradeCache = dict;
    }
    return mateReRecruitingCmsByMateGradeCache[mateGrade];
}
exports.getMateReRecruitingForMateGrade = getMateReRecruitingForMateGrade;
// ------------------------------------------------------------------------------------------------
function IntimacyModifyValue(nationDiplomacyCmsId, count, base) {
    const nationDiplomacyCms = _1.default.NationDiplomacy[nationDiplomacyCmsId];
    if (nationDiplomacyCms.worldIntimacyMultiplier !== undefined) {
        return (Math.floor((_1.default.Const.IntimacyPoint.value * nationDiplomacyCms.worldIntimacyMultiplier) / 1000) * count);
    }
    return nationDiplomacyCms.relatedNationIntimacy * base * count;
}
exports.IntimacyModifyValue = IntimacyModifyValue;
// ------------------------------------------------------------------------------------------------
// Trade's TradeGoods cms cache.
const sellingTradeGoods = {};
function getDateKey(curTimeUtc) {
    const utcDate = new Date(curTimeUtc * 1000);
    const ret = `${mutil.getLocalFullYear(utcDate)}/${mutil.getLocalMonth(utcDate)}/${mutil.getLocalDate(utcDate)}`;
    return ret;
}
function SetSellingTradeGoods(townCmsId, curTimeUtc) {
    const dateKey = getDateKey(curTimeUtc);
    if (sellingTradeGoods[townCmsId] && sellingTradeGoods[townCmsId][dateKey]) {
        return;
    }
    sellingTradeGoods[townCmsId] = {
        [dateKey]: {},
    };
    for (const key of Object.keys(_1.default.Trade)) {
        const elem = _1.default.Trade[key];
        const goodsCms = _1.default.TradeGoods[elem.tradeGoodsId];
        // if (isFilteredByCountryCode(goodsCms)) {
        //   continue;
        // }
        // 순차 출현과 라이브 이벤트가 모두 입력되어 있다면, 라이브 이벤트를 우선으로 처리한다.(태그 및 출현 일시 모두)
        if (elem.townId === townCmsId) {
            if (elem.liveEvent) {
                if (!isLiveEvent(elem.liveEvent, curTimeUtc)) {
                    continue;
                }
            }
            else if (elem.rotationTradeGroupId) {
                if (!_isPickRotation(elem.rotationTradeGroupId, elem.rotationTradeGroupOrder, curTimeUtc)) {
                    continue;
                }
            }
            sellingTradeGoods[townCmsId][dateKey][elem.tradeGoodsId] = elem;
        }
    }
}
/*
  기획데이터 기준 groupId 존재하면서 order가 없으면 실패되는 상황은 정상
  order에 중간 값이 없다면 건너뛰지 않고 계산한다.
  ex) 만약 order가 1, 3만 있고 1주 간격 등장이면 1등장, 2등장, 3등장 순서대로 계산하므로 2가 등장해야 하는 기간에는 등장하지 않는다.
*/
function _isPickRotation(rotationTradeGroupId, rotationTradeGroupOrder, curTimeUtc) {
    if (!rotationTradeGroupOrder) {
        return false;
    }
    const rotationTradeGroupCms = _1.default.RotationTradeGroup[rotationTradeGroupId];
    const startDateUtc = mutil.newDateByCmsDateStr(rotationTradeGroupCms.startDate).getTime() / 1000;
    if (curTimeUtc < startDateUtc) {
        return false;
    }
    const maxOrder = _getRotationMaxTradeOrder(rotationTradeGroupId);
    const secondsPerDay = rotationTradeGroupCms.resetDays * formula.SECONDS_PER_DAY;
    const repeatedCount = Math.floor((curTimeUtc - startDateUtc) / secondsPerDay);
    const pickRotationId = (repeatedCount % maxOrder) + 1;
    return rotationTradeGroupOrder === pickRotationId;
}
function IsSellInTown(townCmsId, tradeGoodsCmsId, curTimeUtc) {
    SetSellingTradeGoods(townCmsId, curTimeUtc);
    const dateKey = getDateKey(curTimeUtc);
    return sellingTradeGoods[townCmsId][dateKey][tradeGoodsCmsId] ? true : false;
}
exports.IsSellInTown = IsSellInTown;
// 도시에서 판매하는 tradeGoods cms id 반환
function getTownSellingTradeGoodsCmsIds(townCmsId, curTimeUtc) {
    SetSellingTradeGoods(townCmsId, curTimeUtc);
    const dateKey = getDateKey(curTimeUtc);
    return lodash_1.default.values(sellingTradeGoods[townCmsId][dateKey]).map((elem) => elem.tradeGoodsId);
}
exports.getTownSellingTradeGoodsCmsIds = getTownSellingTradeGoodsCmsIds;
function getTownTradeGoodsCms(townCmsId, tradeGoodsCmsId, curTimeUtc) {
    SetSellingTradeGoods(townCmsId, curTimeUtc);
    const dateKey = getDateKey(curTimeUtc);
    return sellingTradeGoods[townCmsId][dateKey][tradeGoodsCmsId];
}
exports.getTownTradeGoodsCms = getTownTradeGoodsCms;
// ------------------------------------------------------------------------------------------------
// SailorDraft cms cache.
const sailorDraftCache = {};
function getTownDraftableSailor(townCmsId, developmentLevels) {
    const townSize = _1.default.Town[townCmsId].townSize;
    const sailorDraftCms = _1.default.SailorDraft;
    if (!sailorDraftCache[townSize]) {
        // Cache all cms data.
        for (const key of Object.keys(sailorDraftCms)) {
            const elem = sailorDraftCms[key];
            if (!sailorDraftCache[elem.townSize]) {
                sailorDraftCache[elem.townSize] = {};
            }
            if (!sailorDraftCache[elem.townSize][elem.townDevelopType]) {
                sailorDraftCache[elem.townSize][elem.townDevelopType] = {};
            }
            sailorDraftCache[elem.townSize][elem.townDevelopType][elem.townDevelopLv] = elem.increaseVal;
        }
    }
    let sum = 0;
    for (const developmentType of Object.keys(developmentLevels)) {
        sum += sailorDraftCache[townSize][developmentType][developmentLevels[developmentType]];
    }
    return sum;
}
exports.getTownDraftableSailor = getTownDraftableSailor;
// ------------------------------------------------------------------------------------------------
// TownBuilding cms cache.
const townBuildingsCache = {};
function getTownBuilding(townCmsId, buildingType) {
    if (!townBuildingsCache[townCmsId]) {
        for (const key of Object.keys(_1.default.TownBuilding)) {
            const elem = _1.default.TownBuilding[key];
            if (!townBuildingsCache[elem.townId]) {
                townBuildingsCache[elem.townId] = {};
            }
            if (!townBuildingsCache[elem.townId][elem.buildingType]) {
                townBuildingsCache[elem.townId][elem.buildingType] = [];
            }
            townBuildingsCache[elem.townId][elem.buildingType].push(elem);
        }
    }
    return townBuildingsCache[townCmsId][buildingType];
}
exports.getTownBuilding = getTownBuilding;
// ------------------------------------------------------------------------------------------------
let collectorTownBuildingCmsIdsCache;
function getCollectorTownBuildingCmsIds() {
    if (!collectorTownBuildingCmsIdsCache) {
        collectorTownBuildingCmsIdsCache = [];
        lodash_1.default.forOwn(_1.default.TownBuilding, (elem) => {
            if (elem.buildingType === BUILDING_TYPE.COLLECTOR) {
                collectorTownBuildingCmsIdsCache.push(elem.id);
            }
        });
    }
    return collectorTownBuildingCmsIdsCache;
}
exports.getCollectorTownBuildingCmsIds = getCollectorTownBuildingCmsIds;
// ------------------------------------------------------------------------------------------------
let sortedDiscoveryCollectorRankingCache;
let sortedDiscoveryGlobalRankingCache;
let maxGlobalRanking;
function _sortDiscoveryRanking() {
    if (sortedDiscoveryCollectorRankingCache) {
        return;
    }
    sortedDiscoveryCollectorRankingCache = [];
    sortedDiscoveryGlobalRankingCache = [];
    maxGlobalRanking = 0;
    lodash_1.default.forOwn(_1.default.DiscoveryRanking, (desc) => {
        if (desc.rankingTarget === discoveryRankingDesc_1.DISCOVERY_RANKING_TARGET.GLOBAL) {
            sortedDiscoveryGlobalRankingCache.push(desc);
            maxGlobalRanking = Math.max(maxGlobalRanking, desc.rankingRange);
        }
        else {
            sortedDiscoveryCollectorRankingCache.push(desc);
        }
    });
    sortedDiscoveryCollectorRankingCache.sort((a, b) => a.id - b.id);
    sortedDiscoveryGlobalRankingCache.sort((a, b) => a.id - b.id);
}
function getSortedDiscoveryCollectorRanking() {
    _sortDiscoveryRanking();
    return sortedDiscoveryCollectorRankingCache;
}
exports.getSortedDiscoveryCollectorRanking = getSortedDiscoveryCollectorRanking;
function getSortedDiscoveryGlobalRanking() {
    _sortDiscoveryRanking();
    return sortedDiscoveryGlobalRankingCache;
}
exports.getSortedDiscoveryGlobalRanking = getSortedDiscoveryGlobalRanking;
function getMaxGlobalRanking() {
    _sortDiscoveryRanking();
    return maxGlobalRanking;
}
exports.getMaxGlobalRanking = getMaxGlobalRanking;
// ------------------------------------------------------------------------------------------------
let sortedInvestmentRankingCache;
function _sortInvestmentRanking() {
    if (sortedInvestmentRankingCache) {
        return;
    }
    sortedInvestmentRankingCache = [];
    lodash_1.default.forOwn(_1.default.InvestCompanyRanking, (desc) => {
        if (isFilteredByCountryCode(desc.localBitFlag)) {
            return;
        }
        sortedInvestmentRankingCache.push(desc);
    });
    sortedInvestmentRankingCache.sort((a, b) => a.id - b.id);
}
function getSortedInvestmentRanking() {
    _sortInvestmentRanking();
    return sortedInvestmentRankingCache;
}
exports.getSortedInvestmentRanking = getSortedInvestmentRanking;
// ------------------------------------------------------------------------------------------------
let sortedEventInvestmentRankingCache;
function _sortEventInvestmentRanking() {
    if (sortedEventInvestmentRankingCache) {
        return;
    }
    sortedEventInvestmentRankingCache = [];
    lodash_1.default.forOwn(_1.default.InvestEventRanking, (desc) => {
        if (isFilteredByCountryCode(desc.localBitFlag)) {
            return;
        }
        sortedEventInvestmentRankingCache.push(desc);
    });
    sortedEventInvestmentRankingCache.sort((a, b) => a.id - b.id);
}
function getSortedEventInvestmentRanking() {
    _sortEventInvestmentRanking();
    return sortedEventInvestmentRankingCache;
}
exports.getSortedEventInvestmentRanking = getSortedEventInvestmentRanking;
// ------------------------------------------------------------------------------------------------
// DefaultPoint cms cache.
const defaultPoints = {};
function getDefaultPoint(mateCmsId) {
    if (!defaultPoints[mateCmsId]) {
        for (const key of Object.keys(_1.default.DefaultPoint)) {
            const elem = _1.default.DefaultPoint[key];
            defaultPoints[elem.mateId] = [];
            for (const point of elem.point) {
                defaultPoints[elem.mateId].push({
                    cmsId: point.Type,
                    value: point.Val,
                });
            }
        }
    }
    return defaultPoints[mateCmsId];
}
exports.getDefaultPoint = getDefaultPoint;
// ------------------------------------------------------------------------------------------------
let defaultShipSailCrestsCache;
function getDefaultShipSailCrests() {
    if (!defaultShipSailCrestsCache) {
        defaultShipSailCrestsCache = {};
        lodash_1.default.forOwn(_1.default.DefaultSailCrest, (elem) => {
            const id = elem.defaultsailcrestId;
            const offset = Math.floor(id / 32);
            if (!defaultShipSailCrestsCache[offset]) {
                defaultShipSailCrestsCache[offset] = 0;
            }
            defaultShipSailCrestsCache[offset] =
                (defaultShipSailCrestsCache[offset] | (1 << id % 32)) >>> 0;
        });
    }
    return defaultShipSailCrestsCache;
}
exports.getDefaultShipSailCrests = getDefaultShipSailCrests;
// ------------------------------------------------------------------------------------------------
let defaultShipSailPatternColorsCache;
function getDefaultShipSailPatternColors() {
    if (!defaultShipSailPatternColorsCache) {
        defaultShipSailPatternColorsCache = {};
        lodash_1.default.forOwn(_1.default.DefaultSailPatternColor, (elem) => {
            const id = elem.defaultsailpatterncolorId;
            const offset = Math.floor(id / 32);
            if (!defaultShipSailPatternColorsCache[offset]) {
                defaultShipSailPatternColorsCache[offset] = 0;
            }
            defaultShipSailPatternColorsCache[offset] =
                (defaultShipSailPatternColorsCache[offset] | (1 << id % 32)) >>> 0;
        });
    }
    return defaultShipSailPatternColorsCache;
}
exports.getDefaultShipSailPatternColors = getDefaultShipSailPatternColors;
// ------------------------------------------------------------------------------------------------
let defaultShipBody1ColorsCache;
function getDefaultShipBody1Colors() {
    if (!defaultShipBody1ColorsCache) {
        defaultShipBody1ColorsCache = {};
        lodash_1.default.forOwn(_1.default.DefaultShipBodyFirstColor, (elem) => {
            const id = elem.defaultshipbodyfirstcolorId;
            const offset = Math.floor(id / 32);
            if (!defaultShipBody1ColorsCache[offset]) {
                defaultShipBody1ColorsCache[offset] = 0;
            }
            defaultShipBody1ColorsCache[offset] =
                (defaultShipBody1ColorsCache[offset] | (1 << id % 32)) >>> 0;
        });
    }
    return defaultShipBody1ColorsCache;
}
exports.getDefaultShipBody1Colors = getDefaultShipBody1Colors;
// ------------------------------------------------------------------------------------------------
function getVillageFriendshipCms(friendship) {
    for (const key of Object.keys(_1.default.VillageFriendship)) {
        if (friendship >= _1.default.VillageFriendship[key].reputation) {
            return _1.default.VillageFriendship[key];
        }
    }
    return undefined;
}
exports.getVillageFriendshipCms = getVillageFriendshipCms;
// ------------------------------------------------------------------------------------------------
let sortedVillageFriendShipCmses;
function getVillageFriendshipCmsesSortedByGroup() {
    if (!sortedVillageFriendShipCmses) {
        sortedVillageFriendShipCmses = lodash_1.default.sortBy(_1.default.VillageFriendship, (elem) => elem.grade);
    }
    return sortedVillageFriendShipCmses;
}
exports.getVillageFriendshipCmsesSortedByGroup = getVillageFriendshipCmsesSortedByGroup;
// ------------------------------------------------------------------------------------------------
let villageDiscoveryCache;
function getVillageDiscoveryIds(groupId) {
    if (villageDiscoveryCache) {
        return villageDiscoveryCache[groupId];
    }
    villageDiscoveryCache = {};
    lodash_1.default.forOwn(_1.default.VillageDiscoveryGroup, (villageDiscoveryCms) => {
        const discoveryCms = _1.default.Discovery[villageDiscoveryCms.discoveryId];
        // if (isFilteredByCountryCode(discoveryCms)) {
        //   return;
        // }
        if (!discoveryCms) {
            return;
        }
        if (!villageDiscoveryCache[villageDiscoveryCms.group]) {
            villageDiscoveryCache[villageDiscoveryCms.group] = [];
        }
        villageDiscoveryCache[villageDiscoveryCms.group].push(discoveryCms);
    });
    return villageDiscoveryCache[groupId];
}
exports.getVillageDiscoveryIds = getVillageDiscoveryIds;
// ------------------------------------------------------------------------------------------------
let exchangeVillageListCache;
function getVillageExchangeList(groupId) {
    if (!exchangeVillageListCache) {
        exchangeVillageListCache = {};
        lodash_1.default.forOwn(_1.default.ExchangeVillageList, (exchangeVillageListCms) => {
            if (!exchangeVillageListCache[exchangeVillageListCms.group]) {
                exchangeVillageListCache[exchangeVillageListCms.group] = [];
            }
            exchangeVillageListCache[exchangeVillageListCms.group].push(exchangeVillageListCms);
        });
    }
    return exchangeVillageListCache[groupId];
}
exports.getVillageExchangeList = getVillageExchangeList;
// ------------------------------------------------------------------------------------------------
function getExchangeVillageStorageByLevelValue(levelValue) {
    for (const id in _1.default.ExchangeVillageStorage) {
        const exchangeVillageStorageCms = _1.default.ExchangeVillageStorage[id];
        if (exchangeVillageStorageCms.storageGradeSubLevelMin <= levelValue &&
            levelValue < exchangeVillageStorageCms.storageGradeSubLevelMax) {
            return exchangeVillageStorageCms;
        }
    }
    return undefined;
}
exports.getExchangeVillageStorageByLevelValue = getExchangeVillageStorageByLevelValue;
// ------------------------------------------------------------------------------------------------
function getTradeGoodsCategoryByExchangeVillageList(exchangeVillageListCmsId) {
    const exchangeVillageListCms = _1.default.ExchangeVillageList[exchangeVillageListCmsId];
    const tradeGoodsCms = _1.default.TradeGoods[exchangeVillageListCms.getTradeGoodsId];
    // if (isFilteredByCountryCode(tradeGoodsCms)) {
    //   return undefined;
    // }
    return tradeGoodsCms === null || tradeGoodsCms === void 0 ? void 0 : tradeGoodsCms.tradeGoodsCategory;
}
exports.getTradeGoodsCategoryByExchangeVillageList = getTradeGoodsCategoryByExchangeVillageList;
// ------------------------------------------------------------------------------------------------
let exchangeVillageListCmsIdsByVillageCache;
function getExchangeVillageListCmsIdsByVillage() {
    if (!exchangeVillageListCmsIdsByVillageCache) {
        exchangeVillageListCmsIdsByVillageCache = {};
        lodash_1.default.forOwn(_1.default.Village, (villageCms) => {
            const exchangeVillageListCmses = getVillageExchangeList(villageCms.exchangeVillageListGroup);
            exchangeVillageListCmsIdsByVillageCache[villageCms.id] = [];
            lodash_1.default.forEach(exchangeVillageListCmses, (exchangeVillageListCms) => {
                exchangeVillageListCmsIdsByVillageCache[villageCms.id].push(exchangeVillageListCms.id);
            });
        });
    }
    return exchangeVillageListCmsIdsByVillageCache;
}
exports.getExchangeVillageListCmsIdsByVillage = getExchangeVillageListCmsIdsByVillage;
// ------------------------------------------------------------------------------------------------
// 육지 탐색시  해제 되는  재해 cmsId 저장
let landDisasterCmsIdCache;
function getLandDisasterCmsIds() {
    if (landDisasterCmsIdCache) {
        return landDisasterCmsIdCache;
    }
    landDisasterCmsIdCache = [];
    lodash_1.default.forOwn(_1.default.OceanDisaster, (oceanDisaster) => {
        if (oceanDisaster.isLandingSolution) {
            landDisasterCmsIdCache.push(oceanDisaster.id);
        }
    });
    return landDisasterCmsIdCache;
}
exports.getLandDisasterCmsIds = getLandDisasterCmsIds;
// ------------------------------------------------------------------------------------------------
let defaultShipBody2ColorsCache;
function getDefaultShipBody2Colors() {
    if (!defaultShipBody2ColorsCache) {
        defaultShipBody2ColorsCache = {};
        lodash_1.default.forOwn(_1.default.DefaultShipBodySecondColor, (elem) => {
            const id = elem.defaultshipbodysecondcolorId;
            const offset = Math.floor(id / 32);
            if (!defaultShipBody2ColorsCache[offset]) {
                defaultShipBody2ColorsCache[offset] = 0;
            }
            defaultShipBody2ColorsCache[offset] =
                (defaultShipBody2ColorsCache[offset] | (1 << id % 32)) >>> 0;
        });
    }
    return defaultShipBody2ColorsCache;
}
exports.getDefaultShipBody2Colors = getDefaultShipBody2Colors;
// ------------------------------------------------------------------------------------------------
function getMaxMateLevel(awakenLevel, userLevel) {
    return Math.min(userLevel, _1.default.Const['MateAwaken' + awakenLevel + 'DevelopmentLv'].value);
}
exports.getMaxMateLevel = getMaxMateLevel;
// ------------------------------------------------------------------------------------------------
function calcMateLevel(jobType, exp, awakenLevel, userLevel) {
    const maxLv = getMaxMateLevel(awakenLevel, userLevel);
    for (let level = 1; level <= maxLv; level++) {
        const expData = _1.default.CharacterExp[level];
        if (exp < expData.accumulateExp[jobType - 1]) {
            return expData.id;
        }
    }
    return maxLv;
}
exports.calcMateLevel = calcMateLevel;
// ------------------------------------------------------------------------------------------------
function calcUserLevel(exp) {
    for (let level = 1; level <= _1.default.Const.MaxCompanyLv.value; level++) {
        const expData = _1.default.CompanyExp[level];
        if (exp < expData.accumulateExp) {
            return expData.id;
        }
    }
    return _1.default.Const.MaxCompanyLv.value;
}
exports.calcUserLevel = calcUserLevel;
// ------------------------------------------------------------------------------------------------
function getMaxUserExp() {
    const maxLevel = _1.default.Const.MaxCompanyLv.value;
    return _1.default.CompanyExp[maxLevel - 1].accumulateExp;
}
exports.getMaxUserExp = getMaxUserExp;
// ------------------------------------------------------------------------------------------------
// 패스 이벤트 - LV 계산
function calcPassEventLevel(passEventCms, exp) {
    const maxLevel = getPassEventMaxLevel(passEventCms);
    for (let level = 1; level <= maxLevel; level++) {
        const expData = _1.default.EventMissionExp[level];
        if (exp < expData.accumulateExp) {
            return level;
        }
    }
    return maxLevel;
}
exports.calcPassEventLevel = calcPassEventLevel;
// ------------------------------------------------------------------------------------------------
// 패스 이벤트 - 최대 EXP
function getMaxPassEventExp(passEventCms) {
    const addedExp = getPassEventAddedExp(passEventCms);
    const maxLevel = getPassEventMaxLevel(passEventCms);
    return _1.default.EventMissionExp[maxLevel - 1].accumulateExp + addedExp;
}
exports.getMaxPassEventExp = getMaxPassEventExp;
// ------------------------------------------------------------------------------------------------
function getMaxBpLevel() {
    return _1.default.Const.MaxShipBlueprintLv.value;
}
exports.getMaxBpLevel = getMaxBpLevel;
// ------------------------------------------------------------------------------------------------
function getMaxBlueprintExp(shipSize) {
    const maxBpLevel = getMaxBpLevel();
    return getShipBuildMasteryExpCmsAccumulateExp(maxBpLevel - 1, shipSize);
}
exports.getMaxBlueprintExp = getMaxBlueprintExp;
/*
  shipCms.shipSize 2: 소형, 3: 중형, 4: 대형 5: 초대형 =>
  shipBuildingsCms.shipSize 0: 소형, 1: 중형, 2: 대형, 3: 초대형
*/
function getShipBuildMasteryExpCmsAccumulateExp(bpLevel, shipSize) {
    return _1.default.ShipBuildMasteryExp[bpLevel].accumulateExp[shipSize - 2];
}
exports.getShipBuildMasteryExpCmsAccumulateExp = getShipBuildMasteryExpCmsAccumulateExp;
// ------------------------------------------------------------------------------------------------
function getShipBuildExpCmsAddMinRate(bpLevel, shipSize) {
    return _1.default.ShipBuildMasteryExp[bpLevel].addMinRate[shipSize - 2];
}
exports.getShipBuildExpCmsAddMinRate = getShipBuildExpCmsAddMinRate;
// ------------------------------------------------------------------------------------------------
// blueprint EXP 로 blueprint LV 반환
function calcBlueprintExpLevel(exp, shipSize) {
    const maxBpLevel = getMaxBpLevel();
    for (let level = 1; level <= maxBpLevel; level++) {
        if (exp < getShipBuildMasteryExpCmsAccumulateExp(level, shipSize)) {
            return level;
        }
    }
    return maxBpLevel;
}
exports.calcBlueprintExpLevel = calcBlueprintExpLevel;
// ------------------------------------------------------------------------------------------------
let blackMarketShopCache = {};
function getBlackMarketShopCmses(townCmsId, curTimeUtc) {
    if (!blackMarketShopCache[townCmsId]) {
        blackMarketShopCache[townCmsId] = {};
        const shopCmsIds = getTownShopCmsIds(townCmsId);
        if (!shopCmsIds || shopCmsIds.size === 0) {
            return {};
        }
        for (const cmsId of shopCmsIds) {
            const shopCms = _1.default.Shop[cmsId];
            if (shopCms.shopType === shopDesc_1.SHOP_TYPE.BLACK_MARKET_USER_ITEM ||
                shopCms.shopType === shopDesc_1.SHOP_TYPE.BLACK_MARKET_MATE_EQUIPMENT) {
                if (!shopCms.Probability) {
                    // 기획 테이블 버그.
                    return undefined;
                }
                if (shopCms.blackMarketType === shopDesc_1.BLACK_MARKET_TYPE.EVENT) {
                    if (!isLiveEvent(shopCms.liveEvent, curTimeUtc)) {
                        continue;
                    }
                }
                if (!blackMarketShopCache[townCmsId][shopCms.blackMarketType]) {
                    blackMarketShopCache[townCmsId][shopCms.blackMarketType] = [];
                }
                blackMarketShopCache[townCmsId][shopCms.blackMarketType].push(shopCms);
            }
        }
    }
    return blackMarketShopCache[townCmsId] ? blackMarketShopCache[townCmsId] : {};
}
exports.getBlackMarketShopCmses = getBlackMarketShopCmses;
// ------------------------------------------------------------------------------------------------
let fishDiscoveryCache;
function getFishDiscoveryCmses(groupId) {
    if (!fishDiscoveryCache) {
        fishDiscoveryCache = {};
        lodash_1.default.forOwn(_1.default.FishDiscoveryGroup, (elem) => {
            const discoveryCms = _1.default.Discovery[elem.discoveryId];
            // if (isFilteredByCountryCode(discoveryCms)) {
            //   return;
            // }
            if (!discoveryCms) {
                return;
            }
            if (!fishDiscoveryCache[elem.group]) {
                fishDiscoveryCache[elem.group] = [];
            }
            fishDiscoveryCache[elem.group].push(discoveryCms);
        });
    }
    return fishDiscoveryCache[groupId];
}
exports.getFishDiscoveryCmses = getFishDiscoveryCmses;
// ------------------------------------------------------------------------------------------------
let fishGradeCache;
function getFishGradeCms(grade) {
    if (!fishGradeCache) {
        fishGradeCache = {};
        lodash_1.default.forOwn(_1.default.FishGrade, (elem) => {
            fishGradeCache[elem.discoveryGradeMax] = elem;
        });
    }
    return fishGradeCache[grade];
}
exports.getFishGradeCms = getFishGradeCms;
// ------------------------------------------------------------------------------------------------
const admiralCache = {};
function getAdmiralByMateCmsId(mateCmsId) {
    if (Object.keys(admiralCache).length === 0) {
        for (const key of Object.keys(_1.default.Admiral)) {
            const admiralCms = _1.default.Admiral[key];
            admiralCache[admiralCms.mateId] = admiralCms;
        }
    }
    return admiralCache[mateCmsId];
}
exports.getAdmiralByMateCmsId = getAdmiralByMateCmsId;
// ------------------------------------------------------------------------------------------------
let cachePreferenceItem = {
    lastWeekCnt: undefined,
};
const collectorPreferenceSeed = 100;
function collectorThisWeekPreferenceItems(townBuildingCmsId) {
    const weekCnt = (0, formula_1.GetFullWeeksUsingLocalTime)(mutil.curTimeUtc(), _1.default.Define.DiscoveryPreferentialReport);
    if (cachePreferenceItem && cachePreferenceItem.lastWeekCnt === weekCnt) {
        return cachePreferenceItem[townBuildingCmsId];
    }
    let easyDifficultyItems = [];
    let normalDifficultyItems = [];
    let hardDifficultyItems = [];
    lodash_1.default.forOwn(_1.default.Item, (desc) => {
        if (desc.type !== itemDesc_1.ITEM_TYPE.RESOURCE) {
            return;
        }
        if (desc.mainGrade === itemDesc_1.ITEM_GRADE.C || desc.mainGrade === itemDesc_1.ITEM_GRADE.D) {
            easyDifficultyItems.push({
                itemCmsId: desc.id,
                difficulty: itemDesc_1.ITEM_REPORT_DIFFICULTY.EASY,
            });
        }
        if (desc.mainGrade === itemDesc_1.ITEM_GRADE.A || desc.mainGrade === itemDesc_1.ITEM_GRADE.B) {
            normalDifficultyItems.push({
                itemCmsId: desc.id,
                difficulty: itemDesc_1.ITEM_REPORT_DIFFICULTY.NORMAL,
            });
        }
        if (desc.mainGrade === itemDesc_1.ITEM_GRADE.S) {
            hardDifficultyItems.push({
                itemCmsId: desc.id,
                difficulty: itemDesc_1.ITEM_REPORT_DIFFICULTY.HARD,
            });
        }
    });
    // 자원 보고 아이템이 없는 경우 에러 처리
    (0, assert_1.default)(easyDifficultyItems.length > 0);
    (0, assert_1.default)(normalDifficultyItems.length > 0);
    (0, assert_1.default)(hardDifficultyItems.length > 0);
    (0, formula_1.shuffleReportItems)(easyDifficultyItems, collectorPreferenceSeed);
    (0, formula_1.shuffleReportItems)(normalDifficultyItems, collectorPreferenceSeed);
    (0, formula_1.shuffleReportItems)(hardDifficultyItems, collectorPreferenceSeed);
    // 0 ~ (arr.length-1)값 startIndex 결정한다
    let easyReportIdx = weekCnt % easyDifficultyItems.length;
    let normalReportIdx = weekCnt % normalDifficultyItems.length;
    let hardReportIdx = weekCnt % hardDifficultyItems.length;
    lodash_1.default.forOwn(_1.default.TownBuilding, (desc) => {
        if (desc.buildingType !== BUILDING_TYPE.COLLECTOR) {
            return;
        }
        // cache update
        cachePreferenceItem[desc.id] = [
            easyDifficultyItems[easyReportIdx],
            normalDifficultyItems[normalReportIdx],
            hardDifficultyItems[hardReportIdx],
        ];
        easyReportIdx++;
        if (easyReportIdx >= easyDifficultyItems.length) {
            easyReportIdx = 0;
        }
        normalReportIdx++;
        if (normalReportIdx >= normalDifficultyItems.length) {
            normalReportIdx = 0;
        }
        hardReportIdx++;
        if (hardReportIdx >= hardDifficultyItems.length) {
            hardReportIdx = 0;
        }
    });
    cachePreferenceItem.lastWeekCnt = weekCnt;
    return cachePreferenceItem[townBuildingCmsId];
}
exports.collectorThisWeekPreferenceItems = collectorThisWeekPreferenceItems;
// ------------------------------------------------------------------------------------------------
const mustAppearRequestGroupCache = {};
function isMustAppearQuest(townCmsId, questId) {
    buildRequestGroupCache();
    buildEventRequestGroupCache();
    const townCms = _1.default.Town[townCmsId];
    if (!townCms) {
        return false;
    }
    if (!mustAppearRequestGroupCache[townCmsId]) {
        mustAppearRequestGroupCache[townCmsId] = new Set();
        const groups = townCms.requestGroups;
        if (groups) {
            for (const group of groups) {
                const cache = requestGroupCache[group];
                lodash_1.default.forOwn(cache, (requestGroupCmses) => {
                    for (const requestGroupCms of requestGroupCmses) {
                        if (requestGroupCms.isMustAppear) {
                            mustAppearRequestGroupCache[townCmsId].add(requestGroupCms.questId);
                        }
                    }
                });
                const eventCache = eventRequestGroupCache[group];
                lodash_1.default.forOwn(eventCache, (eventRequestGroupCmses) => {
                    for (const eventRequestGroupCms of eventRequestGroupCmses) {
                        if (eventRequestGroupCms.isMustAppear) {
                            mustAppearRequestGroupCache[townCmsId].add(eventRequestGroupCms.questId);
                        }
                    }
                });
            }
        }
    }
    return mustAppearRequestGroupCache[townCmsId].has(questId);
}
exports.isMustAppearQuest = isMustAppearQuest;
let requestGroupCache;
const townRequestGroupCache = {};
function buildRequestGroupCache() {
    if (requestGroupCache) {
        return;
    }
    requestGroupCache = {};
    lodash_1.default.forOwn(_1.default.RequestGroup, (requestCms) => {
        if (!requestCms.ratio) {
            // 조합-의뢰에서 확률을 무시하는 기획이 있어서 ratio 가 0인데도 뽑히는 경우(QAUWO-2688)가 있었음
            // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
            return;
        }
        if (!requestGroupCache[requestCms.group]) {
            requestGroupCache[requestCms.group] = {
                [JOB_TYPE.NONE]: [],
                [JOB_TYPE.ADVENTURE]: [],
                [JOB_TYPE.TRADE]: [],
                [JOB_TYPE.BATTLE]: [],
            };
        }
        const questCms = _1.default.Quest[requestCms.questId];
        if (isFilteredByCountryCode(questCms.localBitflag)) {
            return;
        }
        if (questCms.category !== QUEST_CATEGORY.REQUEST) {
            return;
        }
        requestGroupCache[requestCms.group][questCms.jobType].push(requestCms);
    });
}
function getTownRequestGroup(townCmsId, getAllRequestGroup = true) {
    buildRequestGroupCache();
    // Build townRequestGroupCache
    if (!townRequestGroupCache[townCmsId]) {
        townRequestGroupCache[townCmsId] = {
            [JOB_TYPE.NONE]: [],
            [JOB_TYPE.ADVENTURE]: [],
            [JOB_TYPE.TRADE]: [],
            [JOB_TYPE.BATTLE]: [],
        };
        const groups = _1.default.Town[townCmsId].requestGroups;
        if (groups) {
            for (const group of groups) {
                const cr = requestGroupCache[group];
                lodash_1.default.forOwn(cr, (jobElem, jobType) => {
                    for (const requestCms of jobElem) {
                        if (!getAllRequestGroup && isMustAppearQuest(townCmsId, requestCms.questId)) {
                            continue;
                        }
                        const sameElem = townRequestGroupCache[townCmsId][jobType].find((inRequestCms) => {
                            return inRequestCms.questId === requestCms.questId;
                        });
                        if (sameElem) {
                            sameElem.ratio += requestCms.ratio;
                        }
                        else {
                            townRequestGroupCache[townCmsId][jobType].push({
                                questId: requestCms.questId,
                                ratio: requestCms.ratio,
                            });
                        }
                    }
                });
            }
        }
    }
    return townRequestGroupCache[townCmsId];
}
exports.getTownRequestGroup = getTownRequestGroup;
let eventRequestGroupCache;
const townEventRequestGroupCache = {};
function buildEventRequestGroupCache() {
    if (eventRequestGroupCache) {
        return;
    }
    eventRequestGroupCache = {};
    lodash_1.default.forOwn(_1.default.EventRequestGroup, (eventRequestCms) => {
        if (!eventRequestCms.ratio) {
            // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
            return;
        }
        if (!eventRequestGroupCache[eventRequestCms.group]) {
            eventRequestGroupCache[eventRequestCms.group] = {
                [JOB_TYPE.NONE]: [],
                [JOB_TYPE.ADVENTURE]: [],
                [JOB_TYPE.TRADE]: [],
                [JOB_TYPE.BATTLE]: [],
            };
        }
        const questCms = _1.default.Quest[eventRequestCms.questId];
        if (isFilteredByCountryCode(questCms.localBitflag)) {
            return;
        }
        if (questCms.category !== QUEST_CATEGORY.EVENT_REQUEST) {
            return;
        }
        eventRequestGroupCache[eventRequestCms.group][questCms.jobType].push(eventRequestCms);
    });
}
function getTownEventRequestGroup(townCmsId, getAllEventRequestGroup = true) {
    buildEventRequestGroupCache();
    // Build townEventRequestGroupCache
    if (!townEventRequestGroupCache[townCmsId]) {
        townEventRequestGroupCache[townCmsId] = {
            [JOB_TYPE.NONE]: [],
            [JOB_TYPE.ADVENTURE]: [],
            [JOB_TYPE.TRADE]: [],
            [JOB_TYPE.BATTLE]: [],
        };
        const groups = _1.default.Town[townCmsId].requestGroups;
        if (groups) {
            for (const group of groups) {
                const cr = eventRequestGroupCache[group];
                lodash_1.default.forOwn(cr, (jobElem, jobType) => {
                    for (const requestCms of jobElem) {
                        if (!getAllEventRequestGroup && isMustAppearQuest(townCmsId, requestCms.questId)) {
                            continue;
                        }
                        const sameElem = townEventRequestGroupCache[townCmsId][jobType].find((inRequestCms) => {
                            return inRequestCms.questId === requestCms.questId;
                        });
                        if (sameElem) {
                            sameElem.ratio += requestCms.ratio;
                        }
                        else {
                            townEventRequestGroupCache[townCmsId][jobType].push({
                                questId: requestCms.questId,
                                ratio: requestCms.ratio,
                            });
                        }
                    }
                });
            }
        }
    }
    return townEventRequestGroupCache[townCmsId];
}
exports.getTownEventRequestGroup = getTownEventRequestGroup;
// ------------------------------------------------------------------------------------------------
let burstGroupCache;
function buildBurstGroupCache() {
    if (burstGroupCache) {
        return;
    }
    burstGroupCache = {};
    lodash_1.default.forOwn(_1.default.BurstGroup, (elem) => {
        if (!elem.ratio) {
            // 조합-의뢰에서 확률을 무시하는 기획이 있어서 ratio 가 0인데도 뽑히는 경우(QAUWO-2688)가 있었음
            // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
            return;
        }
        if (!burstGroupCache[elem.group]) {
            burstGroupCache[elem.group] = [];
        }
        const questCms = _1.default.Quest[elem.questId];
        if (isFilteredByCountryCode(questCms.localBitflag)) {
            return;
        }
        if (questCms.category !== QUEST_CATEGORY.RANDOM) {
            return;
        }
        burstGroupCache[elem.group].push(elem);
    });
}
const itemQuestGroupCache = {};
function getItemQuestGroup(itemCmsId) {
    buildBurstGroupCache();
    // Build itemRequestGroupCache
    if (!itemQuestGroupCache[itemCmsId]) {
        itemQuestGroupCache[itemCmsId] = [];
        const groups = _1.default.Item[itemCmsId].burstGroups;
        if (groups) {
            for (const group of groups) {
                for (const burstGroupCms of burstGroupCache[group]) {
                    const sameElem = itemQuestGroupCache[itemCmsId].find((elem) => {
                        return elem.questId === burstGroupCms.questId;
                    });
                    if (sameElem) {
                        sameElem.ratio += burstGroupCms.ratio;
                    }
                    else {
                        itemQuestGroupCache[itemCmsId].push({
                            questId: burstGroupCms.questId,
                            ratio: burstGroupCms.ratio,
                        });
                    }
                }
            }
        }
    }
    return itemQuestGroupCache[itemCmsId];
}
exports.getItemQuestGroup = getItemQuestGroup;
// ------------------------------------------------------------------------------------------------
let royalOrderGroupCache;
let royalTitleOrderGroupCache;
function buildRoyalOrderGroupCache() {
    if (royalOrderGroupCache && royalTitleOrderGroupCache) {
        return;
    }
    royalOrderGroupCache = {};
    royalTitleOrderGroupCache = {};
    lodash_1.default.forOwn(_1.default.RoyalOrderGroup, (elem) => {
        if (!elem.ratio) {
            // 조합-의뢰에서 확률을 무시하는 기획이 있어서 ratio 가 0인데도 뽑히는 경우(QAUWO-2688)가 있었음
            // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
            // 왕궁-칙명에서도 작위 승급 칙명에서 확률을 무시하는 기획이 있는 것 참고
            return;
        }
        const questCms = _1.default.Quest[elem.questId];
        if (isFilteredByCountryCode(questCms.localBitflag)) {
            return;
        }
        if (questCms.isRoyalTitle) {
            if (!royalTitleOrderGroupCache[elem.nationId]) {
                royalTitleOrderGroupCache[elem.nationId] = [];
            }
            royalTitleOrderGroupCache[elem.nationId].push(elem);
        }
        else {
            if (!royalOrderGroupCache[elem.nationId]) {
                royalOrderGroupCache[elem.nationId] = [];
            }
            royalOrderGroupCache[elem.nationId].push(elem);
        }
    });
}
function getRoyalOrderGroup(nationCmsId) {
    buildRoyalOrderGroupCache();
    return royalOrderGroupCache[nationCmsId];
}
exports.getRoyalOrderGroup = getRoyalOrderGroup;
function getRoyalTitleOrderGroup(nationCmsId) {
    buildRoyalOrderGroupCache();
    return royalTitleOrderGroupCache[nationCmsId];
}
exports.getRoyalTitleOrderGroup = getRoyalTitleOrderGroup;
// ------------------------------------------------------------------------------------------------
function getTradeSpeciality(types) {
    for (let i = types; i > 0; i--) {
        if (_1.default.Const['TradeSpecialities' + i]) {
            return _1.default.Const['TradeSpecialities' + i].value;
        }
    }
    mlog_1.default.warn('can not find TradeSpecialities. types: ', { types });
    return 1;
}
exports.getTradeSpeciality = getTradeSpeciality;
// ------------------------------------------------------------------------------------------------
const regionTownCmsIdsCache = {};
function getRegionTownCmsIds(regionCmsId) {
    if (Object.keys(regionTownCmsIdsCache).length === 0) {
        for (const key of Object.keys(_1.default.Town)) {
            const townCms = _1.default.Town[key];
            if (!regionTownCmsIdsCache[townCms.RegionId]) {
                regionTownCmsIdsCache[townCms.RegionId] = [];
            }
            regionTownCmsIdsCache[townCms.RegionId].push(townCms.id);
        }
    }
    return regionTownCmsIdsCache[regionCmsId];
}
exports.getRegionTownCmsIds = getRegionTownCmsIds;
// ------------------------------------------------------------------------------------------------
let regionCmsIdsThatHasTown = [];
function getRegionCmsIdsThatHasTown() {
    if (regionCmsIdsThatHasTown.length === 0) {
        const cmsIdsSet = new Set();
        lodash_1.default.forOwn(_1.default.Town, (elem) => {
            cmsIdsSet.add(elem.RegionId);
        });
        regionCmsIdsThatHasTown = Array.from(cmsIdsSet);
    }
    return regionCmsIdsThatHasTown;
}
exports.getRegionCmsIdsThatHasTown = getRegionCmsIdsThatHasTown;
// ------------------------------------------------------------------------------------------------
const cultureTownCmsIdsCache = {};
function getCultureTownCmsIds(culturalAreaCmsId) {
    if (Object.keys(cultureTownCmsIdsCache).length === 0) {
        for (const key of Object.keys(_1.default.Town)) {
            const townCms = _1.default.Town[key];
            if (!cultureTownCmsIdsCache[townCms.CulturalAreaId]) {
                cultureTownCmsIdsCache[townCms.CulturalAreaId] = [];
            }
            cultureTownCmsIdsCache[townCms.CulturalAreaId].push(townCms.id);
        }
    }
    return cultureTownCmsIdsCache[culturalAreaCmsId];
}
exports.getCultureTownCmsIds = getCultureTownCmsIds;
// ------------------------------------------------------------------------------------------------
// 선박의 블루프린트 기반, 기본 내구도 캐시.
// 건조 랜덤 스탯 제외한 값만 캐시.
// [TODO] DefaultShip.json 으로 이런걸 만들어도 될듯?
const shipDefaultDurabilityCache = {};
// ShipBlueprint.json 의 데이터를 기반으로, 기본 내구도를 구한다.
function shipDefaultDurability(shipBlueprintCmsId, rndStats) {
    const shipBlueprintCms = _1.default.ShipBlueprint[shipBlueprintCmsId];
    if (shipDefaultDurabilityCache[shipBlueprintCmsId] === undefined) {
        let bpBaseDur = 0;
        for (const shipSlot of shipBlueprintCms.shipSlot) {
            const shipSlotCms = _1.default.ShipSlot[shipSlot.Id];
            if (!shipSlotCms.statEffect) {
                continue;
            }
            for (const s of shipSlotCms.statEffect) {
                if (s.Type !== STAT_EFFECT_TYPE.STAT_OPERATOR) {
                    continue;
                }
                if (s.Id !== STAT_TYPE.SHIP_MAX_DURABILITY) {
                    continue;
                }
                bpBaseDur += s.Value;
            }
        }
        shipDefaultDurabilityCache[shipBlueprintCmsId] = bpBaseDur;
    }
    let d = shipDefaultDurabilityCache[shipBlueprintCmsId];
    for (let i = 0; i < shipBlueprintCms.stat.length; i++) {
        const s = shipBlueprintCms.stat[i];
        if (s.Type === STAT_TYPE.SHIP_MAX_DURABILITY) {
            let v = s.Val;
            if (rndStats[i] !== undefined) {
                v = Math.ceil((v * rndStats[i]) / 1000);
            }
            d += v;
        }
    }
    return d;
}
exports.shipDefaultDurability = shipDefaultDurability;
// ------------------------------------------------------------------------------------------------
let shipDefaultLifeCache;
// ShipBlueprint.json 의 데이터를 기반으로, 기본 내구도를 구한다.
function shipDefaultLife(shipBlueprintCmsId) {
    if (!shipDefaultLifeCache) {
        shipDefaultLifeCache = {};
        lodash_1.default.forOwn(_1.default.ShipBlueprint, (elem) => {
            shipDefaultLifeCache[elem.id] = 0;
            for (const stat of elem.stat) {
                if (stat.Type === STAT_TYPE.SHIP_LIFE) {
                    shipDefaultLifeCache[elem.id] += stat.Val;
                }
            }
        });
    }
    return shipDefaultLifeCache[shipBlueprintCmsId];
}
exports.shipDefaultLife = shipDefaultLife;
// ------------------------------------------------------------------------------------------------
function statName(statType) {
    return STAT_TYPE[statType] || statType.toString();
}
exports.statName = statName;
// ------------------------------------------------------------------------------------------------
let taskCache;
function getTask(category) {
    if (!taskCache) {
        taskCache = {};
        const taskCms = _1.default.Task;
        lodash_1.default.forOwn(taskCms, (elem) => {
            if (!taskCache[elem.category]) {
                taskCache[elem.category] = [];
            }
            taskCache[elem.category].push(elem);
        });
    }
    return taskCache[category];
}
exports.getTask = getTask;
// ------------------------------------------------------------------------------------------------
// achievementTermsId 를 가지는 ContentsTerms 의 id를 반환.
// ContentsTerms 에서 같은 achievementTermsId 을 가지는 행은 없다.
const contentsTermsIdsCacheOfAchievementTerms = {};
function getContentsTermsIdsOfAchievementTerms(achievementTermsCmsId) {
    if (lodash_1.default.isEmpty(contentsTermsIdsCacheOfAchievementTerms)) {
        const contentsTermsCms = _1.default.ContentsTerms;
        lodash_1.default.forOwn(contentsTermsCms, (elem) => {
            if (elem.achievementTermsId) {
                contentsTermsIdsCacheOfAchievementTerms[elem.achievementTermsId] = elem.id;
            }
        });
    }
    return contentsTermsIdsCacheOfAchievementTerms[achievementTermsCmsId];
}
exports.getContentsTermsIdsOfAchievementTerms = getContentsTermsIdsOfAchievementTerms;
// ------------------------------------------------------------------------------------------------
const tradeGoodsByCategoryCache = {};
function getTradeGoodsByCategory(category) {
    if (tradeGoodsByCategoryCache[category]) {
        return tradeGoodsByCategoryCache[category];
    }
    lodash_1.default.forOwn(_1.default.TradeGoods, (elem) => {
        // if (isFilteredByCountryCode(elem)) {
        //   return;
        // }
        if (!tradeGoodsByCategoryCache[elem.tradeGoodsCategory]) {
            tradeGoodsByCategoryCache[elem.tradeGoodsCategory] = [];
        }
        tradeGoodsByCategoryCache[elem.tradeGoodsCategory].push(elem.id);
    });
    // 임시 코드.
    if (!tradeGoodsByCategoryCache[category]) {
        return [];
    }
    return tradeGoodsByCategoryCache[category];
}
exports.getTradeGoodsByCategory = getTradeGoodsByCategory;
// ------------------------------------------------------------------------------------------------
let sortedReputationCache = [];
function getSortedReputationArr() {
    if (sortedReputationCache.length === 0) {
        sortedReputationCache = lodash_1.default.values(_1.default.Reputation);
        sortedReputationCache.sort((a, b) => {
            return a.id - b.id;
        });
    }
    return sortedReputationCache;
}
exports.getSortedReputationArr = getSortedReputationArr;
// ------------------------------------------------------------------------------------------------
const townKickCache = {};
function getTownKick(type, target) {
    if (lodash_1.default.isEmpty(townKickCache)) {
        lodash_1.default.forOwn(_1.default.TownKick, (elem) => {
            if (!townKickCache[elem.kickSituationType]) {
                townKickCache[elem.kickSituationType] = {};
            }
            if (!townKickCache[elem.kickSituationType][elem.kickSituationVal]) {
                townKickCache[elem.kickSituationType][elem.kickSituationVal] = [];
            }
            townKickCache[elem.kickSituationType][elem.kickSituationVal].push(elem);
        });
        lodash_1.default.forOwn(townKickCache, (elem1) => {
            lodash_1.default.forOwn(elem1, (elem2) => {
                elem2.sort((a, b) => {
                    return a.kickResultType - b.kickResultType;
                });
            });
        });
    }
    return townKickCache[type][target];
}
exports.getTownKick = getTownKick;
// ------------------------------------------------------------------------------------------------
const recruitMateStatCache = {};
function getRecruitMateBuildingBuffStat(langLevel) {
    if (lodash_1.default.isEmpty(recruitMateStatCache)) {
        lodash_1.default.forOwn(_1.default.BuildingBuff, (elem) => {
            if (elem.statEffectId === STAT_TYPE.BUILDING_PUB_MATE_RECRUITING_PRICE_MODIFIER) {
                recruitMateStatCache[elem.contentsTermsCount] = elem.statEffectValue;
            }
        });
    }
    return recruitMateStatCache[langLevel];
}
exports.getRecruitMateBuildingBuffStat = getRecruitMateBuildingBuffStat;
// ------------------------------------------------------------------------------------------------
const drinkMateStatCache = {};
function getDrinkMateBuildingBuffStat(langLevel) {
    if (lodash_1.default.isEmpty(drinkMateStatCache)) {
        lodash_1.default.forOwn(_1.default.BuildingBuff, (elem) => {
            if (elem.statEffectId === STAT_TYPE.BUILDING_PUB_DRINK_PRICE_MODIFIER) {
                drinkMateStatCache[elem.contentsTermsCount] = elem.statEffectValue;
            }
        });
    }
    return drinkMateStatCache[langLevel];
}
exports.getDrinkMateBuildingBuffStat = getDrinkMateBuildingBuffStat;
// ------------------------------------------------------------------------------------------------
const mateHighestLanguageLevelsCache = {};
function getMateHighestLanguageLevel(mateCmsId) {
    if (mateHighestLanguageLevelsCache[mateCmsId] === undefined) {
        const mateCms = _1.default.Mate[mateCmsId];
        const langLvs = mateCms.language.map((elem) => elem.Lv);
        langLvs.sort((a, b) => {
            return b - a;
        });
        mateHighestLanguageLevelsCache[mateCmsId] = langLvs[0];
    }
    return mateHighestLanguageLevelsCache[mateCmsId];
}
exports.getMateHighestLanguageLevel = getMateHighestLanguageLevel;
// ------------------------------------------------------------------------------------------------
const achievementTargetsCache = {};
function getAchievementTermsTargets(achievementTermsCmsId, targetIdx) {
    if (lodash_1.default.isEmpty(achievementTargetsCache)) {
        lodash_1.default.forOwn(getAchievementCms(), (elem) => {
            if (!elem.achievementTarget) {
                return;
            }
            if (!achievementTargetsCache[elem.achievementTermsId]) {
                achievementTargetsCache[elem.achievementTermsId] = {};
            }
            for (let i = 0; i < elem.achievementTarget.length; i++) {
                if (!achievementTargetsCache[elem.achievementTermsId][i]) {
                    achievementTargetsCache[elem.achievementTermsId][i] = [];
                }
                achievementTargetsCache[elem.achievementTermsId][i].push(elem.achievementTarget[i]);
            }
        });
        lodash_1.default.forOwn(_1.default.ResearchTask, (elem) => {
            if (!elem.researchTaskTargets) {
                return;
            }
            if (!achievementTargetsCache[elem.researchTaskTermsId]) {
                achievementTargetsCache[elem.researchTaskTermsId] = {};
            }
            for (let i = 0; i < elem.researchTaskTargets.length; i++) {
                if (!achievementTargetsCache[elem.researchTaskTermsId][i]) {
                    achievementTargetsCache[elem.researchTaskTermsId][i] = [];
                }
                achievementTargetsCache[elem.researchTaskTermsId][i].push(elem.researchTaskTargets[i]);
            }
        });
        lodash_1.default.forOwn(achievementTargetsCache, (elem) => {
            lodash_1.default.forOwn(elem, (arr, key) => {
                elem[key] = lodash_1.default.uniq(elem[key]);
                elem[key].sort((a, b) => {
                    return a - b;
                });
            });
        });
    }
    if (!achievementTargetsCache[achievementTermsCmsId] ||
        !achievementTargetsCache[achievementTermsCmsId][targetIdx]) {
        return [];
    }
    return achievementTargetsCache[achievementTermsCmsId][targetIdx];
}
exports.getAchievementTermsTargets = getAchievementTermsTargets;
// ------------------------------------------------------------------------------------------------
let cashShopPreviousIdsCache = undefined;
function isCashShopPreviousId(cmsId) {
    if (!cashShopPreviousIdsCache) {
        cashShopPreviousIdsCache = new Set();
        lodash_1.default.forOwn(_1.default.CashShop, (elem) => {
            cashShopPreviousIdsCache.add(elem.previousId);
        });
    }
    return cashShopPreviousIdsCache.has(cmsId);
}
exports.isCashShopPreviousId = isCashShopPreviousId;
// ------------------------------------------------------------------------------------------------
// 인자로 주어진 cmsId 가 previousId 로 사용되는 지
let contributionShopPreviousIdsCache = undefined;
function isContributionShopPreviousId(cmsId) {
    if (!contributionShopPreviousIdsCache) {
        contributionShopPreviousIdsCache = new Set();
        lodash_1.default.forOwn(_1.default.ContributionShop, (elem) => {
            contributionShopPreviousIdsCache.add(elem.previousId);
        });
    }
    return contributionShopPreviousIdsCache.has(cmsId);
}
exports.isContributionShopPreviousId = isContributionShopPreviousId;
// ------------------------------------------------------------------------------------------------
let taxFreePermitCashShopCmsIds = undefined;
function getTaxFreePermitCashShopCmsId(cmsId) {
    if (!taxFreePermitCashShopCmsIds) {
        taxFreePermitCashShopCmsIds = {};
        lodash_1.default.forOwn(_1.default.CashShop, (elem) => {
            if (elem.taxFreePermitId) {
                taxFreePermitCashShopCmsIds[elem.taxFreePermitId] = elem.id;
            }
        });
    }
    return taxFreePermitCashShopCmsIds[cmsId];
}
exports.getTaxFreePermitCashShopCmsId = getTaxFreePermitCashShopCmsId;
// ------------------------------------------------------------------------------------------------
let cashShopBoxRatioCache = undefined;
function getCashShopBoxRatio(group) {
    if (!cashShopBoxRatioCache) {
        cashShopBoxRatioCache = {};
        lodash_1.default.forOwn(_1.default.CashShopBoxRatio, (elem) => {
            if (!cashShopBoxRatioCache[elem.boxGroup]) {
                cashShopBoxRatioCache[elem.boxGroup] = {
                    cmses: [],
                    totalRatio: 0,
                };
            }
            cashShopBoxRatioCache[elem.boxGroup].cmses.push(elem);
            cashShopBoxRatioCache[elem.boxGroup].totalRatio += elem.boxRatio;
        });
    }
    return cashShopBoxRatioCache[group];
}
exports.getCashShopBoxRatio = getCashShopBoxRatio;
// ------------------------------------------------------------------------------------------------
let limitSalesCache;
let fixedTermLimitSalesCache;
function getCashShopLimitSalesByPointType(salePointType, curTimeUtc) {
    var _a, _b, _c, _d;
    if (!limitSalesCache || !fixedTermLimitSalesCache) {
        limitSalesCache = {};
        fixedTermLimitSalesCache = {};
        lodash_1.default.forOwn(_1.default.CashShopLimitSale, (elem) => {
            if (elem.startDate || elem.endDate) {
                if (!fixedTermLimitSalesCache[elem.cashShopLimitSalePointType]) {
                    fixedTermLimitSalesCache[elem.cashShopLimitSalePointType] = [];
                }
                fixedTermLimitSalesCache[elem.cashShopLimitSalePointType].push(elem);
            }
            else {
                if (!limitSalesCache[elem.cashShopLimitSalePointType]) {
                    limitSalesCache[elem.cashShopLimitSalePointType] = {
                        cmses: [],
                        totalRatio: 0,
                    };
                }
                const ofType = limitSalesCache[elem.cashShopLimitSalePointType];
                ofType.cmses.push(elem);
                ofType.totalRatio += elem.cashShopLimitSaleRatio;
            }
        });
    }
    const curDate = new Date(curTimeUtc * 1000);
    const cmses = Array.from((_b = (_a = limitSalesCache[salePointType]) === null || _a === void 0 ? void 0 : _a.cmses) !== null && _b !== void 0 ? _b : []);
    let totalRatio = (_d = (_c = limitSalesCache[salePointType]) === null || _c === void 0 ? void 0 : _c.totalRatio) !== null && _d !== void 0 ? _d : 0;
    if (fixedTermLimitSalesCache[salePointType] &&
        fixedTermLimitSalesCache[salePointType].length > 0) {
        for (const elem of fixedTermLimitSalesCache[salePointType]) {
            if (elem.startDate && mutil.newDateByCmsDateStr(elem.startDate) > curDate) {
                continue;
            }
            if (elem.endDate && mutil.newDateByCmsDateStr(elem.endDate) < curDate) {
                continue;
            }
            cmses.push(elem);
            totalRatio += elem.cashShopLimitSaleRatio;
        }
    }
    return { cmses, totalRatio };
}
exports.getCashShopLimitSalesByPointType = getCashShopLimitSalesByPointType;
// ------------------------------------------------------------------------------------------------
let itemDiscoveryCmsIdsCache;
function getItemDiscoveryCmsIds(itemCmsId) {
    if (!itemDiscoveryCmsIdsCache) {
        itemDiscoveryCmsIdsCache = {};
        const discoveryCms = getDiscoveryCms();
        lodash_1.default.forOwn(discoveryCms, (elem) => {
            if (!elem.relateItem) {
                return;
            }
            for (const relateItem of elem.relateItem) {
                if (!itemDiscoveryCmsIdsCache[relateItem.Id]) {
                    itemDiscoveryCmsIdsCache[relateItem.Id] = [];
                }
                itemDiscoveryCmsIdsCache[relateItem.Id].push(elem.id);
            }
        });
    }
    return itemDiscoveryCmsIdsCache[itemCmsId];
}
exports.getItemDiscoveryCmsIds = getItemDiscoveryCmsIds;
// ------------------------------------------------------------------------------------------------
let sortedDiscoveryMissionByDifficultyCache;
function getSortedDiscoveryMissionByDifficulty(buildingNpcCmsId) {
    if (!sortedDiscoveryMissionByDifficultyCache) {
        // sortedDiscoveryMissionByDifficultyCache = _.values(cms.DiscoveryMission);
        // sortedDiscoveryMissionByDifficultyCache.sort((a, b) => {
        //   return b.missionDifficulty - a.missionDifficulty;
        // });
        sortedDiscoveryMissionByDifficultyCache = {};
        lodash_1.default.forOwn(_1.default.DiscoveryMission, (elem) => {
            if (!sortedDiscoveryMissionByDifficultyCache[elem.buildingNpc]) {
                sortedDiscoveryMissionByDifficultyCache[elem.buildingNpc] = [];
            }
            sortedDiscoveryMissionByDifficultyCache[elem.buildingNpc].push(elem);
        });
        lodash_1.default.forOwn(sortedDiscoveryMissionByDifficultyCache, (elem) => {
            elem.sort((a, b) => {
                return b.missionDifficulty - a.missionDifficulty;
            });
        });
    }
    return sortedDiscoveryMissionByDifficultyCache[buildingNpcCmsId];
}
exports.getSortedDiscoveryMissionByDifficulty = getSortedDiscoveryMissionByDifficulty;
// ------------------------------------------------------------------------------------------------
let questPassCashShopCmsIdsByQuestCmsIdCache;
function getQuestPassCashShopCmsIdsByQuestCmsId(targetQuestCmsId) {
    if (!questPassCashShopCmsIdsByQuestCmsIdCache) {
        questPassCashShopCmsIdsByQuestCmsIdCache = {};
        lodash_1.default.forOwn(_1.default.CashShop, (elem) => {
            if (elem.questPassId) {
                const questPassCms = _1.default.QuestPass[elem.questPassId];
                for (const questCmsId of questPassCms.questId) {
                    if (!questPassCashShopCmsIdsByQuestCmsIdCache[questCmsId]) {
                        questPassCashShopCmsIdsByQuestCmsIdCache[questCmsId] = [];
                    }
                    questPassCashShopCmsIdsByQuestCmsIdCache[questCmsId].push(elem.id);
                }
            }
        });
    }
    return questPassCashShopCmsIdsByQuestCmsIdCache[targetQuestCmsId];
}
exports.getQuestPassCashShopCmsIdsByQuestCmsId = getQuestPassCashShopCmsIdsByQuestCmsId;
// ------------------------------------------------------------------------------------------------
let questPassCashShopCmsIdByQuestPassCmsIdCache;
function getQuestPassCashShopCmsIdByQuestPassCmsId(questPassCmsId) {
    if (!questPassCashShopCmsIdByQuestPassCmsIdCache) {
        questPassCashShopCmsIdByQuestPassCmsIdCache = {};
        lodash_1.default.forOwn(_1.default.CashShop, (elem) => {
            if (elem.questPassId) {
                questPassCashShopCmsIdByQuestPassCmsIdCache[elem.questPassId] = elem.id;
            }
        });
    }
    return questPassCashShopCmsIdByQuestPassCmsIdCache[questPassCmsId];
}
exports.getQuestPassCashShopCmsIdByQuestPassCmsId = getQuestPassCashShopCmsIdByQuestPassCmsId;
// ------------------------------------------------------------------------------------------------
let cashShopCmsesByProductCodeCache;
function getCashShopCmsByProductCode(productCode) {
    if (!cashShopCmsesByProductCodeCache) {
        const cache = {};
        // 스토어 상품 코드는 유니크하다고 가정한 코드.
        const DoCache = (cashShopCms, cmsProductCode) => {
            if (cmsProductCode === undefined) {
                return;
            }
            if (!cache[cmsProductCode]) {
                cache[cmsProductCode] = [];
            }
            cache[cmsProductCode].push(cashShopCms);
        };
        lodash_1.default.forOwn(_1.default.CashShop, (elem) => {
            DoCache(elem, elem.productCodeGoogle);
            DoCache(elem, elem.productCodeApple);
            DoCache(elem, elem.productCodeFloor);
            DoCache(elem, elem.productCodeSteam);
            DoCache(elem, elem.consecutiveProductCodeGoogle);
            DoCache(elem, elem.consecutiveProductCodeApple);
            DoCache(elem, elem.consecutiveProductCodeFloor);
            DoCache(elem, elem.consecutiveProductCodeSteam);
        });
        cashShopCmsesByProductCodeCache = cache;
    }
    const cashShopCmses = cashShopCmsesByProductCodeCache[productCode] || [];
    if (cashShopCmses.length > 1) {
        // TODO Validation
        assert_1.default.fail(`CMS.CashShop.productCode duplicated. ${productCode}`);
    }
    return cashShopCmses[0];
}
exports.getCashShopCmsByProductCode = getCashShopCmsByProductCode;
// ------------------------------------------------------------------------------------------------
let selectableNationsCache;
function getSelectableNations() {
    if (!selectableNationsCache) {
        selectableNationsCache = [];
        lodash_1.default.forOwn(_1.default.Nation, (elem) => {
            if (elem.canSelect) {
                selectableNationsCache.push(elem);
            }
        });
    }
    return selectableNationsCache;
}
exports.getSelectableNations = getSelectableNations;
// ------------------------------------------------------------------------------------------------
let shopCmsIdsByGroupCache;
function getShopCmsIdsByGroup(group) {
    if (!shopCmsIdsByGroupCache) {
        shopCmsIdsByGroupCache = {};
        lodash_1.default.forOwn(_1.default.Shop, (elem) => {
            if (!shopCmsIdsByGroupCache[elem.group]) {
                shopCmsIdsByGroupCache[elem.group] = [];
            }
            shopCmsIdsByGroupCache[elem.group].push(elem.id);
        });
    }
    return shopCmsIdsByGroupCache[group];
}
exports.getShopCmsIdsByGroup = getShopCmsIdsByGroup;
// ------------------------------------------------------------------------------------------------
let regionItemsCache = {};
function pickRegionRandomItemReward(regionCmsId) {
    if (!regionItemsCache[regionCmsId]) {
        regionItemsCache[regionCmsId] = new Set();
        const townCmsIds = getRegionTownCmsIds(regionCmsId);
        if (townCmsIds) {
            for (const townCmsId of townCmsIds) {
                if (!_1.default.Town[townCmsId].shopGroup) {
                    continue;
                }
                for (const group of _1.default.Town[townCmsId].shopGroup) {
                    const shopCmsIds = getShopCmsIdsByGroup(group);
                    if (!shopCmsIds) {
                        continue;
                    }
                    for (const shopCmsId of shopCmsIds) {
                        regionItemsCache[regionCmsId].add(_1.default.Shop[shopCmsId].itemId);
                    }
                }
            }
        }
    }
    if (regionItemsCache[regionCmsId].size === 0) {
        return null;
    }
    const rnd = Math.floor(Math.random() * regionItemsCache[regionCmsId].size);
    return Array.from(regionItemsCache[regionCmsId])[rnd];
}
exports.pickRegionRandomItemReward = pickRegionRandomItemReward;
// ------------------------------------------------------------------------------------------------
let regionTradeGoodsCache = {};
function pickRegionRandomTradeGoodsReward(regionCmsId, curTimeUtc) {
    if (!regionTradeGoodsCache[regionCmsId]) {
        regionTradeGoodsCache[regionCmsId] = new Set();
        const townCmsIds = getRegionTownCmsIds(regionCmsId);
        if (townCmsIds) {
            for (const townCmsId of townCmsIds) {
                const tradeGoodsCmsIds = getTownSellingTradeGoodsCmsIds(townCmsId, curTimeUtc);
                for (const tradeGoodsCmsId of tradeGoodsCmsIds) {
                    regionTradeGoodsCache[regionCmsId].add(tradeGoodsCmsId);
                }
            }
        }
    }
    if (regionTradeGoodsCache[regionCmsId].size === 0) {
        return null;
    }
    const rnd = Math.floor(Math.random() * regionTradeGoodsCache[regionCmsId].size);
    return Array.from(regionTradeGoodsCache[regionCmsId])[rnd];
}
exports.pickRegionRandomTradeGoodsReward = pickRegionRandomTradeGoodsReward;
// ------------------------------------------------------------------------------------------------
function getShipCustomizingCmsName(cmsId) {
    if (_1.default.SailPattern[cmsId]) {
        return 'SailPattern';
    }
    if (_1.default.SailCrest[cmsId]) {
        return 'SailCrest';
    }
    if (_1.default.SailPatternColor[cmsId]) {
        return 'SailPatternColor';
    }
    if (_1.default.ShipBodyFirstColor[cmsId]) {
        return 'ShipBodyFirstColor';
    }
    if (_1.default.ShipBodySecondColor[cmsId]) {
        return 'ShipBodySecondColor';
    }
}
exports.getShipCustomizingCmsName = getShipCustomizingCmsName;
// ------------------------------------------------------------------------------------------------
let mateAwakenCache;
function getMateAwaken(mateGrade, jobType, awakenLv) {
    if (!mateAwakenCache) {
        const cache = {};
        lodash_1.default.forOwn(_1.default.MateAwaken, (elem) => {
            var _a, _b;
            const duplicated = (_b = (_a = cache[elem.mateGrade]) === null || _a === void 0 ? void 0 : _a[elem.jobType]) === null || _b === void 0 ? void 0 : _b[elem.awakenLv];
            (0, assert_1.default)(!duplicated);
            lodash_1.default.merge(cache, {
                [elem.mateGrade]: {
                    [elem.jobType]: {
                        [elem.awakenLv]: elem,
                    },
                },
            });
        });
        mateAwakenCache = cache;
    }
    return mateAwakenCache[mateGrade][jobType][awakenLv];
}
exports.getMateAwaken = getMateAwaken;
// ------------------------------------------------------------------------------------------------
let tradeByTownAndTradeGoodsCmsIdCache;
function getTradeByTownAndTradeGoodsCmsId(townCmsId, tradeGoodsCmsId) {
    if (!tradeByTownAndTradeGoodsCmsIdCache) {
        tradeByTownAndTradeGoodsCmsIdCache = {};
        lodash_1.default.forOwn(_1.default.Trade, (elem) => {
            const goodsCms = _1.default.TradeGoods[elem.tradeGoodsId];
            // if (isFilteredByCountryCode(goodsCms)) {
            //   return;
            // }
            if (!goodsCms) {
                return;
            }
            lodash_1.default.merge(tradeByTownAndTradeGoodsCmsIdCache, {
                [elem.townId]: {
                    [elem.tradeGoodsId]: elem,
                },
            });
        });
    }
    return tradeByTownAndTradeGoodsCmsIdCache[townCmsId]
        ? tradeByTownAndTradeGoodsCmsIdCache[townCmsId][tradeGoodsCmsId]
        : undefined;
}
exports.getTradeByTownAndTradeGoodsCmsId = getTradeByTownAndTradeGoodsCmsId;
// ------------------------------------------------------------------------------------------------
// 항해사의 패시브 습득 과정에서 사용되는 look-up 캐시 테이블.
// 클라 CMSEx.lua 와 동일한 유사한 로직 유지.
// ------------------------------------------------------------------------------------------------
let mateLearnablePassiveElemCache = {};
function _buildMateLearnablePassiveElemCache(mateCmsId) {
    const cachedTable = {};
    const mateCms = _1.default.Mate[mateCmsId];
    const autoMatePassiveCms = _1.default.AutoMatePassive[mateCms.autoMatePassiveId];
    const characterCms = _1.default.Character[mateCms.characterId];
    const jobCms = _1.default.Job[characterCms.jobId];
    const autoJobPassiveCms = _1.default.AutoJobPassive[jobCms.autoJobPassiveId];
    for (const elem of autoMatePassiveCms.passives) {
        if (cachedTable[elem.id]) {
            mlog_1.default.error('Duplicate auto mate passive!', {
                mateCmsId,
                passiveCmsId: elem.id,
            });
            continue;
        }
        cachedTable[elem.id] = elem;
    }
    for (const elem of autoJobPassiveCms.passives) {
        if (cachedTable[elem.id]) {
            mlog_1.default.error('Duplicate auto job passive!', {
                mateCmsId,
                passiveCmsId: elem.id,
            });
            continue;
        }
        cachedTable[elem.id] = elem;
    }
    mateLearnablePassiveElemCache[mateCmsId] = cachedTable;
    return cachedTable;
}
function getMateLearnablePassiveElemTable(mateCmsId) {
    let cachedTable = mateLearnablePassiveElemCache[mateCmsId];
    if (!cachedTable) {
        cachedTable = _buildMateLearnablePassiveElemCache(mateCmsId);
    }
    return cachedTable;
}
exports.getMateLearnablePassiveElemTable = getMateLearnablePassiveElemTable;
function getMateLearnablePassiveElem(mateCmsId, passiveCmsId) {
    let cachedTable = mateLearnablePassiveElemCache[mateCmsId];
    if (!cachedTable) {
        cachedTable = _buildMateLearnablePassiveElemCache(mateCmsId);
    }
    return cachedTable[passiveCmsId];
}
exports.getMateLearnablePassiveElem = getMateLearnablePassiveElem;
// ------------------------------------------------------------------------------------------------
let autoMateOrderCache;
function getAutoMateOrderElem(autoMateOrderCmsId, orderCmsId) {
    if (!autoMateOrderCache) {
        autoMateOrderCache = {};
        lodash_1.default.forOwn(_1.default.AutoMateOrder, (elem1) => {
            for (const elem2 of elem1.order) {
                lodash_1.default.merge(autoMateOrderCache, {
                    [elem1.id]: {
                        [elem2.id]: elem2,
                    },
                });
            }
        });
    }
    return autoMateOrderCache[autoMateOrderCmsId]
        ? autoMateOrderCache[autoMateOrderCmsId][orderCmsId]
        : undefined;
}
exports.getAutoMateOrderElem = getAutoMateOrderElem;
// ------------------------------------------------------------------------------------------------
let attendanceCache;
function getAttendance(group) {
    if (!attendanceCache) {
        attendanceCache = {};
        lodash_1.default.forOwn(_1.default.Attendance, (elem) => {
            lodash_1.default.merge(attendanceCache, {
                [elem.group]: {
                    [elem.type]: {
                        [elem.day]: elem,
                    },
                },
            });
        });
    }
    return attendanceCache[group];
}
exports.getAttendance = getAttendance;
let attendanceLastDayCache = {};
function getAttendanceLastDay(group) {
    if (!attendanceLastDayCache) {
        attendanceLastDayCache = {};
    }
    lodash_1.default.forOwn(_1.default.Attendance, (elem) => {
        if (!attendanceLastDayCache[elem.group]) {
            attendanceLastDayCache[elem.group] = elem.day;
        }
        if (elem.day > attendanceLastDayCache[elem.group]) {
            attendanceLastDayCache[elem.group] = elem.day;
        }
    });
    return attendanceLastDayCache[group];
}
exports.getAttendanceLastDay = getAttendanceLastDay;
// ------------------------------------------------------------------------------------------------
let revivalUserAttendanceCache;
function getRevivalUserAttendance() {
    if (!revivalUserAttendanceCache) {
        revivalUserAttendanceCache = [];
        lodash_1.default.forOwn(_1.default.EventPage, (elem) => {
            if (isFilteredByCountryCode(elem.localBitflag)) {
                return;
            }
            if (elem.type === eventPageDesc_1.EventPageType.ATTENDANCE_COMBACK && elem.activeDay && elem.comeBackDay) {
                revivalUserAttendanceCache.push(elem);
            }
        });
    }
    return revivalUserAttendanceCache;
}
exports.getRevivalUserAttendance = getRevivalUserAttendance;
// ------------------------------------------------------------------------------------------------
let newUserAttendanceCache;
function getNewUserAttendance() {
    if (!newUserAttendanceCache) {
        newUserAttendanceCache = [];
        lodash_1.default.forOwn(_1.default.EventPage, (elem) => {
            if (isFilteredByCountryCode(elem.localBitflag)) {
                return;
            }
            if (elem.type === eventPageDesc_1.EventPageType.ATTENDANCE_NEW && elem.startDate) {
                newUserAttendanceCache.push(elem);
            }
        });
    }
    return newUserAttendanceCache;
}
exports.getNewUserAttendance = getNewUserAttendance;
// ------------------------------------------------------------------------------------------------
let eventPageCacheForPageType = {};
function getEventPagesForPageType(type) {
    if (eventPageCacheForPageType[type]) {
        return eventPageCacheForPageType[type];
    }
    eventPageCacheForPageType[type] = [];
    lodash_1.default.forOwn(_1.default.EventPage, (evtPage) => {
        if (evtPage.type === type) {
            eventPageCacheForPageType[type].push(evtPage);
        }
    });
    return eventPageCacheForPageType[type];
}
exports.getEventPagesForPageType = getEventPagesForPageType;
// 제독의 mateSet에 해당되는 mate cms ids
let mateCmsIdsOfAdmiralMateSetCache;
function getMateCmsIdsOfAdmiralMateSet(mateCmsId) {
    if (!mateCmsIdsOfAdmiralMateSetCache) {
        mateCmsIdsOfAdmiralMateSetCache = {};
        lodash_1.default.forOwn(_1.default.Mate, (elem) => {
            if (!elem.commanderLink) {
                return;
            }
            if (!mateCmsIdsOfAdmiralMateSetCache[elem.commanderLink]) {
                mateCmsIdsOfAdmiralMateSetCache[elem.commanderLink] = [];
            }
            mateCmsIdsOfAdmiralMateSetCache[elem.commanderLink].push(elem.id);
        });
    }
    return mateCmsIdsOfAdmiralMateSetCache[mateCmsId]
        ? mateCmsIdsOfAdmiralMateSetCache[mateCmsId]
        : [];
}
exports.getMateCmsIdsOfAdmiralMateSet = getMateCmsIdsOfAdmiralMateSet;
// ------------------------------------------------------------------------------------------------
let eventPageCache = {};
function getEventMissionCmses(groupId) {
    if (eventPageCache[groupId]) {
        return eventPageCache[groupId];
    }
    eventPageCache[groupId] = [];
    lodash_1.default.forOwn(_1.default.EventMission, (evtMission) => {
        if (evtMission.group === groupId) {
            eventPageCache[groupId].push(evtMission);
        }
    });
    return eventPageCache[groupId];
}
exports.getEventMissionCmses = getEventMissionCmses;
// ------------------------------------------------------------------------------------------------
function getEventMissionCount(groupId) {
    const eventMissionCmses = getEventMissionCmses(groupId);
    if (eventMissionCmses) {
        return eventMissionCmses.length;
    }
    return 0;
}
exports.getEventMissionCount = getEventMissionCount;
// ------------------------------------------------------------------------------------------------
// 이벤트 기간 체크
function isEventPageExpired(curTimeUtc, eventPageCms) {
    const curDate = new Date(curTimeUtc * 1000);
    const startDate = mutil.newDateByCmsDateStr(eventPageCms.startDate);
    const endDate = mutil.newDateByCmsDateStr(eventPageCms.endDate);
    if (startDate && startDate > curDate) {
        return true;
    }
    if (endDate && endDate < curDate) {
        return true;
    }
    return false;
}
exports.isEventPageExpired = isEventPageExpired;
// ------------------------------------------------------------------------------------------------
let defaultMateEquipmentColorsCache;
function getDefaultMateEquipmentColors() {
    if (!defaultMateEquipmentColorsCache) {
        defaultMateEquipmentColorsCache = {};
        lodash_1.default.forOwn(_1.default.DefaultEquipDyeColor, (elem) => {
            const id = elem.defaultEquipDyeColorId;
            const offset = Math.floor(id / 32);
            if (!defaultMateEquipmentColorsCache[offset]) {
                defaultMateEquipmentColorsCache[offset] = 0;
            }
            defaultMateEquipmentColorsCache[offset] =
                (defaultMateEquipmentColorsCache[offset] | (1 << id % 32)) >>> 0;
        });
    }
    return defaultMateEquipmentColorsCache;
}
exports.getDefaultMateEquipmentColors = getDefaultMateEquipmentColors;
// ------------------------------------------------------------------------------------------------
const equipDyeRandomColorCache = [];
function getRandomEquipDyeColors(cEquipCms, charGender) {
    if (equipDyeRandomColorCache.length === 0) {
        lodash_1.default.forOwn(_1.default.EquipDyeColor, (elem) => {
            if (elem.isView) {
                equipDyeRandomColorCache.push(elem.color);
            }
        });
    }
    let equipCms;
    let randomColors = [];
    // itemShapedsId 인덱스 참조 룰
    // cEquipCms.gender 가 Man, Woman 경우 : 0을 index로 사용 (default)
    // cEquipCms.gender 가 Common일 경우 : characterCms.gender를 index로 사용
    let itemShapedsIdIndex = 0;
    if (cEquipCms.gender === GENDER.COMMON) {
        itemShapedsIdIndex = charGender;
    }
    // emptyColor => 사용하지 않은 염색값
    const emptyColor = 0xffffff;
    switch (cEquipCms.type) {
        case cEquipDesc_1.CEQUIP_TYPE.BODY:
            equipCms = _1.default.BodyShape[cEquipCms.itemShapedsId[itemShapedsIdIndex]];
            randomColors.push(equipCms.color1 === emptyColor ? null : lodash_1.default.sample(equipDyeRandomColorCache));
            randomColors.push(equipCms.color2 === emptyColor ? null : lodash_1.default.sample(equipDyeRandomColorCache));
            break;
        case cEquipDesc_1.CEQUIP_TYPE.HAT:
            equipCms = _1.default.HatShape[cEquipCms.itemShapedsId[itemShapedsIdIndex]];
            randomColors.push(equipCms.color1 === emptyColor ? null : lodash_1.default.sample(equipDyeRandomColorCache));
            randomColors.push(equipCms.color2 === emptyColor ? null : lodash_1.default.sample(equipDyeRandomColorCache));
            break;
    }
    return randomColors;
}
exports.getRandomEquipDyeColors = getRandomEquipDyeColors;
// ------------------------------------------------------------------------------------------------
function convertRewardFixedToGLogRewardData(rewardFixedCmsId) {
    const ret = [];
    const rewardCms = _1.default.RewardFixed[rewardFixedCmsId];
    if (!rewardCms || rewardCms.rewardFixed.length === 0) {
        return ret;
    }
    for (let idx = 0; idx < rewardCms.rewardFixed.length; idx++) {
        const elem = rewardCms.rewardFixed[idx];
        ret.push({
            type: rewardDesc_1.REWARD_TYPE[elem.Type],
            id: elem.Id === undefined ? null : elem.Id,
            uid: null,
            amt: elem.Quantity,
        });
    }
    return ret;
}
exports.convertRewardFixedToGLogRewardData = convertRewardFixedToGLogRewardData;
// ------------------------------------------------------------------------------------------------
// WorldPassive/BattlePassive 의 그룹/스킬레벨 look-up 테이블.
let passiveByGroupAndLevel;
// WorldPassive/BattlePassive 그룹/스킬레벨 look-up 테이블 구성.
function _buildPassiveByGroupAndLevel() {
    passiveByGroupAndLevel = {
        world: {},
        battle: {},
    };
    const worldPassiveByGroupAndLevel = passiveByGroupAndLevel.world;
    lodash_1.default.forOwn(_1.default.WorldPassive, (worldPassiveCms) => {
        const groupNo = worldPassiveCms.groupNo;
        const skillLevel = worldPassiveCms.skillLevel;
        if (!worldPassiveByGroupAndLevel[groupNo]) {
            const targetType = worldPassiveCms.worldTargetType;
            (0, assert_1.default)(targetType === WorldTargetType.FLEET || targetType === WorldTargetType.SHIP);
            worldPassiveByGroupAndLevel[groupNo] = {};
        }
        worldPassiveByGroupAndLevel[groupNo][skillLevel] = worldPassiveCms;
    });
    const battlePassiveByGroupAndLevel = passiveByGroupAndLevel.battle;
    lodash_1.default.forOwn(_1.default.BattlePassive, (battlePassiveCms) => {
        const groupNo = battlePassiveCms.groupNo;
        const skillLevel = battlePassiveCms.skillLevel;
        if (!battlePassiveByGroupAndLevel[groupNo]) {
            battlePassiveByGroupAndLevel[groupNo] = {};
        }
        battlePassiveByGroupAndLevel[groupNo][skillLevel] = battlePassiveCms;
    });
}
// ------------------------------------------------------------------------------------------------
// WorldPassive/BattlePassive 를 그룹/스킬레벨로 가져오기.
function getPassiveByGroupAndLevel(cmsId, groupNo, skillLevel) {
    if (!passiveByGroupAndLevel) {
        _buildPassiveByGroupAndLevel();
    }
    let passiveDesc;
    let passiveDescByGroup;
    if (_1.default.WorldPassive[cmsId]) {
        // 월드 패시브
        passiveDescByGroup = passiveByGroupAndLevel.world[groupNo];
    }
    else {
        // 전투 패시브
        passiveDescByGroup = passiveByGroupAndLevel.battle[groupNo];
    }
    passiveDesc = passiveDescByGroup[skillLevel];
    if (!passiveDesc) {
        mlog_1.default.debug('No passive by group/level. So find max possible skill level. ', {
            cmsId,
            groupNo,
            skillLevel,
        });
        // 해당 스킬레벨이 없는 경우, "최대로 가능한" 패시브를 반환.
        const maxShipSkillLevel = CMSConst.get('MaxShipSkillLevel');
        for (let i = 1; i <= maxShipSkillLevel; ++i) {
            if (!passiveDescByGroup[i]) {
                break;
            }
            passiveDesc = passiveDescByGroup[i];
        }
    }
    if (!passiveDesc) {
        mlog_1.default.error('No passive by group/level.', {
            cmsId,
            groupNo,
            skillLevel,
        });
    }
    return passiveDesc;
}
exports.getPassiveByGroupAndLevel = getPassiveByGroupAndLevel;
// ------------------------------------------------------------------------------------------------
function getPassiveTargetTypeByGroupId(groupNo) {
    if (!passiveByGroupAndLevel) {
        _buildPassiveByGroupAndLevel();
    }
    // worldPassive, battlePassive 간에 groupNo는 안겹친다고 전달 받음
    const passives = passiveByGroupAndLevel.world[groupNo]
        ? passiveByGroupAndLevel.world[groupNo]
        : passiveByGroupAndLevel.battle[groupNo];
    (0, assert_1.default)(passives);
    const arrPassives = Object.values(passives);
    (0, assert_1.default)(arrPassives.length > 0);
    const pci = getPassiveCommonInfo(arrPassives[0].id);
    return pci.targetType;
}
exports.getPassiveTargetTypeByGroupId = getPassiveTargetTypeByGroupId;
// ------------------------------------------------------------------------------------------------
// WorldPassive/BattlePassive 의 스킬/그룹 가져오기.
function getPassiveCommonInfo(cmsId) {
    const worldPassiveCms = _1.default.WorldPassive[cmsId];
    if (worldPassiveCms) {
        return {
            cmsId,
            groupNo: worldPassiveCms.groupNo,
            skillLevel: worldPassiveCms.skillLevel,
            targetType: worldPassiveCms.worldTargetType,
        };
    }
    const battlePassiveCms = _1.default.BattlePassive[cmsId];
    if (battlePassiveCms) {
        return {
            cmsId,
            groupNo: battlePassiveCms.groupNo,
            skillLevel: battlePassiveCms.skillLevel,
            targetType: WorldTargetType.SHIP,
        };
    }
    mlog_1.default.error('Invalid passive CMS ID.', { cmsId });
    return undefined;
}
exports.getPassiveCommonInfo = getPassiveCommonInfo;
// ------------------------------------------------------------------------------------------------
let shipBlueprintSlotByIndexCache;
function getShipBlueprintSlotByIndex(shipBlueprintCmsId, index) {
    if (!shipBlueprintSlotByIndexCache) {
        shipBlueprintSlotByIndexCache = {};
        lodash_1.default.forOwn(_1.default.ShipBlueprint, (shipBlueprintCms) => {
            for (const slotCms of shipBlueprintCms.shipSlot) {
                lodash_1.default.merge(shipBlueprintSlotByIndexCache, {
                    [shipBlueprintCms.id]: {
                        [slotCms.Index]: slotCms,
                    },
                });
            }
        });
    }
    return shipBlueprintSlotByIndexCache[shipBlueprintCmsId]
        ? shipBlueprintSlotByIndexCache[shipBlueprintCmsId][index]
        : undefined;
}
exports.getShipBlueprintSlotByIndex = getShipBlueprintSlotByIndex;
// ------------------------------------------------------------------------------------------------
let shipEnchantCache;
function getShipEnchant(shipTier) {
    if (!shipEnchantCache) {
        shipEnchantCache = {};
        lodash_1.default.forOwn(_1.default.ShipEnchant, (elem) => {
            shipEnchantCache[elem.shipTier] = elem;
        });
    }
    return shipEnchantCache[shipTier];
}
exports.getShipEnchant = getShipEnchant;
let shipEnchantStatRatioExtensions;
function getShipEnchantStatRatioEx(shipTier) {
    if (!shipEnchantStatRatioExtensions) {
        shipEnchantStatRatioExtensions = {};
        lodash_1.default.forOwn(_1.default.ShipEnchantStatRatio, (shipEnchantStatRatioCms) => {
            if (!shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier]) {
                shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier] = {
                    shipEnchantStatRatio: [],
                    totalRatio: 0,
                    totalRatioOfElem: [],
                };
            }
            shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier].shipEnchantStatRatio.push(shipEnchantStatRatioCms);
            shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier].totalRatio +=
                shipEnchantStatRatioCms.enchantStatOperatorRatio;
        });
        lodash_1.default.forOwn(shipEnchantStatRatioExtensions, (elem1) => {
            for (const elem2 of elem1.shipEnchantStatRatio) {
                let sumRatio = 0;
                for (const elem3 of elem2.enchantVal) {
                    sumRatio += elem3.Ratio;
                }
                elem1.totalRatioOfElem.push(sumRatio);
            }
        });
    }
    return shipEnchantStatRatioExtensions[shipTier];
}
exports.getShipEnchantStatRatioEx = getShipEnchantStatRatioEx;
// ------------------------------------------------------------------------------------------------
const townShopCmsIdsCache = {};
function getTownShopCmsIds(townCmsId) {
    if (!townShopCmsIdsCache[townCmsId]) {
        const townCms = _1.default.Town[townCmsId];
        if (!townCms) {
            return undefined;
        }
        const groups = townCms.shopGroup;
        if (!groups) {
            return undefined;
        }
        townShopCmsIdsCache[townCmsId] = new Set();
        for (const group of groups) {
            const cmsIds = getShopCmsIdsByGroup(group);
            for (const cmsId of cmsIds) {
                townShopCmsIdsCache[townCmsId].add(cmsId);
            }
        }
    }
    return townShopCmsIdsCache[townCmsId];
}
exports.getTownShopCmsIds = getTownShopCmsIds;
// ------------------------------------------------------------------------------------------------
let hotTimeBonusCache;
function getCurHotTimeBonus(curTimeUtc) {
    if (!hotTimeBonusCache) {
        hotTimeBonusCache = {};
        lodash_1.default.forOwn(_1.default.HotTimeBonus, (elem) => {
            if (!hotTimeBonusCache[elem.group]) {
                hotTimeBonusCache[elem.group] = [];
            }
            hotTimeBonusCache[elem.group].push(elem);
        });
        lodash_1.default.forOwn(hotTimeBonusCache, (elem) => {
            elem.sort((a, b) => {
                return a.startHour - b.startHour;
            });
        });
    }
    for (const elem of Object.values(hotTimeBonusCache)) {
        const startTime = mutil.newDateByCmsDateStr(elem[0].eventStartDate).getTime() / 1000;
        const endTime = mutil.newDateByCmsDateStr(elem[0].eventEndDate).getTime() / 1000;
        if (curTimeUtc >= startTime && curTimeUtc <= endTime + _1.default.Const.OverHotTime.value) {
            return elem;
        }
    }
    return undefined;
}
exports.getCurHotTimeBonus = getCurHotTimeBonus;
// ------------------------------------------------------------------------------------------------
function convertRewardFixedToCustomAttachmentStr(rewardFixedCmsId, bIsAccum, curTimeUtc) {
    const rewardFixedCms = _1.default.RewardFixed[rewardFixedCmsId];
    if (!rewardFixedCms || !rewardFixedCms.rewardFixed || rewardFixedCms.rewardFixed.length === 0) {
        return null;
    }
    return convertRewardFixedElemsToCustomAttachmentStr(rewardFixedCms.rewardFixed, bIsAccum, curTimeUtc);
}
exports.convertRewardFixedToCustomAttachmentStr = convertRewardFixedToCustomAttachmentStr;
// ------------------------------------------------------------------------------------------------
function convertRewardFixedElemsToCustomAttachmentStr(rewardFixedElems, bIsAccum, curTimeUtc) {
    const customAttachment = [];
    lodash_1.default.forEach(rewardFixedElems, (elem) => {
        const extra = { isAccum: bIsAccum ? 1 : 0, isBound: 1 };
        if (elem.Type === rewardDesc_1.REWARD_TYPE.MATE_EQUIP) {
            const cEquipCms = _1.default.CEquip[elem.Id];
            extra.expireTimeUtc =
                cEquipCms.expireType === EXPIRE_TYPE.PROVIDE
                    ? curTimeUtc + cEquipCms.expireTime
                    : undefined;
        }
        else if (elem.Type === rewardDesc_1.REWARD_TYPE.SHIP_SLOT_ITEM) {
            const shipSlotItemCms = _1.default.ShipSlot[elem.Id];
            extra.expireTimeUtc =
                shipSlotItemCms.expireType === EXPIRE_TYPE.PROVIDE
                    ? curTimeUtc + shipSlotItemCms.expireTime
                    : undefined;
        }
        customAttachment.push({
            Id: elem.Id,
            Type: elem.Type,
            Quantity: elem.Quantity,
            Extra: JSON.stringify(extra),
        });
    });
    return JSON.stringify(customAttachment);
}
exports.convertRewardFixedElemsToCustomAttachmentStr = convertRewardFixedElemsToCustomAttachmentStr;
// ------------------------------------------------------------------------------------------------
let tradeGoodsMarketPriceVolumeBasesCache;
function getTradeGoodsMarketPriceVolumeBases() {
    if (!tradeGoodsMarketPriceVolumeBasesCache) {
        tradeGoodsMarketPriceVolumeBasesCache = {};
        // Note: 이 함수는 모든 TradeGoods를 포함해야 하므로 필터링하지 않습니다
        lodash_1.default.forOwn(_1.default.TradeGoods, (elem) => {
            tradeGoodsMarketPriceVolumeBasesCache[elem.id] = elem.marketPriceVolumeBase;
        });
    }
    return tradeGoodsMarketPriceVolumeBasesCache;
}
exports.getTradeGoodsMarketPriceVolumeBases = getTradeGoodsMarketPriceVolumeBases;
// ------------------------------------------------------------------------------------------------
let tradeGoodsPricePercentClampCache;
function getTradeGoodsPricePercentClamps() {
    if (!tradeGoodsPricePercentClampCache) {
        tradeGoodsPricePercentClampCache = {};
        // Note: 이 함수는 모든 TradeGoods를 포함해야 하므로 필터링하지 않습니다
        lodash_1.default.forOwn(_1.default.TradeGoods, (elem) => {
            tradeGoodsPricePercentClampCache[elem.id] = [
                elem.marketPriceBottomPercent,
                elem.marketPriceTopPercent,
            ];
        });
    }
    return tradeGoodsPricePercentClampCache;
}
exports.getTradeGoodsPricePercentClamps = getTradeGoodsPricePercentClamps;
// ------------------------------------------------------------------------------------------------
let defaultNationPowerCache;
function getDefaultNationPower(nationCmsId) {
    const targetNationCms = _1.default.Nation[nationCmsId];
    if (!targetNationCms || !targetNationCms.canSelect) {
        return 0;
    }
    if (!defaultNationPowerCache) {
        defaultNationPowerCache = {};
        const developmentPerNationPower = _1.default.Const.DevelopmentPerNationPower.value;
        const townNations = {};
        const townDevelopments = {};
        lodash_1.default.forOwn(_1.default.Town, (townCms) => {
            const townNationCms = _1.default.Nation[nationCmsId];
            if (!townNationCms.canSelect) {
                return;
            }
            townNations[townCms.id] = townCms.nationId;
            // 각 마을 기본 발전도는 1이라 가정한다. 이 함수는 디비 리셋 후 발전도 갱신이 아직 되지 않은 시점에만 사용되기 때문에
            townDevelopments[townCms.id] = 3;
        });
        const nationTowns = lodash_1.default.groupBy(Object.keys(townNations), (townKey) => townNations[townKey]);
        lodash_1.default.forOwn(_1.default.Nation, (nationCms) => {
            if (!nationCms.canSelect) {
                return;
            }
            if (!nationTowns[nationCms.id]) {
                defaultNationPowerCache[nationCms.id] = 0;
            }
            else {
                const numOfTown = nationTowns[nationCms.id].length;
                const developments = lodash_1.default.sumBy(nationTowns[nationCms.id].map((townKey) => townDevelopments[townKey]));
                defaultNationPowerCache[nationCms.id] =
                    numOfTown + Math.floor(developments / developmentPerNationPower);
            }
        });
    }
    return defaultNationPowerCache[nationCmsId] ? defaultNationPowerCache[nationCmsId] : 0;
}
exports.getDefaultNationPower = getDefaultNationPower;
// --------------------------------------------------------------------------
// 인연 연대기 퀘스트별 태생 효과가 변경되는 항해사 look-up.
// --------------------------------------------------------------------------
let mateCmsIdsByFixedPassiveQuestId;
function getMateCmsIdsByAutoFixedPassiveClearQuestId(questCmsId) {
    // 캐시가 없으면 만든다.
    if (!mateCmsIdsByFixedPassiveQuestId) {
        mateCmsIdsByFixedPassiveQuestId = {};
        lodash_1.default.forOwn(_1.default.Mate, (mateCms) => {
            const autoMatePassivedFixedCms = _1.default.AutoMatePassiveFixed[mateCms.autoMatePassiveFixedId];
            if (!autoMatePassivedFixedCms) {
                return;
            }
            if (!autoMatePassivedFixedCms.clearQuestId) {
                return;
            }
            let mateCmsIds = mateCmsIdsByFixedPassiveQuestId[autoMatePassivedFixedCms.clearQuestId];
            if (!mateCmsIds) {
                mateCmsIds = [];
                mateCmsIdsByFixedPassiveQuestId[autoMatePassivedFixedCms.clearQuestId] = mateCmsIds;
            }
            mateCmsIds.push(mateCms.id);
        });
    }
    return mateCmsIdsByFixedPassiveQuestId[questCmsId];
}
exports.getMateCmsIdsByAutoFixedPassiveClearQuestId = getMateCmsIdsByAutoFixedPassiveClearQuestId;
// --------------------------------------------------------------------------
// 인연 연대기 퀘스트별 인물, 직업 효과가 변경되는 항해사 look-up.
// --------------------------------------------------------------------------
let mateCmsIdsByAutoPassiveClearQuestId;
function getMateCmsIdsByAutoPassiveClearQuestId(questCmsId) {
    // 캐시가 없으면 만든다.
    if (!mateCmsIdsByAutoPassiveClearQuestId) {
        mateCmsIdsByAutoPassiveClearQuestId = {};
        lodash_1.default.forOwn(_1.default.Mate, (mateCms) => {
            const autoMatePassiveCms = _1.default.AutoMatePassive[mateCms.autoMatePassiveId];
            if (!autoMatePassiveCms) {
                return;
            }
            if (!autoMatePassiveCms.clearQuestId) {
                return;
            }
            let mateCmsIds = mateCmsIdsByAutoPassiveClearQuestId[autoMatePassiveCms.clearQuestId];
            if (!mateCmsIds) {
                mateCmsIds = [];
                mateCmsIdsByAutoPassiveClearQuestId[autoMatePassiveCms.clearQuestId] = mateCmsIds;
            }
            mateCmsIds.push(mateCms.id);
        });
    }
    return mateCmsIdsByAutoPassiveClearQuestId[questCmsId];
}
exports.getMateCmsIdsByAutoPassiveClearQuestId = getMateCmsIdsByAutoPassiveClearQuestId;
// --------------------------------------------------------------------------
// CMS.RewardDropPool, groupId별로 분리
// --------------------------------------------------------------------------
let groupedRewardDropPoolCache;
let defaultGroupedRewardDropPoolCache;
/**
 * https://wiki.line.games/pages/viewpage.action?pageId=96784541 적용되어 있음
 * RewardAndPaymentSpec::pickFromRewardCms 이외의 곳에서 사용 시 주의 바람
 * @param rewardGroupId CMS.Reward.reward.GroupId
 * @returns
 * list: group에 속한 CMS.RewardDropPool 목록
 * sumWeight: group에 속한 CMS.RewardDropPool.rewardWeight 총합, //*디버그 용도
 */
function getRewardDropPoolDataInGroup(rewardGroupId) {
    if (!groupedRewardDropPoolCache) {
        groupedRewardDropPoolCache = {};
        lodash_1.default.forOwn(_1.default.RewardDropPool, (elem) => {
            const r = elem.rewardElem;
            if (r.Type === rewardDesc_1.REWARD_TYPE.SHIP) {
                const shipCms = _1.default.Ship[r.Id];
                if (!shipCms) {
                    return;
                }
                if (isFilteredByCountryCode(shipCms.localBitflag)) {
                    return;
                }
            }
            else if (r.Type === rewardDesc_1.REWARD_TYPE.ITEM) {
                const itemCms = _1.default.Item[r.Id];
                if (!itemCms) {
                    return;
                }
                if (itemCms.type === itemDesc_1.ITEM_TYPE.BLUEPRINT) {
                    // 공용도면은 선박이 될수 없어 검사를 안한다
                    const bpCms = getShipBlueprintCmsFromMaterialBpId(r.Id);
                    if (bpCms) {
                        const shipCms = _1.default.Ship[bpCms.shipId];
                        if (shipCms && isFilteredByCountryCode(shipCms.localBitflag)) {
                            return;
                        }
                    }
                }
                else if (itemCms.type === itemDesc_1.ITEM_TYPE.PIECE) {
                    const mateCms = getMateCmsFromAwakenPieceItem(r.Id);
                    if (mateCms && isFilteredByCountryCode(mateCms.localBitFlag)) {
                        return;
                    }
                }
            }
            if (!groupedRewardDropPoolCache[elem.groupId]) {
                groupedRewardDropPoolCache[elem.groupId] = {
                    list: [elem],
                    sumWeight: elem.rewardWeight,
                };
            }
            else {
                groupedRewardDropPoolCache[elem.groupId].list.push(elem);
                groupedRewardDropPoolCache[elem.groupId].sumWeight += elem.rewardWeight;
            }
        });
    }
    if (!groupedRewardDropPoolCache[rewardGroupId]) {
        if (!defaultGroupedRewardDropPoolCache) {
            const defaultRewardDropPool = _1.default.RewardDropPool[_1.default.Const.DefaultRewardDropPool.value];
            defaultGroupedRewardDropPoolCache = {
                list: [defaultRewardDropPool],
                sumWeight: defaultRewardDropPool.rewardWeight,
            };
        }
        return defaultGroupedRewardDropPoolCache;
    }
    return groupedRewardDropPoolCache[rewardGroupId];
}
exports.getRewardDropPoolDataInGroup = getRewardDropPoolDataInGroup;
// --------------------------------------------------------------------------
// CMS.Item.id(CMS.Mate.pieceId) -> CMS.Mate
// 어떤 항해사의 계약서인지 알아낸다.
// CMS.Mate.piece 중복되는 row 가 없다고 한다. Validation: UWO-12395
// --------------------------------------------------------------------------
let mateCmsByPieceIdCache;
function getMateCmsFromAwakenPieceItem(itemCmsId) {
    if (!mateCmsByPieceIdCache) {
        const byPieceId = {};
        lodash_1.default.forOwn(_1.default.Mate, (elem) => {
            if (elem.pieceId === undefined) {
                return;
            }
            byPieceId[elem.pieceId] = elem;
        });
        mateCmsByPieceIdCache = byPieceId;
    }
    return mateCmsByPieceIdCache[itemCmsId];
}
exports.getMateCmsFromAwakenPieceItem = getMateCmsFromAwakenPieceItem;
// --------------------------------------------------------------------------
// CMS.Item.id(CMS.ShipBlueprint.materialBpId) -> CMS.ShipBlueprint
// 어떤 설계도의 materialBpId 인지 알아낸다.
// CMS.ShipBlueprint.materialBpId 는 중복되는 row 가 없다고 한다. Validation: UWO-16398
// --------------------------------------------------------------------------
let shipBluprintCmsByMaterialBpIdCache;
function getShipBlueprintCmsFromMaterialBpId(itemCmsId) {
    if (!shipBluprintCmsByMaterialBpIdCache) {
        const byMaterialBpId = {};
        lodash_1.default.forOwn(_1.default.ShipBlueprint, (elem) => {
            if (elem.materialBpId === undefined) {
                return;
            }
            byMaterialBpId[elem.materialBpId] = elem;
        });
        shipBluprintCmsByMaterialBpIdCache = byMaterialBpId;
    }
    return shipBluprintCmsByMaterialBpIdCache[itemCmsId];
}
exports.getShipBlueprintCmsFromMaterialBpId = getShipBlueprintCmsFromMaterialBpId;
// 동일한 친밀도 레벨에서 추가가능한 값을 얻기.
function addableIntimacyWithinSameLevel(curIntimacy, input) {
    let max = 0;
    if (curIntimacy <= _1.default.Const.PubStaffLv1MAX.value) {
        max = _1.default.Const.PubStaffLv1MAX.value;
    }
    else if (curIntimacy <= _1.default.Const.PubStaffLv2MAX.value) {
        max = _1.default.Const.PubStaffLv2MAX.value;
    }
    else if (curIntimacy <= _1.default.Const.PubStaffLv3MAX.value) {
        max = _1.default.Const.PubStaffLv3MAX.value;
    }
    else {
        return input;
    }
    // 현재 레벨에서 최대 추가 가능한 값.
    const maxAddable = max - curIntimacy;
    return Math.min(maxAddable, input);
}
exports.addableIntimacyWithinSameLevel = addableIntimacyWithinSameLevel;
function getIntimacyLevelByIntimacyPoint(curIntimacy) {
    let lv = 1;
    while (true) {
        let key = `PubStaffLv${lv}MAX`;
        if (!_1.default.Const[key]) {
            break;
        }
        if (curIntimacy <= _1.default.Const[key].value) {
            return lv;
        }
        lv++;
    }
    return lv;
}
exports.getIntimacyLevelByIntimacyPoint = getIntimacyLevelByIntimacyPoint;
// 친밀도 변화량 %구하기.
function getChangedIntimacyPctOfCurLevel(intimacy, changeVal) {
    const curLv = getIntimacyLevelByIntimacyPoint(intimacy);
    let key = `PubStaffLv${curLv}MAX`;
    if (!_1.default.Const[key]) {
        return 0;
    }
    const endPoint = _1.default.Const[key].value;
    let beginPoint = 1;
    if (curLv > 1) {
        key = `PubStaffLv${curLv - 1}MAX`;
        if (!_1.default.Const[key]) {
            return 0;
        }
        beginPoint = _1.default.Const[key].value + 1;
    }
    const curPct = ((intimacy - beginPoint) / (endPoint - beginPoint)) * 100;
    const changedPct = Math.floor((changeVal / (endPoint - beginPoint)) * 100);
    return changedPct;
}
exports.getChangedIntimacyPctOfCurLevel = getChangedIntimacyPctOfCurLevel;
// 최대 레벨까지의 친밀도% 값 구하기.
function getIntimacyPctOfMaxLevel(curIntimacy) {
    let lv = 1;
    let maxLvPoint = 1;
    while (true) {
        let key = `PubStaffLv${lv}MAX`;
        if (!_1.default.Const[key]) {
            break;
        }
        maxLvPoint = _1.default.Const[key].value;
        lv++;
    }
    const pct = Math.floor((curIntimacy / maxLvPoint) * 100);
    return pct;
}
exports.getIntimacyPctOfMaxLevel = getIntimacyPctOfMaxLevel;
/**
 * 여관-고용 에 등장하는(아직 고용되지 않은) 항해사들의 친밀도 등급을 구함.
 */
function getUnemployedMateIntimacyGrade(intimacy) {
    const constCms = _1.default.Const;
    const maxIntimacyGrade = constCms.MateRecruitingClosenessCountMax.value;
    for (let grade = maxIntimacyGrade; grade > 1; grade--) {
        if (constCms['MateRecruitngClosenessCount' + grade].value <= intimacy) {
            return grade;
        }
    }
    return 1;
}
exports.getUnemployedMateIntimacyGrade = getUnemployedMateIntimacyGrade;
// 오션서버에서 동기화에 필요한 스탯 리스트 얻기.
function getRequiredSyncStatForOcean() {
    // 필요시 아래에 추가할 것.
    return [PASSIVE_EFFECT.ADVENTURE_ENCOUNT_RATE];
}
exports.getRequiredSyncStatForOcean = getRequiredSyncStatForOcean;
// cache
let shipyardBuilds;
function GetShipyardBuilds(shipBuildGroup) {
    if (!shipyardBuilds) {
        shipyardBuilds = {};
        lodash_1.default.forOwn(_1.default.ShipyardBuild, (elem) => {
            if (!shipyardBuilds[elem.group]) {
                shipyardBuilds[elem.group] = [];
            }
            shipyardBuilds[elem.group].push(elem);
        });
    }
    return shipyardBuilds[shipBuildGroup];
}
exports.GetShipyardBuilds = GetShipyardBuilds;
// ------------------------------------------------------------------------------------------------
// 모의전 등급 리스트 우선 순위로 구하기
let arenaTierListByPriorityCache;
function getArenaTierListByPriority() {
    if (!arenaTierListByPriorityCache) {
        arenaTierListByPriorityCache = [];
        let mapByPriority = {};
        lodash_1.default.forOwn(_1.default.ArenaTierList, (elem) => {
            mapByPriority[elem.priority] = elem;
        });
        let loop = 1;
        while (mapByPriority[loop]) {
            arenaTierListByPriorityCache.push(mapByPriority[loop++]);
        }
    }
    return arenaTierListByPriorityCache;
}
exports.getArenaTierListByPriority = getArenaTierListByPriority;
// ------------------------------------------------------------------------------------------------
// 모의전 정산 초기화 점수 구하기
// 최소 승점 구간이 배열의 가장 앞으로 오도록한다(오름차순)
let arenaInitScoresCache;
function getArenaInitScores() {
    if (!arenaInitScoresCache) {
        arenaInitScoresCache = [];
        const tierListByPriority = getArenaTierListByPriority();
        for (let m = tierListByPriority.length - 1; m > 0; m--) {
            const tier = tierListByPriority[m];
            arenaInitScoresCache.push({ min: tier.scoreMin, max: tier.scoreMax, init: tier.initScore });
            // 최대값이 무한인 경우 마지막 구간이다
            if (0 === tier.scoreMax) {
                break;
            }
        }
    }
    return arenaInitScoresCache;
}
exports.getArenaInitScores = getArenaInitScores;
// ------------------------------------------------------------------------------------------------
// 모의전 등급정보 구하기
let arenaGradeByGradeCache;
function getArenaTierByGrade(grade) {
    if (!arenaGradeByGradeCache) {
        arenaGradeByGradeCache = {};
        lodash_1.default.forOwn(_1.default.ArenaTierList, (elem) => {
            arenaGradeByGradeCache[elem.grade] = elem;
        });
    }
    return arenaGradeByGradeCache[grade];
}
exports.getArenaTierByGrade = getArenaTierByGrade;
// ------------------------------------------------------------------------------------------------
// 모의전 봇 group 구하기
let arenaMatchBotByGroupCache;
function getArenaMatchBotByGroup(group) {
    if (!arenaMatchBotByGroupCache) {
        arenaMatchBotByGroupCache = {};
        lodash_1.default.forOwn(_1.default.ArenaMatchBot, (elem) => {
            if (!arenaMatchBotByGroupCache[elem.group]) {
                arenaMatchBotByGroupCache[elem.group] = [];
            }
            arenaMatchBotByGroupCache[elem.group].push(elem);
        });
    }
    return arenaMatchBotByGroupCache[group];
}
exports.getArenaMatchBotByGroup = getArenaMatchBotByGroup;
// ------------------------------------------------------------------------------------------------
// 최초 선택 가능한 제독의 국가 반환
let firstAdmiralNationCmsIdsCache;
function getFirstAdmiralNationCmsIds() {
    if (!firstAdmiralNationCmsIdsCache) {
        firstAdmiralNationCmsIdsCache = [];
        lodash_1.default.forOwn(_1.default.Admiral, (elem) => {
            if (elem.isBasicAdmiral) {
                const nationCmsId = _1.default.Mate[elem.mateId].nationId;
                firstAdmiralNationCmsIdsCache.push(nationCmsId);
            }
        });
    }
    return firstAdmiralNationCmsIdsCache;
}
exports.getFirstAdmiralNationCmsIds = getFirstAdmiralNationCmsIds;
// ------------------------------------------------------------------------------------------------
// glog 를 위한 nodeidx 에 따른 chapter 반환
let questChapterByNodeIdxCache = {};
function getQuestChapterByNodeIdx(questCmsId, nodeIdx) {
    if (!questChapterByNodeIdxCache[questCmsId]) {
        questChapterByNodeIdxCache[questCmsId] = {};
        const questCms = _1.default.Quest[questCmsId];
        for (let i = 0; i < questCms.chapter.length; i++) {
            const questChapterElem = questCms.chapter[i];
            const chapterNodeIdx = questChapterElem.NodeIdx;
            const nextchapterNodeIdx = questCms.chapter[i + 1]
                ? questCms.chapter[i + 1].NodeIdx
                : questCms.nodes.length;
            for (let i = chapterNodeIdx; i < nextchapterNodeIdx; i++) {
                questChapterByNodeIdxCache[questCmsId][i] = questChapterElem.Text;
            }
        }
        console.log(questChapterByNodeIdxCache[questCmsId]);
    }
    if (questChapterByNodeIdxCache[nodeIdx]) {
        return questChapterByNodeIdxCache[nodeIdx];
    }
    return null;
}
exports.getQuestChapterByNodeIdx = getQuestChapterByNodeIdx;
// ------------------------------------------------------------------------------------------------
function getBuildingHours(buildingHoursCmsId, developmentLevels) {
    let sumLevel = 0;
    for (const lv of developmentLevels) {
        sumLevel += lv;
    }
    const buildingHoursCms = _1.default.BuildingHours[buildingHoursCmsId];
    let elem = buildingHoursCms.buildingHour[0];
    for (const buildingHour of buildingHoursCms.buildingHour) {
        if (sumLevel < buildingHour.TownDevelopSumTerms) {
            break;
        }
        elem = buildingHour;
    }
    return elem;
}
exports.getBuildingHours = getBuildingHours;
// ------------------------------------------------------------------------------------------------
// liveEvent 중인지 반환.
function isLiveEvent(liveEventCmsId, curTimeUtc) {
    if (!liveEventCmsId) {
        return false;
    }
    const liveEventCms = _1.default.LiveEvent[liveEventCmsId];
    if (!liveEventCms) {
        return false;
    }
    const curDate = new Date(curTimeUtc * 1000);
    if (isFilteredByCountryCode(liveEventCms.localBitflag)) {
        return false;
    }
    if (curDate >= mutil.newDateByCmsDateStr(liveEventCms.startDate) &&
        curDate <= mutil.newDateByCmsDateStr(liveEventCms.endDate)) {
        return true;
    }
    return false;
}
exports.isLiveEvent = isLiveEvent;
// ------------------------------------------------------------------------------------------------
// TradeComboSetCms 는  tradeComboSetGroup, tradeComboSetGroupSortOrder 유니크 해야 한다
let tradeComboGroupCache = {};
function GetTradeComboGroup(townCmsId) {
    const groupRef = _1.default.Town[townCmsId].tradeComboSetGroup;
    if (!tradeComboGroupCache[groupRef]) {
        lodash_1.default.forOwn(_1.default.TradeComboSet, (elem) => {
            if (elem.tradeComboSetGroup === groupRef) {
                if (!tradeComboGroupCache[elem.tradeComboSetGroup]) {
                    tradeComboGroupCache[elem.tradeComboSetGroup] = [];
                }
                tradeComboGroupCache[elem.tradeComboSetGroup].push(elem);
            }
        });
        tradeComboGroupCache[groupRef].sort((a, b) => a.tradeComboSetGroupSortOrder - b.tradeComboSetGroupSortOrder);
    }
    return tradeComboGroupCache[groupRef];
}
exports.GetTradeComboGroup = GetTradeComboGroup;
// ------------------------------------------------------------------------------------------------
let eventGameCache = {};
function GetEventGameGroup(eventPageCmsId) {
    const groupRef = _1.default.EventPage[eventPageCmsId].groupRef;
    if (!eventGameCache[groupRef]) {
        lodash_1.default.forOwn(_1.default.EventGameList, (elem) => {
            if (elem.groupId === groupRef) {
                if (!eventGameCache[elem.groupId]) {
                    eventGameCache[elem.groupId] = [];
                }
                eventGameCache[elem.groupId].push(elem);
            }
        });
    }
    return eventGameCache[groupRef];
}
exports.GetEventGameGroup = GetEventGameGroup;
// ------------------------------------------------------------------------------------------------
let boardGameRewardCache = {};
function GetBoardGameRewardGroup(groupRef) {
    if (!boardGameRewardCache[groupRef]) {
        lodash_1.default.forOwn(_1.default.BoardGameReward, (elem) => {
            if (elem.groupId === groupRef) {
                if (!boardGameRewardCache[elem.groupId]) {
                    boardGameRewardCache[elem.groupId] = [];
                }
                boardGameRewardCache[elem.groupId].push(elem);
            }
        });
        boardGameRewardCache[groupRef].sort((a, b) => a.tileOrder - b.tileOrder);
    }
    return boardGameRewardCache[groupRef];
}
exports.GetBoardGameRewardGroup = GetBoardGameRewardGroup;
// ------------------------------------------------------------------------------------------------
const tradeAreaTileDataDescByTownCache = {};
function getTradeAreaTileDataByTown(townCmsId) {
    if (!tradeAreaTileDataDescByTownCache[townCmsId]) {
        const townLocation = _1.default.TownLocation[townCmsId];
        if (townLocation) {
            const curTile = (0, oceanCoordinate_1.LatLon2Tile)(townLocation.location.latitude, townLocation.location.longitude);
            tradeAreaTileDataDescByTownCache[townCmsId] = _1.default.TradeAreaTile.t[curTile.x][curTile.y];
        }
    }
    return tradeAreaTileDataDescByTownCache[townCmsId];
}
exports.getTradeAreaTileDataByTown = getTradeAreaTileDataByTown;
// ------------------------------------------------------------------------------------------------
let townCmsIdsByTradeAreaTileDataCache;
function getTownCmsIdsByTradeAreaTileData(tradeAreaTileDataCmsId) {
    if (!townCmsIdsByTradeAreaTileDataCache) {
        townCmsIdsByTradeAreaTileDataCache = {};
        lodash_1.default.forOwn(_1.default.Town, (townCms) => {
            const tradeAreaTileDataCmsId = getTradeAreaTileDataByTown(townCms.id);
            const tradeAreaTileDataCms = _1.default.TradeAreaTileData[tradeAreaTileDataCmsId];
            if (!townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCms.id]) {
                townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCms.id] = [];
            }
            townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCms.id].push(townCms.id);
        });
    }
    return townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCmsId];
}
exports.getTownCmsIdsByTradeAreaTileData = getTownCmsIdsByTradeAreaTileData;
// ------------------------------------------------------------------------------------------------
const sortedTradeEventsByTradeAreaTileDataCache = {};
function getSortedTradeEventsByTradeAreaTileData(tradeAreaTileDataCms) {
    if (!sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id]) {
        sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id] = lodash_1.default.cloneDeep(tradeAreaTileDataCms.majorTrandId);
        sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id].sort((a, b) => {
            const aTradeEvent = _1.default.TradeEvent[a];
            const bTradeEvent = _1.default.TradeEvent[b];
            return bTradeEvent.majorTrendHour - aTradeEvent.majorTrendHour;
        });
    }
    return sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id];
}
exports.getSortedTradeEventsByTradeAreaTileData = getSortedTradeEventsByTradeAreaTileData;
// ------------------------------------------------------------------------------------------------
let tradeEventCmsByEvnetTypeCache;
function getTradeEventCmsByEvnetType(type) {
    if (!tradeEventCmsByEvnetTypeCache) {
        tradeEventCmsByEvnetTypeCache = {};
        lodash_1.default.forOwn(_1.default.TradeEvent, (elem) => {
            tradeEventCmsByEvnetTypeCache[elem.tradeEventType] = elem;
        });
    }
    return tradeEventCmsByEvnetTypeCache[type];
}
exports.getTradeEventCmsByEvnetType = getTradeEventCmsByEvnetType;
// ------------------------------------------------------------------------------------------------
let landExploreDispatchCache = {};
function GetLandExploreDispatchGroup(groupId) {
    if (!landExploreDispatchCache[groupId]) {
        lodash_1.default.forOwn(_1.default.LandExplore, (elem) => {
            if (elem.dispatchGroup === groupId) {
                if (!landExploreDispatchCache[elem.dispatchGroup]) {
                    landExploreDispatchCache[elem.dispatchGroup] = [];
                }
                landExploreDispatchCache[elem.dispatchGroup].push(elem);
            }
        });
    }
    return landExploreDispatchCache[groupId];
}
exports.GetLandExploreDispatchGroup = GetLandExploreDispatchGroup;
let tradeAreaTileDataDispatchCache = {};
function TradeAreaTileDataDispatchGroup(groupId) {
    if (!tradeAreaTileDataDispatchCache[groupId]) {
        lodash_1.default.forOwn(_1.default.TradeAreaTileData, (elem) => {
            if (elem.dispatchGroup === groupId) {
                if (!tradeAreaTileDataDispatchCache[elem.dispatchGroup]) {
                    tradeAreaTileDataDispatchCache[elem.dispatchGroup] = [];
                }
                tradeAreaTileDataDispatchCache[elem.dispatchGroup].push(elem);
            }
        });
    }
    return tradeAreaTileDataDispatchCache[groupId];
}
exports.TradeAreaTileDataDispatchGroup = TradeAreaTileDataDispatchGroup;
// ------------------------------------------------------------------------------------------------
// TradeAreaTile 기반에 속해있는 townCmsId 를 리턴한다
let tradeAreaTileCache = {};
function TradeAreaTileGroup(tradeAreaTileCmsId) {
    if (!tradeAreaTileCache[tradeAreaTileCmsId]) {
        lodash_1.default.forOwn(_1.default.TownLocation, (elem) => {
            const curLoc = elem.spawnArea;
            const tile = (0, oceanCoordinate_1.LatLon2Tile)(curLoc.latitude, curLoc.longitude);
            const cmsId = _1.default.TradeAreaTile.t[tile.x][tile.y];
            if (!tradeAreaTileCache[cmsId]) {
                tradeAreaTileCache[cmsId] = [];
            }
            tradeAreaTileCache[cmsId].push(elem.town.id);
        });
    }
    return tradeAreaTileCache[tradeAreaTileCmsId];
}
exports.TradeAreaTileGroup = TradeAreaTileGroup;
// ------------------------------------------------------------------------------------------------
// 폭증 발생 가능한 교역품 리스트를 가져온다
const unpopularCandidateTradeGoodsCache = {};
// 전체 항구에서 판매중인 교역품을 제외한 교역품 저장
const unSoldTradeGoodsCache = new Set();
function GetUnpopularTradeGoodsCandidates(townCmsId) {
    if (unSoldTradeGoodsCache.size === 0) {
        lodash_1.default.forOwn(_1.default.TradeGoods, (elem) => {
            // if (isFilteredByCountryCode(elem)) {
            //   return;
            // }
            unSoldTradeGoodsCache.add(elem.id);
        });
        lodash_1.default.forOwn(_1.default.Trade, (elem) => {
            unSoldTradeGoodsCache.delete(elem.tradeGoodsId);
        });
    }
    if (unpopularCandidateTradeGoodsCache[townCmsId]) {
        return unpopularCandidateTradeGoodsCache[townCmsId];
    }
    const pickTownSoldGoodsCmsIds = new Set();
    lodash_1.default.forOwn(_1.default.Trade, (elem) => {
        if (elem.townId === townCmsId) {
            pickTownSoldGoodsCmsIds.add(elem.tradeGoodsId);
        }
    });
    const townCulturalAreaCmsId = _1.default.Town[townCmsId].CulturalAreaId;
    unpopularCandidateTradeGoodsCache[townCmsId] = [];
    const soldAble = new Set();
    lodash_1.default.forOwn(_1.default.Trade, (elem) => {
        if (pickTownSoldGoodsCmsIds.has(elem.tradeGoodsId)) {
            return;
        }
        const tradeGoodsItem = _1.default.TradeGoods[elem.tradeGoodsId];
        if (!tradeGoodsItem) {
            return;
        }
        if (unSoldTradeGoodsCache.has(elem.tradeGoodsId)) {
            return;
        }
        if (elem.liveEvent && elem.rotationTradeGroupId) {
            return;
        }
        if (elem.contentsTerms) {
            return;
        }
        // 문화권에서 교역소 판매 불가능한 상품 제외
        const idx = townCulturalAreaCmsId % exports.CultureAreaCmsBaseCmsId;
        if (tradeGoodsItem.cultureSellPrices[idx] < 0) {
            return;
        }
        soldAble.add(elem.tradeGoodsId);
    });
    unpopularCandidateTradeGoodsCache[townCmsId] = Array.from(soldAble);
    return unpopularCandidateTradeGoodsCache[townCmsId];
}
exports.GetUnpopularTradeGoodsCandidates = GetUnpopularTradeGoodsCandidates;
// ------------------------------------------------------------------------------------------------
const tradeGoodsCache = {};
function TradeGoodsGroupInTownCmsId(townCmsId) {
    if (!tradeGoodsCache[townCmsId]) {
        lodash_1.default.forOwn(_1.default.Trade, (elem) => {
            const goodsCms = _1.default.TradeGoods[elem.tradeGoodsId];
            // if (isFilteredByCountryCode(goodsCms)) {
            //   return;
            // }
            if (!goodsCms) {
                return;
            }
            if (elem.townId === townCmsId) {
                if (!tradeGoodsCache[elem.townId]) {
                    tradeGoodsCache[elem.townId] = [];
                }
                tradeGoodsCache[elem.townId].push(elem);
            }
        });
    }
    return tradeGoodsCache[townCmsId];
}
exports.TradeGoodsGroupInTownCmsId = TradeGoodsGroupInTownCmsId;
function isCostumeEquipType(type) {
    return (type === cEquipDesc_1.CEQUIP_TYPE.WEAPON_COSTUME ||
        type === cEquipDesc_1.CEQUIP_TYPE.BODY_COSTUME ||
        type === cEquipDesc_1.CEQUIP_TYPE.HAT_COSTUME ||
        type === cEquipDesc_1.CEQUIP_TYPE.CAPE_COSTUME ||
        type === cEquipDesc_1.CEQUIP_TYPE.FACE_COSTUME ||
        type === cEquipDesc_1.CEQUIP_TYPE.FX_COSTUME);
}
exports.isCostumeEquipType = isCostumeEquipType;
const equipTypeToCostumeEquipType = {
    [cEquipDesc_1.CEQUIP_TYPE.WEAPON]: cEquipDesc_1.CEQUIP_TYPE.WEAPON_COSTUME,
    [cEquipDesc_1.CEQUIP_TYPE.BODY]: cEquipDesc_1.CEQUIP_TYPE.BODY_COSTUME,
    [cEquipDesc_1.CEQUIP_TYPE.HAT]: cEquipDesc_1.CEQUIP_TYPE.HAT_COSTUME,
    [cEquipDesc_1.CEQUIP_TYPE.CAPE]: cEquipDesc_1.CEQUIP_TYPE.CAPE_COSTUME,
    [cEquipDesc_1.CEQUIP_TYPE.FACE]: cEquipDesc_1.CEQUIP_TYPE.FACE_COSTUME,
};
function getCurEquipType(nub) {
    const cEquipCms = _1.default.CEquip[nub.cmsId];
    if (nub.isCostume) {
        return equipTypeToCostumeEquipType[cEquipCms.type];
    }
    else {
        return cEquipCms.type;
    }
}
exports.getCurEquipType = getCurEquipType;
// ------------------------------------------------------------------------------------------------
// landExplore <---> adventureEventReward
// inheritLandFeature 상속 이면서 adventureEventReward.landExplore id 를 사용중인 cms 리턴
// ------------------------------------------------------------------------------------------------
const adventureEventRewardQuestGroup = {};
function GetLandExploreQuestSpot(landExploreCmsId) {
    if (!_1.default.LandExplore[landExploreCmsId].inheritLandFeature) {
        return undefined;
    }
    if (!adventureEventRewardQuestGroup[landExploreCmsId]) {
        lodash_1.default.forOwn(_1.default.AdventureEventReward, (elem) => {
            if (!elem.landExploreId || !elem.ctDiscovery || !elem.ctEffectRate3f) {
                return;
            }
            // !! adventureEventReward.landExploreId 가 여러개 이면 기획 버그
            adventureEventRewardQuestGroup[elem.landExploreId] = elem;
        });
    }
    return adventureEventRewardQuestGroup[landExploreCmsId];
}
exports.GetLandExploreQuestSpot = GetLandExploreQuestSpot;
// ------------------------------------------------------------------------------------------------
// landExplore <---> adventureEventReward
// cms 간 key 를 공유하여 adventureEventReward 를 찾기 위한 캐시데이터
// ------------------------------------------------------------------------------------------------
const adventureEventRewardGroup = {};
function GetAdventureEventRewardGroup(weekSessionId, landExploreCmsId, landGrade, landCulture, landRewardType) {
    var _a;
    const argKey = '' + landExploreCmsId + landGrade + landCulture;
    if (!adventureEventRewardGroup[argKey] ||
        adventureEventRewardGroup[argKey].weekSessionId !== weekSessionId) {
        lodash_1.default.forOwn(_1.default.AdventureEventReward, (elem) => {
            if (elem.advRewardGrade === undefined ||
                elem.advRewardCulture === undefined ||
                elem.landRewardType === undefined) {
                return;
            }
            // landRewardType 중 하나를 weekSessionId 기반으로 뽑는다
            const seed = parseInt(argKey + weekSessionId, 10);
            const pickRand = Math.floor((seed * Math.PI) % 10) / 10;
            const pickRewardType = landRewardType[Math.floor(pickRand * landRewardType.length)];
            if (landGrade === elem.advRewardGrade &&
                landCulture === elem.advRewardCulture &&
                pickRewardType === elem.landRewardType) {
                adventureEventRewardGroup[argKey] = { weekSessionId, adventureEventRewardCms: elem };
            }
        });
    }
    return (_a = adventureEventRewardGroup === null || adventureEventRewardGroup === void 0 ? void 0 : adventureEventRewardGroup[argKey]) === null || _a === void 0 ? void 0 : _a.adventureEventRewardCms;
}
exports.GetAdventureEventRewardGroup = GetAdventureEventRewardGroup;
// ------------------------------------------------------------------------------------------------
// 패스 이벤트 보상 최대 레벨 리턴
const passEventMaxLevelCache = {};
function getPassEventMaxLevel(passEventCms) {
    if (!passEventMaxLevelCache[passEventCms.id]) {
        const eventMissionCmses = getEventMissionCmses(passEventCms.groupRef);
        let maxLevel = 0;
        for (const elem of eventMissionCmses) {
            if (elem.type !== eventMissionDesc_1.EventMissionType.BASE_REWARD &&
                elem.type !== eventMissionDesc_1.EventMissionType.ADDITIONAL_REWARD &&
                elem.type !== eventMissionDesc_1.EventMissionType.REPEATED_REWARD) {
                continue;
            }
            if (elem.val > maxLevel) {
                maxLevel = elem.val;
            }
        }
        passEventMaxLevelCache[passEventCms.id] = maxLevel;
    }
    return passEventMaxLevelCache[passEventCms.id];
}
exports.getPassEventMaxLevel = getPassEventMaxLevel;
// ------------------------------------------------------------------------------------------------
// 반복 보상 EventMission TYPE 이 존재하면 최대 획득 경험치량 리턴
const passEventAddedExpCache = {};
function getPassEventAddedExp(passEventCms) {
    if (!passEventAddedExpCache[passEventCms.id]) {
        const eventMissionCmses = getEventMissionCmses(passEventCms.groupRef);
        for (const elem of eventMissionCmses) {
            if (elem.type === eventMissionDesc_1.EventMissionType.REPEATED_REWARD) {
                passEventAddedExpCache[passEventCms.id] = elem.requiredRepeatedExp * elem.repeatCount;
            }
        }
    }
    return passEventAddedExpCache[passEventCms.id] ? passEventAddedExpCache[passEventCms.id] : 0;
}
// ------------------------------------------------------------------------------------------------
let mateTrainingCache;
function getMateTraining(mateGrade, jobType, trainingGrade) {
    var _a, _b;
    if (!mateTrainingCache) {
        mateTrainingCache = {};
        lodash_1.default.forOwn(_1.default.MateTraining, (elem) => {
            var _a, _b;
            const duplicated = (_b = (_a = mateTrainingCache[elem.mateGrade]) === null || _a === void 0 ? void 0 : _a[elem.jobType]) === null || _b === void 0 ? void 0 : _b[elem.mateTrainingGrade];
            (0, assert_1.default)(!duplicated);
            lodash_1.default.merge(mateTrainingCache, {
                [elem.mateGrade]: {
                    [elem.jobType]: {
                        [elem.mateTrainingGrade]: elem,
                    },
                },
            });
        });
    }
    return (_b = (_a = mateTrainingCache === null || mateTrainingCache === void 0 ? void 0 : mateTrainingCache[mateGrade]) === null || _a === void 0 ? void 0 : _a[jobType]) === null || _b === void 0 ? void 0 : _b[trainingGrade];
}
exports.getMateTraining = getMateTraining;
// ------------------------------------------------------------------------------------------------
// 항해사 버프 이벤트들을 반환
let mateBuffLiveEventCache;
function getMateBuffLiveEvents() {
    if (!mateBuffLiveEventCache) {
        mateBuffLiveEventCache = {};
        lodash_1.default.forOwn(_1.default.LiveEvent, (elem) => {
            if (elem.eventMateJob && elem.eventMateJob.length > 0) {
                mateBuffLiveEventCache[elem.id] = elem;
            }
        });
    }
    return mateBuffLiveEventCache;
}
exports.getMateBuffLiveEvents = getMateBuffLiveEvents;
// ------------------------------------------------------------------------------------------------
// 여관 종업원 간에 동일한 퀘스트를 가지고 있지 않음.
// townBuilding에 등록된 Pubstaff의 경우 하나의 항구에만 배치되야한다고 전달받음.
function getTownCmsIdForPubStaffQuestCmsId(questCmsId) {
    let pubStaffCmsId;
    for (const elem of Object.values(_1.default.PubStaff)) {
        const questIds = elem.questId || [];
        for (const questId of questIds) {
            if (questId.Id === questCmsId) {
                pubStaffCmsId = elem.id;
                break;
            }
        }
        if (pubStaffCmsId) {
            break;
        }
    }
    for (const elem of Object.values(_1.default.TownBuilding)) {
        if (elem.PubStaffId && elem.PubStaffId === pubStaffCmsId) {
            return elem.townId;
        }
    }
    return null;
}
exports.getTownCmsIdForPubStaffQuestCmsId = getTownCmsIdForPubStaffQuestCmsId;
function getPubStaffQuestLevel(questCmsId) {
    for (const elem of Object.values(_1.default.PubStaff)) {
        const questIds = elem.questId || [];
        for (let i = 0; i < questIds.length; i++) {
            if (questIds[i].Id === questCmsId) {
                return i + 1;
            }
        }
    }
    return null;
}
exports.getPubStaffQuestLevel = getPubStaffQuestLevel;
function getEventRankingReceivedRewardIds(eventPageCms, rewardIdxArr) {
    const rewardIds = [];
    const eventRankingListCms = _1.default.EventRankingList[eventPageCms.groupRef];
    for (const rewardIdx of rewardIdxArr) {
        // 클라이언트 req값은 1부터 전달이 되서 -1 처리함
        rewardIds.push(eventRankingListCms.goals[rewardIdx - 1].Reward);
    }
    return rewardIds;
}
exports.getEventRankingReceivedRewardIds = getEventRankingReceivedRewardIds;
// ------------------------------------------------------------------------------------------------
let eventRankingRewardCache = {};
function getEventRankingRewardGroup(groupId, rewardType) {
    if (eventRankingRewardCache[groupId] && eventRankingRewardCache[groupId][rewardType]) {
        return eventRankingRewardCache[groupId][rewardType];
    }
    lodash_1.default.forOwn(_1.default.EventRankingReward, (elem) => {
        if (!eventRankingRewardCache[elem.group]) {
            eventRankingRewardCache[elem.group] = {};
        }
        if (!eventRankingRewardCache[elem.group][elem.relateType]) {
            eventRankingRewardCache[elem.group][elem.relateType] = [];
        }
        eventRankingRewardCache[elem.group][elem.relateType].push(elem);
    });
    return eventRankingRewardCache[groupId][rewardType];
}
exports.getEventRankingRewardGroup = getEventRankingRewardGroup;
// ------------------------------------------------------------------------------------------------
let eventRankingGuildCache = [];
function getEventPageRankingGuild() {
    if (eventRankingGuildCache.length > 0) {
        return eventRankingGuildCache;
    }
    lodash_1.default.forOwn(_1.default.EventPage, (eventPageCms) => {
        if (eventPageCms.type !== eventPageDesc_1.EventPageType.EVENT_RANKING) {
            return;
        }
        if (isFilteredByCountryCode(eventPageCms.localBitflag)) {
            return;
        }
        if (!_1.default.EventRankingList[eventPageCms.groupRef].useGuildRank) {
            return;
        }
        eventRankingGuildCache.push(eventPageCms);
    });
    return eventRankingGuildCache;
}
exports.getEventPageRankingGuild = getEventPageRankingGuild;
// ------------------------------------------------------------------------------------------------
let constellationCache = {};
function getConstellationByDiscoveryId(discoveryId) {
    if (!constellationCache[discoveryId]) {
        lodash_1.default.forOwn(_1.default.Constellation, (elem) => {
            if (elem.discoveryId === discoveryId) {
                constellationCache[elem.discoveryId] = elem;
            }
        });
    }
    return constellationCache[discoveryId];
}
exports.getConstellationByDiscoveryId = getConstellationByDiscoveryId;
function getShipCamouflageType(cmsId) {
    let type;
    if (_1.default.Ship[cmsId]) {
        type = shipCamouflageDesc_1.SHIP_CAMOUFLAGE_TYPE.NORMAL;
    }
    else if (_1.default.ShipCamouflage[cmsId]) {
        type = shipCamouflageDesc_1.SHIP_CAMOUFLAGE_TYPE.SPECIAL;
    }
    return type;
}
exports.getShipCamouflageType = getShipCamouflageType;
// ------------------------------------------------------------------------------------------------
let choiceBoxGroupCache;
function getChoiceBoxGroup(choiceBoxGroupNo) {
    if (!choiceBoxGroupCache) {
        choiceBoxGroupCache = {};
        lodash_1.default.forOwn(_1.default.ChoiceBox, (elem) => {
            const groupNo = elem.choiceBoxGroup;
            if (!choiceBoxGroupCache[groupNo]) {
                choiceBoxGroupCache[groupNo] = {};
            }
            choiceBoxGroupCache[groupNo][elem.id] = elem;
        });
    }
    return choiceBoxGroupCache[choiceBoxGroupNo];
}
exports.getChoiceBoxGroup = getChoiceBoxGroup;
// ------------------------------------------------------------------------------------------------
let returnUserCashShopCmsCache;
function getReturnUserCashShopCms() {
    if (!returnUserCashShopCmsCache) {
        returnUserCashShopCmsCache = [];
        lodash_1.default.forOwn(_1.default.CashShop, (elem) => {
            if (isFilteredByCountryCode(elem.productLocalBitflag)) {
                return;
            }
            if (elem.isSaleReturnUser) {
                returnUserCashShopCmsCache.push(elem);
            }
        });
    }
    return returnUserCashShopCmsCache;
}
exports.getReturnUserCashShopCms = getReturnUserCashShopCms;
// 판매 기간이 가변적인 상품들
function isOpenDurationCashShopCms(cashShopCms) {
    return cashShopCms.isSaleReturnUser;
}
exports.isOpenDurationCashShopCms = isOpenDurationCashShopCms;
// ------------------------------------------------------------------------------------------------
let returnMaxOrderByRotationGroupCache;
function _getRotationMaxTradeOrder(rotationTradeGroupId) {
    if (!returnMaxOrderByRotationGroupCache) {
        returnMaxOrderByRotationGroupCache = {};
        lodash_1.default.forOwn(_1.default.Trade, (elem) => {
            if (!elem.rotationTradeGroupId || !elem.rotationTradeGroupOrder) {
                return;
            }
            if (!returnMaxOrderByRotationGroupCache[elem.rotationTradeGroupId] ||
                returnMaxOrderByRotationGroupCache[elem.rotationTradeGroupId] < elem.rotationTradeGroupOrder) {
                returnMaxOrderByRotationGroupCache[elem.rotationTradeGroupId] =
                    elem.rotationTradeGroupOrder;
            }
        });
    }
    return returnMaxOrderByRotationGroupCache[rotationTradeGroupId];
}
// ------------------------------------------------------------------------------------------------
let admiralQuestPassIdByMateCmsIdCache;
function getAdmiralQuestPassIdByMateCmsId(mateCmsId) {
    if (!admiralQuestPassIdByMateCmsIdCache) {
        admiralQuestPassIdByMateCmsIdCache = {};
        lodash_1.default.forOwn(_1.default.QuestPass, (elem) => {
            if (elem.type === questPassDesc_1.QuestPassType.Admiral) {
                admiralQuestPassIdByMateCmsIdCache[elem.mateId] = elem.id;
            }
        });
    }
    return admiralQuestPassIdByMateCmsIdCache[mateCmsId];
}
exports.getAdmiralQuestPassIdByMateCmsId = getAdmiralQuestPassIdByMateCmsId;
// ------------------------------------------------------------------------------------------------
let relationShipChronicleByQuestIdCache;
function getRelationShipChronicleByQuestId(questCmsId) {
    if (!relationShipChronicleByQuestIdCache) {
        relationShipChronicleByQuestIdCache = {};
        lodash_1.default.forOwn(_1.default.RelationShipChronicle, (elem) => {
            if (isFilteredByCountryCode(elem.localBitflag)) {
                return;
            }
            relationShipChronicleByQuestIdCache[elem.questId] = elem;
        });
    }
    return relationShipChronicleByQuestIdCache[questCmsId];
}
exports.getRelationShipChronicleByQuestId = getRelationShipChronicleByQuestId;
// 투자 가능한 townCmsId
let investmentTownCmsIds = [];
function getInvestmentTownCmsIds() {
    if (investmentTownCmsIds.length === 0) {
        lodash_1.default.forOwn(_1.default.Town, (elem) => {
            if (elem.ownType !== TOWN_OWN_TYPE.UNOCCUPIABLE) {
                investmentTownCmsIds.push(elem.id);
            }
        });
    }
    return investmentTownCmsIds;
}
exports.getInvestmentTownCmsIds = getInvestmentTownCmsIds;
// ------------------------------------------------------------------------------------------------
let cEquipEnchantCache;
function getCEquipEnchant(enchantGroup, enchantLv) {
    if (!cEquipEnchantCache) {
        cEquipEnchantCache = {};
        lodash_1.default.forOwn(_1.default.CEquipEnchant, (elem) => {
            if (!cEquipEnchantCache[elem.enchantGroup]) {
                cEquipEnchantCache[elem.enchantGroup] = {};
            }
            cEquipEnchantCache[elem.enchantGroup][elem.enchantLv] = elem;
        });
    }
    return cEquipEnchantCache[enchantGroup][enchantLv];
}
exports.getCEquipEnchant = getCEquipEnchant;
// ------------------------------------------------------------------------------------------------
let shipSlotEnchantCache;
function getShipSlotEnchant(enchantGroup, enchantLv) {
    if (!shipSlotEnchantCache) {
        shipSlotEnchantCache = {};
        lodash_1.default.forOwn(_1.default.ShipSlotEnchant, (elem) => {
            if (!shipSlotEnchantCache[elem.enchantGroup]) {
                shipSlotEnchantCache[elem.enchantGroup] = {};
            }
            shipSlotEnchantCache[elem.enchantGroup][elem.enchantLv] = elem;
        });
    }
    return shipSlotEnchantCache[enchantGroup][enchantLv];
}
exports.getShipSlotEnchant = getShipSlotEnchant;
// ------------------------------------------------------------------------------------------------
let cEquipEnchantStatRatioCache;
let shipSlotEnchantStatRatioCache;
function _buildEnchantStatRatioCache() {
    if (!cEquipEnchantStatRatioCache || !shipSlotEnchantStatRatioCache) {
        cEquipEnchantStatRatioCache = {};
        shipSlotEnchantStatRatioCache = {};
        const caching = function (enchantStatRatioCache, elem) {
            enchantStatRatioCache[elem.enchantLv] = elem;
        };
        lodash_1.default.forOwn(_1.default.EnchantStatRatio, (elem) => {
            switch (elem.enchantType) {
                case enchantStatRatioDesc_1.ENCHANT_TYPE.SHIP_SLOT:
                    caching(shipSlotEnchantStatRatioCache, elem);
                    break;
                case enchantStatRatioDesc_1.ENCHANT_TYPE.CEQUIP:
                    caching(cEquipEnchantStatRatioCache, elem);
                    break;
            }
        });
    }
}
// ------------------------------------------------------------------------------------------------
function getCEquipEnchantStatRatio(enchantLv) {
    _buildEnchantStatRatioCache();
    return cEquipEnchantStatRatioCache[enchantLv];
}
exports.getCEquipEnchantStatRatio = getCEquipEnchantStatRatio;
// ------------------------------------------------------------------------------------------------
function getShipSlotEnchantStatRatio(enchantLv) {
    _buildEnchantStatRatioCache();
    return shipSlotEnchantStatRatioCache[enchantLv];
}
exports.getShipSlotEnchantStatRatio = getShipSlotEnchantStatRatio;
// ------------------------------------------------------------------------------------------------
let shipComposeGroupCache;
function getShipComposeGroup(groupId) {
    if (!shipComposeGroupCache) {
        shipComposeGroupCache = {};
        lodash_1.default.forOwn(_1.default.ShipCompose, (elem) => {
            if (!shipComposeGroupCache[elem.groupId]) {
                shipComposeGroupCache[elem.groupId] = [];
            }
            shipComposeGroupCache[elem.groupId].push(elem);
        });
    }
    return shipComposeGroupCache[groupId];
}
exports.getShipComposeGroup = getShipComposeGroup;
// ------------------------------------------------------------------------------------------------
const sellingSmuggleGoods = {};
function setSellingSmuggleGoods(townCmsId) {
    if (sellingSmuggleGoods[townCmsId]) {
        return;
    }
    sellingSmuggleGoods[townCmsId] = {};
    lodash_1.default.forOwn(_1.default.Smuggle, (smuggleCms) => {
        const goodsCms = _1.default.SmuggleGoods[smuggleCms.smuggleGoodsId];
        if (isFilteredByCountryCode(goodsCms.localBitFlag)) {
            return;
        }
        if (smuggleCms.townId === townCmsId) {
            sellingSmuggleGoods[townCmsId][smuggleCms.smuggleGoodsId] = smuggleCms;
        }
    });
}
function isSellingSmuggleInTown(townCmsId, smuggleGoodsCmsId) {
    setSellingSmuggleGoods(townCmsId);
    return sellingSmuggleGoods[townCmsId][smuggleGoodsCmsId] ? true : false;
}
exports.isSellingSmuggleInTown = isSellingSmuggleInTown;
function getTownSellingSmuggleGoodsCmsIds(townCmsId) {
    setSellingSmuggleGoods(townCmsId);
    return lodash_1.default.values(sellingSmuggleGoods[townCmsId]).map((elem) => elem.smuggleGoodsId);
}
exports.getTownSellingSmuggleGoodsCmsIds = getTownSellingSmuggleGoodsCmsIds;
function getTownSmuggleGoodsCms(townCmsId, smuggleGoodsCmsId) {
    setSellingSmuggleGoods(townCmsId);
    return sellingSmuggleGoods[townCmsId][smuggleGoodsCmsId];
}
exports.getTownSmuggleGoodsCms = getTownSmuggleGoodsCms;
// ------------------------------------------------------------------------------------------------
let townNpcShopGroupCache;
function getTownNpcShopGroup(groupId) {
    if (!townNpcShopGroupCache) {
        townNpcShopGroupCache = {};
        lodash_1.default.forOwn(_1.default.TownNpcShop, (townNpcShopCms) => {
            if (!townNpcShopGroupCache[townNpcShopCms.npcShopGroup]) {
                townNpcShopGroupCache[townNpcShopCms.npcShopGroup] = [];
            }
            townNpcShopGroupCache[townNpcShopCms.npcShopGroup].push(townNpcShopCms);
        });
    }
    return townNpcShopGroupCache[groupId];
}
exports.getTownNpcShopGroup = getTownNpcShopGroup;
// ------------------------------------------------------------------------------------------------
let townNpcIdCache;
function getNpcIdsByTownCmsId(townCmsId) {
    if (!townNpcIdCache) {
        townNpcIdCache = {};
        lodash_1.default.forOwn(_1.default.TownNpcServer, (townNpc) => {
            if (!townNpcIdCache[townNpc.townId]) {
                townNpcIdCache[townNpc.townId] = [];
            }
            townNpcIdCache[townNpc.townId].push(townNpc.npcId);
        });
    }
    return townNpcIdCache[townCmsId];
}
exports.getNpcIdsByTownCmsId = getNpcIdsByTownCmsId;
// ------------------------------------------------------------------------------------------------
function getNpcShopGroupByTownCmsId(townCmsId) {
    const npcIds = getNpcIdsByTownCmsId(townCmsId);
    if (!npcIds || npcIds.length === 0) {
        return undefined;
    }
    for (const id of npcIds) {
        const npcCms = getTownNpcGlobalCms()[id];
        if (!npcCms) {
            continue;
        }
        const townNpcShopGroup = getTownNpcShopGroup(npcCms.npcShopGroup);
        if (townNpcShopGroup) {
            return townNpcShopGroup;
        }
    }
    return undefined;
}
exports.getNpcShopGroupByTownCmsId = getNpcShopGroupByTownCmsId;
// ------------------------------------------------------------------------------------------------
function getTownNpcInteractionCmsByInteractionFunctionType(townCmsId, interactionFunctionType) {
    var _a;
    const npcIds = getNpcIdsByTownCmsId(townCmsId);
    if (!npcIds || npcIds.length === 0) {
        return undefined;
    }
    for (const id of npcIds) {
        const interactionCmsIds = ((_a = getTownNpcGlobalCms()[id]) !== null && _a !== void 0 ? _a : _1.default.TownNpcTemplate[id])
            .npcInteractionId;
        if (!interactionCmsIds || interactionCmsIds.length === 0) {
            continue;
        }
        for (const interactionCmsId of interactionCmsIds) {
            const npcInteractionCms = _1.default.NpcInteraction[interactionCmsId];
            if (npcInteractionCms.interaction.Function === interactionFunctionType) {
                return npcInteractionCms;
            }
        }
    }
    return undefined;
}
exports.getTownNpcInteractionCmsByInteractionFunctionType = getTownNpcInteractionCmsByInteractionFunctionType;
// ------------------------------------------------------------------------------------------------
function getTownNpcIdByInteractionFunctionType(townCmsId, interactionFunctionType) {
    var _a;
    const npcIds = getNpcIdsByTownCmsId(townCmsId);
    if (!npcIds || npcIds.length === 0) {
        return undefined;
    }
    for (const id of npcIds) {
        const interactionCmsIds = ((_a = getTownNpcGlobalCms()[id]) !== null && _a !== void 0 ? _a : _1.default.TownNpcTemplate[id])
            .npcInteractionId;
        if (!interactionCmsIds || interactionCmsIds.length === 0) {
            continue;
        }
        for (const interactionCmsId of interactionCmsIds) {
            const npcInteractionCms = _1.default.NpcInteraction[interactionCmsId];
            if (npcInteractionCms.interaction.Function === interactionFunctionType) {
                return id;
            }
        }
    }
    return undefined;
}
exports.getTownNpcIdByInteractionFunctionType = getTownNpcIdByInteractionFunctionType;
// ------------------------------------------------------------------------------------------------
// karma 단계는 karma cms의 minValue 기준으로 정렬했을때의 index
let sortedKarmaCache;
function getKarmaLevel(karma) {
    if (!sortedKarmaCache) {
        sortedKarmaCache = lodash_1.default.sortBy(_1.default.Karma, (elem) => elem.karmaMinValue);
    }
    for (let i = 0; i < sortedKarmaCache.length; i++) {
        const karmaCms = sortedKarmaCache[i];
        if (lodash_1.default.inRange(karma, karmaCms.karmaMinValue, karmaCms.karmaMaxValue + 1)) {
            return i;
        }
    }
    return undefined;
}
exports.getKarmaLevel = getKarmaLevel;
// ------------------------------------------------------------------------------------------------
let infiniteLighthouseGroupCache;
function getInfiniteLighthouseGroup(stageGroupId) {
    if (!infiniteLighthouseGroupCache) {
        infiniteLighthouseGroupCache = {};
        lodash_1.default.forOwn(_1.default.InfiniteLighthouse, (elem) => {
            if (!infiniteLighthouseGroupCache[elem.stageGroup]) {
                infiniteLighthouseGroupCache[elem.stageGroup] = {
                    infiniteLighthouseSchedule: undefined,
                    infiniteLighthouses: [],
                };
            }
            infiniteLighthouseGroupCache[elem.stageGroup].infiniteLighthouses[elem.stage] = elem;
        });
        lodash_1.default.forOwn(_1.default.InfiniteLighthouseSchedule, (elem) => {
            if (!infiniteLighthouseGroupCache[elem.stageGroup]) {
                infiniteLighthouseGroupCache[elem.stageGroup] = {
                    infiniteLighthouseSchedule: elem,
                    infiniteLighthouses: [],
                };
            }
            else {
                infiniteLighthouseGroupCache[elem.stageGroup].infiniteLighthouseSchedule = elem;
            }
        });
    }
    return infiniteLighthouseGroupCache[stageGroupId];
}
exports.getInfiniteLighthouseGroup = getInfiniteLighthouseGroup;
// ------------------------------------------------------------------------------------------------
// 이민시 투자 점수 기반 투자 아이템 id 리턴 (가장 단위가 작은 투자 증서 지급)
let refundInvestItems = {};
function getRefundInvestItemCmsId(curTimeUtc) {
    const cmsId = formula.getInvestSeasonId(curTimeUtc);
    if (!refundInvestItems[cmsId]) {
        const elem = lodash_1.default.minBy(_1.default.InvestSeason[cmsId].investItems, (elem) => {
            return elem.Value;
        });
        refundInvestItems[cmsId] = elem.Id;
    }
    return refundInvestItems[cmsId];
}
exports.getRefundInvestItemCmsId = getRefundInvestItemCmsId;
// ------------------------------------------------------------------------------------------------
let clashUseMaps;
function getOceanStageCmsIdByClash(seasonOrder, weeklyOrder) {
    var _a;
    if (!clashUseMaps) {
        clashUseMaps = {};
        for (const cmsId in _1.default.ClashUseMap) {
            const clashUseMapCms = _1.default.ClashUseMap[cmsId];
            if (!clashUseMaps[clashUseMapCms.seasonOrder]) {
                clashUseMaps[clashUseMapCms.seasonOrder] = {};
            }
            clashUseMaps[clashUseMapCms.seasonOrder][clashUseMapCms.weeklyOrder] = clashUseMapCms.stageId;
        }
    }
    return (((_a = clashUseMaps === null || clashUseMaps === void 0 ? void 0 : clashUseMaps[seasonOrder]) === null || _a === void 0 ? void 0 : _a[weeklyOrder]) ||
        _1.default.ClashUseMap[_1.default.Const.ClashUseDefaultMap.value].stageId);
}
exports.getOceanStageCmsIdByClash = getOceanStageCmsIdByClash;
let firstInvestSeasonId = null;
function getFirstInvestSeasonCmsId() {
    if (firstInvestSeasonId) {
        return firstInvestSeasonId;
    }
    for (const cmsId in _1.default.InvestSeason) {
        const seasonId = parseInt(cmsId, 10);
        if (lodash_1.default.isNull(firstInvestSeasonId) || seasonId < firstInvestSeasonId) {
            firstInvestSeasonId = seasonId;
        }
    }
    return firstInvestSeasonId;
}
exports.getFirstInvestSeasonCmsId = getFirstInvestSeasonCmsId;
// ------------------------------------------------------------------------------------------------
let rewardSeason;
function getRewardSeasonElem(investSeasonCmsId, rewardSeasonCmsId) {
    if (!rewardSeason) {
        this.rewardSeason = {};
        lodash_1.default.forOwn(_1.default.RewardSeasonItems, (elem) => {
            const cmsId = elem.id;
            for (const reward of elem.reward) {
                if (!this.rewardSeason[reward.SeasonId]) {
                    this.rewardSeason[reward.SeasonId] = {};
                }
                this.rewardSeason[reward.SeasonId][cmsId] = reward;
            }
        });
    }
    if (!this.rewardSeason[investSeasonCmsId]) {
        mlog_1.default.warn('[RewardSeasonItemsLookupTable] invalid-type-1', {
            investSeasonCmsId,
            rewardSeasonCmsId,
        });
        return undefined;
    }
    if (!this.rewardSeason[investSeasonCmsId][rewardSeasonCmsId]) {
        mlog_1.default.warn('[RewardSeasonItemsLookupTable] invalid-type-2', {
            investSeasonCmsId,
            rewardSeasonCmsId,
        });
        return undefined;
    }
    return this.rewardSeason[investSeasonCmsId][rewardSeasonCmsId];
}
exports.getRewardSeasonElem = getRewardSeasonElem;
// ------------------------------------------------------------------------------------------------
// CMS 테이블 필터링 및 캐싱 함수들
// localBitFlag가 있는 경우 countryCode로 필터링, 없는 경우 전체 반환
// ------------------------------------------------------------------------------------------------
let achievementCmsCache;
function getAchievementCms() {
    if (!achievementCmsCache) {
        achievementCmsCache = {};
        lodash_1.default.forOwn(_1.default.Achievement, (elem) => {
            if (isFilteredByCountryCode(elem.localBitFlag)) {
                return;
            }
            achievementCmsCache[elem.id] = elem;
        });
    }
    return achievementCmsCache;
}
exports.getAchievementCms = getAchievementCms;
let hotTimeBuffCmsCache;
function getHotTimeBuffCms() {
    if (!hotTimeBuffCmsCache) {
        hotTimeBuffCmsCache = {};
        lodash_1.default.forOwn(_1.default.HotTimeBuff, (elem) => {
            if (isFilteredByCountryCode(elem.localBitflag)) {
                return;
            }
            hotTimeBuffCmsCache[elem.id] = elem;
        });
    }
    return hotTimeBuffCmsCache;
}
exports.getHotTimeBuffCms = getHotTimeBuffCms;
let townNpcGlobalCmsCache;
function getTownNpcGlobalCms() {
    if (!townNpcGlobalCmsCache) {
        townNpcGlobalCmsCache = {};
        lodash_1.default.forOwn(_1.default.TownNpcGlobal, (elem) => {
            if (isFilteredByCountryCode(elem.localBitFlag)) {
                return;
            }
            townNpcGlobalCmsCache[elem.id] = elem;
        });
    }
    return townNpcGlobalCmsCache;
}
exports.getTownNpcGlobalCms = getTownNpcGlobalCms;
let oceanNpcCmsCache;
function getOceanNpcCms() {
    if (!oceanNpcCmsCache) {
        oceanNpcCmsCache = {};
        lodash_1.default.forOwn(_1.default.OceanNpc, (elem) => {
            if (isFilteredByCountryCode(elem.localBitFlag)) {
                return;
            }
            oceanNpcCmsCache[elem.id] = elem;
        });
    }
    return oceanNpcCmsCache;
}
exports.getOceanNpcCms = getOceanNpcCms;
let fishCmsCache;
function getFishCms() {
    if (!fishCmsCache) {
        fishCmsCache = {};
        lodash_1.default.forOwn(_1.default.Fish, (elem) => {
            if (isFilteredByCountryCode(elem.localBitFlag)) {
                return;
            }
            fishCmsCache[elem.id] = elem;
        });
    }
    return fishCmsCache;
}
exports.getFishCms = getFishCms;
let bossRaidCmsCache;
function getBossRaidCms() {
    if (!bossRaidCmsCache) {
        bossRaidCmsCache = {};
        lodash_1.default.forOwn(_1.default.BossRaid, (elem) => {
            if (isFilteredByCountryCode(elem.localBitFlag)) {
                return;
            }
            bossRaidCmsCache[elem.id] = elem;
        });
    }
    return bossRaidCmsCache;
}
exports.getBossRaidCms = getBossRaidCms;
let discoveryCmsCache;
function getDiscoveryCms() {
    if (!discoveryCmsCache) {
        discoveryCmsCache = {};
        lodash_1.default.forOwn(_1.default.Discovery, (elem) => {
            // if (isFilteredByCountryCode(elem)) {
            //   return;
            // }
            discoveryCmsCache[elem.id] = elem;
        });
    }
    return discoveryCmsCache;
}
exports.getDiscoveryCms = getDiscoveryCms;
let tradeGoodsCmsCache;
function getTradeGoodsCms() {
    if (!tradeGoodsCmsCache) {
        tradeGoodsCmsCache = {};
        lodash_1.default.forOwn(_1.default.TradeGoods, (elem) => {
            // if (isFilteredByCountryCode(elem)) {
            //   return;
            // }
            tradeGoodsCmsCache[elem.id] = elem;
        });
    }
    return tradeGoodsCmsCache;
}
exports.getTradeGoodsCms = getTradeGoodsCms;
let mateTemplateGroupCmsCache;
function getMateTemplateGroupCms() {
    if (!mateTemplateGroupCmsCache) {
        mateTemplateGroupCmsCache = {};
        lodash_1.default.forOwn(_1.default.MateTemplateGroup, (elem) => {
            // if (isFilteredByCountryCode(elem)) {
            //   return;
            // }
            mateTemplateGroupCmsCache[elem.id] = elem;
        });
    }
    return mateTemplateGroupCmsCache;
}
exports.getMateTemplateGroupCms = getMateTemplateGroupCms;
let worldStateCmsCache;
function getWorldStateCms() {
    if (!worldStateCmsCache) {
        worldStateCmsCache = {};
        lodash_1.default.forOwn(_1.default.WorldState, (elem) => {
            // if (isFilteredByCountryCode(elem)) {
            //   return;
            // }
            worldStateCmsCache[elem.id] = elem;
        });
    }
    return worldStateCmsCache;
}
exports.getWorldStateCms = getWorldStateCms;
// CMS 캐시 초기화 함수 (CMS 재로드 시 호출)
function clearCmsCache() {
    achievementCmsCache = null;
    hotTimeBuffCmsCache = null;
    townNpcGlobalCmsCache = null;
    oceanNpcCmsCache = null;
    fishCmsCache = null;
    bossRaidCmsCache = null;
    discoveryCmsCache = null;
    tradeGoodsCmsCache = null;
    mateTemplateGroupCmsCache = null;
    worldStateCmsCache = null;
    // 기타 캐시들도 초기화
    Object.keys(achievementTargetsCache).forEach((key) => delete achievementTargetsCache[key]);
    townNpcShopGroupCache = null;
    itemDiscoveryCmsIdsCache = null;
    // TradeGoods 관련 캐시들 초기화
    Object.keys(tradeGoodsByCategoryCache).forEach((key) => delete tradeGoodsByCategoryCache[key]);
    unSoldTradeGoodsCache.clear();
    Object.keys(unpopularCandidateTradeGoodsCache).forEach((key) => delete unpopularCandidateTradeGoodsCache[key]);
    Object.keys(tradeGoodsCache).forEach((key) => delete tradeGoodsCache[key]);
    tradeByTownAndTradeGoodsCmsIdCache = null;
    tradeGoodsMarketPriceVolumeBasesCache = null;
    tradeGoodsPricePercentClampCache = null;
    // Discovery 관련 캐시들 초기화
    villageDiscoveryCache = null;
    fishDiscoveryCache = null;
}
exports.clearCmsCache = clearCmsCache;
//# sourceMappingURL=ex.js.map