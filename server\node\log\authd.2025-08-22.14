{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T05:01:28.056Z"}
{"url":"/logout","status":"200","response-time":"8.132","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:01:28.064Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T05:01:28.066Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.765","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:01:28.069Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.534","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:01:34.465Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.410","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:01:39.238Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.407","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:01:44.216Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T05:01:46.958Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:01:46.958Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:01:47.892Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:01:47.894Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:01:47.895Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T05:01:47.896Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T05:01:47.897Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:01:47.897Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:01:47.897Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T05:01:47.897Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:01:47.898Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:01:47.898Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T05:01:47.898Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:01:47.899Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:01:47.899Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:01:47.900Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:01:47.900Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T05:01:47.901Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:01:47.901Z"}
{"environment":"development","type":"authd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:01:50.448Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:02:19.090Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:02:19.380Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:02:19.381Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:02:19.382Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:02:19.382Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:02:23.119Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:02:23.120Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:02:23.120Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:02:23.127Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:02:23.127Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:02:23.141Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:02:23.224Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.245Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.263Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.279Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.296Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.308Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.325Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.342Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.360Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:23.384Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:02:23.467Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:02:23.468Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:02:23.473Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:02:23.565Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:02:23.567Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:02:23.568Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:02:23.568Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:02:23.571Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:02:23.572Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:02:23.572Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:02:23.572Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:02:23.577Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:02:23.578Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:02:23.578Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:02:23.579Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:02:23.580Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:02:23.581Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:02:23.582Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T05:02:24.613Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:24.613Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:24.614Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T05:02:24.614Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T05:02:24.658Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T05:02:24.658Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T05:02:24.670Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T05:02:24.670Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T05:02:24.681Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T05:02:24.681Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T05:02:24.692Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T05:02:24.692Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:24.817Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:24.857Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:24.882Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:24.922Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:24.974Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.017Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.066Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.130Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.162Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T05:02:25.175Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.182Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.268Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.292Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.328Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.346Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.794Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.818Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.837Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.856Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.868Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.882Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.986Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:26.051Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:26.075Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:26.090Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:26.106Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T05:02:26.125Z"}
{"url":"/getWorldStates","status":"200","response-time":"220.283","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:26.128Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:02:26.130Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.005","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:29.076Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.513","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:34.321Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.799","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:39.165Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.288","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:44.383Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.612","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:49.145Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.348","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:54.374Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.514","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:02:59.184Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.675","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:04.430Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.306","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:09.231Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.519","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:14.226Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.841","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:19.162Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.538","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:24.143Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.217","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:29.402Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.371","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:34.180Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.460","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:39.458Z"}
{"body":{"platform":2,"gnidSessionToken":"8047509**********1755839019","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":127722,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T05:03:40.173Z"}
{"_time":"2025-08-22 14:03:40","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":127722,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T05:03:40.192Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T05:03:40.192Z"}
{"url":"/getWorlds","status":"200","response-time":"63.955","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:40.193Z"}
{"body":{"platform":2,"sessionToken":"8047509**********1755839019","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T05:03:40.813Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 14:03:41&endpointip=127.0.0.1&endpointport=0&guid=ecbdde1a2e5047e3b79cdcb49afdebee&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=5d663b44538de74b0636e368d10d8e63","timestamp":"2025-08-22T05:03:41.139Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T05:03:41.515Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8047509**********1755839019","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T05:03:41.525Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T05:03:41.528Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"72b702782c0164895ed37a028457042bebec6c85","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T05:03:41.529Z"}
{"url":"/login","status":"200","response-time":"763.994","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:41.530Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"72b702782c0164895ed37a028457042bebec6c85","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T05:03:41.618Z"}
{"url":"/enterWorld","status":"200","response-time":"6.662","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:41.624Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T05:03:41.748Z"}
{"url":"/getUserNames","status":"200","response-time":"0.866","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:41.748Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T05:03:41.762Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.080","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:03:41.764Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T05:08:26.737Z"}
{"url":"/logout","status":"200","response-time":"6.253","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:08:26.743Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T05:08:26.745Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.324","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:08:26.747Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T05:09:17.694Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:09:17.695Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:09:17.703Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:09:17.704Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:09:17.705Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T05:09:17.706Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T05:09:17.706Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:09:17.707Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:09:17.707Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T05:09:17.707Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:09:17.707Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:09:17.707Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T05:09:17.707Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:09:17.708Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:09:17.708Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:09:17.709Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:09:17.709Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T05:09:17.710Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:09:17.710Z"}
{"environment":"development","type":"authd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:09:21.228Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:09:43.560Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:09:43.726Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:09:43.727Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:09:43.728Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:09:43.728Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:09:47.163Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:09:47.164Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:09:47.164Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:09:47.171Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:09:47.172Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:09:47.186Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:09:47.271Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.292Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.307Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.321Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.336Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.348Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.365Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.382Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.398Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:47.416Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:09:47.501Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:09:47.502Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:09:47.507Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:09:47.596Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:09:47.598Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:09:47.599Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:09:47.599Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:09:47.602Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:09:47.602Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:09:47.602Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:09:47.603Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:09:47.607Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:09:47.608Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:09:47.608Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:09:47.608Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:09:47.609Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:09:47.611Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:09:47.612Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T05:09:48.579Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:48.580Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:48.581Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T05:09:48.581Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T05:09:48.619Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T05:09:48.620Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T05:09:48.630Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T05:09:48.631Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T05:09:48.641Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T05:09:48.641Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T05:09:48.650Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T05:09:48.650Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:48.756Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:48.780Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:48.810Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:48.840Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:48.885Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:48.930Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:48.978Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.029Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.058Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T05:09:49.071Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.077Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.109Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.168Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.190Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.207Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.627Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.647Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.668Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.694Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.710Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.737Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.766Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.812Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.854Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.872Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:49.896Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T05:09:49.918Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:09:49.921Z"}
{"url":"/getWorldStates","status":"200","response-time":"113.461","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:09:51.965Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.109","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:09:56.675Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.631","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:01.884Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.145","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:06.707Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.961","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:11.922Z"}
{"body":{"platform":2,"gnidSessionToken":"8932801**********1755839411","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":41978,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T05:10:12.133Z"}
{"_time":"2025-08-22 14:10:12","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":41978,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T05:10:12.179Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T05:10:12.180Z"}
{"url":"/getWorlds","status":"200","response-time":"89.828","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:12.181Z"}
{"body":{"platform":2,"sessionToken":"8932801**********1755839411","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T05:10:13.930Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 14:10:14&endpointip=127.0.0.1&endpointport=0&guid=40420d7d2d064821821799710f948dbd&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=b0808f63814e027adffa3a52c0c56d66","timestamp":"2025-08-22T05:10:14.308Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T05:10:14.633Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8932801**********1755839411","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T05:10:14.643Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T05:10:14.645Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"85b0a2921a292d5026571e99118e6c9b9b8d5118","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T05:10:14.646Z"}
{"url":"/login","status":"200","response-time":"716.661","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:14.646Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"85b0a2921a292d5026571e99118e6c9b9b8d5118","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T05:10:14.731Z"}
{"url":"/enterWorld","status":"200","response-time":"7.207","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:14.738Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T05:10:14.821Z"}
{"url":"/getUserNames","status":"200","response-time":"1.005","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:14.822Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T05:10:14.835Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"3.562","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:14.838Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.379","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:10:17.021Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T05:24:05.601Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:24:05.601Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:24:06.105Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:24:06.106Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:24:06.107Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T05:24:06.108Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T05:24:06.108Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:24:06.109Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:24:06.109Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T05:24:06.109Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:24:06.109Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:24:06.109Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T05:24:06.110Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:24:06.110Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:24:06.110Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:24:06.111Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:24:06.111Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T05:24:06.111Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:24:06.111Z"}
{"environment":"development","type":"authd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:24:08.997Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:24:36.269Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:24:36.535Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:24:36.536Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:24:36.536Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:24:36.537Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:24:40.219Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:24:40.220Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:24:40.221Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:24:40.227Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:24:40.228Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:24:40.243Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:24:40.327Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.348Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.364Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.375Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.389Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.401Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.418Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.434Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.449Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:40.468Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:24:40.549Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:24:40.549Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:24:40.555Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:24:40.658Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:24:40.660Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:24:40.661Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:24:40.661Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:24:40.664Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:24:40.665Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:24:40.665Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:24:40.665Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:24:40.669Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:24:40.669Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:24:40.670Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:24:40.670Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:24:40.671Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:24:40.673Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:24:40.673Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T05:24:41.621Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:41.622Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:41.623Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T05:24:41.623Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T05:24:41.664Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T05:24:41.664Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T05:24:41.674Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T05:24:41.675Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T05:24:41.684Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T05:24:41.685Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T05:24:41.693Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T05:24:41.693Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:41.804Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:41.828Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:41.858Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:41.890Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:41.942Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:41.983Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.047Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.106Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.140Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T05:24:42.153Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.160Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.198Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.252Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.271Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.291Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.717Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.741Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.763Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.788Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.808Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.829Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.859Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.932Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.976Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:42.997Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:43.014Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T05:24:43.035Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:24:43.037Z"}
{"url":"/getWorldStates","status":"200","response-time":"106.693","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:24:43.289Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.423","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:24:47.925Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.216","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:24:52.899Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.058","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:24:58.125Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.705","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:02.943Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.523","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:08.168Z"}
{"url":"/getWorldStates","status":"200","response-time":"5.891","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:12.997Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.466","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:17.957Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.973","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:22.925Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.256","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:28.155Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.736","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:32.969Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.365","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:38.190Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.455","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:43.015Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.440","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:48.237Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.443","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:53.057Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.702","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:58.270Z"}
{"body":{"platform":2,"gnidSessionToken":"8988630**********1755840357","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":101709,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T05:25:58.383Z"}
{"_time":"2025-08-22 14:25:58","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":101709,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T05:25:58.394Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T05:25:58.395Z"}
{"url":"/getWorlds","status":"200","response-time":"13.316","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:58.395Z"}
{"body":{"platform":2,"sessionToken":"8988630**********1755840357","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T05:25:58.953Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 14:25:59&endpointip=127.0.0.1&endpointport=0&guid=505ab56ae7b1406293de584feb249ceb&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=395f982b71f1351eaeffd2804fb26ba6","timestamp":"2025-08-22T05:25:59.265Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T05:25:59.486Z"}
{"loginDbResult":{"isOnline":1,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8988630**********1755840357","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T05:25:59.496Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T05:25:59.498Z"}
{"accountId":"**********","userId":1000,"lastLobby":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/login user already online","timestamp":"2025-08-22T05:25:59.499Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-22T05:25:59.499Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-22T05:25:59.501Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"76cb67d9fc21a564b9cf0bb9bff1d030b621a7a9","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T05:25:59.502Z"}
{"url":"/login","status":"200","response-time":"596.858","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:59.502Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"76cb67d9fc21a564b9cf0bb9bff1d030b621a7a9","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T05:25:59.580Z"}
{"accountId":"**********","userId":1000,"lastLobbyAppId":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/user is still online","timestamp":"2025-08-22T05:25:59.581Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-22T05:25:59.581Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-22T05:25:59.582Z"}
{"url":"/enterWorld","status":"200","response-time":"8.229","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:59.588Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T05:25:59.684Z"}
{"url":"/getUserNames","status":"200","response-time":"0.869","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:59.685Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T05:25:59.696Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.896","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:25:59.697Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T05:39:04.505Z"}
{"url":"/logout","status":"200","response-time":"6.610","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:39:04.511Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T05:39:04.513Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.713","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:39:04.515Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T05:39:48.191Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:39:48.192Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:39:48.677Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:39:48.678Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:39:48.679Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T05:39:48.680Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T05:39:48.680Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:39:48.681Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:39:48.681Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T05:39:48.681Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:39:48.681Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:39:48.681Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T05:39:48.682Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:39:48.682Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:39:48.682Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:39:48.683Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:39:48.683Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T05:39:48.683Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:39:48.683Z"}
{"environment":"development","type":"authd","gitCommitHash":"3e628d45a9f","gitCommitMessage":"UWO FGT Survey 기능 추가(테스트용)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-22T14:28:39+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:39:51.321Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:40:19.815Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:40:20.078Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:40:20.079Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:40:20.080Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:40:20.080Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:40:23.967Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:40:23.968Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:40:23.968Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:40:23.975Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:40:23.976Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:40:23.991Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:40:24.083Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.108Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.124Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.135Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.149Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.160Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.176Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.191Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.206Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:24.225Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:40:24.299Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:40:24.300Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:40:24.306Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:40:24.399Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:40:24.402Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:40:24.402Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:40:24.403Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:40:24.405Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:40:24.406Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:40:24.406Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:40:24.406Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:40:24.410Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:40:24.411Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:40:24.411Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:40:24.412Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:40:24.413Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:40:24.415Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:40:24.415Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T05:40:25.357Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:25.357Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:25.358Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T05:40:25.358Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T05:40:25.394Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T05:40:25.394Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T05:40:25.405Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T05:40:25.405Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T05:40:25.414Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T05:40:25.414Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T05:40:25.424Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T05:40:25.424Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.539Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.577Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.600Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.639Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.693Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.737Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.794Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.854Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.889Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T05:40:25.902Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.908Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.979Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.998Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.020Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.038Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.470Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.491Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.518Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.554Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.568Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.586Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.616Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.665Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.726Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.750Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:26.769Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T05:40:26.793Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:40:26.796Z"}
{"url":"/getWorldStates","status":"200","response-time":"100.222","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:27.114Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.026","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:29.753Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.280","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:34.986Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.472","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:39.806Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.968","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:45.031Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.290","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:49.853Z"}
{"body":{"platform":2,"gnidSessionToken":"8237257**********1755841253","revision":"3e628d45a9fda1486b49064d9e94580f3888e40a","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":59994,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T05:40:54.643Z"}
{"_time":"2025-08-22 14:40:54","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":59994,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T05:40:54.659Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T05:40:54.659Z"}
{"url":"/getWorlds","status":"200","response-time":"65.795","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:54.660Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.889","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:54.835Z"}
{"body":{"platform":2,"sessionToken":"8237257**********1755841253","revision":"3e628d45a9fda1486b49064d9e94580f3888e40a","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T05:40:55.503Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 14:40:55&endpointip=127.0.0.1&endpointport=0&guid=10d4e5e3779140138fd1815e98cb1c33&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=f719babae1dd711843f776e6b6e68891","timestamp":"2025-08-22T05:40:55.846Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T05:40:59.604Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8237257**********1755841253","revision":"3e628d45a9fda1486b49064d9e94580f3888e40a","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T05:40:59.616Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T05:40:59.618Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"9a686bbf3b1ab4e81c23bdba8beea31aa2230c1f","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T05:40:59.619Z"}
{"url":"/login","status":"200","response-time":"4158.483","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:59.619Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"9a686bbf3b1ab4e81c23bdba8beea31aa2230c1f","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T05:40:59.694Z"}
{"url":"/enterWorld","status":"200","response-time":"6.657","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:59.701Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.290","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:59.794Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T05:40:59.805Z"}
{"url":"/getUserNames","status":"200","response-time":"0.897","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:59.806Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T05:40:59.818Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.948","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T05:40:59.820Z"}
