// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import _ from 'lodash';
import morgan from 'morgan';
import path from 'path';
import { Container, Service } from 'typedi';

import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import stoppable from 'stoppable';
import * as Sentry from '@sentry/node';
import { GameGuardManager } from './gameGuard/gameGuardManager';
import { GameGuardFfi } from './gameGuard/gameGuardFfi';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------
morgan.token('errMsg', (req: any, res: any) => res.errMsg);
morgan.token('userId', (req: any, res: any) => req.body.userId);

let reqCounter = 0;
let hundredCounter = 0;

function ffiReqLog(tokens, req, res) {
  if (res.statusCode !== 200) {
    mlog.warn('ffid-req', {
      url: tokens['url'](req, res),
      status: tokens['status'](req, res),
      'response-time': tokens['response-time'](req, res),
    });
  }

  if (++reqCounter === 100) {
    reqCounter = 0;
    ++hundredCounter;

    mlog.info('100 requests processed.', {
      total: hundredCounter * 100,
    });
  }

  return null;
}

// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------

// Main express app.
const ffiApp = express();

ffiApp.disable('x-powered-by');
ffiApp.disable('etag');
ffiApp.disable('content-type');
ffiApp.use(morgan(ffiReqLog));
ffiApp.use(bodyParser.json());
ffiApp.use(expressError.middleware);

const ffiServer = stoppable(http.createServer(ffiApp));
ffiServer.keepAliveTimeout = 0;

let stopping = false;

// ----------------------------------------------------------------------------
// Service
// ----------------------------------------------------------------------------
@Service()
export class FfiService {
  // GameGuard.
  gameGuardFfi: GameGuardFfi;
  gameGuardManager: GameGuardManager;

  async init() {
    await startTogglet();

    // Init GameGuard FFI.
    this.gameGuardFfi = new GameGuardFfi();
    if (!this.gameGuardFfi.init()) {
      throw Error('Faled to initialize GameGuard FFI');
    }

    // Init GameGuard manager.
    this.gameGuardManager = new GameGuardManager();

    // Start API server.
    const bindAddress = mconf.apiService.bindAddress;
    const port = mconf.apiService.port;

    mutil.registerHealthCheck(ffiApp);
    await dirAsApi.register(ffiApp, path.join(__dirname, 'api'));

    ffiServer.listen(port, bindAddress, () => {
      mlog.info('Start listening ...', { bindAddress, port });
    });

    // Stress test in 3 seconds.
    // setTimeout(() => {
    //   this.gameGuardFfi.STRESSTEST();
    // }, 3000);
  }

  async destroy() {
    this.gameGuardManager.destroy();
    this.gameGuardFfi.destroy();

    stopTogglet();
  }
}

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

async function closeServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    ffiServer.stop((err) => {
      if (err) return reject(err);
      resolve();
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');
    await closeServer();

    const app = Container.get(FfiService);
    await app.destroy();

    mlog.info('server stopped');

    process.exitCode = 0;
  } catch (error) {
    mlog.exception('graceful shutdown failed', error);
    process.exit(1);
  }
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export async function start() {
  try {
    await mhttp.configd.registerInstance(undefined, mconf.appInstanceId, mconf.hostname);

    mutil.initSentry();

    const app = Container.get(FfiService);
    await app.init();
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    process.exit(1);
  }
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await stopServer();
}
