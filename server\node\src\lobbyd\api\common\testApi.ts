// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { RequestAs, Response } from '../../../motiflib/expressEx';
import mlog from '../../../motiflib/mlog';
import { Container } from 'typedi';
import { UserManager } from '../../userManager';
import mconf from '../../../motiflib/mconf';
import { ManufacturePointChange, UserManufacture } from '../../userManufacture';
import * as mutil from '../../../motiflib/mutil';
import { UserChangeSpec } from '../../UserChangeTask/commonChangeSpec';
import { CHANGE_TASK_REASON, CHANGE_TASK_RESULT, Changes, TryData, UserChangeTask } from '../../UserChangeTask/userChangeTask';
import { User } from '../../user';
import { opAddManufacturePoint, opApplyManufacturePointChange } from '../../UserChangeTask/userChangeOperator';

interface RequestBody {
  testParam?: string;
  userId?: number;
}

interface ResponseBody {
  success: boolean;
  message: string;
}

export = async (req: RequestAs<RequestBody>, res: Response) => {
  mlog.info('api/common/testApi req -', req.body);

  if (mconf.isDev == false) {
    return;
  }

  const { testParam, userId } = req.body;
  const curTimeUtc = mutil.curTimeUtc();

  const userManager = Container.get(UserManager);
  const user = userManager.getUserByUserId(userId);

  const [hasPoint, pointChange] = user.userManufacture.buildPointChange(curTimeUtc, parseInt(testParam));

  const changeTask = new UserChangeTask(
    user,
    CHANGE_TASK_REASON.USE_ITEM,
    new ManufacturePointSpec(pointChange, parseInt(testParam))
  );

  const trySpecResult = changeTask.trySpec();

  changeTask.apply().then(async (sync) => {
    sync;
  });
  

  const sync = user.userManufacture.applyPointChange(pointChange, {
    user: user,
    rsn: 'test_rsn',
    add_rsn: 'test_add_rsn',
  });
  const syncAll = user.userManufacture.getSyncData();
  
  const responseBody: ResponseBody = {
    success: true,
    message: '',
  };

  res.json(responseBody);
}

class ManufacturePointSpec implements UserChangeSpec {
  constructor(
    private input: ManufacturePointChange,
    private amount: number
  ) {
    this.input = input;
    this.amount = amount;
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    let result: CHANGE_TASK_RESULT = CHANGE_TASK_RESULT.OK;

    let ret = opAddManufacturePoint(
      user,
      tryData,
      changes,
      this.amount
    );

    if (ret > CHANGE_TASK_RESULT.OK_MAX) {
      return ret;
    }

    // let ret = opApplyManufacturePointChange(
    //   user,
    //   tryData,
    //   changes,
    //   this.input
    // );

    // if (ret > CHANGE_TASK_RESULT.OK_MAX) {
    //   return ret;
    // }

    return CHANGE_TASK_RESULT.OK;
  }
}



