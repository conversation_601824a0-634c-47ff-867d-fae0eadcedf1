// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { LobbyService } from '../../server';
import { Resp, Sync } from '../../type/sync';
import tuExpandInventorySlot from '../../../mysqllib/txn/tuExpandInventorySlot';
import UserPoints, { CashPayment, PointChange, PointConsumptionCostParam } from '../../userPoints';
import { ItemChange } from '../../userInven';
import { MError, MErrorCode } from '../../../motiflib/merror';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { ITEM_TYPE } from '../../../cms/itemDesc';
import { ClientPacketHandler } from '../index';
import mlog from '../../../motiflib/mlog';
import { SlotExpansionDesc } from '../../../cms/SlotExpansionDesc';
import { PrData } from '../../../motiflib/gameLog';
import * as mutil from '../../../motiflib/mutil';
import { AccumulateParam } from '../../userAchievement';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// 1. 쿠폰사용이 체크(bNotUseCoupon 이 false) 되어있고  쿠폰이 있으면 먼저 소모
// [UI이슈로 기획보류] 2. 하나의 슬롯을 구매시 쿠폰이 부족한 경우 부족한 쿠폰만큼 포인트 소모
// 3. 쿠폰이 없거나 사용불가한 슬롯은 포인트로 소모
// ----------------------------------------------------------------------------

const rsn = 'expand_inventory_slot';
const add_rsn = null;

interface CouponCostParam {
  cmsId?: number;
  toPoint?: number;
  count?: number[];
}

interface RequestBody {
  inventroyType: number;
  slotNum: number;
  couponNum?: number;
  bPermitExchange?: boolean;
  bNotUseCoupon?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_ExpandInventorySlot implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;

    const { inventroyType, slotNum, couponNum, bPermitExchange, bNotUseCoupon } = body;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    if (mutil.isNotANumber(slotNum) || slotNum <= 0) {
      throw new MError('invalid-slotNum', MErrorCode.INVALID_REQ_BODY_EXPAND_INVEN_SLOT, {
        body,
      });
    }

    let newSlot = user.userInven.getSlotExpansion(inventroyType);
    const old_slot_cnt = newSlot;
    let couponCostForGLog = 0;
    let pointChanges: PointChange[];
    let cashPayments: CashPayment[];

    let userCouponChange: ItemChange;
    let { needCouponChange, needPointChanges } = getExpandSlotCostEx(
      newSlot,
      inventroyType,
      slotNum
    );

    mlog.verbose('[expandInventorySlot] getExpandSlotCostEx result', {
      userId: user.userId,
      inventroyType,
      curSlot: newSlot,
      slotNum,
      couponNum,
      needCouponChange,
      needPointChanges,
    });

    let tempPointChanges: { [cmsId: number]: PointConsumptionCostParam } = {};

    newSlot += slotNum;
    let curSlotStep = 0;
    if (!bNotUseCoupon && needCouponChange.cmsId) {
      const curCnt = user.userInven.itemInven.getCount(needCouponChange.cmsId);
      if (curCnt > 0) {
        mlog.verbose('[expandInventorySlot] coupon start', {
          userId: user.userId,
          curCnt,
        });

        let needCouponCnt = 0;
        // 쿠폰으로 커버할수있는 단계까지 num만큼순회한다
        for (; curSlotStep < slotNum; curSlotStep++) {
          if (needCouponChange.count.length <= curSlotStep) {
            mlog.verbose('[expandInventorySlot] coupon-count-len-end', {
              userId: user.userId,
              curSlotStep,
            });
            break;
          }

          const need = needCouponChange.count[curSlotStep];
          if (needCouponCnt + need > curCnt) {
            break;

            // [기획보류로 임시주석처리] 쿠폰이 부족한 시점에서 중단하고 해당 단계의 부족분은 포인트로 환산해서충당한다
            // const leftCnt = needCouponCnt + need - curCnt;
            // needCouponCnt = curCnt;

            // let pc: PointConsumptionCostParam = {
            //   cmsId: needPointChanges[curSlotStep].cmsId,
            //   cost: leftCnt * needCouponChange.toPoint,
            // };
            // tempPointChanges[pc.cmsId] = pc;

            // mlog.verbose('[expandInventorySlot] coupon-count-zero', {
            //   userId: user.userId,
            //   curSlotStep,
            //   needCouponCnt,
            //   tempPointChanges,
            // });

            // break;
          }

          needCouponCnt += need;

          mlog.info('[expandInventorySlot] coupon-count-added', {
            userId: user.userId,
            inventroyType,
            curSlotStep,
            needCouponCnt,
            bNotUseCoupon,
          });
        }

        if (needCouponCnt > 0) {
          // [todo] 클라에서 계산한 쿠폰소모량과 교차검증
          if (couponNum !== needCouponCnt) {
            throw new MError('invalid-coupon-num', MErrorCode.INVALID_REQ_BODY_EXPAND_INVEN_SLOT, {
              body,
              curCnt,
              needCouponCnt,
            });
          }

          userCouponChange = user.userInven.itemInven.buildItemChange(
            needCouponChange.cmsId,
            -needCouponCnt,
            true
          );
          couponCostForGLog += needCouponCnt;
        }
      }
    }

    // 이후단계가 남은경우 포인트로 계산한다
    for (; curSlotStep < slotNum; curSlotStep++) {
      const elem = needPointChanges[curSlotStep];

      // 남은 단계는 모두 포인트로 커버되어야한다
      if (!elem) {
        throw new MError(
          'invalid-inventory-type-or-can-not-expand-more-step',
          MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
          {
            inventroyType,
            curSlotStep,
            slotNum,
          }
        );
      }

      if (!tempPointChanges[elem.cmsId]) {
        tempPointChanges[elem.cmsId] = elem;
      } else {
        tempPointChanges[elem.cmsId].cost += elem.cost;
      }

      mlog.info('[expandInventorySlot] point-added', {
        userId: user.userId,
        inventroyType,
        curSlotStep,
        tempPointChanges,
        bNotUseCoupon,
      });
    }

    let finalNeedPointChanges = Object.values(tempPointChanges);
    if (finalNeedPointChanges && finalNeedPointChanges.length > 0) {
      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        finalNeedPointChanges,
        bPermitExchange,
        { itemId: rsn },
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;
    }

    const sync: Sync = {};

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return user.userPoints
      .tryConsumeCashs(cashPayments, sync, user, { user, rsn, add_rsn, exchangeHash })
      .then(() => {
        return tuExpandInventorySlot(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          inventroyType,
          newSlot,
          userCouponChange,
          pointChanges
        );
      })

      .then(() => {
        const accums: AccumulateParam[] = [];

        user.userInven.setSlotExpansion(inventroyType, newSlot);

        _.merge<Sync, Sync>(sync, {
          add: {
            slotExpansions: user.userInven.getSlotExpansions(),
          },
        });

        if (userCouponChange) {
          _.merge<Sync, Sync>(
            sync,
            user.userInven.itemInven.applyItemChange(userCouponChange, accums, {
              user,
              rsn,
              add_rsn,
            })
          );
        }

        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
        );

        const pr_data: PrData[] = [];
        if (finalNeedPointChanges) {
          finalNeedPointChanges.forEach((elem) => {
            pr_data.push({ type: elem.cmsId, amt: elem.cost });
          });
        }
        let couponItemCms;
        if (couponCostForGLog > 0) {
          couponItemCms = cms.Item[needCouponChange.cmsId];
        }
        user.glog('storage_expand', {
          rsn,
          add_rsn,
          type: cmsEx.INVENTORY_TYPE[inventroyType],
          old_slot_cnt,
          cur_slot_cnt: newSlot,
          pr_data: pr_data.length > 0 ? pr_data : null,
          cost_data:
            couponCostForGLog > 0
              ? [
                  {
                    type: ITEM_TYPE[couponItemCms.type],
                    id: couponItemCms.id,
                    amt: couponCostForGLog,
                  },
                ]
              : null,
          exchange_hash: exchangeHash,
        });

        if (accums.length > 0) {
          return user.userAchievement.accumulate(accums, user, sync, { user, rsn, add_rsn });
        }
      })
      .then(() => {
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }
}

// --------------------------------------------------------------------------
function getExpandSlotCostEx(
  newSlot: number,
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number
): {
  needCouponChange: CouponCostParam;
  needPointChanges: PointConsumptionCostParam[];
} {
  let needCouponChange: CouponCostParam = {};
  let needPointChanges: PointConsumptionCostParam[] = [];

  for (let i = 0; i < addSlotCount; i++) {
    newSlot++;
    const slotExpandCms = cms.SlotExpansion[newSlot];
    if (!slotExpandCms) {
      throw new MError(
        'invalid-inventory-type-or-can-not-expand-more-0',
        MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
        {
          inventoryType,
          addSlotCount,
        }
      );
    }
    switch (inventoryType) {
      case cmsEx.INVENTORY_TYPE.MATE_EQUIPMENT:
        _buildCostOfMateEquipment(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needCouponChange,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.USER_ITEM:
        _buildCostOfItemSlot(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needCouponChange,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.CAPTURED:
        _buildCostOfTowSlot(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needCouponChange,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.SHIP_SLOT_ITEM:
        _buildCostOfShipPartSlot(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needCouponChange,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.SHIP_DOCK:
        _buildCostOfShipDockSlot(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needCouponChange,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.SHIP_BUILDING:
        _buildCostOfShipBuildingSlot(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needCouponChange,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.GUILD_CRATE:
        _buildCostOfGuildCraftSlot(inventoryType, addSlotCount, slotExpandCms, needPointChanges);
        break;
      case cmsEx.INVENTORY_TYPE.PRESET:
        _buildCostOfPresetSlot(inventoryType, addSlotCount, slotExpandCms, needPointChanges);
        break;
      case cmsEx.INVENTORY_TYPE.SAIL_WAYPOINT:
        _buildCostOfSailWaypointSlot(inventoryType, addSlotCount, slotExpandCms, needPointChanges);
        break;
      case cmsEx.INVENTORY_TYPE.MATE_EQUIPMENT_COSTUME:
        _buildCostOfMateEquipmentCostume(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needCouponChange,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.GUILD_SYNTHESIS:
        _buildCostOfGuildSynthesisSlot(
          inventoryType,
          addSlotCount,
          slotExpandCms,
          needPointChanges
        );
        break;
      case cmsEx.INVENTORY_TYPE.DISPATCH_PRESET:
        _buildCosOfDispatchPresetSlot(inventoryType, addSlotCount, slotExpandCms, needPointChanges);
        break;
      default:
        throw new MError('invalid-inven-type', MErrorCode.INVALID_REQ_BODY_EXPAND_INVEN_SLOT, {
          inventoryType,
        });
    }
  }

  return { needCouponChange, needPointChanges };
}

// --------------------------------------------------------------------------
function _buildCostOfMateEquipment(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needCouponChange: CouponCostParam,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (
    !cms.Const.CEquipSlotExpansionCouponId ||
    !cms.Const.CEquipSlotCouponToPoint ||
    !slotExpandCms.cEquipSlotPoint
  ) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }
  if (!needCouponChange.cmsId) {
    _.merge<CouponCostParam, CouponCostParam>(needCouponChange, {
      cmsId: cms.Const.CEquipSlotExpansionCouponId.value,
      toPoint: cms.Const.CEquipSlotCouponToPoint.value,
      count: [],
    });
  }
  if (slotExpandCms.cEquipSlotCouponVal) {
    needCouponChange.count.push(slotExpandCms.cEquipSlotCouponVal);
  }

  needPointChanges.push({
    cmsId: slotExpandCms.cEquipSlotPoint.Id,
    cost: slotExpandCms.cEquipSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfItemSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needCouponChange: CouponCostParam,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (
    !cms.Const.ItemSlotExpansionCouponId ||
    !cms.Const.ItemSlotCouponToPoint ||
    !slotExpandCms.itemSlotPoint
  ) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }
  if (!needCouponChange.cmsId) {
    _.merge<CouponCostParam, CouponCostParam>(needCouponChange, {
      cmsId: cms.Const.ItemSlotExpansionCouponId.value,
      toPoint: cms.Const.ItemSlotCouponToPoint.value,
      count: [],
    });
  }

  if (slotExpandCms.itemSlotCouponVal) {
    needCouponChange.count.push(slotExpandCms.itemSlotCouponVal);
  }

  needPointChanges.push({
    cmsId: slotExpandCms.itemSlotPoint.Id,
    cost: slotExpandCms.itemSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfTowSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needCouponChange: CouponCostParam,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (
    !cms.Const.TowSlotExpansionCouponId ||
    !cms.Const.TowSlotCouponToPoint ||
    !slotExpandCms.towSlotPoint
  ) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }
  if (!needCouponChange.cmsId) {
    _.merge<CouponCostParam, CouponCostParam>(needCouponChange, {
      cmsId: cms.Const.TowSlotExpansionCouponId.value,
      toPoint: cms.Const.TowSlotCouponToPoint.value,
      count: [],
    });
  }
  if (slotExpandCms.towSlotCouponVal) {
    needCouponChange.count.push(slotExpandCms.towSlotCouponVal);
  }

  needPointChanges.push({
    cmsId: slotExpandCms.towSlotPoint.Id,
    cost: slotExpandCms.towSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfShipPartSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needCouponChange: CouponCostParam,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (
    !cms.Const.ShipPartSlotExpansionCouponId ||
    !cms.Const.ShipPartSlotCouponToPoint ||
    !slotExpandCms.shipPartSlotPoint
  ) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }
  if (!needCouponChange.cmsId) {
    _.merge<CouponCostParam, CouponCostParam>(needCouponChange, {
      cmsId: cms.Const.ShipPartSlotExpansionCouponId.value,
      toPoint: cms.Const.ShipPartSlotCouponToPoint.value,
      count: [],
    });
  }
  if (slotExpandCms.shipPartSlotCouponVal) {
    needCouponChange.count.push(slotExpandCms.shipPartSlotCouponVal);
  }

  needPointChanges.push({
    cmsId: slotExpandCms.shipPartSlotPoint.Id,
    cost: slotExpandCms.shipPartSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfShipDockSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needCouponChange: CouponCostParam,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (
    !cms.Const.ShipDockSlotExpansionCouponId ||
    !cms.Const.ShipDockSlotCouponToPoint ||
    !slotExpandCms.shipDockSlotPoint
  ) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }
  if (!needCouponChange.cmsId) {
    _.merge<CouponCostParam, CouponCostParam>(needCouponChange, {
      cmsId: cms.Const.ShipDockSlotExpansionCouponId.value,
      toPoint: cms.Const.ShipDockSlotCouponToPoint.value,
      count: [],
    });
  }
  if (slotExpandCms.shipDockSlotCouponVal) {
    needCouponChange.count.push(slotExpandCms.shipDockSlotCouponVal);
  }

  needPointChanges.push({
    cmsId: slotExpandCms.shipDockSlotPoint.Id,
    cost: slotExpandCms.shipDockSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfShipBuildingSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needCouponChange: CouponCostParam,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (
    !cms.Const.ShipBuildingSlotExpansionCouponId ||
    !cms.Const.ShipBuildingSlotCouponToPoint ||
    !slotExpandCms.shipBuildingSlotPoint
  ) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }
  if (!needCouponChange.cmsId) {
    _.merge<CouponCostParam, CouponCostParam>(needCouponChange, {
      cmsId: cms.Const.ShipBuildingSlotExpansionCouponId.value,
      toPoint: cms.Const.ShipBuildingSlotCouponToPoint.value,
      count: [],
    });
  }
  if (slotExpandCms.shipBuildingSlotCouponVal) {
    needCouponChange.count.push(slotExpandCms.shipBuildingSlotCouponVal);
  }

  needPointChanges.push({
    cmsId: slotExpandCms.shipBuildingSlotPoint.Id,
    cost: slotExpandCms.shipBuildingSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfGuildCraftSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (!slotExpandCms.guildCraftSlotPoint) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }

  needPointChanges.push({
    cmsId: slotExpandCms.guildCraftSlotPoint.Id,
    cost: slotExpandCms.guildCraftSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfGuildSynthesisSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (!slotExpandCms.guildSynthesisSlotPoint) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }

  needPointChanges.push({
    cmsId: slotExpandCms.guildCraftSlotPoint.Id,
    cost: slotExpandCms.guildCraftSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfPresetSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (!slotExpandCms.presetSlotPoint) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }

  needPointChanges.push({
    cmsId: slotExpandCms.presetSlotPoint.Id,
    cost: slotExpandCms.presetSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfSailWaypointSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (!slotExpandCms.wayPointSlotPoint) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }

  needPointChanges.push({
    cmsId: slotExpandCms.wayPointSlotPoint.Id,
    cost: slotExpandCms.wayPointSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCostOfMateEquipmentCostume(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needCouponChange: CouponCostParam,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (
    !cms.Const.CCostumeSlotExpansionCouponId ||
    !cms.Const.CCostumeSlotCouponToPoint ||
    !slotExpandCms.cCostumeSlotPoint
  ) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }
  if (!needCouponChange.cmsId) {
    _.merge<CouponCostParam, CouponCostParam>(needCouponChange, {
      cmsId: cms.Const.CCostumeSlotExpansionCouponId.value,
      toPoint: cms.Const.CCostumeSlotCouponToPoint.value,
      count: [],
    });
  }
  if (slotExpandCms.cCostumeSlotCouponVal) {
    needCouponChange.count.push(slotExpandCms.cCostumeSlotCouponVal);
  }

  needPointChanges.push({
    cmsId: slotExpandCms.cCostumeSlotPoint.Id,
    cost: slotExpandCms.cCostumeSlotPoint.Value,
  });
}

// --------------------------------------------------------------------------
function _buildCosOfDispatchPresetSlot(
  inventoryType: cmsEx.INVENTORY_TYPE,
  addSlotCount: number,
  slotExpandCms: SlotExpansionDesc,
  needPointChanges: PointConsumptionCostParam[]
) {
  if (!slotExpandCms.dispatchPresetSlotPoint) {
    throw new MError(
      'invalid-inventory-type-or-can-not-expand-more',
      MErrorCode.INVALID_SLOT_EXPANSION_CMS_ID,
      {
        inventoryType,
        addSlotCount,
      }
    );
  }

  needPointChanges.push({
    cmsId: slotExpandCms.dispatchPresetSlotPoint.Id,
    cost: slotExpandCms.dispatchPresetSlotPoint.Value,
  });
}
