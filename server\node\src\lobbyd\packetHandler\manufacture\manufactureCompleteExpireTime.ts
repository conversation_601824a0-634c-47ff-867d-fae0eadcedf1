// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';
import cms from '../../../cms';
import * as formula from '../../../formula';
import * as mutil from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { Sync, Resp } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import UserPoints, { PointAndCashChanges, PointConsumptionCostParam } from '../../userPoints';
import { ManufactureProgress } from '../../userManufacture';
import { MakeManufactureTimeReductionData } from '../../../motiflib/gameLog';
import tuManufactureCompleteExpireTime from '../../../mysqllib/txn/tuManufactureCompleteExpireTime';

// ----------------------------------------------------------------------------
// 제조 즉시 완료권 사용
// ----------------------------------------------------------------------------

const rsn = 'manufacture_complete_expire_time';
const add_rsn = null;

interface RequestBody {
  roomCmsId: number;
  slot: number;
  bPermitExchange?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Manufacture_Complete_Expire_Time implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { roomCmsId, slot, bPermitExchange } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // slot을 roomCmsId로 사용하여 해당 제조실의 진행 정보를 가져옴
    const roomProgress: ManufactureProgress = user.userManufacture.getRoom(roomCmsId);

    if (!roomProgress) {
      throw new MError(
        'not-in-progress-manufacture-slot',
        MErrorCode.NOT_IN_PROGRESS_SYNTHESIS_SLOT,
        {
          userId: user.userId,
          slot,
          body,
        }
      );
    }

    // Extract the first slot data from the map
    const slotData = roomProgress[slot];
    if (!slotData) {
      throw new MError(
        'not-in-progress-manufacture-slot',
        MErrorCode.NOT_IN_PROGRESS_SYNTHESIS_SLOT,
        {
          userId: user.userId,
          slot,
          body,
        }
      );
    }

    // 이미 제조가 완료된 시간이면 실패처리.
    const curTimeUtc: number = mutil.curTimeUtc();
    if (curTimeUtc >= slotData.completionTimeUtc + 10) {
      throw new MError(
        'manufacture-already-has-been-completed',
        MErrorCode.GUILD_SYNTHESIS_ALREADY_HAS_BEEN_COMPLETED,
        {
          body,
          curTimeUtc,
          slotData,
        }
      );
    }

    // 재화 소모
    const remainingTimeSec = Math.max(slotData.completionTimeUtc - curTimeUtc, 0);
    const cost = formula.CalcImmediateCompletionTimeCost(remainingTimeSec);
    const pointCost: PointConsumptionCostParam | undefined =
      cost > 0 // 비용이 없는 경우가 있음
        ? { cmsId: cms.Const.TimeCostPerMinPointType.value, cost }
        : undefined;
    let pcChanges: PointAndCashChanges = {
      pointChanges: [],
      cashPayments: [],
    };
    if (pointCost) {
      pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [pointCost],
        bPermitExchange,
        { itemId: rsn },
        true
      );
    }

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const sync: Sync = {};

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return user.userPoints
      .tryConsumeCashs(pcChanges.cashPayments, sync, user, { user, rsn, add_rsn, exchangeHash })
      .then(() => {
        // Create a modified version of slotData with updated completion time
        const updatedSlotData = {
          ...slotData,
          completionTimeUtc: curTimeUtc
        };
        
        return tuManufactureCompleteExpireTime(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          { [slot]: updatedSlotData }, // ManufactureProgress object with updated completion time
          roomCmsId,
          slot,
          pcChanges.pointChanges,
          []
        );
      })
      .then(() => {
        const { product_id, product_name } = this.buildManufactureCommonLog(
          slotData.recipeId
        );

        user.glog(
          'time_reduction',
          MakeManufactureTimeReductionData({
            rsn,
            add_rsn,

            old_duration: Math.max(remainingTimeSec, 0),
            cur_duration: 0,
            pr_data: pointCost ? [{ type: pointCost.cmsId, amt: pointCost.cost }] : null,
            cost_data: null,
            manufacture_data: {
              manufacture_slot_idx: slot,
              product_id,
              product_name,
            },
            exchange_hash: exchangeHash,
          })
        );

        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyPointChanges(pcChanges.pointChanges, { user, rsn, add_rsn })
        );

        slotData.completionTimeUtc = curTimeUtc;

        _.merge<Sync, Sync>(sync, {
          add: {
            manufacture: {
              roomInfo: {
                [roomCmsId]: {
                  [slot]: {
                    slot,
                    recipeId: slotData.recipeId,
                    startTimeUtc: slotData.startTimeUtc,
                    completionTimeUtc: slotData.completionTimeUtc,
                    resultType: slotData.resultType,
                    mateCmsIds: slotData.mateCmsIds,
                    successRate: slotData.successRate,
                    greatSuccessRate: slotData.greatSuccessRate,
                    extra: slotData.extra,
                  },
                }
              },
            },
          },
        });

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }

  // --------------------------------------------------------------------------
  private buildManufactureCommonLog(recipeId: number): { product_id: number; product_name: string } {
    const recipeCms = cms.ManufactureRecipe[recipeId];
    if (!recipeCms) {
      return {
        product_id: recipeId,
        product_name: `Unknown Recipe ${recipeId}`,
      };
    }

    // 레시피의 결과 아이템 정보를 가져오는 로직
    // 실제 구현에서는 레시피의 결과 아이템을 확인해야 함
    return {
      product_id: recipeId,
      product_name: `Recipe ${recipeId}`,
    };
  }
}