// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';

import { User } from '../../user';
import { CONNECTION_STATE, CPacket } from '../../userConnection';
import { Sync, Resp } from '../../type/sync';
import { LobbyService } from '../../server';
import { MError, MErrorCode } from '../../../motiflib/merror';
import cms from '../../../cms';
import mhttp from '../../../motiflib/mhttp';
import { Client<PERSON>acketHandler } from '../index';
import _ from 'lodash';
import { OpenedFleetPresetHelper } from '../../openedFleetPresetHelper';
import { FleetPresetInfo } from '../../userFleetPreset';
import tuFleetPresetUpdate from '../../../mysqllib/txn/tuFleetPresetUpdate';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'change_fleet_preset_name';
const add_rsn = null;

interface RequestBody {
  presetId: number;
  presetName: string;
}

// ----------------------------------------------------------------------------
export class Cph_Common_ChangeFleetPresetName implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const { presetId, presetName } = packet.bodyObj;
    const { userDbConnPoolMgr, userCacheRedis } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    user.userFleetPreset.ensureSlotOpened(user, presetId, rsn);

    if (
      !presetName ||
      presetName.length < cms.Const.PresetNameMinimum.value ||
      presetName.length > cms.Const.PresetNameMaximum.value ||
      presetName === user.userFleetPreset.getFleetPresetName(presetId)
    ) {
      throw new MError('invalid-name', MErrorCode.FLEET_PRESET_INVALID_REQ_BODY_PRESET_NAME, {
        presetName,
      });
    }

    const newFleetPresetInfo: FleetPresetInfo = user.userFleetPreset.cloneFleetPresetInfo(presetId);
    newFleetPresetInfo.presetName = presetName;

    const sync: Sync = {};
    return mhttp.lgd
      .hasBadWord(presetName)
      .then((bHas) => {
        if (bHas) {
          throw new MError('has-bad-word', MErrorCode.FLEET_PRESET_HAS_BAD_WORD, {
            presetName,
            oldName: user.userName,
          });
        }
        return tuFleetPresetUpdate(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          newFleetPresetInfo
        );
      })
      .then(() => {
        user.userFleetPreset.applyFleetPresetInfo(newFleetPresetInfo);

        _.merge<Sync, Sync>(sync, {
          add: {
            fleetPresets: {
              [presetId]: newFleetPresetInfo,
            },
          },
        });
        return OpenedFleetPresetHelper.buildOpenedFleetPresetName(
          user,
          userCacheRedis,
          newFleetPresetInfo.presetId,
          newFleetPresetInfo.presetName
        );
      })
      .then((openedFleetPreset) => {
        return OpenedFleetPresetHelper.saveOpenedFleetPresetsToRedis(
          user,
          userCacheRedis,
          openedFleetPreset
        );
      })

      .then(() => {
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }
}
