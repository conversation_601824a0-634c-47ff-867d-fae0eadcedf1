"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendUpdateAutoSailOptionForServer = exports.sendUpdateAutoSailing = exports.sendApplyWaypointSupply = exports.sendGetUserTownState = exports.sendBattleRewardLeave = exports.sendBattleRewardLoadComplete = exports.sendBattleRewardEnter = exports.sendBattleEnd = exports.sendBattleLoadComplete = exports.sendBattleStart = exports.sendBattleResume = exports.sendEncountEnd = exports.sendEncountUserChoice = exports.sendEndAutoSailing = exports.sendReqOfflineSailingMoveDelegateStart = exports.sendCheatWorldBuffRem = exports.sendOceanVillageEnter = exports.sendCheatTeleportTown = exports.sendTownLoadComplete = exports.sendTownEnter = exports.sendOceanArrive = exports.sendOceanLoadComplete = exports.sendOceanEnter = exports.sendTownDepartDepart = exports.sendTownEnterBuilding = exports.sendMap = void 0;
const proto = __importStar(require("../proto/lobby/proto"));
const ex_1 = require("../cms/ex");
exports.sendMap = new Map();
//----------------------------------------------------------
// request senders
//----------------------------------------------------------
const sendTownEnterBuilding = (bot) => {
    const body = {
        buildingType: ex_1.BUILDING_TYPE.DEPART,
        religionType: 0,
    };
    const packet = {
        seqNum: 0,
        type: proto.Town.ENTER_BUILDING,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendTownEnterBuilding = sendTownEnterBuilding;
//----------------------------------------------------------
const sendTownDepartDepart = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Town.DEPART_DEPART,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendTownDepartDepart = sendTownDepartDepart;
//----------------------------------------------------------
const sendOceanEnter = (bot) => {
    let sailId = 0;
    if (bot.sailId) {
        sailId = bot.sailId;
    }
    const body = {
        sailId,
    };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.ENTER,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendOceanEnter = sendOceanEnter;
//----------------------------------------------------------
const sendOceanLoadComplete = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Ocean.LOAD_COMPLETE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendOceanLoadComplete = sendOceanLoadComplete;
//----------------------------------------------------------
const sendOceanArrive = (bot) => {
    const body = {
        townCmsId: bot.destCmsId,
        bGameOver: false,
    };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.ARRIVE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendOceanArrive = sendOceanArrive;
//----------------------------------------------------------
const sendTownEnter = (bot) => {
    const body = {
        townCmsId: bot.destCmsId,
    };
    const packet = {
        seqNum: 0,
        type: proto.Town.ENTER,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendTownEnter = sendTownEnter;
//----------------------------------------------------------
const sendTownLoadComplete = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Town.LOAD_COMPLETE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendTownLoadComplete = sendTownLoadComplete;
//----------------------------------------------------------
const sendCheatTeleportTown = (bot) => {
    const body = {
    //townCmsId: sailConst.townCmsId,
    };
    const packet = {
        seqNum: 0,
        type: proto.Admin.TELEPORT_TOWN,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendCheatTeleportTown = sendCheatTeleportTown;
//----------------------------------------------------------
const sendOceanVillageEnter = (bot) => {
    const body = {
        villageCmsId: bot.destCmsId,
    };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.VILLAGE_ENTER,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendOceanVillageEnter = sendOceanVillageEnter;
//----------------------------------------------------------
// 응답이없는요청
const sendCheatWorldBuffRem = (bot, buffCmsId, targetId) => {
    const body = {
        buffCmsId,
        targetId, // 버프 대상 ID.
    };
    const packet = {
        seqNum: 0,
        type: proto.Dev.WORLD_BUFF_REM_CS,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendCheatWorldBuffRem = sendCheatWorldBuffRem;
//----------------------------------------------------------
const sendReqOfflineSailingMoveDelegateStart = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendReqOfflineSailingMoveDelegateStart = sendReqOfflineSailingMoveDelegateStart;
//----------------------------------------------------------
const sendEndAutoSailing = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Ocean.END_AUTO_SAILING,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendEndAutoSailing = sendEndAutoSailing;
//----------------------------------------------------------
const sendEncountUserChoice = (bot) => {
    const body = { choiceValue: 5 };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.ENCOUNT_USER_CHOICE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendEncountUserChoice = sendEncountUserChoice;
//----------------------------------------------------------
const sendEncountEnd = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Ocean.ENCOUNT_END,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendEncountEnd = sendEncountEnd;
//----------------------------------------------------------
const sendBattleResume = (bot) => {
    const body = {
        battleId: bot.battleId,
        bReconnect: 0,
    };
    const packet = {
        seqNum: 0,
        type: proto.Battle.RESUME,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendBattleResume = sendBattleResume;
//----------------------------------------------------------
const sendBattleStart = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Battle.START,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendBattleStart = sendBattleStart;
//----------------------------------------------------------
const sendBattleLoadComplete = (bot) => {
    const body = {};
    const packet = {
        seqNum: 0,
        type: proto.Battle.LOAD_COMPLETE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendBattleLoadComplete = sendBattleLoadComplete;
//----------------------------------------------------------
const sendBattleEnd = (bot) => {
    const body = { result: undefined };
    const packet = {
        seqNum: 0,
        type: proto.Battle.END,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendBattleEnd = sendBattleEnd;
//----------------------------------------------------------
const sendBattleRewardEnter = (bot) => {
    const body = { result: undefined };
    const packet = {
        seqNum: 0,
        type: proto.BattleReward.ENTER,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendBattleRewardEnter = sendBattleRewardEnter;
//----------------------------------------------------------
const sendBattleRewardLoadComplete = (bot) => {
    const body = { result: undefined };
    const packet = {
        seqNum: 0,
        type: proto.BattleReward.LOAD_COMPLETE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendBattleRewardLoadComplete = sendBattleRewardLoadComplete;
//----------------------------------------------------------
const sendBattleRewardLeave = (bot) => {
    const body = { result: undefined };
    const packet = {
        seqNum: 0,
        type: proto.BattleReward.LEAVE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendBattleRewardLeave = sendBattleRewardLeave;
//----------------------------------------------------------
const sendGetUserTownState = (bot, townCmsId) => {
    const body = {
        townCmsId,
    };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.GET_USER_TOWN_STATE,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendGetUserTownState = sendGetUserTownState;
//----------------------------------------------------------
const sendApplyWaypointSupply = (bot, remainingPath, townCmsId) => {
    const body = {
        remainingPathPoints: remainingPath,
        townCmsId,
    };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.APPLY_WAYPOINT_SUPPLY,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendApplyWaypointSupply = sendApplyWaypointSupply;
//----------------------------------------------------------
const sendUpdateAutoSailing = (bot, remainingPath) => {
    const body = {
        remainingPathPoints: remainingPath,
    };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.UPDATE_AUTO_SAILING,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendUpdateAutoSailing = sendUpdateAutoSailing;
//----------------------------------------------------------
const sendUpdateAutoSailOptionForServer = (bot, bPushSent) => {
    const body = {
        bPushSent,
    };
    const packet = {
        seqNum: 0,
        type: proto.Ocean.UPDATE_AUTO_SAIL_OPTION_FOR_SERVER,
        body: JSON.stringify(body),
    };
    bot.conn.sendJsonPacket(packet, NaN);
};
exports.sendUpdateAutoSailOptionForServer = sendUpdateAutoSailOptionForServer;
//----------------------------------------------------------
//----------------------------------------------------------
// regist request senders
//----------------------------------------------------------
exports.sendMap[proto.Town.ENTER] = exports.sendTownEnter;
exports.sendMap[proto.Town.LOAD_COMPLETE] = exports.sendTownLoadComplete;
exports.sendMap[proto.Town.ENTER_BUILDING] = exports.sendTownEnterBuilding;
exports.sendMap[proto.Town.DEPART_DEPART] = exports.sendTownDepartDepart;
exports.sendMap[proto.Ocean.ENTER] = exports.sendOceanEnter;
exports.sendMap[proto.Ocean.LOAD_COMPLETE] = exports.sendOceanLoadComplete;
exports.sendMap[proto.Ocean.ARRIVE] = exports.sendOceanArrive;
exports.sendMap[proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START] = exports.sendReqOfflineSailingMoveDelegateStart;
exports.sendMap[proto.Ocean.END_AUTO_SAILING] = exports.sendEndAutoSailing;
exports.sendMap[proto.Ocean.ENCOUNT_USER_CHOICE] = exports.sendEncountUserChoice;
exports.sendMap[proto.Ocean.ENCOUNT_END] = exports.sendEncountEnd;
exports.sendMap[proto.Battle.START] = exports.sendBattleStart;
exports.sendMap[proto.Battle.LOAD_COMPLETE] = exports.sendBattleLoadComplete;
exports.sendMap[proto.Battle.END] = exports.sendBattleEnd;
exports.sendMap[proto.BattleReward.ENTER] = exports.sendBattleRewardEnter;
exports.sendMap[proto.BattleReward.LOAD_COMPLETE] = exports.sendBattleRewardLoadComplete;
exports.sendMap[proto.BattleReward.LEAVE] = exports.sendBattleRewardLeave;
exports.sendMap[proto.Admin.TELEPORT_TOWN] = exports.sendCheatTeleportTown;
//----------------------------------------------------------
//# sourceMappingURL=offlineSailingBotReqSenders.js.map