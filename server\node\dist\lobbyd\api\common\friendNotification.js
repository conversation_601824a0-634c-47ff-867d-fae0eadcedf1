"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const proto = __importStar(require("../../../proto/lobby/proto"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const userManager_1 = require("../../userManager");
const typedi_1 = require("typedi");
const lobby_1 = require("../../../motiflib/model/lobby");
const userFriends_1 = require("../../userFriends");
module.exports = async (req, res) => {
    mlog_1.default.debug('api-/common/friendNotification req -', req.body);
    const { packet } = req.body;
    const notiType = packet.notiType;
    const friendUserId = packet.friendUserId;
    const friendUserName = packet.friendUserName;
    const timeUtc = packet.timeUtc;
    packet.userIds.forEach((userId) => {
        const userManager = typedi_1.Container.get(userManager_1.UserManager);
        const user = userManager.getUserByUserId(userId);
        if (!user) {
            mlog_1.default.warn('not-found-user-at-friendNotification', {
                userId,
            });
            return;
        }
        const userFriends = user.userFriends;
        let sync = {};
        if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.REQUESTED) {
            // 다른 유저로부터 친구요청을 받은 경우
            const nub = {
                friendUserId,
                state: userFriends_1.FRIEND_STATE.RECEIVED,
                regTimeUtc: timeUtc,
            };
            sync = userFriends.updateFriend(nub);
        }
        else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.ACCEPTED) {
            // 친구 요청 수락 (이제 서로 친구상태)
            let friendNub = userFriends.getFriend(friendUserId);
            if (!friendNub) {
                mlog_1.default.warn('not-found-friend-at-friendNotification', {
                    userId,
                    friendUserId,
                    notiType,
                });
                return;
            }
            friendNub.state = userFriends_1.FRIEND_STATE.ESTABLISHED;
            sync = userFriends.updateFriend(friendNub);
        }
        else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.DENIED) {
            // 친구 요청 거절
            sync = userFriends.cancelFriend(friendUserId);
        }
        else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.DELETED) {
            // 친구 삭제
            sync = userFriends.deleteFriend(friendUserId);
        }
        else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.CANCELED) {
            // 친구 요청 취소
            sync = userFriends.cancelFriend(friendUserId);
        }
        else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.RECV_POINT) {
            // 친구로부터 포인트 받음
            const friendPointNub = userFriends.getFriendPoint(friendUserId);
            friendPointNub.lastRecvDate = timeUtc;
            friendPointNub.pickup = 0;
            sync = userFriends.updateFriendPoint(friendPointNub);
        }
        else if (notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.LOGIN ||
            notiType === lobby_1.FRIEND_NOTIFICATION_TYPE.LOGOUT) {
            // 친구 로그인 or 로그아웃 알림
            let nub = user.userFriends.getFriend(friendUserId);
            if (!nub) {
                mlog_1.default.warn('not-found-friend-at-friendNotification', {
                    userId,
                    friendUserId,
                    notiType,
                });
                return;
            }
        }
        user.sendJsonPacket(0, proto.Friend.NOTIFIED_SC, {
            userId: friendUserId,
            userName: friendUserName,
            notiType,
            sync,
        });
    });
    res.end();
};
//# sourceMappingURL=friendNotification.js.map