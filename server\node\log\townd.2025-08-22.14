{"url":"/leave","status":"200","response-time":"0.507","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:01:28.049Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T05:01:49.106Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T05:01:49.106Z"}
{"level":"info","message":"[Session] socket disposed, 5N5yyZka","timestamp":"2025-08-22T05:01:49.106Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T05:01:49.106Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T05:01:49.198Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:01:49.198Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:01:49.457Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:01:49.457Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:01:49.459Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:01:50.461Z"}
{"environment":"development","type":"townd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:01:52.894Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:20.835Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:02:20.847Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:02:20.849Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:02:21.006Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:02:21.007Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:02:21.008Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:02:21.008Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:02:24.795Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:02:24.796Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:02:24.797Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:02:24.804Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:02:24.806Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:02:24.823Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:02:24.927Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:24.947Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:24.963Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:24.975Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:24.988Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.000Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.018Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.035Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.050Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.068Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:02:25.150Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:02:25.151Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:02:25.156Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:02:25.266Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:02:25.269Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:02:25.270Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:02:25.271Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:02:25.274Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:02:25.275Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:02:25.275Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:02:25.275Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:02:25.277Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:02:25.278Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:02:25.278Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:02:25.279Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:02:25.280Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:02:25.284Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:02:25.285Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.287Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.287Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.287Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.287Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.287Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.287Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.287Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.288Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.288Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.288Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.288Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:25.288Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.297Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.312Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.323Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.335Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.345Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.365Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.374Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.383Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.396Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.406Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.415Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.425Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.435Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.446Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.456Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.465Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.475Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.484Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:25.494Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:02:25.508Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T05:02:25.508Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:02:25.510Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:02:25.513Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:02:25.513Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:02:26.516Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:02:26.516Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:02:27.518Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:02:27.519Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:02:28.522Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:02:28.522Z"}
{"pingInterval":2000,"curDate":1755838949,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T05:02:29.532Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T05:02:29.532Z"}
{"level":"info","message":"[SessionManager] session created: TVwb9FSr, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:02:41.439Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:02:41.442Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T05:02:41.443Z"}
{"origin":{},"seq":2,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T05:02:41.443Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T05:03:43.285Z"}
{"townCmsId":11000000,"channelId":"Eg55tPlL","level":"info","message":"townZone created","timestamp":"2025-08-22T05:03:43.286Z"}
{"url":"/enter","status":"200","response-time":"37.095","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:03:43.288Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T05:03:43.290Z"}
{"url":"/loadComplete","status":"200","response-time":"0.718","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:03:55.261Z"}
{"url":"/leave","status":"200","response-time":"0.602","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:08:26.730Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T05:09:19.711Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T05:09:19.712Z"}
{"level":"info","message":"[Session] socket disposed, TVwb9FSr","timestamp":"2025-08-22T05:09:19.712Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T05:09:19.712Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T05:09:19.847Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:09:19.848Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:09:20.047Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:09:20.048Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:09:20.050Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:09:21.053Z"}
{"environment":"development","type":"townd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:09:24.768Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:52.823Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:09:52.832Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:09:52.835Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:09:53.123Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:09:53.124Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:09:53.125Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:09:53.126Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:09:56.549Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:09:56.550Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:09:56.551Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:09:56.557Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:09:56.558Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:09:56.573Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:09:56.661Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.681Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.697Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.708Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.722Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.733Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.750Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.766Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.782Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:56.801Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:09:56.879Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:09:56.880Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:09:56.884Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:09:57.011Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:09:57.014Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:09:57.015Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:09:57.015Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:09:57.018Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:09:57.019Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:09:57.019Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:09:57.019Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:09:57.021Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:09:57.022Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:09:57.022Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:09:57.023Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:09:57.024Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:09:57.026Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:09:57.026Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.029Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.029Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.030Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.030Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.030Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.030Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.030Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.030Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.031Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.031Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.031Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:57.031Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.040Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.053Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.061Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.070Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.079Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.092Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.100Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.107Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.118Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.125Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.132Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.140Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.147Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.154Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.161Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.170Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.178Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.186Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:57.194Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:09:57.205Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T05:09:57.207Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:09:57.208Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:09:57.211Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:09:57.211Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:09:58.214Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:09:58.214Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:09:59.217Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:09:59.217Z"}
{"pingInterval":2000,"curDate":1755839400,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T05:10:00.224Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T05:10:00.224Z"}
{"level":"info","message":"[SessionManager] session created: aWb41MVN, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:10:05.478Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:10:05.481Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T05:10:05.482Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T05:10:05.483Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T05:10:16.387Z"}
{"townCmsId":11000000,"channelId":"RwKfzh7-","level":"info","message":"townZone created","timestamp":"2025-08-22T05:10:16.389Z"}
{"url":"/enter","status":"200","response-time":"71.375","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:10:16.391Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T05:10:16.391Z"}
{"url":"/loadComplete","status":"200","response-time":"0.627","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:10:28.750Z"}
{"url":"/leave","status":"200","response-time":"0.586","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:24:08.274Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T05:24:08.913Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T05:24:08.913Z"}
{"level":"info","message":"[Session] socket disposed, aWb41MVN","timestamp":"2025-08-22T05:24:08.913Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T05:24:08.913Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T05:24:09.047Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:24:09.047Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:24:09.219Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:24:09.219Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:24:09.221Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:24:10.223Z"}
{"environment":"development","type":"townd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:24:14.033Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:42.899Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:24:42.909Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:24:42.910Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:24:43.144Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:24:43.145Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:24:43.145Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:24:43.146Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:24:46.586Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:24:46.587Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:24:46.588Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:24:46.595Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:24:46.595Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:24:46.611Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:24:46.699Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.719Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.734Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.745Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.758Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.769Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.786Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.803Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.818Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:46.836Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:24:46.917Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:24:46.918Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:24:46.921Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:24:47.047Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:24:47.049Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:24:47.050Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:24:47.050Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:24:47.053Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:24:47.053Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:24:47.053Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:24:47.053Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:24:47.055Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:24:47.056Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:24:47.057Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:24:47.058Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:24:47.062Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:24:47.063Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:24:47.064Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.065Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.066Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:47.067Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.074Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.089Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.098Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.109Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.118Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.134Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.141Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.149Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.160Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.167Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.176Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.184Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.193Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.200Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.207Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.214Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.222Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.230Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:47.237Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:24:47.248Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T05:24:47.249Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:24:47.251Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:24:47.254Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:24:47.254Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:24:48.257Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:24:48.257Z"}
{"pingInterval":2000,"curDate":1755840289,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T05:24:49.263Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T05:24:49.264Z"}
{"level":"info","message":"[SessionManager] session created: NLeOEX6l, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:24:56.750Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:24:56.755Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T05:24:56.756Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T05:24:56.756Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T05:26:01.245Z"}
{"townCmsId":11000000,"channelId":"k6nlbykc","level":"info","message":"townZone created","timestamp":"2025-08-22T05:26:01.246Z"}
{"url":"/enter","status":"200","response-time":"58.510","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:26:01.247Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T05:26:01.248Z"}
{"url":"/loadComplete","status":"200","response-time":"0.870","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:26:13.049Z"}
{"url":"/leave","status":"200","response-time":"0.598","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:39:04.500Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T05:39:50.695Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T05:39:50.695Z"}
{"level":"info","message":"[Session] socket disposed, NLeOEX6l","timestamp":"2025-08-22T05:39:50.695Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T05:39:50.696Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T05:39:50.929Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:39:50.929Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:39:51.796Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:39:51.796Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:39:51.798Z"}
{"environment":"development","type":"townd","gitCommitHash":"3e628d45a9f","gitCommitMessage":"UWO FGT Survey 기능 추가(테스트용)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-22T14:28:39+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:39:55.857Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:24.018Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:40:24.027Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:40:24.030Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:40:24.191Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:40:24.192Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:40:24.192Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:40:24.193Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:40:27.785Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:40:27.786Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:40:27.787Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:40:27.793Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:40:27.794Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:40:27.809Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:40:27.896Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:27.916Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:27.932Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:27.944Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:27.958Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:27.969Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:27.986Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:28.002Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:28.017Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:28.036Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:40:28.119Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:40:28.120Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:40:28.124Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:40:28.259Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:40:28.261Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:40:28.262Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:40:28.262Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:40:28.265Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:40:28.265Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:40:28.265Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:40:28.266Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:40:28.268Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:40:28.269Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:40:28.269Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:40:28.269Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:40:28.271Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:40:28.272Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:40:28.273Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.274Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.274Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:28.275Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.284Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.296Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.303Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.314Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.322Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.338Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.346Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.353Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.366Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.373Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.379Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.386Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.392Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.400Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.407Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.414Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.420Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.427Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:28.436Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:40:28.447Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T05:40:28.448Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:40:28.450Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:28.453Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:40:28.453Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:29.455Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:40:29.456Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:30.458Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:40:30.458Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:31.460Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:40:31.461Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:32.464Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T05:40:32.464Z"}
{"pingInterval":2000,"curDate":1755841233,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T05:40:33.534Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T05:40:33.534Z"}
{"level":"info","message":"[SessionManager] session created: XBiyuyGp, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:40:40.853Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:40:40.855Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T05:40:40.856Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T05:40:40.857Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T05:41:01.353Z"}
{"townCmsId":11000000,"channelId":"U0bWcGKr","level":"info","message":"townZone created","timestamp":"2025-08-22T05:41:01.354Z"}
{"url":"/enter","status":"200","response-time":"84.657","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:41:01.355Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T05:41:01.356Z"}
{"url":"/loadComplete","status":"200","response-time":"0.829","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T05:41:13.138Z"}
