"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const merror_1 = require("../../motiflib/merror");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const typedi_1 = __importDefault(require("typedi"));
const fleetManager_1 = require("../fleetManager");
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
router.on(protocol_1.Protocol.LB2OC_NTF_OCEAN_MOVE, async (ntf, res) => {
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(ntf.userId);
    if (!fleet) {
        throw new merror_1.MError('[TCP] no agent in ocean zone', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId: ntf.userId,
        });
    }
    const oceanZone = fleet.getCurrentZone();
    if (!oceanZone) {
        mlog_1.default.warn('[TCP] User moving when not in a zone!', {
            userId: ntf.userId,
        });
        return;
    }
    oceanZone.updateUserFleetLocation(fleet, ntf.location, ntf.degrees, ntf.speed, false);
});
router.on(protocol_1.Protocol.LB2OC_NTF_OCEAN_TELEPORT, async (ntf, res) => {
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(ntf.userId);
    if (!fleet) {
        throw new merror_1.MError('[TCP] no agent in ocean zone', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId: ntf.userId,
        });
    }
    const oceanZone = fleet.getCurrentZone();
    if (!oceanZone) {
        mlog_1.default.warn('[TCP] User teleport when not in a zone!', {
            userId: ntf.userId,
        });
        return;
    }
    oceanZone.updateUserFleetLocation(fleet, ntf.location, ntf.degrees, 0, true);
});
router.on(protocol_1.Protocol.LB2OC_NTF_DELEGATE_NAVI_RESULT, async (ntf, res) => {
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(ntf.userId);
    if (!fleet) {
        throw new merror_1.MError('[TCP] no agent in ocean zone', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId: ntf.userId,
        });
    }
    const oceanZone = fleet.getCurrentZone();
    if (!oceanZone) {
        mlog_1.default.warn('[TCP] User teleport when not in a zone!', {
            userId: ntf.userId,
        });
        return;
    }
    fleet.getLocalNpcSpawnEntry().setLocalNpcNaviQuery(ntf.queryResult);
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerMove.js.map