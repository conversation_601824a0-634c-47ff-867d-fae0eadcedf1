module.exports = {

  apps: [

    /* configd */
    {
      name: 'configd',
      cwd: '../node',
      script: 'dist/configd/configd.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* authd */
    {
      name: 'authd',
      cwd: '../node',
      script: 'dist/authd/authd.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* lobbyd */
    {
      name: 'lobbyd',
      cwd: '../node',
      script: 'dist/lobbyd/lobbyd.js',
      env: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      // shutdown_with_message: true, // Windows OS
      kill_timeout: 80000, // 80 seconds
      exp_backoff_restart_delay: 500,
    },

    /* oceand */
    {
      name: 'oceand',
      cwd: '../node',
      script: 'dist/oceand/oceand.js',
      env: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* saild */
    {
      name: 'saild',
      cwd: '../node',
      script: 'dist/saild/saild.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* townd */
    {
      name: 'townd',
      cwd: '../node',
      script: 'dist/townd/townd.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* zonelbd */
    {
      name: 'zonelbd',
      cwd: '../node',
      script: 'dist/zonelbd/zonelbd.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

    /* realmd */
    {
      name: 'realmd',
      cwd: '../node',
      script: 'dist/realmd/realmd.js',
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
      node_args: '--expose-gc',
      exp_backoff_restart_delay: 500,
    },

  ] /* apps */

};
