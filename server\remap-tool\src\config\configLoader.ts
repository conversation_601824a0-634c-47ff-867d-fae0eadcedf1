import fs from 'fs-extra';
import path from 'path';
import JSON5 from 'json5';
import {
  RemapToolConfig,
  WorldShardMapping
} from '../types';

export class ConfigLoader {
  private configDir: string;

  constructor(configDir: string = './config') {
    this.configDir = configDir;
  }

  async loadRemapToolConfig(): Promise<RemapToolConfig> {
    const configPath = path.join(this.configDir, 'database.json5');

    if (!await fs.pathExists(configPath)) {
      throw new Error(`Config file not found: ${configPath}`);
    }

    try {
      const configContent = await fs.readFile(configPath, 'utf8');
      const config = JSON5.parse(configContent) as RemapToolConfig;
      this.validateRemapToolConfig(config);
      return config;
    } catch (error) {
      throw new Error(`Failed to load config: ${configPath} - ${error}`);
    }
  }

  private validateRemapToolConfig(config: RemapToolConfig): void {
    if (!config.sharedConfig || !config.worlds) {
      throw new Error('Configuration is incomplete. sharedConfig and worlds are required.');
    }

    if (!config.sharedConfig.mysqlAuthDb) {
      throw new Error('sharedConfig.mysqlAuthDb is required.');
    }

    if (!Array.isArray(config.worlds) || config.worlds.length === 0) {
      throw new Error('worlds must be a non-empty array.');
    }

    const worldIds = new Set<string>();
    config.worlds.forEach((world, index) => {
      if (!world.id) {
        throw new Error(`worlds[${index}].id is not set.`);
      }

      if (worldIds.has(world.id)) {
        throw new Error(`Duplicate world ID: ${world.id}`);
      }
      worldIds.add(world.id);

      if (!world.mysqlUserDb || !world.mysqlWorldDb) {
        throw new Error(`worlds[${index}] missing mysqlUserDb or mysqlWorldDb.`);
      }

      if (!Array.isArray(world.mysqlUserDb.shards) || world.mysqlUserDb.shards.length === 0) {
        throw new Error(`worlds[${index}].mysqlUserDb.shards must be a non-empty array.`);
      }
    });
  }

  generateWorldShardMapping(config: RemapToolConfig): WorldShardMapping[] {
    const mappings: WorldShardMapping[] = [];

    for (const world of config.worlds) {
      for (const shard of world.mysqlUserDb.shards) {
        const userDatabase = shard.sqlCfg.database ||
          world.mysqlUserDb.sqlDefaultCfg.database + '_' + shard.shardId.toString().padStart(2, '0');

        mappings.push({
          worldId: world.id,
          shardId: shard.shardId,
          userDatabase,
          worldDatabase: world.mysqlWorldDb.database,
        });
      }
    }

    return mappings;
  }

  getShardForWorld(worldId: string, mappings: WorldShardMapping[]): WorldShardMapping | null {
    return mappings.find(mapping => mapping.worldId === worldId) || null;
  }

  async checkConfigFiles(): Promise<boolean> {
    const databasePath = path.join(this.configDir, 'database.json5');
    return await fs.pathExists(databasePath);
  }

  async copyExampleConfigs(): Promise<void> {
    const databaseExamplePath = path.join(this.configDir, 'database.example.json5');
    const databasePath = path.join(this.configDir, 'database.json5');

    if (await fs.pathExists(databaseExamplePath) && !await fs.pathExists(databasePath)) {
      await fs.copy(databaseExamplePath, databasePath);
      console.log(`Copied example database config file: ${databasePath}`);
    }
  }
}
