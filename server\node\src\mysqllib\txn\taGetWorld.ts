// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import { withTxn } from '../mysqlUtil';
import { MError, MErrorCode } from '../../motiflib/merror';
import paAccountLoad from '../sp/paAccountLoad';
import paWorldUsersLoad from '../sp/paWorldUsersLoad';
import Container from 'typedi';
import { MysqlReqRepCounter } from '../mysqlReqRepCounter';

export interface World {
  worldId: string;
  userId: number;
  pubId: string;
  name: string;
  nationCmsId: number;
}
export interface Result {
  lastWorldId?: string;
  worlds: World[];
}

function queryImpl(connection: PoolConnection, id: string, curTimeUtc: number): Promise<Result> {
  const ret: Result = {
    worlds: undefined,
  };
  return paAccountLoad(connection, id)
    .then((result) => {
      if (result) {
        ret.lastWorldId = result.lastWorldId;
        return paWorldUsersLoad(connection, id);
      }
      return [];
    })
    .then((result) => {
      ret.worlds = result;
      return ret;
    })
    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.AUTH_GET_WORLDS_TXN_ERROR);
      }
    });
}

export default function taGetWorld(
  dbConnPool: Pool,
  id: string,
  curTimeUtc: number
): Promise<Result> {
  return withTxn(
    dbConnPool,
    __filename,
    (connection: PoolConnection) => {
      return queryImpl(connection, id, curTimeUtc);
    },
    undefined,
    Container.get(MysqlReqRepCounter)
  ) as Promise<Result>;
}
