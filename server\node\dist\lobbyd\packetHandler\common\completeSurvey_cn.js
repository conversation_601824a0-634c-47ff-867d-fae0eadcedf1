"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Common_CompleteSurvey = void 0;
const userConnection_1 = require("../../userConnection");
const survey_1 = require("../../survey/survey");
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const rsn = 'complete_survey_cn';
const add_rsn = null;
class Cph_Common_CompleteSurvey {
    constructor() { }
    testGameState(user) {
        return true;
    }
    async exec(user, packet) {
        mlog_1.default.info('[CompleteSurvey] rx', {
            body: packet.bodyObj,
        });
        user.ensureConnState(userConnection_1.CONNECTION_STATE.LOGGED_IN);
        const body = packet.bodyObj;
        const surveyId = body.surveyId;
        await survey_1.Survey.completeSurvey(surveyId, user);
    }
}
exports.Cph_Common_CompleteSurvey = Cph_Common_CompleteSurvey;
//# sourceMappingURL=completeSurvey_cn.js.map