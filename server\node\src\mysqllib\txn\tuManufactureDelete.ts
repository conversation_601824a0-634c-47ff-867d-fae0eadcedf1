// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import { withTxn } from '../mysqlUtil';
import puManufactureProgressDelete from '../sp/puManufactureProgressDelete';
import { MError, MErrorCode } from '../../motiflib/merror';

function queryImpl(
  connection: PoolConnection,
  userId: number,
  roomCmsId: number,
  slot: number
) {
  return puManufactureProgressDelete(connection, userId, roomCmsId, slot)
    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.MANUFACTURE_PROGRESS_DELETE_QUERY_ERROR);
      }
    });
}

export default function (
  dbConnPool: Pool,
  userId: number,
  roomCmsId: number,
  slot: number
) {
  return withTxn(dbConnPool, __filename, (connection: PoolConnection) => {
    return queryImpl(connection, userId, roomCmsId, slot);
  });
}