CREATE PROCEDURE `mp_u_admin_extend_all_fixed_term_products_auto`(
  IN inAddedMinutes INT,
  IN inMinExpirationTimeUtc INT
)
label_body:BEGIN

-- cursor common declare
DECLARE done INT DEFAULT FALSE;
DECLARE v_cmsId INT;
DECLARE curProduct CURSOR FOR
  SELECT cmsId
  FROM admin_cash_shop_cms_fixed_term_products
  WHERE cmsId > 0;
DECLARE curProductBuff CURSOR FOR
  SELECT cmsId
  FROM admin_cash_shop_cms_fixed_term_product_buffs
  WHERE cmsId > 0;
DECLARE curReligionBuff CURSOR FOR
  SELECT cmsId
  FROM admin_religion_cms_buffs
  WHERE cmsId > 0;

DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

-- cashshop product
OPEN curProduct;
read_loop: LOOP
  FETCH curProduct INTO v_cmsId;
  IF done THEN
    LEAVE read_loop;
  END IF;

  UPDATE u_cash_shop_fixed_term_products
    SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE)
    WHERE cmsId = v_cmsId AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);

  -- log table insert
  -- INSERT INTO admin_cash_shop_cms_fixed_term_products_log
  --   (cmsId, logTimeUtc)
  --   VALUES (v_cmsId, FROM_UNIXTIME(inMinExpirationTimeUtc));

END LOOP;
CLOSE curProduct;

-- cashshop buff
SET done =  FALSE;

OPEN curProductBuff;
read_loop_buff: LOOP
  FETCH curProductBuff INTO v_cmsId;
  IF done THEN
    LEAVE read_loop_buff;
  END IF;

  UPDATE u_world_buffs
    SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE)
    WHERE cmsId = v_cmsId AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);

  -- log table insert
  -- INSERT INTO admin_cash_shop_cms_fixed_term_product_buffs_log
  --   (cmsId, logTimeUtc)
  --   VALUES (v_cmsId, FROM_UNIXTIME(inMinExpirationTimeUtc));

END LOOP;
CLOSE curProductBuff;

-- 시나리오(기획에서 중요도가 낮아서 자동화 불필요하다고 판단함)
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500079 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500080 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500081 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500082 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500106 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500107 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500108 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500109 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);
UPDATE u_cash_shop_fixed_term_products SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE cmsId = 10500111 AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);

-- 면세증
UPDATE u_tax_free_permits SET expirationTimeUtc = DATE_ADD(expirationTimeUtc, INTERVAL inAddedMinutes MINUTE) WHERE expirationTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);

-- 종교 버프
SET done =  FALSE;

OPEN curReligionBuff;
read_loop_buff: LOOP
  FETCH curReligionBuff INTO v_cmsId;
  IF done THEN
    LEAVE read_loop_buff;
  END IF;

  UPDATE u_world_buffs
    SET endTimeUtc = DATE_ADD(endTimeUtc, INTERVAL inAddedMinutes MINUTE)
    WHERE cmsId = v_cmsId AND endTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);

  -- log table insert
  -- INSERT INTO admin_religion_cms_buffs_log
  --   (cmsId, logTimeUtc)
  --   VALUES (v_cmsId, FROM_UNIXTIME(inMinExpirationTimeUtc));

END LOOP;
CLOSE curReligionBuff;

-- 연속 구매 상품은 종류에 무관하게 연장
UPDATE u_cash_shop_consecutive_products
  SET normalSaleTimeUtc = DATE_ADD(normalSaleTimeUtc, INTERVAL inAddedMinutes MINUTE),
      discountSaleTimeUtc = DATE_ADD(discountSaleTimeUtc, INTERVAL inAddedMinutes MINUTE)
  WHERE discountSaleTimeUtc >= FROM_UNIXTIME(inMinExpirationTimeUtc);

END
