import fs from 'fs-extra';
import path from 'path';
import { AnalysisResult, DatabaseTableInfo, RedisKeyInfo, RemapResult, VerificationResult, GroupedRemapResult } from '../types';

/**
 * HTML 보고서 생성기
 */
export class HtmlReportGenerator {
  /**
   * 분석 결과 HTML 보고서 생성
   */
  static async generateAnalysisReport(
    analysisResult: AnalysisResult,
    outputPath: string,
    databaseConfig?: any
  ): Promise<void> {
    const html = this.createAnalysisHtml(analysisResult, databaseConfig);
    await fs.writeFile(outputPath, html, 'utf8');
  }

  /**
   * 리맵핑 결과 HTML 보고서 생성 (그룹핑된 형태로만 생성)
   */
  static async generateRemapReport(
    groupedResult: GroupedRemapResult,
    outputPath: string,
    databaseConfig?: any
  ): Promise<void> {
    const html = this.createUnifiedRemapHtml(groupedResult, databaseConfig);
    await fs.writeFile(outputPath, html, 'utf8');
  }

  /**
   * 검증 결과 HTML 보고서 생성
   */
  static async generateVerificationReport(
    verificationResult: VerificationResult,
    outputPath: string
  ): Promise<void> {
    const html = this.createVerificationHtml(verificationResult);
    await fs.writeFile(outputPath, html, 'utf8');
  }

  /**
   * HTML 이스케이프
   */
  private static escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * 분석 결과 HTML 생성
   */
  private static createAnalysisHtml(result: AnalysisResult, databaseConfig?: any): string {
    const timestamp = new Date().toLocaleString('ko-KR');

    return `<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>코드베이스 분석 보고서</title>
    <style>
        ${this.getStyles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔍 코드베이스 분석 보고서</h1>
            <p class="timestamp">생성일시: ${timestamp}</p>
        </header>

        <div class="summary-section">
            <h2>📊 분석 요약</h2>
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-icon">📁</div>
                    <div class="summary-content">
                        <div class="summary-number">${result.totalFilesAnalyzed.toLocaleString()}</div>
                        <div class="summary-label">총 분석 파일</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">🗄️</div>
                    <div class="summary-content">
                        <div class="summary-number">${result.databaseTables.length}</div>
                        <div class="summary-label">데이터베이스 테이블</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">🔑</div>
                    <div class="summary-content">
                        <div class="summary-number">${result.redisKeys.length}</div>
                        <div class="summary-label">Redis 키 패턴</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">⏱️</div>
                    <div class="summary-content">
                        <div class="summary-number">${(result.analysisTime / 1000).toFixed(2)}s</div>
                        <div class="summary-label">분석 시간</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="database-section">
            <h2>🗄️ 데이터베이스 테이블 (${result.databaseTables.length}개)</h2>
            ${this.createDatabaseTablesHtml(result.databaseTables)}
        </div>

        <div class="redis-details-section">
            <h2>🔑 Redis 키 패턴 (${result.redisKeys.length}개)</h2>
            ${this.createRedisKeysHtml(result.redisKeys, databaseConfig)}
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * 데이터베이스 테이블 HTML 생성
   */
  private static createDatabaseTablesHtml(tables: DatabaseTableInfo[]): string {
    if (!tables || tables.length === 0) {
      return '<p class="no-data">발견된 데이터베이스 테이블이 없습니다.</p>';
    }

    return `<div class="database-tables">
      ${tables.map(table => this.createDatabaseTableDetailHtml(table)).join('')}
    </div>`;
  }

  /**
   * 개별 데이터베이스 테이블 상세 HTML 생성
   */
  private static createDatabaseTableDetailHtml(table: DatabaseTableInfo): string {
    return `
      <div class="database-table-detail">
        <div class="table-header">
          <h3 class="table-name">
            <span class="table-icon">🗄️</span>
            ${this.escapeHtml(table.tableName || '')}
          </h3>
          <div class="table-meta">
            <span class="database-type">${table.database}</span>
            ${table.shardingRequired ? '<span class="badge badge-shard">샤딩 필요</span>' : ''}
          </div>
        </div>

        <div class="table-summary">
          <div class="summary-badges">
            ${table.hasAccountId ? '<span class="badge badge-account">AccountID 사용</span>' : ''}
            ${table.hasPubId ? '<span class="badge badge-pub">PubID 사용</span>' : ''}
          </div>
        </div>

        <div class="table-columns">
          <h4>📋 컬럼 정보 (${table.columns.length}개)</h4>
          <div class="columns-table-container">
            <table class="columns-table">
              <thead>
                <tr>
                  <th class="col-name">컬럼명</th>
                  <th class="col-type">데이터 타입</th>
                  <th class="col-relation">관련 ID</th>
                  <th class="col-attributes">속성</th>
                </tr>
              </thead>
              <tbody>
                ${table.columns.map(column => `
                  <tr>
                    <td class="col-name">
                      <code class="column-name-code">${this.escapeHtml(column.columnName)}</code>
                    </td>
                    <td class="col-type">
                      <code class="column-type-code">${this.formatColumnType(column)}</code>
                    </td>
                    <td class="col-relation">
                      ${this.getColumnRelationBadge(column.relatedTo)}
                    </td>
                    <td class="col-attributes">
                      <div class="attributes-container">
                        ${column.isPrimaryKey ? '<span class="badge badge-primary">PK</span>' : ''}
                        ${column.isIndexed ? '<span class="badge badge-index">IDX</span>' : ''}
                        ${!column.isNullable ? '<span class="badge badge-notnull">NOT NULL</span>' : ''}
                      </div>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>

        ${table.indexes.length > 0 ? `
        <div class="table-indexes">
          <h4>🔍 인덱스 정보 (${table.indexes.length}개)</h4>
          <div class="index-list">
            ${table.indexes.map(index => `
              <div class="index-item">
                <span class="index-name">${this.escapeHtml(index.indexName)}</span>
                <span class="index-columns">(${index.columns.join(', ')})</span>
                ${index.isUnique ? '<span class="badge badge-unique">UNIQUE</span>' : ''}
              </div>
            `).join('')}
          </div>
        </div>
        ` : ''}
      </div>
    `;
  }

  /**
   * 컬럼 관련 ID 배지 생성
   */
  private static getColumnRelationBadge(relatedTo: 'accountId' | 'pubId' | 'none'): string {
    if (relatedTo === 'none') return '-';

    const badges: { [key: string]: string } = {
      'accountId': '<span class="badge badge-account">AccountID (GNID)</span>',
      'pubId': '<span class="badge badge-pub">PubID (NID)</span>'
    };

    return badges[relatedTo] || '-';
  }

  /**
   * Redis 키 상세 HTML 생성 (테이블 형태)
   */
  private static createRedisKeysHtml(redisKeys: RedisKeyInfo[], databaseConfig?: any): string {
    if (!redisKeys || redisKeys.length === 0) {
      return '<p class="no-data">발견된 Redis 키 패턴이 없습니다.</p>';
    }

    return `
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>키 패턴</th>
              <th>타입</th>
              <th>Redis 인스턴스</th>
              <th>설명</th>
              <th>사용하는 ID</th>
            </tr>
          </thead>
          <tbody>
            ${redisKeys.map(key => `
              <tr>
                <td><code>${this.escapeHtml(key.keyPattern || '')}</code></td>
                <td><span class="key-type key-type-${key.keyType || 'unknown'}">${this.getKeyTypeDisplay(key.keyType || 'unknown')}</span></td>
                <td>${this.escapeHtml(key.redisInstance || '')}</td>
                <td>${this.escapeHtml(key.description || '')}</td>
                <td>
                  <div class="id-badges">
                    ${this.getRedisKeyIdBadges(key)}
                  </div>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
  }

  /**
   * Redis 키의 ID 배지 생성
   */
  private static getRedisKeyIdBadges(key: RedisKeyInfo): string {
    const badges: string[] = [];

    // AccountId와 Gnid가 모두 사용되는 경우
    if (key.usesAccountId && key.usesGnid) {
      badges.push('<span class="badge badge-account">ACCOUNTID(GNID)</span>');
    } else if (key.usesAccountId) {
      badges.push('<span class="badge badge-account">ACCOUNTID</span>');
    } else if (key.usesGnid) {
      badges.push('<span class="badge badge-gnid">GNID</span>');
    }

    // PubId와 Nid가 모두 사용되는 경우
    if (key.usesPubId && key.usesNid) {
      badges.push('<span class="badge badge-pub">PUBID(NID)</span>');
    } else if (key.usesPubId) {
      badges.push('<span class="badge badge-pub">PUBID</span>');
    } else if (key.usesNid) {
      badges.push('<span class="badge badge-nid">NID</span>');
    }

    // UserId는 별도로 표시
    if (key.usesUserId) {
      badges.push('<span class="badge badge-user">USERID</span>');
    }

    return badges.join('');
  }

  /**
   * 키 타입 표시명 반환
   */
  private static getKeyTypeDisplay(keyType: string): string {
    const typeMap: Record<string, string> = {
      'string': 'String',
      'hash': 'Hash',
      'list': 'List',
      'set': 'Set',
      'zset': 'Sorted Set',
      'stream': 'Stream',
      'none': 'None',
      'unknown': 'Unknown'
    };
    return typeMap[keyType] || keyType;
  }

  /**
   * CSS 스타일
   */
  private static getStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f5f7fa;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .timestamp {
        opacity: 0.9;
        font-size: 1.1rem;
      }

      .summary-section {
        margin-bottom: 40px;
      }

      .summary-section h2 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: #2d3748;
      }

      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      /* 새로운 카드 스타일 */
      .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .summary-card {
        background: white;
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        text-align: left;
      }

      .summary-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      }

      .summary-icon {
        font-size: 2.5rem;
        margin-right: 20px;
        flex-shrink: 0;
      }

      .summary-content {
        flex: 1;
      }

      .summary-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 8px;
        line-height: 1;
      }

      .summary-label {
        color: #718096;
        font-weight: 500;
        font-size: 0.9rem;
      }

      .database-section, .redis-section, .redis-details-section {
        margin-bottom: 40px;
      }

      .database-section h2, .redis-section h2, .redis-details-section h2 {
        font-size: 1.8rem;
        margin-bottom: 20px;
        color: #2d3748;
      }

      /* 데이터베이스 테이블 상세 스타일 */
      .database-tables {
        display: flex;
        flex-direction: column;
        gap: 30px;
      }

      .database-table-detail {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e2e8f0;
      }

      .database-table-detail .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e2e8f0;
      }

      .database-table-detail .table-name {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 1.4rem;
        font-weight: bold;
        color: #2d3748;
        margin: 0;
      }

      .table-icon {
        font-size: 1.2rem;
      }

      .table-meta {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .database-table-detail .table-description {
        color: #718096;
        margin-bottom: 20px;
        font-style: italic;
      }

      .table-columns {
        margin-bottom: 25px;
      }

      .table-columns h4 {
        font-size: 1.1rem;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* 데이터베이스 컬럼 테이블 전용 스타일 */
      .columns-table-container {
        overflow-x: auto;
        margin: 15px 0;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        background: white;
      }

      .columns-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        margin: 0;
        font-size: 0.85rem;
        table-layout: fixed;
      }

      .columns-table thead {
        background: #2d3748;
        color: white;
      }

      .columns-table th {
        padding: 12px 16px;
        text-align: left;
        font-weight: 600;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        border-right: 1px solid #4a5568;
        white-space: nowrap;
      }

      .columns-table th:last-child {
        border-right: none;
      }

      .columns-table td {
        padding: 12px 16px;
        border: none;
        border-right: 1px solid #e2e8f0;
        border-bottom: 1px solid #e2e8f0;
        vertical-align: middle;
      }

      .columns-table td:last-child {
        border-right: none;
      }

      .columns-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
      }

      .columns-table tbody tr:nth-child(odd) {
        background-color: white;
      }

      .columns-table tbody tr:hover {
        background-color: #e6fffa !important;
        transition: background-color 0.2s ease;
      }

      .columns-table tbody tr:hover td {
        background-color: transparent;
      }

      /* 컬럼 너비 설정 */
      .col-name { width: 25%; }
      .col-type { width: 20%; }
      .col-relation { width: 25%; }
      .col-attributes { width: 30%; }

      .column-name-code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-weight: bold;
        color: #2d3748;
        background: #f7fafc;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
      }

      .column-type-code {
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        color: #4a5568;
        background: #edf2f7;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
      }

      .col-relation {
        text-align: center;
      }

      .attributes-container {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        justify-content: flex-start;
      }

      .table-files {
        margin-top: 20px;
      }

      .table-files h4 {
        font-size: 1.1rem;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .file-list {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .file-path {
        font-size: 0.8rem;
        color: #a0aec0;
        background: #f7fafc;
        padding: 6px 10px;
        border-radius: 4px;
        font-family: monospace;
        border-left: 3px solid #e2e8f0;
      }

      .file-path.more-files {
        color: #718096;
        font-style: italic;
        background: #edf2f7;
        border-left-color: #cbd5e0;
      }

      /* 테이블 요약 스타일 */
      .table-summary {
        margin-bottom: 20px;
      }

      .summary-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      /* 컬럼 속성 스타일 */
      .column-attributes {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      /* 인덱스 스타일 */
      .table-indexes {
        margin-top: 20px;
      }

      .table-indexes h4 {
        font-size: 1.1rem;
        color: #2d3748;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .index-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .index-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 12px;
        background: #f7fafc;
        border-radius: 6px;
        border-left: 3px solid #e2e8f0;
      }

      .index-name {
        font-family: monospace;
        font-weight: bold;
        color: #2d3748;
      }

      .index-columns {
        font-family: monospace;
        color: #718096;
        font-size: 0.9rem;
      }

      .table-grid, .redis-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
      }

      .table-card, .redis-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s;
      }

      .table-card:hover, .redis-card:hover {
        transform: translateY(-2px);
      }

      .table-header, .redis-header {
        margin-bottom: 15px;
      }

      .table-name, .redis-pattern {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2d3748;
        margin-bottom: 10px;
        word-break: break-all;
      }

      .table-badges, .redis-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
      }

      .badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        display: inline-block;
        white-space: nowrap;
        line-height: 1.2;
      }

      .badge-account { background: #fed7d7; color: #c53030; }
      .badge-pub { background: #bee3f8; color: #2b6cb0; }
      .badge-user { background: #c6f6d5; color: #2f855a; }
      .badge-gnid { background: #fbb6ce; color: #b83280; }
      .badge-nid { background: #faf089; color: #744210; }
      .badge-shard { background: #e6fffa; color: #234e52; }
      .badge-primary { background: #fed7d7; color: #c53030; }
      .badge-index { background: #bee3f8; color: #2b6cb0; }
      .badge-notnull { background: #c6f6d5; color: #2f855a; }
      .badge-unique { background: #fbb6ce; color: #b83280; }

      .table-info {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
      }

      .database-type {
        font-size: 0.8rem;
        color: #4a5568;
        background: #edf2f7;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
      }

      .redis-instance {
        font-size: 0.8rem;
        color: #4a5568;
        margin-bottom: 5px;
      }

      .table-description, .redis-description {
        color: #718096;
        margin-bottom: 10px;
        font-size: 0.9rem;
      }

      .table-files {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .file-path {
        font-size: 0.8rem;
        color: #a0aec0;
        background: #f7fafc;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: monospace;
      }

      .no-data {
        text-align: center;
        color: #a0aec0;
        font-style: italic;
        padding: 40px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }

      /* 테이블 스타일 */
      .table-container {
        overflow-x: auto;
        margin: 20px 0;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border: 1px solid #e2e8f0;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        margin: 0;
        border-spacing: 0;
      }

      thead {
        background: #4a5568;
        color: white;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      th {
        padding: 12px 16px;
        text-align: left;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        border-right: 1px solid #718096;
      }

      th:last-child {
        border-right: none;
      }

      td {
        padding: 12px 16px;
        text-align: left;
        font-size: 0.9rem;
        border: none;
        border-right: 1px solid #e2e8f0;
        border-bottom: 1px solid #e2e8f0;
        vertical-align: top;
      }

      td:last-child {
        border-right: none;
      }

      /* 번갈아가는 행 색상 (zebra striping) */
      tbody tr:nth-child(even) {
        background-color: #f8f9fa;
      }

      tbody tr:nth-child(odd) {
        background-color: white;
      }

      tbody tr:hover {
        background-color: #e6fffa !important;
        transition: background-color 0.2s ease;
      }

      tbody tr:hover td {
        background-color: transparent;
      }

      /* 상태 표시 스타일 */
      .success {
        color: #38a169;
        font-weight: 600;
      }

      .error {
        color: #e53e3e;
        font-weight: 600;
      }

      .warning {
        color: #d69e2e;
        font-weight: 600;
      }

      /* 키 타입 스타일 */
      .key-type {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .key-type-string { background: #e6fffa; color: #234e52; }
      .key-type-hash { background: #fef5e7; color: #744210; }
      .key-type-list { background: #e6f3ff; color: #1a365d; }
      .key-type-set { background: #f0fff4; color: #22543d; }
      .key-type-zset { background: #faf5ff; color: #553c9a; }
      .key-type-stream { background: #fff5f5; color: #742a2a; }
      .key-type-none { background: #f7fafc; color: #718096; }
      .key-type-unknown { background: #fed7d7; color: #742a2a; }

      /* ID 배지 컨테이너 */
      .id-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
      }

      /* 오류 및 경고 목록 스타일 */
      .error-list, .warning-list {
        margin: 20px 0;
      }

      .error-item, .warning-item {
        padding: 12px 16px;
        margin: 8px 0;
        border-radius: 6px;
        font-family: monospace;
        font-size: 0.9rem;
        line-height: 1.4;
      }

      .error-item {
        background: #fed7d7;
        border-left: 4px solid #e53e3e;
        color: #742a2a;
      }

      .warning-item {
        background: #fefcbf;
        border-left: 4px solid #d69e2e;
        color: #744210;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2rem;
        }

        .summary-grid {
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .summary-cards {
          grid-template-columns: 1fr;
        }

        .summary-card {
          flex-direction: column;
          text-align: center;
        }

        .summary-icon {
          margin-right: 0;
          margin-bottom: 10px;
        }

        .table-grid, .redis-grid {
          grid-template-columns: 1fr;
        }

        th, td {
          padding: 8px 12px;
          font-size: 0.8rem;
        }

        .table-container {
          font-size: 0.8rem;
        }
      }
    `;
  }

  /**
   * 검증 결과 HTML 생성
   */
  private static createVerificationHtml(result: VerificationResult): string {
    const timestamp = new Date().toLocaleString('ko-KR');
    const successRate = result.checkedItems > 0 ?
      (result.passedItems / result.checkedItems * 100).toFixed(1) : '0';

    return `<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>데이터 검증 결과</title>
    <style>
        ${this.getStyles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔍 데이터 검증 결과</h1>
            <div class="meta-info">
                <span>생성 시간: ${timestamp}</span>
                <span class="${result.success ? 'success' : 'error'}">
                    ${result.success ? '✅ 검증 통과' : '❌ 검증 실패'}
                </span>
            </div>
        </header>

        <section class="summary-section">
            <h2>📊 검증 요약</h2>
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-icon">📋</div>
                    <div class="summary-content">
                        <div class="summary-number">${result.checkedItems}</div>
                        <div class="summary-label">검증 항목</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">✅</div>
                    <div class="summary-content">
                        <div class="summary-number success">${result.passedItems}</div>
                        <div class="summary-label">통과</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">❌</div>
                    <div class="summary-content">
                        <div class="summary-number ${result.failedItems > 0 ? 'error' : ''}">${result.failedItems}</div>
                        <div class="summary-label">실패</div>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">📊</div>
                    <div class="summary-content">
                        <div class="summary-number">${successRate}%</div>
                        <div class="summary-label">성공률</div>
                    </div>
                </div>
            </div>
        </section>

        ${this.createVerificationDetailsHtml(result.details)}
    </div>
</body>
</html>`;
  }

  /**
   * 데이터베이스 결과 HTML 생성
   */
  private static createDatabaseResultsHtml(results: any[]): string {
    if (!results || results.length === 0) {
      return '<div class="section"><h2>🗄️ 데이터베이스 업데이트</h2><p class="no-data">데이터베이스 업데이트가 없습니다.</p></div>';
    }

    return `
      <div class="section">
        <h2>🗄️ 데이터베이스 업데이트 (${results.length}개)</h2>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>테이블</th>
                <th>업데이트 수</th>
                <th>상태</th>
                <th>소요 시간</th>
              </tr>
            </thead>
            <tbody>
              ${results.map(result => `
                <tr>
                  <td>${this.escapeHtml(result.tableName || 'Unknown')}</td>
                  <td>${result.updatedCount || 0}</td>
                  <td><span class="${result.success ? 'success' : 'error'}">${result.success ? '✅ 성공' : '❌ 실패'}</span></td>
                  <td>${((result.duration || 0) / 1000).toFixed(2)}s</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `;
  }

  /**
   * Redis 결과 HTML 생성
   */
  private static createRedisResultsHtml(results: any[]): string {
    if (!results || results.length === 0) {
      return '<div class="section"><h2>🔑 Redis 업데이트</h2><p class="no-data">Redis 업데이트가 없습니다.</p></div>';
    }

    return `
      <div class="section">
        <h2>🔑 Redis 업데이트 (${results.length}개)</h2>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>키 패턴</th>
                <th>타입</th>
                <th>인스턴스</th>
                <th>업데이트 수</th>
                <th>상태</th>
                <th>소요 시간</th>
              </tr>
            </thead>
            <tbody>
              ${results.map(result => `
                <tr>
                  <td><code>${this.escapeHtml(result.keyPattern || 'Unknown')}</code></td>
                  <td><span class="key-type key-type-${result.keyType || 'unknown'}">${this.getKeyTypeDisplay(result.keyType || 'unknown')}</span></td>
                  <td>${this.escapeHtml(result.instanceName || 'Unknown')}</td>
                  <td>${result.updatedCount || 0}</td>
                  <td><span class="${result.success ? 'success' : 'error'}">${result.success ? '✅ 성공' : '❌ 실패'}</span></td>
                  <td>${((result.duration || 0) / 1000).toFixed(2)}s</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `;
  }

  /**
   * 오류 및 경고 HTML 생성
   */
  private static createErrorsWarningsHtml(errors: string[], warnings: string[]): string {
    if ((!errors || errors.length === 0) && (!warnings || warnings.length === 0)) {
      return '';
    }

    let html = '';

    if (errors && errors.length > 0) {
      html += `
        <div class="section">
          <h2>❌ 오류 (${errors.length}개)</h2>
          <div class="error-list">
            ${errors.map(error => `<div class="error-item">${this.escapeHtml(error)}</div>`).join('')}
          </div>
        </div>
      `;
    }

    if (warnings && warnings.length > 0) {
      html += `
        <div class="section">
          <h2>⚠️ 경고 (${warnings.length}개)</h2>
          <div class="warning-list">
            ${warnings.map(warning => `<div class="warning-item">${this.escapeHtml(warning)}</div>`).join('')}
          </div>
        </div>
      `;
    }

    return html;
  }

  /**
   * 검증 세부사항 HTML 생성
   */
  private static createVerificationDetailsHtml(details: any[]): string {
    if (!details || details.length === 0) {
      return '<div class="section"><h2>📋 검증 세부사항</h2><p class="no-data">검증 세부사항이 없습니다.</p></div>';
    }

    return `
      <div class="section">
        <h2>📋 검증 세부사항 (${details.length}개)</h2>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>항목</th>
                <th>타입</th>
                <th>상태</th>
                <th>메시지</th>
              </tr>
            </thead>
            <tbody>
              ${details.map(detail => `
                <tr>
                  <td>${this.escapeHtml(detail.item || 'Unknown')}</td>
                  <td>${this.escapeHtml(detail.type || 'Unknown')}</td>
                  <td><span class="${detail.success ? 'success' : 'error'}">${detail.success ? '✅ 통과' : '❌ 실패'}</span></td>
                  <td>${this.escapeHtml(detail.message || '')}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    `;
  }

  /**
   * 통합된 리맵핑 결과 HTML 생성 (월드 컬럼 포함)
   */
  private static createUnifiedRemapHtml(result: GroupedRemapResult, databaseConfig?: any): string {
    const statusClass = result.success ? 'success' : 'error';
    const statusIcon = result.success ? '✅' : '❌';

    return `
      <!DOCTYPE html>
      <html lang="ko">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>리맵핑 결과 보고서</title>
        <style>
        ${this.getStyles()}
        .world-column {
          text-align: center;
          vertical-align: middle;
          font-weight: bold;
          background-color: #f8f9fa;
        }
        .world-dash {
          color: #6c757d;
        }

        .shard-column {
          text-align: center;
          font-family: monospace;
          font-weight: 600;
          color: #495057;
        }

        /* 총계 행 스타일 */
        .total-row {
          background-color: #f7fafc !important;
          border-top: 1px solid #a0aec0 !important;
          font-weight: bold;
        }

        .total-row td {
          background-color: #f7fafc !important;
          border-top: 1px solid #a0aec0 !important;
          font-weight: bold;
          color: #2d3748;
        }
        </style>
      </head>
      <body>
        <div class="container">
          <header class="header">
            <h1>🔄 리맵핑 결과 보고서</h1>
            <p class="timestamp">생성일시: ${result.startTime.toLocaleString('ko-KR')}</p>
            <div class="status ${statusClass}">
              ${statusIcon} ${result.success ? '성공' : '실패'}
            </div>
          </header>

          ${this.createGroupedSummarySection(result)}
          ${this.createUnifiedDatabaseSection(result, databaseConfig)}
          ${this.createUnifiedRedisSection(result, databaseConfig)}
          ${this.createErrorsWarningsHtml(result.errors, result.warnings)}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 통합된 데이터베이스 섹션 생성 (월드 컬럼 포함)
   */
  private static createUnifiedDatabaseSection(result: GroupedRemapResult, databaseConfig?: any): string {
    // 모든 데이터베이스 결과를 월드 정보와 함께 수집
    const allDatabaseResults: Array<{ worldId: string | null, result: any }> = [];

    // 공유 리소스 추가 (월드 무관)
    result.sharedResults.databaseResults.forEach(dbResult => {
      allDatabaseResults.push({ worldId: null, result: dbResult });
    });

    // 월드별 리소스 추가
    result.worldResults.forEach(world => {
      world.databaseResults.forEach(dbResult => {
        allDatabaseResults.push({ worldId: world.worldId, result: dbResult });
      });
    });

    if (allDatabaseResults.length === 0) {
      return '';
    }

    // 월드별로 정렬 (null이 먼저, 그 다음 월드 이름순)
    allDatabaseResults.sort((a, b) => {
      if (a.worldId === null && b.worldId === null) return 0;
      if (a.worldId === null) return -1;
      if (b.worldId === null) return 1;
      return a.worldId.localeCompare(b.worldId);
    });

    return `
      <section class="database-results">
        <h2>📊 데이터베이스 테이블</h2>
        <div class="table-container">
          <table class="results-table">
            <thead>
              <tr>
                <th>월드</th>
                <th>데이터베이스</th>
                <th>테이블</th>
                <th>처리된 레코드</th>
                <th>성공</th>
                <th>실패</th>
                <th>처리 시간</th>
                <th>상태</th>
              </tr>
            </thead>
            <tbody>
              ${this.createUnifiedDatabaseRows(allDatabaseResults, databaseConfig)}
            </tbody>
          </table>
        </div>
      </section>
    `;
  }

  /**
   * 통합된 데이터베이스 테이블 행 생성 (rowspan 적용)
   */
  private static createUnifiedDatabaseRows(results: Array<{ worldId: string | null, result: any }>, databaseConfig?: any): string {
    let html = '';
    let currentWorld = '';
    let worldRowCount = 0;
    let worldStartIndex = 0;

    // 각 월드별 행 수 계산 (샤드 정보 제거)
    const worldCounts = new Map<string, number>();
    results.forEach(item => {
      let worldKey = '-';
      if (item.worldId) {
        // 샤드 정보가 포함된 경우 제거 (예: UWO-GL-01_00 -> UWO-GL-01)
        worldKey = item.worldId.includes('_')
          ? item.worldId.split('_')[0] || item.worldId
          : item.worldId;
      }
      worldCounts.set(worldKey, (worldCounts.get(worldKey) || 0) + 1);
    });

    results.forEach((item, index) => {
      // 월드 키에서 샤드 정보 제거
      let worldKey = '-';
      if (item.worldId) {
        worldKey = item.worldId.includes('_')
          ? item.worldId.split('_')[0] || item.worldId
          : item.worldId;
      }
      const dbResult = item.result;

      // 새로운 월드 시작
      if (worldKey !== currentWorld) {
        currentWorld = worldKey;
        worldRowCount = worldCounts.get(worldKey) || 1;
        worldStartIndex = index;
      }

      const isFirstRowOfWorld = index === worldStartIndex;

      // 월드 표시에서 샤드 정보 제거
      let worldDisplay = '<span class="world-dash">-</span>';
      if (item.worldId) {
        // 샤드 정보가 포함된 경우 제거 (예: UWO-GL-01_00 -> UWO-GL-01)
        const cleanWorldId = item.worldId.includes('_')
          ? item.worldId.split('_')[0] || item.worldId
          : item.worldId;
        worldDisplay = cleanWorldId;
      }

      // 테이블 이름에서 데이터베이스와 테이블 분리
      const fullTableName = dbResult.tableName || '';
      let databaseName = '';
      let tableName = '';

      if (fullTableName.includes('.')) {
        const parts = fullTableName.split('.');
        let rawDatabaseName = parts[0];
        let rawTableName = parts.slice(1).join('.');

        // 실제 데이터베이스 이름 추출 (주소 포함)
        databaseName = this.extractRealDatabaseNameWithAddress(rawDatabaseName, databaseConfig);

        tableName = rawTableName;
      } else {
        databaseName = '-';
        tableName = fullTableName;
      }

      html += `
        <tr>
          ${isFirstRowOfWorld ? `<td class="world-column" rowspan="${worldRowCount}">${worldDisplay}</td>` : ''}
          <td>${databaseName}</td>
          <td><code>${tableName}</code></td>
          <td class="number">${(dbResult.affectedRows || 0).toLocaleString()}</td>
          <td class="number success">${dbResult.success ? (dbResult.affectedRows || 0).toLocaleString() : '0'}</td>
          <td class="number ${dbResult.error ? 'error' : ''}">${dbResult.error ? '1' : '0'}</td>
          <td class="number">${((dbResult.executionTime || 0) / 1000).toFixed(2)}s</td>
          <td class="status ${dbResult.success ? 'success' : 'error'}">
            ${dbResult.success ? '✅ 성공' : '❌ 실패'}
          </td>
        </tr>
      `;
    });

    // 총계 행 추가
    const totalExecutionTime = results.reduce((sum: number, item: any) => sum + (item.result.executionTime || 0), 0);
    const totalAffectedRows = results.reduce((sum: number, item: any) => sum + (Number(item.result.affectedRows) || 0), 0);
    const totalSuccessCount = results.filter((item: any) => item.result.success).length;
    const totalErrorCount = results.filter((item: any) => !item.result.success).length;

    // 성공한 항목들의 affectedRows 합계
    const successfulAffectedRows = results
      .filter((item: any) => item.result.success)
      .reduce((sum: number, item: any) => sum + (Number(item.result.affectedRows) || 0), 0);

    // 전체 상태 결정
    let overallStatus = '';
    let statusClass = '';
    if (totalErrorCount === 0) {
      overallStatus = '✅ 모두 성공';
      statusClass = 'success';
    } else if (totalSuccessCount === 0) {
      overallStatus = '❌ 모두 실패';
      statusClass = 'error';
    } else {
      overallStatus = '⚠️ 부분 실패';
      statusClass = 'warning';
    }

    html += `
      <tr class="total-row">
        <td><strong>총계</strong></td>
        <td></td>
        <td></td>
        <td class="number"><strong>${totalAffectedRows.toLocaleString()}</strong></td>
        <td class="number success"><strong>${successfulAffectedRows.toLocaleString()}</strong></td>
        <td class="number ${totalErrorCount > 0 ? 'error' : ''}"><strong>${totalErrorCount.toLocaleString()}</strong></td>
        <td class="number"><strong>${(totalExecutionTime / 1000).toFixed(2)}s</strong></td>
        <td class="status ${statusClass}"><strong>${overallStatus}</strong></td>
      </tr>
    `;

    return html;
  }

  /**
   * 통합된 Redis 섹션 생성 (월드 컬럼 포함)
   */
  private static createUnifiedRedisSection(result: GroupedRemapResult, databaseConfig?: any): string {
    // 모든 Redis 결과를 월드 정보와 함께 수집
    const allRedisResults: Array<{ worldId: string | null, result: any }> = [];

    // 공유 리소스 추가 (월드 무관)
    result.sharedResults.redisResults.forEach(redisResult => {
      allRedisResults.push({ worldId: null, result: redisResult });
    });

    // 월드별 리소스 추가
    result.worldResults.forEach(world => {
      world.redisResults.forEach(redisResult => {
        allRedisResults.push({ worldId: world.worldId, result: redisResult });
      });
    });

    if (allRedisResults.length === 0) {
      return '';
    }

    // 월드별로 정렬 (null이 먼저, 그 다음 월드 이름순)
    allRedisResults.sort((a, b) => {
      if (a.worldId === null && b.worldId === null) return 0;
      if (a.worldId === null) return -1;
      if (b.worldId === null) return 1;
      return a.worldId.localeCompare(b.worldId);
    });

    return `
      <section class="redis-results">
        <h2>🔑 Redis 키 패턴</h2>
        <div class="table-container">
          <table class="results-table">
            <thead>
              <tr>
                <th>월드</th>
                <th>데이터베이스</th>
                <th>키 패턴</th>
                <th>처리된 키</th>
                <th>성공</th>
                <th>실패</th>
                <th>처리 시간</th>
                <th>상태</th>
              </tr>
            </thead>
            <tbody>
              ${this.createUnifiedRedisRows(allRedisResults, databaseConfig)}
            </tbody>
          </table>
        </div>
      </section>
    `;
  }

  /**
   * 통합된 Redis 테이블 행 생성 (rowspan 적용)
   */
  private static createUnifiedRedisRows(results: Array<{ worldId: string | null, result: any }>, databaseConfig?: any): string {
    let html = '';
    let currentWorld = '';
    let worldRowCount = 0;
    let worldStartIndex = 0;

    // 각 월드별 행 수 계산 (샤드 정보 제거)
    const worldCounts = new Map<string, number>();
    results.forEach(item => {
      let worldKey = '-';
      if (item.worldId) {
        // 샤드 정보가 포함된 경우 제거 (예: UWO-GL-01_00 -> UWO-GL-01)
        worldKey = item.worldId.includes('_')
          ? item.worldId.split('_')[0] || item.worldId
          : item.worldId;
      }
      worldCounts.set(worldKey, (worldCounts.get(worldKey) || 0) + 1);
    });

    results.forEach((item, index) => {
      // 월드 키에서 샤드 정보 제거
      let worldKey = '-';
      if (item.worldId) {
        worldKey = item.worldId.includes('_')
          ? item.worldId.split('_')[0] || item.worldId
          : item.worldId;
      }
      const redisResult = item.result;

      // 새로운 월드 시작
      if (worldKey !== currentWorld) {
        currentWorld = worldKey;
        worldRowCount = worldCounts.get(worldKey) || 1;
        worldStartIndex = index;
      }

      const isFirstRowOfWorld = index === worldStartIndex;

      // 월드 표시에서 샤드 정보 제거
      let worldDisplay = '<span class="world-dash">-</span>';
      if (item.worldId) {
        // 샤드 정보가 포함된 경우 제거 (예: UWO-GL-01_00 -> UWO-GL-01)
        const cleanWorldId = item.worldId.includes('_')
          ? item.worldId.split('_')[0] || item.worldId
          : item.worldId;
        worldDisplay = cleanWorldId;
      }

      // Redis 데이터베이스 정보 추출
      const redisDbInfo = this.extractRedisDbInfo(redisResult, item.worldId, databaseConfig);

      html += `
        <tr>
          ${isFirstRowOfWorld ? `<td class="world-column" rowspan="${worldRowCount}">${worldDisplay}</td>` : ''}
          <td>${redisDbInfo}</td>
          <td><code>${redisResult.keyPattern}</code></td>
          <td class="number">${(redisResult.updatedKeys || 0).toLocaleString()}</td>
          <td class="number success">${(redisResult.updatedKeys || 0).toLocaleString()}</td>
          <td class="number ${redisResult.error ? 'error' : ''}">${redisResult.error ? '1' : '0'}</td>
          <td class="number">${((redisResult.executionTime || 0) / 1000).toFixed(2)}s</td>
          <td class="status ${redisResult.success ? 'success' : 'error'}">
            ${redisResult.success ? '✅ 성공' : '❌ 실패'}
          </td>
        </tr>
      `;
    });

    // 총계 행 추가
    const totalExecutionTime = results.reduce((sum: number, item: any) => sum + (item.result.executionTime || 0), 0);
    const totalUpdatedKeys = results.reduce((sum: number, item: any) => sum + (Number(item.result.updatedKeys) || 0), 0);
    const totalSuccessCount = results.filter((item: any) => item.result.success).length;
    const totalErrorCount = results.filter((item: any) => !item.result.success).length;

    // 성공한 항목들의 updatedKeys 합계
    const successfulUpdatedKeys = results
      .filter((item: any) => item.result.success)
      .reduce((sum: number, item: any) => sum + (Number(item.result.updatedKeys) || 0), 0);

    // 전체 상태 결정
    let overallStatus = '';
    let statusClass = '';
    if (totalErrorCount === 0) {
      overallStatus = '✅ 모두 성공';
      statusClass = 'success';
    } else if (totalSuccessCount === 0) {
      overallStatus = '❌ 모두 실패';
      statusClass = 'error';
    } else {
      overallStatus = '⚠️ 부분 실패';
      statusClass = 'warning';
    }

    html += `
      <tr class="total-row">
        <td><strong>총계</strong></td>
        <td></td>
        <td></td>
        <td class="number"><strong>${totalUpdatedKeys.toLocaleString()}</strong></td>
        <td class="number success"><strong>${successfulUpdatedKeys.toLocaleString()}</strong></td>
        <td class="number ${totalErrorCount > 0 ? 'error' : ''}"><strong>${totalErrorCount.toLocaleString()}</strong></td>
        <td class="number"><strong>${(totalExecutionTime / 1000).toFixed(2)}s</strong></td>
        <td class="status ${statusClass}"><strong>${overallStatus}</strong></td>
      </tr>
    `;

    return html;
  }

  /**
   * 그룹핑된 요약 섹션 생성
   */
  private static createGroupedSummarySection(result: GroupedRemapResult): string {
    const totalDatabaseResults = result.sharedResults.databaseResults.length +
      result.worldResults.reduce((sum, world) => sum + world.databaseResults.length, 0);
    const totalRedisResults = result.sharedResults.redisResults.length +
      result.worldResults.reduce((sum, world) => sum + world.redisResults.length, 0);

    return `
      <section class="summary-section">
        <h2>📊 실행 요약</h2>
        <div class="summary-cards">
          <div class="summary-card">
            <div class="summary-icon">📝</div>
            <div class="summary-content">
              <div class="summary-number">${result.totalProcessed.toLocaleString()}</div>
              <div class="summary-label">처리된 레코드</div>
            </div>
          </div>
          <div class="summary-card">
            <div class="summary-icon">🗄️</div>
            <div class="summary-content">
              <div class="summary-number">${totalDatabaseResults}</div>
              <div class="summary-label">데이터베이스 테이블</div>
            </div>
          </div>
          <div class="summary-card">
            <div class="summary-icon">🔑</div>
            <div class="summary-content">
              <div class="summary-number">${totalRedisResults}</div>
              <div class="summary-label">Redis 키 패턴</div>
            </div>
          </div>
          <div class="summary-card">
            <div class="summary-icon">🌍</div>
            <div class="summary-content">
              <div class="summary-number">${result.worldResults.length}</div>
              <div class="summary-label">처리된 월드</div>
            </div>
          </div>
          <div class="summary-card">
            <div class="summary-icon">⏱️</div>
            <div class="summary-content">
              <div class="summary-number">${(result.duration / 1000).toFixed(2)}s</div>
              <div class="summary-label">실행 시간</div>
            </div>
          </div>
        </div>
      </section>
    `;
  }

  /**
   * 공유 리소스 섹션 생성
   */
  private static createSharedResourcesSection(result: GroupedRemapResult): string {
    if (result.sharedResults.databaseResults.length === 0 && result.sharedResults.redisResults.length === 0) {
      return '';
    }

    return `
      <section class="shared-resources">
        <h2>🌐 공유 리소스 (월드 무관)</h2>

        ${result.sharedResults.databaseResults.length > 0 ? `
          <div class="subsection">
            <h3>📊 데이터베이스 테이블</h3>
            ${this.createDatabaseResultsHtml(result.sharedResults.databaseResults)}
          </div>
        ` : ''}

        ${result.sharedResults.redisResults.length > 0 ? `
          <div class="subsection">
            <h3>🔑 Redis 키 패턴</h3>
            ${this.createRedisResultsHtml(result.sharedResults.redisResults)}
          </div>
        ` : ''}
      </section>
    `;
  }

  /**
   * 월드별 리소스 섹션 생성
   */
  private static createWorldResourcesSection(result: GroupedRemapResult): string {
    if (result.worldResults.length === 0) {
      return '';
    }

    return `
      <section class="world-resources">
        <h2>🌍 월드별 리소스</h2>

        ${result.worldResults.map(world => `
          <div class="world-section">
            <h3>🌍 ${world.worldId}</h3>

            ${world.databaseResults.length > 0 ? `
              <div class="subsection">
                <h4>📊 데이터베이스 테이블</h4>
                ${this.createDatabaseResultsHtml(world.databaseResults)}
              </div>
            ` : ''}

            ${world.redisResults.length > 0 ? `
              <div class="subsection">
                <h4>🔑 Redis 키 패턴</h4>
                ${this.createRedisResultsHtml(world.redisResults)}
              </div>
            ` : ''}
          </div>
        `).join('')}
      </section>
    `;
  }

  /**
   * 실제 데이터베이스 이름 추출 (주소 포함)
   */
  private static extractRealDatabaseNameWithAddress(rawDatabaseName: string, databaseConfig?: any): string {
    if (!databaseConfig) {
      // 설정이 없으면 기본 로직 사용
      return this.fallbackDatabaseName(rawDatabaseName);
    }

    try {
      // user 테이블의 경우 샤드 정보 포함
      if (rawDatabaseName.includes('user-') && rawDatabaseName.includes('_')) {
        const match = rawDatabaseName.match(/user-([^_]+)_(\d+)/);
        if (match) {
          const worldId = match[1];
          const shardId = parseInt(match[2] || '0');

          // 해당 월드의 user 데이터베이스 설정에서 샤드 정보 찾기
          const world = databaseConfig.worlds?.find((w: any) => w.id === worldId);
          if (world?.mysqlUserDb?.shards) {
            const shard = world.mysqlUserDb.shards.find((s: any) => s.shardId === shardId);
            if (shard?.sqlCfg?.database) {
              const dbConfig = { ...world.mysqlUserDb.sqlDefaultCfg, ...shard.sqlCfg };
              const address = `${dbConfig.host || 'localhost'}:${dbConfig.port || 3306}`;
              return `${shard.sqlCfg.database}(${address})`;
            }
          }

          // 기본 패턴으로 fallback
          return `uwo_user_${(match[2] || '00').padStart(2, '0')}`;
        }
      }

      // world 테이블의 경우
      if (rawDatabaseName.includes('world-')) {
        // world-UWO-GL-01에서 UWO-GL-01 추출
        const worldId = rawDatabaseName.substring(6); // 'world-' 제거
        const world = databaseConfig.worlds?.find((w: any) => w.id === worldId);
        if (world?.mysqlWorldDb?.database) {
          const host = world.mysqlWorldDb.host || 'localhost';
          const port = world.mysqlWorldDb.port || 3306;
          const address = `${host}:${port}`;
          return `${world.mysqlWorldDb.database}(${address})`;
        }
        return 'uwo_world';
      }

      // auth 테이블의 경우
      if (rawDatabaseName === 'auth') {
        if (databaseConfig.sharedConfig?.mysqlAuthDb?.database) {
          const authConfig = databaseConfig.sharedConfig.mysqlAuthDb;
          const address = `${authConfig.host || 'localhost'}:${authConfig.port || 3306}`;
          return `${authConfig.database}(${address})`;
        }
        return 'uwo_auth';
      }

      return rawDatabaseName;
    } catch (error) {
      console.warn(`Failed to extract database name for ${rawDatabaseName}:`, error);
      return this.fallbackDatabaseName(rawDatabaseName);
    }
  }

  /**
   * 실제 데이터베이스 이름 추출
   */
  private static extractRealDatabaseName(rawDatabaseName: string, databaseConfig?: any): string {
    if (!databaseConfig) {
      // 설정이 없으면 기본 로직 사용
      return this.fallbackDatabaseName(rawDatabaseName);
    }

    try {
      // user 테이블의 경우 샤드 정보 포함
      if (rawDatabaseName.includes('user-') && rawDatabaseName.includes('_')) {
        const match = rawDatabaseName.match(/user-([^_]+)_(\d+)/);
        if (match) {
          const worldId = match[1];
          const shardId = parseInt(match[2] || '0');

          // 해당 월드의 user 데이터베이스 설정에서 샤드 정보 찾기
          const world = databaseConfig.worlds?.find((w: any) => w.id === worldId);
          if (world?.mysqlUserDb?.shards) {
            const shard = world.mysqlUserDb.shards.find((s: any) => s.shardId === shardId);
            if (shard?.sqlCfg?.database) {
              return shard.sqlCfg.database;
            }
          }

          // 기본 패턴으로 fallback
          return `uwo_user_${(match[2] || '00').padStart(2, '0')}`;
        }
      }

      // world 테이블의 경우
      if (rawDatabaseName.includes('world-')) {
        const worldId = rawDatabaseName.split('-')[1];
        const world = databaseConfig.worlds?.find((w: any) => w.id === worldId);
        if (world?.mysqlWorldDb?.database) {
          return world.mysqlWorldDb.database;
        }
        return 'uwo_world';
      }

      // auth 테이블의 경우
      if (rawDatabaseName === 'auth') {
        if (databaseConfig.sharedConfig?.mysqlAuthDb?.database) {
          return databaseConfig.sharedConfig.mysqlAuthDb.database;
        }
        return 'uwo_auth';
      }

      return rawDatabaseName;
    } catch (error) {
      console.warn(`Failed to extract database name for ${rawDatabaseName}:`, error);
      return this.fallbackDatabaseName(rawDatabaseName);
    }
  }

  /**
   * 기본 데이터베이스 이름 로직 (fallback)
   */
  private static fallbackDatabaseName(rawDatabaseName: string): string {
    if (rawDatabaseName.includes('user-') && rawDatabaseName.includes('_')) {
      const match = rawDatabaseName.match(/user-([^_]+)_(\d+)/);
      if (match) {
        return `uwo_user_${(match[2] || '00').padStart(2, '0')}`;
      }
    } else if (rawDatabaseName.includes('world-')) {
      return 'uwo_world';
    } else if (rawDatabaseName === 'auth') {
      return 'uwo_auth';
    }
    return rawDatabaseName;
  }

  /**
   * Redis 데이터베이스 정보 추출
   */
  private static extractRedisDbInfo(redisResult: any, worldId: string | null, databaseConfig?: any): string {
    const instanceName = redisResult.instanceName;

    if (!instanceName || !databaseConfig) {
      return instanceName || '-';
    }

    try {
      // 공유 Redis 인스턴스들 (월드 무관)
      const sharedInstances = ['auth', 'monitor', 'order', 'globalMatch', 'globalBattleLog'];

      if (sharedInstances.includes(instanceName)) {
        // 공유 인스턴스의 경우 sharedConfig에서 찾기
        const sharedRedisKey = `${instanceName}Redis`;
        const redisConfig = databaseConfig.sharedConfig?.[sharedRedisKey];
        if (redisConfig?.redisCfg?.db !== undefined) {
          const host = redisConfig.redisCfg.host || 'localhost';
          const port = redisConfig.redisCfg.port || 6379;
          return `${instanceName}(db=${redisConfig.redisCfg.db}, ${host}:${port})`;
        }
        return instanceName;
      }

      // 월드별 인스턴스들
      if (worldId) {
        // 샤드 정보 제거 (UWO-GL-01_00 -> UWO-GL-01)
        const cleanWorldId = worldId.includes('_') ? worldId.split('_')[0] : worldId;
        const world = databaseConfig.worlds?.find((w: any) => w.id === cleanWorldId);

        if (world) {
          // userCache 인스턴스 처리 (샤딩됨)
          if (instanceName === 'userCache') {
            const userCacheConfig = world.userCacheRedis;
            if (userCacheConfig?.redisCfg?.db !== undefined) {
              const host = userCacheConfig.redisCfg.host || 'localhost';
              const port = userCacheConfig.redisCfg.port || 6379;
              return `userCache(db=${userCacheConfig.redisCfg.db}, ${host}:${port})`;
            }
          }

          // 다른 월드별 인스턴스들
          const redisConfigKey = `${instanceName}Redis`;
          const redisConfig = world[redisConfigKey];
          if (redisConfig?.redisCfg?.db !== undefined) {
            const host = redisConfig.redisCfg.host || 'localhost';
            const port = redisConfig.redisCfg.port || 6379;
            return `${instanceName}(db=${redisConfig.redisCfg.db}, ${host}:${port})`;
          }
        }
      }

      return instanceName;
    } catch (error) {
      console.warn(`Failed to extract Redis DB info for ${instanceName}:`, error);
      return instanceName || '-';
    }
  }

  /**
   * Redis 키 정보에서 데이터베이스 정보 추출 (분석 보고서용)
   */
  private static extractRedisDbInfoFromKey(key: any, databaseConfig?: any): string {
    const instanceName = key.redisInstance;

    if (!instanceName || !databaseConfig) {
      return instanceName || '-';
    }

    try {
      // 공유 Redis 인스턴스들 (월드 무관)
      const sharedInstances = ['auth', 'monitor', 'order', 'globalMatch', 'globalBattleLog'];

      if (sharedInstances.includes(instanceName)) {
        // 공유 인스턴스의 경우 sharedConfig에서 찾기
        const sharedRedisKey = `${instanceName}Redis`;
        const redisConfig = databaseConfig.sharedConfig?.[sharedRedisKey];
        if (redisConfig?.redisCfg?.db !== undefined) {
          return `${instanceName}(${redisConfig.redisCfg.db})`;
        }
        return instanceName;
      }

      // 월드별 인스턴스들 - 첫 번째 월드의 설정을 사용
      if (databaseConfig.worlds && databaseConfig.worlds.length > 0) {
        const firstWorld = databaseConfig.worlds[0];

        // userCache 인스턴스 처리 (샤딩됨)
        if (instanceName === 'userCache') {
          const userCacheConfig = firstWorld.userCacheRedis;
          if (userCacheConfig?.redisCfg?.db !== undefined) {
            return `userCache(<code>샤딩됨</code>)`;
          }
        }

        // 다른 월드별 인스턴스들
        const redisConfigKey = `${instanceName}Redis`;
        const redisConfig = firstWorld[redisConfigKey];
        if (redisConfig?.redisCfg?.db !== undefined) {
          return `${instanceName}(${redisConfig.redisCfg.db})`;
        }
      }

      return instanceName;
    } catch (error) {
      console.warn(`Failed to extract Redis DB info for key ${instanceName}:`, error);
      return instanceName || '-';
    }
  }

  /**
   * 컬럼 타입 포맷팅 (길이 정보 포함)
   */
  private static formatColumnType(column: any): string {
    // fullType이 있으면 우선 사용 (예: varchar(32), decimal(10,2))
    if (column.fullType) {
      return this.escapeHtml(column.fullType.toUpperCase());
    }

    const dataType = (column.dataType || 'Unknown').toUpperCase();

    // 길이 정보가 있는 경우 포함
    if (column.maxLength !== undefined && column.maxLength !== null) {
      return this.escapeHtml(`${dataType}(${column.maxLength})`);
    }

    // 숫자 타입의 경우 precision과 scale 정보 포함
    if (column.precision !== undefined && column.precision !== null) {
      if (column.scale !== undefined && column.scale !== null && column.scale > 0) {
        return this.escapeHtml(`${dataType}(${column.precision},${column.scale})`);
      } else {
        return this.escapeHtml(`${dataType}(${column.precision})`);
      }
    }

    return this.escapeHtml(dataType);
  }
}
