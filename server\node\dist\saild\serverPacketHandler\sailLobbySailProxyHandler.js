"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const tcp = __importStar(require("../../tcplib"));
const basic_packets_1 = require("../../tcplib/shared/basic-packets");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const typedi_1 = require("typedi");
const offlineSailingManager_1 = require("../offlineSailingManager");
const router = tcp.createPacketRouter();
router.on(basic_packets_1.LobbySailProxy, async (req, res) => {
    const offlineSailingManager = typedi_1.Container.get(offlineSailingManager_1.OfflineSailingManager);
    const packet = req.origin;
    const userId = req.userId;
    offlineSailingManager.hadlePacketForBot(userId, packet).catch((err) => {
        mlog_1.default.warn('handle bot-user packet error', {
            userId,
            error: err.message,
        });
    });
});
module.exports = router;
//# sourceMappingURL=sailLobbySailProxyHandler.js.map