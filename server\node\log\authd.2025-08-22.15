{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T06:15:17.068Z"}
{"url":"/logout","status":"200","response-time":"9.419","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:15:17.077Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:15:17.078Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.957","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:15:17.081Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T06:16:06.623Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T06:16:06.623Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T06:16:07.380Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:16:07.383Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:16:07.383Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T06:16:07.385Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:16:07.385Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:16:07.385Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T06:16:07.386Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T06:16:07.387Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:16:07.387Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:16:07.387Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T06:16:07.388Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:16:07.388Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:16:07.388Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:16:07.388Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:16:07.389Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T06:16:07.389Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T06:16:07.389Z"}
{"environment":"development","type":"authd","gitCommitHash":"3e628d45a9f","gitCommitMessage":"UWO FGT Survey 기능 추가(테스트용)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-22T14:28:39+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T06:16:10.757Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:16:36.276Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T06:16:36.537Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T06:16:36.538Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T06:16:36.538Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T06:16:36.539Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T06:16:40.984Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T06:16:40.985Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T06:16:40.986Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T06:16:40.993Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T06:16:40.994Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T06:16:41.009Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T06:16:41.091Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.111Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.127Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.137Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.151Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.163Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.181Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.198Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.214Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:41.232Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T06:16:41.322Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T06:16:41.323Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T06:16:41.329Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T06:16:41.422Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T06:16:41.425Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T06:16:41.426Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T06:16:41.426Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T06:16:41.429Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T06:16:41.429Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T06:16:41.430Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T06:16:41.430Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T06:16:41.433Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T06:16:41.434Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T06:16:41.434Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T06:16:41.435Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T06:16:41.436Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T06:16:41.438Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T06:16:41.439Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T06:16:42.393Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:42.394Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:42.394Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T06:16:42.395Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T06:16:42.432Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T06:16:42.432Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T06:16:42.441Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T06:16:42.441Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T06:16:42.452Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T06:16:42.452Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T06:16:42.462Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T06:16:42.462Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.562Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.608Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.627Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.663Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.717Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.759Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.816Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.870Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.912Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T06:16:42.926Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.933Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:42.968Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.032Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.057Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.076Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.477Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.498Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.523Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.546Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.564Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.582Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.610Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.650Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.690Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.705Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:43.722Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T06:16:43.742Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:16:43.746Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T06:26:54.776Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T06:26:54.777Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T06:26:55.726Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:26:55.727Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:26:55.728Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T06:26:55.730Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T06:26:55.730Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T06:26:55.731Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:26:55.732Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:26:55.732Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T06:26:55.732Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:26:55.733Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:26:55.733Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T06:26:55.733Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T06:26:55.733Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:26:55.733Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:26:55.733Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:26:55.734Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:26:55.734Z"}
{"environment":"development","type":"authd","gitCommitHash":"192d4f2758f","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T15:25:40+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T06:26:58.218Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:27:28.658Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T06:27:28.913Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T06:27:28.914Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T06:27:28.915Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T06:27:28.915Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T06:27:32.274Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T06:27:32.275Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T06:27:32.276Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T06:27:32.283Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T06:27:32.284Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T06:27:32.299Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T06:27:32.405Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.426Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.443Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.455Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.470Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.481Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.499Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.515Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.530Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:32.549Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T06:27:32.631Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T06:27:32.632Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T06:27:32.638Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T06:27:32.733Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T06:27:32.735Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T06:27:32.736Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T06:27:32.736Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T06:27:32.739Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T06:27:32.739Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T06:27:32.740Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T06:27:32.740Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T06:27:32.744Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T06:27:32.744Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T06:27:32.744Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T06:27:32.745Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T06:27:32.746Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T06:27:32.748Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T06:27:32.748Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T06:27:33.814Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:33.814Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:33.815Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T06:27:33.815Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T06:27:33.860Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T06:27:33.860Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T06:27:33.871Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T06:27:33.871Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T06:27:33.881Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T06:27:33.881Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T06:27:33.891Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T06:27:33.892Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.011Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.052Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.093Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.143Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.213Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.257Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.329Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.400Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.437Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T06:27:34.449Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.456Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.498Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.573Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.597Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:34.613Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.106Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.127Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.156Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.186Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.203Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.220Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.357Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.424Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.495Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.515Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:35.540Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T06:27:35.558Z"}
{"url":"/getWorldStates","status":"200","response-time":"307.726","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:27:35.561Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:27:35.563Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.659","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:27:38.413Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.339","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:27:43.373Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.762","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:27:48.349Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.931","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:27:53.613Z"}
{"body":{"platform":2,"gnidSessionToken":"8697466**********1755844077","revision":"192d4f2758fc0b3aa892814ed1ad6b966e2dd8ee","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":60629,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T06:27:58.374Z"}
{"_time":"2025-08-22 15:27:58","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":60629,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T06:27:58.391Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T06:27:58.391Z"}
{"url":"/getWorlds","status":"200","response-time":"65.955","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:27:58.392Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.820","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:27:58.431Z"}
{"body":{"platform":2,"sessionToken":"8697466**********1755844077","revision":"192d4f2758fc0b3aa892814ed1ad6b966e2dd8ee","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T06:27:59.593Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 15:28:00&endpointip=127.0.0.1&endpointport=0&guid=c01b7ce5d6ce4c0493f61b0f2eddfc3c&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=********************************","timestamp":"2025-08-22T06:28:00.025Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T06:28:00.327Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8697466**********1755844077","revision":"192d4f2758fc0b3aa892814ed1ad6b966e2dd8ee","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T06:28:00.338Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T06:28:00.341Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"4e948b69a41f7c54600a632bf8eee5dada69ba70","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T06:28:00.342Z"}
{"url":"/login","status":"200","response-time":"802.866","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:28:00.342Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"4e948b69a41f7c54600a632bf8eee5dada69ba70","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T06:28:00.406Z"}
{"url":"/enterWorld","status":"200","response-time":"6.933","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:28:00.412Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T06:28:00.497Z"}
{"url":"/getUserNames","status":"200","response-time":"0.979","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:28:00.497Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:28:00.509Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.047","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:28:00.511Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T06:33:08.230Z"}
{"url":"/logout","status":"200","response-time":"6.688","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:08.236Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:33:08.239Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.734","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:08.241Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.738","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:23.692Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.560","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:28.910Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.915","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:33.680Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.601","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:38.656Z"}
{"body":{"platform":2,"gnidSessionToken":"8930966**********1755844419","revision":"640c4d70ab652e1b73339f3750b5841715804ae4","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":18605,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T06:33:40.333Z"}
{"_time":"2025-08-22 15:33:40","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":18605,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T06:33:40.336Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T06:33:40.337Z"}
{"url":"/getWorlds","status":"200","response-time":"49.836","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:40.337Z"}
{"body":{"platform":2,"sessionToken":"8930966**********1755844419","revision":"640c4d70ab652e1b73339f3750b5841715804ae4","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T06:33:41.093Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 15:33:41&endpointip=127.0.0.1&endpointport=0&guid=cf0b099012544d40a5a55a904b6b1cbb&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=2155219a3323c16cc9f719fb30181598","timestamp":"2025-08-22T06:33:41.362Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T06:33:41.788Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8930966**********1755844419","revision":"640c4d70ab652e1b73339f3750b5841715804ae4","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T06:33:41.796Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T06:33:41.797Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"62fae28deb8983bc0513edefb9f3771915d08542","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T06:33:41.798Z"}
{"url":"/login","status":"200","response-time":"752.127","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:41.798Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"62fae28deb8983bc0513edefb9f3771915d08542","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T06:33:41.870Z"}
{"url":"/enterWorld","status":"200","response-time":"5.085","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:41.875Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T06:33:41.910Z"}
{"url":"/getUserNames","status":"200","response-time":"0.588","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:41.910Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:33:41.919Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.818","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:41.922Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.461","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:33:44.127Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T06:34:37.078Z"}
{"url":"/logout","status":"200","response-time":"3.386","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:34:37.081Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:34:37.083Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.505","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:34:37.084Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.407","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:40:41.431Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T06:40:42.311Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T06:40:42.312Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T06:40:43.038Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:40:43.041Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:40:43.042Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T06:40:43.043Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T06:40:43.044Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:40:43.044Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:40:43.044Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T06:40:43.045Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:40:43.045Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:40:43.045Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T06:40:43.046Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:40:43.046Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:40:43.047Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T06:40:43.047Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T06:40:43.047Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T06:40:43.048Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T06:40:43.048Z"}
{"environment":"development","type":"authd","gitCommitHash":"640c4d70ab6","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T15:30:35+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T06:40:45.747Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:41:06.231Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T06:41:06.530Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T06:41:06.530Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T06:41:06.531Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T06:41:06.532Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T06:41:10.573Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T06:41:10.575Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T06:41:10.576Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T06:41:10.583Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T06:41:10.584Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T06:41:10.600Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T06:41:10.690Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.711Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.728Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.739Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.753Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.764Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.780Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.796Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.815Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:10.832Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T06:41:10.915Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T06:41:10.916Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T06:41:10.921Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T06:41:11.017Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T06:41:11.020Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T06:41:11.021Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T06:41:11.021Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T06:41:11.024Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T06:41:11.025Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T06:41:11.025Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T06:41:11.026Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T06:41:11.030Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T06:41:11.031Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T06:41:11.031Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T06:41:11.032Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T06:41:11.033Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T06:41:11.035Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T06:41:11.035Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T06:41:11.992Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:11.993Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:11.993Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T06:41:11.994Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T06:41:12.032Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T06:41:12.032Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T06:41:12.042Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T06:41:12.042Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T06:41:12.051Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T06:41:12.051Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T06:41:12.060Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T06:41:12.060Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.169Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.206Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.231Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.277Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.335Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.415Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.425Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.470Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.503Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T06:41:12.514Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.519Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.590Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.608Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.631Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.648Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:12.667Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.041Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.060Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.080Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.093Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.107Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.131Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.186Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.225Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.240Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:13.266Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T06:41:13.293Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:41:13.295Z"}
{"url":"/getWorldStates","status":"200","response-time":"86.854","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:13.563Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.808","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:16.241Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.568","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:21.509Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.619","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:26.299Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.771","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:31.557Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.745","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:36.337Z"}
{"body":{"platform":2,"gnidSessionToken":"8402109**********1755844896","revision":"640c4d70ab652e1b73339f3750b5841715804ae4","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":57659,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T06:41:37.323Z"}
{"_time":"2025-08-22 15:41:37","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":57659,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T06:41:37.338Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T06:41:37.338Z"}
{"url":"/getWorlds","status":"200","response-time":"64.898","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:37.339Z"}
{"body":{"platform":2,"sessionToken":"8402109**********1755844896","revision":"640c4d70ab652e1b73339f3750b5841715804ae4","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T06:41:39.173Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 15:41:39&endpointip=127.0.0.1&endpointport=0&guid=********************************&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=7042153bdbe4b722c5aaebbee986bc25","timestamp":"2025-08-22T06:41:39.620Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T06:41:39.887Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8402109**********1755844896","revision":"640c4d70ab652e1b73339f3750b5841715804ae4","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T06:41:39.896Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T06:41:39.898Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"2feddcc3f94f9f602b51527a01fc9b99137a9006","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T06:41:39.899Z"}
{"url":"/login","status":"200","response-time":"782.915","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:39.900Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"2feddcc3f94f9f602b51527a01fc9b99137a9006","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T06:41:39.993Z"}
{"url":"/enterWorld","status":"200","response-time":"11.370","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:40.004Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T06:41:40.121Z"}
{"url":"/getUserNames","status":"200","response-time":"0.974","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:40.123Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:41:40.132Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.031","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:40.134Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.300","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:41:42.288Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T06:42:16.673Z"}
{"url":"/logout","status":"200","response-time":"5.233","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:16.678Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:42:16.680Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.348","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:16.682Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.124","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:34.126Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.409","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:38.964Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.303","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:43.916Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.800","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:48.900Z"}
{"body":{"platform":2,"gnidSessionToken":"8610178**********1755844968","revision":"58e140f834d3b431324f064d28d4fadef4dae3b5","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":17304,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T06:42:49.553Z"}
{"_time":"2025-08-22 15:42:49","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":17304,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T06:42:49.558Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T06:42:49.559Z"}
{"url":"/getWorlds","status":"200","response-time":"55.072","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:49.559Z"}
{"body":{"platform":2,"sessionToken":"8610178**********1755844968","revision":"58e140f834d3b431324f064d28d4fadef4dae3b5","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T06:42:50.383Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 15:42:50&endpointip=127.0.0.1&endpointport=0&guid=bc11177403324c1babab412c02a60525&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=925bca12000ec422ae7484d9c5c2ad4d","timestamp":"2025-08-22T06:42:50.470Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T06:42:50.516Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8610178**********1755844968","revision":"58e140f834d3b431324f064d28d4fadef4dae3b5","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T06:42:50.523Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T06:42:50.524Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"c0301b9c9b27d829c306070be371328eea5cfda9","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T06:42:50.525Z"}
{"url":"/login","status":"200","response-time":"186.723","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:50.525Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"c0301b9c9b27d829c306070be371328eea5cfda9","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T06:42:50.591Z"}
{"url":"/enterWorld","status":"200","response-time":"4.603","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:50.595Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T06:42:50.624Z"}
{"url":"/getUserNames","status":"200","response-time":"1.466","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:50.624Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:42:50.631Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.107","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:42:50.633Z"}
{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T06:44:05.768Z"}
{"url":"/logout","status":"200","response-time":"5.282","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:44:05.773Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T06:44:05.775Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.281","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T06:44:05.778Z"}
