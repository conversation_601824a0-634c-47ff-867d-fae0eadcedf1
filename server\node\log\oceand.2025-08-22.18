{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T09:00:00.086Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T09:00:37.933Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:00:37.934Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:00:38.034Z"}
{"level":"info","message":"[Session] socket disposed, OPolXvwR","timestamp":"2025-08-22T09:00:38.034Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T09:00:38.035Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T09:00:38.035Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:00:38.040Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T09:00:38.040Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:00:38.040Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:00:38.041Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:38.041Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:38.041Z"}
{"environment":"development","type":"oceand","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:00:40.759Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.332Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.333Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.333Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.333Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.333Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.334Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.334Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.334Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:01:13.334Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:01:13.341Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T09:01:13.344Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:01:13.537Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:01:13.538Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:01:13.538Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T09:01:13.539Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T09:01:17.036Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T09:01:17.037Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T09:01:17.037Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T09:01:17.044Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T09:01:17.044Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T09:01:17.060Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T09:01:17.143Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.163Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.177Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.188Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.202Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.213Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.229Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.245Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.260Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:17.277Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T09:01:17.355Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T09:01:17.356Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T09:01:17.360Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T09:01:17.473Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T09:01:17.477Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T09:01:17.477Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T09:01:17.478Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T09:01:17.481Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T09:01:17.481Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T09:01:17.481Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T09:01:17.481Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T09:01:17.483Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T09:01:17.484Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T09:01:17.484Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T09:01:17.485Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T09:01:17.486Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T09:01:17.488Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T09:01:17.488Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T09:01:17.583Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T09:01:17.625Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:01:17.631Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:01:17.631Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:01:17.631Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:01:17.631Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.692Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:01:17.705Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T09:01:17.708Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T09:01:17.709Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:01:17.712Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T09:01:17.713Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:01:17.794Z"}
{"pingInterval":2000,"curDate":1755853277,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T09:01:17.797Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T09:01:30.200Z"}
{"level":"info","message":"[SessionManager] session created: QddcPBGr, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T09:01:30.534Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T09:01:30.535Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T09:01:30.536Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T09:01:30.536Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.538Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.539Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.540Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.540Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.540Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.540Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.540Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:01:30.541Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T09:02:22.743Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T09:02:22.743Z"}
{"level":"info","message":"[Session] socket disposed, QddcPBGr","timestamp":"2025-08-22T09:02:22.743Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T09:03:04.604Z"}
{"level":"info","message":"[SessionManager] session created: 3m-igMUp, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T09:03:04.920Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T09:03:04.921Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T09:03:04.922Z"}
{"origin":{"bNeedWorldSyncData":false},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T09:03:04.922Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T09:04:29.416Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:04:29.416Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:04:29.566Z"}
{"level":"info","message":"[Session] socket disposed, 3m-igMUp","timestamp":"2025-08-22T09:04:29.566Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T09:04:29.566Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T09:04:29.567Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:04:29.569Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T09:04:29.569Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:04:29.570Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:04:29.570Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:29.570Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:29.570Z"}
{"environment":"development","type":"oceand","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:04:32.393Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.966Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.967Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.967Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.967Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.967Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.967Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.967Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.968Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:00.968Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:05:00.976Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T09:05:00.978Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:05:01.166Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:05:01.167Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:05:01.168Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T09:05:01.168Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T09:05:04.721Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T09:05:04.722Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T09:05:04.722Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T09:05:04.729Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T09:05:04.730Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T09:05:04.745Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T09:05:04.832Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.855Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.870Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.882Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.895Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.908Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.925Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.941Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.956Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:04.974Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T09:05:05.056Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T09:05:05.057Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T09:05:05.061Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T09:05:05.195Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T09:05:05.197Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T09:05:05.198Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T09:05:05.199Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T09:05:05.201Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T09:05:05.201Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T09:05:05.201Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T09:05:05.202Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T09:05:05.204Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T09:05:05.204Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T09:05:05.204Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T09:05:05.205Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T09:05:05.206Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T09:05:05.208Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T09:05:05.208Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T09:05:05.308Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T09:05:05.352Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:05.357Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:05.358Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:05.358Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:05.358Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:05.415Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:05.425Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T09:05:05.428Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T09:05:05.429Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:05:05.431Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T09:05:05.432Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:05:05.434Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:05:05.435Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755853505,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T09:05:05.525Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T09:05:05.525Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:05:06.436Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:05:06.436Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:05:07.437Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:05:07.438Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755853508,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T09:05:08.031Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T09:05:08.031Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:05:08.439Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:05:08.439Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:05:09.440Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:05:09.440Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:05:10.442Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:05:10.442Z"}
{"level":"warn","message":"updateServerdPing signaled to stop. begin stopping server","timestamp":"2025-08-22T09:05:11.042Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:05:11.042Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T09:05:11.042Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T09:05:11.043Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:05:11.045Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T09:05:11.046Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:05:11.046Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:05:11.047Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:05:11.047Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:05:11.047Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T09:05:11.048Z"}
{"environment":"development","type":"oceand","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:05:19.176Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.136Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.137Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.137Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.138Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.138Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.138Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.138Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.139Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:05:42.139Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:05:42.147Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T09:05:42.149Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:05:42.376Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:05:42.377Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:05:42.377Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T09:05:42.378Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T09:05:45.363Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T09:05:45.364Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T09:05:45.365Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T09:05:45.371Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T09:05:45.372Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T09:05:45.384Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T09:05:45.460Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.479Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.493Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.505Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.518Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.529Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.544Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.560Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.575Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:05:45.591Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T09:05:45.669Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T09:05:45.670Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T09:05:45.673Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T09:05:45.802Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T09:05:45.805Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T09:05:45.805Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T09:05:45.806Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T09:05:45.808Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T09:05:45.809Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T09:05:45.809Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T09:05:45.809Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T09:05:45.811Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T09:05:45.811Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T09:05:45.812Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T09:05:45.812Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T09:05:45.813Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T09:05:45.815Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T09:05:45.816Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T09:05:45.909Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T09:05:45.951Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:45.955Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:45.956Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:45.956Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:45.956Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:45.994Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:05:46.003Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T09:05:46.006Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T09:05:46.006Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:05:46.008Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T09:05:46.008Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:05:46.011Z"}
{"pingInterval":2000,"curDate":1755853546,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T09:05:46.013Z"}
{"level":"info","message":"[SessionManager] session created: I98QQ0Af, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T09:05:46.357Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T09:05:46.358Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T09:05:46.359Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T09:05:46.359Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.360Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.364Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.364Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.364Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.364Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.364Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.365Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:05:46.365Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T09:28:14.354Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:28:14.355Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:28:15.224Z"}
{"level":"info","message":"[Session] socket disposed, I98QQ0Af","timestamp":"2025-08-22T09:28:15.224Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T09:28:15.226Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T09:28:15.226Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:28:15.228Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T09:28:15.229Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:28:15.229Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:28:15.229Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:28:15.230Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:28:15.230Z"}
{"environment":"development","type":"oceand","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:28:18.446Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.472Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.473Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.473Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.473Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.473Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.474Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.474Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.474Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T09:28:47.474Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:28:47.483Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T09:28:47.485Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:28:47.741Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:28:47.742Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:28:47.742Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T09:28:47.743Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T09:28:51.524Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T09:28:51.525Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T09:28:51.526Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T09:28:51.532Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T09:28:51.533Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T09:28:51.547Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T09:28:51.631Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.652Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.668Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.679Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.692Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.703Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.720Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.736Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.751Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:28:51.769Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T09:28:51.848Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T09:28:51.849Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T09:28:51.853Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T09:28:51.980Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T09:28:51.982Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T09:28:51.983Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T09:28:51.983Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T09:28:51.986Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T09:28:51.986Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T09:28:51.986Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T09:28:51.987Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T09:28:51.988Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T09:28:51.989Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T09:28:51.989Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T09:28:51.990Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T09:28:51.991Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T09:28:51.993Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T09:28:51.993Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T09:28:52.090Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T09:28:52.134Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:28:52.139Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:28:52.139Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:28:52.139Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:28:52.139Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:52.198Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:28:52.209Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T09:28:52.213Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T09:28:52.213Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:28:52.215Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T09:28:52.216Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:28:52.218Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:28:52.218Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755854932,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T09:28:52.309Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T09:28:52.309Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:28:53.220Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:28:53.220Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:28:54.221Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:28:54.221Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755854935,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T09:28:55.021Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T09:28:55.021Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T09:28:55.223Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T09:28:55.224Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:28:56.300Z"}
{"pingInterval":2000,"curDate":1755854936,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T09:28:56.304Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T09:29:06.510Z"}
{"level":"info","message":"[SessionManager] session created: 1-6FTaJW, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T09:29:06.850Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T09:29:06.851Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T09:29:06.852Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T09:29:06.853Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.855Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.856Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.857Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.857Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.857Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.857Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.857Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T09:29:06.858Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T09:35:37.580Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:35:37.581Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:35:37.784Z"}
{"level":"info","message":"[Session] socket disposed, 1-6FTaJW","timestamp":"2025-08-22T09:35:37.784Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T09:35:37.784Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T09:35:37.785Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T09:35:37.787Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T09:35:37.788Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:35:37.788Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:35:37.788Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:35:37.789Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:35:37.789Z"}
