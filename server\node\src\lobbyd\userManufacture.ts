// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import { GLogParam, LoginInfo } from './user';
import cms from '../cms';
import * as cmsEx from '../cms/ex';
import * as sync from './type/sync';
import { CalcContentsResetTimePassingCount } from '../formula';
import { MError, MErrorCode } from '../motiflib/merror';
import mlog from '../motiflib/mlog';
import { MANUFACTURE_TYPE } from '../cms/manufactureExpDesc';
import { MANUFACTURE_PROGRESS_TYPE } from '../cms/manufactureRecipeDesc';
import { CompanyStat } from '../motiflib/stat/companyStat';
import { User } from './user';
import { ManufactureExpLevelChange } from '../motiflib/model/lobby';

export interface ManufactureExpLevelNub {
  [type: number]: {
    type: number;
    exp: number;
    level: number;
  };
}

export interface ManufacturePointChange {
  point: number;
  lastUpdatePointTimeUtc: number;
}

// 생산실 정보. mate 도 넣어야 할 듯
export interface ManufactureProgress {
  //roomCmsId: number;
  [slot: number]: {
    slot: number;
    recipeId: number;
    startTimeUtc: number;
    completionTimeUtc: number;
    resultType: MANUFACTURE_PROGRESS_TYPE;
    mateCmsIds: number[];
    successRate: number; // 성공 확률 (퍼센트 단위)
    greatSuccessRate: number; // 대성공 확률 (퍼센트 단위)
    extra: string; // 추가 데이터 (JSON)
  }
}

// 생산 유저 클래스
// 레시피는 각 생산 타입의 레벨에 따라 해금된다.
export class UserManufacture {
  private _point: number;
  private _lastUpdatePointTimeUtc: number;

  // 제조 경험치 레벨 데이터
  private _expLevelNub: ManufactureExpLevelNub;

  // 생산실 정보. 미배치, 생산중, 완료 의 상태를 저장
  _manufactureRoomInfo: { [roomCmsId: number]: ManufactureProgress } = {};

  // 해금된 레시피 정보
  private _unlockedRecipes: { [recipeCmsId: number]: boolean } = {};

  constructor() {
    this._expLevelNub = {
      [MANUFACTURE_TYPE.NONE]: { type: MANUFACTURE_TYPE.NONE, exp: 0, level: 1 },
      [MANUFACTURE_TYPE.CASTING]: { type: MANUFACTURE_TYPE.CASTING, exp: 0, level: 1 },
      [MANUFACTURE_TYPE.COOKING]: { type: MANUFACTURE_TYPE.COOKING, exp: 0, level: 1 },
      [MANUFACTURE_TYPE.SEWING]: { type: MANUFACTURE_TYPE.SEWING, exp: 0, level: 1 },
      [MANUFACTURE_TYPE.HANDMADE]: { type: MANUFACTURE_TYPE.HANDMADE, exp: 0, level: 1 },
      [MANUFACTURE_TYPE.MEDICINE]: { type: MANUFACTURE_TYPE.MEDICINE, exp: 0, level: 1 },
    };
  }

  get point() {
    return this._point;
  }

  get exps(): ManufactureExpLevelNub {
    return this._expLevelNub;
  }

  // 특정 제조 타입의 경험치 반환
  getExp(type: MANUFACTURE_TYPE): number {
    const expLevel = this._expLevelNub[type];
    return expLevel ? expLevel.exp : 0;
  }

  // 특정 제조 타입의 레벨 반환
  getLevel(type: MANUFACTURE_TYPE): number {
    const expLevel = this._expLevelNub[type];
    return expLevel ? expLevel.level : 1;
  }

  getRoom(roomId: number) {
    return this._manufactureRoomInfo[roomId];
  }

  clone(): UserManufacture {
    const newUserManufacture = new UserManufacture();
    newUserManufacture.cloneSet(
      this._point,
      this._lastUpdatePointTimeUtc,
      _.cloneDeep(this._expLevelNub),
      _.cloneDeep(this._manufactureRoomInfo),
      _.cloneDeep(this._unlockedRecipes)
    );
    return newUserManufacture;
  }

  cloneSet(
    point: number,
    lastUpdatePointTimeUtc: number,
    expLevel: ManufactureExpLevelNub,
    manufactureRoomInfo: { [roomCmsId: number]: ManufactureProgress },
    unlockedRecipes: { [recipeCmsId: number]: boolean }
  ): void {
    this._point = point;
    this._lastUpdatePointTimeUtc = lastUpdatePointTimeUtc;
    this._expLevelNub = expLevel;
    this._manufactureRoomInfo = manufactureRoomInfo;
    this._unlockedRecipes = unlockedRecipes;
  }

  initWithLoginInfo(loginInfo: LoginInfo) {
    this._point = loginInfo.manufacturePoint;
    this._lastUpdatePointTimeUtc = parseInt(loginInfo.lastUpdateManufacturePointTimeUtc, 10);
    this.setManufactureExpWithLoginInfo(loginInfo.manufactureExpLevel);
    
    // Load manufacture room info from loginInfo with startTimeUtc conversion
    if (loginInfo.manufactureRoomInfo) {
      this._manufactureRoomInfo = {};
      _.forOwn(loginInfo.manufactureRoomInfo, (roomProgress, roomCmsIdStr) => {
        const roomCmsId = parseInt(roomCmsIdStr, 10);
        this._manufactureRoomInfo[roomCmsId] = {};
        
        _.forOwn(roomProgress, (slotProgress, slotStr) => {
          const slot = parseInt(slotStr, 10);
          // DB에서 로드된 데이터를 새로운 형식으로 변환
          this._manufactureRoomInfo[roomCmsId][slot] = {
            ...slotProgress,
            // progressType은 이미 DB에서 올바른 값으로 로드됨
            // extra 필드도 이미 포함됨
          };
        });
      });
    }
    
    // Load unlocked recipes from loginInfo
    if (loginInfo.unlockedRecipes) {
      this._unlockedRecipes = loginInfo.unlockedRecipes;
    }
  }

  // 제조 경험치 데이터 설정
  setManufactureExpWithLoginInfo(expsFromDB: {
    castingExp: number;
    castingLevel: number;
    cookingExp: number;
    cookingLevel: number;
    sewingExp: number;
    sewingLevel: number;
    handmadeExp: number;
    handmadeLevel: number;
    medicineExp: number;
    medicineLevel: number;
  }): void {
    this._expLevelNub = {
      [MANUFACTURE_TYPE.CASTING]: {
        type: MANUFACTURE_TYPE.CASTING,
        exp: expsFromDB.castingExp,
        level: expsFromDB.castingLevel,
      },
      [MANUFACTURE_TYPE.COOKING]: {
        type: MANUFACTURE_TYPE.COOKING,
        exp: expsFromDB.cookingExp,
        level: expsFromDB.cookingLevel,
      },
      [MANUFACTURE_TYPE.SEWING]: {
        type: MANUFACTURE_TYPE.SEWING,
        exp: expsFromDB.sewingExp,
        level: expsFromDB.sewingLevel,
      },
      [MANUFACTURE_TYPE.HANDMADE]: {
        type: MANUFACTURE_TYPE.HANDMADE,
        exp: expsFromDB.handmadeExp,
        level: expsFromDB.handmadeLevel,
      },
      [MANUFACTURE_TYPE.MEDICINE]: {
        type: MANUFACTURE_TYPE.MEDICINE,
        exp: expsFromDB.medicineExp,
        level: expsFromDB.medicineLevel,
      },
    };
  }

  getCurrentPoint(curTimeUtc: number): number {
    return UserManufacture.getCurrentPoint(this._point, this._lastUpdatePointTimeUtc, curTimeUtc);
  }

  // admin 조회시 필요
  static getCurrentPoint(
    point: number,
    lastUpdatePointTimeUtc: number,
    curTimeUtc: number,
    timezone?: number
  ): number {
    const maxPoint = cms.Const.MaxManufacturePoint.value;

    // 최대 값이면 계산 없이 그냥 써도 된다.
    if (point >= maxPoint) {
      return point;
    }

    // 마지막 업데이트된 시간에서, 현재 시간까지 충전된 행동력을 계산.
    // 하루 단위고, 2일 지났으면 tickCount 2
    const tickCount = CalcContentsResetTimePassingCount(
      curTimeUtc,
      lastUpdatePointTimeUtc,
      cms.ContentsResetHour.ManufacturePoint.hour,
      timezone
    );

    const addPoint = cms.Const.GetManufacturePoint.value;
    let currentPoint = tickCount * addPoint + point;

    // 최대값 넘지 않도록.
    if (currentPoint > maxPoint) {
      currentPoint = maxPoint;
    }

    return currentPoint;
  }

  buildPointChange(curTimeUtc: number, delta: number): [boolean, ManufacturePointChange] {
    const maxPoint = cms.Const.MaxManufacturePoint.value;

    const currentPoint = this.getCurrentPoint(curTimeUtc);
    let newPoint = currentPoint + delta;

    // 차감할 포인트가 부족하면 실패
    if (newPoint < 0) {
      return [false, undefined];
    }

    // 최대값 못넘음
    if (newPoint > maxPoint) {
      newPoint = maxPoint;
    }

    const pointChange: ManufacturePointChange = {
      point: newPoint,
      lastUpdatePointTimeUtc: curTimeUtc,
    };

    return [true, pointChange];
  }

  applyPointChange(change: ManufacturePointChange, glogParam: GLogParam): sync.Sync {
    if (!change) {
      return {};
    }

    if (glogParam) {
      glogParam.user.glog('manufacture_point', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        cv: change.point - this._point,
        rv: change.point,
      });
    }

    this._lastUpdatePointTimeUtc = change.lastUpdatePointTimeUtc;
    this._point = change.point;

    return {
      add: {
        user: {
          lastUpdateManufacturePointTimeUtc: this._lastUpdatePointTimeUtc,
        },
        // 생산력은 sync data 에서는 point 로 전달한다.
        points: {
          [cmsEx.ManufacturePointCmsId]: {
            cmsId: cmsEx.ManufacturePointCmsId,
            value: this._point,
          },
        },
      },
    };
  }

  // -------------------------------------------------------------------------------
  // 경험치 획득시 패시브 효과 적용 시점과 calcExpLevel 시점은 분리한다.
  // -------------------------------------------------------------------------------
  buildManufactureExpLevelChanges(
    user: User,
    companyStat: CompanyStat,
    manufactureType: MANUFACTURE_TYPE,
    inAddExp: number,
    expLevelChanges: ManufactureExpLevelChange[]
  ): void {
    // todo. companyStat 에 의한 경험치 획득시 패시브 효과 적용

    const change = this.calcExpLevel(manufactureType, inAddExp);
    if (!change) {
      return;
    }

    expLevelChanges.push(change);
  }

  calcExpLevel(
    manufactureType: MANUFACTURE_TYPE,
    inAddExp: number
  ): ManufactureExpLevelChange | null {
    const curExp = this.getExp(manufactureType);
    const curLevel = this.getLevel(manufactureType);

    // check max over
    const maxLevel = cms.Const.MaxManufactureLv.value;
    const maxExp = cms.ManufactureExp[maxLevel].accumulateExp[manufactureType - 1];
    if (curExp >= maxExp || !inAddExp) {
      return null;
    }

    const newExp = Math.min(curExp + inAddExp, maxExp);
    const newLevel = cmsEx.calcManufactureLevel(manufactureType, newExp);

    return {
      type: manufactureType,
      exp: newExp,
      level: newLevel,
      oldLevel: curLevel,
    };
  }

  private _applyExpLevelChange(change: ManufactureExpLevelChange): sync.All {
    this._expLevelNub[change.type] = {
      type: change.type,
      exp: change.exp,
      level: change.level,
    };

    return {
      manufacture: {
        expLevel: {
          [change.type]: {
            type: change.type,
            exp: change.exp,
            level: change.level,
          },
        }
      },
    };
  }

  // -------------------------------------------------------------------------------
  // 제조 경험치 레벨의 변경사항을 메모리에 적용합니다.
  // -------------------------------------------------------------------------------
  applyExpLevelChange(changes: ManufactureExpLevelChange[], glogParam?: GLogParam): sync.Sync {
    if (!changes) {
      return {};
    }

    const syncData: sync.Sync = { add: {} };

    if (changes.length > 0) {
      for (const change of changes) {
        const oldExp = this.getExp(change.type);
        const oldLevel = this.getLevel(change.type);

        const syncAdd = this._applyExpLevelChange(change);
        _.merge<sync.Sync, sync.Sync>(syncData, { add: syncAdd });

        // glog 기록
        if (glogParam) {
          glogParam.user.glog('manufacture_exp', {
            rsn: glogParam.rsn,
            add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
            type: change.type,
            exp_cv: change.exp - oldExp,
            exp_rv: change.exp,
            level_cv: change.level - oldLevel,
            level_rv: change.level,
          });
        }
      }
    }

    return syncData;
  }

  applyManufactureRoomInfo(mrp: ManufactureProgress, roomCmsId: number) {
    this._manufactureRoomInfo[roomCmsId] = mrp;
  }

  getSyncData(): sync.All {
    const manufactureRoomInfo: {
      [roomCmsId: number]: {
        [slot: number]: {
          slot: number;
          recipeId: number;
          startTimeUtc: number;
          completionTimeUtc: number;
          resultType: MANUFACTURE_PROGRESS_TYPE;
          mateCmsIds: number[];
          successRate: number;
          greatSuccessRate: number;
          extra: string;
        }
      };
    } = {};

    _.forOwn(this._manufactureRoomInfo, (roomProgress, roomCmsIdStr) => {
      const roomCmsId = parseInt(roomCmsIdStr, 10);
      if (!manufactureRoomInfo[roomCmsId]) {
        manufactureRoomInfo[roomCmsId] = {};
      }
      _.forOwn(roomProgress, (slotProgress, slotStr) => {
        const slot = parseInt(slotStr, 10);
        manufactureRoomInfo[roomCmsId][slot] = {
          slot,
          recipeId: slotProgress.recipeId,
          startTimeUtc: slotProgress.startTimeUtc,
          completionTimeUtc: slotProgress.completionTimeUtc,
          resultType: slotProgress.resultType,
          mateCmsIds: slotProgress.mateCmsIds,
          successRate: slotProgress.successRate,
          greatSuccessRate: slotProgress.greatSuccessRate,
          extra: slotProgress.extra || '',
        };
      });
    });

    return {
      user: {
        lastUpdateManufacturePointTimeUtc: this._lastUpdatePointTimeUtc,
      },
      // 생산력은 sync data 에서는 point 로 전달한다.
      points: {
        [cmsEx.ManufacturePointCmsId]: {
          cmsId: cmsEx.ManufacturePointCmsId,
          value: this._point,
        },
      },
      manufacture: {
        expLevel: this._expLevelNub,
        roomInfo: manufactureRoomInfo,
        unlockedRecipes: this._unlockedRecipes,
      },
    };
  }

  // -------------------------------------------------------------------------------
  // 레시피 해금 관련 메서드들
  // -------------------------------------------------------------------------------

  // 특정 레시피가 해금되어 있는지 확인
  isRecipeGroupUnlocked(recipeCmsId: number): boolean {
    return !!this._unlockedRecipes[recipeCmsId];
  }

  // 레시피 해금
  unlockRecipeGroup(recipeCmsId: number): void {
    this._unlockedRecipes[recipeCmsId] = true;
  }

  // 해금된 레시피 목록 반환
  getUnlockedRecipeGroup(): { [recipeCmsId: number]: boolean } {
    return this._unlockedRecipes;
  }

  // 해금된 레시피 정보 설정 (DB에서 로드할 때 사용)
  setUnlockedRecipeGroup(unlockedRecipes: { [recipeCmsId: number]: boolean }): void {
    this._unlockedRecipes = unlockedRecipes;
  }
}
