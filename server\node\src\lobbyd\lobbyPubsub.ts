// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import Pubsub from '../redislib/pubsub';
import * as proto from '../proto/lobby/proto';
import { TownManager } from './townManager';
import { Resp } from './type/sync';
import { UserManager } from './userManager';
import * as cmsEx from '../cms/ex';
import {
  InvestmentSessionClosedPubMsg,
  ArenaSessionClosedPubMsg,
  DevelopmentNationSharePointChangedPubMsg,
  InvestmentSeasonRankSessionChangedPubMsg,
  InvestmentSeasonRankClosedPubMsg,
} from '../motiflib/model/realmd';
import {
  TownMayorChangedPubMsg,
  DevTownNationSharePointChangedPubMsg,
  TownInvestedPubMsg,
  TownMayorTaxChangedPubMsg,
  NationElectionRegisterCandidatePubMsg,
  NationElectionCandidatesModifiedPubMsg,
  NationElectionVotedToCandidatePubMsg,
  NationElectionVotesUpdatedPubMsg,
  NationElectionRemovedCandidatesPubMsg,
  NationCabinetMembersRemovedPubMsg,
  NationCabinetMemberAppointedPubMsg,
  NationCabinetMemberWageRateChangedPubMsg,
  VillageStorageUpdatedPubMsg,
  NationGoalPromiseChangedPubMsg,
  NationBudgetUpdatedPubMsg,
  NationPoliciesUpdatedPubMsg,
  NationSupportShopPurchasedPubMsg,
  NationNoticeUpdatedPubMsg,
  DevNationCabinetMembersAllReloadPubMsg,
  NationCabinetMemberThoughtChangedPubMsg,
  BoughtWebShopProductPubMsg,
} from '../motiflib/model/lobby';
import { NationElectionSessionClosedPubMsg } from '../motiflib/model/realmd';
import { ArenaManager } from './arenaManager';
import { LobbyService } from './server';
import { Raid } from './raidManager';
import { GetFullWeeksUsingLocalTime } from '../formula';
import cms from '../cms';
import { isCategoryEvent } from '../motiflib/model/town';
import { handleWorldBroadCastToastMsg } from './userToast';
import { VillageManager } from './villageManager';
import { ClashPrizeManager } from './clashPrizeManager';
import { InvestmentSeasonRankingManager } from './investmentSeasonRankingManager';

// ----------------------------------------------------------------------------
// Pubsub handler functions.
// ----------------------------------------------------------------------------

// ----------------------------------------------------------------------------
function handleKick(msgStr: string) {
  mlog.debug('handleKick with msgStr:', { msgStr });
  const msg = JSON.parse(msgStr);

  const userId = msg.userId;
  const reason = msg.reason;
  const authdId = msg.authdId;

  mlog.warn('kicking user ...', {
    userId,
    reason,
    authdId,
  });

  const userManager = Container.get(UserManager);
  return userManager.kickUser(userId, reason, authdId).catch((err) => {
    mlog.error('lobbyPubsub.handleKick error', {
      err: err.message,
      stack: err.stack,
    });
  });
}

// ----------------------------------------------------------------------------
function handleDevelopmentNationSharePointChanged(msgStr) {
  const msg: DevelopmentNationSharePointChangedPubMsg = JSON.parse(msgStr);
  if (msg.towns.length === 0) {
    return;
  }

  mlog.info('[SEASON] handleDevelopmentNationSharePointChanged publish called');

  const townManager = Container.get(TownManager);
  return townManager
    .onDevelopmentNationSharePointChanged(
      msg.towns.map((elem) => elem.townCmsId),
      msg.towns[0].updateTimeUtc
    )
    .then(() => {
      mlog.info('[SEASON] handleDevelopmentNationSharePointChanged publish finished');
    })
    .catch((err) => {
      mlog.alert('lobbyPubsub.handleDevelopmentNationSharePointChanged error', {
        err: err.message,
        stack: err.stack,
      });
    });
}

// ----------------------------------------------------------------------------
function handleInvestmentSessionClosed(msgStr) {
  const msg: InvestmentSessionClosedPubMsg = JSON.parse(msgStr);
  if (msg.towns.length === 0) {
    return;
  }

  mlog.info(
    `[SEASON] handleInvestmentSessionClosed publish received for sessionId[${msg.sessionId}], season[${msg.seasonId}]`
  );

  const townManager = Container.get(TownManager);
  return townManager
    .onInvestmentSessionClosed(msg.towns, msg.sessionId, msg.seasonId)
    .then(() => {
      mlog.info(
        `[SEASON] handleInvestmentSessionClosed publish finished for sessionId[${msg.sessionId}], season[${msg.seasonId}]`
      );
    })
    .catch((err) => {
      mlog.error('lobbyPubsub.handleInvestmentSessionClosed error', {
        err: err.message,
        stack: err.stack,
      });
    });
}

// ----------------------------------------------------------------------------
function handleTownInvested(msgStr) {
  const msg: TownInvestedPubMsg = JSON.parse(msgStr);

  const townManager = Container.get(TownManager);
  return townManager.onInvested(msg.townCmsId, msg.sessionId, msg.updateTimeUtc).catch((err) => {
    mlog.alert('lobbyPubsub.handleTownInvested error', {
      townCmsId: msg.townCmsId,
      err: err.message,
      stack: err.stack,
    });
  });
}

// ----------------------------------------------------------------------------
function handleTownMayorTaxChanged(msgStr: string) {
  const msg: TownMayorTaxChangedPubMsg = JSON.parse(msgStr);

  const townManager = Container.get(TownManager);
  _.forOwn(msg.changes, (elem, townCmsIdStr) => {
    const townCmsId = parseInt(townCmsIdStr, 10);
    for (const subChange of elem) {
      townManager.setTownMayorTax(townCmsId, subChange.nationCmsId, subChange.tax);
    }
  });

  mlog.info('[SEASON] handleTownMayorTaxChanged finished', {
    curTimeUtc: msg.curTimeUtc,
  });
}

// ----------------------------------------------------------------------------
function handleTownMayorShipyardTaxChanged(msgStr: string) {
  const msg: TownMayorTaxChangedPubMsg = JSON.parse(msgStr);

  const townManager = Container.get(TownManager);
  _.forOwn(msg.changes, (elem, townCmsIdStr) => {
    const townCmsId = parseInt(townCmsIdStr, 10);
    for (const subChange of elem) {
      townManager.setTownMayorShipyardTax(townCmsId, subChange.nationCmsId, subChange.tax);
    }
  });
}

// ----------------------------------------------------------------------------
function handleDevTownNationSharePointChanged(msgStr: string) {
  const msg: DevTownNationSharePointChangedPubMsg = JSON.parse(msgStr);
  const townManager = Container.get(TownManager);
  return townManager.devUpdateTownNationSharePoint(
    msg.townCmsId,
    msg.nationCmsId,
    msg.nationSharePoint,
    msg.updateTimeUtc
  );
}

// ----------------------------------------------------------------------------
function handleTownMayorChanged(msgStr: string) {
  const msg: TownMayorChangedPubMsg = JSON.parse(msgStr);
  const townManager = Container.get(TownManager);
  townManager.setTownMayor(
    msg.townCmsId,
    msg.mayorUserId,
    msg.mayorUserName,
    msg.mayorNationCmsId,
    msg.updateTimeUtc
  );

  // 투자 점수 로드
  const weeklySessionId = GetFullWeeksUsingLocalTime(
    msg.updateTimeUtc,
    cms.Define.InvestmentWeeklySessionPivotDay
  );
  townManager.onInvested(msg.townCmsId, weeklySessionId, msg.updateTimeUtc).catch((err) => {
    mlog.alert('lobbyPubsub.handleTownMayorChanged townManager.onInvested is failed.', {
      townCmsId: msg.townCmsId,
      err: err.message,
      stack: err.stack,
    });
  });
}
// ----------------------------------------------------------------------------
function handleTradeAllUpdated(msgStr) {
  const msg = JSON.parse(msgStr);
  const townManager = Container.get(TownManager);
  townManager.onAllTradePercentChanged(msg.townCmsId, msg.timeMs);
}

// ----------------------------------------------------------------------------
function handleSomeTradeGoodsUpdated(msgStr) {
  const msg = JSON.parse(msgStr);
  const townManager = Container.get(TownManager);
  townManager.onSomeTradePercentChanged(msg.townCmsId, msg.changedCmsIds, msg.timeMs);
}

// ----------------------------------------------------------------------------
function handleTradeEventOccurred(msgStr) {
  const msg = JSON.parse(msgStr);
  const townManager = Container.get(TownManager);
  townManager.onTradeEventOccurred(msg.townCmsId, msg.occurredEvents);

  // UWO-11420 [서버] Common.UPDATE_TRADE_EVENT_SC 패킷 최적화
  const events = [];
  _.forOwn(msg.occurredEvents, (event, idStr) => {
    const id = parseInt(idStr, 10);
    const eventType = event.t;
    const expiration = event.e;
    let elem;
    if (isCategoryEvent(eventType)) {
      // id is trade goods category.
      elem = `c${id}:${eventType}:${expiration}`;
    } else {
      // id is trade goods cms id.
      elem = `${id - cmsEx.TradeGooodsStartingCmsId}:${eventType}:${expiration}`;
    }

    events.push(elem);
  });

  const resp: Resp = {
    sync: {
      add: {
        tradeEvents: {
          [msg.townCmsId]: events,
        },
      },
    },
  };

  const userManager = Container.get(UserManager);
  userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
}

// ----------------------------------------------------------------------------
function handleTradeEventExpired(msgStr, bActiveMayorTradeEvent: boolean = false) {
  const msg = JSON.parse(msgStr);
  const townManager = Container.get(TownManager);
  const townCmsId = msg.townCmsId;
  townManager.onTradeEventExpired(msg.townCmsId, msg.goodsCmsIdOrCategories);

  // 기존 클라이언트에서 expire 확인하여 이벤트 삭제처리 하였으나
  // 시장 마켓 이벤트 활성화시  기존 이벤트 삭제 처리로 인해 아래와 같이 전부 삭제 처리
  if (bActiveMayorTradeEvent) {
    const resp: Resp = {
      sync: {
        remove: {
          tradeEvents: [townCmsId.toString()],
          towns: {
            [townCmsId.toString()]: ['crazeBudget'],
          },
        },
      },
    };

    const userManager = Container.get(UserManager);
    userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
  }
}

// ----------------------------------------------------------------------------
function handleTradeCrazeEventIsClosedByDucat(msgStr) {
  const msg = JSON.parse(msgStr);
  const townCmsId = msg.townCmsId;
  const pricePercents: { [tradeGoodsCmsId: number]: number } = msg.crazeEvnets;

  const townManager = Container.get(TownManager);
  townManager.onTradeCrazeEventIsClosedByDucat(townCmsId);

  const resp: Resp = {
    sync: {
      add: {
        towns: {
          [townCmsId]: {
            tradePricePercents: pricePercents,
          },
        },
      },
      remove: {
        tradeEvents: [townCmsId.toString()],
        towns: {
          [townCmsId]: ['crazeBudget'],
        },
      },
    },
  };

  const userManager = Container.get(UserManager);
  userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
}

// ----------------------------------------------------------------------------
function handleUnpopularEventClosed(msgStr) {
  const msg = JSON.parse(msgStr);
  const { townCmsId, tradeGoodsCmsId, percent } = msg;

  const townManager = Container.get(TownManager);
  townManager.onTradeUnpopularIsClosedByDucat(townCmsId, tradeGoodsCmsId, percent);

  const resp: Resp = {
    sync: {
      add: {
        towns: {
          [townCmsId]: {
            tradePricePercents: {
              [tradeGoodsCmsId]: percent,
            },
          },
        },
      },
      remove: {
        tradeEvents: [townCmsId.toString()],
        towns: {
          [townCmsId]: ['tradeUnpopular'],
        },
      },
    },
  };

  const userManager = Container.get(UserManager);
  userManager.broadCastJsonPacketToAllUser(proto.Common.UPDATE_TRADE_EVENT_SC, resp);
}

// ----------------------------------------------------------------------------
function handleSmuggleAllUpdated(msgStr) {
  const msg = JSON.parse(msgStr);
  const townManager = Container.get(TownManager);
  townManager.onAllSmugglePercentChanged(msg.townCmsId, msg.timeMs);
}

// ----------------------------------------------------------------------------
function handleArenaSessionClosed(msgStr) {
  const msg: ArenaSessionClosedPubMsg = JSON.parse(msgStr);

  const arenaManager = Container.get(ArenaManager);
  return arenaManager.onArenaSessionClosed(msg.sessionId);
}

// function handleChinaUnder18ClosingTime() {
//   mlog.info('china under 18 closing time.');

//   const userManager = Container.get(UserManager);
//   return userManager.kickChinaAllUnder18(KICK_REASON.CHINA_UNDER_18_CLOSING_TIME);
// }

// ----------------------------------------------------------------------------
function handleUpdateRaid(msgStr) {
  const { notice } = JSON.parse(msgStr);
  const { raidManager } = Container.get(LobbyService);
  raidManager.subcribe(notice);
}

// ----------------------------------------------------------------------------
function handleUpdateGuildRaid(msgStr) {
  const { guildId, cmsId, notice } = JSON.parse(msgStr);
  const { guildRaidManager } = Container.get(LobbyService);
  guildRaidManager.subcribe(guildId, cmsId, notice);
}

// ----------------------------------------------------------------------------
function handleNationElectionCandidateRegistered(msgStr) {
  const msg: NationElectionRegisterCandidatePubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onElectionCandidateRegistered(msg);
}

// ----------------------------------------------------------------------------
function handleNationElectionCandidatesModified(msgStr) {
  const msg: NationElectionCandidatesModifiedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onElectionCandidatesModified(msg);
}

// ----------------------------------------------------------------------------
// 투표 발생시는 브로드캐스팅 없이 주기적인 유저의 요청으로 동기화한다
function handleNationElectionVotedToCandidate(msgStr) {
  const msg: NationElectionVotedToCandidatePubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);
  nationManager.onVotedToElectionCandidate(msg);
}

// ----------------------------------------------------------------------------
// 투표 갱신시는 브로드캐스팅 없이 주기적인 유저의 요청으로 동기화한다
function handleElectionVotesUpdated(msgStr) {
  const msg: NationElectionVotesUpdatedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);
  nationManager.onElectionCandidateVotesUpdated(msg);
}

// ----------------------------------------------------------------------------
function handleNationElectionSessionClosed(msgStr) {
  const msg: NationElectionSessionClosedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);
  const { nations, sessionId } = msg;

  return nationManager.onElectionSessionClosed(nations, sessionId).then(() => {
    const userManager = Container.get(UserManager);

    // 당선자 본인의 맵에서 표시되는 관직 관련 외형 정보 변경 및 sync 동기화
    nations.forEach((nation) => {
      if (nation.primeMinisterUserId) {
        const user = userManager.getUserByUserId(nation.primeMinisterUserId);
        if (user) {
          user.onNationCabinetJoinOrChange();

          // sync 동기화
          user.userNation.applyCabinetMemberSync(user);
        }
      }
    });
  });
}

// ----------------------------------------------------------------------------
function handleNationElectionCandidatesRemoved(msgStr) {
  const msg: NationElectionRemovedCandidatesPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onElectionCandidatesRemoved(msg);
}

// ----------------------------------------------------------------------------
function handleNationCabinetMemberAppointed(msgStr) {
  const msg: NationCabinetMemberAppointedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  // 피임명자 본인의 맵에서 표시되는 관직 관련 외형 정보 변경 및 sync 동기화
  nationManager.onNationCabinetMemberAppointed(msg).then(() => {
    const userManager = Container.get(UserManager);
    const user = userManager.getUserByUserId(msg.memberUserId);
    if (user) {
      user.onNationCabinetJoinOrChange();

      // sync 동기화
      user.userNation.applyCabinetMemberSync(user);
    }
  });
}

// ----------------------------------------------------------------------------
function handleNationCabinetMemberWageRateChanged(msgStr) {
  const msg: NationCabinetMemberWageRateChangedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onNationCabinetMemberWageRateChanged(msg);
}

// ----------------------------------------------------------------------------
function handleNationCabinetMembersRemoved(msgStr) {
  const msg: NationCabinetMembersRemovedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  const nation = nationManager.get(msg.nationCmsId);
  if (!nation) {
    return;
  }

  const primeministerUserId = nation.getPrimeMinisterUserId(msg.sessionId);

  nationManager.onNationCabinetMembersRemoved(msg);

  // 피해임자 본인의 외형 정보 변경 및 sync 동기화
  const userManager = Container.get(UserManager);
  msg.memberUserIds.forEach((userId) => {
    const user = userManager.getUserByUserId(userId);
    if (user) {
      user.onNationCabinetLeave();

      // sync 동기화
      user.userNation.applyCabinetMemberSync(user);
    }
  });

  // 사퇴한 유저중에 총리가 있는경우 전국민에게 브로드캐스트한다
  // !! 주의 !! 원칙적으로는 브로드캐스팅 받는 모든 유저의 updateSyncTracker 를 호출해줘야 하지만
  //  총리가 사퇴한 경우는 1회성이고 다시 임명되지 않기 때문에 호출하지 않는다
  if (primeministerUserId && msg.memberUserIds.indexOf(primeministerUserId) >= 0) {
    mlog.info(
      '[ELECTION] handleNationCabinetMembersRemoved - primeMinister gone.. broadcasting to nation users',
      {
        nationCmsId: msg.nationCmsId,
        sessionId: msg.sessionId,
        primeministerUserId,
        memberUserIds: msg.memberUserIds,
      }
    );

    const nationUserIds = userManager.gatherNationUserIds(msg.nationCmsId, [primeministerUserId]);
    const resp: Resp = {
      sync: {
        remove: {
          nations: {
            [msg.nationCmsId]: {
              cabinetMembers: {
                [msg.sessionId]: { [primeministerUserId]: true },
              },
            },
          },
        },
      },
    };

    mlog.verbose('[ELECTION] handleNationCabinetMembersRemoved - broadcasting nation users', {
      nationUserIds,
    });

    userManager.broadcastJsonPacket(nationUserIds, proto.Common.NATION_ELECTION_UPDATE_SC, resp);
  }
}

// ----------------------------------------------------------------------------
function handleNationWageWeeklySessionClosed(msgStr) {
  const msg: NationBudgetUpdatedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onNationBudgetUpdated(msg.nations, msg.updateTimeUtc);
}

// ----------------------------------------------------------------------------
function handleNationBudgetUpdated(msgStr) {
  const msg: NationBudgetUpdatedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onNationBudgetUpdated(msg.nations, msg.updateTimeUtc);
}

// ----------------------------------------------------------------------------
function handleNationBudgetDonated(msgStr) {
  const msg: NationBudgetUpdatedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onNationBudgetUpdated(msg.nations, msg.updateTimeUtc);
  nationManager.onNationBudgetDonated(msg.nations, msg.updateTimeUtc);
}

// ----------------------------------------------------------------------------
function handleNationPoliciesUpdated(msgStr) {
  const msg: NationPoliciesUpdatedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onNationPoliciesUpdated(msg.nations, msg.updateTimeUtc);
}

// ----------------------------------------------------------------------------
function handleVillageEventUpdate(msgStr) {
  const villageManager = Container.get(VillageManager);
  villageManager.loadVillageEvents();
}

// ----------------------------------------------------------------------------
function handleVillageStorageUpdate(msgStr) {
  const msg: VillageStorageUpdatedPubMsg = JSON.parse(msgStr);
  const villageManager = Container.get(VillageManager);
  villageManager.loadVillageStorages(msg.curSessionId, msg.nextSessionId);
}

// ----------------------------------------------------------------------------
function handleVillageStorageDeleteExpiredSession(msgStr) {
  const msg: number[] = JSON.parse(msgStr);
  const villageManager = Container.get(VillageManager);
  villageManager.deleteExpiredStorageSessions(msg);
}

// ----------------------------------------------------------------------------
function handleNationGoalPromiseChanged(msgStr) {
  const { nationManager } = Container.get(LobbyService);

  const msg: NationGoalPromiseChangedPubMsg = JSON.parse(msgStr);
  const { nationCmsId, cabinetSessionId, updateTimeUtc } = msg;

  nationManager.onGoalPromiseChanged(nationCmsId, cabinetSessionId, updateTimeUtc);
}

// ----------------------------------------------------------------------------
function handleSupportShopPurchasesd(msgStr) {
  const { nationManager } = Container.get(LobbyService);

  const msg: NationSupportShopPurchasedPubMsg = JSON.parse(msgStr);
  const { nationCmsId, cabinetSessionId, updateTimeUtc } = msg;

  nationManager.onSupportShopPurchased(nationCmsId, cabinetSessionId, updateTimeUtc);
}

// ----------------------------------------------------------------------------
function handleNationNoticeUpdated(msgStr) {
  const msg: NationNoticeUpdatedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onNationNoticeUpdated(msg);
}

// ----------------------------------------------------------------------------
function handleDevNationCabinetMembersAllReload(msgStr) {
  const msg: DevNationCabinetMembersAllReloadPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);
  nationManager.onDevNationCabinetMembersAllReload(msg);
}

// ----------------------------------------------------------------------------
function handleNationCabinetMemberThoughtChanged(msgStr) {
  const msg: NationCabinetMemberThoughtChangedPubMsg = JSON.parse(msgStr);

  const { nationManager } = Container.get(LobbyService);

  nationManager.onNationCabinetMemberThoughtChanged(msg);
}

// ----------------------------------------------------------------------------
function handleBoughtWebShopProduct(msgStr) {
  const msg: BoughtWebShopProductPubMsg = JSON.parse(msgStr);

  const userManager = Container.get(UserManager);
  userManager.onBoughtWebShopProduct(msg);
}

// ----------------------------------------------------------------------------
function handleClashPrize(msgStr) {
  const clashPrizeManager = Container.get(ClashPrizeManager);

  clashPrizeManager.updateRankers();
}

// ----------------------------------------------------------------------------
function handleInvestmentSeasonRankSessionChanged(msgStr) {
  const msg: InvestmentSeasonRankSessionChangedPubMsg = JSON.parse(msgStr);

  mlog.info('[SEASON] handleInvestmentSeasonRankSessionChanged publish called');

  const { investmentSeasonRankingManager } = Container.get(LobbyService);
  investmentSeasonRankingManager
    .onInvestmentSeasonRankSessionChanged(msg.seasonId, msg.updateTimeUtc)
    .catch((err) => {
      mlog.alert('lobbyPubsub.handleInvestmentSeasonRankChanged error', {
        seasonId: msg.seasonId,
        err: err.message,
        stack: err.stack,
      });
    });
}

// ----------------------------------------------------------------------------
function handleInvestmentSeasonRankClosed(msgStr) {
  const msg: InvestmentSeasonRankClosedPubMsg = JSON.parse(msgStr);

  mlog.info('[SEASON] handleInvestmentSeasonRankClosed publish called');

  const { investmentSeasonRankingManager } = Container.get(LobbyService);
  investmentSeasonRankingManager.onInvestmentSeasonRankClosed(msg.seasonId).catch((err) => {
    mlog.alert('lobbyPubsub.handleInvestmentSeasonRankClosed error', {
      seasonId: msg.seasonId,
      err: err.message,
      stack: err.stack,
    });
  });
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export const init = () => {
  const worldPubsub = Container.of('pubsub-world').get(Pubsub);
  const authPubsub = Container.of('pubsub-auth').get(Pubsub);

  // Subscribe.
  let ch = `kick:${mconf.appId}`;
  authPubsub.subscribe(ch, handleKick);
  authPubsub.subscribe(`web_shop:${mconf.appId}`, handleBoughtWebShopProduct);

  worldPubsub.subscribe(
    'development_nation_share_point_changed',
    handleDevelopmentNationSharePointChanged
  );
  worldPubsub.subscribe('investment_session_closed', handleInvestmentSessionClosed);
  worldPubsub.subscribe('town_invested', handleTownInvested);
  worldPubsub.subscribe('town_mayor_tax_changed', handleTownMayorTaxChanged);
  worldPubsub.subscribe('town_mayor_shipyard_tax_changed', handleTownMayorShipyardTaxChanged);
  worldPubsub.subscribe(
    'dev_town_nation_share_point_changed',
    handleDevTownNationSharePointChanged
  );
  worldPubsub.subscribe('town_mayor_changed', handleTownMayorChanged);

  worldPubsub.subscribe('trade_all_updated', handleTradeAllUpdated);
  worldPubsub.subscribe('some_trade_goods_updated', handleSomeTradeGoodsUpdated);
  worldPubsub.subscribe('trade_event_occurred', handleTradeEventOccurred);
  worldPubsub.subscribe('trade_event_expired', handleTradeEventExpired);
  worldPubsub.subscribe(
    'trade_craze_event_is_closed_by_budget',
    handleTradeCrazeEventIsClosedByDucat
  );
  worldPubsub.subscribe('unpopular_event_closed', handleUnpopularEventClosed);

  worldPubsub.subscribe('smuggle_all_updated', handleSmuggleAllUpdated);

  worldPubsub.subscribe('arena_session_closed', handleArenaSessionClosed);

  worldPubsub.subscribe('update_raid', handleUpdateRaid);
  worldPubsub.subscribe('update_guild_raid', handleUpdateGuildRaid);

  worldPubsub.subscribe('world_broadcast', handleWorldBroadCastToastMsg);

  worldPubsub.subscribe(
    'nation_election_candidate_registered',
    handleNationElectionCandidateRegistered
  );

  worldPubsub.subscribe(
    'nation_election_candidates_modified',
    handleNationElectionCandidatesModified
  );

  worldPubsub.subscribe('nation_election_voted_to_candidate', handleNationElectionVotedToCandidate);
  worldPubsub.subscribe('nation_election_votes_updated', handleElectionVotesUpdated);
  worldPubsub.subscribe('nation_election_session_closed', handleNationElectionSessionClosed);
  worldPubsub.subscribe(
    'nation_election_candidates_removed',
    handleNationElectionCandidatesRemoved
  );

  worldPubsub.subscribe('nation_cabinet_member_appointed', handleNationCabinetMemberAppointed);
  worldPubsub.subscribe(
    'nation_cabinet_member_wage_rate_changed',
    handleNationCabinetMemberWageRateChanged
  );
  worldPubsub.subscribe('nation_cabinet_member_removed', handleNationCabinetMembersRemoved);
  worldPubsub.subscribe('nation_goal_promise_changed', handleNationGoalPromiseChanged);
  worldPubsub.subscribe('nation_wage_weekly_session_closed', handleNationWageWeeklySessionClosed);
  worldPubsub.subscribe('nation_budget_updated', handleNationBudgetUpdated);
  worldPubsub.subscribe('nation_policies_updated', handleNationPoliciesUpdated);
  worldPubsub.subscribe('nation_support_shop_purchased', handleSupportShopPurchasesd);
  worldPubsub.subscribe('nation_budget_donated', handleNationBudgetDonated);
  worldPubsub.subscribe('nation_notice_updated', handleNationNoticeUpdated);
  worldPubsub.subscribe(
    'dev_nation_cabinet_members_all_reload',
    handleDevNationCabinetMembersAllReload
  );
  worldPubsub.subscribe(
    'nation_cabinet_member_thought_changed',
    handleNationCabinetMemberThoughtChanged
  );

  // 중국 미성년자 몰입 방지 접속 시간 체크
  // if (mconf.countryCode === COUNTRY_CODE.CHINA) {
  //   pubsub.subscribe('china_under_18_closing_time', handleChinaUnder18ClosingTime);
  // }

  worldPubsub.subscribe('village_event_updated', handleVillageEventUpdate);
  worldPubsub.subscribe('village_storage_updated', handleVillageStorageUpdate);
  worldPubsub.subscribe(
    'village_storage_delete_expired_session',
    handleVillageStorageDeleteExpiredSession
  );
  worldPubsub.subscribe('clash_season_rankers_updated', handleClashPrize);

  worldPubsub.subscribe(
    'investment_season_rank_session_changed',
    handleInvestmentSeasonRankSessionChanged
  );
  worldPubsub.subscribe('investment_season_rank_closed', handleInvestmentSeasonRankClosed);
};

// ----------------------------------------------------------------------------
export const pubUserKicked = (userId, authdId) => {
  const authPubsub = Container.of('pubsub-auth').get(Pubsub);
  const ch = `kick:${authdId}`;
  const msg = {
    userId,
  };

  mlog.debug('publish user kicked', {
    userId,
    authdId,
  });

  return authPubsub.publish(ch, JSON.stringify(msg));
};
