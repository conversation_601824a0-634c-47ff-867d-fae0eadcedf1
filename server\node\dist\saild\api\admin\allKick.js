"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const typedi_1 = __importDefault(require("typedi"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const merror_1 = require("../../../motiflib/merror");
const server_1 = require("../../server");
const mutil = __importStar(require("../../../motiflib/mutil"));
module.exports = async (req, res) => {
    try {
        const sailService = typedi_1.default.get(server_1.SailService);
        sailService.bMaintenance = true;
        sailService.lastMaintenanceTick = mutil.curTimeUtc() + 10; // 레디스 체크는 10초 후에 시작하도록 한다
        mlog_1.default.info('allKick received. starting maintenance');
        res.end();
    }
    catch (error) {
        throw new merror_1.MError(error.message, merror_1.MErrorCode.SAIL_API_ERROR);
    }
};
//# sourceMappingURL=allKick.js.map