// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import * as proto from '../proto/lobby/proto';
import * as UserConnection from './userConnection';
import * as lobbyPubsub from './lobbyPubsub';
import * as sync from './type/sync';
import { User, UserSocket } from './user';
import mlog from '../motiflib/mlog';
import mconf from '../motiflib/mconf';
import { Service } from 'typedi';
import { TownManager } from './townManager';
import { Container } from 'typedi';
import { KICK_REASON, CHINA_AGE } from '../motiflib/const';
import { ZoneType } from '../cms/ex';
import { USER_MANAGER_TICK_INTERVAL } from './const';
import { TickPerfmon, UnitTickStat } from '../motiflib/tickPerfmon';
import * as mutil from '../motiflib/mutil';
import { IOPerfmon, IO_CONNCETION_TYPE, IOType } from '../motiflib/ioPerfmon';
import {
  WorldEventNotificationData,
  WorldEventNotificationType,
} from './type/worldEventNotification';
import { PacketPerfmon, PacketSentType, UnitPacketCommonStat } from '../motiflib/packetPerfmon';
import { BoughtWebShopProductPubMsg, PayloadFlag } from '../motiflib/model/lobby';
import { nanoid } from 'nanoid';
import * as cmsEx from '../cms/ex';
import cms from '../cms';

export interface UserCount {
  userCount: number;
  botCount: number;
}

function _townInvestRemoveSyncMergeCustomizer(objValue: any, srcValue: any): any {
  if (_.isArray(objValue)) {
    return objValue.concat(srcValue);
  }
}

// ----------------------------------------------------------------------------
// UserManager object.
// ----------------------------------------------------------------------------
@Service()
export class UserManager {
  // All users by connection ID. (no necessarily logged in)
  private _usersByConnId: { [connId: string]: User } = {};
  // All users by user ID. (logged in)
  private _usersByUserId: { [userId: number]: User } = {};
  // All users in progress of logging out.
  private _usersLoggingOut: { [userId: number]: User } = {};

  private _userTickInterval: NodeJS.Timeout;
  private _userCount: UserCount = { userCount: 0, botCount: 0 };

  getUserByUserId(userId: number): User | undefined {
    return this._usersByUserId[userId];
  }

  addUser(sock: UserSocket): User {
    const existingUser = this._usersByConnId[sock.id];
    if (existingUser) {
      mlog.error('duplicate user (addUser)', {
        userId: existingUser.userId,
        connId: sock.id,
        remoteAddress: sock.remoteAddress,
        remotePort: sock.remotePort,
      });
      return null;
    }

    const newSession = new User(sock);
    this._usersByConnId[sock.id] = newSession;

    return newSession;
  }

  addBotUser(userId: number): User {
    const connId = nanoid(8);

    const existingUser = this._usersByConnId[connId];
    if (existingUser) {
      mlog.error('duplicate bot user (addUser)', {
        userId: existingUser.userId,
        connId: connId,
      });

      return null;
    }
    const newSession = new User(undefined, connId, userId);

    this._usersByConnId[connId] = newSession;

    return newSession;
  }

  removeUser(connId: string): void {
    delete this._usersByConnId[connId];
  }

  addLoggedInUser(user: User): void {
    if (this._usersByUserId[user.userId]) {
      mlog.error('duplicate user (addLoggedInUser)', { userId: user.userId });
      return;
    }

    this._usersByUserId[user.userId] = user;

    // 동접 카운트 업데이트.
    if (user.isOfflineSailingBot) {
      this._userCount.botCount++;
    } else {
      this._userCount.userCount++;
    }
  }

  removeLoggedInUser(userId: number): void {
    const user = this._usersByUserId[userId];
    if (!user) {
      mlog.error('Logged-in-user not found while removing.', {
        userId,
      });
      return;
    }

    // 동접 카운트 업데이트.
    if (user.isOfflineSailingBot) {
      this._userCount.botCount--;
    } else {
      this._userCount.userCount--;
    }

    this._usersLoggingOut[userId] = user;
    delete this._usersByUserId[userId];
  }

  removeLoggingOutUser(userId: number): void {
    const user = this._usersLoggingOut[userId];
    if (!user) {
      mlog.error('Loggin-out-user not found while removing.', {
        userId,
      });
      return;
    }

    delete this._usersLoggingOut[userId];
  }

  kickUser(userId: number, reason: KICK_REASON, authdId: string): Promise<void> {
    return Promise.resolve().then(() => {
      const userToKick = this._usersByUserId[userId];
      if (!userToKick) {
        mlog.warn('user to kick is already offline', {
          userId,
        });
        return lobbyPubsub.pubUserKicked(userId, authdId);
      }

      return userToKick.kick(reason, authdId);
    });
  }

  kickChinaAllUnder18(reason: KICK_REASON): Promise<any[]> {
    const promises = [];
    _.forOwn(this._usersByUserId, (user) => {
      if (user.chinaAge !== CHINA_AGE.ADULT) {
        promises.push(user.kick(reason, null));
      }
    });

    return Promise.all(promises);
  }

  onSocketClose(connId: string): void {
    const user = this._usersByConnId[connId];
    if (!user) {
      mlog.warn('User not found by connection ID. (onSocketClose)', {
        connId,
      });
      return;
    }

    user.onSocketClose();
  }

  disconnectAll(disconnectReason: UserConnection.DisconnectReason): void {
    _.each(this._usersByConnId, (user) => {
      user.disconnect(disconnectReason);
    });
  }

  async waitForTermination() {
    let secondsElapsed: number = 0;

    // HARD-CODED: 60 seconds.
    while (secondsElapsed < 60) {
      const bTerminated = await this.checkTermination(secondsElapsed); // Takes 1 second.
      if (bTerminated) {
        break;
      }

      ++secondsElapsed;
    }
  }

  private async checkTermination(secondsElapsed: number): Promise<boolean> {
    // Log every 5 seconds.
    if (secondsElapsed % 5 === 0) {
      mlog.info('Waiting for termination...', {
        loggedIn: Object.keys(this._usersByUserId).length,
        loggingOut: Object.keys(this._usersLoggingOut).length,
      });
    }

    if (_.isEmpty(this._usersByUserId) && _.isEmpty(this._usersLoggingOut)) {
      return true;
    }

    await mutil.sleep(1000);

    return false;
  }

  gatherOceanUsers(): User[] {
    const users = [];
    for (const [key, user] of Object.entries(this._usersByConnId)) {
      if (user.userState.isInOcean()) {
        users.push(user);
      }
    }
    return users;
  }

  gatherOffSailUsers(): User[] {
    const users = [];
    for (const [key, user] of Object.entries(this._usersByConnId)) {
      if (user.isOfflineSailingBot) {
        users.push(user);
      }
    }
    return users;
  }

  gatherTownUsers(): User[] {
    const users = [];
    for (const [key, user] of Object.entries(this._usersByConnId)) {
      if (user.userState.isInTown()) {
        users.push(user);
      }
    }
    return users;
  }

  gatherNationUserIds(nationCmsId: number, excludeUserIds: number[]): number[] {
    const userIds = [];
    for (const [key, user] of Object.entries(this._usersByConnId)) {
      if (nationCmsId === user.nationCmsId && !excludeUserIds?.includes(user.userId)) {
        userIds.push(user.userId);
      }
    }
    return userIds;
  }

  getUserCount(): UserCount {
    return this._userCount;
  }

  userTick(timeout: number): void {
    this._userTickInterval = setTimeout(() => {
      const startTimeInMs = Date.now();

      try {
        const connIds = Object.keys(this._usersByConnId);
        for (const key of connIds) {
          this._usersByConnId[key].tick(startTimeInMs);
        }
      } catch (e) {
        mlog.error('User tick error!', {
          error: e.message,
          stack: e.stack,
        });
      }

      // Check elapsed time.
      const elapsedTimeInMs = Date.now() - startTimeInMs;
      if (elapsedTimeInMs > mconf.userTick.warningElapsedTime) {
        const memUsage = mutil.getMemoryUsage();
        mlog.warn(`User tick slow ... elapsed: ${elapsedTimeInMs} ms.`, { memUsage });
      }

      const tickPerfmon = Container.get(TickPerfmon);
      const unitTickStat: UnitTickStat = {
        tickType: 'userManager',
        duration: elapsedTimeInMs,
      };
      tickPerfmon.addTickStat(unitTickStat);

      const ioPerfmon = Container.get(IOPerfmon);
      const connectedUsers = Object.keys(this._usersByConnId).length;
      const loggedInUsers = Object.keys(this._usersByUserId).length;
      ioPerfmon.addUserSession(connectedUsers, loggedInUsers);

      // tick에 수행된 시간만큼은 제외하여 다음 tick 수행.
      let nextTickTimeout = USER_MANAGER_TICK_INTERVAL - elapsedTimeInMs;
      this.userTick(nextTickTimeout < 0 ? 0 : nextTickTimeout);
    }, timeout);
  }

  stopUserTick(): void {
    if (this._userTickInterval) {
      clearTimeout(this._userTickInterval);
    }
  }

  // 브로드캐스팅 패킷을 타입에 맞는 함수로 호출해준다
  broadcastPacket(
    userIds: number[],
    packetType: number,
    originPacketSize: number,
    packet: any
  ): void {
    if (UserConnection.BroadcastBinaryPacketTypes.includes(packetType)) {
      // 이미 바이너리형태
      return this.broadcastBinaryPacket(userIds, packetType, originPacketSize, packet);
    } else {
      return this.broadcastJsonPacket(userIds, packetType, packet);
    }
  }

  broadcastBinaryPacket(userIds: number[], packetType: number, size: number, packet: any): void {
    // 브로드캐스팅용 패킷 버퍼 하나를 만들어 모든 유저에게 보내도록 한다.
    const buf = UserConnection.buildBinaryPacketBuffer(packetType, size, packet);
    userIds.map((userId) => {
      this.sendBufferToUser(userId, buf, packetType);
    });

    // perfmon 기록.
    const perfmonPacketSize = buf.byteLength * userIds.length;
    const packetPerfmon = Container.get(PacketPerfmon);
    const unitPacketStat: UnitPacketCommonStat = {
      packetId: packetType,
      packetIdStr: proto.toString(packetType),
      size: perfmonPacketSize,
    };
    packetPerfmon.addPacketSentStat(PacketSentType.USER_PACKET_SENT, unitPacketStat);

    const ioPerfmon = Container.get(IOPerfmon);
    ioPerfmon.addIOBytes(IO_CONNCETION_TYPE.USER, IOType.WRITE, perfmonPacketSize);
  }

  broadcastTownMove(userIds: number[], packet: UserConnection.TownMovePacket): Promise<boolean[]> {
    const buf = UserConnection.buildTownMovePacketBuffer(packet);
    const promises = userIds.map((userId) => {
      return this.sendBufferToUser(userId, buf, proto.Town.USER_MOVE_SC);
    });

    return Promise.all(promises);
  }

  broadcastJsonPacket(userIds: number[], packetType: number, packetBody: any): void {
    // 브로드캐스팅용 패킷 버퍼 하나를 만들어 모든 유저에게 보내도록 한다.
    const buf = UserConnection.buildPacketBuffer(packetType, packetBody);
    userIds.map((userId) => {
      this.sendBufferToUser(userId, buf, packetType);
    });

    // perfmon 기록.
    const perfmonPacketSize = buf.byteLength * userIds.length;
    const packetPerfmon = Container.get(PacketPerfmon);
    const unitPacketStat: UnitPacketCommonStat = {
      packetId: packetType,
      packetIdStr: proto.toString(packetType),
      size: perfmonPacketSize,
    };
    packetPerfmon.addPacketSentStat(PacketSentType.USER_PACKET_SENT, unitPacketStat);

    const ioPerfmon = Container.get(IOPerfmon);
    ioPerfmon.addIOBytes(IO_CONNCETION_TYPE.USER, IOType.WRITE, perfmonPacketSize);
  }

  broadCastJsonPacketToAllUser(packetType: number, packetBody: any): void {
    this.broadcastJsonPacket(Object.keys(this._usersByUserId).map(Number), packetType, packetBody);
  }

  sendJsonPacketToUser<T = any>(
    userId: number,
    packetType: number,
    packetBody: T
  ): Promise<boolean> {
    const user: User = this._usersByUserId[userId];
    if (!user) {
      return Promise.resolve(false);
    }

    if (user.isOfflineSailingBot) {
      return user.userConn.sendJsonPacket<T>(
        userId,
        0,
        packetType,
        packetBody,
        PayloadFlag.Compress
      );
    }

    return user.userConn.sendJsonPacket<T>(userId, 0, packetType, packetBody);
    // .catch((err) => {
    //   mlog.error('failed to send to user', {
    //     userId,
    //     packetType,
    //     error: err.message,
    //   });
    // });
  }

  sendBufferToUser(userId: number, buf: Buffer, packetType?: number): boolean {
    const user = this._usersByUserId[userId];
    if (!user) {
      mlog.warn('cannot send (buffer) to offline user', {
        userId,
        packetType,
      });

      return false;
    }

    return user.userConn.sendBuffer(buf, packetType ? proto.toString(packetType) : '<unknown>');
    // .catch((err) => {
    //   mlog.error('failed to send buf to user', {
    //     userId,
    //     error: err.message,
    //   });
    // });
  }

  broadcastAllTownPricePercent(userIds: number[]) {
    let promises = [];
    const tradeGoodsCmsIdsOfTowns: { [townCmsId: number]: Set<number> } = {};

    const inTownUserIds = [];
    const curTimeUtc = mutil.curTimeUtc();
    for (const userId of userIds) {
      const user = this._usersByUserId[userId];
      if (!user) {
        continue;
      }
      const townCmsId = user.userTown.getTownCmsId();
      if (!townCmsId) {
        continue;
      }
      if (!tradeGoodsCmsIdsOfTowns[townCmsId]) {
        tradeGoodsCmsIdsOfTowns[townCmsId] = new Set();
      }

      // 유저가 가지고 있는 교역품이나 대유행에 해당하는 교역품들 목록 가져옴
      const tradeGoodsCmsIdSet = user.userTown.buildTradeGoodsCmsIdsSetOfTownSaleAndUserHas(
        townCmsId,
        user.userFleets,
        curTimeUtc
      );
      for (const elem of tradeGoodsCmsIdSet) {
        tradeGoodsCmsIdsOfTowns[townCmsId].add(elem);
      }

      inTownUserIds.push(userId);
    }

    const townManager = Container.get(TownManager);
    // 교역품 시세를 town에 세팅하기 위해 호출하는것으로 보임
    for (const townCmsId of Object.keys(tradeGoodsCmsIdsOfTowns)) {
      promises.push(
        townManager.getTownTradePricePercents(
          parseInt(townCmsId, 10),
          Array.from(tradeGoodsCmsIdsOfTowns[townCmsId])
        )
      );
    }

    return Promise.all(promises).then(() => {
      const respPromises = inTownUserIds.map((userId) => {
        const user = this._usersByUserId[userId];
        if (!user) {
          return Promise.resolve();
        }
        const townCmsId = user.userTown.getTownCmsId();
        if (!townCmsId) {
          return Promise.resolve();
        }
        const resp: sync.Resp = {};
        return user.userTown
          .getTradePricePercentsOfTownSaleAndUserHas(townCmsId, user.userFleets, curTimeUtc) // 유저가 가지고 있는 교역품이나 대유행에 해당하는 교역품들의 시세를 가져옴
          .then((ret) => {
            resp.sync = {
              add: {
                towns: {
                  [townCmsId]: {
                    tradePricePercents: ret,
                  },
                },
              },
            };

            const townManager = Container.get(TownManager);
            // 시세 교역품 그래프, 이전 세션의 시세를 가져옴
            return townManager.getTownTradeAllSessionPricePercents(
              townCmsId,
              Object.keys(ret).map(Number)
            );
          })
          .then((ret) => {
            // 필요할 경우 다시 사용.
            // resp.sync.add.towns[townCmsId].tradeAllSessionPricePercents = ret.allSessions;
            // resp.sync.remove.towns[townCmsId].tradeAllSessionPricePercents = true;

            if (ret) {
              resp.sync.add.towns[townCmsId].tradePrePricePercents = ret.pre;
            }

            return this.sendJsonPacketToUser(
              userId,
              proto.Town.TOWN_TRADE_PRICE_PERCENT_UPDATE_SC,
              resp
            );
          });
      });

      return Promise.all(respPromises);
    });
  }

  broadcastTownPricePercent(userIds: number[], changedTradeGoodsCmsIds: number[]) {
    const promises = [];
    const tradeGoodsCmsIdsOfTowns: { [townCmsId: number]: Set<number> } = {};
    const curTimeUtc = mutil.curTimeUtc();
    for (const userId of userIds) {
      const user = this._usersByUserId[userId];
      if (!user) {
        continue;
      }
      const townCmsId = user.userTown.getTownCmsId();
      if (!tradeGoodsCmsIdsOfTowns[townCmsId]) {
        tradeGoodsCmsIdsOfTowns[townCmsId] = new Set();
      }

      const tradeGoodsCmsIdSet = user.userTown.buildTradeGoodsCmsIdsSetOfArgAndUserHas(
        townCmsId,
        changedTradeGoodsCmsIds,
        user.userFleets,
        curTimeUtc
      );
      for (const elem of tradeGoodsCmsIdSet) {
        tradeGoodsCmsIdsOfTowns[townCmsId].add(elem);
      }
    }

    const townManager = Container.get(TownManager);
    for (const townCmsId of Object.keys(tradeGoodsCmsIdsOfTowns)) {
      promises.push(
        townManager.getTownTradePricePercents(
          parseInt(townCmsId, 10),
          Array.from(tradeGoodsCmsIdsOfTowns[townCmsId])
        )
      );
    }

    return Promise.all(promises).then(() => {
      const respPromises = userIds.map((userId) => {
        const user = this._usersByUserId[userId];
        const townCmsId = user.userTown.getTownCmsId();

        const resp: sync.Resp = {
          sync: {
            add: {
              towns: {
                [townCmsId]: {},
              },
            },
          },
        };

        return user.userTown
          .getTradePricePercentsOfArgAndUserHas(
            townCmsId,
            changedTradeGoodsCmsIds,
            user.userFleets,
            curTimeUtc
          )
          .then((tradePricePercents) => {
            if (Object.keys(tradePricePercents).length === 0) {
              return;
            }

            resp.sync.add.towns[townCmsId].tradePricePercents = tradePricePercents;

            return townManager.getTownTradeAllSessionPricePercents(
              townCmsId,
              Object.keys(tradePricePercents).map(Number)
            );
          })
          .then((ret) => {
            if (ret) {
              resp.sync.add.towns[townCmsId].tradePrePricePercents = ret.pre;
            }
            return this.sendJsonPacketToUser(
              userId,
              proto.Town.TOWN_TRADE_PRICE_PERCENT_UPDATE_SC,
              resp
            );
          });
      });

      return Promise.all(respPromises);
    });
  }

  broadcastAllTownSmugglePricePercent(userIds: number[]) {
    const curTimeUtc = mutil.curTimeUtc();
    const smuggleGoodsCmsIdsOfTowns: { [townCmsId: number]: Set<number> } = {};
    const inTownUserIds = [];
    for (const userId of userIds) {
      const user = this._usersByUserId[userId];
      if (!user) {
        continue;
      }
      const townCmsId = user.userTown.getTownCmsId();
      if (!townCmsId) {
        continue;
      }
      if (!smuggleGoodsCmsIdsOfTowns[townCmsId]) {
        smuggleGoodsCmsIdsOfTowns[townCmsId] = new Set<number>();
      }

      const smuggleGoodsCmsIds = cmsEx.getTownSellingSmuggleGoodsCmsIds(townCmsId);
      const firstFleet = user.userFleets.getFleet(cmsEx.FirstFleetIndex);
      _.forOwn(firstFleet.getShips(), (ship) => {
        smuggleGoodsCmsIds.push(...ship.getSmuggleGoodsCmsIdInCargo());
      });

      for (const cmsId of smuggleGoodsCmsIds) {
        smuggleGoodsCmsIdsOfTowns[townCmsId].add(cmsId);
      }

      inTownUserIds.push(userId);
    }

    const townManager = Container.get(TownManager);
    const respPromises = inTownUserIds.map((userId) => {
      const user = this._usersByUserId[userId];
      if (!user) {
        return Promise.resolve();
      }

      const townCmsId = user.userTown.getTownCmsId();
      if (!townCmsId) {
        return Promise.resolve();
      }

      const resp: sync.Resp = {};
      return townManager
        .getTownSmugglePricePercents(townCmsId, Array.from(smuggleGoodsCmsIdsOfTowns[townCmsId]))
        .then((ret) => {
          resp.sync = {
            add: {
              towns: {
                [townCmsId]: {
                  smugglePricePercents: ret,
                },
              },
            },
          };

          return this.sendJsonPacketToUser(
            userId,
            proto.Town.TOWN_SMUGGLE_PRICE_PERCENT_UPDATE_SC,
            resp
          );
        });
    });

    return Promise.all(respPromises);
  }

  broadcastTownInvestmentForSessionClosing(
    userIds: number[],
    resp: sync.Resp,
    townCmsId: number,
    myInvestmentScores: {
      [userId: number]: { myInvestmentScore: string; myInvestmentRank: number };
    },
    bUsersInGover: boolean
  ) {
    const promises = userIds.map((userId) => {
      if (bUsersInGover) {
        _.mergeWith<sync.Sync, sync.Sync>(
          resp.sync,
          {
            remove: {
              towns: {
                [townCmsId]: ['myInvestmentScore', 'myInvestmentRank'],
              },
            },
          },
          _townInvestRemoveSyncMergeCustomizer
        );

        if (myInvestmentScores[userId]) {
          _.merge<sync.Sync, sync.Sync>(resp.sync, {
            add: {
              towns: {
                [townCmsId]: {
                  myInvestmentScore: parseInt(myInvestmentScores[userId].myInvestmentScore, 10), // todo number 인지 확인
                },
              },
            },
          });

          if (!mutil.isNotANumber(myInvestmentScores[userId].myInvestmentRank)) {
            resp.sync.add.towns[townCmsId].myInvestmentRank =
              myInvestmentScores[userId].myInvestmentRank + 1;
          }
        }
      }

      return this.sendJsonPacketToUser<sync.Resp>(
        userId,
        proto.Town.TOWN_INVESTMENT_UPDATE_SC,
        resp
      );
    });

    return Promise.all(promises);
  }

  hadleBufferProxyPacketForUser<T = any>(
    userId: number,
    packetType: number,
    packetBody: T
  ): Promise<boolean> {
    const user: User = this._usersByUserId[userId];
    if (!user) {
      return Promise.resolve(false);
    }

    user.onRecvBufferProxy(packetType, packetBody);
    return Promise.resolve(true);
  }

  // ----------------------------------------------------------------------------
  // 접속한 모든유저들에게 이벤트를 넘긴다.
  // ----------------------------------------------------------------------------
  notifyWorldEventToAllUser(type: WorldEventNotificationType, msg: any) {
    const wend: WorldEventNotificationData = {};
    switch (type) {
      case WorldEventNotificationType.ChangedRegionNation:
        wend.changedRegionNation = {
          regionCmsId: msg.regionCmsId,
          regionOccupation: msg.regionOccupation,
          bComplete: msg.bComplete,
        };
        break;
    }

    _.forOwn(this._usersByUserId, (user) => {
      user.onWorldEventNotification(wend);
    });
  }

  onBoughtWebShopProduct(msg: BoughtWebShopProductPubMsg) {
    const user = this._usersByUserId[msg.userId];
    if (!user) {
      return;
    }

    this.sendJsonPacketToUser(msg.userId, proto.Common.BOUGHT_WEB_SHOP_PRODUCT_SC, {
      orderId: msg.orderId,
      cashShopCmsId: msg.cashShopCmsId,
    });
  }
}
