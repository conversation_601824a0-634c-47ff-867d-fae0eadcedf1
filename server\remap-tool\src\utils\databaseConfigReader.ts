import fs from 'fs-extra';
import path from 'path';
import JSON5 from 'json5';

export interface DatabaseConnectionInfo {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
}

export interface RedisConnectionInfo {
  host: string;
  port: number;
  db: number;
}

export interface WorldDatabaseConfig {
  worldId: string;
  mysqlAuth: DatabaseConnectionInfo;
  mysqlWorld: DatabaseConnectionInfo;
  mysqlUserShards: DatabaseConnectionInfo[];
  redisInstances: { [instanceName: string]: RedisConnectionInfo };
}

export class DatabaseConfigReader {
  private configPath: string;
  private config: any;

  constructor(configPath: string = './config/database.json5') {
    this.configPath = configPath;
  }

  async loadConfig(): Promise<void> {
    try {
      const configContent = await fs.readFile(this.configPath, 'utf8');
      this.config = JSON5.parse(configContent);
    } catch (error) {
      throw new Error(`데이터베이스 설정 파일을 읽을 수 없습니다: ${this.configPath} - ${error}`);
    }
  }

  getAuthDatabaseConfig(): DatabaseConnectionInfo {
    if (!this.config?.sharedConfig?.mysqlAuthDb) {
      throw new Error('Auth 데이터베이스 설정을 찾을 수 없습니다.');
    }

    const authDb = this.config.sharedConfig.mysqlAuthDb;
    return {
      host: authDb.host,
      port: authDb.port,
      user: authDb.user,
      password: authDb.password,
      database: authDb.database
    };
  }

  getWorldDatabaseConfig(worldId: string): WorldDatabaseConfig {
    const world = this.config?.worlds?.find((w: any) => w.id === worldId);
    if (!world) {
      throw new Error(`월드 ${worldId}의 설정을 찾을 수 없습니다.`);
    }

    // Auth DB (공통)
    const mysqlAuth = this.getAuthDatabaseConfig();

    // World DB
    const mysqlWorld: DatabaseConnectionInfo = {
      host: world.mysqlWorldDb.host,
      port: world.mysqlWorldDb.port,
      user: world.mysqlWorldDb.user,
      password: world.mysqlWorldDb.password,
      database: world.mysqlWorldDb.database
    };

    // User Shard DBs
    const mysqlUserShards: DatabaseConnectionInfo[] = [];
    if (world.mysqlUserDb?.shards) {
      const defaultCfg = world.mysqlUserDb.sqlDefaultCfg;
      world.mysqlUserDb.shards.forEach((shard: any) => {
        mysqlUserShards.push({
          host: defaultCfg.host,
          port: defaultCfg.port,
          user: defaultCfg.user,
          password: defaultCfg.password,
          database: shard.sqlCfg.database
        });
      });
    }

    // Redis instances
    const redisInstances: { [instanceName: string]: RedisConnectionInfo } = {};
    
    // Auth Redis (공통)
    if (this.config.sharedConfig.authRedis?.redisCfg) {
      const authRedis = this.config.sharedConfig.authRedis.redisCfg;
      redisInstances.auth = {
        host: authRedis.host,
        port: authRedis.port,
        db: authRedis.db
      };
    }

    // 월드별 Redis 인스턴스들
    const redisTypes = ['userCacheRedis', 'townRedis', 'nationRedis', 'guildRedis', 'rankingRedis'];
    redisTypes.forEach(redisType => {
      if (world[redisType]?.redisCfg) {
        const redisConfig = world[redisType].redisCfg;
        const instanceName = redisType.replace('Redis', ''); // userCacheRedis -> userCache
        redisInstances[instanceName] = {
          host: redisConfig.host,
          port: redisConfig.port,
          db: redisConfig.db
        };
      }
    });

    return {
      worldId,
      mysqlAuth,
      mysqlWorld,
      mysqlUserShards,
      redisInstances
    };
  }

  getAllWorldIds(): string[] {
    if (!this.config?.worlds) {
      return [];
    }
    return this.config.worlds.map((world: any) => world.id);
  }

  async getWorldDatabaseConfigs(worldIds: string[]): Promise<WorldDatabaseConfig[]> {
    await this.loadConfig();
    return worldIds.map(worldId => this.getWorldDatabaseConfig(worldId));
  }

  /**
   * Redis 인스턴스 설정 가져오기
   */
  getRedisConfig(instanceKey: string): RedisConnectionInfo | null {
    console.log(`🔍 Redis 설정 검색: ${instanceKey}`);

    if (!this.config) {
      console.log(`❌ config가 null입니다`);
      return null;
    }

    if (!this.config.sharedConfig) {
      console.log(`❌ config.sharedConfig가 없습니다`);
      return null;
    }

    console.log(`🔍 사용 가능한 sharedConfig 키들: ${Object.keys(this.config.sharedConfig).join(', ')}`);

    if (!this.config.sharedConfig[instanceKey]) {
      console.log(`❌ ${instanceKey} 키를 찾을 수 없습니다`);
      return null;
    }

    const redisConfig = this.config.sharedConfig[instanceKey].redisCfg;
    if (!redisConfig) {
      console.log(`❌ ${instanceKey}.redisCfg가 없습니다`);
      return null;
    }

    console.log(`✅ ${instanceKey} Redis 설정 찾음: ${redisConfig.host}:${redisConfig.port} -n ${redisConfig.db}`);
    return {
      host: redisConfig.host,
      port: redisConfig.port,
      db: redisConfig.db
    };
  }
}
