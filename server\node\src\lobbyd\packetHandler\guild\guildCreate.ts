// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { <PERSON><PERSON><PERSON>acketHandler } from '../index';
import { LobbyService } from '../../server';
import { Container } from 'typedi/Container';
import mhttp from '../../../motiflib/mhttp';
import { curTimeUtc } from '../../../motiflib/mutil';
import UserPoints, { CashPayment, PointChange, PointConsumptionCostParam } from '../../userPoints';
import tuGuildCreate from '../../../mysqllib/txn/tuGuildCreate';
import _ from 'lodash';
import { Sync, UserLightInfo } from '../../type/sync';
import {
  GuildData,
  GuildIdRangePerWorld,
  GUILD_MEMBER_GRADE,
  GUILD_NATION_CONDITION,
} from '../../../motiflib/model/lobby';
import cms from '../../../cms';
import mconf from '../../../motiflib/mconf';
import { GuildLogUtil, GuildUtil } from '../../guildUtil';
import * as mutilLanguage from '../../../motiflib/mutilLanguage';
import * as mutil from '../../../motiflib/mutil';

const rsn = 'guild_create';
const add_rsn = null;

// ----------------------------------------------------------------------------
interface RequestBody {
  guildName: string;
  introduction: string;
  joinType: number;
  joinCondition: number;
  joinConditionValue: number;
  nationCondition: number;
  emblemImageCmsId: number;
  emblemColorCmsId: number;
  emblemBorderCmsId: number;
  bPermitExchange: boolean;
}

/**
 * 길드 생성
 */
// ----------------------------------------------------------------------------
export class Cph_Guild_Create implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    const now = curTimeUtc();
    const reqBody: RequestBody = packet.bodyObj;

    const { guildRedis, userCacheRedis, userDbConnPoolMgr } = Container.get(LobbyService);

    // 길드가입 여부 확인. 코드문맥 실행 중  다른 문맥에서 가입되었을 경우(ex:길드장으로부터 길드가입승인)
    // 동기화가 틀어질 수 있으므로, tuCreateGuild 트랜젝션 처리 중에도 검사를 진행한다.
    if (user.userGuild.guildId) {
      throw new MError('have-already-joined-guild', MErrorCode.GUILD_ALREADY_JOINED, {
        userId: user.userId,
        guildId: user.userGuild.guildId,
      });
    }

    // user.ensureFeatureEnabled('uwo-guild-creation-enabled');

    GuildUtil.ensureLevel(user);

    GuildUtil.ensureExpireTimeToJoin(user);

    if (reqBody.emblemImageCmsId) {
      GuildUtil.ensureEmblemImage(user, 1, reqBody.emblemImageCmsId);
    }
    if (reqBody.emblemColorCmsId) {
      GuildUtil.ensureEmblemColor(user, reqBody.emblemColorCmsId);
    }
    if (reqBody.emblemBorderCmsId) {
      GuildUtil.ensureEmblemBorder(user, reqBody.emblemBorderCmsId);
    }

    GuildUtil.ensureGuildIntroductionLength(user, reqBody.introduction);

    const pointCost: PointConsumptionCostParam = {
      cmsId: cms.Const.GuildMakeNeedType.value,
      cost: cms.Const.GuildMakeNeedValue.value,
    };
    let pointChanges: PointChange[];
    let cashPayments: CashPayment[];
    if (pointCost) {
      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [pointCost],
        reqBody.bPermitExchange,
        { itemId: rsn },
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;
    }

    //  창단비용 체크

    mutilLanguage.ensureValidName(
      null,
      reqBody.guildName,
      cms.Const.GuildNameMinimum.value,
      cms.Const.GuildNameMaximum.value
    );

    let newGuildId = 0;
    const guildData: GuildData = {
      guild: {
        createdDateTimeUtc: now,

        guildName: reqBody.guildName, // 길드명
        introduction: reqBody.introduction, // 길드 소개글
        masterNotice: '', // 길드장 공지글
        autoNotificationType: 0, // 자동알림 <타입으로 분류>
        autoNotificationParam1: null, // 자동알림 파라미터1
        autoNotificationParam2: null, // 자동알림 파라미터2
        autoNotificationRegTimeUtc: null,
        dormantMasterCheckTimeUtc: now,
        joinType: reqBody.joinType, // 가입타입
        joinCondition: reqBody.joinCondition, // 가입 조건 타입
        joinConditionValue: reqBody.joinConditionValue, // 가입조건 값
        nationCondition: reqBody.nationCondition, // 국가 필터
        nationCmsId:
          reqBody.nationCondition === GUILD_NATION_CONDITION.SAME_NATION ? user.nationCmsId : 0,
        grade: GUILD_MEMBER_GRADE.MASTER, // 길드 등급 // ex에 DefaultGrade 설정하자.
        exp: 0, //  길드 경험치
        emblemImageCmsId: reqBody.emblemImageCmsId, // 문양id
        emblemColorCmsId: reqBody.emblemColorCmsId, // 문양색상
        emblemBorderCmsId: reqBody.emblemBorderCmsId, // 테투리
        grade_alias_1: null, // 1등급구성원 별칭
        grade_alias_2: null, // 2등급구성원 별칭
        grade_alias_3: null, // 3등급구성원 별칭
        grade_alias_4: null, // 4등급구성원 별칭
        dailyGuildPointResetTimeUtc: now,
        weeklyGuildPointResetTimeUtc: now,
        selectedBuffCategory: null,
      },
      members: {
        [user.userId]: {
          userId: user.userId,
          grade: GUILD_MEMBER_GRADE.MASTER,
          joinDate: now,
          guildPointUpdateTimeUtc: now,
          donations: {},
          registeredItemScore: 0,
        },
      },
      applicants: {},
      dailyGuildPoints: {},
      weeklyGuildPoints: {},
      accumGuildPoints: {},
      pickedDailyRewardIdxs: {},
      weeklyRewardUsers: {},
      guildUpgradePopupCheckedUsers: {
        [user.userId]: true,
      },

      resources: {},
      weeklyGuildDonations: {},
      accumGuildDonations: {},
      lastOpenTimeRaids: {},
      clearDifficultyRaids: {},
      learnedGuildBuffCmsIds: {},
      registeredGuildBuffItems: {},
      bossRaidBuffs: {},
    };

    // 처리과정 중 실패할 경우 redis 롤백 여부
    let redisRollback = false;

    const sync: Sync = {};
    //======================================================================================================
    // 먼저 cash 소모. 소모 후 길드 생성 실패처리는 운영쪽에서 처리.
    //======================================================================================================
    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return (
      user.userPoints
        .tryConsumeCashs(cashPayments, sync, user, {
          user,
          rsn,
          add_rsn,
          exchangeHash,
        })

        //======================================================================================================
        // 금칙어 검사.
        //======================================================================================================
        .then(() => {
          return mhttp.lgd.hasBadWord(reqBody.guildName).then((bHas) => {
            if (bHas) {
              throw new MError('has-bad-word', MErrorCode.GUILD_NAME_HAS_BAD_WORD, {
                userId: user.userId,
                name: reqBody.guildName,
              });
            }
          });
        })

        //======================================================================================================
        // 중복 길드명체크(길드생성 트랜잭션을 위해 createGuild.lua에서 한번 더 중복명을 검사)
        //======================================================================================================
        .then(() => {
          return guildRedis['getGuildIdByName'](guildData.guild.guildName).then(
            (guildId: number) => {
              if (guildId > 0) {
                throw new MError(
                  'guild-name-already-exists',
                  MErrorCode.GUILD_NAME_ALREADY_EXISTS,
                  {
                    userId: user.userId,
                  }
                );
              }
            }
          );
        })
        //======================================================================================================
        // 길드 id 발행
        // 주의) 먼저 id 발행해야만  'createGuild' 레디스 호출이 실패 났을 경우 발행한 id로 길드 제거가 가능하다.
        //======================================================================================================
        .then(() => {
          return guildRedis['generateGuildId']();
        })

        //======================================================================================================
        // 레디스 길드 생성
        //======================================================================================================
        .then((genId: number) => {
          const strWorldId: string = mconf.worldId;

          const worldNo = parseInt(strWorldId.replace(/\D/g, ''));
          if (mutil.isNotANumber(worldNo)) {
            throw new MError('invalid-world-id', MErrorCode.GUILD_INVALID_WORLD_ID, {
              userId: user.userId,
              worldId: strWorldId,
            });
          }

          const guildId = worldNo * GuildIdRangePerWorld + genId;

          newGuildId = guildId;
          redisRollback = true;
          const score = GuildUtil.makeSearchScore(guildData);
          return guildRedis['createGuild'](
            guildId,
            user.userId,
            score,
            JSON.stringify(guildData.guild),
            JSON.stringify(guildData.members[user.userId])
          );
        })

        //======================================================================================================
        // 유저 DB 업데이트
        //======================================================================================================
        .then(() => {
          return tuGuildCreate(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId,
            newGuildId,
            pointChanges
          );
        })

        //======================================================================================================
        // 유저 캐시 레디스에 길드 업데이트
        //======================================================================================================
        .then(() => {
          redisRollback = false;
          userCacheRedis['setUserGuild'](user.userId, newGuildId).catch((err) => {
            mlog.error('userCacheRedis setUserGuild is failed at guildCreate.', {
              err: err.message,
              userId: user.userId,
            });
          });
        })

        //======================================================================================================
        // 가입대기중인 길드에서 신청자를 제거 후 길드장에게 통보
        //======================================================================================================
        .then(() => {
          const oldWaitingJoinGuildIds: number[] = _.keys(user.userGuild.waitingJoinGuildIds).map(
            (strGuildIds) => parseInt(strGuildIds, 10)
          );

          return GuildUtil.removeGuildApplicantAndNotiToMaster(user.userId, oldWaitingJoinGuildIds);
        })

        //======================================================================================================
        // 메모리 업데이트 및 결과 전달.
        //======================================================================================================
        .then(() => {
          const userLightInfos: { [userId: number]: UserLightInfo } = {
            [user.userId]: {
              userId: user.userId,
              name: user.userName,
              nationCmsId: user.nationCmsId,
              level: user.level,
              leaderMateCmsId: user.userMates.getLeaderMate(user.userFleets).getNub().cmsId,
              leaderMateAwakenLevel: user.userMates.getLeaderMate(user.userFleets).getNub()
                .awakenLv,
              pubId: user.pubId,
              guildId: newGuildId,
              isOnline: 1,
            },
          };

          _.merge<Sync, Sync, Sync, Sync>(
            sync,
            user.userGuild.joinGuild(user, newGuildId, guildData),
            GuildUtil.buildGuildSyncDataAll(user, guildData, userLightInfos),
            user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
          );

          gLog_guildCreate(user, newGuildId, guildData, userLightInfos, exchangeHash);

          return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
        })
        .catch((error) => {
          return Promise.resolve()
            .then(() => {
              // 길드창단 처리 중 실패된 경우 지금까지 업데이트 된 DB를 롤백해야한다.
              if (redisRollback) {
                return guildRedis['deleteGuild'](newGuildId).then(() => {
                  mlog.info('[CREATE GUILD] Rollback deleteGuild', {
                    userId: user.userId,
                    newGuildId,
                  });
                });
              }
            })
            .finally(() => {
              throw error;
            });
        })
    );
  }
}
function gLog_guildCreate(
  user: User,
  guildId: number,
  guildData: GuildData,
  userLightInfos: { [userId: number]: UserLightInfo },
  exchangeHash: string
) {
  const guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);

  let nation: string = null;
  if (user.nationCmsId) {
    const nationCms = cms.Nation[user.nationCmsId];
    nation = nationCms ? nationCms.name : null;
  }

  user.glog('guild_create', {
    nation,
    rsn,
    add_rsn,
    guild_data,
    exchange_hash: exchangeHash,
  });
}
