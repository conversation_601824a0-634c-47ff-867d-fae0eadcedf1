// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { Sync, Resp } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import UserPoints, { CashPayment, PointChange } from '../../userPoints';
import { ItemChange } from '../../userInven';
import tuUnlockManufactureRecipe from '../../../mysqllib/txn/tuUnlockManufactureRecipe';
import { ClientPacketHandler } from '../index';
import { AccumulateParam } from '../../userAchievement';
import { ManufactureRecipeDesc } from '../../../cms/manufactureRecipeDesc';
import { MANUFACTURE_RECIPE_UNLOCKTYPE, ManufactureGroupDesc } from '../../../cms/manufactureGroupDesc';

// ----------------------------------------------------------------------------
// 제조 레시피 해금
// ----------------------------------------------------------------------------

const rsn = 'manufacture_unlock_recipe';
const add_rsn = null;

interface RequestBody {
  recipeCmsId: number;        // 레시피 그룹cms Id : ManufactureGroup
  bPermitExchange?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Manufacture_UnlockRecipe implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;
    const { recipeCmsId, bPermitExchange } = reqBody;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // ===============================================================================================
    // [step.1] 레시피 데이터 validation
    // ===============================================================================================
    const manufactureGroupCms = cms.ManufactureGroup[recipeCmsId];
    if (!manufactureGroupCms) {
      throw new MError('invalid-manufacture-recipe-cms-id', MErrorCode.INVALID_MANUFACTURE_RECIPE_CMS_ID, {
        recipeCmsId,
      });
    }

    // 여기는 item사용만 체크한다.
    if (manufactureGroupCms.unlockType === MANUFACTURE_RECIPE_UNLOCKTYPE.LIVE_EVENT) {
      throw new MError('invalid-manufacture-recipe-cms-id', MErrorCode.INVALID_MANUFACTURE_RECIPE_CMS_ID, {
        recipeCmsId,
      });
    }

    // ===============================================================================================
    // [step.2] 이미 해금되어 있는지 확인
    // ===============================================================================================
    if (user.userManufacture.isRecipeGroupUnlocked(recipeCmsId)) {
      throw new MError(
        'ALREADY_UNLOCKED_MANUFACTURE_RECIPE',
        MErrorCode.ALREADY_UNLOCKED_MANUFACTURE_RECIPE,
        {
          recipeCmsId,
        }
      );
    }

    // ===============================================================================================
    // [step.3] 아이템 사용 로직
    // ===============================================================================================
    let itemChange: ItemChange | null = null;
    if (manufactureGroupCms.unlockTargetId) {
      itemChange = user.userInven.itemInven.buildItemChange(
        manufactureGroupCms.unlockTargetId,
        -1, // 1개 사용
        true
      );
    }

    // ===============================================================================================
    // [step.4] DB 처리 및 응답
    // ===============================================================================================
    const sync: Sync = {};

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return user.userPoints
      .tryConsumeCashs([], sync, user, { user, rsn, add_rsn, exchangeHash })
      .then(() => {
        return tuUnlockManufactureRecipe(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          recipeCmsId,
          itemChange
        );
      })
      .then(() => {
        const accums: AccumulateParam[] = [];

        // ===============================================================================================
        // [step.5] 레시피 해금 정보를 userManufacture에 적용
        // ===============================================================================================
        user.userManufacture.unlockRecipeGroup(recipeCmsId);

        // ===============================================================================================
        // [step.6] 아이템 차감 sync 적용
        // ===============================================================================================
        if (itemChange) {
          _.merge<Sync, Sync>(
            sync,
            user.userInven.itemInven.applyItemChange(itemChange, accums, {
              user,
              rsn,
              add_rsn,
            })
          );
        }

        // ===============================================================================================
        // [step.7] 레시피 해금 sync 적용
        // ===============================================================================================
        _.merge<Sync, Sync>(sync, {
          add: {
            manufacture: {
              unlockedRecipes: {
                [recipeCmsId]: true,
              },
            },
          },
        });

        if (accums.length > 0) {
          return user.userAchievement.accumulate(accums, user, sync, { user, rsn, add_rsn });
        }
      })
      .then(() => {
        // ===============================================================================================
        // [step.8] 로그 및 응답
        // ===============================================================================================
        user.glog('manufacture_unlock_recipe', {
          rsn,
          add_rsn,
          recipeCmsId,
          unlockItemId: manufactureGroupCms.unlockTargetId,
        });

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }
}
