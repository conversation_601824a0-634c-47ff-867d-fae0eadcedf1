import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import ora from 'ora';
import { DatabaseManager } from '../database/databaseManager';
import { RedisManager } from '../redis/redisManager';
import {
  BackupOptions,
  RestoreOptions,
  BackupResult,
  RestoreResult,
  BackupMetadata,
  BackupListItem,
  DatabaseBackupData,
  RedisBackupData
} from '../types/backup';

export class BackupManager {
  private dbManager: DatabaseManager;
  private redisManager: RedisManager;
  private backupDir: string;

  constructor(
    dbManager: DatabaseManager,
    redisManager: RedisManager,
    backupDir: string = './backup'
  ) {
    this.dbManager = dbManager;
    this.redisManager = redisManager;
    this.backupDir = backupDir;
  }

  /**
   * 백업 생성
   */
  async createBackup(options: BackupOptions): Promise<BackupResult> {
    const startTime = Date.now();
    const timestamp = this.generateTimestamp();
    const backupPath = path.join(this.backupDir, timestamp);
    const warnings: string[] = [];

    try {
      console.log(chalk.blue('🗄️ 백업 생성 시작...'));
      
      // 백업 디렉토리 생성
      await fs.ensureDir(backupPath);
      
      // 데이터베이스 백업
      const databaseBackups: { [key: string]: DatabaseBackupData } = {};
      let totalTableCount = 0;
      
      if (!options.databases || options.databases.includes('auth')) {
        console.log(chalk.cyan('💾 Auth 데이터베이스 백업 중...'));
        const authBackup = await this.backupAuthDatabase();
        databaseBackups.auth = authBackup;
        totalTableCount += Object.keys(authBackup.tables).length;
      }
      
      if (!options.databases || options.databases.includes('world')) {
        console.log(chalk.cyan('🌍 World 데이터베이스 백업 중...'));
        const worldBackups = await this.backupWorldDatabases(options.worldId);
        Object.assign(databaseBackups, worldBackups);
        Object.values(worldBackups).forEach(backup => {
          totalTableCount += Object.keys(backup.tables).length;
        });
      }
      
      if (!options.databases || options.databases.includes('user')) {
        console.log(chalk.cyan('👥 User 데이터베이스 백업 중...'));
        const userBackups = await this.backupUserDatabases(options.worldId);
        Object.assign(databaseBackups, userBackups);
        Object.values(userBackups).forEach(backup => {
          totalTableCount += Object.keys(backup.tables).length;
        });
      }
      
      // Redis 백업
      const redisBackups: { [key: string]: RedisBackupData } = {};
      let totalRedisKeyCount = 0;
      
      if (options.includeRedis) {
        console.log(chalk.cyan('🔴 Redis 백업 중...'));
        const redisData = await this.backupRedisData(options.worldId);
        Object.assign(redisBackups, redisData);
        Object.values(redisData).forEach(backup => {
          totalRedisKeyCount += backup.keyCount;
        });
      }
      
      // 백업 데이터 저장
      await fs.writeJson(path.join(backupPath, 'databases.json'), databaseBackups, { spaces: 2 });
      if (options.includeRedis) {
        await fs.writeJson(path.join(backupPath, 'redis.json'), redisBackups, { spaces: 2 });
      }
      
      // 메타데이터 생성
      const metadata: BackupMetadata = {
        timestamp,
        createdAt: new Date(),
        description: options.description,
        worlds: this.getBackedUpWorlds(databaseBackups, redisBackups),
        databases: options.databases || ['auth', 'world', 'user'],
        includesRedis: !!options.includeRedis,
        compressed: !!options.compress,
        size: await this.calculateBackupSize(backupPath),
        tableCount: totalTableCount,
        redisKeyCount: totalRedisKeyCount
      };
      
      await fs.writeJson(path.join(backupPath, 'metadata.json'), metadata, { spaces: 2 });
      
      // 압축 (옵션)
      if (options.compress) {
        console.log(chalk.cyan('🗜️ 백업 압축 중...'));
        await this.compressBackup(backupPath);
      }
      
      const duration = Date.now() - startTime;
      
      return {
        success: true,
        metadata,
        backupPath,
        warnings,
        duration
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(chalk.red('❌ 백업 실패:'), error);
      
      // 실패한 백업 디렉토리 정리
      try {
        await fs.remove(backupPath);
      } catch (cleanupError) {
        console.warn(chalk.yellow('⚠️ 백업 디렉토리 정리 실패:'), cleanupError);
      }
      
      return {
        success: false,
        metadata: {} as BackupMetadata,
        backupPath: '',
        error: error instanceof Error ? error.message : String(error),
        warnings,
        duration
      };
    }
  }

  /**
   * 백업 복원
   */
  async restoreBackup(options: RestoreOptions): Promise<RestoreResult> {
    const startTime = Date.now();
    const backupPath = path.join(this.backupDir, options.timestamp);
    const warnings: string[] = [];

    try {
      console.log(chalk.blue('🔄 백업 복원 시작...'));
      
      // 백업 존재 확인
      if (!await fs.pathExists(backupPath)) {
        throw new Error(`백업을 찾을 수 없습니다: ${options.timestamp}`);
      }
      
      // 메타데이터 로드
      const metadataPath = path.join(backupPath, 'metadata.json');
      if (!await fs.pathExists(metadataPath)) {
        throw new Error('백업 메타데이터를 찾을 수 없습니다');
      }
      
      const metadata: BackupMetadata = await fs.readJson(metadataPath);
      
      // 압축 해제 (필요한 경우)
      if (metadata.compressed) {
        console.log(chalk.cyan('🗜️ 백업 압축 해제 중...'));
        await this.decompressBackup(backupPath);
      }
      
      let restoredDatabases = 0;
      let restoredTables = 0;
      let restoredRedisKeys = 0;
      
      // 데이터베이스 복원
      const databasesPath = path.join(backupPath, 'databases.json');
      if (await fs.pathExists(databasesPath)) {
        console.log(chalk.cyan('💾 데이터베이스 복원 중...'));
        const databaseBackups: { [key: string]: DatabaseBackupData } = await fs.readJson(databasesPath);
        
        for (const [dbName, dbData] of Object.entries(databaseBackups)) {
          if (!options.databases || this.shouldRestoreDatabase(dbName, options.databases)) {
            await this.restoreDatabase(dbName, dbData, options.worldId);
            restoredDatabases++;
            restoredTables += Object.keys(dbData.tables).length;
          }
        }
      }
      
      // Redis 복원
      if (options.includeRedis && metadata.includesRedis) {
        const redisPath = path.join(backupPath, 'redis.json');
        if (await fs.pathExists(redisPath)) {
          console.log(chalk.cyan('🔴 Redis 복원 중...'));
          const redisBackups: { [key: string]: RedisBackupData } = await fs.readJson(redisPath);
          
          for (const [instanceName, redisData] of Object.entries(redisBackups)) {
            if (!options.worldId || this.shouldRestoreRedisInstance(instanceName, options.worldId)) {
              await this.restoreRedisInstance(instanceName, redisData);
              restoredRedisKeys += redisData.keyCount;
            }
          }
        }
      }
      
      const duration = Date.now() - startTime;
      
      return {
        success: true,
        metadata,
        restoredDatabases,
        restoredTables,
        restoredRedisKeys,
        warnings,
        duration
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(chalk.red('❌ 복원 실패:'), error);
      
      return {
        success: false,
        metadata: {} as BackupMetadata,
        restoredDatabases: 0,
        restoredTables: 0,
        restoredRedisKeys: 0,
        error: error instanceof Error ? error.message : String(error),
        warnings,
        duration
      };
    }
  }

  /**
   * 백업 목록 조회
   */
  async listBackups(): Promise<BackupListItem[]> {
    try {
      if (!await fs.pathExists(this.backupDir)) {
        return [];
      }
      
      const backupDirs = await fs.readdir(this.backupDir);
      const backups: BackupListItem[] = [];
      
      for (const dir of backupDirs) {
        const backupPath = path.join(this.backupDir, dir);
        const metadataPath = path.join(backupPath, 'metadata.json');
        
        if (await fs.pathExists(metadataPath)) {
          try {
            const metadata: BackupMetadata = await fs.readJson(metadataPath);
            
            backups.push({
              timestamp: metadata.timestamp,
              createdAt: metadata.createdAt,
              description: metadata.description,
              size: metadata.size,
              compressed: metadata.compressed,
              worldCount: metadata.worlds.length,
              tableCount: metadata.tableCount,
              redisKeyCount: metadata.redisKeyCount
            });
          } catch (error) {
            console.warn(chalk.yellow(`⚠️ 메타데이터 읽기 실패: ${dir}`), error);
          }
        }
      }
      
      // 최신순으로 정렬
      backups.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      
      return backups;
    } catch (error) {
      console.error(chalk.red('❌ 백업 목록 조회 실패:'), error);
      return [];
    }
  }

  /**
   * 타임스탬프 생성
   */
  private generateTimestamp(): string {
    const now = new Date();
    return now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
  }

  /**
   * 백업 크기 계산
   */
  private async calculateBackupSize(backupPath: string): Promise<number> {
    try {
      const stats = await fs.stat(backupPath);
      if (stats.isDirectory()) {
        let totalSize = 0;
        const files = await fs.readdir(backupPath);

        for (const file of files) {
          const filePath = path.join(backupPath, file);
          const fileStats = await fs.stat(filePath);
          totalSize += fileStats.size;
        }

        return totalSize;
      } else {
        return stats.size;
      }
    } catch (error) {
      console.warn(chalk.yellow('⚠️ 백업 크기 계산 실패:'), error);
      return 0;
    }
  }

  /**
   * Auth 데이터베이스 백업
   */
  private async backupAuthDatabase(): Promise<DatabaseBackupData> {
    const spinner = ora('Auth 데이터베이스 백업 중...').start();

    try {
      const authPool = this.dbManager.getAuthConnection();
      const tables: { [tableName: string]: any } = {};

      // 테이블 목록 조회
      const [tableRows] = await authPool.execute(`
        SELECT TABLE_NAME, TABLE_COMMENT, ENGINE, TABLE_COLLATION, AUTO_INCREMENT
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_TYPE = 'BASE TABLE'
        AND TABLE_NAME LIKE 'a_%'
        ORDER BY TABLE_NAME
      `) as any[];

      for (const tableRow of tableRows) {
        const tableName = tableRow.TABLE_NAME;

        // 테이블 스키마 조회
        const [schemaRows] = await authPool.execute(`DESCRIBE ${tableName}`) as any[];

        // 테이블 데이터 조회
        const [dataRows] = await authPool.execute(`SELECT * FROM ${tableName}`) as any[];

        tables[tableName] = {
          schema: schemaRows,
          data: dataRows,
          recordCount: dataRows.length,
          autoIncrement: tableRow.AUTO_INCREMENT,
          tableComment: tableRow.TABLE_COMMENT,
          engine: tableRow.ENGINE,
          collation: tableRow.TABLE_COLLATION
        };
      }

      spinner.succeed(`Auth 데이터베이스 백업 완료 (${Object.keys(tables).length}개 테이블)`);

      return {
        database: 'auth',
        tables,
        views: {},
        procedures: {},
        functions: {},
        triggers: {},
        events: {}
      };
    } catch (error) {
      spinner.fail('Auth 데이터베이스 백업 실패');
      throw error;
    }
  }

  /**
   * World 데이터베이스들 백업
   */
  private async backupWorldDatabases(worldId?: string): Promise<{ [key: string]: DatabaseBackupData }> {
    const worldBackups: { [key: string]: DatabaseBackupData } = {};
    const worldIds = worldId ? [worldId] : this.dbManager.getWorldIds();

    for (const wId of worldIds) {
      const spinner = ora(`World 데이터베이스 백업 중 (${wId})...`).start();

      try {
        const worldPool = this.dbManager.getWorldConnection(wId);
        const tables: { [tableName: string]: any } = {};

        // 테이블 목록 조회
        const [tableRows] = await worldPool.execute(`
          SELECT TABLE_NAME, TABLE_COMMENT, ENGINE, TABLE_COLLATION, AUTO_INCREMENT
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_TYPE = 'BASE TABLE'
          AND TABLE_NAME LIKE 'w_%'
          ORDER BY TABLE_NAME
        `) as any[];

        for (const tableRow of tableRows) {
          const tableName = tableRow.TABLE_NAME;

          // 테이블 스키마 조회
          const [schemaRows] = await worldPool.execute(`DESCRIBE ${tableName}`) as any[];

          // 테이블 데이터 조회
          const [dataRows] = await worldPool.execute(`SELECT * FROM ${tableName}`) as any[];

          tables[tableName] = {
            schema: schemaRows,
            data: dataRows,
            recordCount: dataRows.length,
            autoIncrement: tableRow.AUTO_INCREMENT,
            tableComment: tableRow.TABLE_COMMENT,
            engine: tableRow.ENGINE,
            collation: tableRow.TABLE_COLLATION
          };
        }

        worldBackups[`world_${wId}`] = {
          database: `world_${wId}`,
          tables,
          views: {},
          procedures: {},
          functions: {},
          triggers: {},
          events: {}
        };

        spinner.succeed(`World 데이터베이스 백업 완료 (${wId}: ${Object.keys(tables).length}개 테이블)`);
      } catch (error) {
        spinner.fail(`World 데이터베이스 백업 실패 (${wId})`);
        throw error;
      }
    }

    return worldBackups;
  }

  /**
   * User 데이터베이스들 백업
   */
  private async backupUserDatabases(worldId?: string): Promise<{ [key: string]: DatabaseBackupData }> {
    const userBackups: { [key: string]: DatabaseBackupData } = {};
    const worldIds = worldId ? [worldId] : this.dbManager.getWorldIds();

    for (const wId of worldIds) {
      const shardConnections = this.dbManager.getWorldUserShardConnections(wId);

      for (const [shardId, shardPool] of shardConnections) {
        const spinner = ora(`User 데이터베이스 백업 중 (${wId}-shard-${shardId})...`).start();

        try {
          const tables: { [tableName: string]: any } = {};

          // 테이블 목록 조회
          const [tableRows] = await shardPool.execute(`
            SELECT TABLE_NAME, TABLE_COMMENT, ENGINE, TABLE_COLLATION, AUTO_INCREMENT
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_TYPE = 'BASE TABLE'
            AND TABLE_NAME LIKE 'u_%'
            ORDER BY TABLE_NAME
          `) as any[];

          for (const tableRow of tableRows) {
            const tableName = tableRow.TABLE_NAME;

            // 테이블 스키마 조회
            const [schemaRows] = await shardPool.execute(`DESCRIBE ${tableName}`) as any[];

            // 테이블 데이터 조회
            const [dataRows] = await shardPool.execute(`SELECT * FROM ${tableName}`) as any[];

            tables[tableName] = {
              schema: schemaRows,
              data: dataRows,
              recordCount: dataRows.length,
              autoIncrement: tableRow.AUTO_INCREMENT,
              tableComment: tableRow.TABLE_COMMENT,
              engine: tableRow.ENGINE,
              collation: tableRow.TABLE_COLLATION
            };
          }

          userBackups[`user_${wId}_shard_${shardId}`] = {
            database: `user_${wId}_shard_${shardId}`,
            tables,
            views: {},
            procedures: {},
            functions: {},
            triggers: {},
            events: {}
          };

          spinner.succeed(`User 데이터베이스 백업 완료 (${wId}-shard-${shardId}: ${Object.keys(tables).length}개 테이블)`);
        } catch (error) {
          spinner.fail(`User 데이터베이스 백업 실패 (${wId}-shard-${shardId})`);
          throw error;
        }
      }
    }

    return userBackups;
  }

  /**
   * Redis 데이터 백업
   */
  private async backupRedisData(worldId?: string): Promise<{ [key: string]: RedisBackupData }> {
    const redisBackups: { [key: string]: RedisBackupData } = {};
    const instanceNames = this.redisManager.getInstanceNames();

    for (const instanceName of instanceNames) {
      // 월드 ID가 지정된 경우 해당 월드의 인스턴스만 백업
      if (worldId && !this.shouldBackupRedisInstance(instanceName, worldId)) {
        continue;
      }

      const spinner = ora(`Redis 백업 중 (${instanceName})...`).start();

      try {
        const client = this.redisManager.getClient(instanceName);
        const data: { [key: string]: any } = {};

        // 모든 키 조회
        const keys = await client.keys('*');

        for (const key of keys) {
          // 키 타입 확인
          const type = await client.type(key);

          // TTL 조회
          const ttl = await client.ttl(key);

          // 타입별로 값 조회
          let value: any;
          switch (type) {
            case 'string':
              value = await client.get(key);
              break;
            case 'hash':
              value = await client.hgetall(key);
              break;
            case 'list':
              value = await client.lrange(key, 0, -1);
              break;
            case 'set':
              value = await client.smembers(key);
              break;
            case 'zset':
              value = await client.zrange(key, 0, -1, 'WITHSCORES');
              break;
            default:
              console.warn(chalk.yellow(`⚠️ 지원하지 않는 Redis 타입: ${type} (키: ${key})`));
              continue;
          }

          data[key] = {
            type,
            value,
            ttl
          };
        }

        redisBackups[instanceName] = {
          instance: instanceName,
          database: 0, // 기본 데이터베이스
          data,
          keyCount: keys.length
        };

        spinner.succeed(`Redis 백업 완료 (${instanceName}: ${keys.length}개 키)`);
      } catch (error) {
        spinner.fail(`Redis 백업 실패 (${instanceName})`);
        throw error;
      }
    }

    return redisBackups;
  }

  /**
   * 데이터베이스 복원
   */
  private async restoreDatabase(dbName: string, dbData: DatabaseBackupData, worldId?: string): Promise<void> {
    const spinner = ora(`데이터베이스 복원 중 (${dbName})...`).start();

    try {
      let pool: any;

      // 데이터베이스 타입에 따라 연결 선택
      if (dbName === 'auth') {
        pool = this.dbManager.getAuthConnection();
      } else if (dbName.startsWith('world_')) {
        const wId = dbName.replace('world_', '');
        pool = this.dbManager.getWorldConnection(wId);
      } else if (dbName.startsWith('user_')) {
        const match = dbName.match(/user_(.+)_shard_(\d+)/);
        if (match && match[1] && match[2]) {
          const wId = match[1];
          const shardId = parseInt(match[2]);
          pool = this.dbManager.getUserShardConnection(wId, shardId);
        }
      }

      if (!pool) {
        throw new Error(`데이터베이스 연결을 찾을 수 없습니다: ${dbName}`);
      }

      // 테이블별 복원
      for (const [tableName, tableData] of Object.entries(dbData.tables)) {
        // 기존 데이터 삭제
        await pool.execute(`DELETE FROM ${tableName}`);

        // 데이터 복원
        if (tableData.data.length > 0) {
          const columns = Object.keys(tableData.data[0]);
          const placeholders = columns.map(() => '?').join(', ');
          const insertSql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

          for (const row of tableData.data) {
            const values = columns.map(col => row[col]);
            await pool.execute(insertSql, values);
          }
        }

        // AUTO_INCREMENT 값 복원
        if (tableData.autoIncrement) {
          await pool.execute(`ALTER TABLE ${tableName} AUTO_INCREMENT = ${tableData.autoIncrement}`);
        }
      }

      spinner.succeed(`데이터베이스 복원 완료 (${dbName}: ${Object.keys(dbData.tables).length}개 테이블)`);
    } catch (error) {
      spinner.fail(`데이터베이스 복원 실패 (${dbName})`);
      throw error;
    }
  }

  /**
   * Redis 인스턴스 복원
   */
  private async restoreRedisInstance(instanceName: string, redisData: RedisBackupData): Promise<void> {
    const spinner = ora(`Redis 복원 중 (${instanceName})...`).start();

    try {
      const client = this.redisManager.getClient(instanceName);

      // 기존 데이터 삭제
      await client.flushdb();

      // 데이터 복원
      for (const [key, keyData] of Object.entries(redisData.data)) {
        // 타입별로 데이터 복원
        switch (keyData.type) {
          case 'string':
            await client.set(key, keyData.value);
            break;
          case 'hash':
            await client.hset(key, keyData.value);
            break;
          case 'list':
            if (Array.isArray(keyData.value)) {
              await client.lpush(key, ...keyData.value.reverse());
            }
            break;
          case 'set':
            if (Array.isArray(keyData.value)) {
              await client.sadd(key, ...keyData.value);
            }
            break;
          case 'zset':
            if (Array.isArray(keyData.value)) {
              const args: any[] = [];
              for (let i = 0; i < keyData.value.length; i += 2) {
                args.push(keyData.value[i + 1], keyData.value[i]); // score, member
              }
              if (args.length > 0) {
                await client.zadd(key, ...args);
              }
            }
            break;
        }

        // TTL 설정
        if (keyData.ttl > 0) {
          await client.expire(key, keyData.ttl);
        }
      }

      spinner.succeed(`Redis 복원 완료 (${instanceName}: ${Object.keys(redisData.data).length}개 키)`);
    } catch (error) {
      spinner.fail(`Redis 복원 실패 (${instanceName})`);
      throw error;
    }
  }

  /**
   * 유틸리티 메서드들
   */
  private getBackedUpWorlds(databaseBackups: any, redisBackups: any): string[] {
    const worlds = new Set<string>();

    // 데이터베이스에서 월드 추출
    for (const dbName of Object.keys(databaseBackups)) {
      if (dbName.startsWith('world_')) {
        worlds.add(dbName.replace('world_', ''));
      } else if (dbName.startsWith('user_')) {
        const match = dbName.match(/user_(.+)_shard_\d+/);
        if (match && match[1]) {
          worlds.add(match[1]);
        }
      }
    }

    // Redis에서 월드 추출
    for (const instanceName of Object.keys(redisBackups)) {
      const match = instanceName.match(/^(.+)-(.+)$/);
      if (match && match[1]) {
        worlds.add(match[1]);
      }
    }

    return Array.from(worlds);
  }

  private shouldBackupRedisInstance(instanceName: string, worldId: string): boolean {
    // 공유 인스턴스는 항상 백업
    const sharedInstances = ['auth', 'monitor', 'order'];
    if (sharedInstances.includes(instanceName)) {
      return true;
    }

    // 월드별 인스턴스는 해당 월드만 백업
    return instanceName.startsWith(`${worldId}-`);
  }

  private shouldRestoreDatabase(dbName: string, databases: ('auth' | 'world' | 'user')[]): boolean {
    if (databases.includes('auth') && dbName === 'auth') return true;
    if (databases.includes('world') && dbName.startsWith('world_')) return true;
    if (databases.includes('user') && dbName.startsWith('user_')) return true;
    return false;
  }

  private shouldRestoreRedisInstance(instanceName: string, worldId: string): boolean {
    // 공유 인스턴스는 항상 복원
    const sharedInstances = ['auth', 'monitor', 'order'];
    if (sharedInstances.includes(instanceName)) {
      return true;
    }

    // 월드별 인스턴스는 해당 월드만 복원
    return instanceName.startsWith(`${worldId}-`);
  }

  private async compressBackup(backupPath: string): Promise<void> {
    // 압축 기능은 추후 구현 (tar.gz 등)
    console.log(chalk.yellow('⚠️ 압축 기능은 아직 구현되지 않았습니다.'));
  }

  private async decompressBackup(backupPath: string): Promise<void> {
    // 압축 해제 기능은 추후 구현
    console.log(chalk.yellow('⚠️ 압축 해제 기능은 아직 구현되지 않았습니다.'));
  }
}
