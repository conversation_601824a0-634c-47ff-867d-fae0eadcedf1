"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const merror_1 = require("../../motiflib/merror");
const typedi_1 = __importDefault(require("typedi"));
const fleetManager_1 = require("../fleetManager");
const ex_1 = require("../../cms/ex");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const mutil = __importStar(require("../../motiflib/mutil"));
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
router.on(protocol_1.Protocol.LB2OC_REQ_ZOOM_IN_NPC, async (req, res) => {
    const userId = req.userId;
    const targetNpcId = req.targetNpcId;
    const interactableDistance = req.interactableDistance;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const ownerFleet = fleetManager.get(userId);
    if (!ownerFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const grid = ownerFleet.getGrid();
    const npcFleet = grid.findVisibleNpc(targetNpcId);
    if (!npcFleet) {
        // 로그가 너무 많이 남고 있다.
        // 기획에 문의한바 무시해도 되는 경우라고 한다.
        return;
        // throw new MError('NPC not found.', MErrorCode.INVALID_OCEAN_NPC_ID, {
        //   userId: req.userId,
        //   targetNpcId,
        // });
    }
    // 인터렉션이 불가능한 npc
    const npcCms = (0, ex_1.getOceanNpcCms)()[npcFleet.getNpcCmsId()];
    if (npcCms.interactionType && npcCms.interactionType === 0) {
        throw new merror_1.MError('npc-interaction-type-off', merror_1.MErrorCode.NPC_INTERACTION_TYPE_OFF, {
            userId: ownerFleet.userId,
            npcCmsId: npcCms.id,
        });
    }
    const targetDistance = ownerFleet.getDistanceFrom(npcFleet.getLocation());
    if (targetDistance > interactableDistance) {
        throw new merror_1.MError('npc-is-too-far-away', merror_1.MErrorCode.NPC_INTERACTION_TYPE_OFF, {
            userId: ownerFleet.userId,
            npcCmsId: npcCms.id,
            interactableDistance,
            targetDistance,
        });
    }
    const sendPacket = new protocol_1.Protocol.OC2LB_RES_ZOOM_IN_NPC();
    sendPacket.zoominNpcDesc = npcFleet.getZoomInNpcDesc();
    sendPacket.targetDistance = targetDistance;
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_REQ_ZOOM_IN_USER, async (req, res) => {
    const userId = req.userId;
    const targetUserId = req.targetUserId;
    const interactableDistance = req.interactableDistance;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const ownerFleet = fleetManager.get(userId);
    if (!ownerFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const grid = ownerFleet.getGrid();
    const targetUserFleet = grid.findVisibleUser(targetUserId);
    if (!targetUserFleet) {
        throw new merror_1.MError('user not found.', merror_1.MErrorCode.OCEAN_ZOOM_IN_USER_NOT_FOUND, {
            userId: req.userId,
            targetUserId,
        });
    }
    const targetDistance = ownerFleet.getDistanceFrom(targetUserFleet.getLocation());
    if (targetDistance > interactableDistance) {
        throw new merror_1.MError('target-is-too-far-away', merror_1.MErrorCode.NPC_INTERACTION_TYPE_OFF, {
            userId: ownerFleet.userId,
            targetuserId: targetUserFleet.userId,
            interactableDistance,
            targetDistance,
        });
    }
    const curTimeUtc = mutil.curTimeUtc();
    const sendPacket = new protocol_1.Protocol.OC2LB_RES_ZOOM_IN_USER();
    sendPacket.zoominUserDesc = targetUserFleet.getZoomInUserDesc(ownerFleet.getNationCmsId(), curTimeUtc);
    sendPacket.targetDistance = targetDistance;
    sendPacket.encountable = true;
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_NTF_SHOW_EMOTICON_INSTANT, async (ntf, res) => {
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(ntf.userId);
    if (!fleet) {
        mlog_1.default.warn('[TCP] emoticon-instant no agent in ocean zone', {
            userId: ntf.userId,
        });
        return;
    }
    const oceanZone = fleet.getCurrentZone();
    if (!oceanZone) {
        mlog_1.default.warn('[TCP] User emoticon-instant when not in a zone!', {
            userId: ntf.userId,
        });
        return;
    }
    oceanZone.broadcastShowEmoticonInstant(fleet, ntf.emoticonCmsId);
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerInteraction.js.map