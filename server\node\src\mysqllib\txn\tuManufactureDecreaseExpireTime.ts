// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import { withTxn } from '../mysqlUtil';
import puPointUpdate from '../sp/puPointUpdate';
import puItemsBatchUpdate from '../sp/puItemsBatchUpdate';
import { PointChange } from '../../lobbyd/userPoints';
import { ItemChange } from '../../lobbyd/userInven';
import { MError, MErrorCode } from '../../motiflib/merror';
import puManufactureProgressUpdate from '../sp/puManufactureProgressUpdate';
import puPointsBatchUpdate from '../sp/puPointsBatchUpdate';
import { ManufactureProgress } from '../../lobbyd/userManufacture';

function queryImpl(
  connection: PoolConnection,
  userId: number,
  manufactureProgress: ManufactureProgress | null,
  roomCmsId: number | null,
  slot: number | null,
  pointChanges: PointChange[],
  userItemChanges: ItemChange[]
) {
  return Promise.resolve()
    .then(() => {
      if (pointChanges && pointChanges.length > 0) {
        return puPointsBatchUpdate(connection, userId, pointChanges);
      }
      return;
    })
    .then(() => {
      if (userItemChanges && userItemChanges.length > 0) {
        return puItemsBatchUpdate(connection, userId, userItemChanges);
      }
      return;
    })

    .then(() => {
      if (manufactureProgress && roomCmsId !== null && slot !== null) {
        const slotData = manufactureProgress[slot];
        if (slotData) {
          return puManufactureProgressUpdate(
            connection,
            userId,
            roomCmsId,
            slot,
            slotData
          );
        }
      }
    })

    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.MANUFACTURE_UPDATE_TXN_ERROR);
      }
    });
}

export default function (
  dbConnPool: Pool,
  userId: number,
  manufactureProgress: ManufactureProgress | null,
  roomCmsId: number | null,
  slot: number | null,
  pointChanges: PointChange[],
  userItemChanges: ItemChange[]
) {
  return withTxn(dbConnPool, __filename, (connection: PoolConnection) => {
    return queryImpl(
      connection,
      userId,
      manufactureProgress,
      roomCmsId,
      slot,
      pointChanges,
      userItemChanges
    );
  });
} 