import { User } from "../user";
import { BuilderMailCreateParams } from "../../motiflib/mailBuilder";
import * as mutil from "../../motiflib/mutil";
import tuDevAddDirectMail from "../../mysqllib/txn/tuDevAddDirectMail";
import { Container } from "typedi/Container";
import { LobbyService } from "../server";
import { UserManager } from "../userManager";
import * as proto from '../../proto/lobby/proto';
import mconf from "../../motiflib/mconf";
import path from 'path';
import fs from 'fs';
import JSON5 from 'json5';
import mlog from "../../motiflib/mlog";
import axios from 'axios';

const SURVEY_MAIL_ID = 92999000;

type SurveyCondition = {
  level?: number;
  daysSinceSignup?: number;
};

type SurveyReward = {
  type: string;
  cmsId: number;
  value: number;
};

type SurveyItem = {
  surveyId: string;
  title: string;
  body: string;
  conditions: SurveyCondition[];
  rewards: SurveyReward[];
};

type SurveyDefinitions = {
  baseSurveyUrl: string;
  baseJoinedUrl: string;
  linkCaption: string;
  rewardMail: {
    title: string;
    body: string;
  };
  surveys: SurveyItem[];
};

export class Survey {
  static surveyDefs: SurveyDefinitions | null;

  static loadSurveryDefinitions() {
    const filename = path.resolve(path.join(__dirname, '..', '..', '..', 'config', 'survey.json5'));

    try {
      const json = fs.readFileSync(filename, 'utf8');
      this.surveyDefs = JSON5.parse(json);
    } catch (error) {
      mlog.error('Failed to load survey definitions!', {
        filename,
        errMsg: error.message,
      });
    }
  }

  static conditionalRequestSurvey(user: User) {
    if (!this.surveyDefs) {
      Survey.loadSurveryDefinitions();
    }

    if (!this.surveyDefs) {
      return;
    }

    const now = mutil.curTimeUtc();
    const signupTimeUtc = user.createTimeUtc;
    const daysSinceSignup = Math.floor((now - signupTimeUtc) / 86400);

    for (const survey of this.surveyDefs.surveys) {
      let isConditionMet = true;

      for (const condition of survey.conditions) {
        if (condition.level && user.level < condition.level) {
          isConditionMet = false;
          break;
        }
        if (condition.daysSinceSignup && daysSinceSignup < condition.daysSinceSignup) {
          isConditionMet = false;
          break;
        }
      }

      if (isConditionMet) {
        this.requestServey(survey, user);
        break;
      }
    }
  }

  static async requestServey(survey: SurveyItem, user: User) {
    // TODO 이미 요청한 경우에는 하지 말아야함.

    const baseSurveyUrl = Survey.surveyDefs.baseSurveyUrl;
    const surveyId = survey.surveyId;
    const roleId = user.pubId;
    const gpId = user.accountId;
    const level = user.level;
    const areaGroup = mconf.sdo.areaId;
    const channel = user.channel;

    const surveyUrl = `${baseSurveyUrl}/${surveyId}?roleId=${roleId}&gpId=${gpId}&level=${level}&areaGroup=${areaGroup}&channel=${channel}`;

    await Survey.sendMail(SURVEY_MAIL_ID,
      survey.title,
      survey.body + '\n' + `<hyperlink color="#1717daff" action="${surveyUrl}">${Survey.surveyDefs.linkCaption}</>`,
      null,
      user);
  }

  static async completeSurvey(surveyId: string, user: User) {
    if (!this.surveyDefs) {
      return;
    }

    const survey = this.surveyDefs.surveys.find((survey) => survey.surveyId === surveyId);
    if (!survey) {
      return;
    }

    const baseJoinedUrl = Survey.surveyDefs.baseJoinedUrl;

    // const ptId = user.accountId;
    const roleId = user.pubId;
    const level = user.level;
    const areaGroup = mconf.sdo.areaId;
    const channel = user.channel;
    const key = '123';

    // const joinedUrl = `${baseJoinedUrl}/${surveyId}?roleId=${roleId}&ptId=${ptId}&level=${level}&areaGroup=${areaGroup}&channel=${channel}`;
    const joinedUrl = `${baseJoinedUrl}/${surveyId}?roleId=${roleId}&level=${level}&areaGroup=${areaGroup}&channel=${channel}`;

    const timestamp = Math.floor(mutil.getLocalSecs());
    const sign = Survey.md5(`key=${key}&surveyId=${surveyId}&roleId=${roleId}&timestamp=${timestamp}`);

    const joinedUrlWithSign = `${joinedUrl}&timestamp=${timestamp}&sign=${sign}`;
    const response = await axios.get(joinedUrlWithSign);
    const json = response.data;
    if (json.code !== 0) {
      mlog.error('Failed to complete survey!', {
        userId: user.userId,
        surveyId,
        code: json.code,
        message: json.message,
      });
      return;
    } else {
      const attachments = survey.rewards ? JSON.stringify(survey.rewards.map((reward) => ({
        Type: reward.type,
        Id: reward.cmsId,
        Quantity: reward.value,
      }))) : null;

      await Survey.sendMail(SURVEY_MAIL_ID,
        Survey.surveyDefs.rewardMail.title,
        Survey.surveyDefs.rewardMail.body,
        attachments,
        user);

      mlog.info('Survey completed!', {
        userId: user.userId,
        surveyId,
      });
    }
  }

  private static md5(input: string): string {
    const crypto = require('node:crypto');
    return crypto.createHash('md5').update(input).digest('hex');
  }

  private static async sendMail(mailCmsId: number, title: string, body: string, attachments: string | null, user: User) {
    const now = mutil.curTimeUtc();
    const expireTimeUtc = null;
    const newMail = new BuilderMailCreateParams(
      user.userMails.generateNewDirectMailId(),
      mailCmsId,
      now,
      expireTimeUtc,
      0,
      title,
      null,
      body,
      null,
      attachments
    ).getParam();

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    // TODO: 개발용 기능인것 같은데, 변경해야할까?
    await tuDevAddDirectMail(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        newMail);

    user.userMails.addDirectMail(newMail, null);

    const userManager = Container.get(UserManager);
    await userManager.sendJsonPacketToUser(user.userId, proto.Common.NEW_MAIL_SC, {
        sync: {
            add: {
                userDirectMails: {
                    [newMail.id]: user.userMails.getDirectMailSyncData(newMail.id),
                },
            },
        }
    });
  }
}
