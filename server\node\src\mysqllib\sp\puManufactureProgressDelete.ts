// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MErrorCode, MError } from '../../motiflib/merror';

export const spName = 'mp_u_manufacture_progress_delete';
export const errorCode = MErrorCode.MANUFACTURE_PROGRESS_DELETE_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  roomCmsId: number,
  slot: number
): Promise<void> {
  return spFunction(connection, userId, roomCmsId, slot)
    .then((qr) => {
      if (qr.rows[0][0]['affectedRows'] === 0) {
        throw new MError(spName + ' is failed', errorCode, {
          userId,
          roomCmsId,
          slot,
        });
      }
      return;
    })
    .catch(catchHandler);
}