// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import mhttp from '../../../motiflib/mhttp';
import { Container } from 'typedi/Container';
import { LobbyService } from '../../server';
import { MErrorCode } from '../../../motiflib/merrorCode';
import { MError } from '../../../motiflib/merror';
import * as mutilLanguage from '../../../motiflib/mutilLanguage';
import cms from '../../../cms';
// ----------------------------------------------------------------------------
interface RequestBody {
  name: string;
}

/**
 * 길드명 중복 확인
 */
// ----------------------------------------------------------------------------
export class Cph_Guild_CheckForDuplicatesName implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;
    const { name } = reqBody;
    const { guildRedis, userDbConnPoolMgr } = Container.get(LobbyService);

    mutilLanguage.ensureValidName(
      null,
      name,
      cms.Const.GuildNameMinimum.value,
      cms.Const.GuildNameMaximum.value
    );

    return mhttp.lgd
      .hasBadWord(name)
      .then((bHas) => {
        // 2. 길드명 금칙어 체크
        if (bHas) {
          throw new MError('has-bad-word', MErrorCode.GUILD_NAME_HAS_BAD_WORD, {
            userId: user.userId,
            name,
          });
        }
        return guildRedis['getGuildIdByName'](name);
      })

      .then((guildId: number) => {
        return user.sendJsonPacket(packet.seqNum, packet.type, { bHas: guildId ? true : false });
      });
  }
}
