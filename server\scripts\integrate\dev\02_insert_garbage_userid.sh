#!/bin/bash

CWD="$(dirname "$0")"

if [ ! -f $CWD/_config.sh ]; then
	echo "Please create a custom '_config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/_query.sh ]; then
	echo "Please create a custom '_query.sh' file at '$CWD' directory."
	exit 1
fi

if [ -z "$1" ]
then
    echo "No argument shard length"
    exit 1
fi

if [ -z "$2" ]
then
    echo "No argument current shard number"
    exit 1
fi

if [ -z "$3" ]
then
    echo "No argument backup database name"
    exit 1
fi

SECONDS=0

source $CWD/_config.sh
source $CWD/_query.sh

SHARD_LENGTH=$1
SHARD_NUM=$2
BACKUP_DB_NAME=$3
main() 
{
  echo "===== CREATE TABLE integration_garbage_users"
  q="
    DROP TABLE IF EXISTS integration_garbage_users;

    CREATE TABLE integration_garbage_users (
      userId int NOT NULL,
      PRIMARY KEY (userId)
    ) 
    ENGINE=InnoDB DEFAULT 
    CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"


  echo "===== FIND u_users garbage userId"
  q="
    SELECT u_users.id
      FROM u_users
      LEFT JOIN integration_auth_users
    ON u_users.id = integration_auth_users.userId
    WHERE integration_auth_users.userId IS NULL;
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
  userIds=()
  userIds+=(${QUERY_RESULT[@]})

  q="
    SELECT id
      FROM u_users
    WHERE MOD(id, ${SHARD_LENGTH}) != ${SHARD_NUM};
    "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
  userIds+=(${QUERY_RESULT[@]})


  echo "===== SHOW TABLES"
  q="SHOW TABLES;"
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"


  echo "===== FIND u_* tables garbage userId"
  tableList=(${QUERY_RESULT})
  for tableName in ${tableList[@]}
  do
    if [[ "$tableName" == "u_"* &&  "$tableName" != "u_users" ]]
    then
      q="
        SELECT DISTINCT ${tableName}.userId
          FROM ${tableName}
          LEFT JOIN u_users
        ON ${tableName}.userId = u_users.id
        WHERE u_users.id IS NULL;
      "
      queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
      userIds+=(${QUERY_RESULT[@]})
    fi
  done


  let multipleUserIds
  for userId in ${userIds[@]}
  do
    multipleUserIds+=\($userId\)\,
  done

  echo "===== INSERT integration_garbage_users.userId"
  q="
    INSERT INTO integration_garbage_users(userId)
      VALUES ${multipleUserIds:0:-1}
      ON DUPLICATE KEY UPDATE
    userId = values(userId);
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
}


main "$@"; 

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
exit
