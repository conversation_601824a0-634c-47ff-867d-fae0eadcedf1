#!/bin/bash

SECONDS=0

echo "===== copy kr01_uwo_00 uwo_user_00"
./copy_user_db.sh kr01_uwo_00 uwo_user_00
echo "===== copy kr01_uwo_01 uwo_user_01"
./copy_user_db.sh kr01_uwo_01 uwo_user_01
echo "===== copy kr01_uwo_02 uwo_user_02"
./copy_user_db.sh kr01_uwo_02 uwo_user_02
echo "===== copy kr01_uwo_03 uwo_user_03"
./copy_user_db.sh kr01_uwo_03 uwo_user_03


echo "===== copy kr02_uwo_00 uwo_user_00"
./copy_user_db.sh kr02_uwo_00 uwo_user_00
echo "===== copy kr02_uwo_01 uwo_user_01"
./copy_user_db.sh kr02_uwo_01 uwo_user_01
echo "===== copy kr02_uwo_02 uwo_user_02"
./copy_user_db.sh kr02_uwo_02 uwo_user_02
echo "===== copy kr02_uwo_03 uwo_user_03"
./copy_user_db.sh kr02_uwo_03 uwo_user_03


echo "===== copy kr04_uwo_00 uwo_user_00"
./copy_user_db.sh kr04_uwo_00 uwo_user_00
echo "===== copy kr04_uwo_01 uwo_user_01"
./copy_user_db.sh kr04_uwo_01 uwo_user_01
echo "===== copy kr04_uwo_02 uwo_user_02"
./copy_user_db.sh kr04_uwo_02 uwo_user_02
echo "===== copy kr04_uwo_03 uwo_user_03"
./copy_user_db.sh kr04_uwo_03 uwo_user_03

echo "===== copy FINISH"

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
