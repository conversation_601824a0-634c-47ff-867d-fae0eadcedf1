// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import assert from 'assert';
import _ from 'lodash';
import Container from 'typedi';

import mlog from '../../../motiflib/mlog';
import mhttp from '../../../motiflib/mhttp';
import * as mutil from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { All, Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import {
  LGBillingResponseBody,
  LineGamesBillingApiClient,
} from '../../../motiflib/mhttp/linegamesBillingApiClient';
import puCashShopRestrictedProductUpdate from '../../../mysqllib/sp/puCashShopRestrictedProductUpdate';
import { LobbyService } from '../../server';
import {
  BillingUtil,
  ConsecutiveProduct,
  HotSpotProduct,
  RestrictedProduct,
  UNBUYABLE_REASON,
} from '../../userCashShop';
import * as cmsEx from '../../../cms/ex';
import {
  CASH_SHOP_PRODUCT_TYPE,
  CASH_SHOP_SALE_POINT_TYPE,
  CASH_SHOP_SALE_TYPE,
  getConsecutiveProductCodeByStoreCode,
  isConsecutiveProductCode,
} from '../../../cms/cashShopDesc';
import cms from '../../../cms';
import { SECONDS_PER_DAY, SECONDS_PER_HOUR } from '../../../formula';
import tuHotSpotUpdate from '../../../mysqllib/txn/tuHotSpotUpdate';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { sync } from 'mkdirp';
import tuUpdateBillingProducts from '../../../mysqllib/txn/tuUpdateBillingProducts';

// ----------------------------------------------------------------------------
// 빌링 서버에 예약된 구매건을 완료 요청
// ----------------------------------------------------------------------------

const rsn = 'billing_complete_reserved_purchase';
const add_rsn = null;

interface RequestBody {
  orderId: string;
  receipt: string;
  ignoreReceiptYn?: string;
  price: number;
  microPrice: number;
  currency: string;
  memo: string | undefined;
  cmsId: number;
  floorOrderId: string;
  floorStoreOrderId: string;
  productId: string;

  googleSignature?: string; // GOOGLE
  signature?: string; // FLOOR

  transactionId?: string;

  // glog 를 위함
  productName: string;
}

interface ResponseBody extends Resp {
  billingApiResp: unknown; // 빌링 서버 API 의 response
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingCompleteReservedPurchase implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const requestBody: RequestBody = packet.bodyObj;
    const respBody: ResponseBody = { billingApiResp: undefined, sync: {} };
    const { orderId, cmsId, floorOrderId, productId, floorStoreOrderId } = requestBody;
    const { userDbConnPoolMgr } = Container.get(LobbyService);

    if (!LineGamesBillingApiClient.isCompleteReservedPurchaseImplemented(user.storeCode)) {
      // 이 부분이 필요할지 모르겠음.
      throw new MError('invalid-store-code', MErrorCode.INTERNAL_ERROR, {
        storeCode: user.storeCode,
      });
    }

    const signature =
      user.storeCode === 'GOOGLE_PLAY'
        ? requestBody.googleSignature ?? null
        : requestBody.signature ?? null;

    /**
     * 구매 가능한지 검사
     * https://jira.line.games/browse/QAUWO-10905
     */
    const cashShopCms = cms.CashShop[cmsId];
    if (!cashShopCms) {
      throw new MError(
        'failed-to-get-cash-shop-cms',
        MErrorCode.INVALID_REQ_BODY_BILLING_COMPLETE_RESERVE_PURCHASE,
        { requestBody }
      );
    }
    const curTimeUtc = mutil.curTimeUtc();
    if (floorOrderId) {
      const reason = user.userCashShop.isBuyableProduct(
        user,
        cmsId,
        null,
        user.userCashShop.getExpiredRestrictedProducts(curTimeUtc),
        1
      );
      if (
        reason === UNBUYABLE_REASON.SOLD_OUT ||
        reason === UNBUYABLE_REASON.ALREADY_BOUGHT_SOUND ||
        reason === UNBUYABLE_REASON.ALREADY_BOUGHT_EVENT_PAGE_PRODUCT ||
        reason === UNBUYABLE_REASON.ALREADY_BOUGHT_MATE ||
        reason === UNBUYABLE_REASON.ALREADY_BOUGHT_PET
      ) {
        return mhttp.lgpayd
          .cancelFloorReserve(
            user.storeCode,
            floorOrderId,
            floorStoreOrderId,
            orderId,
            productId,
            user.userId
          )
          .catch((err) => {
            throw new MError('billing-cancel-is-failed-2', MErrorCode.BILLING_CANCEL_IS_FAILED, {
              reason,
              restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
              err: err.message,
            });
          })
          .then((ret) => {
            if (ret.errorCd) {
              throw new MError('billing-cancel-is-failed-1', MErrorCode.BILLING_CANCEL_IS_FAILED, {
                reason,
                restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
                ret,
              });
            } else {
              throw new MError(
                'can-not-buy-cash-shop-biiling-product',
                MErrorCode.BILLING_UNBUYABLE_PRODUCT,
                {
                  reason,
                  restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
                }
              );
            }
          });
      }
    }

    // 핫스팟은 쿨타임 및 상품 등장 관련 여러 문제로 보관함에서 받았을때가 아닌 구매했을때 바로 쿨타임이 시작하도록 함
    let restrictedProductChangeForGLog: RestrictedProduct;
    let hotSpotProduct: HotSpotProduct;
    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.HOT_SPOT) {
      hotSpotProduct = _.cloneDeep(user.userCashShop.getHotSpotProduct(cashShopCms.id));
      if (hotSpotProduct) {
        hotSpotProduct.expireTimeUtc = curTimeUtc;
        hotSpotProduct.coolTimeUtc = curTimeUtc + cashShopCms.coolTimeDays * SECONDS_PER_DAY;
      }
    }

    let consecutiveProduct: ConsecutiveProduct;
    if (getConsecutiveProductCodeByStoreCode(cashShopCms, user.storeCode)) {
      const product = user.userCashShop.getConsecutiveProduct(cashShopCms.id);
      const normalSaleSeconds = cashShopCms.normalSaleHours * SECONDS_PER_HOUR;
      const discountSaleSeconds = cashShopCms.discountSaleHours * SECONDS_PER_HOUR;

      if (!product || curTimeUtc > product.discountSaleTimeUtc) {
        consecutiveProduct = {
          cmsId: cashShopCms.id,
          normalSaleTimeUtc: curTimeUtc + normalSaleSeconds,
          discountSaleTimeUtc: curTimeUtc + normalSaleSeconds + discountSaleSeconds,
        };
      } else {
        consecutiveProduct = _.cloneDeep(product);
        const normalSaleTimeUtc =
          curTimeUtc + (product.discountSaleTimeUtc - curTimeUtc) + normalSaleSeconds;
        consecutiveProduct.normalSaleTimeUtc = normalSaleTimeUtc;
        consecutiveProduct.discountSaleTimeUtc = normalSaleTimeUtc + discountSaleSeconds;
      }
    }

    return Promise.resolve()
      .then(() => {
        return _completeReservedPurchaseByStoreCode(user, requestBody);
      })
      .then(async (billingApiResp) => {
        respBody.billingApiResp = billingApiResp;

        if (billingApiResp.success === true) {
          // 빌링 API 에서 인자들의 타입(RequestBody) 및 유효성이 검증됐다고 가정.

          const productId = (billingApiResp.data as any)?.productId ?? null;

          user.glog('common_iap', {
            pn: productId,
            spn: requestBody.productName ?? null,
            pr: Number(requestBody.price),
            currency: requestBody.currency,
            os: user.deviceType?.toUpperCase() ?? null,
            osv: user.osv ?? null,
            lang: user.deviceLang ?? null,
            lang_game: user.lineLangCultre ?? null,
            sk: user.storeCode,
            country_ip: user.countryIp ?? null,
            receipt: requestBody.receipt,
            sig: signature,
            order_id: requestBody.orderId,
            billingOrderId: requestBody.orderId,
            adjust_id: user.adjustId ?? null,
            gps_adid: user.gpsAdid ?? null,
            idfa: user.idfa ?? null,
            idfv: user.idfv ?? null,
          });
          user.glog('common_purchase_box', {
            rsn,
            add_rsn,
            flag: 1, // 1:추가 / 2:삭제
            type: CASH_SHOP_SALE_POINT_TYPE.CASH, // iap 상품
            product_id: productId,
            product_name: requestBody.productName ?? null,
            order_id: requestBody.orderId,
            inven_id: (billingApiResp.data as any)?.invenId ?? null,
          });

          // 빌링 보관함에 자동으로 들어간다!

          // 횟수 제한 상품 처리.
          // 폴리싱(?) 필요. 실패하는 경우에 대한 방안을 고려 해봐야 함.
          try {
            const result: { sync: Sync; restrictedProductChange: RestrictedProduct } =
              await _applyCashShopRestrictedProductIfNeeded(user, productId, curTimeUtc);

            if (!result) {
              return;
            }

            respBody.sync = result.sync;
            restrictedProductChangeForGLog = result.restrictedProductChange;
          } catch (err) {
            let e: unknown;
            if (err instanceof MError) {
              e = { mcode: err.mcode, message: err.message, extra: err.extra, stack: err.stack };
            } else if (err instanceof Error) {
              e = { message: err.message, stack: err.stack };
            } else {
              e = err;
            }
            mlog.error(`[${rsn}] restictedProductChange-apply-failed`, {
              userId: user.userId,
              requestBody,
              billingApiResp,
              error: e,
            });
          }
        } else {
          mlog.warn(`[${rsn}] billing-api-failed`, {
            userId: user.userId,
            requestBody,
            billingApiResp,
          });
        }
      })
      .then(() => {
        return tuUpdateBillingProducts(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          hotSpotProduct,
          consecutiveProduct
        ).then(() => {
          if (hotSpotProduct) {
            user.userCashShop.setHotSpotProduct(hotSpotProduct);
            _.merge(respBody.sync, {
              add: { hotSpotProducts: { [hotSpotProduct.cmsId]: hotSpotProduct } },
            });

            let remain_cnt = null;
            if (restrictedProductChangeForGLog) {
              remain_cnt = cashShopCms.saleTypeVal - restrictedProductChangeForGLog.amount;
            }
            user.glog('hot_spot_product_start', {
              rsn,
              add_rsn,
              id: cmsId,
              name: displayNameUtil.getCashShopProductDisplayName(cashShopCms),
              limit_type: cashShopCms.saleType,
              remain_cnt:
                cashShopCms.saleType === CASH_SHOP_SALE_TYPE.UNLIMITED
                  ? 999
                  : remain_cnt ?? cashShopCms.saleTypeVal,
              amt: 1,
              pr_data: [],
              reward_data: cmsEx.convertRewardFixedToGLogRewardData(
                cashShopCms.productRewardFixedId
              ),
              is_exposure: false,
            });
          }

          if (consecutiveProduct) {
            user.userCashShop.setConsecutiveProduct(consecutiveProduct);
            _.merge<Sync, Sync>(respBody.sync, {
              add: {
                cashShopConsecutiveProducts: { [consecutiveProduct.cmsId]: consecutiveProduct },
              },
            });
          }
        });
      })
      .then(() => {
        const accums = [
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.CASH_SHOP_BUY_COUNT,
            targets: [cashShopCms.id],
            addedValue: 1,
          },
        ];

        return user.userAchievement.accumulate(accums, user, respBody.sync, { user, rsn, add_rsn });
      })
      .then(() => {
        mlog.info('billingCompleteReservedPurchase is done.', {
          userId: user.userId,
          customInt1: cmsId,
        });
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, respBody);
      });
  }
}

async function _completeReservedPurchaseByStoreCode(
  user: User,
  reqBody: Readonly<RequestBody>
): Promise<LGBillingResponseBody> {
  // linegamesBillingApiClient.ts, CompleteReservedPurchaseReqBody.cfViewerCountry 참고
  const cfViewerCountry: string = user.countryIp ?? '';

  switch (user.storeCode) {
    case 'GOOGLE_PLAY': {
      return mhttp.lgbillingd.completeReservedPurchaseGoogle(
        user.accountId,
        user.userId.toString(),
        user.storeCode,
        cfViewerCountry,

        user.idfa,
        user.idfv,
        user.adjustId,
        user.udid,
        user.gpsAdid,

        undefined, //* 필요시 구현 필요. ( 클라에서 보내주는 작업 등 )

        reqBody.orderId,
        reqBody.receipt,
        reqBody.ignoreReceiptYn as any,
        reqBody.price,
        reqBody.microPrice,
        reqBody.currency,
        reqBody.memo,

        reqBody.googleSignature
      );
    }

    case 'APPLE_APP_STORE': {
      return mhttp.lgbillingd.completeReservedPurchaseApple(
        user.accountId,
        user.userId.toString(),
        user.storeCode,
        cfViewerCountry,

        user.idfa,
        user.idfv,
        user.adjustId,
        user.udid,
        user.gpsAdid,

        reqBody.orderId,
        reqBody.receipt,
        reqBody.transactionId,
        reqBody.ignoreReceiptYn as any,
        reqBody.price,
        reqBody.microPrice,
        reqBody.currency,
        reqBody.memo
      );
    }

    case 'FLOOR_STORE': {
      return mhttp.lgbillingd.completeReservedPurchaseFloor(
        user.accountId,
        user.userId.toString(),
        user.storeCode,
        cfViewerCountry,

        user.idfa,
        user.idfv,
        user.adjustId,
        user.udid,
        user.gpsAdid,

        reqBody.orderId,
        reqBody.receipt,
        reqBody.ignoreReceiptYn as any,
        reqBody.price,
        reqBody.microPrice,
        reqBody.currency,
        reqBody.memo,

        reqBody.signature
      );
    }

    case 'STEAM': {
      return mhttp.lgbillingd.completeReservedPurchaseSteam(
        user.accountId,
        user.userId.toString(),
        user.storeCode,
        cfViewerCountry,

        user.idfa,
        user.idfv,
        user.adjustId,
        user.udid,
        user.gpsAdid,

        reqBody.orderId,
        reqBody.receipt,
        reqBody.ignoreReceiptYn as any,
        reqBody.price,
        reqBody.microPrice,
        reqBody.currency,
        reqBody.memo
      );
    }

    default:
      assert.fail(`invalid-store-code: ${user.storeCode}`);
  }
}

async function _applyCashShopRestrictedProductIfNeeded(
  user: User,
  productId: string,
  timeUtc: number
): Promise<{ sync: Sync; restrictedProductChange: RestrictedProduct }> | undefined {
  const cashShopCms = cmsEx.getCashShopCmsByProductCode(productId);
  if (!cashShopCms) {
    throw new MError(
      'failed-to-get-cash-shop-cms',
      MErrorCode.INVALID_REQ_BODY_BILLING_COMPLETE_RESERVE_PURCHASE,
      { productId }
    );
  }
  const restrictedProductChange = BillingUtil.buildRestrictedProductChange(
    user,
    cashShopCms,
    timeUtc
  );
  if (!restrictedProductChange) {
    return undefined;
  }

  const { userDbConnPoolMgr } = Container.get(LobbyService);
  return puCashShopRestrictedProductUpdate(
    userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
    user.userId,
    restrictedProductChange
  ).then(() => {
    user.userCashShop.setRestrictedProduct(restrictedProductChange);
    const sync: Sync = {
      add: {
        cashShopRestrictedProducts: {
          [restrictedProductChange.cmsId]: restrictedProductChange,
        },
      },
    };

    return { sync, restrictedProductChange };
  });
}
