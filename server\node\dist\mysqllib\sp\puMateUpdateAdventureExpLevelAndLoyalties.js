"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorCode = void 0;
const query = __importStar(require("../query"));
const lodash_1 = __importDefault(require("lodash"));
const merror_1 = require("../../motiflib/merror");
exports.errorCode = merror_1.MErrorCode.MATE_UPDATE_EXP_LEVEL_LOYALTY_QUERY_ERROR;
const catchHandler = query.generateMErrorRejection(exports.errorCode);
function makePromise(connection, userId, changes, spName, count) {
    const mateCmsIds = [];
    const adventureExps = [];
    const adventureLevels = [];
    const loyalties = [];
    for (let i = 0; i < count; i++) {
        const change = changes.shift();
        mateCmsIds.push(change.cmsId);
        adventureExps.push(change.adventureExp);
        adventureLevels.push(change.adventureLevel);
        loyalties.push(change.loyalty);
    }
    const spFunction = query.generateSPFunction(spName + count);
    return spFunction(connection, userId, ...mateCmsIds, ...adventureExps, ...adventureLevels, ...loyalties);
}
async function default_1(connection, userId, inChanges) {
    const changes = lodash_1.default.cloneDeep(inChanges);
    const spName = 'mp_u_mate_update_adventure_exp_level_and_loyalty_';
    const promises = [];
    while (changes.length > 0) {
        if (changes.length >= 64) {
            promises.push(makePromise(connection, userId, changes, spName, 64));
        }
        else if (changes.length >= 32) {
            promises.push(makePromise(connection, userId, changes, spName, 32));
        }
        else if (changes.length >= 16) {
            promises.push(makePromise(connection, userId, changes, spName, 16));
        }
        else if (changes.length >= 8) {
            promises.push(makePromise(connection, userId, changes, spName, 8));
        }
        else if (changes.length >= 4) {
            promises.push(makePromise(connection, userId, changes, spName, 4));
        }
        else if (changes.length >= 2) {
            promises.push(makePromise(connection, userId, changes, spName, 2));
        }
        else if (changes.length >= 1) {
            promises.push(makePromise(connection, userId, changes, spName, 1));
        }
    }
    return Promise.all(promises)
        .then(() => {
        return;
    })
        .catch(catchHandler);
}
exports.default = default_1;
//# sourceMappingURL=puMateUpdateAdventureExpLevelAndLoyalties.js.map