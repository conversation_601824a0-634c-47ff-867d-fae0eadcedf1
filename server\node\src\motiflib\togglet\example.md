# ToggletClient 사용 예제

이 문서는 ToggletClient를 다양한 상황에서 어떻게 사용할 수 있는지 보여주는 예제들을 제공합니다.

## 기본 설정 및 초기화

```typescript
import ToggletClient from './toggletClient';
import { Context } from 'unleash-client';

// 클라이언트 인스턴스 생성
const togglet = new ToggletClient({
  apiUrl: 'https://us.app.unleash-hosted.com/usii0012/api/',
  accessToken: '*:development.8d662424920812bad929a7f778d607a00779c75a2e8a25575541d5f3',
  appName: 'my-application',
  refreshInterval: 10_000, // 10초마다 업데이트
  defaultContext: {
    userId: 'system',
    properties: {
      environment: 'development'
    }
  }
});

// 비동기 초기화
async function initializeApp() {
  await togglet.init();
  console.log('Feature toggle system initialized');
  
  // 앱 시작 로직...
}

// 앱 종료 시 리소스 정리
function shutdownApp() {
  togglet.destroy();
  console.log('Feature toggle system shut down');
  
  // 다른 종료 로직...
}

initializeApp().catch(console.error);
```

## 사용자 컨텍스트 생성 및 사용

```typescript
// 사용자 정보에서 컨텍스트 생성
function createUserContext(user): Context {
  return {
    userId: user.id,
    sessionId: user.sessionId,
    remoteAddress: user.ipAddress,
    properties: {
      country: user.country,
      language: user.language,
      appVersion: '1.2.3',
      platform: user.platform,
      deviceType: user.deviceType
    }
  };
}

// API 요청 처리 함수
async function handleApiRequest(req, res) {
  const user = req.user;
  const context = createUserContext(user);
  
  // 기능 플래그 확인
  if (togglet.isEnabled('new-api-feature', context)) {
    // 새 기능 활성화 로직
    return res.json(await processWithNewFeature(req.body));
  } else {
    // 기존 로직
    return res.json(await processWithOldFeature(req.body));
  }
}
```

## 다양한 변형(Variant) 사용

```typescript
// 사용자에게 표시할 UI 버전 결정
function determineUiVersion(user) {
  const context = createUserContext(user);
  const toggle = togglet.getToggle('ui-experiment', context);
  
  if (!toggle.isEnabled()) {
    return 'default';
  }
  
  // 변형 이름 가져오기
  const variant = toggle.getVariantName();
  
  switch (variant) {
    case 'blue-theme':
      return 'blue';
    case 'dark-theme':
      return 'dark';
    case 'light-theme':
      return 'light';
    default:
      return 'default';
  }
}

// 사용자 설정 가져오기
function getUserSettings(user) {
  const context = createUserContext(user);
  
  return {
    theme: determineUiVersion(user),
    itemsPerPage: togglet.numberVariation('items-per-page', context, 10),
    enableNotifications: togglet.boolVariation('enable-notifications', context, true),
    welcomeMessage: togglet.stringVariation('welcome-message', context, 'Welcome to our app!'),
    featureConfig: togglet.jsonVariation('feature-config', context, { enabled: false, options: [] })
  };
}
```

## 스토어 코드에 따른 다른 처리

```typescript
// 스토어 코드에 따라 다른 기능 활성화
function isFeatureEnabledForStore(featureName, user, storeCode) {
  const context = {
    ...createUserContext(user),
    properties: {
      ...createUserContext(user).properties,
      storeCode: storeCode
    }
  };
  
  return togglet.isEnabled(featureName, context);
}

// 스토어별 결제 처리
function processPayment(user, amount, storeCode) {
  // 스토어 코드에 따른 컨텍스트 생성
  const context = {
    userId: user.id,
    properties: {
      storeCode: storeCode,
      paymentAmount: amount,
      country: user.country
    }
  };
  
  // 새 결제 시스템 사용 여부 확인
  if (togglet.isEnabled('new-payment-system', context)) {
    console.log(`Using new payment system for store: ${storeCode}`);
    return processWithNewPaymentSystem(user, amount, storeCode);
  } else {
    console.log(`Using legacy payment system for store: ${storeCode}`);
    return processWithLegacyPaymentSystem(user, amount, storeCode);
  }
}

// 스토어별 할인 정책 적용
function applyDiscount(user, product, storeCode) {
  const context = {
    userId: user.id,
    properties: {
      storeCode: storeCode,
      productId: product.id,
      productCategory: product.category
    }
  };
  
  // 할인 정책 변형 가져오기
  const discountPolicy = togglet.variation('discount-policy', context, 'standard');
  
  switch (discountPolicy) {
    case 'premium':
      return applyPremiumDiscount(product);
    case 'seasonal':
      return applySeasonalDiscount(product);
    case 'first-purchase':
      return applyFirstPurchaseDiscount(user, product);
    default:
      return applyStandardDiscount(product);
  }
}
```

## 점진적 롤아웃 구현

```typescript
// 새 기능의 점진적 롤아웃
function isNewFeatureEnabledForUser(user) {
  const context = createUserContext(user);
  
  // userId를 기반으로 일관된 롤아웃 결정
  return togglet.isEnabled('new-feature-rollout', context, false);
}

// 사용자 그룹별 기능 활성화
function isFeatureEnabledForUserGroup(featureName, user) {
  const context = createUserContext(user);
  
  // 사용자 그룹 정보 추가
  context.properties.userGroup = user.group;
  context.properties.userTier = user.tier;
  
  return togglet.isEnabled(featureName, context, false);
}

// 지역별 기능 활성화
function isFeatureEnabledForRegion(featureName, user) {
  const context = createUserContext(user);
  
  // 지역 정보 강조
  context.properties.region = user.region;
  context.properties.country = user.country;
  context.properties.timezone = user.timezone;
  
  return togglet.isEnabled(featureName, context, false);
}
```

## 고급 사용 사례

### 기능 플래그 상태 캐싱

```typescript
// 성능 최적화를 위한 캐싱 (주의: 상황에 따라 적절히 사용)
const featureCache = new Map();
const CACHE_TTL = 60_000; // 1분 캐시

function isFeatureEnabledWithCache(featureName, user) {
  const userId = user.id;
  const cacheKey = `${featureName}:${userId}`;
  
  // 캐시 확인
  const cachedItem = featureCache.get(cacheKey);
  if (cachedItem && Date.now() - cachedItem.timestamp < CACHE_TTL) {
    return cachedItem.enabled;
  }
  
  // 캐시 미스: 실제 확인
  const context = createUserContext(user);
  const enabled = togglet.isEnabled(featureName, context);
  
  // 결과 캐싱
  featureCache.set(cacheKey, {
    enabled,
    timestamp: Date.now()
  });
  
  return enabled;
}
```

### 여러 토글의 조합 사용

```typescript
// 여러 토글을 조합하여 복잡한 기능 결정
function determineUserExperience(user) {
  const context = createUserContext(user);
  
  // 기본 기능 세트 확인
  const hasNewUi = togglet.isEnabled('new-ui', context);
  const hasAdvancedSearch = togglet.isEnabled('advanced-search', context);
  const hasPremiumFeatures = togglet.isEnabled('premium-features', context);
  
  // UI 테마 변형 가져오기
  const uiTheme = togglet.variation('ui-theme', context, 'classic');
  
  // 검색 결과 페이지 크기
  const searchResultsPerPage = togglet.numberVariation('search-results-per-page', context, 20);
  
  // 사용자 경험 구성
  return {
    ui: hasNewUi ? 'modern' : 'classic',
    theme: uiTheme,
    search: {
      advanced: hasAdvancedSearch,
      resultsPerPage: searchResultsPerPage
    },
    premium: hasPremiumFeatures ? {
      enabled: true,
      features: togglet.jsonVariation('premium-features-config', context, { features: [] })
    } : {
      enabled: false
    }
  };
}
```

### 토글 프록시를 활용한 간결한 코드

```typescript
// ToggleProxy를 활용한 간결한 코드 작성
function processUserRequest(user, requestData) {
  const context = createUserContext(user);
  
  // 단일 토글 객체로 여러 기능 제어
  const featureToggle = togglet.getToggle('user-request-processing', context);
  
  if (!featureToggle.isEnabled()) {
    return processLegacyRequest(requestData);
  }
  
  // 변형에 따른 처리
  const processingMode = featureToggle.getVariantName();
  
  // 처리 설정 가져오기
  const config = featureToggle.jsonVariation({
    timeout: 5000,
    retries: 3,
    batchSize: 10
  });
  
  // 설정 적용
  return processRequest(requestData, {
    mode: processingMode,
    timeout: config.timeout,
    retries: config.retries,
    batchSize: config.batchSize
  });
}
```

## 테스트 및 디버깅

```typescript
// 개발 환경에서 토글 상태 로깅
function logFeatureStatus(featureName, context) {
  const isEnabled = togglet.isEnabled(featureName, context);
  console.log(`Feature '${featureName}' is ${isEnabled ? 'ENABLED' : 'DISABLED'} for context:`, context);
  return isEnabled;
}

// 모든 활성화된 기능 로깅
function logAllEnabledFeatures(user) {
  const context = createUserContext(user);
  const features = [
    'new-ui',
    'advanced-search',
    'premium-features',
    'notifications',
    'analytics',
    'social-sharing'
  ];
  
  console.log('Enabled features for user', user.id);
  features.forEach(feature => {
    if (togglet.isEnabled(feature, context)) {
      console.log(`- ${feature}`);
    }
  });
}
```

## 실제 애플리케이션 통합 예제

```typescript
// Express 미들웨어로 사용
function featureToggleMiddleware(featureName, redirectUrl = '/feature-disabled') {
  return (req, res, next) => {
    const user = req.user;
    const context = createUserContext(user);
    
    if (togglet.isEnabled(featureName, context)) {
      return next();
    }
    
    // 기능이 비활성화된 경우 리디렉션
    return res.redirect(redirectUrl);
  };
}

// API 라우트에 적용
app.get('/api/premium-features', 
  featureToggleMiddleware('premium-features', '/api/upgrade-required'),
  (req, res) => {
    // 프리미엄 기능 처리 로직
    res.json({ features: getPremiumFeatures(req.user) });
  }
);

// 데이터베이스 쿼리 최적화
async function getUserData(userId) {
  const user = await getUserById(userId);
  const context = createUserContext(user);
  
  // 새로운 데이터베이스 쿼리 사용 여부
  if (togglet.isEnabled('optimized-db-queries', context)) {
    return await getOptimizedUserData(userId);
  }
  
  return await getLegacyUserData(userId);
}
```