// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import morgan from 'morgan';
import path from 'path';
import { Container } from 'typedi';
import cms, { load as loadCms } from '../cms';
import * as OC from '../cms/oceanCoordinate';
import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import * as tcp from '../tcplib';
import { OceanZoneManager } from './oceanZoneManager';
import { WalkableTileData } from './walkableTileData';
import * as cmsEx from '../cms/ex';
import Pubsub from '../redislib/pubsub';
import { MRedisConnPool } from '../redislib/connPool';
import {
  CultureNationGroup,
  MateCmsKeyGroup,
  ShipCmsKeyGroup,
  ShipSlotCmsKeyGroup,
  CEquipCmsKeyGroup,
  MatePersonalityLookupTable,
  OceanNpcTemplateLookupTable,
  MateTemplateGroupLookupTable,
} from '../motiflib/model/cmsKeyGroup';
import { OceanSyncManager } from './oceanSyncManager';
import * as oceanPubsub from './oceanPubsub';
import { NavApiClient } from '../motiflib/mhttp/navApiClient';
import { Segments } from '../tcplib/shared/segments';
import { FleetManager } from './fleetManager';
import { OCEAN_ZONE_LEAVE_REASON } from '../motiflib/model/lobby/enum';
import { OceanPerfmonManager } from './oceanPerfmonManager';
import { PacketPerfmon, PacketRecvType, UnitPacketRecvStat } from '../motiflib/packetPerfmon';
import stoppable from 'stoppable';
import * as configPubsubSyncer from '../motiflib/model/config/configPubsubSyncer';
import * as Sentry from '@sentry/node';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// 부락 및 항구의 네비 포인트가 유효한 위치 여부 확인.
const validNavPoint = () => {
  for (const key of Object.keys(cms.OceanZone)) {
    const oceanZoneCms = cms.OceanZone[key];
    if (!oceanZoneCms.townAndVillageDesc) {
      continue;
    }
    oceanZoneCms.townAndVillageDesc.forEach((v) => {
      const body = {
        uu: false,
        start: {
          x: -51,
          y: 180,
        },
        end: {
          x: v.navPoint.latitude,
          y: v.navPoint.longitude,
        },
      };

      mhttp.navid
        .navigation(body)
        .then((result) => {
          if (!result.success) {
            mlog.error('failed', {
              cmsId: v.cmsId,
              body,
            });
            return;
          }
          result.path;
        })
        .catch((err) => {
          mlog.error('NavQuery requestQuery error', {
            err: err.message,
            stack: err.stack,
            navPoint: v.navPoint,
          });
        });
    });
  }
};

// -------------------------------------------------------------------------------------------------
// Module variables.
// -------------------------------------------------------------------------------------------------

export class OceanService {
  nationRedis: MRedisConnPool;
  pubsub: Pubsub;
  configPubsub: Pubsub;
  oceanSyncManager: OceanSyncManager;

  cultureNationGroup: CultureNationGroup;
  mateCmsKeyGroup: MateCmsKeyGroup;
  shipCmsKeyGroup: ShipCmsKeyGroup;
  shipSlotCmsKeyGroup: ShipSlotCmsKeyGroup;
  cEquipCmsKeyGroup: CEquipCmsKeyGroup;

  matePersonalityLookup: MatePersonalityLookupTable;
  oceanNpcTemplateLookup: OceanNpcTemplateLookupTable;
  mateTemplateGroupLookup: MateTemplateGroupLookupTable;

  constructor() {}
  async init() {
    await startTogglet();

    this.oceanSyncManager = new OceanSyncManager();
    this.cultureNationGroup = new CultureNationGroup(cms);
    this.mateCmsKeyGroup = new MateCmsKeyGroup(cms);
    this.shipCmsKeyGroup = new ShipCmsKeyGroup(cms);
    this.shipSlotCmsKeyGroup = new ShipSlotCmsKeyGroup(cms);
    this.cEquipCmsKeyGroup = new CEquipCmsKeyGroup(cms);
    this.matePersonalityLookup = new MatePersonalityLookupTable(cms);
    this.oceanNpcTemplateLookup = new OceanNpcTemplateLookupTable(cms);
    this.mateTemplateGroupLookup = new MateTemplateGroupLookupTable(cms);

    // Init redis pool.
    this.nationRedis = new MRedisConnPool();
    await this.nationRedis.init('nation-redis', mconf.nationRedis);

    const [snapshots, updateTimeUtc] = await this.nationRedis['getPowerSnapshot']();
    this.oceanSyncManager.setupNationPower(JSON.parse(snapshots));

    // Init redis pubsub.
    this.pubsub = oceanPubsub.init();

    this.configPubsub = Container.of('pubsub-config').get(Pubsub);
    this.configPubsub.init(mconf.configPubsubRedis);

    // test navi point
    // validNavPoint();
  }

  async destroy() {
    await this.nationRedis.destroy();
    await this.pubsub.quit();
    await this.configPubsub.quit();

    stopTogglet();
  }
}

// Main express app.
const app = express();

app.disable('x-powered-by');
app.disable('etag');
app.disable('content-type');

const server = stoppable(http.createServer(app));
server.keepAliveTimeout = 0;

export const tcpServer = tcp.server();
tcpServer.routeEx(__dirname, './serverPacketHandler');
tcp.logger.setMoudle(mlog); // Setup tcp log writer

let stopping = false;
export let pingInterval: number = 2000;

// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

function oceanReqLog(tokens, req, res) {
  const netPerfmon = Container.get(PacketPerfmon);
  const unitPacketStat: UnitPacketRecvStat = {
    packetId: 0,
    packetIdStr: req.url,
    size: parseInt(tokens['res'](req, res, 'content-length'), 10),
    duration: parseFloat(tokens['response-time'](req, res)),
  };
  netPerfmon.addApiRecvStat(PacketRecvType.SERVER_API_RECEIVED, unitPacketStat);

  if (req.url === '/move' || req.url === '/health') {
    return;
  }

  const logBody = {
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  };

  if (logBody.mcode) {
    mlog.warn('oceand-req', logBody);
  } else {
    mlog.info('oceand-req', logBody);
  }

  return null;
}

async function unregisterServerd(appServiceUrl: string): Promise<void> {
  try {
    await mhttp.zonelbd.unregisterServerd({ appServiceUrl, zoneType: cmsEx.ZoneType.OCEAN });
    mlog.info('unregistration succeeded.');
  } catch (err) {
    mlog.warn(err.message);

    if (stopping) {
      return;
    }

    await mutil.sleep(1000);
    return unregisterServerd(appServiceUrl);
  }
}

async function registerServerd(appServiceUrl: string, curDate: number): Promise<void> {
  try {
    const resp = await mhttp.zonelbd.registerServerd({
      appServiceUrl,
      curDate,
      zoneType: cmsEx.ZoneType.OCEAN,
    });

    pingInterval = resp.pingInterval;

    mlog.info('registration succeeded.', { pingInterval, curDate });
  } catch (err) {
    mlog.warn(err.message);

    if (stopping) {
      return;
    }

    await mutil.sleep(1000);
    return registerServerd(appServiceUrl, mutil.curTimeUtc());
  }
}

export async function updateServerdPing(appServiceUrl: string, curDate: number): Promise<void> {
  try {
    const resp = await mhttp.zonelbd.updateServerdPing({
      appServiceUrl,
      curDate,
      zoneType: cmsEx.ZoneType.OCEAN,
    });

    if (resp.bStop) {
      Sentry.captureMessage('updateServerdPing signaled to stop. begin stopping oceand server');

      mlog.warn('updateServerdPing signaled to stop. begin stopping server');
      stop();
    } else {
      // mlog.info('updateServerdPing succeeded.');
    }
  } catch (err) {
    mlog.warn(err.message);
  }
}

async function closeServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    server.stop((err) => {
      if (err) return reject(err);
      resolve();
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');
    tcpServer.dispose();

    const zoneManager = Container.get(OceanZoneManager);
    zoneManager.stopTick();

    const oceanPerfmonManager = Container.get(OceanPerfmonManager);
    oceanPerfmonManager.stopPerfmonTick();

    mlog.info('unregister from zonelbd', {
      zonelbd: mconf.http.zonelbd.url,
      apiService: mconf.apiService.url,
    });

    await unregisterServerd(mconf.apiService.url);

    const oceanService = Container.get(OceanService);
    await oceanService.destroy();

    await closeServer();
    mlog.info('server stopped');
    process.exitCode = 0;
  } catch (error) {
    mlog.error('graceful shutdown failed', { error: error.message });
    process.exit(1);
  }
}

// -------------------------------------------------------------------------------------------------
// Misc.
// -------------------------------------------------------------------------------------------------

function _testConfigSanity() {
  const gridDimX = mconf.visibility.gridDimension.x;
  const gridDimY = mconf.visibility.gridDimension.y;
  if (OC.TileSizeUuX % gridDimX !== 0 || OC.TileSizeUuY % gridDimY !== 0) {
    throw new Error(`Invalid visibility grid dimension: (${gridDimX}, ${gridDimY})`);
  }
}

// -------------------------------------------------------------------------------------------------
// Public functions.
// -------------------------------------------------------------------------------------------------

export async function start() {
  try {
    await mhttp.configd.registerInstance(
      process.env.WORLD_ID ? process.env.WORLD_ID : mconf.instance.worldId,
      mconf.appInstanceId,
      mconf.hostname
    );

    mutil.initSentry();

    // Init http clients.
    mhttp.init();

    _testConfigSanity();

    // Init cms.
    loadCms();

    // ocean service
    const oceanService = Container.get(OceanService);
    await oceanService.init();

    // Assets
    const data = await WalkableTileData.fromFile(
      path.resolve(__dirname, '../../../assets/walkabledata.tiles')
    );
    Container.set(WalkableTileData, data);

    app.use(morgan(oceanReqLog));
    app.use(bodyParser.json());
    mutil.registerHealthCheck(app);
    mutil.registerGarbageCollector(app);
    await dirAsApi.register(app, path.join(__dirname, 'api'));

    app.use(expressError.middleware);
    const onDisconnected = (segment: Segments): void => {
      const disconnectedServerURL = segment.get('url');
      const fleetManager = Container.get(FleetManager);
      const userFleets = fleetManager.getAll();

      // 접속종료된 로비서버와 연결된 유저를 제거한다.
      const removedUsersForLog = [];
      userFleets.forEach((userFleet) => {
        const userLobbyUrl = userFleet.getLobbyUrl();
        if (userLobbyUrl === disconnectedServerURL) {
          fleetManager.delete(userFleet.userId);
          const zone = userFleet.getCurrentZone();
          if (zone) {
            zone.removeUserFleet(userFleet.userId, OCEAN_ZONE_LEAVE_REASON.DISCONNECTED_SERVER);
          }
          removedUsersForLog.push(userFleet.userId);

          mlog.error('oceanUserFleet was destroyed by disconnected lobbyd', {
            userId: userFleet.userId,
            oceanZoneCmsId: zone ? zone.oceanZoneCms.id : 0,
            location: userFleet.getLocation(),
          });
        }
      });
    };

    const tcpconf = mconf.apiService.tcpServer;
    tcpServer.start(tcpconf.port, tcpconf.ip, onDisconnected);

    // Start ticking the zone manager.
    const zoneManager = Container.get(OceanZoneManager);
    zoneManager.init(cms);
    zoneManager.tick();

    const oceanPerfmonManager = Container.get(OceanPerfmonManager);
    oceanPerfmonManager.startPerfmonTick();

    const bindAddress = mconf.apiService.bindAddress;
    const port = mconf.apiService.port;
    server.listen(port, bindAddress, () => {
      mlog.info('start listening ...', { bindAddress, port });
    });

    // 동적 서버 인스턴스 증가 대응(scale out)
    configPubsubSyncer.subscribeForRegisterInstance(
      oceanService.configPubsub,
      () => {
        // 필요한 경우 이곳에 새로운 인스턴스 등록 이벤트 발생시 처리할 작업을 등록
        mlog.info(
          `received a new instance in layout[${mconf.layoutVersion}] but nothing to do currently`
        );
      },
      isStopping,
      stop
    );

    // config final sync
    const beforeVer = mconf.layoutVersion;
    await mhttp.configd.sync(beforeVer, isStopping, stop).then(() => {
      if (beforeVer < mconf.layoutVersion) {
        // do something
      }
    });
    const worldConfig = mconf.getWorldConfig();

    mlog.info('register to zonelbd', {
      zonelbd: worldConfig.http.zonelbd.url,
      apiService: mconf.apiService.url,
    });

    await unregisterServerd(mconf.apiService.url);
    await registerServerd(mconf.apiService.url, mutil.curTimeUtc());
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
}

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await stopServer();
  process.exit(0);
}
