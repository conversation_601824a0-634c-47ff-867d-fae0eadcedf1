"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const merror_1 = require("../../motiflib/merror");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const typedi_1 = __importDefault(require("typedi"));
const fleetManager_1 = require("../fleetManager");
const cms_1 = __importDefault(require("../../cms"));
const ex_1 = require("../../cms/ex");
const oceanEntityInstanceManager_1 = require("../oceanEntityInstanceManager");
const oceanNpcDesc_1 = require("../../cms/oceanNpcDesc");
const battleUtil_1 = require("../../lobbyd/battleUtil");
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
router.on(protocol_1.Protocol.LB2OC_NTF_BATTLE_LOSE, async (req, res) => {
    const userId = req.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        mlog_1.default.warn('user not found in this oceand.(LB2OC_NTF_BATTLE_END)', {
            userId,
        });
        return;
    }
    const oceanZone = fleet.getCurrentZone();
    if (!oceanZone) {
        mlog_1.default.warn('zone not found in this oceand.(LB2OC_NTF_BATTLE_END)', {
            userId,
        });
        return;
    }
    const oceanNpcAreaSpawnerCmsId = fleet.getOceanNpcAreaSpawnerCmsId();
    if (oceanNpcAreaSpawnerCmsId) {
        const areaSpawnEntry = oceanZone.getOceanTileNpcAreaSpawnEntry(oceanNpcAreaSpawnerCmsId);
        if (areaSpawnEntry) {
            areaSpawnEntry.revive();
        }
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER, async (req, res) => {
    const offlineUserId = req.offlineUserId;
    const battleResult = req.battleResult;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(offlineUserId);
    if (!fleet) {
        mlog_1.default.warn('offline user not found in this oceand', {
            offlineUserId,
        });
        return;
    }
    if (!fleet.isOfflineAutoSailing()) {
        mlog_1.default.warn('offline user has become online', {
            offlineUserId,
        });
        return;
    }
    const packet = new protocol_1.Protocol.OC2LB_NTF_PVP_BATTLE_END_FOR_OFFLINE_USER();
    packet.offlineUserId = offlineUserId;
    packet.battleResult = battleResult;
    fleet.sendToLobby(packet);
});
router.on(protocol_1.Protocol.LB2OC_NTF_NET_SWEEP_LOCAL_NPC, async (req, res) => {
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(req.userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand.', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId: req.userId,
            npcId: req.npcId,
        });
    }
    const grid = userFleet.getGrid();
    if (!grid) {
        throw new merror_1.MError('User not in a grid.', merror_1.MErrorCode.USER_NOT_IN_GRID, {
            userId: req.userId,
        });
    }
    userFleet.setCombatPower(req.userCombatPower);
    const sendFail = (codeResult) => {
        const responsePacket = new protocol_1.Protocol.OC2LB_NTF_NET_SWEEP_LOCAL_NPC();
        responsePacket.result = codeResult;
        res.send(responsePacket);
    };
    const npcFleet = grid.findVisibleNpc(req.npcId);
    if (!npcFleet) {
        sendFail(merror_1.MErrorCode.SWEEP_NOT_FOUND_NPC_FLEET);
        return;
    }
    // npc가 인카운트 공격을 받을 수 있는지 체크.
    const result = npcFleet.canBeAttacked();
    if (result !== merror_1.MErrorCode.OK) {
        sendFail(result);
        return;
    }
    // 인카운트 거리 체크.
    const distUu = userFleet.getDistanceFrom(npcFleet.getLocation());
    if (distUu > cms_1.default.Const.OceanUserAttackRadius.value + battleUtil_1.MARGIN_OF_ERROR_UU) {
        sendFail(merror_1.MErrorCode.SWEEP_NPC_TOO_FAR_AWAY);
        return;
    }
    // 소탕의 경우 local npc 한해서만 가능.
    const oceanNpcCms = (0, ex_1.getOceanNpcCms)()[npcFleet.getNpcCmsId()];
    if (oceanNpcCms.OceanNpcType !== oceanNpcDesc_1.OCEAN_NPC_TYPE.LOCAL) {
        throw new merror_1.MError('can-only-local-npc', merror_1.MErrorCode.SWEEP_CAN_ONLY_LOCAL_NPC, {
            userId: req.userId,
        });
    }
    // SweepBattlePowerRateUseServer. 컴뱃 파워가 동적이므로 클라가 사용하는
    // SweepBattlePowerRate 보다 여유값을 둬서 검사함.
    const userCombatPower = userFleet.getCombatPower();
    const npcCombatPower = npcFleet.getCombatPower();
    if (userCombatPower <
        Math.floor((npcCombatPower * cms_1.default.Const.SweepBattlePowerRateUseServer.value) / 1000)) {
        throw new merror_1.MError('not-enought-combat-power', merror_1.MErrorCode.NOT_ENOUGHT_COMBAT_POWER, {
            userCombatPower,
            npcCombatPower,
            SweepBattlePowerRateUseServer: cms_1.default.Const.SweepBattlePowerRateUseServer.value,
        });
    }
    // 멀티인카운트 안됨
    typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager).deleteNpcFleet(npcFleet);
    const resPacket = new protocol_1.Protocol.OC2LB_NTF_NET_SWEEP_LOCAL_NPC();
    resPacket.result = merror_1.MErrorCode.OK;
    resPacket.oceanFleetData = npcFleet.getOceanFleetData();
    res.send(resPacket);
});
module.exports = router;
//# sourceMappingURL=oceanpacketHandlerBattle.js.map