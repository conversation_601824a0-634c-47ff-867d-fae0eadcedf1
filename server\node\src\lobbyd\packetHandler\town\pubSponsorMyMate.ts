// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';
import assert from 'assert';

import tuPubSponsorMyMate from '../../../mysqllib/txn/tuPubSponsorMyMate';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import mlog from '../../../motiflib/mlog';
import { LobbyService } from '../../server';
import { Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import {
  getSponsorSuccessProbForAdmiral,
  getSponsorSuccessProbForLoyalty,
} from '../../../cms/mateReRecruitingDesc';
import { ItemChange, ItemInven, UserInven } from '../../userInven';
import UserPoints, { CashPayment, PointChange, PointConsumptionCostParam } from '../../userPoints';
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import { RewardData } from '../../../motiflib/gameLog';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { MateReRecruitingDesc, ReRecruitFixedPointType } from '../../../cms/mateReRecruitingDesc';
import mconf from '../../../motiflib/mconf';
import { needMateRecruitingTermsCheck } from '../../../cms/mateRecruitingGroupDesc';
import { MateDesc } from '../../../cms/mateDesc';
import Mate from '../../mate';
import { TownMyMate } from '../../userMates';
import { AccumulateParam } from '../../userAchievement';

// ----------------------------------------------------------------------------
// 여관-동료, 항해사 격려, 상여금 (기획에서 사용하는 용어는 reRecruit재고용)
// ----------------------------------------------------------------------------

const rsn = 'pub_sponsor_my_mate';
const add_rsn = null;

enum SponsorType {
  // 격려 (항해사 등급, 충성도에 따라 확률이 다름)
  Encourage = 1,

  // 상여금 (Normal, Special 모두 100퍼로 계약서 얻음. 다만 소모되는 재화가 다름.)
  NormalBonus = 2,
  SpecialBonus = 3,
}

interface PubSponsorInfo {
  // 소모 재화 정보.
  pointCmsId: number;
  pointAmount: number;

  // sponsor로 얻은 계약서 정보.
  itemCmsId: number;
  itemAmount: number;

  // 성공 정보.
  bSuccess: boolean;
  successRatio: number;

  townMyMateChange: TownMyMate;
}

interface RequestBody {
  mateCmsIds: number[];
  sponsorType: SponsorType;
  bPermitExchange?: boolean;
}

interface ResponseBody extends Resp {
  bResults: { [mateCmsId: number]: boolean };
}

// ----------------------------------------------------------------------------
export class Cph_Town_PubSponsorMyMate implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureInTown();

    const body: RequestBody = packet.bodyObj;
    const { sponsorType, bPermitExchange } = body;
    const mateCmsIds: number[] = body.mateCmsIds ? body.mateCmsIds : []; // 임시.

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    // 여관-동료 메뉴는 잠금되지 않음
    // user.userContentsTerms.ensureBuildingContentsUnlock(
    //   cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID,
    //   user
    // );

    // if (mconf.binaryCode === 'GL') {
    //   throw new MError('invalid-binary-code', MErrorCode.PUB_SPONSORE_MY_MATE_INVALID_BINARY_CODE, {
    //     userId: user.userId,
    //     binaryCode: mconf.binaryCode,
    //   });
    // }

    const curTimeUtc = mutil.curTimeUtc();
    _ensure(user, mateCmsIds, sponsorType, curTimeUtc);

    const townMyMates = user.userMates.getCurTownMyMates(user.userTown);

    const townCmsId = user.userTown.getTownCmsId();
    const townCms = cms.Town[user.userTown.getTownCmsId()];

    // 대상이 되는 항해사 별로 소모 재화, 성공 여부, 성공 시 획득한 아이템 세팅을 한다.
    const sponsorMates: { [mateCmsId: number]: PubSponsorInfo } = {};
    for (const mateCmsId of mateCmsIds) {
      const mateCms = cms.Mate[mateCmsId];
      const userMate = user.userMates.getMate(mateCmsId);
      const townMyMate = townMyMates.mates[mateCmsId];

      const mateReRecruitingCms: MateReRecruitingDesc = cmsEx.getMateReRecruitingForMateGrade(
        mateCms.mateGrade
      );

      const sponsorMate: PubSponsorInfo = {
        pointCmsId: null,
        pointAmount: 0,

        bSuccess: false,
        successRatio: 0,

        itemCmsId: null,
        itemAmount: 0,

        townMyMateChange: null,
      };

      switch (sponsorType) {
        case SponsorType.Encourage:
          sponsorMate.pointCmsId = mateReRecruitingCms.reRecruitPointId;
          sponsorMate.pointAmount = mateReRecruitingCms.reRecruitPointVal;

          const successProbPermil = userMate.isLeadable() // 제독은 충성도 시스템이 없는 것 참고
            ? getSponsorSuccessProbForAdmiral(mateReRecruitingCms)
            : getSponsorSuccessProbForLoyalty(mateReRecruitingCms, userMate.getLoyalty());

          const successRatio = successProbPermil / 1000;

          // roll
          sponsorMate.bSuccess = Math.random() < successRatio;
          sponsorMate.successRatio = successRatio;
          break;
        case SponsorType.NormalBonus:
        case SponsorType.SpecialBonus:
          const reRecruitFixedPointIndex =
            sponsorType === SponsorType.NormalBonus
              ? ReRecruitFixedPointType.NoramlBonus
              : ReRecruitFixedPointType.SpecialBonus;

          sponsorMate.pointCmsId =
            mateReRecruitingCms.reRecruitFixedPointId[reRecruitFixedPointIndex];
          sponsorMate.pointAmount =
            mateReRecruitingCms.reRecruitFixedPointVal[reRecruitFixedPointIndex];

          sponsorMate.bSuccess = true;
          sponsorMate.successRatio = 1;
          break;
        default:
          throw new MError(
            'invalid-my-mate-sponsor-type',
            MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE,
            { body }
          );
      }

      // 랜덤 결과 적용.
      if (sponsorMate.bSuccess) {
        sponsorMate.itemCmsId = mateCms.reRecruitRewardItemId;
        sponsorMate.itemAmount = mateCms.reRecruitRewardItemCount;

        sponsorMate.townMyMateChange = {
          index: townMyMate.index,
          mateCmsId: null,
          sponsorableTimeUtc: null,
        };
      } else {
        assert(mateReRecruitingCms.reRecruitFailResetCycleSec !== undefined);
        sponsorMate.townMyMateChange = {
          index: townMyMate.index,
          mateCmsId: townMyMate.mateCmsId,
          sponsorableTimeUtc: curTimeUtc + mateReRecruitingCms.reRecruitFailResetCycleSec,
        };
      }
      sponsorMates[mateCmsId] = sponsorMate;
    }

    // 항해사 별로 소모한 재화, 얻은 계약서를 하나의 객체로 관리.
    const clonItemInven: ItemInven = user.userInven.itemInven.clone();
    const itemChanges: { [itemCmsId: number]: ItemChange } = {};
    const pointCosts: { [pointCmsId: number]: PointConsumptionCostParam } = {};
    _.forEach(sponsorMates, (sponsorMate) => {
      if (!pointCosts[sponsorMate.pointCmsId]) {
        pointCosts[sponsorMate.pointCmsId] = {
          cmsId: sponsorMate.pointCmsId,
          cost: 0,
        };
      }
      pointCosts[sponsorMate.pointCmsId].cost += sponsorMate.pointAmount;

      if (!sponsorMate.bSuccess) {
        return;
      }

      clonItemInven.ensureCanAdd(sponsorMate.itemCmsId, sponsorMate.itemAmount, true);
      const itemChange: ItemChange = clonItemInven.buildItemChange(
        sponsorMate.itemCmsId,
        sponsorMate.itemAmount,
        true,
        true
      );
      clonItemInven.applyItemChange(itemChange, null, null);
      itemChanges[itemChange.cmsId] = itemChange;
    });

    const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
      Object.values(pointCosts),
      sponsorType === SponsorType.Encourage ? false : bPermitExchange,
      { itemId: rsn },
      true
    );

    const resp: ResponseBody = { sync: {}, bResults: {} };
    const accums: AccumulateParam[] = [];
    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return Promise.resolve()
      .then(() => {
        return user.userPoints.tryConsumeCashs(pcChanges.cashPayments, resp.sync, user, {
          user,
          rsn,
          add_rsn,
          exchangeHash,
        });
      })
      .then(() => {
        return tuPubSponsorMyMate(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          pcChanges.pointChanges,
          Object.values(itemChanges),
          townCmsId,
          Object.values(sponsorMates).map((v) => v.townMyMateChange)
        );
      })
      .then(() => {
        // glog
        let collection: string;
        if (sponsorType === SponsorType.Encourage) {
          collection = 'mate_support';
        } else if (
          sponsorType === SponsorType.NormalBonus ||
          sponsorType === SponsorType.SpecialBonus
        ) {
          collection = 'mate_support_bonus';
        }

        // point update.
        _.merge<Sync, Sync>(
          resp.sync,
          user.userPoints.applyPointChanges(pcChanges.pointChanges, { user, rsn, add_rsn })
        );

        // item update.
        _.forEach(itemChanges, (itemChange) => {
          _.merge<Sync, Sync>(
            resp.sync,
            user.userInven.itemInven.applyItemChange(itemChange, accums, {
              user,
              rsn,
              add_rsn,
            })
          );
        });

        _.forEach(sponsorMates, (sponsorMate, mateCmsIdStr) => {
          const mateCmsId: number = parseInt(mateCmsIdStr);
          const mateCms: MateDesc = cms.Mate[mateCmsId];
          const userMate: Mate = user.userMates.getMate(mateCmsId);
          const townMyMate: TownMyMate = townMyMates.mates[mateCmsId];

          const townMyMateChange = sponsorMate.townMyMateChange;
          if (sponsorMate.bSuccess) {
            delete townMyMates.mates[mateCmsId];
            _.merge<Sync, Sync>(resp.sync, {
              remove: {
                towns: {
                  [townCmsId]: {
                    myPubMyMates: {
                      mates: {
                        [mateCmsId]: true,
                      },
                    },
                  },
                },
              },
            });
          } else {
            const { sponsorableTimeUtc } = townMyMateChange;
            townMyMate.sponsorableTimeUtc = sponsorableTimeUtc;
            _.merge<Sync, Sync>(resp.sync, {
              add: {
                towns: {
                  [townCmsId]: {
                    myPubMyMates: {
                      mates: {
                        [mateCmsId]: {
                          sponsorableTimeUtc,
                        },
                      },
                    },
                  },
                },
              },
            });
          }
          resp.bResults[mateCmsId] = sponsorMate.bSuccess;

          const mateReRecruitingCms: MateReRecruitingDesc = cmsEx.getMateReRecruitingForMateGrade(
            mateCms.mateGrade
          );
          user.glog(collection, {
            rsn,
            add_rsn,

            town_id: user.userTown.getTownCmsId(),
            town_name: townCms.name,
            mate_id: mateCmsId,
            mate_name: displayNameUtil.getMateDisplayName(mateCmsId),
            mate_grade: mateCms.mateGrade,
            mate_awakenLv: userMate.getAwakenLv(),
            loyalty: _.defaultTo(userMate.getLoyalty(), null),
            success_rate: sponsorMate.successRatio, // [0, 1] ex) 0.123
            is_success: sponsorMate.bSuccess ? 1 : 0, // 성공: 1 , 실패: 0
            reset_time: sponsorMate.bSuccess
              ? null
              : mateReRecruitingCms.reRecruitFailResetCycleSec, // 실패시. (단위: 초)
            pr_data: sponsorMate.pointCmsId
              ? [{ type: sponsorMate.pointCmsId, amt: sponsorMate.pointAmount }]
              : null,
            reward_data: sponsorMate.itemCmsId
              ? {
                  type: REWARD_TYPE[REWARD_TYPE.ITEM],
                  id: sponsorMate.itemCmsId,
                  uid: null,
                  amt: sponsorMate.itemAmount,
                }
              : null,
            exchange_hash: exchangeHash,
          });
        });

        if (accums.length > 0) {
          return user.userAchievement.accumulate(accums, user, resp.sync, { user, rsn, add_rsn });
        }
      })
      .then(() => {
        return user.sendJsonPacket(packet.seqNum, packet.type, resp);
      });
  }
}

function _ensure(user: User, mateCmsIds: number[], sponsorType: SponsorType, curTimeUtc: number) {
  const townMyMates = user.userMates.getCurTownMyMates(user.userTown);
  const townCms = cms.Town[user.userTown.getTownCmsId()];

  if (!mateCmsIds || mateCmsIds.length <= 0) {
    throw new MError('invalid-mate-cms-ids', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
      mateCmsIds,
    });
  }

  if (sponsorType != SponsorType.Encourage && mateCmsIds.length != 1) {
    throw new MError(
      'mate-cms-ids-length-only-1-for-bonus-type',
      MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE,
      {
        sponsorType,
        mateCmsIds,
      }
    );
  }

  const duplicated: { [cmsId: number]: boolean } = {};
  for (const mateCmsId of mateCmsIds) {
    const mateCms = cms.Mate[mateCmsId];
    if (!mateCms) {
      throw new MError('invalid-mate-cms-id', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
        mateCmsId: mateCmsIds,
      });
    }

    // 항해사 목록이 보유 중인 항해사에서 선정되기 때문에 정상적인 상황은 아님.
    const userMate = user.userMates.getMate(mateCmsId);
    if (!userMate) {
      throw new MError('has-not-mate', MErrorCode.INTERNAL_ERROR, {
        mateCmsId: mateCmsIds,
      });
    }

    // Check time. 서버 클라간 시간이 정확히 같지 않기 때문에 100초의 여유를 둔다.
    const curLastUpdateTimeUtc = townMyMates.lastUpdateTimeUtc;
    const mateResetSeconds = cms.Const.RecruitResetCycleSec.value;
    if (curLastUpdateTimeUtc + mateResetSeconds <= curTimeUtc - 100) {
      throw new MError('pub-my-mates-expired', MErrorCode.PUB_MY_MATE_TIME_EXPIRED, {
        mateCmsId: mateCmsIds,
        curLastUpdateTimeUtc,
        curTimeUtc,
        diff: curTimeUtc - curLastUpdateTimeUtc,
      });
    }

    // https://jira.line.games/browse/UWO-7925,
    // https://jira.line.games/browse/UWO-19699 특권 보너스,
    // cms.MateRecruitingGroup.isMustAppear 이 true 또는 mustAppearEvent 조건 만족인 경우
    // cms.MateRecruitingGroup.contentsTerms 을 검사해야됨
    const mateRecruitingGroupCms = cmsEx.getMateRecruitingGroupByGroupAndMateCmsId(
      townCms.mateRecruitingGroup,
      mateCmsId
    );

    const eventPageProducts = user.userCashShop.getEventPageProducts();
    if (
      !mateRecruitingGroupCms ||
      (needMateRecruitingTermsCheck(mateRecruitingGroupCms, eventPageProducts, curTimeUtc) &&
        !user.userContentsTerms.isValidContentsTerms(mateRecruitingGroupCms.contentsTerms, user))
    ) {
      throw new MError('contents-terms-invalid', MErrorCode.CONTENTS_TERMS_INVALID, {
        mateCmsId: mateCmsIds,
        townCmsId: townCms.id,
        mateRecruitingGroupCms,
        contentsTerms: mateRecruitingGroupCms.contentsTerms,
      });
    }

    const townMyMate = townMyMates.mates[mateCmsId];
    if (!townMyMate) {
      throw new MError('not-found-mate', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
        mateCmsId: mateCmsIds,
      });
    }

    // 격려 대기 중인지 확인
    if (sponsorType === SponsorType.Encourage) {
      // 서버 클라간 시간이 정확히 같지 않기 때문에, 얼마간의 여유를 둔다.
      if (
        townMyMate.sponsorableTimeUtc &&
        curTimeUtc < townMyMate.sponsorableTimeUtc - cms.Const.TimeCostFreeTimeSec.value
      ) {
        throw new MError(
          'wait-time-has-not-expired',
          MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE,
          {
            mateCmsId: mateCmsIds,
            curTimeUtc,
            sponsorableTimeUtc: townMyMate.sponsorableTimeUtc,
            diff: curTimeUtc - townMyMate.sponsorableTimeUtc,
          }
        );
      }
    }

    if (duplicated[mateCmsId]) {
      throw new MError('duplicated-mate-cms-id', MErrorCode.INVALID_REQ_BODY_PUB_SPONSOR_MY_MATE, {
        mateCmsIds,
      });
    }
    duplicated[mateCmsId] = true;
  }
}
