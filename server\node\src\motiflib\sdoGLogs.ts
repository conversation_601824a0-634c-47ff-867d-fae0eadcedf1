// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { customAlphabet } from 'nanoid';
import { SdoGLogSerializable } from './sdoGLogs.core';

export * from './sdoGLogs.generated';
export * from './sdoGLogs.core';

export namespace SdoGLogs {
  export namespace Utils {
    const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    const nanoid = customAlphabet(alphabet, 16);

    export function generateRequestId(): string {
      return nanoid();
    }

    export function osToPlatform(os: string): number {
      switch (os?.toLowerCase()) {
        case 'android': return Constants.Platform.ANDROID;
        case 'ios': return Constants.Platform.IOS;
        case 'windows': return Constants.Platform.PC;
        //TODO H5?
        //TODO WECHAT_MINI_GAME?
        //TODO HARMONY_OS?
        default: return Constants.Platform.UNKNOWN;
      }
    }
  }

  export namespace Constants {
    export const PAY_TYPE = {
      NORMAL: 1,
      GIFT: 2,
    };

    export const DEPOSIT_TYPE = {
      // 게임내결제
      IN_GAME: 1,
      // 게임외부결제
      OUT_GAME: 2,
      // 게임상점결제
      STORE: 3,
    };

    export enum CHANGE_TYPE {
      Add = 1,
      Del = 0,
    }

    export const DEPOSIT_STATUS = {
      ORDER_CREATED: 1,
      PAYMENT_SUCCESS: 2,
      PAYMENT_FAILED: 3,
      DELIVERY_SUCCESS: 4,
      DELIVERY_FAILED: 5,
    };

    export enum Platform {
      UNKNOWN = 0,
      ANDROID = 1,
      IOS = 2,
      H5 = 3,
      PC = 4,
      WECHAT_MINI_GAME = 5,
      HARMONY_OS = 6,
    }
  }
}


// String escape: remove forbidden characters |, \n, ;
function escapeStr(v: any): string {
  return String(v ?? '').replace(/\n/g, '').replace(/\|/g, '').replace(/;/g, '');
}

export class SdoGLogFromGLog implements SdoGLogSerializable {
  private data: any;

  constructor(data: any) {
    this.data = data;
  }

  getEventName(): string {
    return this.data.event_name;
  }

  serialize(): string {
    for (const key in this.data) {
      if (typeof this.data[key] === 'string') {
        this.data[key] = escapeStr(this.data[key]);
      }
    }

    return Object.values(this.data).join('|');
  }

  toObject(): object {
    return this.data;
  }
}
