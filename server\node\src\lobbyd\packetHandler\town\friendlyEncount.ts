// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import mhttp from '../../../motiflib/mhttp';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as UserConnection from '../../userConnection';
import { FriendlyEncountUtil } from '../../userFriendlyEncount';
import { ClientPacketHandler } from '../index';
import { TOWN_USER_STATE } from '../../../townd/townUserState';
import _ from 'lodash';
import { Resp } from '../../type/sync';
import * as cmsEx from '../../../cms/ex';
import { EncountResult, EncountTargetType } from '../../../motiflib/model/ocean/enum';
import * as mutil from '../../../motiflib/mutil';
import { OceanNpcStageDesc } from '../../../cms/oceanNpcStageDesc';
import cms from '../../../cms';
import { nanoid } from 'nanoid';
import { NPC_INTERACTION_FUNCTION_TYPE } from '../../../cms/npcInteractionDesc';
import { FleetHelper } from '../../fleetHelper';
import { BattleType } from '../../../motiflib/model/lobby';
import Container from 'typedi';
import { LobbyService } from '../../server';
import { FriendlyEncountResult, FriendlyEncountState } from '../../userFriendlyEncount';

// ----------------------------------------------------------------------------
// 이동 패킷 처리.
// ----------------------------------------------------------------------------
interface RequestBody {
  userId: number;
}

interface ResponseBody {
  encountResult: FriendlyEncountResult;
}

// ----------------------------------------------------------------------------
export class Cph_Town_FriendlyEncount implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    if (
      user.userState.getGameState() === GAME_STATE.IN_TOWN &&
      user.userState.getTownUserState() === TOWN_USER_STATE.IN_TOWN
    ) {
      return true;
    }

    return false;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const { userId: defenserUserId } = packet.bodyObj;

    const { userCacheRedis } = Container.get(LobbyService);
    const curTimeUtc: number = mutil.curTimeUtc();

    if (!defenserUserId || !_.isInteger(defenserUserId)) {
      throw new MError('invalid-user-id', MErrorCode.INVALID_REQ_BODY_REQUEST_FRIENDLY_BATTLE, {
        defenserUserId,
      });
    }

    const encountResult: FriendlyEncountResult = FriendlyEncountUtil.checkFriendlyEncount(
      user,
      true
    );

    if (encountResult !== FriendlyEncountResult.WAITING_FOR_CHOICE) {
      return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
        encountResult,
      });
    }
    const pvpId = nanoid(8);

    return mhttp.chatd
      .getMuteUserIds(defenserUserId)
      .then((muteUserIds) => {
        for (const muteUserId of muteUserIds) {
          if (muteUserId === user.userId) {
            throw new MError(
              'attacker-is-blocked-user',
              MErrorCode.FRIENDLY_ENCOUNT_REQUEST_BLOCKED_USER,
              {
                attackerUserId: user.userId,
                defenserUserId,
                defenserMuteUserIds: muteUserIds,
              }
            );
          }
        }
      })
      .then(() => {
        return userCacheRedis['getIsFriendlyBattleRequestable'](defenserUserId);
      })
      .then((defenserRequestable) => {
        // 상대가 친선전 허용한 상태인지 검사.
        if (defenserRequestable !== 1) {
          throw new MError(
            'no-friendly-encount-requestable',
            MErrorCode.INVALID_REQ_BODY_REQUEST_FRIENDLY_BATTLE,
            {
              defenserUserId,
              defenserRequestable,
            }
          );
        }
      })
      .then(() => {
        const attackerFleetData = FleetHelper.buildOceanFleetData(user, BattleType.Friendly);

        const townInfo = user.userTown.getTownInfo();
        const townApi = mhttp.townpx.channel(townInfo.url);

        return townApi.friendlyEncount(
          user.userId,
          user.pubId,
          attackerFleetData,
          user.userGuild.getGuildName(),
          defenserUserId,
          pvpId
        );
      })
      .then((recv) => {
        if (recv.encountResult === FriendlyEncountResult.WAITING_FOR_CHOICE) {
          const encountState: FriendlyEncountState = {
            bAttack: true,
            encountResult: FriendlyEncountResult.WAITING_FOR_CHOICE,
            timeUtc: curTimeUtc,
            bClosing: false,
            fleetData: recv.defenserFleetData,
            userId: defenserUserId,
            pubId: recv.defenserPubId,
            pvpId,
            representedMateCmsId:
              recv.defenserRepresentedMateCmsId > 0 ? recv.defenserRepresentedMateCmsId : undefined,
            representedMateIllustCmsId:
              recv.defenserRepresentedMateIllustCmsId > 0
                ? recv.defenserRepresentedMateIllustCmsId
                : undefined,
            oceanNpcStageCmsId: recv.oceanStageCmsId,
          };

          user.userFriendlyEncount.beginEncount(encountState);
          user.userState.onEncountStateSet();
        }

        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          encountResult: recv.encountResult,
        });
      });
  }
}
