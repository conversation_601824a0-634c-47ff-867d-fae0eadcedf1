// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import { Job } from 'node-schedule';
import { Service } from 'typedi';

import mconf from '../../motiflib/mconf';
import * as townTrade from './townTrade';
import * as updateNationIntimacy from './updateNationIntimacy';
import * as updatePopulation from './updatePopulation';
import * as collectorRankReward from './collectorRankReward';
import * as arenaWeekly from './arenaWeekly';
import * as monitor from './monitor';
import * as deleteAccounts from './deleteAccounts';
import * as tmpCollectorRankRewardLog from './tmpCollectorRankRewardLog';
import * as bulkMail from './bulkMail';
import * as flushSailingDiaries from './flushSailingDiaries';
import * as nationElection from './nationElection';
import * as villageStatus from './villageStatus';
import * as nationWeeklyDonationRank from './nationWeeklyDonationRank';
import * as townSmuggle from './townSmuggle';
import * as determineBlindBidder from './determineBlindBidWinner';
import * as investmentManager from './investmentManager/investmentManager';

@Service()
export class ScheduledJobs {
  townTrade: Job[];
  updateNationIntimacy: Job;
  updatePopulation: Job;
  chinaUnder18TimeLimit: Job;
  investmentManager: Job[];
  collectorRankReward: Job;
  arenaWeekly: Job;
  monitorForServerGroup: Job;
  monitorForWorld: Job;
  deleteAccounts: Job;
  bulkMail: Job;
  flushSailingDiaries: Job;
  nationElection: Job;
  villageStatus: Job;
  nationWeeklyDonationRank: Job;
  townSmuggle: Job[];
  determineBlindBidder: Job;

  start() {
    this.townTrade = townTrade.start();
    this.updateNationIntimacy = updateNationIntimacy.start();
    this.updatePopulation = updatePopulation.start();
    this.investmentManager = investmentManager.start();
    this.collectorRankReward = collectorRankReward.start();
    tmpCollectorRankRewardLog.start();
    this.arenaWeekly = arenaWeekly.start();
    this.flushSailingDiaries = flushSailingDiaries.start();
    this.nationElection = nationElection.start();
    this.villageStatus = villageStatus.start();
    this.nationWeeklyDonationRank = nationWeeklyDonationRank.start();
    this.townSmuggle = townSmuggle.start();
    this.determineBlindBidder = determineBlindBidder.start();

    // 서버군 안에서 한 곳에서만 수행되어야 되는 스퀘줄.
    if (mconf.getWorldNumber() === 1) {
      // 예외적으로 호주 서버는 한국 서버군에 들어갔기 때문에 해당되지 않는다.
      if (mconf.worldId !== 'UWO-AU-01') {
        this.monitorForServerGroup = monitor.startForServerGroup();
        this.deleteAccounts = deleteAccounts.start();
      }
    }

    this.monitorForWorld = monitor.startForWorld();

    this.bulkMail = bulkMail.start();
  }
}
