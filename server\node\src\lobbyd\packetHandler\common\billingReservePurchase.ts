// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mhttp from '../../../motiflib/mhttp';
import * as mutil from '../../../motiflib/mutil';
import {
  ensureGiveItems,
  LGBillingErrorCode,
  LGBillingResponseBody,
  LineGamesBillingApiClient,
} from '../../../motiflib/mhttp/linegamesBillingApiClient';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE, UserRealConnection } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { UNBUYABLE_REASON, BillingUtil } from '../../userCashShop';
import { CASH_SHOP_SALE_POINT_TYPE, SHOP_CASE } from '../../../cms/cashShopDesc';

// ----------------------------------------------------------------------------
// 단순히 LG Billing Server API 로 이어줌.
// *이어주는 기능만 했지만, 상품 구입 가능 검증도 겸하게 됨.
// ----------------------------------------------------------------------------

interface RequestBody {
  productId: string;
  price: number;
  currency: string;
  microPrice: number;

  // glog 를 위함.
  productName: string;
}

interface ResponseBody {
  step: unknown; // 일단은 클라 로그 참고용으로 보냄.
  billingApiResp: unknown; // 빌링 서버 API 의 response
}

const ERROR_CODE_NOT_EXIST_DATA = LGBillingErrorCode[LGBillingErrorCode.NOT_EXIST_DATA];

class BillingApiFailError extends Error {
  public readonly step: string;
  public readonly resp: LGBillingResponseBody;
  constructor(api: string, billingApiResp: LGBillingResponseBody) {
    super();
    this.step = api;
    this.resp = billingApiResp;
  }
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingReservePurchase implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const userConn = user.userConn;
    // getIp 를 사용할 수 있도록 UserRealConnection 검증
    // (UserBotConnection 에는 소켓이 없음)
    if (!(userConn instanceof UserRealConnection)) {
      throw new MError('invalid-user-connection', MErrorCode.INVALID_CONN_STATE);
    }

    if (!LineGamesBillingApiClient.isCompleteReservedPurchaseImplemented(user.storeCode)) {
      throw new MError('invalid-store-code', MErrorCode.INTERNAL_ERROR, {
        storeCode: user.storeCode,
      });
    }

    const reqBody: RequestBody = packet.bodyObj;
    const { productId, price, currency, microPrice } = reqBody;
    const curTimeUtc = mutil.curTimeUtc();

    const cashShopCms = cmsEx.getCashShopCmsByProductCode(productId);
    if (!cashShopCms) {
      throw new MError(
        'failed-to-get-cash-shop-cms',
        MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE,
        { productId, storeCode: user.storeCode }
      );
    }
    if (cashShopCms.salePointType !== CASH_SHOP_SALE_POINT_TYPE.CASH) {
      throw new MError(
        'invalid-sale-point-type',
        MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE,
        { productId, cashShopCmsId: cashShopCms.id }
      );
    }
    if (cashShopCms.shopCase === SHOP_CASE.WEB_SHOP) {
      throw new MError(
        'can-not-buy-webshop-product',
        MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE,
        { productId, cashShopCmsId: cashShopCms.id }
      );
    }

    user.userContentsTerms.ensureContentsTerms(cashShopCms.contentsTerms, user);

    const curDate = new Date(curTimeUtc * 1000);
    const reason = user.userCashShop.isBuyableProduct(
      user,
      cashShopCms.id,
      curDate,
      user.userCashShop.getExpiredRestrictedProducts(curTimeUtc),
      1,
      productId
    );
    if (reason !== UNBUYABLE_REASON.BUYABLE) {
      throw new MError(
        `can-not-buy-cash-shop-biiling-product, reason: ${UNBUYABLE_REASON[reason]}`,
        MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE,
        {
          productId,
          curTimeUtc,
          restrictedProduct: user.userCashShop.getRestrictedProducts()[cashShopCms.id],
        }
      );
    }

    return Promise.resolve()
      .then(() => {
        // 보관함에 많이 안 쌓이도록.
        return mhttp.lgbillingd
          .queryInventoryPurchaseList(user.userId.toString())
          .then((billingApiResp) => {
            if (billingApiResp.success !== true) {
              if (billingApiResp.errorCd === ERROR_CODE_NOT_EXIST_DATA) {
                // 보관함에 아무것도 없을 때도 위 에러가 나온다.
                return null;
              }
              throw new BillingApiFailError('QUERY_INVEN_PURCHASE_LIST', billingApiResp);
            }

            const invenPurchases = billingApiResp.data as any;
            if (!invenPurchases) {
              return null;
            }
            if (!Array.isArray(invenPurchases)) {
              throw new MError(
                'query-inven-purchase-resp-data-array-expected',
                MErrorCode.BILLING_API_UNEXPECTED_RESP,
                { billingApiResp }
              );
            }

            // 빌링 보관함에서 제공하는 개수와 무관한, 게임 서버에서의 제한
            const bEnoughSpace = invenPurchases.length < cms.Define.BillingInventorySlotSize;
            if (!bEnoughSpace) {
              throw new MError(
                'billing-inventory-not-enough-slot',
                MErrorCode.INVALID_REQ_BODY_BILLING_RESERVE_PURCHASE,
                { billingApiResp, slotSize: cms.Define.MaxBillingInventorySize }
              );
            }

            return null;
          });
      })
      .then(() => {
        // 등록된 개별 품목들이 지급 가능한 것인지 확인해본다.
        return mhttp.lgbillingd
          .queryProductGiveItemDetail(user.storeCode, productId)
          .then((billingApiResp) => {
            if (billingApiResp.success !== true) {
              throw new BillingApiFailError('QUERY_PRODUCT_GIVE_ITEM', billingApiResp);
            }

            const giveItemList = (billingApiResp.data as any).giveItemList;
            ensureGiveItems(giveItemList);
            for (const elem of giveItemList) {
              if (elem.coinManageBalanceYn === 'Y') {
                continue;
              }
              const ret = BillingUtil.buildEnsuredGiveItem(elem);
              if (ret.bOk !== true) {
                throw new MError(
                  `failed to ensure give item(${BillingUtil.giveItemToString(elem)}). reason: ${
                    ret.err?.reason
                  }.`,
                  MErrorCode.BILLING_API_UNEXPECTED_GIVE_ITEM,
                  { billingApiResp, elem }
                );
              }
            }
          });
      })
      .then(() => {
        return mhttp.lgbillingd.reservePurchase(
          user.userId.toString(),
          userConn.getIp(),
          productId,
          user.accountId,
          user.storeCode,
          price,
          currency,
          user.appVersion,
          user.deviceType.toUpperCase(),
          user.countryCreated,
          microPrice
        );
      })
      .then((billingApiResp) => {
        if (billingApiResp.success === true) {
          // 빌링 API 에서 인자들의 타입(RequestBody) 및 유효성이 검증됐다고 가정.
          user.glog('common_prepurchase', {
            pn: productId,
            spn: reqBody.productName ?? null,
            pr: Number(price),
            currency: currency,
            os: user.deviceType.toUpperCase(),
            osv: user.osv ?? null,
            lang: user.deviceLang ?? null,
            lang_game: user.lineLangCultre ?? null,
            sk: user.storeCode,
            country_ip: user.countryIp ?? null,
          });
        }

        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          step: 'RESERVE_PURCHASE',
          billingApiResp,
        });
      })
      .catch((err) => {
        if (err instanceof BillingApiFailError) {
          mlog.warn('[BillingReservePurchase] billing-api-failed', {
            userId: user.userId,
            step: err.step,
            resp: err.resp,
          });

          // 빌링 API success 가 false 인 경우, resp 를 그대로 클라로 전송하도록 한다.
          // 클라에서 빌링 에러를 알 수 있도록함.
          return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
            step: err.step,
            billingApiResp: err.resp,
          });
        }

        throw err;
      });
  }
}
