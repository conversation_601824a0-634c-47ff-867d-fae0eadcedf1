"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TownPerfmonManager = void 0;
//import _ from 'lodash';
const typedi_1 = __importStar(require("typedi"));
const packetPerfmon_1 = require("../motiflib/packetPerfmon");
const ioPerfmon_1 = require("../motiflib/ioPerfmon");
const queryPerfmon_1 = require("../mysqllib/queryPerfmon");
const tickPerfmon_1 = require("../motiflib/tickPerfmon");
const mconf_1 = __importDefault(require("../motiflib/mconf"));
// ----------------------------------------------------------------------------
// TownPerfmonManager object.
// ----------------------------------------------------------------------------
let TownPerfmonManager = class TownPerfmonManager {
    //---------------------------------------------------
    startPerfmonTick() {
        const packetPerfmon = typedi_1.default.get(packetPerfmon_1.PacketPerfmon);
        if (packetPerfmon.isActivable(mconf_1.default)) {
            packetPerfmon.isActivated = true;
            this._perfmonPacketInterval = setInterval(() => {
                packetPerfmon.flushPacket();
            }, mconf_1.default.perfmon.packet.interval * 1000); //msec
        }
        const ioPerfmon = typedi_1.default.get(ioPerfmon_1.IOPerfmon);
        if (ioPerfmon.isActivable(mconf_1.default)) {
            ioPerfmon.isActivated = true;
            this._perfmonIOInterval = setInterval(() => {
                ioPerfmon.flushIO();
            }, mconf_1.default.perfmon.io.interval * 1000); //msec
        }
        const queryPerfmon = typedi_1.default.get(queryPerfmon_1.QueryPerfmon);
        if (queryPerfmon.isActivable(mconf_1.default)) {
            queryPerfmon.isActivated = true;
            this._perfmonQueryInterval = setInterval(() => {
                queryPerfmon.flushQuery();
            }, mconf_1.default.perfmon.query.interval * 1000); //msec
        }
        const tickPerfmon = typedi_1.default.get(tickPerfmon_1.TickPerfmon);
        if (tickPerfmon.isActivable(mconf_1.default)) {
            tickPerfmon.isActivated = true;
            this._perfmonTickInterval = setInterval(() => {
                tickPerfmon.flushTick();
            }, mconf_1.default.perfmon.tick.interval * 1000); //msec
        }
    }
    //---------------------------------------------------
    stopPerfmonTick() {
        if (this._perfmonPacketInterval) {
            clearInterval(this._perfmonPacketInterval);
        }
        if (this._perfmonIOInterval) {
            clearInterval(this._perfmonIOInterval);
        }
        if (this._perfmonQueryInterval) {
            clearInterval(this._perfmonQueryInterval);
        }
        if (this._perfmonTickInterval) {
            clearInterval(this._perfmonTickInterval);
        }
        const packetPerfmon = typedi_1.default.get(packetPerfmon_1.PacketPerfmon);
        packetPerfmon.isActivated = false;
        const ioPerfmon = typedi_1.default.get(ioPerfmon_1.IOPerfmon);
        ioPerfmon.isActivated = false;
        const queryPerfmon = typedi_1.default.get(queryPerfmon_1.QueryPerfmon);
        queryPerfmon.isActivated = false;
        const tickPerfmon = typedi_1.default.get(tickPerfmon_1.TickPerfmon);
        tickPerfmon.isActivated = false;
    }
};
TownPerfmonManager = __decorate([
    (0, typedi_1.Service)()
], TownPerfmonManager);
exports.TownPerfmonManager = TownPerfmonManager;
//# sourceMappingURL=townPerfmonManager.js.map