// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MError, MErrorCode } from '../../motiflib/merror';

export const spName = 'mp_u_user_load2';
export const errorCode = MErrorCode.USER_LOAD2_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export interface Result {
  tutorialCrazeTradeEvents: {
    tutorialTradeEventCmsId: number;
    townCmsId: number;
    usedBudget: number;
    isActive: number;
    isQuestPaused: number;
  }[];

  manufactureExpLevel: {
    castingExp: number;
    castingLevel: number;
    cookingExp: number;
    cookingLevel: number;
    sewingExp: number;
    sewingLevel: number;
    handmadeExp: number;
    handmadeLevel: number;
    medicineExp: number;
    medicineLevel: number;
  };

  manufactureRoomInfo: {
    [roomCmsId: number]: {
      [slot: number]: {
        slot: number;
        recipeId: number;
        startTimeUtc: number;
        completionTimeUtc: number;
        resultType: number;
        mateCmsIds: number[];
        successRate: number;
        greatSuccessRate: number;
        extra: string;
      }
    }
  };

  unlockedRecipes: {
    [recipeCmsId: number]: boolean;
  };
}

export default function (
  connection: query.Connection,
  userId: number,
  curTimeUtc: number
): Promise<Result> {
  return spFunction(connection, userId, curTimeUtc)
    .then((qr) => {
      const result: Result = {
        tutorialCrazeTradeEvents: [],
        manufactureExpLevel: {
          castingExp: 0,
          castingLevel: 1,
          cookingExp: 0,
          cookingLevel: 1,
          sewingExp: 0,
          sewingLevel: 1,
          handmadeExp: 0,
          handmadeLevel: 1,
          medicineExp: 0,
          medicineLevel: 1,
        },
        manufactureRoomInfo: {},
        unlockedRecipes: {},
      };

      const rows = qr.rows;
      let rowIndex = 0;
      result.tutorialCrazeTradeEvents = rows[rowIndex];

      rowIndex += 1;
      if (rows[rowIndex].length > 0) {
        result.manufactureExpLevel = rows[rowIndex][0];
      }

      rowIndex += 1;
      if (rows[rowIndex] && rows[rowIndex].length > 0) {
        // Convert flat rows to nested structure
        const roomInfo: { [roomCmsId: number]: { [slot: number]: any } } = {};
        rows[rowIndex].forEach((row: any) => {
          const roomCmsId = row.roomCmsId;
          const slot = row.slot;
          if (!roomInfo[roomCmsId]) {
            roomInfo[roomCmsId] = {};
          }
          // startTimeUtc and completionTimeUtc are already timestamps from UNIX_TIMESTAMP()
          const startTimeUtc = row.startTimeUtc || 0;
          const completionTimeUtc = row.completionTimeUtc || 0;

          roomInfo[roomCmsId][slot] = {
            slot: row.slot,
            recipeId: row.recipeId,
            startTimeUtc,
            completionTimeUtc,
            progressType: row.progressType || 0,
            mateCmsIds: row.mateCmsIds ? JSON.parse(row.mateCmsIds) : [],
            successRate: row.successRate || 0,
            greatSuccessRate: row.greatSuccessRate || 0,
            extra: row.extra ? JSON.parse(row.extra) : null,
          };
        });
        result.manufactureRoomInfo = roomInfo;
      }

      rowIndex += 1;
      if (rows[rowIndex] && rows[rowIndex].length > 0) {
        // Convert flat rows to unlocked recipes object
        const unlockedRecipes: { [recipeCmsId: number]: boolean } = {};
        rows[rowIndex].forEach((row: any) => {
          unlockedRecipes[row.recipeCmsId] = true;
        });
        result.unlockedRecipes = unlockedRecipes;
      }

      return result;
    })
    .catch(catchHandler);
}
