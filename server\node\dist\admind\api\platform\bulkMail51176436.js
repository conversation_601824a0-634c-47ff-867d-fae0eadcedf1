"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const lodash_1 = __importDefault(require("lodash"));
const typedi_1 = __importDefault(require("typedi"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const server_1 = require("../../server");
module.exports = (req, res) => {
    mlog_1.default.info('[RX] /platform/bulkMail', { body: req.body });
    const { gameServerId: worldId, mailDataList, configId } = req.body;
    if (!mailDataList || !lodash_1.default.isArray(mailDataList) || mailDataList.length === 0) {
        return res.status(400).json({
            isSuccess: false,
            msg: 'Invalid mailDataList',
            errorCd: 'INVALID_PARAMETER',
        });
    }
    const { userRedises } = typedi_1.default.get(server_1.AdminService);
    const userRedis = userRedises[worldId];
    if (!userRedis) {
        return res.status(400).json({
            isSuccess: false,
            msg: 'Invalid gameServerId',
            errorCd: 'INVALID_PARAMETER',
        });
    }
    // 요청 큐잉
    return userRedis['addBulkMail'](configId, JSON.stringify(mailDataList)).then(() => {
        const resp = {
            isSuccess: true,
            msg: 'request success',
        };
        mlog_1.default.info('[TX] /platform/bulkMail', { body: resp });
        return res.json(resp);
    });
};
//# sourceMappingURL=bulkMail51176436.js.map