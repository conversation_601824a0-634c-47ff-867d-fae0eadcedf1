{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755831950,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755831950,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T03:05:50.875Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755831950,"afterTs":1755831950,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T03:05:50.879Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755832550,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755832550,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T03:15:50.875Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755832550,"afterTs":1755832550,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T03:15:50.876Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755833150,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755833150,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T03:25:50.875Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755833150,"afterTs":1755833150,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T03:25:50.876Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:29:59.007Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.896","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:29:59.008Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T03:29:59.540Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.183","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:29:59.541Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T03:30:00.423Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T03:30:00.423Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T03:30:00.871Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T03:30:00.873Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T03:30:00.874Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:30:00.876Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:30:00.876Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T03:30:00.876Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:30:00.877Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:30:00.877Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T03:30:00.877Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T03:30:00.877Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:30:00.879Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:30:00.879Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:30:00.879Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:30:00.879Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:30:00.880Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:30:00.880Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T03:30:07.418Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T03:30:42.136Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T03:30:42.139Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T03:30:42.295Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T03:30:42.296Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T03:30:42.297Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T03:30:42.298Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T03:30:42.307Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T03:30:42.307Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T03:30:42.315Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T03:30:42.316Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T03:30:42.325Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.331Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.362Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.369Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.377Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.384Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.391Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.399Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.406Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.413Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:30:42.426Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T03:30:42.442Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T03:30:42.444Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:30:42.673Z"}
{"url":"/unregisterServerd","status":"200","response-time":"77.474","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:30:42.674Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755833442,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T03:30:42.676Z"}
{"url":"/registerServerd","status":"200","response-time":"1.894","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:30:42.677Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T03:30:43.171Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.657","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:30:43.171Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755833443,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T03:30:43.172Z"}
{"url":"/registerServerd","status":"200","response-time":"0.883","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:30:43.173Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T03:30:52.446Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:34:43.080Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.032","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:34:43.080Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T03:34:43.597Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T03:34:43.598Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T03:34:44.340Z"}
{"url":"/unregisterServerd","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:34:44.342Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T03:34:44.342Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T03:34:44.343Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T03:34:44.343Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:34:44.344Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:34:44.344Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:34:44.344Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:34:44.344Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T03:34:44.344Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:34:44.344Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:34:44.344Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T03:34:44.345Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T03:34:44.345Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:34:44.345Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:34:44.345Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:34:44.345Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:34:44.345Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T03:34:49.104Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T03:35:23.122Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T03:35:23.124Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T03:35:23.317Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T03:35:23.318Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T03:35:23.319Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T03:35:23.321Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T03:35:23.329Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T03:35:23.329Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T03:35:23.338Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T03:35:23.338Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T03:35:23.345Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.351Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.378Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.388Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.395Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.404Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.412Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.420Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.428Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.435Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:35:23.446Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T03:35:23.456Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T03:35:23.458Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T03:35:23.700Z"}
{"url":"/unregisterServerd","status":"200","response-time":"53.003","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:35:23.701Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755833723,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T03:35:23.703Z"}
{"url":"/registerServerd","status":"200","response-time":"1.633","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:35:23.703Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:35:23.923Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.199","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:35:23.924Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755833723,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T03:35:23.927Z"}
{"url":"/registerServerd","status":"200","response-time":"1.837","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:35:23.927Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T03:35:33.460Z"}
{"url":"/findTown","status":"200","response-time":"1.134","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:36:23.847Z"}
{"url":"/createZone","status":"200","response-time":"1.135","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:36:23.909Z"}
{"url":"/incUserCount","status":"200","response-time":"1.271","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:36:23.914Z"}
{"url":"/decUserCount","status":"200","response-time":"1.099","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:38:27.053Z"}
{"url":"/findTown","status":"200","response-time":"0.680","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:39:01.310Z"}
{"url":"/incUserCount","status":"200","response-time":"0.556","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:39:01.313Z"}
{"url":"/decUserCount","status":"200","response-time":"0.718","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:41:22.119Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:42:52.560Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.995","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:42:52.560Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T03:42:52.865Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.302","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:42:52.866Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T03:42:53.944Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T03:42:53.944Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T03:42:54.362Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T03:42:54.362Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T03:42:54.363Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T03:42:54.364Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:42:54.364Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:42:54.364Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:42:54.364Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:42:54.364Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T03:42:54.365Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T03:42:54.365Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:42:54.365Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:42:54.365Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:42:54.365Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:42:54.365Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T03:42:54.366Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T03:42:54.366Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T03:42:59.050Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T03:43:29.030Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T03:43:29.031Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T03:43:29.184Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T03:43:29.185Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T03:43:29.185Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T03:43:29.187Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T03:43:29.195Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T03:43:29.195Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T03:43:29.204Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T03:43:29.204Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T03:43:29.213Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.220Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.246Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.258Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.266Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.275Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.283Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.291Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.298Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.307Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T03:43:29.318Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T03:43:29.330Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T03:43:29.331Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T03:43:29.686Z"}
{"url":"/unregisterServerd","status":"200","response-time":"86.242","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:29.687Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755834209,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T03:43:29.689Z"}
{"url":"/registerServerd","status":"200","response-time":"1.990","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:29.690Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:43:30.081Z"}
{"url":"/unregisterServerd","status":"200","response-time":"2.134","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:30.081Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"warn","message":"[updateServerdPing] serverd[OCEAN] is not online.","timestamp":"2025-08-22T03:43:30.081Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755834210,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T03:43:30.083Z"}
{"url":"/registerServerd","status":"200","response-time":"0.838","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:30.083Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:43:30.085Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.819","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:30.085Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T03:43:39.333Z"}
{"url":"/findTown","status":"200","response-time":"0.811","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:51.000Z"}
{"url":"/createZone","status":"200","response-time":"0.800","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:51.063Z"}
{"url":"/incUserCount","status":"200","response-time":"0.686","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:43:51.069Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T03:44:04.847Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.101","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:44:04.848Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755834244,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T03:44:04.849Z"}
{"url":"/registerServerd","status":"200","response-time":"0.923","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T03:44:04.850Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755834818,"worldId":"UWO-GL-01","userCount":1,"botCount":0},"curTs":1755834818,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T03:53:38.752Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755834818,"afterTs":1755834818,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T03:53:38.753Z"}
