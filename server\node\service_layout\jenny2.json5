{
  // ----------------------------------------------------
  // Configurations shared by different 'xxxd' services.
  // ----------------------------------------------------

  sharedConfig: {
    maxUsersPerChannel: 40,
    amqp: 'amqp://localhost',
    slackNotify: {
      history: {
        file: 'slackNotifyHistory.json',
        duration: 300,
      },
      username: 'jenny2',
      url: '*****************************************************************************',
    },
    gameLog: {
      FGT: true,
    },

    platform: 1, // motiflib/model/auth/enum.ts 에 PLATFORM

    LineGameCode: 'UWO',

    binaryCode: 'KR',

    userHeartBeatInterval: 10, //유저의 heart beat 주기(sec)
    offlineSailingHeartBeatInterval: 10, //offlineSailing heart beat 주기(sec)

    authPubsubRedis: {
      host: 'localhost',
      port: 6379,
    },

    // 서버 모니터링 Redis.
    monitorRedis: {
      redisCfg: {
        host: 'localhost',
        port: 6379,
        db: 3,
      },
      scriptDir: 'monitor',
      pool: {
        min: 1,
        max: 4,
      },
    },

    // 대기열 Redis.
    orderRedis: {
      redisCfg: {
        host: 'localhost',
        port: 6379,
        db: 18,
      },
      scriptDir: 'order',
      pool: {
        min: 1,
        max: 4,
      },
    },

    // 서버 군에 속한 모든 서버를 대상으로 pubsub redis (lobbyd 사용)
    globalPubsubRedis: {
      host: 'localhost',
      port: 6379,
    },

    // 인증 Redis.
    authRedis: {
      redisCfg: {
        host: 'localhost',
        port: 6379,
        db: 19,
      },
      scriptDir: 'auth',
      pool: {
        min: 1,
        max: 4,
      },
    },

    // 변경 시 migrateRdb.ts 같이 수정해주어야 됨
    mysqlAuthDb: {
      host: 'localhost',
      port: 3306,
      user: 'motif_dev',
      password: 'dev123$',
      database: 'uwo_auth',
      multipleStatements: true,
      supportBigNumbers: true,
      bigNumberStrings: true,
      connectTimeout: 3000,
      connectionLimit: 10,
      flags: '-FOUND_ROWS',
      driver: 'mysql',
    },

    userCacheRedis: {
      redisCfg: {
        host: 'localhost',
        port: 6379,
        db: 13,
      },
      scriptDir: 'userCache',
    },

    // 매칭 Redis(lobbyd 사용)
    globalMatchRedis: {
      redisCfg: {
        host: 'localhost',
        port: 6379,
        db: 23,
      },
      pool: {
        min: 1,
        max: 4,
      },
      scriptDir: 'globalMatch',
    },

    // 전투 로그 Redis.
    globalBattleLogRedis: {
      redisCfg: {
        host: 'localhost',
        port: 6379,
        db: 24,
      },
      scriptDir: 'battleLog',
      pool: {
        min: 1,
        max: 4,
      },
    },

    // 전투 검증을 위한 mongoDB.
    battleLogMongoDb: {
      url: 'mongodb://localhost:27017',
      // https://mongoosejs.com/docs/connections.html#options 참고
      connOptions: {
        dbName: 'uwo_judge',
        // 최대 5개.
        poolSize: 5,
        useNewUrlParser: true,
      },
    },

    // 데몬 등록 이벤트 notification (configd 에서 layout 정보를 받는 모든 서버들이 사용)
    configPubsubRedis: {
      host: 'localhost',
      port: 6379,
    },

    http: {
      authd: {
        url: 'http://localhost:10700',
      },
      lgd: {
        // linegames api server (dev)
        url: 'https://dev-cf-api-integ.line.games',
        authPwd: 'beta_eogkdgotleo_roakr#5', // DEV

        // "api": "qa-cf-api-integ.line.games"		// QA
        // "authPwd": "qa_eokdgofhrkwmdk_rnt!2"       // QA

        // "api": "cbt-cf-api-integ.line.games"	  // CBT
      },
      navid: {
        url: 'http://************:34568',
      },
      lglogd: {
        url: 'https://beta-cf-api-integ.line.games', // DEV
        authPwd: 'beta_eogkdgotleo_roakr#5', // DEV
      },
    },

    enterWorldTokenExpireSec: 5400,

    // 월드당 최대 입장 허용 유저수
    maxUsersPerWorld: 5000,

    // 스트레스 테스트시 적용할 설정들 (개발 환경(DEV)에서만 동작함)
    stressTest: {
      // 스트레스 테스트 설정 적용하는 경우 true
      enabled: false,

      // 테스트용 공관 투자 정산 주기(분)
      townInvestCloseTime: 30, // 매시 30분에 정산
    },

    // 로그에 출력되는 패킷 내용 간소화 여부.
    // false 설정시, 로그 용량이 커지니 주의.
    bShrinkPacketLog: true,

    // 대기열 작동 시작 기준 월드단위 동접률(백분률)
    orderIdCheckWorldUserRate: 80, // (ex. 최대유저의 80% = 5000*0.8 = 4000)

    // 대기열 작동시 초당 최대 입장 허용 인원
    maxAllowOrderIdPerSec: 10,

    // enter world token 갱신 주기
    enterWorldTokenRefreshmentIntervalSec: 300, // 300 seconds

    // 프롤로그 상태 타임아웃(초)
    prologueGnidTimeout: 90, // 90 seconds

    // 총리 선거 초기 세션값 (존재할 경우 적용됨. 없을 경우 기본값 사용)
    nationElectionInitSessionId: 2800,

    // 총리 선서 득표수 캐싱 갱신 주기(초)
    AcquiredVotesRefreshIntervalTime: 1, // 1 seconds
  },

  // ----------------------------------------------------
  // World 목록 + 설정.
  // ----------------------------------------------------

  world: {
    worlds: [
      {
        id: 'UWO-GL-01',

        address: 'jenny2.motifgames.in',
        port: 10100,

        disabled: false,

        bIsNonPK: false,

        // timezone. 정수만 사용해야 됨. 실수 사용해야 될 경우 코드 수정 필요함.
        timezone: 9, // ex) seoul 9, london 0

        countryCode: 0, // motiflib/const.ts 에 정의되어 있음.

        // 서버 열린 날짜
        createDate: '2023-01-16',

        http: {
          saild: {
            url: 'http://localhost:11100',
          },
          zonelbd: {
            url: 'http://localhost:10600',
          },
          realmd: {
            url: 'http://localhost:10900',
          },
          chatd: {
            // 채팅
            url: 'https://dev-volante-chat-api.line.games',
            salt: 'AC65F7D6D0A1D843E9DB6AF855CA',
          },
          lgbillingd: {
            url: 'https://beta-api.billing.line.games', // DEV
            authPwd: 'UWO_beta_eoqkrqlqjs@34$', // DEV
          },
          lgpayd: {
            url: 'https://beta-pay.billing.line.games', // DEV
            authPwd: 'UWO_beta_payeoqkrqlqjs@34$', // DEV
          },
        },

        // 변경 시 migrateRdb.ts 같이 수정해주어야 됨
        mysqlUserDb: {
          shardFunction: 'userDbShardDev', // or userDbShardLive
          sqlDefaultCfg: {
            host: 'localhost',
            port: 3306,
            user: 'motif_dev',
            password: 'dev123$',
            database: 'uwo_user',
            multipleStatements: true,
            supportBigNumbers: true,
            bigNumberStrings: true,
            connectTimeout: 3000,
            connectionLimit: 10,
            flags: '-FOUND_ROWS',
            driver: 'mysql',
          },
          shards: [
            {
              shardId: 0,
              sqlCfg: {
                database: 'uwo_user_00',
              },
            },
            {
              shardId: 1,
              sqlCfg: {
                database: 'uwo_user_01',
              },
            },
          ],
        },

        // 월드 DB (lobbyd, realmd 사용)
        // 변경 시 migrateRdb.ts 같이 수정해주어야 됨
        mysqlWorldDb: {
          host: 'localhost',
          port: 3306,
          user: 'motif_dev',
          password: 'dev123$',
          database: 'uwo_world',
          multipleStatements: true,
          supportBigNumbers: true,
          bigNumberStrings: true,
          connectTimeout: 3000,
          connectionLimit: 10,
          flags: '-FOUND_ROWS',
          driver: 'mysql',
        },

        worldPubsubRedis: {
          host: 'localhost',
          port: 6379,
        },

        // 길드 pubsub redis (lobbyd 사용)
        guildPubsubRedis: {
          host: 'localhost',
          port: 6379,
        },

        townRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 11,
          },
          scriptDir: 'town',
        },

        nationRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 10,
          },
          scriptDir: 'nation',
        },

        collectorRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 4,
          },
          scriptDir: 'collector',
        },

        // 항해 정보 Redis.
        sailRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 15,
          },
          scriptDir: 'sail',
          pool: {
            min: 1,
            max: 4,
          },
        },

        // 거래소 Redis.
        auctionRedis: {
          scriptDir: 'auction',
          shards: [
            {
              auctionCategory: 1,
              redisCfg: {
                host: 'localhost',
                port: 6379,
                db: 2,
              },
              pool: {
                min: 1,
                max: 4,
              },
            },
            {
              auctionCategory: 2,
              redisCfg: {
                host: 'localhost',
                port: 6379,
                db: 2,
              },
              pool: {
                min: 1,
                max: 4,
              },
            },
            {
              auctionCategory: 3,
              redisCfg: {
                host: 'localhost',
                port: 6379,
                db: 2,
              },
              pool: {
                min: 1,
                max: 4,
              },
            },
            {
              auctionCategory: 4,
              redisCfg: {
                host: 'localhost',
                port: 6379,
                db: 2,
              },
              pool: {
                min: 1,
                max: 4,
              },
            },
            {
              auctionCategory: 5,
              redisCfg: {
                host: 'localhost',
                port: 6379,
                db: 2,
              },
              pool: {
                min: 1,
                max: 4,
              },
            },
            {
              auctionCategory: 6,
              redisCfg: {
                host: 'localhost',
                port: 6379,
                db: 2,
              },
              pool: {
                min: 1,
                max: 4,
              },
            },
          ],
        },

        // guild 정보 Redis.
        guildRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 16,
          },
          scriptDir: 'guild',
          pool: {
            min: 1,
            max: 4,
          },
        },

        // 모의전(아레나) Redis
        arenaRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 17,
          },
          scriptDir: 'arena',
          pool: {
            min: 1,
            max: 4,
          },
        },

        // 레이드 Redis.
        raidRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 20,
          },
          scriptDir: 'raid',
          pool: {
            min: 1,
            max: 4,
          },
        },

        // 랭킹 Redis.
        rankingRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 21,
          },
          scriptDir: 'ranking',
          pool: {
            min: 1,
            max: 4,
          },
        },

        userRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 9,
          },
          scriptDir: 'user',
        },

        // townd 로드밸런싱 정보 redis.
        townLbRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 14,
          },

          pool: {
            min: 2,
            max: 4,
          },
          scriptDir: 'zoneLb',
        },

        // oceand 로드밸런싱 정보 redis.
        oceanLbRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 5,
          },

          pool: {
            min: 2,
            max: 2,
          },
          scriptDir: 'zoneLb',
        },

        // 블라인드 입찰 redis.
        blindBidRedis: {
          redisCfg: {
            host: 'localhost',
            port: 6379,
            db: 22,
          },

          pool: {
            min: 1,
            max: 4,
          },
          scriptDir: 'blindBid',
        },

        clientVolanteUrl: '',
      }, // "worldId": "UWO-GL-01"
    ],
  },

  // ----------------------------------------------------
  // configd service configurations
  // ----------------------------------------------------
  configd: {
    // configd 공용 설정.
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },

      configRedis: {
        redisCfg: {
          host: 'localhost',
          port: 6379,
          db: 1,
        },

        pool: {
          min: 1,
          max: 4,
        },
        scriptDir: 'config',
      },
    }, // common
  },

  // ----------------------------------------------------
  // authd service configurations
  // ----------------------------------------------------

  authd: {
    // authd 공용 설정.
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },

      // motiflib/model/auth/enum.ts ACCOUNT_ACCESS_LEVEL 에 정의되어 있음
      // 0: 기본 유저, 100: admin 최소 권한, 127: admin 최대 권한.
      // production 환경에서는 해당 값을 사용하지 않고 항상 0으로 유저를 생성한다.
      defaultAccessLevel: 127,

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // 클라이언트 요청 처리.
        publicApiService: {
          bindAddress: '0.0.0.0',
          port: 10701,
        },

        // 타 서버들 요청 처리.
        privateApiService: {
          bindAddress: '0.0.0.0',
          port: 10700,
          url: 'http://__HOSTNAME__:10700',
        },
      },

      // mysql 커넥션 요청큐의 최대값
      // (이 값을 넘어서는 요청이 쌓이면 이후 로그인 요청에 대해 '서버가 바쁩니다' 응답처리)
      mysqlReqRepLimit: 100,
    }, // common
  }, // authd

  // ----------------------------------------------------
  // lobbyd service configurations
  // ----------------------------------------------------

  lobbyd: {
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },

      // 패킷 payload 최대 사이즈
      maximumPayloadQueueSize: 10,

      // 로그인 타임아웃 설정 (소켓 연결 후, 로그인 까지 소요되는 시간)
      loginTimeout: 8000, // 8 seconds

      // 전투검증 필요 여부를 판단하는 설정.
      battleAudit: {
        // 활성화 여부.
        active: false,

        // 전투력이 N 퍼센트 이상 차이나는 대상을 이기는 경우.
        // 기본값: 10
        // e.g. 아군 120, 적군 150 이면, FLOOR(150 - 120 / 120) = 25
        combatPowerDiffPct: 10,

        // 전투력 차이 퍼센티지에 의해 결정되는, 예상되는 최소 턴수를 결정하는 변수.
        // 기본값: 0.08
        // e.g. 전투력 차이가 30% 인 경우를 가정하면,
        //  minTurnFactor |     최소턴수
        //     0.08       | ROUND(30 * 0.08) = 2
        //     0.10       | ROUND(30 * 0.10) = 3
        //     0.12       | ROUND(30 * 0.12) = 4
        //     0.08       | ROUND(30 * 0.08) = 3
        //     0.10       | ROUND(30 * 0.10) = 4
        //     0.12       | ROUND(30 * 0.12) = 4
        minTurnFactor: 0.08,

        // 액션당 최소 소요 예상되는 초단위 시간.
        // 기본값: 0.8
        minActionTimeSec: 0.8,

        // 전투당 획득 가능한 전투오브젝트 최대수.
        // 기본값: 10
        maxRewardBmos: 10,

        // 특정 시간 안에 전투 취소를 너무 자주 하는 경우 경고 로그를 남기도록. (1시간 기준)
        // 기본값: 3 (한시간 안에 3번 이상 "유저에 의한 취소"를 하면, alert 로그 발생)
        alertCancelsPerHour: 3,
      },

      // ping 타임아웃 설정
      ping: {
        // 핑 주기
        interval: 2000, // 2 seconds

        // 타임아웃
        timeout: 600000, // 10 minutes

        // 로딩시 타임아웃 (클라 로딩중)
        timeoutInLoading: 60000, // 60 seconds

        // 클라이언트 백그라운드 상태 시 타임아웃
        timeoutOnClientBackground: 10000, // 10 seconds

        // 에디터로 플레이시 타임아웃
        editorTimeout: 600000, // 10 minutes
      },

      // 유저 틱 설정
      userTick: {
        // 틱 주기
        interval: 1000, // 1 second

        // 서버의 틱 수행시간이 길 경우, 경고를 로깅하는 기준.
        warningElapsedTime: 100, // 100 milliseconds

        // ping 체크 주기.
        pingPongIntervalSec: 5, // 5 seconds

        // 제거 될 예정.
        chinaUnder18PlayTimeIntervalSec: 5, // 5 seconds

        // line mail pooling 주기
        lineMailPoolingIntervalSec: 300, // 300 seconds

        // glog - login_out_save 저장 주기
        glogSaveIntervalSec: 300, // 300 seconds
      },

      battleLogRedis: {
        redisCfg: {
          host: 'localhost',
          port: 6379,
          db: 12,
        },
        scriptDir: 'battleLog',
      },

      // 통계 설정 (각 SortType 에서 사용하지 않을 우선순위는 주석처리하면 됨)
      perfmon: {
        packet: {
          packetRecvSortType: [
            'callCount',
            // "avgSize",
            // "maxSize",
            'avgDuration',
            //"maxDuration",
          ],
          PacketSentSortType: [
            'callCount',
            'avgSize',
            //"maxSize",
          ],
          interval: 120, // 주기(sec)
          limit: 10, // 카테고리별 추출수량
        },
        io: {
          interval: 120, // 주기(sec)
        },
        query: {
          querySortType: [
            'callCount',
            'avgDuration',
            //"maxDuration",
          ],
          interval: 120, // 주기(sec)
          limit: 10, // 카테고리별 추출수량
        },
        tick: {
          tickSortType: [
            'callCount',
            'avgDuration',
            //"maxDuration",
          ],
          interval: 120, // 주기(sec)
          limit: 10, // 카테고리별 추출수량
        },
      },

      // 전투 만료 시간.
      battleExpireSec: 21600, // (3600 seconds * 6 = 6 hours)

      // 전투 취소 cool time
      battleCancelCooldownSec: 180, // (3 min)

      // 상태 차이 오류 허용 시간. (msec)
      invalidGameStateTolerance: 1000,

      // 모의전 관련 캐싱 정보 DB저장 간격(sec)
      arenaTickSaveInterval: 60,

      // 게임가드 설정. 없는 경우 동작 게임가드 관련 동작 안함.
      // 설정이 있더라도, "active" 를 명시적으로 true 로 설정하지 않는 이상 동작 안함.
      gameGuard: {
        // 게임가드 서버 인증 활성화 여부. (기본 false)
        active: false,

        // 서버 인증 주기
        authCheckIntervalSec: 240, // (4 min)

        // 인증 실패시, 로깅후 접속 유지 여부 (기본 false)
        bGenerous: false,

        // 게임가드 처리 서버들 목록
        // (userId % 서버수) 로 샤딩 되어 처리 됨.
        ffidShards: ['http://localhost:11300'],
      },

      // 앱가드 설정. 없는 경우 동작 앱가드 관련 동작 안함.
      // 설정이 있더라도, "active" 를 명시적으로 true 로 설정하지 않는 이상 동작 안함.
      appGuard: {
        // 앱가드 서버 인증 활성화 여부. (기본 false)
        active: false,

        // 앱가드 라이센스 키
        androidLicenseKey: 'TAIBAQE=',
        iosLicenseKey: 'TAIBAQI=',

        // 서버 인증 주기.
        authCheckIntervalSec: 180, // (3 min)

        // 앱가드 인증 주소.
        authUrl: 'https://auth.appguard.co.kr/queryStatus',

        // 인증 실패시, 로깅후 접속 유지 여부 (기본 false)
        bGenerous: false,
      },

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // 월드 ID.
        worldId: '_DUMMY_', // 키만 사용. 각 서버장비의 config/local.json5 에 있는 worldId값으로 덮어씀

        // TCP socket listener.
        socketServer: {
          bindAddress: '0.0.0.0',
          port: 10100,
        },

        // Rest API 서비스. (HTTP)
        apiService: {
          bindAddress: '0.0.0.0',
          port: 10200,
          url: 'http://__HOSTNAME__:10200',
        },
      },
    }, // common
  }, // lobbyd

  // ----------------------------------------------------
  // oceand service configurations
  // ----------------------------------------------------

  oceand: {
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },

      zoneTick: {
        // 존들을 저장할 틱 슬롯 테이블의 크기
        maxZoneTickSlot: 10,
        interval: 1000, // 1 second
        warningElapsedTime: 100, // 100 milliseconds
      },

      visibility: {
        gridDimension: {
          x: 2,
          y: 3,
        },
        radius: 1,
      },

      // 통계 설정 (각 SortType 에서 사용하지 않을 우선순위는 주석처리하면 됨)
      perfmon: {
        packet: {
          packetRecvSortType: [
            'callCount',
            // "avgSize",
            // "maxSize",
            'avgDuration',
            //"maxDuration",
          ],
          PacketSentSortType: [
            'callCount',
            'avgSize',
            //"maxSize",
          ],
          interval: 120, // 주기(sec)
          limit: 10, // 카테고리별 추출수량
        },
        io: {
          interval: 120, // 주기(sec)
        },
        tick: {
          tickSortType: [
            'callCount',
            'avgDuration',
            //"maxDuration",
          ],
          interval: 120, // 주기(sec)
          limit: 10, // 카테고리별 추출수량
        },
      },

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        worldId: '_DUMMY_', // 키만 사용. 각 서버장비의 config/local.json5 에 있는 worldId값으로 덮어씀

        // API service.
        apiService: {
          bindAddress: '0.0.0.0',
          port: 10800,
          url: 'http://__HOSTNAME__:10800',

          // TCP 소켓 API service.
          tcpServer: {
            port: 10808,
            ip: '0.0.0.0',
          },
        },
      },
    }, // common
  }, // oceand

  // ----------------------------------------------------
  // saild service configurations
  // ----------------------------------------------------

  saild: {
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },

      // 오프라인 항해 틱 설정
      offlineSailingTick: {
        // (option)메니저 틱 주기
        managerTickInterval: 1000, // 1 second

        // (option)DB scan 틱 주기
        dbScanTickInterval: 1000, // 1 second

        // (option)job 틱 주기
        jobTickInterval: 1000, // 1 second
      },

      // (option)자동항해 DB테이블 스캔시 최대 레코드 제한(오프라인 항해용)
      dbAutoSailingScanLimit: 10,

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        worldId: '_DUMMY_', // 키만 사용. 각 서버장비의 config/local.json5 에 있는 worldId값으로 덮어씀

        apiService: {
          bindAddress: '0.0.0.0',
          port: 11100,
          url: 'http://__HOSTNAME__:11100',

          // TCP 소켓 API service.
          tcpServer: {
            port: 11109,
            ip: '0.0.0.0',
          },
        },
      },
    }, // common
  }, // saild

  // ----------------------------------------------------
  // townd service configurations
  // ----------------------------------------------------

  townd: {
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },
      visibility: {
        gridDimension: {
          x: 10,
          y: 8,
        },
        radius: 1,
        gridExtentScale: 4,
      },

      // 통계 설정 (각 SortType 에서 사용하지 않을 우선순위는 주석처리하면 됨)
      perfmon: {
        packet: {
          packetRecvSortType: [
            'callCount',
            // "avgSize",
            // "maxSize",
            'avgDuration',
            // "maxDuration",
          ],
          PacketSentSortType: [
            'callCount',
            'avgSize',
            // "maxSize",
          ],
          interval: 120, // 주기(sec)
          limit: 10, // 카테고리별 추출수량
        },
        io: {
          interval: 120, // 주기(sec)
        },
      },

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // 월드 ID.
        worldId: '_DUMMY_', // 키만 사용. 각 서버장비의 config/local.json5 에 있는 worldId값으로 덮어씀

        // Rest API service.
        apiService: {
          bindAddress: '0.0.0.0',
          port: 10500,
          url: 'http://__HOSTNAME__:10500',

          // TCP 소켓 API service.
          tcpServer: {
            port: 10508,
            ip: '0.0.0.0',
          },
        },
      },
    }, // common
  }, // townd

  // ----------------------------------------------------
  // zonelbd service configurations
  // ----------------------------------------------------

  zonelbd: {
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },

      ping: {
        // 핑 주기
        interval: 2000, // 2 seconds

        // 타임 아웃
        timeout: 10000, // 10 seconds
      },

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // 월드 ID.
        worldId: '_DUMMY_', // 키만 사용. 각 서버장비의 config/local.json5 에 있는 worldId값으로 덮어씀

        // Rest API service.
        apiService: {
          bindAddress: '0.0.0.0',
          port: 10600,
          url: 'http://__HOSTNAME__:10600',
        },
      },
    }, // common
  }, // zonelbd

  // ----------------------------------------------------
  // realmd service configurations
  // ----------------------------------------------------

  realmd: {
    common: {
      sentry: {
        dsn: 'https://<EMAIL>/8',
      },

      tickInterval: 60000, // 1 minutes.

      lobbydHealthCheck: {
        interval: 30000, // 30 seconds

        // 타임 아웃
        timeout: 60000, // 60 seconds
      },

      // 모의전 정산시 유저 스캔 크기
      arenaWeeklyUpdateScanLimit: 10000,

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // 월드 ID.
        worldId: '_DUMMY_', // 키만 사용. 각 서버장비의 config/local.json5 에 있는 worldId값으로 덮어씀

        // Rest API service.
        apiService: {
          bindAddress: '0.0.0.0',
          port: 10900,
          url: 'http://__HOSTNAME__:10900',
        },
      },
    }, // common
  }, // realmd

  // ----------------------------------------------------
  // admind service configurations
  // ----------------------------------------------------

  admind: {
    sentry: {
      //dsn": "https://<EMAIL>/8",
    },

    serverHealthCheck: {
      // 내부 체크 타임 아웃
      timeout: 1500, // 1500 ms
    },

    apiService: {
      bindAddress: '0.0.0.0',
      port: 11000,
      allowIp: [
        '********',
        '127.0.0.1',
        '***********',
        '***********',
        '***********',
        '***********',
        '***********',
      ], // 허용 ip. 사용하지 않을 경우 주석처리 하면 된다.
    },
  }, // admind
  // ----------------------------------------------------
  // judged service configurations
  // ----------------------------------------------------

  judged: {
    common: {
      sentry: {
        //dsn": "https://<EMAIL>/8",
      },

      loopDelayMax: 16000, // 검증 루프 지연 최대 tick. (16 second)

      // 상대경로 or 절대 경로.
      validationHome: 'home/uwo/battle_validation',
      validationPkg: 'home/uwo/battle_validation_pkg',

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // Rest API service.
        apiService: {
          bindAddress: '0.0.0.0',
          port: 11200,
          url: 'http://__HOSTNAME__:11200',
        },
      },
    }, // common
  }, // judged

  // ----------------------------------------------------
  // navid service configurations
  // ----------------------------------------------------

  navid: {
    common: {
      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // Rest API service.
        apiService: {
          port: 34568,
          //"url": "http://__HOSTNAME__:34568",
          url: 'http://************:34568',
        },
      },
    }, // common
  }, // navid

  // ----------------------------------------------------
  // ffid service configurations
  // ----------------------------------------------------

  ffid: {
    common: {
      sentry: {
        // dsn: 'https://<EMAIL>/8',
      },

      // Windows 서버의 경우에 동작하지 않음.
      gameGuard: {
        // 게임가드 폴더 위치. (뒤에 '/' 꼭 필요!)
        homeDir: '/home/<USER>/gameguard/',
      },

      // 각 서버가 configd에 등록할 정보의 메타 데이터(포멧 정보)로서 수정 불필요.
      instanceTemplate: {
        // Rest API service.
        apiService: {
          port: 11300,
          //"url": "http://__HOSTNAME__:11300",
          url: 'http://************:11300',
        },
      },
    }, // common
  }, // ffid
}
