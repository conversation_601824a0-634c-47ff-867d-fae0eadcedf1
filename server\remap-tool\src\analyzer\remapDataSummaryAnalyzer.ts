import fs from 'fs-extra';
import path from 'path';
import { parse } from 'csv-parse/sync';

export interface RemapRecord {
  seq: number;
  uwo_Gnid: string;
  uwo_Nid: string;
  uwogl_Gnid: string;
  uwogl_Nid: string;
  gameServerId: string;
}

export interface WorldDistribution {
  worldId: string;
  recordCount: number;
  uniqueAccounts: number;
  uniqueUsers: number;
  duplicateUsers: number;
  percentage: number;
}

export interface DuplicateInfo {
  type: 'seq' | 'account_user_pair' | 'target_account_user_pair';
  duplicates: Array<{
    value: string;
    records: RemapRecord[];
  }>;
}

export interface ValidationIssue {
  type: 'missing_field' | 'invalid_format' | 'empty_value';
  record: RemapRecord;
  field?: string;
  message: string;
}

export interface RemapSummaryResult {
  // 기본 통계
  totalRecords: number;
  uniqueAccounts: number;
  uniqueUsers: number;
  uniqueTargetAccounts: number;
  uniqueTargetUsers: number;
  
  // 월드별 분포
  worldDistribution: WorldDistribution[];
  
  // 중복 검사
  duplicates: DuplicateInfo[];
  
  // 검증 이슈
  validationIssues: ValidationIssue[];
  
  // 매핑 통계
  accountMappings: Array<{
    sourceAccount: string;
    targetAccount: string;
    userCount: number;
    worlds: string[];
  }>;
  
  // 원본 데이터
  records: RemapRecord[];
  
  // 분석 메타데이터
  analyzedAt: string;
  csvFile: string;
}

export class RemapDataSummaryAnalyzer {
  
  async analyzeCSV(csvFilePath: string): Promise<RemapSummaryResult> {
    console.log(`📄 CSV 파일 읽는 중: ${csvFilePath}`);

    // CSV 파일 읽기
    const csvContent = await fs.readFile(csvFilePath, 'utf-8');
    const records = this.parseCSV(csvContent);

    console.log(`📊 ${records.length}개 레코드 분석 중...`);

    // 기본 통계 계산
    const basicStats = this.calculateBasicStats(records);

    // 월드별 분포 계산
    const worldDistribution = this.calculateWorldDistribution(records);

    // 중복 검사
    const duplicates = this.findDuplicates(records);
    console.log(`🔍 ${duplicates.length}개 카테고리에서 중복 발견`);

    // 검증 이슈 확인
    const validationIssues = this.validateRecords(records);
    console.log(`⚠️ ${validationIssues.length}개 검증 문제 발견`);
    
    // 계정 매핑 분석
    const accountMappings = this.analyzeAccountMappings(records);
    
    return {
      ...basicStats,
      worldDistribution,
      duplicates,
      validationIssues,
      accountMappings,
      records,
      analyzedAt: new Date().toISOString(),
      csvFile: path.basename(csvFilePath)
    };
  }
  
  private parseCSV(csvContent: string): RemapRecord[] {
    try {
      const records = parse(csvContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true
      });
      
      return records.map((record: any) => ({
        seq: parseInt(record.seq) || 0,
        uwo_Gnid: record.uwo_Gnid?.toString().trim() || '',
        uwo_Nid: record.uwo_Nid?.toString().trim() || '',
        uwogl_Gnid: record.uwogl_Gnid?.toString().trim() || '',
        uwogl_Nid: record.uwogl_Nid?.toString().trim() || '',
        gameServerId: record.gameServerId?.toString().trim() || ''
      }));
    } catch (error) {
      throw new Error(`CSV parsing failed: ${error}`);
    }
  }
  
  private calculateBasicStats(records: RemapRecord[]) {
    const uniqueAccounts = new Set(records.map(r => r.uwo_Gnid)).size;
    const uniqueUsers = new Set(records.map(r => r.uwo_Nid)).size;
    const uniqueTargetAccounts = new Set(records.map(r => r.uwogl_Gnid)).size;
    const uniqueTargetUsers = new Set(records.map(r => r.uwogl_Nid)).size;
    
    return {
      totalRecords: records.length,
      uniqueAccounts,
      uniqueUsers,
      uniqueTargetAccounts,
      uniqueTargetUsers
    };
  }
  
  private calculateWorldDistribution(records: RemapRecord[]): WorldDistribution[] {
    const worldMap = new Map<string, {
      records: RemapRecord[];
      accounts: Set<string>;
      users: Set<string>;
      duplicateUsers: Set<string>;
    }>();
    
    // 월드별 데이터 수집
    records.forEach(record => {
      if (!worldMap.has(record.gameServerId)) {
        worldMap.set(record.gameServerId, {
          records: [],
          accounts: new Set(),
          users: new Set(),
          duplicateUsers: new Set()
        });
      }
      
      const worldData = worldMap.get(record.gameServerId)!;
      worldData.records.push(record);
      worldData.accounts.add(record.uwo_Gnid);
      worldData.users.add(record.uwo_Nid);
    });

    // 각 월드별로 중복 캐릭터 찾기
    worldMap.forEach((data) => {
      const userCounts = new Map<string, number>();
      data.records.forEach(record => {
        const key = `${record.uwo_Gnid}:${record.uwo_Nid}`;
        userCounts.set(key, (userCounts.get(key) || 0) + 1);
      });

      userCounts.forEach((count, key) => {
        if (count > 1) {
          const parts = key.split(':');
          if (parts.length >= 2 && parts[1]) {
            data.duplicateUsers.add(parts[1]);
          }
        }
      });
    });

    // 분포 계산
    const totalRecords = records.length;
    return Array.from(worldMap.entries()).map(([worldId, data]) => ({
      worldId,
      recordCount: data.records.length,
      uniqueAccounts: data.accounts.size,
      uniqueUsers: data.users.size,
      duplicateUsers: data.duplicateUsers.size,
      percentage: Math.round((data.records.length / totalRecords) * 100 * 100) / 100
    })).sort((a, b) => b.recordCount - a.recordCount);
  }
  
  private findDuplicates(records: RemapRecord[]): DuplicateInfo[] {
    const duplicates: DuplicateInfo[] = [];
    
    // seq 중복 검사
    const seqMap = new Map<number, RemapRecord[]>();
    records.forEach(record => {
      if (!seqMap.has(record.seq)) {
        seqMap.set(record.seq, []);
      }
      seqMap.get(record.seq)!.push(record);
    });
    
    const seqDuplicates = Array.from(seqMap.entries())
      .filter(([_, records]) => records.length > 1)
      .map(([seq, records]) => ({
        value: seq.toString(),
        records
      }));
    
    if (seqDuplicates.length > 0) {
      duplicates.push({
        type: 'seq',
        duplicates: seqDuplicates
      });
    }
    
    // (uwo_Gnid, uwo_Nid) 조합 중복 검사
    const sourceMap = new Map<string, RemapRecord[]>();
    records.forEach(record => {
      const key = `${record.uwo_Gnid}:${record.uwo_Nid}`;
      if (!sourceMap.has(key)) {
        sourceMap.set(key, []);
      }
      sourceMap.get(key)!.push(record);
    });
    
    const sourceDuplicates = Array.from(sourceMap.entries())
      .filter(([_, records]) => records.length > 1)
      .map(([key, records]) => ({
        value: key,
        records
      }));
    
    if (sourceDuplicates.length > 0) {
      duplicates.push({
        type: 'account_user_pair',
        duplicates: sourceDuplicates
      });
    }
    
    // (uwogl_Gnid, uwogl_Nid) 조합 중복 검사
    const targetMap = new Map<string, RemapRecord[]>();
    records.forEach(record => {
      const key = `${record.uwogl_Gnid}:${record.uwogl_Nid}`;
      if (!targetMap.has(key)) {
        targetMap.set(key, []);
      }
      targetMap.get(key)!.push(record);
    });
    
    const targetDuplicates = Array.from(targetMap.entries())
      .filter(([_, records]) => records.length > 1)
      .map(([key, records]) => ({
        value: key,
        records
      }));
    
    if (targetDuplicates.length > 0) {
      duplicates.push({
        type: 'target_account_user_pair',
        duplicates: targetDuplicates
      });
    }
    
    return duplicates;
  }
  
  private validateRecords(records: RemapRecord[]): ValidationIssue[] {
    const issues: ValidationIssue[] = [];
    
    records.forEach(record => {
      // 필수 필드 검사
      const requiredFields = ['seq', 'uwo_Gnid', 'uwo_Nid', 'uwogl_Gnid', 'uwogl_Nid', 'gameServerId'];
      requiredFields.forEach(field => {
        const value = (record as any)[field];
        if (value === undefined || value === null) {
          issues.push({
            type: 'missing_field',
            record,
            field,
            message: `Missing required field: ${field}`
          });
        } else if (value === '') {
          issues.push({
            type: 'empty_value',
            record,
            field,
            message: `Empty value for field: ${field}`
          });
        }
      });
      
      // seq 형식 검사
      if (isNaN(record.seq) || record.seq <= 0) {
        issues.push({
          type: 'invalid_format',
          record,
          field: 'seq',
          message: 'seq must be a positive number'
        });
      }
      
      // gameServerId 형식 검사 (영숫자와 하이픈만 허용)
      if (record.gameServerId && !/^[a-zA-Z0-9\-]+$/.test(record.gameServerId)) {
        issues.push({
          type: 'invalid_format',
          record,
          field: 'gameServerId',
          message: 'gameServerId contains invalid characters'
        });
      }
    });
    
    return issues;
  }
  
  private analyzeAccountMappings(records: RemapRecord[]) {
    const mappingMap = new Map<string, {
      targetAccount: string;
      users: Set<string>;
      worlds: Set<string>;
    }>();
    
    records.forEach(record => {
      const sourceAccount = record.uwo_Gnid;
      if (!mappingMap.has(sourceAccount)) {
        mappingMap.set(sourceAccount, {
          targetAccount: record.uwogl_Gnid,
          users: new Set(),
          worlds: new Set()
        });
      }
      
      const mapping = mappingMap.get(sourceAccount)!;
      mapping.users.add(record.uwo_Nid);
      mapping.worlds.add(record.gameServerId);
    });
    
    return Array.from(mappingMap.entries()).map(([sourceAccount, data]) => ({
      sourceAccount,
      targetAccount: data.targetAccount,
      userCount: data.users.size,
      worlds: Array.from(data.worlds).sort()
    })).sort((a, b) => b.userCount - a.userCount);
  }
}
