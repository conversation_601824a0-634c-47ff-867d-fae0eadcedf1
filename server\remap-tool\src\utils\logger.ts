import winston from 'winston';
import path from 'path';
import fs from 'fs-extra';
import { LogLevel } from '../types';

/**
 * 로거 설정 및 생성
 */
export class Logger {
  private logger: winston.Logger;
  private logDir: string;

  constructor(logLevel: LogLevel = 'info', outputDir: string = './logs') {
    this.logDir = outputDir;
    this.ensureLogDirectory();
    this.logger = this.createLogger(logLevel);
  }

  /**
   * 로그 디렉토리 생성
   */
  private ensureLogDirectory(): void {
    fs.ensureDirSync(this.logDir);
  }

  /**
   * Winston 로거 생성
   */
  private createLogger(logLevel: LogLevel): winston.Logger {
    const logFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.errors({ stack: true }),
      winston.format.printf(({ timestamp, level, message, stack }) => {
        return `${timestamp} [${level.toUpperCase()}]: ${message}${stack ? '\n' + stack : ''}`;
      })
    );

    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({ format: 'HH:mm:ss' }),
      winston.format.printf(({ timestamp, level, message }) => {
        return `${timestamp} ${level}: ${message}`;
      })
    );

    return winston.createLogger({
      level: logLevel,
      format: logFormat,
      transports: [
        // 콘솔 출력
        new winston.transports.Console({
          format: consoleFormat,
        }),
        // 전체 로그 파일
        new winston.transports.File({
          filename: path.join(this.logDir, 'remap-tool.log'),
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
        // 에러 로그 파일
        new winston.transports.File({
          filename: path.join(this.logDir, 'error.log'),
          level: 'error',
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
      ],
    });
  }

  /**
   * 디버그 로그
   */
  debug(message: string, meta?: unknown): void {
    this.logger.debug(message, meta);
  }

  /**
   * 정보 로그
   */
  info(message: string, meta?: unknown): void {
    this.logger.info(message, meta);
  }

  /**
   * 경고 로그
   */
  warn(message: string, meta?: unknown): void {
    this.logger.warn(message, meta);
  }

  /**
   * 에러 로그
   */
  error(message: string, error?: Error | unknown): void {
    if (error instanceof Error) {
      this.logger.error(message, { error: error.message, stack: error.stack });
    } else {
      this.logger.error(message, { error });
    }
  }

  /**
   * 작업 시작 로그
   */
  startOperation(operation: string): void {
    this.info(`=== ${operation} 시작 ===`);
  }

  /**
   * 작업 완료 로그
   */
  endOperation(operation: string, duration?: number): void {
    const durationText = duration ? ` (${duration}ms)` : '';
    this.info(`=== ${operation} 완료${durationText} ===`);
  }

  /**
   * 진행 상황 로그
   */
  progress(current: number, total: number, message: string): void {
    const percentage = Math.round((current / total) * 100);
    this.info(`[${current}/${total}] (${percentage}%) ${message}`);
  }

  /**
   * 로그 레벨 변경
   */
  setLevel(level: LogLevel): void {
    this.logger.level = level;
  }

  /**
   * 로그 파일 경로 반환
   */
  getLogFilePath(): string {
    return path.join(this.logDir, 'remap-tool.log');
  }

  /**
   * 에러 로그 파일 경로 반환
   */
  getErrorLogFilePath(): string {
    return path.join(this.logDir, 'error.log');
  }
}

// 기본 로거 인스턴스
let defaultLogger: Logger | null = null;

/**
 * 기본 로거 초기화
 */
export function initializeLogger(logLevel: LogLevel = 'info', outputDir: string = './logs'): Logger {
  defaultLogger = new Logger(logLevel, outputDir);
  return defaultLogger;
}

/**
 * 기본 로거 반환
 */
export function getLogger(): Logger {
  if (!defaultLogger) {
    defaultLogger = new Logger();
  }
  return defaultLogger;
}
