// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';

import { LoginInfo, User } from './user';
import { All } from './type/sync';
import cms from '../cms';
import * as cmsEx from '../cms/ex';
import {
  CASH_SHOP_SALE_TYPE,
  CASH_SHOP_PRODUCT_TYPE,
  CashShopDesc,
  isConsecutiveProductCode,
  HOT_SPOT_RESET_TYPE,
  isExistWorld<PERSON><PERSON>TimeField,
} from '../cms/cashShopDesc';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as formula from '../formula';
import { newDateByCmsDateStr } from '../motiflib/mutil';
import { CashShopLimitSaleDesc, CASH_SHOP_LIMIT_SALE_POINT_TYPE } from '../cms/cashShopLimitSale';
import { EventPageDesc, EventPageType } from '../cms/eventPageDesc';
import { GiveItem } from '../motiflib/mhttp/linegamesBillingApiClient';
import { ITEM_TYPE } from '../cms/itemDesc';
import { DailySubscriptionDesc } from '../cms/dailySubscription';
import * as mutil from '../motiflib/mutil';
import { isCash } from '../cms/pointDesc';

export interface RestrictedProduct {
  cmsId: number;
  amount: number;
  lastBuyingTimeUtc: number;
}

export interface FixedTermProduct {
  cmsId: number;
  startTimeUtc: number;
  endTimeUtc: number;
}

export interface OpenDurationProduct {
  cmsId: number;
  startTimeUtc: number;
  endTimeUtc: number;
}

/**
 * 1) 연속 할인 상품을 구매한 순간부터 구매한 시간 + normalSaleHours 까지 기본 상품 보여줌 + 구매 불가
 * 2) 구매한 시간 + normalSaleHours가 지난 이후 부터 discountSaleHours 동안 할인 상품 보여줌 + 구매 가능
 * 3) discountSaleHours 시간 내에 구매를 했을 경우 남은 discountSaleHours + normalSaleHours 동안 기본 상품 보여줌 + 구매 불가
 * 4) discountSaleHours 시간 내에 구매를 안했을 경우 discountSaleHours 시간이 지나고 기본 상품 보여줌 + 구매 가능 이후 구매시 1)으로 반복
 */
export interface ConsecutiveProduct {
  cmsId: number;
  normalSaleTimeUtc: number;
  discountSaleTimeUtc: number;
}

export enum UNBUYABLE_REASON {
  BUYABLE = 0,
  SOLD_OUT = 1,
  LOCALIZATION = 2,
  PREVIOUS_ID = 3,
  DATE = 4,
  ALREADY_BOUGHT_SOUND = 5,
  ALREADY_BOUGHT_EVENT_PAGE_PRODUCT = 6,
  INVALID_HOT_SPOT_PRODUCT = 7,
  ENOUGH_POPUP_COUNT = 8,
  INVALID_OPEN_CONTENTS_TERMS = 9,
  INVALID_EXPIRE_TIME_AND_COOL_TIME = 10,
  COOL_TIME = 11,
  NOT_OPEN_HOT_SPOT_PRODUCT = 12,
  INVALID_OPEN_DURATION_PRODUCT = 13,
  NOT_OPEN_PRODUCT = 14,
  ALREADY_BOUGHT_MATE = 15,
  CAN_ONLY_BUY_IN_WEB = 16,
  HAS_USER_TITLE = 17,
  NOT_MATCHED_CONSECUTIVE_PRODUCT = 18,
  ALREADY_BOUGHT_PET = 19,

  CMS_ERROR = 100, // 잘못된 기획 테이블 값.
}

export interface CashShopDailyProduct {
  /** {@link CashShopLimitSaleDesc.id} */
  cmsId: number;
  isBought: 0 | 1;
}

export interface CashShopDailySale {
  lastUpdateTimeUtc: number | null;
  dailyProducts: { [cashShopLimitSaleCmsId: number]: CashShopDailyProduct };
}

export interface DailySubscription {
  cmsId: number;
  createTimeUtc: number;
  expireTimeUtc: number;
  lastReceiveTimeUtc: number;
}

export interface HotSpotProduct {
  cmsId: number;
  // 등장 횟수
  popupCount: number;
  // 등장 조건 만족시 현재시간 + durationHours(Days), 구입할때 구입 시간으로 변경
  expireTimeUtc?: number;
  // 등장 조건 만족시 expireTimeUtc + coolTimeDays, 구입할때 현재시간 + cooltimeDays
  coolTimeUtc?: number;
  // startDate 기준으로 HotSpotResetValue만큼 지났다면 등장 횟수 초기화
  lastResetTimeUtc: number;
}

// ----------------------------------------------------------------------------
// UserCashShop
// ----------------------------------------------------------------------------
class UserCashShop {
  private _restrictedProducts: { [cmsId: number]: RestrictedProduct } = {};
  private _fixedTermProducts: { [cmsId: number]: FixedTermProduct } = {};
  private _gachaBoxGuarantees: { [cashShopCmsId: number]: number } = {};
  private _soundPacks: { [offset: number]: number } = {};
  private _eventPageProducts: { [offset: number]: number } = {};
  private _dailySale: CashShopDailySale = { lastUpdateTimeUtc: null, dailyProducts: {} };
  private _dailySubscriptions: { [cmsId: number]: DailySubscription } = {};
  private _hotSpotProducts: { [cmsId: number]: HotSpotProduct } = {};
  private _openDurationProducts: { [cmsId: number]: OpenDurationProduct } = {};
  private _consecutiveProducts: { [cmsId: number]: ConsecutiveProduct } = {};
  private _lastOpenHotSpotTimeUtc: number = 0;

  constructor() {}

  initWithLoginInfo(loginInfo: LoginInfo): void {
    for (const elem of loginInfo.cashShopRestrictedProducts) {
      this._restrictedProducts[elem.cmsId] = {
        cmsId: elem.cmsId,
        amount: elem.amount,
        lastBuyingTimeUtc: parseInt(elem.lastBuyingTimeUtc, 10),
      };
    }
    for (const elem of loginInfo.cashShopFixedTermProducts) {
      this._fixedTermProducts[elem.cmsId] = {
        cmsId: elem.cmsId,
        startTimeUtc: parseInt(elem.startTimeUtc, 10),
        endTimeUtc: elem.endTimeUtc ? parseInt(elem.endTimeUtc, 10) : null,
      };
    }
    for (const elem of loginInfo.cashShopGachaBoxGuarantees) {
      this._gachaBoxGuarantees[elem.cashShopCmsId] = elem.accum;
    }
    for (const elem of loginInfo.soundPacks) {
      this._soundPacks[elem.offset] = parseInt(elem.idxField, 10);
    }
    for (const elem of loginInfo.eventPageProducts) {
      this._eventPageProducts[elem.offset] = parseInt(elem.idxField, 10);
    }

    const dailyProducts: { [cashShopLimitSaleCmsId: number]: CashShopDailyProduct } = {};
    for (const elem of loginInfo.cashShopDailyProducts) {
      dailyProducts[elem.cmsId] = {
        cmsId: elem.cmsId,
        isBought: elem.isBought === 0 ? 0 : 1,
      };
    }
    this._dailySale = {
      lastUpdateTimeUtc: loginInfo.lastCashShopDailyProductsUpdateTimeUtc
        ? parseInt(loginInfo.lastCashShopDailyProductsUpdateTimeUtc, 10)
        : null,
      dailyProducts: dailyProducts,
    };

    for (const elem of loginInfo.dailySubscriptions) {
      this._dailySubscriptions[elem.cmsId] = {
        cmsId: elem.cmsId,
        createTimeUtc: parseInt(elem.createTimeUtc, 10),
        expireTimeUtc: parseInt(elem.expireTimeUtc, 10),
        lastReceiveTimeUtc: parseInt(elem.lastReceiveTimeUtc, 10),
      };
    }

    for (const elem of loginInfo.hotSpotProducts) {
      this._hotSpotProducts[elem.cmsId] = {
        cmsId: elem.cmsId,
        popupCount: elem.popupCount,
        expireTimeUtc: parseInt(elem.expireTimeUtc, 10),
        coolTimeUtc: parseInt(elem.coolTimeUtc, 10),
        lastResetTimeUtc: parseInt(elem.lastResetTimeUtc, 10),
      };
    }

    for (const elem of loginInfo.openDurationProducts) {
      this._openDurationProducts[elem.cmsId] = {
        cmsId: elem.cmsId,
        startTimeUtc: parseInt(elem.startTimeUtc, 10),
        endTimeUtc: elem.endTimeUtc ? parseInt(elem.endTimeUtc, 10) : null,
      };
    }

    for (const elem of loginInfo.cashShopConsecutiveProducts) {
      this._consecutiveProducts[elem.cmsId] = {
        cmsId: elem.cmsId,
        normalSaleTimeUtc: parseInt(elem.normalSaleTimeUtc, 10),
        discountSaleTimeUtc: parseInt(elem.discountSaleTimeUtc, 10),
      };
    }

    this._lastOpenHotSpotTimeUtc = parseInt(loginInfo.lastOpenHotSpotTimeUtc, 10);
  }

  clone(): UserCashShop {
    const c = new UserCashShop();
    c.cloneSet(
      _.cloneDeep(this._restrictedProducts),
      _.cloneDeep(this._fixedTermProducts),
      _.cloneDeep(this._gachaBoxGuarantees),
      _.cloneDeep(this._soundPacks),
      _.cloneDeep(this._eventPageProducts),
      _.cloneDeep(this._dailySale),
      _.cloneDeep(this._dailySubscriptions),
      _.cloneDeep(this._hotSpotProducts),
      _.cloneDeep(this._openDurationProducts),
      _.cloneDeep(this._consecutiveProducts)
    );
    return c;
  }

  cloneSet(
    restrictedProducts: { [cmsId: number]: RestrictedProduct },
    fixedTermProducts: { [cmsId: number]: FixedTermProduct },
    gachaBoxGuarantees: { [cashShopCmsId: number]: number },
    soundPacks: { [offset: number]: number },
    eventPageProducts: { [offset: number]: number },
    dailySale: CashShopDailySale,
    dailySubscriptions: { [cmsId: number]: DailySubscription },
    hotSpotProducts: { [cmsId: number]: HotSpotProduct },
    openDurationProducts: { [cmsId: number]: OpenDurationProduct },
    consecutiveProducts: { [cmsId: number]: ConsecutiveProduct }
  ): void {
    this._restrictedProducts = restrictedProducts;
    this._fixedTermProducts = fixedTermProducts;
    this._gachaBoxGuarantees = gachaBoxGuarantees;
    this._soundPacks = soundPacks;
    this._eventPageProducts = eventPageProducts;
    this._dailySale = dailySale;
    this._dailySubscriptions = dailySubscriptions;
    this._hotSpotProducts = hotSpotProducts;
    this._openDurationProducts = openDurationProducts;
    this._consecutiveProducts = consecutiveProducts;
  }

  getRestrictedProducts(): { [cmsId: number]: RestrictedProduct } {
    return this._restrictedProducts;
  }

  getFixedTermProducts(): { [cmsId: number]: FixedTermProduct } {
    return this._fixedTermProducts;
  }

  getEventPageProducts(): { [cmsId: number]: number } {
    return this._eventPageProducts;
  }

  getGachaBoxGuarantees(filter: number[]): { [cmsId: number]: number } {
    const ret: { [cmsId: number]: number } = {};
    _.forOwn(this._gachaBoxGuarantees, (score, cmsIdStr) => {
      if (filter.findIndex((elem) => elem === parseInt(cmsIdStr, 10)) !== -1) {
        ret[cmsIdStr] = score;
      }
    });

    return ret;
  }

  getGachaBoxGuaranteeAccum(cashShopCmsId: number): number {
    return this._gachaBoxGuarantees[cashShopCmsId];
  }
  setGachaBoxGuaranteeAccum(cashShopCmsId: number, accum: number): void {
    this._gachaBoxGuarantees[cashShopCmsId] = accum;
  }

  getFixedTermProduct(cmsId: number, curTimeUtc: number): FixedTermProduct {
    for (const elem of _.values(this._fixedTermProducts)) {
      if (elem.endTimeUtc && elem.endTimeUtc <= curTimeUtc) {
        continue;
      }
      if (elem.cmsId === cmsId) {
        return elem;
      }
    }
    return null;
  }

  getFixedTermProductByGroup(group: number, curTimeUtc: number): FixedTermProduct {
    for (const elem of _.values(this._fixedTermProducts)) {
      if (elem.endTimeUtc && elem.endTimeUtc <= curTimeUtc) {
        continue;
      }
      const cashShopCms = cms.CashShop[elem.cmsId];
      if (cashShopCms.buffGroup === group) {
        return elem;
      }
    }
    return null;
  }

  getExpiredFixedTermProducts(curTimeUtc: number): Set<number> {
    const ret = new Set<number>();
    _.forOwn(this._fixedTermProducts, (elem) => {
      if (elem.endTimeUtc && elem.endTimeUtc <= curTimeUtc) {
        ret.add(elem.cmsId);
      }
    });
    return ret;
  }

  deleteRestrictedProducts(cmsIds: number[]): void {
    for (const cmsId of cmsIds) {
      delete this._restrictedProducts[cmsId];
    }
  }

  setRestrictedProduct(product: RestrictedProduct): void {
    this._restrictedProducts[product.cmsId] = product;
  }

  deleteFixedTermProducts(cmsIds: number[]): void {
    for (const cmsId of cmsIds) {
      delete this._fixedTermProducts[cmsId];
    }
  }

  setFixedTermProduct(product: FixedTermProduct): void {
    this._fixedTermProducts[product.cmsId] = product;
  }

  getExpiredRestrictedProducts(curTimeUtc: number): Set<number> {
    const expiredRestrictedProducts = new Set<number>();
    _.forOwn(this._restrictedProducts, (elem) => {
      const elemCms = cms.CashShop[elem.cmsId];
      if (cmsEx.isCashShopPreviousId(elem.cmsId)) {
        return;
      }

      let bPassed;
      if (elemCms.saleType === CASH_SHOP_SALE_TYPE.DAY) {
        bPassed = formula.HasContentsResetTimePassed(
          curTimeUtc,
          elem.lastBuyingTimeUtc,
          cms.Define.CashShopResetHour
        );
      } else if (elemCms.saleType === CASH_SHOP_SALE_TYPE.WEEK) {
        bPassed = formula.HasWeeklyContentsResetTimePassed(
          curTimeUtc,
          elem.lastBuyingTimeUtc,
          cms.Define.CashShopResetHour,
          cms.Define.WeeklyCashShopResetDay
        );
      } else if (elemCms.saleType === CASH_SHOP_SALE_TYPE.MONTH) {
        bPassed = formula.HasMonthlyContentsResetTimePassed(
          curTimeUtc,
          elem.lastBuyingTimeUtc,
          cms.Define.CashShopResetHour,
          cms.Define.MonthlyCashShopResetDate
        );
      }
      if (bPassed) {
        expiredRestrictedProducts.add(elem.cmsId);
      }
    });

    return expiredRestrictedProducts;
  }

  setHotSpotProduct(product: HotSpotProduct) {
    this._hotSpotProducts[product.cmsId] = product;
  }

  getHotSpotProduct(cmsId: number): HotSpotProduct {
    return this._hotSpotProducts[cmsId];
  }

  getHotSpotProducts() {
    return this._hotSpotProducts;
  }

  getOpeningHotSpotProducts(curTimeUtc: number) {
    const result: HotSpotProduct[] = [];
    for (const product of Object.values(this._hotSpotProducts)) {
      if (curTimeUtc < product.expireTimeUtc) {
        result.push(product);
      }
    }

    return result;
  }

  getNeedResetPopupCountHotSpotProducts(curTimeUtc: number) {
    const result: { [cmsId: number]: HotSpotProduct } = {};
    for (const product of Object.values(this._hotSpotProducts)) {
      const cashShopCms = cms.CashShop[product.cmsId];
      if (cashShopCms.HotSpotResetType === HOT_SPOT_RESET_TYPE.RESET) {
        const hotSpotResetSec = cashShopCms.HotSpotResetValue * formula.SECONDS_PER_HOUR;
        const saleStartTimeUtc =
          mutil.newDateByCmsDateStr(cashShopCms.saleStartDate).getTime() / 1000;

        const lastResetTimeUtc =
          product.lastResetTimeUtc < saleStartTimeUtc ? saleStartTimeUtc : product.lastResetTimeUtc;

        const lastResetTimeSession = Math.floor(
          (lastResetTimeUtc - saleStartTimeUtc) / hotSpotResetSec
        );
        const curTimeSession = Math.floor((curTimeUtc - saleStartTimeUtc) / hotSpotResetSec);

        if (curTimeSession !== lastResetTimeSession) {
          result[product.cmsId] = product;
        }
      }
    }

    return result;
  }

  getHotSpotProductToDelete(curTimeUtc: number): number[] {
    const result: number[] = [];
    for (const product of Object.values(this._hotSpotProducts)) {
      const cashShopCms = cms.CashShop[product.cmsId];
      // 등장 횟수 제한이 있는건 db에서 지우면 안됨
      if (cashShopCms.endPopupCount > 0) {
        return;
      }

      // 쿨타임 지났으면 db에서 제거
      if (product.coolTimeUtc && product.coolTimeUtc < curTimeUtc) {
        result.push(product.cmsId);
      }
    }

    return result;
  }

  deleteHotSpotProducts(cmsIds: number[]) {
    for (const cmsId of cmsIds) {
      delete this._hotSpotProducts[cmsId];
    }
  }

  get lastOpenHotSpotTimeUtc() {
    return this._lastOpenHotSpotTimeUtc;
  }

  set lastOpenHotSpotTimeUtc(v: number) {
    this._lastOpenHotSpotTimeUtc = v;
  }

  setOpenDurationProduct(openDurationProduct: OpenDurationProduct) {
    this._openDurationProducts[openDurationProduct.cmsId] = openDurationProduct;
  }

  getOpenDurationProducts(cmsId: number): OpenDurationProduct {
    return this._openDurationProducts[cmsId];
  }

  getExpiredOpenDurationProducts(curTimeUtc: number): Set<number> {
    const expiredOpenDurationProducts = new Set<number>();
    _.forOwn(this._openDurationProducts, (elem) => {
      if (elem.endTimeUtc < curTimeUtc) {
        expiredOpenDurationProducts.add(elem.cmsId);
      }
    });

    return expiredOpenDurationProducts;
  }

  deleteOpenDurationProducts(cmsIds: number[]) {
    for (const cmsId of cmsIds) {
      delete this._openDurationProducts[cmsId];
    }
  }

  setConsecutiveProduct(consecutiveProduct: ConsecutiveProduct) {
    this._consecutiveProducts[consecutiveProduct.cmsId] = consecutiveProduct;
  }

  getConsecutiveProduct(cmsId: number) {
    return this._consecutiveProducts[cmsId];
  }

  getConsecutiveProducts() {
    return this._consecutiveProducts;
  }

  getExpiredConsecutiveProducts(curTimeUtc: number) {
    const expiredConsecutiveProducts = new Set<number>();
    _.forOwn(this._consecutiveProducts, (elem) => {
      if (curTimeUtc > elem.discountSaleTimeUtc) {
        expiredConsecutiveProducts.add(elem.cmsId);
      }
    });

    return expiredConsecutiveProducts;
  }

  deleteConsecutiveProducts(cmsIds: number[]) {
    for (const cmsId of cmsIds) {
      delete this._consecutiveProducts[cmsId];
    }
  }
  // 등장 조건
  isActiveProduct(
    cmsId: number,
    curDate: Date,
    expiredRestrictedProducts: Set<number>,
    buyingAmount: number = 1,
    exclusiveReasons: UNBUYABLE_REASON[] = []
  ) {
    const cashShopCms = cms.CashShop[cmsId];

    // saleType
    if (
      exclusiveReasons.findIndex((elem) => elem === UNBUYABLE_REASON.SOLD_OUT) === -1 &&
      this.isSoldOut(cashShopCms, expiredRestrictedProducts, buyingAmount)
    ) {
      return UNBUYABLE_REASON.SOLD_OUT;
    }

    // productLocalBitflag
    if (
      exclusiveReasons.findIndex((elem) => elem === UNBUYABLE_REASON.LOCALIZATION) === -1 &&
      cmsEx.isFilteredByCountryCode(cashShopCms.productLocalBitflag)
    ) {
      return UNBUYABLE_REASON.LOCALIZATION;
    }

    // previousId
    if (
      exclusiveReasons.findIndex((elem) => elem === UNBUYABLE_REASON.PREVIOUS_ID) === -1 &&
      cashShopCms.previousId &&
      !this._restrictedProducts[cashShopCms.previousId]
    ) {
      return UNBUYABLE_REASON.PREVIOUS_ID;
    }

    // date
    if (exclusiveReasons.findIndex((elem) => elem === UNBUYABLE_REASON.DATE) === -1) {
      if (curDate && cashShopCms.saleStartDate) {
        if (curDate < newDateByCmsDateStr(cashShopCms.saleStartDate)) {
          return UNBUYABLE_REASON.DATE;
        }
      }
      if (curDate && cashShopCms.saleEndDate) {
        if (curDate > newDateByCmsDateStr(cashShopCms.saleEndDate)) {
          return UNBUYABLE_REASON.DATE;
        }
      }
    }

    return UNBUYABLE_REASON.BUYABLE;
  }

  /**
   * @param curDate null 일 경우 검사 하지 않음
   */
  isBuyableProduct(
    user: User,
    cmsId: number,
    curDate: Date,
    expiredRestrictedProducts: Set<number>,
    buyingAmount: number = 1,
    productCode: string = undefined,
    exclusiveReasons: UNBUYABLE_REASON[] = []
  ): UNBUYABLE_REASON {
    const firstReason = this.isActiveProduct(
      cmsId,
      curDate,
      expiredRestrictedProducts,
      buyingAmount,
      exclusiveReasons
    );

    if (firstReason !== UNBUYABLE_REASON.BUYABLE) {
      return firstReason;
    }

    const cashShopCms = cms.CashShop[cmsId];

    // sound pack buying
    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.SOUND) {
      if (this.isSoundPackBought(cashShopCms.soundPackId)) {
        return UNBUYABLE_REASON.ALREADY_BOUGHT_SOUND;
      }
    }
    // event page product buying
    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.EVENT_PAGE) {
      const eventPageCms = cms.EventPage[cashShopCms.eventPageId];
      if (!eventPageCms) {
        return UNBUYABLE_REASON.CMS_ERROR;
      }
      if (eventPageCms.type === EventPageType.PASS_EVENT) {
        if (this.hasPassEventTicket(eventPageCms)) {
          return UNBUYABLE_REASON.ALREADY_BOUGHT_EVENT_PAGE_PRODUCT;
        }
      } else if (eventPageCms.type === EventPageType.PACKAGE_EVENT) {
        if (this.isPackageEventUnlocked(eventPageCms)) {
          return UNBUYABLE_REASON.ALREADY_BOUGHT_EVENT_PAGE_PRODUCT;
        }
      } else {
        return UNBUYABLE_REASON.CMS_ERROR;
      }
    }

    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.HOT_SPOT) {
      const hotSpotProduct = this._hotSpotProducts[cashShopCms.id];
      if (!hotSpotProduct) {
        return UNBUYABLE_REASON.INVALID_HOT_SPOT_PRODUCT;
      }

      // 등장 조건 검사
      if (!user.userContentsTerms.isValidContentsTerms(cashShopCms.contentsTerms, user)) {
        return UNBUYABLE_REASON.INVALID_OPEN_CONTENTS_TERMS;
      }

      if (!hotSpotProduct.expireTimeUtc || !hotSpotProduct.coolTimeUtc) {
        return UNBUYABLE_REASON.INVALID_EXPIRE_TIME_AND_COOL_TIME;
      }

      // 구매 가능한 시간인지 검사
      const expireDate = new Date(hotSpotProduct.expireTimeUtc * 1000);
      const coolDate = new Date(hotSpotProduct.coolTimeUtc * 1000);
      if (curDate && expireDate < curDate) {
        return coolDate > curDate
          ? UNBUYABLE_REASON.COOL_TIME
          : UNBUYABLE_REASON.NOT_OPEN_HOT_SPOT_PRODUCT;
      }
    }

    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.MATE) {
      if (user.userMates.hasMate(cashShopCms.mateId)) {
        return UNBUYABLE_REASON.ALREADY_BOUGHT_MATE;
      }
    }

    if (cmsEx.isOpenDurationCashShopCms(cashShopCms)) {
      const openDurationProduct = this._openDurationProducts[cashShopCms.id];
      if (!openDurationProduct) {
        return UNBUYABLE_REASON.INVALID_OPEN_DURATION_PRODUCT;
      }

      const startDate = new Date(openDurationProduct.startTimeUtc * 1000);
      const endDate = new Date(openDurationProduct.endTimeUtc * 1000);
      if (
        curDate &&
        (startDate > curDate || (openDurationProduct.endTimeUtc && endDate < curDate))
      ) {
        return UNBUYABLE_REASON.NOT_OPEN_PRODUCT;
      }
    }

    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.SERVER_TRANSFER) {
      return UNBUYABLE_REASON.CAN_ONLY_BUY_IN_WEB;
    }

    if (cashShopCms.productType === CASH_SHOP_PRODUCT_TYPE.USER_TITLE) {
      if (!cashShopCms.userTitleId) {
        return UNBUYABLE_REASON.CMS_ERROR;
      }

      if (curDate) {
        const curTimeUtc: number = mutil.dateToUtc(curDate);
        if (user.userTitles.getUserTitle(cashShopCms.userTitleId, curTimeUtc)) {
          return UNBUYABLE_REASON.HAS_USER_TITLE;
        }
      }
    }

    if (curDate) {
      const bIsConsecutiveProductCode = isConsecutiveProductCode(cashShopCms, productCode);

      // consecutiveProducts에 데이터가 있다 == 연속 구매 할인 상품을 기본 상품일때 한번 구매를 했다
      if (this._consecutiveProducts[cmsId]) {
        const product = this._consecutiveProducts[cmsId];
        const normalSaleDate = new Date(product.normalSaleTimeUtc * 1000);
        // 구매를 했으면 normalSaleDate까지 구매가 안됨
        if (curDate <= normalSaleDate) {
          return UNBUYABLE_REASON.NOT_MATCHED_CONSECUTIVE_PRODUCT;
        }

        if (productCode) {
          const discountSaleDate = new Date(product.discountSaleTimeUtc * 1000);
          // discountSaleDate가 지났으면 기본 상품 구매 가능
          if (bIsConsecutiveProductCode && curDate > discountSaleDate) {
            return UNBUYABLE_REASON.NOT_MATCHED_CONSECUTIVE_PRODUCT;
          }
        }
      } else if (bIsConsecutiveProductCode) {
        // consecutiveProducts에 데이터가 없으면 할인 상품은 구매 못함
        return UNBUYABLE_REASON.NOT_MATCHED_CONSECUTIVE_PRODUCT;
      }
    }

    if (cashShopCms.petId) {
      if (user.userPets.hasPet(cashShopCms.petId)) {
        return UNBUYABLE_REASON.ALREADY_BOUGHT_PET;
      }
    }

    return UNBUYABLE_REASON.BUYABLE;
  }

  isSoldOut(
    cashShopCms: CashShopDesc,
    expiredRestrictedProducts: Set<number>,
    buyingAmount: number
  ): boolean {
    if (
      cashShopCms.saleType === CASH_SHOP_SALE_TYPE.LIMIT ||
      ((cashShopCms.saleType === CASH_SHOP_SALE_TYPE.DAY ||
        cashShopCms.saleType === CASH_SHOP_SALE_TYPE.WEEK ||
        cashShopCms.saleType === CASH_SHOP_SALE_TYPE.MONTH) &&
        (!expiredRestrictedProducts || !expiredRestrictedProducts.has(cashShopCms.id)))
    ) {
      if (
        this._restrictedProducts[cashShopCms.id] &&
        this._restrictedProducts[cashShopCms.id].amount + buyingAmount > cashShopCms.saleTypeVal
      ) {
        return true;
      }
    }

    return false;
  }

  isSoundPackBought(cmsId: number): boolean {
    const offset = Math.floor(cmsId / 32);
    if (this._soundPacks[offset] !== undefined) {
      return (this._soundPacks[offset] & (1 << cmsId % 32)) !== 0;
    }

    return false;
  }

  addSoundPack(cmsId: number): void {
    const offset = Math.floor(cmsId / 32);
    if (this._soundPacks[offset] === undefined) {
      this._soundPacks[offset] = 0;
    }

    this._soundPacks[offset] = (this._soundPacks[offset] | (1 << cmsId % 32)) >>> 0;
  }

  getSoundPacks(): { [offset: number]: number } {
    return this._soundPacks;
  }

  getSoundPackIdxField(cmsId: number) {
    const offset = Math.floor(cmsId / 32);
    return this._soundPacks[offset] ? this._soundPacks[offset] : 0;
  }

  addEventPageProduct(eventPageCms: EventPageDesc): void {
    assert(
      eventPageCms.type === EventPageType.PASS_EVENT ||
        eventPageCms.type === EventPageType.PACKAGE_EVENT
    );

    const offset = Math.floor(eventPageCms.id / 32);
    if (this._eventPageProducts[offset] === undefined) {
      this._eventPageProducts[offset] = 0;
    }

    this._eventPageProducts[offset] =
      (this._eventPageProducts[offset] | (1 << eventPageCms.id % 32)) >>> 0;
  }

  removeEventPageProduct(eventPageCms: EventPageDesc): void {
    const offset = Math.floor(eventPageCms.id / 32);
    if (this._eventPageProducts[offset] === undefined) {
      mlog.error('Can not remove event page product', { cmsId: eventPageCms.id });
      return;
    }

    this._eventPageProducts[offset] =
      (this._eventPageProducts[offset] & ~(1 << eventPageCms.id % 32)) >>> 0;
  }

  getEventPageProductIdxField(offset: number): number {
    return this._eventPageProducts[offset] ? this._eventPageProducts[offset] : 0;
  }

  /**
   * '유료 일일 임무' 오픈
   */
  isPackageEventUnlocked(eventPageCms: EventPageDesc): boolean {
    assert(eventPageCms.type === EventPageType.PACKAGE_EVENT);

    const offset = Math.floor(eventPageCms.id / 32);
    if (this._eventPageProducts[offset] !== undefined) {
      return (this._eventPageProducts[offset] & (1 << eventPageCms.id % 32)) !== 0;
    }
    return false;
  }

  /**
   * '패스 임무' 추가보상권
   */
  hasPassEventTicket(eventPageCms: EventPageDesc): boolean {
    assert(eventPageCms.type === EventPageType.PASS_EVENT);

    const offset = Math.floor(eventPageCms.id / 32);
    if (this._eventPageProducts[offset] !== undefined) {
      return (this._eventPageProducts[offset] & (1 << eventPageCms.id % 32)) !== 0;
    }
    return false;
  }

  hasQuestPassByQuestPassCmsId(questPassCmsId: number): boolean {
    const cashShopCmsId = cmsEx.getQuestPassCashShopCmsIdByQuestPassCmsId(questPassCmsId);
    if (this._fixedTermProducts[cashShopCmsId]) {
      return true;
    }
    return false;
  }

  hasQuestPassByQuestCmsId(questCmsId: number): boolean {
    const cmsIds = cmsEx.getQuestPassCashShopCmsIdsByQuestCmsId(questCmsId);
    if (!cmsIds) {
      return false;
    }
    for (const cmsId of cmsIds) {
      if (this._fixedTermProducts[cmsId]) {
        return true;
      }
    }
    return false;
  }

  getDailySale(): CashShopDailySale {
    return this._dailySale;
  }

  setDailySale(x: CashShopDailySale): void {
    this._dailySale = x;
  }

  setDailyProductBought(cashShopLimitSaleCmsId: number, isBought: 0 | 1): void {
    this._dailySale.dailyProducts[cashShopLimitSaleCmsId].isBought = isBought;
  }

  static pickCashShopLimitSaleCmsIds(curTimeUtc: number): number[] {
    return CashShopLimitSaleUtil.pickCashShopLimitSaleCmsIds(curTimeUtc);
  }

  getDailySubscription(cmsId: number, curTimeUtc: number): DailySubscription {
    const ds = this._dailySubscriptions[cmsId];
    if (!ds) {
      return undefined;
    }
    if (ds.expireTimeUtc < curTimeUtc) {
      return undefined;
    }
    return ds;
  }

  buyDailySubscription(dsCms: DailySubscriptionDesc, curTimeUtc: number) {
    const userDs = this.getDailySubscription(dsCms.id, curTimeUtc);
    if (userDs) {
      userDs.expireTimeUtc += dsCms.durationDays * formula.SECONDS_PER_DAY;
    } else {
      const expireTimeUtc =
        formula.GetNextContentResetTimeByAddDays(
          curTimeUtc,
          dsCms.durationDays - 1,
          cms.ContentsResetHour.DailySubscriptionRewardReset.hour
        ) - 1;
      this._dailySubscriptions[dsCms.id] = {
        cmsId: dsCms.id,
        createTimeUtc: curTimeUtc,
        expireTimeUtc,
        lastReceiveTimeUtc: 1,
      };
    }
  }

  setDailySubscription(inData: DailySubscription) {
    this._dailySubscriptions[inData.cmsId] = inData;
  }

  getSyncData(): All {
    const ret: All = {
      // - quest pass 소유 여부
      // 를 cashShopFixedTermProducts 로 판단해야 되기 때문에 login sync data에 포함시킴
      cashShopFixedTermProducts: this._fixedTermProducts,
      soundPacks: this._soundPacks,
      eventPageProducts: this._eventPageProducts,
      dailySubscriptions: this._dailySubscriptions,
      hotSpotProducts: this._hotSpotProducts,
      lastOpenHotSpotTimeUtc: this._lastOpenHotSpotTimeUtc,
    };

    return ret;
  }
}

/**
 * {@link CashShopLimitSaleDesc} 관련 유틸
 */
namespace CashShopLimitSaleUtil {
  export function pickCashShopLimitSaleCmsIds(curTimeUtc: number): number[] {
    const ret: number[] = [];

    // 무료
    _pickCashShopLimitSaleCmsIds(
      cms.Define.MaxFreeCashShopDailyProductCount,
      cmsEx.getCashShopLimitSalesByPointType(CASH_SHOP_LIMIT_SALE_POINT_TYPE.FREE, curTimeUtc),
      ret
    );

    // 유료
    _pickCashShopLimitSaleCmsIds(
      cms.Define.MaxPaidCashShopDailyProductCount,
      cmsEx.getCashShopLimitSalesByPointType(CASH_SHOP_LIMIT_SALE_POINT_TYPE.PAID, curTimeUtc),
      ret
    );

    return ret;
  }

  function _pickCashShopLimitSaleCmsIds(
    pickCount: number,
    source: Readonly<{ totalRatio: number; cmses: readonly CashShopLimitSaleDesc[] }> | undefined,
    outPicked: number[]
  ): void {
    if (!source) {
      return;
    }

    const sourceCmses = Array.from(source.cmses); // 목록 복사
    let sourceTotalRatio = source.totalRatio;

    for (let i = 0; i < pickCount; i += 1) {
      const pickedIndex = _pickIndexFromCashShopLimitSaleCmses(sourceCmses, sourceTotalRatio);
      if (pickedIndex === undefined) {
        // 아마 더 이상 뽑을 게 없거나 확률이 0인 것들만 남은 경우
        break;
      }

      const picked = sourceCmses[pickedIndex];

      // 뽑힌 것 후보군에서 제외 ( 다음 루프에서 뽑히지 않도록 )
      sourceCmses[pickedIndex] = sourceCmses[sourceCmses.length - 1];
      sourceCmses.pop();
      sourceTotalRatio -= picked.cashShopLimitSaleRatio;

      // 외부 변수에 적용
      outPicked.push(picked.id);
    }
  }

  function _pickIndexFromCashShopLimitSaleCmses(
    candidates: readonly CashShopLimitSaleDesc[],
    totalWeight: number // 최대한 계산 덜하게 하려고 인자로 넣을 수 있게는 했는데.. 클래스로 만드는게 나을지?
  ): number | undefined {
    const rand = Math.random() * totalWeight;
    let cumulativeWeight: number = 0;
    for (let index = 0; index < candidates.length; index++) {
      const candidate = candidates[index];
      cumulativeWeight += candidate.cashShopLimitSaleRatio;
      // 0을 입력할 수도 있을 법한 것 참고.
      if (cumulativeWeight > rand) {
        return index;
      }
    }
    return undefined;
  }
}

/**
 * 빌링 보관함 관련 유틸
 */
export namespace BillingUtil {
  export function buildRestrictedProductChange(
    user: User,
    cashShopCms: CashShopDesc,
    curTimeUtc: number
  ): RestrictedProduct | undefined {
    let restrictedProductChange: RestrictedProduct | undefined;

    const expiredRestrictedProducts = user.userCashShop.getExpiredRestrictedProducts(curTimeUtc);
    const restrictedProducts = user.userCashShop.getRestrictedProducts();
    if (cashShopCms.saleType !== CASH_SHOP_SALE_TYPE.UNLIMITED) {
      if (expiredRestrictedProducts.has(cashShopCms.id) || !restrictedProducts[cashShopCms.id]) {
        restrictedProductChange = {
          cmsId: cashShopCms.id,
          amount: 1,
          lastBuyingTimeUtc: curTimeUtc,
        };
      } else {
        restrictedProductChange = {
          cmsId: cashShopCms.id,
          amount: restrictedProducts[cashShopCms.id].amount + 1,
          lastBuyingTimeUtc: curTimeUtc,
        };
      }
    } else if (cmsEx.isCashShopPreviousId(cashShopCms.id) && !restrictedProducts[cashShopCms.id]) {
      restrictedProductChange = {
        cmsId: cashShopCms.id,
        amount: 1,
        lastBuyingTimeUtc: curTimeUtc,
      };
    }

    return restrictedProductChange;
  }

  type SimpleError = { reason: string; extra?: JsonLike };
  type ResultOk<T> = { bOk: true; value: T };
  type ResultFail<E> = { bOk: false; err: E };

  type Result<T, E = SimpleError> = ResultOk<T> | ResultFail<E>;
  function Ok<T>(v: T): ResultOk<T> {
    return {
      bOk: true,
      value: v,
    };
  }
  function Fail<E>(e: E): ResultFail<E> {
    return {
      bOk: false,
      err: e,
    };
  }

  export enum BILLING_GIVE_ITEM_TYPE {
    EVENT_PAGE = 1,
    ITEM = 2,
    SHIP = 3,
    CEQUIP = 4,
    SHIP_SLOT = 5,
    DAILY_SUBSCRIPTION = 6,
    ILLUST_SKIN = 7,
    MATE = 8,
    SERVER_TRANFER = 9,
    USER_TITLE = 10,
    POINT = 11,
    WORLD_BUFF = 12,
    PET = 13,

    DUMMY = 100,
  }

  export interface EnsuredGiveItem {
    type: BILLING_GIVE_ITEM_TYPE;
    id: number;
    amount: number;
  }

  /**
   * 각 타입과 코드는 콘텐츠 분류표에 정의되어 있고, 빌링 어드민 툴을 통해 입력할 수 있다.
   */
  export function buildEnsuredGiveItem(item: GiveItem): Result<EnsuredGiveItem> {
    if (item.coinManageBalanceYn === 'Y') {
      assert.fail('coinManageBalanceYn-should-not-be-Y'); // 밖에서 검증해서 넣어주도록.
    }

    switch (item.productItemType) {
      case 'EventPage': {
        const eventPageCms = cms.EventPage[item.itemCd];
        if (!eventPageCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        if (
          !(
            eventPageCms.type === EventPageType.PASS_EVENT ||
            eventPageCms.type === EventPageType.PACKAGE_EVENT
          )
        ) {
          return Fail({
            reason: `not supported CMS.EventPage.type: ${EventPageType[eventPageCms.type]}`,
          });
        }
        if (item.amount !== 1) {
          // 필요 없을 듯?
          return Fail({
            reason: `invalid amount. can be only 1`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.EVENT_PAGE,
          id: eventPageCms.id,
          amount: 1,
        });
      }

      case 'Item': {
        const itemCms = cms.Item[item.itemCd];
        if (!itemCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        if (itemCms.type === ITEM_TYPE.RANDOM_QUEST) {
          return Fail({
            reason: `not supported CMS.Item.type: ${ITEM_TYPE[itemCms.type]}.`,
          });
        }
        if (itemCms.havableCount !== undefined) {
          // https://jira.line.games/browse/UWO-10623
          return Fail({
            reason: `not supported CMS.Item.havableCount.`,
          });
        }
        if (!(Number.isSafeInteger(item.amount) && item.amount > 0)) {
          return Fail({
            reason: `invalid amount.`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.ITEM,
          id: itemCms.id,
          amount: item.amount,
        });
      }
      case 'Ship': {
        const shipCms = cms.Ship[item.itemCd];
        if (!shipCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        if (!(Number.isSafeInteger(item.amount) && item.amount > 0)) {
          return Fail({
            reason: `invalid amount.`,
          });
        }
        if (item.amount !== 1) {
          return Fail({
            reason: `not implemented multiple amount`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.SHIP,
          id: shipCms.id,
          amount: item.amount,
        });
      }

      case 'CEquip': {
        const equipCms = cms.CEquip[item.itemCd];
        if (!equipCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        if (!(Number.isSafeInteger(item.amount) && item.amount > 0)) {
          return Fail({
            reason: `invalid amount.`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.CEQUIP,
          id: equipCms.id,
          amount: item.amount,
        });
      }

      case 'ShipSlot': {
        const shipSlotCms = cms.ShipSlot[item.itemCd];
        if (!shipSlotCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        if (!(Number.isSafeInteger(item.amount) && item.amount > 0)) {
          return Fail({
            reason: `invalid amount.`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.SHIP_SLOT,
          id: shipSlotCms.id,
          amount: item.amount,
        });
      }

      case 'DailySubscription': {
        const dailySubscriptionCms = cms.DailySubscription[item.itemCd];
        if (!dailySubscriptionCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.DAILY_SUBSCRIPTION,
          id: dailySubscriptionCms.id,
          amount: 1,
        });
      }

      case 'IllustSkin': {
        const illustSkinCms = cms.IllustSkin[item.itemCd];
        if (!illustSkinCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }

        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.ILLUST_SKIN,
          id: illustSkinCms.id,
          amount: 1,
        });
      }
      case 'Mate': {
        const mateCms = cms.Mate[item.itemCd];
        if (!mateCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        if (item.amount !== 1) {
          return Fail({
            reason: `not implemented multiple amount`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.MATE,
          id: mateCms.id,
          amount: item.amount,
        });
      }
      case 'ServerTranfer': {
        // 서버 이전권 상품의 경우 라인측에 수령 완료 통보만 하고 구성품 수령 작업은 하지 않는다.
        // 빌링 툴에서 상품을 등록하려면 최소 1개의 구성품을 등록해야하는 이슈가 있음.

        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.SERVER_TRANFER,
          id: Number(item.itemCd),
          amount: item.amount,
        });
      }
      case 'UserTitle': {
        const userTitleCms = cms.UserTitle[item.itemCd];
        if (!userTitleCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }

        if (item.amount !== 1) {
          return Fail({
            reason: `not implemented multiple amount`,
          });
        }

        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.USER_TITLE,
          id: userTitleCms.id,
          amount: item.amount,
        });
      }
      case 'Point': {
        const pointCms = cms.Point[item.itemCd];
        if (!pointCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }

        if (
          isCash(pointCms.id) ||
          pointCms.id === cmsEx.CashShopMileage ||
          pointCms.id === cmsEx.PaidRedGemPointCmsId
        ) {
          return Fail({
            reason: `not supported CMS.Point.id: ${pointCms.id}`,
          });
        }

        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.POINT,
          id: pointCms.id,
          amount: item.amount,
        });
      }
      case 'WorldBuff': {
        const cashShopCms = cms.CashShop[item.itemCd];
        if (!cashShopCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }

        if (!cashShopCms.productWorldBuffId || cashShopCms.productWorldBuffId.length <= 0) {
          return Fail({
            reason: `invalid world buff Id`,
          });
        }

        // 이런 검사는 cms 로드할때 체크하는게 맞는듯
        if (isExistWorldBuffTimeField(cashShopCms) == false) {
          return Fail({
            reason: `invalid duration.`,
          });
        }

        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.WORLD_BUFF,
          id: cashShopCms.id,
          amount: 1,
        });
      }
      case 'Pet': {
        const petCms = cms.Pet[item.itemCd];
        if (!petCms) {
          return Fail({
            reason: `invalid itemCd.`,
          });
        }
        if (item.amount !== 1) {
          return Fail({
            reason: `not implemented multiple amount pet`,
          });
        }
        return Ok({
          type: BILLING_GIVE_ITEM_TYPE.PET,
          id: petCms.id,
          amount: item.amount,
        });
      }
      case 'DEFAULT_TYPE': {
        // 빌링 어드민 툴에서, itemCd 만 입력하는 경우 자동으로 설정되는 듯함.
      }
      default:
        return Fail({
          reason: `undefied productItemType`,
        });
    }
  }

  export function giveItemToString(giveItem: GiveItem) {
    return `itemType: ${giveItem.productItemType}, itemCd: ${giveItem.itemCd}`;
  }

  //
}

// ----------------------------------------------------------------------------
// Exports.
// ----------------------------------------------------------------------------

export default UserCashShop;
