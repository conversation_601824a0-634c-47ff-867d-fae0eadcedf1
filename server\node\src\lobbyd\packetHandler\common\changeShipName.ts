// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import tuChangeShipName from '../../../mysqllib/txn/tuChangeShipName';
import { Sync, Resp } from '../../type/sync';
import { LobbyService } from '../../server';
import { MError, MErrorCode } from '../../../motiflib/merror';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import mhttp from '../../../motiflib/mhttp';
import * as mutilLanguage from '../../../motiflib/mutilLanguage';

import { ClientPacketHandler } from '../index';
import mlog from '../../../motiflib/mlog';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'change_ship_name';
const add_rsn = null;

// ----------------------------------------------------------------------------
export class Cph_Common_ChangeShipName implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body = packet.bodyObj;

    const id = body.id;
    const name = body.name;

    mutilLanguage.ensureNameLength(
      name,
      cms.Const.ShipNameMinimum.value,
      cms.Const.ShipNameMaximum.value
    );

    const { userDbConnPoolMgr, userCacheRedis } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const userShip = user.userFleets.getShip(id);
    if (!userShip) {
      throw new MError('invalid-ship-id', MErrorCode.INVALID_REQ_BODY_CHANGE_SHIP_NAME_SHIP_ID, {
        body,
      });
    }

    return mhttp.lgd
      .hasBadWord(name)
      .then((bHas) => {
        if (bHas) {
          throw new MError('has-bad-word', MErrorCode.HAS_BAD_WORD, {
            name,
            oldName: user.userName,
          });
        }
        return tuChangeShipName(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          id,
          name
        );
      })
      .then(() => {
        // glog
        const shipCms = cms.Ship[userShip.getNub().cmsId];
        user.glog('ship_nickname', {
          rsn,
          add_rsn,
          ship_id: shipCms.id,
          ship_name: shipCms.name,
          ship_uid: id,
          ship_guid: userShip.getNub().guid,
          old_nick: userShip.getNub().name ?? shipCms.name,
          cur_nick: name,
        });

        user.userFleets.getShip(id).setName(name);

        const shipNub = user.userFleets.getShip(id).getNub();

        if (shipNub.fleetIndex === cmsEx.FirstFleetIndex) {
          userCacheRedis['setUserFirstFleetInfoFlag'](user.userId, shipNub.formationIndex).catch(
            (err) => {
              mlog.error('userCacheRedis setFleetInfoFlag error', {
                userId: user.userId,
                err,
              });
            }
          );
        }

        const sync: Sync = {
          add: {
            ships: {
              [id]: {
                name,
              },
            },
          },
        };

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }
}
