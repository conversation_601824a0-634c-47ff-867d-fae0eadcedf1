#!/bin/bash


QUERY_RESULT=""
queryExecute()
{
  DB_HOST=$1
  DB_USER=$2
  DB_PASSWD=$3
  DB_NAME=$4
  QUERY=$5

  if [ -z "$DB_HOST" ]; then
    echo "No DB_HOST supplied!"
    exit 1
  fi

  if [ -z "$DB_NAME" ]; then
    echo "No DB_NAME supplied!"
    exit 1
  fi

  if [ -z "$DB_USER" ]; then
    echo "No DB_USER supplied!"
    exit 1
  fi

  if [ -z "$DB_PASSWD" ]; then
    echo "No DB_PASSWD supplied!"
    exit 1
  fi

  if [[ -z "$QUERY" ]]; then
    echo "No query string supplied!"
    exit 1
  fi

  #QUERY_RESULT=$(echo $QUERY | \
    #mysql -N --protocol=tcp -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWD" -D "$DB_NAME" \
    #2>&1 | grep -v "Using a password")

  QUERY_RESULT=$(echo $QUERY | \
    mysql -N --protocol=tcp -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWD" -D "$DB_NAME" \
    2> >(grep -v "Using a password" >&2))
  if [[ $? != 0 ]]; then
    echo "!! QUERY FAILED: "${QUERY}
    exit 1;
  fi
}
