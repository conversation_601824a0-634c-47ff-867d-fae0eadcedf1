// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import assert from 'assert';
import _ from 'lodash';
import { reduce as reducePromise } from 'bluebird';
import { Container } from 'typedi';
import moment from 'moment';

import mlog from '../../../motiflib/mlog';
import mhttp from '../../../motiflib/mhttp';
import mconf from '../../../motiflib/mconf';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import { CashShopFixedTermProduct, Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import {
  ensureGiveItems,
  GiveItem,
  LGBillingErrorCode,
  LGBillingResponseBody,
  LineGamesBillingApiClient,
} from '../../../motiflib/mhttp/linegamesBillingApiClient';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import * as cmsEx from '../../../cms/ex';
import cms from '../../../cms';
import { CASH_SHOP_SALE_POINT_TYPE, getWorldBuffAddTime } from '../../../cms/cashShopDesc';
import { LobbyService } from '../../server';
import { AccumulateParam } from '../../userAchievement';
import tuBillingReceiveInvenPurchases from '../../../mysqllib/txn/tuBillingReceiveInvenPurchases';
import Ship, { makeShipRndStats, ShipEnchantedStat, ShipNub } from '../../ship';
import { ItemChange, ShipSlotItem, UserInven } from '../../userInven';
import {
  RewardCmsElemItemExtra,
  RewardCmsElemMateEquipmentExtra,
  RewardCmsElemShipExtra,
  RewardCmsElemShipSlotItemExtra,
} from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import { REWARD_TYPE, RewardCmsElem } from '../../../cms/rewardDesc';
import Fleet from '../../fleet';
import { SHIP_ASSIGNMENT } from '../../../motiflib/model/lobby/enum';
import { EventPageDesc, EventPageType } from '../../../cms/eventPageDesc';
import UserCashShop, { BillingUtil, DailySubscription, FixedTermProduct } from '../../userCashShop';
import BILLING_GIVE_ITEM_TYPE = BillingUtil.BILLING_GIVE_ITEM_TYPE;
import EnsuredGiveItem = BillingUtil.EnsuredGiveItem;
import UserMates from '../../userMates';
import { MateEquipmentNub, MateNub } from '../../../motiflib/model/lobby';
import Mate, { MateUtil } from '../../mate';
import { BuffSync, UserBuffs, WorldBuffNub, WorldBuffUtil } from '../../userBuffs';
import { UserTitles, UserTitle } from '../../userTitles';
import UserPoints, { PointChange } from '../../userPoints';
import { EnergyChange, UserEnergy } from '../../userEnergy';
import { SECONDS_PER_DAY, SECONDS_PER_HOUR } from '../../../formula';
import { UserPets } from '../../userPets';
import { BuilderMailCreateParams, MailCreatingParams } from '../../../motiflib/mailBuilder';

// ----------------------------------------------------------------------------
// 빌링 보관함에서 수령
// 중복 수령 관련해서, 먼저 빌링 서버에 완료처리 해버린다.
// 당장은 아이템을 지급에 실패하는 경우 CS 처리 해야함.
// ----------------------------------------------------------------------------

const rsn = 'billing_receive_inven_purchases';
const add_rsn = null;

interface RequestBody {
  invenIds: string[];
}

interface ResponseBody extends BuffSync {
  failStep?: string; // 일단은 클라 로그 참고용으로 보냄.
  billingApiRespForFail?: unknown;
  /** 공간 부족 등 받을 수 없어 메일로 간 것들 */
  mailIds?: number[];
}

interface InvenPurchase {
  appStoreCd: string;
  productId: string;
  orderId: string;
  invenId: string;
}

class BillingApiFailError extends Error {
  public readonly step: string;
  public readonly billingApiResp: LGBillingResponseBody;

  constructor(step: string, billingApiResp: LGBillingResponseBody) {
    super();
    this.step = step;
    this.billingApiResp = billingApiResp;
  }
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingReceiveInvenPurchases implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const reqBody: RequestBody = packet.bodyObj;
    const respBody: ResponseBody = { sync: {} };
    const mailIds: number[] = [];
    const curTimeUtc = mutil.curTimeUtc();

    if (!Array.isArray(reqBody.invenIds)) {
      throw new MError(
        'invenIds-array-expected',
        MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES,
        { reqBody }
      );
    }
    if (reqBody.invenIds.length === 0) {
      throw new MError(
        'invenIds-empty',
        MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES,
        { reqBody }
      );
    }
    for (const invenId of reqBody.invenIds) {
      if (!_isString(invenId) || invenId.length === 0) {
        throw new MError(
          'invenId-non-empty-string-expected',
          MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES,
          { reqBody, invenId }
        );
      }
    }
    const reqInvenIdsUniqueChecked = new Set(reqBody.invenIds);
    if (reqInvenIdsUniqueChecked.size !== reqBody.invenIds.length) {
      throw new MError(
        'invenIds-duplicated',
        MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES,
        { reqBody }
      );
    }
    if (reqInvenIdsUniqueChecked.size > cms.Define.BillingInventorySlotSize) {
      // 인벤 공간에 대한 Define 이긴 하지만 편의상.
      // 수량을 조정하게 된다면 빌링 API 에서 요청 가능한 개수도 확인해봐야한다.
      throw new MError(
        'invenIds-exceeded-allowed-request-quantity',
        MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES,
        { requestBody: reqBody, slotSize: cms.Define.MaxBillingInventorySize }
      );
    }

    const productNames: {
      [appStoreCd: string]: {
        [productId: string]: string | null;
      };
    } = {};
    const coinManageBalanceItemsByProductId: {
      [appStoreCd: string]: {
        [productId: string]: GiveItem[];
      };
    } = {};
    const ensuredGiveItemsByProductId: {
      [appStoreCd: string]: {
        [productId: string]: EnsuredGiveItem[];
      };
    } = {};

    const completeWithChargeCoin: InvenPurchase[] = [];
    const completeOnly: InvenPurchase[] = [];

    const invenRemoveds: InvenPurchase[] = [];

    return Promise.resolve()
      .then(() => {
        // #. 상품보관함에서 조회.
        return mhttp.lgbillingd.queryInventoryPurchaseList(user.userId.toString());
      })
      .then((billingApiResp) => {
        if (billingApiResp.success !== true) {
          if (billingApiResp.errorCd === LGBillingErrorCode[LGBillingErrorCode.NOT_EXIST_DATA]) {
            // 보관함에 아무것도 없을 때도 위 에러가 나온다.
            throw new MError(
              'billing-inven-empty',
              MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES,
              { requestBody: reqBody, billingApiResp }
            );
          }
          throw new BillingApiFailError('QUERY_INVEN_LIST', billingApiResp);
        }

        if (!Array.isArray(billingApiResp.data)) {
          throw new MError(
            'query-inven-purchase-resp-data-array-expected',
            MErrorCode.BILLING_API_UNEXPECTED_RESP,
            { reqBody, billingApiResp }
          );
        }
        const allInvenPurchases: unknown[] = billingApiResp.data;
        const allInvenPurchasesByInvenId: { [invenId: string]: InvenPurchase } = {};
        for (const invenPurchase of allInvenPurchases) {
          // 일단은 방어적으로..
          if (typeof invenPurchase !== 'object' || !invenPurchase) {
            throw new MError(
              'query-inven-purchase-resp-data-array-elem-object-expected',
              MErrorCode.BILLING_API_UNEXPECTED_RESP,
              { billingApiResp }
            );
          }
          if ((invenPurchase as any).status !== 'READY') {
            // READY 인 것들만 전달된다고는 함.
            throw new MError(
              'query-inven-purchase-resp-data-array-elem-status-READY-expected',
              MErrorCode.BILLING_API_UNEXPECTED_RESP,
              { billingApiResp }
            );
          }

          // 꼭 필요한 정보들.
          _ensureInvenPurchase(invenPurchase, {
            billingApiResp,
          });

          if (allInvenPurchasesByInvenId[invenPurchase.invenId]) {
            throw new MError(
              'query-inven-purchase-resp-data-array-elem-invenId-duplicated',
              MErrorCode.BILLING_API_UNEXPECTED_RESP,
              { billingApiResp }
            );
          }
          allInvenPurchasesByInvenId[invenPurchase.invenId] = invenPurchase;
        }

        const byStoreCd: Map<string, Map<string, InvenPurchase[]>> = new Map();
        for (const reqInvenId of reqInvenIdsUniqueChecked) {
          const invenPurchase = allInvenPurchasesByInvenId[reqInvenId];
          if (!invenPurchase) {
            throw new MError(
              'not-found-in-billing-inventory',
              MErrorCode.INVALID_REQ_BODY_BILLING_RECEIVE_INVEN_PURCHASES,
              { reqBody, billingApiResp }
            );
          }
          if (!byStoreCd.has(invenPurchase.appStoreCd)) {
            byStoreCd.set(invenPurchase.appStoreCd, new Map());
          }
          const byProductId = byStoreCd.get(invenPurchase.appStoreCd);
          if (!byProductId.has(invenPurchase.productId)) {
            byProductId.set(invenPurchase.productId, []);
          }
          byProductId.get(invenPurchase.productId).push(invenPurchase);
        }

        const queriesForGiveItem: { appStoreCd: string; productId: string }[] = [];
        for (const [appStoreCd, toReceivesbyProductId] of byStoreCd) {
          for (const [productId] of toReceivesbyProductId) {
            queriesForGiveItem.push({
              appStoreCd,
              productId,
            });

            // 빌링 서버에서 상품명을 조회하는 게 번거롭기 때문에 기획 테이블에서 조회하도록 한다.
            // 추후 빌링 서버를 통해 가져와야 할 수 있음.
            // 그리고 수령 진행 과정에서 별 것도 아닌 것에서 에러나는 상황이 없도록 미리 만들어 놓도록 함.
            if (!productNames[appStoreCd]) {
              productNames[appStoreCd] = {};
            }
            const cashShopCms = cmsEx.getCashShopCmsByProductCode(productId);
            productNames[appStoreCd][productId] = cashShopCms
              ? displayNameUtil.getCashShopProductDisplayName(cashShopCms)
              : null;
          }
        }

        // #. 상품 구성품 조회
        return reducePromise(
          queriesForGiveItem,
          async (_, query) => {
            return mhttp.lgbillingd
              .queryProductGiveItemDetail(query.appStoreCd, query.productId)
              .then((billingApiResp) => {
                if (billingApiResp.success !== true) {
                  throw new BillingApiFailError('QUERY_PRODUCT_GIVE_ITEM', billingApiResp);
                }

                const data = billingApiResp.data as any;
                const giveItemList = data.giveItemList;
                ensureGiveItems(giveItemList);

                // 빌링에서 관리하는 재화를 나눠 담는다.
                const coinManageBalanceItems: GiveItem[] = [];
                const ensuredGiveItems: EnsuredGiveItem[] = [];
                for (const giveItem of giveItemList) {
                  if (giveItem.coinManageBalanceYn === 'Y') {
                    coinManageBalanceItems.push(giveItem);
                  } else {
                    const ret = BillingUtil.buildEnsuredGiveItem(giveItem);
                    if (ret.bOk !== true) {
                      throw new MError(
                        `failed to ensure give item (${BillingUtil.giveItemToString(
                          giveItem
                        )}). reason: ${ret.err?.reason}.`,
                        MErrorCode.BILLING_API_UNEXPECTED_GIVE_ITEM,
                        { billingApiResp, giveItem }
                      );
                    }

                    ensuredGiveItems.push(ret.value);
                  }
                }

                //
                if (coinManageBalanceItems.length > 0) {
                  if (!coinManageBalanceItemsByProductId[query.appStoreCd]) {
                    coinManageBalanceItemsByProductId[query.appStoreCd] = {};
                  }
                  coinManageBalanceItemsByProductId[query.appStoreCd][query.productId] =
                    coinManageBalanceItems;
                }
                if (ensuredGiveItems.length > 0) {
                  if (!ensuredGiveItemsByProductId[query.appStoreCd]) {
                    ensuredGiveItemsByProductId[query.appStoreCd] = {};
                  }
                  ensuredGiveItemsByProductId[query.appStoreCd][query.productId] = ensuredGiveItems;
                }

                //
                if (coinManageBalanceItems.length > 0) {
                  completeWithChargeCoin.push(
                    ...byStoreCd.get(query.appStoreCd).get(query.productId)
                  );
                } else {
                  completeOnly.push(...byStoreCd.get(query.appStoreCd).get(query.productId));
                }
              });
          },
          {}
        );
      })
      .then(() => {
        if (completeWithChargeCoin.length === 0) {
          return null;
        }

        // #. "빌링에서 관리하는 재화" 충전과 함께 보관함에서 제거하는 API 를 사용하는 경우

        type Query = Parameters<LineGamesBillingApiClient['chargeCoinWithInvenList']>[3][number];

        const billingQueries: Query[] = [];

        for (const elem of completeWithChargeCoin) {
          const coinManageBalanceItems: GiveItem[] =
            coinManageBalanceItemsByProductId[elem.appStoreCd]?.[elem.productId];
          assert(coinManageBalanceItems && coinManageBalanceItems.length > 0); // 로직이 의도와 다르게 동작하는 상태.

          billingQueries.push({
            coinChargeList: coinManageBalanceItems.map((elem) => {
              return {
                coinCd: elem.itemCd,
                chargeTypeCd: elem.coinChargeTypeCd,
                chargeAmt: elem.amount,
              };
            }),
            orderId: elem.orderId,
            invenDelYn: 'Y' as const,
            invenMemo: `${rsn}`,
          });
        }

        return Promise.resolve()
          .then(() => {
            return mhttp.lgbillingd.chargeCoinWithInvenList(
              user.userId.toString(),
              user.accountId,
              user.countryCreated,
              billingQueries
            );
          })
          .then((billingApiResp) => {
            if (billingApiResp.success !== true) {
              throw new BillingApiFailError('CHARGE_WITH_INVEN', billingApiResp);
            }

            for (const invenPurchase of completeWithChargeCoin) {
              user.glog('common_purchase_box', {
                rsn,
                add_rsn,
                flag: 2, // 1:추가 / 2:삭제
                type: CASH_SHOP_SALE_POINT_TYPE.CASH, // iap 상품
                product_id: invenPurchase.productId,
                product_name:
                  productNames[invenPurchase.appStoreCd]?.[invenPurchase.productId] ?? null,
                order_id: invenPurchase.orderId,
                inven_id: invenPurchase.invenId,
              });
            }

            invenRemoveds.push(...completeWithChargeCoin);

            const chargedBillingCoins = billingApiResp.data;
            _ensureChargedCoinList(chargedBillingCoins, { billingApiResp }, user.userId);
            _.merge<Sync, Sync>(
              respBody.sync,
              user.userPoints.onChargeByPurchaseProduct(chargedBillingCoins, {
                user,
                rsn,
                add_rsn,
              })
            );

            return null;
          });
      })
      .then(() => {
        if (completeOnly.length === 0) {
          return null;
        }

        type Query = Parameters<
          LineGamesBillingApiClient['completeReceiveInvenPurchaseBulk']
        >[1][number];

        // #. 보관함 수령 완료 통보 API 를 사용하는 경우
        const queries: Query[] = completeOnly.map((elem, index) => {
          const coinManageBalanceItems =
            coinManageBalanceItemsByProductId[elem.appStoreCd]?.[elem.productId];
          if (coinManageBalanceItems && coinManageBalanceItems.length > 0) {
            assert.fail(`coinManageBalanceYn-should-not-be-Y`); // 로직이 의도와 다르게 동작하는 상태.
          }
          return {
            invenId: elem.invenId,
            memo: `${rsn}`,
          };
        });

        return Promise.resolve()
          .then(() => {
            // #. 수령 통보.
            return mhttp.lgbillingd.completeReceiveInvenPurchaseBulk(
              user.userId.toString(),
              queries
            );
          })
          .then((billingApiResp) => {
            if (billingApiResp.success !== true) {
              throw new BillingApiFailError('COMPLETE_RECEIVE', billingApiResp);
            }

            const results = billingApiResp.data;
            if (!Array.isArray(results)) {
              throw new MError(
                'complete-receive-inven-purchase-resp-data-array-expected',
                MErrorCode.BILLING_API_UNEXPECTED_RESP,
                { billingApiResp }
              );
            }

            const successResults: InvenPurchase[] = [];
            const failResults: unknown[] = [];
            const invalidResults: unknown[] = [];
            const dupCheck = {};

            //* 건당 처리임에 유의해야함. 뭐하나 실패시 롤백이 아님.
            // TODO '모두 받기'를 실질적으로 사용하게 된다면, 실패한 것에 대해 클라에 전달할 필요가 있을지
            for (const result of results) {
              if (result.resJsonVO?.success !== true) {
                failResults.push({ result });
                continue;
              }
              const invenPurchase = completeOnly.find(
                (invenPurchase) => invenPurchase.invenId === result.invenId
              );
              if (!invenPurchase) {
                invalidResults.push([result, 'invenId-not-matched']);
                continue;
              }
              if (dupCheck[invenPurchase.invenId]) {
                invalidResults.push([result, 'invenId-duplicated']);
                continue;
              }
              dupCheck[invenPurchase.invenId] = true;
              successResults.push(invenPurchase);
            }

            if (failResults.length > 0) {
              mlog.error('complete-receive-inven-purchase-fail-exist', {
                userId: user.userId,
                queries,
                failResults,
              });
            }
            if (invalidResults.length > 0) {
              mlog.error('complete-receive-inven-purchase-invalid-resp-data-received', {
                userId: user.userId,
                queries,
                invalidResults,
              });
            }

            // 보관함에서 성공적으로 제거된 것에 관해 먼저 로그를 남기도록 한다.
            for (const invenPurchase of successResults) {
              user.glog('common_purchase_box', {
                rsn,
                add_rsn,
                flag: 2, // 1:추가 / 2:삭제
                type: CASH_SHOP_SALE_POINT_TYPE.CASH, // iap 상품
                product_id: invenPurchase.productId,
                product_name:
                  productNames[invenPurchase.appStoreCd]?.[invenPurchase.productId] ?? null,
                order_id: invenPurchase.orderId,
                inven_id: invenPurchase.invenId,
              });
            }

            invenRemoveds.push(...successResults);

            return null;
          });
      })
      .then(() => {
        const ensuredGiveItemsList: EnsuredGiveItem[][] = [];
        for (const invenPurchase of invenRemoveds) {
          const giveItems =
            ensuredGiveItemsByProductId[invenPurchase.appStoreCd]?.[invenPurchase.productId];
          if (giveItems) {
            ensuredGiveItemsList.push(giveItems);
          }
        }

        if (ensuredGiveItemsList.length === 0) {
          return null;
        }

        return _receiveProducts(ensuredGiveItemsList, respBody, mailIds, user, curTimeUtc);
      })
      .then(() => {
        if (mailIds.length > 0) {
          respBody.mailIds = mailIds;
        }
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, respBody);
      })
      .catch((err) => {
        if (err instanceof BillingApiFailError) {
          mlog.error(`[${rsn}] billing-api-failed`, {
            userId: user.userId,
            reqBody,
            failStep: err.step,
            billingApiResp: err.billingApiResp,
          });

          // 빌링 API success 가 false 인 경우, resp 를 그대로 클라로 전송하도록 한다.
          // 클라에서 빌링 에러를 알 수 있도록함.
          return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
            failStep: err.step,
            billingApiRespForFail: err.billingApiResp,
          });
        }

        throw err;
      });
  }
}

function _isString(x: any): x is string {
  return x !== undefined && x !== null && typeof x === 'string';
}

function _ensureInvenPurchase(x: any, extra: JsonLike): asserts x is InvenPurchase {
  const appStoreCd = x.appStoreCd;
  if (!_isString(appStoreCd)) {
    throw new MError('appStoreCd-string-expected', MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
  }
  const productId = x.productId;
  if (!_isString(productId)) {
    throw new MError('productId-string-expected', MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
  }
  const orderId = x.orderId;
  if (!_isString(orderId)) {
    throw new MError('orderId-string-expected', MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
  }
  const invenId = x.invenId;
  if (!_isString(invenId)) {
    throw new MError('invenId-string-expected', MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
  }
}

function _ensureChargedCoinList(
  x: any,
  extra: JsonLike,
  userId: number
): asserts x is {
  paymentType: string;
  coinCd: string;
  balance: number;
}[] {
  if (!Array.isArray(x)) {
    new MError('array-expected', MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
  }

  for (const elem of x) {
    if (!Number.isInteger(elem.balance)) {
      new MError('balance-integer-expected', MErrorCode.BILLING_API_UNEXPECTED_RESP, extra);
    }

    // 이런 경우가 없긴 할 텐데 문서에 bigint 라고 되어있어서.
    const bInMaxSafeInteger = Math.abs(elem.balance) <= Number.MAX_SAFE_INTEGER;
    if (!bInMaxSafeInteger) {
      mlog.warn('balance, Precision may be lost!', {
        userId,
        elem,
      });
    }
  }
}

export function ReceiveProducts(
  ensuredGiveItemsList: Iterable<EnsuredGiveItem[]>,
  resp: ResponseBody,
  mailIds: number[],

  user: User,
  curTimeUtc: number,
  isBound: 0 | 1 = 1
) {
  return _receiveProducts(ensuredGiveItemsList, resp, mailIds, user, curTimeUtc, isBound);
}

/**
 * 먼저 빌링 서버에 완료 통보를 하기 때문에, 이 함수에 진입하게 된다면 어떻게든 지급해야한다.
 * 못 받는 경우는 메일로 이동시키다.
 *
 * user RDB, 메모리, 업적 적용.
 * * UserChangeTask 사용을 고려해봐야함.
 */
async function _receiveProducts(
  ensuredGiveItemsList: Iterable<EnsuredGiveItem[]>,
  resp: ResponseBody,
  mailIds: number[],

  user: User,
  curTimeUtc: number,
  isBound: 0 | 1 = 1
) {
  interface ReceiveYard {
    itemChanges: { [itemCmsId: number]: ItemChange };

    newLastShipId: number;
    addedShips: ShipNub[];

    eventPageProduct: {
      eventPageCmses: EventPageDesc[];
      changes: { [offset: number]: number };
    };

    newLastMateEquipmentId: number;
    addedMateEquipments: MateEquipmentNub[];

    newLastShipSlotId: number;
    addedShipSlots: ShipSlotItem[];

    newLastMailId: number;
    addedMailParams: MailCreatingParams[];

    dailySubscriptions: { [cmsId: number]: DailySubscription };

    addedMateIllusts: number[];

    addedMates: MateNub[];

    addedUserTitles: { [cmsId: number]: UserTitle };

    // 행동력은 cmsEx.EnergyPointCmsId 이거 하나임
    energyChange?: EnergyChange;

    pointChanges: { [cmsId: number]: PointChange };

    fixedTermProducts: FixedTermProduct[];
    worldBuffNubs: WorldBuffNub[];

    addedMatePassives: { mateCmsId: number; passiveCmsId: number }[];

    addedPets: number[];
  }

  const yard: ReceiveYard = {
    itemChanges: {},

    newLastShipId: undefined,
    addedShips: [],

    eventPageProduct: undefined,

    newLastMateEquipmentId: undefined,
    addedMateEquipments: [],

    newLastShipSlotId: undefined,
    addedShipSlots: [],

    newLastMailId: undefined,
    addedMailParams: [],

    dailySubscriptions: {},

    addedMateIllusts: [],

    addedMates: [],

    addedUserTitles: {},

    pointChanges: {},

    fixedTermProducts: [],
    worldBuffNubs: [],

    addedMatePassives: [],

    addedPets: [],
  };

  const tryData: {
    userInven?: UserInven;
    mates?: UserMates;
    cashShop?: UserCashShop;
    userTitles?: UserTitles;
    userPoints?: UserPoints;
    userEnergy?: UserEnergy;
    userPets?: UserPets;
  } = {};
  const lastMailId = user.userMails.getLastDirectMailId();
  const lastShipId = user.userFleets.getLastShipId();
  const lastMateEquipmentId = user.userMates.getLastMateEquipmentId();
  const lastShipSlotId = user.userInven.getLastShipSlotItemId();

  for (const ensuredGiveItems of ensuredGiveItemsList) {
    for (const ensuredGiveItem of ensuredGiveItems) {
      switch (ensuredGiveItem.type) {
        case BILLING_GIVE_ITEM_TYPE.EVENT_PAGE: {
          if (!yard.eventPageProduct) {
            yard.eventPageProduct = {
              eventPageCmses: [],
              changes: {},
            };
          }
          const eventPageCms = cms.EventPage[ensuredGiveItem.id];
          if (
            (eventPageCms.type === EventPageType.PASS_EVENT &&
              user.userCashShop.hasPassEventTicket(eventPageCms)) ||
            (eventPageCms.type === EventPageType.PACKAGE_EVENT &&
              user.userCashShop.isPackageEventUnlocked(eventPageCms))
          ) {
            // TODO 특권 같은 상품이 활성화된 상태에서 구입되어 보관함에 들어간 경우에 대해 고려해봐야함.
            mlog.error(`[${rsn}] already-bought-event-page-product-so-ignored`, {
              userId: user.userId,
              eventPgaeCmsId: eventPageCms.id,
            });
            break;
          }

          yard.eventPageProduct.eventPageCmses.push(eventPageCms);
          const offset = Math.floor(eventPageCms.id / 32);
          if (yard.eventPageProduct.changes[offset] === undefined) {
            yard.eventPageProduct.changes[offset] =
              user.userCashShop.getEventPageProductIdxField(offset);
          }
          yard.eventPageProduct.changes[offset] =
            (yard.eventPageProduct.changes[offset] | (1 << eventPageCms.id % 32)) >>> 0;
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.ITEM: {
          if (!tryData.userInven) {
            tryData.userInven = user.userInven.clone();
          }

          const itemCms = cms.Item[ensuredGiveItem.id];
          const amount = ensuredGiveItem.amount;

          const bIsBound = isBound !== 0 ? true : false;
          const ret = tryData.userInven.itemInven.calcReceivable(itemCms.id, amount, bIsBound);
          if (ret.receivable === amount) {
            yard.itemChanges[itemCms.id] = tryData.userInven.itemInven.buildItemChange(
              itemCms.id,
              amount,
              true,
              bIsBound
            );
          } else {
            const extra: RewardCmsElemItemExtra = {
              isBound,
              isAccum: 1,
            };
            const attachment: RewardCmsElem = {
              Type: REWARD_TYPE.ITEM,
              Id: itemCms.id,
              Quantity: amount,
              Extra: JSON.stringify(extra),
            };

            const mail = _buildMailParam(
              lastMailId + yard.addedMailParams.length + 1,
              cms.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
              curTimeUtc,
              JSON.stringify([attachment])
            );
            yard.newLastMailId = mail.id;
            yard.addedMailParams.push(mail);
          }
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.SHIP: {
          if (!tryData.userInven) {
            tryData.userInven = user.userInven.clone();
          }

          const shipCms = cms.Ship[ensuredGiveItem.id];
          const amount = 1; // elem.amount;

          const rndStats: number[] = makeShipRndStats(
            shipCms.shipBlueprintId,
            cms.Const.CashShipBlueprintRatio.value
          );
          const defaultDurability: number = cmsEx.shipDefaultDurability(
            shipCms.shipBlueprintId,
            rndStats
          );
          const defaultLife: number = cmsEx.shipDefaultLife(shipCms.shipBlueprintId);
          const shipExtra = {
            enchantCount: 0,
            enchantedStats: [] as ShipEnchantedStat[],
            enchantResult: null as string,
            life: defaultLife,
            rndStats,
            isBound,
          };

          const curDockShipsCount =
            user.userFleets.getShipsCountByAssignment(SHIP_ASSIGNMENT.DOCK) +
            yard.addedShips.length;
          const maxDockShipCount = Fleet.getMaxShipCount(
            SHIP_ASSIGNMENT.DOCK,
            cmsEx.NoFleetIndex,
            user.level,
            tryData.userInven
          );

          if (curDockShipsCount < maxDockShipCount) {
            const shipId = lastShipId + yard.addedShips.length + 1;
            const ship: ShipNub = {
              id: shipId,
              cmsId: shipCms.id,
              assignment: SHIP_ASSIGNMENT.DOCK,
              fleetIndex: cmsEx.NoFleetIndex,
              formationIndex: 0,
              sailor: 0,
              durability: defaultDurability,
              permanentDamage: 0,
              slots: {},
              cargos: {},
              name: null,
              life: shipExtra.life,
              isLocked: 0,
              enchantedStats: shipExtra.enchantedStats,
              enchantResult: shipExtra.enchantResult,
              enchantCount: shipExtra.enchantCount,
              rndStats: shipExtra.rndStats,
              battleQuickSkills: Ship.defaultBattleQuickSkills(),
              isBound,
              guid: `${user.userId}:${shipId}`,
            };
            yard.newLastShipId = ship.id;
            yard.addedShips.push(ship);
          } else {
            const extra: RewardCmsElemShipExtra = {
              ...shipExtra,
              guid: undefined, // 메일을 받는 시점에 만들어져야 할 듯.
              isAccum: 1,
            };
            const attachment: RewardCmsElem = {
              Type: REWARD_TYPE.SHIP,
              Id: shipCms.id,
              Quantity: amount,
              Extra: JSON.stringify(extra),
            };
            const mail = _buildMailParam(
              lastMailId + yard.addedMailParams.length + 1,
              cms.Const.CashShopShipMailId.value,
              curTimeUtc,
              JSON.stringify([attachment])
            );

            yard.newLastMailId = mail.id;
            yard.addedMailParams.push(mail);
          }
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.CEQUIP: {
          if (!tryData.userInven) {
            tryData.userInven = user.userInven.clone();
          }
          if (!tryData.mates) {
            tryData.mates = user.userMates.clone();
          }

          const equipCms = cms.CEquip[ensuredGiveItem.id];
          const amount = ensuredGiveItem.amount;

          const addable = tryData.mates.calcMateEquipmentAddableForSpace(
            equipCms,
            amount,
            tryData.userInven,
            0
          );

          // 항해사 외형 장비 => 현금 결제 상품은  비귀속 처리
          if (cmsEx.isCostumeEquipType(equipCms.type)) {
            isBound = 0;
          }

          if (addable === amount) {
            for (let i = 0; i < amount; i++) {
              const newItem = tryData.mates.buildMateEquipmentNub(
                equipCms.id,
                0,
                isBound,
                0,
                curTimeUtc
              );

              yard.newLastMateEquipmentId = newItem.id;
              yard.addedMateEquipments.push(newItem);
              tryData.mates.addMateEquipment(newItem, null);
            }
          } else {
            const extra: RewardCmsElemMateEquipmentExtra = {
              isBound,
              isAccum: 1,
              expireTimeUtc:
                equipCms.expireType === cmsEx.EXPIRE_TYPE.PROVIDE
                  ? curTimeUtc + equipCms.expireTime
                  : undefined,
              enchantLv: 0,
            };
            const attachment: RewardCmsElem = {
              Type: REWARD_TYPE.MATE_EQUIP,
              Id: equipCms.id,
              Quantity: amount,
              Extra: JSON.stringify(extra),
            };

            const mail = _buildMailParam(
              lastMailId + yard.addedMailParams.length + 1,
              cms.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
              curTimeUtc,
              JSON.stringify([attachment])
            );
            yard.newLastMailId = mail.id;
            yard.addedMailParams.push(mail);
          }
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.SHIP_SLOT: {
          if (!tryData.userInven) {
            tryData.userInven = user.userInven.clone();
          }

          const shipSlotCms = cms.ShipSlot[ensuredGiveItem.id];
          const amount = ensuredGiveItem.amount;
          const added = tryData.userInven.calcShipSlotItemAddable(amount);
          if (added === amount) {
            for (let i = 0; i < amount; i++) {
              const shipSlotItem = tryData.userInven.buildShipSlotItem(
                shipSlotCms.id,
                isBound,
                0,
                curTimeUtc
              );
              yard.addedShipSlots.push(shipSlotItem);
              yard.newLastShipSlotId = shipSlotItem.id;
              tryData.userInven.addShipSlotItem(shipSlotItem, null);
            }
          } else {
            const extra: RewardCmsElemShipSlotItemExtra = {
              isBound,
              isAccum: 1,
              expireTimeUtc:
                shipSlotCms.expireType === cmsEx.EXPIRE_TYPE.PROVIDE
                  ? curTimeUtc + shipSlotCms.expireTime
                  : undefined,
              enchantLv: 0,
            };
            const attachment: RewardCmsElem = {
              Type: REWARD_TYPE.SHIP_SLOT_ITEM,
              Id: shipSlotCms.id,
              Quantity: amount,
              Extra: JSON.stringify(extra),
            };

            const mail = _buildMailParam(
              lastMailId + yard.addedMailParams.length + 1,
              cms.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
              curTimeUtc,
              JSON.stringify([attachment])
            );
            yard.newLastMailId = mail.id;
            yard.addedMailParams.push(mail);
          }
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.DAILY_SUBSCRIPTION: {
          if (!tryData.cashShop) {
            tryData.cashShop = user.userCashShop.clone();
          }

          const dailySubscriptionCms = cms.DailySubscription[ensuredGiveItem.id];

          tryData.cashShop.buyDailySubscription(dailySubscriptionCms, curTimeUtc);
          const userDS = tryData.cashShop.getDailySubscription(ensuredGiveItem.id, curTimeUtc);

          yard.dailySubscriptions[ensuredGiveItem.id] = {
            cmsId: ensuredGiveItem.id,
            createTimeUtc: userDS.createTimeUtc,
            expireTimeUtc: userDS.expireTimeUtc,
            lastReceiveTimeUtc: userDS.lastReceiveTimeUtc,
          };
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.ILLUST_SKIN: {
          yard.addedMateIllusts.push(ensuredGiveItem.id);
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.MATE: {
          if (!tryData.mates) {
            tryData.mates = user.userMates.clone();
          }

          const mateCms = cms.Mate[ensuredGiveItem.id];
          if (tryData.mates.hasMate(ensuredGiveItem.id)) {
            if (!tryData.userInven) {
              tryData.userInven = user.userInven.clone();
            }

            const itemCms = cms.Item[mateCms.reRecruitRewardItemId];
            const amount =
              cms.Const.CashShopDuplicatedMateToPiece.value +
              (yard.itemChanges[itemCms.id]?.count ?? 0);
            const ret = tryData.userInven.itemInven.calcReceivable(itemCms.id, amount, true);
            if (ret.receivable === amount) {
              yard.itemChanges[itemCms.id] = tryData.userInven.itemInven.buildItemChange(
                itemCms.id,
                amount,
                true,
                true
              );
            } else {
              const extra: RewardCmsElemItemExtra = {
                isBound: 1,
                isAccum: 1,
              };
              const attachment: RewardCmsElem = {
                Type: REWARD_TYPE.ITEM,
                Id: itemCms.id,
                Quantity: amount,
                Extra: JSON.stringify(extra),
              };

              const mail = _buildMailParam(
                lastMailId + yard.addedMailParams.length + 1,
                cms.Const.CashShopCEquipMailId.value, // 아이템도 장비와 동일한 Const 사용한다고 함.
                curTimeUtc,
                JSON.stringify([attachment])
              );
              yard.newLastMailId = mail.id;
              yard.addedMailParams.push(mail);
            }
          } else {
            const mateNub = Mate.defaultNub(ensuredGiveItem.id);
            yard.addedMates.push(mateNub);
            tryData.mates.addNewMate(mateNub, null, null, undefined, undefined);

            if (mateCms.CEquipId && mateCms.CEquipId.length > 0) {
              for (const cequipCmsId of mateCms.CEquipId) {
                const newItem = tryData.mates.buildMateEquipmentNub(
                  cequipCmsId,
                  mateCms.id,
                  1,
                  0,
                  curTimeUtc
                );

                yard.newLastMateEquipmentId = newItem.id;
                yard.addedMateEquipments.push(newItem);

                tryData.mates.addMateEquipment(newItem, null);
                tryData.mates.equipMateEquipment(
                  newItem.equippedMateCmsId,
                  newItem.id,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined,
                  undefined
                );
              }
            }
            const addedPassives = MateUtil.getRelationChronicleLearnablePassives(
              tryData.mates,
              user.questManager,
              mateCms.id
            );

            if (addedPassives) {
              yard.addedMatePassives.push(...addedPassives);
            }
          }

          break;
        }
        case BILLING_GIVE_ITEM_TYPE.SERVER_TRANFER: {
          // 서버 이전권 상품의 경우 라인측에 수령 완료 통보만 하고 구성품 수령 작업은 하지 않는다.
          // 빌링 툴에서 상품을 등록하려면 최소 1개의 구성품을 등록해야하는 이슈가 있음.
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.USER_TITLE: {
          if (!tryData.userTitles) {
            tryData.userTitles = user.userTitles.clone();
          }

          const newUserTitle: UserTitle = tryData.userTitles.buildUserTitle(
            ensuredGiveItem.id,
            curTimeUtc
          );

          tryData.userTitles.setUserTitle(newUserTitle, null);

          yard.addedUserTitles[ensuredGiveItem.id] = {
            cmsId: newUserTitle.cmsId,
            expiredTimeUtc: newUserTitle.expiredTimeUtc,
            isEquipped: newUserTitle.isEquipped,
          };
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.POINT: {
          const pointCms = cms.Point[ensuredGiveItem.id];
          const amount = ensuredGiveItem.amount;

          if (pointCms.id === cmsEx.EnergyPointCmsId) {
            if (!tryData.userEnergy) {
              tryData.userEnergy = user.userEnergy.clone();
            }

            yard.energyChange = tryData.userEnergy.buildEnergyChangeWithConsume(
              curTimeUtc,
              user.level,
              user.level,
              -amount,
              false
            );

            tryData.userEnergy.applyEnergyChange(yard.energyChange, { user, rsn, add_rsn });
          } else {
            if (!tryData.userPoints) {
              tryData.userPoints = user.userPoints.clone();
            }

            if (!yard.pointChanges[pointCms.id]) {
              yard.pointChanges[pointCms.id] = {
                cmsId: pointCms.id,
                value: tryData.userPoints.getPoint(pointCms.id),
              };
            }
            yard.pointChanges[pointCms.id].value += amount;

            tryData.userPoints.applyPointChanges(_.values(yard.pointChanges), {
              user,
              rsn,
              add_rsn,
            });
          }
          break;
        }

        case BILLING_GIVE_ITEM_TYPE.WORLD_BUFF: {
          const cashShopCms = cms.CashShop[ensuredGiveItem.id];

          const oldBuffProduct = user.userCashShop.getFixedTermProductByGroup(
            cashShopCms.buffGroup,
            curTimeUtc
          );

          // 현금 상품에서 지급하는 버프는 보관함에서 수령하는 타이밍때문에 buffGroupLevel를 적용할 수 없어서 레벨에 관한 처리는 무시한다
          let newFixedTermProduct: FixedTermProduct = {
            cmsId: cashShopCms.id,
            startTimeUtc: oldBuffProduct ? oldBuffProduct.startTimeUtc : curTimeUtc,
            endTimeUtc: oldBuffProduct ? oldBuffProduct.endTimeUtc : curTimeUtc,
          };
          
          // 만약에 기획 데이터 실수로 중복되는 버프가 이미 있어도 시간 더해 준다.
          let addEndTimeUtc = getWorldBuffAddTime(cashShopCms);
          newFixedTermProduct.endTimeUtc += addEndTimeUtc

          yard.fixedTermProducts.push(newFixedTermProduct);
          for (const buffElem of cashShopCms.worldBuffElems) {
            const worldBuffCms = cms.WorldBuff[buffElem.worldBuffId];

            // worldBuffElem 에 각각 시간 타임 값을 추가 했지만..
            // 기존 처럼 상점 시간을 사용한다.
            const startTimeUtc = newFixedTermProduct.startTimeUtc;
            const endTimeUtc = newFixedTermProduct.endTimeUtc;

            const wbNub = WorldBuffUtil.makeNubWithCustomEndTime(
              worldBuffCms,
              cmsEx.FirstFleetIndex,
              cmsEx.WorldBuffSourceType.BILLING,
              cashShopCms.id,
              startTimeUtc,
              endTimeUtc
            );

            yard.worldBuffNubs.push(wbNub);
          }

          break;
        }
        case BILLING_GIVE_ITEM_TYPE.PET: {
          if (!tryData.userPets) {
            tryData.userPets = user.userPets.clone();
          }

          const petCms = cms.Pet[ensuredGiveItem.id];
          tryData.userPets.addNewPet(petCms.id);
          yard.addedPets.push(petCms.id);
          break;
        }
        case BILLING_GIVE_ITEM_TYPE.DUMMY:
          break;
        default:
          // 모든 case 가 직접 대응되어야 됨.
          assert.fail('all-billing-give-item-type-must-be-mapped');
      }
    }
  }

  return Promise.resolve()
    .then(() => {
      const { userDbConnPoolMgr } = Container.get(LobbyService);
      return tuBillingReceiveInvenPurchases(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        _.values(yard.itemChanges),
        yard.newLastShipId,
        yard.addedShips,
        yard.eventPageProduct?.changes
          ? _.map(yard.eventPageProduct?.changes, (idxField, strOffset) => {
              return {
                offset: parseInt(strOffset, 10),
                idxField,
              };
            })
          : undefined,
        yard.newLastMateEquipmentId,
        yard.addedMateEquipments,
        yard.newLastShipSlotId,
        yard.addedShipSlots,
        yard.newLastMailId,
        yard.addedMailParams,
        Object.values(yard.dailySubscriptions),
        yard.addedMateIllusts,
        yard.addedMates,
        Object.values(yard.addedUserTitles),
        yard.energyChange,
        Object.values(yard.pointChanges),
        yard.fixedTermProducts,
        yard.addedMatePassives,
        yard.addedPets
      );
    })
    .then(() => {
      const accums: AccumulateParam[] = [];

      if (yard.eventPageProduct) {
        for (const eventPageCms of yard.eventPageProduct.eventPageCmses) {
          user.userCashShop.addEventPageProduct(eventPageCms);
        }
        _.merge<Sync, Sync>(resp.sync, {
          add: { eventPageProducts: yard.eventPageProduct.changes },
        });
      }

      _.forOwn(yard.itemChanges, (change) => {
        _.merge<Sync, Sync>(
          resp.sync,
          user.userInven.itemInven.applyItemChange(change, accums, { user, rsn, add_rsn })
        );
      });

      for (const addedShip of yard.addedShips) {
        user.userFleets.addNewShip(
          addedShip,
          user.companyStat,
          user.userShipBlueprints,
          user.userMates,
          user.userFleets,
          user.userInven,
          user.userBuffs,
          user.userPassives,
          user.userCollection,
          user.questManager,
          user.userSailing,
          user.userTriggers,
          user.userNation,
          user.userResearch,
          { user, rsn, add_rsn },
          resp.sync
        );
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            ships: {
              [addedShip.id]: addedShip,
            },
          },
        });
      }

      for (const elem of yard.addedMateEquipments) {
        _.merge<Sync, Sync>(
          resp.sync,
          user.userMates.addMateEquipment(elem, { user, rsn, add_rsn })
        );
      }

      for (const elem of yard.addedShipSlots) {
        user.userInven.addShipSlotItem(elem, { user, rsn, add_rsn });
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            shipSlotItems: {
              [elem.id]: {
                id: elem.id,
                isBound: elem.isBound,
                shipSlotCmsId: elem.shipSlotCmsId,
                isLocked: elem.isLocked,
                expireTimeUtc: elem.expireTimeUtc,
                enchantLv: elem.enchantLv,
              },
            },
          },
        });
      }

      for (const mailCreatingParam of yard.addedMailParams) {
        const mail = user.userMails.addDirectMail(mailCreatingParam, { user, rsn, add_rsn });
        _.merge<Sync, Sync>(resp.sync, {
          add: { userDirectMails: { [mail.id]: user.userMails.getDirectMailSyncData(mail.id) } },
        });
        mailIds.push(mail.id);
      }

      _.forOwn(yard.dailySubscriptions, (elem) => {
        user.userCashShop.setDailySubscription(elem);
        const userDS = user.userCashShop.getDailySubscription(elem.cmsId, curTimeUtc);
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            dailySubscriptions: {
              [elem.cmsId]: userDS,
            },
          },
        });
      });

      for (const illustCmsId of yard.addedMateIllusts) {
        const illustSkinCms = cms.IllustSkin[illustCmsId];
        const userMate = user.userMates.getMate(illustSkinCms.mateId);
        userMate.addIllust(user, illustCmsId, resp.sync);
      }

      for (const mateNub of yard.addedMates) {
        const mateCmsId = mateNub.cmsId;
        user.userMates.addNewMate(mateNub, user.companyStat, user, { user, rsn, add_rsn }, resp);
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            mates: {
              [mateCmsId]: user.userMates.getMate(mateCmsId).getNub(),
            },
          },
        });
      }

      for (const elem of yard.addedMatePassives) {
        const userMate: Mate = user.userMates.getMate(elem.mateCmsId);
        userMate.addPassive(elem.passiveCmsId, 0, resp.sync);
      }

      _.forOwn(yard.addedUserTitles, (userTitle) => {
        if (userTitle.isEquipped) {
          user.userTitles.equipUserTitle(user, userTitle, curTimeUtc, resp);
          user.userTitles.notifyCurTitleChanged(user, curTimeUtc);
        } else {
          user.userTitles.setUserTitle(userTitle, resp);
        }
      });

      if (yard.energyChange) {
        _.merge<Sync, Sync>(
          resp.sync,
          user.userEnergy.applyEnergyChange(yard.energyChange, { user, rsn, add_rsn })
        );
      }

      if (yard.pointChanges) {
        _.merge<Sync, Sync>(
          resp.sync,
          user.userPoints.applyPointChanges(_.values(yard.pointChanges), { user, rsn, add_rsn })
        );
      }

      if (yard.fixedTermProducts.length > 0) {
        const cashShopFixedTermProducts: { [cmsId: number]: CashShopFixedTermProduct } = {};

        for (const fixedTermProduct of yard.fixedTermProducts) {
          user.userCashShop.setFixedTermProduct(fixedTermProduct);
          cashShopFixedTermProducts[fixedTermProduct.cmsId] = fixedTermProduct;
        }

        _.merge<Sync, Sync>(resp.sync, {
          add: { cashShopFixedTermProducts: cashShopFixedTermProducts },
        });
      }

      for (const wbNub of yard.worldBuffNubs) {
        user.userBuffs.addSingleBuffByWBNub(wbNub, user, resp);
      }

      for (const petCmsId of yard.addedPets) {
        user.userPets.addNewPet(petCmsId);
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            pets: {
              [petCmsId]: {
                cmsId: petCmsId,
              },
            },
          },
        });
      }

      // CMS.AchievementTerms
      const shipsGroupedByCmsId = _.groupBy(yard.addedShips, (v) => v.cmsId);
      _.forOwn(shipsGroupedByCmsId, (ships, strShipCmsId) => {
        const shipCms = cms.Ship[strShipCmsId];
        const shipCount = ships.length;

        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SHIP,
          addedValue: shipCount,
        });
        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_SHIP,
          targets: [shipCms.id],
          addedValue: shipCount,
        });

        const userBP = user.userShipBlueprints.getUserShipBlueprint(shipCms.shipBlueprintId);
        const userBpLv = userBP ? userBP.level : 1;
        for (let bpLv = 1; bpLv <= userBpLv; bpLv++) {
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SHIP,
            targets: [bpLv],
            addedValue: shipCount,
          });
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP,
            targets: [shipCms.id, bpLv],
            addedValue: shipCount,
          });
        }
      });

      for (const mateNub of yard.addedMates) {
        const mateCmsId = mateNub.cmsId;
        const mateCms = cms.Mate[mateCmsId];
        accums.push(
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_MATE,
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_NATION_MATE,
            targets: [mateCms.nationId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_MATE,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
            targets: [mateCmsId],
            addedValue: 3,
          }
        );

        // language
        for (let i = 1; i <= cmsEx.getMateHighestLanguageLevel(mateCmsId); i++) {
          const accum: AccumulateParam = {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.MATE_LANGUAGE_LEVEL,
            targets: [i],
            addedValue: 1,
          };
          accums.push(accum);
        }

        // admiral
        if (mateCms.character.charType === cmsEx.CHARACTER_TYPE.LEADERABLE_MATE) {
          accums.push(
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_ADMIRAL,
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_ADMIRAL,
              targets: [mateCmsId],
              addedValue: 1,
            }
          );

          // royal title
          const admiralCms = cmsEx.getAdmiralByMateCmsId(mateCmsId);
          const mateRoyalTitle = admiralCms.startRoyalTitelId;
          let targets = cmsEx.getAchievementTermsTargets(cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE, 0);

          if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
            for (const target of targets) {
              if (mateRoyalTitle < target) {
                break;
              }
              if (target !== mateRoyalTitle) {
                continue;
              }

              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE,
                targets: [target],
                addedValue: 1,
              });
            }
          }

          targets = cmsEx.getAchievementTermsTargets(
            cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
            1
          );
          if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
            for (const target of targets) {
              if (mateRoyalTitle < target) {
                break;
              }
              if (target !== mateRoyalTitle) {
                continue;
              }

              accums.push({
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
                targets: [mateCmsId, target],
                addedValue: 1,
              });
            }
          }
        }
      }

      // glog
      _.forOwn(yard.dailySubscriptions, (elem) => {
        const expireDate = new Date(elem.expireTimeUtc * 1000);
        user.glog('common_active_iap', {
          rsn,
          add_rsn,
          id: elem.cmsId,
          expire_date: moment(expireDate).format('YYYY-MM-DD HH:mm:ss'),
        });
      });

      return user.userAchievement.accumulate(accums, user, resp.sync, {
        user,
        rsn,
        add_rsn,
      });
    });
}

function _buildMailParam(
  mailId: number,
  mailCmsId: number,
  curTimeUtc: number,
  attachment: string
): MailCreatingParams {
  const mailCms = cms.Mail[mailCmsId];
  let expireTimeUtc = null;
  let bShouldSetExpirationWhenReceiveAttachment = 0;
  if (mailCms.mailKeepTime > 0) {
    expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
  } else if (mailCms.mailKeepTime === -1) {
    bShouldSetExpirationWhenReceiveAttachment = 1;
  }

  return new BuilderMailCreateParams(
    mailId,
    mailCms.id,
    curTimeUtc,
    expireTimeUtc,
    bShouldSetExpirationWhenReceiveAttachment,
    null,
    null,
    null,
    null,
    attachment
  ).getParam();
}
