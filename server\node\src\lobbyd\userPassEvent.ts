// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';

import cms from '../cms';
import * as cmsEx from '../cms/ex';
import { HasContentsResetTimePassed, SECONDS_PER_HOUR } from '../formula';
import mlog from '../motiflib/mlog';
import mconf from '../motiflib/mconf';
import * as mutil from '../motiflib/mutil';
import { MError, MErrorCode } from '../motiflib/merror';
import { All } from './type/sync';
import { LoginInfo, GLogParam } from './user';
import { EventMissionDesc, EventMissionType } from '../cms/eventMissionDesc';
import { EventPageDesc, EventPageType } from '../cms/eventPageDesc';
import { PrData } from '../motiflib/gameLog';
import { GLogCashParam } from './userPoints';

interface PassEvent {
  eventPageCmsId: number;

  /** 없으면 0, DB 기본값도 0인 것 참고. */
  exp: number;
  /** 없으면 1, DB 기본값도 1인 것 참고. */
  level: number;

  /** '일일 임무'의 갱신을 위함. */
  lastDailyResetTimeUtc: number | null;
  /** '일일 임무'/'기본 보상'/'추가 보상'이 섞여있는 것 유의  */
  eventMissions: { [eventMissionCmsId: number]: PassEventMission };

  /**
   * {@link EventPageDesc.completedRemove} 플래그가 true 이면
   * '기본 보상'/'추가 보상' 타입인 EventMission 보상을 전부 받았는지 여부로
   * 이벤트 완료 여부를 판단하는 기획이 있음.
   * ('이벤트 과제' 타입은 일일 갱신됨.)
   *
   * - {@link UserPassEvent._setCompleteIfNeeded} 참고.
   */
  bCompleted: boolean;
}

export type PassEventMission = {
  eventMissionCmsId: number;
  count: number;
  repeatedRewardReceiveCount: number;
  isRewarded: number;
};

export interface PassEventMissionChange {
  eventPageCmsId: number;
  eventMissionCmsId: number;
  count: number;
  repeatedRewardReceiveCount: number;
  isRewarded: 0 | 1;
}

/**
 * {@link https://wiki.line.games/pages/viewpage.action?pageId=60311650 이벤트 과제}의 패스 임무
 */
class UserPassEvent {
  private _passEvents: { [eventPageCmsId: number]: PassEvent };

  constructor() {
    this._passEvents = {};
  }

  clone(): UserPassEvent {
    const c = new UserPassEvent();
    c.cloneSet(_.cloneDeep(this._passEvents));
    return c;
  }

  cloneSet(passEvents: { [eventPageCmsId: number]: PassEvent }): void {
    this._passEvents = passEvents;
  }

  initWithLoginInfo(loginInfo: LoginInfo): void {
    for (const elem of loginInfo.passEvents) {
      const passEventPageCms = cms.EventPage[elem.eventPageCmsId];
      if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
        // 굳이 assert 까지는 필요 없을 듯.
        mlog.error('non-pass-event-page-loaded!', {
          elem,
          userId: loginInfo.userId,
        });
      }

      this._passEvents[passEventPageCms.id] = {
        eventPageCmsId: passEventPageCms.id,

        exp: elem.exp,
        level: elem.level,

        lastDailyResetTimeUtc: elem.lastDailyResetTimeUtc
          ? parseInt(elem.lastDailyResetTimeUtc, 10)
          : null,
        eventMissions: {},

        bCompleted: false,
      };
    }

    for (const elem of loginInfo.passEventMissions) {
      const passEventPageCms = cms.EventPage[elem.eventPageCmsId];
      if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
        // 굳이 assert 까지는 필요 없을 듯.
        mlog.error('non-pass-event-page-loaded!!', {
          elem,
          userId: loginInfo.userId,
        });
      }

      if (!this._passEvents[passEventPageCms.id]) {
        this._passEvents[passEventPageCms.id] = UserPassEvent._defaultPassEvent(
          passEventPageCms.id
        );
      }
      this._passEvents[passEventPageCms.id].eventMissions[elem.eventMissionCmsId] = {
        eventMissionCmsId: elem.eventMissionCmsId,
        count: parseInt(elem.count, 10), // CMS.EventMission.type 에 따라 사용되지 않을 수 있음.
        repeatedRewardReceiveCount: elem.repeatedRewardReceiveCount,
        isRewarded: elem.isRewarded,
      };
    }

    _.forOwn(this._passEvents, (passEvent) => {
      const eventPageCms = cms.EventPage[passEvent.eventPageCmsId];

      assert(
        passEvent.lastDailyResetTimeUtc === null ||
          Number.isInteger(passEvent.lastDailyResetTimeUtc)
      );
      assert(
        (passEvent.exp === undefined && passEvent.level === undefined) ||
          (Number.isInteger(passEvent.exp) && Number.isInteger(passEvent.level))
      );

      UserPassEvent._setCompleteIfNeeded(eventPageCms, passEvent);
    });
  }

  private static _defaultPassEvent(eventPageCmsId: number): PassEvent {
    // optional 지정하면 두루뭉실한 거 같아서 만들었는데, 굳이 필요 없을 거 같기도
    return {
      eventPageCmsId,

      exp: undefined,
      level: undefined,

      lastDailyResetTimeUtc: null,
      eventMissions: {},

      bCompleted: false,
    };
  }

  private static _setCompleteIfNeeded(eventPageCms: EventPageDesc, passEvent: PassEvent): void {
    if (eventPageCms.completedRemove) {
      const eventMissionCmsesForCompleteCond =
        cmsEx
          .getEventMissionCmses(eventPageCms.groupRef)
          ?.filter(
            (elem) =>
              elem.type === EventMissionType.BASE_REWARD ||
              elem.type === EventMissionType.ADDITIONAL_REWARD ||
              elem.type === EventMissionType.REPEATED_REWARD
          ) ?? [];

      const userEventMissions: PassEvent['eventMissions'] = passEvent.eventMissions ?? {};
      const bCompleted = eventMissionCmsesForCompleteCond.every(
        (elem) => userEventMissions[elem.id] && userEventMissions[elem.id].isRewarded
      );

      if (bCompleted) {
        passEvent.bCompleted = true;

        // 굳이 undefined 로 만들 필요가 있을지?
        // 로그인 시에 sync 로 보내주지만 않도록 하는 게 나을지?
        passEvent.eventMissions = undefined;
      }
    }
  }

  getLastDailyResetTimeUtc(eventPageCmsId: number): number | null {
    if (!this._passEvents[eventPageCmsId]) {
      return null;
    }
    const ret = this._passEvents[eventPageCmsId].lastDailyResetTimeUtc;
    assert(Number.isInteger(ret) || ret === null);
    return ret;
  }

  isPassEventCompleted(eventPageCmsId: number): boolean {
    return this._passEvents[eventPageCmsId]?.bCompleted ? true : false;
  }

  isDailyResetNeeded(eventPageCmsId: number, curTimeUtc: number): boolean {
    const passEvent = this._passEvents[eventPageCmsId];
    if (!passEvent || passEvent.lastDailyResetTimeUtc === null) {
      // lastDailyResetTimeUtc 를 설정해주어야함.
      return true;
    }
    assert(Number.isInteger(passEvent.lastDailyResetTimeUtc));
    return HasContentsResetTimePassed(
      curTimeUtc,
      passEvent.lastDailyResetTimeUtc,
      cms.ContentsResetHour.EventPageReset.hour
    );
  }

  /**
   * 임무 목록이 아니라 "누적"/"보상 기록"에 대한 것임에 유의
   * 반환 받은 오브젝트를 가공한다면, {@link PassEvent.bCompleted} 유의해야함.
   * 읽기 전용으로 생각하고 만들었는데, 직관적이지 않은 듯..
   */
  getPassEventMissions(eventPageCmsId: number): PassEvent['eventMissions'] {
    if (this._passEvents[eventPageCmsId]) {
      // 모두 완료된 것은 목록을 제거한 상태임. 사용해야한다면 관련 처리가 필요함.
      assert(!this._passEvents[eventPageCmsId].bCompleted);
    }

    return this._passEvents[eventPageCmsId]?.eventMissions ?? {};
  }

  /**
   * - 주어진 인자로 모두 갈아치우는 게 아님에 유의.
   * - bCompleted 관련해서 sync 넣어주는 부분 누락되지 않도록 유의해야함.
   *   ( sync 에 굳이 목록 remove 할 필요는 없을 듯? )
   */
  applyPassEventMissions(
    passEventPageCms: EventPageDesc,
    passEventMissions: readonly Readonly<PassEventMission>[]
  ): void {
    assert(passEventPageCms.type === EventPageType.PASS_EVENT);

    if (!this._passEvents[passEventPageCms.id]) {
      this._passEvents[passEventPageCms.id] = UserPassEvent._defaultPassEvent(passEventPageCms.id);
    }

    const passEvent = this._passEvents[passEventPageCms.id];
    if (passEvent.bCompleted) {
      // 완료된 상태에서 변경할 필요가 있다면 다른 처리가 필요함.
      // 완료되었다면 목록을 undefined 로 지우고 있기도 함.
      assert.fail('pass-event-page-already-completed');
    }
    const userEventMissions = passEvent.eventMissions;

    for (const elem of passEventMissions) {
      userEventMissions[elem.eventMissionCmsId] = {
        eventMissionCmsId: elem.eventMissionCmsId,
        count: elem.count,
        repeatedRewardReceiveCount: elem.repeatedRewardReceiveCount,
        isRewarded: elem.isRewarded,
      };
    }

    UserPassEvent._setCompleteIfNeeded(passEventPageCms, passEvent);
  }

  getPassEventExpLevel(passEventPageCms: EventPageDesc): { exp: number; level: number } {
    assert(passEventPageCms.type === EventPageType.PASS_EVENT);

    const passEvent = this._passEvents[passEventPageCms.id];
    if (!passEvent) {
      return { exp: 0, level: 1 };
    }

    // DB 에 default 값으로 row 가 생성된 경우 등?
    if (passEvent.exp === undefined) {
      assert(passEvent.level === undefined);
      return { exp: 0, level: 1 };
    }

    assert(Number.isInteger(passEvent.exp));
    assert(Number.isInteger(passEvent.level));
    return { exp: passEvent.exp, level: passEvent.level };
  }

  setPassEventExpLevel(
    passEventPageCms: EventPageDesc,
    exp: number, // 객체 형태로 받는 게 나을지, 실수할 여지가 있음.
    level: number,
    glogParam: GLogCashParam & { pr_data: PrData[] | null }
  ): void {
    assert(passEventPageCms.type === EventPageType.PASS_EVENT); // 타입 가드로 막으면 좋을 듯 한데..
    assert(Number.isInteger(exp));
    assert(Number.isInteger(level));

    if (glogParam) {
      const oldExpLevel = this.getPassEventExpLevel(passEventPageCms);
      if (level !== oldExpLevel.level) {
        glogParam.user.glog('event_pass_levelup', {
          rsn: glogParam.rsn,
          add_rsn: glogParam.add_rsn,
          event_id: passEventPageCms.id,
          event_name: passEventPageCms.name ?? null,
          event_type: passEventPageCms.type,
          event_group: passEventPageCms.groupRef,
          old_lv: oldExpLevel.level,
          cur_lv: level,
          pr_data: glogParam.pr_data,
          exchange_hash: glogParam.exchangeHash ? glogParam.exchangeHash : null,
        });
      }
    }

    if (!this._passEvents[passEventPageCms.id]) {
      this._passEvents[passEventPageCms.id] = UserPassEvent._defaultPassEvent(passEventPageCms.id);
    }

    const passEvent = this._passEvents[passEventPageCms.id];
    passEvent.exp = exp;
    passEvent.level = level;
  }

  setPassEventLastDailyResetTimeUtc(
    eventPageCmsId: number,
    lastDailyResetTimeUtc: number | null
  ): void {
    assert(cms.EventPage[eventPageCmsId].type === EventPageType.PASS_EVENT);
    assert(Number.isInteger(lastDailyResetTimeUtc) || lastDailyResetTimeUtc === null);

    if (!this._passEvents[eventPageCmsId]) {
      this._passEvents[eventPageCmsId] = UserPassEvent._defaultPassEvent(eventPageCmsId);
    }
    this._passEvents[eventPageCmsId].lastDailyResetTimeUtc = lastDailyResetTimeUtc;
  }

  /**
   * 로그인 시점에 지워야할 목록들을 추출
   */
  getEventPageCmsIdsToDeleteOnLogin(curTimeUtc: number): number[] {
    const eventPageCmsIds: number[] = [];
    _.forOwn(this._passEvents, (elem) => {
      const ret = UserPassEvent._isOutOfPeriodRewardTimeIncluded(
        curTimeUtc,
        cms.EventPage[elem.eventPageCmsId]
      );
      if (typeof ret === 'object') {
        // 로그인 시점이라 throw 하기도, 마냥 DB 에서 지우기도 애매해서.
        // TODO CMS Validation
        mlog.error('failed-to-get-pass-event-page-period', ret);
        return;
      }

      // 다른 이벤트 페이지(출석부, 7일임무, 일반 임무)에서 로그인 시에
      // 시작 시간이 되지 않은 것도 체크하고 DB 에서 지우고 있어서 따라감.
      const bOutOfPeriodRewardTimeIncluded = ret;
      if (bOutOfPeriodRewardTimeIncluded) {
        eventPageCmsIds.push(elem.eventPageCmsId);
      }
    });
    return eventPageCmsIds;
  }

  calcReceiveRewardCount(
    repeatedRewardMissionLv: number,
    curRepeatedRewardCount: number,
    passEventPageCms: EventPageDesc,
    eventMissionCms: EventMissionDesc
  ): number {
    // 남은 경험치 (현재 경험치 - 최대 레벨 시작 경험치)
    let remainExp =
      this.getPassEventExpLevel(passEventPageCms).exp -
      cms.EventMissionExp[repeatedRewardMissionLv - 1].accumulateExp;
    // 보상에 필요한 경험치
    const requireRewardExp = eventMissionCms.requiredRepeatedExp;
    // 현재 받은 보상 횟수 만큼  획득 경험치 차감
    remainExp = remainExp - curRepeatedRewardCount * requireRewardExp;
    const receiveRewardCount = Math.floor(remainExp / requireRewardExp);
    // 반복 보상을 받기 위한 경험치 부족
    if (receiveRewardCount <= 0) {
      throw new MError(
        'not-enough-repeated-reward-exp',
        MErrorCode.INVALID_REQ_BODY_RECEIVE_PASS_EVENT_MISSION_REWARD,
        {
          eventPageCmsId: passEventPageCms.id,
          eventMissionCmsId: eventMissionCms.id,
          passEventExp: this.getPassEventExpLevel(passEventPageCms).exp,
          remainExp,
          requireRewardExp,
          receiveRewardCount,
        }
      );
    }
    // 반복 수령 횟수를 초과하면 에러 처리
    if (curRepeatedRewardCount + receiveRewardCount > eventMissionCms.repeatCount) {
      throw new MError(
        'over-receive-repeated-reward',
        MErrorCode.INVALID_REQ_BODY_RECEIVE_PASS_EVENT_MISSION_REWARD,
        {
          eventPageCmsId: passEventPageCms.id,
          eventMissionCmsId: eventMissionCms.id,
          passEventExp: this.getPassEventExpLevel(passEventPageCms).exp,
          remainExp,
          requireRewardExp,
          curRepeatedRewardCount,
          receiveRewardCount,
          maxRepeatedReceiveCount: eventMissionCms.repeatCount,
        }
      );
    }
    return receiveRewardCount;
  }

  static isOutOfPeriodRewardTimeIncluded(curTimeUtc: number, eventPageCms: EventPageDesc): boolean {
    assert(eventPageCms.type === EventPageType.PASS_EVENT);

    const ret = UserPassEvent._isOutOfPeriodRewardTimeIncluded(curTimeUtc, eventPageCms);
    if (typeof ret === 'object') {
      throw new MError(ret.msg, MErrorCode.INTERNAL_ERROR, ret.extra);
    }
    return ret;
  }

  private static _isOutOfPeriodRewardTimeIncluded(
    curTimeUtc: number,
    eventPageCms: EventPageDesc
  ): boolean | { msg: string; extra: unknown } {
    if (eventPageCms.startDate) {
      const curDate = new Date(curTimeUtc * 1000);
      const startDate = mutil.newDateByCmsDateStr(eventPageCms.startDate);
      if (!Number.isInteger(startDate.getTime())) {
        // TODO? CMS Validation
        return {
          msg: 'CMS.EventPage.startDate invalid!',
          extra: { eventPageCmsId: eventPageCms.id, eventPageCmsDate: eventPageCms.startDate },
        };
      }
      if (curDate < startDate) {
        return true;
      }
    }

    if (eventPageCms.endDate) {
      const endDate = mutil.newDateByCmsDateStr(eventPageCms.endDate);
      const endDateTimeUtc = endDate.getTime() / 1000;
      if (!Number.isInteger(endDateTimeUtc)) {
        // TODO? CMS Validation
        return {
          msg: 'CMS.EventPage.endDate invalid!',
          extra: { eventPageCmsId: eventPageCms.id, eventPageCmsDate: eventPageCms.endDate },
        };
      }

      const rewardPeriodSecs = eventPageCms.passRewardHour
        ? eventPageCms.passRewardHour * SECONDS_PER_HOUR
        : 0;
      const endDateTimeUtcIncludeRewardPeriod = endDateTimeUtc + rewardPeriodSecs;
      if (curTimeUtc > endDateTimeUtcIncludeRewardPeriod) {
        return true;
      }
    }

    return false;
  }

  deletePassEvents(eventPageCmsIds: number[]) {
    for (const cmsId of eventPageCmsIds) {
      delete this._passEvents[cmsId];
    }
  }

  getSyncData(): All {
    const ret: All = {
      passEvents: this._passEvents,
    };
    return ret;
  }
}

// ----------------------------------------------------------------------------
// Exports.
// ----------------------------------------------------------------------------

export default UserPassEvent;
