// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mlog from '../../../motiflib/mlog';
import mhttp from '../../../motiflib/mhttp';
import { Resp } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../index';

// ----------------------------------------------------------------------------
// 단순히 LG Billing Server API 로 이어줌.
// ----------------------------------------------------------------------------

const rsn = null;
const add_rsn = null;

interface ResponseBody extends Resp {
  billingApiResp: unknown;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingQueryInvenPurchaseList implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    return Promise.resolve()
      .then(() => {
        return mhttp.lgbillingd.queryInventoryPurchaseList(user.userId.toString());
      })
      .then((billingApiResp) => {
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          billingApiResp,
        });
      });
  }
}
