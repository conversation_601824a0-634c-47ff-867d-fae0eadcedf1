// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import morgan from 'morgan';
import path from 'path';
import fs from 'fs';
import * as JSON5 from 'json5';
import { Container, Service } from 'typedi';
import _ from 'lodash';

import * as dirAsApi from '../motiflib/directoryAsApi';
import * as adminError from './adminError';
import mconf from '../motiflib/mconf';
import { resolveLocalDotJson5 } from '../motiflib/resolveLocalDotJson5';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import { MRedisConnPool, AuctionRedisConnPoolMgr } from '../redislib/connPool';
import Pubsub from '../redislib/pubsub';
import { DBConnPool, DbConnPoolManager } from '../mysqllib/pool';
import cms, { load as loadCms } from '../cms';
import { AdminAuthApiClient } from './apiClient/adminAuthApiClient';
import stoppable from 'stoppable';
import os from 'os';
import { AdminHttpUtils } from './apiClient/adminHttpUtils';
import { ServiceLayoutMgr } from './serviceLayoutMgr';
import ServerHealthChecker from './serverHealthChecker';
import mhttp from '../motiflib/mhttp';
import * as configPubsubSyncer from '../motiflib/model/config/configPubsubSyncer';
import { AdminUtil } from '../motiflib/model/admin/adminUtil';
import * as Sentry from '@sentry/node';
import { AdminCashShopFixedTermProductsLookup, AdminReligionBuffLookup } from './adminLookup';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------

// admind app.
const adminApp = express();

adminApp.disable('x-powered-by');
adminApp.disable('etag');
adminApp.disable('content-type');

const adminServer = stoppable(http.createServer(adminApp));

let stopping = false;

@Service()
export class AdminService {
  nationRedises: { [worldId: string]: MRedisConnPool };
  townRedises: { [worldId: string]: MRedisConnPool };
  collectorRedises: { [worldId: string]: MRedisConnPool };
  sailRedises: { [worldId: string]: MRedisConnPool };
  townLbRedises: { [worldId: string]: MRedisConnPool };
  oceanLbRedises: { [worldId: string]: MRedisConnPool };
  userRedises: { [worldId: string]: MRedisConnPool };
  guildRedises: { [worldId: string]: MRedisConnPool };
  auctionRedisConnPoolMgr: { [worldId: string]: AuctionRedisConnPoolMgr };
  arenaRedises: { [worldId: string]: MRedisConnPool };
  rankingRedises: { [worldId: string]: MRedisConnPool };
  raidRedises: { [worldId: string]: MRedisConnPool };

  worldPubsubRedises: { [worldId: string]: Pubsub };
  guildPubsubRedises: { [worldId: string]: Pubsub };

  userCacheRedis: MRedisConnPool;
  monitorRedis: MRedisConnPool;
  battleLogRedis: MRedisConnPool;
  orderRedis: MRedisConnPool;
  authRedis: MRedisConnPool;
  authPubsub: Pubsub;
  configPubsub: Pubsub;
  globalBattleLogRedis: MRedisConnPool;

  authDbConnPool: DBConnPool;
  userDbConnPoolMgrs: { [worldId: string]: DbConnPoolManager };
  worldDbConnPools: { [worldId: string]: DBConnPool };
  serviceLayoutMgr: ServiceLayoutMgr;
  authApiClient: AdminAuthApiClient;
  serverHealthChecker: ServerHealthChecker;

  adminCashShopFixedTermProductsLookup: AdminCashShopFixedTermProductsLookup;
  adminReligionBuffLookup: AdminReligionBuffLookup;

  async init(layoutData) {
    await startTogglet();

    this.serviceLayoutMgr = Container.get(ServiceLayoutMgr);
    this.serviceLayoutMgr = new ServiceLayoutMgr(layoutData);
    this.serviceLayoutMgr.loadInstances();

    const worlds = layoutData.world.worlds;
    const sharedConfig = layoutData.sharedConfig;
    const lobbydCommon = layoutData.lobbyd.common;
    const zonelbdCommon = layoutData.zonelbd.common;

    this.monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
    await this.monitorRedis.init('monitor-redis', sharedConfig.monitorRedis);

    this.userCacheRedis = Container.of('user-cache-redis').get(MRedisConnPool);
    await this.userCacheRedis.init('user-cache-redis', sharedConfig.userCacheRedis);

    this.battleLogRedis = Container.of('battle-log-redis').get(MRedisConnPool);
    await this.battleLogRedis.init('battle-log-redis', lobbydCommon.battleLogRedis);

    this.globalBattleLogRedis = Container.of('global-battle-log-redis').get(MRedisConnPool);
    await this.globalBattleLogRedis.init('global-battle-log-redis', mconf.globalBattleLogRedis);

    this.orderRedis = Container.of('order-redis').get(MRedisConnPool);
    await this.orderRedis.init('order-redis', mconf.orderRedis);

    this.authRedis = Container.of('auth-redis').get(MRedisConnPool);
    await this.authRedis.init('auth-redis', mconf.authRedis);

    this.authPubsub = Container.of('pubsub-auth').get(Pubsub);
    this.authPubsub.init(sharedConfig.authPubsubRedis);

    this.configPubsub = Container.of('pubsub-config').get(Pubsub);
    this.configPubsub.init(sharedConfig.configPubsubRedis);

    this.authDbConnPool = Container.of('auth').get(DBConnPool);
    await this.authDbConnPool.init(sharedConfig.mysqlAuthDb);

    this.authApiClient = Container.get(AdminAuthApiClient);
    this.authApiClient = new AdminAuthApiClient();
    this.authApiClient.init(sharedConfig.http.authd.url, sharedConfig.http.authd.timeout);

    this.townRedises = {};
    this.nationRedises = {};
    this.collectorRedises = {};
    this.sailRedises = {};
    this.townLbRedises = {};
    this.oceanLbRedises = {};
    this.userRedises = {};
    this.guildRedises = {};
    this.auctionRedisConnPoolMgr = {};
    this.arenaRedises = {};
    this.rankingRedises = {};
    this.raidRedises = {};

    this.worldPubsubRedises = {};
    this.guildPubsubRedises = {};

    this.userDbConnPoolMgrs = {};
    this.worldDbConnPools = {};

    for (const world of worlds) {
      if (world.disabled) {
        mlog.warn('skipping disabled world', { worldId: world.id });
        continue;
      }

      this.townRedises[world.id] = new MRedisConnPool();
      await this.townRedises[world.id].init('town-redis-' + world.id, world.townRedis);

      this.nationRedises[world.id] = new MRedisConnPool();
      await this.nationRedises[world.id].init('nation-redis-' + world.id, world.nationRedis);

      this.collectorRedises[world.id] = new MRedisConnPool();
      await this.collectorRedises[world.id].init(
        'collector-redis-' + world.id,
        world.collectorRedis
      );

      this.sailRedises[world.id] = new MRedisConnPool();
      await this.sailRedises[world.id].init('sail-redis-' + world.id, world.sailRedis);

      this.townLbRedises[world.id] = new MRedisConnPool();
      await this.townLbRedises[world.id].init('town-lb-redis-' + world.id, world.townLbRedis);

      this.oceanLbRedises[world.id] = new MRedisConnPool();
      await this.oceanLbRedises[world.id].init('ocean-lb-redis-' + world.id, world.oceanLbRedis);

      this.userRedises[world.id] = new MRedisConnPool();
      await this.userRedises[world.id].init('user-redis-' + world.id, world.userRedis);

      this.guildRedises[world.id] = new MRedisConnPool();
      await this.guildRedises[world.id].init('guild-redis-' + world.id, world.guildRedis);

      this.auctionRedisConnPoolMgr[world.id] = new AuctionRedisConnPoolMgr();
      await this.auctionRedisConnPoolMgr[world.id].init(
        'auction-redis-' + world.id,
        world.auctionRedis
      );

      this.arenaRedises[world.id] = new MRedisConnPool();
      await this.arenaRedises[world.id].init('arena-redis-' + world.id, world.arenaRedis);

      this.rankingRedises[world.id] = new MRedisConnPool();
      await this.rankingRedises[world.id].init('ranking-redis-' + world.id, world.rankingRedis);

      this.raidRedises[world.id] = new MRedisConnPool();
      await this.raidRedises[world.id].init('ranking-redis-' + world.id, world.raidRedis);

      this.worldPubsubRedises[world.id] = new Pubsub();
      this.worldPubsubRedises[world.id].init(world.worldPubsubRedis);

      this.guildPubsubRedises[world.id] = new Pubsub();
      this.guildPubsubRedises[world.id].init(world.guildPubsubRedis);

      this.userDbConnPoolMgrs[world.id] = new DbConnPoolManager();
      await this.userDbConnPoolMgrs[world.id].init(world.mysqlUserDb);

      this.worldDbConnPools[world.id] = new DBConnPool();
      await this.worldDbConnPools[world.id].init(world.mysqlWorldDb);
    }

    this.serverHealthChecker = new ServerHealthChecker();
    //this.serverHealthChecker.startTick();

    // 동적 서버 인스턴스 증가 대응(scale out)
    configPubsubSyncer.subscribeForRegisterInstance(
      this.configPubsub,
      (fullCfg) => {
        mlog.info('configPubsubSyncer updating new layout', { fullCfg });

        // 새로운 인스턴스 등록 이벤트 발생시 처리할 작업을 등록
        this.serviceLayoutMgr.setLayout(fullCfg);
        this.serviceLayoutMgr.loadInstances();
      },
      isStopping,
      stop,
      true
    );

    // 캐시샵 정보 캐싱 및 DB cms테이블 업데이트
    this.adminCashShopFixedTermProductsLookup = new AdminCashShopFixedTermProductsLookup(cms);
    this.adminReligionBuffLookup = new AdminReligionBuffLookup(cms);
    mlog.info('adminCashShopFixedTermProductsLookup initialized');
  }

  async destroy() {
    //this.serverHealthChecker.stopTick();

    await this.authPubsub.quit();
    await this.configPubsub.quit();
    await this.monitorRedis.destroy();
    await this.userCacheRedis.destroy();
    await this.battleLogRedis.destroy();
    await this.orderRedis.destroy();
    await this.authRedis.destroy();
    await this.authDbConnPool.destroy();
    await this.globalBattleLogRedis.destroy();

    for (const redis of Object.values(this.townRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.nationRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.collectorRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.sailRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.townLbRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.oceanLbRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.userRedises)) {
      await redis.destroy();
    }

    for (const redis of Object.values(this.guildRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.auctionRedisConnPoolMgr)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.arenaRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.rankingRedises)) {
      await redis.destroy();
    }
    for (const redis of Object.values(this.raidRedises)) {
      await redis.destroy();
    }

    for (const redis of Object.values(this.worldPubsubRedises)) {
      await redis.quit();
    }

    for (const redis of Object.values(this.guildPubsubRedises)) {
      await redis.quit();
    }

    for (const mgr of Object.values(this.userDbConnPoolMgrs)) {
      await mgr.destroy();
    }
    for (const connPool of Object.values(this.worldDbConnPools)) {
      await connPool.destroy();
    }

    stopTogglet();
  }
}

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

function admindReqLog(tokens, req, res) {
  mlog.info('admind-req', {
    'remote-addr': tokens['remote-addr'](req, res),
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  });
  return null;
}

// ----------------------------------------------------
// 호출서버 IP인증 확인(플랫폼 서버에서 제공하는 API를 통해 IP리스트는 확인 가능)
// + admind/allowIp 추가 허용
// ----------------------------------------------------
async function adminIpFilter(req, res, next) {
  const clientIp = AdminUtil.getReqClientIp(req);

  let allowIpList = await AdminHttpUtils.lgd.reqWhiteServerIpList();

  if (mconf.apiService.allowIp && mconf.apiService.allowIp.length > 0) {
    allowIpList = allowIpList.concat(mconf.apiService.allowIp);
  }

  //mlog.verbose('[adminIpFilter] allowed IP addresses: ', { allowIpList });

  if (!_.includes(allowIpList, clientIp)) {
    return res.status(405).json({
      isSuccess: false,
      msg: 'Not allow your request',
      errorCd: 'NOT_ALLOW_AUTH',
    });
  }

  mlog.info('[adminIpFilter] Access granted to IP address: ', { clientIp });

  next();
}

async function closeHttpServer() {
  return new Promise((resolve, reject) => {
    adminServer.stop((err) => {
      if (err) {
        return reject(err);
      }
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');

    await closeHttpServer();

    const service = Container.get(AdminService);
    await service.destroy();

    mlog.info('admin-server closed');
  } catch (error) {
    mlog.error('graceful shutdown failed', { error: error.message });
    process.exit(1);
  }

  mlog.info('server stopped');
  process.exitCode = 0;
}

function loadLayout(): void {
  const ServiceLayoutFolder = 'service_layout';
  const layoutFileName = mconf.layout || resolveLocalDotJson5();
  const layoutFilePath = path.join(ServiceLayoutFolder, layoutFileName);

  const fileStr = fs.readFileSync(layoutFilePath, 'utf8');
  try {
    // Replace '__HOSTNAME__'.
    const replaceRe = new RegExp('__HOSTNAME__', 'g');
    const newData = fileStr.replace(replaceRe, os.hostname());

    const layoutData = JSON5.parse(newData);

    // merge layout data to mconf
    mconf.append(layoutData.sharedConfig);
    mconf.append(layoutData.world);
    mconf.append(layoutData.admind);

    return layoutData;
  } catch (err) {
    throw new Error(`failed to parse layout': ${err.message}`);
  }
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export const start = async () => {
  try {
    const layoutData = await mhttp.configd.fetchForAdmind();

    loadCms();

    // Init http clients.
    mhttp.init();

    mutil.initSentry();

    AdminHttpUtils.init();

    const service = Container.get(AdminService);
    await service.init(layoutData);

    // listen admin app
    const bindAddress = mconf.apiService.bindAddress;
    const port = mconf.apiService.port;

    // 호출서버 IP인증 확인(플랫폼 서버에서 제공하는 API를 통해 IP리스트는 확인 가능)
    adminApp.use(adminIpFilter);
    adminApp.use(morgan(admindReqLog));
    adminApp.use(bodyParser.json({ limit: '50mb' }));
    adminApp.use(bodyParser.urlencoded({ extended: true }));
    mutil.registerHealthCheck(adminApp);
    mutil.registerGarbageCollector(adminApp);
    await dirAsApi.register(adminApp, path.join(__dirname, 'api'));

    adminApp.use(adminError.middleware);
    adminServer.listen(port, bindAddress, () => {
      mlog.info('start admin server listening ...', { bindAddress, port });
    });
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
};

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await stopServer();
}
