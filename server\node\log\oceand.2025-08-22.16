{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T07:00:00.103Z"}
{"12000000":{"10000000":216,"10000001":337,"10000005":72,"10001065":289,"10001130":84},"12000100":{"10001011":93,"10001012":186,"10001024":93,"10001027":133,"10001054":93,"10001058":186,"10001082":93,"10001092":120},"12000200":{"10001006":116,"10001016":116,"10001036":433,"10001097":183,"10001125":150},"12000300":{},"12000301":{"10001071":1000},"12000302":{"10001033":740,"10001103":259},"12000303":{},"12000304":{},"12000400":{"10000005":428,"10001001":142,"10001017":142,"10001081":285},"12000500":{"10000000":122,"10000001":81,"10000002":244,"10000004":367,"10001056":183},"12000600":{"10001055":106,"10001062":106,"10001069":469,"10001072":106,"10001091":212},"12000700":{"10001013":121,"10001014":195,"10001016":109,"10001032":121,"10001045":280,"10001094":85,"10001095":85},"12000800":{"10001031":1000},"12000900":{"10001009":153,"10001026":76,"10001028":109,"10001035":76,"10001052":98,"10001059":98,"10001074":76,"10001077":76,"10001079":76,"10001090":76,"10001127":76},"12001000":{"10000004":223,"10001070":342,"10001097":118,"10001125":131,"10001128":184},"12001100":{"10001029":91,"10001042":346,"10001049":71,"10001063":91,"10001087":234,"10001100":91,"10001124":71},"12001200":{"10000002":250,"10000003":312,"10001016":296,"10001046":140},"12001300":{"10001022":200,"10001044":400,"10001053":200,"10001080":200},"12001301":{"10001008":145,"10001022":112,"10001053":145,"10001066":112,"10001068":225,"10001080":145,"10001086":112},"12001302":{},"12001303":{},"12001400":{"10001003":74,"10001010":180,"10001012":74,"10001018":74,"10001019":74,"10001040":148,"10001043":74,"10001067":74,"10001089":223},"12001500":{"10001013":333,"10001014":666},"12001501":{},"12001502":{},"12001503":{"10001032":500,"10001060":250,"10001068":250},"12001504":{"10001068":1000},"12001505":{"10001032":1000},"12001600":{"10000005":112,"10001029":516,"10001036":370},"12001700":{"10001021":132,"10001060":132,"10001071":75,"10001073":132,"10001093":132,"10001096":132,"10001101":132,"10001102":132},"12001701":{},"12001702":{},"12001703":{},"12001704":{},"12001705":{},"12001706":{"10001023":166,"10001061":500,"10001088":166,"10001126":166},"12001707":{},"12001708":{},"12001800":{"10001038":218,"10001078":281,"10001079":500},"12001801":{"10001015":92,"10001020":210,"10001025":210,"10001030":92,"10001057":92,"10001083":92,"10001084":210},"12001900":{"10001005":79,"10001047":79,"10001051":79,"10001075":79,"10001076":340,"10001085":79,"10001086":261},"12002000":{"10001004":93,"10001007":93,"10001037":93,"10001041":320,"10001048":186,"10001050":120,"10001098":93},"12010000":{},"12010100":{},"12010101":{},"12010102":{},"12010200":{},"12010300":{},"12010400":{},"12010500":{},"12010600":{},"12010700":{},"12010701":{},"12010800":{},"12010900":{"10001131":1000},"12011000":{},"12011100":{},"12011200":{},"12011300":{},"12011400":{},"12011401":{},"level":"info","message":"subscribe nation_share_rate_updated","timestamp":"2025-08-22T07:00:00.177Z"}
{"12000000":{"10000000":216,"10000001":337,"10000005":72,"10001065":289,"10001130":84},"12000100":{"10001011":93,"10001012":186,"10001024":93,"10001027":133,"10001054":93,"10001058":186,"10001082":93,"10001092":120},"12000200":{"10001006":116,"10001016":116,"10001036":433,"10001097":183,"10001125":150},"12000300":{},"12000301":{"10001071":1000},"12000302":{"10001033":740,"10001103":259},"12000303":{},"12000304":{},"12000400":{"10000005":428,"10001001":142,"10001017":142,"10001081":285},"12000500":{"10000000":122,"10000001":81,"10000002":244,"10000004":367,"10001056":183},"12000600":{"10001055":106,"10001062":106,"10001069":469,"10001072":106,"10001091":212},"12000700":{"10001013":121,"10001014":195,"10001016":109,"10001032":121,"10001045":280,"10001094":85,"10001095":85},"12000800":{"10001031":1000},"12000900":{"10001009":153,"10001026":76,"10001028":109,"10001035":76,"10001052":98,"10001059":98,"10001074":76,"10001077":76,"10001079":76,"10001090":76,"10001127":76},"12001000":{"10000004":223,"10001070":342,"10001097":118,"10001125":131,"10001128":184},"12001100":{"10001029":91,"10001042":346,"10001049":71,"10001063":91,"10001087":234,"10001100":91,"10001124":71},"12001200":{"10000002":250,"10000003":312,"10001016":296,"10001046":140},"12001300":{"10001022":200,"10001044":400,"10001053":200,"10001080":200},"12001301":{"10001008":145,"10001022":112,"10001053":145,"10001066":112,"10001068":225,"10001080":145,"10001086":112},"12001302":{},"12001303":{},"12001400":{"10001003":74,"10001010":180,"10001012":74,"10001018":74,"10001019":74,"10001040":148,"10001043":74,"10001067":74,"10001089":223},"12001500":{"10001013":333,"10001014":666},"12001501":{},"12001502":{},"12001503":{"10001032":500,"10001060":250,"10001068":250},"12001504":{"10001068":1000},"12001505":{"10001032":1000},"12001600":{"10000005":112,"10001029":516,"10001036":370},"12001700":{"10001021":132,"10001060":132,"10001071":75,"10001073":132,"10001093":132,"10001096":132,"10001101":132,"10001102":132},"12001701":{},"12001702":{},"12001703":{},"12001704":{},"12001705":{},"12001706":{"10001023":166,"10001061":500,"10001088":166,"10001126":166},"12001707":{},"12001708":{},"12001800":{"10001038":218,"10001078":281,"10001079":500},"12001801":{"10001015":92,"10001020":210,"10001025":210,"10001030":92,"10001057":92,"10001083":92,"10001084":210},"12001900":{"10001005":79,"10001047":79,"10001051":79,"10001075":79,"10001076":340,"10001085":79,"10001086":261},"12002000":{"10001004":93,"10001007":93,"10001037":93,"10001041":320,"10001048":186,"10001050":120,"10001098":93},"12010000":{},"12010100":{},"12010101":{},"12010102":{},"12010200":{},"12010300":{},"12010400":{},"12010500":{},"12010600":{},"12010700":{},"12010701":{},"12010800":{},"12010900":{"10001131":1000},"12011000":{},"12011100":{},"12011200":{},"12011300":{},"12011400":{},"12011401":{},"level":"info","message":"subscribe nation_share_rate_updated","timestamp":"2025-08-22T07:00:01.095Z"}
{"powers":{"10000000":5,"10000001":6,"10000002":5,"10000003":4,"10000005":6},"level":"info","message":"subscribe national_power_updated","timestamp":"2025-08-22T07:00:01.099Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T07:22:57.648Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T07:22:57.649Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:22:57.756Z"}
{"level":"info","message":"[Session] socket disposed, v2Ujja9W","timestamp":"2025-08-22T07:22:57.757Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T07:22:57.758Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T07:22:57.759Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T07:22:57.761Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T07:22:57.763Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:22:57.763Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:22:57.763Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T07:22:57.764Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T07:22:57.765Z"}
{"environment":"development","type":"oceand","gitCommitHash":"58e140f834d","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T15:41:41+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:23:00.208Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.955Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.955Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.955Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.956Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.956Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.956Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.956Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.956Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:21.957Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:23:21.965Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T07:23:21.967Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T07:23:22.238Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T07:23:22.239Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T07:23:22.239Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T07:23:22.240Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T07:23:26.571Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T07:23:26.572Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T07:23:26.573Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T07:23:26.580Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T07:23:26.581Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T07:23:26.597Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T07:23:26.727Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.749Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.767Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.778Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.792Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.803Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.825Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.846Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.863Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:26.886Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T07:23:26.979Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T07:23:26.980Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T07:23:26.984Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T07:23:27.105Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T07:23:27.107Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T07:23:27.111Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T07:23:27.111Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T07:23:27.114Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T07:23:27.114Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T07:23:27.115Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T07:23:27.115Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T07:23:27.117Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T07:23:27.117Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T07:23:27.117Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T07:23:27.118Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T07:23:27.119Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T07:23:27.121Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T07:23:27.121Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T07:23:27.214Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T07:23:27.263Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:27.267Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:27.268Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:27.268Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:27.268Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:27.336Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:27.350Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:23:27.353Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T07:23:27.354Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:23:27.356Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T07:23:27.356Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:27.358Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:27.358Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847407,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:23:27.451Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:23:27.452Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:28.360Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:28.361Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:29.362Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:29.362Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847410,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:23:30.059Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:23:30.059Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:30.363Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:30.363Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:31.364Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:31.365Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:32.367Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:32.367Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847413,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:23:33.072Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:23:33.072Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:33.369Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:33.369Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:34.371Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:34.372Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:35.373Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:35.373Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847416,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:23:36.081Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:23:36.081Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:36.375Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:23:36.375Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T07:23:37.378Z"}
{"pingInterval":2000,"curDate":1755847417,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T07:23:37.381Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T07:23:43.020Z"}
{"level":"info","message":"[SessionManager] session created: cwLIrkm0, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T07:23:43.347Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T07:23:43.350Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T07:23:43.351Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T07:23:43.352Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.353Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.354Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.355Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.355Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.358Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.358Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.359Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:23:43.360Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T07:31:16.834Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T07:31:16.834Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:31:17.319Z"}
{"level":"info","message":"[Session] socket disposed, cwLIrkm0","timestamp":"2025-08-22T07:31:17.320Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T07:31:17.320Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T07:31:17.321Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T07:31:17.323Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T07:31:17.324Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:31:17.324Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T07:31:17.325Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T07:31:17.325Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:31:17.325Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T07:31:17.326Z"}
{"environment":"development","type":"oceand","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:31:20.593Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.983Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.983Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.984Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.984Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.984Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.984Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.984Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.985Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:44.985Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:31:44.993Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T07:31:44.995Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T07:31:45.229Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T07:31:45.230Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T07:31:45.231Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T07:31:45.231Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T07:31:49.149Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T07:31:49.150Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T07:31:49.150Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T07:31:49.157Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T07:31:49.157Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T07:31:49.171Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T07:31:49.256Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.277Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.292Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.303Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.316Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.328Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.344Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.360Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.374Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:49.392Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T07:31:49.470Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T07:31:49.471Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T07:31:49.474Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T07:31:49.604Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T07:31:49.606Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T07:31:49.607Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T07:31:49.607Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T07:31:49.610Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T07:31:49.610Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T07:31:49.610Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T07:31:49.610Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T07:31:49.612Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T07:31:49.613Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T07:31:49.613Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T07:31:49.614Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T07:31:49.615Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T07:31:49.616Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T07:31:49.617Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T07:31:49.710Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T07:31:49.754Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:49.759Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:49.759Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:49.759Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:49.759Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:49.818Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:49.829Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:31:49.832Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T07:31:49.832Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:31:49.834Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T07:31:49.834Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:49.836Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:49.836Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847909,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:31:49.929Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:31:49.930Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:50.837Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:50.837Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:51.838Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:51.839Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847912,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:31:52.037Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:31:52.037Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:52.840Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:52.840Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:53.842Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:53.842Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:54.844Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:54.845Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847915,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:31:55.042Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:31:55.042Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:55.847Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:55.847Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:56.849Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:56.850Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:57.852Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:31:57.852Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755847918,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:31:58.047Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:31:58.047Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T07:31:58.855Z"}
{"pingInterval":2000,"curDate":1755847918,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T07:31:58.858Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T07:32:02.536Z"}
{"level":"info","message":"[SessionManager] session created: HF34ZRtR, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T07:32:02.921Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T07:32:02.922Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T07:32:02.923Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T07:32:02.924Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.925Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.926Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.927Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.927Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.927Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.927Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.927Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:32:02.927Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T07:33:51.032Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T07:33:51.033Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:33:51.272Z"}
{"level":"info","message":"[Session] socket disposed, HF34ZRtR","timestamp":"2025-08-22T07:33:51.272Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T07:33:51.273Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T07:33:51.273Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T07:33:51.275Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T07:33:51.276Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:33:51.276Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:33:51.277Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T07:33:51.277Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T07:33:51.277Z"}
{"environment":"development","type":"oceand","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:33:54.398Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.819Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.819Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.819Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.820Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.820Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.820Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.820Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.820Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:19.821Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:34:19.828Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T07:34:19.830Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T07:34:19.983Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T07:34:19.984Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T07:34:19.985Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T07:34:19.986Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T07:34:23.863Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T07:34:23.864Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T07:34:23.864Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T07:34:23.871Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T07:34:23.871Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T07:34:23.886Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T07:34:23.968Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:23.990Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.006Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.017Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.030Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.042Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.059Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.077Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.091Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:24.109Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T07:34:24.188Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T07:34:24.189Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T07:34:24.192Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T07:34:24.281Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T07:34:24.283Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T07:34:24.284Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T07:34:24.284Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T07:34:24.286Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T07:34:24.287Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T07:34:24.287Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T07:34:24.287Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T07:34:24.289Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T07:34:24.289Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T07:34:24.290Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T07:34:24.290Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T07:34:24.291Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T07:34:24.295Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T07:34:24.295Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T07:34:24.389Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T07:34:24.437Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:24.441Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:24.441Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:24.441Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:24.442Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:24.502Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:24.512Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:34:24.515Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T07:34:24.516Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:34:24.517Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T07:34:24.517Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:24.519Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:34:24.520Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755848064,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:34:24.612Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:34:24.612Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:25.521Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:34:25.521Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:26.522Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:34:26.522Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755848067,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:34:27.025Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:34:27.025Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:27.523Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:34:27.523Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:28.524Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:34:28.524Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:29.525Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:34:29.526Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755848070,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T07:34:30.043Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T07:34:30.043Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:30.527Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T07:34:30.527Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T07:34:31.586Z"}
{"pingInterval":2000,"curDate":1755848071,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T07:34:31.590Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T07:34:35.993Z"}
{"level":"info","message":"[SessionManager] session created: FuPthXiI, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T07:34:36.344Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T07:34:36.346Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T07:34:36.347Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T07:34:36.349Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.351Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.353Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.354Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.355Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.355Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.356Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.356Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T07:34:36.357Z"}
