"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JudgeD = void 0;
const typedi_1 = require("typedi");
const mkdirp_1 = __importDefault(require("mkdirp"));
const path = __importStar(require("path"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const slackNotifier_1 = require("../motiflib/slackNotifier");
const mutil = __importStar(require("../motiflib/mutil"));
const body_parser_1 = __importDefault(require("body-parser"));
const express_1 = __importDefault(require("express"));
const http_1 = __importDefault(require("http"));
const expressError = __importStar(require("../motiflib/expressError"));
const stoppable_1 = __importDefault(require("stoppable"));
const judgeLoop_1 = require("./judgeLoop");
const Sentry = __importStar(require("@sentry/node"));
// ------------------------------------------------------------------------------------------------
// 서비스 중단 플래그.
// ------------------------------------------------------------------------------------------------
let bStopping = false;
// ------------------------------------------------------------------------------------------------
// undefined 참조등으로 예외를 catch 하지 못하면 호출
// ------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('uncaught Exception', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// ------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// ------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('unhandled Rejection', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// ----------------------------------------------------------------------------
// JudgeD 서버.
// ----------------------------------------------------------------------------
let JudgeD = class JudgeD {
    // ----------------------------------------------------------------------------
    constructor() {
        this._bStopping = false;
        this._expressServer = undefined;
        this._judgeLoop = undefined;
    }
    // ----------------------------------------------------------------------------
    async _startExpress() {
        return new Promise((resolve, reject) => {
            // Health-check 를 위한 express.
            const judgeApp = (0, express_1.default)();
            judgeApp.disable('x-powered-by');
            judgeApp.disable('etag');
            judgeApp.disable('content-type');
            judgeApp.use(body_parser_1.default.json());
            judgeApp.use(expressError.middleware);
            this._expressServer = (0, stoppable_1.default)(http_1.default.createServer(judgeApp));
            const bindAddress = mconf_1.default.apiService.bindAddress;
            const port = mconf_1.default.apiService.port;
            mutil.registerHealthCheck(judgeApp);
            mutil.registerGarbageCollector(judgeApp);
            this._expressServer.listen(port, bindAddress, (err) => {
                if (err) {
                    reject(err);
                }
                mlog_1.default.info('start listening ...', { bindAddress, port });
                resolve();
            });
        });
    }
    // ----------------------------------------------------------------------------
    async _stopExpress() {
        return new Promise((resolve, reject) => {
            this._expressServer.stop((err) => {
                if (err) {
                    reject(err);
                }
                mlog_1.default.info('Express server stopped.');
                resolve();
            });
        });
    }
    // ----------------------------------------------------------------------------
    async start() {
        try {
            // configd register/fetch.
            await mhttp_1.default.configd.registerInstance(undefined, mconf_1.default.appInstanceId, mconf_1.default.hostname);
            // config 동기화.
            // judged 의 경우, 설정 버전이 업데이트 된 경우, 딱히 할게 없다.
            const beforeVer = mconf_1.default.layoutVersion;
            await mhttp_1.default.configd.sync(beforeVer, undefined, undefined);
            // Create temp folder.
            mkdirp_1.default.sync(path.join(mconf_1.default.validationHome, 'temp'));
            mutil.initSentry();
            await this._startExpress();
            // 메인 검증 서비스
            this._judgeLoop = new judgeLoop_1.JudgeLoopService();
            this._judgeLoop.run(0);
            mlog_1.default.info('judged is up and running...');
        }
        catch (error) {
            mlog_1.default.error('failed to start judged', { error: error.message, extra: error.extra });
            mlog_1.default.error(error.stack);
            const slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
            await slackNotifier.notify({ username: process.name, text: error.message });
            process.exit(1);
        }
    }
    // ----------------------------------------------------------------------------
    async stop(signalCode) {
        mlog_1.default.info('Stop signaled.', { signalCode });
        if (this._bStopping) {
            return;
        }
        this._bStopping = true;
        mlog_1.default.info('Stopping judged...');
        await this._judgeLoop.destroy();
        await this._stopExpress();
        mlog_1.default.info('judged stopped gracefully.');
        process.exit(0);
    }
};
JudgeD = __decorate([
    (0, typedi_1.Service)(),
    __metadata("design:paramtypes", [])
], JudgeD);
exports.JudgeD = JudgeD;
//# sourceMappingURL=server.js.map