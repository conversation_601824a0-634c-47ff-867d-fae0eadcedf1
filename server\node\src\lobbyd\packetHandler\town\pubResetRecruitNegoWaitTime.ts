// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import mlog from '../../../motiflib/mlog';
import tuReduceRecruitNegoWaitTime from '../../../mysqllib/txn/tuReudceRecruitNegoWaitTime';
import { ClientPacketHandler } from '../index';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import * as formula from '../../../formula';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import { LobbyService } from '../../server';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Sync, Resp } from '../../type/sync';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import UserPoints, { CashPayment, PointChange, PointConsumptionCostParam } from '../../userPoints';
import { MakePubMateNegoTimeReductionData } from '../../../motiflib/gameLog';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { needMateRecruitingTermsCheck } from '../../../cms/mateRecruitingGroupDesc';

// ----------------------------------------------------------------------------
// 여관-고용 협상 대기 시간을 초기화 한다.
// ----------------------------------------------------------------------------

const rsn = 'pub_reset_recruit_nego_wait_time';
const add_rsn = null;

interface RequestBody {
  mateCmsId: number;
  bPermitExchange: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Town_PubResetRecruitNegoWaitTime implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureInTown();

    const body: RequestBody = packet.bodyObj;
    const { mateCmsId, bPermitExchange } = body;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    // 여관-고용 메뉴는 잠금되지 않음
    // user.userContentsTerms.ensureBuildingContentsUnlock(
    //   cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID,
    //   user
    // );

    const townCmsId = user.userTown.getTownCmsId();
    const curTimeUtc = mutil.curTimeUtc();
    const townMates = user.userMates.getCurTownMates(user.userTown);

    // 항해사 확인
    const mateCms = cms.Mate[mateCmsId];
    const townMate = townMates.mates[mateCmsId];
    if (!mateCms || !townMate) {
      throw new MError('invalid-mate-cms-id', MErrorCode.INVALID_MATE_CMS_ID, {
        mateCmsId,
      });
    }

    // Check time. 서버 클라간 시간이 정확히 같지 않기 때문에 100초의 여유를 둔다.
    const curLastUpdateTimeUtc = townMates.lastUpdateTimeUtc;
    const mateResetSeconds = cms.Const.RecruitResetCycleSec.value;
    if (curLastUpdateTimeUtc + mateResetSeconds <= curTimeUtc - 100) {
      throw new MError('time-is-expired', MErrorCode.PUB_MATE_TIME_EXPIRED, {
        mateCmsId,
        curLastUpdateTimeUtc,
        curTimeUtc,
        diff: curTimeUtc - curLastUpdateTimeUtc,
      });
    }

    // https://jira.line.games/browse/UWO-7925
    // https://jira.line.games/browse/UWO-19699 특권 보너스,
    // cms.MateRecruitingGroup.isMustAppear 이 true 또는 mustAppearEvent 조건 만족인 경우
    // cms.MateRecruitingGroup.contentsTerms 을 검사해야됨
    const townCms = cms.Town[user.userTown.getTownCmsId()];
    const mateRecruitingGroupCms = cmsEx.getMateRecruitingGroupByGroupAndMateCmsId(
      townCms.mateRecruitingGroup,
      mateCmsId
    );
    const eventPageProducts = user.userCashShop.getEventPageProducts();
    if (
      !mateRecruitingGroupCms ||
      (needMateRecruitingTermsCheck(mateRecruitingGroupCms, eventPageProducts, curTimeUtc) &&
        !user.userContentsTerms.isValidContentsTerms(mateRecruitingGroupCms.contentsTerms, user))
    ) {
      throw new MError('contents-terms-invalid', MErrorCode.CONTENTS_TERMS_INVALID, {
        mateCmsId,
        townCmsId: townCms.id,
        mateRecruitingGroupCms,
        contentsTerms: mateRecruitingGroupCms.contentsTerms,
      });
    }

    // Check contents terms
    const mateRecruitingCms = mateCms.mateRecruiting;
    user.userContentsTerms.ensureContentsTerms(mateRecruitingCms.contentsTerms, user);

    // 협상 대기 중이 아닌 경우
    if (!townMate.negoWaitExpirationTimeUtc || curTimeUtc >= townMate.negoWaitExpirationTimeUtc) {
      throw new MError(
        'not-waiting-for-negotiations',
        MErrorCode.NOT_IN_MATE_RECRUITING_NEGO_WAIT_TIME,
        {
          mateCmsId,
          negoWaitExpirationTimeUtc: townMate.negoWaitExpirationTimeUtc,
          curTimeUtc,
        }
      );
    }

    const remainingTimeSec = Math.max(townMate.negoWaitExpirationTimeUtc - curTimeUtc, 0);

    // 포인트 확인
    const cost = formula.CalcImmediateCompletionTimeCost(remainingTimeSec);
    const pointCost: PointConsumptionCostParam | undefined =
      cost > 0 // 비용이 없는 경우가 있음
        ? { cmsId: cms.Const.TimeCostPerMinPointType.value, cost }
        : undefined;

    let pointChanges: PointChange[] | undefined;
    let cashPayments: CashPayment[] | undefined;
    if (pointCost) {
      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [pointCost],
        bPermitExchange,
        { itemId: rsn },
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;
    }

    const resp: Resp = { sync: {} };

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return Promise.resolve()
      .then(() => {
        return user.userPoints.tryConsumeCashs(cashPayments, resp.sync, user, {
          user,
          rsn,
          add_rsn,
          exchangeHash,
        });
      })
      .then(() => {
        return tuReduceRecruitNegoWaitTime(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          pointChanges,
          undefined, // ItemChange
          townCmsId,
          townMate.index,
          null // negoWaitExpirationTimeUtc
        );
      })
      .then(() => {
        user.glog(
          'time_reduction',
          MakePubMateNegoTimeReductionData({
            rsn,
            add_rsn,

            old_duration: remainingTimeSec,
            cur_duration: 0,
            pr_data: pointCost ? [{ type: pointCost.cmsId, amt: pointCost.cost }] : null,
            cost_data: null,
            mate_recruit_data: {
              town_id: townCmsId,
              town_name: townCms.name,
              mate_id: mateCmsId,
              mate_name: displayNameUtil.getMateDisplayName(mateCmsId),
            },
            exchange_hash: exchangeHash,
          })
        );

        _.merge<Sync, Sync>(
          resp.sync,
          user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
        );

        townMate.negoWaitExpirationTimeUtc = null;
        _.merge<Sync, Sync>(resp.sync, {
          // 클라 SyncData, null 관련해서, 항목에서 제거
          remove: {
            towns: {
              [townCmsId]: {
                myPubMates: {
                  mates: {
                    [mateCmsId]: {
                      negoWaitExpirationTimeUtc: true,
                    },
                  },
                },
              },
            },
          },
        });
      })
      .then(() => {
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, resp);
      });
  }
}
