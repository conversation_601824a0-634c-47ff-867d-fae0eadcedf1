// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import morgan from 'morgan';
import path from 'path';
import Container from 'typedi';
import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import { DBConnPool } from '../mysqllib/pool';
import { MRedisConnPool } from '../redislib/connPool';
import Pubsub from '../redislib/pubsub';
import * as authPubsub from './authPubsub';
import stoppable from 'stoppable';
import cms, { load as loadCms } from '../cms';
import * as configPubsubSyncer from '../motiflib/model/config/configPubsubSyncer';
import { MysqlReqRepCounter } from '../mysqllib/mysqlReqRepCounter';
import * as Sentry from '@sentry/node';
import townUserWeeklyInvestmentReport from './api/townUserWeeklyInvestmentReport';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------

// Main authd app.
const publicAuthApp = express();
const privateAuthApp = express();

publicAuthApp.disable('x-powered-by');
publicAuthApp.disable('etag');
publicAuthApp.disable('content-type');

privateAuthApp.disable('x-powered-by');
privateAuthApp.disable('etag');
privateAuthApp.disable('content-type');

const publicAuthServer = stoppable(http.createServer(publicAuthApp));
const privateAuthServer = stoppable(http.createServer(privateAuthApp));
privateAuthServer.keepAliveTimeout = 0;

let stopping = false;

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

function authReqLog(tokens, req, res) {
  if (req.url === '/health') {
    return;
  }

  mlog.info('authd-req', {
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  });
  return null;
}

async function closeHttpServer() {
  return new Promise<void>((resolve, reject) => {
    publicAuthServer.stop((err) => {
      if (err) {
        return reject(err);
      }

      privateAuthServer.stop((err) => {
        if (err) {
          return reject(err);
        }
        resolve();
      });
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');
    // Close mysql connection pool.
    await Container.get(DBConnPool).destroy();
    await Container.of('pubsub-auth').get(Pubsub).quit();
    await Container.of('pubsub-config').get(Pubsub).quit();
    await Container.of('monitor-redis').get(MRedisConnPool).destroy();
    await Container.of('order-redis').get(MRedisConnPool).destroy();
    await Container.of('auth-redis').get(MRedisConnPool).destroy();
    await Container.get(MRedisConnPool).destroy();

    await closeHttpServer();

    stopTogglet();

    mlog.info('auth-server closed');
  } catch (error) {
    mlog.error('graceful shutdown failed', { error: error.message });
    process.exit(1);
  }

  mlog.info('server stopped');
  process.exitCode = 0;
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export const start = async () => {
  try {
    await mhttp.configd.registerInstance(undefined, mconf.appInstanceId, mconf.hostname);

    mutil.initSentry();

    // Init http clients.
    mhttp.init();

    await startTogglet();

    // Init cms.
    loadCms();

    // Init mysql connection pool.
    const dbConnPool = Container.get(DBConnPool);
    await dbConnPool.init(mconf.mysqlAuthDb);

    // Init redis pubsub.
    const pubsub = Container.of('pubsub-auth').get(Pubsub);
    pubsub.init(mconf.authPubsubRedis);
    authPubsub.init(pubsub);

    const configPubsub = Container.of('pubsub-config').get(Pubsub);
    configPubsub.init(mconf.configPubsubRedis);

    configPubsubSyncer.subscribeForUpdateMaxUsersPerWorld(configPubsub, () => {
      // 새로운 월드당 최대유저수 이벤트 발생시 처리할 작업을 등록
    });

    // Init user cache redis pool.
    const userCacheRedis = Container.get(MRedisConnPool);
    await userCacheRedis.init('user-cache-redis', mconf.userCacheRedis);

    // Init monitor redis pool.
    const monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
    await monitorRedis.init('monitor-redis', mconf.monitorRedis);

    // Init order redis pool.
    const orderRedis = Container.of('order-redis').get(MRedisConnPool);
    await orderRedis.init('order-redis', mconf.orderRedis);

    // Init auth redis pool.
    const authRedis = Container.of('auth-redis').get(MRedisConnPool);
    await authRedis.init('auth-redis', mconf.authRedis);

    // mysql request-response counter
    const reqRepCounter = Container.get(MysqlReqRepCounter);
    reqRepCounter.setLimit(mconf.mysqlReqRepLimit || 100);

    // listen public auth app
    {
      const bindAddress = mconf.publicApiService.bindAddress;
      const port = mconf.publicApiService.port;
      publicAuthApp.use(morgan(authReqLog));
      publicAuthApp.use(bodyParser.json());
      publicAuthApp.use(bodyParser.urlencoded({ extended: true }));
      mutil.registerHealthCheck(publicAuthApp);
      mutil.registerGarbageCollector(publicAuthApp);
      await dirAsApi.register(publicAuthApp, path.join(__dirname, 'api', 'public'));

      // /public/townUserWeeklyInvestmentReport
      publicAuthApp.get(
        '/public/townUserWeeklyInvestmentReport/:nid/:lang?',
        townUserWeeklyInvestmentReport
      );

      publicAuthApp.use(expressError.middleware);
      publicAuthServer.listen(port, bindAddress, () => {
        mlog.info('start public auth server listening ...', { bindAddress, port });
      });
    }

    // listen private auth app
    {
      const bindAddress = mconf.privateApiService.bindAddress;
      const port = mconf.privateApiService.port;
      privateAuthApp.use(morgan(authReqLog));
      privateAuthApp.use(bodyParser.json());
      mutil.registerHealthCheck(privateAuthApp);
      await dirAsApi.register(privateAuthApp, path.join(__dirname, 'api', 'private'));

      privateAuthApp.use(expressError.middleware);
      privateAuthServer.listen(port, bindAddress, () => {
        mlog.info('start private auth server listening ...', { bindAddress, port });
      });
    }

    // config final sync
    const beforeVer = mconf.layoutVersion;
    await mhttp.configd.sync(beforeVer, isStopping, stop).then(() => {
      if (beforeVer < mconf.layoutVersion) {
        // do something
      }
    });
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
};

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await stopServer();
}
