// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { BUILDING_TYPE } from '../../../cms/ex';
import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import tuEnterBuilding from '../../../mysqllib/txn/tuEnterBuilding';
import { LobbyService } from '../../server';
import { TownManager } from '../../townManager';
import { NationIntimacy } from '../../nationManager';
import { Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import UserTown, { ARREST_STATE } from '../../userTown';
import * as mutil from '../../../motiflib/mutil';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { RELIGION_TYPE } from '../../../cms/religionDesc';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import { QuestUtil } from '../../questUtil';
import { TOWN_USER_STATE } from '../../../townd/townUserState';
import { ClientPacketHandler } from '../index';
import { TownDesc } from '../../../cms/townDesc';
import { TownBuildingDesc } from '../../../cms/townBuildingDesc';
import { TownKickDesc } from '../../../cms/townKickDesc';
import { NationDesc } from '../../../cms/nationDesc';
import { BuffSync } from '../../userBuffs';
import { BuildingHoursElem } from '../../../cms/buildingHoursDesc';
import { ShipSlotChange } from '../../ship';
import mlog from '../../../motiflib/mlog';
import { CostumeShipSlotChange } from '../../userFleets';
import { SHIP_SLOT_SUB_TYPE } from '../../../cms/shipSlotDesc';
import { FriendlyEncountResult, FriendlyEncountState } from '../../userFriendlyEncount';

// ----------------------------------------------------------------------------
// 타운에서 건물 입장.
// ----------------------------------------------------------------------------

const rsn = 'enter_building';
const add_rsn = null;

interface RequestBody {
  buildingType: number;
  religionType: number;
  bForce: boolean;
}

interface Response extends BuffSync {
  slowdownMateCmsId?: number;
}

// ----------------------------------------------------------------------------
export class Cph_Town_EnterBuilding implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const { userDbConnPoolMgr, nationIntimacy } = Container.get(LobbyService);
    const townManager = Container.get(TownManager);

    const body: RequestBody = packet.bodyObj;
    const buildingType = body.buildingType;
    const religionType = body.religionType || RELIGION_TYPE.NONE;
    const bForce = body.bForce;

    // [TEMP] For not login user in town.
    // user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // 바로 이동을 통해 leave building 없이 enter building 할 수 있음.
    user.userState.ensureInTown();
    const townInfo = user.userTown.getTownInfo();
    if (!townInfo) {
      throw new MError('user-not-town', MErrorCode.USER_NOT_IN_TOWN, {
        gameState: user.userState.getGameState(),
      });
    }

    // Since we're in IN_TOWN, game state, we assume user.townInfo is valid.
    // If already in the same building, return immediately.
    if (townInfo.buildingType === buildingType) {
      return user.sendJsonPacket(packet.seqNum, packet.type, {});
    }

    const friendlyEncount: FriendlyEncountState = user.userFriendlyEncount.getEncountState();
    if (friendlyEncount && friendlyEncount.bAttack) {
      throw new MError(
        'friendly-encount-attacker-not-allowed-to-enter-building',
        MErrorCode.FRIENDLY_ENCOUNT_ATTACKER_NOT_ALLOWED_TO_ENTER_BUILDING,
        { friendlyEncount }
      );
    }

    const townCmsId = user.userTown.getTownCmsId();
    const townState = user.userTown.getUserTownState(townCmsId);
    if (townState.arrestState === ARREST_STATE.CAUGHT) {
      throw new MError('invalid-arrest-state', MErrorCode.INVALID_ARREST_STATE, {
        townCmsId,
        buildingType,
        arrestState: townState.arrestState,
      });
    }

    const townBuildingCmses = cmsEx.getTownBuilding(townCmsId, buildingType);
    const townBuildingCms = townBuildingCmses
      ? buildingType === BUILDING_TYPE.RELIGION
        ? townBuildingCmses.find((elem) => elem.religionType === religionType)
        : townBuildingCmses[0]
      : undefined;
    if (!townBuildingCms) {
      throw new MError('invalid-town-building', MErrorCode.INVALID_REQ_BODY_TOWN_ENTER_BUILDING, {
        townCmsId,
        buildingType,
        religionType,
      });
    }

    const curTimeUtc = mutil.curTimeUtc();
    const townCms = cms.Town[townCmsId];

    if (!bForce) {
      const buildingHoursCmsId = townBuildingCms.buildingHoursId;
      const buildingHoursElem: BuildingHoursElem = cmsEx.getBuildingHours(
        buildingHoursCmsId,
        _.values(townManager.getDevelopmentLevels(townCmsId))
      );
      // const constGraceTime = cms.Const.BuildingEnterGraceTime.value;
      const townTime = TownManager.calcTownLocalTimeUtc(townCms, curTimeUtc);
      // if (
      //   (townTime + constGraceTime < buildingHoursElem.OpenTime1 * 3600 ||
      //     townTime - constGraceTime > buildingHoursElem.CloseTime1 * 3600) &&
      //   (townTime + constGraceTime < buildingHoursElem.OpenTime2 * 3600 ||
      //     townTime - constGraceTime > buildingHoursElem.CloseTime2 * 3600)
      // ) {
      //   throw new MError('can-not-enter-building', MErrorCode.CAN_NOT_ENTER_TOWN_BUILDING, {
      //     townCmsId,
      //     buildingType,
      //     townTime,
      //     constGraceTime,
      //     buildingHoursElem,
      //   });
      // }

      // 도구점에서 암시장 입장 가능한지 셋팅
      if (townBuildingCms.buildingType === BUILDING_TYPE.SHOP) {
        user.userTown.setBuyBlackMarket(
          townCmsId,
          townTime,
          buildingHoursElem.OpenTime2 * 3600,
          buildingHoursElem.CloseTime2 * 3600,
          user
        );
      }
    }

    // Check mate slowdown
    let slowdownMateCmsId;
    let bIsSlowdownChecked = false;
    let slowdownMateStateFlags;
    // 도시에서 1회의 확률로 태업이 발생함
    if (
      !user.userTown.isSlowdownEventChecked &&
      (buildingType === BUILDING_TYPE.DEPART || buildingType === BUILDING_TYPE.PUB)
    ) {
      slowdownMateCmsId = user.userFleets.checkSlowdownEvent(user.userMates);
      bIsSlowdownChecked = true;
      if (slowdownMateCmsId) {
        slowdownMateStateFlags =
          user.userMates.getMate(slowdownMateCmsId).getStateFlags() |
          cmsEx.MATE_STATE_FLAG.SLOWDOWN;
      }
    }

    // 체포 이벤트 발생 검사
    let newArrestState: ARREST_STATE | undefined;
    const occurredTownKick: OccurredTownKick | undefined = _chooseTownKickToOccur(
      user,
      townState.arrestState,
      townCms,
      townBuildingCms,
      townManager,
      nationIntimacy,
      curTimeUtc
    );
    if (occurredTownKick) {
      const kickCms = occurredTownKick.townKickCms;
      if (kickCms.kickResultType === cmsEx.KICK_RESULT_TYPE.NEGO) {
        newArrestState = ARREST_STATE.CAUGHT;
      } else if (kickCms.kickResultType === cmsEx.KICK_RESULT_TYPE.KICK) {
        _glogTownKick(user, townCms, townBuildingCms, occurredTownKick);
        return user.sendJsonPacket(packet.seqNum, packet.type, {
          bKicked: true,
        });
      }
    }

    let expiredEquipmentIds: number[] = [];
    const shipSlotChanges: ShipSlotChange[] = [];
    const costumeShipSlotChanges: CostumeShipSlotChange[] = [];
    // 기간 지난 장비, 부품 장착 해제
    if (buildingType !== BUILDING_TYPE.DEPART) {
      expiredEquipmentIds = user.userMates.getExpiredEquippedMateEquipments(curTimeUtc);
      const expiredShipSlotItemIds = user.userInven.getExpiredEquippedShipSlotItemIds(curTimeUtc);
      for (const shipSlotId of expiredShipSlotItemIds) {
        const shipSlotItem = user.userInven.getShipSlotItem(shipSlotId);
        if (shipSlotItem.isEquippedCostumeShipSlot) {
          const shipSlotCms = cms.ShipSlot[shipSlotItem.shipSlotCmsId];
          costumeShipSlotChanges.push({
            slotSubType: shipSlotCms.slotSubType,
          });
        } else {
          shipSlotChanges.push({
            shipId: shipSlotItem.equippedShipId,
            slotIndex: shipSlotItem.equippedShipSlotIdx,
            shipSlotItemId: 0,
            mateCmsId: null,
            isLocked: 0,
          });
        }
      }
    }

    const sync: Sync = {
      add: {
        user: {},
        mateEquipments: {},
      },
    };

    const townApi = mhttp.townpx.channel(townInfo.url);
    return townApi
      .enterBuilding(user.userId, buildingType)

      .then(() => {
        return tuEnterBuilding(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          bIsSlowdownChecked,
          slowdownMateCmsId,
          slowdownMateStateFlags,
          newArrestState,
          townCmsId,
          expiredEquipmentIds,
          shipSlotChanges,
          costumeShipSlotChanges
        );
      })

      .then(() => {
        sync.add.user.buildingType = buildingType;
        sync.add.user.townUserState = TOWN_USER_STATE.IN_BUILDING;

        // u_ship_slots shipSlotItemId 컬럼 체크용 로그
        mlog.info('shipSlotChange enterBuilding', {
          userId: user.userId,
          shipSlotChanges,
        });

        user.userTown.enterBuilding(buildingType, religionType);
        user.userState.setTownUserState(TOWN_USER_STATE.IN_BUILDING);

        if (newArrestState !== undefined) {
          if (occurredTownKick) {
            _glogTownKick(user, townCms, townBuildingCms, occurredTownKick);
          }

          _.merge<Sync, Sync>(sync, {
            add: {
              towns: {
                [townCmsId]: {
                  myTownState: {
                    arrestState: newArrestState,
                  },
                },
              },
            },
          });
          user.userTown.setArrestState(townCmsId, newArrestState);
        }
        const resp: Response = {
          sync,
        };

        if (bIsSlowdownChecked) {
          user.userTown.isSlowdownEventChecked = true;
        }

        if (slowdownMateCmsId) {
          const mate = user.userMates.getMate(slowdownMateCmsId);
          mate.setStateFlags(slowdownMateStateFlags, user.companyStat);

          const building_name = displayNameUtil.getTownBuildingDisplayName(townBuildingCms);
          mate.addSlowdownBuff(user, { user, rsn, add_rsn, building_name }, resp);
          sync.add.mates = {
            [slowdownMateCmsId]: {
              stateFlags: slowdownMateStateFlags,
            },
          };
          resp.slowdownMateCmsId = slowdownMateCmsId;
        }

        if (expiredEquipmentIds && expiredEquipmentIds.length > 0) {
          for (const id of expiredEquipmentIds) {
            user.userMates.unequipMateEquipment(
              id,
              user.companyStat,
              user.userPassives,
              user.userFleets,
              user.userSailing,
              user.userTriggers,
              user.userBuffs,
              resp.sync,
              { user, rsn, add_rsn }
            );
            resp.sync.add.mateEquipments[id] = {
              equippedMateCmsId: 0,
            };
          }
        }

        if (shipSlotChanges && shipSlotChanges.length > 0) {
          for (const change of shipSlotChanges) {
            const userShip = user.userFleets.getShip(change.shipId);
            userShip.applyEquipSlotItem(user, change, { user, rsn, add_rsn }, resp.sync);
          }
        }

        if (costumeShipSlotChanges && costumeShipSlotChanges.length > 0) {
          for (const change of costumeShipSlotChanges) {
            user.userFleets.applyCostumeShipSlotItem(
              user,
              change,
              { user, rsn, add_rsn },
              resp.sync
            );
          }
        }
      })
      .then(() => {
        return user.handleUnexpectedException(
          FriendlyEncountResult.ENTER_BUILDING,
          curTimeUtc,
          rsn
        );
      })
      .then(() => {
        return user.sendJsonPacket<Response>(packet.seqNum, packet.type, { sync });
      });
  }
}

interface OccurredTownKick {
  townKickCms: TownKickDesc;

  // 발생했을 때 상황.
  townNationCms: NationDesc;
  townTimeSec: number; // 항구 시각 [0, 86400)
  intimacyValue: number | undefined; // 자국항인 경우 등 우호도가 없는 경우가 있음
  reputationValue: number;
}

function _chooseTownKickToOccur(
  user: User,
  arrestState: ARREST_STATE,
  townCms: TownDesc,
  townBuildingCms: TownBuildingDesc,
  townManager: TownManager,
  nationIntimacy: NationIntimacy,
  curTimeUtc: number
): OccurredTownKick | undefined {
  if (arrestState !== ARREST_STATE.NONE) {
    return;
  }
  if (user.nationCmsId === undefined) {
    // 소속된 국가가 없으면 체포 이벤트가 발생하지 않는다함.
    return;
  }

  const townKickCmses = cmsEx.getTownKick(
    cmsEx.KICK_SITUATION_TYPE.ENTER_BUILDING,
    townBuildingCms.buildingType
  );
  if (!townKickCmses) {
    return;
  }

  const townNationCmsId = townManager.getNationOfTown(townCms.id);
  const townNationCms = cms.Nation[townNationCmsId];
  if (!townNationCms.canSelect) {
    // 선택할 수 없는 국가의 항구에서는 체포 이벤트가 발생하지 않는다.
    // (우호도/평판 관련인 듯.)
    return;
  }

  if (
    UserTown.isTownDisguiseSatisfied(
      townCms,
      user.userMates.getLeaderMate(user.userFleets).getNub()
    )
  ) {
    // 복장 조건을 만족하면 체포 이벤트가 발생하지 않는다.
    return;
  }

  if (
    townCms.ownType === cmsEx.TOWN_OWN_TYPE.CAPITAL_TOWN &&
    townCms.nationId === user.nationCmsId &&
    townBuildingCms.buildingType === BUILDING_TYPE.PALACE &&
    QuestUtil.getExistAllowablePalaceRoyalOrder(user)
  ) {
    // 칙명을 받을 수 있는 조건일 때, 왕궁 입장은 체포 이벤트가 발생하지 않는다.
    return;
  }

  const townTimeSec = TownManager.calcTownLocalTimeUtc(townCms, curTimeUtc);
  const intimacyValue =
    user.nationCmsId !== townNationCms.id
      ? nationIntimacy.getIntimacyValue(user.nationCmsId, townNationCms.id)
      : undefined;
  const reputationValue = user.userReputation.get(townNationCms.id, curTimeUtc);

  for (const kickCms of townKickCmses) {
    if (
      user.userTown.isArrestOccurred(
        kickCms,
        user,
        townTimeSec,
        intimacyValue,
        reputationValue,
        townCms,
        townNationCmsId
      ) ||
      user.userSmuggle.isArrestOccurred(user, townBuildingCms)
    ) {
      return {
        townKickCms: kickCms,
        intimacyValue,
        reputationValue,
        townNationCms,
        townTimeSec,
      };
    }
  }

  return;
}

function _glogTownKick(
  user: User,
  townCms: TownDesc,
  townBuildingCms: TownBuildingDesc,
  occurredTownKick: OccurredTownKick
) {
  user.glog('town_kick', {
    rsn,
    add_rsn,
    flag: 1, // 이벤트 발생:1 / 유저 선택:2
    type: occurredTownKick.townKickCms.kickSituationType,
    town_id: townCms.id,
    town_name: townCms.name,
    building_name: townBuildingCms
      ? displayNameUtil.getTownBuildingDisplayName(townBuildingCms)
      : null,
    nation: user.nationCmsId ?? null,
    town_nation: occurredTownKick.townNationCms.id,
    town_type: townCms.ownType, // TODO 동맹항/점령항
    town_time: mutil.formatDaySecondsToHHMM(occurredTownKick.townTimeSec), // '16:05'
    nation_amity: occurredTownKick.intimacyValue ?? null,
    nation_reputation: occurredTownKick.reputationValue,

    // flag 2일 때
    choice: null,
    result_type: null,
    pr_data: null,
  });
}
