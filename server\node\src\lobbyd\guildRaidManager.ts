// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container, Service } from 'typedi';
import cms from '../cms';
import * as cmsEx from '../cms/ex';
import * as mutil from '../motiflib/mutil';
import { LobbyService } from './server';
import Pubsub from '../redislib/pubsub';
import mlog from '../motiflib/mlog';
import { buildAreaOceanFleetData } from '../motiflib/model/ocean/npcFleetBuilder';
import { MRedisConnPool } from '../redislib/connPool';
import { RECORD_HP } from '../cms/BossRaidDesc';
import { MError, MErrorCode } from '../motiflib/merror';
import { User } from './user';
import {
  GUILD_RAID_NOTICE_TYPE,
  RAID_HIT_TYPE,
  RaidBattleResult,
  GUILD_RAID_RANKING_CATEGORY,
  GuildRaidProgressNub,
  GuildData,
  GUILD_RAID_STATE,
} from '../motiflib/model/lobby';
import { UserManager } from './userManager';
import * as proto from '../proto/lobby/proto';
import { OceanFleetData } from '../motiflib/model/ocean';
import { oceanNpcStatCache } from '../motiflib/stat/cache/oceanNpcStatCache';
import { SECONDS_PER_DAY, SECONDS_PER_HOUR } from '../formula';
import { BossRaidRankingDesc, RAID_RANKING_REWARD_CALC_TYPE } from '../cms/BossRaidRankingDesc';
import moment from 'moment';
import mconf from '../motiflib/mconf';
import glog from '../motiflib/gameLog';
import { isNotANumber } from '../motiflib/mutil';
import { GuildBossRaidDesc } from '../cms/guildBossRaidDesc';
import { getUserDbShardId } from '../mysqllib/mysqlUtil';
import { UserLightInfo, getUserLightInfos } from '../motiflib/userCacheRedisHelper';
import puLangAndGameOptionPushNotificationLoad from '../mysqllib/sp/puLangAndGameOptionPushNotificationLoad';
import { PUSH_NOTIFICATION_ID, PushNotificationDesc } from '../cms/pushNotificationDesc';
import { PushNotificationOption } from '../saild/serverPushNotification';
import mhttp from '../motiflib/mhttp';
import { getPushMsg, isAllowNotification } from '../motiflib/serverPushNotification';
import { Sync } from './type/sync';
import { GuildLogUtil, GuildUtil } from './guildUtil';

// 상회 정보 로그 기록 주기(초)
const LOG_INTERVAL = 60;

export type RewardNMailCmsId = {
  mailCmsId: number;
  rewardCmsIds: number[];
};

@Service()
export class GuildRaidManager {
  private guildRaidContainers: {
    [guildId: number]: {
      [guildBossRaidCmsId: number]: GuildRaid;
    };
  };
  private lastRaidsLogTimeUtc: number;

  constructor() {
    this.guildRaidContainers = {};
    this.lastRaidsLogTimeUtc = 0;
  }

  // ----------------------------------------------------------------------------
  getFirstGuildRaid(guildId: number): GuildRaid {
    if (this.guildRaidContainers[guildId]) {
      const raids = _.values(this.guildRaidContainers[guildId]);
      if (raids.length > 0) {
        return raids[0];
      }
    }
    return null;
  }

  // ----------------------------------------------------------------------------
  getGuildRaid(guildId: number, cmsId: number): GuildRaid {
    if (this.guildRaidContainers[guildId]) {
      return this.guildRaidContainers[guildId][cmsId];
    }
    return null;
  }
  // ----------------------------------------------------------------------------
  getGuildRaids(guildId: number): GuildRaid[] {
    if (this.guildRaidContainers[guildId]) {
      return _.values(this.guildRaidContainers[guildId]);
    }
    return [];
  }

  // ----------------------------------------------------------------------------
  insertGuildRaid(guildId: number, cmsId: number, gr: GuildRaid): void {
    if (!this.guildRaidContainers[guildId]) {
      this.guildRaidContainers[guildId] = {};
    }
    this.guildRaidContainers[guildId][cmsId] = gr;
  }

  // ----------------------------------------------------------------------------
  // 서버 실행 시 현재 진행 중인 상회토벌을 모두 로드
  // ----------------------------------------------------------------------------
  loadGuildRaid() {
    const { guildRedis } = Container.get(LobbyService);
    const _loadFromRedis = (cursor: number) => {
      // 레디스 호출당 로드 할 토벌 수
      const raidCountPerCall: number = 20;
      return guildRedis['loadGuildRaids'](cursor, raidCountPerCall).then((out: string) => {
        const result: {
          nextCursor: number;
          guildRaids: { [index: number]: GuildRaidProgressNub };
        } = JSON.parse(out);

        // 로드한 토벌정도를 추가.
        _.forOwn(result.guildRaids, (progressNub: GuildRaidProgressNub) => {
          this.insertGuildRaid(
            progressNub.guildId,
            progressNub.cmsId,
            new GuildRaid(cms.GuildBossRaid[progressNub.cmsId], progressNub)
          );

          mlog.info('[GUILD_RAID] loading-guild-raid', {
            guildRaid: progressNub,
          });
        });

        if (result.nextCursor > 0) {
          return _loadFromRedis(result.nextCursor);
        }

        let count = 0;
        _.keys(this.guildRaidContainers).forEach((key) => {
          count += _.keys(this.guildRaidContainers[key]).length;
        });

        mlog.info('[GUILD_RAID] load-complete-from-guild-redis', {
          totalRaids: count,
        });

        this.raidTick();
      });
    };

    return _loadFromRedis(0);
  }

  // ------------------------------------------------------------------------
  raidTick(): void {
    const { guildRedis } = Container.get(LobbyService);

    setInterval(() => {
      const nowUtc = mutil.curTimeUtc();
      const bWirteLog = nowUtc - this.lastRaidsLogTimeUtc > LOG_INTERVAL;
      const runningGuildIds = [];

      if (bWirteLog) {
        this.lastRaidsLogTimeUtc = nowUtc;
      }
      const toDeleteGuildRaid: { guildId: number; cmsId: number }[] = [];

      _.forOwn(this.guildRaidContainers, (guildRaidContainer) => {
        _.forOwn(guildRaidContainer, (guildRaid: GuildRaid) => {
          if (!guildRaid.isActivated()) {
            toDeleteGuildRaid.push({
              guildId: guildRaid.getNub().guildId,
              cmsId: guildRaid.getNub().cmsId,
            });
            return;
          }
          guildRaid.tick(guildRedis, nowUtc);

          // 로그용 스냅샷 기록.
          if (bWirteLog) {
            runningGuildIds.push(guildRaid.getNub().guildId);
          }
        });
      });

      toDeleteGuildRaid.forEach((elem) => {
        if (this.guildRaidContainers[elem.guildId]) {
          delete this.guildRaidContainers[elem.guildId][elem.cmsId];
          if (_.keys(this.guildRaidContainers[elem.guildId]).length === 0)
            delete this.guildRaidContainers[elem.guildId];
        }
      });

      if (bWirteLog) {
        mlog.info('current-guild-raids-snapshot', {
          count: runningGuildIds.length,
          guildIds: runningGuildIds,
        });
      }
    }, 1000);
  }

  // ------------------------------------------------------------------------
  subcribe(guildId: number, cmsId: number, notice: number) {
    let guildRaid: GuildRaid = this.getGuildRaid(guildId, cmsId);
    if (!guildRaid) {
      const guildBossRaidCms: GuildBossRaidDesc = cms.GuildBossRaid[cmsId];
      if (!guildBossRaidCms) {
        mlog.error('invalid-GuildBossRaid-cms-id', {
          guildId,
          cmsId,
        });
        return;
      }
      const now: number = mutil.curTimeUtc();
      const maxHp: number = GuildRaidUtil.buildRaidBossHP(guildBossRaidCms);
      // 없을 경우 임시로 만든다. GuildRaid:subscribe() 함수에서 다시 로드한다.
      const guildRaidProgressNub: GuildRaidProgressNub = {
        guildId,
        cmsId: guildBossRaidCms.id,
        stateSeq: 1,
        stateBeginAt: now,
        stateEndAt: now + cms.Const.GuildBossRaidPlayHour.value * SECONDS_PER_HOUR,
        state: GUILD_RAID_STATE.INPROGRESS,
        maxHp,
        curHp: maxHp,
      };

      guildRaid = new GuildRaid(guildBossRaidCms, guildRaidProgressNub);
      this.insertGuildRaid(guildId, cmsId, guildRaid);
    }

    guildRaid.subcribe(notice);
  }

  // ------------------------------------------------------------------------
  // 신규 상회토벌 오픈
  // ------------------------------------------------------------------------
  openNewGuildRaid(guildId: number, guildData: GuildData, guildBossRaidCms: GuildBossRaidDesc) {
    const now: number = mutil.curTimeUtc();

    const maxHp: number = GuildRaidUtil.buildRaidBossHP(guildBossRaidCms);
    const guildRaidProgressNub: GuildRaidProgressNub = {
      guildId,
      cmsId: guildBossRaidCms.id,
      stateSeq: 1,
      stateBeginAt: now,
      stateEndAt: now + cms.Const.GuildBossRaidPlayHour.value * SECONDS_PER_HOUR,
      state: GUILD_RAID_STATE.INPROGRESS,
      maxHp,
      curHp: maxHp,
    };

    const { guildRedis } = Container.get(LobbyService);
    return guildRedis['openGuildRaidProgress'](
      guildRaidProgressNub.guildId,
      guildRaidProgressNub.cmsId,
      guildRaidProgressNub.stateSeq,
      guildRaidProgressNub.stateBeginAt,
      guildRaidProgressNub.stateEndAt,
      guildRaidProgressNub.state,
      guildRaidProgressNub.maxHp,
      guildRaidProgressNub.curHp
    ).then(() => {
      guildData.lastOpenTimeRaids[guildBossRaidCms.id] = now;

      const pubsub = Container.of('pubsub-world').get(Pubsub);
      return pubsub
        .publish(
          'update_guild_raid',
          JSON.stringify({
            guildId: guildRaidProgressNub.guildId,
            cmsId: guildRaidProgressNub.cmsId,
            notice: GUILD_RAID_NOTICE_TYPE.APPEARANCE,
          })
        )
        .then(() => {
          GuildRaidUtil.pushNotificationToGuildMembers(guildId, PUSH_NOTIFICATION_ID.GUILD_RAID_OPEN);
        })
        .catch((e) => {
          mlog.alert('[guild-raid-pubsub] publish error!', {
            guildId: guildRaidProgressNub.guildId,
            cmsId: guildRaidProgressNub.cmsId,
            error: e.message,
            stack: e.stack,
          });
        });
    });
  }
}

enum TIMER {
  SYNC_INVERVAL = 30, // 30초마다 진행상태 로비와 동기화.
}
export class GuildRaid {
  private guildBossRaidCms: GuildBossRaidDesc;

  // 현재 상회 레이드 진행정보
  private progressNub: GuildRaidProgressNub;

  // 현재 스케줄 만료할지 여부.(개발용)
  devScheduleEnd: boolean;

  private bActivated: boolean;

  // redis 데이터와 동기화할 시간(UTC)
  private nextSyncTimeUtc: number;

  constructor(guildBossRaidCms: GuildBossRaidDesc, raidNub: GuildRaidProgressNub | null) {
    this.progressNub = raidNub;
    this.guildBossRaidCms = guildBossRaidCms;
    this.bActivated = true;
    this.devScheduleEnd = false;

    this.nextSyncTimeUtc = mutil.curTimeUtc() + TIMER.SYNC_INVERVAL;
  }

  // ----------------------------------------------------------------------------
  isInProgress(): boolean {
    return this.progressNub.state === GUILD_RAID_STATE.INPROGRESS;
  }

  // ----------------------------------------------------------------------------
  leftTime(): number {
    return this.progressNub.stateEndAt - this.progressNub.stateBeginAt;
  }

  // ----------------------------------------------------------------------------
  getNub(): GuildRaidProgressNub {
    return this.progressNub;
  }

  // ----------------------------------------------------------------------------
  private hasStateExpired(now: number) {
    return now >= this.progressNub.stateEndAt;
  }

  // ----------------------------------------------------------------------------
  isActivated(): boolean {
    return this.bActivated;
  }

  // ----------------------------------------------------------------------------
  private _devTickLog(now) {
    const reaminingSec = this.progressNub.stateEndAt - now;
    const hours = Math.floor(reaminingSec / 3600);
    const mins = Math.floor((reaminingSec % 3600) / 60);
    const secs = Math.floor((reaminingSec % 3600) % 60);
    mlog.info('[GUILD_RAID] tick', {
      name: this.guildBossRaidCms.name,
      state: GUILD_RAID_STATE[this.progressNub.state],
      time: `${hours}:${mins}:${secs}`,
    });
  }

  // ----------------------------------------------------------------------------
  // 로비서버에게 상회 레이드 상태 업데이트 pub_sub 전달.
  // ----------------------------------------------------------------------------
  publishToLobbys(msg: string, notice: GUILD_RAID_NOTICE_TYPE): Promise<any> {
    const pubsub = Container.of('pubsub-world').get(Pubsub);
    return pubsub
      .publish(
        msg,
        JSON.stringify({
          guildId: this.progressNub.guildId,
          cmsId: this.guildBossRaidCms.id,
          notice,
        })
      )
      .catch((e) => {
        mlog.alert('[guild-raid-pubsub] publish error!', {
          guildId: this.progressNub.guildId,
          bossRaidCmsId: this.guildBossRaidCms.id,
          error: e.message,
          stack: e.stack,
        });
      });
  }

  // ----------------------------------------------------------------------------
  // 상회 레이드 상태 업데이트시 pub_sub 이벤트 받는 callback 함수
  // ----------------------------------------------------------------------------
  subcribe(notice: number) {
    const { guildRedis } = Container.get(LobbyService);

    const old: GuildRaidProgressNub = this.progressNub;
    return guildRedis['getGuildRaidProgress'](this.progressNub.guildId, this.progressNub.cmsId)
      .then((raidProgressStr: string) => {
        if (!raidProgressStr) {
          throw new MError(
            'cannot-find-guild-raid-progress.',
            MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS,
            {
              progress: this.progressNub,
            }
          );
        }
        this.progressNub = JSON.parse(raidProgressStr);

        //전달할 notice가 없을 경우 redis로부터 받은 데이터만 갱신.
        if (notice === null) {
          return;
        }
        mlog.info('[GUILD_RAID] updated-pubsub', {
          notice: GUILD_RAID_NOTICE_TYPE[notice],
          oldRaid: old,
          curRaid: this.progressNub,
        });

        return this.broadCastGuildRaidState(guildRedis, notice);
      })
      .catch((error) => {
        mlog.error('getRaidProgress-redis-error!', {
          cmsId: this.progressNub.cmsId,
          message: error.message,
          stack: error.stack,
        });
      });
  }

  // ----------------------------------------------------------------------------
  // 업데이트
  // ----------------------------------------------------------------------------
  tick(guildRedis: MRedisConnPool, now: number) {
    if (!this.progressNub) {
      return;
    }
    if (this.devScheduleEnd) {
      this.devScheduleEnd = false;
      this.progressNub.stateEndAt = now;
    }
    // this._devTickLog(now);

    Promise.resolve()
      .then(() => {
        if (this.progressNub.state === GUILD_RAID_STATE.INPROGRESS) {
          if (this.progressNub.curHp <= 0) {
            mlog.info('[GUILD-RAID-TICK] progress-end(clear)', {
              guildId: this.progressNub.guildId,
              progressNub: this.progressNub,
            });

            return guildRedis['updateGuildRaidProgress'](
              this.progressNub.guildId,
              this.progressNub.cmsId,
              this.progressNub.stateSeq + 1,
              now,
              now + cms.Const.BossRaidCalculateTimeMin.value * 60,
              GUILD_RAID_STATE.CLOSING,
              this.progressNub.maxHp,
              this.progressNub.curHp,
              1 // 상회 토벌 버프 삭제 여부. 종료 시 삭제.
            ).then((updated) => {
              if (parseInt(updated, 10)) {
                this.publishToLobbys('update_guild_raid', GUILD_RAID_NOTICE_TYPE.KILL_BOSS);
              }
            });
          }
          if (this.hasStateExpired(now)) {
            mlog.info('[GUILD-RAID-TICK] progress-end(timeout)', {
              guildId: this.progressNub.guildId,
              progressNub: this.progressNub,
            });
            return guildRedis['updateGuildRaidProgress'](
              this.progressNub.guildId,
              this.progressNub.cmsId,
              this.progressNub.stateSeq + 1,
              now,
              now + cms.Const.BossRaidCalculateTimeMin.value * 60,
              GUILD_RAID_STATE.CLOSING,
              this.progressNub.maxHp,
              this.progressNub.curHp,
              0 // 상회 토벌 버프 삭제 여부. 종료 시 삭제.
            ).then((updated) => {
              if (parseInt(updated, 10)) {
                this.publishToLobbys('update_guild_raid', GUILD_RAID_NOTICE_TYPE.DISAPPEARANCE);

                GuildRaidUtil.pushNotificationToGuildMembers(this.progressNub.guildId, PUSH_NOTIFICATION_ID.GUILD_RAID_CLOSED);
              }
            });
          }
        } else if (this.progressNub.state === GUILD_RAID_STATE.CLOSING) {
          if (this.hasStateExpired(now)) {
            mlog.info('[GUILD-RAID-TICK] closing-end', {
              guildId: this.progressNub.guildId,
              progressNub: this.progressNub,
            });
            const endTimeAt = now + cms.Const.GuildBossRaidCooltimeHour.value * 3600;
            if (isNotANumber(endTimeAt)) {
              mlog.alert('[GUILD_RAID] invalid-endTimeAt-schedule', {
                raid: this.progressNub,
              });
              return;
            }

            return guildRedis['updateGuildRaidProgress'](
              this.progressNub.guildId,
              this.progressNub.cmsId,
              this.progressNub.stateSeq + 1,
              now,
              endTimeAt,
              this.progressNub.curHp === 0 ? GUILD_RAID_STATE.SUCCESS : GUILD_RAID_STATE.FAILED,
              this.progressNub.maxHp,
              this.progressNub.curHp,
              0 // 상회 토벌 버프 삭제 여부. 종료 시 삭제.
            ).then((updated) => {
              if (parseInt(updated, 10)) {
                this._glogRankBossGuildRaid(
                  this.progressNub.guildId,
                  this.progressNub.curHp === 0 ? true : false,
                  guildRedis
                );
                this.publishToLobbys('update_guild_raid', GUILD_RAID_NOTICE_TYPE.RANKING_COMPLETED);
              }
            });
          }
        } else {
          if (this.hasStateExpired(now)) {
            this.bActivated = false;

            mlog.info('[GUILD-RAID-TICK] dispose', {
              guildId: this.progressNub.guildId,
              progressNub: this.progressNub,
            });

            return guildRedis['closeGuildRaidProgress'](
              this.progressNub.guildId,
              this.progressNub.cmsId
            );
          }
        }

        // 특정 간격마다 redis 데이터와 동기화한다.
        // 만약, redis에 데이터가 없을 경우 중인 상회 레이드는 즉시 진행 종료처리한다.
        if (now > this.nextSyncTimeUtc) {
          this.nextSyncTimeUtc = now + TIMER.SYNC_INVERVAL;
          return guildRedis['getGuildRaidProgress'](
            this.progressNub.guildId,
            this.progressNub.cmsId
          ).then((result: string) => {
            const raidProgress: GuildRaidProgressNub = JSON.parse(result);
            if (!raidProgress) {
              mlog.info('[GUILD-RAID] guild-raid-has-been-closed-due-to-no-redis-data', {
                guildId: this.progressNub.guildId,
                cmsId: this.progressNub.cmsId,
                redisCacheData: this.progressNub,
              });
              this.bActivated = false;

              return guildRedis['closeGuildRaidProgress'](
                this.progressNub.guildId,
                this.progressNub.cmsId
              );
            } else {
              this.progressNub = raidProgress;
            }
          });
        }
      })
      .catch((error) => {
        mlog.alert('raid-schedule-tick-error!', {
          bossRaidCmsId: this.guildBossRaidCms.id,
          progressNub: this.progressNub,
          message: error.message,
          stack: error.stack,
        });
      });
  }

  // ----------------------------------------------------------------------------
  // 모든 상회원들에게 레이드 상태 전달.
  // ----------------------------------------------------------------------------
  broadCastGuildRaidState(guildRedis: MRedisConnPool, notice: GUILD_RAID_NOTICE_TYPE) {
    const raids: {
      [bossRaidCmsId: number]: {
        state: GUILD_RAID_STATE;
        endTimeUtc: number;
        maxHp: number;
        hp: number;
        notice?: GUILD_RAID_NOTICE_TYPE;
      };
    } = {};

    raids[this.progressNub.cmsId] = {
      state: this.progressNub.state,
      endTimeUtc: this.progressNub.stateEndAt,
      maxHp: this.progressNub.maxHp,
      hp: this.progressNub.curHp,
      notice,
    };

    const userManager = Container.get(UserManager);

    return guildRedis['getGuild'](this.progressNub.guildId).then((guild: string) => {
      if (guild) {
        const guildData: GuildData = JSON.parse(guild);

        //토벌버프가 없을경우 싱크데이터에 동기화.
        let sync: Sync = {};
        if (_.isEmpty(guildData.bossRaidBuffs)) {
          sync = {
            remove: {
              userGuild: {
                guild: {
                  bossRaidBuffs: true,
                },
              },
            },
          };
        }
        _.forOwn(guildData.members, (member) => {
          const user: User = userManager.getUserByUserId(member.userId);
          if (user) {
            user.sendJsonPacket(0, proto.Guild.STATE_SC, { sync, raids });
          }
        });
      }
    });
  }

  // ----------------------------------------------------------------------------
  // 레이드 보스 데미지 적용
  // ----------------------------------------------------------------------------
  battleResult(user: User, addedDamage: number, forDevTest?: boolean): Promise<RaidBattleResult> {
    const nowUtc = mutil.curTimeUtc();
    const { guildRedis } = Container.get(LobbyService);

    const originDamage = addedDamage;

    // 아무런 스케줄이 없는 상태에서는 데미지를 줄 수 없다.
    if (this.progressNub.state === GUILD_RAID_STATE.NONE) {
      throw new MError('have-closed-raid-schedule', MErrorCode.CLOSED_RAID, {
        userId: user.userId,
        bossRaidCmsId: this.progressNub.cmsId,
      });
    }

    // 정산이 끝난 후 남은 전투는 데미지 0으로 적용한다.
    if (
      this.progressNub.state === GUILD_RAID_STATE.SUCCESS ||
      this.progressNub.state === GUILD_RAID_STATE.FAILED
    ) {
      addedDamage = 0;
      mlog.info('[GUILD_RAID] damage-fixed-to-0-by-state', {
        userId: user.userId,
        bossRaidCmsId: this.progressNub.cmsId,
        state: this.progressNub.state,
        originDamage,
      });
    }
    const guildBossCms = cms.GuildBossRaid[this.progressNub.cmsId];
    if (guildBossCms) {
      addedDamage = GuildRaidUtil.clampDmg(
        user,
        this.progressNub,
        guildBossCms.vaildDmgMax,
        addedDamage,
        forDevTest
      );
    }

    if (isNotANumber(addedDamage)) {
      throw new MError('invalid-guild-raid-damage', MErrorCode.INTERNAL_ERROR, {
        userId: user.userId,
        addedDamage,
        progress: this.progressNub,
      });
    }

    // 종료시까지의 남은시간(분) 랭킹 집계시 동일스코어 처리를 위해 필요.
    let leftTimeSec = 0;
    if (this.progressNub.state === GUILD_RAID_STATE.INPROGRESS) {
      leftTimeSec = Math.max(0, Math.floor(this.progressNub.stateEndAt - nowUtc));
    }

    if (addedDamage === 0) {
      mlog.info('[GUILD_RAID] zero-damage', {
        userId: user.userId,
        bossRaidCmsId: this.progressNub.cmsId,
        state: this.progressNub.state,
        originDamage,
      });
    }

    let result: {
      hitResult: RAID_HIT_TYPE;
      remainingHp: number;
      // bestDamage: number;
      rankComparisons: {
        category: GUILD_RAID_RANKING_CATEGORY;
        // 이전 순위
        oldRanking: number;
        // 바뀐 순위
        newRanking: number;
        // 바뀐 순위의 스코어
        score: number;
        // 총 랭킹인원
        numOfRankers: number;
      }[];
    } = null;

    const guildBossRaidCms: GuildBossRaidDesc = cms.GuildBossRaid[this.progressNub.cmsId];

    return guildRedis['updateGuildRaidBossHp'](
      user.userId,
      user.userGuild.guildId,
      this.progressNub.cmsId,
      addedDamage,
      Math.floor(leftTimeSec / 60), // min
      guildBossRaidCms.groupNo,
      guildBossRaidCms.difficulty
    ).then((resultStr: string) => {
      result = JSON.parse(resultStr);
      const rbr: RaidBattleResult = {
        actualDamage: addedDamage,
        hitResult: result.hitResult,
        accumulatedDamage: 0,
        oldRanking: 0,
        newRanking: 0,
        rankingPct: 0,
        leftTimeSec,
        maxHp: this.progressNub.maxHp,
        curHp: result.remainingHp,
      };

      result.rankComparisons.findIndex((elem) => {
        if (elem.category === GUILD_RAID_RANKING_CATEGORY.ACCUMULATED_DMG) {
          rbr.accumulatedDamage = elem.score;
          rbr.oldRanking = elem.oldRanking;
          rbr.newRanking = elem.newRanking;
          if (elem.numOfRankers > 0) {
            const rankingPct = Math.floor((elem.newRanking / elem.numOfRankers) * 100);
            rbr.rankingPct = mutil.clamp(rankingPct, 0, 100);
          }
        }
      });

      this.publishToLobbys('update_guild_raid', GUILD_RAID_NOTICE_TYPE.LEFT_HP);

      // guild_boss_raid_finish 로그남길때  guild_data가 필요해서 얻어옴.
      const guildId = user.userGuild.guildId;
      if (guildId) {
        return GuildUtil.GetGuildDataWithMemberLightInfo(user, guildId).then((result) => {
          rbr.guild_data = GuildLogUtil.buildGLogGuildSchema(
            user.userGuild.guildId,
            result.guildData,
            result.userLightInfos
          );
          return rbr;
        });
      }
      return rbr;
    });
  }

  private _glogRankBossGuildRaid(guildId: number, bKillBoss: boolean, guildRedis: MRedisConnPool) {
    Promise.resolve()
      .then(() => {
        const rankingCategory = GUILD_RAID_RANKING_CATEGORY.ACCUMULATED_DMG;
        return guildRedis['getGuildRaidRankings'](
          guildId,
          this.progressNub.cmsId,
          rankingCategory,
          1,
          100
        ).then((result: string) => {
          const rankingPage: {
            [raking: number]: {
              userId: number;
              score: number;
              ranking: number;
            };
          } = JSON.parse(result);

          const ranks: {
            gameUserId: number;
            accumulate_dmg: number;
            accumulate_dmg_rank: number;
          }[] = [];

          _.forOwn(rankingPage, (elem) => {
            ranks.push({
              gameUserId: elem.userId,
              accumulate_dmg: elem.score,
              accumulate_dmg_rank: elem.ranking,
            });
          });

          glog('w_rank_guild_boss_raid', {
            _time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
            server_id: mconf.worldId,
            boss_raid_result: bKillBoss,
            ranks,
          });
        });
      })
      .catch((e) => {
        mlog.alert('[GUILD_RAID] faild-to-log-(w_rank_guild_boss_raid).', {
          raidCmsId: this.progressNub.cmsId,
          bKillBoss,
          message: e.message,
          stack: e.stack,
          extra: e.extra,
        });
      });
  }
}

export namespace GuildRaidUtil {
  // 레이드 보스의 전체 HP 계산.
  export function buildRaidBossHP(guildBossRaidCms: GuildBossRaidDesc): number {
    let npcFleetData: OceanFleetData = buildAreaOceanFleetData(guildBossRaidCms.targetOceanNpc);
    if (!npcFleetData) {
      mlog.error('[GUILD_RAID] failded to buildAreaOceanFleetData');
      return null;
    }
    const npcFleetStat = oceanNpcStatCache.buildFleetStatParam(npcFleetData);
    npcFleetData = oceanNpcStatCache.mergeOceanFleetDataStat({
      npcFleetData,
      fleetStat: npcFleetStat,
    });

    if (guildBossRaidCms.recordHP === RECORD_HP.DURABILITY) {
      return npcFleetData.ships[0].durability;
    } else if (guildBossRaidCms.recordHP === RECORD_HP.SAILOR) {
      return npcFleetData.ships[0].sailor * cmsEx.BATTLE_SAILOR_MULTIPLIER;
    } else if (guildBossRaidCms.recordHP === RECORD_HP.OUTER) {
      // 아직 정해진 기획이 없다.
    }
    return null;
  }

  // 랭킹에 의한 보상,메일 cmsid를 구하기.
  export function getRewardAndMailCmsId(
    user: User,
    guildBossRaidCmsId: number,
    isWin: boolean,
    userRanking: number,
    numOfRankers: number
  ): RewardNMailCmsId {
    const guildBossRaidCms = cms.GuildBossRaid[guildBossRaidCmsId];

    let mailCmsId: number = 0;
    const rewardCmsIds: number[] = [];
    if (isWin) {
      mailCmsId = guildBossRaidCms.mailIdDefeat;
      rewardCmsIds.push(guildBossRaidCms.rewardDefeat);
    } else {
      mailCmsId = guildBossRaidCms.mailIdLose;
      rewardCmsIds.push(guildBossRaidCms.rewardLose);
    }

    if (userRanking && numOfRankers) {
      // 보상가능한 목록을 저장 후  이중 가장 좋은 보상을 하나만 준다.
      const rewardableCmses: BossRaidRankingDesc[] = [];
      for (let bossRaidRankingCms of guildBossRaidCms.bossRaidRankings) {
        if (bossRaidRankingCms.type === RAID_RANKING_REWARD_CALC_TYPE.RATE_PCT) {
          // 유저의 랭킹 퍼센트 (순위가 전체중 상위 몇%인지)
          const userRatePtc = (userRanking / numOfRankers) * 100;
          if (userRatePtc > bossRaidRankingCms.rateMax) {
            continue;
          }
        } else if (bossRaidRankingCms.type === RAID_RANKING_REWARD_CALC_TYPE.RANK_VALUE) {
          if (userRanking > bossRaidRankingCms.valueMax) {
            continue;
          }
        } else {
          mlog.warn('[GUILD_RAID] invalid-type-of-BossRaidRanking-cms', {
            userId: user.userId,
            cmsId: bossRaidRankingCms.id,
            type: bossRaidRankingCms.type,
          });
          break;
        }

        rewardableCmses.push(bossRaidRankingCms);
      }

      if (rewardableCmses.length > 0) {
        // CMS 아이디가 클 수록 상위 보상이므로, 가장 높은 아이디로 보상.(기획의도)
        rewardableCmses.sort((a, b) => b.id - a.id);
        if (isWin) {
          mailCmsId = rewardableCmses[0].mailIdDefeat;
          rewardCmsIds.push(rewardableCmses[0].rewardDefeat);
        } else {
          mailCmsId = rewardableCmses[0].mailIdLose;
          rewardCmsIds.push(rewardableCmses[0].rewardLose);
        }
      }
    }

    return {
      mailCmsId,
      rewardCmsIds,
    };
  }

  export function clampDmg(
    user: User,
    nub: GuildRaidProgressNub,
    hardCap: number,
    damage: number,
    dev: boolean
  ): number {
    //  기획에서 정한 최대 데미지를 넘을 수 없다.
    if (!dev && damage >= hardCap) {
      mlog.alert('guild-raid-damage-has-far-exceeded-server-cap', {
        userId: user.userId,
        bossRaidCmsId: nub.cmsId,
        damage: damage,
        serverCap: hardCap,
        raidNub: nub,
      });
      return hardCap;
    }

    return Math.max(0, damage);
  }

  export function pushNotificationToGuildMembers(
    guildId: number,
    pushId: PUSH_NOTIFICATION_ID,
    args?: string[]
  ) {
    const { userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr } =
      Container.get(LobbyService);

    const pushCms = cms.PushNotification[pushId];
    if (!pushCms) {
      mlog.warn('invalid PushNotification id call designer', {
        userId: this._userId,
        pushId,
      });
      return;
    }

    let offlineUsers: {
      [userId: number]: {
        pubId: string;
        name: string;
      };
    } = {};
    return guildRedis['getGuild'](guildId)
      .then((guild: string) => {
        if (!guild) {
          throw new MError('cannot-find-guild.', MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS, {
            guildId,
          });
        }

        const guildData: GuildData = JSON.parse(guild);
        const userIds = [];
        _.forOwn(guildData.members, (elem) => userIds.push(elem.userId));

        const worldConfg = mconf.getWorldConfig();
        return getUserLightInfos(
          userIds,
          userCacheRedis,
          userRedis,
          guildRedis,
          townRedis,
          userDbConnPoolMgr,
          worldConfg.mysqlUserDb.shardFunction
        );
      })
      .then((userLightInfos: { [userId: number]: UserLightInfo } | null) => {
        const userIdGroupByShardid: {
          [shardId: number]: {
            userId: number;
            name: string;
            pubId: string;
          }[];
        } = {};
        _.forOwn(userLightInfos, (lightInfo, userIdStr) => {
          if (!lightInfo.isOnline) {
            offlineUsers[lightInfo.userId] = {
              pubId: lightInfo.pubId,
              name: lightInfo.name,
            };

            // DB shard Id를 찾아 적재.
            const userDbShardId = getUserDbShardId(lightInfo.userId);
            if (!userIdGroupByShardid[userDbShardId]) {
              userIdGroupByShardid[userDbShardId] = [];
            }

            userIdGroupByShardid[userDbShardId].push({
              userId: lightInfo.userId,
              name: lightInfo.name,
              pubId: lightInfo.pubId,
            });
          }
        });

        const promises = [];

        _.forOwn(userIdGroupByShardid, (guildUsers, key) => {
          const userDbShardId: number = parseInt(key);
          for (const guildUser of guildUsers) {
            promises.push(
              puLangAndGameOptionPushNotificationLoad(
                userDbConnPoolMgr.getPoolByShardId(userDbShardId),
                guildUser.userId
              )
                .then(
                  (result: {
                    lang: string;
                    isAllowed: number;
                    isNightAllowed: number;
                    allowedPushNotificationGroupIds: string;
                  }) => {
                    if (result) {
                      const pushOption: PushNotificationOption = {
                        isPushNotificationAllowed: result.isAllowed,
                        isNightPushNotificationAllowed: result.isNightAllowed,
                        allowedPushNotificationGroupIds: result.allowedPushNotificationGroupIds
                          ? JSON.parse(result.allowedPushNotificationGroupIds)
                          : null,
                      };
                      return processPushNotification(
                        pushCms,
                        guildUser.userId,
                        guildUser.pubId,
                        guildUser.name,
                        result.lang,
                        pushOption
                      );
                    }
                  }
                )
                .catch((e) => {
                  mlog.error('[guild-raid-push-notification] error!', {
                    guildId: guildId,
                    pushId,
                    userId: guildUser.userId,
                    pubId: guildUser.pubId,
                    name: guildUser.name,
                    error: e.message,
                    stack: e.stack,
                  });
                })
            );
          }
        });
        return Promise.all(promises);
      });
  }

  //----------------------------------------------------------
  function processPushNotification(
    pushCms: PushNotificationDesc,
    userId: number,
    pubId: string,
    userName: string,
    lang: string,
    pushNotificationOption: PushNotificationOption,

    args?: string[]
  ) {
    // 유저의 알람 옵션 체크
    if (!isAllowNotification(pushCms.pushGroup, pushNotificationOption)) {
      mlog.info('processPushNotification N/A', {
        userId: userId,
        pushId: pushCms.id,
      });

      return;
    }

    try {
      // 전송할 메시지 선택
      const sourceMsg = getPushMsg(lang, pushCms);
      let resultArgs: string[] = [userName];
      if (args && args.length > 0) {
        resultArgs = resultArgs.concat(args);

        // mlog.info('[TEMP] sendPushNotification args', {
        //   userId: userId,
        //   args,
        //   resultArgs,
        // });
      }
      const sendMsg = mutil.stringFormat(sourceMsg, resultArgs);

      // mlog.verbose('[TEMP] processPushNotification result msg', {
      //   userId: userId,
      //   sendMsg,
      // });

      mhttp.lgd
        .sendPushNotification(sendMsg, pubId)
        .then(() => {
          mlog.info('called sendPushNotification', {
            userId,
            pushId: pushCms.id,
            lang,
            sendMsg,
          });
        })
        .catch((err) => {
          mlog.warn('sendPushNotification failed', {
            userId: userId,
            pushId: pushCms.id,
            err: err.message,
            stack: err.stack,
          });
        });
    } catch (err) {
      mlog.error('sendPushNotification failed', {
        userId: userId,
        pushId: pushCms.id,
        lang,
        errormsg: err.message,
      });
    }
  }
}
