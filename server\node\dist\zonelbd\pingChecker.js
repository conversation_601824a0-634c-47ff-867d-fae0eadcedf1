"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const lodash_1 = __importDefault(require("lodash"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const typedi_1 = __importDefault(require("typedi"));
const server_1 = require("./server");
const cmsEx = __importStar(require("./../cms/ex"));
const mutil = __importStar(require("../motiflib/mutil"));
class PingChecker {
    constructor() { }
    startTick() {
        mlog_1.default.info('start PingChecker ...', {
            interval: mconf_1.default.ping.interval,
            timeout: mconf_1.default.ping.timeout,
        });
        this._pingInterval = setInterval(() => {
            const curTimeUtc = mutil.curTimeUtc();
            for (let zoneType = cmsEx.ZoneType.TOWN; zoneType < cmsEx.ZoneType.MAX_ZONE_TYPE; ++zoneType) {
                this._processPingTimeout(zoneType, curTimeUtc);
            }
        }, mconf_1.default.ping.interval);
    }
    stopTick() {
        if (this._pingInterval) {
            clearInterval(this._pingInterval);
        }
    }
    _processPingTimeout(zoneType, curTimeUtc) {
        const dStart = new Date();
        const zoneLbRedis = typedi_1.default.get(server_1.ZonelbService).get(zoneType);
        return zoneLbRedis['processPingTimeout'](curTimeUtc, mconf_1.default.ping.timeout / 1000)
            .then((ret) => {
            if (ret) {
                const unregisteredServerdUrls = JSON.parse(ret);
                if (!lodash_1.default.isEmpty(unregisteredServerdUrls)) {
                    mlog_1.default.warn(`processPingTimeout ${cmsEx.ZoneType[zoneType]}`, {
                        unregisteredServerdUrls,
                        processDuration: Math.abs(new Date().getTime() - dStart.getTime()),
                    });
                }
            }
        })
            .catch((err) => {
            mlog_1.default.error('/zonelbd/processPingTimeout error', { err: err.message, zoneType });
        });
    }
}
exports.default = PingChecker;
//# sourceMappingURL=pingChecker.js.map