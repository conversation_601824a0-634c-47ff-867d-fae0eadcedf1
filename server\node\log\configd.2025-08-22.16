{"level":"info","message":"[!] server is stopping: type=configd, signal=SIGINT","timestamp":"2025-08-22T07:22:55.932Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:22:55.938Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:22:55.940Z"}
{"level":"info","message":"redis pool (config-redis) destroyed","timestamp":"2025-08-22T07:22:55.944Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T07:22:55.944Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T07:22:55.945Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T07:22:55.946Z"}
{"environment":"development","type":"configd","gitCommitHash":"58e140f834d","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T15:41:41+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"configd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:22:59.533Z"}
{"layoutFileName":"local.cn.json5","level":"info","message":"Reloading layout...","timestamp":"2025-08-22T07:23:06.306Z"}
{"dump":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Layout reloaded successfully from json file.","timestamp":"2025-08-22T07:23:06.319Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"},"level":"info","message":"redis pool (config-redis) initializing ...","timestamp":"2025-08-22T07:23:06.320Z"}
{"level":"info","message":"redis pool (config-redis) initialized","timestamp":"2025-08-22T07:23:06.330Z"}
{"layoutVersion":7,"level":"info","message":"loadInstances getAllInstances success","timestamp":"2025-08-22T07:23:06.336Z"}
{"appId":"townd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10500,\"url\":\"http://DESKTOP-2FFOGVN:10500\",\"tcpServer\":{\"port\":10508,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:23:06.337Z"}
{"appId":"saild.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":11100,\"url\":\"http://DESKTOP-2FFOGVN:11100\",\"tcpServer\":{\"port\":11109,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:23:06.337Z"}
{"appId":"realmd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10900,\"url\":\"http://DESKTOP-2FFOGVN:10900\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:23:06.338Z"}
{"appId":"zonelbd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10600,\"url\":\"http://DESKTOP-2FFOGVN:10600\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:23:06.338Z"}
{"appId":"lobbyd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"socketServer\":{\"bindAddress\":\"0.0.0.0\",\"port\":10100},\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10200,\"url\":\"http://DESKTOP-2FFOGVN:10200\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:23:06.338Z"}
{"appId":"oceand.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10800,\"url\":\"http://DESKTOP-2FFOGVN:10800\",\"tcpServer\":{\"port\":10808,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:23:06.338Z"}
{"appId":"authd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"publicApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10701},\"privateApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10700,\"url\":\"http://DESKTOP-2FFOGVN:10700\"}}","worldId":""},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:23:06.338Z"}
{"value":5000,"level":"info","message":"got maxUsersPerWorld from redis","timestamp":"2025-08-22T07:23:06.339Z"}
{"path":"/getAllInstances","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.346Z"}
{"path":"/fetch","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.377Z"}
{"path":"/getMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.387Z"}
{"path":"/registerInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.396Z"}
{"path":"/reload","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.409Z"}
{"path":"/setMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.420Z"}
{"path":"/sync","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.437Z"}
{"path":"/unregisterInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:06.449Z"}
{"bindAddress":"0.0.0.0","port":10001,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:23:06.462Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"lobbyd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:23:20.346Z"}
{"url":"/registerInstance","status":"200","response-time":"95.958","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:20.348Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"oceand","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:23:21.961Z"}
{"url":"/registerInstance","status":"200","response-time":"0.913","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:21.962Z"}
{"body":{"serverType":"authd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:23:26.513Z"}
{"url":"/registerInstance","status":"200","response-time":"0.980","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:26.514Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"saild","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:23:26.685Z"}
{"url":"/registerInstance","status":"200","response-time":"0.837","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:26.686Z"}
{"url":"/sync","status":"200","response-time":"0.397","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:27.355Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"townd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:23:29.400Z"}
{"url":"/registerInstance","status":"200","response-time":"0.709","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:29.400Z"}
{"url":"/sync","status":"200","response-time":"0.464","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:31.739Z"}
{"url":"/sync","status":"200","response-time":"0.453","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:32.949Z"}
{"url":"/sync","status":"200","response-time":"0.469","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:34.047Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"realmd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:23:35.299Z"}
{"url":"/registerInstance","status":"200","response-time":"0.873","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:35.300Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"zonelbd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:23:36.585Z"}
{"url":"/registerInstance","status":"200","response-time":"0.740","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:36.586Z"}
{"url":"/sync","status":"200","response-time":"0.301","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:37.024Z"}
{"url":"/sync","status":"200","response-time":"0.392","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:39.971Z"}
{"url":"/sync","status":"200","response-time":"0.317","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:23:43.971Z"}
{"level":"info","message":"[!] server is stopping: type=configd, signal=SIGINT","timestamp":"2025-08-22T07:31:15.116Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:31:15.117Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:31:15.117Z"}
{"level":"info","message":"redis pool (config-redis) destroyed","timestamp":"2025-08-22T07:31:15.119Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T07:31:15.119Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T07:31:15.120Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T07:31:15.120Z"}
{"environment":"development","type":"configd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"configd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:31:17.829Z"}
{"layoutFileName":"local.cn.json5","level":"info","message":"Reloading layout...","timestamp":"2025-08-22T07:31:26.019Z"}
{"dump":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Layout reloaded successfully from json file.","timestamp":"2025-08-22T07:31:26.032Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"},"level":"info","message":"redis pool (config-redis) initializing ...","timestamp":"2025-08-22T07:31:26.033Z"}
{"level":"info","message":"redis pool (config-redis) initialized","timestamp":"2025-08-22T07:31:26.045Z"}
{"layoutVersion":7,"level":"info","message":"loadInstances getAllInstances success","timestamp":"2025-08-22T07:31:26.050Z"}
{"appId":"townd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10500,\"url\":\"http://DESKTOP-2FFOGVN:10500\",\"tcpServer\":{\"port\":10508,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:31:26.051Z"}
{"appId":"saild.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":11100,\"url\":\"http://DESKTOP-2FFOGVN:11100\",\"tcpServer\":{\"port\":11109,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:31:26.051Z"}
{"appId":"realmd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10900,\"url\":\"http://DESKTOP-2FFOGVN:10900\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:31:26.051Z"}
{"appId":"zonelbd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10600,\"url\":\"http://DESKTOP-2FFOGVN:10600\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:31:26.052Z"}
{"appId":"lobbyd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"socketServer\":{\"bindAddress\":\"0.0.0.0\",\"port\":10100},\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10200,\"url\":\"http://DESKTOP-2FFOGVN:10200\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:31:26.052Z"}
{"appId":"oceand.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10800,\"url\":\"http://DESKTOP-2FFOGVN:10800\",\"tcpServer\":{\"port\":10808,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:31:26.052Z"}
{"appId":"authd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"publicApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10701},\"privateApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10700,\"url\":\"http://DESKTOP-2FFOGVN:10700\"}}","worldId":""},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:31:26.052Z"}
{"value":5000,"level":"info","message":"got maxUsersPerWorld from redis","timestamp":"2025-08-22T07:31:26.053Z"}
{"path":"/fetch","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.059Z"}
{"path":"/getAllInstances","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.081Z"}
{"path":"/getMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.097Z"}
{"path":"/registerInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.107Z"}
{"path":"/reload","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.123Z"}
{"path":"/setMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.135Z"}
{"path":"/sync","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.149Z"}
{"path":"/unregisterInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:26.160Z"}
{"bindAddress":"0.0.0.0","port":10001,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:31:26.170Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"lobbyd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:31:40.831Z"}
{"url":"/registerInstance","status":"200","response-time":"97.992","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:40.834Z"}
{"body":{"serverType":"authd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:31:40.911Z"}
{"url":"/registerInstance","status":"200","response-time":"1.409","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:40.912Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"townd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:31:43.451Z"}
{"url":"/registerInstance","status":"200","response-time":"0.779","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:43.452Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"oceand","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:31:44.989Z"}
{"url":"/registerInstance","status":"200","response-time":"0.640","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:44.990Z"}
{"url":"/sync","status":"200","response-time":"0.722","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:47.445Z"}
{"url":"/sync","status":"200","response-time":"0.336","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:49.514Z"}
{"url":"/sync","status":"200","response-time":"0.363","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:49.833Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"saild","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:31:51.036Z"}
{"url":"/registerInstance","status":"200","response-time":"1.207","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:51.037Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"realmd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:31:55.001Z"}
{"url":"/registerInstance","status":"200","response-time":"0.785","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:55.001Z"}
{"url":"/sync","status":"200","response-time":"0.566","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:56.104Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"zonelbd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:31:57.855Z"}
{"url":"/registerInstance","status":"200","response-time":"1.139","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:57.856Z"}
{"url":"/sync","status":"200","response-time":"0.332","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:58.234Z"}
{"url":"/sync","status":"200","response-time":"0.379","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:31:59.999Z"}
{"url":"/sync","status":"200","response-time":"0.906","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:32:03.618Z"}
{"level":"info","message":"[!] server is stopping: type=configd, signal=SIGINT","timestamp":"2025-08-22T07:33:49.315Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:33:49.315Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:33:49.316Z"}
{"level":"info","message":"redis pool (config-redis) destroyed","timestamp":"2025-08-22T07:33:49.318Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T07:33:49.318Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T07:33:49.318Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T07:33:49.319Z"}
{"environment":"development","type":"configd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"configd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:33:52.774Z"}
{"layoutFileName":"local.cn.json5","level":"info","message":"Reloading layout...","timestamp":"2025-08-22T07:34:00.498Z"}
{"dump":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Layout reloaded successfully from json file.","timestamp":"2025-08-22T07:34:00.534Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"},"level":"info","message":"redis pool (config-redis) initializing ...","timestamp":"2025-08-22T07:34:00.536Z"}
{"level":"info","message":"redis pool (config-redis) initialized","timestamp":"2025-08-22T07:34:00.549Z"}
{"layoutVersion":7,"level":"info","message":"loadInstances getAllInstances success","timestamp":"2025-08-22T07:34:00.555Z"}
{"appId":"townd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10500,\"url\":\"http://DESKTOP-2FFOGVN:10500\",\"tcpServer\":{\"port\":10508,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:34:00.556Z"}
{"appId":"saild.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":11100,\"url\":\"http://DESKTOP-2FFOGVN:11100\",\"tcpServer\":{\"port\":11109,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:34:00.556Z"}
{"appId":"realmd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10900,\"url\":\"http://DESKTOP-2FFOGVN:10900\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:34:00.557Z"}
{"appId":"zonelbd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10600,\"url\":\"http://DESKTOP-2FFOGVN:10600\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:34:00.557Z"}
{"appId":"lobbyd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"socketServer\":{\"bindAddress\":\"0.0.0.0\",\"port\":10100},\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10200,\"url\":\"http://DESKTOP-2FFOGVN:10200\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:34:00.557Z"}
{"appId":"oceand.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10800,\"url\":\"http://DESKTOP-2FFOGVN:10800\",\"tcpServer\":{\"port\":10808,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:34:00.557Z"}
{"appId":"authd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"publicApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10701},\"privateApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10700,\"url\":\"http://DESKTOP-2FFOGVN:10700\"}}","worldId":""},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T07:34:00.557Z"}
{"value":5000,"level":"info","message":"got maxUsersPerWorld from redis","timestamp":"2025-08-22T07:34:00.558Z"}
{"path":"/fetch","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.565Z"}
{"path":"/getAllInstances","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.589Z"}
{"path":"/getMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.606Z"}
{"path":"/registerInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.616Z"}
{"path":"/reload","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.629Z"}
{"path":"/setMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.639Z"}
{"path":"/sync","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.653Z"}
{"path":"/unregisterInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:00.665Z"}
{"bindAddress":"0.0.0.0","port":10001,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:34:00.675Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"lobbyd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:34:13.973Z"}
{"url":"/registerInstance","status":"200","response-time":"104.111","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:13.975Z"}
{"body":{"serverType":"authd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:34:14.070Z"}
{"url":"/registerInstance","status":"200","response-time":"1.014","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:14.070Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"saild","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:34:18.301Z"}
{"url":"/registerInstance","status":"200","response-time":"1.204","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:18.302Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"oceand","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:34:19.824Z"}
{"url":"/registerInstance","status":"200","response-time":"0.795","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:19.825Z"}
{"url":"/sync","status":"200","response-time":"0.723","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:20.953Z"}
{"url":"/sync","status":"200","response-time":"0.570","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:24.124Z"}
{"url":"/sync","status":"200","response-time":"0.321","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:24.516Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"townd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:34:25.513Z"}
{"url":"/registerInstance","status":"200","response-time":"0.763","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:25.514Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"realmd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:34:29.628Z"}
{"url":"/registerInstance","status":"200","response-time":"0.832","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:29.629Z"}
{"url":"/sync","status":"200","response-time":"0.691","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:29.926Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"zonelbd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T07:34:31.063Z"}
{"url":"/registerInstance","status":"200","response-time":"0.635","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:31.064Z"}
{"url":"/sync","status":"200","response-time":"0.366","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:31.355Z"}
{"url":"/sync","status":"200","response-time":"0.351","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:34.122Z"}
{"url":"/sync","status":"200","response-time":"0.290","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T07:34:37.280Z"}
