"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stop = exports.start = exports.stopServer = exports.tcpServer = exports.SailService = void 0;
const body_parser_1 = __importDefault(require("body-parser"));
const express_1 = __importDefault(require("express"));
const http_1 = __importDefault(require("http"));
const morgan_1 = __importDefault(require("morgan"));
const path_1 = __importDefault(require("path"));
const typedi_1 = __importStar(require("typedi"));
const dirAsApi = __importStar(require("../motiflib/directoryAsApi"));
const expressError = __importStar(require("../motiflib/expressError"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const slackNotifier_1 = require("../motiflib/slackNotifier");
const tcp = __importStar(require("../tcplib"));
const connPool_1 = require("../redislib/connPool");
const pubsub_1 = __importDefault(require("../redislib/pubsub"));
const pool_1 = require("../mysqllib/pool");
const saildPubsub = __importStar(require("./sailPubsub"));
const offlineSailingBotBehaviorCommonNode_1 = require("./offlineSailingBotBehaviorCommonNode");
const offlineSailingManager_1 = require("./offlineSailingManager");
const cms_1 = __importStar(require("../cms"));
const stoppable_1 = __importDefault(require("stoppable"));
const Sentry = __importStar(require("@sentry/node"));
require("express-async-errors");
const cmsKeyGroup_1 = require("../motiflib/model/cmsKeyGroup");
// -------------------------------------------------------------------------------------------------
// undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('uncaught Exception', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err) => {
    Sentry.captureException(err);
    mlog_1.default.error('unhandled Rejection', {
        msg: err.message,
        stack: err.stack,
    });
    // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});
//////
// -------------------------------------------------------------------------------------------------
// Api interfaces.
// -------------------------------------------------------------------------------------------------
let SailService = class SailService {
    constructor() {
        this.bMaintenance = false;
        this.lastMaintenanceTick = 0;
    }
    async init() {
        // Init mysql connection pool.
        this.worldDbConnPool = typedi_1.default.of('world').get(pool_1.DBConnPool);
        await this.worldDbConnPool.init(mconf_1.default.mysqlWorldDb);
        // Init user cache redis pool.
        this.userCacheRedis = typedi_1.default.of('user-cache-redis').get(connPool_1.MRedisConnPool);
        await this.userCacheRedis.init('user-cache-redis', mconf_1.default.userCacheRedis);
        // Init sail redis pool.
        this.sailRedis = typedi_1.default.of('sail-redis').get(connPool_1.MRedisConnPool);
        await this.sailRedis.init('sail-redis', mconf_1.default.sailRedis);
        this.monitorRedis = typedi_1.default.of('monitor-redis').get(connPool_1.MRedisConnPool);
        await this.monitorRedis.init('monitor-redis', mconf_1.default.monitorRedis);
        // Init redis pubsub.
        this.authPubsub = typedi_1.default.of('pubsub-auth').get(pubsub_1.default);
        this.authPubsub.init(mconf_1.default.authPubsubRedis);
        saildPubsub.init();
        this.pushLocalizeLookupTable = new cmsKeyGroup_1.PushLocalizeLookupTable(cms_1.default);
    }
    async destroy() {
        await this.authPubsub.quit();
        await this.worldDbConnPool.destroy();
        await this.userCacheRedis.destroy();
        await this.sailRedis.destroy();
        await this.monitorRedis.destroy();
    }
};
SailService = __decorate([
    (0, typedi_1.Service)()
], SailService);
exports.SailService = SailService;
// -------------------------------------------------------------------------------------------------
// Module variables.
// -------------------------------------------------------------------------------------------------
// Main express app.
const app = (0, express_1.default)();
app.disable('x-powered-by');
app.disable('etag');
app.disable('content-type');
const server = (0, stoppable_1.default)(http_1.default.createServer(app));
server.keepAliveTimeout = 0;
exports.tcpServer = tcp.server();
exports.tcpServer.routeEx(__dirname, './serverPacketHandler');
tcp.logger.setMoudle(mlog_1.default); // Setup tcp log writer
let stopping = false;
let offSailStartDelayTimeout = null;
// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------
morgan_1.default.token('mcode', (_req, res) => res.mcode || 0);
function sailReqLog(tokens, req, res) {
    if (req.url === '/health') {
        return;
    }
    mlog_1.default.info('saild-req', {
        url: tokens['url'](req, res),
        status: tokens['status'](req, res),
        'response-time': tokens['response-time'](req, res),
        mcode: tokens['mcode'](req, res),
    });
    return null;
}
async function closeServer() {
    return new Promise((resolve, reject) => {
        server.stop((err) => {
            if (err)
                return reject(err);
            resolve(null);
        });
    });
}
async function stopServer() {
    try {
        mlog_1.default.info('stopping server ...');
        exports.tcpServer.dispose();
        if (offSailStartDelayTimeout) {
            clearTimeout(offSailStartDelayTimeout);
            offSailStartDelayTimeout = null;
        }
        await closeServer();
        const offlineSailingManager = typedi_1.default.get(offlineSailingManager_1.OfflineSailingManager);
        offlineSailingManager.stopTick();
        const app = typedi_1.default.get(SailService);
        await app.destroy();
        mlog_1.default.info('server stopped');
        process.exitCode = 0;
    }
    catch (error) {
        mlog_1.default.error('graceful shutdown failed', { error: error.message });
        process.exit(1);
    }
}
exports.stopServer = stopServer;
// -------------------------------------------------------------------------------------------------
// Public functions.
// -------------------------------------------------------------------------------------------------
async function start() {
    try {
        await mhttp_1.default.configd.registerInstance(process.env.WORLD_ID ? process.env.WORLD_ID : mconf_1.default.instance.worldId, mconf_1.default.appInstanceId, mconf_1.default.hostname);
        mutil.initSentry();
        // Init http clients.
        mhttp_1.default.init();
        // Init cms.
        (0, cms_1.load)();
        const appService = typedi_1.default.get(SailService);
        await appService.init();
        const bindAddress = mconf_1.default.apiService.bindAddress;
        const port = mconf_1.default.apiService.port;
        app.use((0, morgan_1.default)(sailReqLog));
        app.use(body_parser_1.default.json());
        mutil.registerHealthCheck(app);
        mutil.registerGarbageCollector(app);
        await dirAsApi.register(app, path_1.default.join(__dirname, 'api'));
        app.use(expressError.middleware);
        const onDisconnected = (segment) => {
            const disconnectedServerURL = segment.get('url');
            const offlineSailingManager = typedi_1.default.get(offlineSailingManager_1.OfflineSailingManager);
            const jobs = offlineSailingManager.getAllJobs();
            jobs.forEach((job) => {
                if (job.getBot().conn.getLobbydUrl() === disconnectedServerURL) {
                    // 접속종료된 로비서버와 연결된 job을 종료한다
                    job.close();
                    mlog_1.default.warn('offSailBot was destroyed by disconnected lobbyd', {
                        userId: job.userId,
                    });
                }
            });
        };
        const tcpconf = mconf_1.default.apiService.tcpServer;
        exports.tcpServer.start(tcpconf.port, tcpconf.ip, onDisconnected);
        //register behaviorTree nodes
        (0, offlineSailingBotBehaviorCommonNode_1.registerBehaviorNodes)();
        // Init user tick.
        const offSailStartDelay = mutil.randIntInc(1, 30000); // 최대 30초 후에 시작
        offSailStartDelayTimeout = setTimeout(async () => {
            // 서버 점검 여부 체크
            Promise.resolve()
                .then(() => {
                return mhttp_1.default.platformApi
                    .isWorldInMaintenance(mconf_1.default.worldId)
                    .then((ret) => {
                    mlog_1.default.info('init bMaintenance state ', {
                        new: ret,
                    });
                    appService.bMaintenance = ret;
                })
                    .catch((error) => {
                    mlog_1.default.error('failed to check maintenance', {
                        error: error.message,
                        extra: error.extra,
                    });
                });
            })
                .then(() => {
                if (!stopping) {
                    const offlineSailingManager = typedi_1.default.get(offlineSailingManager_1.OfflineSailingManager);
                    offlineSailingManager.startTick();
                }
            });
        }, offSailStartDelay);
        server.listen(port, bindAddress, () => {
            mlog_1.default.info('start listening ...', { bindAddress, port });
        });
        // config final sync
        const beforeVer = mconf_1.default.layoutVersion;
        await mhttp_1.default.configd.sync(beforeVer, isStopping, stop).then(() => {
            if (beforeVer < mconf_1.default.layoutVersion) {
                // do something
            }
        });
    }
    catch (error) {
        mlog_1.default.error('failed to start', { error: error.message, extra: error.extra });
        mlog_1.default.error(error.stack);
        const slackNotifier = await (0, slackNotifier_1.CreateSlackNotifier)(mconf_1.default.slackNotify);
        await slackNotifier.notify({ username: process.name, text: error.message });
        process.exit(1);
    }
}
exports.start = start;
function isStopping() {
    return stopping;
}
async function stop() {
    if (stopping) {
        return;
    }
    stopping = true;
    await stopServer();
}
exports.stop = stop;
//# sourceMappingURL=server.js.map