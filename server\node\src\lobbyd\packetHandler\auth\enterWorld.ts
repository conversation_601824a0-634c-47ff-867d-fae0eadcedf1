// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _, { result } from 'lodash';
import { Container } from 'typedi';

import mhttp from '../../../motiflib/mhttp';
import mconf from '../../../motiflib/mconf';
import * as mutil from '../../../motiflib/mutil';
import puUserLoad from '../../../mysqllib/sp/puUserLoad';
import tuEnterWorld from '../../../mysqllib/txn/tuEnterWorld';
import * as UserConnection from '../../userConnection';
import * as offlineSailingBotLogicInLobby from '../../sailing/offlineSailingBotLogicInLobby';
import { LobbyService } from '../../server';
import { TownManager } from '../../townManager';
import { UserManager } from '../../userManager';
import { Sync, All } from '../../type/sync';
import { User, KarmaChange, LoginInfo } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { EnergyChange } from '../../userEnergy';
import { KICK_REASON, DEV_APP_VERSION } from '../../../motiflib/const';
import * as cmsEx from '../../../cms/ex';
import { UserReputationChange } from '../../userReputation';
import mlog from '../../../motiflib/mlog';
import { Attendance } from '../../userAttendance';
import {
  getDateOfSpecificLocalHourOfToday,
  GetElapssedDaysFromCmsDateStr,
  SECONDS_PER_DAY,
  getUtcOfLastEventExchangeTime,
  GetNextContentResetTimeByAddDays,
  isContentsTimeUtcInHours,
  getLocalDailySessionId,
} from '../../../formula';
import tuUpdateAttendances from '../../../mysqllib/txn/tuUpdateAttendances';
import tuEnterWorldPostProcess from '../../../mysqllib/txn/tuEnterWorldPostProcess';
import { Location } from '../../../motiflib/model/ocean';
import cms from '../../../cms';
import {
  FRIEND_NOTIFICATION_TYPE,
  GuildData,
  GUILD_MEMBER_GRADE,
  OfflineBattleHistories,
} from '../../../motiflib/model/lobby';
import { getGLogCoordinate } from '../../../cms/oceanCoordinate';
import { EnterWorldResult } from '../../../motiflib/mhttp/authApiClient';
import { GAME_STATE, GsUtil } from '../../../motiflib/model/lobby/gameState';
import { BuffSync, WorldBuffUtil } from '../../userBuffs';
import { ClientPacketHandler } from '../index';
import { WeeklyEventChange } from '../../userAchievement';
import { getUserLightInfos, UserLightInfo } from '../../../motiflib/userCacheRedisHelper';
import puUserUpdateGuildId from '../../../mysqllib/sp/puUserUpdateGuildId';
import { onGuildPublish } from '../../guildPubsub';
import { Shield } from '../../userShield';
import { ExploreQuickModeChange, ExploreTicketChange } from '../../userExplore';
import { EventPageDesc, EventPageType } from '../../../cms/eventPageDesc';
import { GuildUtil } from '../../guildUtil';
import { PendingDirectMail } from '../../../motiflib/model/town';
import { MileageExpirationMailCmsId } from '../../../cms/mailDesc';
import puDirectMailPendingsDelete from '../../../mysqllib/sp/puDirectMailPendingsDelete';
import { FRIEND_STATE } from '../../userFriends';
import { FriendUtil } from '../../friendUtil';
import { WaypointSupplyTicketChange } from '../../userSailWaypoints';
import { GLOG_LOGIN_OUT_SAVE_TYPE } from '../../const';
import { ChatTranslationChange } from '../../userChatTranslationCount';
import { RANKING_CMS_ID } from '../../../cms/rankingDesc';
import tuUpdateNewOrRevivalUserBenefit from '../../../mysqllib/txn/tuUpdateNewOrRevivalUserBenefit';
import { HotSpotProduct, OpenDurationProduct, UNBUYABLE_REASON } from '../../userCashShop';
import { buildSyncDataOfBaseElection } from '../../userNation';
import { GuildBuffDesc } from '../../../cms/guildBuffDesc';
import { VillageManager } from '../../villageManager';
import { ShipSlotChange } from '../../ship';
import { CostumeShipSlotChange } from '../../userFleets';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { EventRankingUtil } from '../../userEventRanking';
import { BuilderMailCreateParams, MailCreatingParams } from '../../../motiflib/mailBuilder';
import puUserLoad2 from '../../../mysqllib/sp/puUserLoad2';
import { SdoGLogs, SdoGLogEvents } from '../../../motiflib/sdoGLogs';
import { FirstFleetIndex } from '../../../cms/ex';
import { Survey } from '../../survey/survey';

// ----------------------------------------------------------------------------
// 월드 진입 처리.
// ----------------------------------------------------------------------------

const rsn = 'enter_world';
const add_rsn = null;

interface RequestBody {
  isDevLogin?: boolean;
  sessionToken: string; // 에디터인 경우 pubId
  lang: string;
  reconnect: number;
  enterWorldToken: string;
  deviceType: string;
  isTestBot?: boolean; // 더미 테스트봇 여부

  // for glog, billing, ...
  // https://developer.line.games/pages/viewpage.action?pageId=35849397 참고
  osv: string; // OS 버전 값
  dm: string; // 기기 값. 기기 모델 값.
  deviceLang: string; // language-culture 값으로 디바이스 언어값
  lineLang: string; // 라인에서 사용하는 ko_KR 와 같은 형식의 언어지역값
  v: string; // 클라이언트 마켓 바이너리 버전, AppVersion
  sk: string; // 스토어 타입
  country_ip: string; // IP 기반 국가 코드
  os_modulation: boolean; // OS 변조 유무. 변조일 경우 True, 아닌 경우 False
  emulator: boolean; // 에뮬레이터 분류. 에뮬레이터일 경우 True, 아닌 경우 False
  loading_time: number; // 게임 실행 시간부터 해당 로그인까지의 시간 (millisecond)
  loginPlatform: string; // ex) 구글, 페이스북
  adjust_id: string; // adjust SDK에서 전송하는 adid 값
  gps_adid: string; // OS = Android 인 경우 구글 ADID 값
  idfa: string; // OS = iOS 인 경우 애플 IDFA 값
  idfv: string; //OS = iOS 인 경우 애플 IDFV 값
  udid: string; // OS = Android, Andorid 고유 식별자

  // for glog, billing, ...
  // steam 인 경우
  steamAppId: number;
  steamUserId: string;

  // 루아에서 만들어지는 json 문자열 인자. (바이너리 패치 없이 enter world 관련 수정하는 용도)
  lparam: string;

  // 중국 전용
  channel?: string;
  subChannel?: string;
}

interface EnterWorldLuaParam {
  vertxt: string; // LUUwoGameInstance:GetVersionText()
}

interface ResponseBody extends BuffSync {
  pingInterval?: number;
  serverTimeUtc?: number;
  serverTimeZoneOffsetMin?: number;
  reconnect?: number;
  sailId?: number;
  offlineBattleHistories?: OfflineBattleHistories;
  kickReason?: KICK_REASON;
  worldId?: string;
  bIsNewUser?: boolean;
  clientVolanteUrl?: any;
  appGuardId?: string; // 앱가드 서버 인증 사용시, android/ios 클라 전용.
}

// ----------------------------------------------------------------------------
export class Cph_Auth_EnterWorld implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;

    const {
      isDevLogin,
      sessionToken,
      lang,
      reconnect,
      enterWorldToken,
      deviceType,
      isTestBot,
      lparam,
    } = body;

    user.ensureConnState(CONNECTION_STATE.KEYS_EXCHANGED);

    // 빌드 타이밍에 의해 없을수도 있으니 잠시 이렇게 가드 해둠.
    if (lparam) {
      const luaParam: EnterWorldLuaParam = JSON.parse(lparam);
      user.versionText = luaParam.vertxt;
    }

    return _login(
      user,
      packet,
      isDevLogin,
      reconnect,
      sessionToken,
      enterWorldToken,
      lang,
      deviceType,
      isTestBot,
      body
    );
  }
}

function _login(
  user: User,
  packet: CPacket,
  isDevLogin: boolean,
  reconnect: number,
  sessionToken: string,
  enterWorldToken,
  lang: string,
  deviceType: string,
  isTestBot: boolean,
  body: RequestBody
): Promise<any> {
  let ewr: EnterWorldResult;
  return mhttp.authd
    .enterWorld(isDevLogin, sessionToken, mconf.worldId, enterWorldToken, reconnect)
    .then((resp) => {
      ewr = resp;
      if (ewr.kickReason && ewr.kickReason === KICK_REASON.WORLD_STATE_FULL) {
        // 월드 포화
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          kickReason: KICK_REASON.WORLD_STATE_FULL,
          worldId: mconf.worldId,
          sync: {},
        });
      } else if (ewr.kickReason) {
        // 재접속인데 다른 월드로 접속 차단
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          kickReason: ewr.kickReason,
          sync: {},
        });
      }
      if (!ewr.bValidEnterWorldToken) {
        // 중복 로그인 또는 토큰 만료
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          kickReason: KICK_REASON.INVALID_ENTER_WORLD_TOKEN,
          sync: {},
        });
      }
      return Promise.resolve()
        .then(() => {
          // check if bot user is logged-in to this lobbyd
          const userManager = Container.get(UserManager);
          const prevUser = userManager.getUserByUserId(ewr.userId);
          if (prevUser) {
            userManager.removeLoggedInUser(ewr.userId);
            const isBot = prevUser.isOfflineSailingBot;
            mlog.error('duplicate user (_login)', {
              userId: user.userId,
              isBot,
              connId: user.getConnId(),
            });
            prevUser.disconnect(UserConnection.DisconnectReason.EnterWorldKick);
            return prevUser.waitForLogout();
          }

          return null;
        })
        .then(() => {
          user.accountId = ewr.accountId;
          user.pubId = ewr.pubId;
          user.accessLevel = ewr.accessLevel;
          user.revision = ewr.revision;
          user.patchRevision = ewr.patchRevision;
          user.countryCreated = ewr.countryCreated;

          if (isTestBot && isDevLogin) {
            user.setIsTestBot(isTestBot);
          }
          return _loginImpl(user, packet, ewr.userId, ewr.pubId, reconnect, lang, deviceType, body);
        });
    });
}

function _loginImpl(
  user: User,
  packet: CPacket,
  userId: number,
  pubId: string,
  reconnect: number,
  lang: string,
  deviceType: string,
  body: RequestBody
): Promise<any> {
  // for glog, billing
  const {
    isDevLogin,
    osv,
    dm,
    deviceLang,
    lineLang,
    v,
    sk,
    country_ip,
    os_modulation,
    emulator,
    loading_time,
    loginPlatform,
    adjust_id,
    gps_adid,
    idfa,
    idfv,
    udid,
    steamAppId,
    steamUserId,

    channel,
    subChannel,
  } = body;

  const townManager = Container.get(TownManager);
  const {
    nationManager,
    nationIntimacy,
    userDbConnPoolMgr,
    userCacheRedis,
    userRedis,
    sailRedis,
    guildRedis,
    rankingRedis,
    townRedis,
  } = Container.get(LobbyService);

  let bIsNewUser = false;
  // cms.Const.UserComBackDay로 체크한 복귀유저 여부
  // 복귀 유저 출석판은 EventPageDesc.comeBackDay로 복귀 여부 체크하기 때문에 제외
  let bIsReturnUser = false;

  // for tuEnterWorldPostProcess
  let energyChange: EnergyChange;
  let exploreTicketChange: ExploreTicketChange;
  let exploreQuickModeChange: ExploreQuickModeChange;
  let karmaChange: KarmaChange;
  let eventPageCmsIdsToDelete: number[];
  let weeklyEventChanges: WeeklyEventChange[];
  let passEventEventPageCmsIdsToDelete: number[];
  let attendanceEventPageCmsIdsToDelete: number[];
  let hotTimeMails: MailCreatingParams[] = [];
  let newLastReceiveHotTimeUtc: number;
  let newLastGameState;
  const shieldChanges: Shield[] = [];
  const reputationChanges: UserReputationChange[] = [];
  const expiredEventPageCmsIdsOfEventShop: number[] = []; // 추후 삭제 필요
  let notifiedMileageMonth;
  let gameEventPageCmsIdsToDelete: number[];
  let waypointSupplyTicketChange: WaypointSupplyTicketChange;
  let chatTranslationChange: ChatTranslationChange;
  let expiredQuestGroupIds: number[];
  let hotSpotProductCmsIdsToDelete: number[];
  let resetPopupCountHotSpotProducts: HotSpotProduct[];
  const restrictedProductsToDelete: number[] = [];
  const openDurationProducts: OpenDurationProduct[] = [];
  let expiredEquipmentIds: number[] = [];
  const shipSlotChanges: ShipSlotChange[] = [];
  const costumeShipSlotChanges: CostumeShipSlotChange[] = [];

  const curTimeUtc = mutil.curTimeUtc();
  const newEnterWorldToken = mutil.generateEnterWorldToken(user.accountId);
  const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
  const curDate = new Date(curTimeUtc * 1000);

  let sailId;
  let offlineBattleHistories: OfflineBattleHistories = {};
  let userGuildSync: Sync;
  let userLightInfos: { [userId: number]: UserLightInfo };
  let guildData: GuildData;
  return tuEnterWorld(userDbPool, userId, pubId, lang, curTimeUtc, country_ip, curTimeUtc)
    .then((ret) => {
      bIsNewUser = ret.bIsNewUser;

      // 신규 유저일 경우 userCacheRedis 에 default 정보 적기.
      if (bIsNewUser) {
        userCacheRedis['setDefault'](userId, pubId).catch((err) => {
          mlog.error('userCacheRedis setDefault is failed.', {
            userId,
            err: err.message,
          });
        });
      }

      const attendances: Attendance[] = [];
      if (ret.lastLoginTimeUtc) {
        bIsReturnUser =
          curTimeUtc - ret.lastLoginTimeUtc >= cms.Const.UserComeBackDay.value * SECONDS_PER_DAY;

        // 복귀 유저 출석부 지급.
        const revivalUserAttendance = cmsEx.getRevivalUserAttendance();
        for (const elem of revivalUserAttendance) {
          if (curTimeUtc - ret.lastLoginTimeUtc >= elem.comeBackDay * SECONDS_PER_DAY) {
            attendances.push({
              eventPageCmsId: elem.id,
              accum: 0,
              consecutive: 0,
              lastAttendanceTimeUtc: null,
              lastRewardedConsecutiveAttendanceCmsId: null,
              startTimeUtc: curTimeUtc,
              endTimeUtc: GetNextContentResetTimeByAddDays(
                curTimeUtc,
                elem.activeDay,
                cms.ContentsResetHour.EventPageReset.hour
              ),
            });
          }
        }
      }
      // 신규 유저 출석부 지급
      if (bIsNewUser) {
        const newUserAttendance = cmsEx.getNewUserAttendance();
        const curDate = new Date(curTimeUtc * 1000);
        for (const elem of newUserAttendance) {
          if (curDate >= mutil.newDateByCmsDateStr(elem.startDate)) {
            attendances.push({
              eventPageCmsId: elem.id,
              accum: 0,
              consecutive: 0,
              lastAttendanceTimeUtc: null,
              lastRewardedConsecutiveAttendanceCmsId: null,
              startTimeUtc: curTimeUtc,
              endTimeUtc: null,
            });
          }
        }
      }

      return tuUpdateNewOrRevivalUserBenefit(userDbPool, userId, attendances);
    })
    .then(() => {
      return userCacheRedis['updateUserHeartBeatWhenEnterWorld'](
        user.accountId,
        curTimeUtc,
        userId,
        mconf.appId
      );
    })
    .then(async () => {
      // puUserLoad 호출
      const userLoadResult = await puUserLoad(userDbPool, userId, curTimeUtc);

      // puUserLoad2 호출
      const userLoad2Result = await puUserLoad2(userDbPool, userId, curTimeUtc);

      // 두 결과를 병합
      const mergedResult: LoginInfo = {
        ...userLoadResult,
        ...userLoad2Result,
      };

      user.login(
        mergedResult,
        curTimeUtc,
        deviceType,
        sk,
        country_ip,
        isDevLogin && !v ? DEV_APP_VERSION : v,
        lineLang,
        {
          osv,
          deviceLang,
          adjsutId: adjust_id,
          gpsAdid: gps_adid,
          idfa,
          idfv,
          udid,
          steamAppId,
          steamUserId: steamUserId ? BigInt(steamUserId) : null,

          channel,
          subChannel,
        }
      );

      // shield 시간 충전
      _.forOwn(cms.Shield, (elem) => {
        const change = user.userShield.buildShieldChange(
          elem.id,
          curTimeUtc,
          user.level,
          user.companyStat
        );
        if (_.isEqual(change, user.userShield.getShield(elem.id))) {
          return;
        }

        shieldChanges.push(change);
      });

      // enter world 후처리
      // 시간 흐름에 따른 energy, karma 변경 사항.
      if (bIsNewUser) {
        energyChange = {
          lastUpdateTimeUtc: curTimeUtc,
          energy: 0,
        };
      } else {
        energyChange = user.userEnergy.buildEnergyChange(curTimeUtc, user.level, user.level);
      }
      karmaChange = user.getKarmaChange(curTimeUtc);

      exploreTicketChange = user.userExplore.buildTicketChange(curTimeUtc);
      exploreQuickModeChange = user.userExplore.buildQuickModeChange(curTimeUtc);

      // hot time.
      const hotTimeCmses = cmsEx.getCurHotTimeBonus(curTimeUtc);
      if (hotTimeCmses) {
        const overHotTime = cms.Const.OverHotTime.value;

        for (const elem of hotTimeCmses) {
          // 시간 검사.
          if (!isContentsTimeUtcInHours(curTimeUtc, elem.startHour, elem.endHour, overHotTime)) {
            continue;
          }

          const startDate = getDateOfSpecificLocalHourOfToday(curTimeUtc, elem.startHour);
          const startTimeUtc = Math.floor(startDate.getTime() / 1000);

          const expireDate = getDateOfSpecificLocalHourOfToday(curTimeUtc, elem.endHour);
          let expireTimeUtc = Math.floor(expireDate.getTime() / 1000);

          // 이미 보상을 받은 경우 제외.
          if (
            user.lastReceiveHotTimeUtc >= startTimeUtc &&
            user.lastReceiveHotTimeUtc < expireTimeUtc + overHotTime
          ) {
            continue;
          }

          // 만료 시간 설정.
          // 남은 시간이 일정값보다 작을 경우 만료 시간을 늘려준다.
          const remainingSec = expireTimeUtc - curTimeUtc;
          if (remainingSec < overHotTime) {
            expireTimeUtc += overHotTime - remainingSec;
          }

          // 보상 결정
          let attachment;
          if (elem.rewardFixeds && elem.rewardFixeds.length > 0) {
            // levels 가 없고 rewardFixeds 이 있을 경우 rewardFixeds[0] 을 보상으로 첨부한다.
            // levels 의 배열 길이를 항상 rewardFixeds 의 배열 길이 -1 으로 하여 levels 범위안에 들지 않을 경우
            // rewardFixeds 배열의 마지막 보상을 첨부한다.
            attachment = cmsEx.convertRewardFixedToCustomAttachmentStr(
              elem.rewardFixeds[elem.rewardFixeds.length - 1],
              false,
              curTimeUtc
            );

            if (elem.levels && elem.levels.length > 0) {
              for (let i = elem.levels.length - 1; i >= 0; i--) {
                if (elem.levels[i] < user.level) {
                  break;
                }
                attachment = cmsEx.convertRewardFixedToCustomAttachmentStr(
                  elem.rewardFixeds[i],
                  false,
                  curTimeUtc
                );
              }
            }
          }

          hotTimeMails.push(
            new BuilderMailCreateParams(
              user.userMails.generateNewDirectMailId(),
              elem.mailId,
              curTimeUtc,
              expireTimeUtc,
              0,
              null,
              null,
              null,
              null,
              attachment
            ).getParam()
          );
        }
      }

      if (hotTimeMails && hotTimeMails.length > 0) {
        newLastReceiveHotTimeUtc = curTimeUtc;
      }

      // 마일리지 만료 안내
      const newPendingDirectMails: PendingDirectMail[] = [];
      const curDayOfMonth = curDate.getDate();

      // 만료 7일전 안내이기에 매달 20일에 발송한다.
      if (curDayOfMonth >= 20) {
        const expireMileageInThisMonth = user.userPoints.getExpireMileageInThisMonth(curTimeUtc);
        if (
          expireMileageInThisMonth &&
          expireMileageInThisMonth.value > 0 &&
          !expireMileageInThisMonth.bIsExpirationNotified
        ) {
          notifiedMileageMonth = expireMileageInThisMonth.month;
          const mileageMailCms = cms.Mail[MileageExpirationMailCmsId];
          let expireTimeUtc = null;
          let bShouldSetExpirationWhenReceiveAttachment = 0;
          if (mileageMailCms.mailKeepTime > 0) {
            expireTimeUtc = curTimeUtc + mileageMailCms.mailKeepTime;
          } else if (mileageMailCms.mailKeepTime === -1) {
            bShouldSetExpirationWhenReceiveAttachment = 1;
          }

          newPendingDirectMails.push(
            new BuilderMailCreateParams(
              null,
              mileageMailCms.id,
              Math.floor(
                new Date(
                  mutil.getLocalFullYear(curDate),
                  mutil.getLocalMonth(curDate),
                  20
                ).getTime() / 1000
              ),
              expireTimeUtc,
              bShouldSetExpirationWhenReceiveAttachment,
              null,
              null,
              null,
              expireMileageInThisMonth.value,
              null
            ).getPendingMailParam(user.userId)
          );
        }
      }

      // 선택할 수 있는 국가들 중 평판이 없는 국가가 있을 경우 추가해준다.
      // (기본값에서 20%까지 자연 상승해야 되기 때문에 null인 경우 기본값으로 생각하는 방법을 사용하기 힘듦)
      const selectableNations = cmsEx.getSelectableNations();

      for (const nationCms of selectableNations) {
        if (!user.userReputation.has(nationCms.id)) {
          reputationChanges.push({
            nationCmsId: nationCms.id,
            updateTimeUtc: curTimeUtc,
            reputation: user.userReputation.getNewValue(nationCms.id, curTimeUtc, 0),
          });
        }
      }

      // 계정 생성이 완료된 경우 gameState 값을 lastGameState 에 넣고 gameState 는 NONE 으로 변경
      const oldGameState = user.userState.getGameState();
      if (oldGameState > GAME_STATE.CREATE_ACCOUNT_MAX) {
        // db 상의 lastGameState 가 NONE이 아닌 경우 lastGameState 를 유지한다.
        if (user.userState.getRawLastGameState() === GAME_STATE.NONE) {
          newLastGameState = oldGameState;
        }

        // 모의전 로비 상태는 순단으로 인한 재접속시에만 유지한다
        // 만약 gameState 가 모의전 로비상태인 경우 정상 로그인이면 마을/해양으로 변경한다
        if (
          0 === reconnect &&
          (user.userState.isInOceanBattleLobby() || user.userState.isInTownBattleLobby())
        ) {
          if (user.userState.isInOceanBattleLobby()) {
            newLastGameState = GAME_STATE.IN_OCEAN;
          } else {
            newLastGameState = GAME_STATE.IN_TOWN;
          }

          mlog.info('converted battleLobby state', { newLastGameState });
        } else if (user.userState.isInOceanSalvage()) {
          // SalvageEnter 패킷 호출 시 oceanDoodadId가 필요한데 타이밍 이슈로
          // 생성이 안될수 있기 때문에 인양의 경우 재 접속 시 IN_OCEAN으로 설정한다.
          newLastGameState = GAME_STATE.IN_OCEAN;
        }
      }

      // EventPage류
      // 이벤트 미션중 시간이 지난 이벤트를 삭제
      eventPageCmsIdsToDelete = user.userAchievement.getExpiredTimeEventPage(curTimeUtc);
      // 신규 7일 임무가 있으면 시작 날짜 저장
      weeklyEventChanges = user.userAchievement.createWeeklyEvents(curTimeUtc);
      // 만료된 패스 이벤트 삭제
      passEventEventPageCmsIdsToDelete =
        user.userPassEvent.getEventPageCmsIdsToDeleteOnLogin(curTimeUtc);
      // 만료된 출석부 삭제
      attendanceEventPageCmsIdsToDelete = user.userAttendance.getExpiredAttendances(
        new Date(curTimeUtc * 1000)
      );
      // 만료된 복귀유저 출석부 삭제
      for (const attendance of user.userAttendance.getCombackAttendances()) {
        if (curTimeUtc >= attendance.endTimeUtc) {
          attendanceEventPageCmsIdsToDelete.push(attendance.eventPageCmsId);
        }
      }
      // 만료된 이벤트 게임 삭제
      gameEventPageCmsIdsToDelete = user.userEventGames.getExpiredGames(curTimeUtc);

      // 이벤트 상점 임시 코드
      // -> 서버 재연결 시 eventShopProducts가 초기화되는 이슈가 있어, 임시로 enter_world에서
      // EVENT_SHOP_GET_PRODUCTS 패킷 작업을 임시로 처리함 추후 수정 필요
      const eventPageCmses: EventPageDesc[] = cmsEx.getEventPagesForPageType(
        EventPageType.EVENT_SHOP
      );

      // 만료된 eventshop의 eventPageCmsIds
      if (eventPageCmses && eventPageCmses.length > 0) {
        for (const cms of eventPageCmses) {
          if (cmsEx.isFilteredByCountryCode(cms.localBitflag)) {
            continue;
          }

          const lastExchangeTime: number = getUtcOfLastEventExchangeTime(
            cms.endDate,
            cms.shopRemainHour
          );

          if (curTimeUtc > lastExchangeTime) {
            expiredEventPageCmsIdsOfEventShop.push(cms.id);
          }
        }
      }

      waypointSupplyTicketChange = user.userSailWaypoints.buildTicketChange(user, curTimeUtc);

      chatTranslationChange = user.userChatTranslationCount.replenishIf1DayPassed();

      expiredQuestGroupIds = user.questManager.getExpiredQuestGroupIds(curTimeUtc);

      hotSpotProductCmsIdsToDelete = user.userCashShop.getHotSpotProductToDelete(curTimeUtc);

      // 등장 횟수가 존재하는 핫스팟 상품은 db에서 삭제되지 않으므로
      // hotSpotProductCmsIdsToDelete이랑 resetPopupCountHotSpotProducts의 id 가 중복되지 않음
      resetPopupCountHotSpotProducts = Object.values(
        _.cloneDeep(user.userCashShop.getNeedResetPopupCountHotSpotProducts(curTimeUtc))
      );
      for (const product of resetPopupCountHotSpotProducts) {
        product.popupCount = 0;
        product.lastResetTimeUtc = curTimeUtc;
      }

      const isInBattle =
        (newLastGameState >= GAME_STATE.TOWN_BATTLE_MIN &&
          newLastGameState <= GAME_STATE.TOWN_BATTLE_MAX) ||
        (newLastGameState >= GAME_STATE.OCEAN_BATTLE_MIN &&
          newLastGameState <= GAME_STATE.OCEAN_BATTLE_MAX);

      const isInDuel =
        newLastGameState === GAME_STATE.IN_TOWN_DUEL ||
        newLastGameState === GAME_STATE.IN_TOWN_BATTLE_DUEL ||
        newLastGameState === GAME_STATE.IN_OCEAN_DUEL ||
        newLastGameState === GAME_STATE.IN_OCEAN_BATTLE_DUEL;

      if (!isInBattle && !isInDuel) {
        if (!GsUtil.isInOcean(newLastGameState)) {
          const expiredShipSlotItemIds =
            user.userInven.getExpiredEquippedShipSlotItemIds(curTimeUtc);
          for (const shipSlotId of expiredShipSlotItemIds) {
            const shipSlotItem = user.userInven.getShipSlotItem(shipSlotId);
            if (shipSlotItem.isEquippedCostumeShipSlot) {
              const shipSlotCms = cms.ShipSlot[shipSlotItem.shipSlotCmsId];
              costumeShipSlotChanges.push({
                slotSubType: shipSlotCms.slotSubType,
              });
            } else {
              shipSlotChanges.push({
                shipId: shipSlotItem.equippedShipId,
                slotIndex: shipSlotItem.equippedShipSlotIdx,
                shipSlotItemId: 0,
                mateCmsId: null,
                isLocked: 0,
              });
            }
          }
        }

        expiredEquipmentIds = user.userMates.getExpiredEquippedMateEquipments(curTimeUtc);
      }

      if (bIsReturnUser) {
        // 복귀 유저 전용 상품
        const restrictedProducts = user.userCashShop.getRestrictedProducts();
        const returnUserCashShopCmses = cmsEx.getReturnUserCashShopCms();
        for (const cashShopCms of returnUserCashShopCmses) {
          if (
            user.userCashShop.isActiveProduct(cashShopCms.id, curDate, undefined, 1, [
              UNBUYABLE_REASON.SOLD_OUT,
              UNBUYABLE_REASON.PREVIOUS_ID,
            ]) === UNBUYABLE_REASON.BUYABLE
          ) {
            // 이전에 샀더라도 복귀유저면 다시 구매 가능
            if (restrictedProducts[cashShopCms.id]) {
              restrictedProductsToDelete.push(cashShopCms.id);
            }

            openDurationProducts.push({
              cmsId: cashShopCms.id,
              startTimeUtc: curTimeUtc,
              endTimeUtc: curTimeUtc + cashShopCms.returnUserSaleDurationDays * SECONDS_PER_DAY,
            });
          }
        }
      }

      return tuEnterWorldPostProcess(
        userDbPool,
        userId,
        energyChange,
        karmaChange,
        waypointSupplyTicketChange,
        eventPageCmsIdsToDelete,
        weeklyEventChanges,
        passEventEventPageCmsIdsToDelete,
        attendanceEventPageCmsIdsToDelete,
        hotTimeMails,
        newLastReceiveHotTimeUtc,
        reputationChanges,
        newLastGameState,
        shieldChanges,
        exploreTicketChange,
        exploreQuickModeChange,
        expiredEventPageCmsIdsOfEventShop,
        newPendingDirectMails,
        notifiedMileageMonth,
        gameEventPageCmsIdsToDelete,
        chatTranslationChange,
        expiredQuestGroupIds,
        hotSpotProductCmsIdsToDelete,
        resetPopupCountHotSpotProducts,
        restrictedProductsToDelete,
        openDurationProducts,
        expiredEquipmentIds,
        shipSlotChanges,
        costumeShipSlotChanges,
        curTimeUtc
      );
    })
    .then(() => {
      // u_ship_slots shipSlotItemId 컬럼 체크용 로그
      mlog.info('shipSlotChange enterWorld', { userId: user.userId, shipSlotChanges });

      user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn });
      user.applyKarma(karmaChange, null);
      user.userSailWaypoints.applyTicketChange(waypointSupplyTicketChange);

      if (newLastGameState !== undefined) {
        user.userState.onEnterWorld(newLastGameState);
      }
      user.userExplore.applyTicketChange(exploreTicketChange, {
        user,
        rsn,
        add_rsn,
        maxQuickModeCount: user.userExplore.getMaxTicketCount(user),
      });
      user.userExplore.applyQuickModeChange(exploreQuickModeChange, {
        user,
        rsn,
        add_rsn,
        maxQuickModeCount: user.userExplore.getMaxQuickModeCount(user),
      });

      if (eventPageCmsIdsToDelete && eventPageCmsIdsToDelete.length > 0) {
        user.userAchievement.deleteEventPage(eventPageCmsIdsToDelete);
      }

      if (weeklyEventChanges && weeklyEventChanges.length > 0) {
        for (const change of weeklyEventChanges) {
          user.userAchievement.setWeeklyEvent(
            change.eventPageCmsId,
            change.weeklyEventStartTimeUtc
          );
        }
      }

      if (expiredEventPageCmsIdsOfEventShop && expiredEventPageCmsIdsOfEventShop.length > 0) {
        for (const cmsId of expiredEventPageCmsIdsOfEventShop) {
          user.userEventShop.deleteProductsByExpiredEventPage(cmsId);
        }
      }

      if (passEventEventPageCmsIdsToDelete && passEventEventPageCmsIdsToDelete.length > 0) {
        user.userPassEvent.deletePassEvents(passEventEventPageCmsIdsToDelete);
      }

      if (attendanceEventPageCmsIdsToDelete && attendanceEventPageCmsIdsToDelete.length > 0) {
        user.userAttendance.deleteAttendances(attendanceEventPageCmsIdsToDelete);
      }

      if (gameEventPageCmsIdsToDelete && gameEventPageCmsIdsToDelete.length > 0) {
        user.userEventGames.deleteEventGame(gameEventPageCmsIdsToDelete);
      }

      if (expiredQuestGroupIds && expiredQuestGroupIds.length > 0) {
        user.questManager.deleteQuestGroupIds(expiredQuestGroupIds);
      }

      if (hotSpotProductCmsIdsToDelete && hotSpotProductCmsIdsToDelete.length > 0) {
        user.userCashShop.deleteHotSpotProducts(hotSpotProductCmsIdsToDelete);
      }

      if (resetPopupCountHotSpotProducts && resetPopupCountHotSpotProducts.length > 0) {
        for (const product of resetPopupCountHotSpotProducts) {
          user.userCashShop.setHotSpotProduct(product);
        }
      }

      if (restrictedProductsToDelete && restrictedProductsToDelete.length > 0) {
        user.userCashShop.deleteRestrictedProducts(restrictedProductsToDelete);
      }

      if (expiredEquipmentIds && expiredEquipmentIds.length > 0) {
        for (const id of expiredEquipmentIds) {
          user.userMates.unequipMateEquipment(
            id,
            user.companyStat,
            user.userPassives,
            user.userFleets,
            user.userSailing,
            user.userTriggers,
            user.userBuffs,
            null,
            { user, rsn, add_rsn }
          );
        }
      }

      if (shipSlotChanges && shipSlotChanges.length > 0) {
        for (const change of shipSlotChanges) {
          const userShip = user.userFleets.getShip(change.shipId);
          userShip.applyEquipSlotItem(user, change, { user, rsn, add_rsn }, null);
        }
      }

      if (costumeShipSlotChanges && costumeShipSlotChanges.length > 0) {
        for (const change of costumeShipSlotChanges) {
          user.userFleets.applyCostumeShipSlotItem(user, change, { user, rsn, add_rsn }, null);
        }
      }

      if (openDurationProducts && openDurationProducts.length > 0) {
        for (const product of openDurationProducts) {
          user.userCashShop.setOpenDurationProduct(product);
        }
      }

      if (hotTimeMails && hotTimeMails.length > 0) {
        for (const mail of hotTimeMails) {
          user.userMails.addDirectMail(mail, { user });
        }
      }

      if (newLastReceiveHotTimeUtc) {
        user.lastReceiveHotTimeUtc = newLastReceiveHotTimeUtc;
      }

      for (const elem of shieldChanges) {
        user.userShield.applyShieldChange(elem, { user, rsn, add_rsn });
      }

      for (const change of reputationChanges) {
        user.userReputation.set(
          change.nationCmsId,
          {
            reputation: change.reputation,
            updateTimeUtc: change.updateTimeUtc,
          },
          { user, rsn, add_rsn }
        );
      }

      if (notifiedMileageMonth) {
        const curMonthMileage = user.userPoints.getExpireMileageInThisMonth(curTimeUtc);
        curMonthMileage.bIsExpirationNotified = true;
      }

      if (chatTranslationChange) {
        user.userChatTranslationCount.applyTranslationNub(chatTranslationChange);
      }

      user.userTitles.deleteUserTitles(curTimeUtc, null);

      // resolve pending mails.
      return user.userMails.resolvePendingDirectMails(false, user);
    })
    .then(() => {
      return user.userPoints.queryCash(user);
    })
    .then(() => {
      // write enterWorldToken to userCacheRedis
      return userCacheRedis['setEnterWorldToken'](user.accountId, newEnterWorldToken, curTimeUtc);
    })
    .then(() => {
      if (user.userState.isInOcean()) {
        return offlineSailingBotLogicInLobby.loadSailState(user, curTimeUtc);
      }
      return null;
    })
    .then((loadSailStateResult) => {
      // get sailId.
      if (user.userState.isInOcean()) {
        if (loadSailStateResult.sailId) {
          sailId = loadSailStateResult.sailId;
        }
      }
      return null;
    })
    .then(() => {
      // 인카운트 회피가능한 유저목록을 얻어오기.
      return userCacheRedis['loadAvoidableEncountUsers'](user.userId);
    })
    .then((avoidableEncountUsers) => {
      const users = avoidableEncountUsers.map((param) => JSON.parse(param));
      user.userEncount.setupAvoidableEncountUser(users);
      return null;
    })
    .then(() => {
      if (user.userGuild.guildId) {
        return guildRedis['getGuild'](user.userGuild.guildId).then((guild: string) => {
          if (!guild) {
            // redis에 없는 경우에는 rdb에서 제거해주자.
            mlog.error('there-is-no-guild-in-redis', {
              userId: user.userId,
              guildId: user.userGuild.guildId,
            });
            return puUserUpdateGuildId(userDbPool, user.userId, 0).then(() => {
              user.userGuild.guildId = 0;
            });
          }

          guildData = JSON.parse(guild);
          if (!guildData.members[user.userId]) {
            // redis에 member 없는 경우에는 rdb에서 제거해주자.
            mlog.error('there-is-no-guild-member-in-redis', {
              userId: user.userId,
              guildId: user.userGuild.guildId,
            });
            return puUserUpdateGuildId(userDbPool, user.userId, 0).then(() => {
              user.userGuild.guildId = 0;
              guildData = undefined;
            });
          }

          // 만약 혼자만 남았는데 길드장이 아닐경우 강제로 변경해준다.
          if (_.keys(guildData.members).length === 1) {
            if (guildData.members[user.userId].grade !== GUILD_MEMBER_GRADE.MASTER) {
              mlog.warn('change-gulid-master-by-force', {
                userId: user.userId,
                guildId: user.userGuild.guildId,
                grade: guildData.members[user.userId].grade,
              });

              guildData.members[user.userId].grade = GUILD_MEMBER_GRADE.MASTER;
              guildRedis['updateGuildMember'](
                user.userGuild.guildId,
                user.userId,
                JSON.stringify(guildData.members[user.userId])
              ).catch((e) => {
                mlog.error('[guild] updateGuildMember redis error', {
                  userId: this._userId,
                  error: e.message,
                  stack: mconf.isDev ? undefined : e.stack,
                });
              });
            }
          }

          const userIds = [];
          _.forOwn(guildData.members, (elem) => {
            userIds.push(elem.userId);
          });

          if (guildData.members[user.userId].grade == GUILD_MEMBER_GRADE.MASTER) {
            _.forOwn(guildData.applicants, (elem) => {
              userIds.push(elem.userId);
            });
          } else {
            const guildAccessPermissionCms =
              cms.GuildAccessPermission[guildData.members[user.userId].grade];
            if (guildAccessPermissionCms && guildAccessPermissionCms.manageMember) {
              _.forOwn(guildData.applicants, (elem) => {
                userIds.push(elem.userId);
              });
            }
          }

          const worldConfg = mconf.getWorldConfig();
          return getUserLightInfos(
            userIds,
            userCacheRedis,
            userRedis,
            guildRedis,
            townRedis,
            userDbConnPoolMgr,
            worldConfg.mysqlUserDb.shardFunction
          ).then((result: { [userId: number]: UserLightInfo } | null) => {
            userLightInfos = result;
            userLightInfos[user.userId].isOnline = 1;

            user.userGuild.setGuildAppearance({
              guildName: guildData.guild.guildName,
              grade: guildData.members[user.userId].grade,
              emblemImageCmsId: guildData.guild.emblemImageCmsId,
              emblemColorCmsId: guildData.guild.emblemColorCmsId,
              emblemBorderCmsId: guildData.guild.emblemBorderCmsId,
            });

            userGuildSync = {
              add: {
                userGuild: {
                  guild: {
                    members: {
                      [user.userId]: {
                        isOnline: true,
                      },
                    },
                  },
                },
              },
            };

            onGuildPublish(guildData, userLightInfos, [user.userId], userGuildSync);

            GuildUtil.changeMasterIfLongTermLogout(
              user,
              user.userGuild.guildId,
              guildData,
              userLightInfos
            );

            _.merge(
              userGuildSync,
              GuildUtil.buildGuildSyncDataAll(user, guildData, userLightInfos)
            );
          });
        });
      }
    })
    .then(() => {
      // 친구들에게 로그인 알림 통보.
      const friendIds = user.userFriends.getFriendIdsByState(FRIEND_STATE.ESTABLISHED);
      return FriendUtil.notifyOnlineFriends(
        user.userId,
        user.userName,
        friendIds,
        FRIEND_NOTIFICATION_TYPE.LOGIN,
        mutil.curTimeUtc()
      );
    })
    .then(() => {
      return userRedis['loadQuestDailyLimit'](user.userId);
    })
    .then((ret) => {
      if (!ret) {
        // 기본값으로 간주
        return null;
      }
      const expireTimeUtc = parseInt(ret[0], 10);
      const completedCounts: { [gruop: string]: number } = JSON.parse(ret[1]);
      if (
        !(Number.isInteger(expireTimeUtc) && completedCounts && typeof completedCounts === 'object')
      ) {
        mlog.error('unexpected quest daily limit data type!', {
          userId: user.userId,
          ret,
        });
        return null;
      }
      user.questManager.setDailyLimitCompleted({
        expireTimeUtc,
        counts: completedCounts,
      });
      return null;
    })
    .then(() => {
      return EventRankingUtil.initToEventRanking(user, curTimeUtc);
    })
    .then(() => {
      return user.userTown.initTownInvestmentAccumPoints(user, curTimeUtc, townRedis);
    })
    .then(() => {
      return sailRedis['getOfflineBattleHistories'](user.userId);
    })
    .then((obHistories) => {
      // 오프라인항해 중 전투보상 내역을 클라이언트에게 전달.(단순 정보 전달용)
      if (obHistories) {
        obHistories.forEach((param) => {
          const obHistory: OfflineBattleHistories = JSON.parse(param);
          if (obHistory.addedDucat && obHistory.addedDucat !== 0) {
            offlineBattleHistories.addedDucat =
              obHistory.addedDucat +
              (offlineBattleHistories.addedDucat ? offlineBattleHistories.addedDucat : 0);
          }

          if (obHistory.addedUserExp && obHistory.addedUserExp !== 0) {
            offlineBattleHistories.addedUserExp =
              obHistory.addedUserExp +
              (offlineBattleHistories.addedUserExp ? offlineBattleHistories.addedUserExp : 0);
          }

          if (obHistory.fameGain && obHistory.fameGain !== 0) {
            offlineBattleHistories.fameGain =
              obHistory.fameGain +
              (offlineBattleHistories.fameGain ? offlineBattleHistories.fameGain : 0);
          }

          // 중복된 항해사는 exp를 병합.
          if (obHistory.mates && obHistory.mates.length > 0) {
            obHistory.mates.forEach((mate) => {
              if (!offlineBattleHistories.mates) {
                offlineBattleHistories.mates = [];
              }
              const found = offlineBattleHistories.mates.findIndex(
                (m) => m.mateCmsId === mate.mateCmsId
              );
              if (found !== -1) {
                offlineBattleHistories.mates[found].addedExp += mate.addedExp;
              } else {
                offlineBattleHistories.mates.push({
                  mateCmsId: mate.mateCmsId,
                  addedExp: mate.addedExp,
                });
              }
            });
          }

          // 중복된 보급품,교역품은  병합.
          if (obHistory.rewards && obHistory.rewards.length > 0) {
            obHistory.rewards.forEach((reward) => {
              if (!offlineBattleHistories.rewards) {
                offlineBattleHistories.rewards = [];
              }
              const found = offlineBattleHistories.rewards.findIndex(
                (r) => r.type === reward.type && r.cmsId === reward.cmsId
              );
              if (found !== -1) {
                offlineBattleHistories.rewards[found].quantity += reward.quantity;
              } else {
                offlineBattleHistories.rewards.push({
                  type: reward.type,
                  cmsId: reward.cmsId,
                  quantity: reward.quantity,
                });
              }
            });
          }
        });
      }

      let location: Location;
      let sailState;
      if (sailId) {
        sailState = user.userSailing.getSailState();
        location = sailState.location;
      }

      // build sync.
      const sync: Sync = {
        add: _.merge<All, All, All, All, All>(
          {
            user: {
              enterWorldToken: newEnterWorldToken,
            },
            server: {
              bIsNonPK: mconf.bIsNonPK,
            },
          },
          user.getLoginSyncData(),
          nationManager.getSyncDataOfSelectableNations(),
          nationIntimacy.getSyncData(),
          townManager.getSyncData()
        ),
      };
      // build sync guild data
      if (userGuildSync) {
        _.merge(sync, userGuildSync);
      }

      const userNationSync = buildSyncDataOfBaseElection(user, curTimeUtc);
      if (userNationSync) {
        _.merge(sync, userNationSync);
      }

      // game log.
      if (bIsNewUser) {
        user.glog('common_register', {
          os: deviceType.toUpperCase(),
          osv,
          dm,
          lang: deviceLang,
          lang_game: lineLang,
          v,
          sk,
          platform: loginPlatform,
          country_ip,
        });
      }
      let region_id = null;
      const townCms = cms.Town[user.userTown.getLastTownCmsId()];
      if (GsUtil.isInTown(newLastGameState) && townCms) {
        region_id = townCms.RegionId;
      } else if (sailState) {
        region_id = sailState.region.id;
      }

      user.glog('common_login', {
        os: deviceType.toUpperCase(),
        osv,
        dm,
        lang: deviceLang,
        lang_game: lineLang,
        v,
        sk,
        platform: loginPlatform,
        country_ip,
        os_modulation,
        emulator,
        loading_time,
        region_id,
        town_id: user.userState.isInTown() ? user.userTown.getLastTownCmsId() : null,
        coordinates: getGLogCoordinate(location),
      });
      user.glog('common_adid', {
        os: deviceType.toUpperCase(),
        sk,
        platform: loginPlatform,
        country_ip,
        adjust_id,
        gps_adid: gps_adid ? gps_adid : null,
        idfa: idfa ? idfa : null,
        idfv: idfv ? idfv : null,
      });

      user.glogLoginOutSave(GLOG_LOGIN_OUT_SAVE_TYPE.UserLogin, curTimeUtc, 0, rsn, add_rsn);

      if (mconf.isSDO) {
        const now = mutil.curTimeUtc();
        const firstFleetStat = user.companyStat.getFleetStat(FirstFleetIndex); // 1함대
        const leaderMate = user.userMates.getLeaderMate(user.userFleets);

        SdoGLogEvents.uwo_character_login_glog({
          mid: user.accountId,
          character_id: user.pubId,
          character_name: user.userName,
          character_level: user.level,
          channel_id: user.channel,
          sub_channel_id: user.subChannel,
          platform: user.platform,
          device_id: user.udid,
          ip: user.countryIp,
          power: firstFleetStat.getCombatPower(), // 전투력
          port: 0,
          guild_id: user.userGuild.guildId.toString(),
          map_id: mconf.worldId,
          representive_id: user.userMates.getRepresentedMateCmsId().toString(), // 대표 항해사
          supervisor_id: leaderMate ? leaderMate.getNub().cmsId.toString() : '', // 현재 선택 제독 ID
          tmp_pin: location ? `${location.latitude},${location.longitude}` : '0,0', // 맞는지 확인은 필요?
          tmp_activity: user.userEnergy.getCurrentEnergy(now, user.level), // 현재 선단 행동력
          tmp_diamond: user.userPoints.paidRedGem, // 유료 레드젬
          tmp_free_diamond: user.userPoints.freeRedGem, // 무료 레드젬
          tmp_fight: firstFleetStat.getCombatPower(), // 현재 선단 전투력 (이게 맞는지 확인 필요)
          tmp_gold: user.userPoints.getPoint(cmsEx.BlueGemPointCmsId),
          tmp_silver: user.userPoints.getPoint(cmsEx.DucatPointCmsId),
          tmp_mile: user.userPoints.getMileage(now),//user.userPoints.getPoint(cmsEx.CashShopMileage),
          tmp_exp: user.exp,
          total_pay: 0, // TODO u_users 테이블에 `accumPay` 정도의 필드를 추가해서 대응!
        });
      }

      if (mconf.isSDO) {
        Survey.conditionalRequestSurvey(user);
      }

      // write isOnline to userCacheRedis
      userCacheRedis['setOnline'](userId, 1).catch((err) => {
        mlog.error('userCacheRedis setOnline is failed.', {
          userId,
          isOnline: 1,
          err: err.message,
        });
      });

      // 정산과 관련있을 수 있는 일,월요일을 제외한 날에 u_direct_mail_pendings 에서 유효하지 않은 메일을 삭제한다.
      if (new Date().getDay() > 1) {
        puDirectMailPendingsDelete(userDbPool, user.userId).catch((err) => {
          mlog.error('puDirectMailPendingsDelete is failed.', { err: err.message });
        });
      }

      // 로그인 할때 유저의 랭킹이 없을 수 있으므로 반영해준다.
      if (user.companyJobCmsId) {
        const { rankingManager } = Container.get(LobbyService);
        rankingManager.updateRanking(
          RANKING_CMS_ID.COMPANY_LEVEL,
          user.userId,
          user.exp,
          user.userId,
          true
        );

        const combatPower: number = user.userFleets.getFirstFleetCombatPower(user.companyStat);
        rankingManager.updateRanking(
          RANKING_CMS_ID.COMPANY_COMBAT_POWER,
          user.userId,
          combatPower,
          user.userId,
          true
        );
      }

      // build resp.
      // serverTimeUtc은 위에 선언한 currentTime으로 사용하면 로그인시 부하가 심할 때 오차가 클 수 있다.
      // 그렇기 때문에 mutil.curTimeUtc()로  새로 utc를 얻어와 적용해줘야한다.
      const resp: ResponseBody = {
        pingInterval: mconf.ping.interval, // Include ping interval.
        serverTimeUtc: mutil.curTimeUtc(),
        serverTimeZoneOffsetMin: mutil.getTimezoneOffset(),
        reconnect,
        sailId,
        offlineBattleHistories,
        bIsNewUser,
        sync,
        clientVolanteUrl: mconf.clientVolanteUrl,
        appGuardId: user.getAppGuardId(),
      };

      //! 온라인 유저나 오프라인 유저에 공통으로 적용되어야 하는 버프들은 아래 함수에 추가
      return user.addAllBuffsIfNeeded(curTimeUtc, guildData, bIsReturnUser, resp).then(() => {
        return resp;
      });
    })
    .then((resp) => {
      return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, resp);
    })
    .then(() => {
      user.setLoggedIn();
    });
}
