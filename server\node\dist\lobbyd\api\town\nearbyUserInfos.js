"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const proto = __importStar(require("../../../proto/lobby/proto"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const userManager_1 = require("../../userManager");
const typedi_1 = require("typedi");
module.exports = async (req, res) => {
    mlog_1.default.debug('api-/town/nearbyUserInfos req -', req.body);
    const { userId, packet: packetBody } = req.body;
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    setImmediate(() => {
        userManager.sendJsonPacketToUser(userId, proto.Town.NEARBY_USERS_SC, packetBody);
    });
    res.end();
};
//# sourceMappingURL=nearbyUserInfos.js.map