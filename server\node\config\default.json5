{
  // 기본 로깅 설정
  log: {
    // 콘솔 출력
    console: {
      level: 'verbose',
      inspectDepth: 7,
      // 개발용 '예쁜' 로깅이라, 라이브에서는 아래 값을 true 로.
      bUseStringifiedJsonPrintFormat: false,
    },

    // 파일 출력
    file: {
      level: 'verbose',
      // 로그파일 Rotation 설정.
      datePattern: 'YYYY-MM-DD.HH',
      datePatternDaily: 'YYYY-MM-DD',
    },

    // 로그 폴더 위치.
    // 이 위치 밑에 각종 로그 폴더/파일들이 생성됨. (log, glog, perfmonLog, etc.)
    // 상대 path 의 기준은 server/node 폴더를 기준으로 함.
    baseDir: '.',

    // 라이브에서 앱아이디를 포함한 파일명으로 생성하는 경우 아래 값을 true 로.
    bAppIdName: false,
  }, // 기본 로깅 설정

  // configd 설정
  http: {
    configd: {
      url: 'http://localhost:10001',
    },
  },

  // 앱 인스턴스 관련 설정
  instance: {
    // 월드 ID
    worldId: 'UWO-GL-01',
  },

  // APM 설정
  // https://www.elastic.co/guide/en/apm/agent/nodejs/current/configuration.html
  //
  // 서버가 자체적으로 세팅하는 옵션들.
  // {
  //   serviceName: [apm.serviceGroup]_[instance.worldId]_[process.name],
  //   serviceNodeName: mconf.appId,
  //
  //   // 로거.
  //   logger: mlog,
  //
  //   // Uncaught excpetion 은 sentry 가 잡도록.
  //   captureExceptions: false,
  // }
  apm: {
    active: false,
    secretToken: '',
    apiKey: '',
    serverUrl: 'http://turtle.motifgames.in:8200',
    transactionIgnoreUrls: [],
    transactionSampleRate: 1,
    disableInstrumentations: [],
    cloudProvider: 'none',
    transactionSampleRate: 1,

    // 아래 serviceGroup 은 apm 로그 적재시 사용되는,
    // 'serviceName' 을 구성할때만 사용됩니다.
    // serviceGroup: 'dev',
  },
}
