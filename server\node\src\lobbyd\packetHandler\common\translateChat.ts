// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import { Container } from 'typedi';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { Resp, Sync } from '../../type/sync';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../index';
import { LobbyService } from '../../server';
import { ChatTranslationChange } from '../../userChatTranslationCount';
import tuUpdateChatTranslationCount from '../../../mysqllib/txn/tuUpdateChatTranslationCount';

// ----------------------------------------------------------------------------
// 채팅 번역 사용횟수 감소 요청.
// 클라 의존적이라 해킹에 대한 이슈가 있지만 리스크는 안고 가기로 협의.
// ----------------------------------------------------------------------------

interface RequestBody {
  // 로그 저장용.
  text: string;
  chattingText: string;
  channelName: string;
  aliasName: string;
  uniqueId: string;
}

// ----------------------------------------------------------------------------
export class Cph_Common_TranslateChat implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // 무제한 번역 이후로는 더이상 이 패킷을 요청할 수없다.
    throw new MError(
      'You-can-no-longer-send-this-packet-after-an-unlimited-number-of-translations',
      MErrorCode.CANNOT_USE_TRANSLATION_PACKET
    );

    const chatTranslationChange: ChatTranslationChange =
      user.userChatTranslationCount.buildChatTranslationChange();

    if (!chatTranslationChange.freeCount) {
      throw new MError('lack-of-translation-count', MErrorCode.LACK_OF_TRANSLATION_COUNT, {
        reqBody,
      });
    }

    chatTranslationChange.freeCount--;

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    return tuUpdateChatTranslationCount(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      [], // PointChanges[]
      chatTranslationChange
    ).then(() => {
      const sync: Sync = {
        add: user.userChatTranslationCount.applyTranslationNub(chatTranslationChange),
      };

      mlog.info('consume-chat-translation', {
        userId: user.userId,
        reqBody,
      });

      return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, {
        sync,
      });
    });
  }
}
