import fs from 'fs-extra';
import { RemapSummaryResult, WorldDistribution, DuplicateInfo, ValidationIssue } from '../analyzer/remapDataSummaryAnalyzer';

export class SummaryReportGenerator {

  async generateReport(analysisResult: RemapSummaryResult, outputPath: string): Promise<void> {
    const html = this.generateHTML(analysisResult);
    await fs.writeFile(outputPath, html, 'utf-8');
  }
  
  private generateHTML(result: RemapSummaryResult): string {
    return `<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV 리맵 데이터 요약 보고서</title>
    <style>
        ${this.getCSS()}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>📊 CSV 리맵 데이터 요약 보고서</h1>
            <div class="meta-info">
                <span>📄 파일: ${result.csvFile}</span>
                <span>⏰ 생성일시: ${new Date(result.analyzedAt).toLocaleString('ko-KR')}</span>
            </div>
        </header>

        ${this.generateSummarySection(result)}
        ${this.generateWorldDistributionSection(result)}
        ${this.generateValidationSection(result)}
        ${this.generateDuplicatesSection(result)}
        ${this.generateAccountMappingsSection(result)}
        ${this.generateDataTableSection(result)}
    </div>

    <script>
        ${this.generateChartScript(result)}
    </script>
</body>
</html>`;
  }
  
  private getCSS(): string {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .meta-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 1.1em;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        tr:hover {
            background-color: #e9ecef !important;
        }

        tbody tr:nth-child(odd) {
            background-color: #f8f9fa;
        }

        tbody tr:nth-child(even) {
            background-color: #ffffff;
        }
        
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 0.8em;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .badge-primary {
            background-color: #007bff;
            color: white;
        }
        
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .badge-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .collapsible {
            cursor: pointer;
            padding: 10px;
            background-color: #f8f9fa;
            border: none;
            text-align: left;
            outline: none;
            font-size: 1em;
            width: 100%;
            border-radius: 5px;
            margin: 5px 0;
            transition: background-color 0.2s;
        }

        .collapsible:hover {
            background-color: #e9ecef;
        }

        .collapsible-content {
            display: none;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin: 5px 0;
        }

        .collapsible-content.active {
            display: block;
        }

        .collapsible-arrow {
            float: right;
            transition: transform 0.2s;
        }

        .collapsible.active .collapsible-arrow {
            transform: rotate(180deg);
        }
    `;
  }
  
  private generateSummarySection(result: RemapSummaryResult): string {
    return `
        <div class="section">
            <div class="section-header">
                <h2>📈 요약 통계</h2>
            </div>
            <div class="section-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">📊 ${result.totalRecords.toLocaleString()}</div>
                        <div class="stat-label">총 레코드 수</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">👤 ${result.uniqueAccounts.toLocaleString()}</div>
                        <div class="stat-label">원본 계정 수 (GNID)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">🎮 ${result.uniqueUsers.toLocaleString()}</div>
                        <div class="stat-label">원본 캐릭터 수 (NID)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">👥 ${result.uniqueTargetAccounts.toLocaleString()}</div>
                        <div class="stat-label">대상 계정 수 (GNID)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">🎯 ${result.uniqueTargetUsers.toLocaleString()}</div>
                        <div class="stat-label">대상 캐릭터 수 (NID)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">🌍 ${result.worldDistribution.length}</div>
                        <div class="stat-label">월드 수</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">🔄 ${result.duplicates.reduce((sum, dup) => sum + dup.duplicates.length, 0)}</div>
                        <div class="stat-label">전체 중복 수</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">⚠️ ${result.validationIssues.length}</div>
                        <div class="stat-label">검증 문제 수</div>
                    </div>
                </div>
            </div>
        </div>
    `;
  }
  
  private generateWorldDistributionSection(result: RemapSummaryResult): string {
    const tableRows = result.worldDistribution.map(world => `
        <tr>
            <td>${world.worldId}</td>
            <td>${world.recordCount.toLocaleString()}</td>
            <td>${world.uniqueUsers.toLocaleString()}</td>
            <td>${world.duplicateUsers.toLocaleString()}</td>
            <td>${world.percentage}%</td>
        </tr>
    `).join('');

    // 총계 계산
    const totalRecords = result.worldDistribution.reduce((sum, world) => sum + world.recordCount, 0);
    const totalUsers = result.worldDistribution.reduce((sum, world) => sum + world.uniqueUsers, 0);
    const totalDuplicateUsers = result.worldDistribution.reduce((sum, world) => sum + world.duplicateUsers, 0);

    const totalRow = `
        <tr style="background-color: #f8f9fa; font-weight: bold; border-top: 2px solid #dee2e6;">
            <td>총계</td>
            <td>${totalRecords.toLocaleString()}</td>
            <td>${totalUsers.toLocaleString()}</td>
            <td>${totalDuplicateUsers.toLocaleString()}</td>
            <td>100%</td>
        </tr>
    `;

    return `
        <div class="section">
            <button class="collapsible section-header" style="width: 100%; text-align: left; background: #f8f9fa; padding: 15px; margin: 0; border: none; border-radius: 5px; font-size: 1.2em; font-weight: bold; cursor: pointer;">
                🌍 월드별 분포 <span class="collapsible-arrow">▼</span>
            </button>
            <div class="collapsible-content section-content">
                <div class="chart-container">
                    <canvas id="worldChart"></canvas>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>월드 ID</th>
                                <th>레코드 수</th>
                                <th>캐릭터 수 (NID)</th>
                                <th>중복 캐릭터 수</th>
                                <th>비율</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tableRows}
                            ${totalRow}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
  }
  
  private generateValidationSection(result: RemapSummaryResult): string {
    const hasIssues = result.validationIssues.length > 0;
    const alertClass = hasIssues ? 'alert-danger' : 'alert-success';
    const alertMessage = hasIssues
      ? `⚠️ ${result.validationIssues.length}개의 검증 문제를 발견했습니다`
      : '✅ 검증 문제가 발견되지 않았습니다';

    let issuesContent = '';
    if (hasIssues) {
      const issuesByType = result.validationIssues.reduce((acc, issue) => {
        if (!acc[issue.type]) acc[issue.type] = [];
        acc[issue.type]!.push(issue);
        return acc;
      }, {} as Record<string, ValidationIssue[]>);

      const typeNames: Record<string, string> = {
        'missing_field': '필수 필드 누락',
        'empty_value': '빈 값',
        'invalid_format': '잘못된 형식'
      };

      issuesContent = Object.entries(issuesByType).map(([type, issues]) => `
        <button class="collapsible">${typeNames[type] || type} (${issues.length}개)</button>
        <div class="collapsible-content">
          <ul>
            ${issues.slice(0, 10).map(issue => `
              <li>순번 ${issue.record.seq}: ${issue.message}</li>
            `).join('')}
            ${issues.length > 10 ? `<li>... 외 ${issues.length - 10}개 더</li>` : ''}
          </ul>
        </div>
      `).join('');
    }

    return `
        <div class="section">
            <button class="collapsible section-header" style="width: 100%; text-align: left; background: #f8f9fa; padding: 15px; margin: 0; border: none; border-radius: 5px; font-size: 1.2em; font-weight: bold; cursor: pointer;">
                ✅ 데이터 검증 <span class="collapsible-arrow">▼</span>
            </button>
            <div class="collapsible-content section-content">
                <div class="alert ${alertClass}">
                    ${alertMessage}
                </div>
                ${issuesContent}
            </div>
        </div>
    `;
  }
  
  private generateDuplicatesSection(result: RemapSummaryResult): string {
    const hasDuplicates = result.duplicates.length > 0;
    const alertClass = hasDuplicates ? 'alert-warning' : 'alert-success';
    const alertMessage = hasDuplicates
      ? `⚠️ ${result.duplicates.length}개 카테고리에서 중복을 발견했습니다`
      : '✅ 중복이 발견되지 않았습니다';

    let duplicatesContent = '';
    if (hasDuplicates) {
      const typeNames: Record<string, string> = {
        'seq': '순번 (seq)',
        'account_user_pair': '원본 계정-캐릭터 조합',
        'target_account_user_pair': '대상 계정-캐릭터 조합'
      };

      duplicatesContent = result.duplicates.map(duplicate => {
        // 최대 50개까지만 표시
        const displayDuplicates = duplicate.duplicates.slice(0, 50);
        const hasMore = duplicate.duplicates.length > 50;

        // 모든 중복 레코드를 수집
        const allRecords: any[] = [];
        displayDuplicates.forEach(dup => {
          dup.records.forEach(record => {
            allRecords.push({
              ...record,
              duplicateValue: dup.value
            });
          });
        });

        // 계정ID(GNID)별로 그룹핑
        const groupedByAccount = allRecords.reduce((groups, record) => {
          const accountId = record.uwo_Gnid;
          if (!groups[accountId]) {
            groups[accountId] = [];
          }
          groups[accountId].push(record);
          return groups;
        }, {} as Record<string, any[]>);

        // 그룹별로 테이블 생성
        const groupTables = Object.entries(groupedByAccount).map(([accountId, records]) => {
          // 순번 순으로 정렬
          const sortedRecords = (records as any[]).sort((a: any, b: any) => a.seq - b.seq);

          // 중복 값별로 그룹핑 (같은 원본 캐릭터)
          const duplicateGroups = sortedRecords.reduce((groups, record) => {
            const key = record.duplicateValue;
            if (!groups[key]) {
              groups[key] = [];
            }
            groups[key].push(record);
            return groups;
          }, {} as Record<string, any[]>);

          // 중복 그룹별로 테이블 생성
          const duplicateGroupTables = Object.entries(duplicateGroups).map(([duplicateValue, dupRecords]) => {
            const [sourceGnid, sourceNid] = duplicateValue.split(':');
            const typedDupRecords = dupRecords as any[];

            // 첫 번째 레코드 (원본)
            const firstRecord = typedDupRecords[0];
            const duplicateRecords = typedDupRecords.slice(1);

            // 첫 번째 시도 테이블
            const firstAttemptTable = `
              <div style="margin-bottom: 15px;">
                <h5 style="background: #d4edda; padding: 8px 12px; margin: 0; border-radius: 5px; color: #155724; font-weight: bold;">
                  ✅ 첫 번째 시도 (유지)
                </h5>
                <table style="margin: 0; width: 100%; border: 1px solid #c3e6cb;">
                  <thead>
                    <tr style="background: #f1f8e9;">
                      <th>순번</th>
                      <th>원본 캐릭터 (NID)</th>
                      <th>대상 계정 (GNID)</th>
                      <th>대상 캐릭터 (NID)</th>
                      <th>월드</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>${firstRecord.seq}</td>
                      <td>${sourceNid}</td>
                      <td>${firstRecord.uwogl_Gnid}</td>
                      <td>${firstRecord.uwogl_Nid}</td>
                      <td>${firstRecord.gameServerId}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            `;

            // 중복된 시도 테이블
            let duplicateAttemptTable = '';
            if (duplicateRecords.length > 0) {
              const duplicateRows = duplicateRecords.map((record: any) => `
                <tr style="background-color: #fff3cd;">
                  <td>${record.seq}</td>
                  <td>${sourceNid}</td>
                  <td>${record.uwogl_Gnid}</td>
                  <td>${record.uwogl_Nid}</td>
                  <td>${record.gameServerId}</td>
                </tr>
              `).join('');

              duplicateAttemptTable = `
                <div style="margin-bottom: 15px;">
                  <h5 style="background: #fff3cd; padding: 8px 12px; margin: 0; border-radius: 5px; color: #856404; font-weight: bold;">
                    ⚠️ 중복된 시도 (삭제 필요) - ${duplicateRecords.length}개
                  </h5>
                  <table style="margin: 0; width: 100%; border: 1px solid #ffeaa7;">
                    <thead>
                      <tr style="background: #fef9e7;">
                        <th>순번</th>
                        <th>원본 캐릭터 (NID)</th>
                        <th>대상 계정 (GNID)</th>
                        <th>대상 캐릭터 (NID)</th>
                        <th>월드</th>
                      </tr>
                    </thead>
                    <tbody>
                      ${duplicateRows}
                    </tbody>
                  </table>
                </div>
              `;
            }

            return `
              <div style="margin-bottom: 20px; border: 2px solid #dee2e6; border-radius: 8px; background: white;">
                <div style="background: #f8f9fa; padding: 10px 15px; border-bottom: 1px solid #dee2e6; font-weight: bold;">
                  🎯 원본 캐릭터: ${sourceNid} (총 ${typedDupRecords.length}개 매핑)
                </div>
                <div style="padding: 15px;">
                  ${firstAttemptTable}
                  ${duplicateAttemptTable}
                </div>
              </div>
            `;
          }).join('');

          return `
            <div style="margin-bottom: 25px;">
              <button class="collapsible" style="width: 100%; text-align: left; background: #e9ecef; padding: 10px; margin: 0; border: none; border-radius: 5px 5px 0 0; border-left: 4px solid #007bff; font-weight: bold; cursor: pointer;">
                📋 계정 ID: ${accountId} (${(records as any[]).length}개 중복 레코드, ${Object.keys(duplicateGroups).length}개 캐릭터) <span class="collapsible-arrow">▼</span>
              </button>
              <div class="collapsible-content" style="padding: 15px; border: 1px solid #dee2e6; border-top: none; background: #fafafa;">
                ${duplicateGroupTables}
              </div>
            </div>
          `;
        }).join('');

        return `
          <button class="collapsible">${typeNames[duplicate.type] || duplicate.type} (${duplicate.duplicates.length}개 중복) <span class="collapsible-arrow">▼</span></button>
          <div class="collapsible-content">
            <div style="margin-bottom: 15px; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 5px; color: #0c5460;">
              <strong>💡 사용 팁:</strong> 각 계정 ID를 클릭하면 상세 내용을 접거나 펼칠 수 있습니다.
            </div>
            ${groupTables}
            ${hasMore ? `
              <div class="alert alert-warning" style="margin-top: 15px;">
                <strong>표시 제한:</strong> 총 ${duplicate.duplicates.length}개 중복 중 처음 50개만 표시됩니다.
              </div>
            ` : ''}
            <div style="margin-top: 15px; padding: 10px; background: #e9ecef; border-radius: 5px;">
              <strong>요약:</strong> ${Object.keys(groupedByAccount).length}개 계정, ${duplicate.duplicates.length}개의 중복된 값, 총 ${allRecords.length}개의 레코드
            </div>
          </div>
        `;
      }).join('');
    }

    return `
        <div class="section">
            <button class="collapsible section-header" style="width: 100%; text-align: left; background: #f8f9fa; padding: 15px; margin: 0; border: none; border-radius: 5px; font-size: 1.2em; font-weight: bold; cursor: pointer;">
                🔍 중복 분석 <span class="collapsible-arrow">▼</span>
            </button>
            <div class="collapsible-content section-content">
                <div class="alert ${alertClass}">
                    ${alertMessage}
                </div>
                ${duplicatesContent}
            </div>
        </div>
    `;
  }
  
  private generateAccountMappingsSection(result: RemapSummaryResult): string {
    const topMappings = result.accountMappings.slice(0, 20);
    const tableRows = topMappings.map(mapping => `
        <tr>
            <td>${mapping.sourceAccount}</td>
            <td>${mapping.targetAccount}</td>
            <td>${mapping.userCount}</td>
            <td>${mapping.worlds.join(', ')}</td>
        </tr>
    `).join('');

    return `
        <div class="section">
            <button class="collapsible section-header" style="width: 100%; text-align: left; background: #f8f9fa; padding: 15px; margin: 0; border: none; border-radius: 5px; font-size: 1.2em; font-weight: bold; cursor: pointer;">
                🔄 계정 매핑 (상위 20개) <span class="collapsible-arrow">▼</span>
            </button>
            <div class="collapsible-content section-content">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>원본 계정 (GNID)</th>
                                <th>대상 계정 (GNID)</th>
                                <th>캐릭터 수 (NID)</th>
                                <th>월드</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tableRows}
                        </tbody>
                    </table>
                </div>
                ${result.accountMappings.length > 20 ? `
                    <p class="text-muted">총 ${result.accountMappings.length}개 매핑 중 상위 20개 표시</p>
                ` : ''}
            </div>
        </div>
    `;
  }
  
  private generateDataTableSection(result: RemapSummaryResult): string {
    const sampleRecords = result.records.slice(0, 100);
    const tableRows = sampleRecords.map(record => `
        <tr>
            <td>${record.seq}</td>
            <td>${record.uwo_Gnid}</td>
            <td>${record.uwo_Nid}</td>
            <td>${record.uwogl_Gnid}</td>
            <td>${record.uwogl_Nid}</td>
            <td>${record.gameServerId}</td>
        </tr>
    `).join('');

    return `
        <div class="section">
            <button class="collapsible section-header" style="width: 100%; text-align: left; background: #f8f9fa; padding: 15px; margin: 0; border: none; border-radius: 5px; font-size: 1.2em; font-weight: bold; cursor: pointer;">
                📋 샘플 데이터 (처음 100개 레코드) <span class="collapsible-arrow">▼</span>
            </button>
            <div class="collapsible-content section-content">
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>순번</th>
                                <th>원본 계정 (GNID)</th>
                                <th>원본 캐릭터 (NID)</th>
                                <th>대상 계정 (GNID)</th>
                                <th>대상 캐릭터 (NID)</th>
                                <th>월드</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tableRows}
                        </tbody>
                    </table>
                </div>
                ${result.records.length > 100 ? `
                    <p class="text-muted">총 ${result.records.length}개 레코드 중 처음 100개 표시</p>
                ` : ''}
            </div>
        </div>
    `;
  }
  


  private generateChartScript(result: RemapSummaryResult): string {
    return `
        // World Distribution Chart
        const worldCtx = document.getElementById('worldChart').getContext('2d');
        new Chart(worldCtx, {
            type: 'doughnut',
            data: {
                labels: ${JSON.stringify(result.worldDistribution.map(w => w.worldId))},
                datasets: [{
                    data: ${JSON.stringify(result.worldDistribution.map(w => w.recordCount))},
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                        '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: '월드별 레코드 분포'
                    }
                }
            }
        });
        
        // Collapsible functionality
        document.querySelectorAll('.collapsible').forEach(button => {
            button.addEventListener('click', function() {
                const content = this.nextElementSibling;
                content.classList.toggle('active');
                this.classList.toggle('active');
            });
        });
    `;
  }
}
