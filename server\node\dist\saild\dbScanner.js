"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbScanner = void 0;
const typedi_1 = require("typedi");
const server_1 = require("./server");
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mutil = __importStar(require("../motiflib/mutil"));
const sailConst_1 = require("./sailConst");
const pwAutoSailingScan_1 = __importDefault(require("../mysqllib/sp/pwAutoSailingScan"));
const offlineSailingManager_1 = require("./offlineSailingManager");
const dbScanner = async () => {
    const { worldDbConnPool, sailRedis } = typedi_1.Container.get(server_1.SailService);
    const offlineSailingManager = typedi_1.Container.get(offlineSailingManager_1.OfflineSailingManager);
    const dbScanLimit = mconf_1.default.dbAutoSailingScanLimit
        ? mconf_1.default.dbAutoSailingScanLimit
        : sailConst_1.DEFAULT_DB_AUTO_SAILING_SCAN_LIMIT;
    const curTimeUtc = mutil.curTimeUtc();
    Promise.resolve()
        .then(() => {
        //acquire dbScanLock
        return sailRedis['acquireDbScanLockAndLastScanedUserId'](mconf_1.default.apiService.url, sailConst_1.DB_SCAN_LOCK_TIMEOUT);
    })
        .then(async (resp) => {
        const ret = JSON.parse(resp);
        if (ret.result == 0) {
            //couldn't get the lock..try next time
            return null;
        }
        //mlog.info(`[dbScanner] startUserId: ${ret.lastUserId}`);
        //scan autoSailing list from world db
        return (0, pwAutoSailingScan_1.default)(worldDbConnPool.getPool(), ret.lastUserId, dbScanLimit)
            .then(async (retScan) => {
            let lastUserId = 0;
            if (retScan && retScan.length === dbScanLimit) {
                lastUserId = retScan[retScan.length - 1].userId;
            }
            if (retScan && retScan.length > 0) {
                if (!mconf_1.default.isDev) {
                    mlog_1.default.info(`[dbScanner] retLastUserId: ${retScan ? retScan[retScan.length - 1].userId : 0}, lastUserId: ${lastUserId}, count: ${retScan ? retScan.length : 0}`);
                }
            }
            // release dbScanLock
            sailRedis['releaseDbScanLockAndLastScanedUserId'](mconf_1.default.apiService.url, lastUserId);
            return retScan;
        })
            .then((retScan) => {
            if (!retScan) {
                return;
            }
            const minHeartBeatTs = curTimeUtc - mconf_1.default.userHeartBeatInterval;
            const minOffSailTs = curTimeUtc - mconf_1.default.offlineSailingHeartBeatInterval;
            for (const elem of retScan) {
                // 이미 등록된 잡인지 체크
                if (offlineSailingManager.getJob(elem.userId)) {
                    continue;
                }
                //check user online & register to redis
                registerOfflineSailingJob(elem, curTimeUtc, minHeartBeatTs, minOffSailTs);
            }
        })
            .catch((e) => {
            mlog_1.default.error('dbScanner got error1', e.message);
        });
    })
        .catch((e) => {
        mlog_1.default.error('dbScanner got error2', e.message);
    });
};
exports.dbScanner = dbScanner;
const registerOfflineSailingJob = async (autoSailingInfo, curTimeUtc, minHeartBeatTs, minOffSailTs) => {
    const { userCacheRedis } = typedi_1.Container.get(server_1.SailService);
    Promise.resolve()
        .then(() => {
        //check user online
        const minHeartBeatTs = curTimeUtc - mconf_1.default.userHeartBeatInterval;
        return userCacheRedis['getUserHeartBeat'](autoSailingInfo.accountId, minHeartBeatTs);
    })
        .then((lastLoginTimeUtc) => {
        if (lastLoginTimeUtc) {
            // loggedIn bot user should get out.
            mlog_1.default.debug('registerOfflineSailingJob. user logged in.. stopping process', {
                accountId: autoSailingInfo.accountId,
            });
            return Promise.reject();
        }
        return Promise.resolve();
    })
        .then(() => {
        //register job to redis
        return userCacheRedis['tryRegisterOfflineSailingJob'](autoSailingInfo.accountId, autoSailingInfo.userId, mconf_1.default.appId, curTimeUtc, minHeartBeatTs, minOffSailTs);
    }, () => {
        //rejected
        mlog_1.default.debug('go around tryRegisterOfflineSailingJob: ', {
            accountId: autoSailingInfo.accountId,
            userId: autoSailingInfo.userId,
        });
        return 99;
    })
        .then((ret) => {
        let resultMsg = 'unknwon';
        if (ret === 0) {
            //insert to jobs
            const offlineSailingManager = typedi_1.Container.get(offlineSailingManager_1.OfflineSailingManager);
            offlineSailingManager.addJob(autoSailingInfo);
            resultMsg = 'success';
            mlog_1.default.info('registerOfflineSailingJob result: ' + resultMsg, {
                accountId: autoSailingInfo.accountId,
                userId: autoSailingInfo.userId,
            });
        }
        else if (1 === ret) {
            resultMsg = 'user-online';
        }
        else if (2 === ret) {
            resultMsg = 'already-exist-job';
        }
        else if (99 === ret) {
            resultMsg = 'user-online2';
        }
        // mlog.info('registerOfflineSailingJob result: ' + resultMsg, {
        //   accountId: autoSailingInfo.accountId,
        //   userId: autoSailingInfo.userId,
        // });
    });
};
//# sourceMappingURL=dbScanner.js.map