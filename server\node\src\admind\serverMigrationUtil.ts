// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
import _ from 'lodash';
import Container from 'typedi';

import { AdminService } from './server';
import { DBConnPool, DbConnPoolManager } from '../mysqllib/pool';
import { getUserDbShardId } from '../mysqllib/mysqlUtil';
import mhttp from '../motiflib/mhttp';
import { LGBillingErrorCode } from '../motiflib/mhttp/linegamesBillingApiClient';
import * as mutil from '../motiflib/mutil';
import { MAIL_STATE } from '../lobbyd/const';
import puAdminUserLoadGuildId from '../mysqllib/sp/puAdminUserLoadGuildId';
import pwAdminAuctionLoadProduct from '../mysqllib/sp/pwAdminAuctionLoadProduct';
import pwAdminAuctionExpiredProductLoad from '../mysqllib/sp/pwAdminAuctionExpiredProductLoad';
import puAdminDirectMailLoad from '../mysqllib/sp/puAdminDirectMailLoad';
import puAdminDirectMailPendingLoad from '../mysqllib/sp/puAdminDirectMailPendingLoad';
import puAdminLineMailLoad from '../mysqllib/sp/puAdminLineMailLoad';
import puAdminShowTables from '../mysqllib/sp/puAdminShowTables';
import mlog from '../motiflib/mlog';

export enum SERVER_MIGRATION_STEP {
  NONE = 0,
  USER_REDIS_IS_DELETED = 1,
  FRIENDS_IS_DELETED = 2,
  USER_RDB_IS_MIGRATED = 3,
  COLLECTOR_REDIS_IS_MIGRATED = 4,
  NATION_REDIS_IS_MIGRATED = 5,
  TOWN_REDIS_ACCUM_INVEST_IS_MIGRATED = 6,
  TOWN_REDIS_IS_DELETED = 7,
  AUTH_RDB_IS_UPDATED = 8,
}

export enum FAIL_REASON {
  HAS_GUILD = 0,
  HAS_ACUTION_PRODUCT = 1,
  HAS_RECEIVABLE_AUCTION_PROCEEDS = 2,
  HAS_CASH_SHOP_ITEM = 3,
  HAS_UNDELETED_MAIL = 4,
}

const UNMIGRATE_USER_TABLES = {
  ['migrations']: true,
  ['u_direct_mails']: true,
  ['u_direct_mail_last_ids']: true,
  ['u_direct_mail_pendings']: true,
  ['u_line_mails']: true,
  ['u_last_reported_discoveries']: true,
  ['u_friends']: true,
  ['u_friend_points']: true,
  ['u_arena']: true,
  ['u_arena_grade_rewards']: true,
  ['u_waiting_join_guilds']: true,
  ['u_guild_raid_tickets']: true,
  ['u_guild_shop_restricted_products']: true,
};

let userTables: string[];

export function getUserTables(dbPool: DBConnPool) {
  if (userTables) {
    return Promise.resolve(userTables);
  }

  userTables = [];
  return puAdminShowTables(dbPool.getPool()).then((ret) => {
    for (const elem of ret) {
      const tableName: any = Object.values(elem)[0];
      if (UNMIGRATE_USER_TABLES[tableName]) {
        continue;
      }

      userTables.push(tableName);
    }

    return userTables;
  });
}

// user db table 중 userId로 select 하지 않아야 되거나 select 문에 포함되지 않아야 되는 컬럼이 있는 경우 따로 정의 되어야 됨.
export function userDbSelectQuery(tableName: string, userId: number) {
  switch (tableName) {
    case 'u_users':
      return `SELECT * FROM u_users WHERE id = ${userId};`;
    // case 'u_direct_mail_pendings':
    //   return `SELECT userId, cmsId, createTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, title, titleFormatValue, body, bodyFormatValue, attachment, isValid FROM u_direct_mail_pendings WHERE id = ${userId};`;
    default:
      return `SELECT * FROM ${tableName} WHERE userId = ${userId};`;
  }
}

export function userDbDeleteQuery(tableName: string, userId: number) {
  switch (tableName) {
    case 'u_users':
      return `DELETE FROM u_users WHERE id = ${userId};`;
    default:
      return `DELETE FROM ${tableName} WHERE userId = ${userId};`;
  }
}

export function canMigrate(
  userId: number,
  worldId: string,
  bTest: boolean = false
): Promise<number> {
  const { userDbConnPoolMgrs, worldDbConnPools } = Container.get(AdminService);

  const userDbConnPoolMgr: DbConnPoolManager = userDbConnPoolMgrs[worldId];
  const userDbShardId = getUserDbShardId(userId, worldId);
  const userDbPool: DBConnPool = userDbConnPoolMgr.getDBConnPoolByShardId(userDbShardId);
  const worldDbConnPool = worldDbConnPools[worldId];
  const curTimeUtc = mutil.curTimeUtc();

  let rsn = 0;

  return Promise.resolve()
    .then(() => {
      // 상회 탈퇴 여부 검사.
      return puAdminUserLoadGuildId(userDbPool.getPool(), userId).then((ret) => {
        if (ret) {
          mlog.info('[can-migrate] has-guild', { userId, worldId });
          rsn += 1 << FAIL_REASON.HAS_GUILD;
        }
      });
    })
    .then(() => {
      // 거래소에 등록된 상품 있는지 검사.
      return pwAdminAuctionLoadProduct(worldDbConnPool.getPool(), userId).then((ret) => {
        if (ret && _.isArray(ret) && ret.length > 0) {
          mlog.info('[can-migrate] has-acution-product', { userId, worldId });
          rsn += 1 << FAIL_REASON.HAS_ACUTION_PRODUCT;
        }
      });
    })
    .then(() => {
      // 거래소 정산 완료 여부 검사.
      return pwAdminAuctionExpiredProductLoad(worldDbConnPool.getPool(), userId).then((ret) => {
        if (ret && _.isArray(ret) && ret.length > 0) {
          mlog.info('[can-migrate] has-receivable-auction-proceeds', { userId, worldId });
          rsn += 1 << FAIL_REASON.HAS_RECEIVABLE_AUCTION_PROCEEDS;
        }
      });
    })
    .then(() => {
      // 유료 보관함 수령 여부 검사
      if (bTest) {
        return null;
      }

      const lgbillingd = mhttp.worldHttp[worldId].lgbillingd;
      return lgbillingd.queryInventoryPurchaseList(userId.toString()).then((ret) => {
        let bHas = true;
        if (!ret.success) {
          if (ret.errorCd === LGBillingErrorCode[LGBillingErrorCode.NOT_EXIST_DATA]) {
            // 보관함에 아무것도 없을 때도 위 에러가 나온다.
            bHas = false;
          }
        } else if (!Array.isArray(ret.data) || ret.data.length === 0) {
          bHas = false;
        }
        if (bHas) {
          mlog.info('[can-migrate] has-receivable-auction-proceeds', {
            userId,
            worldId,
            resp: ret,
          });
          rsn += 1 << FAIL_REASON.HAS_CASH_SHOP_ITEM;
        }
      });
    })
    .then(() => {
      // 모든 우편 삭제되었는지 검사
      return puAdminDirectMailLoad(userDbPool.getPool(), userId, curTimeUtc)
        .then((ret) => {
          if (ret.length > 0) {
            mlog.info('[can-migrate] has-undeleted-mail', { userId, worldId });
            rsn += 1 << FAIL_REASON.HAS_UNDELETED_MAIL;
          }

          if ((rsn & (1 << FAIL_REASON.HAS_UNDELETED_MAIL)) === 0) {
            return puAdminDirectMailPendingLoad(userDbPool.getPool(), userId, curTimeUtc);
          }

          return [];
        })
        .then((ret) => {
          if (ret.length > 0) {
            mlog.info('[can-migrate] has-undeleted-pending-mail', { userId, worldId });
            rsn += 1 << FAIL_REASON.HAS_UNDELETED_MAIL;
          }

          if ((rsn & (1 << FAIL_REASON.HAS_UNDELETED_MAIL)) === 0) {
            return puAdminLineMailLoad(userDbPool.getPool(), userId);
          }

          return [];
        })
        .then((ret) => {
          if (ret.length > 0) {
            for (const mail of ret) {
              if (mail.state === MAIL_STATE.DELETED) {
                continue;
              }

              const expireTimeUtc = parseInt(mail.expireTimeUtc, 10);
              if (expireTimeUtc && expireTimeUtc <= curTimeUtc) {
                continue;
              }

              // 라인메일(URL:'api/mailboxG/current/list')에서 받아온 정보를 우리쪽 게임DB에 저장된 메일정보.
              mlog.info('[can-migrate] has-undeleted-line-mail', { userId, worldId });
              rsn += 1 << FAIL_REASON.HAS_UNDELETED_MAIL;
              break;
            }
          }
        });
    })
    .then(() => {
      return Promise.resolve(rsn);
    });
}
