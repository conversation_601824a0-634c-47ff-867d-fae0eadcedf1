// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

// Check Proto.lua & NetCommon.h
// CS means client to server. CS packet is no response.
// SC means server to client without client request.
export enum Auth {
  HELLO = 101,
  ENTER_WORLD = 102,
  CHANGE_NAME = 103,
  COMPLETE_PROLOGUE = 105,
  REFRESH_ENTER_WORLD_TOKEN_SC = 106,
  REVOKE = 108,
  KICK_SC = 123,
  GAME_GUARD_CHECK_SC = 130,
  GAME_GUARD_CHECK_CS = 131,
  APP_GUARD_CHECK_CS = 140,
}

export enum Town {
  ENTER = 2001,
  LOAD_COMPLETE = 2002,
  MOVE_CS = 2003,
  NEARBY_USERS_SC = 2005,
  USER_UPDATE_SC = 2006,
  QUERY_MATE_CS = 2007,
  QUERY_MATE_RES_SC = 2008,
  ENTER_BUILDING = 2009,
  LEAVE_BUILDING = 2010,
  // USER_STATE_UPDATE_SC = 2011, 사용 안함
  BANK_DEPOSIT = 2012,
  BANK_DEPOSIT_INSTALLMENT_SAVINGS = 2013,
  BANK_WITHDRAW = 2014,
  BANK_WITHDRAW_INSTALLMENT_SAVINGS = 2015,
  BANK_BUY_INSURANCE = 2016,
  USER_MOVE_SC = 2017,
  USER_LEAVE_SC = 2018,
  DEPART_CHANGE_SHIP_FLEET_FORMATION = 2019,
  DEPART_BUY_SUPPLIES = 2020,
  DEPART_DEPART = 2021,
  SHIPYARD_CREATE_SHIP = 2022,
  SHIPYARD_UPGRADE_BLUEPRINT = 2023,
  SHIPYARD_REPAIR = 2024,
  SHIPYARD_SELL_SHIP = 2025,
  SHIPYARD_CHANGE_BLUEPRINT_SLOT = 2026,
  PUB_DRAFT_SAILOR = 2027,
  PUB_GET_SYNC_DATA = 2028,
  PUB_RECRUIT_MATE = 2029,
  PUB_BUY_DRINK = 2030,
  GOVER_INVEST = 2031,
  GOVER_GET_SYNC_DATA = 2032,
  TOWN_STATES_UPDATE_SC = 2033,
  TOWN_USER_SYNC_DATA_UPDATE_SC = 2034,
  TRADE_BUY = 2035,
  TRADE_SELL = 2036,
  TRADE_GET_SYNC_DATA = 2037,
  TOWN_TRADE_PRICE_PERCENT_UPDATE_SC = 2038,
  TOWN_INVESTMENT_UPDATE_SC = 2039,
  TRADE_GET_ALL_SESSION_PRICE_PERCENTS = 2040,
  RELIGION_UPDATE_BUFF = 2041,
  // SHOP_BUY = 2042,  사용 안함. SHOP_BUY_EX 로 대체됨. (UWO-16461)
  SHOP_GET_TOWN_BLACK_MARKET = 2043,
  SHOP_SELL = 2044,
  SHOP_RESET_TOWN_BLACK_MARKET = 2045,
  RECEIVE_INSURANCE = 2047,
  CHANGE_SHIP_SLOT = 2048,
  CHANGE_SHIP_SLOTS = 2049,
  UNION_RESET_REQUEST = 2050,
  PUB_GIFT = 2051,
  TRADE_TRY_BEGIN_NEGO = 2052,
  TRADE_TRY_NEGO = 2053,
  NOVICE_SUPPLY = 2054,
  PALACE_GET_ROYAL_ORDER = 2055,
  PALACE_REJECT_ROYAL_ORDER = 2056,
  MANTIC_FORTUNE = 2057,
  ARREST_USER_CHOICE = 2058,
  COLLECTOR_REPORT_DISCOVERIES = 2059,
  COLLECTOR_CONTRACT = 2060,
  COLLECTOR_GET_RANK = 2061,
  TRADE_RESET_BOUGHT = 2063,
  COLLECTOR_REPORT_WORLD_MAP_TILES = 2064,
  SHIPYARD_BUY_SHIP_SLOT_ITEM = 2066, // 사용 안함. SHIPYARD_BUY 로 대체됨. (UWO-16460)
  SHIPYARD_ENCHANT = 2067,
  SHIPYARD_CHOICE_ENCHANT_RESULT = 2068,
  SHIPYARD_DISMANTLE_SHIP = 2069,
  UPDATE_SOCIAL_ANI_PERSIST_CS = 2070,
  SHOW_SOCIAL_ANI_INSTANT_CS = 2071,
  SHOW_SOCIAL_ANI_INSTANT_SC = 2072,
  SHOW_EMOTICON_INSTANT_CS = 2073,
  SHOW_EMOTICON_INSTANT_SC = 2074,
  MANTIC_SWAP_PIECE = 2075,
  GOVER_CHANGE_MAYOR_TAX = 2076,
  MAYOR_TAX_UPDATE_SC = 2077,
  PUB_STAFF_CALL = 2078,
  PUB_STAFF_NOMINATION = 2079,
  PUB_STAFF_TALKING = 2080,
  PUB_STAFF_BOASTING = 2081,
  PUB_STAFF_GIFT = 2082,
  TOWN_NATION_SHARE_POINT_UPDATE_SC = 2083,
  TOWN_MAYOR_UPDATE_SC = 2084,
  PUB_STAFF_GET_SYNC_DATA = 2085,
  GOVER_GET_LAST_WEEK_RANK = 2086,
  PUB_REDUCE_RECRUIT_NEGO_WAIT_TIME = 2087,
  PUB_RESET_RECRUIT_NEGO_WAIT_TIME = 2088,
  // NEW_SHIPYARD_CREATE_SHIP = 2089, 사용하지 않음
  SHIPYARD_RECEIVE_SHIP = 2090,
  PUB_SPONSOR_MY_MATE = 2091,
  PUB_REDUCE_MY_MATE_SPONSOR_WAIT_TIME = 2092,
  PUB_RESET_MY_MATE_SPONSOR_WAIT_TIME = 2093,
  SHIPYARD_VERIFY_TOW_SHIP = 2094,
  SHIPYARD_REPAIR_LIFE_SHIP = 2095,
  PALACE_GET_CONTRIBUTION_SHOP_SYNC_DATA = 2096,
  PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT = 2097,
  SHOP_BUY_EX = 2098,
  SHIPYARD_BUY = 2099,
  UNION_RESET_EVENT_REQUEST = 2100,
  SHIP_SLOT_ITEMS_EQUIP = 2101,
  // SAVE_FLEET_PRESET = 2102, town -> common
  // DELETE_FLEET_PRESET = 2103, town -> common
  //APPLY_FLEET_PRESET = 2104, town -> common
  //LOAD_FLEET_PRESET = 2105,
  TRADE_RECEIVE_TRADE_AREA_REWARD = 2106,
  SHIPYARD_RELAUNCH_SHIP = 2107,
  CRAZE_EVENT_BUDGET_CHANGED_SC = 2108,
  PUB_RESET_MATES_BY_PAID = 2109,
  PALACE_GET_ROYAL_TITLE_ORDER = 2110,
  GOVER_CHANGE_MAYOR_SHIPYARD_TAX = 2111,
  CHANGE_SHIP_CAMOUFLAGE = 2112,
  SHIPYARD_BUY_IMPROVED_BLUEPRINT = 2113,
  GOVER_REGISTER_MAYOR_TRADE_EVENT = 2114,
  USE_CANAL = 2115,
  COSTUME_SHIP_SLOT_ITEMS_EQUIP = 2116,
  NATION_CABINET_MEMBER_ENTER_NOTICE_SC = 2117,
  ENCHANT_EQUIPMENT = 2118,
  ENCHANT_SHIP_SLOT = 2119,
  SHIP_COMPOSE = 2120,
  NPC_SHOP_BUY = 2121,
  SMUGGLE_ENTER_CONFIRM = 2122,
  CONTACT_NPC = 2123,
  AWAY_NPC = 2124,
  TOWN_SMUGGLE_PRICE_PERCENT_UPDATE_SC = 2125,
  SMUGGLE_GET_SYNC_DATA = 2126,
  SMUGGLE_TRY_BEGIN_NEGO = 2127,
  SMUGGLE_TRY_NEGO = 2128,
  SMUGGLE_BUY = 2129,
  SMUGGLE_SELL = 2130,
  SMUGGLE_RESET_BOUGHT = 2131,
  SHIPYARD_RESET_ENCHANT_COUNT = 2132,

  FRIENDLY_ENCOUNT = 2133,
  FRIENDLY_ENCOUNT_SC = 2134,
  FRIENDLY_ENCOUNT_CHOICE = 2135,
  FRIENDLY_ENCOUNT_END_SC = 2136,
  FRIENDLY_ENCOUNT_CANCEL = 2137,

  UNPOPULAR_EVENT_CHANGED_SC = 2138,

  CLASH_REGISTER = 2139,
  CLASH_UNREGISTER = 2140,
  CLASH_UNREGISTER_SC = 2141,
  CLASH_CHOICE = 2142,
  CLASH_FOUND_NOTIFICATION_SC = 2143,
  CLASH_MATCHING_END_SC = 2144,
  CLASH_CLEAR_REJECT_COOL_DOWN = 2145,
}

export enum Ocean {
  ARRIVE = 3001,
  LOAD_COMPLETE = 3002,
  ENTER = 3003,
  // BATTLE_START = 3004, 사용 안함
  // BATTLE_ACTION = 3005, 사용 안함
  // BATTLE_END = 3006, 사용 안함
  // BATTLE_RESUME = 3007, 사용 안함
  // BATTLE_GIVE_UP = 3008, 사용 안함
  ADD_FLEET_SC = 3009,
  REMOVE_OCEAN_ENTITY_SC = 3010,
  UPDATE_ZONE_SYNC_DATA_SC = 3011,
  MOVE_CS = 3012,
  NET_USER_MOVE_SC = 3013,
  INSPECT_TOWN = 3014,
  DEPRECATED_3015 = 3015,
  BEGIN_AUTO_SAILING = 3016,
  END_AUTO_SAILING = 3017,
  UPDATE_AUTO_SAILING = 3018,
  // Notification for the prediction of any disaster
  PREDICT_DISASTER_SC = 3019,
  // Notification for the changing of the sailing days
  CHANGE_SAILING_DAYS_SC = 3020,
  // 재해 해소
  RESOLVE_DISASTER = 3021,
  // 난파 알림
  WRECK_FLEET_SC = 3022,
  // 난파 상태에서 회항
  // SAIL_BACK = 3023,  // 사용 안함.
  // BATTLE_LOAD_COMPLETE = 3024, 사용 안함
  // NPC spawn/despawn
  NPC_SPAWN_SC = 3025,
  NPC_DESPAWN_SC = 3026,
  // NPC 가 싸움 걸어옴.
  ENCOUNT_BY_NPC_SC = 3027,
  // 인카운트 과정에서 유저의 선택.
  ENCOUNT_USER_CHOICE = 3028,
  // 유저가 NPC 에게 인카운트 걸기.
  ENCOUNT_BY_USER = 3029,

  EMERGENCY_RECOVER_SHIPS = 3031,
  GAME_OVER_RESURRECT = 3032,

  GET_USER_TOWN_STATE = 3033,

  UPDATE_SYNC_DATA_SC = 3034, // 항해 중, 서버에서 주도적으로 변경되는 데이터 싱크.
  UPDATE_DISASTER_SC = 3035, // 재해 시작 알림.(미사용. ADD_DISASTER_SC, REMOVE_DISATER_SC로 대체)

  BUFF_SYNC_UPDATE_SC = 3036, // 버프 추가/업데이트.
  BUFF_SYNC_REMOVE_SC = 3037, // 버프 제거.

  // 재해 방어 알림.(클라이언트 미구현, 패킷만 전송)
  DEFENSE_DISASTER_SC = 3038,
  // 인카운트 회피 (클라이언트 미구현, 패킷만 전송)
  ENCOUNT_AVOIDANCE_SC = 3039,

  //인카운트 다이얼로그가 닫혔을 때 호출.인카운트 종료시점을 클라이은트와 맞추기 위함.
  ENCOUNT_END = 3040,

  UPDATE_DAILY_CONSUME_SUPPLIES_SC = 3041, // 항해 중 일일 보급품 소모 업데이트
  BUFF_ACTIVE_EFFECT_SC = 3042, // 버프의 이펙트 효과 동기

  NPC_FLEET_MOVE_SC = 3043,
  NPC_FLEET_STOP_SC = 3044,

  ZOOM_IN_NPC = 3045,
  ZOOM_IN_USER = 3046,

  // 보험금 납입.
  PAY_INSURANCE_SC = 3047,

  REVEAL_WORLD_MAP_TILE = 3048,

  DISCOVER_VILLAGE = 3049,

  NPC_FLEET_MOVE_SYNC_SC = 3050,

  // Doodad(해양오브젝트) spawn/despawn
  DOODAD_SPAWN_SC = 3051,
  DOODAD_DESPAWN_SC = 3052,

  NET_USER_CHANGE_STATE_SC = 3053,

  ADD_QUESTION_PLACE = 3054,

  ACTIVATE_OCEAN_DOODAD = 3055,

  LAND_ENTER = 3056,
  LAND_LEAVE = 3057,
  LAND_EXPLORE = 3058,
  LAND_START_CS = 3059,

  NET_USER_CHANGE_QUEST_SINGLE_MODE_SC = 3060,

  DOODAD_SYNC_UPDATE_SC = 3061,
  NET_USER_CHANGE_LAND_ANCHOR_SC = 3062,
  NET_USER_CHANGE_CUSTOMIZE_SC = 3063,

  INSPECT_VILLAGE = 3064,
  VILLAGE_ENTER = 3065,
  VILLAGE_LEAVE = 3066,
  NET_USER_VILLAGE_ENTER_SC = 3067,

  // 엘리트몬스터의 위치를 권역 내 모든 유저들에게 전달.
  WORLD_MAP_NPC_LOCATION_SC = 3069,

  // 해상가호 업데이트(시작/해제).(미사용. ADD_PROTECTION_SC, REMOVE_PROTECTION_SC로 대체)
  UPDATE_PROTECTION_SC = 3070,

  // 특정 위치로 텔레포트
  TELELPORT_TO_LOCATION_SC = 3071,

  // 다른유저가 텔레포트 이동.
  NET_USER_TELEPORT_TO_LOCATION_SC = 3072,

  WORLD_TILE_DEBUFF_NOTICE_SC = 3073,
  ENCOUNT_BY_NET_USER_SC = 3074,

  OFFLINE_SAILING_MOVE_DELEGATE_START = 3075,
  OFFLINE_SAILING_MOVE_DELEGATE_UPDATE_SC = 3076,

  UPDATE_DAILY_TRADE_GOODS_CHANGES_SC = 3077,

  ENCOUNT_CANCEL_SC = 3078,

  SHOW_EMOTICON_INSTANT_CS = 3079,
  SHOW_EMOTICON_INSTANT_SC = 3080,

  // n초 후 출발한 마을앞으로 강제이동 알림.
  TELEPORT_TO_STARTED_TOWN_NOTICE_SC = 3081,

  // 특정 마을 앞 위치로 텔레포트
  TELEPORT_TO_STARTED_TOWN_SC = 3082,

  // 오프라인 항해 중단 처리
  OFFLINE_SAILING_STOP_SC = 3083,

  RESOLVE_DISASTER_BY_DELEGATION_SC = 3084,

  // 상대방이 인카운트 선택시 통보받음
  ENCOUNT_ENEMY_CHOICE_SC = 3086,

  // 강제로 인카운트를 종료.(전투여부는 패킷 데이터 따라 결정)
  ENCOUNT_END_FORCEDLY_SC = 3087,
  // 낚시 시작 패킷
  FISHING_START = 3088,
  FISHING_FIGHTING = 3089,
  FISHING_END = 3090,

  VILLAGE_GIFT = 3091,
  VILLAGE_DRAFT_SAILOR = 3092,
  VILLAGE_PLUNDER = 3093,
  VILLAGE_EXPLORE = 3094,
  ITEM_RESURRECT = 3095,

  ADD_DISASTER_SC = 3096, // 재해 시작 알림.
  REMOVE_DISASTER_SC = 3097, // 재해 종료 알림.

  ADD_PROTECTION_SC = 3098, // 가호 시작 알림.
  REMOVE_PROTECTION_SC = 3099, // 가호 종료 알림.
  ARRIVE_VILLAGE = 3100,

  NET_USER_CHANGE_COMPANY_LEVEL_SC = 3101,
  NET_USER_CHANGE_KARMA_SC = 3102,

  // 선박 난파(함대난파 아님)
  SHIP_WRECKED_SC = 3103,
  // 해역 밝히기
  REVEAL_REGION = 3104,

  REVEAL_OCEAN_DOODADS = 3105,
  DISCOVER_OCEAN_DOODAD = 3106,
  LAND_EXPLORE_QUERY_FEATURE = 3107,

  NET_USER_GUILD_LEAVE_SC = 3109,
  NET_USER_GUILD_UPDATE_SC = 3110,

  REDUCE_SHIP_LIFE_WHILE_SAILING_SC = 3111,
  UPDATE_SAIL_SPEED_SC = 3113,

  ELITE_NPC_SPAWN_NOTIFICATION_SC = 3114,
  // 항해 경로에 의한 오프라인 자동 항해중 일괄 보급/수리
  APPLY_WAYPOINT_SUPPLY = 3115,

  UPDATE_AUTO_SAIL_OPTION_FOR_SERVER = 3116, // 자동항해 정보의 서버전용 부가정보 업데이트

  SEARCH_CONSTELLATION = 3117, // 별자리 미니게임 시작
  DISCOVER_CONSTELLATION = 3118, // 별자리 발견물 획득

  NET_USER_CHANGE_USER_TITLE_SC = 3119,

  VILLAGE_EXCHANGE = 3120,
  VILLAGE_EXCHANGE_TRY_NEGO = 3121,
  VILLAGE_FRIENDSHIP_REWARD = 3122,
  UPDATE_DAILY_GET_WATER_SC = 3123, // 항해 중 물 획득

  DELEGATE_NAVI_QUERY_SC = 3124,
  DELEGATE_NAVI_QUERY_RESULT = 3125,
  SWEEP_LOCAL_NPC = 3126,

  NET_USER_NATION_CABINET_LEAVE_SC = 3127,
  NET_USER_NATION_CABINET_UPDATE_SC = 3128,

  SWEEP_RAID = 3129,

  SALVAGE_ENTER = 3130,
  SALVAGE_GAME_START = 3131,
  SALVAGE_GAME_END = 3132,
  SALVAGE_LEAVE = 3133,
  NET_USER_SALVAGE_ENTER_SC = 3134,

  GET_EXCHANGE_INFO = 3135,

  ENTER_CONTINUOUS_SWEEP_REWARD = 3136,
  LEAVE_CONTINUOUS_SWEEP_REWARD = 3137,

  VILLAGE_GIFT_PAID = 3138, // 마을 우호도 유료회복
}

export enum Battle {
  ACTION = 4001,
  END = 4002,
  LOAD_COMPLETE = 4003,
  RESUME = 4004,
  START = 4005,
  START_CHALLENGE = 4006,
  LOSE = 4007, // 도전 전투 패배 이후 '나가기'.
  END_RAID = 4008,
  CANCEL = 4009,
  QUEST_RESUME_BLOCK = 4010, // 전투 퀘스트의 블럭 이어하기
  START_ARENA = 4011,
  START_RAID = 4012,
  END_CHALLENGE = 4013,
  MULTI_ACTION_SC = 4014,
  MULTI_STATE_SYNC_SC = 4015,
  MULTI_ACTION_USER_CHANGE_CS = 4016,
  START_GUILD_RAID = 4017,
  END_MULTI_PVP = 4018,
  MULTI_SYNC_INIT = 4019,
  MULTI_CTRL_REQUEST = 4020,
  MULTI_AUTO_CHANGE_REQUEST_CS = 4021,
  MULTI_ACTION_REQUEST = 4022,
  MULTI_ACTION_REQUEST_SC = 4023,
  MULTI_TIMEOUT_NOTIFICATION = 4024,
  MULTI_ACTION_PHASE_CONFIRM_CS = 4025,
  MULTI_GIVE_UP_CS = 4026,
  MULTI_ACTION_PHASE_PASS_CS = 4027,
  START_INFINITE_LIGHT_HOUSE = 4028,
  END_INFINITE_LIGHT_HOUSE = 4029,
  END_FRIENDLY = 4030,
  START_CLASH = 4031,
  END_CLASH = 4032,
}

export enum Duel {
  START = 4501,
  ACTION = 4502,
  END = 4503,
  RESUME = 4504,
}

export enum Common {
  CREATE_FIRST_MATE = 5001,
  EQUIP_MATE_EQUIPMENT = 5002,
  QUERY_NATION = 5003,
  SELECT_NATION = 5004,
  CHANGE_NATION = 5005,
  GET_USER_LIGHT_INFOS = 5006,
  CHANGE_COMPANY_JOB = 5007,
  CHANGE_SHIP_NAME = 5009,
  RELOCATE_SAILOR = 5010,
  REMOVE_MATE_EQUIPMENT = 5011,
  REMOVE_ITEM = 5012,
  EXPAND_INVENTORY_SLOT = 5013,
  RECEIVE_MAILS = 5014,
  QUEST_ACCEPT = 5015,
  QUEST_START_BLOCK = 5016,
  QUEST_ENTRUST = 5017,
  QUEST_END_BLOCK = 5018,
  QUEST_REG_STORE = 5019,
  NEW_MAIL_SC = 5020,
  DELETE_MAILS = 5021,
  READ_MAILS = 5022,
  REMOVE_CARGO = 5023,
  RELOAD_CARGO = 5024,
  GET_TOWN_TRADE_PRICE_PERCENTS = 5025,
  QUEST_DROP = 5026,
  USE_ITEM = 5027,
  QUEST_GOTO = 5028,
  LOCK_SHIP_SLOTS = 5029,
  EQUIP_MATE_EQUIPMENTS = 5030,
  BUY_ADMIRAL = 5031,
  BUFF_UPDATE_SC = 5032,
  MATE_STATE_UPDATE_SC = 5033,
  RECOVER_INJURY = 5034,
  INCREASE_ROYALTY = 5035,
  MEET_MATES = 5036,
  TALK_MATES = 5038,
  SET_CARGO_LOAD_PRESET = 5039,
  QUEST_OPERATE_REG = 5040,
  RECOVER_SHIPS = 5041,
  RESET_TASK = 5042,
  RECEIVE_ACHIEVEMENT_REWARD = 5043,
  RECEIVE_TASK_REWARD = 5044,
  UPDATE_TRADE_EVENT_SC = 5045,
  GET_TOWN_NATION_SHARE_POINTS = 5046,
  COMPLETE_TASK_IMMEDIATELY = 5047,
  RECEIVE_TASK_CATEGORY_REWARD = 5048,
  RECEIVE_ACHIEVEMENT_POINT_REWARD = 5049,
  RECEIVE_HOT_TIME = 5050,
  BUY_TAX_FREE_PERMIT = 5051,
  GET_WORLD_MAP_TOWN_INFO = 5052,
  CONSUME_QUEST_ENERGY = 5053,
  EXPAND_REQUEST_SLOT = 5054,
  GET_NATION_TOWNS = 5055,
  REMOVE_EXPIRED_REQUEST_SLOT = 5056,
  CASH_SHOP_GET_PRODUCTS = 5057,
  CASH_SHOP_BUY_WITHOUT_PURCHASE = 5058,
  GET_REGION_OCCUPATIONS = 5059,
  CASH_SHOP_RECEIVE_GACHA_BOX_GUARANTEE = 5060,
  CHAT_INIT = 5061,
  UPDATE_ACHIEVEMNT_SC = 5063,
  GET_USER_LIGHT_INFO_BY_NAME = 5064,
  GET_USER_LIGHT_INFOS_ONLY_IS_ONLINE = 5065,
  ALLOW_RX_TEXT_CHAT_ON_CELLULAR_DATA_NET = 5066,
  UNLOCK_SHIP_CUSTOMIZING = 5067,
  CUSTOMIZE_SHIP = 5068,
  CHAT_JOIN_CHANNEL = 5069,
  MATE_START_AWAKEN = 5070,
  MATE_START_LEARN_PASSIVE = 5071,
  QUEST_ITEM_SET_QUEST = 5072,
  QUEST_ITEM_USE = 5073,
  // MATE_LEARN_ORDER = 5074, -- 기획 변경으로 인해 사용 안함.
  ATTENDANCE = 5075,
  MATE_EQUIP_PASSIVES = 5076,
  QUEST_SET_PAUSE = 5077,
  CUSTOMIZE_MATE_EQUIP = 5078,
  UNLOCK_MATE_EQUIP_COLOR = 5079,
  PASSIVE_UPDATE_SC = 5080,
  BUBBLE_EVENT_NEW_SC = 5081,
  BUBBLE_EVENT_REMOVE_SC = 5082,
  BUBBLE_EVENT_ACTION = 5083,
  //SHIP_SLOT_ITEM_EQUIP = 5084, //n 개를 처리하는 SHIP_SLOT_ITEMS_EQUIP 추가로 사용 안함.
  SHIP_SLOT_ITEM_SELL = 5085,
  SHIP_SLOT_ITEM_REMOVE = 5086,
  // QUEST_ITEM_DROP = 5087, removeItem 에서 모두 처리.
  SET_GAME_OPTION_PUSH_NOTIFICATION_CS = 5088,
  GET_GAME_OPTION_PUSH_NOTIFICATION = 5089,
  CASH_SHOP_SYNC_SC = 5090,
  TOOGLE_ENCOUNT_SHIELD_ACTIVATION = 5091,
  UPDATE_ENCOUNT_SHIELD_SC = 5092,
  NEW_LINE_MAIL_SC = 5093,
  MATE_INJURY_BUFF_SC = 5094,
  BATTLE_FORMATION_ACQUIRE = 5095,
  BATTLE_FORMATION_CHANGE = 5096,
  QUERY_NEAREST_TOWN = 5097,
  USE_WORLD_SKILL = 5098,
  REGISTER_COLLECTION = 5099,
  SHIPYARD_SHIP_BUILDING_DECREASE_EXPIRE_TIME = 5100,
  SHIPYARD_SHIP_BUILDING_COMPLETE_EXPIRE_TIME = 5101,
  SHIPYARD_SHIP_BUILDING_DELIVER = 5102,
  RECEIVE_EVENT_MISSION_REWARD = 5103,
  MATE_COMPLETE_AWAKEN = 5104,
  MATE_COMPLETE_AWAKEN_IMMEDIATELY = 5105,
  MATE_REDUCE_AWAKEN_TIME = 5106,
  FIND_DISTANCE_FROM_TILE_TO_TOWN = 5107,
  GET_ADMIRAL_PROFILE = 5108,
  GET_FLAG_SHIP_PROFILE = 5109,
  SET_OPTION_PROFILES = 5110,
  MATE_COMPLETE_LEARN_PASSIVE = 5111,
  MATE_COMPLETE_LEARN_PASSIVE_IMMEDIATELY = 5112,
  MATE_REDUCE_LEARN_PASSIVE_TIME = 5113,
  AUCTION_LOAD_MY_PRODUCTS = 5114,
  AUCTION_REGISTER = 5115,
  AUCTION_QUERY_PRODUCTS = 5116,
  AUCTION_BUY = 5117,
  AUCTION_CANCEL = 5118,
  AUCTION_QUERY_SALE_PRICES = 5119,
  AUCTION_REREGISTER = 5120,
  AUCTION_RECEIVE_PROCEEDS = 5121,
  REFRESH_WEEKLY_EVENT = 5122,
  UPDATE_ADJUTANT_DELEGATION_CONFIG = 5123,
  AUCTION_UPDATE_MY_PRODUCT_STATE_SC = 5124,
  REPAIR_DURABILITY_SHIPS = 5125,
  AUCTION_QUERY_SHIP_PRODUCTS = 5126,
  BILLING_QUERY_SALES_LIST = 5127,
  BILLING_RESERVE_PURCHASE = 5128,
  BILLING_CANCEL_RESERVED_PURCHASE = 5129,
  BILLING_COMPLETE_RESERVED_PURCHASE_AND_GIVE = 5130,
  BILLING_QUERY_PURCHASE_DETAIL = 5131,
  BILLING_QUERY_PRODUCT_GIVE_ITEM_DETAIL = 5132,
  BILLING_CHARGE_BY_PURCHASE_PRODUCT = 5133,
  SET_PING_TIME_OUT = 5134,
  ARENA_ENTER = 5135,
  ARENA_MATCH_LIST_REFRESH = 5136,
  ARENA_TICKET_BUY = 5137,
  ARENA_REWARD_RECEIVE = 5138,
  ARENA_UPDATE_SC = 5139,
  ARENA_TICKET_UPDATE_SC = 5140,
  ARENA_FLEET_UPDATE = 5141,
  QUERY_MY_MAYOR_TOWNS = 5142,
  UPDATE_SHIELDS = 5144,
  QUERY_VILLAGE = 5145,
  PROLOGUE_START = 5146,
  INCREASE_LOYALTY_USE_ITEM = 5147,
  QUERY_REPORTED_WORLD_MAP_TILES = 5148,
  CASH_SHOP_BUY_DAILY_PRODUCT = 5149,
  QUERY_ACHIEVEMENTS = 5150,
  CASH_SHOP_GET_DAILY_SALE = 5151,
  QUERY_RED_GEM_DETAIL = 5152,
  AUCTION_QUERY_CLOSED = 5153,
  QUERY_LOCKED_TOWN_TRADE_GOODS = 5154,
  RECEIVE_PASS_EVENT_MISSION_REWARD = 5156,
  BUY_PASS_EVENT_EXP = 5157,
  BILLING_COMPLETE_RESERVED_PURCHASE = 5158,
  BILLING_QUERY_INVEN_PURCHASE_LIST = 5159,
  BILLING_RECEIVE_INVEN_PURCHASES = 5160,
  EVENT_SHOP_BUY = 5161,
  EVENT_SHOP_GET_PRODUCTS = 5162,
  BILLING_QUERY_LATEST_RESERVED_PURCHASE = 5163,
  REPORT_BAD_CHATTING = 5164,
  GET_REPORTED_BAD_CHATTING_LIST = 5165,
  CHAT_MUTE_USER = 5166,
  CHAT_UNMUTE_USER = 5167,
  SET_SAIL_WAYPOINT = 5168,
  QUERY_ALL_TOWN_INVESTMENTS = 5169,
  REMOVE_SAIL_WAYPOINT = 5170,
  QUERY_TRADE_AREA = 5171,
  CHANGE_FLEET_PRESET_NAME = 5172,
  LOAD_FLEET_PRESET = 5173,
  ADD_MATE_EXP_USE_ITEM = 5174,
  LOCK_SHIP = 5175,
  EVENT_GET_MINI_BOARD_GAME = 5176,
  EVENT_PLAY_MINI_BOARD_GAME = 5177,
  HIDE_EQUIP_SLOTS = 5178,
  BILLING_STEAM_PURCHASE_INIT_TXN = 5179,
  QUERY_CRAZE_EVENT_BUDGET = 5180,
  SAVE_FLEET_PRESET = 5181,
  DELETE_FLEET_PRESET = 5182,
  FLEET_DISPATCH_START = 5183,
  FLEET_DISPATCH_CANCEL = 5184,
  FLEET_DISPATCH_END = 5185,
  FLEET_DISPATCH_QUERY_IN_PROGRESS_STATE = 5186,
  FLEET_DISPATCH_REWARD_CHOICE = 5187,
  FLEET_DISPATCH_REWARD_RECEIVE = 5188,
  FLEET_DISPATCH_SLOT_OPEN = 5189,
  FLEET_DISPATCH_DECREASE_EXPIRE_TIME = 5190,
  GET_WORLD_RANKING = 5191,
  MATES_COMPLETE_AWAKEN = 5192,
  MATES_COMPLETE_LEARN_PASSIVE = 5193,
  TOGGLE_BATTLE_CONTINUOUS = 5194,
  UPDATE_BATTLE_CONTINUOUS_RESULT = 5195,
  TRANSLATE_CHAT = 5196,
  BUY_CHAT_TRANSLATION_COUNT = 5197,
  BUY_ATTENDANCE = 5198,
  RECEIVE_DAILY_SUBSCRIPTION_REWARD = 5199,
  PUBLISH_TOAST_MESSAGE_CS = 5200,
  BROADCAST_TOAST_MSG_SC = 5201,
  MATE_START_TRAINING = 5202,
  MATES_COMPLETE_TRAINING = 5203,
  MATE_COMPLETE_TRAINING_IMMEDIATELY = 5204,
  MATE_REDUCE_TRAINING_TIME = 5205,
  MATE_USE_TRAINING_POINTS = 5206,
  MATE_RESET_TRAINING_POINTS = 5207,
  OPEN_HOT_SPOT_PRODUCT = 5208,
  GET_WORLD_EVENT_RANKING = 5209,
  RECEIVE_EVENT_RANKING_MISSION_REWARD = 5210,
  RECEIVE_EVENT_RANKING_REWARD = 5211,
  SHIP_SLOT_ITEM_LOCK = 5212,
  RECEIVE_DISCOVERY_REWARD = 5213,
  GET_SAILING_DIARIES = 5214,
  EQUIP_MATE_ILLUST = 5215,
  RECEIVE_FISH_SIZE_REWARDS = 5216,
  QUERY_ALL_INVESTED_TOWNS_GUILD_SHARE_POINTS = 5217,
  QUERY_GUILD_TOWNS = 5218,
  QUERY_GUILD_INVESTMENT_USER_SCORES = 5219,
  UPDATE_HOT_TIME_BUFF = 5220,
  QUERY_TOWNS_FIRST_GUILD_SHARE_POINT = 5221,
  CHANGE_USER_TITLE = 5222,
  EQUIP_BATTLE_QUICK_SKILL = 5223,
  UPDATE_USER_TITLE_SC = 5224,
  QUERY_NATION_ELECTION_SYNC = 5225,
  REGISTER_NATION_ELECTION_CANDIDATE = 5226,
  MODIFY_NATION_ELECTION_CANDIDATE_DATA = 5227,
  VOTE_TO_NATION_ELECTION_CANDIDATE = 5228,
  APPLY_FLEET_PRESET = 5229,
  SHIP_BUILDING_CANCEL = 5230,
  RECEIVE_NATION_ELECTION_VOTE_REWARD = 5231,
  RECEIVE_NATION_GOAL_PROMISE_REWARD = 5232,
  CHANGE_CARGO_PRESET_NAME = 5233,
  CHANGE_EQUIPMENT_TO_COSTUME = 5234,
  CHANGE_CARGO_PRESET_ID_IN_FLEET_PRESET = 5235,
  SHIP_BUILDING_TERMS_INFO = 5236,
  REMOTE_CREATE_SHIP = 5237,
  UPDATE_NATION_POLICIES_SC = 5238,
  UPGRADE_NATION_POLICY_STEP = 5239,
  DONATE_TO_NATION_BUDGET = 5240,
  QUEST_STATE_CHANGE = 5241,
  BUY_NATION_SUPPORT_SHOP = 5242,
  NATION_SUPPORT_SHOP_BOUGHT_SC = 5243,
  RECEIVE_NATION_SUPPORT_SHOP_REWARD = 5244,
  ENABLE_LAST_WEEK_INVESTMENT_REWARD = 5245,
  GET_NATION_WEEKLY_DONATION_RANKS = 5246,
  RECEIVE_NATION_WEEKLY_DONATION_RANK_REWARD = 5247,
  RECEIVE_LAST_WEEK_INVESTMENT_REWARD = 5248,
  JOIN_TO_NATION_CABINET_APPLICANT = 5249,
  APPOINT_NATION_CABINET_MEMBER = 5250,
  DISMISS_NATION_CABINET_MEMBER = 5251,
  WRITE_NATION_NOTICE = 5252,
  SET_NATION_WAGE_RATE = 5253,
  LOAD_NATION_CABINET_APPLICANTS = 5254,
  NATION_ELECTION_UPDATE_SC = 5255,
  WRITE_NATION_PRIME_MINISTER_THOUGHT = 5256,
  // SET_FLEET_PRESET_IS_OPEN = 5257,
  // SET_FLEET_PRESET_IS_LOCKED = 5258,
  GET_OPENED_FLEET_PRESET = 5259,
  SET_REPRESENTED_MATE = 5260,
  QUEST_REG_UTCTIME = 5261,
  GET_MARKET_EVENT_INFO = 5262,
  SET_SIDEKICK_MATE = 5263,
  SET_SIDEKICK_PET = 5264,
  RECEIVE_EVENT_RANKING_GUILD_REWARD = 5265,
  QUERY_ENCHANT_TOAST_CS = 5266,
  SET_FAVORITE_MATE = 5267,

  GET_MY_BLIND_BID_INVEN = 5268,
  TRY_BLIND_BID = 5269,
  GET_BLIND_BID_COMPLETED_PRODUCTS = 5270,
  RECEIVE_BLIND_BID_WINNER_REWARD = 5271,
  BLIND_BID_TICKET_BUY = 5272,
  REFUND_BLIND_BID = 5273,
  MATE_START_TRANSCENDENCE = 5274,
  MATE_COMPLETE_TRANSCENDENCE = 5275,
  MATE_COMPLETE_TRANSCENDENCE_IMMEDIATELY = 5276,
  GET_INFINITE_LIGHT_HOUSE_FRIEND_CLEARED_INFO = 5277,
  SWEEP_INFINITE_LIGHT_HOUSE_STAGE = 5278,
  GET_USER_FIRST_FLEET_INFOS = 5279,
  GET_USER_FIRST_FLEET_INFO_BY_NAME = 5280,
  SET_OPTION_FRIENDLY_BATTLE = 5281,
  QUERY_UNPOPULAR_EVENT = 5282,
  SET_OPTION_FLEET_PRESET = 5283,
  RESEARCH_CANCEL = 5284,
  RESEARCH_REPORT = 5285,
  RESEARCH_START = 5286,
  RESEARCH_RESET = 5287,
  GET_INVESTMENT_SEASON_RANKING = 5288,

  BOUGHT_WEB_SHOP_PRODUCT_SC = 5289,

  CLEAR_REENTRY_COOLDOWN = 5290,
  GET_CLASH_PAGE = 5291,
  RECEIVE_CLASH_REWARD = 5292,
  GET_MY_INVESTMENT_SEASON_NATION_RANKING_SCORE = 5293,
  CHECK_LAST_SEASON_INVESTMENT_RANK_REWARDABLE = 5294,
  RECEIVE_LAST_SEASON_INVESTMENT_RANK_REWARD = 5295,

  AUTO_CHANGE_ITEMS_TRY = 5296,
  AUTO_CHANGE_ITEMS_HISTORY_GET = 5297,
  AUTO_CHANGE_ITEMS_HISTORY_RESET = 5298,
}

export enum BattleReward {
  RECEIVE_BATTLE_REWARD = 6001,
  LEAVE = 6003,
  ENTER = 6004,
  LOAD_COMPLETE = 6005,
}

export enum LandExploreReward {
  OPEN_BOX = 7001,
  RECEIVE_ENTER = 7002,
  RECEIVE_LEAVE = 7003,
  RECEIVE = 7004,
}

export enum Etc {
  PING_CS = 5,
  PONG_SC = 6,
  SET_CLIENT_BACKGROUND_STATE_CS = 7,
  SET_CLIENT_BACKGROUND_STATE_SC = 8,
}

export enum Admin {
  TELEPORT_TOWN = 8001,
  TELEPORT_TO_USER = 8002,
}

export enum Dev {
  GIVE_MATE_EQUIPMENT = 9001,
  ADD_POINT = 9002,
  ADD_MATE = 9003,
  SET_SHIP_SAILOR = 9004,
  ADD_SHIP = 9005,
  UPDATE_NATION = 9006,
  UPDATE_NATION_INTIMACY = 9007,
  SET_NATION = 9008,
  UPDATE_REPUTATION = 9009,
  INVOKE_EVENT = 9010,
  INVOKE_NATION_INTIMACY_UPDATE_JOB = 9011,
  REMOVE_POINT = 9012,
  SET_POINT = 9013,
  SET_SHIP_DURABILITY = 9014,
  OCEAN_MOVE_TO_TOWN_CC = 9015, // move to town 치트의 경우 치트 api 가 따로 존재하지 않고 arrive 사용
  REMOVE_ALL_SHIP_CARGOS = 9016,
  SET_FIRST_SHIP_CARGO = 9017,
  ADD_ITEM = 9018,
  SET_DEVELOPMENT_LEVEL = 9019,
  SET_MATE_FAME = 9020,
  SET_MATE_ROYAL_TITLE = 9021,
  ADD_DIRECT_MAIL_CS = 9022,
  ADD_INSTALLMENT_SAVINGS_LAST_DEPOSIT_TIME = 9023,
  SET_USER_LEVEL = 9024,
  QUEST_SET_FLAGS = 9025,
  QUEST_SET_REGISTER = 9026,
  QUEST_SET_TEMP_REGISTER = 9027,
  QUEST_SET_COMPLETED = 9028,
  QUEST_SET_NODE_IDX = 9029,
  PREDICT_DISASTER = 9030,
  GENERATE_DISASTER = 9031,
  RESOLVE_DISASTER = 9032,
  UNLIMITED_INVEST = 9033,
  SET_IGNORE_NPC_ENCOUNT_CS = 9034,
  STAT_DUMP = 9035,
  STAT_SET = 9036,
  ADD_NEAR_SPAWNER = 9037,
  WORLD_BUFF_ADD_CS = 9038,
  WORLD_BUFF_REM_CS = 9039,
  DISCONNECT_SERVER_CS = 9040,
  ENCOUNT_NPC_ATT_CHOICE = 9041,
  ENCOUNT_NPC_DEF_CHOICE = 9042,
  CHANGE_NPC_ATTACK_RADIUS = 9043,
  TRADE_DUMP = 9044,
  CHANGE_NPC_TICK_PER_SEC = 9045,
  REMOVE_NEAR_SPAWNER = 9046,
  SET_LOYALTY = 9047,
  RESET_PUB_MATES = 9048,
  ADD_ALL_MATES = 9049,
  GET_NPC_LOCATION_BY_OCEAN_NPC_AREA_SPAWNER = 9050,
  ADD_OCEAN_DOODAD_NEAR_SPAWNER = 9051,
  REMOVE_OCEAN_DOODAD_NEAR_SPAWNER = 9052,
  QUEST_FORCE_EXEC = 9053,
  SET_LOCAL_NPC_SPAWN = 9054,
  SET_LOCAL_DOODAD_SPAWN = 9055,
  SET_DISASTER_LUCK = 9056,
  GENERATE_PROTECTION = 9057,
  RESOLVE_PROTECTION = 9058,
  TELEPORT_TO_LOCATION_CS = 9059,
  PREDICT_PROTECTION = 9060,
  SET_MATE_LEVEL = 9061,
  ADD_USER_DATA_NPC_SPAWNER = 9062,
  REVEAL_ALL_WORLD_MAP_TILES = 9063,
  DISCOVER_ALL_TOWNS = 9064,
  ADD_ALL_TOWNS_TO_QUESTION_PLACE = 9065,
  WORLD_PASSIVE_ADD_CS = 9066,
  WORLD_PASSIVE_REM_CS = 9067,
  DEBUFF_IMMUNE_CS = 9068,
  DISASTER_IMMUNE_CS = 9069,
  SERVER_DEBUG_MSG_SC = 9070,
  SHOW_DISASTER_STAT_CS = 9071,
  SET_TRADE_GOODS_BREED_SUCCESS_CS = 9072,
  DISCOVER = 9073,
  BATTLE_RESUME_FOR_USER_ID = 9074, //모의 전투에서만 사용. 특정 userId의 최근 전투로 이어하기.
  GIVE_ALL_MATE_EQUIPMENTS = 9075,
  DISCOVER_ALL = 9076,
  SET_USER_KARMA = 9077,
  SET_USER_COMPANY_JOB = 9078,
  SET_MATE_INJERY = 9079,
  ATTACK_TO_ME_CS = 9080,
  ADD_BATTLE_FORMATION = 9081,
  SPECIAL_STAT_DUMP = 9082,
  SET_MATE_AWAKEN = 9083,
  PUB_STAFF_RESET = 9084,
  SET_TOWN_NATION_SHARE_POINT_CS = 9085,
  I_AM_MAYOR_CS = 9086,
  CHANGE_MAYOR_TAX = 9087,
  EASY_LANGUAGE = 9088,
  CHANGE_SPAWNED_LOCAL_NPC_NUM_CS = 9089,
  SET_SAILING_DAYS_ONOFF_CS = 9090,
  ADD_PARTS = 9091,
  RESET_COLLECTION = 9092,
  SET_MATE_TALK_WAIT_TIME = 9093,
  LOAD = 9094,
  RESET_PALACE_ROYAL_ORDER = 9095,
  QUEST_SET_GLOBAL_REGISTER = 9096,
  UNLOCK_ALL_MATE_AWAKEN_AND_SKILL = 9097,
  RESET_GUILD_LEFT_TIME_TEMPORARILY = 9098,
  SET_GUILD_UPGRADE_POPUP = 9099,
  SET_ARENA_SCORE = 9100,
  SET_ARENA_TICKET_COUNT = 9101,
  SET_ARENA_TICKET_BOUGHT_COUNT = 9102,
  SET_ARENA_MATCH_LIST_REFRESH_COUNT = 9103,
  ADD_GUILD_POINT = 9104,
  RESET_ARENA_DATA = 9105,
  RESET_CASH_SHOP_DAILY_PRODUCTS = 9106,
  SET_LEADER_MATE_SWITCH_COUNT = 9107,
  QUEST_SET_ADMIN_PAUSE = 9108,
  GENERATE_ARENA_DUMMY_USERS = 9109,
  SET_SHIP_BUILD_LEVEL = 9110,
  SET_USER_SHIP_BUILD_LEVEL = 9111,
  SET_VILLAGE_FRIENDSHIP = 9112,
  SET_DEBUG_MSG_FOR_ENCOUNT_BY_NPC_CS = 9113,
  SHOW_SPAWNED_NPC_COUNT_CS = 9114,
  INIT_EXPLORE_TICKET = 9115,
  RESET_GUILD_SHOP = 9116,
  SET_EVENT_PAGE_PRODUCT = 9117,
  SET_PASS_EVENT_EXP = 9118,
  ESTIMATED_SAILING_TIME = 9119,
  FIXED_SPEED_CS = 9120,
  DISABLE_SPEED_HACK_CS = 9121,
  SET_EXPLORE_TIME_CHECK = 9122,
  RESET_GUILD_DATE = 9123,
  SET_SHIP_LIFE = 9124,
  ADD_ALL_SHIPS = 9125,
  ADD_ALL_PARTS = 9126,
  NOTICE_RAID = 9127,
  ADD_RAID_DAMAGE = 9128,
  GET_RAID_STATE = 9129,
  SET_RAID_SCHEDULE_END = 9130,
  SET_TRADE_POINT = 9131,
  RESET_WORLD_SKILL = 9132,
  SET_NATION_LAST_UPDATE_TIME = 9133,
  // KILL_RAID_BOSS = 9134,
  QUEST_RESET_ALL_DAILY_LIMIT_COMPLETED_COUNT = 9135,
  SET_MATE_STATE = 9136,
  ADD_FLEET = 9137,
  SET_SHIP_SAILMASTERY_LEVEL = 9138,
  MAKE_DIPATCH_END = 9139,
  ADD_ALL_ITEMS = 9140,
  RESET_WAYPOINT_SUPPLY_TICKET = 9141,
  SET_SHIP_SAILMASTERY_EXP = 9142,
  RESET_FOR_TIME_TRAVEL = 9143,
  UNLINK_COMPANY = 9144,
  CLEAR_DISCOVER = 9145,
  REMOVE_ALL_ITEMS = 9146,
  REMOVE_UNEQUIP_EQUIPMENTS = 9147,
  REMOVE_UNEQUIP_PARTS = 9148,
  ADD_RELEASED_MATES = 9149,
  ADD_ALL_MATES_AWAKEN = 9150,
  SET_ALL_MATES_LEVEL = 9151,
  RECOVER_CHAT_TRANSLATION = 9152,
  SHOW_DISPATCH_ACTION_RESULT_STAT_CS = 9153,
  CHANGE_GUILD_SYNTHESIS_PROB_GREAT_SUCCESS = 9154,
  RESET_GUILD_RAID_TICKET = 9155,
  SET_GUILD_RAID_SCHEDULE_END = 9156,
  ADD_GUILD_RAID_DAMAGE = 9157,
  ADD_GUILD_RESOURCE = 9158,
  RESET_GUILD_RAID_OPEN_TIME = 9159,
  RESET_GUILD_RAID_BATTLE_N_REWARD_HISTORY = 9160,
  QUEST_SET_ACCUM = 9161,
  ADD_SHIP_CAMOUFLAGE = 9162,
  ADD_MATE_ILLUST = 9163,
  SET_USER_LAST_LOGIN_TIME_DAYS_AGO = 9164,
  SET_USER_ATTENDANCE = 9165,
  ADD_USER_TITLE = 9166,
  RESET_SESSION_RANKING = 9167,
  REMOVE_NATION_ELECTION_CANDIDATE = 9168,
  RESERVE_MAYOR_TRADE_EVENT = 9169,
  DELETE_MAYOR_TRADE_EVENT = 9170,
  CHANGE_NATION_SESSIONID = 9171,
  RECEIVE_PRODUCTS = 9172,
  SET_SWEEP_TICKET = 9173,
  RESET_EXCHANGE_HISTORY = 9174,
  SET_VILLAGE_EVENT = 9175,
  SET_NATION_POLICY = 9176,
  SET_NATION_BUDGET = 9177,
  DELETE_ALL_DIRECT_MAILS = 9178,
  GET_NATION_ACCUMULATED_TAX = 9179,
  SET_QUEST_PASS = 9180,
  I_AM_PRIME_MINISTER = 9181,
  RESET_NATION_CABINET_LAST_APPOINTED_TIMES = 9182,
  RESET_MAYOR_REMOTE_INVEST = 9183,
  RESET_REMOTE_INVEST_COUNT = 9184,
  ADD_PET = 9185,
  RESET_BLACK_MARKET_REFRESH_COUNT = 9186,
  OPEN_HOT_SPOT_BUYABLE_PRODUCTS = 9187,
  TEST = 9188,
  RESET_HOT_SPOT_PRODUCTS_HISTORY = 9189,
  CHANGE_INFINITE_LIGHTHOUSE_CLEARED_INFO_SESSION = 9190,
  ADD_ALL_PUB_MATES = 9191,
  CLEAR_INFINITE_LIGHTHOUSE_STAGE = 9192,
  RESET_MY_FIRST_FLEET_INFO = 9193,
  RESERVE_UNPOPULAR_TRADE_EVENT = 9194,
  DELETE_UNPOPULAR_TRADE_EVENT = 9195,
  UPDATE_RESEARCH = 9196,
  SET_CLASH_SCORE_AND_WINSTREAK = 9197,
  ACTIVE_CLASH_BATTLE_RECORD = 9198,
  GENERATE_TOWN_USER_WEEKLY_INVESTMENT_SCORE = 9199,
  // 중국 전용 채팅 관련
  CHAT_SEND = 9200,
  CHAT_RECV = 9201,
  SET_TUTORIAL_CRAZE_EVENT_BUDGET = 9202,
  TRY_FORCE_ADD_CRAZE_EVENT = 9203,

  DISCOVER_ALL_FILTERED = 9300,
  SET_MANUFACTURE_LEVEL = 9301,
}

export enum Temp {
  BUTTON_LOG_CS = 10001, // [TEMP] CBT 까지 사용 후 제거
}

export enum Guild {
  CHECK_DUPLICATE_NAME = 11001,
  CREATE = 11002,
  DISBAND = 11003,
  SHOW_LIST = 11004,
  JOIN = 11005,
  JOIN_CANCEL = 11006,
  LEAVE = 11007,
  GET_MY_GUILD_INFO = 11008,
  GET_LIGHT_INFO = 11009,
  GET_DETAIL_INFO = 11010,
  CHECKED_UPGRADE_POPUP = 11011,
  GUILD_UPDATE_SYNC_DATA_SC = 11012,
  PICK_UP_DAILY_REWARD = 11013,
  CRAFT = 11014, // no use
  GUILD_RECEIVE_WEEKLY_REWARD_MAIL = 11015,
  CRAFT_CREATE = 11016,
  CRAFT_RECEIVE = 11017,
  CRAFT_DECREASE_EXPIRE_TIME = 11018,
  CRAFT_COMPLETE_EXPIRE_TIME = 11019,

  SYNTHESIS_CREATE = 11020,
  SYNTHESIS_RECEIVE = 11021,
  SYNTHESIS_DECREASE_EXPIRE_TIME = 11022,
  SYNTHESIS_COMPLETE_EXPIRE_TIME = 11023,

  DONATE = 11024, //  상회 기부
  RAID_OPEN = 11025, // 상회 레이드 열기
  STATE_SC = 11026,
  RAID_GET_INFO = 11027,
  RAID_GET_RANKING_PAGE = 11028,
  RAID_PICKUP_REWARD = 11029,
  RAID_BUY_TICKET = 11030,
  RAID_GET_PREV_RANKING = 11031,
  BUY_DONATION_COUNT = 11032,

  // 상회 버프 성향 변경
  SELECT_GUILD_BUFF_CATEGORY = 11033,
  // 상회 버프 습득
  LEARN_GUILD_BUFF = 11034,
  // 버프 업그레이드용 아이템 등록
  REGISTER_GUILD_BUFF_ITEM_FOR_UPGRADE = 11035,

  // 상회 토벌 버프 구매.
  BUY_GUILD_RAID_BUFF = 11036,

  MANAGING_ACCEPT_JOIN = 11101,
  MANAGING_REFUSE_JOIN = 11102,
  MANAGING_CHANGE_MEMBER_GRADE = 11103,
  MANAGING_KICK_MEMBER = 11104,
  MANAGING_CHANGE_INFO = 11105,
  MANAGING_DELEGATE_MASTER = 11106,

  GET_GULID_SHOP_SYNC_DATA = 11201,
  BUY_GULID_SHOP_PRODUCT = 11202,
}

export enum BattleLobby {
  LEAVE = 12001,
  ENTER = 12002,
}

export enum Raid {
  STATE_SC = 13001,
  GET_RAID_INFO = 13002,
  GET_RANKING_PAGE = 13003,
  PICKUP_REWARD = 13004,
  // BUY_TICKET = 13005,
  PICK_RAID_BOSS = 13006, // 도전 설정.
  GET_REWARD_STATES = 13007,
  PICK_RAID_BOSS_CANCEL = 13008, // 도전 취소.
}

export enum Friend {
  NOTIFIED_SC = 14001, // 알림정보 받기.(친구요청,친구삭제,수락,거절등..)
  REQUEST_FRIEND = 14002, // 친구 요청
  CANCEL_FRIEND_REQUEST = 14003, // 요청 취소
  DELETE_FRIEND = 14004, // 친구 삭제
  ACCEPT_FRIEND_REQUEST = 14005, // 친구 요청 수락
  DENY_FRIEND_REQUEST = 14006, // 친구 욫어 거절
  SEND_POINT = 14007, // 친구에게 포인트 보내기.
  PICKUP_POINT = 14008, //  친구로부터 받은 포인트 획득하기.
}

export enum Manufacture {
  MANUFACTURE_START = 15001,    // 생산 시작
  MANUFACTURE_RECEIVE = 15002,  // 생산 완료 및 수령
  MANUFACTURE_DECREASE_EXPIRE_TIME = 15003, // 생산 시간 감소
  MANUFACTURE_COMPLETE_EXPIRE_TIME = 15004, // 생산 시간 즉시 완료
  MANUFACTURE_UNLOCK_RECIPE = 15005, // 레시피 해금
  MANUFACTURE_END = 15006, // 생산 완료 체크 (수령 아님)
}

export const toString = (type: number) => {
  if (Auth[type] !== undefined) {
    return `Auth.${Auth[type]}`;
  } else if (Town[type] !== undefined) {
    return `Town.${Town[type]}`;
  } else if (Ocean[type] !== undefined) {
    return `Ocean.${Ocean[type]}`;
  } else if (Common[type] !== undefined) {
    return `Common.${Common[type]}`;
  } else if (Etc[type] !== undefined) {
    return `Etc.${Etc[type]}`;
  } else if (Admin[type] !== undefined) {
    return `Admin.${Admin[type]}`;
  } else if (Dev[type] !== undefined) {
    return `Dev.${Dev[type]}`;
  } else if (BattleReward[type] !== undefined) {
    return `BattleReward.${BattleReward[type]}`;
  } else if (Battle[type] !== undefined) {
    return `Battle.${Battle[type]}`;
  } else if (Duel[type] !== undefined) {
    return `Duel.${Duel[type]}`;
  } else if (Temp[type] !== undefined) {
    return `Temp.${Dev[type]}`;
  } else if (Guild[type] !== undefined) {
    return `Guild.${Guild[type]}`;
  } else if (BattleLobby[type] !== undefined) {
    return `BattleLobby.${BattleLobby[type]}`;
  } else if (Raid[type] !== undefined) {
    return `Raid.${Raid[type]}`;
  } else if (Friend[type] !== undefined) {
    return `Friend.${Friend[type]}`;
  } else if (Manufacture[type] !== undefined) {
    return `Manufacture.${Manufacture[type]}`;
  } else {
    return `Unknown(${type})`;
  }
};
