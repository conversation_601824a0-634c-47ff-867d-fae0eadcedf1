# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output
dist/
build/

# Configuration files (contains sensitive information)
config/database.json5

# CSV files (may contain sensitive data)
*.csv
!example/*.csv

# Backup files
#backup/

# Output files
output/
artifacts/
logs/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage reports
coverage/

# Test files
*.test.js
*.spec.js

# Temporary files
tmp/
temp/

# Lock files (choose one)
package-lock.json
# yarn.lock
