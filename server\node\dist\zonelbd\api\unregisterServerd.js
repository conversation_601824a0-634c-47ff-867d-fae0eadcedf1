"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const typedi_1 = __importDefault(require("typedi"));
const merror_1 = require("../../motiflib/merror");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const server_1 = require("../server");
const cmsEx = __importStar(require("../../cms/ex"));
module.exports = async (req, res) => {
    const { appServiceUrl, zoneType } = req.body;
    const zoneLbRedis = typedi_1.default.get(server_1.ZonelbService).get(zoneType);
    if (!zoneLbRedis.poolImpl) {
        mlog_1.default.warn('MRedisConnPool is destroyed');
        return;
    }
    return zoneLbRedis['unregisterServerd'](appServiceUrl)
        .then(() => {
        mlog_1.default.info(`unregisterServerd success ${cmsEx.ZoneType[zoneType]}`, {
            appServiceUrl,
        });
        res.end();
    })
        .catch((err) => {
        mlog_1.default.warn('/zonelbd/unregisterServerd error', { err: err.message, zoneType });
        if (err instanceof merror_1.MError) {
            throw err;
        }
        else {
            let errCode = merror_1.MErrorCode.INTERNAL_ERROR;
            if (cmsEx.ZoneType.TOWN == zoneType) {
                errCode = merror_1.MErrorCode.TOWN_SERVER_UNREGISTER_ERROR;
            }
            else if (cmsEx.ZoneType.OCEAN == zoneType) {
                errCode = merror_1.MErrorCode.OCEAN_SERVER_UNREGISTER_ERROR;
            }
            throw new merror_1.MError(err.message, errCode, err.message);
        }
    });
};
//# sourceMappingURL=unregisterServerd.js.map