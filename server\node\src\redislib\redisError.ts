import { MError } from '../motiflib/merror';

import { MErrorCode } from '../motiflib/merror';

import mlog from '../motiflib/mlog';

// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

export const convertToMError = (errMsg: string): MError => {
  mlog.verbose('convertToMError', { errMsg });

  switch (errMsg) {
    case 'no-townd':
      return new MError('townd-unavailable', MErrorCode.TOWND_UNAVAILABLE);
    case 'no-oceand':
      return new MError('oceand-unavailable', MErrorCode.OCEAND_UNAVAILABLE);
    case 'already-in-ocean':
      return new MError('already-in-ocean', MErrorCode.ALREADY_IN_OCEAN);
    case 'not-in-ocean':
      return new MError('not-in-ocean', MErrorCode.NOT_IN_OCEAN);
    case 'invalid-sail-id':
      return new MError('invalid-sail-id', MErrorCode.INVALID_SAIL_ID);
    case 'sail-already-ended':
      return new MError('sail-already-ended', MErrorCode.SAIL_ALREADY_ENDED);
    case 'cannot-battle-cancel-cause-expire-time':
      return new MError(
        'battle-cancel-fail-expire',
        MErrorCode.BATTLE_CANCEL_FAILED_NOT_YET_EXPIRE_TIME
      );

    // nation
    case 'already-cabinet-applicant':
      return new MError('already-cabinet-applicant', MErrorCode.ALREADY_CABINET_APPLICANT);
    case 'already-cabinet-member':
      return new MError('already-cabinet-member', MErrorCode.ALREADY_CABINET_MEMBER);
    case 'not-cabinet-applicant':
      return new MError('not-cabinet-applicant', MErrorCode.NOT_CABINET_APPLICANT);
    case 'not-cabinet-member':
      return new MError('not-cabinet-member', MErrorCode.NOT_A_CABINET_MEMBER);
    case 'exceed-max-wage-rate':
      return new MError('exceed-max-wage-rate', MErrorCode.EXCEED_TOTAL_WAGE_RATE_SUM_MAX);

    // raid
    case 'raid-not-reward-state':
      return new MError('raid-not-reward-state', MErrorCode.RAID_NOT_REWARD_STATE);
    case 'duplicated-pick-boss':
      return new MError('duplicated-pick-boss', MErrorCode.RAID_DUPLICATED_PICK_BOSS);
    case 'pick-raid-boss-limit-exceeded':
      return new MError(
        'pick-raid-boss-limit-exceeded',
        MErrorCode.RAID_PICK_RAID_BOSS_LIMIT_EXCEEDED
      );
    default:
      mlog.error('unknown-redis-error', {
        errMsg,
      });
      return new MError('unknown-redis-error', MErrorCode.INTERNAL_ERROR);
  }
};
