CREATE PROCEDURE `mp_u_manufacture_progress_update`(
  IN inUserId INT,
  IN inRoomCmsId INT,
  IN inSlot INT,
  IN inRecipeId INT,
  IN inStartTimeUtc BIGINT,
  IN inCompletionTimeUtc BIGINT,
  IN inProgressType TINYINT,
  IN inMateCmsIds JSON,
  IN inSuccessRate INT,
  IN inGreatSuccessRate INT,
  IN inExtra JSON
)
label_body:BEGIN
  -- INSERT ... ON DUPLICATE KEY UPDATE 사용으로 한 번의 쿼리로 처리
  INSERT INTO u_manufacture_progress (
    userId,
    roomCmsId,
    slot,
    recipeId,
    startTimeUtc,
    completionTimeUtc,
    progressType,
    mateCmsIds,
    successRate,
    greatSuccessRate,
    extra
  ) VALUES (
    inUserId,
    inRoomCmsId,
    inSlot,
    inRecipeId,
    FROM_UNIXTIME(inStartTimeUtc),
    FROM_UNIXTIME(inCompletionTimeUtc),
    inProgressType,
    inMateCmsIds,
    inSuccessRate,
    inGreatSuccessRate,
    inExtra
  )
  ON DUPLICATE KEY UPDATE
    recipeId = VALUES(recipeId),
    startTimeUtc = FROM_UNIXTIME(inStartTimeUtc),
    completionTimeUtc = FROM_UNIXTIME(inCompletionTimeUtc),
    progressType = VALUES(progressType),
    mateCmsIds = VALUES(mateCmsIds),
    successRate = VALUES(successRate),
    greatSuccessRate = VALUES(greatSuccessRate),
    extra = VALUES(extra);
    
  SELECT ROW_COUNT() as affectedRows, 
         CASE WHEN ROW_COUNT() = 1 THEN 'INSERT' ELSE 'UPDATE' END as operation;
END 