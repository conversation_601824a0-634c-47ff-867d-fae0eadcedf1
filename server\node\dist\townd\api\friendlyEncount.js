"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
const merror_1 = require("../../motiflib/merror");
const libTown = __importStar(require("../libTown"));
const friendlyMatchUseMapsDesc_1 = require("../../cms/friendlyMatchUseMapsDesc");
const userFriendlyEncount_1 = require("../../lobbyd/userFriendlyEncount");
module.exports = async (req, res) => {
    const { attackerUserId, attackerFleetData, attackerGuildName, attackerPubId, defenserUserId, pvpId, } = req.body;
    let attackerUser = libTown.getTownUser(attackerUserId);
    if (!attackerUser) {
        throw new merror_1.MError('/town/friendlyEncount user not found', merror_1.MErrorCode.TOWN_USER_NOT_FOUND);
    }
    const defenserUser = libTown.getTownUser(defenserUserId);
    if (!defenserUser) {
        return res.json({ encountResult: userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_NOT_FOUND_USER });
    }
    if (attackerUser.curTownCmsId !== defenserUser.curTownCmsId) {
        return res.json({ encountResult: userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_NOT_MATCHED_TOWN });
    }
    const reuslt = defenserUser.checkFriendlyEncount();
    if (reuslt !== userFriendlyEncount_1.FriendlyEncountResult.WAITING_FOR_CHOICE) {
        return res.json({ encountResult: reuslt });
    }
    const oceanStageCmsId = (0, friendlyMatchUseMapsDesc_1.pickFriendlyUseMap)();
    const msg = {
        defenserUserId,
        attackerUserId,
        attackerFleetData,
        attackerPubId,
        attackerGuildName,
        attackerRepresentedMateCmsId: attackerUser.representedMate.cmsId,
        attackerRepresentedMateIllustCmsId: attackerUser.representedMate.equippedIllustCmsId,
        pvpId,
        oceanStageCmsId,
    };
    attackerUser.setFriendlyEncountUserId(defenserUserId);
    defenserUser.setFriendlyEncountUserId(attackerUserId);
    const defTownZone = defenserUser.getCurrentZone();
    const defenserRes = await defTownZone.onNotifyFriendlyEncountRequested(defenserUser, msg);
    if (defenserRes.encountResult !== userFriendlyEncount_1.FriendlyEncountResult.WAITING_FOR_CHOICE) {
        attackerUser.setFriendlyEncountUserId(null);
        defenserUser.setFriendlyEncountUserId(null);
        return res.json({ encountResult: defenserRes.encountResult });
    }
    libTown.updateFriendlyEncount(defTownZone, defenserUser);
    libTown.updateFriendlyEncount(attackerUser.getCurrentZone(), attackerUser);
    return res.json({
        encountResult: defenserRes.encountResult,
        defenserFleetData: defenserRes.fleetData,
        defenserPubId: defenserRes.pubId,
        defenserRepresentedMateCmsId: defenserRes.representedMateCmsId,
        defenserRepresentedMateIllustCmsId: defenserRes.reprentedMateIllustCmsId,
        oceanStageCmsId,
    });
};
//# sourceMappingURL=friendlyEncount.js.map