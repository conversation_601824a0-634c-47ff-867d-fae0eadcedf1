// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import { MError, MErrorCode } from '../../../motiflib/merror';
import { Sync, All } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import * as mutil from '../../../motiflib/mutil';
import { LobbyService } from '../../server';
import { <PERSON>lientPacketHandler } from '../index';
import { NationUtil } from '../../../motiflib/model/lobby/nationUtil';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { NationSupportShopPurchasedPubMsg } from '../../../motiflib/model/lobby';
import mlog from '../../../motiflib/mlog';
import UserPoints, { CashPayment, PointChange, PointConsumptionCostParam } from '../../userPoints';
import tuBuyNationSupportShop from '../../../mysqllib/txn/tuBuyNationSupportShop';
import tuUpdatePoints from '../../../mysqllib/txn/tuUpdatePoints';
import mconf from '../../../motiflib/mconf';
import { NATION_SUPPORT_SHOP_NEED_RESOURCE_TYPE } from '../../../cms/nationSupportShopDesc';
import { ITEM_TYPE } from '../../../cms/itemDesc';
import { ItemChange } from '../../userInven';
import { PrData, CostData } from '../../../motiflib/gameLog';
import { isActiveProduct } from '../../userNation';
import { NATION_CABINET_AUTHORITY_TYPE } from '../../../cms/NationCabinetAuthorityDesc';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// 총리의 응원 상점 구매 요청
// ----------------------------------------------------------------------------

const rsn = 'buy_nation_support_shop';
const add_rsn = null;

interface RequestBody {
  supportShopCmsId: number;
  bPermitExchange?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BuyNationSupportShop implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { supportShopCmsId, bPermitExchange } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const {
      nationRedis,
      nationManager,
      worldPubsub: pubsub,
      userDbConnPoolMgr,
    } = Container.get(LobbyService);

    const curTimeUtc = mutil.curTimeUtc();
    const curCabinetSessionId = nationManager.getCabinetSessionId(curTimeUtc);

    const userId = user.userId;
    const nationCmsId = user.nationCmsId;

    mlog.info('[ELECTION] BuyNationSupportShop start', {
      userId,
      nationCmsId,
      curCabinetSessionId,
      supportShopCmsId,
    });

    let outTrace: any = {};
    if (
      NationUtil.isClosingElectionSession(
        nationManager.getLastClosedElectionSessionId(nationCmsId),
        curTimeUtc,
        outTrace
      )
    ) {
      throw new MError(
        'it-is-nation-election-cleaning-time',
        MErrorCode.IT_IS_NATION_ELECTION_CLEANING_TIME,
        {
          curTimeUtc,
          outTrace,
        }
      );
    }

    // 시장 선출중(투자 정산중)인 경우 차단
    if (NationUtil.isClosingInvestmentWeeklySession(curTimeUtc)) {
      throw new MError(
        'it-is-weekly-investment-cleaning-time',
        MErrorCode.IT_IS_WEEKLY_INVESTMENT_CLEANING_TIME,
        { nationCmsId }
      );
    }

    // 총리의 구매 제한 시간인지 체크
    if (
      NationUtil.isInNationSupportShopUnbuyableHours(
        nationManager.getLastClosedElectionSessionId(nationCmsId),
        curTimeUtc,
        outTrace
      )
    ) {
      throw new MError(
        'it-is-nation-support-shop-unbuyable-hours',
        MErrorCode.IT_IS_NATION_SUPPORT_SHOP_UNBUYABLE_HOURS,
        { curTimeUtc, outTrace }
      );
    }

    const nation = nationManager.get(nationCmsId);
    if (
      nation.hasCabinetAuthority(
        curCabinetSessionId,
        userId,
        NATION_CABINET_AUTHORITY_TYPE.SupportShopBuy
      ) === false
    ) {
      throw new MError('none-cabinet-authority', MErrorCode.DONT_HAVE_A_AUTHORITY, {
        curCabinetSessionId,
        authorityType: NATION_CABINET_AUTHORITY_TYPE.SupportShopBuy,
      });
    }
    // 응원 상점 정보 체크
    const supportShopCms = cms.NationSupportShop[supportShopCmsId];
    if (!supportShopCms) {
      throw new MError('invalid-support-shop-cms-id', MErrorCode.INVALID_REQUEST, {
        supportShopCmsId,
      });
    }

    // Validate localBitflag
    if (cmsEx.isFilteredByCountryCode(supportShopCms.localBitFlag)) {
      throw new MError('invalid-local-bit-flag', MErrorCode.NOT_ALLOWED_IN_COUNTRY, {
        supportShopCmsId,
        cmsLocalBitFlag: supportShopCms.localBitFlag,
        mconfCountryCode: mconf.countryCode,
      });
    }

    // 판매 기간 체크
    if (!isActiveProduct(supportShopCms, curTimeUtc)) {
      throw new MError(
        'not-active-support-shop-product',
        MErrorCode.NATION_SUPPORT_SHOP_INACTIVE_PRODUCT,
        {
          supportShopCmsId,
          cmsStartDate: supportShopCms.startDate,
          cmsEndDate: supportShopCms.endDate,
          curTimeUtc,
        }
      );
    }

    let prData: PrData[] = [];
    let costData: CostData[] = [];
    let itemChange: ItemChange;
    let pointChanges: PointChange[];
    let cashPayments: CashPayment[];

    // 필요 자원 체크
    if (
      supportShopCms.needResourceType === NATION_SUPPORT_SHOP_NEED_RESOURCE_TYPE.POINT &&
      !mutil.isNotANumber(supportShopCms.needResourceVal) &&
      supportShopCms.needResourceVal > 0
    ) {
      const pointCost: PointConsumptionCostParam = {
        cmsId: supportShopCms.needResourceId,
        cost: supportShopCms.needResourceVal,
      };

      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [pointCost],
        bPermitExchange,
        { itemId: rsn },
        true,
        prData
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;
    } else if (supportShopCms.needResourceType === NATION_SUPPORT_SHOP_NEED_RESOURCE_TYPE.ITEM) {
      // todo. 아이템으로 구매 처리
      throw new MError(
        'not-yet-allowed-to-purchase-support-shop-by-item',
        MErrorCode.INVALID_REQUEST,
        {
          supportShopCmsId,
        }
      );

      const itemCms = cms.Item[supportShopCms.needResourceId];

      costData.push({
        type: ITEM_TYPE[itemCms.type],
        id: itemCms.id,
        amt: supportShopCms.needResourceVal,
      });

      itemChange = user.userInven.itemInven.buildItemChange(
        itemCms.id,
        -supportShopCms.needResourceVal,
        true
      );
    }

    // 이미 구매 여부 체크
    const cabinet = nation.getCabinet(curCabinetSessionId);
    if (cabinet?.purchasedSupportShops?.[supportShopCmsId]) {
      throw new MError('already-purchased-support-shop', MErrorCode.INVALID_REQUEST, {
        supportShopCmsId,
      });
    }

    // 장애 시 롤백을 위한
    let bPointChangeCommitted = false;

    const sync: Sync = {};
    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return Promise.resolve()
      .then(() => {
        return user.userPoints.tryConsumeCashs(cashPayments, sync, user, {
          user,
          rsn,
          add_rsn,
          exchangeHash,
        });
      })
      .then(() => {
        return tuBuyNationSupportShop(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          pointChanges
        );
      })
      .then(() => {
        if (pointChanges?.length > 0) {
          bPointChangeCommitted = true;
        }

        // 레디스에 구매 정보 저장(실제 지급처리는 국민들의 수령요청시 지급)
        return nationRedis['buyNationSupportShop'](
          nationCmsId,
          curCabinetSessionId,
          supportShopCmsId
        );
      })
      .then(() => {
        // 이후부터는 throw 되어도 point change commit 을 rollback 하지 않는다.
        bPointChangeCommitted = false;

        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
        );

        // 총리 개인용 구매정보 패킷 셋팅
        _.merge<Sync, Sync>(sync, {
          add: {
            nations: {
              [nationCmsId]: {
                purchasedSupportShops: {
                  [curCabinetSessionId]: {
                    [supportShopCmsId]: {
                      cmsId: supportShopCmsId,
                    },
                  },
                },
              },
            },
          },
        });

        // 응답 및 다른 로비에 동기화(pub-sub) 처리
        const msgObj: NationSupportShopPurchasedPubMsg = {
          nationCmsId,
          cabinetSessionId: curCabinetSessionId,
          updateTimeUtc: curTimeUtc,
        };

        pubsub.publish('nation_support_shop_purchased', JSON.stringify(msgObj));

        return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
      })
      .catch((err) => {
        return Promise.resolve()
          .then(() => {
            // If redis error occurred, we should rollback user point.
            if (bPointChangeCommitted) {
              mlog.warn('buyNationSupportShop rollback changed point', {
                userId: user.userId,
                pointChanges,
                err: err.message,
              });
              const changeForRollback: PointChange[] = [];
              for (const change of pointChanges) {
                changeForRollback.push({
                  cmsId: change.cmsId,
                  value: user.userPoints.getPoint(change.cmsId),
                });
              }
              return tuUpdatePoints(
                userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
                user.userId,
                changeForRollback
              );
            }
            return null;
          })
          .then(() => {
            throw err;
          });
      });
  }
}
