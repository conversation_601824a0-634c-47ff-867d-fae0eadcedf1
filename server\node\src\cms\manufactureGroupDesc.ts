// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

export enum MANUFACTURE_RECIPE_UNLOCKTYPE {
  ITEM_UNLOCK = 1,
  LIVE_EVENT,
}

export interface RawManufactureGroupDesc {
  id: number;                                 // key
  order: number;                              // 정렬순서
  unlockType: MANUFACTURE_RECIPE_UNLOCKTYPE;  // 1: item, 2:liveEvent
  unlockTargetId: number;                     // 1에 해당하는 itemId, 2에 해당하는 liveEventId
  recipeCategory: boolean[];                  // [조리, 주조, 공예, 봉제, 제약] 에 해당하는 boolean flag
}

export interface ManufactureGroupDesc extends RawManufactureGroupDesc {

}