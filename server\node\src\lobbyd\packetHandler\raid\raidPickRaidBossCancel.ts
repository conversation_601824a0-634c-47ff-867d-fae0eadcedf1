// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';
import cms from '../../../cms';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import { RAID_STATE } from '../../../motiflib/model/lobby';
import { RaidTicketNub } from '../../userRaidTicket';
import mlog from '../../../motiflib/mlog';

// ----------------------------------------------------------------------------
// 도전권 (보스 도전할 수 있도록 설정)
// ----------------------------------------------------------------------------
const rsn = 'raid_pick_raid_boss_cancel';
const add_rsn = null;

interface RequestBody {
  bossRaidCmsId: number;
}

interface ResponseBody extends Resp {
  pickedBossList: number[];
}

// ----------------------------------------------------------------------------
export class Cph_Raid_PickRaidBossCancel implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() { }

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    const { raidRedis, raidManager, userDbConnPoolMgr } = Container.get(LobbyService);
    const body: RequestBody = packet.bodyObj;
    const { bossRaidCmsId } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const bossRaidCms = cms.BossRaid[bossRaidCmsId];
    if (!bossRaidCms.bossRaidTicketCms) {
      throw new MError('not-exist-boss-raid-cms',
        MErrorCode.RAID_BOSS_PICK_CANCEL_INVALID_BOSS_RAID_CMS_ID, {
        bossRaidCmsId,
      });
    }

    const bossRaidTicketCms = bossRaidCms.bossRaidTicketCms;
    if (!bossRaidTicketCms) {
      throw new MError('not-exist-boss-raid-ticket',
        MErrorCode.RAID_BOSS_PICK_CANCEL_NOT_EXIST_BOSS_RAID_TICKET_CMS, {
        bossRaidCmsId,
      });
    }

    const bossRaidTicketInitCnt = bossRaidTicketCms.initCnt;

    const raidProgressNub = raidManager.getNub();
    if (!raidProgressNub) {
      throw new MError('invalid-raid-manager-nub',
        MErrorCode.RAID_BOSS_PICK_CANCEL_INVALID_RAID_MANAGER_NUB,
        {}
      );
    }

    if (raidProgressNub.state !== RAID_STATE.INPROGRESS) {
      throw new MError('raid-has-closed',
        MErrorCode.RAID_BOSS_PICK_CANCEL_RAID_HAS_CLOSED, {
        raidProgressNub: raidProgressNub,
        bossRaidCmsId,
      });
    }

    const raid = raidManager.getRaid(bossRaidCmsId);
    if (!raid) {
      throw new MError('invalid-boss-raid-cmsid',
        MErrorCode.RAID_BOSS_PICK_CANCEL_INVALID_BOSS_RAID_CMS_ID, {
        bossRaidCmsId,
      });
    }

    const oldPickedBossListString = await raidRedis['getPickedBossList'](user.userId);
    const oldPickedBossList: number[] = JSON.parse(oldPickedBossListString);

    // 도전(픽)한 보스인가?
    if (!_.includes(oldPickedBossList, bossRaidCmsId)) {
      throw new MError('not-picked-boss',
        MErrorCode.RAID_BOSS_PICK_CANCEL_NOT_PICKED_BOSS, {
        bossRaidCmsId,
      });
    }

    // 티켓을 사용한 적이 없는가?
    // 티켓 정보가 있는데, 시즌이 같다면
    // 티켓 카운트, 구매 카운트를 확인 한다.
    const raidTicketNub: RaidTicketNub = user.userRaidTicket.getRaidTicketNub(bossRaidCmsId);
    if (raidTicketNub && raidTicketNub.sessionId === raidProgressNub.sessionId) {
      if (raidTicketNub.count < bossRaidTicketInitCnt || raidTicketNub.buyCount > 0) {
        throw new MError('already-used-ticket',
          MErrorCode.RAID_BOSS_PICK_CANCEL_ALREADY_USED_TICKET, {
          bossRaidCmsId,
        });
      }
    }

    // 도전 취소
    const newPickedBossList: number[] = await raidRedis['pickRaidBoss'](
      user.userId,
      bossRaidCmsId,
      cms.Const.BossRaidChallengeCnt.value,
      0 //
    );

    user.glog('boss_raid_pick_cancel', {
      rsn,
      add_rsn,
      boss_raid_id: bossRaidCmsId,
    });

    mlog.info('[RAID] raidPickRaidBossCancel', {
      userId: user.userId,
      bossRaidCmsId,
      oldPickedBossList,
      newPickedBossList,
    });

    const sync: Sync = {};
    return user.sendJsonPacket(packet.seqNum, packet.type, {
      sync,
      pickedRaidBosses: newPickedBossList,
    });
  }
}
