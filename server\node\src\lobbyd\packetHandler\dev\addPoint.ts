// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import tuChangeUserPoint from '../../../mysqllib/txn/tuChangeUserPoint';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Resp, Sync } from '../../type/sync';
import { LobbyService } from '../../server';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import cms from '../../../cms';
import { Dev } from '../../../proto/lobby/proto';
import { EnergyChange } from '../../userEnergy';
import { CashShopMileage, EnergyPointCmsId, ManufacturePointCmsId } from '../../../cms/ex';
import { curTimeUtc } from '../../../motiflib/mutil';
import puUserUpdateEnergy from '../../../mysqllib/sp/puUserUpdateEnergy';
import { isCash } from '../../../cms/pointDesc';
import { ClientPacketHandler } from '../index';
import tuUpdateMileage from '../../../mysqllib/txn/tuUpdateMileage';
import mhttp from '../../../motiflib/mhttp';
import puUserUpdateManufacturePoint from '../../../mysqllib/sp/puUserUpdateManufacturePoint';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'dev_add_point';
const add_rsn = null;

// ----------------------------------------------------------------------------
export class Cph_Dev_AddPoint implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body = packet.bodyObj;

    const cmsId = body.cmsId;
    const amount = body.amount;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.ensureCheatAccessLevel(Dev.ADD_POINT);

    // eunseok_test
    mhttp.lglogd.refresh();
    

    if (!Number.isInteger(amount) || amount === 0) {
      throw new MError('invalid-amount', MErrorCode.INVALID_REQ_BODY_DEV_ADD_POINT, {
        reqBody: body,
      });
    }

    const now = curTimeUtc();
    if (cmsId === EnergyPointCmsId) {
      const energyChange: EnergyChange = user.userEnergy.buildEnergyChangeWithConsume(
        now,
        user.level,
        user.level,
        -amount,
        false
      );

      const sync: Sync = {};
      const { userDbConnPoolMgr } = Container.get(LobbyService);
      return puUserUpdateEnergy(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        energyChange.energy,
        energyChange.lastUpdateTimeUtc
      ).then(() => {
        _.merge<Sync, Sync>(sync, user.userEnergy.applyEnergyChange(energyChange, null));
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    } else if(cmsId === ManufacturePointCmsId){
      const [success, pointChange] = user.userManufacture.buildPointChange(now, amount);
      if(!success){
        throw new MError('fail-to-build-manufacture-point-change',
          MErrorCode.INVALID_REQ_BODY_DEV_ADD_POINT, {
          reqBody: body,
        });
      }

      const sync: Sync = {};
      const { userDbConnPoolMgr } = Container.get(LobbyService);

      return puUserUpdateManufacturePoint(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        pointChange.point,
        pointChange.lastUpdatePointTimeUtc
      ).then(() => {
        _.merge<Sync, Sync>(
          sync,
          user.userManufacture.applyPointChange(pointChange, null)
        );

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
        });

    } else if (isCash(cmsId)) {
      return _addCash(cmsId, amount, user, packet);
    } else if (cmsId === CashShopMileage) {
      const mileageChanges = user.userPoints.buildMileageChanges(amount, now);
      return tuUpdateMileage(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        mileageChanges
      ).then(() => {
        const sync = user.userPoints.applyMileageChanges(mileageChanges, null);
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    } else {
      const pointCms = cms.Point[cmsId];
      if (!pointCms) {
        throw new MError('no-key-in-point-cms', MErrorCode.INVALID_REQ_BODY_DEV_ADD_POINT, {
          cmsId,
        });
      }

      const oldPoint = user.userPoints.getPoint(cmsId);
      const pointValue = oldPoint + amount;
      if (pointValue > pointCms.hardCap) {
        throw new MError('exceeds-hard-cap', MErrorCode.EXCEEDS_HARD_CAP, { cmsId, pointValue });
      }
      if (pointValue < 0) {
        mlog.info('requested to apply negative point', {
          rsn,
          userId: user.userId,
          cmsId,
          oldPoint,
          newPoint: pointValue,
        });
      }

      return tuChangeUserPoint(
        userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
        user.userId,
        cmsId,
        pointValue
      ).then(() => {
        const sync: Sync = {};
        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyPointChanges([{ cmsId, value: pointValue }], null)
        );

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
    }
  }
}

function _addCash(cmsId: number, amount: number, user: User, packet: CPacket): Promise<any> {
  return Promise.resolve()
    .then(() => {
      if (amount > 0) {
        return user.userPoints.addCash(cmsId, amount, rsn, user, null);
      } else {
        return user.userPoints.consumeCash(cmsId, -amount, { itemId: rsn }, user, null);
      }
    })
    .then(() => {
      const sync: Sync = {
        add: {
          points: {
            [cmsId]: {
              cmsId,
              value: user.userPoints.getPoint(cmsId),
            },
          },
        },
      };

      return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
    });
}
