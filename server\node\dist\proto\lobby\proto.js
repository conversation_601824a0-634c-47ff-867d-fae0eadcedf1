"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
Object.defineProperty(exports, "__esModule", { value: true });
exports.toString = exports.Friend = exports.Raid = exports.BattleLobby = exports.Guild = exports.Temp = exports.Dev = exports.Admin = exports.Etc = exports.LandExploreReward = exports.BattleReward = exports.Common = exports.Duel = exports.Battle = exports.Ocean = exports.Town = exports.Auth = void 0;
// Check Proto.lua & NetCommon.h
// CS means client to server. CS packet is no response.
// SC means server to client without client request.
var Auth;
(function (Auth) {
    Auth[Auth["HELLO"] = 101] = "HELLO";
    Auth[Auth["ENTER_WORLD"] = 102] = "ENTER_WORLD";
    Auth[Auth["CHANGE_NAME"] = 103] = "CHANGE_NAME";
    Auth[Auth["COMPLETE_PROLOGUE"] = 105] = "COMPLETE_PROLOGUE";
    Auth[Auth["REFRESH_ENTER_WORLD_TOKEN_SC"] = 106] = "REFRESH_ENTER_WORLD_TOKEN_SC";
    Auth[Auth["REVOKE"] = 108] = "REVOKE";
    Auth[Auth["KICK_SC"] = 123] = "KICK_SC";
    Auth[Auth["GAME_GUARD_CHECK_SC"] = 130] = "GAME_GUARD_CHECK_SC";
    Auth[Auth["GAME_GUARD_CHECK_CS"] = 131] = "GAME_GUARD_CHECK_CS";
    Auth[Auth["APP_GUARD_CHECK_CS"] = 140] = "APP_GUARD_CHECK_CS";
})(Auth = exports.Auth || (exports.Auth = {}));
var Town;
(function (Town) {
    Town[Town["ENTER"] = 2001] = "ENTER";
    Town[Town["LOAD_COMPLETE"] = 2002] = "LOAD_COMPLETE";
    Town[Town["MOVE_CS"] = 2003] = "MOVE_CS";
    Town[Town["NEARBY_USERS_SC"] = 2005] = "NEARBY_USERS_SC";
    Town[Town["USER_UPDATE_SC"] = 2006] = "USER_UPDATE_SC";
    Town[Town["QUERY_MATE_CS"] = 2007] = "QUERY_MATE_CS";
    Town[Town["QUERY_MATE_RES_SC"] = 2008] = "QUERY_MATE_RES_SC";
    Town[Town["ENTER_BUILDING"] = 2009] = "ENTER_BUILDING";
    Town[Town["LEAVE_BUILDING"] = 2010] = "LEAVE_BUILDING";
    // USER_STATE_UPDATE_SC = 2011, 사용 안함
    Town[Town["BANK_DEPOSIT"] = 2012] = "BANK_DEPOSIT";
    Town[Town["BANK_DEPOSIT_INSTALLMENT_SAVINGS"] = 2013] = "BANK_DEPOSIT_INSTALLMENT_SAVINGS";
    Town[Town["BANK_WITHDRAW"] = 2014] = "BANK_WITHDRAW";
    Town[Town["BANK_WITHDRAW_INSTALLMENT_SAVINGS"] = 2015] = "BANK_WITHDRAW_INSTALLMENT_SAVINGS";
    Town[Town["BANK_BUY_INSURANCE"] = 2016] = "BANK_BUY_INSURANCE";
    Town[Town["USER_MOVE_SC"] = 2017] = "USER_MOVE_SC";
    Town[Town["USER_LEAVE_SC"] = 2018] = "USER_LEAVE_SC";
    Town[Town["DEPART_CHANGE_SHIP_FLEET_FORMATION"] = 2019] = "DEPART_CHANGE_SHIP_FLEET_FORMATION";
    Town[Town["DEPART_BUY_SUPPLIES"] = 2020] = "DEPART_BUY_SUPPLIES";
    Town[Town["DEPART_DEPART"] = 2021] = "DEPART_DEPART";
    Town[Town["SHIPYARD_CREATE_SHIP"] = 2022] = "SHIPYARD_CREATE_SHIP";
    Town[Town["SHIPYARD_UPGRADE_BLUEPRINT"] = 2023] = "SHIPYARD_UPGRADE_BLUEPRINT";
    Town[Town["SHIPYARD_REPAIR"] = 2024] = "SHIPYARD_REPAIR";
    Town[Town["SHIPYARD_SELL_SHIP"] = 2025] = "SHIPYARD_SELL_SHIP";
    Town[Town["SHIPYARD_CHANGE_BLUEPRINT_SLOT"] = 2026] = "SHIPYARD_CHANGE_BLUEPRINT_SLOT";
    Town[Town["PUB_DRAFT_SAILOR"] = 2027] = "PUB_DRAFT_SAILOR";
    Town[Town["PUB_GET_SYNC_DATA"] = 2028] = "PUB_GET_SYNC_DATA";
    Town[Town["PUB_RECRUIT_MATE"] = 2029] = "PUB_RECRUIT_MATE";
    Town[Town["PUB_BUY_DRINK"] = 2030] = "PUB_BUY_DRINK";
    Town[Town["GOVER_INVEST"] = 2031] = "GOVER_INVEST";
    Town[Town["GOVER_GET_SYNC_DATA"] = 2032] = "GOVER_GET_SYNC_DATA";
    Town[Town["TOWN_STATES_UPDATE_SC"] = 2033] = "TOWN_STATES_UPDATE_SC";
    Town[Town["TOWN_USER_SYNC_DATA_UPDATE_SC"] = 2034] = "TOWN_USER_SYNC_DATA_UPDATE_SC";
    Town[Town["TRADE_BUY"] = 2035] = "TRADE_BUY";
    Town[Town["TRADE_SELL"] = 2036] = "TRADE_SELL";
    Town[Town["TRADE_GET_SYNC_DATA"] = 2037] = "TRADE_GET_SYNC_DATA";
    Town[Town["TOWN_TRADE_PRICE_PERCENT_UPDATE_SC"] = 2038] = "TOWN_TRADE_PRICE_PERCENT_UPDATE_SC";
    Town[Town["TOWN_INVESTMENT_UPDATE_SC"] = 2039] = "TOWN_INVESTMENT_UPDATE_SC";
    Town[Town["TRADE_GET_ALL_SESSION_PRICE_PERCENTS"] = 2040] = "TRADE_GET_ALL_SESSION_PRICE_PERCENTS";
    Town[Town["RELIGION_UPDATE_BUFF"] = 2041] = "RELIGION_UPDATE_BUFF";
    // SHOP_BUY = 2042,  사용 안함. SHOP_BUY_EX 로 대체됨. (UWO-16461)
    Town[Town["SHOP_GET_TOWN_BLACK_MARKET"] = 2043] = "SHOP_GET_TOWN_BLACK_MARKET";
    Town[Town["SHOP_SELL"] = 2044] = "SHOP_SELL";
    Town[Town["SHOP_RESET_TOWN_BLACK_MARKET"] = 2045] = "SHOP_RESET_TOWN_BLACK_MARKET";
    Town[Town["RECEIVE_INSURANCE"] = 2047] = "RECEIVE_INSURANCE";
    Town[Town["CHANGE_SHIP_SLOT"] = 2048] = "CHANGE_SHIP_SLOT";
    Town[Town["CHANGE_SHIP_SLOTS"] = 2049] = "CHANGE_SHIP_SLOTS";
    Town[Town["UNION_RESET_REQUEST"] = 2050] = "UNION_RESET_REQUEST";
    Town[Town["PUB_GIFT"] = 2051] = "PUB_GIFT";
    Town[Town["TRADE_TRY_BEGIN_NEGO"] = 2052] = "TRADE_TRY_BEGIN_NEGO";
    Town[Town["TRADE_TRY_NEGO"] = 2053] = "TRADE_TRY_NEGO";
    Town[Town["NOVICE_SUPPLY"] = 2054] = "NOVICE_SUPPLY";
    Town[Town["PALACE_GET_ROYAL_ORDER"] = 2055] = "PALACE_GET_ROYAL_ORDER";
    Town[Town["PALACE_REJECT_ROYAL_ORDER"] = 2056] = "PALACE_REJECT_ROYAL_ORDER";
    Town[Town["MANTIC_FORTUNE"] = 2057] = "MANTIC_FORTUNE";
    Town[Town["ARREST_USER_CHOICE"] = 2058] = "ARREST_USER_CHOICE";
    Town[Town["COLLECTOR_REPORT_DISCOVERIES"] = 2059] = "COLLECTOR_REPORT_DISCOVERIES";
    Town[Town["COLLECTOR_CONTRACT"] = 2060] = "COLLECTOR_CONTRACT";
    Town[Town["COLLECTOR_GET_RANK"] = 2061] = "COLLECTOR_GET_RANK";
    Town[Town["TRADE_RESET_BOUGHT"] = 2063] = "TRADE_RESET_BOUGHT";
    Town[Town["COLLECTOR_REPORT_WORLD_MAP_TILES"] = 2064] = "COLLECTOR_REPORT_WORLD_MAP_TILES";
    Town[Town["SHIPYARD_BUY_SHIP_SLOT_ITEM"] = 2066] = "SHIPYARD_BUY_SHIP_SLOT_ITEM";
    Town[Town["SHIPYARD_ENCHANT"] = 2067] = "SHIPYARD_ENCHANT";
    Town[Town["SHIPYARD_CHOICE_ENCHANT_RESULT"] = 2068] = "SHIPYARD_CHOICE_ENCHANT_RESULT";
    Town[Town["SHIPYARD_DISMANTLE_SHIP"] = 2069] = "SHIPYARD_DISMANTLE_SHIP";
    Town[Town["UPDATE_SOCIAL_ANI_PERSIST_CS"] = 2070] = "UPDATE_SOCIAL_ANI_PERSIST_CS";
    Town[Town["SHOW_SOCIAL_ANI_INSTANT_CS"] = 2071] = "SHOW_SOCIAL_ANI_INSTANT_CS";
    Town[Town["SHOW_SOCIAL_ANI_INSTANT_SC"] = 2072] = "SHOW_SOCIAL_ANI_INSTANT_SC";
    Town[Town["SHOW_EMOTICON_INSTANT_CS"] = 2073] = "SHOW_EMOTICON_INSTANT_CS";
    Town[Town["SHOW_EMOTICON_INSTANT_SC"] = 2074] = "SHOW_EMOTICON_INSTANT_SC";
    Town[Town["MANTIC_SWAP_PIECE"] = 2075] = "MANTIC_SWAP_PIECE";
    Town[Town["GOVER_CHANGE_MAYOR_TAX"] = 2076] = "GOVER_CHANGE_MAYOR_TAX";
    Town[Town["MAYOR_TAX_UPDATE_SC"] = 2077] = "MAYOR_TAX_UPDATE_SC";
    Town[Town["PUB_STAFF_CALL"] = 2078] = "PUB_STAFF_CALL";
    Town[Town["PUB_STAFF_NOMINATION"] = 2079] = "PUB_STAFF_NOMINATION";
    Town[Town["PUB_STAFF_TALKING"] = 2080] = "PUB_STAFF_TALKING";
    Town[Town["PUB_STAFF_BOASTING"] = 2081] = "PUB_STAFF_BOASTING";
    Town[Town["PUB_STAFF_GIFT"] = 2082] = "PUB_STAFF_GIFT";
    Town[Town["TOWN_NATION_SHARE_POINT_UPDATE_SC"] = 2083] = "TOWN_NATION_SHARE_POINT_UPDATE_SC";
    Town[Town["TOWN_MAYOR_UPDATE_SC"] = 2084] = "TOWN_MAYOR_UPDATE_SC";
    Town[Town["PUB_STAFF_GET_SYNC_DATA"] = 2085] = "PUB_STAFF_GET_SYNC_DATA";
    Town[Town["GOVER_GET_LAST_WEEK_RANK"] = 2086] = "GOVER_GET_LAST_WEEK_RANK";
    Town[Town["PUB_REDUCE_RECRUIT_NEGO_WAIT_TIME"] = 2087] = "PUB_REDUCE_RECRUIT_NEGO_WAIT_TIME";
    Town[Town["PUB_RESET_RECRUIT_NEGO_WAIT_TIME"] = 2088] = "PUB_RESET_RECRUIT_NEGO_WAIT_TIME";
    // NEW_SHIPYARD_CREATE_SHIP = 2089, 사용하지 않음
    Town[Town["SHIPYARD_RECEIVE_SHIP"] = 2090] = "SHIPYARD_RECEIVE_SHIP";
    Town[Town["PUB_SPONSOR_MY_MATE"] = 2091] = "PUB_SPONSOR_MY_MATE";
    Town[Town["PUB_REDUCE_MY_MATE_SPONSOR_WAIT_TIME"] = 2092] = "PUB_REDUCE_MY_MATE_SPONSOR_WAIT_TIME";
    Town[Town["PUB_RESET_MY_MATE_SPONSOR_WAIT_TIME"] = 2093] = "PUB_RESET_MY_MATE_SPONSOR_WAIT_TIME";
    Town[Town["SHIPYARD_VERIFY_TOW_SHIP"] = 2094] = "SHIPYARD_VERIFY_TOW_SHIP";
    Town[Town["SHIPYARD_REPAIR_LIFE_SHIP"] = 2095] = "SHIPYARD_REPAIR_LIFE_SHIP";
    Town[Town["PALACE_GET_CONTRIBUTION_SHOP_SYNC_DATA"] = 2096] = "PALACE_GET_CONTRIBUTION_SHOP_SYNC_DATA";
    Town[Town["PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT"] = 2097] = "PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT";
    Town[Town["SHOP_BUY_EX"] = 2098] = "SHOP_BUY_EX";
    Town[Town["SHIPYARD_BUY"] = 2099] = "SHIPYARD_BUY";
    Town[Town["UNION_RESET_EVENT_REQUEST"] = 2100] = "UNION_RESET_EVENT_REQUEST";
    Town[Town["SHIP_SLOT_ITEMS_EQUIP"] = 2101] = "SHIP_SLOT_ITEMS_EQUIP";
    // SAVE_FLEET_PRESET = 2102, town -> common
    // DELETE_FLEET_PRESET = 2103, town -> common
    //APPLY_FLEET_PRESET = 2104, town -> common
    //LOAD_FLEET_PRESET = 2105,
    Town[Town["TRADE_RECEIVE_TRADE_AREA_REWARD"] = 2106] = "TRADE_RECEIVE_TRADE_AREA_REWARD";
    Town[Town["SHIPYARD_RELAUNCH_SHIP"] = 2107] = "SHIPYARD_RELAUNCH_SHIP";
    Town[Town["CRAZE_EVENT_BUDGET_CHANGED_SC"] = 2108] = "CRAZE_EVENT_BUDGET_CHANGED_SC";
    Town[Town["PUB_RESET_MATES_BY_PAID"] = 2109] = "PUB_RESET_MATES_BY_PAID";
    Town[Town["PALACE_GET_ROYAL_TITLE_ORDER"] = 2110] = "PALACE_GET_ROYAL_TITLE_ORDER";
    Town[Town["GOVER_CHANGE_MAYOR_SHIPYARD_TAX"] = 2111] = "GOVER_CHANGE_MAYOR_SHIPYARD_TAX";
    Town[Town["CHANGE_SHIP_CAMOUFLAGE"] = 2112] = "CHANGE_SHIP_CAMOUFLAGE";
    Town[Town["SHIPYARD_BUY_IMPROVED_BLUEPRINT"] = 2113] = "SHIPYARD_BUY_IMPROVED_BLUEPRINT";
    Town[Town["GOVER_REGISTER_MAYOR_TRADE_EVENT"] = 2114] = "GOVER_REGISTER_MAYOR_TRADE_EVENT";
    Town[Town["USE_CANAL"] = 2115] = "USE_CANAL";
    Town[Town["COSTUME_SHIP_SLOT_ITEMS_EQUIP"] = 2116] = "COSTUME_SHIP_SLOT_ITEMS_EQUIP";
    Town[Town["NATION_CABINET_MEMBER_ENTER_NOTICE_SC"] = 2117] = "NATION_CABINET_MEMBER_ENTER_NOTICE_SC";
    Town[Town["ENCHANT_EQUIPMENT"] = 2118] = "ENCHANT_EQUIPMENT";
    Town[Town["ENCHANT_SHIP_SLOT"] = 2119] = "ENCHANT_SHIP_SLOT";
    Town[Town["SHIP_COMPOSE"] = 2120] = "SHIP_COMPOSE";
    Town[Town["NPC_SHOP_BUY"] = 2121] = "NPC_SHOP_BUY";
    Town[Town["SMUGGLE_ENTER_CONFIRM"] = 2122] = "SMUGGLE_ENTER_CONFIRM";
    Town[Town["CONTACT_NPC"] = 2123] = "CONTACT_NPC";
    Town[Town["AWAY_NPC"] = 2124] = "AWAY_NPC";
    Town[Town["TOWN_SMUGGLE_PRICE_PERCENT_UPDATE_SC"] = 2125] = "TOWN_SMUGGLE_PRICE_PERCENT_UPDATE_SC";
    Town[Town["SMUGGLE_GET_SYNC_DATA"] = 2126] = "SMUGGLE_GET_SYNC_DATA";
    Town[Town["SMUGGLE_TRY_BEGIN_NEGO"] = 2127] = "SMUGGLE_TRY_BEGIN_NEGO";
    Town[Town["SMUGGLE_TRY_NEGO"] = 2128] = "SMUGGLE_TRY_NEGO";
    Town[Town["SMUGGLE_BUY"] = 2129] = "SMUGGLE_BUY";
    Town[Town["SMUGGLE_SELL"] = 2130] = "SMUGGLE_SELL";
    Town[Town["SMUGGLE_RESET_BOUGHT"] = 2131] = "SMUGGLE_RESET_BOUGHT";
    Town[Town["SHIPYARD_RESET_ENCHANT_COUNT"] = 2132] = "SHIPYARD_RESET_ENCHANT_COUNT";
    Town[Town["FRIENDLY_ENCOUNT"] = 2133] = "FRIENDLY_ENCOUNT";
    Town[Town["FRIENDLY_ENCOUNT_SC"] = 2134] = "FRIENDLY_ENCOUNT_SC";
    Town[Town["FRIENDLY_ENCOUNT_CHOICE"] = 2135] = "FRIENDLY_ENCOUNT_CHOICE";
    Town[Town["FRIENDLY_ENCOUNT_END_SC"] = 2136] = "FRIENDLY_ENCOUNT_END_SC";
    Town[Town["FRIENDLY_ENCOUNT_CANCEL"] = 2137] = "FRIENDLY_ENCOUNT_CANCEL";
    Town[Town["UNPOPULAR_EVENT_CHANGED_SC"] = 2138] = "UNPOPULAR_EVENT_CHANGED_SC";
    Town[Town["CLASH_REGISTER"] = 2139] = "CLASH_REGISTER";
    Town[Town["CLASH_UNREGISTER"] = 2140] = "CLASH_UNREGISTER";
    Town[Town["CLASH_UNREGISTER_SC"] = 2141] = "CLASH_UNREGISTER_SC";
    Town[Town["CLASH_CHOICE"] = 2142] = "CLASH_CHOICE";
    Town[Town["CLASH_FOUND_NOTIFICATION_SC"] = 2143] = "CLASH_FOUND_NOTIFICATION_SC";
    Town[Town["CLASH_MATCHING_END_SC"] = 2144] = "CLASH_MATCHING_END_SC";
    Town[Town["CLASH_CLEAR_REJECT_COOL_DOWN"] = 2145] = "CLASH_CLEAR_REJECT_COOL_DOWN";
})(Town = exports.Town || (exports.Town = {}));
var Ocean;
(function (Ocean) {
    Ocean[Ocean["ARRIVE"] = 3001] = "ARRIVE";
    Ocean[Ocean["LOAD_COMPLETE"] = 3002] = "LOAD_COMPLETE";
    Ocean[Ocean["ENTER"] = 3003] = "ENTER";
    // BATTLE_START = 3004, 사용 안함
    // BATTLE_ACTION = 3005, 사용 안함
    // BATTLE_END = 3006, 사용 안함
    // BATTLE_RESUME = 3007, 사용 안함
    // BATTLE_GIVE_UP = 3008, 사용 안함
    Ocean[Ocean["ADD_FLEET_SC"] = 3009] = "ADD_FLEET_SC";
    Ocean[Ocean["REMOVE_OCEAN_ENTITY_SC"] = 3010] = "REMOVE_OCEAN_ENTITY_SC";
    Ocean[Ocean["UPDATE_ZONE_SYNC_DATA_SC"] = 3011] = "UPDATE_ZONE_SYNC_DATA_SC";
    Ocean[Ocean["MOVE_CS"] = 3012] = "MOVE_CS";
    Ocean[Ocean["NET_USER_MOVE_SC"] = 3013] = "NET_USER_MOVE_SC";
    Ocean[Ocean["INSPECT_TOWN"] = 3014] = "INSPECT_TOWN";
    Ocean[Ocean["DEPRECATED_3015"] = 3015] = "DEPRECATED_3015";
    Ocean[Ocean["BEGIN_AUTO_SAILING"] = 3016] = "BEGIN_AUTO_SAILING";
    Ocean[Ocean["END_AUTO_SAILING"] = 3017] = "END_AUTO_SAILING";
    Ocean[Ocean["UPDATE_AUTO_SAILING"] = 3018] = "UPDATE_AUTO_SAILING";
    // Notification for the prediction of any disaster
    Ocean[Ocean["PREDICT_DISASTER_SC"] = 3019] = "PREDICT_DISASTER_SC";
    // Notification for the changing of the sailing days
    Ocean[Ocean["CHANGE_SAILING_DAYS_SC"] = 3020] = "CHANGE_SAILING_DAYS_SC";
    // 재해 해소
    Ocean[Ocean["RESOLVE_DISASTER"] = 3021] = "RESOLVE_DISASTER";
    // 난파 알림
    Ocean[Ocean["WRECK_FLEET_SC"] = 3022] = "WRECK_FLEET_SC";
    // 난파 상태에서 회항
    // SAIL_BACK = 3023,  // 사용 안함.
    // BATTLE_LOAD_COMPLETE = 3024, 사용 안함
    // NPC spawn/despawn
    Ocean[Ocean["NPC_SPAWN_SC"] = 3025] = "NPC_SPAWN_SC";
    Ocean[Ocean["NPC_DESPAWN_SC"] = 3026] = "NPC_DESPAWN_SC";
    // NPC 가 싸움 걸어옴.
    Ocean[Ocean["ENCOUNT_BY_NPC_SC"] = 3027] = "ENCOUNT_BY_NPC_SC";
    // 인카운트 과정에서 유저의 선택.
    Ocean[Ocean["ENCOUNT_USER_CHOICE"] = 3028] = "ENCOUNT_USER_CHOICE";
    // 유저가 NPC 에게 인카운트 걸기.
    Ocean[Ocean["ENCOUNT_BY_USER"] = 3029] = "ENCOUNT_BY_USER";
    Ocean[Ocean["EMERGENCY_RECOVER_SHIPS"] = 3031] = "EMERGENCY_RECOVER_SHIPS";
    Ocean[Ocean["GAME_OVER_RESURRECT"] = 3032] = "GAME_OVER_RESURRECT";
    Ocean[Ocean["GET_USER_TOWN_STATE"] = 3033] = "GET_USER_TOWN_STATE";
    Ocean[Ocean["UPDATE_SYNC_DATA_SC"] = 3034] = "UPDATE_SYNC_DATA_SC";
    Ocean[Ocean["UPDATE_DISASTER_SC"] = 3035] = "UPDATE_DISASTER_SC";
    Ocean[Ocean["BUFF_SYNC_UPDATE_SC"] = 3036] = "BUFF_SYNC_UPDATE_SC";
    Ocean[Ocean["BUFF_SYNC_REMOVE_SC"] = 3037] = "BUFF_SYNC_REMOVE_SC";
    // 재해 방어 알림.(클라이언트 미구현, 패킷만 전송)
    Ocean[Ocean["DEFENSE_DISASTER_SC"] = 3038] = "DEFENSE_DISASTER_SC";
    // 인카운트 회피 (클라이언트 미구현, 패킷만 전송)
    Ocean[Ocean["ENCOUNT_AVOIDANCE_SC"] = 3039] = "ENCOUNT_AVOIDANCE_SC";
    //인카운트 다이얼로그가 닫혔을 때 호출.인카운트 종료시점을 클라이은트와 맞추기 위함.
    Ocean[Ocean["ENCOUNT_END"] = 3040] = "ENCOUNT_END";
    Ocean[Ocean["UPDATE_DAILY_CONSUME_SUPPLIES_SC"] = 3041] = "UPDATE_DAILY_CONSUME_SUPPLIES_SC";
    Ocean[Ocean["BUFF_ACTIVE_EFFECT_SC"] = 3042] = "BUFF_ACTIVE_EFFECT_SC";
    Ocean[Ocean["NPC_FLEET_MOVE_SC"] = 3043] = "NPC_FLEET_MOVE_SC";
    Ocean[Ocean["NPC_FLEET_STOP_SC"] = 3044] = "NPC_FLEET_STOP_SC";
    Ocean[Ocean["ZOOM_IN_NPC"] = 3045] = "ZOOM_IN_NPC";
    Ocean[Ocean["ZOOM_IN_USER"] = 3046] = "ZOOM_IN_USER";
    // 보험금 납입.
    Ocean[Ocean["PAY_INSURANCE_SC"] = 3047] = "PAY_INSURANCE_SC";
    Ocean[Ocean["REVEAL_WORLD_MAP_TILE"] = 3048] = "REVEAL_WORLD_MAP_TILE";
    Ocean[Ocean["DISCOVER_VILLAGE"] = 3049] = "DISCOVER_VILLAGE";
    Ocean[Ocean["NPC_FLEET_MOVE_SYNC_SC"] = 3050] = "NPC_FLEET_MOVE_SYNC_SC";
    // Doodad(해양오브젝트) spawn/despawn
    Ocean[Ocean["DOODAD_SPAWN_SC"] = 3051] = "DOODAD_SPAWN_SC";
    Ocean[Ocean["DOODAD_DESPAWN_SC"] = 3052] = "DOODAD_DESPAWN_SC";
    Ocean[Ocean["NET_USER_CHANGE_STATE_SC"] = 3053] = "NET_USER_CHANGE_STATE_SC";
    Ocean[Ocean["ADD_QUESTION_PLACE"] = 3054] = "ADD_QUESTION_PLACE";
    Ocean[Ocean["ACTIVATE_OCEAN_DOODAD"] = 3055] = "ACTIVATE_OCEAN_DOODAD";
    Ocean[Ocean["LAND_ENTER"] = 3056] = "LAND_ENTER";
    Ocean[Ocean["LAND_LEAVE"] = 3057] = "LAND_LEAVE";
    Ocean[Ocean["LAND_EXPLORE"] = 3058] = "LAND_EXPLORE";
    Ocean[Ocean["LAND_START_CS"] = 3059] = "LAND_START_CS";
    Ocean[Ocean["NET_USER_CHANGE_QUEST_SINGLE_MODE_SC"] = 3060] = "NET_USER_CHANGE_QUEST_SINGLE_MODE_SC";
    Ocean[Ocean["DOODAD_SYNC_UPDATE_SC"] = 3061] = "DOODAD_SYNC_UPDATE_SC";
    Ocean[Ocean["NET_USER_CHANGE_LAND_ANCHOR_SC"] = 3062] = "NET_USER_CHANGE_LAND_ANCHOR_SC";
    Ocean[Ocean["NET_USER_CHANGE_CUSTOMIZE_SC"] = 3063] = "NET_USER_CHANGE_CUSTOMIZE_SC";
    Ocean[Ocean["INSPECT_VILLAGE"] = 3064] = "INSPECT_VILLAGE";
    Ocean[Ocean["VILLAGE_ENTER"] = 3065] = "VILLAGE_ENTER";
    Ocean[Ocean["VILLAGE_LEAVE"] = 3066] = "VILLAGE_LEAVE";
    Ocean[Ocean["NET_USER_VILLAGE_ENTER_SC"] = 3067] = "NET_USER_VILLAGE_ENTER_SC";
    // 엘리트몬스터의 위치를 권역 내 모든 유저들에게 전달.
    Ocean[Ocean["WORLD_MAP_NPC_LOCATION_SC"] = 3069] = "WORLD_MAP_NPC_LOCATION_SC";
    // 해상가호 업데이트(시작/해제).(미사용. ADD_PROTECTION_SC, REMOVE_PROTECTION_SC로 대체)
    Ocean[Ocean["UPDATE_PROTECTION_SC"] = 3070] = "UPDATE_PROTECTION_SC";
    // 특정 위치로 텔레포트
    Ocean[Ocean["TELELPORT_TO_LOCATION_SC"] = 3071] = "TELELPORT_TO_LOCATION_SC";
    // 다른유저가 텔레포트 이동.
    Ocean[Ocean["NET_USER_TELEPORT_TO_LOCATION_SC"] = 3072] = "NET_USER_TELEPORT_TO_LOCATION_SC";
    Ocean[Ocean["WORLD_TILE_DEBUFF_NOTICE_SC"] = 3073] = "WORLD_TILE_DEBUFF_NOTICE_SC";
    Ocean[Ocean["ENCOUNT_BY_NET_USER_SC"] = 3074] = "ENCOUNT_BY_NET_USER_SC";
    Ocean[Ocean["OFFLINE_SAILING_MOVE_DELEGATE_START"] = 3075] = "OFFLINE_SAILING_MOVE_DELEGATE_START";
    Ocean[Ocean["OFFLINE_SAILING_MOVE_DELEGATE_UPDATE_SC"] = 3076] = "OFFLINE_SAILING_MOVE_DELEGATE_UPDATE_SC";
    Ocean[Ocean["UPDATE_DAILY_TRADE_GOODS_CHANGES_SC"] = 3077] = "UPDATE_DAILY_TRADE_GOODS_CHANGES_SC";
    Ocean[Ocean["ENCOUNT_CANCEL_SC"] = 3078] = "ENCOUNT_CANCEL_SC";
    Ocean[Ocean["SHOW_EMOTICON_INSTANT_CS"] = 3079] = "SHOW_EMOTICON_INSTANT_CS";
    Ocean[Ocean["SHOW_EMOTICON_INSTANT_SC"] = 3080] = "SHOW_EMOTICON_INSTANT_SC";
    // n초 후 출발한 마을앞으로 강제이동 알림.
    Ocean[Ocean["TELEPORT_TO_STARTED_TOWN_NOTICE_SC"] = 3081] = "TELEPORT_TO_STARTED_TOWN_NOTICE_SC";
    // 특정 마을 앞 위치로 텔레포트
    Ocean[Ocean["TELEPORT_TO_STARTED_TOWN_SC"] = 3082] = "TELEPORT_TO_STARTED_TOWN_SC";
    // 오프라인 항해 중단 처리
    Ocean[Ocean["OFFLINE_SAILING_STOP_SC"] = 3083] = "OFFLINE_SAILING_STOP_SC";
    Ocean[Ocean["RESOLVE_DISASTER_BY_DELEGATION_SC"] = 3084] = "RESOLVE_DISASTER_BY_DELEGATION_SC";
    // 상대방이 인카운트 선택시 통보받음
    Ocean[Ocean["ENCOUNT_ENEMY_CHOICE_SC"] = 3086] = "ENCOUNT_ENEMY_CHOICE_SC";
    // 강제로 인카운트를 종료.(전투여부는 패킷 데이터 따라 결정)
    Ocean[Ocean["ENCOUNT_END_FORCEDLY_SC"] = 3087] = "ENCOUNT_END_FORCEDLY_SC";
    // 낚시 시작 패킷
    Ocean[Ocean["FISHING_START"] = 3088] = "FISHING_START";
    Ocean[Ocean["FISHING_FIGHTING"] = 3089] = "FISHING_FIGHTING";
    Ocean[Ocean["FISHING_END"] = 3090] = "FISHING_END";
    Ocean[Ocean["VILLAGE_GIFT"] = 3091] = "VILLAGE_GIFT";
    Ocean[Ocean["VILLAGE_DRAFT_SAILOR"] = 3092] = "VILLAGE_DRAFT_SAILOR";
    Ocean[Ocean["VILLAGE_PLUNDER"] = 3093] = "VILLAGE_PLUNDER";
    Ocean[Ocean["VILLAGE_EXPLORE"] = 3094] = "VILLAGE_EXPLORE";
    Ocean[Ocean["ITEM_RESURRECT"] = 3095] = "ITEM_RESURRECT";
    Ocean[Ocean["ADD_DISASTER_SC"] = 3096] = "ADD_DISASTER_SC";
    Ocean[Ocean["REMOVE_DISASTER_SC"] = 3097] = "REMOVE_DISASTER_SC";
    Ocean[Ocean["ADD_PROTECTION_SC"] = 3098] = "ADD_PROTECTION_SC";
    Ocean[Ocean["REMOVE_PROTECTION_SC"] = 3099] = "REMOVE_PROTECTION_SC";
    Ocean[Ocean["ARRIVE_VILLAGE"] = 3100] = "ARRIVE_VILLAGE";
    Ocean[Ocean["NET_USER_CHANGE_COMPANY_LEVEL_SC"] = 3101] = "NET_USER_CHANGE_COMPANY_LEVEL_SC";
    Ocean[Ocean["NET_USER_CHANGE_KARMA_SC"] = 3102] = "NET_USER_CHANGE_KARMA_SC";
    // 선박 난파(함대난파 아님)
    Ocean[Ocean["SHIP_WRECKED_SC"] = 3103] = "SHIP_WRECKED_SC";
    // 해역 밝히기
    Ocean[Ocean["REVEAL_REGION"] = 3104] = "REVEAL_REGION";
    Ocean[Ocean["REVEAL_OCEAN_DOODADS"] = 3105] = "REVEAL_OCEAN_DOODADS";
    Ocean[Ocean["DISCOVER_OCEAN_DOODAD"] = 3106] = "DISCOVER_OCEAN_DOODAD";
    Ocean[Ocean["LAND_EXPLORE_QUERY_FEATURE"] = 3107] = "LAND_EXPLORE_QUERY_FEATURE";
    Ocean[Ocean["NET_USER_GUILD_LEAVE_SC"] = 3109] = "NET_USER_GUILD_LEAVE_SC";
    Ocean[Ocean["NET_USER_GUILD_UPDATE_SC"] = 3110] = "NET_USER_GUILD_UPDATE_SC";
    Ocean[Ocean["REDUCE_SHIP_LIFE_WHILE_SAILING_SC"] = 3111] = "REDUCE_SHIP_LIFE_WHILE_SAILING_SC";
    Ocean[Ocean["UPDATE_SAIL_SPEED_SC"] = 3113] = "UPDATE_SAIL_SPEED_SC";
    Ocean[Ocean["ELITE_NPC_SPAWN_NOTIFICATION_SC"] = 3114] = "ELITE_NPC_SPAWN_NOTIFICATION_SC";
    // 항해 경로에 의한 오프라인 자동 항해중 일괄 보급/수리
    Ocean[Ocean["APPLY_WAYPOINT_SUPPLY"] = 3115] = "APPLY_WAYPOINT_SUPPLY";
    Ocean[Ocean["UPDATE_AUTO_SAIL_OPTION_FOR_SERVER"] = 3116] = "UPDATE_AUTO_SAIL_OPTION_FOR_SERVER";
    Ocean[Ocean["SEARCH_CONSTELLATION"] = 3117] = "SEARCH_CONSTELLATION";
    Ocean[Ocean["DISCOVER_CONSTELLATION"] = 3118] = "DISCOVER_CONSTELLATION";
    Ocean[Ocean["NET_USER_CHANGE_USER_TITLE_SC"] = 3119] = "NET_USER_CHANGE_USER_TITLE_SC";
    Ocean[Ocean["VILLAGE_EXCHANGE"] = 3120] = "VILLAGE_EXCHANGE";
    Ocean[Ocean["VILLAGE_EXCHANGE_TRY_NEGO"] = 3121] = "VILLAGE_EXCHANGE_TRY_NEGO";
    Ocean[Ocean["VILLAGE_FRIENDSHIP_REWARD"] = 3122] = "VILLAGE_FRIENDSHIP_REWARD";
    Ocean[Ocean["UPDATE_DAILY_GET_WATER_SC"] = 3123] = "UPDATE_DAILY_GET_WATER_SC";
    Ocean[Ocean["DELEGATE_NAVI_QUERY_SC"] = 3124] = "DELEGATE_NAVI_QUERY_SC";
    Ocean[Ocean["DELEGATE_NAVI_QUERY_RESULT"] = 3125] = "DELEGATE_NAVI_QUERY_RESULT";
    Ocean[Ocean["SWEEP_LOCAL_NPC"] = 3126] = "SWEEP_LOCAL_NPC";
    Ocean[Ocean["NET_USER_NATION_CABINET_LEAVE_SC"] = 3127] = "NET_USER_NATION_CABINET_LEAVE_SC";
    Ocean[Ocean["NET_USER_NATION_CABINET_UPDATE_SC"] = 3128] = "NET_USER_NATION_CABINET_UPDATE_SC";
    Ocean[Ocean["SWEEP_RAID"] = 3129] = "SWEEP_RAID";
    Ocean[Ocean["SALVAGE_ENTER"] = 3130] = "SALVAGE_ENTER";
    Ocean[Ocean["SALVAGE_GAME_START"] = 3131] = "SALVAGE_GAME_START";
    Ocean[Ocean["SALVAGE_GAME_END"] = 3132] = "SALVAGE_GAME_END";
    Ocean[Ocean["SALVAGE_LEAVE"] = 3133] = "SALVAGE_LEAVE";
    Ocean[Ocean["NET_USER_SALVAGE_ENTER_SC"] = 3134] = "NET_USER_SALVAGE_ENTER_SC";
    Ocean[Ocean["GET_EXCHANGE_INFO"] = 3135] = "GET_EXCHANGE_INFO";
    Ocean[Ocean["ENTER_CONTINUOUS_SWEEP_REWARD"] = 3136] = "ENTER_CONTINUOUS_SWEEP_REWARD";
    Ocean[Ocean["LEAVE_CONTINUOUS_SWEEP_REWARD"] = 3137] = "LEAVE_CONTINUOUS_SWEEP_REWARD";
})(Ocean = exports.Ocean || (exports.Ocean = {}));
var Battle;
(function (Battle) {
    Battle[Battle["ACTION"] = 4001] = "ACTION";
    Battle[Battle["END"] = 4002] = "END";
    Battle[Battle["LOAD_COMPLETE"] = 4003] = "LOAD_COMPLETE";
    Battle[Battle["RESUME"] = 4004] = "RESUME";
    Battle[Battle["START"] = 4005] = "START";
    Battle[Battle["START_CHALLENGE"] = 4006] = "START_CHALLENGE";
    Battle[Battle["LOSE"] = 4007] = "LOSE";
    Battle[Battle["END_RAID"] = 4008] = "END_RAID";
    Battle[Battle["CANCEL"] = 4009] = "CANCEL";
    Battle[Battle["QUEST_RESUME_BLOCK"] = 4010] = "QUEST_RESUME_BLOCK";
    Battle[Battle["START_ARENA"] = 4011] = "START_ARENA";
    Battle[Battle["START_RAID"] = 4012] = "START_RAID";
    Battle[Battle["END_CHALLENGE"] = 4013] = "END_CHALLENGE";
    Battle[Battle["MULTI_ACTION_SC"] = 4014] = "MULTI_ACTION_SC";
    Battle[Battle["MULTI_STATE_SYNC_SC"] = 4015] = "MULTI_STATE_SYNC_SC";
    Battle[Battle["MULTI_ACTION_USER_CHANGE_CS"] = 4016] = "MULTI_ACTION_USER_CHANGE_CS";
    Battle[Battle["START_GUILD_RAID"] = 4017] = "START_GUILD_RAID";
    Battle[Battle["END_MULTI_PVP"] = 4018] = "END_MULTI_PVP";
    Battle[Battle["MULTI_SYNC_INIT"] = 4019] = "MULTI_SYNC_INIT";
    Battle[Battle["MULTI_CTRL_REQUEST"] = 4020] = "MULTI_CTRL_REQUEST";
    Battle[Battle["MULTI_AUTO_CHANGE_REQUEST_CS"] = 4021] = "MULTI_AUTO_CHANGE_REQUEST_CS";
    Battle[Battle["MULTI_ACTION_REQUEST"] = 4022] = "MULTI_ACTION_REQUEST";
    Battle[Battle["MULTI_ACTION_REQUEST_SC"] = 4023] = "MULTI_ACTION_REQUEST_SC";
    Battle[Battle["MULTI_TIMEOUT_NOTIFICATION"] = 4024] = "MULTI_TIMEOUT_NOTIFICATION";
    Battle[Battle["MULTI_ACTION_PHASE_CONFIRM_CS"] = 4025] = "MULTI_ACTION_PHASE_CONFIRM_CS";
    Battle[Battle["MULTI_GIVE_UP_CS"] = 4026] = "MULTI_GIVE_UP_CS";
    Battle[Battle["MULTI_ACTION_PHASE_PASS_CS"] = 4027] = "MULTI_ACTION_PHASE_PASS_CS";
    Battle[Battle["START_INFINITE_LIGHT_HOUSE"] = 4028] = "START_INFINITE_LIGHT_HOUSE";
    Battle[Battle["END_INFINITE_LIGHT_HOUSE"] = 4029] = "END_INFINITE_LIGHT_HOUSE";
    Battle[Battle["END_FRIENDLY"] = 4030] = "END_FRIENDLY";
    Battle[Battle["START_CLASH"] = 4031] = "START_CLASH";
    Battle[Battle["END_CLASH"] = 4032] = "END_CLASH";
})(Battle = exports.Battle || (exports.Battle = {}));
var Duel;
(function (Duel) {
    Duel[Duel["START"] = 4501] = "START";
    Duel[Duel["ACTION"] = 4502] = "ACTION";
    Duel[Duel["END"] = 4503] = "END";
    Duel[Duel["RESUME"] = 4504] = "RESUME";
})(Duel = exports.Duel || (exports.Duel = {}));
var Common;
(function (Common) {
    Common[Common["CREATE_FIRST_MATE"] = 5001] = "CREATE_FIRST_MATE";
    Common[Common["EQUIP_MATE_EQUIPMENT"] = 5002] = "EQUIP_MATE_EQUIPMENT";
    Common[Common["QUERY_NATION"] = 5003] = "QUERY_NATION";
    Common[Common["SELECT_NATION"] = 5004] = "SELECT_NATION";
    Common[Common["CHANGE_NATION"] = 5005] = "CHANGE_NATION";
    Common[Common["GET_USER_LIGHT_INFOS"] = 5006] = "GET_USER_LIGHT_INFOS";
    Common[Common["CHANGE_COMPANY_JOB"] = 5007] = "CHANGE_COMPANY_JOB";
    Common[Common["CHANGE_SHIP_NAME"] = 5009] = "CHANGE_SHIP_NAME";
    Common[Common["RELOCATE_SAILOR"] = 5010] = "RELOCATE_SAILOR";
    Common[Common["REMOVE_MATE_EQUIPMENT"] = 5011] = "REMOVE_MATE_EQUIPMENT";
    Common[Common["REMOVE_ITEM"] = 5012] = "REMOVE_ITEM";
    Common[Common["EXPAND_INVENTORY_SLOT"] = 5013] = "EXPAND_INVENTORY_SLOT";
    Common[Common["RECEIVE_MAILS"] = 5014] = "RECEIVE_MAILS";
    Common[Common["QUEST_ACCEPT"] = 5015] = "QUEST_ACCEPT";
    Common[Common["QUEST_START_BLOCK"] = 5016] = "QUEST_START_BLOCK";
    Common[Common["QUEST_ENTRUST"] = 5017] = "QUEST_ENTRUST";
    Common[Common["QUEST_END_BLOCK"] = 5018] = "QUEST_END_BLOCK";
    Common[Common["QUEST_REG_STORE"] = 5019] = "QUEST_REG_STORE";
    Common[Common["NEW_MAIL_SC"] = 5020] = "NEW_MAIL_SC";
    Common[Common["DELETE_MAILS"] = 5021] = "DELETE_MAILS";
    Common[Common["READ_MAILS"] = 5022] = "READ_MAILS";
    Common[Common["REMOVE_CARGO"] = 5023] = "REMOVE_CARGO";
    Common[Common["RELOAD_CARGO"] = 5024] = "RELOAD_CARGO";
    Common[Common["GET_TOWN_TRADE_PRICE_PERCENTS"] = 5025] = "GET_TOWN_TRADE_PRICE_PERCENTS";
    Common[Common["QUEST_DROP"] = 5026] = "QUEST_DROP";
    Common[Common["USE_ITEM"] = 5027] = "USE_ITEM";
    Common[Common["QUEST_GOTO"] = 5028] = "QUEST_GOTO";
    Common[Common["LOCK_SHIP_SLOTS"] = 5029] = "LOCK_SHIP_SLOTS";
    Common[Common["EQUIP_MATE_EQUIPMENTS"] = 5030] = "EQUIP_MATE_EQUIPMENTS";
    Common[Common["BUY_ADMIRAL"] = 5031] = "BUY_ADMIRAL";
    Common[Common["BUFF_UPDATE_SC"] = 5032] = "BUFF_UPDATE_SC";
    Common[Common["MATE_STATE_UPDATE_SC"] = 5033] = "MATE_STATE_UPDATE_SC";
    Common[Common["RECOVER_INJURY"] = 5034] = "RECOVER_INJURY";
    Common[Common["INCREASE_ROYALTY"] = 5035] = "INCREASE_ROYALTY";
    Common[Common["MEET_MATES"] = 5036] = "MEET_MATES";
    Common[Common["TALK_MATES"] = 5038] = "TALK_MATES";
    Common[Common["SET_CARGO_LOAD_PRESET"] = 5039] = "SET_CARGO_LOAD_PRESET";
    Common[Common["QUEST_OPERATE_REG"] = 5040] = "QUEST_OPERATE_REG";
    Common[Common["RECOVER_SHIPS"] = 5041] = "RECOVER_SHIPS";
    Common[Common["RESET_TASK"] = 5042] = "RESET_TASK";
    Common[Common["RECEIVE_ACHIEVEMENT_REWARD"] = 5043] = "RECEIVE_ACHIEVEMENT_REWARD";
    Common[Common["RECEIVE_TASK_REWARD"] = 5044] = "RECEIVE_TASK_REWARD";
    Common[Common["UPDATE_TRADE_EVENT_SC"] = 5045] = "UPDATE_TRADE_EVENT_SC";
    Common[Common["GET_TOWN_NATION_SHARE_POINTS"] = 5046] = "GET_TOWN_NATION_SHARE_POINTS";
    Common[Common["COMPLETE_TASK_IMMEDIATELY"] = 5047] = "COMPLETE_TASK_IMMEDIATELY";
    Common[Common["RECEIVE_TASK_CATEGORY_REWARD"] = 5048] = "RECEIVE_TASK_CATEGORY_REWARD";
    Common[Common["RECEIVE_ACHIEVEMENT_POINT_REWARD"] = 5049] = "RECEIVE_ACHIEVEMENT_POINT_REWARD";
    Common[Common["RECEIVE_HOT_TIME"] = 5050] = "RECEIVE_HOT_TIME";
    Common[Common["BUY_TAX_FREE_PERMIT"] = 5051] = "BUY_TAX_FREE_PERMIT";
    Common[Common["GET_WORLD_MAP_TOWN_INFO"] = 5052] = "GET_WORLD_MAP_TOWN_INFO";
    Common[Common["CONSUME_QUEST_ENERGY"] = 5053] = "CONSUME_QUEST_ENERGY";
    Common[Common["EXPAND_REQUEST_SLOT"] = 5054] = "EXPAND_REQUEST_SLOT";
    Common[Common["GET_NATION_TOWNS"] = 5055] = "GET_NATION_TOWNS";
    Common[Common["REMOVE_EXPIRED_REQUEST_SLOT"] = 5056] = "REMOVE_EXPIRED_REQUEST_SLOT";
    Common[Common["CASH_SHOP_GET_PRODUCTS"] = 5057] = "CASH_SHOP_GET_PRODUCTS";
    Common[Common["CASH_SHOP_BUY_WITHOUT_PURCHASE"] = 5058] = "CASH_SHOP_BUY_WITHOUT_PURCHASE";
    Common[Common["GET_REGION_OCCUPATIONS"] = 5059] = "GET_REGION_OCCUPATIONS";
    Common[Common["CASH_SHOP_RECEIVE_GACHA_BOX_GUARANTEE"] = 5060] = "CASH_SHOP_RECEIVE_GACHA_BOX_GUARANTEE";
    Common[Common["CHAT_INIT"] = 5061] = "CHAT_INIT";
    Common[Common["UPDATE_ACHIEVEMNT_SC"] = 5063] = "UPDATE_ACHIEVEMNT_SC";
    Common[Common["GET_USER_LIGHT_INFO_BY_NAME"] = 5064] = "GET_USER_LIGHT_INFO_BY_NAME";
    Common[Common["GET_USER_LIGHT_INFOS_ONLY_IS_ONLINE"] = 5065] = "GET_USER_LIGHT_INFOS_ONLY_IS_ONLINE";
    Common[Common["ALLOW_RX_TEXT_CHAT_ON_CELLULAR_DATA_NET"] = 5066] = "ALLOW_RX_TEXT_CHAT_ON_CELLULAR_DATA_NET";
    Common[Common["UNLOCK_SHIP_CUSTOMIZING"] = 5067] = "UNLOCK_SHIP_CUSTOMIZING";
    Common[Common["CUSTOMIZE_SHIP"] = 5068] = "CUSTOMIZE_SHIP";
    Common[Common["CHAT_JOIN_CHANNEL"] = 5069] = "CHAT_JOIN_CHANNEL";
    Common[Common["MATE_START_AWAKEN"] = 5070] = "MATE_START_AWAKEN";
    Common[Common["MATE_START_LEARN_PASSIVE"] = 5071] = "MATE_START_LEARN_PASSIVE";
    Common[Common["QUEST_ITEM_SET_QUEST"] = 5072] = "QUEST_ITEM_SET_QUEST";
    Common[Common["QUEST_ITEM_USE"] = 5073] = "QUEST_ITEM_USE";
    // MATE_LEARN_ORDER = 5074, -- 기획 변경으로 인해 사용 안함.
    Common[Common["ATTENDANCE"] = 5075] = "ATTENDANCE";
    Common[Common["MATE_EQUIP_PASSIVES"] = 5076] = "MATE_EQUIP_PASSIVES";
    Common[Common["QUEST_SET_PAUSE"] = 5077] = "QUEST_SET_PAUSE";
    Common[Common["CUSTOMIZE_MATE_EQUIP"] = 5078] = "CUSTOMIZE_MATE_EQUIP";
    Common[Common["UNLOCK_MATE_EQUIP_COLOR"] = 5079] = "UNLOCK_MATE_EQUIP_COLOR";
    Common[Common["PASSIVE_UPDATE_SC"] = 5080] = "PASSIVE_UPDATE_SC";
    Common[Common["BUBBLE_EVENT_NEW_SC"] = 5081] = "BUBBLE_EVENT_NEW_SC";
    Common[Common["BUBBLE_EVENT_REMOVE_SC"] = 5082] = "BUBBLE_EVENT_REMOVE_SC";
    Common[Common["BUBBLE_EVENT_ACTION"] = 5083] = "BUBBLE_EVENT_ACTION";
    //SHIP_SLOT_ITEM_EQUIP = 5084, //n 개를 처리하는 SHIP_SLOT_ITEMS_EQUIP 추가로 사용 안함.
    Common[Common["SHIP_SLOT_ITEM_SELL"] = 5085] = "SHIP_SLOT_ITEM_SELL";
    Common[Common["SHIP_SLOT_ITEM_REMOVE"] = 5086] = "SHIP_SLOT_ITEM_REMOVE";
    // QUEST_ITEM_DROP = 5087, removeItem 에서 모두 처리.
    Common[Common["SET_GAME_OPTION_PUSH_NOTIFICATION_CS"] = 5088] = "SET_GAME_OPTION_PUSH_NOTIFICATION_CS";
    Common[Common["GET_GAME_OPTION_PUSH_NOTIFICATION"] = 5089] = "GET_GAME_OPTION_PUSH_NOTIFICATION";
    Common[Common["CASH_SHOP_SYNC_SC"] = 5090] = "CASH_SHOP_SYNC_SC";
    Common[Common["TOOGLE_ENCOUNT_SHIELD_ACTIVATION"] = 5091] = "TOOGLE_ENCOUNT_SHIELD_ACTIVATION";
    Common[Common["UPDATE_ENCOUNT_SHIELD_SC"] = 5092] = "UPDATE_ENCOUNT_SHIELD_SC";
    Common[Common["NEW_LINE_MAIL_SC"] = 5093] = "NEW_LINE_MAIL_SC";
    Common[Common["MATE_INJURY_BUFF_SC"] = 5094] = "MATE_INJURY_BUFF_SC";
    Common[Common["BATTLE_FORMATION_ACQUIRE"] = 5095] = "BATTLE_FORMATION_ACQUIRE";
    Common[Common["BATTLE_FORMATION_CHANGE"] = 5096] = "BATTLE_FORMATION_CHANGE";
    Common[Common["QUERY_NEAREST_TOWN"] = 5097] = "QUERY_NEAREST_TOWN";
    Common[Common["USE_WORLD_SKILL"] = 5098] = "USE_WORLD_SKILL";
    Common[Common["REGISTER_COLLECTION"] = 5099] = "REGISTER_COLLECTION";
    Common[Common["SHIPYARD_SHIP_BUILDING_DECREASE_EXPIRE_TIME"] = 5100] = "SHIPYARD_SHIP_BUILDING_DECREASE_EXPIRE_TIME";
    Common[Common["SHIPYARD_SHIP_BUILDING_COMPLETE_EXPIRE_TIME"] = 5101] = "SHIPYARD_SHIP_BUILDING_COMPLETE_EXPIRE_TIME";
    Common[Common["SHIPYARD_SHIP_BUILDING_DELIVER"] = 5102] = "SHIPYARD_SHIP_BUILDING_DELIVER";
    Common[Common["RECEIVE_EVENT_MISSION_REWARD"] = 5103] = "RECEIVE_EVENT_MISSION_REWARD";
    Common[Common["MATE_COMPLETE_AWAKEN"] = 5104] = "MATE_COMPLETE_AWAKEN";
    Common[Common["MATE_COMPLETE_AWAKEN_IMMEDIATELY"] = 5105] = "MATE_COMPLETE_AWAKEN_IMMEDIATELY";
    Common[Common["MATE_REDUCE_AWAKEN_TIME"] = 5106] = "MATE_REDUCE_AWAKEN_TIME";
    Common[Common["FIND_DISTANCE_FROM_TILE_TO_TOWN"] = 5107] = "FIND_DISTANCE_FROM_TILE_TO_TOWN";
    Common[Common["GET_ADMIRAL_PROFILE"] = 5108] = "GET_ADMIRAL_PROFILE";
    Common[Common["GET_FLAG_SHIP_PROFILE"] = 5109] = "GET_FLAG_SHIP_PROFILE";
    Common[Common["SET_OPTION_PROFILES"] = 5110] = "SET_OPTION_PROFILES";
    Common[Common["MATE_COMPLETE_LEARN_PASSIVE"] = 5111] = "MATE_COMPLETE_LEARN_PASSIVE";
    Common[Common["MATE_COMPLETE_LEARN_PASSIVE_IMMEDIATELY"] = 5112] = "MATE_COMPLETE_LEARN_PASSIVE_IMMEDIATELY";
    Common[Common["MATE_REDUCE_LEARN_PASSIVE_TIME"] = 5113] = "MATE_REDUCE_LEARN_PASSIVE_TIME";
    Common[Common["AUCTION_LOAD_MY_PRODUCTS"] = 5114] = "AUCTION_LOAD_MY_PRODUCTS";
    Common[Common["AUCTION_REGISTER"] = 5115] = "AUCTION_REGISTER";
    Common[Common["AUCTION_QUERY_PRODUCTS"] = 5116] = "AUCTION_QUERY_PRODUCTS";
    Common[Common["AUCTION_BUY"] = 5117] = "AUCTION_BUY";
    Common[Common["AUCTION_CANCEL"] = 5118] = "AUCTION_CANCEL";
    Common[Common["AUCTION_QUERY_SALE_PRICES"] = 5119] = "AUCTION_QUERY_SALE_PRICES";
    Common[Common["AUCTION_REREGISTER"] = 5120] = "AUCTION_REREGISTER";
    Common[Common["AUCTION_RECEIVE_PROCEEDS"] = 5121] = "AUCTION_RECEIVE_PROCEEDS";
    Common[Common["REFRESH_WEEKLY_EVENT"] = 5122] = "REFRESH_WEEKLY_EVENT";
    Common[Common["UPDATE_ADJUTANT_DELEGATION_CONFIG"] = 5123] = "UPDATE_ADJUTANT_DELEGATION_CONFIG";
    Common[Common["AUCTION_UPDATE_MY_PRODUCT_STATE_SC"] = 5124] = "AUCTION_UPDATE_MY_PRODUCT_STATE_SC";
    Common[Common["REPAIR_DURABILITY_SHIPS"] = 5125] = "REPAIR_DURABILITY_SHIPS";
    Common[Common["AUCTION_QUERY_SHIP_PRODUCTS"] = 5126] = "AUCTION_QUERY_SHIP_PRODUCTS";
    Common[Common["BILLING_QUERY_SALES_LIST"] = 5127] = "BILLING_QUERY_SALES_LIST";
    Common[Common["BILLING_RESERVE_PURCHASE"] = 5128] = "BILLING_RESERVE_PURCHASE";
    Common[Common["BILLING_CANCEL_RESERVED_PURCHASE"] = 5129] = "BILLING_CANCEL_RESERVED_PURCHASE";
    Common[Common["BILLING_COMPLETE_RESERVED_PURCHASE_AND_GIVE"] = 5130] = "BILLING_COMPLETE_RESERVED_PURCHASE_AND_GIVE";
    Common[Common["BILLING_QUERY_PURCHASE_DETAIL"] = 5131] = "BILLING_QUERY_PURCHASE_DETAIL";
    Common[Common["BILLING_QUERY_PRODUCT_GIVE_ITEM_DETAIL"] = 5132] = "BILLING_QUERY_PRODUCT_GIVE_ITEM_DETAIL";
    Common[Common["BILLING_CHARGE_BY_PURCHASE_PRODUCT"] = 5133] = "BILLING_CHARGE_BY_PURCHASE_PRODUCT";
    Common[Common["SET_PING_TIME_OUT"] = 5134] = "SET_PING_TIME_OUT";
    Common[Common["ARENA_ENTER"] = 5135] = "ARENA_ENTER";
    Common[Common["ARENA_MATCH_LIST_REFRESH"] = 5136] = "ARENA_MATCH_LIST_REFRESH";
    Common[Common["ARENA_TICKET_BUY"] = 5137] = "ARENA_TICKET_BUY";
    Common[Common["ARENA_REWARD_RECEIVE"] = 5138] = "ARENA_REWARD_RECEIVE";
    Common[Common["ARENA_UPDATE_SC"] = 5139] = "ARENA_UPDATE_SC";
    Common[Common["ARENA_TICKET_UPDATE_SC"] = 5140] = "ARENA_TICKET_UPDATE_SC";
    Common[Common["ARENA_FLEET_UPDATE"] = 5141] = "ARENA_FLEET_UPDATE";
    Common[Common["QUERY_MY_MAYOR_TOWNS"] = 5142] = "QUERY_MY_MAYOR_TOWNS";
    Common[Common["UPDATE_SHIELDS"] = 5144] = "UPDATE_SHIELDS";
    Common[Common["QUERY_VILLAGE"] = 5145] = "QUERY_VILLAGE";
    Common[Common["PROLOGUE_START"] = 5146] = "PROLOGUE_START";
    Common[Common["INCREASE_LOYALTY_USE_ITEM"] = 5147] = "INCREASE_LOYALTY_USE_ITEM";
    Common[Common["QUERY_REPORTED_WORLD_MAP_TILES"] = 5148] = "QUERY_REPORTED_WORLD_MAP_TILES";
    Common[Common["CASH_SHOP_BUY_DAILY_PRODUCT"] = 5149] = "CASH_SHOP_BUY_DAILY_PRODUCT";
    Common[Common["QUERY_ACHIEVEMENTS"] = 5150] = "QUERY_ACHIEVEMENTS";
    Common[Common["CASH_SHOP_GET_DAILY_SALE"] = 5151] = "CASH_SHOP_GET_DAILY_SALE";
    Common[Common["QUERY_RED_GEM_DETAIL"] = 5152] = "QUERY_RED_GEM_DETAIL";
    Common[Common["AUCTION_QUERY_CLOSED"] = 5153] = "AUCTION_QUERY_CLOSED";
    Common[Common["QUERY_LOCKED_TOWN_TRADE_GOODS"] = 5154] = "QUERY_LOCKED_TOWN_TRADE_GOODS";
    Common[Common["RECEIVE_PASS_EVENT_MISSION_REWARD"] = 5156] = "RECEIVE_PASS_EVENT_MISSION_REWARD";
    Common[Common["BUY_PASS_EVENT_EXP"] = 5157] = "BUY_PASS_EVENT_EXP";
    Common[Common["BILLING_COMPLETE_RESERVED_PURCHASE"] = 5158] = "BILLING_COMPLETE_RESERVED_PURCHASE";
    Common[Common["BILLING_QUERY_INVEN_PURCHASE_LIST"] = 5159] = "BILLING_QUERY_INVEN_PURCHASE_LIST";
    Common[Common["BILLING_RECEIVE_INVEN_PURCHASES"] = 5160] = "BILLING_RECEIVE_INVEN_PURCHASES";
    Common[Common["EVENT_SHOP_BUY"] = 5161] = "EVENT_SHOP_BUY";
    Common[Common["EVENT_SHOP_GET_PRODUCTS"] = 5162] = "EVENT_SHOP_GET_PRODUCTS";
    Common[Common["BILLING_QUERY_LATEST_RESERVED_PURCHASE"] = 5163] = "BILLING_QUERY_LATEST_RESERVED_PURCHASE";
    Common[Common["REPORT_BAD_CHATTING"] = 5164] = "REPORT_BAD_CHATTING";
    Common[Common["GET_REPORTED_BAD_CHATTING_LIST"] = 5165] = "GET_REPORTED_BAD_CHATTING_LIST";
    Common[Common["CHAT_MUTE_USER"] = 5166] = "CHAT_MUTE_USER";
    Common[Common["CHAT_UNMUTE_USER"] = 5167] = "CHAT_UNMUTE_USER";
    Common[Common["SET_SAIL_WAYPOINT"] = 5168] = "SET_SAIL_WAYPOINT";
    Common[Common["QUERY_ALL_TOWN_INVESTMENTS"] = 5169] = "QUERY_ALL_TOWN_INVESTMENTS";
    Common[Common["REMOVE_SAIL_WAYPOINT"] = 5170] = "REMOVE_SAIL_WAYPOINT";
    Common[Common["QUERY_TRADE_AREA"] = 5171] = "QUERY_TRADE_AREA";
    Common[Common["CHANGE_FLEET_PRESET_NAME"] = 5172] = "CHANGE_FLEET_PRESET_NAME";
    Common[Common["LOAD_FLEET_PRESET"] = 5173] = "LOAD_FLEET_PRESET";
    Common[Common["ADD_MATE_EXP_USE_ITEM"] = 5174] = "ADD_MATE_EXP_USE_ITEM";
    Common[Common["LOCK_SHIP"] = 5175] = "LOCK_SHIP";
    Common[Common["EVENT_GET_MINI_BOARD_GAME"] = 5176] = "EVENT_GET_MINI_BOARD_GAME";
    Common[Common["EVENT_PLAY_MINI_BOARD_GAME"] = 5177] = "EVENT_PLAY_MINI_BOARD_GAME";
    Common[Common["HIDE_EQUIP_SLOTS"] = 5178] = "HIDE_EQUIP_SLOTS";
    Common[Common["BILLING_STEAM_PURCHASE_INIT_TXN"] = 5179] = "BILLING_STEAM_PURCHASE_INIT_TXN";
    Common[Common["QUERY_CRAZE_EVENT_BUDGET"] = 5180] = "QUERY_CRAZE_EVENT_BUDGET";
    Common[Common["SAVE_FLEET_PRESET"] = 5181] = "SAVE_FLEET_PRESET";
    Common[Common["DELETE_FLEET_PRESET"] = 5182] = "DELETE_FLEET_PRESET";
    Common[Common["FLEET_DISPATCH_START"] = 5183] = "FLEET_DISPATCH_START";
    Common[Common["FLEET_DISPATCH_CANCEL"] = 5184] = "FLEET_DISPATCH_CANCEL";
    Common[Common["FLEET_DISPATCH_END"] = 5185] = "FLEET_DISPATCH_END";
    Common[Common["FLEET_DISPATCH_QUERY_IN_PROGRESS_STATE"] = 5186] = "FLEET_DISPATCH_QUERY_IN_PROGRESS_STATE";
    Common[Common["FLEET_DISPATCH_REWARD_CHOICE"] = 5187] = "FLEET_DISPATCH_REWARD_CHOICE";
    Common[Common["FLEET_DISPATCH_REWARD_RECEIVE"] = 5188] = "FLEET_DISPATCH_REWARD_RECEIVE";
    Common[Common["FLEET_DISPATCH_SLOT_OPEN"] = 5189] = "FLEET_DISPATCH_SLOT_OPEN";
    Common[Common["FLEET_DISPATCH_DECREASE_EXPIRE_TIME"] = 5190] = "FLEET_DISPATCH_DECREASE_EXPIRE_TIME";
    Common[Common["GET_WORLD_RANKING"] = 5191] = "GET_WORLD_RANKING";
    Common[Common["MATES_COMPLETE_AWAKEN"] = 5192] = "MATES_COMPLETE_AWAKEN";
    Common[Common["MATES_COMPLETE_LEARN_PASSIVE"] = 5193] = "MATES_COMPLETE_LEARN_PASSIVE";
    Common[Common["TOGGLE_BATTLE_CONTINUOUS"] = 5194] = "TOGGLE_BATTLE_CONTINUOUS";
    Common[Common["UPDATE_BATTLE_CONTINUOUS_RESULT"] = 5195] = "UPDATE_BATTLE_CONTINUOUS_RESULT";
    Common[Common["TRANSLATE_CHAT"] = 5196] = "TRANSLATE_CHAT";
    Common[Common["BUY_CHAT_TRANSLATION_COUNT"] = 5197] = "BUY_CHAT_TRANSLATION_COUNT";
    Common[Common["BUY_ATTENDANCE"] = 5198] = "BUY_ATTENDANCE";
    Common[Common["RECEIVE_DAILY_SUBSCRIPTION_REWARD"] = 5199] = "RECEIVE_DAILY_SUBSCRIPTION_REWARD";
    Common[Common["PUBLISH_TOAST_MESSAGE_CS"] = 5200] = "PUBLISH_TOAST_MESSAGE_CS";
    Common[Common["BROADCAST_TOAST_MSG_SC"] = 5201] = "BROADCAST_TOAST_MSG_SC";
    Common[Common["MATE_START_TRAINING"] = 5202] = "MATE_START_TRAINING";
    Common[Common["MATES_COMPLETE_TRAINING"] = 5203] = "MATES_COMPLETE_TRAINING";
    Common[Common["MATE_COMPLETE_TRAINING_IMMEDIATELY"] = 5204] = "MATE_COMPLETE_TRAINING_IMMEDIATELY";
    Common[Common["MATE_REDUCE_TRAINING_TIME"] = 5205] = "MATE_REDUCE_TRAINING_TIME";
    Common[Common["MATE_USE_TRAINING_POINTS"] = 5206] = "MATE_USE_TRAINING_POINTS";
    Common[Common["MATE_RESET_TRAINING_POINTS"] = 5207] = "MATE_RESET_TRAINING_POINTS";
    Common[Common["OPEN_HOT_SPOT_PRODUCT"] = 5208] = "OPEN_HOT_SPOT_PRODUCT";
    Common[Common["GET_WORLD_EVENT_RANKING"] = 5209] = "GET_WORLD_EVENT_RANKING";
    Common[Common["RECEIVE_EVENT_RANKING_MISSION_REWARD"] = 5210] = "RECEIVE_EVENT_RANKING_MISSION_REWARD";
    Common[Common["RECEIVE_EVENT_RANKING_REWARD"] = 5211] = "RECEIVE_EVENT_RANKING_REWARD";
    Common[Common["SHIP_SLOT_ITEM_LOCK"] = 5212] = "SHIP_SLOT_ITEM_LOCK";
    Common[Common["RECEIVE_DISCOVERY_REWARD"] = 5213] = "RECEIVE_DISCOVERY_REWARD";
    Common[Common["GET_SAILING_DIARIES"] = 5214] = "GET_SAILING_DIARIES";
    Common[Common["EQUIP_MATE_ILLUST"] = 5215] = "EQUIP_MATE_ILLUST";
    Common[Common["RECEIVE_FISH_SIZE_REWARDS"] = 5216] = "RECEIVE_FISH_SIZE_REWARDS";
    Common[Common["QUERY_ALL_INVESTED_TOWNS_GUILD_SHARE_POINTS"] = 5217] = "QUERY_ALL_INVESTED_TOWNS_GUILD_SHARE_POINTS";
    Common[Common["QUERY_GUILD_TOWNS"] = 5218] = "QUERY_GUILD_TOWNS";
    Common[Common["QUERY_GUILD_INVESTMENT_USER_SCORES"] = 5219] = "QUERY_GUILD_INVESTMENT_USER_SCORES";
    Common[Common["UPDATE_HOT_TIME_BUFF"] = 5220] = "UPDATE_HOT_TIME_BUFF";
    Common[Common["QUERY_TOWNS_FIRST_GUILD_SHARE_POINT"] = 5221] = "QUERY_TOWNS_FIRST_GUILD_SHARE_POINT";
    Common[Common["CHANGE_USER_TITLE"] = 5222] = "CHANGE_USER_TITLE";
    Common[Common["EQUIP_BATTLE_QUICK_SKILL"] = 5223] = "EQUIP_BATTLE_QUICK_SKILL";
    Common[Common["UPDATE_USER_TITLE_SC"] = 5224] = "UPDATE_USER_TITLE_SC";
    Common[Common["QUERY_NATION_ELECTION_SYNC"] = 5225] = "QUERY_NATION_ELECTION_SYNC";
    Common[Common["REGISTER_NATION_ELECTION_CANDIDATE"] = 5226] = "REGISTER_NATION_ELECTION_CANDIDATE";
    Common[Common["MODIFY_NATION_ELECTION_CANDIDATE_DATA"] = 5227] = "MODIFY_NATION_ELECTION_CANDIDATE_DATA";
    Common[Common["VOTE_TO_NATION_ELECTION_CANDIDATE"] = 5228] = "VOTE_TO_NATION_ELECTION_CANDIDATE";
    Common[Common["APPLY_FLEET_PRESET"] = 5229] = "APPLY_FLEET_PRESET";
    Common[Common["SHIP_BUILDING_CANCEL"] = 5230] = "SHIP_BUILDING_CANCEL";
    Common[Common["RECEIVE_NATION_ELECTION_VOTE_REWARD"] = 5231] = "RECEIVE_NATION_ELECTION_VOTE_REWARD";
    Common[Common["RECEIVE_NATION_GOAL_PROMISE_REWARD"] = 5232] = "RECEIVE_NATION_GOAL_PROMISE_REWARD";
    Common[Common["CHANGE_CARGO_PRESET_NAME"] = 5233] = "CHANGE_CARGO_PRESET_NAME";
    Common[Common["CHANGE_EQUIPMENT_TO_COSTUME"] = 5234] = "CHANGE_EQUIPMENT_TO_COSTUME";
    Common[Common["CHANGE_CARGO_PRESET_ID_IN_FLEET_PRESET"] = 5235] = "CHANGE_CARGO_PRESET_ID_IN_FLEET_PRESET";
    Common[Common["SHIP_BUILDING_TERMS_INFO"] = 5236] = "SHIP_BUILDING_TERMS_INFO";
    Common[Common["REMOTE_CREATE_SHIP"] = 5237] = "REMOTE_CREATE_SHIP";
    Common[Common["UPDATE_NATION_POLICIES_SC"] = 5238] = "UPDATE_NATION_POLICIES_SC";
    Common[Common["UPGRADE_NATION_POLICY_STEP"] = 5239] = "UPGRADE_NATION_POLICY_STEP";
    Common[Common["DONATE_TO_NATION_BUDGET"] = 5240] = "DONATE_TO_NATION_BUDGET";
    Common[Common["QUEST_STATE_CHANGE"] = 5241] = "QUEST_STATE_CHANGE";
    Common[Common["BUY_NATION_SUPPORT_SHOP"] = 5242] = "BUY_NATION_SUPPORT_SHOP";
    Common[Common["NATION_SUPPORT_SHOP_BOUGHT_SC"] = 5243] = "NATION_SUPPORT_SHOP_BOUGHT_SC";
    Common[Common["RECEIVE_NATION_SUPPORT_SHOP_REWARD"] = 5244] = "RECEIVE_NATION_SUPPORT_SHOP_REWARD";
    Common[Common["ENABLE_LAST_WEEK_INVESTMENT_REWARD"] = 5245] = "ENABLE_LAST_WEEK_INVESTMENT_REWARD";
    Common[Common["GET_NATION_WEEKLY_DONATION_RANKS"] = 5246] = "GET_NATION_WEEKLY_DONATION_RANKS";
    Common[Common["RECEIVE_NATION_WEEKLY_DONATION_RANK_REWARD"] = 5247] = "RECEIVE_NATION_WEEKLY_DONATION_RANK_REWARD";
    Common[Common["RECEIVE_LAST_WEEK_INVESTMENT_REWARD"] = 5248] = "RECEIVE_LAST_WEEK_INVESTMENT_REWARD";
    Common[Common["JOIN_TO_NATION_CABINET_APPLICANT"] = 5249] = "JOIN_TO_NATION_CABINET_APPLICANT";
    Common[Common["APPOINT_NATION_CABINET_MEMBER"] = 5250] = "APPOINT_NATION_CABINET_MEMBER";
    Common[Common["DISMISS_NATION_CABINET_MEMBER"] = 5251] = "DISMISS_NATION_CABINET_MEMBER";
    Common[Common["WRITE_NATION_NOTICE"] = 5252] = "WRITE_NATION_NOTICE";
    Common[Common["SET_NATION_WAGE_RATE"] = 5253] = "SET_NATION_WAGE_RATE";
    Common[Common["LOAD_NATION_CABINET_APPLICANTS"] = 5254] = "LOAD_NATION_CABINET_APPLICANTS";
    Common[Common["NATION_ELECTION_UPDATE_SC"] = 5255] = "NATION_ELECTION_UPDATE_SC";
    Common[Common["WRITE_NATION_PRIME_MINISTER_THOUGHT"] = 5256] = "WRITE_NATION_PRIME_MINISTER_THOUGHT";
    // SET_FLEET_PRESET_IS_OPEN = 5257,
    // SET_FLEET_PRESET_IS_LOCKED = 5258,
    Common[Common["GET_OPENED_FLEET_PRESET"] = 5259] = "GET_OPENED_FLEET_PRESET";
    Common[Common["SET_REPRESENTED_MATE"] = 5260] = "SET_REPRESENTED_MATE";
    Common[Common["QUEST_REG_UTCTIME"] = 5261] = "QUEST_REG_UTCTIME";
    Common[Common["GET_MARKET_EVENT_INFO"] = 5262] = "GET_MARKET_EVENT_INFO";
    Common[Common["SET_SIDEKICK_MATE"] = 5263] = "SET_SIDEKICK_MATE";
    Common[Common["SET_SIDEKICK_PET"] = 5264] = "SET_SIDEKICK_PET";
    Common[Common["RECEIVE_EVENT_RANKING_GUILD_REWARD"] = 5265] = "RECEIVE_EVENT_RANKING_GUILD_REWARD";
    Common[Common["QUERY_ENCHANT_TOAST_CS"] = 5266] = "QUERY_ENCHANT_TOAST_CS";
    Common[Common["SET_FAVORITE_MATE"] = 5267] = "SET_FAVORITE_MATE";
    Common[Common["GET_MY_BLIND_BID_INVEN"] = 5268] = "GET_MY_BLIND_BID_INVEN";
    Common[Common["TRY_BLIND_BID"] = 5269] = "TRY_BLIND_BID";
    Common[Common["GET_BLIND_BID_COMPLETED_PRODUCTS"] = 5270] = "GET_BLIND_BID_COMPLETED_PRODUCTS";
    Common[Common["RECEIVE_BLIND_BID_WINNER_REWARD"] = 5271] = "RECEIVE_BLIND_BID_WINNER_REWARD";
    Common[Common["BLIND_BID_TICKET_BUY"] = 5272] = "BLIND_BID_TICKET_BUY";
    Common[Common["REFUND_BLIND_BID"] = 5273] = "REFUND_BLIND_BID";
    Common[Common["MATE_START_TRANSCENDENCE"] = 5274] = "MATE_START_TRANSCENDENCE";
    Common[Common["MATE_COMPLETE_TRANSCENDENCE"] = 5275] = "MATE_COMPLETE_TRANSCENDENCE";
    Common[Common["MATE_COMPLETE_TRANSCENDENCE_IMMEDIATELY"] = 5276] = "MATE_COMPLETE_TRANSCENDENCE_IMMEDIATELY";
    Common[Common["GET_INFINITE_LIGHT_HOUSE_FRIEND_CLEARED_INFO"] = 5277] = "GET_INFINITE_LIGHT_HOUSE_FRIEND_CLEARED_INFO";
    Common[Common["SWEEP_INFINITE_LIGHT_HOUSE_STAGE"] = 5278] = "SWEEP_INFINITE_LIGHT_HOUSE_STAGE";
    Common[Common["GET_USER_FIRST_FLEET_INFOS"] = 5279] = "GET_USER_FIRST_FLEET_INFOS";
    Common[Common["GET_USER_FIRST_FLEET_INFO_BY_NAME"] = 5280] = "GET_USER_FIRST_FLEET_INFO_BY_NAME";
    Common[Common["SET_OPTION_FRIENDLY_BATTLE"] = 5281] = "SET_OPTION_FRIENDLY_BATTLE";
    Common[Common["QUERY_UNPOPULAR_EVENT"] = 5282] = "QUERY_UNPOPULAR_EVENT";
    Common[Common["SET_OPTION_FLEET_PRESET"] = 5283] = "SET_OPTION_FLEET_PRESET";
    Common[Common["RESEARCH_CANCEL"] = 5284] = "RESEARCH_CANCEL";
    Common[Common["RESEARCH_REPORT"] = 5285] = "RESEARCH_REPORT";
    Common[Common["RESEARCH_START"] = 5286] = "RESEARCH_START";
    Common[Common["RESEARCH_RESET"] = 5287] = "RESEARCH_RESET";
    Common[Common["GET_INVESTMENT_SEASON_RANKING"] = 5288] = "GET_INVESTMENT_SEASON_RANKING";
    Common[Common["BOUGHT_WEB_SHOP_PRODUCT_SC"] = 5289] = "BOUGHT_WEB_SHOP_PRODUCT_SC";
    Common[Common["CLEAR_REENTRY_COOLDOWN"] = 5290] = "CLEAR_REENTRY_COOLDOWN";
    Common[Common["GET_CLASH_PAGE"] = 5291] = "GET_CLASH_PAGE";
    Common[Common["RECEIVE_CLASH_REWARD"] = 5292] = "RECEIVE_CLASH_REWARD";
    Common[Common["GET_MY_INVESTMENT_SEASON_NATION_RANKING_SCORE"] = 5293] = "GET_MY_INVESTMENT_SEASON_NATION_RANKING_SCORE";
    Common[Common["CHECK_LAST_SEASON_INVESTMENT_RANK_REWARDABLE"] = 5294] = "CHECK_LAST_SEASON_INVESTMENT_RANK_REWARDABLE";
    Common[Common["RECEIVE_LAST_SEASON_INVESTMENT_RANK_REWARD"] = 5295] = "RECEIVE_LAST_SEASON_INVESTMENT_RANK_REWARD";
    Common[Common["AUTO_CHANGE_ITEMS_TRY"] = 5296] = "AUTO_CHANGE_ITEMS_TRY";
    Common[Common["AUTO_CHANGE_ITEMS_HISTORY_GET"] = 5297] = "AUTO_CHANGE_ITEMS_HISTORY_GET";
    Common[Common["AUTO_CHANGE_ITEMS_HISTORY_RESET"] = 5298] = "AUTO_CHANGE_ITEMS_HISTORY_RESET";
    //-----------------------------------------------------------------------------
    // 중국 전용은 5800~6000
    //-----------------------------------------------------------------------------
    Common[Common["CN_PROTOCOL_RANGE_MIN"] = 5800] = "CN_PROTOCOL_RANGE_MIN";
    Common[Common["CN_PROTOCOL_RANGE_MAX"] = 6000] = "CN_PROTOCOL_RANGE_MAX";
    Common[Common["CASH_SHOP_START_TRANSACTION_CN"] = 5800] = "CASH_SHOP_START_TRANSACTION_CN";
    Common[Common["CASH_SHOP_CANCEL_TRANSACTION_CN"] = 5801] = "CASH_SHOP_CANCEL_TRANSACTION_CN";
    // CASH_SHOP_PAID_TRANSACTION_CN = 5602, // deprecated
    Common[Common["CASH_SHOP_CONSUME_TRANSACTION_CN"] = 5803] = "CASH_SHOP_CONSUME_TRANSACTION_CN";
    Common[Common["ANTIADDICTION_DEADLINE_IS_IMMINENT_CN"] = 5810] = "ANTIADDICTION_DEADLINE_IS_IMMINENT_CN";
    Common[Common["CHAT_SEND_CN"] = 5830] = "CHAT_SEND_CN";
    Common[Common["CHAT_BROADCAST_CN"] = 5831] = "CHAT_BROADCAST_CN";
    Common[Common["CHAT_MUTED_LIST_CN"] = 5832] = "CHAT_MUTED_LIST_CN";
    Common[Common["CHAT_MUTED_LIST_CHANGED_CN"] = 5833] = "CHAT_MUTED_LIST_CHANGED_CN";
    // 설문 완료
    Common[Common["COMPLETE_SURVEY_CN"] = 5841] = "COMPLETE_SURVEY_CN";
})(Common = exports.Common || (exports.Common = {}));
var BattleReward;
(function (BattleReward) {
    BattleReward[BattleReward["RECEIVE_BATTLE_REWARD"] = 6001] = "RECEIVE_BATTLE_REWARD";
    BattleReward[BattleReward["LEAVE"] = 6003] = "LEAVE";
    BattleReward[BattleReward["ENTER"] = 6004] = "ENTER";
    BattleReward[BattleReward["LOAD_COMPLETE"] = 6005] = "LOAD_COMPLETE";
})(BattleReward = exports.BattleReward || (exports.BattleReward = {}));
var LandExploreReward;
(function (LandExploreReward) {
    LandExploreReward[LandExploreReward["OPEN_BOX"] = 7001] = "OPEN_BOX";
    LandExploreReward[LandExploreReward["RECEIVE_ENTER"] = 7002] = "RECEIVE_ENTER";
    LandExploreReward[LandExploreReward["RECEIVE_LEAVE"] = 7003] = "RECEIVE_LEAVE";
    LandExploreReward[LandExploreReward["RECEIVE"] = 7004] = "RECEIVE";
})(LandExploreReward = exports.LandExploreReward || (exports.LandExploreReward = {}));
var Etc;
(function (Etc) {
    Etc[Etc["PING_CS"] = 5] = "PING_CS";
    Etc[Etc["PONG_SC"] = 6] = "PONG_SC";
    Etc[Etc["SET_CLIENT_BACKGROUND_STATE_CS"] = 7] = "SET_CLIENT_BACKGROUND_STATE_CS";
    Etc[Etc["SET_CLIENT_BACKGROUND_STATE_SC"] = 8] = "SET_CLIENT_BACKGROUND_STATE_SC";
})(Etc = exports.Etc || (exports.Etc = {}));
var Admin;
(function (Admin) {
    Admin[Admin["TELEPORT_TOWN"] = 8001] = "TELEPORT_TOWN";
    Admin[Admin["TELEPORT_TO_USER"] = 8002] = "TELEPORT_TO_USER";
})(Admin = exports.Admin || (exports.Admin = {}));
var Dev;
(function (Dev) {
    Dev[Dev["GIVE_MATE_EQUIPMENT"] = 9001] = "GIVE_MATE_EQUIPMENT";
    Dev[Dev["ADD_POINT"] = 9002] = "ADD_POINT";
    Dev[Dev["ADD_MATE"] = 9003] = "ADD_MATE";
    Dev[Dev["SET_SHIP_SAILOR"] = 9004] = "SET_SHIP_SAILOR";
    Dev[Dev["ADD_SHIP"] = 9005] = "ADD_SHIP";
    Dev[Dev["UPDATE_NATION"] = 9006] = "UPDATE_NATION";
    Dev[Dev["UPDATE_NATION_INTIMACY"] = 9007] = "UPDATE_NATION_INTIMACY";
    Dev[Dev["SET_NATION"] = 9008] = "SET_NATION";
    Dev[Dev["UPDATE_REPUTATION"] = 9009] = "UPDATE_REPUTATION";
    Dev[Dev["INVOKE_EVENT"] = 9010] = "INVOKE_EVENT";
    Dev[Dev["INVOKE_NATION_INTIMACY_UPDATE_JOB"] = 9011] = "INVOKE_NATION_INTIMACY_UPDATE_JOB";
    Dev[Dev["REMOVE_POINT"] = 9012] = "REMOVE_POINT";
    Dev[Dev["SET_POINT"] = 9013] = "SET_POINT";
    Dev[Dev["SET_SHIP_DURABILITY"] = 9014] = "SET_SHIP_DURABILITY";
    Dev[Dev["OCEAN_MOVE_TO_TOWN_CC"] = 9015] = "OCEAN_MOVE_TO_TOWN_CC";
    Dev[Dev["REMOVE_ALL_SHIP_CARGOS"] = 9016] = "REMOVE_ALL_SHIP_CARGOS";
    Dev[Dev["SET_FIRST_SHIP_CARGO"] = 9017] = "SET_FIRST_SHIP_CARGO";
    Dev[Dev["ADD_ITEM"] = 9018] = "ADD_ITEM";
    Dev[Dev["SET_DEVELOPMENT_LEVEL"] = 9019] = "SET_DEVELOPMENT_LEVEL";
    Dev[Dev["SET_MATE_FAME"] = 9020] = "SET_MATE_FAME";
    Dev[Dev["SET_MATE_ROYAL_TITLE"] = 9021] = "SET_MATE_ROYAL_TITLE";
    Dev[Dev["ADD_DIRECT_MAIL_CS"] = 9022] = "ADD_DIRECT_MAIL_CS";
    Dev[Dev["ADD_INSTALLMENT_SAVINGS_LAST_DEPOSIT_TIME"] = 9023] = "ADD_INSTALLMENT_SAVINGS_LAST_DEPOSIT_TIME";
    Dev[Dev["SET_USER_LEVEL"] = 9024] = "SET_USER_LEVEL";
    Dev[Dev["QUEST_SET_FLAGS"] = 9025] = "QUEST_SET_FLAGS";
    Dev[Dev["QUEST_SET_REGISTER"] = 9026] = "QUEST_SET_REGISTER";
    Dev[Dev["QUEST_SET_TEMP_REGISTER"] = 9027] = "QUEST_SET_TEMP_REGISTER";
    Dev[Dev["QUEST_SET_COMPLETED"] = 9028] = "QUEST_SET_COMPLETED";
    Dev[Dev["QUEST_SET_NODE_IDX"] = 9029] = "QUEST_SET_NODE_IDX";
    Dev[Dev["PREDICT_DISASTER"] = 9030] = "PREDICT_DISASTER";
    Dev[Dev["GENERATE_DISASTER"] = 9031] = "GENERATE_DISASTER";
    Dev[Dev["RESOLVE_DISASTER"] = 9032] = "RESOLVE_DISASTER";
    Dev[Dev["UNLIMITED_INVEST"] = 9033] = "UNLIMITED_INVEST";
    Dev[Dev["SET_IGNORE_NPC_ENCOUNT_CS"] = 9034] = "SET_IGNORE_NPC_ENCOUNT_CS";
    Dev[Dev["STAT_DUMP"] = 9035] = "STAT_DUMP";
    Dev[Dev["STAT_SET"] = 9036] = "STAT_SET";
    Dev[Dev["ADD_NEAR_SPAWNER"] = 9037] = "ADD_NEAR_SPAWNER";
    Dev[Dev["WORLD_BUFF_ADD_CS"] = 9038] = "WORLD_BUFF_ADD_CS";
    Dev[Dev["WORLD_BUFF_REM_CS"] = 9039] = "WORLD_BUFF_REM_CS";
    Dev[Dev["DISCONNECT_SERVER_CS"] = 9040] = "DISCONNECT_SERVER_CS";
    Dev[Dev["ENCOUNT_NPC_ATT_CHOICE"] = 9041] = "ENCOUNT_NPC_ATT_CHOICE";
    Dev[Dev["ENCOUNT_NPC_DEF_CHOICE"] = 9042] = "ENCOUNT_NPC_DEF_CHOICE";
    Dev[Dev["CHANGE_NPC_ATTACK_RADIUS"] = 9043] = "CHANGE_NPC_ATTACK_RADIUS";
    Dev[Dev["TRADE_DUMP"] = 9044] = "TRADE_DUMP";
    Dev[Dev["CHANGE_NPC_TICK_PER_SEC"] = 9045] = "CHANGE_NPC_TICK_PER_SEC";
    Dev[Dev["REMOVE_NEAR_SPAWNER"] = 9046] = "REMOVE_NEAR_SPAWNER";
    Dev[Dev["SET_LOYALTY"] = 9047] = "SET_LOYALTY";
    Dev[Dev["RESET_PUB_MATES"] = 9048] = "RESET_PUB_MATES";
    Dev[Dev["ADD_ALL_MATES"] = 9049] = "ADD_ALL_MATES";
    Dev[Dev["GET_NPC_LOCATION_BY_OCEAN_NPC_AREA_SPAWNER"] = 9050] = "GET_NPC_LOCATION_BY_OCEAN_NPC_AREA_SPAWNER";
    Dev[Dev["ADD_OCEAN_DOODAD_NEAR_SPAWNER"] = 9051] = "ADD_OCEAN_DOODAD_NEAR_SPAWNER";
    Dev[Dev["REMOVE_OCEAN_DOODAD_NEAR_SPAWNER"] = 9052] = "REMOVE_OCEAN_DOODAD_NEAR_SPAWNER";
    Dev[Dev["QUEST_FORCE_EXEC"] = 9053] = "QUEST_FORCE_EXEC";
    Dev[Dev["SET_LOCAL_NPC_SPAWN"] = 9054] = "SET_LOCAL_NPC_SPAWN";
    Dev[Dev["SET_LOCAL_DOODAD_SPAWN"] = 9055] = "SET_LOCAL_DOODAD_SPAWN";
    Dev[Dev["SET_DISASTER_LUCK"] = 9056] = "SET_DISASTER_LUCK";
    Dev[Dev["GENERATE_PROTECTION"] = 9057] = "GENERATE_PROTECTION";
    Dev[Dev["RESOLVE_PROTECTION"] = 9058] = "RESOLVE_PROTECTION";
    Dev[Dev["TELEPORT_TO_LOCATION_CS"] = 9059] = "TELEPORT_TO_LOCATION_CS";
    Dev[Dev["PREDICT_PROTECTION"] = 9060] = "PREDICT_PROTECTION";
    Dev[Dev["SET_MATE_LEVEL"] = 9061] = "SET_MATE_LEVEL";
    Dev[Dev["ADD_USER_DATA_NPC_SPAWNER"] = 9062] = "ADD_USER_DATA_NPC_SPAWNER";
    Dev[Dev["REVEAL_ALL_WORLD_MAP_TILES"] = 9063] = "REVEAL_ALL_WORLD_MAP_TILES";
    Dev[Dev["DISCOVER_ALL_TOWNS"] = 9064] = "DISCOVER_ALL_TOWNS";
    Dev[Dev["ADD_ALL_TOWNS_TO_QUESTION_PLACE"] = 9065] = "ADD_ALL_TOWNS_TO_QUESTION_PLACE";
    Dev[Dev["WORLD_PASSIVE_ADD_CS"] = 9066] = "WORLD_PASSIVE_ADD_CS";
    Dev[Dev["WORLD_PASSIVE_REM_CS"] = 9067] = "WORLD_PASSIVE_REM_CS";
    Dev[Dev["DEBUFF_IMMUNE_CS"] = 9068] = "DEBUFF_IMMUNE_CS";
    Dev[Dev["DISASTER_IMMUNE_CS"] = 9069] = "DISASTER_IMMUNE_CS";
    Dev[Dev["SERVER_DEBUG_MSG_SC"] = 9070] = "SERVER_DEBUG_MSG_SC";
    Dev[Dev["SHOW_DISASTER_STAT_CS"] = 9071] = "SHOW_DISASTER_STAT_CS";
    Dev[Dev["SET_TRADE_GOODS_BREED_SUCCESS_CS"] = 9072] = "SET_TRADE_GOODS_BREED_SUCCESS_CS";
    Dev[Dev["DISCOVER"] = 9073] = "DISCOVER";
    Dev[Dev["BATTLE_RESUME_FOR_USER_ID"] = 9074] = "BATTLE_RESUME_FOR_USER_ID";
    Dev[Dev["GIVE_ALL_MATE_EQUIPMENTS"] = 9075] = "GIVE_ALL_MATE_EQUIPMENTS";
    Dev[Dev["DISCOVER_ALL"] = 9076] = "DISCOVER_ALL";
    Dev[Dev["SET_USER_KARMA"] = 9077] = "SET_USER_KARMA";
    Dev[Dev["SET_USER_COMPANY_JOB"] = 9078] = "SET_USER_COMPANY_JOB";
    Dev[Dev["SET_MATE_INJERY"] = 9079] = "SET_MATE_INJERY";
    Dev[Dev["ATTACK_TO_ME_CS"] = 9080] = "ATTACK_TO_ME_CS";
    Dev[Dev["ADD_BATTLE_FORMATION"] = 9081] = "ADD_BATTLE_FORMATION";
    Dev[Dev["SPECIAL_STAT_DUMP"] = 9082] = "SPECIAL_STAT_DUMP";
    Dev[Dev["SET_MATE_AWAKEN"] = 9083] = "SET_MATE_AWAKEN";
    Dev[Dev["PUB_STAFF_RESET"] = 9084] = "PUB_STAFF_RESET";
    Dev[Dev["SET_TOWN_NATION_SHARE_POINT_CS"] = 9085] = "SET_TOWN_NATION_SHARE_POINT_CS";
    Dev[Dev["I_AM_MAYOR_CS"] = 9086] = "I_AM_MAYOR_CS";
    Dev[Dev["CHANGE_MAYOR_TAX"] = 9087] = "CHANGE_MAYOR_TAX";
    Dev[Dev["EASY_LANGUAGE"] = 9088] = "EASY_LANGUAGE";
    Dev[Dev["CHANGE_SPAWNED_LOCAL_NPC_NUM_CS"] = 9089] = "CHANGE_SPAWNED_LOCAL_NPC_NUM_CS";
    Dev[Dev["SET_SAILING_DAYS_ONOFF_CS"] = 9090] = "SET_SAILING_DAYS_ONOFF_CS";
    Dev[Dev["ADD_PARTS"] = 9091] = "ADD_PARTS";
    Dev[Dev["RESET_COLLECTION"] = 9092] = "RESET_COLLECTION";
    Dev[Dev["SET_MATE_TALK_WAIT_TIME"] = 9093] = "SET_MATE_TALK_WAIT_TIME";
    Dev[Dev["LOAD"] = 9094] = "LOAD";
    Dev[Dev["RESET_PALACE_ROYAL_ORDER"] = 9095] = "RESET_PALACE_ROYAL_ORDER";
    Dev[Dev["QUEST_SET_GLOBAL_REGISTER"] = 9096] = "QUEST_SET_GLOBAL_REGISTER";
    Dev[Dev["UNLOCK_ALL_MATE_AWAKEN_AND_SKILL"] = 9097] = "UNLOCK_ALL_MATE_AWAKEN_AND_SKILL";
    Dev[Dev["RESET_GUILD_LEFT_TIME_TEMPORARILY"] = 9098] = "RESET_GUILD_LEFT_TIME_TEMPORARILY";
    Dev[Dev["SET_GUILD_UPGRADE_POPUP"] = 9099] = "SET_GUILD_UPGRADE_POPUP";
    Dev[Dev["SET_ARENA_SCORE"] = 9100] = "SET_ARENA_SCORE";
    Dev[Dev["SET_ARENA_TICKET_COUNT"] = 9101] = "SET_ARENA_TICKET_COUNT";
    Dev[Dev["SET_ARENA_TICKET_BOUGHT_COUNT"] = 9102] = "SET_ARENA_TICKET_BOUGHT_COUNT";
    Dev[Dev["SET_ARENA_MATCH_LIST_REFRESH_COUNT"] = 9103] = "SET_ARENA_MATCH_LIST_REFRESH_COUNT";
    Dev[Dev["ADD_GUILD_POINT"] = 9104] = "ADD_GUILD_POINT";
    Dev[Dev["RESET_ARENA_DATA"] = 9105] = "RESET_ARENA_DATA";
    Dev[Dev["RESET_CASH_SHOP_DAILY_PRODUCTS"] = 9106] = "RESET_CASH_SHOP_DAILY_PRODUCTS";
    Dev[Dev["SET_LEADER_MATE_SWITCH_COUNT"] = 9107] = "SET_LEADER_MATE_SWITCH_COUNT";
    Dev[Dev["QUEST_SET_ADMIN_PAUSE"] = 9108] = "QUEST_SET_ADMIN_PAUSE";
    Dev[Dev["GENERATE_ARENA_DUMMY_USERS"] = 9109] = "GENERATE_ARENA_DUMMY_USERS";
    Dev[Dev["SET_SHIP_BUILD_LEVEL"] = 9110] = "SET_SHIP_BUILD_LEVEL";
    Dev[Dev["SET_USER_SHIP_BUILD_LEVEL"] = 9111] = "SET_USER_SHIP_BUILD_LEVEL";
    Dev[Dev["SET_VILLAGE_FRIENDSHIP"] = 9112] = "SET_VILLAGE_FRIENDSHIP";
    Dev[Dev["SET_DEBUG_MSG_FOR_ENCOUNT_BY_NPC_CS"] = 9113] = "SET_DEBUG_MSG_FOR_ENCOUNT_BY_NPC_CS";
    Dev[Dev["SHOW_SPAWNED_NPC_COUNT_CS"] = 9114] = "SHOW_SPAWNED_NPC_COUNT_CS";
    Dev[Dev["INIT_EXPLORE_TICKET"] = 9115] = "INIT_EXPLORE_TICKET";
    Dev[Dev["RESET_GUILD_SHOP"] = 9116] = "RESET_GUILD_SHOP";
    Dev[Dev["SET_EVENT_PAGE_PRODUCT"] = 9117] = "SET_EVENT_PAGE_PRODUCT";
    Dev[Dev["SET_PASS_EVENT_EXP"] = 9118] = "SET_PASS_EVENT_EXP";
    Dev[Dev["ESTIMATED_SAILING_TIME"] = 9119] = "ESTIMATED_SAILING_TIME";
    Dev[Dev["FIXED_SPEED_CS"] = 9120] = "FIXED_SPEED_CS";
    Dev[Dev["DISABLE_SPEED_HACK_CS"] = 9121] = "DISABLE_SPEED_HACK_CS";
    Dev[Dev["SET_EXPLORE_TIME_CHECK"] = 9122] = "SET_EXPLORE_TIME_CHECK";
    Dev[Dev["RESET_GUILD_DATE"] = 9123] = "RESET_GUILD_DATE";
    Dev[Dev["SET_SHIP_LIFE"] = 9124] = "SET_SHIP_LIFE";
    Dev[Dev["ADD_ALL_SHIPS"] = 9125] = "ADD_ALL_SHIPS";
    Dev[Dev["ADD_ALL_PARTS"] = 9126] = "ADD_ALL_PARTS";
    Dev[Dev["NOTICE_RAID"] = 9127] = "NOTICE_RAID";
    Dev[Dev["ADD_RAID_DAMAGE"] = 9128] = "ADD_RAID_DAMAGE";
    Dev[Dev["GET_RAID_STATE"] = 9129] = "GET_RAID_STATE";
    Dev[Dev["SET_RAID_SCHEDULE_END"] = 9130] = "SET_RAID_SCHEDULE_END";
    Dev[Dev["SET_TRADE_POINT"] = 9131] = "SET_TRADE_POINT";
    Dev[Dev["RESET_WORLD_SKILL"] = 9132] = "RESET_WORLD_SKILL";
    Dev[Dev["SET_NATION_LAST_UPDATE_TIME"] = 9133] = "SET_NATION_LAST_UPDATE_TIME";
    // KILL_RAID_BOSS = 9134,
    Dev[Dev["QUEST_RESET_ALL_DAILY_LIMIT_COMPLETED_COUNT"] = 9135] = "QUEST_RESET_ALL_DAILY_LIMIT_COMPLETED_COUNT";
    Dev[Dev["SET_MATE_STATE"] = 9136] = "SET_MATE_STATE";
    Dev[Dev["ADD_FLEET"] = 9137] = "ADD_FLEET";
    Dev[Dev["SET_SHIP_SAILMASTERY_LEVEL"] = 9138] = "SET_SHIP_SAILMASTERY_LEVEL";
    Dev[Dev["MAKE_DIPATCH_END"] = 9139] = "MAKE_DIPATCH_END";
    Dev[Dev["ADD_ALL_ITEMS"] = 9140] = "ADD_ALL_ITEMS";
    Dev[Dev["RESET_WAYPOINT_SUPPLY_TICKET"] = 9141] = "RESET_WAYPOINT_SUPPLY_TICKET";
    Dev[Dev["SET_SHIP_SAILMASTERY_EXP"] = 9142] = "SET_SHIP_SAILMASTERY_EXP";
    Dev[Dev["RESET_FOR_TIME_TRAVEL"] = 9143] = "RESET_FOR_TIME_TRAVEL";
    Dev[Dev["UNLINK_COMPANY"] = 9144] = "UNLINK_COMPANY";
    Dev[Dev["CLEAR_DISCOVER"] = 9145] = "CLEAR_DISCOVER";
    Dev[Dev["REMOVE_ALL_ITEMS"] = 9146] = "REMOVE_ALL_ITEMS";
    Dev[Dev["REMOVE_UNEQUIP_EQUIPMENTS"] = 9147] = "REMOVE_UNEQUIP_EQUIPMENTS";
    Dev[Dev["REMOVE_UNEQUIP_PARTS"] = 9148] = "REMOVE_UNEQUIP_PARTS";
    Dev[Dev["ADD_RELEASED_MATES"] = 9149] = "ADD_RELEASED_MATES";
    Dev[Dev["ADD_ALL_MATES_AWAKEN"] = 9150] = "ADD_ALL_MATES_AWAKEN";
    Dev[Dev["SET_ALL_MATES_LEVEL"] = 9151] = "SET_ALL_MATES_LEVEL";
    Dev[Dev["RECOVER_CHAT_TRANSLATION"] = 9152] = "RECOVER_CHAT_TRANSLATION";
    Dev[Dev["SHOW_DISPATCH_ACTION_RESULT_STAT_CS"] = 9153] = "SHOW_DISPATCH_ACTION_RESULT_STAT_CS";
    Dev[Dev["CHANGE_GUILD_SYNTHESIS_PROB_GREAT_SUCCESS"] = 9154] = "CHANGE_GUILD_SYNTHESIS_PROB_GREAT_SUCCESS";
    Dev[Dev["RESET_GUILD_RAID_TICKET"] = 9155] = "RESET_GUILD_RAID_TICKET";
    Dev[Dev["SET_GUILD_RAID_SCHEDULE_END"] = 9156] = "SET_GUILD_RAID_SCHEDULE_END";
    Dev[Dev["ADD_GUILD_RAID_DAMAGE"] = 9157] = "ADD_GUILD_RAID_DAMAGE";
    Dev[Dev["ADD_GUILD_RESOURCE"] = 9158] = "ADD_GUILD_RESOURCE";
    Dev[Dev["RESET_GUILD_RAID_OPEN_TIME"] = 9159] = "RESET_GUILD_RAID_OPEN_TIME";
    Dev[Dev["RESET_GUILD_RAID_BATTLE_N_REWARD_HISTORY"] = 9160] = "RESET_GUILD_RAID_BATTLE_N_REWARD_HISTORY";
    Dev[Dev["QUEST_SET_ACCUM"] = 9161] = "QUEST_SET_ACCUM";
    Dev[Dev["ADD_SHIP_CAMOUFLAGE"] = 9162] = "ADD_SHIP_CAMOUFLAGE";
    Dev[Dev["ADD_MATE_ILLUST"] = 9163] = "ADD_MATE_ILLUST";
    Dev[Dev["SET_USER_LAST_LOGIN_TIME_DAYS_AGO"] = 9164] = "SET_USER_LAST_LOGIN_TIME_DAYS_AGO";
    Dev[Dev["SET_USER_ATTENDANCE"] = 9165] = "SET_USER_ATTENDANCE";
    Dev[Dev["ADD_USER_TITLE"] = 9166] = "ADD_USER_TITLE";
    Dev[Dev["RESET_SESSION_RANKING"] = 9167] = "RESET_SESSION_RANKING";
    Dev[Dev["REMOVE_NATION_ELECTION_CANDIDATE"] = 9168] = "REMOVE_NATION_ELECTION_CANDIDATE";
    Dev[Dev["RESERVE_MAYOR_TRADE_EVENT"] = 9169] = "RESERVE_MAYOR_TRADE_EVENT";
    Dev[Dev["DELETE_MAYOR_TRADE_EVENT"] = 9170] = "DELETE_MAYOR_TRADE_EVENT";
    Dev[Dev["CHANGE_NATION_SESSIONID"] = 9171] = "CHANGE_NATION_SESSIONID";
    Dev[Dev["RECEIVE_PRODUCTS"] = 9172] = "RECEIVE_PRODUCTS";
    Dev[Dev["SET_SWEEP_TICKET"] = 9173] = "SET_SWEEP_TICKET";
    Dev[Dev["RESET_EXCHANGE_HISTORY"] = 9174] = "RESET_EXCHANGE_HISTORY";
    Dev[Dev["SET_VILLAGE_EVENT"] = 9175] = "SET_VILLAGE_EVENT";
    Dev[Dev["SET_NATION_POLICY"] = 9176] = "SET_NATION_POLICY";
    Dev[Dev["SET_NATION_BUDGET"] = 9177] = "SET_NATION_BUDGET";
    Dev[Dev["DELETE_ALL_DIRECT_MAILS"] = 9178] = "DELETE_ALL_DIRECT_MAILS";
    Dev[Dev["GET_NATION_ACCUMULATED_TAX"] = 9179] = "GET_NATION_ACCUMULATED_TAX";
    Dev[Dev["SET_QUEST_PASS"] = 9180] = "SET_QUEST_PASS";
    Dev[Dev["I_AM_PRIME_MINISTER"] = 9181] = "I_AM_PRIME_MINISTER";
    Dev[Dev["RESET_NATION_CABINET_LAST_APPOINTED_TIMES"] = 9182] = "RESET_NATION_CABINET_LAST_APPOINTED_TIMES";
    Dev[Dev["RESET_MAYOR_REMOTE_INVEST"] = 9183] = "RESET_MAYOR_REMOTE_INVEST";
    Dev[Dev["RESET_REMOTE_INVEST_COUNT"] = 9184] = "RESET_REMOTE_INVEST_COUNT";
    Dev[Dev["ADD_PET"] = 9185] = "ADD_PET";
    Dev[Dev["RESET_BLACK_MARKET_REFRESH_COUNT"] = 9186] = "RESET_BLACK_MARKET_REFRESH_COUNT";
    Dev[Dev["OPEN_HOT_SPOT_BUYABLE_PRODUCTS"] = 9187] = "OPEN_HOT_SPOT_BUYABLE_PRODUCTS";
    Dev[Dev["TEST"] = 9188] = "TEST";
    Dev[Dev["RESET_HOT_SPOT_PRODUCTS_HISTORY"] = 9189] = "RESET_HOT_SPOT_PRODUCTS_HISTORY";
    Dev[Dev["CHANGE_INFINITE_LIGHTHOUSE_CLEARED_INFO_SESSION"] = 9190] = "CHANGE_INFINITE_LIGHTHOUSE_CLEARED_INFO_SESSION";
    Dev[Dev["ADD_ALL_PUB_MATES"] = 9191] = "ADD_ALL_PUB_MATES";
    Dev[Dev["CLEAR_INFINITE_LIGHTHOUSE_STAGE"] = 9192] = "CLEAR_INFINITE_LIGHTHOUSE_STAGE";
    Dev[Dev["RESET_MY_FIRST_FLEET_INFO"] = 9193] = "RESET_MY_FIRST_FLEET_INFO";
    Dev[Dev["RESERVE_UNPOPULAR_TRADE_EVENT"] = 9194] = "RESERVE_UNPOPULAR_TRADE_EVENT";
    Dev[Dev["DELETE_UNPOPULAR_TRADE_EVENT"] = 9195] = "DELETE_UNPOPULAR_TRADE_EVENT";
    Dev[Dev["UPDATE_RESEARCH"] = 9196] = "UPDATE_RESEARCH";
    Dev[Dev["SET_CLASH_SCORE_AND_WINSTREAK"] = 9197] = "SET_CLASH_SCORE_AND_WINSTREAK";
    Dev[Dev["ACTIVE_CLASH_BATTLE_RECORD"] = 9198] = "ACTIVE_CLASH_BATTLE_RECORD";
    Dev[Dev["GENERATE_TOWN_USER_WEEKLY_INVESTMENT_SCORE"] = 9199] = "GENERATE_TOWN_USER_WEEKLY_INVESTMENT_SCORE";
    // 중국 전용 채팅 관련
    Dev[Dev["CHAT_SEND"] = 9200] = "CHAT_SEND";
    Dev[Dev["CHAT_RECV"] = 9201] = "CHAT_RECV";
    Dev[Dev["SET_TUTORIAL_CRAZE_EVENT_BUDGET"] = 9202] = "SET_TUTORIAL_CRAZE_EVENT_BUDGET";
    Dev[Dev["TRY_FORCE_ADD_CRAZE_EVENT"] = 9203] = "TRY_FORCE_ADD_CRAZE_EVENT";
    Dev[Dev["DISCOVER_ALL_FILTERED"] = 9300] = "DISCOVER_ALL_FILTERED";
})(Dev = exports.Dev || (exports.Dev = {}));
var Temp;
(function (Temp) {
    Temp[Temp["BUTTON_LOG_CS"] = 10001] = "BUTTON_LOG_CS";
})(Temp = exports.Temp || (exports.Temp = {}));
var Guild;
(function (Guild) {
    Guild[Guild["CHECK_DUPLICATE_NAME"] = 11001] = "CHECK_DUPLICATE_NAME";
    Guild[Guild["CREATE"] = 11002] = "CREATE";
    Guild[Guild["DISBAND"] = 11003] = "DISBAND";
    Guild[Guild["SHOW_LIST"] = 11004] = "SHOW_LIST";
    Guild[Guild["JOIN"] = 11005] = "JOIN";
    Guild[Guild["JOIN_CANCEL"] = 11006] = "JOIN_CANCEL";
    Guild[Guild["LEAVE"] = 11007] = "LEAVE";
    Guild[Guild["GET_MY_GUILD_INFO"] = 11008] = "GET_MY_GUILD_INFO";
    Guild[Guild["GET_LIGHT_INFO"] = 11009] = "GET_LIGHT_INFO";
    Guild[Guild["GET_DETAIL_INFO"] = 11010] = "GET_DETAIL_INFO";
    Guild[Guild["CHECKED_UPGRADE_POPUP"] = 11011] = "CHECKED_UPGRADE_POPUP";
    Guild[Guild["GUILD_UPDATE_SYNC_DATA_SC"] = 11012] = "GUILD_UPDATE_SYNC_DATA_SC";
    Guild[Guild["PICK_UP_DAILY_REWARD"] = 11013] = "PICK_UP_DAILY_REWARD";
    Guild[Guild["CRAFT"] = 11014] = "CRAFT";
    Guild[Guild["GUILD_RECEIVE_WEEKLY_REWARD_MAIL"] = 11015] = "GUILD_RECEIVE_WEEKLY_REWARD_MAIL";
    Guild[Guild["CRAFT_CREATE"] = 11016] = "CRAFT_CREATE";
    Guild[Guild["CRAFT_RECEIVE"] = 11017] = "CRAFT_RECEIVE";
    Guild[Guild["CRAFT_DECREASE_EXPIRE_TIME"] = 11018] = "CRAFT_DECREASE_EXPIRE_TIME";
    Guild[Guild["CRAFT_COMPLETE_EXPIRE_TIME"] = 11019] = "CRAFT_COMPLETE_EXPIRE_TIME";
    Guild[Guild["SYNTHESIS_CREATE"] = 11020] = "SYNTHESIS_CREATE";
    Guild[Guild["SYNTHESIS_RECEIVE"] = 11021] = "SYNTHESIS_RECEIVE";
    Guild[Guild["SYNTHESIS_DECREASE_EXPIRE_TIME"] = 11022] = "SYNTHESIS_DECREASE_EXPIRE_TIME";
    Guild[Guild["SYNTHESIS_COMPLETE_EXPIRE_TIME"] = 11023] = "SYNTHESIS_COMPLETE_EXPIRE_TIME";
    Guild[Guild["DONATE"] = 11024] = "DONATE";
    Guild[Guild["RAID_OPEN"] = 11025] = "RAID_OPEN";
    Guild[Guild["STATE_SC"] = 11026] = "STATE_SC";
    Guild[Guild["RAID_GET_INFO"] = 11027] = "RAID_GET_INFO";
    Guild[Guild["RAID_GET_RANKING_PAGE"] = 11028] = "RAID_GET_RANKING_PAGE";
    Guild[Guild["RAID_PICKUP_REWARD"] = 11029] = "RAID_PICKUP_REWARD";
    Guild[Guild["RAID_BUY_TICKET"] = 11030] = "RAID_BUY_TICKET";
    Guild[Guild["RAID_GET_PREV_RANKING"] = 11031] = "RAID_GET_PREV_RANKING";
    Guild[Guild["BUY_DONATION_COUNT"] = 11032] = "BUY_DONATION_COUNT";
    // 상회 버프 성향 변경
    Guild[Guild["SELECT_GUILD_BUFF_CATEGORY"] = 11033] = "SELECT_GUILD_BUFF_CATEGORY";
    // 상회 버프 습득
    Guild[Guild["LEARN_GUILD_BUFF"] = 11034] = "LEARN_GUILD_BUFF";
    // 버프 업그레이드용 아이템 등록
    Guild[Guild["REGISTER_GUILD_BUFF_ITEM_FOR_UPGRADE"] = 11035] = "REGISTER_GUILD_BUFF_ITEM_FOR_UPGRADE";
    // 상회 토벌 버프 구매.
    Guild[Guild["BUY_GUILD_RAID_BUFF"] = 11036] = "BUY_GUILD_RAID_BUFF";
    Guild[Guild["MANAGING_ACCEPT_JOIN"] = 11101] = "MANAGING_ACCEPT_JOIN";
    Guild[Guild["MANAGING_REFUSE_JOIN"] = 11102] = "MANAGING_REFUSE_JOIN";
    Guild[Guild["MANAGING_CHANGE_MEMBER_GRADE"] = 11103] = "MANAGING_CHANGE_MEMBER_GRADE";
    Guild[Guild["MANAGING_KICK_MEMBER"] = 11104] = "MANAGING_KICK_MEMBER";
    Guild[Guild["MANAGING_CHANGE_INFO"] = 11105] = "MANAGING_CHANGE_INFO";
    Guild[Guild["MANAGING_DELEGATE_MASTER"] = 11106] = "MANAGING_DELEGATE_MASTER";
    Guild[Guild["GET_GULID_SHOP_SYNC_DATA"] = 11201] = "GET_GULID_SHOP_SYNC_DATA";
    Guild[Guild["BUY_GULID_SHOP_PRODUCT"] = 11202] = "BUY_GULID_SHOP_PRODUCT";
})(Guild = exports.Guild || (exports.Guild = {}));
var BattleLobby;
(function (BattleLobby) {
    BattleLobby[BattleLobby["LEAVE"] = 12001] = "LEAVE";
    BattleLobby[BattleLobby["ENTER"] = 12002] = "ENTER";
})(BattleLobby = exports.BattleLobby || (exports.BattleLobby = {}));
var Raid;
(function (Raid) {
    Raid[Raid["STATE_SC"] = 13001] = "STATE_SC";
    Raid[Raid["GET_RAID_INFO"] = 13002] = "GET_RAID_INFO";
    Raid[Raid["GET_RANKING_PAGE"] = 13003] = "GET_RANKING_PAGE";
    Raid[Raid["PICKUP_REWARD"] = 13004] = "PICKUP_REWARD";
    // BUY_TICKET = 13005,
    Raid[Raid["PICK_RAID_BOSS"] = 13006] = "PICK_RAID_BOSS";
    Raid[Raid["GET_REWARD_STATES"] = 13007] = "GET_REWARD_STATES";
})(Raid = exports.Raid || (exports.Raid = {}));
var Friend;
(function (Friend) {
    Friend[Friend["NOTIFIED_SC"] = 14001] = "NOTIFIED_SC";
    Friend[Friend["REQUEST_FRIEND"] = 14002] = "REQUEST_FRIEND";
    Friend[Friend["CANCEL_FRIEND_REQUEST"] = 14003] = "CANCEL_FRIEND_REQUEST";
    Friend[Friend["DELETE_FRIEND"] = 14004] = "DELETE_FRIEND";
    Friend[Friend["ACCEPT_FRIEND_REQUEST"] = 14005] = "ACCEPT_FRIEND_REQUEST";
    Friend[Friend["DENY_FRIEND_REQUEST"] = 14006] = "DENY_FRIEND_REQUEST";
    Friend[Friend["SEND_POINT"] = 14007] = "SEND_POINT";
    Friend[Friend["PICKUP_POINT"] = 14008] = "PICKUP_POINT";
})(Friend = exports.Friend || (exports.Friend = {}));
const toString = (type) => {
    if (Auth[type] !== undefined) {
        return `Auth.${Auth[type]}`;
    }
    else if (Town[type] !== undefined) {
        return `Town.${Town[type]}`;
    }
    else if (Ocean[type] !== undefined) {
        return `Ocean.${Ocean[type]}`;
    }
    else if (Common[type] !== undefined) {
        return `Common.${Common[type]}`;
    }
    else if (Etc[type] !== undefined) {
        return `Etc.${Etc[type]}`;
    }
    else if (Admin[type] !== undefined) {
        return `Admin.${Admin[type]}`;
    }
    else if (Dev[type] !== undefined) {
        return `Dev.${Dev[type]}`;
    }
    else if (BattleReward[type] !== undefined) {
        return `BattleReward.${BattleReward[type]}`;
    }
    else if (Battle[type] !== undefined) {
        return `Battle.${Battle[type]}`;
    }
    else if (Duel[type] !== undefined) {
        return `Duel.${Duel[type]}`;
    }
    else if (Temp[type] !== undefined) {
        return `Temp.${Dev[type]}`;
    }
    else if (Guild[type] !== undefined) {
        return `Guild.${Guild[type]}`;
    }
    else if (BattleLobby[type] !== undefined) {
        return `BattleLobby.${BattleLobby[type]}`;
    }
    else if (Raid[type] !== undefined) {
        return `Raid.${Raid[type]}`;
    }
    else if (Friend[type] !== undefined) {
        return `Friend.${Friend[type]}`;
    }
    else {
        return `Unknown(${type})`;
    }
};
exports.toString = toString;
//# sourceMappingURL=proto.js.map