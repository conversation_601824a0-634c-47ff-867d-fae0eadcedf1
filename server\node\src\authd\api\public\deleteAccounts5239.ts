// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { Container } from 'typedi/Container';
import { MRedisConnPool } from '../../../redislib/connPool';
import { AdminUtil } from '../../../motiflib/model/admin/adminUtil';

// https://developer.line.games/pages/viewpage.action?pageId=18186609
interface RequestBody {
  gameCd: string;

  // 탈퇴한 삭제 대상 NID리스트 (json string)
  // ex) ["1530500895726014092","1519279210510015963"]
  targetNidsJson: string;
}

type ResponseErrCd = 'NOT_ALLOW_AUTH' | 'INVALID_PARAMETER' | 'SYSTEM_ERROR' | 'SYSTEM_MAINTENANCE';

interface ResponseBody {
  isSuccess: boolean;
  msg: string;
  errorCd?: ResponseErrCd;
}

export = (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /deleteAccouts', { url: req.url, body: req.body });
  const { gameCd, targetNidsJson } = req.body;

  // 파라미터 검사 실행
  if (mhttp.lgd.gameCode !== gameCd || typeof targetNidsJson !== 'string') {
    return res.status(400).json({
      isSuccess: false,
      msg: 'Invalid your parameter',
      errorCd: 'INVALID_PARAMETER',
    });
  }

  // 호출서버 IP인증 확인(플랫폼 서버에서 제공하는 API를 통해 IP리스트는 확인 가능)
  return mhttp.lgd
    .reqWhiteServerIpList()
    .then((allowIpList) => {
      const clientIp = AdminUtil.getReqClientIp(req);

      if (!_.includes(allowIpList, clientIp)) {
        mlog.warn('ip filtered', { clientIp, allowIpList });
        throw new MError('not-allowed-ip', MErrorCode.NOT_ALLOWED_IP_ERROR);
      }
      return null;
    })
    .then(() => {
      const authRedis = Container.of('auth-redis').get(MRedisConnPool);
      return authRedis['addDeletionPubIds'](targetNidsJson);
    })
    .then(() => {
      return res.json({
        isSuccess: true,
        msg: 'OK',
      });
    })
    .catch((err) => {
      mlog.error('api deleteAccouts exception. ', err);
      if (err instanceof MError && err.mcode === MErrorCode.NOT_ALLOWED_IP_ERROR) {
        return res.status(405).json({
          isSuccess: false,
          msg: 'Not allowed ip.',
          errorCd: 'NOT_ALLOW_AUTH',
        });
      } else {
        return res.status(400).json({
          isSuccess: false,
          msg: err.message,
          errorCd: 'SYSTEM_ERROR',
        });
      }
    });
};
