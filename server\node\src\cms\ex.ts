// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
import _ from 'lodash';
import assert from 'assert';

import cms from '.';
import * as CMSConst from './const';
import * as formula from '../formula';
import { StatTable } from '../motiflib/stat/statCommon';
import mlog from '../motiflib/mlog';
import { COUNTRY_CODE } from '../motiflib/const';
import mconf from '../motiflib/mconf';
import { AdmiralDesc } from './admiralDesc';
import { TownKickDesc } from './townKickDesc';
import { RequestGroupDesc } from './requestGroupDesc';
import { RoyalOrderGroupDesc } from './royalOrderGroupDesc';
import { TradeDesc } from './tradeDesc';
import { CashShopBoxRatioDesc } from './cashShopBoxRatioDesc';
import { TownBuildingDesc } from './townBuildingDesc';
import { DiscoveryMissionDesc } from './discoveryMissionDesc';
import { NationDesc } from './nationDesc';
import { DiscoveryRankingDesc, DISCOVERY_RANKING_TARGET } from './discoveryRankingDesc';
import { MateDesc, MATE_GRADE } from './mateDesc';
import { MateAwakenDesc } from './MateAwakenDesc';
import { MatePassiveElemDesc } from './commonInterfaceDesc';
import { AutoMateOrderElemDesc } from './autoMateOrderDesc';
import { BurstGroupDesc } from './burstGroupDesc';
import { AttendanceDesc } from './attendanceDesc';
import { EventPageDesc, EventPageType } from './eventPageDesc';
import { TaskDesc } from './taskDesc';
import { WorldPassiveDesc as BattlePassiveDesc, WorldPassiveDesc } from './worldPassiveDesc';
import { ShipBlueprintDesc, ShipBlueprintSlotElemDesc } from './shipBlueprintDesc';
import { ShipEnchantDesc } from './shipEnchantDesc';
import { ShipEnchantStatRatioDesc } from './shipEnchantStatRatioDesc';
import { MateRecruitingGroupDesc } from './mateRecruitingGroupDesc';
import { MateReRecruitingDesc } from './mateReRecruitingDesc';
import { RewardData } from '../motiflib/gameLog';
import { BLACK_MARKET_TYPE, ShopDesc, SHOP_TYPE } from './shopDesc';
import { DiscoveryDesc, DISCOVERY_GROUP, DISCOVERY_TYPE } from './DiscoveryDesc';
import { REWARD_TYPE } from './rewardDesc';
import { TRADE_GOODS_CATEGORY, TradeGoodsDesc } from './tradeGoodsDesc';
import { HotTimeBonusDesc } from './hotTimeBonusDesc';
import { CEquipDesc, CEQUIP_TYPE } from './cEquipDesc';
import { RewardDropPoolDesc } from './rewardDropPoolDesc';
import * as mutil from '../motiflib/mutil';
import { GetFullWeeksUsingLocalTime, shuffleReportItems } from '../formula';
import { InvestCompanyRankingDesc } from './investCompanyRanking';
import { RewardFixedElemDesc } from './rewardFixedDesc';
import { VillageFriendshipDesc } from './villageFriendshipDesc';
import { EventMissionDesc, EventMissionType } from './eventMissionDesc';
import {
  PreferenceItem,
  ItemDesc,
  ITEM_GRADE,
  ITEM_TYPE,
  ITEM_REPORT_DIFFICULTY,
} from './itemDesc';
import { ShipyardBuildDesc } from './shipyardBuildDesc';
import { ArenaTierListDesc } from './arenaTierListDesc';
import { ArenaMatchBotDesc } from './arenaMatchBotDesc';
import { FishGradeDesc } from './fishGradeDesc';
import { CashShopDesc } from './cashShopDesc';
import { CashShopLimitSaleDesc, CASH_SHOP_LIMIT_SALE_POINT_TYPE } from './cashShopLimitSale';
import { EventRequestGroupDesc } from './EventrequestGroupDesc';
import { BuildingHoursElem } from './buildingHoursDesc';
import { TradeComboSetDesc } from './tradeComboSetDesc';
import { EventGameListDesc } from './eventGameListDesc';
import { BoardGameRewardDesc } from './boardGameRewardDesc';
import { TradeAreaTileDataDesc } from './tradeAreaTileDataDesc';
import { LatLon2Tile } from './oceanCoordinate';
import { TRADE_EVENT_TYPE } from '../motiflib/model/town';
import { TradeEventDesc } from './tradeEventDesc';
import { TileCoordinate } from './oceanProp';
import { Location } from '../motiflib/model/ocean';
import { LandExploreDesc } from './landExploreDesc';
import { AdventureEventRewardDesc } from './adventureEventRewardDesc';
import { MateTrainingDesc } from './mateTrainingDesc';
import { LiveEventDesc } from './liveEventDesc';
import { EventRankingRewardDesc } from './eventRankingRewardDesc';
import { ConstellationDesc } from './constellationDesc';
import { SHIP_CAMOUFLAGE_TYPE } from './shipCamouflageDesc';
import { ChoiceBoxDesc } from './choiceBoxDesc';
import { ExchangeVillageListDesc } from './ExchangeVillageListDesc';
import { ExchangeVillageStorageDesc } from './ExchangeVillageStorageDesc';
import { MateEquipmentNub } from '../motiflib/model/lobby';
import { RelationShipChronicleDesc } from './relationShipChronicleDesc';
import { QuestPassType } from './questPassDesc';
import { MErrorCode } from '../motiflib/merrorCode';
import { CEquipEnchantDesc } from './cEquipEnchantDesc';
import { ShipsSlotEnchantDesc } from './shipSlotEnchantDesc';
import { ENCHANT_TYPE, EnchantStatRatioDesc } from './enchantStatRatioDesc';
import {
  RewardCmsElemCommonExtra,
  RewardCmsElemMateEquipmentExtra,
  RewardCmsElemShipSlotItemExtra,
} from '../lobbyd/UserChangeTask/rewardAndPaymentChangeSpec';
import { ShipComposeDesc } from './shipComposeDesc';
import { TownNpcShopDesc } from './townNpcShopDesc';
import { TownNpcServerDesc } from './TownNpcServerDesc';
import { SmuggleDesc } from './smuggleDesc';
import { SMUGGLE_GOODS_CATEGORY } from './smuggleGoodsCategoryDesc';
import { KarmaDesc } from './karmaDesc';
import { NPC_INTERACTION_FUNCTION_TYPE } from './npcInteractionDesc';
import { InfiniteLighthouseScheduleDesc } from './infiniteLighthouseScheduleDesc';
import { InfiniteLighthouseDesc } from './infiniteLighthouseDesc';
import { InvestItem } from './investSeasonDesc';
import { RewardSeasonElemDesc } from './rewardSeasonItemsDesc';
import { AchievementDesc } from './achievementDesc';
import { HotTimeBuffDesc } from './hotTimeBuffDesc';
import { TownNpcGlobalDesc } from './townNpcGlobalDesc';
import { OceanNpcDesc } from './oceanNpcDesc';
import { FishDesc } from './fishDesc';
import { BossRaidDesc } from './BossRaidDesc';
import { MateTemplateGroupDesc } from './mateTemplateGroupDesc';
import { WorldStateDesc } from './worldStateDesc';
import { MANUFACTURE_TYPE } from './manufactureExpDesc';

export const DucatPointCmsId = 100001;
export const DepositPointCmsId = 100002;
export const BlueGemPointCmsId = 100003; // 블루젬
export const RedGemPointCmsId = 100004; // 레드잼
// 에너지는 cms, syncdata 상에만 point 에 있고 실제로는 UserEnergy 에서 관리됨.
export const EnergyPointCmsId = 100005;
export const CashShopMileage = 100006;
export const AchievementPointCmsId = 100007;
export const CashShopGachaBoxTokenCmsId = 100008; // 상자 교환권
export const PaidRedGemPointCmsId = 100009; // 레드잼(유료용)
export const ContributionPointCmsId = 100010; // 공헌도
export const GuildCoinCmsId = 100011; // 길드(상회)코인
// 모의전 입장권은 cms, syncdata 상에만 point 에 있고 실제로는 UserArena 에서 관리됨.
export const ArenaTicketCmsId = 100012; // 모의전 입장권

export const SmugglePointCmsId = 100014; // 밀수 교역상 이용에 필요한 포인트 (밀수단 문서 조각)
export const SmuggleShopPointCmsId = 100015; // 밀수단 지부장/단장 상점 이용에 필요한 포인트 (밀수단의 신용 증서)

export const ResearchPointCmsId = 100020; // 연구 포인트.
export const ManufacturePointCmsId = 100021; // 생산력.

export const RisbonTownCmsId = 11000000;

export const TradeGooodsStartingCmsId = 24000000;

export const ContentsTermsStartingCmsId = 81000000;
export const BattleContentsTermsStartingCmsId = 69400000;
export const BattleContentsTermsToContentsTermsOffset =
  ContentsTermsStartingCmsId - BattleContentsTermsStartingCmsId;

// 2036-01-19 03:14:09 (시간 만료가 없다는 의미로 사용됨)
export const TheEndTimeUtc = 2147483647;

// 항해사 부상 worldbuff cmsId
export const MateInjuryCmsId = 38010000;

// 항해사 태업 worldbuff cmsId
export const MateSlowdownCmsId = 38010036;

// 미카엘 2,3급 worldbuff cmsId
export const MichaelRank2CmsId = 38020531;
export const MichaelRank3CmsId = 38020532;

// 밀수품 보유시의 worldBuff cmsId
export const SmuggleWorldBuffCmsId = 38043300; // 밀수

export const REGION_REWARD_MAIL_CMS_ID = 92900001;
export const MAYOR_REWARD_MAIL_CMS_ID = 92002001;
export const INVEST_RANKER_MAIL_CMS_ID = 92002002;

// https://wiki.line.games/pages/viewpage.action?pageId=3310023
// 전투중 사용되는 선원수 수치에 적용.
export const BATTLE_SAILOR_MULTIPLIER = 10;

export const PRIME_MINISTER_CMS_ID = 10140001; // 총리
export const VICE_PRIME_MINISTER_CMS_ID = 10140002; // 부총리

export enum CHARACTER_TYPE {
  MATE = 0,
  LEADERABLE_MATE = 1, // Can become leader mate.
  NPC = 2,
}

// Ship slot index must be between 1 ~ 15.
export const ShipSlotIndexCaptainRoom = 1;
export const ShipCabinSlotIndexMax = 15;
export const ShipSlotIndexMax = 25;

export enum SHIP_CARGO_TYPE {
  NONE = 0,
  SUPPLY = 1,
  TRADE_GOODS = 2,
}

export enum CARGO_LOAD_PRESET_TYPE {
  NONE = 0,
  WATER = 1,
  FOOD = 2,
  LUMBER = 3,
  AMMO = 4,
  TRADE_GOODS = 5, // 교역품 + 밀수품
  ANY = 6, // 사용 안함.
}

export enum SUPPLY_CMS_ID {
  WATER = 40600001,
  FOOD = 40600002,
  LUMBER = 40600003,
  AMMO = 40600004,
}

export enum BUFF_CMS_ID {}
// HUNGER = 38010003, // 굶주림 디버프
//ANXIETY = 38001011, // 불안 디버프

export enum NATION_CMS_ID {
  PIRATE = 10001002, // 해적국가
  NEUTRAL_NATION = 10001001, // 중립국
}

export enum OCEAN_NPC_CMS_ID {
  USER_DATA_NPC_CMS_ID = 15000010, // 유저데이터로 npc생성 시 사용 할 cmsID
}

export enum SHIP_MOVE_TYPE {
  SAILING_SHIP = 1,
  GALLEY = 2,
}

export enum SHIP_BOTTOM_TYPE {
  POINTY = 1,
  FLAT = 2,
}

// 항해사 상태 플래그
export enum MATE_STATE_FLAG {
  SLOWDOWN = 1, // 태업
  INJURY = 1 << 1, // 부상
  AWOL = 1 << 2, // 이탈
}

// 장비, 부품에 대한 만료 타입(아이템은 미구현)
export enum EXPIRE_TYPE {
  PROVIDE = 1, // 제공할때 부터
  RECEIVE = 2, // 수령할때 부터
}

// ----------------------------------------------------------------------------
// 버프의 발생 출처.
// 추가 시 WorldBuffUtil.getSourceIdCmsByWorldBuffSourceType 수정 필요
// OX는 DB 저장여부
// ----------------------------------------------------------------------------
export enum WorldBuffSourceType {
  INVALID = 0,
  PRAYER = 1, // O
  DONATION = 2, // O
  USER_ITEM = 3, // O
  DISASTER = 4, // X
  PROTECT_ENCOUNT = 5, // X
  HUNGER = 6, // X
  CASH_SHOP_BUY_WITHOUT_PURCHASE = 7, // O
  OCEAN_DOODAD_EFFECT = 8, // OX
  POINT = 9, // OX
  CHEAT = 10, // X
  PROTECTION = 11, // X
  WORLD_TILE = 12, // X
  MATE_SET = 13, // X
  COMPANY_JOB = 14, // X
  WEATHER = 15, // X
  COMPANY_LEVEL = 16, // X
  OCCUPIED_NATION = 17, // X // 권역 점령국
  MATE_INJURY = 18, // X
  ANXIETY = 19, // X
  BATTLE_FORMATION = 20, // X
  MATE_PASSIVE = 21, // X
  WORLD_SKILL = 22, // O
  ADDED_TICK_BUFF = 23, // O
  COLLECTION_BUFF = 24, // X
  REVOLT = 25, // X
  TOWING = 26, // X
  MATE_SLOWDOWN = 27, // X
  ATTENDANCE = 28, // O
  LIVE_EVENT = 29, // X
  HOT_TIME_BUFF = 30, // X
  RETURNER_BUFF = 31, // O
  USER_TITLE = 32, // X
  GUILD_BUFF = 33, // X
  NATION_EFFECT_PROMISE = 34, // X
  BILLING = 35, // O,
  PET = 36, // X
  SMUGGLE = 37, // X
  SEASON_BUFF = 38, // O
  CLASH_BUFF = 39, // X
}

// ----------------------------------------------------------------------------
// 월드 대상 타입 (world buff, world active effect 등에서 사용)
// ----------------------------------------------------------------------------
export enum WorldTargetType {
  INVALID = 0,
  COMPANY = 1,
  FLEET = 2,
  SHIP = 3,
  MATE = 4,
}

// ----------------------------------------------------------------------------
// NPC, Doodad 스폰할 좌표의 타입
// ----------------------------------------------------------------------------
export enum SPAWN_RADIUS_TYPE {
  NONE = 0,
  CIRCLE = 1,
  SQUARE = 2,
  REGION = 3,
}

// ----------------------------------------------------------------------------
// NPC, Doodad 스폰 상태
// ----------------------------------------------------------------------------
export enum SPAWN_STATE {
  START = 0,
  SPAWNED = 1,
  DESPAWN = 2,
}

export function convertCargoCmsIdToLoadPresetType(cmsId: number): CARGO_LOAD_PRESET_TYPE {
  if (cmsId === SUPPLY_CMS_ID.WATER) {
    return CARGO_LOAD_PRESET_TYPE.WATER;
  }
  if (cmsId === SUPPLY_CMS_ID.FOOD) {
    return CARGO_LOAD_PRESET_TYPE.FOOD;
  }
  if (cmsId === SUPPLY_CMS_ID.LUMBER) {
    return CARGO_LOAD_PRESET_TYPE.LUMBER;
  }
  if (cmsId === SUPPLY_CMS_ID.AMMO) {
    return CARGO_LOAD_PRESET_TYPE.AMMO;
  }
  if (cms.TradeGoods[cmsId] || cms.SmuggleGoods[cmsId]) {
    return CARGO_LOAD_PRESET_TYPE.TRADE_GOODS;
  }
  return CARGO_LOAD_PRESET_TYPE.NONE;
}

export function convertLoadPresetTypeToSupplyCmsId(type: CARGO_LOAD_PRESET_TYPE): number {
  switch (type) {
    case CARGO_LOAD_PRESET_TYPE.WATER:
      return SUPPLY_CMS_ID.WATER;
    case CARGO_LOAD_PRESET_TYPE.FOOD:
      return SUPPLY_CMS_ID.FOOD;
    case CARGO_LOAD_PRESET_TYPE.LUMBER:
      return SUPPLY_CMS_ID.LUMBER;
    case CARGO_LOAD_PRESET_TYPE.AMMO:
      return SUPPLY_CMS_ID.AMMO;
    default:
      return 0;
  }
}

export function isFoodOrWater(cmsId: number): boolean {
  if (cmsId === SUPPLY_CMS_ID.WATER || cmsId === SUPPLY_CMS_ID.FOOD) {
    return true;
  }
  return false;
}

export const NoFleetIndex = 0;
export const FirstFleetIndex = 1;

export const FlagShipFormationIndex = 1;

export const CultureAreaCmsBaseCmsId = 12200000;
export const RoyalTitleBaseCmsId = 21700000;

export enum TOWN_OWN_TYPE {
  FREE_TOWN = 0, // 자유항
  CAPITAL_TOWN = 1, // 본거지
  NATIONAL_TOWN = 2, // 영지
  UNOCCUPIABLE = 3,
}

// Types of events that occur between two nations
export enum NATION_DIPLOMACY_CMS_ID {
  ATTACK_MY_NATION_FLEET = 10200000,
  ATTACK_OTHER_NATION_FLEET = 10200001,
  WIN_IN_BATTLE_WITH_MY_NATION_FLEET = 10200002,
  WIN_IN_BATTLE_WITH_OTHER_NATION_FLEET = 10200003,
  INVEST_IN_OWN_NATION_TOWN = 10200004,
  INVEST_IN_OTHER_NATION_TOWN = 10200005,
  BUY_AND_SELL_TRADE_GOODS = 10200006,
  COMPLETE_REQUEST = 10200007,
  CHANGE_NATION = 10200008,
  CHANGE_TOWN_NATION_BY_INVEST = 10200009,

  SWEEP_MY_NATION_FLEET = 10200015,
  SWEEP_OTHER_NATION_FLEET = 10200016,

  BUY_AND_SELL_MY_NATION_SMUGGLE_GOODS = 10200017,
  BUY_AND_SELL_OTHER_NATION_SMUGGLE_GOODS = 10200018,

  REMOTE_INVEST = 10200019,
}

// lower case specially.
export enum DEVELOPMENT_TYPE {
  none = 0,
  industry = 1,
  commerce = 2,
  armory = 3,
  max = 4,
}

export enum CULTURE_TYPE {
  HYBRID = 0, // 동서양 혼합
  WESTERN = 1, // 서양
  ORIENTAL = 2, // 동양
}

export enum ECONOMIC_BLOC_TYPE {
  WESTERN = 0,
  ORIENTAL = 1,
}

// 권역 속성 (안전,위험,무법)
export enum SEA_AREA_TYPE {
  SAFE = 1,
  DANGEROUS = 2,
  LAWLESS = 3,
}

// ----------------------------------------------------------------------------
// 인카운트 선택값. (CMSEx.lua 의 ENCOUNT_USER_CHOICE)
// ----------------------------------------------------------------------------
export enum EncountChoice {
  NONE = 0,

  ATT_ESCAPE_ALLOW = 1,
  ATT_ESCAPE_DENY = 2,
  ATT_SURRENDER_ALLOW = 3,
  ATT_SURRENDER_DENY = 4,
  ATT_NEGOTIATE_ALLOW = 5,
  ATT_NEGOTIATE_DELY = 6,

  DEF_START_BATTLE = 7,
  DEF_ESCAPE = 8,
  DEF_SURRENDER = 9,
  DEF_NEGOTIATE = 10,

  MIN = ATT_ESCAPE_ALLOW,
  MAX = DEF_NEGOTIATE,

  ATT_MIN = ATT_ESCAPE_ALLOW,
  ATT_MAX = ATT_NEGOTIATE_DELY,
  DEF_MIN = DEF_START_BATTLE,
  DEF_MAX = DEF_NEGOTIATE,
}

// 전문지식 스탯.
// https://docs.google.com/spreadsheets/d/18L1rhIFwGXAAijb2z7PPwo9aRNbm1CnltKu9fESbpi0/edit#gid=1128321814
export enum SPECIAL_STAT_TYPE {
  // 전투
  CANNON = 1,
  RAMMING = 2,
  SUPPORT = 3,
  MELEE = 4,

  // 교역
  BUY = 5,
  SELL = 6,
  NEGOTIATE = 7,
  TRADE = 8,

  // 모험
  NATURAL_HISTORY = 9,
  AESTHETICS = 10,
  SCOUT = 11,
  SUPPLY = 12,

  MIN = 1,
  MAX = 12,
}

export enum SPECIAL_STAT_MODIFIER_TYPE {
  ADD = 1,
  ADD_PCT = 2,
  ADD_LEVEL = 3,
}

export interface SpecialStatExpModifier {
  jobType: JOB_TYPE;
  modifierType: SPECIAL_STAT_MODIFIER_TYPE;
  value: number;
}

// shipTemplate cms overrideStat의 계산 방법
export enum SHIP_TEMPLATE_OVERRIDE_CALC_TYPE {
  SET = 1, // 덮어쓰기
  ADD = 2, // 추가
  MULTIPLY = 3, // 곱하기
}

export interface PreferenceDiscovery {
  discoveryType: DISCOVERY_TYPE;
  discoveryGrade: number;
  difficulty: DISCOVERY_MISSION_DIFFICULTY;
}

export enum DISCOVERY_MISSION_DIFFICULTY {
  EASY = 1,
  NORMAL = 2,
  HARD = 3,
}

// UWO stat type doc link
// https://docs.google.com/spreadsheets/d/15gj00RrBf27b3DLg7MeYtZVKBjKKXaTtLkAQnaYjlSk/edit#gid=1903224352
export enum STAT_TYPE {
  NONE = 0,

  // Ship. (0 ~ 99)
  SHIP_MIN_SAILOR = 1,
  SHIP_MAX_SAILOR = 2,
  SHIP_MAX_DURABILITY = 3,
  SHIP_HOLD = 4,
  SHIP_GUNPORTS = 5,
  SHIP_OAR_POWER = 6,
  SHIP_VERTICAL_SAIL = 7,
  SHIP_HORIZONTAL_SAIL = 8,
  SHIP_WAVE_RESISTANCE = 9,
  SHIP_ARMOR = 10,
  SHIP_LIFE = 11,
  SHIP_ACCELERATION = 12,
  SHIP_ANGULARPOWER = 13,

  // Mate. (101 ~ 199)
  MATE_COMMAND = 101,
  MATE_FORCE = 102,
  MATE_INTUITION = 103,
  MATE_NEGOTIATION = 104,
  MATE_KNOWLEDGE = 105,
  MATE_CHARM = 106,
  MATE_FORTUNE = 107,

  NONCOMBAT_ICE_BREAKING = 108,
  NONCOMBAT_BREAK_THROUGH = 109,

  // 전투. (1000 ~ 1999)
  BATTLE_RAMMING_POWER = 1000,

  BATTLE_HUMAN_ATTACK = 1002,

  BATTLE_MOVE_POWER = 1004,
  BATTLE_ROTATE_POWER = 1005,
  BATTLE_ACTION_POWER = 1006,
  BATTLE_2ND_CANNON_ATTACK = 1007,
  BATTLE_2ND_CANNON_DEFENSE = 1008,
  BATTLE_2ND_CANNON_ACCURACY = 1009,
  BATTLE_2ND_CANNON_DODGE_RATING = 1010,
  BATTLE_2ND_CANNON_CRITICAL = 1011,
  BATTLE_2ND_MELEE_ATTACK = 1012,
  BATTLE_2ND_MELEE_DEFENSE = 1013,
  BATTLE_2ND_MELEE_ACCURACY = 1014,
  BATTLE_2ND_MELEE_DODGE_RATING = 1015,
  BATTLE_2ND_MELEE_CRITICAL = 1016,
  BATTLE_2ND_RAMMING_ATTACK = 1017,
  BATTLE_REPAIR_AMOUNT = 1018,
  BATTLE_REPAIR_CRITICAL_RATING = 1019,
  BATTLE_HEAL_AMOUNT = 1020,
  BATTLE_HEAL_CRITICAL_RATING = 1021,

  BATTLE_ADDED_CANNON_ATTACK = 1022,
  BATTLE_ADDED_CANNON_ATTACK_PCT = 1023,
  BATTLE_ADDED_CANNON_DEFENSE = 1024,
  BATTLE_ADDED_CANNON_DEFENSE_PCT = 1025,
  BATTLE_ADDED_CANNON_ACCURACY = 1026,
  BATTLE_ADDED_CANNON_ACCURACY_PCT = 1027,
  BATTLE_ADDED_CANNON_CRITICAL = 1028,
  BATTLE_ADDED_CANNON_CRITICAL_PCT = 1029,
  BATTLE_CANNON_CRITICAL_DAMAGE_PCT = 1030,
  BATTLE_ADDED_CANNON_DODGE_RATING = 1031,
  BATTLE_ADDED_CANNON_DODGE_RATING_PCT = 1032,

  BATTLE_CANNON_COUNTER_ATTACK_DAMAGE_PCT = 1036,
  BATTLE_ADDED_MELEE_ATTACK = 1037,
  BATTLE_ADDED_MELEE_ATTACK_PCT = 1038,
  BATTLE_ADDED_MELEE_DEFENSE = 1039,
  BATTLE_ADDED_MELEE_DEFENSE_PCT = 1040,
  BATTLE_ADDED_MELEE_ACCURACY = 1041,
  BATTLE_ADDED_MELEE_ACCURACY_PCT = 1042,
  BATTLE_ADDED_MELEE_CRITICAL = 1043,
  BATTLE_ADDED_MELEE_CRITICAL_PCT = 1044,
  BATTLE_MELEE_CRITICAL_DAMAGE_PCT = 1045,
  BATTLE_ADDED_MELEE_DODGE_RATING = 1046,
  BATTLE_ADDED_MELEE_DODGE_RATING_PCT = 1047,

  BATTLE_MELEE_COUNTER_ATTACK_DAMAGE_PCT = 1051,
  BATTLE_ADDED_RAMMING_ATTACK = 1052,
  BATTLE_ADDED_RAMMING_ATTACK_PCT = 1053,
  BATTLE_RAMMING_COUNTER_ATTACK_DAMAGE_PCT = 1055,

  BATTLE_ADDED_REPAIR_AMOUNT = 1056,
  BATTLE_ADDED_REPAIR_AMOUNT_PCT = 1057,
  BATTLE_ADDED_REPAIR_CRITICAL_RATING = 1058,
  BATTLE_ADDED_HEAL_AMOUNT = 1059,
  BATTLE_ADDED_HEAL_AMOUNT_PCT = 1060,
  BATTLE_ADDED_HEAL_CRITICAL_RATING = 1061,
  BATTLE_ADDED_MOVE_POWER = 1062,
  BATTLE_ADDED_MOVE_POWER_PCT = 1063,
  BATTLE_ADDED_ROTATE_POWER = 1064,
  BATTLE_ADDED_ROTATE_POWER_PCT = 1065,
  BATTLE_ADDED_ACTION_POWER_RECOVER = 1066,
  BATTLE_ADDED_ACTION_POWER_RECOVER_PCT = 1067,

  BATTLE_DUEL_PREEMPTIVE_STRIKE_PCT = 1074, // 결투 선공 확률%
  BATTLE_ADDED_DUEL_HP = 1075, // 추가 결투 HP
  BATTLE_ADDED_DUEL_HP_PCT = 1076, // 추가 결투 HP%
  BATTLE_DUEL_ATTACK = 1077,
  BATTLE_DUEL_ATTACK_PCT = 1078, // 결투 공격력%
  BATTLE_DUEL_DEFENCE = 1079, // 결투 방어력
  BATTLE_DUEL_DEFENCE_PCT = 1080, // 결투 방어력%
  BATTLE_ACTION_POWER_RECOVER = 1081,
  BATTLE_2ND_RAMMING_DEFENSE = 1082,
  BATTLE_2ND_RAMMING_ACCURACY = 1083,
  BATTLE_2ND_RAMMING_DODGE_RATING = 1084,
  BATTLE_2ND_RAMMING_CRITICAL_RATING = 1085,
  BATTLE_ADDED_RAMMING_DEFENSE = 1086,
  BATTLE_ADDED_RAMMING_DEFENSE_PCT = 1087,
  BATTLE_ADDED_RAMMING_ACCURACY = 1088,
  BATTLE_ADDED_RAMMING_ACCURACY_PCT = 1089,
  BATTLE_ADDED_RAMMING_CRITICAL_RATING = 1090,
  BATTLE_ADDED_RAMMING_CRITICAL_RATING_PCT = 1091,
  BATTLE_RAMMING_CRITICAL_DAMAGE_PCT = 1092,
  BATTLE_ADDED_RAMMING_DODGE = 1093,
  BATTLE_ADDED_RAMMING_DODGE_PCT = 1094,
  BATTLE_ADDED_REPAIR_CRITICAL_RATING_PCT = 1095,
  BATTLE_REPAIR_CRITICAL_AMOUNT_PCT = 1096,
  BATTLE_ADDED_HEAL_CRITICAL_RATING_PCT = 1097,
  BATTLE_HEAL_CRITICAL_AMOUNT_PCT = 1098,
  BATTLE_ADDED_ACTION_POWER = 1099,
  BATTLE_ADDED_ACTION_POWER_PCT = 1100,
  BATTLE_DEBUFF_RESISTANCE_ALL_PCT = 1101,
  BATTLE_DEBUFF_RESISTANCE_CANNON_PCT = 1102,
  BATTLE_DEBUFF_RESISTANCE_SHIP_PCT = 1103,
  BATTLE_DEBUFF_RESISTANCE_MOVE_PCT = 1104,
  BATTLE_DEBUFF_RESISTANCE_SAILOR_PCT = 1105,

  BATTLE_DAMAGE_BONUS_PCT = 1106,
  BATTLE_ACCURACY_BONUS_PCT = 1107,
  BATTLE_CRITICAL_RATING_BONUS_PCT = 1108,

  BATTLE_ADDED_CANNON_CRITICAL_DAMAGE_PCT = 1109,
  BATTLE_ADDED_MELEE_CRITICAL_DAMAGE_PCT = 1110,
  BATTLE_ADDED_RAMMING_CRITICAL_DAMAGE_PCT = 1111,
  BATTLE_ADDED_REPAIR_CRITICAL_AMOUNT_PCT = 1112,
  BATTLE_ADDED_HEAL_CRITICAL_AMOUNT_PCT = 1113,

  BATTLE_SHIP_CANNON_DEFENSE = 1114,
  BATTLE_SHIP_MELEE_DEFENSE = 1115,
  BATTLE_SHIP_RAMMING_DEFENSE = 1116,
  BATTLE_SHIP_REPAIR_AMOUNT = 1117,
  BATTLE_SHIP_HEAL_AMOUNT = 1118,

  BATTLE_RAMMING_DISTANCE_BONUS_PCT = 1119,

  BATTLE_2ND_MIN_SAILOR = 1120,
  BATTLE_2ND_MAX_SAILOR = 1121,

  BATTLE_CEQUIP_ATTACK = 1122,
  BATTLE_CEQUIP_DEFENSE = 1123,

  BATTLE_CANNON_ADD_TAKE_DAMAGE = 1124,
  BATTLE_CANNON_ADD_TAKE_DAMAGE_PCT = 1125,
  BATTLE_MELEE_ADD_TAKE_DAMAGE = 1126,
  BATTLE_MELEE_ADD_TAKE_DAMAGE_PCT = 1127,
  BATTLE_RAMMING_ADD_TAKE_DAMAGE = 1128,
  BATTLE_RAMMING_ADD_TAKE_DAMAGE_PCT = 1129,

  BATTLE_ADDED_CANNON_CRITICAL_RATING_RESISTANCE = 1130,
  BATTLE_ADDED_CANNON_CRITICAL_RATING_RESISTANCE_PCT = 1131,
  BATTLE_2ND_CANNON_CRITICAL_RATING_RESISTANCE = 1132,
  BATTLE_ADDED_CANNON_CRITICAL_DAMAGE_RESISTANCE_PCT = 1133,
  BATTLE_2ND_CANNON_CRITICAL_DAMAGE_RESISTANCE_PCT = 1134,
  BATTLE_ADDED_MELEE_CRITICAL_RATING_RESISTANCE = 1135,
  BATTLE_ADDED_MELEE_CRITICAL_RATING_RESISTANCE_PCT = 1136,
  BATTLE_2ND_MELEE_CRITICAL_RATING_RESISTANCE = 1137,
  BATTLE_ADDED_MELEE_CRITICAL_DAMAGE_RESISTANCE_PCT = 1138,
  BATTLE_2ND_MELEE_CRITICAL_DAMAGE_RESISTANCE_PCT = 1139,
  BATTLE_ADDED_RAMMING_CRITICAL_RATING_RESISTANCE = 1140,
  BATTLE_ADDED_RAMMING_CRITICAL_RATING_RESISTANCE_PCT = 1141,
  BATTLE_2ND_RAMMING_CRITICAL_RATING_RESISTANCE = 1142,
  BATTLE_ADDED_RAMMING_CRITICAL_DAMAGE_RESISTANCE_PCT = 1143,
  BATTLE_2ND_RAMMING_CRITICAL_DAMAGE_RESISTANCE_PCT = 1144,

  BATTLE_SHIP_ACTION_POWER = 1145,

  // 교역 (2000 ~ 2999)
  TRADE_NEGOTIATE_TRIGGER_RATING = 2001,
  TRADE_NEGOTIATE_TRIGGER_RATING_PCT = 2002,
  TRADE_NEGOTIATE_SUCCESS_RATING = 2003,
  TRADE_NEGOTIATE_SUCCESS_RATING_PCT = 2004,
  TRADE_NEGOTIATE_COUNT = 2005,
  TRADE_NEGOTIATE_COUNT_PCT = 2006,
  TRADE_WORLD_MAP_PREDICT_PRICE = 2007,
  TOWN_KICK_ARREST_NEGO_SUCCESS_PROBABILITY = 2008, // 타운킥 체포, 협상 확률
  TRADE_NEGOTIATE_BUY_PRICE_PCT = 2009, // 협상 시 가격 할인율
  TRADE_NEGOTIATE_SALE_PRICE_PCT = 2010, // 협상 시 가격 할증률
  TRADE_BUY_PRICE_PCT = 2013, // 교역품 구매 가격 할인
  TRADE_SALE_PRICE_PCT = 2014, // 교역품 판매 가격 할증

  // 모험 (3000 ~ 3999)
  ADVENTURE_SURVEILLANCE_RANGE = 3001,
  ADVENTURE_DISASTER_PREDICTION_RATE = 3002,
  ADVENTURE_SAIL_SPEED = 3005, // (표기용) 구현 예정 없음
  ADVENTURE_SEARCH_RANGE = 3006, // 해양 탐색시 검색할 거리

  BONUS_FRIENDSHIP_POINT = 3114, // 마을 환대 우호도 % 증가
  ADVENTURE_DISCOVER_CHANCE_NATURE = 3115, // 자연 발견 확률
  ADVENTURE_DISCOVER_CHANCE_ANIMAL = 3116, // 동물 발견 확률
  ADVENTURE_DISCOVER_CHANCE_PLANT = 3117, // 식물 발견 확률
  ADVENTURE_DISCOVER_CHANCE_ARTIFACT = 3118, // 유물 발견 확률
  ADVENTURE_DISCOVER_CHANCE_TREASURE = 3119, // 보물 발견 확률
  ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE = 3120, // 건축 발견 확률

  ADVENTURE_LAND_EXPLORE_REWARD_DUCAT_GAIN_PCT = 3118, // 구현 안됨

  FISHING_SUCCESS_RATE = 3131,
  FISHING_GOODFISH_DROP_RATE = 3132,

  TOWN_KICK_ARREST_RUN_AWAY_SUCCESS_PROBABILITY = 3141, // 타운킥 체포, 도망 확률
  DISCOVERY_DROP_RATE = 3142,
  REPORT_DISCOVERY_GET_DUCAT_INCREASE = 3143, // 발견물 보고 시 재화 획득량 증가%
  REPORT_DISCOVERY_GET_FAME_INCREASE = 3144, // 발견물 보고 시 명성 획득량 증가%

  VILLAGE_PLUNDER_REDUCE_FRIENDSHIP_DECREASE = 3146, // 마을 약탈 시 우호도 감소량 감소%

  ADVENTURE_EXPLORE_COMBAT = 3148, // 탐험 전투력
  ADVENTURE_EXPLORE_GATHERING = 3149, // 탐험 채집력
  ADVENTURE_EXPLORE_OBSERVATION = 3150, // 탐험 관찰력
  ADVENTURE_REDUCE_FOOD_AND_WATER = 3151, // 탐험 물자 소비율 감소%
  ADVENTURE_REDUCE_COMBAT_SAILOR_DEATH = 3152, // 탐험 선원 사망률 감소
  ADVENTURE_REDUCE_COMBAT_MATE_INJURED = 3153, // 탐험 항해사 부상률 감소%
  ADVENTURE_INCREASE_HIGH_GRADE_EVENT_SPOT = 3154, // 고급 탐험 확률 증가%
  REPORT_RESOURCE_GET_DUCAT_INCREASE = 3155, // 자원 보고 시 재화 획득량 증가%
  REPORT_RESOURCE_GET_FAME_INCREASE = 3156, // 자원 보고 시 명성 획득량 증가%
  ADVENTURE_INCREASE_RATE_REWARD_COMBAT = 3157, // 탐험 전투 전리품 획득률 증가%
  ADVENTURE_INCREASE_RATE_REWARD_OBSERVATION = 3158, // 탐험 탐색 전리품 획득률 증가%
  ADVENTURE_INCREASE_RATE_REWARD_GATHER = 3159, // 탐험 채집 전리품 획득률 증가%
  ADVENTURE_REDUCE_PURCHASE_ADVENTURE_ITEM = 3160, // 모험 도구 구매 비용 감소%
  ADVENTURE_GET_COMBAT_STAT_VERSUS_PIRATE = 3161, // 탐험 해적 상대시 전투력 증가
  ADVENTURE_GET_COMBAT_STAT_VERSUS_WILD = 3162, // 탐험 맹수 상대시 전투력 증가
  ADVENTURE_INCREASE_RATE_DISCOVERY = 3163, // 탐험 발견물 획득률 증가%
  ADVENTURE_INCREASE_GET_FOOD_WATER_WOOD = 3164, // 탐험 물자 획득량 증가%

  // 건물 (4000 ~ 4999)
  BUILDING_BANK_WITHDRAWAL_FEE_MODIFIER = 4001,
  BUILDING_DEPART_SUPPLY_PRICE_MODIFIER = 4002,
  BUILDING_SHIPYARD_SHIP_PRICE_MODIFIER = 4003,
  BUILDING_SHIPYARD_REPAIRING_PRICE_MODIFIER = 4004,
  BUILDING_SHIPYARD_SHIP_SALE_PRICE_MODIFIER = 4005,
  BUILDING_PUB_SAILOR_DRAFTING_PRICE_MODIFIER = 4006,
  BUILDING_PUB_MATE_RECRUITING_PRICE_MODIFIER = 4007,
  BUILDING_RELIGION_DONATION_PRICE_MODIFIER = 4008,
  BUILDING_TRADE_GOODS_PRICE_MODIFIER = 4009,
  BUILDING_TRADE_GOODS_SALE_PRICE_MODIFIER = 4010,
  BUILDING_PUB_DRINK_PRICE_MODIFIER = 4011,
  BUILDING_DRAFTABLE_SAILORS = 4012,
  BUILDING_MATE_INTIMACY = 4013,
  BUILDING_RELIGION_PRAYER_PRICE_MODIFIER = 4014,
  BUILDING_SHOP_EQUIP_BUY_PRICE_MODIFIER = 4015,
  BUILDING_SHOP_EQUIP_SELL_PRICE_MODIFIER = 4016,
  BUILDING_SHOP_ITEM_BUY_PRICE_MODIFIER = 4017,
  BUILDING_SHOP_ITEM_SELL_PRICE_MODIFIER = 4018,
  BUILDING_SHOP_BLACKMARKET_BUY_PRICE_MODIFIER = 4019,
  BUILDING_MANTIC_FORTUNE_PRICE_MODIFIER = 4022,
  BUILDING_SHIPYARD_SHIP_SLOT_ITEM_PRICE_MODIFIER = 4023,
}

// WorldActiveEfect
// https://docs.google.com/spreadsheets/d/15gj00RrBf27b3DLg7MeYtZVKBjKKXaTtLkAQnaYjlSk/edit?pli=1#gid=1976171197
export enum ACTIVE_EFFECT {
  // 1척의 랜덤 선박 적용
  FLEET_CHANGE_WATER_RANDOMLY_SELECT_ONE_SHIP = 75110002,
  FLEET_CHANGE_WATER_PCT_RANDOMLY_SELECT_ONE_SHIP = 75110003,
  FLEET_CHANGE_FOOD_RANDOMLY_SELECT_ONE_SHIP = 75110004,
  FLEET_CHANGE_FOOD_PCT_RANDOMLY_SELECT_ONE_SHIP = 75110005,
  FLEET_CHANGE_DURABILITY_RANDOMLY_SELECT_ONE_SHIP = 75110006,
  FLEET_CHANGE_DURABILITY_PCT_RANDOMLY_SELECT_ONE_SHIP = 75110007,
  FLEET_CHANGE_SAILOR_RANDOMLY_SELECT_ONE_SHIP = 75110009,
  FLEET_CHANGE_SAILOR_PCT_RANDOMLY_SELECT_ONE_SHIP = 75110010,

  // 모든 선박에게 동일 적용
  FLEET_CHANGE_WATRER_EQUALLY_ALL_SHIP = 75110102,
  FLEET_CHANGE_WATRER_PCT_EQUALLY_ALL_SHIP = 75110103,
  FLEET_CHANGE_FOOD_EQUALLY_ALL_SHIP = 75110104,
  FLEET_CHANGE_FOOD_PCT_EQUALLY_ALL_SHIP = 75110105,
  FLEET_CHANGE_DURABILITY_EQUALLY_ALL_SHIP = 75110106,
  FLEET_CHANGE_DURABILITY_PCT_EQUALLY_ALL_SHIP = 75110107,
  FLEET_CHANGE_SAILOR_EQUALLY_ALL_SHIP = 75110109,
  FLEET_CHANGE_SAILOR_PCT_EQUALLY_ALL_SHIP = 75110110,
  FLEET_CHANGE_LOYALTY_EQUALLY_ALL_SHIP = 75111026,
  FLEET_CHANGE_LOYALTY_PCT_EQUALLY_ALL_SHIP = 75111027,
  FLEET_REMOVE_DISASTER_EQUALLY_ALL_SHIP = 75111028,
  FLEET_REMOVE_MATE_INJURY_EQUALLY_ALL_SHIP = 75111038,

  // 1번 선박부터 순차적으로 가능한 만큼 적용
  FLEET_CHANGE_WATRER_SEQUENTIALLY_ALL_SHIP = 75111039,
  FLEET_CHANGE_FOOD_SEQUENTIALLY_ALL_SHIP = 75111040,

  SHIP_CHANGE_LOYALTY = 75110012,
  SHIP_CHANGE_LOYALTY_PCT = 75110013,
  SHIP_CHANGE_WATER = 75111001,
  SHIP_CHANGE_WATER_PCT = 75111002,
  SHIP_CHANGE_FOOD = 75111003,
  SHIP_CHANGE_FOOD_PCT = 75111004,
  SHIP_CHANGE_DURABILITY = 75111005,
  SHIP_CHANGE_DURABILITY_PCT = 75111006,
  SHIP_CHANGE_SAILOR = 75111008,
  SHIP_CHANGE_SAILOR_PCT = 75111009,

  SHIP_CHANGE_TRADE_GOODS_LIKE_COMBUSTIBLE = 75111016,
  SHIP_CHANGE_TRADE_GOODS_LIKE_COMBUSTIBLE_PCT = 75111017,
  SHIP_CHANGE_TRADE_GOODS_LIKE_VALUABLE = 75111018,
  SHIP_CHANGE_TRADE_GOODS_LIKE_VALUABLE_PCT = 75111019,
  SHIP_CHANGE_TRADE_GOODS_LIKE_SPOILABLE = 75111020,
  SHIP_CHANGE_TRADE_GOODS_LIKE_SPOILABLE_PCT = 75111021,
  SHIP_CHANGE_TRADE_GOODS_LIKE_FALLDEAD = 75111022,
  SHIP_CHANGE_TRADE_GOODS_LIKE_FALLDEAD_PCT = 75111023,
  SHIP_CHANGE_TRADE_GOODS_LIKE_BREEDING = 75111024,
  SHIP_CHANGE_TRADE_GOODS_LIKE_BREEDING_PCT = 75111025,
}

// WorldPassiveEfect
// https://docs.google.com/spreadsheets/d/15gj00RrBf27b3DLg7MeYtZVKBjKKXaTtLkAQnaYjlSk/edit?pli=1#gid=*********
// 월드에서 (타운/항해) 사용되는 효과들인데, stat 과 매우 유사함.
// 하지만, 그 정의가 다른만큼, 추후 기획 요구에 대응하기 위해 스탯과는 일단 분리해둠.
export enum PASSIVE_EFFECT {
  COMPANY_ADDED_EXP = 75300000,
  COMPANY_ADDED_EXP_PCT = 75300001,

  MATE_ADDED_ALL_EXP = 75300010,
  MATE_ADDED_ALL_EXP_PCT = 75300011,
  MATE_ADDED_BATTLE_EXP = 75300020,
  MATE_ADDED_BATTLE_EXP_PCT = 75300021,
  MATE_ADDED_TRADE_EXP = 75300030,
  MATE_ADDED_TRADE_EXP_PCT = 75300031,
  MATE_ADDED_ADVENTURE_EXP = 75300040,
  MATE_ADDED_ADVENTURE_EXP_PCT = 75300041,

  MATE_ADDED_BATTLE_FAME = 75300050,
  MATE_ADDED_BATTLE_FAME_PCT = 75300051,
  MATE_ADDED_TRADE_FAME = 75300060,
  MATE_ADDED_TRADE_FAME_PCT = 75300061,
  MATE_ADDED_ADVENTURE_FAME = 75300070,
  MATE_ADDED_ADVENTURE_FAME_PCT = 75300071,

  TRADE_GOODS_PRICE = 75300080,
  TRADE_GOODS_PRICE_PCT = 75300081,

  TRADE_NEGOTIATE_DISCOUNT_RATE = 75300090,
  TRADE_NEGOTIATE_EXTRA_CHARGE_RATE = 75300100,
  TRADE_TARIFF_RATE = 75300110,

  TRADE_RESTOCK_DURATION = 75300120,
  TRADE_RESTOCK_DURATION_PCT = 75300121,

  TRADE_DEVELOPMENT_COMMERCE = 75300130,
  TRADE_DEVELOPMENT_COMMERCE_PCT = 75300131,
  TRADE_DEVELOPMENT_ARMORY = 75300132,
  TRADE_DEVELOPMENT_ARMORY_PCT = 75300133,
  TRADE_DEVELOPMENT_INDUSTRY = 75300134,
  TRADE_DEVELOPMENT_INDUSTRY_PCT = 75300135,

  TRADE_ADDED_BUYABLE_FOOD = 75300140,
  TRADE_ADDED_BUYABLE_FOOD_PCT = 75300141,
  TRADE_ADDED_BUYABLE_CONDIMENT = 75300150,
  TRADE_ADDED_BUYABLE_CONDIMENT_PCT = 75300151,
  TRADE_ADDED_BUYABLE_LIVESTOCK = 75300160,
  TRADE_ADDED_BUYABLE_LIVESTOCK_PCT = 75300161,
  TRADE_ADDED_BUYABLE_MEDICINE = 75300170,
  TRADE_ADDED_BUYABLE_MEDICINE_PCT = 75300171,
  TRADE_ADDED_BUYABLE_GENERAL = 75300180,
  TRADE_ADDED_BUYABLE_GENERAL_PCT = 75300181,
  TRADE_ADDED_BUYABLE_LIQUOR = 75300190,
  TRADE_ADDED_BUYABLE_LIQUOR_PCT = 75300191,
  TRADE_ADDED_BUYABLE_DYE = 75300200,
  TRADE_ADDED_BUYABLE_DYE_PCT = 75300201,
  TRADE_ADDED_BUYABLE_ORE = 75300210,
  TRADE_ADDED_BUYABLE_ORE_PCT = 75300211,
  TRADE_ADDED_BUYABLE_INDUSTRIAL = 75300220,
  TRADE_ADDED_BUYABLE_INDUSTRIAL_PCT = 75300221,
  TRADE_ADDED_BUYABLE_LUXURY = 75300230,
  TRADE_ADDED_BUYABLE_LUXURY_PCT = 75300231,
  TRADE_ADDED_BUYABLE_TEXTILE = 75300240,
  TRADE_ADDED_BUYABLE_TEXTILE_PCT = 75300241,
  TRADE_ADDED_BUYABLE_FABRIC = 75300250,
  TRADE_ADDED_BUYABLE_FABRIC_PCT = 75300251,
  TRADE_ADDED_BUYABLE_WEAPON = 75300260,
  TRADE_ADDED_BUYABLE_WEAPON_PCT = 75300261,
  TRADE_ADDED_BUYABLE_FIREARM = 75300270,
  TRADE_ADDED_BUYABLE_FIREARM_PCT = 75300271,
  TRADE_ADDED_BUYABLE_CRAFT = 75300280,
  TRADE_ADDED_BUYABLE_CRAFT_PCT = 75300281,
  TRADE_ADDED_BUYABLE_ART = 75300290,
  TRADE_ADDED_BUYABLE_ART_PCT = 75300291,
  TRADE_ADDED_BUYABLE_SPICE = 75300300,
  TRADE_ADDED_BUYABLE_SPICE_PCT = 75300301,
  TRADE_ADDED_BUYABLE_JEWELRY = 75300310,
  TRADE_ADDED_BUYABLE_JEWELRY_PCT = 75300311,
  TRADE_ADDED_BUYABLE_AROMA = 75300320,
  TRADE_ADDED_BUYABLE_AROMA_PCT = 75300321,
  TRADE_ADDED_BUYABLE_GEM = 75300330,
  TRADE_ADDED_BUYABLE_GEM_PCT = 75300331,

  TRADE_DISCOUNT_FOOD = 75300340,
  TRADE_DISCOUNT_CONDIMENT = 75300350,
  TRADE_DISCOUNT_LIVESTOCK = 75300360,
  TRADE_DISCOUNT_MEDICINE = 75300370,
  TRADE_DISCOUNT_GENERAL = 75300380,
  TRADE_DISCOUNT_LIQUOR = 75300390,
  TRADE_DISCOUNT_DYE = 75300400,
  TRADE_DISCOUNT_ORE = 75300410,
  TRADE_DISCOUNT_INDUSTRIAL = 75300420,
  TRADE_DISCOUNT_LUXURY = 75300430,
  TRADE_DISCOUNT_TEXTILE = 75300440,
  TRADE_DISCOUNT_FABRIC = 75300450,
  TRADE_DISCOUNT_WEAPON = 75300460,
  TRADE_DISCOUNT_FIREARM = 75300470,
  TRADE_DISCOUNT_CRAFT = 75300480,
  TRADE_DISCOUNT_ART = 75300490,
  TRADE_DISCOUNT_SPICE = 75300500,
  TRADE_DISCOUNT_JEWELRY = 75300510,
  TRADE_DISCOUNT_AROMA = 75300520,
  TRADE_DISCOUNT_GEM = 75300530,

  TRADE_EXTRA_CHARGE_FOOD = 75300540,
  TRADE_EXTRA_CHARGE_CONDIMENT = 75300550,
  TRADE_EXTRA_CHARGE_LIVESTOCK = 75300560,
  TRADE_EXTRA_CHARGE_MEDICINE = 75300570,
  TRADE_EXTRA_CHARGE_GENERAL = 75300580,
  TRADE_EXTRA_CHARGE_LIQUOR = 75300590,
  TRADE_EXTRA_CHARGE_DYE = 75300600,
  TRADE_EXTRA_CHARGE_ORE = 75300610,
  TRADE_EXTRA_CHARGE_INDUSTRIAL = 75300620,
  TRADE_EXTRA_CHARGE_LUXURY = 75300630,
  TRADE_EXTRA_CHARGE_TEXTILE = 75300640,
  TRADE_EXTRA_CHARGE_FABRIC = 75300650,
  TRADE_EXTRA_CHARGE_WEAPON = 75300660,
  TRADE_EXTRA_CHARGE_FIREARM = 75300670,
  TRADE_EXTRA_CHARGE_CRAFT = 75300680,
  TRADE_EXTRA_CHARGE_ART = 75300690,
  TRADE_EXTRA_CHARGE_SPICE = 75300700,
  TRADE_EXTRA_CHARGE_JEWELRY = 75300710,
  TRADE_EXTRA_CHARGE_AROMA = 75300720,
  TRADE_EXTRA_CHARGE_GEM = 75300730,

  TRADE_PREDICT_PRICE_FOOD = 75300740,
  TRADE_PREDICT_PRICE_CONDIMENT = 75300750,
  TRADE_PREDICT_PRICE_LIVESTOCK = 75300760,
  TRADE_PREDICT_PRICE_MEDICINE = 75300770,
  TRADE_PREDICT_PRICE_GENERAL = 75300780,
  TRADE_PREDICT_PRICE_LIQUOR = 75300790,
  TRADE_PREDICT_PRICE_DYE = 75300800,
  TRADE_PREDICT_PRICE_ORE = 75300810,
  TRADE_PREDICT_PRICE_INDUSTRIAL = 75300820,
  TRADE_PREDICT_PRICE_LUXURY = 75300830,
  TRADE_PREDICT_PRICE_TEXTILE = 75300840,
  TRADE_PREDICT_PRICE_FABRIC = 75300850,
  TRADE_PREDICT_PRICE_WEAPON = 75300860,
  TRADE_PREDICT_PRICE_FIREARM = 75300870,
  TRADE_PREDICT_PRICE_CRAFT = 75300880,
  TRADE_PREDICT_PRICE_ART = 75300890,
  TRADE_PREDICT_PRICE_SPICE = 75300900,
  TRADE_PREDICT_PRICE_JEWELRY = 75300910,
  TRADE_PREDICT_PRICE_AROMA = 75300920,
  TRADE_PREDICT_PRICE_GEM = 75300930,

  TRADE_INVESTMENT_SCORE_PCT = 75300950,

  TRADE_GOODS_CHANGE_RATE_BREED_PCT = 75300960,

  ADVENTURE_ADDED_SHIP_SAIL_SPEED = 75310000,
  ADVENTURE_ADDED_SHIP_SAIL_SPEED_PCT = 75310001,
  ADVENTURE_ADDED_CONSUME_WATER = 75310002,
  ADVENTURE_ADDED_CONSUME_WATER_PCT = 75310003,
  ADVENTURE_ADDED_CONSUME_FOOD = 75310004,
  ADVENTURE_ADDED_CONSUME_FOOD_PCT = 75310005,
  ADVENTURE_ADDED_SHIP_SAIL_DAMAGE_RATE = 75310006, // 구현 안됨
  ADVENTURE_ADDED_SHIP_KEY_DAMAGE_RATE = 75310007, // 구현 안됨
  ADVENTURE_ADDED_RADAR_RANGE = 75310008, // 구현 안됨
  ADVENTURE_ENCOUNT_RATE = 75310009,
  ADVENTURE_ADDED_MATE_INJURY_RATE = 75310010,
  ADVENTURE_ADDED_MATE_INJURY_DURATION = 75310011,
  ADVENTURE_ADDED_MATE_INJURY_HEAL_RATE = 75310012,
  ADVENTURE_ADDED_FLEET_SAIL_SPEED = 75310013,
  ADVENTURE_ADDED_FLEET_SAIL_SPEED_PCT = 75310014,
  ADVENTURE_ADDED_SHIP_HORIZONTAL_SAIL = 75310015,
  ADVENTURE_ADDED_SHIP_HORIZONTAL_SAIL_PCT = 75310016,
  ADVENTURE_ADDED_SHIP_VERTICAL_SAIL = 75310017,
  ADVENTURE_ADDED_SHIP_VERTICAL_SAIL_PCT = 75310018,
  ADVENTURE_ADDED_SHIP_OAR_POWER = 75310019,
  ADVENTURE_ADDED_SHIP_OAL_POWER_PCT = 75310020,

  ADVENTURE_PREDICT_DISASTER = 75311000,

  // 재해 방어 관련
  ADVENTURE_DISASTER_DEFENSE_RATS = 75311001,
  ADVENTURE_DISASTER_DEFENSE_FILTH = 75311002,
  ADVENTURE_DISASTER_DEFENSE_SCURVY = 75311003,
  ADVENTURE_DISASTER_DEFENSE_EPIDEMIC = 75311004,
  ADVENTURE_DISASTER_DEFENSE_DECAY = 75311005,
  ADVENTURE_DISASTER_DEFENSE_MALARIA = 75311006,
  ADVENTURE_DISASTER_DEFENSE_FIRE = 75311007,
  ADVENTURE_DISASTER_DEFENSE_SEAWEED = 75311008,
  ADVENTURE_DISASTER_DEFENSE_REEF = 75311009,
  ADVENTURE_DISASTER_DEFENSE_FLOODING = 75311010,
  ADVENTURE_DISASTER_DEFENSE_GREAT_FIRE = 75311011,
  ADVENTURE_DISASTER_DEFENSE_REVOLT = 75311012,
  ADVENTURE_DISASTER_DEFENSE_FIGHT = 75311013,
  ADVENTURE_DISASTER_DEFENSE_CARGO_THEFT = 75311014,
  ADVENTURE_DISASTER_DEFENSE_CARGO_DAMAGE = 75311015,
  ADVENTURE_DISASTER_DEFENSE_FRUSTRATION = 75311016,
  ADVENTURE_DISASTER_DEFENSE_ANXIETY = 75311017,
  ADVENTURE_DISASTER_DEFENSE_HOMESICK = 75311018,
  ADVENTURE_DISASTER_DEFENSE_INSOMNIA = 75311019,
  ADVENTURE_DISASTER_DEFENSE_STORM = 75311020,
  ADVENTURE_DISASTER_DEFENSE_BIG_WAVE = 75311021,
  ADVENTURE_DISASTER_DEFENSE_BLIZZARD = 75311022,
  ADVENTURE_DISASTER_DEFENSE_WHIRLWIND = 75311023,
  ADVENTURE_DISASTER_DEFENSE_WINDLESS = 75311024,
  ADVENTURE_DISASTER_DEFENSE_MAGNETIC_DISTURBANCE = 75311025,
  ADVENTURE_DISASTER_DEFENSE_SIREN = 75311026,
  ADVENTURE_DISASTER_DEFENSE_SHARK = 75311027,
  ADVENTURE_DISASTER_DEFENSE_WHALE = 75311028,
  ADVENTURE_DISASTER_DEFENSE_HYPOTHERMIA = 75311029,
  ADVENTURE_DISASTER_DEFENSE_FROSTBITE = 75311030,

  // 재해 자연 해결 관련
  ADVENTURE_DISASTER_RESOLVE_RATS = 75311100,
  ADVENTURE_DISASTER_RESOLVE_FILTH = 75311101,
  ADVENTURE_DISASTER_RESOLVE_SCURVY = 75311102,
  ADVENTURE_DISASTER_RESOLVE_EPIDEMIC = 75311103,
  ADVENTURE_DISASTER_RESOLVE_DECAY = 75311104,
  ADVENTURE_DISASTER_RESOLVE_MALARIA = 75311105,
  ADVENTURE_DISASTER_RESOLVE_FIRE = 75311106,
  ADVENTURE_DISASTER_RESOLVE_SEAWEED = 75311107,
  ADVENTURE_DISASTER_RESOLVE_FLOODING = 75311108,
  ADVENTURE_DISASTER_RESOLVE_GREAT_FIRE = 75311109,
  ADVENTURE_DISASTER_RESOLVE_REVOLT = 75311110,
  ADVENTURE_DISASTER_RESOLVE_FIGHT = 75311111,
  ADVENTURE_DISASTER_RESOLVE_FRUSTRATION = 75311112,
  ADVENTURE_DISASTER_RESOLVE_ANXIETY = 75311113,
  ADVENTURE_DISASTER_RESOLVE_HOMESICK = 75311114,
  ADVENTURE_DISASTER_RESOLVE_INSOMNIA = 75311115,
  ADVENTURE_DISASTER_RESOLVE_STORM = 75311116,
  ADVENTURE_DISASTER_RESOLVE_BLIZZARD = 75311117,
  ADVENTURE_DISASTER_RESOLVE_WINDLESS = 75311118,
  ADVENTURE_DISASTER_RESOLVE_MAGNETIC_DISTURBANCE = 75311119,
  ADVENTURE_DISASTER_RESOLVE_SIREN = 75311120,
  ADVENTURE_DISASTER_RESOLVE_SHARK = 75311121,
  ADVENTURE_DISASTER_RESOLVE_HYPOTHERMIA = 75311122,
  ADVENTURE_DISASTER_RESOLVE_FROSTBITE = 75311123,
  ADVENTURE_DISASTER_RESOLVE_REEF = 75311124,
  ADVENTURE_DISASTER_RESOLVE_CARGO_THEFT = 75311125,
  ADVENTURE_DISASTER_RESOLVE_WHIRLWIND = 75311126,

  // 인카운트 보호 관련
  ADVENTURE_PROTECT_ENCOUNT_FROM_USER = 75311150,
  ADVENTURE_PROTECT_ENCOUNT_FROM_NPC = 75311151,

  // 재해 발생 확률
  ADVENTURE_DISASTER_GENERATE_PCT = 75312000,

  SHIELD_ADD_DAILY_COUNT_DISASTER = 75312001,
  // 전투 관련
  BATTLE_ADDED_MAX_TURN_TAKE_BACK_COUNT = 75312002, // [전투] 턴 무료 무르기 횟수 추가
  BATTLE_ADDED_MAX_QUICK_MODE_COUNT = 75312003, // [전투] 퀵 모드 최대 횟수 추가
  BATTLE_TURN_TAKE_BACK_COUNT_UNLIMITED = 75312004, // [전투] 턴 무르기 무제한
  BATTLE_QUICK_MODE_COUNT_UNLIMITED = 75312005, // [전투] 퀵모드 횟수 무제한

  TRADE_GOODS_SALE_PRICE_PCT = 75312094, // [교역] 판매 가격 할증

  BATTLE_ADDED_MAX_PHASE_TAKE_BACK_COUNT = 75312097, // [전투] 페이즈 무료 무르기 최대 추가
  BATTLE_PHASE_TAKE_BACK_COUNT_UNLIMITED = 75312098, // [전투] 페이즈 무르기 무제한

  ADVENTURE_NO_TOWN_KICK_IF_TOWN_KICK_RESULT_IS_1 = 75312008, // 입항 거부 면제 (KICK_RESULT_TYPE 이 1인 경우에만)
  ADVENTURE_DISATER_PROTECT = 75312009, // 재해 보호(1이상이면 재해에 걸리지않는다.)
  ADVENTURE_INSURANCE_COST_PCT = 75312010,

  ADVENTURE_CHANGE_MATE_LOYALTY_DECREASE_SPEED_PCT = 75312011,

  BATTLE_MATE_ADDED_BATTLE_EXP_COMPANY = 75312012,
  BATTLE_MATE_ADDED_BATTLE_EXP_PCT_COMPANY = 75312013,
  TRADE_MATE_ADDED_TRADE_EXP_COMPANY = 75312014,
  TRADE_MATE_ADDED_TRADE_EXP_PCT_COMPANY = 75312015,
  ADVENTURE_MATE_ADDED_ADVENTURE_EXP_COMPANY = 75312016,
  ADVENTURE_MATE_ADDED_ADVENTURE_EXP_PCT_COMPANY = 75312017,

  ADVENTURE_DISCOVER_CHANCE_NATURE = 75312018,
  ADVENTURE_DISCOVER_CHANCE_ANIMAL = 75312019,
  ADVENTURE_DISCOVER_CHANCE_PLANT = 75312020,
  ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE = 75312021,
  ADVENTURE_DISCOVER_CHANCE_ARTIFACT = 75312022,
  ADVENTURE_DISCOVER_CHANCE_TREASURE = 75312023,

  ADVENTURE_ADDED_GALLEY_SHIP_SAIL_SPEED = 75312024,
  ADVENTURE_ADDED_GALLEY_SHIP_SAIL_SPEED_PCT = 75312025,

  ADVENTURE_ADDED_FLEET_ACCELERATION = 75312036, // 구현 안됨
  ADVENTURE_ADDED_FLEET_ACCELERATION_PCT = 75312037, // 구현 안됨
  ADVENTURE_ADDED_SHIP_ACCELERATION = 75312038, // 구현 안됨
  ADVENTURE_ADDED_SHIP_ACCELERATION_PCT = 75312039, // 구현 안됨
  ADVENTURE_ADDED_FLEET_ANGULARPOWER = 75312040, // 구현 안됨
  ADVENTURE_ADDED_FLEET_ANGULARPOWER_PCT = 75312041, // 구현 안됨
  ADVENTURE_ADDED_SHIP_ANGULARPOWER = 75312042, // 구현 안됨
  ADVENTURE_ADDED_SHIP_ANGULARPOWER_PCT = 75312043, // 구현 안됨

  FISHING_GOODFISH_DROP_RATE = 75312051, // 상급 어종 발견 확률

  REPORT_DISCOVERY_GET_DUCAT_INCREASE = 75312054, // 발견물 보고 시 재화 획득량 증가%
  REPORT_DISCOVERY_GET_FAME_INCREASE = 75312055, // 발견물 보고 시 명성 획득량 증가%

  REPORT_RESOURCE_GET_DUCAT_INCREASE = 75312060, // 자원 보고 시 재화 획득량 증가%
  REPORT_RESOURCE_GET_FAME_INCREASE = 75312061, // 자원 보고 시 명성 획득량 증가%
  ADVENTURE_REDUCE_COMBAT_MATE_INJURED = 75312062, // 탐험 항해사 부상률 감소%
  ADVENTURE_REDUCE_COMBAT_SAILOR_DEATH = 75312063, // 탐험 선원 사망률 감소%
  ADVENTURE_INCREASE_RATE_REWARD_COMBAT = 75312064, // 탐험 전투 전리품 획득률 증가%
  ADVENTURE_INCREASE_RATE_REWARD_OBSERVATION = 75312065, // 탐험 탐색 전리품 획득률 증가%
  ADVENTURE_INCREASE_RATE_REWARD_GATHER = 75312066, // 탐험 채집 전리품 획득률 증가%
  ADVENTURE_REDUCE_FOOD_AND_WATER = 75312067, // 탐험 물자 소비 감소%
  ADVENTURE_INCREASE_HIGH_GRADE_EVENT_SPOT = 75312068, // 고급 탐험 확률 증가%
  ADVENTURE_REDUCE_PURCHASE_ADVENTURE_ITEM = 75312069, // 모험 도구 구매 비용 감소%
  ADVENTURE_GET_COMBAT_STAT_VERSUS_PIRATE = 75312070, // 탐험 해적 상대시 전투력 증가
  ADVENTURE_GET_COMBAT_STAT_VERSUS_WILD = 75312071, // 탐험 맹수 상대시 전투력 증가
  ADVENTURE_INCREASE_RATE_DISCOVERY = 75312072, // 탐험 발견물 발견 확률 증가%
  ADVENTURE_INCREASE_GET_FOOD_WATER_WOOD = 75312073, // 탐험 자원 획득량 증가%
  ADVENTURE_ADD_DAILY_TICKET_COUNT = 75312074, // 탐험 수련 입장 횟수 증가

  BONUS_FRIENDSHIP_POINT = 75312075, // 마을 환대 우호도 % 증가
  VILLAGE_PLUNDER_REDUCE_FRIENDSHIP_DECREASE = 75312076, // 마을 약탈 시 우호도 감소량 감소%
  ADVENTURE_SEARCH_RANGE = 75312077, // 해양 탐색시 검색할 거리

  SHIELD_ADD_DAILY_COUNT_RESURRECTION = 75312092,

  TRADE_GOODS_BUY_PRICE_PCT = 75312093, // 구매 가격 할인

  TRADE_ADDED_BUYABLE = 75312095, // 구매 수량 증가

  TRADE_NEGOTIATE_TRIGGER_RATING = 75312096,

  SHIELD_ADD_DAILY_COUNT_RETURN_TO_TOWN = 75312099,

  ADVENTURE_MATE_LOYALTY_DECREASE_PREVENT = 75312102, // 충성도 감소 방지(1이상이면 감소되지 않는다.)
  ADVENTURE_MATE_INJURY_PREVENT = 75312103, // 부상 발생 방지(1이상이면 감소되지 않는다.)

  TRADE_NEGOTIATE_SUCCESS_RATING = 75312104,
  EXPLORE_ADDED_MAX_QUICK_MODE_COUNT = 75312105, // [육지탐험] 퀵 모드 최대 횟수 추가
  BATTLE_CONTINUOUS_ADDED_COUNT = 75312106, // 연속 전투 횟수 증가

  // 함대안의 항해사 명성.
  MATE_ADDED_BATTLE_FAME_FLEET_FOR_ORDER = 75312107,
  MATE_ADDED_BATTLE_FAME_PCT_FLEET_FOR_ORDER = 75312108,
  MATE_ADDED_TRADE_FAME_FLEET_FOR_ORDER = 75312109,
  MATE_ADDED_TRADE_FAME_PCT_FLEET_FOR_ORDER = 75312110,
  MATE_ADDED_ADVENTURE_FAME_FLEET_FOR_ORDER = 75312111,
  MATE_ADDED_ADVENTURE_FAME_PCT_FLEET_FOR_ORDER = 75312112,
  // 함대안의 항해사 경험치.
  BATTLE_MATE_ADDED_BATTLE_EXP_FLEET = 75312113,
  BATTLE_MATE_ADDED_BATTLE_EXP_PCT_FLEET = 75312114,
  TRADE_MATE_ADDED_TRADE_EXP_FLEET = 75312115,
  TRADE_MATE_ADDED_TRADE_EXP_PCT_FLEET = 75312116,
  ADVENTURE_MATE_ADDED_ADVENTURE_EXP_FLEET = 75312117,
  ADVENTURE_MATE_ADDED_ADVENTURE_EXP_PCT_FLEET = 75312118,

  TRADE_POINT_TO_EARN_DECREASE_PCT = 75312119, // 거래 점수 기준 감소

  COMPANY_ADDED_EXP_WITH_MATES_PCT = 75312120, // https://jira.line.games/browse/UWO-19917

  TRADE_COMBO_INCREASE_MAXIMUM = 75312121, // 기본 콤보 배율로 획득 가능한 최대치 제한을 증가시킨다

  ALWAYS_ENTER_BLACK_MARKET = 75312122, // 암시장 상시 입장

  ADVENTURE_FREE_WAYPOINT_SUPPLY = 75312123, // 항로 자동 보급 무료 횟수

  CAPTURE_SHIP_ADDED_PCT = 75312124,

  COMPANY_FLEET_DISPATCH_ADDED_EXPIRE_TIME = 75312126, // 파견 시간 변화
  COMPANY_FLEET_DISPATCH_ADDED_EXPIRE_TIME_PCT = 75312127, // 파견 시간 변화%
  COMPANY_FLEET_DISPATCH_BATTLE_ADDED_EXPIRE_TIME = 75312128, // 전투 파견 시간 변화
  COMPANY_FLEET_DISPATCH_BATTLE_ADDED_EXPIRE_TIME_PCT = 75312129, // 전투 파견 시간 변화%
  COMPANY_FLEET_DISPATCH_TRADE_ADDED_EXPIRE_TIME = 75312130, // 교역 파견 시간 변화
  COMPANY_FLEET_DISPATCH_TRADE_ADDED_EXPIRE_TIME_PCT = 75312131, // 교역 파견 시간 변화%
  COMPANY_FLEET_DISPATCH_ADVENTURE_ADDED_EXPIRE_TIME = 75312132, // 탐험 파견 시간 변화
  COMPANY_FLEET_DISPATCH_ADVENTURE_ADDED_EXPIRE_TIME_PCT = 75312133, // 탐험 파견 시간 변화%

  TASK_REWARD_INCREASE = 75312135,
  TAX_FREE = 75312136,
  SHIP_BLUEPRINT_EXP_PCT = 75312137,
  SHIP_SAIL_MASTERY_EXP_PCT = 75312138,

  LAND_EXPLORE_QUICK_MODE_COUNT_UNLIMITED = 75312140, // 육지 탐색 쾌속 무제한

  ORIENT_USER_BUILDING_EXP_PCT = 75312141,
  WEST_USER_BUILDING_EXP_PCT = 75312142,
  ALL_SHIP_BUILDING_EXP_PCT = 75312143,
  TRADE_BUY_ADDED_TRADE_GOODS_QUANTITY_PCT = 75312144,
  CHANGE_SHIP_BUILDING_TIME_PCT = 75312145,

  // 보급품 가격 변경

  RELATE_ITEM_RATIO_PCT = 75312201, // 특수자원 2배 획득 확률%

  KARMA_DECREASE = 75312204, // 업보 감소.

  FISHING_BIG_CATCH_ADDED_RELATE_ITEM = 75312210, // 월척 시 추가 자원 획득

  BUILDING_DEPART_SUPPLY_PRICE_MULTIPLY = 75312212,

  // 물물교환
  EXCHANGE_ADDED_COUNT = 75312151,
  EXCHANGE_GET_GOODS_PCT = 75312152,

  EXCHANGE_GET_GOODS_FOOD_PCT = 75312153,
  EXCHANGE_GET_GOODS_CONDIMENT_PCT = 75312154,
  EXCHANGE_GET_GOODS_LIVESTOCK_PCT = 75312155,
  EXCHANGE_GET_GOODS_MEDICINE_PCT = 75312156,
  EXCHANGE_GET_GOODS_GENERAL_PCT = 75312157,
  EXCHANGE_GET_GOODS_LIQUOR_PCT = 75312158,
  EXCHANGE_GET_GOODS_DYE_PCT = 75312159,
  EXCHANGE_GET_GOODS_ORE_PCT = 75312160,
  EXCHANGE_GET_GOODS_INDUSTRIAL_PCT = 75312161,
  EXCHANGE_GET_GOODS_LUXURY_PCT = 75312162,
  EXCHANGE_GET_GOODS_TEXTILE_PCT = 75312163,
  EXCHANGE_GET_GOODS_FABRIC_PCT = 75312164,
  EXCHANGE_GET_GOODS_CRAFT_PCT = 75312165,
  EXCHANGE_GET_GOODS_ART_PCT = 75312166,
  EXCHANGE_GET_GOODS_SPICE_PCT = 75312167,
  EXCHANGE_GET_GOODS_JEWELRY_PCT = 75312168,
  EXCHANGE_GET_GOODS_AROMA_PCT = 75312169,
  EXCHANGE_GET_GOODS_GEM_PCT = 75312170,
  EXCHANGE_GET_GOODS_WEAPON_PCT = 75312171,
  EXCHANGE_GET_GOODS_FIREARM_PCT = 75312172,

  EXCHANGE_PRICE_PCT = 75312173,

  EXCHANGE_PRICE_FOOD_PCT = 75312174,
  EXCHANGE_PRICE_CONDIMENT_PCT = 75312175,
  EXCHANGE_PRICE_LIVESTOCK_PCT = 75312176,
  EXCHANGE_PRICE_MEDICINE_PCT = 75312177,
  EXCHANGE_PRICE_GENERAL_PCT = 75312178,
  EXCHANGE_PRICE_LIQUOR_PCT = 75312179,
  EXCHANGE_PRICE_DYE_PCT = 75312180,
  EXCHANGE_PRICE_ORE_PCT = 75312181,
  EXCHANGE_PRICE_INDUSTRIAL_PCT = 75312182,
  EXCHANGE_PRICE_LUXURY_PCT = 75312183,
  EXCHANGE_PRICE_TEXTILE_PCT = 75312184,
  EXCHANGE_PRICE_FABRIC_PCT = 75312185,
  EXCHANGE_PRICE_CRAFT_PCT = 75312186,
  EXCHANGE_PRICE_ART_PCT = 75312187,
  EXCHANGE_PRICE_SPICE_PCT = 75312188,
  EXCHANGE_PRICE_JEWELRY_PCT = 75312189,
  EXCHANGE_PRICE_AROMA_PCT = 75312190,
  EXCHANGE_PRICE_GEM_PCT = 75312191,
  EXCHANGE_PRICE_WEAPON_PCT = 75312192,
  EXCHANGE_PRICE_FIREARM_PCT = 75312193,

  EXCHANGE_PRICE_NEGO_FRIENDSHIP = 75312194,
  EXCHANGE_PRICE_NEGO_FRIENDSHIP_PCT = 75312195,

  EXCHANGE_GET_GOODS_NEGO_FRIENDSHIP = 75312196,
  EXCHANGE_GET_GOODS_NEGO_FRIENDSHIP_PCT = 75312197,

  EXCHANGE_PRICE_NEGO_SUCCESS_PCT = 75312198,
  EXCHANGE_GET_GOODS_NEGO_SUCCESS_PCT = 75312199,

  ADVENTURE_ADDED_GET_WATER_PCT = 75312209, // 항해 중 물 획득 증가%
  ADVENTURE_ADDED_GET_WATER = 75312213, // 항해 중 물 획득
  REMOTE_SHIP_CREATE_UNLIMITED = 75312214, // 원격 건조 무제한
  FREE_SHIP_DELIVER = 75312215, // 무료 탁송 무제한

  SWEEP_LOCAL_NPC_TICKET_DECREASE_COUNT = 75312216, // 소탕권 소모 갯수 감소.

  // 운하
  CANAL_USE_DEFAULT_COST_PCT = 75312218, // 운하 기본 비용 증가%
  CANAL_USE_TRADE_GOODS_COST_PCT = 75312219, // 운하 교역 비용 증가%
  CANAL_USE_FREE_COUNT = 75312220, // 운하 교역품 운송 무료 횟수 증가
  CANAL_USE_MAX_COUNT = 75312221, // 운하 이용 최대 횟수 증가
  ADVENTURE_EXPLORE_GATHERING = 75312223, // 탐험 채집력
  ADVENTURE_EXPLORE_OBSERVATION = 75312224, // 탐험 관찰력
  ADVENTURE_EXPLORE_COMBAT = 75312225, // 탐험 전투력

  OCEAN_SAILING_KNOT_SOFT_CAP_ADD = 75312226, // 최대 속도 제한 증가

  REMOTE_INVEST = 75312222, // 원격 투자

  // 인양
  // 75312227, 75312228 인게임이 클라 위주로 돌아가기 때문에 해당 값은 서버에선 사용될 수 없음.
  // https://wiki.line.games/pages/viewpage.action?pageId=117505815
  SALVAGE_ADDED_DEEP_SEA_SCORE = 75312227,
  SALVAGE_ADDED_SHALLOW_SEA_SCORE = 75312228,

  SALVAGE_ADDED_DEEP_SEA_REWARD_AMOUNT = 75312229,
  SALVAGE_ADDED_SHALLOW_SEA_REWARD_AMOUNT = 75312230,

  SALVAGE_ADDED_DEEP_SEA_REWARD_SCORE = 75312231,
  SALVAGE_ADDED_SHALLOW_SEA_REWARD_SCORE = 75312232,

  SALVAGE_ADDED_SHALLOW_SEA_LOOP_DAMAGED = 75312233,
  SALVAGE_ADDED_REWARD_AMOUNT = 75312234,

  // 인양-산호초
  SALVAGE_ADDED_CORAL_REEF_SCORE = 75312235,
  SALVAGE_ADDED_CORAL_REEF_REWARD_AMOUNT = 75312236,
  SALVAGE_ADDED_CORAL_REEF_REWARD_SCORE = 75312237,

  // 인양-대륙봉
  SALVAGE_ADDED_CONTINENTAL_SHELF_SCORE = 75312238,
  SALVAGE_ADDED_CONTINENTAL_SHELF_REWARD_AMOUNT = 75312239,
  SALVAGE_ADDED_CONTINENTAL_SHELF_REWARD_SCORE = 75312240,

  // 인양-해구
  SALVAGE_ADDED_TRENCH_SCORE = 75312241,
  SALVAGE_ADDED_TRENCH_REWARD_AMOUNT = 75312242,
  SALVAGE_ADDED_TRENCH_REWARD_SCORE = 75312243,

  // 인양-해령
  SALVAGE_ADDED_OCEAN_RIDGE_SCORE = 75312244,
  SALVAGE_ADDED_OCEAN_RIDGE_REWARD_AMOUNT = 75312245,
  SALVAGE_ADDED_OCEAN_RIDGE_REWARD_SCORE = 75312246,

  SALVAGE_ADDED_DEEP_SEA_LOOP_DAMAGED = 75312311,
  SALVAGE_ADDED_CORAL_REEF_LOOP_DAMAGED = 75312312,
  SALVAGE_ADDED_CONTINENTAL_SHELF_DAMAGED = 75312313,
  SALVAGE_ADDED_TRENCH_LOOP_DAMAGED = 75312314,
  SALVAGE_ADDED_OCEAN_RIDGE_LOOP_DAMAGED = 75312315,

  // 밀수
  POSSESS_SMUGGLE_GOODS = 75312258, // 밀수품 소지
  SMUGGLE_ADDED_BUYABLE_PCT = 75312259, // 밀수품 구매 수량 %
  SMUGGLE_ADDED_BUYABLE_ILLEGAL_PCT = 75312260, // 불법 밀수품 구매 수량 %
  SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS_PCT = 75312261, // 만용 밀수품 구매 수량 %
  SMUGGLE_ADDED_BUYABLE_GREED_PCT = 75312262, // 탐욕 밀수품 구매 수량 %
  SMUGGLE_ADDED_BUYABLE_LUXURY_PCT = 75312263, // 사치 밀수품 구매 수량 %

  SMUGGLE_ADDED_BUYABLE = 75312264, // 밀수품 구매 수량
  SMUGGLE_ADDED_BUYABLE_ILLEGAL = 75312265, // 불법 밀수품 구매 수량
  SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS = 75312266, // 만용 밀수품 구매 수량
  SMUGGLE_ADDED_BUYABLE_GREED = 75312267, // 탐욕 밀수품 구매 수량
  SMUGGLE_ADDED_BUYABLE_LUXURY = 75312268, // 사치 밀수품 구매 수량

  SMUGGLE_NEGOTIATE_TRIGGER_RATING_PCT = 75312269, // 밀수품 협상 발생 확률 %
  SMUGGLE_NEGOTIATE_ADDITIONAL_TRIGGER_RATING_PCT = 75312270, // 밀수품 추가 협상 성공 확률 %
  SMUGGLE_NEGOTIATE_COUNT = 75312271, // 밀수품 협상 발생 횟수 변화
  SMUGGLE_NEGOTIATE_SUCCESS_RATING_PCT = 75312272, // 밀수품 협상 성공 확률 %
  SMUGGLE_NEGOTIATE_BUY_PRICE_PCT = 75312273, // 밀수품 협상 성공 시 할인율
  SMUGGLE_NEGOTIATE_SALE_PRICE_PCT = 75312274, // 밀수품 협상 성공 시 할증율

  SMUGGLING_CHECK_RATING_PCT = 75312285, // 밀수 검문 발동 확률 %

  SMUGGLE_POINT_GAIN_PCT = 75312286, // 밀수단 문서 조각 획득 확률 %
  SMUGGLE_POINT_GAIN_AMOUNT_PCT = 75312287, // 밀수단 문서 조각 획득량 %
  SMUGGLE_POINT_GAIN_AMOUNT = 75312288, // 밀수단 문서 조각 획득량

  SMUGGLE_SHOP_POINT_GAIN_AMOUNT_PCT = 75312289, // 밀수단 신용 증서 획득량 %
  SMUGGLE_SHOP_POINT_GAIN_AMOUNT = 75312290, // 밀수단 신용 증서 획득량

  SMUGGLE_BUY_PRICE_PCT = 75312291, // 밀수품 구매 가격 할인율 %
  SMUGGLE_BUY_PRICE_ILLEGAL_PCT = 75312292, // 불법 밀수품 구매 가격 할인율 %
  SMUGGLE_BUY_PRICE_FOOLHARDINESS_PCT = 75312293, // 만용 밀수품 구매 가격 할인율 %
  SMUGGLE_BUY_PRICE_GREED_PCT = 75312294, // 탐욕 밀수품 구매 가격 할인율 %
  SMUGGLE_BUY_PRICE_LUXURY_PCT = 75312295, // 사치 밀수품 구매 가격 할인율 %

  SMUGGLE_BUY_PRICE = 75312296, // 밀수품 구매 가격 할인
  SMUGGLE_BUY_PRICE_ILLEGAL = 75312297, // 불법 밀수품 구매 가격 할인
  SMUGGLE_BUY_PRICE_FOOLHARDINESS = 75312298, // 만용 밀수품 구매 가격 할인
  SMUGGLE_BUY_PRICE_GREED = 75312299, // 탐욕 밀수품 구매 가격 할인
  SMUGGLE_BUY_PRICE_LUXURY = 75312300, // 사치 밀수품 구매 가격 할인

  SMUGGLE_SALE_PRICE_PCT = 75312301, // 밀수품 판매 가격 할증률 %
  SMUGGLE_SALE_PRICE_ILLEGAL_PCT = 75312302, // 불법 밀수품 판매 가격 할증률 %
  SMUGGLE_SALE_PRICE_FOOLHARDINESS_PCT = 75312303, // 만용 밀수품 판매 가격 할증률 %
  SMUGGLE_SALE_PRICE_GREED_PCT = 75312304, // 탐욕 밀수품 판매 가격 할증률 %
  SMUGGLE_SALE_PRICE_LUXURY_PCT = 75312305, // 사치 밀수품 판매 가격 할증률 %

  SMUGGLE_SALE_PRICE = 75312306, // 밀수품 판매 가격 할증
  SMUGGLE_SALE_PRICE_ILLEGAL = 75312307, // 불법 밀수품 판매 가격 할증
  SMUGGLE_SALE_PRICE_FOOLHARDINESS = 75312308, // 만용 밀수품 판매 가격 할증
  SMUGGLE_SALE_PRICE_GREED = 75312309, // 탐욕 밀수품 판매 가격 할증
  SMUGGLE_SALE_PRICE_LUXURY = 75312310, // 사치 밀수품 판매 가격 할증

  TASK_AUTO_CLEAR = 75312316, // 과제 자동 완료
}

export const DiscoverChanceSpecialStatByDiscoveryGroup = {
  [DISCOVERY_GROUP.NATURE]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_NATURE,
  [DISCOVERY_GROUP.ANIMAL]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_ANIMAL,
  [DISCOVERY_GROUP.PLANT]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_PLANT,
  [DISCOVERY_GROUP.ARCHITECTURE]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE,
  [DISCOVERY_GROUP.ARTIFACT]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_ARTIFACT,
  [DISCOVERY_GROUP.TREASURE]: STAT_TYPE.ADVENTURE_DISCOVER_CHANCE_TREASURE,
};

export const DiscoverChancePassiveEffectByDiscoveryGroup = {
  [DISCOVERY_GROUP.NATURE]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_NATURE,
  [DISCOVERY_GROUP.ANIMAL]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_ANIMAL,
  [DISCOVERY_GROUP.PLANT]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_PLANT,
  [DISCOVERY_GROUP.ARCHITECTURE]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_ARCHITECTURE,
  [DISCOVERY_GROUP.ARTIFACT]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_ARTIFACT,
  [DISCOVERY_GROUP.TREASURE]: PASSIVE_EFFECT.ADVENTURE_DISCOVER_CHANCE_TREASURE,
};

export enum CONTENTS_TERMS_CMS_ID {
  NONE = 0,
  LANGUAGE = 81000000,
  COMPANY_LEVEL = 81000001,
  SHIPS_COUNT = 81000002,
  SHIPS_COUNT_OVER_BP_LEVEL = 81000003,
  SPECIFIC_SHIPS_COUNT = 81000004,
  SPECIFIC_BLUEPRINT_LEVEL = 81000005,
  CUR_TOWN_REGION = 81000006,
  CUR_TOWN = 81000007,
  VISIT_REGION = 81000008,
  INVEST_ANY_TOWN_ANY_DEVELOPMENT_TYPE = 81000010,
  INVEST_ANY_TOWN_SPECIPIC_DEVELOPMENT_TYPE = 81000011,
  INVEST_SPECIPIC_REGION_ANY_DEVELOPMENT_TYPE = 81000012,
  ACCUMULATED_INVEST = 81000014,
  INVEST_SPECIPIC_REGION_SPECIPIC_DEVELOPMENT_TYPE = 81000015,
  INVEST_POINT_CUR_TOWN_ANY_DEVELOPMENT_TYPE = 81000016,
  ADMIRALS_COUNT = 81000017,
  HAS_SPECIFIC_ADMIRAL = 81000018,
  MATES_COUNT = 81000019,
  SPECIFIC_NATION_MATES_COUNT = 81000020,
  HAS_SPECIFIC_MATE = 81000021,
  SPECIFIC_MATE_ADVENTURE_LEVEL = 81000022,
  SPECIFIC_MATE_TRADE_LEVEL = 81000023,
  SPECIFIC_MATE_BATTLE_LEVEL = 81000024,
  SPECIFIC_MATE_SUM_LEVEL = 81000025,
  MATES_COUNT_OVER_LOYALTY = 81000026,
  SPECIFIC_MATE_LOYALTY = 81000027,
  CUR_TOWN_SPECIPIC_DEVELOPMENT_LEVEL = 81000028,
  CUR_TOWN_SUM_DEVELOPMENT_LEVEL = 81000029,
  SEASON = 81000030,
  MONTH = 81000031,
  WEATHER = 81000032,
  SPECIFIC_MATE_ROYAL_TITLE = 81000033,
  SPECIFIC_MATE_HIGHEST_LEVEL = 81000034,
  REGION_OCCUPIED = 81000035,
  REGION_COMPLETELY_OCCUPIED = 81000036,
  QUEST_GLOBAL_REGISTER_BIT_FLAG = 81000037,
  COMPANY_LEVEL_LESS_THAN = 81000038,
  QUEST_NODE = 81000039,
  IS_MATE_IN_FIRST_FLEET = 81000040,
  ADMIRAL_ADVENTURE_LEVEL = 81000041,
  COMPARE_ADMIRAL_ROYAL_TITLE = 81000042,
  IS_CLEAR_QUEST_SCENARIO = 81000043,
  ALIVE_SHIPS_COUNT_IN_FIRST_FLEET_GREATER_OR_EQUAL = 81000044,
  ALIVE_SHIPS_COUNT_IN_FIRST_FLEET_LESS_THAN = 81000045,
  HAS_SPECIFIC_BLUEPRINT = 81000046,
  SPECIFIC_JOB_MATES_COUNT = 81000047,
  REPORTED_WORLD_MAP_TILE_PERCENT = 81000048,
  QUEST_NODE_EQUAL = 81000049,
  USER_SHIP_BUILD_LEVEL = 81000050,
  UNIQUE_GROUP_COMPLETE_QUEST = 81000051,
  IS_DISCOVERY = 81000052,
  ALWAYS_FAIL = 81000053,
  SPECIFIC_ITEM_COUNT = 81000054,
  ACHIEVEMENT_COMPLETE = 81000056,

  CUR_TOWN_NATION = 81000057,
  IS_CUR_TOWN_MY_GUILD = 81000058,
  NOT_HAS_SPECIFIC_ITEM = 81000059,
  KARMA = 81000060,

  CUR_VILLAGE_FRIENDSHIP = 81000064,
  CUR_VILLAGE_EVENT = 81000065,
  TARGET_TOWN_INDUSTRY_DEVELOPMENT_LEVEL = 81000066, // 특정 항구 공업발전LV
  TARGET_TOWN_COMMERCE_DEVELOPMENT_LEVEL = 81000067, // 특정 항구 상업발전LV
  TARGET_TOWN_ARMORY_DEVELOPMENT_LEVEL = 81000068, // 특정 항구 군사발전LV
  IS_TARGET_TOWN_MY_GUILD = 81000069, // 특정 항구의 상회 독점
  INVEST_POINT_TARGET_TOWN_ANY_DEVELOPMENT_TYPE = 81000070, // 특정 항구 투자 총액

  LIVE_EVENT_PROGRESS = 81000072, // LiveEvent 진행중 체크

  IS_TARGET_TOWN_NOT_MY_GUILD = 81000073, //

  CASH_SHOP_BUY_COUNT = 81000074, // 특정 상품 구매 횟수

  PUB_STAFF_INTIMACY = 81000075, // 종업원 친밀도

  SPECIAL_STAT_LEVEL = 81000076, // 전문 지식 LV
  KARMA_LEVEL = 81000077, // 카르마 단계
}

interface DisasterWpeIdLinker {
  [a: number]: {
    defenseWpeType: number;
    resolveWpeType: number;
  };
}

export enum DISASTER_CMS_ID_TYPE {
  WRECK = 16100000, // 난파
  RATS = 16100001, // 쥐
  FILTH = 16100002, // 비위생
  SCURVY = 16100003, // 괴혈병
  EPIDEMIC = 16100004, // 전염병
  MALARIA = 16100005, // 말라리아
  FIRE = 16100006, // 화재
  SEAWEED = 16100007, // 해초
  REEF = 16100008, // 암초
  FLOODING = 16100009, // 침
  GREAT_FIRE = 16100010, // 대화재
  DECAY = 16100011, // 부패
  FIGHT = 16100012, // 싸움
  REVOLT = 16100013, // 반란
  CARGO_DAMAGE = 16100014, // 적재화물 붕괴
  CARGO_THEFT = 16100015, // 적재화물 도난
  FRUSTRATION = 16100016, // 욕구불만
  HOMESICK = 16100017, // 향수병
  ANXIETY = 16100018, // 정신 불안
  INSOMNIA = 16100019, // 불면증
  STORM = 16100020, // 폭풍
  BIG_WAVE = 16100021, // 높은 파도
  BLIZZARD = 16100022, // 눈보라
  WHIRLWIND = 16100023, // 돌풍
  WINDLESS = 16100024, // 무풍
  DISTURBANCE = 16100025, // 자기장 이상
  SIREN = 16100026, // 세이렌
  SHARK = 16100027, // 식인상어
  WHALE = 16100028, // 고래 충돌
  KRAKEN = 16100029, // 크라켄
  HYPOTHERMIA = 16100030, // 처체온증
  FROSTBITE = 16100031, // 동상
  BERMUDA_TRIANGLE = 16100032, // 버뮤다 삼각지대
  HYPOTHERMIA_POLAR = 16100040, // 저체온증 (북극해에서 확률이 높은 저체온증 16100030)
}

// 재해아이디 종류에 따라 재해방어 능력치 타입과 자연 해결 능력치 타입을 연결 시킨다.
// 재해가 적용되기 전 fleet의 능력치[defenseWpeType]에 따라 방어 성공여부를 판단하며,
// 재해가 적용 후 fleet의의 능력치[resolveWpeType]에 따라 자연해결 성공여부를 판단한다.
export const _DisasterWpeIdLinker: DisasterWpeIdLinker = {
  // 난파
  [DISASTER_CMS_ID_TYPE.WRECK]: {
    defenseWpeType: 0,
    resolveWpeType: 0,
  },
  // 쥐
  [DISASTER_CMS_ID_TYPE.RATS]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_RATS,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_RATS,
  },
  // 비위생
  [DISASTER_CMS_ID_TYPE.FILTH]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FILTH,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FILTH,
  },
  // 괴혈병
  [DISASTER_CMS_ID_TYPE.SCURVY]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SCURVY,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SCURVY,
  },
  // 전염병
  [DISASTER_CMS_ID_TYPE.EPIDEMIC]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_EPIDEMIC,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_EPIDEMIC,
  },
  // 말라리아
  [DISASTER_CMS_ID_TYPE.MALARIA]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_MALARIA,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_MALARIA,
  },
  // 화재
  [DISASTER_CMS_ID_TYPE.FIRE]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FIRE,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FIRE,
  },
  // 해초
  [DISASTER_CMS_ID_TYPE.SEAWEED]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SEAWEED,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SEAWEED,
  },
  // 암초
  [DISASTER_CMS_ID_TYPE.REEF]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_REEF,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_REEF,
  },
  // 침수
  [DISASTER_CMS_ID_TYPE.FLOODING]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FLOODING,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FLOODING,
  },
  // 대화재
  [DISASTER_CMS_ID_TYPE.GREAT_FIRE]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_GREAT_FIRE,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_GREAT_FIRE,
  },
  // 부패
  [DISASTER_CMS_ID_TYPE.DECAY]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_DECAY,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_DECAY,
  },
  // 싸움
  [DISASTER_CMS_ID_TYPE.FIGHT]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FIGHT,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FIGHT,
  },
  // 반란
  [DISASTER_CMS_ID_TYPE.REVOLT]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_REVOLT,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_REVOLT,
  },
  // 적재화물 붕괴
  [DISASTER_CMS_ID_TYPE.CARGO_DAMAGE]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_CARGO_DAMAGE,
    resolveWpeType: 0,
  },
  // 적재화물 도난
  [DISASTER_CMS_ID_TYPE.CARGO_THEFT]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_CARGO_THEFT,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_CARGO_THEFT,
  },
  // 욕구불만
  [DISASTER_CMS_ID_TYPE.FRUSTRATION]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FRUSTRATION,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FRUSTRATION,
  },
  // 향수병
  [DISASTER_CMS_ID_TYPE.HOMESICK]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_HOMESICK,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_HOMESICK,
  },
  // 정신 불안
  [DISASTER_CMS_ID_TYPE.ANXIETY]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_ANXIETY,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_ANXIETY,
  },
  // 불면증
  [DISASTER_CMS_ID_TYPE.INSOMNIA]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_INSOMNIA,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_INSOMNIA,
  },
  // 폭풍
  [DISASTER_CMS_ID_TYPE.STORM]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_STORM,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_STORM,
  },
  // 높은 파도
  [DISASTER_CMS_ID_TYPE.BIG_WAVE]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_BIG_WAVE,
    resolveWpeType: 0,
  },
  // 눈보라
  [DISASTER_CMS_ID_TYPE.BLIZZARD]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_BLIZZARD,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_BLIZZARD,
  },
  // 돌풍
  [DISASTER_CMS_ID_TYPE.WHIRLWIND]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_WHIRLWIND,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_WHIRLWIND,
  },
  // 무풍
  [DISASTER_CMS_ID_TYPE.WINDLESS]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_WINDLESS,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_WINDLESS,
  },
  // 자기장 이상
  [DISASTER_CMS_ID_TYPE.DISTURBANCE]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_MAGNETIC_DISTURBANCE,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_MAGNETIC_DISTURBANCE,
  },
  // 세이렌
  [DISASTER_CMS_ID_TYPE.SIREN]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SIREN,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SIREN,
  },
  // 식인상어
  [DISASTER_CMS_ID_TYPE.SHARK]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_SHARK,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_SHARK,
  },
  // 고래 충돌
  [DISASTER_CMS_ID_TYPE.WHALE]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_WHALE,
    resolveWpeType: 0,
  },
  // 크라켄
  [DISASTER_CMS_ID_TYPE.KRAKEN]: {
    defenseWpeType: 0,
    resolveWpeType: 0,
  },

  [DISASTER_CMS_ID_TYPE.HYPOTHERMIA]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_HYPOTHERMIA,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_HYPOTHERMIA,
  },

  [DISASTER_CMS_ID_TYPE.FROSTBITE]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_FROSTBITE,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_FROSTBITE,
  },

  // 버뮤다 삼각지대
  [DISASTER_CMS_ID_TYPE.BERMUDA_TRIANGLE]: {
    defenseWpeType: 0, // 당장은 기획없음 확인완료
    resolveWpeType: 0,
  },
  [DISASTER_CMS_ID_TYPE.HYPOTHERMIA_POLAR]: {
    defenseWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_DEFENSE_HYPOTHERMIA,
    resolveWpeType: PASSIVE_EFFECT.ADVENTURE_DISASTER_RESOLVE_HYPOTHERMIA,
  },
};

// 재해와 버블이벤트 링크
export const _BubbleEventCmsId = {
  ForShipDurability: ********,
  ForMateLoyalty: ********,
};

export enum CONTENTS_TERMS_TYPE {
  NONE = 0,
  ACCUMULATION = 1,
  CURRENT_VALUE = 2,
}

export enum BUILDING_CONTENTS_UNLOCK_CMS_ID {
  NONE = 0,
  LANGUAGE = ********,
  BANK_DEPOSIT_WITHDRAW = ********,
  BANK_DEPOSIT_INSTALLMENT_SAVINGS = ********,
  BANK_INSURANCE = ********,
  DEPART_DOCK = ********, // 기획에서 해당 컨텐츠에서는 언어 확인을 클라에서만 하기로 결정.
  SHIPYARD_CREATE_SHIP = ********,
  SHIPYARD_CHANGE_BLUEPRINT_SLOT = ********,
  SHIPYARD_REPAIR_SHIP = ********,
  SHIPYARD_DISMANTLE_SHIP = ********,
  PUB_DRAFT_SAILOR = ********,
  GOVER_INVEST = ********,
  GOVER_GET_PRE_SESSION_INVEST = ********, // 서버에서는 가드 필요 없을듯.
  GOVER_PREDICT_SHARE_POINTS = ********, // 서버에서는 가드 필요 없을듯.
  PUB_BUY_DRINK = ********,
  PUB_STAFF = ********,
  SHOP_BUY_EQUIPMENT = ********,
  SHOP_BUY_USER_ITEM = ********,
  SHOP_BUY_FROM_BLACK_MARKET = ********,
  PALACE_GET_ROYAL_ORDER = ********,
  PALACE_BUY_TAX_FREE_PERMIT = ********,
  CHANGE_NATION = ********,
  RELIGION_PRAYER = ********,
  RELIGION_DONATION = ********,
  COLLECTOR_CONTRACT = ********,
  COLLECTOR_REPORT = ********,
  UNION_ACCEPT_REQUEST = ********,
  TRADE_BUY = 38300026,
  TRADE_SELL = 38300027,
  PUB_MANAGE_MATE = 38300028, // 주점 밖에서도 언제든지 할 수 있는 일들이여서 가드 필요 없을듯.
  MANTIC_FORTUNE = 38300029,
  COLLECTOR_WORLD_MAP = 38300031,
  MANTIC_SWAP_PIECE_MATE = 38300032,
  MANTIC_SWAP_PIECE_SHIP_BLUEPRINT = 38300033,
  // SHIPYARD_VERIFY_TOW_SHIP = 38300035, // 조선소에서 예인 선박을 선거 슬롯으로 이동시킨다
  PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT = 38300037,
  UNION_ACCEPT_EVENT_REQEUST = 38300038,
  MANTIC_SWAP_PIECE_GACHA_TICKET = 38300041,
  MANTIC_SWAP_PIECE_TOOL = 38300042,

  MANTIC_SWAP_PIECE_TOKEN = 38300047,
}

export enum BUILDING_MENU_CMS_ID {
  PUB_RECRUITING_MATE = 38400001, // 여관-고용
  SHIPYARD = 38400014, // 조선소-건조
  PUB_MY_MATE = 38400002, // 여관-동료
}

// 승급 시간 가속권
enum MATE_AWAKEN_TIME_COST_ITEM {
  A = 23500022, // 5분
  B = 23500023, // 10분
  C = 23500024, // 30분
  D = 23500025, // 1시간
  E = 23500026, // 6시간
  F = 23500027, // 1일
}

// 학습 시간 가속권
enum MATE_LEARN_PASSIVE_TIME_COST_ITEM {
  A = 23500028, // 5분
  B = 23500029, // 10분
  C = 23500030, // 30분
  D = 23500031, // 1시간
  E = 23500032, // 6시간
  F = 23500033, // 1일
}

// 길드 제작 가속권
enum GUILD_CRAFT_TIME_COST_ITEM {
  A = 23500034, //	제작 가속권 5분
  B = 23500035, //	제작 가속권 10분
  C = 23500036, //	제작 가속권 30분
  D = 23500037, //	제작 가속권 1시간
  E = 23500038, //	제작 가속권 6시간
  F = 23500039, //	제작 가속권 1일
}

enum GUILD_SYNTHESIS_TIME_COST_ITEM {
  A = 23500046, //	합성 가속권 10분
  B = 23500047, //	합성 가속권 30분
  C = 23500048, //	합성 가속권 1시간
  D = 23500049, //	합성 가속권 6시간
  E = 23500050, //	합성 가속권 1일
  F = 23500051, //  합성 가속권 5분
}

enum MATE_TRAINING_TIEM_COST_ITEM {
  A = 23700091, // 훈련 가속권 5분
  B = 23700092, // 훈련 가속권 10분
  C = 23700093, // 훈련 가속권 30분
  D = 23700094, // 훈련 가속권 1시간
  E = 23700095, // 훈련 가속권 6시간
  F = 23700096, // 훈련 가속권 1일
}

export function isMateAwakenTimeCostItem(itemCms: ItemDesc): boolean {
  return MATE_AWAKEN_TIME_COST_ITEM[itemCms.id] ? true : false;
}

export function isMateLearnPassiveTimeCostItem(itemCms: ItemDesc): boolean {
  return MATE_LEARN_PASSIVE_TIME_COST_ITEM[itemCms.id] ? true : false;
}

export function isGuildCraftTimeCostItem(itemCms: ItemDesc): boolean {
  return GUILD_CRAFT_TIME_COST_ITEM[itemCms.id] ? true : false;
}

export function isGuildSynthesisTimeCostItem(itemCms: ItemDesc): boolean {
  return GUILD_SYNTHESIS_TIME_COST_ITEM[itemCms.id] ? true : false;
}

export function isMateTrainingTimeCostItem(itemCms: ItemDesc): boolean {
  return MATE_TRAINING_TIEM_COST_ITEM[itemCms.id] ? true : false;
}

export enum JOB_TYPE {
  NONE = 0,
  ADVENTURE = 1,
  TRADE = 2,
  BATTLE = 3,
}

export enum SPECIAL_STAT_JOB_TYPE {
  ALL = 0,
  ADVENTURE = 1,
  TRADE = 2,
  BATTLE = 3,
  ADVENTURE_AND_TRADE = 4,
}

export const ExpPropName = {
  [JOB_TYPE.ADVENTURE]: 'adventureExp',
  [JOB_TYPE.TRADE]: 'tradeExp',
  [JOB_TYPE.BATTLE]: 'battleExp',
};

export const LevelPropName = {
  [JOB_TYPE.ADVENTURE]: 'adventureLevel',
  [JOB_TYPE.TRADE]: 'tradeLevel',
  [JOB_TYPE.BATTLE]: 'battleLevel',
};

export enum INVENTORY_TYPE {
  NONE = 0,
  MATE_EQUIPMENT = 1,
  USER_ITEM = 2,
  CAPTURED = 3,
  SHIP_SLOT_ITEM = 5,
  SHIP_DOCK = 6,
  SHIP_BUILDING = 7,
  GUILD_CRATE = 8,
  PRESET = 9,
  SAIL_WAYPOINT = 10,
  MATE_EQUIPMENT_COSTUME = 11,
  GUILD_SYNTHESIS = 12,
  DISPATCH_PRESET = 13,
}

export enum MAIL_TYPE {
  SYSTEM = 0,
  CASH = 1,
  OPERATION = 2,
  GUILD = 3,
}

export enum QUEST_CATEGORY {
  NONE = 0,
  CONTENTS_BY_LEVEL = 1,
  ROYAL_ORDER = 2,
  REQUEST = 3,
  RANDOM = 4,
  SCENARIO = 5,
  RELATIONSHIP = 6,
  CHALLENGE = 7,
  PUB_STAFF = 8,
  // 이벤트의(한정 탭) 경우 퀘스트 클리어 시 국가 우호도 증가 X, 퀘스트 포기 시 명성 감소 X
  EVENT_REQUEST = 9,
  ADVENTURE = 10,
  NPC = 11,
}

export enum BUILDING_TYPE {
  NONE = 0,
  PALACE = 1,
  GOVER = 2,
  UNION = 3,
  PUB = 4,
  SHOP = 5,
  SPECIAL = 6,
  RELIGION = 7,
  COLLECTOR = 8,
  BANK = 9,
  TRADE = 10,
  SHIPYARD = 11,
  DEPART = 12,
  MANTIC = 13,
  CANAL = 14,
  EVENT_GOVER = 30,
}

export enum RELIGION_BUFF_TYPE {
  NONE = 0,
  PRAYER = 1,
  DONATION = 2,
}

export enum CONTENTS_TERMS_OPERATOR {
  EQUAL = 1,
  GREATER_OR_EQUAL = 2,
  LESS_THAN = 3,
}

export enum POINT_TYPE {
  POINT = 0,
  CASH = 1,
}

export enum SPECIAL_STAT_TARGET_TYPE {
  SHIP = 0,
  FLEET = 1,
}

export enum SHIP_GRADE_TYPE {
  C = 2,
  B = 3,
  A = 4,
  S = 5,
}

/*
  shipSize 변경이 일어나면
  shipBuildExpCms shipSize 와 관련된 함수를 변경해줘야 한다
*/
export enum SHIP_SIZE {
  SMALL = 2,
  MEDIUM = 3,
  LARGE = 4,
  EXTRA_LARGE = 5,
}

export enum STAT_EFFECT_TYPE {
  STAT_OPERATOR = 0,
  WORLD_PASSIVE_EFFECT = 1,
}

// https://docs.google.com/spreadsheets/d/1KQRAhLwxmRNXSVGqQ__4AovzNsZJHlh_U9romm1IQYE/edit#gid=0
// 클라의 CMSEx.lua , ACHIEVEMENT_TERMS 참고
// 클라 전투 내부에서 일부 id 를 사용하고 있음.
export enum ACHIEVEMENT_TERMS {
  MATE_LANGUAGE_LEVEL = 87000000,
  COMPANY_LEVEL = 87000001,

  BUILD_SHIP = 87000002,
  DISMANTLE_SHIP = 87000003,
  GAIN_SHIP = 87000004,
  GAIN_SPECIFIC_BP_LEVEL_SHIP = 87000005,
  TRY_UPDATE_SHIP_BLUEPRINT = 87000006,
  GAIN_SPECIFIC_SHIP = 87000007,
  GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP = 87000008,

  EQUIP_SHIP_SLOT_ITEM = 87000009,
  CHANGE_SHIP_BLUEPRINT_ROOM_SLOT = 87000010,

  VISIT_REGION = 87000011,
  ENTER_TOWN = 87000012,

  INVEST_COUNT = 87000013,
  SPECIFIC_DEVELOPMENT_TYPE_INVEST_COUNT = 87000014,
  SPECIFIC_REGION_INVEST_COUNT = 87000015,
  SPECIFIC_TOWN_INVEST_COUNT = 87000016,
  INVEST_POINT = 87000017,
  SPECIFIC_REGION_INVEST_POINT = 87000018,
  SPECIFIC_TOWN_INVEST_POINT = 87000019,

  INVEST_COUNT_TOWN_OWN_TYPE_3 = 87000130,
  SPECIFIC_DEVELOPMENT_TYPE_INVEST_COUNT_TOWN_OWN_TYPE_3 = 87000131,
  SPECIFIC_REGION_INVEST_COUNT_TOWN_OWN_TYPE_3 = 87000132,
  SPECIFIC_TOWN_INVEST_COUNT_TOWN_OWN_TYPE_3 = 87000133,
  INVEST_POINT_TOWN_OWN_TYPE_3 = 87000134,
  SPECIFIC_REGION_INVEST_POINT_TOWN_OWN_TYPE_3 = 87000135,
  SPECIFIC_TOWN_INVEST_POINT_TOWN_OWN_TYPE_3 = 87000136,

  GIFT_TO_MATE = 87000020,

  GAIN_ADMIRAL = 87000021,
  GAIN_SPECIFIC_ADMIRAL = 87000022,
  GAIN_MATE = 87000023,
  GAIN_SPECIFIC_NATION_MATE = 87000024,
  GAIN_SPECIFIC_MATE = 87000025,
  SPECIFIC_MATE_ADVENTURE_LEVEL = 87000026,
  SPECIFIC_MATE_TRADE_LEVEL = 87000027,
  SPECIFIC_MATE_BATTLE_LEVEL = 87000028,
  SPECIFIC_MATE_TOTAL_LEVEL = 87000029,
  ROYAL_TITLE = 87000030,
  ROYAL_TITLE_SPECIFIC_ADMIRAL = 87000031,

  SUCCESS_RECRUIT_MATE = 87000032,
  FAIL_RECRUIT_MATE = 87000033,
  BUY_DRINK = 87000034,
  DRAFT_SAILOR = 87000035,
  FIRE_SAILOR = 87000036,

  TRADE_BUY_GOODS_COUNT = 87000037,
  TRADE_SELL_GOODS_COUNT = 87000038,
  TRADE_BUY_SELL_SPECIFIC_TRADE_GOODS = 87000039,
  TRADE_BUY_SELL_FAMOUSE_TRADE_GOODS = 87000040,
  TRADE_BUY_SELL_SPECIFIC_FAMOUSE_TRADE_GOODS = 87000041,
  TRADE_PROFIT = 87000042,
  TRADE_SPECIFIC_PROFIT = 87000043,

  BUY_SHOP_ITEM = 87000044,
  SELL_SHOP_ITEM = 87000045,
  BUY_BLACK_MARKET_ITEM = 87000046,

  DEPART = 87000047,
  BUY_FOOD = 87000048,
  BUY_WATER = 87000049,
  BUY_AMMO = 87000050,
  BUY_LUMBER = 87000051,
  SHIP_REPAIRING_COST = 87000052,

  SAILING_DAYS = 87000053,
  DISASTER = 87000054,
  SPECIFIC_DISASTER = 87000055,

  DISCOVER_OCEAN_DOODAD = 87000056,
  DISCOVER_SPECIFIC_CATEGORY = 87000057,
  DISCOVER = 87000058,

  BATTLE_VICTORY = 87000059,
  BATTLE_DEFEAT = 87000060,
  BATTLE_ACTION = 87000061,
  BATTLE_SINK_ENEMY_SHIP = 87000062,
  CAPTURE_SHIP = 87000063,
  BATTLE_GIVE_UP = 87000064,

  USE_ENERGY = 87000065,

  BATTLE_VICTORY_FROM_SPECIFIC_NPC = 87000066,
  BATTLE_DEFEAT_FROM_SPECIFIC_NPC = 87000067,

  COMPLETE_QUEST_NODE = 87000068,

  ENCOUNT_SURRENDER_SUCCESS = 87000069,
  ENCOUNT_NEGOTIATE_SUCCESS = 87000070,
  ENCOUNT_ESCAPE_SUCCESS = 87000071,

  COMPLETE_QUEST = 87000072,
  DISCOVER_SPECIFIC = 87000073,

  BATTLE_MISSION_ALL_CLEAR = 87000075,

  PUB_STAFF_INTIMACY_LEVEL_5 = 87000077,
  FISHING_SUCCESS = 87000078,
  CUSTOMIZE_MATE_EQUIPMENT = 87000079,
  BATTLE_ESCAPE = 87000080,
  BATTLE_VICTORY_IN_DUEL = 87000081,
  COMPLETE_QUEST_SPECIFIC_LEADER_MATE_SPECIFIC_JOB_TYPE = 87000082,
  BATTLE_ONE_ATTACK = 87000083,
  EQUIP_MATE_EQUIPMENT_SPECIFIC_GRADE_SPECIFIC_TYPE = 87000084,
  COLLECTOR_REPORT_DISCOVERIES = 87000085,
  OCEAN_PROTECTION = 87000086,
  BATTLE_GREATE_VICTORY_IN_DUEL = 87000087,
  GAIN_FAME = 87000088,
  TRADE_NEGO_WITHOUT_FAILURE = 87000089,
  OPEN_CASH_SHOP_GACHA_BOX = 87000090,
  PUB_STAFF_TALKING = 87000091,
  OVERWRITE_SHIP_ENCHANT = 87000092,
  DEFEAT_ELITE_FLEET = 87000093,
  BATTLE_USE_ORDER_SKILL = 87000094,

  TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS = 87000098,
  TRADE_SELL_PROFIT_TRADE_EVENT = 87000099,
  TRADE_SELL_CONSTANT_PRICE_PERCENT_CONSTANT_COUNT = 87000100,
  TRADE_BUY_SELL_SPECIFIC_CATEGORY = 87000101,

  DISCOVERY_SCORE = 87000103,
  LAND_EXPLORE = 87000104,
  UNLOCK_SHIP_BLUEPRINT = ********,
  ACTIVATE_OCEAN_DOODAD_BY_TOUCH = ********,
  COMPLETE_SPECIFIC_CYCLE_TASK_CATEGORY = ********,
  BATTLE_VICTORY_FROM_USER = ********,
  BATTLE_VICTORY_FROM_USER_SPECIFIC_NATION = ********,
  KARMA_VALUE = ********,

  BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION_SPECIFIC_JOB_TYPE = ********,
  BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION = ********,
  BATTLE_VICTORY_FROM_NPC_SPECIFIC_JOB_TYPE = ********,
  SPECIFIC_NATION_TOWN_INVEST_POINT = ********,
  BUILD_SHIP_SPECIFIC_TIER = ********,
  DEPOSIT_BANK_INSTALLMENT_SAVINGS = ********,
  RECEIVE_SPECIFIC_SHIP_FROM_SHIPYARD = ********,
  SAILING_DAYS_FOR_QUEST = ********,

  BATTLE_DUEL_COUNT = ********,
  TRADE_POINT = ********,

  BOSS_RAID_PARTICIPATE = ********,
  BOSS_RAID_DAMAGE = ********,
  BOSS_RAID_LAST_HIT = ********,
  BOSS_RAID_FIRST_HIT = ********,

  FISHING_BIG_CATCH = ********,
  FISHING_MAX_SIZE = ********,
  SPECIFIC_LAND_EXPLORE = ********,
  BOSS_RAID_PARTICIPATE_ANY = ********,
  BOSS_RAID_DAMAGE_ANY = ********,

  TRADE_BUY_SPECIFIC_GOODS_COUNT = ********,
  TRADE_SELL_SPECIFIC_GOODS_COUNT = ********,
  TRADE_BUY_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_TOWN = ********,
  TRADE_SELL_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_TOWN = ********,
  TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS_IN_SPECIFIC_TOWN = ********,
  TRADE_SELL_PROFIT_SPECIFIC_TRADE_GOODS_CATEGORY = ********,
  ACCUMULATED_ITEM_COUNT = ********,

  EXCHANGE_COUNT = ********,
  EXCHANGE_COUNT_IN_SPECIFIC_VILLAGE = ********,
  EXCHANGE_SPECIFIC_GOODS_COUNT = ********,
  EXCHANGE_SPECIFIC_GOODS_COUNT_IN_SPECIFIC_VILLAGE = 87000157,

  CASH_SHOP_BUY_COUNT = 87000158,

  SALVAGE_COUNT = 87000160,
  SALVAGE_SCORE = 87000161,
  SALVAGE_ACCUM_SCORE = 87000162,
  SALVAGE_ACCUM_DECREASE_SCORE = 87000163,
  SALVAGE_SPECIAL_OBJECT_COUNT = 87000164,
}

export enum TASK_CATEGORY {
  NONE = 0,
  DAILY = 1,
  WEEKLY = 2,
  MONTHLY = 3,
  MAX = 3,
}

export const TRADE_DISCOUNT_PASSIVE_EFFECT = {
  [TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_DISCOUNT_FOOD,
  [TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_DISCOUNT_CONDIMENT,
  [TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_DISCOUNT_LIVESTOCK,
  [TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_DISCOUNT_MEDICINE,
  [TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_DISCOUNT_GENERAL,
  [TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_DISCOUNT_LIQUOR,
  [TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_DISCOUNT_DYE,
  [TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_DISCOUNT_ORE,
  [TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_DISCOUNT_INDUSTRIAL,
  [TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_DISCOUNT_LUXURY,
  [TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_DISCOUNT_TEXTILE,
  [TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_DISCOUNT_FABRIC,
  [TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_DISCOUNT_CRAFT,
  [TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_DISCOUNT_ART,
  [TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_DISCOUNT_SPICE,
  [TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_DISCOUNT_JEWELRY,
  [TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_DISCOUNT_AROMA,
  [TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_DISCOUNT_GEM,
  [TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_DISCOUNT_WEAPON,
  [TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_DISCOUNT_FIREARM,
};

export const TRADE_EXTRA_CHARGE_PASSIVE_EFFECT = {
  [TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_FOOD,
  [TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_CONDIMENT,
  [TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_LIVESTOCK,
  [TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_MEDICINE,
  [TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_GENERAL,
  [TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_LIQUOR,
  [TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_DYE,
  [TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_ORE,
  [TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_INDUSTRIAL,
  [TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_LUXURY,
  [TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_TEXTILE,
  [TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_FABRIC,
  [TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_CRAFT,
  [TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_ART,
  [TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_SPICE,
  [TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_JEWELRY,
  [TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_AROMA,
  [TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_GEM,
  [TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_WEAPON,
  [TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_EXTRA_CHARGE_FIREARM,
};

export const INVEST_DEVELOPMENT_PASSIVE_EFFECT = {
  [DEVELOPMENT_TYPE.industry]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_INDUSTRY,
  [DEVELOPMENT_TYPE.commerce]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_COMMERCE,
  [DEVELOPMENT_TYPE.armory]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_ARMORY,
};
export const INVEST_DEVELOPMENT_PCT_PASSIVE_EFFECT = {
  [DEVELOPMENT_TYPE.industry]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_INDUSTRY_PCT,
  [DEVELOPMENT_TYPE.commerce]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_COMMERCE_PCT,
  [DEVELOPMENT_TYPE.armory]: PASSIVE_EFFECT.TRADE_DEVELOPMENT_ARMORY_PCT,
};

export const TRADE_ADDED_BUYABLE_PASSIVE_EFFECT = {
  [TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FOOD,
  [TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CONDIMENT,
  [TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIVESTOCK,
  [TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_MEDICINE,
  [TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GENERAL,
  [TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIQUOR,
  [TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_DYE,
  [TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ORE,
  [TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_INDUSTRIAL,
  [TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LUXURY,
  [TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_TEXTILE,
  [TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FABRIC,
  [TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CRAFT,
  [TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ART,
  [TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_SPICE,
  [TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_JEWELRY,
  [TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_AROMA,
  [TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GEM,
  [TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_WEAPON,
  [TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FIREARM,
};

export const TRADE_ADDED_BUYABLE_PCT_PASSIVE_EFFECT = {
  [TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FOOD_PCT,
  [TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CONDIMENT_PCT,
  [TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIVESTOCK_PCT,
  [TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_MEDICINE_PCT,
  [TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GENERAL_PCT,
  [TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LIQUOR_PCT,
  [TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_DYE_PCT,
  [TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ORE_PCT,
  [TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_INDUSTRIAL_PCT,
  [TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_LUXURY_PCT,
  [TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_TEXTILE_PCT,
  [TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FABRIC_PCT,
  [TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_CRAFT_PCT,
  [TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_ART_PCT,
  [TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_SPICE_PCT,
  [TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_JEWELRY_PCT,
  [TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_AROMA_PCT,
  [TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_GEM_PCT,
  [TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_WEAPON_PCT,
  [TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_ADDED_BUYABLE_FIREARM_PCT,
};

export const TRADE_PREDICT_PRICE_PASSIVE_EFFECT = {
  [TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_FOOD,
  [TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_CONDIMENT,
  [TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_LIVESTOCK,
  [TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_MEDICINE,
  [TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_GENERAL,
  [TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_LIQUOR,
  [TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_DYE,
  [TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_ORE,
  [TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_INDUSTRIAL,
  [TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_LUXURY,
  [TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_TEXTILE,
  [TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_FABRIC,
  [TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_CRAFT,
  [TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_ART,
  [TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_SPICE,
  [TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_JEWELRY,
  [TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_AROMA,
  [TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_GEM,
  [TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_WEAPON,
  [TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.TRADE_PREDICT_PRICE_FIREARM,
};

export const EXCHANGE_PRICE_PCT_PASSIVE_EFFECT = {
  [TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.EXCHANGE_PRICE_FOOD_PCT,
  [TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.EXCHANGE_PRICE_CONDIMENT_PCT,
  [TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.EXCHANGE_PRICE_LIVESTOCK_PCT,
  [TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.EXCHANGE_PRICE_MEDICINE_PCT,
  [TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.EXCHANGE_PRICE_GENERAL_PCT,
  [TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.EXCHANGE_PRICE_LIQUOR_PCT,
  [TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.EXCHANGE_PRICE_DYE_PCT,
  [TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.EXCHANGE_PRICE_ORE_PCT,
  [TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.EXCHANGE_PRICE_INDUSTRIAL_PCT,
  [TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.EXCHANGE_PRICE_LUXURY_PCT,
  [TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.EXCHANGE_PRICE_TEXTILE_PCT,
  [TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.EXCHANGE_PRICE_FABRIC_PCT,
  [TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.EXCHANGE_PRICE_CRAFT_PCT,
  [TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.EXCHANGE_PRICE_ART_PCT,
  [TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.EXCHANGE_PRICE_SPICE_PCT,
  [TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.EXCHANGE_PRICE_JEWELRY_PCT,
  [TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.EXCHANGE_PRICE_AROMA_PCT,
  [TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.EXCHANGE_PRICE_GEM_PCT,
  [TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.EXCHANGE_PRICE_WEAPON_PCT,
  [TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.EXCHANGE_PRICE_FIREARM_PCT,
};

export const EXCHANGE_GET_GOODS_PCT_PASSIVE_EFFECT = {
  [TRADE_GOODS_CATEGORY.FOOD]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_FOOD_PCT,
  [TRADE_GOODS_CATEGORY.CONDIMENT]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_CONDIMENT_PCT,
  [TRADE_GOODS_CATEGORY.LIVESTOCK]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_LIVESTOCK_PCT,
  [TRADE_GOODS_CATEGORY.MEDICINE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_MEDICINE_PCT,
  [TRADE_GOODS_CATEGORY.GENERAL]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_GENERAL_PCT,
  [TRADE_GOODS_CATEGORY.LIQUOR]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_LIQUOR_PCT,
  [TRADE_GOODS_CATEGORY.DYE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_DYE_PCT,
  [TRADE_GOODS_CATEGORY.ORE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_ORE_PCT,
  [TRADE_GOODS_CATEGORY.INDUSTRIAL]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_INDUSTRIAL_PCT,
  [TRADE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_LUXURY_PCT,
  [TRADE_GOODS_CATEGORY.TEXTILE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_TEXTILE_PCT,
  [TRADE_GOODS_CATEGORY.FABRIC]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_FABRIC_PCT,
  [TRADE_GOODS_CATEGORY.CRAFT]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_CRAFT_PCT,
  [TRADE_GOODS_CATEGORY.ART]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_ART_PCT,
  [TRADE_GOODS_CATEGORY.SPICE]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_SPICE_PCT,
  [TRADE_GOODS_CATEGORY.JEWELRY]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_JEWELRY_PCT,
  [TRADE_GOODS_CATEGORY.AROMA]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_AROMA_PCT,
  [TRADE_GOODS_CATEGORY.GEM]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_GEM_PCT,
  [TRADE_GOODS_CATEGORY.WEAPON]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_WEAPON_PCT,
  [TRADE_GOODS_CATEGORY.FIREARM]: PASSIVE_EFFECT.EXCHANGE_GET_GOODS_FIREARM_PCT,
};

export const SMUGGLE_ADDED_BUYABLE_PCT_PASSIVE_EFFECT = {
  [SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_ILLEGAL_PCT,
  [SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS_PCT,
  [SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_GREED_PCT,
  [SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_LUXURY_PCT,
};

export const SMUGGLE_ADDED_BUYABLE_PASSIVE_EFFECT = {
  [SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_ILLEGAL,
  [SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_FOOLHARDINESS,
  [SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_GREED,
  [SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_ADDED_BUYABLE_LUXURY,
};

export const SMUGGLE_BUY_PRICE_PCT_PASSIVE_EFFECT = {
  [SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_ILLEGAL_PCT,
  [SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_FOOLHARDINESS_PCT,
  [SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_GREED_PCT,
  [SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_LUXURY_PCT,
};

export const SMUGGLE_BUY_PRICE_PASSIVE_EFFECT = {
  [SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_ILLEGAL,
  [SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_FOOLHARDINESS,
  [SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_GREED,
  [SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_BUY_PRICE_LUXURY,
};

export const SMUGGLE_SALE_PRICE_PCT_PASSIVE_EFFECT = {
  [SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_ILLEGAL_PCT,
  [SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_FOOLHARDINESS_PCT,
  [SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_GREED_PCT,
  [SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_LUXURY_PCT,
};

export const SMUGGLE_SALE_PRICE_PASSIVE_EFFECT = {
  [SMUGGLE_GOODS_CATEGORY.ILLEGAL]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_ILLEGAL,
  [SMUGGLE_GOODS_CATEGORY.FOOLHARDINESS]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_FOOLHARDINESS,
  [SMUGGLE_GOODS_CATEGORY.GREED]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_GREED,
  [SMUGGLE_GOODS_CATEGORY.LUXURY]: PASSIVE_EFFECT.SMUGGLE_SALE_PRICE_LUXURY,
};

// https://wiki.line.games/display/MOTIF/missionTargetType
// https://wiki.line.games/pages/viewpage.action?pageId=38240414
export enum MISSION_TARGET_TYPE {
  GO_TO_TOWN_BUILDING = 1,
  GO_TO_OCEAN = 2,
  GO_TO_OCEAN_NPC = 3,
  GO_TO_NATION_OCEAN_NPC = 4,
  DELIVERY_ITEM_TO_TOWN_BUILDING = 5,
  DELIVERY_CEQUIP_TO_TOWN_BUILDING = 6,
  DELIVERY_TRADEGOODS_TO_TOWN_BUILDING = 7,
  DELIVERY_ITEM_TO_REGION_OCEAN_NPC = 8,
  DELIVERY_CEQUIP_TO_REGION_OCEAN_NPC = 9,
  DELIVERY_TRADEGOODS_TO_REGION_OCEAN_NPC = 10,
  DELIVERY_POINT_TO_TOWN_BUILDING = 11,

  GO_TO_NEAREST_TOWN_ITEM = 14,
  GO_TO_NEAREST_TOWN_CEQUIP = 15,
  GO_TO_NEAREST_TOWN_TRADEGOODS = 16,
  GO_TO_NEAREST_TOWN_BUILDING = 17,
  GO_TO_NEAREST_TOWN_EXCEPT = 18,
  GO_TO_NEAREST_TOWN_BUILDING_EXCEPT = 19,
  GO_TO_NEAREST_REGION_TOWN_BUILDING = 20,
  GO_TO_NEAREST_CULTURAL_AREA_TOWN_BUILDING = 21,
  GO_TO_NEAREST_TOWN_MATE = 22,
  GO_TO_DISCOVERY = 23,

  RANDOM_MIN = 101,
  RANDOM_TOWN = 101,
  RANDOM_TOWN_RANDOM_TRADE_GOODS = 102,
  RANDOM_REGION = 103,
  RANDOM_REGION_OCEAN_NPC = 104,
  RANDOM_REGION_LOW_INTIMACY_NATION_OCEAN_NPC = 105,
  RANDOM_REGION_LAND_EXPLORE = 106,
  RANDOM_TOWN_RANDOM_SHOP_ITEM = 107,
  RANDOM_REGION_RANDOM_OCEAN_DOODAD = 108,
  RANDOM_TOWN_RANDOM_SHOP_MATE_EQUIP = 109,
  RANDOM_TOWN_RANDOM_SHOP_MATE_EQUIP_ONLY_EQUIPABLE = 110,
  RANDOM_TOWN_INVESTABLE_BY_TOWN_OWN_TYPE = 111,
}

export function isRandomMissionTargetType(type: MISSION_TARGET_TYPE): boolean {
  return type >= MISSION_TARGET_TYPE.RANDOM_MIN;
}

/**
 * https://wiki.line.games/display/MOTIF/QuestTerms
 */
export enum QUEST_TERMS {
  // [~86100000] CMS.QuestTerms.type = '현재 상태'
  MOVE = 86000001,
  DELIVERY = 86000002,

  REGISTER_EQUAL = 86000014, // 두 레지스터가 같은지
  FLAG = 86000015, // 플래그
  LEADER_MATE_ROYAL_TITLE = 86000016, // 해당 연대기의 제독 작위
  LEADER_MATE_FAME = 86000017, // 해당 연대기의 제독 명성

  REGISTER = 86000020, // 해당 레지스터 값
  MATE = 86000021, // 항해사 보유
  DUCAT = 86000022, // 두캇 보유
  SAILED_DAYS = 86000024, // 항해 일수(현재)
  MATE_LANGUAGE_LEVEL = 86000025, // 항해사 언어레벨
  STAT_OPERATOR = 86000026, // 1함대의 특정 StatOperator 의 평균이 특정 값 이상인지
  COMPANY_LEVEL = 86000027, // 선단 LV
  LEADER_MATE_LEVEL = 86000028, // 현제독 제독 LV
  COMBAT_POWER = 86000029, // 1함대 전투력
  SPECIFIC_TIER_MAX_SHIP_BLUEPRINT_LEVEL = 86000030, // 특정 티어 최고 설계도 LV
  SPECIFIC_SHIP_BLUEPRINT_LEVEL = 86000031, // 특정 설계도 레벨
  EQUIPPED_MATE_EQUIPMENT_COUNT_OVER_SPECIFIC_GRADE = 86000032, // 특정 등급 이상의 장착된 항해사 장비 개수
  EQUIPPED_SHIP_SLOT_ITEM_COUNT_OVER_SPECIFIC_GRADE = 86000033, // 특정 등급 이상의 장착된 선박 부품 개수
  SHIP_COUNT_OVER_SPECIFIC_TIER_WITH_JOB_TYPE = 86000034, // 1함대에 배치된 특정 티어 이상의 특정 전투 타입인 선박 수
  MATE_COUNT_OVER_SPECIFIC_GRADE_WITH_JOB_TYPE = 86000035, // 1함대에 배치된 특정 등급 이상의 특정 직업 계열인 항해사 수
  DISCOVERY_COUNT_OVER_SPECIFIC_GRADE = 86000036, // 특정 등급 이상의 발견물을 발견한 수
  SPECIFIC_DISCOVERY = 86000037, // 특정 발견물 발견 여부
  SPECIFIC_TOWN_COLLECTOR_CONTRACT = 86000038, // 특정 항구 수집가 계약 여부
  COLLECTOR_REPORTED_WORLD_MAP_RATE = 86000039, // 저택 지도 제작률
  SPECIFIC_TRADE_GOODS_COUNT_OVER = 86000040, // 1함대의 특정 교역품 보유
  LEADER_MATE_SPECIFIC_CEQUIP_EQUIPPED = 86000041,
  QUEST_NODE_CURRENT_INDEX = 86000042, // 현재 퀘스트 노드 인덱스
  QUEST_UNIQUE_GROUP_COMPLETE = 86000043, // 완료 퀘스트 unique Group 비교

  // [86100000~] CMS.QuestTerms.type = '누적', CMS.QuestTerms.achievementTermsId 참고
  BATTLE_VICTORY_FROM_SPECIFIC_NPC = 86100066,
}

export enum COUNTRY_CODE_MASK {
  KOREA = 1 << COUNTRY_CODE.KOREA,
  KOREA_NON_PK = 1 << COUNTRY_CODE.KOREA_NON_PK,
  GLOBAL = 1 << COUNTRY_CODE.GLOBAL,
  GLOBAL_NON_PK = 1 << COUNTRY_CODE.GLOBAL_NON_PK,
  CHINA = 1 << COUNTRY_CODE.CHINA,
  CHINA_NON_PK = 1 << COUNTRY_CODE.CHINA_NON_PK,
}

export enum KICK_SITUATION_TYPE {
  ENTER_TOWN = 1,
  ENTER_BUILDING = 2,
}

export enum KICK_RESULT_TYPE {
  NEGO = 1,
  KICK = 2,
}

export enum OCCUPY_TYPE {
  MIN = 0,
  OCCUPY = 0, // 점령국
  UNOCCUPIED = 1, // 비점령국
  RANDOM = 2, // 동양,서양에 중 랜덤
  PIRATE = 3, // 해적
  MAX = 4,
}

export const firstAchievementPointCmsId = 2000;
export const firstWeatherTileCmsId = 13300000;
export const firstCulturalAreaCmsId = 12200000;

export enum NATION_RANKING_EFFECT_CMS_ID {
  SELECT_NATION = 10300000, // 국가 최초 선택 시 레드젬 지급
  EXP = 10300001, // 획득 경험치 증가
  FAME = 10300002, // 획득 명성 증가
  TRADE_TAX = 10300003, // 관세 추가
  REWARD_DUCAT_CARGO = 10300004, // 보상 중 두캇, 교역품, 보급풍에 대한 수량 증가
  DISCOUNT_SUPPLY_WHERE_MY_NATION_TOWN = 10300005, // 자국 항구에서 보급 비용 할인
}

export enum GENDER {
  MAN = 0,
  WOMAN = 1,
  COMMON = 2,
}

export enum ZoneType {
  INVALID = 0,
  TOWN = 1,
  OCEAN = 2,
  MAX_ZONE_TYPE = 3,
}

export enum QUEST_SPAWN_OCEAN_DOODAD_TYPE {
  CURRENT_LOCATION_RADIUS = 0,
  DESIGNATED_LOCATION_RADIUS = 1,
}

export enum DISCOVERY_GRADE {
  D = 1,
  C = 2,
  B = 3,
  A = 4,
  S = 5,
}

export enum PUB_GIFT_GRADE {
  C = 1,
  B = 2,
  A = 3,
  S = 4,
}

export const SailConsumeSupplies = [SUPPLY_CMS_ID.WATER, SUPPLY_CMS_ID.FOOD];

export const NoInsuranceCmsId = 40200001;

export enum NATION_PROMISE_CONDITION_CMS_ID {
  NONE = 0,
  SPECIFIC_REGION_INVEST_POINT = 10160001,
  SPECIFIC_TOWN_INVEST_POINT = 10160002,
  SPECIFIC_TOWN_OCCPIED_CURRENTLY = 10160003,
  REGION_PARTIALLY_OCCUPIED = 10160004,
  REGION_COMPLETELY_OCCUPIED = 10160005,
  NATION_RANK_ACQUIRED = 10160006,
  NATION_BUDGET_ACQUIRED = 10160007,
}

// ------------------------------------------------------------------------------------------------
export function GetShipCargoType(cmsId) {
  if (cms.DepartSupply[cmsId]) {
    return SHIP_CARGO_TYPE.SUPPLY;
  } else {
    const tradeGoodsCms = cms.TradeGoods[cmsId];
    // if (tradeGoodsCms && isFilteredByCountryCode(tradeGoodsCms)) {
    //   // TradeGoods가 필터링되면 NONE 반환
    // } else
    if (tradeGoodsCms || cms.SmuggleGoods[cmsId]) {
      return SHIP_CARGO_TYPE.TRADE_GOODS;
    }
  }

  return SHIP_CARGO_TYPE.NONE;
}

// ------------------------------------------------------------------------------------------------
export function IsBlackMarketShopType(shopType: SHOP_TYPE) {
  return (
    shopType === SHOP_TYPE.BLACK_MARKET_USER_ITEM ||
    shopType === SHOP_TYPE.BLACK_MARKET_MATE_EQUIPMENT
  );
}

// ------------------------------------------------------------------------------------------------
// 여관-고용 에서 사용되는 항해사 후보군 cms cache
// ------------------------------------------------------------------------------------------------
const mateRecrutingGroupsForPubRecruit: { [group: number]: MateRecruitingGroupDesc[] } = {};
export function GetMateRecrutingGroupsForPubRecruiting(group: number): MateRecruitingGroupDesc[] {
  if (!mateRecrutingGroupsForPubRecruit[group]) {
    const grpCmses = _GetMateRecruitingGroupsByGroupIdCache()[group];
    if (grpCmses) {
      // 여관-동료에서만 사용되는 것 제외
      const reRecruitFiltered = grpCmses.filter((elem) => !elem.isReRecruit);
      mateRecrutingGroupsForPubRecruit[group] = reRecruitFiltered;
    } else {
      mateRecrutingGroupsForPubRecruit[group] = [];
    }
  }

  return mateRecrutingGroupsForPubRecruit[group];
}

// ------------------------------------------------------------------------------------------------
// 여관-동료 에서 사용되는 항해사 후보군 cms cache
// ------------------------------------------------------------------------------------------------
const mateRecrutingGroupsForPubReRecruit: { [group: number]: MateRecruitingGroupDesc[] } = {};
export function GetMateRecrutingGroupsForPubReRecruiting(group: number): MateRecruitingGroupDesc[] {
  if (!mateRecrutingGroupsForPubReRecruit[group]) {
    const grpCmses = _GetMateRecruitingGroupsByGroupIdCache()[group];
    if (grpCmses) {
      mateRecrutingGroupsForPubReRecruit[group] = grpCmses;
    } else {
      mateRecrutingGroupsForPubReRecruit[group] = [];
    }
  }
  return mateRecrutingGroupsForPubReRecruit[group];
}

export function isFilteredByCountryCode(localBitFlag: number): boolean {
  return (localBitFlag & (1 << mconf.countryCode)) === 0;
}

// ------------------------------------------------------------------------------------------------
// 국가 코드가 맞지 않는 것을 걸러내고, 단순히 그룹별로 나누기만 합니다.
// ------------------------------------------------------------------------------------------------
let mateRecruitingGroupByGroupIdCache: { [groupId: number]: MateRecruitingGroupDesc[] };
function _GetMateRecruitingGroupsByGroupIdCache() {
  if (!mateRecruitingGroupByGroupIdCache) {
    const grouped: { [groupId: number]: MateRecruitingGroupDesc[] } = {};
    _.forOwn(cms.MateRecruitingGroup, (elem) => {
      const mateCms = cms.Mate[elem.mateId];
      if (isFilteredByCountryCode(mateCms.localBitFlag)) {
        return;
      }
      assert(elem.group !== undefined);
      assert(elem.Ratio !== undefined);
      if (grouped[elem.group]) {
        grouped[elem.group].push(elem);
      } else {
        grouped[elem.group] = [elem];
      }
    });
    mateRecruitingGroupByGroupIdCache = grouped;
  }
  return mateRecruitingGroupByGroupIdCache;
}

// ------------------------------------------------------------------------------------------------
// https://motif-hq.slack.com/archives/C74627QGJ/p1602581665055100
let mateRecruitingGroupByGroupAndMateCmsIdCache: {
  [group: number]: { [mateCmsId: number]: MateRecruitingGroupDesc };
};
export function getMateRecruitingGroupByGroupAndMateCmsId(
  group: number,
  mateCmsId: number
): MateRecruitingGroupDesc {
  if (!mateRecruitingGroupByGroupAndMateCmsIdCache) {
    mateRecruitingGroupByGroupAndMateCmsIdCache = {};
    _.forOwn(cms.MateRecruitingGroup, (elem) => {
      _.merge(mateRecruitingGroupByGroupAndMateCmsIdCache, {
        [elem.group]: {
          [elem.mateId]: elem,
        },
      });
    });
  }

  return mateRecruitingGroupByGroupAndMateCmsIdCache[group]
    ? mateRecruitingGroupByGroupAndMateCmsIdCache[group][mateCmsId]
    : undefined;
}

// ------------------------------------------------------------------------------------------------
let mateReRecruitingCmsByMateGradeCache: { [mateGrade: number]: MateReRecruitingDesc };
export function getMateReRecruitingForMateGrade(mateGrade: MATE_GRADE): MateReRecruitingDesc {
  if (!mateReRecruitingCmsByMateGradeCache) {
    const dict: typeof mateReRecruitingCmsByMateGradeCache = {};
    _.forOwn(cms.MateReRecruiting, (mateReRecruitingCms) => {
      assert(!dict[mateReRecruitingCms.mateGrade]);
      dict[mateReRecruitingCms.mateGrade] = mateReRecruitingCms;
    });
    mateReRecruitingCmsByMateGradeCache = dict;
  }
  return mateReRecruitingCmsByMateGradeCache[mateGrade];
}

// ------------------------------------------------------------------------------------------------
export function IntimacyModifyValue(
  nationDiplomacyCmsId: NATION_DIPLOMACY_CMS_ID,
  count: number,
  base: number
): number {
  const nationDiplomacyCms = cms.NationDiplomacy[nationDiplomacyCmsId];
  if (nationDiplomacyCms.worldIntimacyMultiplier !== undefined) {
    return (
      Math.floor(
        (cms.Const.IntimacyPoint.value * nationDiplomacyCms.worldIntimacyMultiplier) / 1000
      ) * count
    );
  }

  return nationDiplomacyCms.relatedNationIntimacy * base * count;
}

// ------------------------------------------------------------------------------------------------
// Trade's TradeGoods cms cache.
const sellingTradeGoods: {
  [townCmsId: number]: { [date: string]: { [tradeGoodsCmsId: number]: TradeDesc } };
} = {};

function getDateKey(curTimeUtc: number): string {
  const utcDate = new Date(curTimeUtc * 1000);
  const ret = `${mutil.getLocalFullYear(utcDate)}/${mutil.getLocalMonth(
    utcDate
  )}/${mutil.getLocalDate(utcDate)}`;
  return ret;
}

function SetSellingTradeGoods(townCmsId: number, curTimeUtc: number) {
  const dateKey = getDateKey(curTimeUtc);
  if (sellingTradeGoods[townCmsId] && sellingTradeGoods[townCmsId][dateKey]) {
    return;
  }

  sellingTradeGoods[townCmsId] = {
    [dateKey]: {},
  };
  for (const key of Object.keys(cms.Trade)) {
    const elem = cms.Trade[key];
    const goodsCms = cms.TradeGoods[elem.tradeGoodsId];
    // if (isFilteredByCountryCode(goodsCms)) {
    //   continue;
    // }

    // 순차 출현과 라이브 이벤트가 모두 입력되어 있다면, 라이브 이벤트를 우선으로 처리한다.(태그 및 출현 일시 모두)
    if (elem.townId === townCmsId) {
      if (elem.liveEvent) {
        if (!isLiveEvent(elem.liveEvent, curTimeUtc)) {
          continue;
        }
      } else if (elem.rotationTradeGroupId) {
        if (!_isPickRotation(elem.rotationTradeGroupId, elem.rotationTradeGroupOrder, curTimeUtc)) {
          continue;
        }
      }
      sellingTradeGoods[townCmsId][dateKey][elem.tradeGoodsId] = elem;
    }
  }
}

/*
  기획데이터 기준 groupId 존재하면서 order가 없으면 실패되는 상황은 정상
  order에 중간 값이 없다면 건너뛰지 않고 계산한다.
  ex) 만약 order가 1, 3만 있고 1주 간격 등장이면 1등장, 2등장, 3등장 순서대로 계산하므로 2가 등장해야 하는 기간에는 등장하지 않는다.
*/
function _isPickRotation(
  rotationTradeGroupId: number,
  rotationTradeGroupOrder: number,
  curTimeUtc: number
): boolean {
  if (!rotationTradeGroupOrder) {
    return false;
  }

  const rotationTradeGroupCms = cms.RotationTradeGroup[rotationTradeGroupId];
  const startDateUtc = mutil.newDateByCmsDateStr(rotationTradeGroupCms.startDate).getTime() / 1000;
  if (curTimeUtc < startDateUtc) {
    return false;
  }

  const maxOrder = _getRotationMaxTradeOrder(rotationTradeGroupId);
  const secondsPerDay = rotationTradeGroupCms.resetDays * formula.SECONDS_PER_DAY;
  const repeatedCount = Math.floor((curTimeUtc - startDateUtc) / secondsPerDay);
  const pickRotationId = (repeatedCount % maxOrder) + 1;
  return rotationTradeGroupOrder === pickRotationId;
}

export function IsSellInTown(
  townCmsId: number,
  tradeGoodsCmsId: number,
  curTimeUtc: number
): boolean {
  SetSellingTradeGoods(townCmsId, curTimeUtc);
  const dateKey = getDateKey(curTimeUtc);
  return sellingTradeGoods[townCmsId][dateKey][tradeGoodsCmsId] ? true : false;
}

// 도시에서 판매하는 tradeGoods cms id 반환
export function getTownSellingTradeGoodsCmsIds(townCmsId: number, curTimeUtc: number): number[] {
  SetSellingTradeGoods(townCmsId, curTimeUtc);
  const dateKey = getDateKey(curTimeUtc);
  return _.values(sellingTradeGoods[townCmsId][dateKey]).map((elem) => elem.tradeGoodsId);
}

export function getTownTradeGoodsCms(
  townCmsId: number,
  tradeGoodsCmsId: number,
  curTimeUtc: number
): TradeDesc {
  SetSellingTradeGoods(townCmsId, curTimeUtc);
  const dateKey = getDateKey(curTimeUtc);
  return sellingTradeGoods[townCmsId][dateKey][tradeGoodsCmsId];
}

// ------------------------------------------------------------------------------------------------
// SailorDraft cms cache.
const sailorDraftCache: {
  [townSize: number]: { [developmentType: number]: { [developmentLevel: number]: number } };
} = {};

export function getTownDraftableSailor(
  townCmsId: number,
  developmentLevels: { [developmentType: number]: number }
): number {
  const townSize = cms.Town[townCmsId].townSize;
  const sailorDraftCms = cms.SailorDraft;
  if (!sailorDraftCache[townSize]) {
    // Cache all cms data.
    for (const key of Object.keys(sailorDraftCms)) {
      const elem = sailorDraftCms[key];
      if (!sailorDraftCache[elem.townSize]) {
        sailorDraftCache[elem.townSize] = {};
      }
      if (!sailorDraftCache[elem.townSize][elem.townDevelopType]) {
        sailorDraftCache[elem.townSize][elem.townDevelopType] = {};
      }
      sailorDraftCache[elem.townSize][elem.townDevelopType][elem.townDevelopLv] = elem.increaseVal;
    }
  }

  let sum = 0;
  for (const developmentType of Object.keys(developmentLevels)) {
    sum += sailorDraftCache[townSize][developmentType][developmentLevels[developmentType]];
  }

  return sum;
}

// ------------------------------------------------------------------------------------------------
// TownBuilding cms cache.
const townBuildingsCache: {
  [townCmsId: number]: { [buildingType: number]: TownBuildingDesc[] };
} = {};
export function getTownBuilding(townCmsId: number, buildingType: number): TownBuildingDesc[] {
  if (!townBuildingsCache[townCmsId]) {
    for (const key of Object.keys(cms.TownBuilding)) {
      const elem = cms.TownBuilding[key];
      if (!townBuildingsCache[elem.townId]) {
        townBuildingsCache[elem.townId] = {};
      }
      if (!townBuildingsCache[elem.townId][elem.buildingType]) {
        townBuildingsCache[elem.townId][elem.buildingType] = [];
      }

      townBuildingsCache[elem.townId][elem.buildingType].push(elem);
    }
  }
  return townBuildingsCache[townCmsId][buildingType];
}

// ------------------------------------------------------------------------------------------------
let collectorTownBuildingCmsIdsCache: number[];
export function getCollectorTownBuildingCmsIds(): number[] {
  if (!collectorTownBuildingCmsIdsCache) {
    collectorTownBuildingCmsIdsCache = [];
    _.forOwn(cms.TownBuilding, (elem) => {
      if (elem.buildingType === BUILDING_TYPE.COLLECTOR) {
        collectorTownBuildingCmsIdsCache.push(elem.id);
      }
    });
  }

  return collectorTownBuildingCmsIdsCache;
}

// ------------------------------------------------------------------------------------------------
let sortedDiscoveryCollectorRankingCache: DiscoveryRankingDesc[];
let sortedDiscoveryGlobalRankingCache: DiscoveryRankingDesc[];
let maxGlobalRanking;

function _sortDiscoveryRanking() {
  if (sortedDiscoveryCollectorRankingCache) {
    return;
  }

  sortedDiscoveryCollectorRankingCache = [];
  sortedDiscoveryGlobalRankingCache = [];
  maxGlobalRanking = 0;

  _.forOwn(cms.DiscoveryRanking, (desc) => {
    if (desc.rankingTarget === DISCOVERY_RANKING_TARGET.GLOBAL) {
      sortedDiscoveryGlobalRankingCache.push(desc);
      maxGlobalRanking = Math.max(maxGlobalRanking, desc.rankingRange);
    } else {
      sortedDiscoveryCollectorRankingCache.push(desc);
    }
  });

  sortedDiscoveryCollectorRankingCache.sort((a, b) => a.id - b.id);
  sortedDiscoveryGlobalRankingCache.sort((a, b) => a.id - b.id);
}

export function getSortedDiscoveryCollectorRanking(): DiscoveryRankingDesc[] {
  _sortDiscoveryRanking();
  return sortedDiscoveryCollectorRankingCache;
}

export function getSortedDiscoveryGlobalRanking(): DiscoveryRankingDesc[] {
  _sortDiscoveryRanking();
  return sortedDiscoveryGlobalRankingCache;
}

export function getMaxGlobalRanking(): number {
  _sortDiscoveryRanking();
  return maxGlobalRanking;
}

// ------------------------------------------------------------------------------------------------
let sortedInvestmentRankingCache: InvestCompanyRankingDesc[];

function _sortInvestmentRanking() {
  if (sortedInvestmentRankingCache) {
    return;
  }

  sortedInvestmentRankingCache = [];

  _.forOwn(cms.InvestCompanyRanking, (desc) => {
    if (isFilteredByCountryCode(desc.localBitFlag)) {
      return;
    }
    sortedInvestmentRankingCache.push(desc);
  });

  sortedInvestmentRankingCache.sort((a, b) => a.id - b.id);
}

export function getSortedInvestmentRanking(): InvestCompanyRankingDesc[] {
  _sortInvestmentRanking();
  return sortedInvestmentRankingCache;
}

// ------------------------------------------------------------------------------------------------
let sortedEventInvestmentRankingCache: InvestCompanyRankingDesc[];

function _sortEventInvestmentRanking() {
  if (sortedEventInvestmentRankingCache) {
    return;
  }

  sortedEventInvestmentRankingCache = [];

  _.forOwn(cms.InvestEventRanking, (desc) => {
    if (isFilteredByCountryCode(desc.localBitFlag)) {
      return;
    }
    sortedEventInvestmentRankingCache.push(desc);
  });

  sortedEventInvestmentRankingCache.sort((a, b) => a.id - b.id);
}

export function getSortedEventInvestmentRanking(): InvestCompanyRankingDesc[] {
  _sortEventInvestmentRanking();
  return sortedEventInvestmentRankingCache;
}

// ------------------------------------------------------------------------------------------------
// DefaultPoint cms cache.
const defaultPoints: { [mateCmsId: number]: { cmsId: number; value: number }[] } = {};
export function getDefaultPoint(mateCmsId: number): { cmsId: number; value: number }[] {
  if (!defaultPoints[mateCmsId]) {
    for (const key of Object.keys(cms.DefaultPoint)) {
      const elem = cms.DefaultPoint[key];
      defaultPoints[elem.mateId] = [];
      for (const point of elem.point) {
        defaultPoints[elem.mateId].push({
          cmsId: point.Type,
          value: point.Val,
        });
      }
    }
  }
  return defaultPoints[mateCmsId];
}

// ------------------------------------------------------------------------------------------------
let defaultShipSailCrestsCache: { [offset: number]: number };
export function getDefaultShipSailCrests(): { [offset: number]: number } {
  if (!defaultShipSailCrestsCache) {
    defaultShipSailCrestsCache = {};

    _.forOwn(cms.DefaultSailCrest, (elem) => {
      const id = elem.defaultsailcrestId;
      const offset = Math.floor(id / 32);
      if (!defaultShipSailCrestsCache[offset]) {
        defaultShipSailCrestsCache[offset] = 0;
      }
      defaultShipSailCrestsCache[offset] =
        (defaultShipSailCrestsCache[offset] | (1 << id % 32)) >>> 0;
    });
  }

  return defaultShipSailCrestsCache;
}

// ------------------------------------------------------------------------------------------------
let defaultShipSailPatternColorsCache: { [offset: number]: number };
export function getDefaultShipSailPatternColors(): { [offset: number]: number } {
  if (!defaultShipSailPatternColorsCache) {
    defaultShipSailPatternColorsCache = {};

    _.forOwn(cms.DefaultSailPatternColor, (elem) => {
      const id = elem.defaultsailpatterncolorId;
      const offset = Math.floor(id / 32);
      if (!defaultShipSailPatternColorsCache[offset]) {
        defaultShipSailPatternColorsCache[offset] = 0;
      }
      defaultShipSailPatternColorsCache[offset] =
        (defaultShipSailPatternColorsCache[offset] | (1 << id % 32)) >>> 0;
    });
  }

  return defaultShipSailPatternColorsCache;
}

// ------------------------------------------------------------------------------------------------
let defaultShipBody1ColorsCache: { [offset: number]: number };
export function getDefaultShipBody1Colors(): { [offset: number]: number } {
  if (!defaultShipBody1ColorsCache) {
    defaultShipBody1ColorsCache = {};

    _.forOwn(cms.DefaultShipBodyFirstColor, (elem) => {
      const id = elem.defaultshipbodyfirstcolorId;
      const offset = Math.floor(id / 32);
      if (!defaultShipBody1ColorsCache[offset]) {
        defaultShipBody1ColorsCache[offset] = 0;
      }
      defaultShipBody1ColorsCache[offset] =
        (defaultShipBody1ColorsCache[offset] | (1 << id % 32)) >>> 0;
    });
  }

  return defaultShipBody1ColorsCache;
}

// ------------------------------------------------------------------------------------------------
export function getVillageFriendshipCms(friendship: number): VillageFriendshipDesc {
  for (const key of Object.keys(cms.VillageFriendship)) {
    if (friendship >= cms.VillageFriendship[key].reputation) {
      return cms.VillageFriendship[key];
    }
  }

  return undefined;
}

// ------------------------------------------------------------------------------------------------
let sortedVillageFriendShipCmses: VillageFriendshipDesc[];
export function getVillageFriendshipCmsesSortedByGroup() {
  if (!sortedVillageFriendShipCmses) {
    sortedVillageFriendShipCmses = _.sortBy(cms.VillageFriendship, (elem) => elem.grade);
  }

  return sortedVillageFriendShipCmses;
}

// ------------------------------------------------------------------------------------------------
let villageDiscoveryCache: { [groupId: number]: DiscoveryDesc[] };
export function getVillageDiscoveryIds(groupId: number) {
  if (villageDiscoveryCache) {
    return villageDiscoveryCache[groupId];
  }

  villageDiscoveryCache = {};
  _.forOwn(cms.VillageDiscoveryGroup, (villageDiscoveryCms) => {
    const discoveryCms = cms.Discovery[villageDiscoveryCms.discoveryId];
    // if (isFilteredByCountryCode(discoveryCms)) {
    //   return;
    // }
    if (!discoveryCms) {
      return;
    }

    if (!villageDiscoveryCache[villageDiscoveryCms.group]) {
      villageDiscoveryCache[villageDiscoveryCms.group] = [];
    }
    villageDiscoveryCache[villageDiscoveryCms.group].push(discoveryCms);
  });

  return villageDiscoveryCache[groupId];
}

// ------------------------------------------------------------------------------------------------
let exchangeVillageListCache: { [groupId: number]: ExchangeVillageListDesc[] };
export function getVillageExchangeList(groupId: number) {
  if (!exchangeVillageListCache) {
    exchangeVillageListCache = {};
    _.forOwn(cms.ExchangeVillageList, (exchangeVillageListCms) => {
      if (!exchangeVillageListCache[exchangeVillageListCms.group]) {
        exchangeVillageListCache[exchangeVillageListCms.group] = [];
      }
      exchangeVillageListCache[exchangeVillageListCms.group].push(exchangeVillageListCms);
    });
  }

  return exchangeVillageListCache[groupId];
}

// ------------------------------------------------------------------------------------------------
export function getExchangeVillageStorageByLevelValue(
  levelValue: number
): ExchangeVillageStorageDesc {
  for (const id in cms.ExchangeVillageStorage) {
    const exchangeVillageStorageCms = cms.ExchangeVillageStorage[id];
    if (
      exchangeVillageStorageCms.storageGradeSubLevelMin <= levelValue &&
      levelValue < exchangeVillageStorageCms.storageGradeSubLevelMax
    ) {
      return exchangeVillageStorageCms;
    }
  }

  return undefined;
}

// ------------------------------------------------------------------------------------------------
export function getTradeGoodsCategoryByExchangeVillageList(exchangeVillageListCmsId: number) {
  const exchangeVillageListCms = cms.ExchangeVillageList[exchangeVillageListCmsId];
  const tradeGoodsCms = cms.TradeGoods[exchangeVillageListCms.getTradeGoodsId];
  // if (isFilteredByCountryCode(tradeGoodsCms)) {
  //   return undefined;
  // }
  return tradeGoodsCms?.tradeGoodsCategory;
}

// ------------------------------------------------------------------------------------------------
let exchangeVillageListCmsIdsByVillageCache: { [villageCmsId: number]: number[] };
export function getExchangeVillageListCmsIdsByVillage() {
  if (!exchangeVillageListCmsIdsByVillageCache) {
    exchangeVillageListCmsIdsByVillageCache = {};
    _.forOwn(cms.Village, (villageCms) => {
      const exchangeVillageListCmses = getVillageExchangeList(villageCms.exchangeVillageListGroup);
      exchangeVillageListCmsIdsByVillageCache[villageCms.id] = [];
      _.forEach(exchangeVillageListCmses, (exchangeVillageListCms) => {
        exchangeVillageListCmsIdsByVillageCache[villageCms.id].push(exchangeVillageListCms.id);
      });
    });
  }

  return exchangeVillageListCmsIdsByVillageCache;
}

// ------------------------------------------------------------------------------------------------
// 육지 탐색시  해제 되는  재해 cmsId 저장
let landDisasterCmsIdCache: number[];
export function getLandDisasterCmsIds() {
  if (landDisasterCmsIdCache) {
    return landDisasterCmsIdCache;
  }

  landDisasterCmsIdCache = [];
  _.forOwn(cms.OceanDisaster, (oceanDisaster) => {
    if (oceanDisaster.isLandingSolution) {
      landDisasterCmsIdCache.push(oceanDisaster.id);
    }
  });
  return landDisasterCmsIdCache;
}

// ------------------------------------------------------------------------------------------------
let defaultShipBody2ColorsCache: { [offset: number]: number };
export function getDefaultShipBody2Colors(): { [offset: number]: number } {
  if (!defaultShipBody2ColorsCache) {
    defaultShipBody2ColorsCache = {};

    _.forOwn(cms.DefaultShipBodySecondColor, (elem) => {
      const id = elem.defaultshipbodysecondcolorId;
      const offset = Math.floor(id / 32);
      if (!defaultShipBody2ColorsCache[offset]) {
        defaultShipBody2ColorsCache[offset] = 0;
      }
      defaultShipBody2ColorsCache[offset] =
        (defaultShipBody2ColorsCache[offset] | (1 << id % 32)) >>> 0;
    });
  }

  return defaultShipBody2ColorsCache;
}

// ------------------------------------------------------------------------------------------------
export function getMaxMateLevel(awakenLevel: number, userLevel: number): number {
  return Math.min(userLevel, cms.Const['MateAwaken' + awakenLevel + 'DevelopmentLv'].value);
}

// ------------------------------------------------------------------------------------------------
export function calcMateLevel(
  jobType: JOB_TYPE,
  exp: number,
  awakenLevel: number,
  userLevel: number
): number {
  const maxLv = getMaxMateLevel(awakenLevel, userLevel);
  for (let level = 1; level <= maxLv; level++) {
    const expData = cms.CharacterExp[level];
    if (exp < expData.accumulateExp[jobType - 1]) {
      return expData.id;
    }
  }

  return maxLv;
}

// ------------------------------------------------------------------------------------------------
export function calcUserLevel(exp: number): number {
  for (let level = 1; level <= cms.Const.MaxCompanyLv.value; level++) {
    const expData = cms.CompanyExp[level];
    if (exp < expData.accumulateExp) {
      return expData.id;
    }
  }

  return cms.Const.MaxCompanyLv.value;
}

// ------------------------------------------------------------------------------------------------
export function getMaxUserExp(): number {
  const maxLevel = cms.Const.MaxCompanyLv.value;
  return cms.CompanyExp[maxLevel - 1].accumulateExp;
}

// ------------------------------------------------------------------------------------------------
// 패스 이벤트 - LV 계산
export function calcPassEventLevel(passEventCms: EventPageDesc, exp: number): number {
  const maxLevel = getPassEventMaxLevel(passEventCms);
  for (let level = 1; level <= maxLevel; level++) {
    const expData = cms.EventMissionExp[level];
    if (exp < expData.accumulateExp) {
      return level;
    }
  }
  return maxLevel;
}

// ------------------------------------------------------------------------------------------------
// 패스 이벤트 - 최대 EXP
export function getMaxPassEventExp(passEventCms: EventPageDesc): number {
  const addedExp = getPassEventAddedExp(passEventCms);
  const maxLevel = getPassEventMaxLevel(passEventCms);
  return cms.EventMissionExp[maxLevel - 1].accumulateExp + addedExp;
}

// ------------------------------------------------------------------------------------------------
export function getMaxBpLevel(): number {
  return cms.Const.MaxShipBlueprintLv.value;
}

// ------------------------------------------------------------------------------------------------
export function getMaxBlueprintExp(shipSize: number): number {
  const maxBpLevel = getMaxBpLevel();
  return getShipBuildMasteryExpCmsAccumulateExp(maxBpLevel - 1, shipSize);
}

/*
  shipCms.shipSize 2: 소형, 3: 중형, 4: 대형 5: 초대형 =>
  shipBuildingsCms.shipSize 0: 소형, 1: 중형, 2: 대형, 3: 초대형
*/
export function getShipBuildMasteryExpCmsAccumulateExp(bpLevel: number, shipSize: SHIP_SIZE) {
  return cms.ShipBuildMasteryExp[bpLevel].accumulateExp[shipSize - 2];
}

// ------------------------------------------------------------------------------------------------
export function getShipBuildExpCmsAddMinRate(bpLevel: number, shipSize: SHIP_SIZE) {
  return cms.ShipBuildMasteryExp[bpLevel].addMinRate[shipSize - 2];
}

// ------------------------------------------------------------------------------------------------
// blueprint EXP 로 blueprint LV 반환
export function calcBlueprintExpLevel(exp: number, shipSize: number): number {
  const maxBpLevel = getMaxBpLevel();
  for (let level = 1; level <= maxBpLevel; level++) {
    if (exp < getShipBuildMasteryExpCmsAccumulateExp(level, shipSize)) {
      return level;
    }
  }
  return maxBpLevel;
}

// ------------------------------------------------------------------------------------------------
// Shop cms cache.
export interface shopCacheInfo {
  [blackMarketType: number]: ShopDesc[];
}

// ------------------------------------------------------------------------------------------------
let blackMarketShopCache: { [townCmsId: number]: shopCacheInfo } = {};
export function getBlackMarketShopCmses(townCmsId: number, curTimeUtc: number): shopCacheInfo {
  if (!blackMarketShopCache[townCmsId]) {
    blackMarketShopCache[townCmsId] = {};

    const shopCmsIds: Set<number> = getTownShopCmsIds(townCmsId);
    if (!shopCmsIds || shopCmsIds.size === 0) {
      return {};
    }

    for (const cmsId of shopCmsIds) {
      const shopCms = cms.Shop[cmsId];
      if (
        shopCms.shopType === SHOP_TYPE.BLACK_MARKET_USER_ITEM ||
        shopCms.shopType === SHOP_TYPE.BLACK_MARKET_MATE_EQUIPMENT
      ) {
        if (!shopCms.Probability) {
          // 기획 테이블 버그.
          return undefined;
        }

        if (shopCms.blackMarketType === BLACK_MARKET_TYPE.EVENT) {
          if (!isLiveEvent(shopCms.liveEvent, curTimeUtc)) {
            continue;
          }
        }

        if (!blackMarketShopCache[townCmsId][shopCms.blackMarketType]) {
          blackMarketShopCache[townCmsId][shopCms.blackMarketType] = [];
        }
        blackMarketShopCache[townCmsId][shopCms.blackMarketType].push(shopCms);
      }
    }
  }
  return blackMarketShopCache[townCmsId] ? blackMarketShopCache[townCmsId] : {};
}

// ------------------------------------------------------------------------------------------------
let fishDiscoveryCache: { [group: number]: DiscoveryDesc[] };
export function getFishDiscoveryCmses(groupId: number): DiscoveryDesc[] {
  if (!fishDiscoveryCache) {
    fishDiscoveryCache = {};
    _.forOwn(cms.FishDiscoveryGroup, (elem) => {
      const discoveryCms = cms.Discovery[elem.discoveryId];
      // if (isFilteredByCountryCode(discoveryCms)) {
      //   return;
      // }
      if (!discoveryCms) {
        return;
      }

      if (!fishDiscoveryCache[elem.group]) {
        fishDiscoveryCache[elem.group] = [];
      }
      fishDiscoveryCache[elem.group].push(discoveryCms);
    });
  }
  return fishDiscoveryCache[groupId];
}

// ------------------------------------------------------------------------------------------------
let fishGradeCache: { [grade: number]: FishGradeDesc };
export function getFishGradeCms(grade: number) {
  if (!fishGradeCache) {
    fishGradeCache = {};
    _.forOwn(cms.FishGrade, (elem) => {
      fishGradeCache[elem.discoveryGradeMax] = elem;
    });
  }
  return fishGradeCache[grade];
}

// ------------------------------------------------------------------------------------------------
const admiralCache: { [mateCmsId: number]: AdmiralDesc } = {};
export function getAdmiralByMateCmsId(mateCmsId: number): AdmiralDesc {
  if (Object.keys(admiralCache).length === 0) {
    for (const key of Object.keys(cms.Admiral)) {
      const admiralCms = cms.Admiral[key];
      admiralCache[admiralCms.mateId] = admiralCms;
    }
  }

  return admiralCache[mateCmsId];
}

// ------------------------------------------------------------------------------------------------
let cachePreferenceItem: {
  lastWeekCnt: number;
  [townBuildingCmsId: number]: PreferenceItem[];
} = {
  lastWeekCnt: undefined,
};
const collectorPreferenceSeed = 100;

export function collectorThisWeekPreferenceItems(townBuildingCmsId: number): PreferenceItem[] {
  const weekCnt = GetFullWeeksUsingLocalTime(
    mutil.curTimeUtc(),
    cms.Define.DiscoveryPreferentialReport
  );
  if (cachePreferenceItem && cachePreferenceItem.lastWeekCnt === weekCnt) {
    return cachePreferenceItem[townBuildingCmsId];
  }

  let easyDifficultyItems: PreferenceItem[] = [];
  let normalDifficultyItems: PreferenceItem[] = [];
  let hardDifficultyItems: PreferenceItem[] = [];

  _.forOwn(cms.Item, (desc) => {
    if (desc.type !== ITEM_TYPE.RESOURCE) {
      return;
    }

    if (desc.mainGrade === ITEM_GRADE.C || desc.mainGrade === ITEM_GRADE.D) {
      easyDifficultyItems.push({
        itemCmsId: desc.id,
        difficulty: ITEM_REPORT_DIFFICULTY.EASY,
      });
    }
    if (desc.mainGrade === ITEM_GRADE.A || desc.mainGrade === ITEM_GRADE.B) {
      normalDifficultyItems.push({
        itemCmsId: desc.id,
        difficulty: ITEM_REPORT_DIFFICULTY.NORMAL,
      });
    }
    if (desc.mainGrade === ITEM_GRADE.S) {
      hardDifficultyItems.push({
        itemCmsId: desc.id,
        difficulty: ITEM_REPORT_DIFFICULTY.HARD,
      });
    }
  });

  // 자원 보고 아이템이 없는 경우 에러 처리
  assert(easyDifficultyItems.length > 0);
  assert(normalDifficultyItems.length > 0);
  assert(hardDifficultyItems.length > 0);

  shuffleReportItems(easyDifficultyItems, collectorPreferenceSeed);
  shuffleReportItems(normalDifficultyItems, collectorPreferenceSeed);
  shuffleReportItems(hardDifficultyItems, collectorPreferenceSeed);

  // 0 ~ (arr.length-1)값 startIndex 결정한다
  let easyReportIdx = weekCnt % easyDifficultyItems.length;
  let normalReportIdx = weekCnt % normalDifficultyItems.length;
  let hardReportIdx = weekCnt % hardDifficultyItems.length;

  _.forOwn(cms.TownBuilding, (desc) => {
    if (desc.buildingType !== BUILDING_TYPE.COLLECTOR) {
      return;
    }

    // cache update
    cachePreferenceItem[desc.id] = [
      easyDifficultyItems[easyReportIdx],
      normalDifficultyItems[normalReportIdx],
      hardDifficultyItems[hardReportIdx],
    ];

    easyReportIdx++;
    if (easyReportIdx >= easyDifficultyItems.length) {
      easyReportIdx = 0;
    }

    normalReportIdx++;
    if (normalReportIdx >= normalDifficultyItems.length) {
      normalReportIdx = 0;
    }

    hardReportIdx++;
    if (hardReportIdx >= hardDifficultyItems.length) {
      hardReportIdx = 0;
    }
  });

  cachePreferenceItem.lastWeekCnt = weekCnt;
  return cachePreferenceItem[townBuildingCmsId];
}

// ------------------------------------------------------------------------------------------------
const mustAppearRequestGroupCache: {
  [townCmsId: number]: Set<number>;
} = {};

export function isMustAppearQuest(townCmsId: number, questId: number): boolean {
  buildRequestGroupCache();
  buildEventRequestGroupCache();

  const townCms = cms.Town[townCmsId];
  if (!townCms) {
    return false;
  }

  if (!mustAppearRequestGroupCache[townCmsId]) {
    mustAppearRequestGroupCache[townCmsId] = new Set<number>();
    const groups = townCms.requestGroups;
    if (groups) {
      for (const group of groups) {
        const cache = requestGroupCache[group];
        _.forOwn(cache, (requestGroupCmses) => {
          for (const requestGroupCms of requestGroupCmses) {
            if (requestGroupCms.isMustAppear) {
              mustAppearRequestGroupCache[townCmsId].add(requestGroupCms.questId);
            }
          }
        });

        const eventCache = eventRequestGroupCache[group];
        _.forOwn(eventCache, (eventRequestGroupCmses) => {
          for (const eventRequestGroupCms of eventRequestGroupCmses) {
            if (eventRequestGroupCms.isMustAppear) {
              mustAppearRequestGroupCache[townCmsId].add(eventRequestGroupCms.questId);
            }
          }
        });
      }
    }
  }

  return mustAppearRequestGroupCache[townCmsId].has(questId);
}

let requestGroupCache: { [group: number]: { [jobType: number]: RequestGroupDesc[] } };
export interface QuestGroupElem {
  questId: number;
  ratio: number;
}
const townRequestGroupCache: {
  [townCmsId: number]: { [jobType: number]: QuestGroupElem[] };
} = {};
function buildRequestGroupCache(): void {
  if (requestGroupCache) {
    return;
  }
  requestGroupCache = {};
  _.forOwn(cms.RequestGroup, (requestCms) => {
    if (!requestCms.ratio) {
      // 조합-의뢰에서 확률을 무시하는 기획이 있어서 ratio 가 0인데도 뽑히는 경우(QAUWO-2688)가 있었음
      // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
      return;
    }

    if (!requestGroupCache[requestCms.group]) {
      requestGroupCache[requestCms.group] = {
        [JOB_TYPE.NONE]: [],
        [JOB_TYPE.ADVENTURE]: [],
        [JOB_TYPE.TRADE]: [],
        [JOB_TYPE.BATTLE]: [],
      };
    }
    const questCms = cms.Quest[requestCms.questId];
    if (isFilteredByCountryCode(questCms.localBitflag)) {
      return;
    }
    if (questCms.category !== QUEST_CATEGORY.REQUEST) {
      return;
    }
    requestGroupCache[requestCms.group][questCms.jobType].push(requestCms);
  });
}
export function getTownRequestGroup(
  townCmsId: number,
  getAllRequestGroup = true
): { [jobType: number]: QuestGroupElem[] } {
  buildRequestGroupCache();

  // Build townRequestGroupCache
  if (!townRequestGroupCache[townCmsId]) {
    townRequestGroupCache[townCmsId] = {
      [JOB_TYPE.NONE]: [],
      [JOB_TYPE.ADVENTURE]: [],
      [JOB_TYPE.TRADE]: [],
      [JOB_TYPE.BATTLE]: [],
    };
    const groups = cms.Town[townCmsId].requestGroups;
    if (groups) {
      for (const group of groups) {
        const cr = requestGroupCache[group];
        _.forOwn(cr, (jobElem, jobType) => {
          for (const requestCms of jobElem) {
            if (!getAllRequestGroup && isMustAppearQuest(townCmsId, requestCms.questId)) {
              continue;
            }

            const sameElem: QuestGroupElem = townRequestGroupCache[townCmsId][jobType].find(
              (inRequestCms: QuestGroupElem) => {
                return inRequestCms.questId === requestCms.questId;
              }
            );
            if (sameElem) {
              sameElem.ratio += requestCms.ratio;
            } else {
              townRequestGroupCache[townCmsId][jobType].push({
                questId: requestCms.questId,
                ratio: requestCms.ratio,
              });
            }
          }
        });
      }
    }
  }

  return townRequestGroupCache[townCmsId];
}

let eventRequestGroupCache: {
  [group: number]: { [jobType: number]: EventRequestGroupDesc[] };
};

const townEventRequestGroupCache: {
  [townCmsId: number]: { [jobType: number]: QuestGroupElem[] };
} = {};

function buildEventRequestGroupCache(): void {
  if (eventRequestGroupCache) {
    return;
  }
  eventRequestGroupCache = {};
  _.forOwn(cms.EventRequestGroup, (eventRequestCms) => {
    if (!eventRequestCms.ratio) {
      // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
      return;
    }

    if (!eventRequestGroupCache[eventRequestCms.group]) {
      eventRequestGroupCache[eventRequestCms.group] = {
        [JOB_TYPE.NONE]: [],
        [JOB_TYPE.ADVENTURE]: [],
        [JOB_TYPE.TRADE]: [],
        [JOB_TYPE.BATTLE]: [],
      };
    }
    const questCms = cms.Quest[eventRequestCms.questId];
    if (isFilteredByCountryCode(questCms.localBitflag)) {
      return;
    }
    if (questCms.category !== QUEST_CATEGORY.EVENT_REQUEST) {
      return;
    }
    eventRequestGroupCache[eventRequestCms.group][questCms.jobType].push(eventRequestCms);
  });
}
export function getTownEventRequestGroup(
  townCmsId: number,
  getAllEventRequestGroup = true
): {
  [jobType: number]: QuestGroupElem[];
} {
  buildEventRequestGroupCache();

  // Build townEventRequestGroupCache
  if (!townEventRequestGroupCache[townCmsId]) {
    townEventRequestGroupCache[townCmsId] = {
      [JOB_TYPE.NONE]: [],
      [JOB_TYPE.ADVENTURE]: [],
      [JOB_TYPE.TRADE]: [],
      [JOB_TYPE.BATTLE]: [],
    };
    const groups = cms.Town[townCmsId].requestGroups;
    if (groups) {
      for (const group of groups) {
        const cr = eventRequestGroupCache[group];
        _.forOwn(cr, (jobElem, jobType) => {
          for (const requestCms of jobElem) {
            if (!getAllEventRequestGroup && isMustAppearQuest(townCmsId, requestCms.questId)) {
              continue;
            }

            const sameElem: QuestGroupElem = townEventRequestGroupCache[townCmsId][jobType].find(
              (inRequestCms: QuestGroupElem) => {
                return inRequestCms.questId === requestCms.questId;
              }
            );
            if (sameElem) {
              sameElem.ratio += requestCms.ratio;
            } else {
              townEventRequestGroupCache[townCmsId][jobType].push({
                questId: requestCms.questId,
                ratio: requestCms.ratio,
              });
            }
          }
        });
      }
    }
  }

  return townEventRequestGroupCache[townCmsId];
}

// ------------------------------------------------------------------------------------------------
let burstGroupCache: { [group: number]: BurstGroupDesc[] };
function buildBurstGroupCache(): void {
  if (burstGroupCache) {
    return;
  }
  burstGroupCache = {};
  _.forOwn(cms.BurstGroup, (elem) => {
    if (!elem.ratio) {
      // 조합-의뢰에서 확률을 무시하는 기획이 있어서 ratio 가 0인데도 뽑히는 경우(QAUWO-2688)가 있었음
      // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
      return;
    }
    if (!burstGroupCache[elem.group]) {
      burstGroupCache[elem.group] = [];
    }
    const questCms = cms.Quest[elem.questId];
    if (isFilteredByCountryCode(questCms.localBitflag)) {
      return;
    }
    if (questCms.category !== QUEST_CATEGORY.RANDOM) {
      return;
    }
    burstGroupCache[elem.group].push(elem);
  });
}
const itemQuestGroupCache: { [itemCmsId: number]: QuestGroupElem[] } = {};
export function getItemQuestGroup(itemCmsId: number): QuestGroupElem[] {
  buildBurstGroupCache();

  // Build itemRequestGroupCache
  if (!itemQuestGroupCache[itemCmsId]) {
    itemQuestGroupCache[itemCmsId] = [];
    const groups = cms.Item[itemCmsId].burstGroups;
    if (groups) {
      for (const group of groups) {
        for (const burstGroupCms of burstGroupCache[group]) {
          const sameElem: QuestGroupElem = itemQuestGroupCache[itemCmsId].find(
            (elem: QuestGroupElem) => {
              return elem.questId === burstGroupCms.questId;
            }
          );
          if (sameElem) {
            sameElem.ratio += burstGroupCms.ratio;
          } else {
            itemQuestGroupCache[itemCmsId].push({
              questId: burstGroupCms.questId,
              ratio: burstGroupCms.ratio,
            });
          }
        }
      }
    }
  }

  return itemQuestGroupCache[itemCmsId];
}

// ------------------------------------------------------------------------------------------------
let royalOrderGroupCache: { [nationCmsId: number]: RoyalOrderGroupDesc[] };
let royalTitleOrderGroupCache: { [nationCmsId: number]: RoyalOrderGroupDesc[] };

function buildRoyalOrderGroupCache() {
  if (royalOrderGroupCache && royalTitleOrderGroupCache) {
    return;
  }

  royalOrderGroupCache = {};
  royalTitleOrderGroupCache = {};

  _.forOwn(cms.RoyalOrderGroup, (elem) => {
    if (!elem.ratio) {
      // 조합-의뢰에서 확률을 무시하는 기획이 있어서 ratio 가 0인데도 뽑히는 경우(QAUWO-2688)가 있었음
      // 기획에서 사용하지 않는 row 의 경우 ratio 를 0으로 해둔다고 함.
      // 왕궁-칙명에서도 작위 승급 칙명에서 확률을 무시하는 기획이 있는 것 참고
      return;
    }

    const questCms = cms.Quest[elem.questId];
    if (isFilteredByCountryCode(questCms.localBitflag)) {
      return;
    }

    if (questCms.isRoyalTitle) {
      if (!royalTitleOrderGroupCache[elem.nationId]) {
        royalTitleOrderGroupCache[elem.nationId] = [];
      }
      royalTitleOrderGroupCache[elem.nationId].push(elem);
    } else {
      if (!royalOrderGroupCache[elem.nationId]) {
        royalOrderGroupCache[elem.nationId] = [];
      }
      royalOrderGroupCache[elem.nationId].push(elem);
    }
  });
}

export function getRoyalOrderGroup(nationCmsId: number): RoyalOrderGroupDesc[] {
  buildRoyalOrderGroupCache();
  return royalOrderGroupCache[nationCmsId];
}

export function getRoyalTitleOrderGroup(nationCmsId: number): RoyalOrderGroupDesc[] {
  buildRoyalOrderGroupCache();
  return royalTitleOrderGroupCache[nationCmsId];
}

// ------------------------------------------------------------------------------------------------
export function getTradeSpeciality(types: number) {
  for (let i = types; i > 0; i--) {
    if (cms.Const['TradeSpecialities' + i]) {
      return cms.Const['TradeSpecialities' + i].value;
    }
  }

  mlog.warn('can not find TradeSpecialities. types: ', { types });
  return 1;
}

// ------------------------------------------------------------------------------------------------
const regionTownCmsIdsCache: { [regionCmsId: number]: number[] } = {};
export function getRegionTownCmsIds(regionCmsId: number): number[] {
  if (Object.keys(regionTownCmsIdsCache).length === 0) {
    for (const key of Object.keys(cms.Town)) {
      const townCms = cms.Town[key];
      if (!regionTownCmsIdsCache[townCms.RegionId]) {
        regionTownCmsIdsCache[townCms.RegionId] = [];
      }
      regionTownCmsIdsCache[townCms.RegionId].push(townCms.id);
    }
  }

  return regionTownCmsIdsCache[regionCmsId];
}

// ------------------------------------------------------------------------------------------------
let regionCmsIdsThatHasTown: number[] = [];
export function getRegionCmsIdsThatHasTown(): number[] {
  if (regionCmsIdsThatHasTown.length === 0) {
    const cmsIdsSet = new Set<number>();
    _.forOwn(cms.Town, (elem) => {
      cmsIdsSet.add(elem.RegionId);
    });

    regionCmsIdsThatHasTown = Array.from(cmsIdsSet);
  }

  return regionCmsIdsThatHasTown;
}

// ------------------------------------------------------------------------------------------------
const cultureTownCmsIdsCache: { [culturalAreaCmsId: number]: number[] } = {};
export function getCultureTownCmsIds(culturalAreaCmsId: number): number[] {
  if (Object.keys(cultureTownCmsIdsCache).length === 0) {
    for (const key of Object.keys(cms.Town)) {
      const townCms = cms.Town[key];
      if (!cultureTownCmsIdsCache[townCms.CulturalAreaId]) {
        cultureTownCmsIdsCache[townCms.CulturalAreaId] = [];
      }
      cultureTownCmsIdsCache[townCms.CulturalAreaId].push(townCms.id);
    }
  }

  return cultureTownCmsIdsCache[culturalAreaCmsId];
}

// ------------------------------------------------------------------------------------------------
// 선박의 블루프린트 기반, 기본 내구도 캐시.
// 건조 랜덤 스탯 제외한 값만 캐시.
// [TODO] DefaultShip.json 으로 이런걸 만들어도 될듯?
const shipDefaultDurabilityCache: { [shipBlueprintCmsId: number]: number } = {};

// ShipBlueprint.json 의 데이터를 기반으로, 기본 내구도를 구한다.
export function shipDefaultDurability(shipBlueprintCmsId: number, rndStats: number[]) {
  const shipBlueprintCms = cms.ShipBlueprint[shipBlueprintCmsId];

  if (shipDefaultDurabilityCache[shipBlueprintCmsId] === undefined) {
    let bpBaseDur = 0;

    for (const shipSlot of shipBlueprintCms.shipSlot) {
      const shipSlotCms = cms.ShipSlot[shipSlot.Id];
      if (!shipSlotCms.statEffect) {
        continue;
      }

      for (const s of shipSlotCms.statEffect) {
        if (s.Type !== STAT_EFFECT_TYPE.STAT_OPERATOR) {
          continue;
        }

        if (s.Id !== STAT_TYPE.SHIP_MAX_DURABILITY) {
          continue;
        }

        bpBaseDur += s.Value;
      }
    }

    shipDefaultDurabilityCache[shipBlueprintCmsId] = bpBaseDur;
  }

  let d = shipDefaultDurabilityCache[shipBlueprintCmsId];
  for (let i = 0; i < shipBlueprintCms.stat.length; i++) {
    const s = shipBlueprintCms.stat[i];
    if (s.Type === STAT_TYPE.SHIP_MAX_DURABILITY) {
      let v = s.Val;
      if (rndStats[i] !== undefined) {
        v = Math.ceil((v * rndStats[i]) / 1000);
      }
      d += v;
    }
  }

  return d;
}

// ------------------------------------------------------------------------------------------------
let shipDefaultLifeCache: { [shipBlueprintCmsId: number]: number };
// ShipBlueprint.json 의 데이터를 기반으로, 기본 내구도를 구한다.
export function shipDefaultLife(shipBlueprintCmsId: number): number {
  if (!shipDefaultLifeCache) {
    shipDefaultLifeCache = {};
    _.forOwn(cms.ShipBlueprint, (elem) => {
      shipDefaultLifeCache[elem.id] = 0;
      for (const stat of elem.stat) {
        if (stat.Type === STAT_TYPE.SHIP_LIFE) {
          shipDefaultLifeCache[elem.id] += stat.Val;
        }
      }
    });
  }

  return shipDefaultLifeCache[shipBlueprintCmsId];
}

// ------------------------------------------------------------------------------------------------
export function statName(statType: number): string {
  return STAT_TYPE[statType] || statType.toString();
}

// ------------------------------------------------------------------------------------------------
let taskCache: { [category: number]: TaskDesc[] };
export function getTask(category: number): TaskDesc[] {
  if (!taskCache) {
    taskCache = {};
    const taskCms = cms.Task;
    _.forOwn(taskCms, (elem) => {
      if (!taskCache[elem.category]) {
        taskCache[elem.category] = [];
      }
      taskCache[elem.category].push(elem);
    });
  }

  return taskCache[category];
}

// ------------------------------------------------------------------------------------------------
// achievementTermsId 를 가지는 ContentsTerms 의 id를 반환.
// ContentsTerms 에서 같은 achievementTermsId 을 가지는 행은 없다.
const contentsTermsIdsCacheOfAchievementTerms: { [achievementTermsCmsId: number]: number } = {};
export function getContentsTermsIdsOfAchievementTerms(achievementTermsCmsId: number): number {
  if (_.isEmpty(contentsTermsIdsCacheOfAchievementTerms)) {
    const contentsTermsCms = cms.ContentsTerms;
    _.forOwn(contentsTermsCms, (elem) => {
      if (elem.achievementTermsId) {
        contentsTermsIdsCacheOfAchievementTerms[elem.achievementTermsId] = elem.id;
      }
    });
  }

  return contentsTermsIdsCacheOfAchievementTerms[achievementTermsCmsId];
}

// ------------------------------------------------------------------------------------------------
const tradeGoodsByCategoryCache: { [category: number]: number[] } = {};
export function getTradeGoodsByCategory(category: TRADE_GOODS_CATEGORY): number[] {
  if (tradeGoodsByCategoryCache[category]) {
    return tradeGoodsByCategoryCache[category];
  }

  _.forOwn(cms.TradeGoods, (elem) => {
    // if (isFilteredByCountryCode(elem)) {
    //   return;
    // }
    if (!tradeGoodsByCategoryCache[elem.tradeGoodsCategory]) {
      tradeGoodsByCategoryCache[elem.tradeGoodsCategory] = [];
    }

    tradeGoodsByCategoryCache[elem.tradeGoodsCategory].push(elem.id);
  });

  // 임시 코드.
  if (!tradeGoodsByCategoryCache[category]) {
    return [];
  }

  return tradeGoodsByCategoryCache[category];
}

// ------------------------------------------------------------------------------------------------
let sortedReputationCache: any[] = [];
export function getSortedReputationArr(): any[] {
  if (sortedReputationCache.length === 0) {
    sortedReputationCache = _.values(cms.Reputation);
    sortedReputationCache.sort((a, b) => {
      return a.id - b.id;
    });
  }

  return sortedReputationCache;
}

// ------------------------------------------------------------------------------------------------
const townKickCache: {
  [kickSituationType: number]: { [target: number]: TownKickDesc[] };
} = {};
export function getTownKick(type: KICK_SITUATION_TYPE, target: number): TownKickDesc[] {
  if (_.isEmpty(townKickCache)) {
    _.forOwn(cms.TownKick, (elem) => {
      if (!townKickCache[elem.kickSituationType]) {
        townKickCache[elem.kickSituationType] = {};
      }
      if (!townKickCache[elem.kickSituationType][elem.kickSituationVal]) {
        townKickCache[elem.kickSituationType][elem.kickSituationVal] = [];
      }

      townKickCache[elem.kickSituationType][elem.kickSituationVal].push(elem);
    });

    _.forOwn(townKickCache, (elem1) => {
      _.forOwn(elem1, (elem2) => {
        elem2.sort((a, b) => {
          return a.kickResultType - b.kickResultType;
        });
      });
    });
  }

  return townKickCache[type][target];
}

// ------------------------------------------------------------------------------------------------
const recruitMateStatCache: {
  [langLevel: number]: number;
} = {};
export function getRecruitMateBuildingBuffStat(langLevel: number) {
  if (_.isEmpty(recruitMateStatCache)) {
    _.forOwn(cms.BuildingBuff, (elem) => {
      if (elem.statEffectId === STAT_TYPE.BUILDING_PUB_MATE_RECRUITING_PRICE_MODIFIER) {
        recruitMateStatCache[elem.contentsTermsCount] = elem.statEffectValue;
      }
    });
  }

  return recruitMateStatCache[langLevel];
}

// ------------------------------------------------------------------------------------------------
const drinkMateStatCache: {
  [langLevel: number]: number;
} = {};
export function getDrinkMateBuildingBuffStat(langLevel: number) {
  if (_.isEmpty(drinkMateStatCache)) {
    _.forOwn(cms.BuildingBuff, (elem) => {
      if (elem.statEffectId === STAT_TYPE.BUILDING_PUB_DRINK_PRICE_MODIFIER) {
        drinkMateStatCache[elem.contentsTermsCount] = elem.statEffectValue;
      }
    });
  }

  return drinkMateStatCache[langLevel];
}

// ------------------------------------------------------------------------------------------------
const mateHighestLanguageLevelsCache: {
  [mateCmsId: number]: number;
} = {};
export function getMateHighestLanguageLevel(mateCmsId: number): number {
  if (mateHighestLanguageLevelsCache[mateCmsId] === undefined) {
    const mateCms = cms.Mate[mateCmsId];
    const langLvs = mateCms.language.map((elem) => elem.Lv);
    langLvs.sort((a, b) => {
      return b - a;
    });
    mateHighestLanguageLevelsCache[mateCmsId] = langLvs[0];
  }

  return mateHighestLanguageLevelsCache[mateCmsId];
}

// ------------------------------------------------------------------------------------------------
const achievementTargetsCache: {
  [achievementTermsCmsId: number]: { [targetIdx: number]: number[] };
} = {};
export function getAchievementTermsTargets(
  achievementTermsCmsId: number,
  targetIdx: number
): number[] {
  if (_.isEmpty(achievementTargetsCache)) {
    _.forOwn(getAchievementCms(), (elem) => {
      if (!elem.achievementTarget) {
        return;
      }

      if (!achievementTargetsCache[elem.achievementTermsId]) {
        achievementTargetsCache[elem.achievementTermsId] = {};
      }

      for (let i = 0; i < elem.achievementTarget.length; i++) {
        if (!achievementTargetsCache[elem.achievementTermsId][i]) {
          achievementTargetsCache[elem.achievementTermsId][i] = [];
        }

        achievementTargetsCache[elem.achievementTermsId][i].push(elem.achievementTarget[i]);
      }
    });

    _.forOwn(cms.ResearchTask, (elem) => {
      if (!elem.researchTaskTargets) {
        return;
      }

      if (!achievementTargetsCache[elem.researchTaskTermsId]) {
        achievementTargetsCache[elem.researchTaskTermsId] = {};
      }

      for (let i = 0; i < elem.researchTaskTargets.length; i++) {
        if (!achievementTargetsCache[elem.researchTaskTermsId][i]) {
          achievementTargetsCache[elem.researchTaskTermsId][i] = [];
        }

        achievementTargetsCache[elem.researchTaskTermsId][i].push(elem.researchTaskTargets[i]);
      }
    });

    _.forOwn(achievementTargetsCache, (elem) => {
      _.forOwn(elem, (arr, key) => {
        elem[key] = _.uniq(elem[key]);

        elem[key].sort((a, b) => {
          return a - b;
        });
      });
    });
  }

  if (
    !achievementTargetsCache[achievementTermsCmsId] ||
    !achievementTargetsCache[achievementTermsCmsId][targetIdx]
  ) {
    return [];
  }

  return achievementTargetsCache[achievementTermsCmsId][targetIdx];
}

// ------------------------------------------------------------------------------------------------
let cashShopPreviousIdsCache: Set<number> = undefined;
export function isCashShopPreviousId(cmsId: number): boolean {
  if (!cashShopPreviousIdsCache) {
    cashShopPreviousIdsCache = new Set<number>();
    _.forOwn(cms.CashShop, (elem) => {
      cashShopPreviousIdsCache.add(elem.previousId);
    });
  }

  return cashShopPreviousIdsCache.has(cmsId);
}

// ------------------------------------------------------------------------------------------------
// 인자로 주어진 cmsId 가 previousId 로 사용되는 지
let contributionShopPreviousIdsCache: Set<number> = undefined;
export function isContributionShopPreviousId(cmsId: number): boolean {
  if (!contributionShopPreviousIdsCache) {
    contributionShopPreviousIdsCache = new Set<number>();
    _.forOwn(cms.ContributionShop, (elem) => {
      contributionShopPreviousIdsCache.add(elem.previousId);
    });
  }

  return contributionShopPreviousIdsCache.has(cmsId);
}

// ------------------------------------------------------------------------------------------------
let taxFreePermitCashShopCmsIds: { [taxFreePermitCmsId: number]: number } = undefined;
export function getTaxFreePermitCashShopCmsId(cmsId: number): number {
  if (!taxFreePermitCashShopCmsIds) {
    taxFreePermitCashShopCmsIds = {};
    _.forOwn(cms.CashShop, (elem) => {
      if (elem.taxFreePermitId) {
        taxFreePermitCashShopCmsIds[elem.taxFreePermitId] = elem.id;
      }
    });
  }

  return taxFreePermitCashShopCmsIds[cmsId];
}

// ------------------------------------------------------------------------------------------------
let cashShopBoxRatioCache: {
  [group: number]: { cmses: CashShopBoxRatioDesc[]; totalRatio: number };
} = undefined;
export function getCashShopBoxRatio(group: number): {
  cmses: CashShopBoxRatioDesc[];
  totalRatio: number;
} {
  if (!cashShopBoxRatioCache) {
    cashShopBoxRatioCache = {};
    _.forOwn(cms.CashShopBoxRatio, (elem) => {
      if (!cashShopBoxRatioCache[elem.boxGroup]) {
        cashShopBoxRatioCache[elem.boxGroup] = {
          cmses: [],
          totalRatio: 0,
        };
      }
      cashShopBoxRatioCache[elem.boxGroup].cmses.push(elem);
      cashShopBoxRatioCache[elem.boxGroup].totalRatio += elem.boxRatio;
    });
  }

  return cashShopBoxRatioCache[group];
}

// ------------------------------------------------------------------------------------------------
let limitSalesCache: {
  [type: number]: { totalRatio: number; cmses: CashShopLimitSaleDesc[] };
};
let fixedTermLimitSalesCache: {
  [type: number]: CashShopLimitSaleDesc[];
};
export function getCashShopLimitSalesByPointType(
  salePointType: CASH_SHOP_LIMIT_SALE_POINT_TYPE,
  curTimeUtc: number
): { totalRatio: number; cmses: CashShopLimitSaleDesc[] } | undefined {
  if (!limitSalesCache || !fixedTermLimitSalesCache) {
    limitSalesCache = {};
    fixedTermLimitSalesCache = {};

    _.forOwn(cms.CashShopLimitSale, (elem) => {
      if (elem.startDate || elem.endDate) {
        if (!fixedTermLimitSalesCache[elem.cashShopLimitSalePointType]) {
          fixedTermLimitSalesCache[elem.cashShopLimitSalePointType] = [];
        }

        fixedTermLimitSalesCache[elem.cashShopLimitSalePointType].push(elem);
      } else {
        if (!limitSalesCache[elem.cashShopLimitSalePointType]) {
          limitSalesCache[elem.cashShopLimitSalePointType] = {
            cmses: [],
            totalRatio: 0,
          };
        }
        const ofType = limitSalesCache[elem.cashShopLimitSalePointType];
        ofType.cmses.push(elem);
        ofType.totalRatio += elem.cashShopLimitSaleRatio;
      }
    });
  }

  const curDate = new Date(curTimeUtc * 1000);
  const cmses = Array.from(limitSalesCache[salePointType]?.cmses ?? []);
  let totalRatio = limitSalesCache[salePointType]?.totalRatio ?? 0;
  if (
    fixedTermLimitSalesCache[salePointType] &&
    fixedTermLimitSalesCache[salePointType].length > 0
  ) {
    for (const elem of fixedTermLimitSalesCache[salePointType]) {
      if (elem.startDate && mutil.newDateByCmsDateStr(elem.startDate) > curDate) {
        continue;
      }

      if (elem.endDate && mutil.newDateByCmsDateStr(elem.endDate) < curDate) {
        continue;
      }

      cmses.push(elem);
      totalRatio += elem.cashShopLimitSaleRatio;
    }
  }
  return { cmses, totalRatio };
}

// ------------------------------------------------------------------------------------------------
let itemDiscoveryCmsIdsCache: { [itemCmsId: number]: number[] };
export function getItemDiscoveryCmsIds(itemCmsId: number): number[] {
  if (!itemDiscoveryCmsIdsCache) {
    itemDiscoveryCmsIdsCache = {};
    const discoveryCms = getDiscoveryCms();
    _.forOwn(discoveryCms, (elem) => {
      if (!elem.relateItem) {
        return;
      }
      for (const relateItem of elem.relateItem) {
        if (!itemDiscoveryCmsIdsCache[relateItem.Id]) {
          itemDiscoveryCmsIdsCache[relateItem.Id] = [];
        }
        itemDiscoveryCmsIdsCache[relateItem.Id].push(elem.id);
      }
    });
  }

  return itemDiscoveryCmsIdsCache[itemCmsId];
}

// ------------------------------------------------------------------------------------------------
let sortedDiscoveryMissionByDifficultyCache: { [buildingNpcCmsId: number]: DiscoveryMissionDesc[] };
export function getSortedDiscoveryMissionByDifficulty(
  buildingNpcCmsId: number
): DiscoveryMissionDesc[] {
  if (!sortedDiscoveryMissionByDifficultyCache) {
    // sortedDiscoveryMissionByDifficultyCache = _.values(cms.DiscoveryMission);
    // sortedDiscoveryMissionByDifficultyCache.sort((a, b) => {
    //   return b.missionDifficulty - a.missionDifficulty;
    // });

    sortedDiscoveryMissionByDifficultyCache = {};
    _.forOwn(cms.DiscoveryMission, (elem) => {
      if (!sortedDiscoveryMissionByDifficultyCache[elem.buildingNpc]) {
        sortedDiscoveryMissionByDifficultyCache[elem.buildingNpc] = [];
      }
      sortedDiscoveryMissionByDifficultyCache[elem.buildingNpc].push(elem);
    });

    _.forOwn(sortedDiscoveryMissionByDifficultyCache, (elem) => {
      elem.sort((a, b) => {
        return b.missionDifficulty - a.missionDifficulty;
      });
    });
  }

  return sortedDiscoveryMissionByDifficultyCache[buildingNpcCmsId];
}

// ------------------------------------------------------------------------------------------------
let questPassCashShopCmsIdsByQuestCmsIdCache: { [questCmsId: number]: number[] };
export function getQuestPassCashShopCmsIdsByQuestCmsId(targetQuestCmsId: number): number[] {
  if (!questPassCashShopCmsIdsByQuestCmsIdCache) {
    questPassCashShopCmsIdsByQuestCmsIdCache = {};
    _.forOwn(cms.CashShop, (elem) => {
      if (elem.questPassId) {
        const questPassCms = cms.QuestPass[elem.questPassId];
        for (const questCmsId of questPassCms.questId) {
          if (!questPassCashShopCmsIdsByQuestCmsIdCache[questCmsId]) {
            questPassCashShopCmsIdsByQuestCmsIdCache[questCmsId] = [];
          }
          questPassCashShopCmsIdsByQuestCmsIdCache[questCmsId].push(elem.id);
        }
      }
    });
  }
  return questPassCashShopCmsIdsByQuestCmsIdCache[targetQuestCmsId];
}

// ------------------------------------------------------------------------------------------------
let questPassCashShopCmsIdByQuestPassCmsIdCache: { [questPassCmsId: number]: number };
export function getQuestPassCashShopCmsIdByQuestPassCmsId(questPassCmsId: number): number {
  if (!questPassCashShopCmsIdByQuestPassCmsIdCache) {
    questPassCashShopCmsIdByQuestPassCmsIdCache = {};
    _.forOwn(cms.CashShop, (elem) => {
      if (elem.questPassId) {
        questPassCashShopCmsIdByQuestPassCmsIdCache[elem.questPassId] = elem.id;
      }
    });
  }

  return questPassCashShopCmsIdByQuestPassCmsIdCache[questPassCmsId];
}

// ------------------------------------------------------------------------------------------------
let cashShopCmsesByProductCodeCache: { [productCode: string]: CashShopDesc[] };
export function getCashShopCmsByProductCode(productCode: string): CashShopDesc {
  if (!cashShopCmsesByProductCodeCache) {
    const cache: typeof cashShopCmsesByProductCodeCache = {};

    // 스토어 상품 코드는 유니크하다고 가정한 코드.
    const DoCache = (cashShopCms: CashShopDesc, cmsProductCode: string) => {
      if (cmsProductCode === undefined) {
        return;
      }
      if (!cache[cmsProductCode]) {
        cache[cmsProductCode] = [];
      }
      cache[cmsProductCode].push(cashShopCms);
    };

    _.forOwn(cms.CashShop, (elem) => {
      DoCache(elem, elem.productCodeGoogle);
      DoCache(elem, elem.productCodeApple);
      DoCache(elem, elem.productCodeFloor);
      DoCache(elem, elem.productCodeSteam);

      DoCache(elem, elem.consecutiveProductCodeGoogle);
      DoCache(elem, elem.consecutiveProductCodeApple);
      DoCache(elem, elem.consecutiveProductCodeFloor);
      DoCache(elem, elem.consecutiveProductCodeSteam);
    });

    cashShopCmsesByProductCodeCache = cache;
  }

  const cashShopCmses = cashShopCmsesByProductCodeCache[productCode] || [];
  if (cashShopCmses.length > 1) {
    // TODO Validation
    assert.fail(`CMS.CashShop.productCode duplicated. ${productCode}`);
  }
  return cashShopCmses[0];
}

// ------------------------------------------------------------------------------------------------
let selectableNationsCache: NationDesc[];
export function getSelectableNations(): NationDesc[] {
  if (!selectableNationsCache) {
    selectableNationsCache = [];
    _.forOwn(cms.Nation, (elem) => {
      if (elem.canSelect) {
        selectableNationsCache.push(elem);
      }
    });
  }
  return selectableNationsCache;
}

// ------------------------------------------------------------------------------------------------
let shopCmsIdsByGroupCache: { [group: number]: number[] };
export function getShopCmsIdsByGroup(group: number): number[] {
  if (!shopCmsIdsByGroupCache) {
    shopCmsIdsByGroupCache = {};
    _.forOwn(cms.Shop, (elem) => {
      if (!shopCmsIdsByGroupCache[elem.group]) {
        shopCmsIdsByGroupCache[elem.group] = [];
      }
      shopCmsIdsByGroupCache[elem.group].push(elem.id);
    });
  }

  return shopCmsIdsByGroupCache[group];
}

// ------------------------------------------------------------------------------------------------
let regionItemsCache: { [regionCmsId: number]: Set<number> } = {};
export function pickRegionRandomItemReward(regionCmsId: number): number {
  if (!regionItemsCache[regionCmsId]) {
    regionItemsCache[regionCmsId] = new Set<number>();
    const townCmsIds = getRegionTownCmsIds(regionCmsId);
    if (townCmsIds) {
      for (const townCmsId of townCmsIds) {
        if (!cms.Town[townCmsId].shopGroup) {
          continue;
        }
        for (const group of cms.Town[townCmsId].shopGroup) {
          const shopCmsIds = getShopCmsIdsByGroup(group);
          if (!shopCmsIds) {
            continue;
          }
          for (const shopCmsId of shopCmsIds) {
            regionItemsCache[regionCmsId].add(cms.Shop[shopCmsId].itemId);
          }
        }
      }
    }
  }

  if (regionItemsCache[regionCmsId].size === 0) {
    return null;
  }

  const rnd = Math.floor(Math.random() * regionItemsCache[regionCmsId].size);
  return Array.from(regionItemsCache[regionCmsId])[rnd];
}

// ------------------------------------------------------------------------------------------------
let regionTradeGoodsCache: { [regionCmsId: number]: Set<number> } = {};
export function pickRegionRandomTradeGoodsReward(regionCmsId: number, curTimeUtc: number): number {
  if (!regionTradeGoodsCache[regionCmsId]) {
    regionTradeGoodsCache[regionCmsId] = new Set<number>();
    const townCmsIds = getRegionTownCmsIds(regionCmsId);
    if (townCmsIds) {
      for (const townCmsId of townCmsIds) {
        const tradeGoodsCmsIds = getTownSellingTradeGoodsCmsIds(townCmsId, curTimeUtc);
        for (const tradeGoodsCmsId of tradeGoodsCmsIds) {
          regionTradeGoodsCache[regionCmsId].add(tradeGoodsCmsId);
        }
      }
    }
  }

  if (regionTradeGoodsCache[regionCmsId].size === 0) {
    return null;
  }

  const rnd = Math.floor(Math.random() * regionTradeGoodsCache[regionCmsId].size);
  return Array.from(regionTradeGoodsCache[regionCmsId])[rnd];
}

// ------------------------------------------------------------------------------------------------
export function getShipCustomizingCmsName(cmsId: number): string {
  if (cms.SailPattern[cmsId]) {
    return 'SailPattern';
  }
  if (cms.SailCrest[cmsId]) {
    return 'SailCrest';
  }
  if (cms.SailPatternColor[cmsId]) {
    return 'SailPatternColor';
  }
  if (cms.ShipBodyFirstColor[cmsId]) {
    return 'ShipBodyFirstColor';
  }
  if (cms.ShipBodySecondColor[cmsId]) {
    return 'ShipBodySecondColor';
  }
}

// ------------------------------------------------------------------------------------------------
let mateAwakenCache: {
  [mateGrade: number]: {
    [jobType: number]: {
      [awakenLv: number]: MateAwakenDesc;
    };
  };
};
export function getMateAwaken(
  mateGrade: MATE_GRADE,
  jobType: JOB_TYPE,
  awakenLv: number
): MateAwakenDesc | undefined {
  if (!mateAwakenCache) {
    type Cache = typeof mateAwakenCache;

    const cache: Cache = {};
    _.forOwn(cms.MateAwaken, (elem) => {
      const duplicated = cache[elem.mateGrade]?.[elem.jobType]?.[elem.awakenLv];
      assert(!duplicated);
      _.merge<Cache, Cache>(cache, {
        [elem.mateGrade]: {
          [elem.jobType]: {
            [elem.awakenLv]: elem,
          },
        },
      });
    });
    mateAwakenCache = cache;
  }
  return mateAwakenCache[mateGrade][jobType][awakenLv];
}

// ------------------------------------------------------------------------------------------------
let tradeByTownAndTradeGoodsCmsIdCache: {
  [townCmsId: number]: { [tradeGoodsCmsId: number]: TradeDesc };
};
export function getTradeByTownAndTradeGoodsCmsId(
  townCmsId: number,
  tradeGoodsCmsId: number
): TradeDesc {
  if (!tradeByTownAndTradeGoodsCmsIdCache) {
    tradeByTownAndTradeGoodsCmsIdCache = {};
    _.forOwn(cms.Trade, (elem) => {
      const goodsCms = cms.TradeGoods[elem.tradeGoodsId];
      // if (isFilteredByCountryCode(goodsCms)) {
      //   return;
      // }
      if (!goodsCms) {
        return;
      }
      _.merge(tradeByTownAndTradeGoodsCmsIdCache, {
        [elem.townId]: {
          [elem.tradeGoodsId]: elem,
        },
      });
    });
  }

  return tradeByTownAndTradeGoodsCmsIdCache[townCmsId]
    ? tradeByTownAndTradeGoodsCmsIdCache[townCmsId][tradeGoodsCmsId]
    : undefined;
}

// ------------------------------------------------------------------------------------------------
// 항해사의 패시브 습득 과정에서 사용되는 look-up 캐시 테이블.
// 클라 CMSEx.lua 와 동일한 유사한 로직 유지.
// ------------------------------------------------------------------------------------------------
let mateLearnablePassiveElemCache: {
  [mateCmsId: number]: {
    [passiveCmsId: number]: MatePassiveElemDesc;
  };
} = {};

function _buildMateLearnablePassiveElemCache(mateCmsId: number): {
  [passiveCmsId: number]: MatePassiveElemDesc;
} {
  const cachedTable = {};
  const mateCms = cms.Mate[mateCmsId];
  const autoMatePassiveCms = cms.AutoMatePassive[mateCms.autoMatePassiveId];
  const characterCms = cms.Character[mateCms.characterId];
  const jobCms = cms.Job[characterCms.jobId];
  const autoJobPassiveCms = cms.AutoJobPassive[jobCms.autoJobPassiveId];

  for (const elem of autoMatePassiveCms.passives) {
    if (cachedTable[elem.id]) {
      mlog.error('Duplicate auto mate passive!', {
        mateCmsId,
        passiveCmsId: elem.id,
      });
      continue;
    }
    cachedTable[elem.id] = elem;
  }

  for (const elem of autoJobPassiveCms.passives) {
    if (cachedTable[elem.id]) {
      mlog.error('Duplicate auto job passive!', {
        mateCmsId,
        passiveCmsId: elem.id,
      });
      continue;
    }
    cachedTable[elem.id] = elem;
  }

  mateLearnablePassiveElemCache[mateCmsId] = cachedTable;
  return cachedTable;
}

export function getMateLearnablePassiveElemTable(mateCmsId: number): {
  [passiveCmsId: number]: MatePassiveElemDesc;
} {
  let cachedTable = mateLearnablePassiveElemCache[mateCmsId];
  if (!cachedTable) {
    cachedTable = _buildMateLearnablePassiveElemCache(mateCmsId);
  }

  return cachedTable;
}

export function getMateLearnablePassiveElem(
  mateCmsId: number,
  passiveCmsId: number
): MatePassiveElemDesc {
  let cachedTable = mateLearnablePassiveElemCache[mateCmsId];
  if (!cachedTable) {
    cachedTable = _buildMateLearnablePassiveElemCache(mateCmsId);
  }

  return cachedTable[passiveCmsId];
}

// ------------------------------------------------------------------------------------------------
let autoMateOrderCache: {
  [autoMateOrderCmsId: number]: { [orderCmsId: number]: AutoMateOrderElemDesc };
};
export function getAutoMateOrderElem(
  autoMateOrderCmsId: number,
  orderCmsId: number
): AutoMateOrderElemDesc {
  if (!autoMateOrderCache) {
    autoMateOrderCache = {};
    _.forOwn(cms.AutoMateOrder, (elem1) => {
      for (const elem2 of elem1.order) {
        _.merge<
          {
            [autoMateOrderCmsId: number]: { [orderCmsId: number]: AutoMateOrderElemDesc };
          },
          {
            [autoMateOrderCmsId: number]: { [orderCmsId: number]: AutoMateOrderElemDesc };
          }
        >(autoMateOrderCache, {
          [elem1.id]: {
            [elem2.id]: elem2,
          },
        });
      }
    });
  }

  return autoMateOrderCache[autoMateOrderCmsId]
    ? autoMateOrderCache[autoMateOrderCmsId][orderCmsId]
    : undefined;
}

// ------------------------------------------------------------------------------------------------
let attendanceCache: { [group: number]: { [type: number]: { [day: number]: AttendanceDesc } } };
export function getAttendance(group: number) {
  if (!attendanceCache) {
    attendanceCache = {};
    _.forOwn(cms.Attendance, (elem) => {
      _.merge(attendanceCache, {
        [elem.group]: {
          [elem.type]: {
            [elem.day]: elem,
          },
        },
      });
    });
  }

  return attendanceCache[group];
}

let attendanceLastDayCache: { [group: number]: number } = {};
export function getAttendanceLastDay(group: number): number {
  if (!attendanceLastDayCache) {
    attendanceLastDayCache = {};
  }
  _.forOwn(cms.Attendance, (elem) => {
    if (!attendanceLastDayCache[elem.group]) {
      attendanceLastDayCache[elem.group] = elem.day;
    }

    if (elem.day > attendanceLastDayCache[elem.group]) {
      attendanceLastDayCache[elem.group] = elem.day;
    }
  });

  return attendanceLastDayCache[group];
}

// ------------------------------------------------------------------------------------------------
let revivalUserAttendanceCache: EventPageDesc[];
export function getRevivalUserAttendance(): EventPageDesc[] {
  if (!revivalUserAttendanceCache) {
    revivalUserAttendanceCache = [];
    _.forOwn(cms.EventPage, (elem) => {
      if (isFilteredByCountryCode(elem.localBitflag)) {
        return;
      }
      if (elem.type === EventPageType.ATTENDANCE_COMBACK && elem.activeDay && elem.comeBackDay) {
        revivalUserAttendanceCache.push(elem);
      }
    });
  }

  return revivalUserAttendanceCache;
}

// ------------------------------------------------------------------------------------------------
let newUserAttendanceCache: EventPageDesc[];
export function getNewUserAttendance(): EventPageDesc[] {
  if (!newUserAttendanceCache) {
    newUserAttendanceCache = [];
    _.forOwn(cms.EventPage, (elem) => {
      if (isFilteredByCountryCode(elem.localBitflag)) {
        return;
      }
      if (elem.type === EventPageType.ATTENDANCE_NEW && elem.startDate) {
        newUserAttendanceCache.push(elem);
      }
    });
  }
  return newUserAttendanceCache;
}

// ------------------------------------------------------------------------------------------------
let eventPageCacheForPageType: { [type: number]: EventPageDesc[] } = {};
export function getEventPagesForPageType(type: EventPageType) {
  if (eventPageCacheForPageType[type]) {
    return eventPageCacheForPageType[type];
  }

  eventPageCacheForPageType[type] = [];
  _.forOwn(cms.EventPage, (evtPage: EventPageDesc) => {
    if (evtPage.type === type) {
      eventPageCacheForPageType[type].push(evtPage);
    }
  });
  return eventPageCacheForPageType[type];
}
// 제독의 mateSet에 해당되는 mate cms ids
let mateCmsIdsOfAdmiralMateSetCache: { [mateCmsId: number]: number[] };
export function getMateCmsIdsOfAdmiralMateSet(mateCmsId: number): number[] {
  if (!mateCmsIdsOfAdmiralMateSetCache) {
    mateCmsIdsOfAdmiralMateSetCache = {};
    _.forOwn(cms.Mate, (elem) => {
      if (!elem.commanderLink) {
        return;
      }
      if (!mateCmsIdsOfAdmiralMateSetCache[elem.commanderLink]) {
        mateCmsIdsOfAdmiralMateSetCache[elem.commanderLink] = [];
      }
      mateCmsIdsOfAdmiralMateSetCache[elem.commanderLink].push(elem.id);
    });
  }

  return mateCmsIdsOfAdmiralMateSetCache[mateCmsId]
    ? mateCmsIdsOfAdmiralMateSetCache[mateCmsId]
    : [];
}

// ------------------------------------------------------------------------------------------------
let eventPageCache: { [groupId: number]: EventMissionDesc[] } = {};
export function getEventMissionCmses(groupId: number): EventMissionDesc[] {
  if (eventPageCache[groupId]) {
    return eventPageCache[groupId];
  }

  eventPageCache[groupId] = [];
  _.forOwn(cms.EventMission, (evtMission: EventMissionDesc) => {
    if (evtMission.group === groupId) {
      eventPageCache[groupId].push(evtMission);
    }
  });
  return eventPageCache[groupId];
}

// ------------------------------------------------------------------------------------------------
export function getEventMissionCount(groupId: number): number {
  const eventMissionCmses: EventMissionDesc[] = getEventMissionCmses(groupId);
  if (eventMissionCmses) {
    return eventMissionCmses.length;
  }
  return 0;
}

// ------------------------------------------------------------------------------------------------
// 이벤트 기간 체크
export function isEventPageExpired(curTimeUtc: number, eventPageCms: EventPageDesc): boolean {
  const curDate = new Date(curTimeUtc * 1000);
  const startDate = mutil.newDateByCmsDateStr(eventPageCms.startDate);
  const endDate = mutil.newDateByCmsDateStr(eventPageCms.endDate);

  if (startDate && startDate > curDate) {
    return true;
  }
  if (endDate && endDate < curDate) {
    return true;
  }
  return false;
}

// ------------------------------------------------------------------------------------------------
let defaultMateEquipmentColorsCache: { [offset: number]: number };
export function getDefaultMateEquipmentColors(): { [offset: number]: number } {
  if (!defaultMateEquipmentColorsCache) {
    defaultMateEquipmentColorsCache = {};

    _.forOwn(cms.DefaultEquipDyeColor, (elem) => {
      const id = elem.defaultEquipDyeColorId;
      const offset = Math.floor(id / 32);
      if (!defaultMateEquipmentColorsCache[offset]) {
        defaultMateEquipmentColorsCache[offset] = 0;
      }
      defaultMateEquipmentColorsCache[offset] =
        (defaultMateEquipmentColorsCache[offset] | (1 << id % 32)) >>> 0;
    });
  }

  return defaultMateEquipmentColorsCache;
}

// ------------------------------------------------------------------------------------------------
const equipDyeRandomColorCache: number[] = [];
export function getRandomEquipDyeColors(cEquipCms: CEquipDesc, charGender: GENDER): number[] {
  if (equipDyeRandomColorCache.length === 0) {
    _.forOwn(cms.EquipDyeColor, (elem) => {
      if (elem.isView) {
        equipDyeRandomColorCache.push(elem.color);
      }
    });
  }
  let equipCms: any;
  let randomColors: number[] = [];

  // itemShapedsId 인덱스 참조 룰
  // cEquipCms.gender 가 Man, Woman 경우 : 0을 index로 사용 (default)
  // cEquipCms.gender 가 Common일 경우 : characterCms.gender를 index로 사용
  let itemShapedsIdIndex: number = 0;
  if (cEquipCms.gender === GENDER.COMMON) {
    itemShapedsIdIndex = charGender;
  }

  // emptyColor => 사용하지 않은 염색값
  const emptyColor = 0xffffff;
  switch (cEquipCms.type) {
    case CEQUIP_TYPE.BODY:
      equipCms = cms.BodyShape[cEquipCms.itemShapedsId[itemShapedsIdIndex]];
      randomColors.push(equipCms.color1 === emptyColor ? null : _.sample(equipDyeRandomColorCache));
      randomColors.push(equipCms.color2 === emptyColor ? null : _.sample(equipDyeRandomColorCache));
      break;
    case CEQUIP_TYPE.HAT:
      equipCms = cms.HatShape[cEquipCms.itemShapedsId[itemShapedsIdIndex]];
      randomColors.push(equipCms.color1 === emptyColor ? null : _.sample(equipDyeRandomColorCache));
      randomColors.push(equipCms.color2 === emptyColor ? null : _.sample(equipDyeRandomColorCache));
      break;
  }
  return randomColors;
}

// ------------------------------------------------------------------------------------------------
export function convertRewardFixedToGLogRewardData(rewardFixedCmsId: number): RewardData[] {
  const ret: RewardData[] = [];
  const rewardCms = cms.RewardFixed[rewardFixedCmsId];
  if (!rewardCms || rewardCms.rewardFixed.length === 0) {
    return ret;
  }
  for (let idx = 0; idx < rewardCms.rewardFixed.length; idx++) {
    const elem = rewardCms.rewardFixed[idx];
    ret.push({
      type: REWARD_TYPE[elem.Type],
      id: elem.Id === undefined ? null : elem.Id,
      uid: null,
      amt: elem.Quantity,
    });
  }
  return ret;
}

// ------------------------------------------------------------------------------------------------
// WorldPassive/BattlePassive 의 그룹/스킬레벨 look-up 테이블.
let passiveByGroupAndLevel: {
  world: {
    [groupNo: number]: {
      [level: number]: WorldPassiveDesc;
    };
  };
  battle: {
    [groupNo: number]: {
      [level: number]: BattlePassiveDesc;
    };
  };
};

// WorldPassive/BattlePassive 그룹/스킬레벨 look-up 테이블 구성.
function _buildPassiveByGroupAndLevel() {
  passiveByGroupAndLevel = {
    world: {},
    battle: {},
  };

  const worldPassiveByGroupAndLevel = passiveByGroupAndLevel.world;
  _.forOwn(cms.WorldPassive, (worldPassiveCms: WorldPassiveDesc) => {
    const groupNo = worldPassiveCms.groupNo;
    const skillLevel = worldPassiveCms.skillLevel;

    if (!worldPassiveByGroupAndLevel[groupNo]) {
      const targetType = worldPassiveCms.worldTargetType;
      assert(targetType === WorldTargetType.FLEET || targetType === WorldTargetType.SHIP);

      worldPassiveByGroupAndLevel[groupNo] = {};
    }

    worldPassiveByGroupAndLevel[groupNo][skillLevel] = worldPassiveCms;
  });

  const battlePassiveByGroupAndLevel = passiveByGroupAndLevel.battle;
  _.forOwn(cms.BattlePassive, (battlePassiveCms: BattlePassiveDesc) => {
    const groupNo = battlePassiveCms.groupNo;
    const skillLevel = battlePassiveCms.skillLevel;

    if (!battlePassiveByGroupAndLevel[groupNo]) {
      battlePassiveByGroupAndLevel[groupNo] = {};
    }

    battlePassiveByGroupAndLevel[groupNo][skillLevel] = battlePassiveCms;
  });
}

// ------------------------------------------------------------------------------------------------
// WorldPassive/BattlePassive 를 그룹/스킬레벨로 가져오기.
export function getPassiveByGroupAndLevel(
  cmsId: number,
  groupNo: number,
  skillLevel: number
): WorldPassiveDesc | BattlePassiveDesc {
  if (!passiveByGroupAndLevel) {
    _buildPassiveByGroupAndLevel();
  }

  let passiveDesc: WorldPassiveDesc | BattlePassiveDesc;
  let passiveDescByGroup;
  if (cms.WorldPassive[cmsId]) {
    // 월드 패시브
    passiveDescByGroup = passiveByGroupAndLevel.world[groupNo];
  } else {
    // 전투 패시브
    passiveDescByGroup = passiveByGroupAndLevel.battle[groupNo];
  }

  passiveDesc = passiveDescByGroup[skillLevel];
  if (!passiveDesc) {
    mlog.debug('No passive by group/level. So find max possible skill level. ', {
      cmsId,
      groupNo,
      skillLevel,
    });

    // 해당 스킬레벨이 없는 경우, "최대로 가능한" 패시브를 반환.
    const maxShipSkillLevel = CMSConst.get('MaxShipSkillLevel');
    for (let i = 1; i <= maxShipSkillLevel; ++i) {
      if (!passiveDescByGroup[i]) {
        break;
      }

      passiveDesc = passiveDescByGroup[i];
    }
  }

  if (!passiveDesc) {
    mlog.error('No passive by group/level.', {
      cmsId,
      groupNo,
      skillLevel,
    });
  }

  return passiveDesc;
}

// ------------------------------------------------------------------------------------------------
export function getPassiveTargetTypeByGroupId(groupNo: number): WorldTargetType {
  if (!passiveByGroupAndLevel) {
    _buildPassiveByGroupAndLevel();
  }

  // worldPassive, battlePassive 간에 groupNo는 안겹친다고 전달 받음
  const passives = passiveByGroupAndLevel.world[groupNo]
    ? passiveByGroupAndLevel.world[groupNo]
    : passiveByGroupAndLevel.battle[groupNo];
  assert(passives);

  const arrPassives = Object.values(passives);
  assert(arrPassives.length > 0);

  const pci = getPassiveCommonInfo(arrPassives[0].id);
  return pci.targetType;
}

// ------------------------------------------------------------------------------------------------
// WorldPassive / BattlePassive 의 공통 필드.
export interface PassiveCommonInfo {
  cmsId: number;
  groupNo: number;
  skillLevel: number;
  targetType: WorldTargetType;
}

// ------------------------------------------------------------------------------------------------
// WorldPassive/BattlePassive 의 스킬/그룹 가져오기.
export function getPassiveCommonInfo(cmsId: number): PassiveCommonInfo {
  const worldPassiveCms = cms.WorldPassive[cmsId];
  if (worldPassiveCms) {
    return {
      cmsId,
      groupNo: worldPassiveCms.groupNo,
      skillLevel: worldPassiveCms.skillLevel,
      targetType: worldPassiveCms.worldTargetType,
    };
  }

  const battlePassiveCms = cms.BattlePassive[cmsId];
  if (battlePassiveCms) {
    return {
      cmsId,
      groupNo: battlePassiveCms.groupNo,
      skillLevel: battlePassiveCms.skillLevel,
      targetType: WorldTargetType.SHIP,
    };
  }

  mlog.error('Invalid passive CMS ID.', { cmsId });
  return undefined;
}

// ------------------------------------------------------------------------------------------------
let shipBlueprintSlotByIndexCache: {
  [shipBlueprintCmsId: number]: { [index: number]: ShipBlueprintSlotElemDesc };
};
export function getShipBlueprintSlotByIndex(
  shipBlueprintCmsId: number,
  index: number
): ShipBlueprintSlotElemDesc {
  if (!shipBlueprintSlotByIndexCache) {
    shipBlueprintSlotByIndexCache = {};
    _.forOwn(cms.ShipBlueprint, (shipBlueprintCms) => {
      for (const slotCms of shipBlueprintCms.shipSlot) {
        _.merge<
          { [shipBlueprintCmsId: number]: { [index: number]: ShipBlueprintSlotElemDesc } },
          { [shipBlueprintCmsId: number]: { [index: number]: ShipBlueprintSlotElemDesc } }
        >(shipBlueprintSlotByIndexCache, {
          [shipBlueprintCms.id]: {
            [slotCms.Index]: slotCms,
          },
        });
      }
    });
  }

  return shipBlueprintSlotByIndexCache[shipBlueprintCmsId]
    ? shipBlueprintSlotByIndexCache[shipBlueprintCmsId][index]
    : undefined;
}

// ------------------------------------------------------------------------------------------------
let shipEnchantCache: { [shipSize: number]: ShipEnchantDesc };
export function getShipEnchant(shipTier: number): ShipEnchantDesc {
  if (!shipEnchantCache) {
    shipEnchantCache = {};
    _.forOwn(cms.ShipEnchant, (elem) => {
      shipEnchantCache[elem.shipTier] = elem;
    });
  }

  return shipEnchantCache[shipTier];
}

// ------------------------------------------------------------------------------------------------
export interface ShipEnchantStatRatioEx {
  shipEnchantStatRatio: ShipEnchantStatRatioDesc[];
  totalRatio: number;
  totalRatioOfElem: number[];
}
let shipEnchantStatRatioExtensions: { [shipTier: number]: ShipEnchantStatRatioEx };
export function getShipEnchantStatRatioEx(shipTier: number): ShipEnchantStatRatioEx {
  if (!shipEnchantStatRatioExtensions) {
    shipEnchantStatRatioExtensions = {};

    _.forOwn(cms.ShipEnchantStatRatio, (shipEnchantStatRatioCms) => {
      if (!shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier]) {
        shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier] = {
          shipEnchantStatRatio: [],
          totalRatio: 0,
          totalRatioOfElem: [],
        };
      }
      shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier].shipEnchantStatRatio.push(
        shipEnchantStatRatioCms
      );
      shipEnchantStatRatioExtensions[shipEnchantStatRatioCms.shipTier].totalRatio +=
        shipEnchantStatRatioCms.enchantStatOperatorRatio;
    });

    _.forOwn(shipEnchantStatRatioExtensions, (elem1) => {
      for (const elem2 of elem1.shipEnchantStatRatio) {
        let sumRatio = 0;
        for (const elem3 of elem2.enchantVal) {
          sumRatio += elem3.Ratio;
        }
        elem1.totalRatioOfElem.push(sumRatio);
      }
    });
  }

  return shipEnchantStatRatioExtensions[shipTier];
}

// ------------------------------------------------------------------------------------------------
const townShopCmsIdsCache: { [townCmsId: number]: Set<number> } = {};
export function getTownShopCmsIds(townCmsId: number): Set<number> {
  if (!townShopCmsIdsCache[townCmsId]) {
    const townCms = cms.Town[townCmsId];
    if (!townCms) {
      return undefined;
    }
    const groups = townCms.shopGroup;
    if (!groups) {
      return undefined;
    }
    townShopCmsIdsCache[townCmsId] = new Set<number>();
    for (const group of groups) {
      const cmsIds = getShopCmsIdsByGroup(group);
      for (const cmsId of cmsIds) {
        townShopCmsIdsCache[townCmsId].add(cmsId);
      }
    }
  }

  return townShopCmsIdsCache[townCmsId];
}

// ------------------------------------------------------------------------------------------------
let hotTimeBonusCache: { [group: number]: HotTimeBonusDesc[] };
export function getCurHotTimeBonus(curTimeUtc: number): HotTimeBonusDesc[] {
  if (!hotTimeBonusCache) {
    hotTimeBonusCache = {};
    _.forOwn(cms.HotTimeBonus, (elem) => {
      if (!hotTimeBonusCache[elem.group]) {
        hotTimeBonusCache[elem.group] = [];
      }
      hotTimeBonusCache[elem.group].push(elem);
    });

    _.forOwn(hotTimeBonusCache, (elem) => {
      elem.sort((a, b) => {
        return a.startHour - b.startHour;
      });
    });
  }

  for (const elem of Object.values(hotTimeBonusCache)) {
    const startTime = mutil.newDateByCmsDateStr(elem[0].eventStartDate).getTime() / 1000;
    const endTime = mutil.newDateByCmsDateStr(elem[0].eventEndDate).getTime() / 1000;
    if (curTimeUtc >= startTime && curTimeUtc <= endTime + cms.Const.OverHotTime.value) {
      return elem;
    }
  }

  return undefined;
}

// ------------------------------------------------------------------------------------------------
export function convertRewardFixedToCustomAttachmentStr(
  rewardFixedCmsId: number,
  bIsAccum: boolean,
  curTimeUtc: number
): string {
  const rewardFixedCms = cms.RewardFixed[rewardFixedCmsId];
  if (!rewardFixedCms || !rewardFixedCms.rewardFixed || rewardFixedCms.rewardFixed.length === 0) {
    return null;
  }
  return convertRewardFixedElemsToCustomAttachmentStr(
    rewardFixedCms.rewardFixed,
    bIsAccum,
    curTimeUtc
  );
}

// ------------------------------------------------------------------------------------------------
export function convertRewardFixedElemsToCustomAttachmentStr(
  rewardFixedElems: RewardFixedElemDesc[],
  bIsAccum: boolean,
  curTimeUtc: number
): string {
  const customAttachment = [];
  _.forEach(rewardFixedElems, (elem) => {
    const extra: RewardCmsElemCommonExtra = { isAccum: bIsAccum ? 1 : 0, isBound: 1 };
    if (elem.Type === REWARD_TYPE.MATE_EQUIP) {
      const cEquipCms = cms.CEquip[elem.Id];
      (extra as RewardCmsElemMateEquipmentExtra).expireTimeUtc =
        cEquipCms.expireType === EXPIRE_TYPE.PROVIDE
          ? curTimeUtc + cEquipCms.expireTime
          : undefined;
    } else if (elem.Type === REWARD_TYPE.SHIP_SLOT_ITEM) {
      const shipSlotItemCms = cms.ShipSlot[elem.Id];
      (extra as RewardCmsElemShipSlotItemExtra).expireTimeUtc =
        shipSlotItemCms.expireType === EXPIRE_TYPE.PROVIDE
          ? curTimeUtc + shipSlotItemCms.expireTime
          : undefined;
    }

    customAttachment.push({
      Id: elem.Id,
      Type: elem.Type,
      Quantity: elem.Quantity,
      Extra: JSON.stringify(extra),
    });
  });

  return JSON.stringify(customAttachment);
}

// ------------------------------------------------------------------------------------------------
let tradeGoodsMarketPriceVolumeBasesCache: { [cmsId: number]: number };
export function getTradeGoodsMarketPriceVolumeBases(): { [cmsId: number]: number } {
  if (!tradeGoodsMarketPriceVolumeBasesCache) {
    tradeGoodsMarketPriceVolumeBasesCache = {};
    // Note: 이 함수는 모든 TradeGoods를 포함해야 하므로 필터링하지 않습니다
    _.forOwn(cms.TradeGoods, (elem) => {
      tradeGoodsMarketPriceVolumeBasesCache[elem.id] = elem.marketPriceVolumeBase;
    });
  }

  return tradeGoodsMarketPriceVolumeBasesCache;
}

// ------------------------------------------------------------------------------------------------
let tradeGoodsPricePercentClampCache: { [tradeGoodsCmsId: number]: number[] };
export function getTradeGoodsPricePercentClamps() {
  if (!tradeGoodsPricePercentClampCache) {
    tradeGoodsPricePercentClampCache = {};
    // Note: 이 함수는 모든 TradeGoods를 포함해야 하므로 필터링하지 않습니다
    _.forOwn(cms.TradeGoods, (elem) => {
      tradeGoodsPricePercentClampCache[elem.id] = [
        elem.marketPriceBottomPercent,
        elem.marketPriceTopPercent,
      ];
    });
  }

  return tradeGoodsPricePercentClampCache;
}

// ------------------------------------------------------------------------------------------------
let defaultNationPowerCache;
export function getDefaultNationPower(nationCmsId: number): number {
  const targetNationCms = cms.Nation[nationCmsId];
  if (!targetNationCms || !targetNationCms.canSelect) {
    return 0;
  }

  if (!defaultNationPowerCache) {
    defaultNationPowerCache = {};

    const developmentPerNationPower = cms.Const.DevelopmentPerNationPower.value;
    const townNations: { [townCmsId: string]: number } = {};
    const townDevelopments: { [townCmsId: string]: number } = {};

    _.forOwn(cms.Town, (townCms) => {
      const townNationCms = cms.Nation[nationCmsId];
      if (!townNationCms.canSelect) {
        return;
      }

      townNations[townCms.id] = townCms.nationId;
      // 각 마을 기본 발전도는 1이라 가정한다. 이 함수는 디비 리셋 후 발전도 갱신이 아직 되지 않은 시점에만 사용되기 때문에
      townDevelopments[townCms.id] = 3;
    });

    const nationTowns = _.groupBy(Object.keys(townNations), (townKey) => townNations[townKey]);

    _.forOwn(cms.Nation, (nationCms) => {
      if (!nationCms.canSelect) {
        return;
      }

      if (!nationTowns[nationCms.id]) {
        defaultNationPowerCache[nationCms.id] = 0;
      } else {
        const numOfTown = nationTowns[nationCms.id].length;
        const developments = _.sumBy(
          nationTowns[nationCms.id].map((townKey) => townDevelopments[townKey])
        );
        defaultNationPowerCache[nationCms.id] =
          numOfTown + Math.floor(developments / developmentPerNationPower);
      }
    });
  }

  return defaultNationPowerCache[nationCmsId] ? defaultNationPowerCache[nationCmsId] : 0;
}

// --------------------------------------------------------------------------
// 인연 연대기 퀘스트별 태생 효과가 변경되는 항해사 look-up.
// --------------------------------------------------------------------------
let mateCmsIdsByFixedPassiveQuestId: {
  [questCmsId: number]: number[];
};

export function getMateCmsIdsByAutoFixedPassiveClearQuestId(questCmsId: number): number[] {
  // 캐시가 없으면 만든다.
  if (!mateCmsIdsByFixedPassiveQuestId) {
    mateCmsIdsByFixedPassiveQuestId = {};
    _.forOwn(cms.Mate, (mateCms: MateDesc) => {
      const autoMatePassivedFixedCms = cms.AutoMatePassiveFixed[mateCms.autoMatePassiveFixedId];
      if (!autoMatePassivedFixedCms) {
        return;
      }

      if (!autoMatePassivedFixedCms.clearQuestId) {
        return;
      }

      let mateCmsIds = mateCmsIdsByFixedPassiveQuestId[autoMatePassivedFixedCms.clearQuestId];
      if (!mateCmsIds) {
        mateCmsIds = [];
        mateCmsIdsByFixedPassiveQuestId[autoMatePassivedFixedCms.clearQuestId] = mateCmsIds;
      }

      mateCmsIds.push(mateCms.id);
    });
  }

  return mateCmsIdsByFixedPassiveQuestId[questCmsId];
}

// --------------------------------------------------------------------------
// 인연 연대기 퀘스트별 인물, 직업 효과가 변경되는 항해사 look-up.
// --------------------------------------------------------------------------
let mateCmsIdsByAutoPassiveClearQuestId: {
  [questCmsId: number]: number[];
};

export function getMateCmsIdsByAutoPassiveClearQuestId(questCmsId: number): number[] {
  // 캐시가 없으면 만든다.
  if (!mateCmsIdsByAutoPassiveClearQuestId) {
    mateCmsIdsByAutoPassiveClearQuestId = {};
    _.forOwn(cms.Mate, (mateCms: MateDesc) => {
      const autoMatePassiveCms = cms.AutoMatePassive[mateCms.autoMatePassiveId];
      if (!autoMatePassiveCms) {
        return;
      }

      if (!autoMatePassiveCms.clearQuestId) {
        return;
      }

      let mateCmsIds = mateCmsIdsByAutoPassiveClearQuestId[autoMatePassiveCms.clearQuestId];
      if (!mateCmsIds) {
        mateCmsIds = [];
        mateCmsIdsByAutoPassiveClearQuestId[autoMatePassiveCms.clearQuestId] = mateCmsIds;
      }

      mateCmsIds.push(mateCms.id);
    });
  }

  return mateCmsIdsByAutoPassiveClearQuestId[questCmsId];
}

// --------------------------------------------------------------------------
// CMS.RewardDropPool, groupId별로 분리
// --------------------------------------------------------------------------
let groupedRewardDropPoolCache: {
  [groupId: number]: {
    sumWeight: number;
    list: RewardDropPoolDesc[];
  };
};
let defaultGroupedRewardDropPoolCache: {
  sumWeight: number;
  list: RewardDropPoolDesc[];
};

/**
 * https://wiki.line.games/pages/viewpage.action?pageId=96784541 적용되어 있음
 * RewardAndPaymentSpec::pickFromRewardCms 이외의 곳에서 사용 시 주의 바람
 * @param rewardGroupId CMS.Reward.reward.GroupId
 * @returns
 * list: group에 속한 CMS.RewardDropPool 목록
 * sumWeight: group에 속한 CMS.RewardDropPool.rewardWeight 총합, //*디버그 용도
 */
export function getRewardDropPoolDataInGroup(rewardGroupId: number) {
  if (!groupedRewardDropPoolCache) {
    groupedRewardDropPoolCache = {};
    _.forOwn(cms.RewardDropPool, (elem) => {
      const r = elem.rewardElem;
      if (r.Type === REWARD_TYPE.SHIP) {
        const shipCms = cms.Ship[r.Id];
        if (!shipCms) {
          return;
        }
        if (isFilteredByCountryCode(shipCms.localBitflag)) {
          return;
        }
      } else if (r.Type === REWARD_TYPE.ITEM) {
        const itemCms = cms.Item[r.Id];
        if (!itemCms) {
          return;
        }
        if (itemCms.type === ITEM_TYPE.BLUEPRINT) {
          // 공용도면은 선박이 될수 없어 검사를 안한다
          const bpCms = getShipBlueprintCmsFromMaterialBpId(r.Id);
          if (bpCms) {
            const shipCms = cms.Ship[bpCms.shipId];
            if (shipCms && isFilteredByCountryCode(shipCms.localBitflag)) {
              return;
            }
          }
        } else if (itemCms.type === ITEM_TYPE.PIECE) {
          const mateCms = getMateCmsFromAwakenPieceItem(r.Id);
          if (mateCms && isFilteredByCountryCode(mateCms.localBitFlag)) {
            return;
          }
        }
      }

      if (!groupedRewardDropPoolCache[elem.groupId]) {
        groupedRewardDropPoolCache[elem.groupId] = {
          list: [elem],
          sumWeight: elem.rewardWeight,
        };
      } else {
        groupedRewardDropPoolCache[elem.groupId].list.push(elem);
        groupedRewardDropPoolCache[elem.groupId].sumWeight += elem.rewardWeight;
      }
    });
  }

  if (!groupedRewardDropPoolCache[rewardGroupId]) {
    if (!defaultGroupedRewardDropPoolCache) {
      const defaultRewardDropPool = cms.RewardDropPool[cms.Const.DefaultRewardDropPool.value];
      defaultGroupedRewardDropPoolCache = {
        list: [defaultRewardDropPool],
        sumWeight: defaultRewardDropPool.rewardWeight,
      };
    }
    return defaultGroupedRewardDropPoolCache;
  }
  return groupedRewardDropPoolCache[rewardGroupId];
}

// --------------------------------------------------------------------------
// CMS.Item.id(CMS.Mate.pieceId) -> CMS.Mate
// 어떤 항해사의 계약서인지 알아낸다.
// CMS.Mate.piece 중복되는 row 가 없다고 한다. Validation: UWO-12395
// --------------------------------------------------------------------------
let mateCmsByPieceIdCache: { [itemCmsId: number]: MateDesc };
export function getMateCmsFromAwakenPieceItem(itemCmsId: number) {
  if (!mateCmsByPieceIdCache) {
    const byPieceId: typeof mateCmsByPieceIdCache = {};

    _.forOwn(cms.Mate, (elem) => {
      if (elem.pieceId === undefined) {
        return;
      }
      byPieceId[elem.pieceId] = elem;
    });

    mateCmsByPieceIdCache = byPieceId;
  }
  return mateCmsByPieceIdCache[itemCmsId];
}

// --------------------------------------------------------------------------
// CMS.Item.id(CMS.ShipBlueprint.materialBpId) -> CMS.ShipBlueprint
// 어떤 설계도의 materialBpId 인지 알아낸다.
// CMS.ShipBlueprint.materialBpId 는 중복되는 row 가 없다고 한다. Validation: UWO-16398
// --------------------------------------------------------------------------
let shipBluprintCmsByMaterialBpIdCache: { [itemCmsId: number]: ShipBlueprintDesc };
export function getShipBlueprintCmsFromMaterialBpId(itemCmsId: number) {
  if (!shipBluprintCmsByMaterialBpIdCache) {
    const byMaterialBpId: typeof shipBluprintCmsByMaterialBpIdCache = {};

    _.forOwn(cms.ShipBlueprint, (elem) => {
      if (elem.materialBpId === undefined) {
        return;
      }
      byMaterialBpId[elem.materialBpId] = elem;
    });

    shipBluprintCmsByMaterialBpIdCache = byMaterialBpId;
  }
  return shipBluprintCmsByMaterialBpIdCache[itemCmsId];
}

// 동일한 친밀도 레벨에서 추가가능한 값을 얻기.
export function addableIntimacyWithinSameLevel(curIntimacy, input): number {
  let max = 0;
  if (curIntimacy <= cms.Const.PubStaffLv1MAX.value) {
    max = cms.Const.PubStaffLv1MAX.value;
  } else if (curIntimacy <= cms.Const.PubStaffLv2MAX.value) {
    max = cms.Const.PubStaffLv2MAX.value;
  } else if (curIntimacy <= cms.Const.PubStaffLv3MAX.value) {
    max = cms.Const.PubStaffLv3MAX.value;
  } else {
    return input;
  }

  // 현재 레벨에서 최대 추가 가능한 값.
  const maxAddable = max - curIntimacy;

  return Math.min(maxAddable, input);
}

export function getIntimacyLevelByIntimacyPoint(curIntimacy): number {
  let lv = 1;
  while (true) {
    let key = `PubStaffLv${lv}MAX`;
    if (!cms.Const[key]) {
      break;
    }
    if (curIntimacy <= cms.Const[key].value) {
      return lv;
    }
    lv++;
  }

  return lv;
}

// 친밀도 변화량 %구하기.
export function getChangedIntimacyPctOfCurLevel(intimacy: number, changeVal: number): number {
  const curLv = getIntimacyLevelByIntimacyPoint(intimacy);
  let key = `PubStaffLv${curLv}MAX`;
  if (!cms.Const[key]) {
    return 0;
  }

  const endPoint = cms.Const[key].value;

  let beginPoint = 1;
  if (curLv > 1) {
    key = `PubStaffLv${curLv - 1}MAX`;
    if (!cms.Const[key]) {
      return 0;
    }
    beginPoint = cms.Const[key].value + 1;
  }
  const curPct = ((intimacy - beginPoint) / (endPoint - beginPoint)) * 100;

  const changedPct = Math.floor((changeVal / (endPoint - beginPoint)) * 100);
  return changedPct;
}

// 최대 레벨까지의 친밀도% 값 구하기.
export function getIntimacyPctOfMaxLevel(curIntimacy: number): number {
  let lv = 1;
  let maxLvPoint = 1;
  while (true) {
    let key = `PubStaffLv${lv}MAX`;
    if (!cms.Const[key]) {
      break;
    }
    maxLvPoint = cms.Const[key].value;
    lv++;
  }

  const pct = Math.floor((curIntimacy / maxLvPoint) * 100);
  return pct;
}

/**
 * 여관-고용 에 등장하는(아직 고용되지 않은) 항해사들의 친밀도 등급을 구함.
 */
export function getUnemployedMateIntimacyGrade(intimacy: number): number {
  const constCms = cms.Const;
  const maxIntimacyGrade = constCms.MateRecruitingClosenessCountMax.value;
  for (let grade = maxIntimacyGrade; grade > 1; grade--) {
    if (constCms['MateRecruitngClosenessCount' + grade].value <= intimacy) {
      return grade;
    }
  }
  return 1;
}

// 오션서버에서 동기화에 필요한 스탯 리스트 얻기.
export function getRequiredSyncStatForOcean(): number[] {
  // 필요시 아래에 추가할 것.
  return [PASSIVE_EFFECT.ADVENTURE_ENCOUNT_RATE];
}

// cache
let shipyardBuilds: { [townCmsId: number]: ShipyardBuildDesc[] };
export function GetShipyardBuilds(shipBuildGroup: number): ShipyardBuildDesc[] {
  if (!shipyardBuilds) {
    shipyardBuilds = {};
    _.forOwn(cms.ShipyardBuild, (elem) => {
      if (!shipyardBuilds[elem.group]) {
        shipyardBuilds[elem.group] = [];
      }
      shipyardBuilds[elem.group].push(elem);
    });
  }
  return shipyardBuilds[shipBuildGroup];
}

// ------------------------------------------------------------------------------------------------
// 모의전 등급 리스트 우선 순위로 구하기
let arenaTierListByPriorityCache: ArenaTierListDesc[];
export function getArenaTierListByPriority(): ArenaTierListDesc[] {
  if (!arenaTierListByPriorityCache) {
    arenaTierListByPriorityCache = [];
    let mapByPriority: { [priority: number]: ArenaTierListDesc } = {};
    _.forOwn(cms.ArenaTierList, (elem) => {
      mapByPriority[elem.priority] = elem;
    });

    let loop = 1;
    while (mapByPriority[loop]) {
      arenaTierListByPriorityCache.push(mapByPriority[loop++]);
    }
  }

  return arenaTierListByPriorityCache;
}

export interface InitScore {
  min: number;
  max: number;
  init: number;
}

// ------------------------------------------------------------------------------------------------
// 모의전 정산 초기화 점수 구하기
// 최소 승점 구간이 배열의 가장 앞으로 오도록한다(오름차순)
let arenaInitScoresCache: InitScore[];
export function getArenaInitScores() {
  if (!arenaInitScoresCache) {
    arenaInitScoresCache = [];
    const tierListByPriority = getArenaTierListByPriority();
    for (let m = tierListByPriority.length - 1; m > 0; m--) {
      const tier = tierListByPriority[m];
      arenaInitScoresCache.push({ min: tier.scoreMin, max: tier.scoreMax, init: tier.initScore });

      // 최대값이 무한인 경우 마지막 구간이다
      if (0 === tier.scoreMax) {
        break;
      }
    }
  }
  return arenaInitScoresCache;
}

// ------------------------------------------------------------------------------------------------
// 모의전 등급정보 구하기
let arenaGradeByGradeCache: { [grade: number]: ArenaTierListDesc };
export function getArenaTierByGrade(grade: number): ArenaTierListDesc {
  if (!arenaGradeByGradeCache) {
    arenaGradeByGradeCache = {};
    _.forOwn(cms.ArenaTierList, (elem) => {
      arenaGradeByGradeCache[elem.grade] = elem;
    });
  }

  return arenaGradeByGradeCache[grade];
}

// ------------------------------------------------------------------------------------------------
// 모의전 봇 group 구하기
let arenaMatchBotByGroupCache: { [group: number]: ArenaMatchBotDesc[] };
export function getArenaMatchBotByGroup(group: number): ArenaMatchBotDesc[] {
  if (!arenaMatchBotByGroupCache) {
    arenaMatchBotByGroupCache = {};
    _.forOwn(cms.ArenaMatchBot, (elem) => {
      if (!arenaMatchBotByGroupCache[elem.group]) {
        arenaMatchBotByGroupCache[elem.group] = [];
      }
      arenaMatchBotByGroupCache[elem.group].push(elem);
    });
  }

  return arenaMatchBotByGroupCache[group];
}

// ------------------------------------------------------------------------------------------------
// 최초 선택 가능한 제독의 국가 반환
let firstAdmiralNationCmsIdsCache: number[];
export function getFirstAdmiralNationCmsIds() {
  if (!firstAdmiralNationCmsIdsCache) {
    firstAdmiralNationCmsIdsCache = [];
    _.forOwn(cms.Admiral, (elem) => {
      if (elem.isBasicAdmiral) {
        const nationCmsId = cms.Mate[elem.mateId].nationId;
        firstAdmiralNationCmsIdsCache.push(nationCmsId);
      }
    });
  }

  return firstAdmiralNationCmsIdsCache;
}

// ------------------------------------------------------------------------------------------------
// glog 를 위한 nodeidx 에 따른 chapter 반환
let questChapterByNodeIdxCache: { [questCmsId: number]: { [nodeIdx: number]: string } } = {};
export function getQuestChapterByNodeIdx(questCmsId: number, nodeIdx: number) {
  if (!questChapterByNodeIdxCache[questCmsId]) {
    questChapterByNodeIdxCache[questCmsId] = {};
    const questCms = cms.Quest[questCmsId];
    for (let i = 0; i < questCms.chapter.length; i++) {
      const questChapterElem = questCms.chapter[i];
      const chapterNodeIdx = questChapterElem.NodeIdx;
      const nextchapterNodeIdx = questCms.chapter[i + 1]
        ? questCms.chapter[i + 1].NodeIdx
        : questCms.nodes.length;

      for (let i = chapterNodeIdx; i < nextchapterNodeIdx; i++) {
        questChapterByNodeIdxCache[questCmsId][i] = questChapterElem.Text;
      }
    }
    console.log(questChapterByNodeIdxCache[questCmsId]);
  }

  if (questChapterByNodeIdxCache[nodeIdx]) {
    return questChapterByNodeIdxCache[nodeIdx];
  }
  return null;
}

// ------------------------------------------------------------------------------------------------
export function getBuildingHours(
  buildingHoursCmsId: number,
  developmentLevels: number[]
): BuildingHoursElem {
  let sumLevel = 0;
  for (const lv of developmentLevels) {
    sumLevel += lv;
  }

  const buildingHoursCms = cms.BuildingHours[buildingHoursCmsId];
  let elem: BuildingHoursElem = buildingHoursCms.buildingHour[0];
  for (const buildingHour of buildingHoursCms.buildingHour) {
    if (sumLevel < buildingHour.TownDevelopSumTerms) {
      break;
    }
    elem = buildingHour;
  }
  return elem;
}

// ------------------------------------------------------------------------------------------------
// liveEvent 중인지 반환.
export function isLiveEvent(liveEventCmsId: number, curTimeUtc: number): boolean {
  if (!liveEventCmsId) {
    return false;
  }
  const liveEventCms = cms.LiveEvent[liveEventCmsId];
  if (!liveEventCms) {
    return false;
  }
  const curDate = new Date(curTimeUtc * 1000);
  if (isFilteredByCountryCode(liveEventCms.localBitflag)) {
    return false;
  }

  if (
    curDate >= mutil.newDateByCmsDateStr(liveEventCms.startDate) &&
    curDate <= mutil.newDateByCmsDateStr(liveEventCms.endDate)
  ) {
    return true;
  }
  return false;
}

// ------------------------------------------------------------------------------------------------
// TradeComboSetCms 는  tradeComboSetGroup, tradeComboSetGroupSortOrder 유니크 해야 한다
let tradeComboGroupCache: {
  [tradeComboSetGroup: number]: TradeComboSetDesc[];
} = {};
export function GetTradeComboGroup(townCmsId: number) {
  const groupRef = cms.Town[townCmsId].tradeComboSetGroup;

  if (!tradeComboGroupCache[groupRef]) {
    _.forOwn(cms.TradeComboSet, (elem) => {
      if (elem.tradeComboSetGroup === groupRef) {
        if (!tradeComboGroupCache[elem.tradeComboSetGroup]) {
          tradeComboGroupCache[elem.tradeComboSetGroup] = [];
        }
        tradeComboGroupCache[elem.tradeComboSetGroup].push(elem);
      }
    });
    tradeComboGroupCache[groupRef].sort(
      (a, b) => a.tradeComboSetGroupSortOrder - b.tradeComboSetGroupSortOrder
    );
  }

  return tradeComboGroupCache[groupRef];
}

// ------------------------------------------------------------------------------------------------
let eventGameCache: {
  [group: number]: EventGameListDesc[];
} = {};
export function GetEventGameGroup(eventPageCmsId: number) {
  const groupRef = cms.EventPage[eventPageCmsId].groupRef;

  if (!eventGameCache[groupRef]) {
    _.forOwn(cms.EventGameList, (elem) => {
      if (elem.groupId === groupRef) {
        if (!eventGameCache[elem.groupId]) {
          eventGameCache[elem.groupId] = [];
        }
        eventGameCache[elem.groupId].push(elem);
      }
    });
  }

  return eventGameCache[groupRef];
}

// ------------------------------------------------------------------------------------------------
let boardGameRewardCache: {
  [group: number]: BoardGameRewardDesc[];
} = {};
export function GetBoardGameRewardGroup(groupRef: number): BoardGameRewardDesc[] {
  if (!boardGameRewardCache[groupRef]) {
    _.forOwn(cms.BoardGameReward, (elem) => {
      if (elem.groupId === groupRef) {
        if (!boardGameRewardCache[elem.groupId]) {
          boardGameRewardCache[elem.groupId] = [];
        }
        boardGameRewardCache[elem.groupId].push(elem);
      }
    });
    boardGameRewardCache[groupRef].sort((a, b) => a.tileOrder - b.tileOrder);
  }

  return boardGameRewardCache[groupRef];
}

// ------------------------------------------------------------------------------------------------
const tradeAreaTileDataDescByTownCache: { [townCmsId: number]: number } = {};
export function getTradeAreaTileDataByTown(townCmsId: number) {
  if (!tradeAreaTileDataDescByTownCache[townCmsId]) {
    const townLocation = cms.TownLocation[townCmsId];
    if (townLocation) {
      const curTile = LatLon2Tile(townLocation.location.latitude, townLocation.location.longitude);
      tradeAreaTileDataDescByTownCache[townCmsId] = cms.TradeAreaTile.t[curTile.x][curTile.y];
    }
  }

  return tradeAreaTileDataDescByTownCache[townCmsId];
}

// ------------------------------------------------------------------------------------------------
let townCmsIdsByTradeAreaTileDataCache: { [TradeAreaTileDataCmsId: number]: number[] };
export function getTownCmsIdsByTradeAreaTileData(tradeAreaTileDataCmsId: number) {
  if (!townCmsIdsByTradeAreaTileDataCache) {
    townCmsIdsByTradeAreaTileDataCache = {};
    _.forOwn(cms.Town, (townCms) => {
      const tradeAreaTileDataCmsId = getTradeAreaTileDataByTown(townCms.id);
      const tradeAreaTileDataCms = cms.TradeAreaTileData[tradeAreaTileDataCmsId];
      if (!townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCms.id]) {
        townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCms.id] = [];
      }
      townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCms.id].push(townCms.id);
    });
  }

  return townCmsIdsByTradeAreaTileDataCache[tradeAreaTileDataCmsId];
}

// ------------------------------------------------------------------------------------------------
const sortedTradeEventsByTradeAreaTileDataCache: { [tradeAreaTileDataCmsId: number]: number[] } =
  {};
export function getSortedTradeEventsByTradeAreaTileData(
  tradeAreaTileDataCms: TradeAreaTileDataDesc
) {
  if (!sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id]) {
    sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id] = _.cloneDeep(
      tradeAreaTileDataCms.majorTrandId
    );
    sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id].sort((a, b) => {
      const aTradeEvent = cms.TradeEvent[a];
      const bTradeEvent = cms.TradeEvent[b];
      return bTradeEvent.majorTrendHour - aTradeEvent.majorTrendHour;
    });
  }
  return sortedTradeEventsByTradeAreaTileDataCache[tradeAreaTileDataCms.id];
}

// ------------------------------------------------------------------------------------------------
let tradeEventCmsByEvnetTypeCache: { [type: number]: TradeEventDesc };
export function getTradeEventCmsByEvnetType(type: TRADE_EVENT_TYPE): TradeEventDesc {
  if (!tradeEventCmsByEvnetTypeCache) {
    tradeEventCmsByEvnetTypeCache = {};
    _.forOwn(cms.TradeEvent, (elem) => {
      tradeEventCmsByEvnetTypeCache[elem.tradeEventType] = elem;
    });
  }
  return tradeEventCmsByEvnetTypeCache[type];
}

// ------------------------------------------------------------------------------------------------
let landExploreDispatchCache: {
  [group: number]: LandExploreDesc[];
} = {};
export function GetLandExploreDispatchGroup(groupId: number): LandExploreDesc[] {
  if (!landExploreDispatchCache[groupId]) {
    _.forOwn(cms.LandExplore, (elem) => {
      if (elem.dispatchGroup === groupId) {
        if (!landExploreDispatchCache[elem.dispatchGroup]) {
          landExploreDispatchCache[elem.dispatchGroup] = [];
        }
        landExploreDispatchCache[elem.dispatchGroup].push(elem);
      }
    });
  }

  return landExploreDispatchCache[groupId];
}

let tradeAreaTileDataDispatchCache: {
  [group: number]: TradeAreaTileDataDesc[];
} = {};
export function TradeAreaTileDataDispatchGroup(groupId: number): TradeAreaTileDataDesc[] {
  if (!tradeAreaTileDataDispatchCache[groupId]) {
    _.forOwn(cms.TradeAreaTileData, (elem) => {
      if (elem.dispatchGroup === groupId) {
        if (!tradeAreaTileDataDispatchCache[elem.dispatchGroup]) {
          tradeAreaTileDataDispatchCache[elem.dispatchGroup] = [];
        }
        tradeAreaTileDataDispatchCache[elem.dispatchGroup].push(elem);
      }
    });
  }

  return tradeAreaTileDataDispatchCache[groupId];
}

// ------------------------------------------------------------------------------------------------
// TradeAreaTile 기반에 속해있는 townCmsId 를 리턴한다
let tradeAreaTileCache: {
  [tradeAreaTileCmsId: number]: number[];
} = {};
export function TradeAreaTileGroup(tradeAreaTileCmsId: number): number[] {
  if (!tradeAreaTileCache[tradeAreaTileCmsId]) {
    _.forOwn(cms.TownLocation, (elem) => {
      const curLoc: Location = elem.spawnArea;
      const tile: TileCoordinate = LatLon2Tile(curLoc.latitude, curLoc.longitude);

      const cmsId = cms.TradeAreaTile.t[tile.x][tile.y];
      if (!tradeAreaTileCache[cmsId]) {
        tradeAreaTileCache[cmsId] = [];
      }
      tradeAreaTileCache[cmsId].push(elem.town.id);
    });
  }
  return tradeAreaTileCache[tradeAreaTileCmsId];
}
// ------------------------------------------------------------------------------------------------
// 폭증 발생 가능한 교역품 리스트를 가져온다
const unpopularCandidateTradeGoodsCache: {
  [townCmsId: number]: number[];
} = {};
// 전체 항구에서 판매중인 교역품을 제외한 교역품 저장
const unSoldTradeGoodsCache = new Set<number>();
export function GetUnpopularTradeGoodsCandidates(townCmsId: number) {
  if (unSoldTradeGoodsCache.size === 0) {
    _.forOwn(cms.TradeGoods, (elem) => {
      // if (isFilteredByCountryCode(elem)) {
      //   return;
      // }
      unSoldTradeGoodsCache.add(elem.id);
    });
    _.forOwn(cms.Trade, (elem) => {
      unSoldTradeGoodsCache.delete(elem.tradeGoodsId);
    });
  }

  if (unpopularCandidateTradeGoodsCache[townCmsId]) {
    return unpopularCandidateTradeGoodsCache[townCmsId];
  }

  const pickTownSoldGoodsCmsIds = new Set<number>();
  _.forOwn(cms.Trade, (elem) => {
    if (elem.townId === townCmsId) {
      pickTownSoldGoodsCmsIds.add(elem.tradeGoodsId);
    }
  });

  const townCulturalAreaCmsId = cms.Town[townCmsId].CulturalAreaId;
  unpopularCandidateTradeGoodsCache[townCmsId] = [];
  const soldAble = new Set<number>();
  _.forOwn(cms.Trade, (elem) => {
    if (pickTownSoldGoodsCmsIds.has(elem.tradeGoodsId)) {
      return;
    }
    const tradeGoodsItem = cms.TradeGoods[elem.tradeGoodsId];
    if (!tradeGoodsItem) {
      return;
    }
    if (unSoldTradeGoodsCache.has(elem.tradeGoodsId)) {
      return;
    }
    if (elem.liveEvent && elem.rotationTradeGroupId) {
      return;
    }
    if (elem.contentsTerms) {
      return;
    }

    // 문화권에서 교역소 판매 불가능한 상품 제외
    const idx = townCulturalAreaCmsId % CultureAreaCmsBaseCmsId;
    if (tradeGoodsItem.cultureSellPrices[idx] < 0) {
      return;
    }
    soldAble.add(elem.tradeGoodsId);
  });

  unpopularCandidateTradeGoodsCache[townCmsId] = Array.from(soldAble);
  return unpopularCandidateTradeGoodsCache[townCmsId];
}

// ------------------------------------------------------------------------------------------------
const tradeGoodsCache: {
  [townCmsId: number]: TradeDesc[];
} = {};
export function TradeGoodsGroupInTownCmsId(townCmsId: number) {
  if (!tradeGoodsCache[townCmsId]) {
    _.forOwn(cms.Trade, (elem) => {
      const goodsCms = cms.TradeGoods[elem.tradeGoodsId];
      // if (isFilteredByCountryCode(goodsCms)) {
      //   return;
      // }
      if (!goodsCms) {
        return;
      }
      if (elem.townId === townCmsId) {
        if (!tradeGoodsCache[elem.townId]) {
          tradeGoodsCache[elem.townId] = [];
        }
        tradeGoodsCache[elem.townId].push(elem);
      }
    });
  }
  return tradeGoodsCache[townCmsId];
}

export function isCostumeEquipType(type: CEQUIP_TYPE) {
  return (
    type === CEQUIP_TYPE.WEAPON_COSTUME ||
    type === CEQUIP_TYPE.BODY_COSTUME ||
    type === CEQUIP_TYPE.HAT_COSTUME ||
    type === CEQUIP_TYPE.CAPE_COSTUME ||
    type === CEQUIP_TYPE.FACE_COSTUME ||
    type === CEQUIP_TYPE.FX_COSTUME
  );
}

const equipTypeToCostumeEquipType: {
  [type: number]: number;
} = {
  [CEQUIP_TYPE.WEAPON]: CEQUIP_TYPE.WEAPON_COSTUME,
  [CEQUIP_TYPE.BODY]: CEQUIP_TYPE.BODY_COSTUME,
  [CEQUIP_TYPE.HAT]: CEQUIP_TYPE.HAT_COSTUME,
  [CEQUIP_TYPE.CAPE]: CEQUIP_TYPE.CAPE_COSTUME,
  [CEQUIP_TYPE.FACE]: CEQUIP_TYPE.FACE_COSTUME,
};

export function getCurEquipType(nub: MateEquipmentNub) {
  const cEquipCms = cms.CEquip[nub.cmsId];
  if (nub.isCostume) {
    return equipTypeToCostumeEquipType[cEquipCms.type];
  } else {
    return cEquipCms.type;
  }
}

// ------------------------------------------------------------------------------------------------
// landExplore <---> adventureEventReward
// inheritLandFeature 상속 이면서 adventureEventReward.landExplore id 를 사용중인 cms 리턴
// ------------------------------------------------------------------------------------------------
const adventureEventRewardQuestGroup: { [landExploreCmsId: number]: AdventureEventRewardDesc } = {};
export function GetLandExploreQuestSpot(landExploreCmsId: number): AdventureEventRewardDesc {
  if (!cms.LandExplore[landExploreCmsId].inheritLandFeature) {
    return undefined;
  }

  if (!adventureEventRewardQuestGroup[landExploreCmsId]) {
    _.forOwn(cms.AdventureEventReward, (elem) => {
      if (!elem.landExploreId || !elem.ctDiscovery || !elem.ctEffectRate3f) {
        return;
      }
      // !! adventureEventReward.landExploreId 가 여러개 이면 기획 버그
      adventureEventRewardQuestGroup[elem.landExploreId] = elem;
    });
  }

  return adventureEventRewardQuestGroup[landExploreCmsId];
}

// ------------------------------------------------------------------------------------------------
// landExplore <---> adventureEventReward
// cms 간 key 를 공유하여 adventureEventReward 를 찾기 위한 캐시데이터
// ------------------------------------------------------------------------------------------------
const adventureEventRewardGroup: {
  [key: string]: { weekSessionId: number; adventureEventRewardCms: AdventureEventRewardDesc };
} = {};
export function GetAdventureEventRewardGroup(
  weekSessionId: number,
  landExploreCmsId: number,
  landGrade: number,
  landCulture: number,
  landRewardType: number[]
): AdventureEventRewardDesc {
  const argKey = '' + landExploreCmsId + landGrade + landCulture;
  if (
    !adventureEventRewardGroup[argKey] ||
    adventureEventRewardGroup[argKey].weekSessionId !== weekSessionId
  ) {
    _.forOwn(cms.AdventureEventReward, (elem) => {
      if (
        elem.advRewardGrade === undefined ||
        elem.advRewardCulture === undefined ||
        elem.landRewardType === undefined
      ) {
        return;
      }

      // landRewardType 중 하나를 weekSessionId 기반으로 뽑는다
      const seed = parseInt(argKey + weekSessionId, 10);
      const pickRand = Math.floor((seed * Math.PI) % 10) / 10;
      const pickRewardType = landRewardType[Math.floor(pickRand * landRewardType.length)];
      if (
        landGrade === elem.advRewardGrade &&
        landCulture === elem.advRewardCulture &&
        pickRewardType === elem.landRewardType
      ) {
        adventureEventRewardGroup[argKey] = { weekSessionId, adventureEventRewardCms: elem };
      }
    });
  }

  return adventureEventRewardGroup?.[argKey]?.adventureEventRewardCms;
}

// ------------------------------------------------------------------------------------------------
// 패스 이벤트 보상 최대 레벨 리턴
const passEventMaxLevelCache: {
  [eventPageCmsId: number]: number;
} = {};
export function getPassEventMaxLevel(passEventCms: EventPageDesc) {
  if (!passEventMaxLevelCache[passEventCms.id]) {
    const eventMissionCmses = getEventMissionCmses(passEventCms.groupRef);
    let maxLevel = 0;
    for (const elem of eventMissionCmses) {
      if (
        elem.type !== EventMissionType.BASE_REWARD &&
        elem.type !== EventMissionType.ADDITIONAL_REWARD &&
        elem.type !== EventMissionType.REPEATED_REWARD
      ) {
        continue;
      }
      if (elem.val > maxLevel) {
        maxLevel = elem.val;
      }
    }
    passEventMaxLevelCache[passEventCms.id] = maxLevel;
  }
  return passEventMaxLevelCache[passEventCms.id];
}

// ------------------------------------------------------------------------------------------------
// 반복 보상 EventMission TYPE 이 존재하면 최대 획득 경험치량 리턴
const passEventAddedExpCache: {
  [eventPageCmsId: number]: number;
} = {};
function getPassEventAddedExp(passEventCms: EventPageDesc) {
  if (!passEventAddedExpCache[passEventCms.id]) {
    const eventMissionCmses = getEventMissionCmses(passEventCms.groupRef);
    for (const elem of eventMissionCmses) {
      if (elem.type === EventMissionType.REPEATED_REWARD) {
        passEventAddedExpCache[passEventCms.id] = elem.requiredRepeatedExp * elem.repeatCount;
      }
    }
  }

  return passEventAddedExpCache[passEventCms.id] ? passEventAddedExpCache[passEventCms.id] : 0;
}

// ------------------------------------------------------------------------------------------------
let mateTrainingCache: {
  [mateGrade: number]: {
    [jobType: number]: {
      [trainingGrade: number]: MateTrainingDesc;
    };
  };
};
export function getMateTraining(mateGrade: number, jobType: number, trainingGrade: number) {
  if (!mateTrainingCache) {
    mateTrainingCache = {};
    _.forOwn(cms.MateTraining, (elem) => {
      const duplicated =
        mateTrainingCache[elem.mateGrade]?.[elem.jobType]?.[elem.mateTrainingGrade];
      assert(!duplicated);

      _.merge(mateTrainingCache, {
        [elem.mateGrade]: {
          [elem.jobType]: {
            [elem.mateTrainingGrade]: elem,
          },
        },
      });
    });
  }
  return mateTrainingCache?.[mateGrade]?.[jobType]?.[trainingGrade];
}

// ------------------------------------------------------------------------------------------------
// 항해사 버프 이벤트들을 반환
let mateBuffLiveEventCache: {
  [liveEventCmsId: number]: LiveEventDesc;
};
export function getMateBuffLiveEvents(): { [liveEventCmsId: number]: LiveEventDesc } {
  if (!mateBuffLiveEventCache) {
    mateBuffLiveEventCache = {};
    _.forOwn(cms.LiveEvent, (elem) => {
      if (elem.eventMateJob && elem.eventMateJob.length > 0) {
        mateBuffLiveEventCache[elem.id] = elem;
      }
    });
  }
  return mateBuffLiveEventCache;
}

// ------------------------------------------------------------------------------------------------
// 여관 종업원 간에 동일한 퀘스트를 가지고 있지 않음.
// townBuilding에 등록된 Pubstaff의 경우 하나의 항구에만 배치되야한다고 전달받음.
export function getTownCmsIdForPubStaffQuestCmsId(questCmsId: number): number | null {
  let pubStaffCmsId: number;

  for (const elem of Object.values(cms.PubStaff)) {
    const questIds: { Id: number }[] = elem.questId || [];
    for (const questId of questIds) {
      if (questId.Id === questCmsId) {
        pubStaffCmsId = elem.id;
        break;
      }
    }

    if (pubStaffCmsId) {
      break;
    }
  }

  for (const elem of Object.values(cms.TownBuilding)) {
    if (elem.PubStaffId && elem.PubStaffId === pubStaffCmsId) {
      return elem.townId;
    }
  }
  return null;
}

export function getPubStaffQuestLevel(questCmsId: number): number | null {
  for (const elem of Object.values(cms.PubStaff)) {
    const questIds: { Id: number }[] = elem.questId || [];
    for (let i = 0; i < questIds.length; i++) {
      if (questIds[i].Id === questCmsId) {
        return i + 1;
      }
    }
  }
  return null;
}

export function getEventRankingReceivedRewardIds(
  eventPageCms: EventPageDesc,
  rewardIdxArr: number[]
): number[] {
  const rewardIds: number[] = [];
  const eventRankingListCms = cms.EventRankingList[eventPageCms.groupRef];
  for (const rewardIdx of rewardIdxArr) {
    // 클라이언트 req값은 1부터 전달이 되서 -1 처리함
    rewardIds.push(eventRankingListCms.goals[rewardIdx - 1].Reward);
  }
  return rewardIds;
}

// ------------------------------------------------------------------------------------------------
let eventRankingRewardCache: {
  [group: number]: { [rewardType: number]: EventRankingRewardDesc[] };
} = {};
export function getEventRankingRewardGroup(
  groupId: number,
  rewardType: number
): EventRankingRewardDesc[] {
  if (eventRankingRewardCache[groupId] && eventRankingRewardCache[groupId][rewardType]) {
    return eventRankingRewardCache[groupId][rewardType];
  }

  _.forOwn(cms.EventRankingReward, (elem) => {
    if (!eventRankingRewardCache[elem.group]) {
      eventRankingRewardCache[elem.group] = {};
    }
    if (!eventRankingRewardCache[elem.group][elem.relateType]) {
      eventRankingRewardCache[elem.group][elem.relateType] = [];
    }
    eventRankingRewardCache[elem.group][elem.relateType].push(elem);
  });
  return eventRankingRewardCache[groupId][rewardType];
}

// ------------------------------------------------------------------------------------------------
let eventRankingGuildCache: EventPageDesc[] = [];
export function getEventPageRankingGuild() {
  if (eventRankingGuildCache.length > 0) {
    return eventRankingGuildCache;
  }

  _.forOwn(cms.EventPage, (eventPageCms) => {
    if (eventPageCms.type !== EventPageType.EVENT_RANKING) {
      return;
    }
    if (isFilteredByCountryCode(eventPageCms.localBitflag)) {
      return;
    }
    if (!cms.EventRankingList[eventPageCms.groupRef].useGuildRank) {
      return;
    }

    eventRankingGuildCache.push(eventPageCms);
  });
  return eventRankingGuildCache;
}

// ------------------------------------------------------------------------------------------------
let constellationCache: {
  [discoveryId: number]: ConstellationDesc;
} = {};
export function getConstellationByDiscoveryId(discoveryId: number): ConstellationDesc {
  if (!constellationCache[discoveryId]) {
    _.forOwn(cms.Constellation, (elem) => {
      if (elem.discoveryId === discoveryId) {
        constellationCache[elem.discoveryId] = elem;
      }
    });
  }

  return constellationCache[discoveryId];
}

export function getShipCamouflageType(cmsId: number) {
  let type: SHIP_CAMOUFLAGE_TYPE;
  if (cms.Ship[cmsId]) {
    type = SHIP_CAMOUFLAGE_TYPE.NORMAL;
  } else if (cms.ShipCamouflage[cmsId]) {
    type = SHIP_CAMOUFLAGE_TYPE.SPECIAL;
  }
  return type;
}

// ------------------------------------------------------------------------------------------------
let choiceBoxGroupCache: { [choiceBoxRewardGroupId: number]: { [id: number]: ChoiceBoxDesc } };
export function getChoiceBoxGroup(choiceBoxGroupNo: number): { [id: number]: ChoiceBoxDesc } {
  if (!choiceBoxGroupCache) {
    choiceBoxGroupCache = {};
    _.forOwn(cms.ChoiceBox, (elem) => {
      const groupNo = elem.choiceBoxGroup;
      if (!choiceBoxGroupCache[groupNo]) {
        choiceBoxGroupCache[groupNo] = {};
      }

      choiceBoxGroupCache[groupNo][elem.id] = elem;
    });
  }

  return choiceBoxGroupCache[choiceBoxGroupNo];
}

// ------------------------------------------------------------------------------------------------
let returnUserCashShopCmsCache: CashShopDesc[];
export function getReturnUserCashShopCms(): CashShopDesc[] {
  if (!returnUserCashShopCmsCache) {
    returnUserCashShopCmsCache = [];
    _.forOwn(cms.CashShop, (elem) => {
      if (isFilteredByCountryCode(elem.productLocalBitflag)) {
        return;
      }

      if (elem.isSaleReturnUser) {
        returnUserCashShopCmsCache.push(elem);
      }
    });
  }

  return returnUserCashShopCmsCache;
}

// 판매 기간이 가변적인 상품들
export function isOpenDurationCashShopCms(cashShopCms: CashShopDesc) {
  return cashShopCms.isSaleReturnUser;
}

// ------------------------------------------------------------------------------------------------
let returnMaxOrderByRotationGroupCache: { [groupId: number]: number };
function _getRotationMaxTradeOrder(rotationTradeGroupId: number): number {
  if (!returnMaxOrderByRotationGroupCache) {
    returnMaxOrderByRotationGroupCache = {};
    _.forOwn(cms.Trade, (elem) => {
      if (!elem.rotationTradeGroupId || !elem.rotationTradeGroupOrder) {
        return;
      }

      if (
        !returnMaxOrderByRotationGroupCache[elem.rotationTradeGroupId] ||
        returnMaxOrderByRotationGroupCache[elem.rotationTradeGroupId] < elem.rotationTradeGroupOrder
      ) {
        returnMaxOrderByRotationGroupCache[elem.rotationTradeGroupId] =
          elem.rotationTradeGroupOrder;
      }
    });
  }

  return returnMaxOrderByRotationGroupCache[rotationTradeGroupId];
}

// ------------------------------------------------------------------------------------------------
let admiralQuestPassIdByMateCmsIdCache: { [mateCmsId: number]: number };
export function getAdmiralQuestPassIdByMateCmsId(mateCmsId: number): number {
  if (!admiralQuestPassIdByMateCmsIdCache) {
    admiralQuestPassIdByMateCmsIdCache = {};
    _.forOwn(cms.QuestPass, (elem) => {
      if (elem.type === QuestPassType.Admiral) {
        admiralQuestPassIdByMateCmsIdCache[elem.mateId] = elem.id;
      }
    });
  }
  return admiralQuestPassIdByMateCmsIdCache[mateCmsId];
}

// ------------------------------------------------------------------------------------------------
let relationShipChronicleByQuestIdCache: { [questId: number]: RelationShipChronicleDesc };
export function getRelationShipChronicleByQuestId(questCmsId: number) {
  if (!relationShipChronicleByQuestIdCache) {
    relationShipChronicleByQuestIdCache = {};

    _.forOwn(cms.RelationShipChronicle, (elem) => {
      if (isFilteredByCountryCode(elem.localBitflag)) {
        return;
      }

      relationShipChronicleByQuestIdCache[elem.questId] = elem;
    });
  }

  return relationShipChronicleByQuestIdCache[questCmsId];
}

// 투자 가능한 townCmsId
let investmentTownCmsIds = [];
export function getInvestmentTownCmsIds(): number[] {
  if (investmentTownCmsIds.length === 0) {
    _.forOwn(cms.Town, (elem) => {
      if (elem.ownType !== TOWN_OWN_TYPE.UNOCCUPIABLE) {
        investmentTownCmsIds.push(elem.id);
      }
    });
  }
  return investmentTownCmsIds;
}

// ------------------------------------------------------------------------------------------------
let cEquipEnchantCache: {
  [group: number]: { [enchantLv: number]: CEquipEnchantDesc };
};
export function getCEquipEnchant(enchantGroup: number, enchantLv: number) {
  if (!cEquipEnchantCache) {
    cEquipEnchantCache = {};
    _.forOwn(cms.CEquipEnchant, (elem) => {
      if (!cEquipEnchantCache[elem.enchantGroup]) {
        cEquipEnchantCache[elem.enchantGroup] = {};
      }
      cEquipEnchantCache[elem.enchantGroup][elem.enchantLv] = elem;
    });
  }

  return cEquipEnchantCache[enchantGroup][enchantLv];
}

// ------------------------------------------------------------------------------------------------
let shipSlotEnchantCache: {
  [group: number]: { [enchantLv: number]: ShipsSlotEnchantDesc };
};
export function getShipSlotEnchant(enchantGroup: number, enchantLv: number) {
  if (!shipSlotEnchantCache) {
    shipSlotEnchantCache = {};
    _.forOwn(cms.ShipSlotEnchant, (elem) => {
      if (!shipSlotEnchantCache[elem.enchantGroup]) {
        shipSlotEnchantCache[elem.enchantGroup] = {};
      }
      shipSlotEnchantCache[elem.enchantGroup][elem.enchantLv] = elem;
    });
  }

  return shipSlotEnchantCache[enchantGroup][enchantLv];
}

// ------------------------------------------------------------------------------------------------
let cEquipEnchantStatRatioCache: { [enchnatLv: number]: EnchantStatRatioDesc };
let shipSlotEnchantStatRatioCache: { [enchnatLv: number]: EnchantStatRatioDesc };
function _buildEnchantStatRatioCache() {
  if (!cEquipEnchantStatRatioCache || !shipSlotEnchantStatRatioCache) {
    cEquipEnchantStatRatioCache = {};
    shipSlotEnchantStatRatioCache = {};

    const caching = function (
      enchantStatRatioCache: { [enchnatLv: number]: EnchantStatRatioDesc },
      elem: EnchantStatRatioDesc
    ) {
      enchantStatRatioCache[elem.enchantLv] = elem;
    };

    _.forOwn(cms.EnchantStatRatio, (elem) => {
      switch (elem.enchantType) {
        case ENCHANT_TYPE.SHIP_SLOT:
          caching(shipSlotEnchantStatRatioCache, elem);
          break;
        case ENCHANT_TYPE.CEQUIP:
          caching(cEquipEnchantStatRatioCache, elem);
          break;
      }
    });
  }
}

// ------------------------------------------------------------------------------------------------
export function getCEquipEnchantStatRatio(enchantLv: number) {
  _buildEnchantStatRatioCache();
  return cEquipEnchantStatRatioCache[enchantLv];
}

// ------------------------------------------------------------------------------------------------
export function getShipSlotEnchantStatRatio(enchantLv: number) {
  _buildEnchantStatRatioCache();
  return shipSlotEnchantStatRatioCache[enchantLv];
}

// ------------------------------------------------------------------------------------------------
let shipComposeGroupCache: { [groupId: number]: ShipComposeDesc[] };
export function getShipComposeGroup(groupId: number) {
  if (!shipComposeGroupCache) {
    shipComposeGroupCache = {};
    _.forOwn(cms.ShipCompose, (elem) => {
      if (!shipComposeGroupCache[elem.groupId]) {
        shipComposeGroupCache[elem.groupId] = [];
      }
      shipComposeGroupCache[elem.groupId].push(elem);
    });
  }

  return shipComposeGroupCache[groupId];
}

// ------------------------------------------------------------------------------------------------
const sellingSmuggleGoods: {
  [townCmsId: number]: { [smuggleGoodsCmsId: number]: SmuggleDesc };
} = {};

function setSellingSmuggleGoods(townCmsId: number) {
  if (sellingSmuggleGoods[townCmsId]) {
    return;
  }

  sellingSmuggleGoods[townCmsId] = {};
  _.forOwn(cms.Smuggle, (smuggleCms) => {
    const goodsCms = cms.SmuggleGoods[smuggleCms.smuggleGoodsId];
    if (isFilteredByCountryCode(goodsCms.localBitFlag)) {
      return;
    }

    if (smuggleCms.townId === townCmsId) {
      sellingSmuggleGoods[townCmsId][smuggleCms.smuggleGoodsId] = smuggleCms;
    }
  });
}

export function isSellingSmuggleInTown(townCmsId: number, smuggleGoodsCmsId: number) {
  setSellingSmuggleGoods(townCmsId);
  return sellingSmuggleGoods[townCmsId][smuggleGoodsCmsId] ? true : false;
}

export function getTownSellingSmuggleGoodsCmsIds(townCmsId: number) {
  setSellingSmuggleGoods(townCmsId);
  return _.values(sellingSmuggleGoods[townCmsId]).map((elem) => elem.smuggleGoodsId);
}

export function getTownSmuggleGoodsCms(townCmsId: number, smuggleGoodsCmsId: number) {
  setSellingSmuggleGoods(townCmsId);
  return sellingSmuggleGoods[townCmsId][smuggleGoodsCmsId];
}

// ------------------------------------------------------------------------------------------------
let townNpcShopGroupCache: { [npcShopGroup: number]: TownNpcShopDesc[] };
export function getTownNpcShopGroup(groupId: number) {
  if (!townNpcShopGroupCache) {
    townNpcShopGroupCache = {};

    _.forOwn(cms.TownNpcShop, (townNpcShopCms) => {
      if (!townNpcShopGroupCache[townNpcShopCms.npcShopGroup]) {
        townNpcShopGroupCache[townNpcShopCms.npcShopGroup] = [];
      }

      townNpcShopGroupCache[townNpcShopCms.npcShopGroup].push(townNpcShopCms);
    });
  }

  return townNpcShopGroupCache[groupId];
}

// ------------------------------------------------------------------------------------------------
let townNpcIdCache: { [townCmsId: number]: number[] };
export function getNpcIdsByTownCmsId(townCmsId: number) {
  if (!townNpcIdCache) {
    townNpcIdCache = {};
    _.forOwn(cms.TownNpcServer, (townNpc) => {
      if (!townNpcIdCache[townNpc.townId]) {
        townNpcIdCache[townNpc.townId] = [];
      }

      townNpcIdCache[townNpc.townId].push(townNpc.npcId);
    });
  }

  return townNpcIdCache[townCmsId];
}

// ------------------------------------------------------------------------------------------------
export function getNpcShopGroupByTownCmsId(townCmsId: number) {
  const npcIds = getNpcIdsByTownCmsId(townCmsId);
  if (!npcIds || npcIds.length === 0) {
    return undefined;
  }

  for (const id of npcIds) {
    const npcCms = getTownNpcGlobalCms()[id];
    if (!npcCms) {
      continue;
    }

    const townNpcShopGroup = getTownNpcShopGroup(npcCms.npcShopGroup);
    if (townNpcShopGroup) {
      return townNpcShopGroup;
    }
  }

  return undefined;
}

// ------------------------------------------------------------------------------------------------
export function getTownNpcInteractionCmsByInteractionFunctionType(
  townCmsId: number,
  interactionFunctionType: NPC_INTERACTION_FUNCTION_TYPE
) {
  const npcIds = getNpcIdsByTownCmsId(townCmsId);
  if (!npcIds || npcIds.length === 0) {
    return undefined;
  }

  for (const id of npcIds) {
    const interactionCmsIds = (getTownNpcGlobalCms()[id] ?? cms.TownNpcTemplate[id])
      .npcInteractionId;
    if (!interactionCmsIds || interactionCmsIds.length === 0) {
      continue;
    }

    for (const interactionCmsId of interactionCmsIds) {
      const npcInteractionCms = cms.NpcInteraction[interactionCmsId];
      if (npcInteractionCms.interaction.Function === interactionFunctionType) {
        return npcInteractionCms;
      }
    }
  }

  return undefined;
}

// ------------------------------------------------------------------------------------------------
export function getTownNpcIdByInteractionFunctionType(
  townCmsId: number,
  interactionFunctionType: NPC_INTERACTION_FUNCTION_TYPE
) {
  const npcIds = getNpcIdsByTownCmsId(townCmsId);
  if (!npcIds || npcIds.length === 0) {
    return undefined;
  }

  for (const id of npcIds) {
    const interactionCmsIds = (getTownNpcGlobalCms()[id] ?? cms.TownNpcTemplate[id])
      .npcInteractionId;
    if (!interactionCmsIds || interactionCmsIds.length === 0) {
      continue;
    }

    for (const interactionCmsId of interactionCmsIds) {
      const npcInteractionCms = cms.NpcInteraction[interactionCmsId];
      if (npcInteractionCms.interaction.Function === interactionFunctionType) {
        return id;
      }
    }
  }

  return undefined;
}

// ------------------------------------------------------------------------------------------------
// karma 단계는 karma cms의 minValue 기준으로 정렬했을때의 index
let sortedKarmaCache: KarmaDesc[];
export function getKarmaLevel(karma: number) {
  if (!sortedKarmaCache) {
    sortedKarmaCache = _.sortBy(cms.Karma, (elem) => elem.karmaMinValue);
  }

  for (let i = 0; i < sortedKarmaCache.length; i++) {
    const karmaCms = sortedKarmaCache[i];
    if (_.inRange(karma, karmaCms.karmaMinValue, karmaCms.karmaMaxValue + 1)) {
      return i;
    }
  }

  return undefined;
}

// ------------------------------------------------------------------------------------------------
let infiniteLighthouseGroupCache: {
  [groupId: number]: {
    infiniteLighthouseSchedule: InfiniteLighthouseScheduleDesc;
    infiniteLighthouses: InfiniteLighthouseDesc[];
  };
};
export function getInfiniteLighthouseGroup(stageGroupId: number) {
  if (!infiniteLighthouseGroupCache) {
    infiniteLighthouseGroupCache = {};
    _.forOwn(cms.InfiniteLighthouse, (elem) => {
      if (!infiniteLighthouseGroupCache[elem.stageGroup]) {
        infiniteLighthouseGroupCache[elem.stageGroup] = {
          infiniteLighthouseSchedule: undefined,
          infiniteLighthouses: [],
        };
      }

      infiniteLighthouseGroupCache[elem.stageGroup].infiniteLighthouses[elem.stage] = elem;
    });

    _.forOwn(cms.InfiniteLighthouseSchedule, (elem) => {
      if (!infiniteLighthouseGroupCache[elem.stageGroup]) {
        infiniteLighthouseGroupCache[elem.stageGroup] = {
          infiniteLighthouseSchedule: elem,
          infiniteLighthouses: [],
        };
      } else {
        infiniteLighthouseGroupCache[elem.stageGroup].infiniteLighthouseSchedule = elem;
      }
    });
  }

  return infiniteLighthouseGroupCache[stageGroupId];
}

// ------------------------------------------------------------------------------------------------
// 이민시 투자 점수 기반 투자 아이템 id 리턴 (가장 단위가 작은 투자 증서 지급)
let refundInvestItems: {
  [id: number]: number;
} = {};
export function getRefundInvestItemCmsId(curTimeUtc: number) {
  const cmsId = formula.getInvestSeasonId(curTimeUtc);
  if (!refundInvestItems[cmsId]) {
    const elem = _.minBy(cms.InvestSeason[cmsId].investItems, (elem: InvestItem) => {
      return elem.Value;
    });
    refundInvestItems[cmsId] = elem.Id;
  }

  return refundInvestItems[cmsId];
}

// ------------------------------------------------------------------------------------------------
let clashUseMaps: {
  [seasonOrder: number]: { [weeklyOrder: number]: number };
};
export function getOceanStageCmsIdByClash(seasonOrder: number, weeklyOrder: number) {
  if (!clashUseMaps) {
    clashUseMaps = {};
    for (const cmsId in cms.ClashUseMap) {
      const clashUseMapCms = cms.ClashUseMap[cmsId];
      if (!clashUseMaps[clashUseMapCms.seasonOrder]) {
        clashUseMaps[clashUseMapCms.seasonOrder] = {};
      }
      clashUseMaps[clashUseMapCms.seasonOrder][clashUseMapCms.weeklyOrder] = clashUseMapCms.stageId;
    }
  }

  return (
    clashUseMaps?.[seasonOrder]?.[weeklyOrder] ||
    cms.ClashUseMap[cms.Const.ClashUseDefaultMap.value].stageId
  );
}

let firstInvestSeasonId: number = null;
export function getFirstInvestSeasonCmsId(): number {
  if (firstInvestSeasonId) {
    return firstInvestSeasonId;
  }

  for (const cmsId in cms.InvestSeason) {
    const seasonId = parseInt(cmsId, 10);
    if (_.isNull(firstInvestSeasonId) || seasonId < firstInvestSeasonId) {
      firstInvestSeasonId = seasonId;
    }
  }
  return firstInvestSeasonId;
}

// ------------------------------------------------------------------------------------------------
let rewardSeason: {
  [seasonId: number]: {
    [id: number]: RewardSeasonElemDesc;
  };
};
export function getRewardSeasonElem(
  investSeasonCmsId: number,
  rewardSeasonCmsId: number
): RewardSeasonElemDesc {
  if (!rewardSeason) {
    this.rewardSeason = {};
    _.forOwn(cms.RewardSeasonItems, (elem) => {
      const cmsId = elem.id;
      for (const reward of elem.reward) {
        if (!this.rewardSeason[reward.SeasonId]) {
          this.rewardSeason[reward.SeasonId] = {};
        }
        this.rewardSeason[reward.SeasonId][cmsId] = reward;
      }
    });
  }

  if (!this.rewardSeason[investSeasonCmsId]) {
    mlog.warn('[RewardSeasonItemsLookupTable] invalid-type-1', {
      investSeasonCmsId,
      rewardSeasonCmsId,
    });
    return undefined;
  }

  if (!this.rewardSeason[investSeasonCmsId][rewardSeasonCmsId]) {
    mlog.warn('[RewardSeasonItemsLookupTable] invalid-type-2', {
      investSeasonCmsId,
      rewardSeasonCmsId,
    });
    return undefined;
  }

  return this.rewardSeason[investSeasonCmsId][rewardSeasonCmsId];
}

// ------------------------------------------------------------------------------------------------
// CMS 테이블 필터링 및 캐싱 함수들
// localBitFlag가 있는 경우 countryCode로 필터링, 없는 경우 전체 반환
// ------------------------------------------------------------------------------------------------

let achievementCmsCache: { [cmsId: number]: AchievementDesc };
export function getAchievementCms(): { [cmsId: number]: AchievementDesc } {
  if (!achievementCmsCache) {
    achievementCmsCache = {};
    _.forOwn(cms.Achievement, (elem) => {
      if (isFilteredByCountryCode(elem.localBitFlag)) {
        return;
      }
      achievementCmsCache[elem.id] = elem;
    });
  }
  return achievementCmsCache;
}

let hotTimeBuffCmsCache: { [cmsId: number]: HotTimeBuffDesc };
export function getHotTimeBuffCms(): { [cmsId: number]: HotTimeBuffDesc } {
  if (!hotTimeBuffCmsCache) {
    hotTimeBuffCmsCache = {};
    _.forOwn(cms.HotTimeBuff, (elem) => {
      if (isFilteredByCountryCode(elem.localBitflag)) {
        return;
      }
      hotTimeBuffCmsCache[elem.id] = elem;
    });
  }
  return hotTimeBuffCmsCache;
}

let townNpcGlobalCmsCache: { [cmsId: number]: TownNpcGlobalDesc };
export function getTownNpcGlobalCms(): { [cmsId: number]: TownNpcGlobalDesc } {
  if (!townNpcGlobalCmsCache) {
    townNpcGlobalCmsCache = {};
    _.forOwn(cms.TownNpcGlobal, (elem) => {
      if (isFilteredByCountryCode(elem.localBitFlag)) {
        return;
      }
      townNpcGlobalCmsCache[elem.id] = elem;
    });
  }
  return townNpcGlobalCmsCache;
}

let oceanNpcCmsCache: { [cmsId: number]: OceanNpcDesc };
export function getOceanNpcCms(): { [cmsId: number]: OceanNpcDesc } {
  if (!oceanNpcCmsCache) {
    oceanNpcCmsCache = {};
    _.forOwn(cms.OceanNpc, (elem) => {
      if (isFilteredByCountryCode(elem.localBitFlag)) {
        return;
      }
      oceanNpcCmsCache[elem.id] = elem;
    });
  }
  return oceanNpcCmsCache;
}

let fishCmsCache: { [cmsId: number]: FishDesc };
export function getFishCms(): { [cmsId: number]: FishDesc } {
  if (!fishCmsCache) {
    fishCmsCache = {};
    _.forOwn(cms.Fish, (elem) => {
      if (isFilteredByCountryCode(elem.localBitFlag)) {
        return;
      }
      fishCmsCache[elem.id] = elem;
    });
  }
  return fishCmsCache;
}

let bossRaidCmsCache: { [cmsId: number]: BossRaidDesc };
export function getBossRaidCms(): { [cmsId: number]: BossRaidDesc } {
  if (!bossRaidCmsCache) {
    bossRaidCmsCache = {};
    _.forOwn(cms.BossRaid, (elem) => {
      if (isFilteredByCountryCode(elem.localBitFlag)) {
        return;
      }
      bossRaidCmsCache[elem.id] = elem;
    });
  }
  return bossRaidCmsCache;
}

let discoveryCmsCache: { [cmsId: number]: DiscoveryDesc };
export function getDiscoveryCms(): { [cmsId: number]: DiscoveryDesc } {
  if (!discoveryCmsCache) {
    discoveryCmsCache = {};
    _.forOwn(cms.Discovery, (elem) => {
      // if (isFilteredByCountryCode(elem)) {
      //   return;
      // }
      discoveryCmsCache[elem.id] = elem;
    });
  }
  return discoveryCmsCache;
}

let tradeGoodsCmsCache: { [cmsId: number]: TradeGoodsDesc };
export function getTradeGoodsCms(): { [cmsId: number]: TradeGoodsDesc } {
  if (!tradeGoodsCmsCache) {
    tradeGoodsCmsCache = {};
    _.forOwn(cms.TradeGoods, (elem) => {
      // if (isFilteredByCountryCode(elem)) {
      //   return;
      // }
      tradeGoodsCmsCache[elem.id] = elem;
    });
  }
  return tradeGoodsCmsCache;
}

let mateTemplateGroupCmsCache: { [cmsId: number]: MateTemplateGroupDesc };
export function getMateTemplateGroupCms(): { [cmsId: number]: MateTemplateGroupDesc } {
  if (!mateTemplateGroupCmsCache) {
    mateTemplateGroupCmsCache = {};
    _.forOwn(cms.MateTemplateGroup, (elem) => {
      // if (isFilteredByCountryCode(elem)) {
      //   return;
      // }
      mateTemplateGroupCmsCache[elem.id] = elem;
    });
  }
  return mateTemplateGroupCmsCache;
}

let worldStateCmsCache: { [cmsId: number]: WorldStateDesc };
export function getWorldStateCms(): { [cmsId: number]: WorldStateDesc } {
  if (!worldStateCmsCache) {
    worldStateCmsCache = {};
    _.forOwn(cms.WorldState, (elem) => {
      // if (isFilteredByCountryCode(elem)) {
      //   return;
      // }
      worldStateCmsCache[elem.id] = elem;
    });
  }
  return worldStateCmsCache;
}

// CMS 캐시 초기화 함수 (CMS 재로드 시 호출)
export function clearCmsCache(): void {
  achievementCmsCache = null;
  hotTimeBuffCmsCache = null;
  townNpcGlobalCmsCache = null;
  oceanNpcCmsCache = null;
  fishCmsCache = null;
  bossRaidCmsCache = null;
  discoveryCmsCache = null;
  tradeGoodsCmsCache = null;
  mateTemplateGroupCmsCache = null;
  worldStateCmsCache = null;

  // 기타 캐시들도 초기화
  Object.keys(achievementTargetsCache).forEach((key) => delete achievementTargetsCache[key]);
  townNpcShopGroupCache = null;
  itemDiscoveryCmsIdsCache = null;

  // TradeGoods 관련 캐시들 초기화
  Object.keys(tradeGoodsByCategoryCache).forEach((key) => delete tradeGoodsByCategoryCache[key]);
  unSoldTradeGoodsCache.clear();
  Object.keys(unpopularCandidateTradeGoodsCache).forEach(
    (key) => delete unpopularCandidateTradeGoodsCache[key]
  );
  Object.keys(tradeGoodsCache).forEach((key) => delete tradeGoodsCache[key]);
  tradeByTownAndTradeGoodsCmsIdCache = null;
  tradeGoodsMarketPriceVolumeBasesCache = null;
  tradeGoodsPricePercentClampCache = null;

  // Discovery 관련 캐시들 초기화
  villageDiscoveryCache = null;
  fishDiscoveryCache = null;
}

export function calcManufactureLevel(manufactureType: MANUFACTURE_TYPE, exp: number): number {
  const maxLv = cms.Const.MaxManufactureLv.value;
  for (let level = 1; level <= maxLv; level++) {
    const expData = cms.ManufactureExp[level];
    if (exp < expData.accumulateExp[manufactureType - 1]) {
      return expData.id;
    }
  }
  return maxLv;
}
