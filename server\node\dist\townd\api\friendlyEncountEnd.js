"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
const merror_1 = require("../../motiflib/merror");
const libTown = __importStar(require("../libTown"));
const userFriendlyEncount_1 = require("../../lobbyd/userFriendlyEncount");
function canBeBattleEntered(defenser, attacker, encountResult) {
    encountResult = defenser.checkFriendlyBattle(false);
    if (encountResult !== userFriendlyEncount_1.FriendlyEncountResult.START_BATTLE) {
        return encountResult;
    }
    return attacker.checkFriendlyBattle(true);
}
module.exports = async (req, res) => {
    const { userId } = req.body;
    let encountResult = req.body.encountResult;
    let curUser = libTown.getTownUser(userId);
    if (!curUser) {
        throw new merror_1.MError('/town/friendlyEncountEnd user not found', merror_1.MErrorCode.TOWN_USER_NOT_FOUND);
    }
    // 인카운트 중인 상대 유저를 찾지 못하면 리턴.
    const targetUser = libTown.getTownUser(curUser.getFriendlyEncountUserId());
    if (!targetUser) {
        libTown.endFriendlyEncount(curUser.getCurrentZone(), curUser);
        curUser.setFriendlyEncountUserId(null);
        return res.json({
            encountResult: userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_NOT_FOUND_USER,
        });
    }
    // START_BATTLE 일 경우 전투 입장 가능 여부 검사.]
    if (encountResult === userFriendlyEncount_1.FriendlyEncountResult.START_BATTLE) {
        encountResult = canBeBattleEntered(curUser, targetUser, encountResult);
    }
    const msg = {
        targetUserId: targetUser.userId,
        encountResult,
    };
    // 상대 유저한테 알림
    const townZone = targetUser.getCurrentZone();
    const recv = await townZone.onNotifyFriendlyEncountEnd(targetUser, msg);
    if (recv.encountResult !== userFriendlyEncount_1.FriendlyEncountResult.START_BATTLE) {
        libTown.endFriendlyEncount(targetUser.getCurrentZone(), targetUser);
        libTown.endFriendlyEncount(curUser.getCurrentZone(), curUser);
    }
    targetUser.setFriendlyEncountUserId(null);
    curUser.setFriendlyEncountUserId(null);
    return res.json({
        encountResult: recv.encountResult,
    });
};
//# sourceMappingURL=friendlyEncountEnd.js.map