// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

// [NOTE] For mails, MinQuantity == MaxQuantity (Reward.json).

import { Promise as promise } from 'bluebird';
import _ from 'lodash';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { getAchievementCms } from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { RewardAndPaymentSpec, RNP_TYPE } from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import {
  TryData,
  Changes,
  CHANGE_TASK_RESULT,
  UserChangeTask,
  CHANGE_TASK_REASON,
} from '../../UserChangeTask/userChangeTask';
import { opSetAchievementRewarded } from '../../UserChangeTask/userChangeOperator';
import { Sync, Resp } from '../../type/sync';
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import { ACHIEVEMENT_TYPE } from '../../../cms/achievementDesc';
import { DISCOVERY_TYPE } from '../../../cms/DiscoveryDesc';
import { RewardData } from '../../../motiflib/gameLog';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { ClientPacketHandler } from '../index';
import mlog from '../../../motiflib/mlog';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'receive_achievement_reward';
const add_rsn = null;

interface RequestBody {
  cmsIds: number[];
}

// ----------------------------------------------------------------------------
export class Cph_Common_ReceiveAchievementReward implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() { }

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;

    const { cmsIds } = reqBody;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const changeTasks: { task: UserChangeTask, achievementCmsId: number }[] = [];
    const checkedCmsId = {};
    const userAchieve = user.userAchievement.getAchievements();
    let oldAchievementPoint = user.userPoints.getPoint(cmsEx.AchievementPointCmsId);
    for (const cmsId of cmsIds) {
      const achieveCms = getAchievementCms()[cmsId];
      if (!achieveCms) {
        throw new MError(
          'invalid-cms-id',
          MErrorCode.INVALID_REQ_BODY_RECEIVE_ACHIEVEMENT_REWARD_CMS_ID,
          {
            cmsId,
          }
        );
      }
      if (checkedCmsId[cmsId]) {
        throw new MError(
          'duplicated-cms-id',
          MErrorCode.INVALID_REQ_BODY_RECEIVE_ACHIEVEMENT_REWARD_DUPLICATED_CMS_ID,
          {
            reqBody,
          }
        );
      }
      if (!userAchieve[cmsId] || userAchieve[cmsId].count < achieveCms.achievementCount) {
        throw new MError('not-enough-count', MErrorCode.NOT_ENOUGH_ACHIEVEMENT_COUNT, {
          cmsId,
          curCount: userAchieve[cmsId]?.count,
          targetCount: achieveCms.achievementCount,
        });
      }
      if (userAchieve[cmsId].isRewarded) {
        throw new MError(
          'already-received-achievement-reward',
          MErrorCode.ALREADY_RECEIVED_ACHIEVEMENT_REWARD,
          {
            cmsId,
          }
        );
      }

      changeTasks.push(
        {
          task: new UserChangeTask(
            user,
            CHANGE_TASK_REASON.RECEIVE_ACHIEVEMENT_REWARD,
            new ReceiveAchievementRewardSpec(cmsId, achieveCms.reward)
          ),
          achievementCmsId: cmsId,
        }
      );
      checkedCmsId[cmsId] = true;
    }

    return promise
      .reduce(
        changeTasks,
        (sync, changeTask) => {
          return Promise.resolve(changeTask.task.trySpec()).then((res) => {
            if (res === CHANGE_TASK_RESULT.OK) {
              return changeTask.task.apply().then((s) => {
                return _.merge<Sync, Sync>(sync, s);
              });
            } else {
              mlog.error('change task failed', {
                userId: user.userId,
                taskReason: changeTask.task.getTaskReason(),
                achievementCmsId: changeTask.achievementCmsId,
                result: res,
              });
              return sync;
            }
          });
        },
        {}
      )
      .then((sync) => {
        // glog
        for (const cmsId of cmsIds) {
          const achievementCms = getAchievementCms()[cmsId];
          let point = 0;
          const reward_data: RewardData[] = [];

          const rewardFixedCms = cms.RewardFixed[achievementCms.reward];
          for (const elem of rewardFixedCms.rewardFixed) {
            reward_data.push({
              type: REWARD_TYPE[elem.Type],
              id: elem.Id === undefined ? null : elem.Id,
              uid: null,
              amt: elem.Quantity,
            });
            if (elem.Id !== cmsEx.AchievementPointCmsId) {
              continue;
            }
            point += elem.Quantity;
          }

          if (point) {
            oldAchievementPoint += point;
          }

          if (achievementCms.achievementType === ACHIEVEMENT_TYPE.ACHIEVEMENT) {
            const { name, desc } = displayNameUtil.getAchievementDisplayNameAndDesc(achievementCms);
            user.glog('achivement', {
              rsn,
              add_rsn,
              category: achievementCms.jobType,
              flag: 2, // 1: 달성, 2: 보상 획득
              id: cmsId,
              name,
              desc,
              point,
              total_point: oldAchievementPoint,
              reward_data,
            });
          } else if (achievementCms.achievementType === ACHIEVEMENT_TYPE.SCENARIO) {
            const questCmsId = achievementCms.achievementTarget
              ? achievementCms.achievementTarget[0]
              : null;
            const questCms = cms.Quest[questCmsId];
            let story_name = questCms ? questCms.name : null;
            const achievementTargets = achievementCms.achievementTarget;
            if (achievementTargets && achievementTargets[1]) {
              const questNodeCms = cms.QuestNode[achievementTargets[1]];
              story_name = questNodeCms.name;
            }

            const admiralCms = cms.Admiral[achievementCms.extra[0]];
            user.glog('admiral_story_reward', {
              rsn,
              add_rsn,
              story_admiral_id: achievementCms.extra && admiralCms ? admiralCms.mateId : null,
              story_id: questCms ? questCms.id : null,
              story_name,
              story_step: achievementCms.extra ? achievementCms.extra[1] : null,
              is_pass: achievementCms.needQuestPassId ? 1 : 0,
              reward_data,
            });
          } else if (achievementCms.achievementType === ACHIEVEMENT_TYPE.DISCOVERY) {
            let discovery_type = null;
            if (achievementCms && achievementCms.achievementTarget[0]) {
              discovery_type = DISCOVERY_TYPE[achievementCms.achievementTarget[0]];
            }
            user.glog('discovery_book_reward', {
              rsn,
              add_rsn,
              subgroup: discovery_type,
              reward_data,
            });
          }
        }

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }
}

class ReceiveAchievementRewardSpec extends RewardAndPaymentSpec {
  constructor(private achievementCmsId: number, rewardCmsId: number) {
    super([{ type: RNP_TYPE.REWARD_FIXED, cmsId: rewardCmsId, bIsAccum: true, bIsBound: true }]);
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const res = super.accumulate(user, tryData, changes);
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      return res;
    }
    return opSetAchievementRewarded(user, tryData, changes, this.achievementCmsId);
  }
}
