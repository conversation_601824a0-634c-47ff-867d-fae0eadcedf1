"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mysqlUtil_1 = require("../mysqlUtil");
const merror_1 = require("../../motiflib/merror");
const paAccountLoad_1 = __importDefault(require("../sp/paAccountLoad"));
const paWorldUsersLoad_1 = __importDefault(require("../sp/paWorldUsersLoad"));
const typedi_1 = __importDefault(require("typedi"));
const mysqlReqRepCounter_1 = require("../mysqlReqRepCounter");
const worldNameMapping_1 = __importDefault(require("../../authd/worldNameMapping"));
function queryImpl(connection, id, curTimeUtc) {
    const ret = {
        worlds: undefined,
    };
    return (0, paAccountLoad_1.default)(connection, id)
        .then((result) => {
        if (result) {
            ret.lastWorldId = result.lastWorldId;
            return (0, paWorldUsersLoad_1.default)(connection, id);
        }
        return [];
    })
        .then((result) => {
        ret.worlds = result;
        // world name mapping(NEW FEATURE)
        for (const world of ret.worlds) {
            world.worldName = (0, worldNameMapping_1.default)(world.worldId);
        }
        return ret;
    })
        .catch((err) => {
        if (err instanceof merror_1.MError) {
            throw err;
        }
        else {
            throw new merror_1.MError(err.message, merror_1.MErrorCode.AUTH_GET_WORLDS_TXN_ERROR);
        }
    });
}
function taGetWorld(dbConnPool, id, curTimeUtc) {
    return (0, mysqlUtil_1.withTxn)(dbConnPool, __filename, (connection) => {
        return queryImpl(connection, id, curTimeUtc);
    }, undefined, typedi_1.default.get(mysqlReqRepCounter_1.MysqlReqRepCounter));
}
exports.default = taGetWorld;
//# sourceMappingURL=taGetWorld.js.map