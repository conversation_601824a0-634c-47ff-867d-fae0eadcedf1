// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import mlog from '../../../motiflib/mlog';
import { LGLoginResult } from '../../../motiflib/mhttp/linegamesApiClient';
import { PLATFORM } from '../../../motiflib/model/auth/enum';
import { DBConnPool } from '../../../mysqllib/pool';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { MRedisConnPool } from '../../../redislib/connPool';
import mconf from '../../../motiflib/mconf';
import * as mutil from '../../../motiflib/mutil';
import taEnterWorld from '../../../mysqllib/txn/taEnterWorld';
import mhttp from '../../../motiflib/mhttp';
import * as kicker from '../../kicker';
import { KICK_REASON } from '../../../motiflib/const';
import paAccountLoadLastWorldId from '../../../mysqllib/sp/paAccountLoadLastWorldId';
import paAccountUpdateLastWorldIdLastLobbyLastUserIdIsOnline from '../../../mysqllib/sp/paAccountUpdateLastWorldIdLastLobbyLastUserIdIsOnline';
import { SERVER_MIGRATION_STEP } from '../../../admind/serverMigrationUtil';
import * as crypto from 'crypto';

interface RequestBody {
  isDevLogin?: boolean;
  sessionToken: string; // 에디터인 경우 pubId
  worldId: string;
  enterWorldToken: string;
  lobbyId: string;
  platform: number; // dev 환경에서만 유효. 값이 있을 경우 mconf.platform 대신 사용한다.
  reconnect: number;
}

interface Response {
  bValidEnterWorldToken: boolean;
  accountId?: string; // (lg:gnid, editor:pubId)
  pubId?: string; // (lg:nid, editor:pubId + worldId)
  countryCreated?: string;
  userId?: number;
  accessLevel?: number;
  revision: string; // client git revision string
  patchRevision: string; // client patch git revision string
  kickReason?: number;
}

export = (req: RequestAs<RequestBody>, res: ResponseAs<JsonEmpty>) => {
  const { isDevLogin, sessionToken, worldId, enterWorldToken, lobbyId, reconnect }: RequestBody =
    req.body;
  mlog.info('/enterWorld', req.body);

  const platform =
    mconf.isDev && req.body.platform !== undefined ? req.body.platform : mconf.platform;
  const dbConnPool = Container.get(DBConnPool);
  const userCacheRedis = Container.get(MRedisConnPool);
  const authRedis = Container.of('auth-redis').get(MRedisConnPool);

  const resp: Response = {
    bValidEnterWorldToken: true,
    revision: '',
    patchRevision: '',
  };

  const curTimeUtc = mutil.curTimeUtc();
  let bWhitePassOk = false;

  return Promise.resolve()
    .then(() => {
      if (!isDevLogin && platform === PLATFORM.LINE) {
        return mhttp.lgd.login(sessionToken); // TODO jaykay: 제거
      }
      return null;
    })
    .then((lgLoginResult: LGLoginResult) => {
      if (isDevLogin) {
        resp.accountId = sessionToken;
        resp.pubId = crypto.randomBytes(16).toString('hex'); // sessionToken + ':' + worldId;
      } else if (!isDevLogin && platform === PLATFORM.LINE) {
        resp.accountId = lgLoginResult.gnid;
        resp.pubId = lgLoginResult.nid;
        resp.countryCreated = lgLoginResult.countryCreated;
        const whiteYn = lgLoginResult.whiteNidYn.toLowerCase();
        if (whiteYn === 'y') {
          bWhitePassOk = true;
        }
      } else {
        throw new Error(
          'enterWorld - invalid platform value. forget to specify the platform in service layout?'
        );
      }

      if (reconnect) {
        return paAccountLoadLastWorldId(dbConnPool.getPool(), resp.accountId).then(
          (retLastWorldId) => {
            if (worldId !== retLastWorldId) {
              resp.kickReason = KICK_REASON.RECONNECT_TO_DIFFERENT_WORLD;

              mlog.warn('reconnect to different world', {
                accountId: resp.accountId,
                fromWorld: retLastWorldId,
                toWorld: worldId,
              });
            }
          }
        );
      }
      return null;
    })
    .then(() => {
      if (!resp.kickReason) {
        const minTokenTs = curTimeUtc - mconf.enterWorldTokenExpireSec;
        return userCacheRedis['getEnterWorldToken'](resp.accountId, minTokenTs).then(
          (tokenAndRevision: string[] | null) => {
            if (!tokenAndRevision || tokenAndRevision[0] !== enterWorldToken) {
              // 중복 로그인일 가능성이 높음.
              mlog.warn('taEnterWorld invalid enter world token', {
                storedEnterWorldToken: tokenAndRevision ? tokenAndRevision[0] : null,
                enterWorldToken,
                body: req.body,
              });
              resp.bValidEnterWorldToken = false;
              return null;
            }

            resp.revision = tokenAndRevision[1];
            resp.patchRevision = tokenAndRevision[2];

            // 대기열의 순번 체크적용(최대동접에 의한 차단은 대기열에서 고려함)
            if (!bWhitePassOk) {
              // 입장하는 월드의 허용 순번과 유저의 순번을 비교한다(redis의 worldId와 현재월드도 체크)
              if (!tokenAndRevision[3] || tokenAndRevision[3].length === 0) {
                mlog.warn('enterWorld invalid worldId and orderId', {
                  body: req.body,
                });

                resp.kickReason = KICK_REASON.INVALID_ORDER_ID;
                return null;
              }

              // worldId와 orderId는 항상 같이 존재
              const orderWorldId = tokenAndRevision[3];
              const orderId = parseInt(tokenAndRevision[4], 10);

              // 입장하는 월드의 허용 순번과 유저의 순번을 비교한다(redis의 worldId와 현재월드도 체크)
              if (orderWorldId !== worldId) {
                resp.kickReason = KICK_REASON.ENTERING_TO_DIFFERENT_ORDER_WORLD;
                mlog.warn('enterWorld different worldIds', {
                  orderWorldId,
                  worldId,
                });

                return null;
              }

              // orderId > 0 인 경우만 처리
              if (orderId > 0) {
                const orderRedis = Container.of('order-redis').get(MRedisConnPool);
                // 허용 순번을 조회한다
                return orderRedis['getLastAllowedOrder'](worldId).then((retResult) => {
                  if (retResult) {
                    const lastAllowedOrderId = parseInt(retResult, 10);

                    if (orderId > lastAllowedOrderId) {
                      resp.kickReason = KICK_REASON.NOT_YET_ALLOWED_ORDER_ID;

                      mlog.warn('enterWorld not yet allowed orderId', {
                        accountId: resp.accountId,
                        worldId,
                        orderId,
                        lastAllowedOrderId,
                      });

                      return null;
                    }
                  }
                });
              }
            }
          }
        );
      }
    })
    .then(() => {
      if (!resp.kickReason && resp.bValidEnterWorldToken) {
        // 여기서 중복로그인 체크진행한다
        const minHeartBeatTs = curTimeUtc - mconf.userHeartBeatInterval;
        return userCacheRedis['getUserHeartBeatWithLobbyInfo'](resp.accountId, minHeartBeatTs)
          .then((heartBeatInfoStr) => {
            const heartBeatInfo = JSON.parse(heartBeatInfoStr);

            if (heartBeatInfo.ret && heartBeatInfo.lastUserId && heartBeatInfo.lastLobbyAppId) {
              mlog.warn('/user is still online', {
                accountId: resp.accountId,
                userId: heartBeatInfo.lastUserId,
                lastLobbyAppId: heartBeatInfo.lastLobbyAppId,
              });

              return kicker.kick(
                heartBeatInfo.lastUserId,
                heartBeatInfo.lastLobbyAppId,
                KICK_REASON.DUPLICATE_LOGIN
              );
            }
            return null;
          })
          .then(() => {
            return taEnterWorld(dbConnPool.getPool(), resp.accountId, resp.pubId, worldId, lobbyId);
          })
          .then((result) => {
            resp.userId = result.userId;
            resp.accessLevel = result.accessLevel;
            resp.pubId = result.pubId;

            // 서버 이전 중인 선단인지 확인.
            return authRedis['migrateLogLoadStep'](resp.userId).then((ret) => {
              const step = parseInt(ret, 10);
              if (step !== SERVER_MIGRATION_STEP.NONE) {
                resp.kickReason = KICK_REASON.IMMIGRATING;

                mlog.warn('can not enter world while immigrating', {
                  userId: resp.userId,
                  step,
                });
              } else {
                return paAccountUpdateLastWorldIdLastLobbyLastUserIdIsOnline(
                  dbConnPool.getPool(),
                  resp.accountId,
                  worldId,
                  lobbyId,
                  result.userId,
                  1
                )
                  .then(() => {
                    //set user heartBeat
                    return userCacheRedis['updateUserHeartBeat'](resp.accountId, curTimeUtc);
                  })
                  .then(() => {
                    //kick offline sailing bot
                    const minOffSailTs = curTimeUtc - mconf.offlineSailingHeartBeatInterval;
                    return userCacheRedis['getOfflineSailingInfo'](resp.accountId, minOffSailTs);
                  })
                  .then((ret: JsonString) => {
                    const offlineSailingInfo = JSON.parse(ret);

                    // mlog.warn('getOfflineSailingInfo result', {
                    //   accountId: offlineSailingInfo.accountId,
                    //   userId: offlineSailingInfo.userId,
                    //   appId: offlineSailingInfo.appId,
                    // });

                    // offlineSailing 진행중인경우 중단시킨다
                    if (offlineSailingInfo.userId && offlineSailingInfo.appId) {
                      mlog.warn('/bot user is online', {
                        accountId: offlineSailingInfo.accountId,
                        userId: offlineSailingInfo.userId,
                        appId: offlineSailingInfo.appId,
                      });

                      return kicker.kick(
                        offlineSailingInfo.userId,
                        offlineSailingInfo.appId,
                        KICK_REASON.OFFLINE_SAILING_BOT
                      );
                    }
                  });
              }
            });
          });
      }
    })
    .then(() => {
      res.json(resp);
    })
    .catch((error: Error) => {
      mlog.error('exception:', error);
      if (error instanceof MError) {
        throw error;
      } else {
        throw new MError(error.message, MErrorCode.AUTH_ENTER_WORLD_ERROR);
      }
    });
};
