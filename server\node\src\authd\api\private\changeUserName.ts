// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { DBConnPool } from '../../../mysqllib/pool';
import taChangeUserName from '../../../mysqllib/txn/taChangeUserName';
import mlog from '../../../motiflib/mlog';
import paPreoccupancyUserLoad from '../../../mysqllib/sp/paPreoccupancyUserLoad';
import paPreoccupancyUserLoadName from '../../../mysqllib/sp/paPreoccupancyUserLoadName';
import * as mutil from '../../../motiflib/mutil';
import paReservedNameLoad from '../../../mysqllib/sp/paReservedNameLoad';
import mhttp from '../../../motiflib/mhttp';

interface RequestBody {
  userId: number;
  name: string;
  worldId: string;
  preoccupancyCode?: string;
}

// Request from motiflib/authApiClient.js::changeUserName
export = (req: RequestAs<RequestBody>, res: ResponseAs<JsonEmpty>) => {
  mlog.info('/changeUserName', req.body);

  const { userId, name, worldId, preoccupancyCode }: RequestBody = req.body;

  const dbConnPool = Container.get(DBConnPool);

  let bIsDuplicated = false;
  return mhttp.platformApi.hasBadWord(name)
    .then((bHas) => {
      if (bHas) {
        throw new MError('has-bad-word', MErrorCode.HAS_BAD_WORD, {
          name,
        });
      }
    })
    .then(() => {
      if (preoccupancyCode) {
        return paPreoccupancyUserLoad(dbConnPool.getPool(), preoccupancyCode.toUpperCase());
      }
      return null;
    })
    .then((ret) => {
      if (preoccupancyCode && (!ret || ret.game_server_id !== worldId || ret.nick_name !== name)) {
        // 코드를 유저가 넣었으나 해당 코드에 해당되는 선점이 없거나 선점한 정보와 다른 경우.
        throw new MError('invalid-preoccupancy-code', MErrorCode.AUTH_INVALID_PREOCCUPANCY_CODE, {
          body: req.body,
          ret,
        });
      } else if (preoccupancyCode) {
        // 선점한 선단명 사용하는 경우
        return null;
      } else {
        // 다른 사람이 선점한 선단명인지 확인.
        return paPreoccupancyUserLoadName(dbConnPool.getPool(), name, worldId);
      }
    })
    .then((ret) => {
      if (ret) {
        bIsDuplicated = true;
      }

      return paReservedNameLoad(dbConnPool.getPool(), worldId, name);
    })
    .then((ret) => {
      if (ret) {
        const oldExpirationTimeUtc = parseInt(ret.expirationTimeUtc, 10);
        const curTimeUtc = mutil.curTimeUtc();
        if (ret.userId !== userId && oldExpirationTimeUtc > curTimeUtc) {
          bIsDuplicated = true;
        }
      }

      if (!bIsDuplicated) {
        return taChangeUserName(dbConnPool.getPool(), userId, name);
      }
      return null;
    })
    .then((ret) => {
      if (ret) {
        bIsDuplicated = true;
      }
      res.json({ bIsDuplicated });
    })
    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.AUTH_API_CHANGE_USER_NAME_ERROR);
      }
    });
};
