"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const typedi_1 = __importDefault(require("typedi"));
const merror_1 = require("../../motiflib/merror");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const server_1 = require("../server");
const cmsEx = __importStar(require("../../cms/ex"));
const mutil = __importStar(require("../../motiflib/mutil"));
module.exports = async (req, res) => {
    const { appServiceUrl, curDate: sentTs, zoneType } = req.body;
    const curTs = mutil.curTimeUtc();
    mlog_1.default.debug('[ZONE-PING] recv', {
        body: req.body,
        curTs,
    });
    const zoneLbRedis = typedi_1.default.get(server_1.ZonelbService).get(zoneType);
    return zoneLbRedis['updateServerdPing'](appServiceUrl, curTs)
        .then((online) => {
        let stop = false;
        const afterTs = mutil.curTimeUtc();
        if (online) {
            // 현재시간과 받은시간의 차이가 기준값 이상인 경우 로그남기기
            // 레디스 스크립트 호출완료에 걸리는 시간이 10초 이상이면 로그남기기
            const diff = curTs - sentTs;
            if (Math.abs(diff) > 2 || Math.abs(afterTs - curTs) > 10) {
                mlog_1.default.warn(`updateServerdPing delayed`, {
                    appServiceUrl,
                    zoneTypeStr: cmsEx.ZoneType[zoneType],
                    curTs,
                    sentTs,
                    afterTs,
                    diff,
                    online,
                });
            }
        }
        else {
            stop = true;
            mlog_1.default.warn(`[updateServerdPing] serverd[${cmsEx.ZoneType[zoneType]}] is not online.`, {
                appServiceUrl,
            });
        }
        mlog_1.default.debug('[ZONE-PING] recv ack', {
            appServiceUrl,
            online,
            stop,
            curTs,
            afterTs,
        });
        const resp = {
            bStop: stop,
        };
        res.json(resp);
    })
        .catch((err) => {
        mlog_1.default.warn('/zonelbd/updateServerdPing error', { err: err.message, zoneType });
        if (err instanceof merror_1.MError) {
            throw err;
        }
        else {
            let errCode = merror_1.MErrorCode.INTERNAL_ERROR;
            if (cmsEx.ZoneType.TOWN == zoneType) {
                errCode = merror_1.MErrorCode.TOWN_SERVER_UPDATE_PING_ERROR;
            }
            else if (cmsEx.ZoneType.OCEAN == zoneType) {
                errCode = merror_1.MErrorCode.OCEAN_SERVER_UPDATE_PING_ERROR;
            }
            throw new merror_1.MError(err.message, errCode, err.message);
        }
    });
};
//# sourceMappingURL=updateServerdPing.js.map