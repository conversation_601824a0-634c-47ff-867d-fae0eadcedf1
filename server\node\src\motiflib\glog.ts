// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import path from 'path';
import winston from 'winston';
import mkdirp from 'mkdirp';
import Transport from 'winston-transport';
import _ from 'lodash';

import DailyRotateFile from 'winston-daily-rotate-file';
import mconf from './mconf';
import mhttp from './mhttp';
import { LineLogData } from './mhttp/linegamesLogApiClient';
import mlog from './mlog';

const LogDirName = 'glog';

export default function loggerFactory(name: string): winston.Logger {
  const logDir = path.join(mconf.log.baseDir, LogDirName);
  mkdirp.sync(LogDirName);
  return winston.createLogger({
    levels: winston.config.cli.levels,
    transports: [
      new DailyRotateFile({
        filename: path.join(logDir, name),
        level: mconf.get('log.file.level'),
        json: true,
        //timestamp: true,
        handleExceptions: false,
        datePattern: mconf.get('log.file.datePattern'),
      }),
      new LineGamesLog({
        level: mconf.get('log.file.level'),
        handleExceptions: false,
      }),
    ],
  });
}

export class LineGamesLog extends Transport {
  private _pendings: { collection: string; data: any }[] = [];
  constructor(opts?: Transport.TransportStreamOptions) {
    super(opts);
  }

  log(info: any, next: () => void): any {
    if (next) {
      setImmediate(next);
    }
    if (!mconf.http.lglogd.url) {
      return;
    }

    const collection = info.message;
    const data = info.data;
    if (!data) {
      return;
    }

    if (mhttp.lglogd.isTokenRefreshing()) {
      this._pendings.push({
        collection,
        data,
      });

      mlog.info('The glog is stacked to the pending queue while refreshing token.', {
        collection,
        data,
      });
      return;
    }

    const reqBodyData: { [collection: string]: LineLogData } = {
      [collection]: {
        collection,
        dataList: [data],
      },
    };

    let accumCount = 1;
    while (this._pendings.length > 0) {
      const elem = this._pendings.pop();
      if (!reqBodyData[elem.collection]) {
        reqBodyData[elem.collection] = {
          collection: elem.collection,
          dataList: [],
        };
      }
      reqBodyData[elem.collection].dataList.push(elem.data);

      // 최대 10건의 로그만 누적한다. (라인 게임즈와 이야기하여 최대 10건 정도의 로그만 누적하기로 이야기 함)
      accumCount++;
      if (accumCount >= 10) {
        break;
      }
    }

    mhttp.lglogd.saveCommonLog(_.values(reqBodyData)).then((isSuccess) => {
      if (!isSuccess) {
        _.forOwn(reqBodyData, (collectionData) => {
          for (const elem of collectionData.dataList) {
            this._pendings.push({
              collection: collectionData.collection,
              data: elem,
            });
          }
        });

        mlog.info('The glog is stacked to the pending queue.', {
          logData: reqBodyData,
        });
      }
    });
  }
}
