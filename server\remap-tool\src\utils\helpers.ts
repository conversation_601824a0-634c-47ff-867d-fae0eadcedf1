/**
 * 유틸리티 헬퍼 함수들
 */

/**
 * 지연 함수
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 배열을 청크 단위로 나누기
 */
export function chunk<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

/**
 * 객체 깊은 복사
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 문자열이 비어있는지 확인
 */
export function isEmpty(str: string | null | undefined): boolean {
  return !str || str.trim().length === 0;
}

/**
 * 안전한 JSON 파싱
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString);
  } catch {
    return defaultValue;
  }
}

/**
 * 파일 크기를 읽기 쉬운 형태로 변환
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

/**
 * 시간을 읽기 쉬운 형태로 변환
 */
export function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

/**
 * 현재 타임스탬프 문자열 생성
 */
export function getTimestamp(): string {
  return new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
}

/**
 * 배열에서 중복 제거
 */
export function unique<T>(array: T[]): T[] {
  return Array.from(new Set(array));
}

/**
 * 객체의 키-값 쌍을 안전하게 순회
 */
export function safeEntries<T>(obj: Record<string, T> | null | undefined): Array<[string, T]> {
  if (!obj) return [];
  return Object.entries(obj);
}

/**
 * 문자열에서 SQL 인젝션 위험 문자 이스케이프
 */
export function escapeSql(str: string): string {
  return str.replace(/'/g, "''").replace(/\\/g, '\\\\');
}

/**
 * Redis 키 패턴에서 특수 문자 이스케이프
 */
export function escapeRedisPattern(pattern: string): string {
  return pattern.replace(/[*?[\]\\]/g, '\\$&');
}

/**
 * 안전한 정수 파싱
 */
export function safeParseInt(value: string | number, defaultValue: number = 0): number {
  if (typeof value === 'number') return Math.floor(value);
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 안전한 부동소수점 파싱
 */
export function safeParseFloat(value: string | number, defaultValue: number = 0): number {
  if (typeof value === 'number') return value;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 문자열 배열을 SQL IN 절용으로 포맷
 */
export function formatSqlInClause(values: string[]): string {
  return values.map(v => `'${escapeSql(v)}'`).join(', ');
}

/**
 * 진행률 계산
 */
export function calculateProgress(current: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((current / total) * 100);
}

/**
 * 에러 메시지 정규화
 */
export function normalizeError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return String(error);
}

/**
 * 객체가 비어있는지 확인
 */
export function isEmptyObject(obj: object): boolean {
  return Object.keys(obj).length === 0;
}

/**
 * 배열이 비어있는지 확인
 */
export function isEmptyArray<T>(arr: T[]): boolean {
  return !Array.isArray(arr) || arr.length === 0;
}

/**
 * 문자열을 카멜케이스로 변환
 */
export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * 문자열을 스네이크케이스로 변환
 */
export function toSnakeCase(str: string): string {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

/**
 * 랜덤 문자열 생성
 */
export function generateRandomString(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 값이 정의되어 있는지 확인
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * 값이 null이 아닌지 확인
 */
export function isNotNull<T>(value: T | null): value is T {
  return value !== null;
}

/**
 * 값이 undefined가 아닌지 확인
 */
export function isNotUndefined<T>(value: T | undefined): value is T {
  return value !== undefined;
}

/**
 * 안전한 JSON 문자열화
 */
export function safeStringify(obj: unknown, space?: number): string {
  try {
    return JSON.stringify(obj, null, space);
  } catch {
    return String(obj);
  }
}

/**
 * 메모리 사용량을 읽기 쉬운 형태로 변환
 */
export function formatMemoryUsage(bytes: number): string {
  return formatFileSize(bytes);
}

/**
 * 현재 메모리 사용량 조회
 */
export function getMemoryUsage(): { rss: string; heapTotal: string; heapUsed: string; external: string } {
  const usage = process.memoryUsage();
  return {
    rss: formatMemoryUsage(usage.rss),
    heapTotal: formatMemoryUsage(usage.heapTotal),
    heapUsed: formatMemoryUsage(usage.heapUsed),
    external: formatMemoryUsage(usage.external),
  };
}
