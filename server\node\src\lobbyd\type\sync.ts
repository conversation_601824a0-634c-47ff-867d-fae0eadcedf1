// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import { TRADE_EVENT_TYPE } from '../../motiflib/model/town';
import { ARREST_STATE, PubStaff as PubStaff } from '../userTown';
import { SHIP_ASSIGNMENT } from '../../motiflib/model/lobby';
import { QuestBlockState, QuestState } from '../quest';
import { EncountChoice, STAT_TYPE } from '../../cms/ex';
import { BattleEndMateBattleExpGain } from '../userBattle';
import { BattleMissionClearInfo, BattleResultShip } from '../battleResult';
import { TOWN_USER_STATE } from '../../townd/townUserState';
import { WorldBuffNub } from '../userBuffs';
import { GuildShopRestrictedProduct } from '../userGuildShop';
import { OceanFleetNameDesc } from '../../motiflib/model/ocean';
import { PathEx } from '../../motiflib/model/ocean';
import { EventGame } from '../userEventGames';
import { FRIEND_STATE } from '../userFriends';
import { VillageEvent, VillageStorage } from '../villageManager';
import { SHIP_SLOT_SUB_TYPE } from '../../cms/shipSlotDesc';
import { ConsecutiveProduct } from '../userCashShop';
import { GroupRestrictedProduct, ShopRestrictedProduct } from '../userShopRestricted';
import { ShipComposeInfo } from '../userFleets';
import { NpcShopRestrictedProducts } from '../userNpcShop';
import { NPC_INTERACTION_FUNCTION_TYPE } from '../../cms/npcInteractionDesc';
import { BlindBid } from '../userBlindBid';
import { InfiniteLighthouseClearedInfo } from '../userInfiniteLighthouse';
import { UserFirstFleetInfo } from '../../motiflib/userCacheRedisHelper';
import { ResearchTask } from '../userAchievement';
import { ResearchNode } from '../userResearch';
import { Reentry } from '../userReentry';

export interface Nation {
  power?: number;
  population?: number;
  numTowns?: number;

  // 총리 관련
  lastClosedElectionSessionId?: number; // 마지막 총리 선출 시간
  election?: { [sessionId: number]: NationElectionSync };
  cabinetMembers?: { [sessionId: number]: { [userId: number]: NationCabinetMemberSync } };
  policies?: { [sessionId: number]: { [group: number]: NationPolicySync } };
  effectPromiseCmsIds?: { [sessionId: number]: number[] };
  goalPromises?: {
    [sessionId: number]: { [cmsId: number]: NationPromiseSync };
  };
  promiseConditionProgresses?: {
    [sessionId: number]: { [cmsId: number]: NationPromiseConditionProgressSync };
  };
  budget?: number; // 예산
  purchasedSupportShops?: { [sessionId: number]: { [cmsId: number]: NationSupportShopSync } }; // 응원 상점
  donation?: { [weekSessionId: number]: NationDonationSync }; // 기부
  notice?: { [sessionId: number]: string }; // 국가 공지
  myCabinetOptions?: { [sessionId: number]: UserNationCabinetOptionsSync }; // 나의 내각 관련 각종 옵션들
  cabinetLastAppointedTimes?: {
    [sessionId: number]: {
      [cmsId: number]: NationCabinetAppointedTimeSync;
    };
  }; // 직책별 내각원 임명 시간
}

export interface NationCabinetAppointedTimeSync {
  cmsId?: number;
  lastTimeUtc?: number;
}

export interface Nations {
  [nationCmsId: string]: Nation;
}

export interface NationIntimacy {
  [nodePair: string]: number;
}

export interface UpdateTime {
  population?: number;
  nationalPower?: number;
  nationIntimacy?: number;
}

// 공약
export interface NationPromiseSync {
  cmsId?: number;
  target?: number;
  clearedTimeUtc?: number; // 목표형 공약에만 있을수있다
  rewarded?: number; // 목표형 공약에만 있을수있다(1: 보상수령완료, 0(or nil): 미수령)
}

export interface NationPromiseConditionProgressSync {
  promiseConditionCmsId?: number;
  target?: number;
  count?: number;
}

// 총리 후보
export interface NationElectionCandidateSync {
  userId?: number;
  statement?: string;
  townMayorCount?: number;
  acquiredVotes?: number;
  investmentScore?: number;
  level?: number;
  createTimeUtc?: number;
  registerTimeUtc?: number;
  promises?: NationPromiseSync[];
  rank?: number; // 개표이후 지난세션정보에서 상위 n명에 표기
}

// 총리 선출
export interface NationElectionSync {
  candidates?: { [userId: number]: NationElectionCandidateSync };
  myVotedCandidateUserId?: number;
  myVotes?: number;
  isRewarded?: number;
}

// 내각
export interface NationCabinetMemberSync {
  cmsId?: number;
  userId?: number;
  wageRate?: number;
  thought?: string; // 소감
}

// 정책
export interface NationPolicySync {
  group?: number;
  step?: number;
}

// 응원 상점
export interface NationSupportShopSync {
  cmsId?: number; // 응원 상점 cmsId(구매시 추가됨)
}

// 유저별 기부 랭크
export interface NationDonationRankSync {
  userId?: number;
  score?: number;
}

export interface NationDonationSync {
  ranks?: NationDonationRankSync[]; // 기부 랭킹 리스트(지난 세션정보에는 보내지않음)
  myScore?: number; // 내 기부 랭킹 점수
  myRank?: number; // 내 기부 랭킹 (기부 랭킹 리스트에 없을 경우 nil)
  totalUserNum?: number; // 총 기부 랭킹 유저 수
  bPercent?: boolean; // 내 랭킹 표시가 백분율인지
  isRewarded?: number; // 보상 수령 여부 (1: 보상수령완료, 0(or nil): 미수령
}

export interface UserNationCabinetOptionsSync {
  amICabinetApplicant?: number; // 내가 내각 지원자인지 여부(1: 지원, 0(or nil): 미지원)
}

export interface User {
  userId?: number;
  pubId?: string;
  name?: string;
  bGameOver?: boolean;
  gameState?: number;
  lastGameState?: number;
  gameEnterState?: number;
  townUserState?: TOWN_USER_STATE;
  lastTownCmsId?: number;
  lastVillageCmsId?: number;
  buildingType?: number;
  npcInteractionFunctionType?: NPC_INTERACTION_FUNCTION_TYPE;
  nationCmsId?: number;
  lastUpdateNationTimeUtc?: number;
  companyJobCmsId?: number;
  exp?: number;
  level?: number;
  lastBlackMarketResetTimeUtc?: number;
  blackMarketResetCount?: number;
  lastRewardedAchievementPointCmsId?: number;
  noviceSupplyCount?: number;
  lastNoviceSupplyTimeUtc?: number;
  lastUpdateEnergyTimeUtc?: number;
  palaceRoyalOrderCmsId?: number;
  palaceRoyalOrderRnds?: number[];
  lastRoyalOrderCompletedTimeUtc?: number;
  palaceRoyalTitleOrderCmsId?: number;
  palaceRoyalTitleOrderRnds?: number[];
  contractedCollectorTownBuildingCmsId?: number;
  contractedCollectorWorldMapTownBuildingCmsId?: number;
  enterWorldToken?: string;
  lastGuildLeftTimeUtc?: number;
  tradeResetCount?: number;
  lastTradeResetTimeUtc?: number;
  karma?: number;
  lastKarmaUpdateTimeUtc?: number;
  usedFreeTurnTakebackCount?: number;
  usedFreePhaseTakebackCount?: number;
  lastFreeTakebackUpdateTimeUtc?: number;
  quickModeCount?: number;
  lastQuickModeCountUpdateTimeUtc?: number;
  totalSailedDays?: number;
  lastCompanyJobUpdateTimeUtc?: number;
  lastReceiveHotTimeUtc?: number;
  isAdmiralProfileOpened?: number;
  isFirstFleetProfileOpened?: number;
  westShipBuildLevel?: number;
  westShipBuildExp?: number;
  orientShipBuildLevel?: number;
  orientShipBuildExp?: number;
  freeLeaderMateSwitchCount?: number;
  freeLastLeaderMateSwitchTimeUtc?: number | null;
  usedExploreTicketCount?: number;
  lastTicketCountUpdateTimeUtc?: number;
  usedExploreQuickModeCount?: number;
  lastExploreQuickModeCountUpdateTimeUtc?: number;
  usedFreeContinuousCount?: number;
  lastUsedFreeContinuousUpdateTimeUtc?: number;
  waypointSupplyTicketUsedCount?: number;
  lastWaypointSupplyTicketUpdateTimeUtc?: number;
  accumInvestByGem?: number;
  lastHotTimeBuffUpdateTimeUtc?: number;
  curCargoPresetId?: number;
  sweepTicketCount?: number;
  buySweepTicketCount?: number;
  representedMateCmsId?: number;
  lastPaidSmuggleEnterTownCmsId?: number;
  smuggleResetCount?: number;
  lastSmuggleResetTimeUtc?: number;
  blindBidTicketCount?: number;
  isFriendlyBattleRequestable?: number;
  lastFirstFleetPresetId?: number;
  researchTotalPoint?: number;
  researchRemainingPoint?: number;
  clashState?: number;
  lastUpdateManufacturePointTimeUtc?: number;
}

export interface Point {
  cmsId?: number;
  value: number;
}

export interface Points {
  [cmsId: number]: Point;
}

export interface Reputation {
  reputation?: number;
  updateTimeUtc?: number;
}

export interface Reputations {
  [nationCmsId: string]: Reputation;
}

export interface TownDevelopment {
  id?: number; // town cms id
  i?: number; // industry exp
  c?: number; // commerce exp
  a?: number; // armory exp
  il?: number; // industry level
  cl?: number; // commerce level
  al?: number; // armory level
}

export interface TownUserInvestmentScore {
  uid?: number; // user id
  p?: number; // accumulative point
}

export interface TownNationSharePoint {
  townCmsId?: number;
  nationCmsId?: number;
  value?: number;
  preInvestment?: number;
}

export interface TownGuildSharePoint {
  townCmsId?: number;
  guildId?: number;
  value?: number;
  preInvestment?: number;
}

export interface TownGuildUserScore {
  userId: number;
  score: number;
}

export interface UserTownBoughtTradeGoods {
  townCmsId?: number;
  tradeGoods?: { [tradeGoodsCmsId: number]: number };
  lastResetTimeUtc?: number;
}

export interface UserTownBoughtSmuggleGoods {
  townCmsId?: number;
  smuggleGoods?: { [smuggleGoodsCmsId: number]: number };
  lastResetTimeUtc?: number;
}

export interface InstallmentSavings {
  accumPoint?: number;
  accumRate?: number;
  accumCount?: number;
  lastCmsId?: number;
  lastDepositTimeUtc?: number;
}

export interface ShipSlot {
  slotIndex?: number;
  mateCmsId?: number;
  isLocked?: number;
  shipSlotItemId?: number;
}

export interface ShipSlots {
  [slotIndex: number]: ShipSlot;
}

export interface ShipCargo {
  cmsId?: number;
  quantity?: number;
  pointInvested?: number;
}

export interface ShipCargos {
  [cargoCmsId: number]: ShipCargo;
}

export interface ShipEnchantedStat {
  statType: STAT_TYPE;
  value: number;
}

export interface Ship {
  id?: number;
  cmsId?: number;
  assignment?: SHIP_ASSIGNMENT;
  fleetIndex?: number;
  formationIndex?: number;
  sailor?: number;
  durability?: number;
  permanentDamage?: number;
  slots?: ShipSlots;
  cargos?: ShipCargos;
  name?: string;
  life?: number;
  isLocked?: number;
  enchantedStats?: ShipEnchantedStat[];
  enchantResult?: string;
  enchantCount?: number;
  rndStats?: number[];
  battleQuickSkills?: { [slotIndex: number]: number };
  isBound?: number;
  guid?: string; // UserChangeTask 때문에 추가함. 나중에 UserChangeTask refactoring 이후에 제거.
}

export interface ShipBuilding {
  shipId?: number;
  shipCmsId?: number;
  rndStats?: number[];
  expireTimeUtc?: number; // 선박 건조가 완료될 시간 저장
  townCmsId?: number; // 건조한 선박이 위차한 town
}

export interface Mate {
  cmsId?: number;
  loyalty?: number | null;
  stateFlags?: number;
  adventureExp?: number;
  adventureLevel?: number;
  tradeExp?: number;
  tradeLevel?: number;
  battleExp?: number;
  battleLevel?: number;
  adventureFame?: number;
  tradeFame?: number;
  battleFame?: number;
  royalTitle?: number;
  injuryExpireTimeUtc?: number;
  lastTalkTimeUtc?: number;
  awakenLv?: number;
  awakenTimeUtc?: number | null;
  passives?: { [passiveCmsId: number]: MatePassive };
  passiveLearnings?: { [passiveCmsId: number]: MatePassiveLearning };
  orderCmsIds?: number[];
  colorSkin?: number | null;
  colorEye?: number | null;
  colorHairAcc1?: number | null;
  colorHairAcc2?: number | null;
  colorHair?: number | null;
  colorBody1?: number | null;
  colorBody2?: number | null;
  colorFaceAcc1?: number | null;
  colorFaceAcc2?: number | null;
  colorFaceAcc3?: number | null;
  colorCape1?: number | null;
  colorCape2?: number | null;
  colorCape3?: number | null;
  breastSize?: number | null;
  isHideHat?: number | null;
  isHideCape?: number | null;
  isHideFace?: number | null;
  trainingGrade?: number;
  trainingPoints?: { [type: number]: number };
  trainingExpiredTimeUtc?: number;
  illusts?: { [illustCmsId: number]: boolean };
  equippedIllustCmsId?: number;
  isFavorite?: number;
  isTranscended?: number;
  transcendExpiredTimeUtc?: number;
}

export interface MateEquipment {
  id?: number;
  cmsId?: number;
  dye1?: number | null;
  dye2?: number | null;
  dye3?: number | null;
  dye4?: number | null;
  dye5?: number | null;
  dye6?: number | null;
  equippedMateCmsId?: number;
  isBound?: number;
  isCostume?: number;
  expireTimeUtc?: number;
  enchantLv?: number;
}

export interface MatePassive {
  passiveCmsId?: number;
  equipIndex?: number;
}

export interface MatePassiveLearning {
  passiveCmsId?: number;
  learnTimeUtc?: number;
}

export interface Fleet {
  fleetIndex?: number;
  battleFormationCmsId?: number;

  // 일일물빵 소비시 1미만의 값은 차감할 수 없으므로 다음날 소비할 때 추가 누적할 값.(소수점)
  remainingConsumePiece?: {
    [supplyCmsId: number]: number;
  };
}

export interface Item {
  cmsId?: number;
  count?: number;
  unboundCount?: number;
}

export interface Items {
  [cmsId: number]: Item;
}

export interface ShipBlueprintSlot {
  slotIndex?: number;
  shipSlotCmsId?: number;
}

export interface ShipBlueprint {
  cmsId?: number;
  level?: number;
  exp?: number;
  sailMasteryLevel?: number;
  sailMasteryExp?: number;
  isPurchased?: number;
  slots?: { [slotIndex: number]: ShipBlueprintSlot };
}

export interface TownRequest {
  questCmsId?: number;
  rnds?: number[];
}

export interface UserTownState {
  cmsId?: number;
  sailor?: number;
  overDraftedSailor?: number;
  arrestState?: ARREST_STATE;
}

export interface UnemployedMate {
  mateCmsId?: number;
  intimacy?: number;
  isMet?: number;
}

export interface TownMate {
  mateCmsId?: number;
  renegotiationCount?: number;
  isRenegotiation?: boolean;
  index?: number;
  negoWaitExpirationTimeUtc?: number | null;
}

export interface UserTownMate {
  townCmsId?: number;
  mates?: { [mateCmsId: number]: TownMate };
  lastUpdateTimeUtc?: number;
}

export interface TownMyMate {
  index?: number;
  mateCmsId?: number;
  sponsorableTimeUtc?: number | null;
}

export interface UserTownMyMate {
  townCmsId?: number;
  lastUpdateTimeUtc?: number;
  mates?: { [mateCmsId: number]: TownMyMate };
}

export interface BlackMarketItem {
  shopCmsId?: number;
  buyCount?: number;
  shopCount?: number;
}

export interface TownBlackMarket {
  townCmsId?: number;
  items?: BlackMarketItem[];
  lastUpdateTimeUtc?: number;
}

export interface CrazeEvent {
  totalBudget?: number;
  usedBudget?: number;
}

export interface UnpopularEvent {
  createSessionId?: number;
  totalQuantity?: number;
  usedQuantity?: number;
}

export interface Town {
  developments?: TownDevelopment;
  invests?: TownUserInvestmentScore[];
  lastInvestClosedSessionId?: number;
  nationSharePoints?: TownNationSharePoint[];
  nationInvests?: { [nationCmsId: number]: number };

  guildSharePoints?: TownGuildSharePoint[];
  guildInvests?: { [guildId: number]: number };

  mayorUserId?: { [sessionId: number]: number };
  mayorUserName?: { [sessionId: number]: string };
  mayorNationCmsId?: { [sessionId: number]: number };
  mayorTax?: { [nationCmsId: number]: number };
  lastMayorTaxUpdateTimeUtc?: { [nationCmsId: number]: number };
  mayorShipyardTax?: { [nationCmsId: number]: number };
  lastMayorShipyardTaxUpdateTimeUtc?: { [nationCmsId: number]: number };
  lastRemoteInvestmentWeeklySessionId?: number;
  mayorConsecutive?: number; // 시장 연임 횟수 (현재 시장 기준)
  reserveTradeEvent?: ReserveTradeEventNub; // 예약된 시장 마켓 이벤트 정보

  tradePricePercents?: { [tradeGoodsCmsId: number]: number };
  tradePrePricePercents?: { [tradeGoodsCmsId: number]: number };
  tradeAllSessionPricePercents?: { [tradeGoodsCmsId: number]: number[] };
  lastAllSessionUpdateTimeMs?: number;

  myInvestmentAccumPoint?: number;
  myInvestmentScore?: number;
  myInvestmentRank?: number;

  guildUserScores?: TownGuildUserScore[]; // todo
  myGuildInvestmentScore?: number;
  myGuildInvestmentRank?: number;

  allGuildUserScores?: TownUserInvestmentScore[];
  myAllGuildInvestmentScore?: number;
  myAllGuildInvestmentRank?: number;

  myBoughtTradeGoods?: UserTownBoughtTradeGoods;
  myPubMates?: UserTownMate;
  myPubMyMates?: UserTownMyMate;
  myTownState?: UserTownState;
  myBlackMarket?: TownBlackMarket;
  myPubStaffIntimacy?: PubStaff;
  myUnionRequests?: TownRequest[];
  myUnionEventRequests?: TownRequest[];

  shipyardShopRestrictedProducts?: {
    [shipyardShopCmsId: number]: ShipyardShopRestrictedProduct;
  };

  shopRestrictedProducts?: {
    [shopCmsId: number]: ShopRestrictedProduct;
  };

  crazeBudget?: CrazeEvent;
  tradeUnpopular?: UnpopularEvent;

  smugglePricePercents?: { [smuggleGoodsCmsId: number]: number };
  boughtSmuggleGoods?: UserTownBoughtSmuggleGoods;
}

export interface UserLightInfo {
  userId?: number;
  name?: string;
  nationCmsId?: number;
  level?: number;
  leaderMateCmsId?: number;
  leaderMateAwakenLevel?: number;
  pubId?: string;
  guildId?: number;
  isOnline?: number;
  lastLogoutTimeUtc?: number;
  isFirstFleetProfileOpened?: number;
  isAdmiralProfileOpened?: number;
  leaderMateTrainingGrade?: number;
  leaderMateIllustCmsId?: number;
  primeMinisterElectedCount?: number;
  representedMateCmsId?: number;
  representedMateIllustCmsId?: number;
  isOpenPresets?: { [presetId: number]: number };
  leaderMateIsTranscended?: number;
  isFriendlyBattleRequestable?: number;
}

export interface Sailing {
  // sailId of the sailing information
  sailId?: number;
  sailedDays?: number;
  // an Object representing the currently occurred disaster for ships
  // shipId => oceanDisasterCmsId
  shipsInDisaster?: { [shipId: number]: number };

  // if the fleet is wrecked or not
  isWrecked?: boolean;

  // an Object representing the currently occurred protection for fleets
  // fleetId => oceanProtectionCmsId
  fleetsInProtection?: { [fleetId: number]: number };
}

export interface UserMail {
  id?: number;
  cmsId?: number;
  state?: number;
  createTimeUtc?: number;
  readTimeUtc?: number;
  expireTimeUtc?: number;
  title?: string;
  body?: string;
  bodyFormatValue?: number;
  attachment?: string;
}

export interface QuestTempRegisters {
  [id: string]: any;
}

export interface QuestContext {
  cmsId?: number;
  nodeIdx?: number;
  blockId?: string;
  state?: QuestState;
  blockState?: QuestBlockState;
  startTimeUtc?: number;
  uflags?: number;
  lflags?: number;
  r0?: number;
  r1?: number;
  r2?: number;
  r3?: number;
  r4?: number;
  r5?: number;
  r6?: number;
  r7?: number;
  r8?: number;
  r9?: number;
  userLevel?: number; // 퀘스트를 받은 시점에 유저 레벨.
  rnd1?: number;
  rnd2?: number;
  rnd3?: number;
  accum1?: number;
  accum2?: number;
  accum3?: number;
  accum4?: number;
  accum5?: number;
  requestSlotIdx?: number;
  requestNationCmsId?: number;
  startTotalSailedDays?: number;
  isPaused?: number;
  isAdminPaused?: number;
}

export interface QuestsCompleted {
  [questCmsId: number]: boolean; // Treated as a set.
}

export interface NpcQuest {
  cmsId: number;
  lastCompletedTimeUtc: number;
}

export interface QuestData {
  completed?: QuestsCompleted;
  tempRegisters?: QuestTempRegisters;
  contexts?: { [questCmsId: number]: QuestContext };
  bSingleMode?: boolean;
  dailyLimitCompleted?: {
    counts?: { [group: number]: number };
    expireTimeUtc?: number;
  };
  npcQuests?: { [cmsId: number]: { cmsId: number; lastCompletedTimeUtc: number } };
}

export interface WorldPassiveSync {
  groupNo?: number;
  cmsId?: number;
  targetId?: number;
}

export interface BattleReward {
  Type: number;
  Id: number;
  Quantity: number;
  receivedQuantity?: number;
}

export interface GameOverLosses {
  sunkShipIds?: number[];
  injuredMateCmsIds?: { [mateCmsId: number]: number /* 부상 시간 */ };
  recoverableSailors?: { [shipId: number]: number };
}

export interface Insurance {
  insuranceCmsId?: number;
  unpaidTradeGoods?: number;
  unpaidShip?: number;
  unpaidSailor?: number;
  unpaidDucat?: number;
}

export interface CargoLoadPresetInfo {
  id?: number;
  name?: string;
  ratios?: CargoLoadPreset;
}
export interface CargoLoadPreset {
  water?: number;
  food?: number;
  lumber?: number;
  ammo?: number;
  tradeGoods?: number;
  any?: number;
}

export interface Achievement {
  cmsId?: number;
  count?: number;
  isRewarded?: number;
}

export interface Task {
  cmsId?: number;
  count?: number;
  isRewarded?: number;
  index?: number;
}

export interface Tasks {
  tasks?: { [taskCmsId: number]: Task };
  lastResetTimeUtc?: number;
  shipBuildLevel?: number;
  isCategoryRewarded?: number;
}

export interface EventMission {
  eventPageCmsId?: number;
  eventMissionCmsId?: number;
  count?: number;
  repeatedRewardReceiveCount?: number;
  isRewarded?: number;
}

export interface EventPage {
  eventMissions?: { [eventMissionCmsId: number]: EventMission };
  weeklyEventStartTimeUtc?: number;
  bCompleted?: boolean;
}

export interface PassEvent {
  eventPageCmsId?: number;

  exp?: number;
  level?: number;

  eventMissions?: {
    [eventMissionCmsId: number]: {
      eventMissionCmsId?: number;
      count?: number;
      repeatedRewardReceiveCount?: number;
      isRewarded?: number;
    };
  };
  lastDailyResetTimeUtc?: number;

  bCompleted?: boolean;
}

export interface TradeEvent {
  t: TRADE_EVENT_TYPE;
  e: number; // 만료 세션
}

export interface TradeNego {
  chance?: number;
  success?: number;
  successProbability?: number;
  // smuggle only
  canNextNego?: boolean;
}

export interface TaxFreePermit {
  cmsId?: number;
  expirationTimeUtc?: number;
}

export interface NationTowns {
  nations?: { [nationCmsId: number]: number[] };
  timestamp?: number;
}

export interface CashShopRestrictedProduct {
  cmsId?: number;
  amount?: number;
  lastBuyingTimeUtc?: number;
}

export interface CashShopFixedTermProduct {
  cmsId?: number;
  startTimeUtc?: number;
  endTimeUtc?: number;
}

export interface CashShopDailySale {
  lastUpdateTimeUtc?: number | null;
  /** daily 는 클라에서 덜 헷갈리게 하려고 붙임. */
  dailyProducts?: {
    [cashShopLimitSaleCmsId: number]: {
      cmsId?: number;
      isBought?: 0 | 1;
    };
  };
}

export interface ContributionShopRestrictedProduct {
  cmsId?: number;
  amount?: number;
  lastBuyingTimeUtc?: number;
}

export interface ShipyardShopRestrictedProduct {
  amount: number;
  expiredTimeUtc: number;
}

export interface EventShopRestrictedProduct {
  eventPageCmsId?: number;
  eventShopCmsId?: number;
  packageCount?: number;
  expiredTimeUtc?: number;
}

export interface RegionOccupation {
  cmsId?: number; // 값이 0일 경우 중립.
  bComplete?: boolean; // 지배일 경우 true. 값이 없을 경우 중립이나 예속.
}

export interface RegionOccupations {
  regions?: { [regionCmsId: number]: RegionOccupation };
  timestamp?: number;
}

export interface LandExploreResultResult {
  cargoChanges?: { [boxIdx: number]: { [cmsId: number]: number } };
  sailorChanges?: { [shipId: number]: number };
  discoveryCmsId?: number;
  discoveryBox?: number;
}

export interface LandExploreResult {
  boxes?: number;
  result?: LandExploreResultResult;
  gainItem?: number;
}

export interface ShipSailPattern {
  sailPatternCmsId?: number;
  color1?: number;
  color2?: number;
  color3?: number;
}

export interface ShipCustomizing {
  sailPatternCmsId?: number;
  sailCrestCmsId?: number;
  bodyColor1?: number;
  bodyColor2?: number;
  bodyColorTint?: number;
  camouflageCmsId?: number;
}

export interface Village {
  cmsId?: number;
  friendship?: number;
  recruitedSailor?: number;
  lastRecruitedSailorTimeUtc?: number;
  lastDepartureTimeUtc?: number; // null 인 경우 방문한 적이 없거나 방문 중인 경우임.
  friendshipFirstRewardReceiveBitflag?: number;
  lastReceiveFriendshipWeeklyRewardGrade?: number;
  lastReceiveFriendshipWeeklyRewardTimeUtc?: number;
  lastPlunderTimeUtc?: number;
  maximumFriendship?: number;
}

export interface TryingNego {
  cmsId: number;
  priceNegoProgress: boolean[];
  getGoodsNegoProgress: boolean[];
  tempConsumeFriendship: number;
}

export interface ExchangeHistory {
  exchangeVillageListCmsId: number;
  count: number;
}

export interface QuestItem {
  id?: number;
  cmsId?: number;
  questCmsId?: number;
  questRnds?: number[];
}

export interface Attendance {
  eventPageCmsId?: number;
  accum?: number;
  consecutive?: number;
  lastAttendanceTimeUtc?: number;
  lastRewardedConsecutiveAttendanceCmsId?: number;
  startTimeUtc?: number;
  endTimeUtc?: number;
}

export interface Challenge {
  cmsId: number;
  clearedDifficulty: number;
  clearedMissionField: number;
}

export interface ShipSlotItem {
  id?: number;
  shipSlotCmsId?: number;
  equippedShipId?: number;
  equippedShipSlotIdx?: number;
  isEquippedCostumeShipSlot?: boolean;
  isBound?: number;
  isLocked?: number;
  expireTimeUtc?: number;
  enchantLv?: number;
}

export interface BattleEndResult {
  mateExpGains?: BattleEndMateBattleExpGain[];
  ducatGains?: number;
  userExpGain?: number;
  fameGain?: number;
  missionsCleared?: BattleMissionClearInfo;
  numTurns?: number; // 종료 시점의 턴 번호
  mapCmsId?: number;
  oceanNpcId?: number;
  allyShips?: BattleResultShip[];
  sunkShipIds: number[];
}

export interface LineMail {
  id?: number;
  title?: string;
  body?: string;
  attachment?: string;
  state?: number;
  createTimeUtc?: number;
  readTimeUtc?: number | null;
  expireTimeUtc?: number;
}

// 부관 위임 옵션
export interface AdjutantDelegation {
  // 인카운트 위임정보
  encount: {
    // 부관위임 활성 및 비활성 여부
    enable: boolean;
    //  타겟과의 전투력차이가 value 이상일 경우 전투 선택.
    battleValue: number;
    // 타겟과의 전투력차이가 value 미만일 경우 도주 선택.
    escapeValue: number;
    // 교섭 비용 부족시 선택
    secondChoice: EncountChoice;
  };

  // 재해 위임정보
  disaster: {
    // 재해 해결 활성여부
    enable: boolean;
  };
}

export interface BattleFormation {
  cmsId?: number;
  expireTimeUtc?: number;
}
export interface BattleFormations {
  [cmsId: number]: BattleFormation;
}

export interface Shield {
  cmsId?: number;
  dailyFreeCount?: number;
  lastDailyChargeTimeUtc?: number;
  nonPurchaseCount?: number;
  purchaseCount?: number;
  isActivated?: number;
}

export interface WorldSkill {
  coolTimeUtc?: number;
}

export interface Collection {
  collectionCmsId?: number;
  slots?: {
    [slotIndex: number]: {
      stack: number;
    };
  };
}
export interface AuctionProduct {
  id?: number;
  state?: number;
  sellerUserId?: number;
  buyerUserId?: number;
  productCategory?: number;
  productCmsId?: number;
  price?: number;
  count?: number;
  extra?: string;
  createTimeUtc?: number;
  transactionTimeUtc?: number;
  expirationTimeUtc?: number;
}

// 가입된 길드 정보
export interface GuildSync {
  guildId?: number;
  guildName?: string; // 길드명
  introduction?: string | null; // 길드 소개글
  masterNotice?: string | null; // 길드장 공지글
  autoNotificationType?: number | null; // 자동알림 <타입으로 분류>
  autoNotificationParam1?: string | null; // 자동알림 파라미터1
  autoNotificationParam2?: string | null; // 자동알림 파라미터2
  joinType?: number; // 가입타입
  joinCondition?: number; // 가입 조건 타입
  joinConditionValue?: number; // 가입조건 값
  nationCondition?: number; // 국가 필터
  nationCmsId?: number; //
  grade?: number; // 길드 등급
  exp?: number; //  길드 경험치
  emblemImageCmsId?: number; // 문양id
  emblemColorCmsId?: number; // 문양색상 id
  emblemBorderCmsId?: number; // 테투리 id
  grade_alias_1?: string | null; //길드원 등급 별칭
  grade_alias_2?: string | null;
  grade_alias_3?: string | null;
  grade_alias_4?: string | null;
  newGuildUpgradePopup?: boolean; // 길드 등급 업 신규 팝업 알림여부(길드화면 들어갈때 길드등급 상승 연출 표시할지 여부)
  weeklyReward?: boolean; // 지난주 주간 랭킹보상 획득가능여부
  members?: {
    [userId: number]: GuildMemberSync;
  }; // 길드 신청자 리스트
  applicants?: {
    [userId: number]: GuildApplicantSync;
  }; // 길드 가입 신청자

  lastResetTimeUtc?: number;

  // 상회 자원(기부금으로 모인 수량)
  resources?: {
    [guildResourceCmsId: number]: number;
  };

  // 상회 토벌 클리어 난이도 정보
  clearDifficultyRaids?: { [groupNo: number]: number };

  // 선택된 버프 카테고리
  selectedBuffCategory?: number;

  // 습득한 상회 버프
  learnedGuildBuffCmsIds?: {
    [guildBuffCmsId: number]: boolean;
  };

  // 버프 아이템 등록 정보
  registeredGuildBuffItems?: {
    [guildBuffCmsId: number]: {
      [itemId: number]: {
        amount: number; //
      };
    };
  };

  // 상회토벌 버프.
  bossRaidBuffs?: {
    [guildBossRaidCmsId: number]: {
      [index: number]: {
        bossRaidBuffCmsId: number;
        level: number;
        userId: number; // 버프 구매자}
      };
    };
  };
}

export interface GuildPointSync {
  [category: number]: number;
}
export interface GuildMemberSync {
  name?: string;
  mateCmsId?: number;
  level?: number;
  grade?: number;
  isOnline?: boolean;
  logoutTimeUtc?: number | null;

  // 일일 길드포인트
  dailyGuildPoints?: {
    [category: number]: number;
  };
  // 주간 길드포인트
  weeklyGuildPoints?: {
    [category: number]: number;
  };
  // 누적 길드포인트
  accumGuildPoints?: {
    [category: number]: number;
  };

  // 수령한 일일 보상 인덱스.
  pickedDailyRewardIdxs?: number[];

  // 주간기여도 랭킹
  weeklyRanking?: number;

  // 주간, 누적 기부점수
  weeklyDonationScore?: number;
  accumDonationScore?: number;

  // 기부 정보
  donations?: {
    [guildDonationCmsId: number]: {
      count: number; // 기부 횟수
      buyCount: number; // 추가 기부 기회 구매 횟수
      lastDonationTime: number; // 마지막 기부 날짜
    };
  };

  // 상회 아이템 기부 점수
  registeredItemScore?: number;
}

export interface GuildApplicantSync {
  name?: string;
  mateCmsId?: number;
  level?: number;
  regTimeUtc?: number;
  isOnline?: boolean;
  logoutTimeUtc: number | null;
}
export interface UserGuildSync {
  // 소속 상회 정보
  guild?: GuildSync;

  // 가입 대기중인 상회리스트
  waitingJoinGuildIds?: {
    [giuldId: number]: {
      regTimeUtc?: number;
    };
  };

  // 상회 탈퇴시간.(재가입시 쿨타임 필요)
  leftGuildTimeUtc?: number;

  // 길드 제작 진행 정보
  craftProgresses?: {
    [slot: number]: {
      cmsId?: number;
      slot?: number;
      startTimeUtc?: number;
      completionTimeUtc?: number;
    };
  };

  // 길드 합성 진행 정보
  synthesisProgresses?: {
    [slot: number]: {
      cmsId?: number;
      slot?: number;
      startTimeUtc?: number;
      completionTimeUtc?: number;
    };
  };
}

// 육지탐색 모드
interface LandExploreFeature {
  weekSessionId: number;
  landExploreCmsId: number;
  exploreAdventureModeCmsId: number; // 탐험 모드 (AdventureEventMode CMS)
  gatherAdventureModeCmsId: number; // 채집 모드 (AdventureEventMode CMS)
  eventAdventureModeCmsId: number; // 이벤트 모드 (AdventureEventMode CMS)
  trainingAdventureModeCmsId: number;
  landFeatureIds: number[]; // 육지 특성 id
  maxUseItemCount: number; // 유틸 아이템 최대 수량
  advEventRewardId: number; // adventureEventReward cms id 값
}

interface DayOfEventSpot {
  eventSpotCmsId: number;
  day: number;
  isWin?: boolean;
}
interface DayOfEventSpots {
  eventSpots: DayOfEventSpot[];
  endDay: number;
}

export interface ArenaSmallShip {
  shipCmsId?: number;
  formationIndex?: number;
}
export interface ArenaSmallFleet {
  battleFormationCmsId?: number; // 진형 keyIndex
  ships?: ArenaSmallShip[]; // 진형 내 각 슬롯에 배치한 선박 keyIndex
  mateCmsId?: number; // 기함에 타고 있던 캐릭터 keyIndex
  illustCmsId?: number; // 기함에 타고 있던 캐릭터 일러스트.
}

export interface ArenaOpponentBase {
  idx: number; // 리스트 인덱스
  grade?: number; // 등급
  score?: number; // 승점
  combatPower?: number; // 전투력
  userId?: number;
  botNameDesc?: OceanFleetNameDesc; // 봇인경우 userId대신 botNameDesc로 대체
  guildname?: string; // 길드명
  state?: number; //  (0:전투가능, 1:승리, 2:무승부, 3:패배)
  smallFleet?: ArenaSmallFleet; // 상대방 함대정보
}

export interface ArenaMatchOpponent {
  opponentBase: ArenaOpponentBase;
}

export interface ArenaDefendOpponent {
  opponentBase: ArenaOpponentBase;
  changedScore?: number; // 승점변화량
  updateTimeUtc?: number; // 플레이한 시각
}

export interface ArenaSession {
  myScore?: number; // 자신의 승점
  myGrade?: number; // 자신의 등급
  myRank?: number; // 자신의 랭크
  bPercent?: boolean; // myRank값이 퍼센트인지 구별
  playedCount?: number; // 플레이 횟수
  matchList?: { [idx: number]: ArenaMatchOpponent }; // 대전 리스트
  defendList?: { [idx: number]: ArenaDefendOpponent }; // 방어 리스트
}

export interface Arena {
  ticketCount?: number; // 입장권 수량
  lastTicketUpdateTimeUtc?: number; // 지난 입장권 수량 갱신시간
  ticketBoughtCount?: number; // 입장권을 포인트로 구매한 횟수
  lastTicketBuyLimitResetTimeUtc?: number; // 지난 입장권 구매 횟수 갱신 시간
  matchListFreeRefreshableCount?: number; // 대전리스트 무료갱신가능한 횟수
  lastMatchListFreeRefreshableCountResetTimeUtc?: number; // 지난 대전리스트 무료갱신가능 횟수 재충전 시간
  combatPower?: number; // 자신의 전투력
  smallFleet?: ArenaSmallFleet; // 자신의 등록한 함대정보
  sessions?: { [sessionId: number]: ArenaSession }; // 세션별 모의전 정보(현재 세션Id에 해당 되는 ArenaSession만 사용하면 됨)
}

export interface Mileage {
  month?: number;
  value?: number;
}

export interface FleetPresetSync {
  presetId?: number;
  presetName?: string;
  ships?: {
    [formationIndex: number]: {
      shipId?: number;
      shipSlots?: {
        [slotIndex: number]: {
          shipSlotItemId?: number;
          mateCmsId?: number;
        };
      };
    };
  };
  matePassives?: {
    [mateCmsId: number]: {
      [passiveCmsId: number]: {
        equipIndex?: number;
      };
    };
  };
}

export interface SailWaypoint {
  slot?: number;
  name?: string;
  destCmsId?: number;
  destType?: number; // AUTO_SAIL_DEST_TYPE(1: Town  2: Village)
  waypoints?: PathEx[];
}

export interface TownInvestMent {
  score?: number;
  lastInvestWeeklySessionId?: number;
}
export interface TradeArea {
  tradeAreaCmsId: number;
  tradePoint: number;
  accumProfitDucat: number;
  lastUpdateTimeUtc: number;
}

export interface RaidTickets {
  [bossRaidCmsId: number]: {
    count: number; // 소지수
    buyCount: number; // 유료구매 횟수
  };
}

export interface GuildRaidTickets {
  [guildBossRaidCmsId: number]: {
    guildId: number; // 지급당시의 상회ID
    count: number; // 소지수
    buyCount: number; // 구매수
  };
}

export interface Server {
  bIsNonPK?: boolean;
}
export interface FleetDispatchSlot {
  fleetIndex?: number;
  expireTimeUtc?: number;
}

export interface FleetDispatchActionSync {
  actionCmsId: number;
  resultCmsId?: number; // UI에서 action 내용을 텍스트로 표시할때 참조 정보(항해일지용)
  fleetNameDesc?: OceanFleetNameDesc; // 전투파견인 경우 npc의 이름 정보
  townCmsId?: number; // 도시명을 표시하는 경우
}

export interface FleetDispatchRewardSync {
  Type: number;
  Id: number;
  Quantity: number;
}

export interface FleetDispatchEndMateLevelGain {
  mateCmsId: number;
  jobType: number;
  beforeLv: number;
  AfterLv: number;
}

export interface FleetDispatchEndShipChange {
  shipId: number;
  beforeLife: number;
  afterLife: number;
}

export interface FleetDispatchEndViewSync {
  mateExpGain: number;
  ducatGain: number;
  mateLevelGains: FleetDispatchEndMateLevelGain[];
  shipChanges: FleetDispatchEndShipChange[];
}

export interface FleetDispatchSync {
  fleetIndex?: number;
  dispatchCmsId?: number;
  state?: number; //
  endTimeUtc?: number;
  actionCount?: number; // 파견중에 현황동기화하는경우 클라에서 단위액션시간을 계산에필요
  actions?: FleetDispatchActionSync[];
  rewards?: FleetDispatchRewardSync[];
  lostRewards?: FleetDispatchRewardSync[];
  lostRewardIdCount?: number;
  endView?: FleetDispatchEndViewSync;
  duration?: number;
}

export interface BattleContinuousContextSync {
  state?: number;
  count?: number;
  options?: any;
  result?: any;
}

export interface ChatTranslation {
  freeCount?: number;
  lastUpdateTimeUtc?: number;
}

export interface DailySubscription {
  cmsId?: number;
  createTimeUtc?: number;
  expireTimeUtc?: number;
  lastReceiveTimeUtc?: number;
}

export interface HotSpotProduct {
  cmsId?: number;
  popupCount?: number;
  expireTimeUtc?: number;
  coolTimeUtc?: number;
  lastResetTimeUtc?: number;
}

export interface EventRanking {
  score?: number;
  receivedRewards?: {
    [offset: number]: number;
  };
  guildUserScore?: number;
  isGuildRewarded?: boolean;
}

export interface UserTitle {
  cmsId?: number;
  expiredTimeUtc?: number | null; // null일 경우 무제한.
  isEquipped?: number;
}

export interface ReserveTradeEventNub {
  reserveSessionId?: number;
  tradeEventCmsId?: number;
  tradeGoodsCmsId?: number;
}

export interface CanalInfo {
  groupId?: number;
  includeGoodsPaidUseCount?: number;
  paidUseCount?: number;
  freeUseCount?: number;
  lastUseTimeUtc?: number;
}

export interface UserGlobalNationSync {
  rewardedSupportShopCmsIds: { [sessionId: number]: number[] };
}

export interface SidekickMateSync {
  slot?: number;
  cmsId?: number;
}

export interface PetSync {
  cmsId?: number;
  sidekickSlot?: number;
}

export interface TutorialCrazeTradeEventSync {
  eventType?: number;
  townCmsId?: number;
  usedBudget?: number;
  totalBudget?: number;
}

export interface ManufactureSync {
  // 생산 레벨 정보
  expLevel?: {
    [type: number]: {
      type?: number;
      exp?: number;
      level?: number;
    };
  }

  // 생산실 정보
  roomInfo?: {
    [roomCmsId: number]: {
      [slot: number]: {
        slot: number;
        recipeId: number;
        startTimeUtc: number;
        completionTimeUtc: number;
        resultType: number;
        mateCmsIds: number[];
        successRate: number;
        greatSuccessRate: number;
        extra: string;
        wpe?: number;
      }
    };
  };

  // 해금된 레시피 정보
  unlockedRecipes?: {
    [recipeCmsId: number]: boolean;
  };
}

export interface All {
  user?: User;
  points?: Points;
  reputations?: Reputations;
  nations?: Nations;
  nationIntimacies?: NationIntimacy;
  updateTimeUtc?: UpdateTime;
  towns?: { [townCmsId: number]: Town };
  allTownInvestments?: { [townCmsId: number]: TownInvestMent };
  installmentSavings?: InstallmentSavings;
  shipBuildings?: { [shipId: number]: ShipBuilding };
  ships?: { [id: number]: Ship };
  mates?: { [cmsId: number]: Mate };
  mateEquipments?: { [id: number]: MateEquipment };
  fleets?: { [index: number]: Fleet };
  items?: Items;
  shipBlueprints?: { [cmsId: number]: ShipBlueprint };
  unemployedMates?: { [mateCmsId: number]: UnemployedMate };
  contentsTermsProgresses?: { [cmsId: number]: { [target: number]: number } };
  userLightInfos?: { [userId: number]: UserLightInfo };
  sailing?: Sailing;
  userDirectMails?: { [id: number]: UserMail };
  questData?: QuestData;
  worldBuffs?: { [id: number]: WorldBuffNub };
  worldPassives?: { [id: number]: WorldPassiveSync };
  battleRewards?: BattleReward[];
  gameOverLosses?: GameOverLosses;
  multiPvpLoss?: any;
  insurance?: Insurance;
  slotExpansions?: { [type: number]: number };
  cargoLoadPresetInfos?: { [id: number]: CargoLoadPresetInfo };
  revealedOceanDoodads?: { [offset: number]: number };
  discoveries?: { [offset: number]: number };
  achievements?: { [cmsId: number]: Achievement };
  tasks?: { [category: number]: Tasks };
  eventPages?: { [eventPageCmsId: number]: EventPage };
  passEvents?: { [eventPageCmsId: number]: PassEvent };
  tradeEvents?: { [townCmsId: number]: string[] };
  tradeNego?: TradeNego;
  taxFreePermits?: { [cmsId: number]: TaxFreePermit };
  revealedWorldMapTiles?: { [offset: number]: number };
  revealedRegions?: { [offset: number]: number };
  countryCode?: number;
  requestSlots?: { [idx: number]: number };
  nationTowns?: NationTowns;
  cashShopRestrictedProducts?: { [cmsId: number]: CashShopRestrictedProduct };
  cashShopFixedTermProducts?: { [cmsId: number]: CashShopFixedTermProduct };
  cashShopGachaBoxGuarantees?: { [cashShopCmsId: number]: number };
  cashShopDailySale?: CashShopDailySale;
  contributionShopRestrictedProducts?: {
    [cmsId: number]: ContributionShopRestrictedProduct;
  };
  guildShopRestrictedProducts?: {
    [cmsId: number]: GuildShopRestrictedProduct;
  };
  regionOccupations?: RegionOccupations;
  reportedDiscoveries?: { [offset: number]: number };
  soundPacks?: { [offset: number]: number };
  eventPageProducts?: { [offset: number]: number };
  globalRegs?: { [id: number]: number };
  questionPlaces?: { [offset: number]: number };
  reportedWorldMapTiles?: { [offset: number]: number };
  landExploreResult?: LandExploreResult;
  shipSailPatterns?: { [cmsId: number]: ShipSailPattern };
  shipSailCrests?: { [offset: number]: number };
  shipSailPatternColors?: { [offset: number]: number };
  shipBody1Colors?: { [offset: number]: number };
  shipBody2Colors?: { [offset: number]: number };
  shipCustomizing?: ShipCustomizing;
  costumeShipSlots?: { [subType: number]: number };
  villages?: { [cmsId: number]: Village };
  villageExchangeHistories?: {
    [villageCmsId: number]: { [dailySessionId: number]: ExchangeHistory[] };
  };
  villageTryingNegos?: {
    [villageCmsId: number]: {
      [sessionId: number]: { [exchangeVillageListCmsId: number]: TryingNego };
    };
  };
  villageEvents?: {
    [villageCmsId: number]: { tradeEventType: TRADE_EVENT_TYPE; expireTimeUtc: number };
  };
  villageStorages?: {
    [villageCmsId: number]: { [sessionId: number]: { [tradeGoodsCategory: number]: number } };
  };

  questItems?: { [id: number]: QuestItem };
  attendances?: { [eventPageCmsId: number]: Attendance };
  challenges?: { [cmsId: number]: Challenge };
  mateEquipmentColors?: { [offset: number]: number };
  shipSlotItems?: { [id: number]: ShipSlotItem };
  battleEndResult?: BattleEndResult;
  lineMails?: { [id: number]: LineMail };
  adjutantDelegation?: AdjutantDelegation;
  battleFormations?: BattleFormations;
  reportedPubStaffDiscoveries?: { [offset: number]: number };
  shields?: { [cmsId: number]: Shield };
  worldSkills?: { [mateCmsId: number]: { [worldSkillCmsId: number]: WorldSkill } };
  collections?: { [collectionCmsId: number]: Collection };
  auctionMyProducts?: { [id: number]: AuctionProduct };
  landExploreFeature?: LandExploreFeature;
  userGuild?: UserGuildSync;
  dayOfEventSpots?: DayOfEventSpots;
  arena?: Arena;
  mileages?: Mileage[];
  eventShopProducts?: {
    [eventPageCmsId: number]: {
      [eventShopCmdId: number]: EventShopRestrictedProduct;
    };
  };

  fleetPresets?: {
    [presetId: number]: FleetPresetSync;
  };

  sailWaypoints?: { [slot: number]: SailWaypoint };
  tradeArea?: { [tradeAreaCmsId: number]: TradeArea };

  raidTickets?: RaidTickets;
  eventGames?: {
    [eventPageCmsId: number]: {
      [eventGameCmsId: number]: EventGame;
    };
  };
  guildRaidTickets?: GuildRaidTickets;

  server?: Server;

  friends?: {
    [userId: number]: {
      state?: FRIEND_STATE;
      regTimeUtc?: number;
    };
  };

  friendPoints?: {
    [userId: number]: {
      sendDate?: number; // 행동력 보낸 날짜
      recvDate?: number; // 행동력 받은 날짜
      received?: number; // 수령여부(당일에만 수령가능) 0: 미수령 1:수령완료
      totalReceivedPts?: number; // 지금까지 수령받은 포인트
    };
  };

  fleetDispatchSlots?: { [fleetIndex: number]: FleetDispatchSlot };
  fleetDispatches?: { [fleetIndex: number]: FleetDispatchSync };
  battleContinuousContext?: BattleContinuousContextSync;
  chatTranslation?: ChatTranslation;

  dailySubscriptions?: { [cmsId: number]: DailySubscription };

  hotSpotProducts?: { [cmsId: number]: HotSpotProduct };

  eventRanking?: { [cmsId: number]: EventRanking };

  discoveryRewards?: { [offset: number]: number };

  shipCamouflages?: { [offset: number]: number };

  fishCatch?: {
    [cmsId: number]: {
      fishId?: number;
      maxSize?: number;
      isRewarded?: number;
    };
  };

  userTitles?: { [cmsId: number]: UserTitle };

  canalInfos?: { [groupId: number]: CanalInfo };

  userGlobalNation?: UserGlobalNationSync;

  wpeRemoteInvest?: {
    [townCmsId: number]: { weeklySessionId: number; investCount: number };
  };

  /*
    https://jira.line.games/browse/UWO-25955
    클라이언트는 sync.add.town.myPubStaffIntimacy  데이터는 출항시 
    데이터를 삭제하는 경우가 존재
    종업원 친밀도 클라이언트 확인을 위해 별도 sync 데이터 전달
    데이터 이원화 이슈가 있지만  클라이언트 라이브 도중 변경 및 
    테스트로 인해 contentsTerms 에 필요한 데이터만 추가로 전달
  */
  pubStaffIntimacy?: { [cmsId: number]: number };

  sidekickMates?: { [slot: number]: SidekickMateSync };

  cashShopConsecutiveProducts?: { [cmsId: number]: ConsecutiveProduct };

  pets?: { [cmsId: number]: PetSync };

  lastOpenHotSpotTimeUtc?: number;
  shopGroupRestrictedProducts?: { [groupId: number]: GroupRestrictedProduct };

  villageExchangeInfo?: {
    [exchangeVillageListCmsId: number]: {
      getGoodsQuantityPerOneCount: number;
      priceGoodsQuantitiesPerOneCount: { [goodsId: number]: number };
    };
  };

  shipCompose?: { [groupId: number]: ShipComposeInfo };

  npcShopRestrictedProducts?: NpcShopRestrictedProducts;
  blindBids?: { [cmsId: number]: BlindBid };

  clearedInfiniteLighthouseStages?: {
    [sessionId: number]: { [stageNum: number]: InfiniteLighthouseClearedInfo };
  };

  researchNodes?: { [groupId: number]: ResearchNode };
  researchTasks?: { [researchCmsId: number]: { [researchTaskCmsId: number]: ResearchTask } };

  reentrys?: { [type: number]: Reentry };

  clash?: {
    sessionId?: number;
    score?: number;
    isRewarded?: number;
    winStreak?: number;
  };

  clashRejection?: {
    count?: number;
    lastRejectTimeUtc?: number;
  };

  investmentSeason?: {
    lastSeasonCmsId?: number; // 클라에서 요청패킷 빈도 줄이기 위한 용도(현재가 1시즌인경우 nil)
    rankRewards?: {
      [seasonCmsId: number]: {
        isRewardable?: number; // 1(받을 수 있는 유저), nil(이미 받았거나 받을 수 없는 유저)
      };
    };
  };

  tutorials?: {
    crazeTradeEvent?: TutorialCrazeTradeEventSync;
  };

  manufacture?: ManufactureSync;
}

export interface Sync {
  add?: All;
  remove?: any;
}

export interface Resp {
  sync?: Sync;
}
