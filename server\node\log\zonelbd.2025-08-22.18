{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:00:38.036Z"}
{"url":"/unregisterServerd","status":"200","response-time":"3.662","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:00:38.040Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T09:00:39.283Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.952","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:00:39.283Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T09:00:39.864Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:00:39.864Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:00:39.936Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T09:00:39.937Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T09:00:39.938Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:39.938Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:39.938Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T09:00:39.938Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:39.939Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:39.939Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T09:00:39.939Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T09:00:39.939Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:39.939Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:39.939Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:39.940Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:39.940Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:39.940Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:39.940Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:00:45.476Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:01:14.190Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T09:01:14.192Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:01:14.381Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:01:14.382Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:01:14.383Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T09:01:14.385Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T09:01:14.398Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T09:01:14.398Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T09:01:14.410Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T09:01:14.411Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T09:01:14.427Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.437Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.472Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.489Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.499Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.508Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.518Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.527Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.536Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.548Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:14.561Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T09:01:14.576Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:01:14.577Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:01:17.793Z"}
{"url":"/unregisterServerd","status":"200","response-time":"77.811","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:01:17.794Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755853277,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T09:01:17.796Z"}
{"url":"/registerServerd","status":"200","response-time":"1.728","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:01:17.797Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T09:01:18.032Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.040","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:01:18.032Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755853278,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T09:01:18.034Z"}
{"url":"/registerServerd","status":"200","response-time":"1.154","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:01:18.034Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T09:01:24.578Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:04:29.568Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.159","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:04:29.569Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T09:04:30.849Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.859","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:04:30.850Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T09:04:31.444Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:04:31.444Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:04:31.455Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T09:04:31.456Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T09:04:31.457Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:31.457Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:31.458Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T09:04:31.458Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:31.458Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:31.458Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T09:04:31.458Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T09:04:31.458Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:31.459Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:31.459Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:31.459Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:31.459Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:31.459Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:31.459Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:04:37.036Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:05:10.227Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T09:05:10.229Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:05:10.376Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:05:10.377Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:05:10.378Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T09:05:10.379Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T09:05:10.390Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T09:05:10.390Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T09:05:10.401Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T09:05:10.401Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T09:05:10.410Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.415Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.441Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.456Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.465Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.474Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.481Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.488Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.495Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.502Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:05:10.513Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T09:05:10.524Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:05:10.527Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T09:05:10.846Z"}
{"url":"/unregisterServerd","status":"200","response-time":"65.558","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:05:10.846Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755853510,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T09:05:10.848Z"}
{"url":"/registerServerd","status":"200","response-time":"1.713","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:05:10.849Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"warn","message":"[updateServerdPing] serverd[OCEAN] is not online.","timestamp":"2025-08-22T09:05:11.040Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:05:11.045Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.503","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:05:11.045Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T09:05:20.527Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:05:46.010Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.816","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:05:46.011Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755853546,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T09:05:46.012Z"}
{"url":"/registerServerd","status":"200","response-time":"0.734","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:05:46.012Z"}
{"url":"/findTown","status":"200","response-time":"0.755","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:06:27.480Z"}
{"url":"/createZone","status":"200","response-time":"0.765","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:06:27.524Z"}
{"url":"/incUserCount","status":"200","response-time":"0.649","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:06:27.529Z"}
{"url":"/decUserCount","status":"200","response-time":"0.641","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:07:05.636Z"}
{"url":"/findTown","status":"200","response-time":"0.628","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:07:06.336Z"}
{"url":"/incUserCount","status":"200","response-time":"0.533","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:07:06.340Z"}
{"url":"/decUserCount","status":"200","response-time":"0.532","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:09:20.474Z"}
{"url":"/findTown","status":"200","response-time":"0.630","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:09:27.452Z"}
{"url":"/incUserCount","status":"200","response-time":"0.401","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:09:27.455Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755854118,"worldId":"UWO-GL-01","userCount":1,"botCount":0},"curTs":1755854118,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T09:15:18.087Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755854118,"afterTs":1755854118,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T09:15:18.088Z"}
{"url":"/decUserCount","status":"200","response-time":"0.645","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:25:10.799Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755854718,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755854718,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T09:25:18.087Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755854718,"afterTs":1755854718,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T09:25:18.088Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:28:15.227Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.997","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:28:15.227Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T09:28:15.788Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.842","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:28:15.788Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T09:28:16.785Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:28:16.785Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:28:17.613Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T09:28:17.614Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T09:28:17.615Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:28:17.616Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:28:17.616Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T09:28:17.616Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:28:17.617Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:28:17.617Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T09:28:17.617Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T09:28:17.617Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:28:17.617Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:28:17.617Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:28:17.617Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:28:17.617Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:28:17.618Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:28:17.618Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:28:22.771Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:28:55.488Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T09:28:55.490Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:28:55.682Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:28:55.684Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:28:55.684Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T09:28:55.685Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T09:28:55.694Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T09:28:55.695Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T09:28:55.705Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T09:28:55.705Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T09:28:55.716Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.722Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.748Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.764Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.775Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.784Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.793Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.802Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.811Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.821Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:28:55.835Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T09:28:55.850Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:28:55.852Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:28:56.299Z"}
{"url":"/unregisterServerd","status":"200","response-time":"73.214","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:28:56.300Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755854936,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T09:28:56.303Z"}
{"url":"/registerServerd","status":"200","response-time":"2.319","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:28:56.304Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T09:28:56.426Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.117","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:28:56.426Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755854936,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T09:28:56.428Z"}
{"url":"/registerServerd","status":"200","response-time":"1.545","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:28:56.429Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T09:29:05.854Z"}
{"url":"/findTown","status":"200","response-time":"0.846","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:29:24.795Z"}
{"url":"/createZone","status":"200","response-time":"0.740","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:29:24.894Z"}
{"url":"/incUserCount","status":"200","response-time":"0.682","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:29:24.899Z"}
{"url":"/findTown","status":"200","response-time":"0.677","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:29:50.978Z"}
{"url":"/decUserCount","status":"200","response-time":"0.702","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:29:50.982Z"}
{"url":"/incUserCount","status":"200","response-time":"0.586","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:29:50.983Z"}
{"url":"/decUserCount","status":"200","response-time":"0.560","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:29:51.399Z"}
{"url":"/findTown","status":"200","response-time":"0.775","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:30:00.466Z"}
{"url":"/incUserCount","status":"200","response-time":"0.584","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:30:00.469Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T09:35:37.786Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.093","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:35:37.787Z"}
{"url":"/decUserCount","status":"200","response-time":"0.631","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:35:38.886Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T09:35:39.694Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:35:39.694Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:35:39.727Z"}
{"url":"/unregisterServerd","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T09:35:40.112Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T09:35:40.113Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T09:35:40.113Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T09:35:40.114Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:35:40.115Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:35:40.115Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T09:35:40.115Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:35:40.115Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:35:40.115Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T09:35:40.116Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T09:35:40.116Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:35:40.116Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:35:40.116Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:35:40.116Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:35:40.117Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:35:40.117Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:35:40.117Z"}
