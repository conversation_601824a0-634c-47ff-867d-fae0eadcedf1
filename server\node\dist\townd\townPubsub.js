"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.init = void 0;
const bluebird_1 = require("bluebird");
const lodash_1 = __importDefault(require("lodash"));
const libTown = __importStar(require("./libTown"));
// ----------------------------------------------------------------------------
// Pubsub handler functions.
// ----------------------------------------------------------------------------
// ----------------------------------------------------------------------------
function handleTradeAllUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifyTradeAllUpdated(townZones, msg);
    });
}
// ----------------------------------------------------------------------------
function handleSmuggleAllUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifySmuggleAllUpdated(townZones, msg);
    });
}
// ----------------------------------------------------------------------------
function handleTownInvested(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifyTownInvested(townZones, msg);
    });
}
// ----------------------------------------------------------------------------
function handleDevelopmentNationSharePointChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    return bluebird_1.Promise.reduce(msg.towns, (_, townElem) => {
        return libTown.getTownZones(townElem.townCmsId).then((townZones) => {
            libTown.notifyDevelopmentNationSharePointChanged(townZones, townElem);
        });
    }, {});
}
// ----------------------------------------------------------------------------
function handleInvestmentSessionClosed(msgStr) {
    const msg = JSON.parse(msgStr);
    return bluebird_1.Promise.reduce(msg.towns, (_, townElem) => {
        return libTown.getTownZones(townElem.townCmsId).then((townZones) => {
            libTown.notifyInvestmentSessionClosed(townZones, {
                townCmsId: townElem.townCmsId,
                mayorUserId: townElem.mayorUserId,
                sessionId: msg.sessionId,
                bSeasonClosed: msg.seasonId ? true : false,
            });
        });
    }, {});
}
// ----------------------------------------------------------------------------
function handleTownMayorTaxChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    function exec(townCmsId, msg) {
        return libTown.getTownZones(townCmsId).then((townZones) => {
            libTown.notifyTownMayorTaxChanged(townZones, msg);
        });
    }
    const promises = [];
    lodash_1.default.forOwn(msg.changes, (elem, townCmsIdStr) => {
        const townCmsId = parseInt(townCmsIdStr, 10);
        const subMsg = {
            townCmsId,
            changes: elem,
        };
        promises.push(exec(townCmsId, subMsg));
    });
    return Promise.all(promises);
}
// ----------------------------------------------------------------------------
function handleTownMayorShipyardTaxChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    function exec(townCmsId, msg) {
        return libTown.getTownZones(townCmsId).then((townZones) => {
            libTown.notifyTownMayorShipyardTaxChanged(townZones, msg);
        });
    }
    const promises = [];
    lodash_1.default.forOwn(msg.changes, (elem, townCmsIdStr) => {
        const townCmsId = parseInt(townCmsIdStr, 10);
        const subMsg = {
            townCmsId,
            changes: elem,
        };
        promises.push(exec(townCmsId, subMsg));
    });
    return Promise.all(promises);
}
// ----------------------------------------------------------------------------
function handleDevTownNationSharePointChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifyDevTownNationSharePointChanged(townZones, msg);
    });
}
// ----------------------------------------------------------------------------
function handleDevTownMayorChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifyDevTownMayorChanged(townZones, msg);
    });
}
// ----------------------------------------------------------------------------
function handleSomeTradeGoodsUpdated(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifySomeTradeGoodsUpdated(townZones, msg);
    });
}
// ----------------------------------------------------------------------------
function handleTradeCrazeEventBudgetChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifyTradeCrazeEventBudgetChanged(townZones, msg);
    });
}
// ----------------------------------------------------------------------------
function handleTradeUnpopularChanged(msgStr) {
    const msg = JSON.parse(msgStr);
    return libTown.getTownZones(msg.townCmsId).then((townZones) => {
        libTown.notifyTradeUnpopularChanged(townZones, msg);
    });
}
let usingPubsub = null;
// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
const init = (pubsub) => {
    usingPubsub = pubsub;
    usingPubsub.subscribe('town_invested', handleTownInvested);
    usingPubsub.subscribe('development_nation_share_point_changed', handleDevelopmentNationSharePointChanged);
    usingPubsub.subscribe('investment_session_closed', handleInvestmentSessionClosed);
    usingPubsub.subscribe('town_mayor_tax_changed', handleTownMayorTaxChanged);
    usingPubsub.subscribe('town_mayor_shipyard_tax_changed', handleTownMayorShipyardTaxChanged);
    usingPubsub.subscribe('dev_town_nation_share_point_changed', handleDevTownNationSharePointChanged);
    usingPubsub.subscribe('town_mayor_changed', handleDevTownMayorChanged);
    usingPubsub.subscribe('trade_all_updated', handleTradeAllUpdated);
    usingPubsub.subscribe('some_trade_goods_updated', handleSomeTradeGoodsUpdated);
    usingPubsub.subscribe('trade_craze_event_budget_changed', handleTradeCrazeEventBudgetChanged);
    usingPubsub.subscribe('smuggle_all_updated', handleSmuggleAllUpdated);
    usingPubsub.subscribe('trade_unpopular_event_changed', handleTradeUnpopularChanged);
};
exports.init = init;
//# sourceMappingURL=townPubsub.js.map