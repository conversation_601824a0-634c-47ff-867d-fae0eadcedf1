"use strict";
// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.start = void 0;
const node_schedule_1 = __importDefault(require("node-schedule"));
const typedi_1 = __importDefault(require("typedi"));
const lodash_1 = __importStar(require("lodash"));
const moment_1 = __importDefault(require("moment"));
const cms_1 = __importDefault(require("../../cms"));
const cmsEx = __importStar(require("../../cms/ex"));
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const mutil = __importStar(require("../../motiflib/mutil"));
const server_1 = require("../server");
const formula_1 = require("../../formula");
const mysqlUtil_1 = require("../../mysqllib/mysqlUtil");
const ex_1 = require("../../cms/ex");
const pubsub_1 = __importDefault(require("../../redislib/pubsub"));
const townDesc_1 = require("../../cms/townDesc");
const investCompanyRanking_1 = require("../../cms/investCompanyRanking");
const rewardDesc_1 = require("../../cms/rewardDesc");
const mconf_1 = __importDefault(require("../../motiflib/mconf"));
const gameLog_1 = __importDefault(require("../../motiflib/gameLog"));
const scheduleMailHelper_1 = require("./scheduleMailHelper");
const puUserLoadNationCmsId_1 = __importDefault(require("../../mysqllib/sp/puUserLoadNationCmsId"));
const puHealth_1 = __importDefault(require("../../mysqllib/sp/puHealth"));
const mailBuilder_1 = require("../../motiflib/mailBuilder");
// true 일 경우 8시간에 한 번 발생하는 정산이 매분 발생.
const EVERY_MIN_TEST = false;
// true 일 경우 주간 정산 시 지난 주 투자가 아니라 이번 주 투자 내용을 정산함.
const THIS_WEEK_ADJUSTMENT_FOR_TEST = false;
let regionIdsStr;
let townCmsIds;
let townCmsIdsStr;
let unoccupiableTownCmsIds;
let defaultDevelopments;
let maxDevelopmentLevels;
let developmentLevelNamesStr;
let bIsUnInvestableTownByForeigner;
let defaultNations;
let defaultNationSharePoints;
function _applyInvestToNationSharePoint(curTimeUtc, bNaturalDecreaseSp, firstNations, nativeTowns) {
    mlog_1.default.info('Starting applyInvestToNationSharePoint...', { bNaturalDecreaseSp });
    const dStart = new Date();
    const app = typedi_1.default.get(server_1.RealmService);
    const { townRedis, amqpCh } = app;
    const pubsub = typedi_1.default.of('pubsub-world').get(pubsub_1.default);
    let oldDevelopmentLevels;
    let oldFirstNations;
    return townRedis['loadTownDevLvAndSharePoints'](townCmsIdsStr, developmentLevelNamesStr)
        .then((ret) => {
        oldDevelopmentLevels = JSON.parse(ret[0]);
        oldFirstNations = JSON.parse(ret[1]);
        const constCms = cms_1.default.Const;
        const curSessionId = (0, formula_1.GetFullWeeksUsingLocalTime)(curTimeUtc, cms_1.default.Define.InvestmentWeeklySessionPivotDay);
        return townRedis['applyTownInvestToNationSharePoint'](townCmsIdsStr, 
        // 타국이 투자 할수 없는 townCmsIds
        JSON.stringify(bIsUnInvestableTownByForeigner), 
        // 항구 기본 투자 레벨
        JSON.stringify(defaultDevelopments), 
        // 항구 최대 투자 레벨
        JSON.stringify(maxDevelopmentLevels), 
        // 항구 기본 국가
        JSON.stringify(defaultNations), // 5
        // 항구 국가 기본 점유율
        JSON.stringify(defaultNationSharePoints), JSON.stringify(cms_1.default.TownDevelopExp), constCms.InvestDevelopExpMinusPer.value, constCms.InvestNationSharePointPer.value, constCms.DefaultInvestMinusSharePoint.value, // 10
        constCms.InvestMinusPer.value, constCms.InvestMinusPerLimit.value, curSessionId, cms_1.default.Define.InvestmentSocreMultiplier, bNaturalDecreaseSp, // 15
        constCms.InvestMinusSharePointMaxPer.value, constCms.InvestNpcIncreaseSharePointMaxPer.value, cms_1.default.Const.InvestGuildSharePointPer.value);
    })
        .then((ret) => {
        // glog and pubsub publish
        const newNationSharePoints = JSON.parse(ret[0]);
        const newDevelopmentLevels = JSON.parse(ret[1]);
        const userScore = JSON.parse(ret[2]);
        const retNativeTowns = JSON.parse(ret[3]); // 기본국가가 점유한 도시 (자유 투자 가능한 타운들 중에)
        if (lodash_1.default.isArray(retNativeTowns)) {
            nativeTowns.push(...retNativeTowns);
        }
        lodash_1.default.forOwn(newNationSharePoints, (nationSharePoints, townCmsIdStr) => {
            if (!nationSharePoints || !nationSharePoints[0]) {
                return;
            }
            const nationSp = [];
            let rank = 1;
            for (const elem of nationSharePoints) {
                nationSp.push({
                    nation_id: elem.nationCmsId,
                    nation_name: cms_1.default.Nation[elem.nationCmsId] ? cms_1.default.Nation[elem.nationCmsId].name : null,
                    value: elem.value,
                    rank: rank++,
                });
            }
            const firstNationCmsId = nationSharePoints[0].nationCmsId;
            firstNations[townCmsIdStr] = parseInt(firstNationCmsId, 10);
            (0, gameLog_1.default)('w_town_nation', {
                _time: (0, moment_1.default)(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                server_id: mconf_1.default.worldId,
                town_id: parseInt(townCmsIdStr, 10),
                town_name: cms_1.default.Town[townCmsIdStr] ? cms_1.default.Town[townCmsIdStr].name : null,
                nation_id: parseInt(firstNationCmsId, 10),
                nation_name: cms_1.default.Nation[firstNationCmsId] ? cms_1.default.Nation[firstNationCmsId].name : null,
                nation_sp: nationSp,
            });
        });
        // 항구 점유율 1위 국가
        lodash_1.default.forOwn(userScore, (user_score, townCmsIdStr) => {
            (0, gameLog_1.default)('w_town_user_invest_score', {
                _time: (0, moment_1.default)(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                server_id: mconf_1.default.worldId,
                town_id: parseInt(townCmsIdStr, 10),
                town_name: cms_1.default.Town[townCmsIdStr] ? cms_1.default.Town[townCmsIdStr].name : null,
                user_score,
            });
        });
        lodash_1.default.forOwn(newDevelopmentLevels, (levels, townCmsIdStr) => {
            lodash_1.default.forOwn(levels, (level, type) => {
                (0, gameLog_1.default)('w_town_development_level', {
                    _time: (0, moment_1.default)(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                    server_id: mconf_1.default.worldId,
                    town_id: parseInt(townCmsIdStr, 10),
                    town_name: cms_1.default.Town[townCmsIdStr] ? cms_1.default.Town[townCmsIdStr].name : null,
                    type,
                    reason: 'weekly',
                    level,
                });
            });
        });
        const towns = [];
        for (const townCmsId of townCmsIds) {
            towns.push({
                townCmsId,
                oldDevelopmentLevels: oldDevelopmentLevels[townCmsId]
                    ? oldDevelopmentLevels[townCmsId].map(Number)
                    : [1, 1, 1],
                oldFirstNationCmsId: oldFirstNations[townCmsId]
                    ? parseInt(oldFirstNations[townCmsId], 10)
                    : null,
                updateTimeUtc: curTimeUtc,
            });
        }
        // notify investment session is closed
        const msg = {
            towns,
        };
        return pubsub.publish('development_nation_share_point_changed', JSON.stringify(msg));
    })
        .then(() => {
        return amqpCh.assertQueue(`${mconf_1.default.worldId}:development_nation_share_point_changed_as_consumable`, {
            durable: false,
        });
    })
        .then(() => {
        const msg = {
            updateTimeUtc: curTimeUtc,
        };
        amqpCh.sendToQueue(`${mconf_1.default.worldId}:development_nation_share_point_changed_as_consumable`, Buffer.from(JSON.stringify(msg)));
        mlog_1.default.info('applyInvestToNationSharePoint finished', {
            time: Math.abs(new Date().getTime() - dStart.getTime()),
        });
    })
        .catch((err) => {
        mlog_1.default.alert('applyInvestToNationSharePoint is failed', {
            err: err.message,
            stack: err.stack,
            time: Math.abs(new Date().getTime() - dStart.getTime()),
        });
        return null;
    });
}
function _closeInvestmentSession(sessionId, curTimeUtc, retries, mayorUserIds) {
    mlog_1.default.info('Starting townInvestment closeInvestmentSession...', { sessionId, retries });
    let step = 1;
    const dStart = new Date();
    const app = typedi_1.default.get(server_1.RealmService);
    const { townRedis } = app;
    const pubsub = typedi_1.default.of('pubsub-world').get(pubsub_1.default);
    const handledTownCmsIds = [];
    const promises = [];
    const investCompanyPointMinusPer = [cms_1.default.Const.InvestCompanyPointMinusPer1.value];
    for (let i = 1; i <= 5; i++) {
        investCompanyPointMinusPer.push(cms_1.default.Const[`InvestCompanyPointMinusPer${i}`].value);
    }
    for (const townCmsId of townCmsIds) {
        const promise = townRedis['closeInvestmentSession'](townCmsId, sessionId, JSON.stringify(investCompanyPointMinusPer), cms_1.default.Define.InvestmentSocreMultiplier, cms_1.default.Const.InvestGuildPointMinusPer.value).then((ret) => {
            const bHandled = parseInt(ret[0]);
            if (bHandled) {
                handledTownCmsIds.push(townCmsId);
            }
            const newMayorUserId = parseInt(ret[1]);
            if (!mutil.isNotANumber(newMayorUserId)) {
                mayorUserIds[townCmsId] = newMayorUserId;
                (0, gameLog_1.default)('w_new_mayor', {
                    _time: (0, moment_1.default)(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                    server_id: mconf_1.default.worldId,
                    town_id: townCmsId,
                    town_name: cms_1.default.Town[townCmsId] ? cms_1.default.Town[townCmsId].name : null,
                    mayor_gameUserId: mayorUserIds[townCmsId],
                    session_id: sessionId,
                });
            }
        });
        promises.push(promise);
    }
    // 이벤트 항구
    for (const townCmsId of unoccupiableTownCmsIds) {
        const promise = townRedis['closeInvestmentSessionForUnoccupiedTown'](townCmsId, sessionId);
        promises.push(promise);
    }
    const towns = [];
    return Promise.all(promises)
        .then(() => {
        step = 2;
        return townRedis['setLastInvestmentClosedSessionId'](sessionId);
    })
        .then(() => {
        step = 3;
        if (handledTownCmsIds && (0, lodash_1.isArray)(handledTownCmsIds) && handledTownCmsIds.length > 0) {
            for (const townCmsId of handledTownCmsIds) {
                towns.push({
                    townCmsId: townCmsId,
                    mayorUserId: mayorUserIds[townCmsId],
                });
            }
            // notify investment session is closed
            const msg = {
                towns,
                sessionId,
            };
            return pubsub.publish('investment_session_closed', JSON.stringify(msg));
        }
        return null;
    })
        .then(() => {
        // 총리관련 동기화
        step = 4;
        return _postProcessForNationElection(sessionId, towns);
    })
        .then(() => {
        step = 5;
        return _reward(sessionId, curTimeUtc, 0);
    })
        .then(() => {
        step = 6;
        mlog_1.default.info('closeInvestmentSession finished', {
            sessionId,
            retries,
            time: Math.abs(new Date().getTime() - dStart.getTime()),
        });
    })
        .catch((err) => {
        mlog_1.default.alert('closeInvestmentSession is failed', {
            sessionId,
            retries,
            step,
            err: err.message,
            stack: err.stack,
            time: Math.abs(new Date().getTime() - dStart.getTime()),
        });
        // const timeout = Math.min(Math.pow(2, retries), 60);
        // setTimeout(() => {
        //   _closeInvestmentSession(sessionId, curTimeUtc, retries + 1);
        // }, timeout * 1000);
    });
}
function _reward(sessionId, curTimeUtc, retries) {
    mlog_1.default.info('starting townInvestment reward...', { sessionId, retries });
    const app = typedi_1.default.get(server_1.RealmService);
    const { townRedis, userDbConnPoolMgr } = app;
    const pendingDirectMails = [];
    let succeedGroup;
    let insertMailResult;
    // 차후 기획 변경(ui에서 선택해서 정산 메일 지급)에 따라 제거될 예정.
    const tmpPromises = [];
    lodash_1.default.forOwn(userDbConnPoolMgr.getDbConnPools(), (conn) => {
        for (let i = 0; i < 10; i++) {
            tmpPromises.push((0, puHealth_1.default)(conn.getPool()));
        }
    });
    return Promise.all(tmpPromises)
        .then(() => {
        return townRedis['loadTownUserWeeklyInvestments'](townCmsIdsStr, sessionId, cms_1.default.Define.InvestmentSocreMultiplier * cms_1.default.Const.InvestCompanyRewardLimitPoint.value);
    })
        .then((ret) => {
        /*
          * 유저가 도시 투자 보상을 직접 수령하는 방식으로 변경된 이후 아래 코드 주석처리
          1. 리전 보상은 정산시 월요일 자정에 자동 발송
          2. 도시 투자 보상 (블루잼, 두캇)  선단관리:투자현황 보상 버튼으로 수령
          3. 시장 퇴직금 수령 (두캇)은 선단관리:투자현황 보상 버튼으로 수령
        */
        const weeklyInvestments = JSON.parse(ret[0]);
        /*
        const investmentScoreRanks = JSON.parse(ret[1]);
        const investmentScores = JSON.parse(ret[2]);
        const mayors = JSON.parse(ret[3]);
        const mayorTaxes = JSON.parse(ret[4]);
        */
        succeedGroup = ret[5] ? JSON.parse(ret[5]) : undefined;
        succeedGroup = succeedGroup && lodash_1.default.isArray(succeedGroup) ? succeedGroup : undefined;
        // 리전 보상 계산.
        _calcRegionReward(weeklyInvestments, curTimeUtc, succeedGroup, pendingDirectMails);
        // 타운 보상 계산
        // _calcTownReward(investmentScoreRanks, curTimeUtc, succeedGroup, pendingDirectMails);
        // 시장 퇴직금 계산.
        /*
        _calcMayorReward(
          mayors,
          mayorTaxes,
          investmentScoreRanks,
          investmentScores,
          curTimeUtc,
          succeedGroup,
          pendingDirectMails
        );
        */
        const postProcessAfterInsertMail = (group) => {
            const app = typedi_1.default.get(server_1.RealmService);
            const { townRedis } = app;
            return townRedis['addSucceedInvestmentRewardGroup'](sessionId, group);
        };
        return (0, scheduleMailHelper_1.insertMail)(pendingDirectMails, 'TownInvestment', mconf_1.default.worldId, postProcessAfterInsertMail);
    })
        .then((ret) => {
        insertMailResult = ret;
        if (insertMailResult.failedGroup.length === 0) {
            mlog_1.default.info('Towninvestment._reward insertMail is done.', { sessionId });
        }
        else {
            mlog_1.default.alert('Towninvestment._reward insertMail is partially failed.', {
                sessionId,
                insertMailResult,
            });
        }
        return townRedis['endInvestmentWeeklyReward'](sessionId, townCmsIdsStr, regionIdsStr);
    })
        .then(() => {
        mlog_1.default.info('Towninvestment._reward is done.', { sessionId });
    })
        .catch((err) => {
        mlog_1.default.alert('townInvestment._reward is failed', {
            sessionId,
            retries,
            insertMailResult,
            err: err.message,
            stack: err.stack,
        });
    });
}
function _postProcessForNationElection(sessionId, towns) {
    const app = typedi_1.default.get(server_1.RealmService);
    const { amqpCh } = app;
    return Promise.resolve()
        .then(() => {
        // 총리관련 동기화
        return amqpCh.assertQueue(`${mconf_1.default.worldId}:investment_session_closed_as_consumable`, {
            durable: false,
        });
    })
        .then(() => {
        mlog_1.default.info('_postProcessForNationElection starting', { sessionId });
        const msg = {
            towns,
            sessionId,
        };
        amqpCh.sendToQueue(`${mconf_1.default.worldId}:investment_session_closed_as_consumable`, Buffer.from(JSON.stringify(msg)));
    })
        .catch((err) => {
        mlog_1.default.alert('_postProcessForNationElection is failed', {
            sessionId,
            err: err.message,
            stack: err.stack,
        });
    });
}
function _buildDefault() {
    if (!townCmsIdsStr) {
        const regionIds = new Set();
        townCmsIds = [];
        unoccupiableTownCmsIds = [];
        lodash_1.default.forOwn(cms_1.default.Town, (elem) => {
            if (elem.ownType === cmsEx.TOWN_OWN_TYPE.UNOCCUPIABLE) {
                unoccupiableTownCmsIds.push(elem.id);
            }
            else {
                townCmsIds.push(elem.id);
                if (elem.RegionId) {
                    regionIds.add(elem.RegionId);
                }
            }
        });
        // townCmsIds = ['11000003'];
        townCmsIdsStr = JSON.stringify(townCmsIds);
        regionIdsStr = JSON.stringify(Array.from(regionIds));
        defaultDevelopments = {};
        maxDevelopmentLevels = {};
        bIsUnInvestableTownByForeigner = {};
        defaultNations = {};
        defaultNationSharePoints = {};
        for (const key of Object.keys(cms_1.default.Town)) {
            const townCms = cms_1.default.Town[key];
            const townCmsIdStr = key;
            // Build defaultDevelopments
            const defaultDevelopment = {};
            const expCms = cms_1.default.TownDevelopExp;
            const maxDevelopmentLevel = cms_1.default.Const['MaxDevelopLvOfTownSize' + townCms.townSize].value;
            if (townCms.industryExp !== 0) {
                defaultDevelopment['industry'] = townCms.industryExp;
                for (let i = 1; i <= maxDevelopmentLevel; i++) {
                    if (townCms.industryExp < expCms[i].accumulateExp) {
                        defaultDevelopment['industryLevel'] = i;
                        break;
                    }
                }
                if (defaultDevelopment['industryLevel'] === undefined) {
                    defaultDevelopment['industryLevel'] = maxDevelopmentLevel;
                }
            }
            if (townCms.commerceExp !== 0) {
                defaultDevelopment['commerce'] = townCms.commerceExp;
                for (let i = 1; i <= maxDevelopmentLevel; i++) {
                    if (townCms.commerceExp < expCms[i].accumulateExp) {
                        defaultDevelopment['commerceLevel'] = i;
                        break;
                    }
                }
                if (defaultDevelopment['commerceLevel'] === undefined) {
                    defaultDevelopment['commerceLevel'] = maxDevelopmentLevel;
                }
            }
            if (townCms.armoryExp !== 0) {
                defaultDevelopment['armory'] = townCms.armoryExp;
                for (let i = 1; i <= maxDevelopmentLevel; i++) {
                    if (townCms.armoryExp < expCms[i].accumulateExp) {
                        defaultDevelopment['armoryLevel'] = i;
                        break;
                    }
                }
                if (defaultDevelopment['armoryLevel'] === undefined) {
                    defaultDevelopment['armoryLevel'] = maxDevelopmentLevel;
                }
            }
            defaultDevelopments[townCmsIdStr] = defaultDevelopment;
            maxDevelopmentLevels[townCmsIdStr] = maxDevelopmentLevel;
            bIsUnInvestableTownByForeigner[townCmsIdStr] = (0, townDesc_1.IsUnInvestableTownByForeigner)(townCms.ownType);
            defaultNations[townCmsIdStr] = townCms.nationId;
            defaultNationSharePoints[townCmsIdStr] = townCms.sharePoint;
        }
        const developmentLevelNames = [];
        for (let i = cmsEx.DEVELOPMENT_TYPE.none + 1; i < cmsEx.DEVELOPMENT_TYPE.max; i++) {
            developmentLevelNames.push(cmsEx.DEVELOPMENT_TYPE[i] + 'Level');
        }
        developmentLevelNamesStr = JSON.stringify(developmentLevelNames);
    }
}
function _calcRegionReward(townUserWeeklyInvestments, curTimeUtc, succeedGroup, pendingDirectMails) {
    const regionInvestments = {};
    lodash_1.default.forOwn(townUserWeeklyInvestments, (investments, townCmsIdStr) => {
        const regionCmsId = cms_1.default.Town[townCmsIdStr].RegionId;
        lodash_1.default.forOwn(investments, (point, userIdStr) => {
            if (!regionInvestments[regionCmsId]) {
                regionInvestments[regionCmsId] = {};
            }
            if (!regionInvestments[regionCmsId][userIdStr]) {
                regionInvestments[regionCmsId][userIdStr] = {
                    userId: parseInt(userIdStr, 10),
                    point: 0,
                };
            }
            regionInvestments[regionCmsId][userIdStr].point += point;
        });
    });
    const scheduleMailBatchSize = (0, scheduleMailHelper_1.getScheduleMailBatchSize)(mconf_1.default.worldId);
    lodash_1.default.forOwn(regionInvestments, (investments, regionCmsIdStr) => {
        const investmentsArr = lodash_1.default.values(investments);
        investmentsArr.sort((a, b) => {
            return b.point - a.point;
        });
        if (investmentsArr[0].point === 0) {
            return;
        }
        const regionCms = cms_1.default.Region[regionCmsIdStr];
        const rewardFixedCmsId = mconf_1.default.bIsNonPK ? regionCms.nonPKOccupyReward : regionCms.occupyReward;
        const regionRewardMailCms = cms_1.default.Mail[ex_1.REGION_REWARD_MAIL_CMS_ID];
        let expireTimeUtc = null;
        let bShouldSetExpirationWhenReceiveAttachment = 0;
        if (regionRewardMailCms.mailKeepTime > 0) {
            expireTimeUtc = curTimeUtc + regionRewardMailCms.mailKeepTime;
        }
        else if (regionRewardMailCms.mailKeepTime === -1) {
            bShouldSetExpirationWhenReceiveAttachment = 1;
        }
        // 공동 1등이 발생할 경우 모두에게 보상 지급.
        for (const elem of investmentsArr) {
            if (elem.point !== investmentsArr[0].point) {
                break;
            }
            const userId = elem.userId;
            const userDbShardId = (0, mysqlUtil_1.getUserDbShardId)(userId);
            const subGroupId = Math.floor(userId / scheduleMailBatchSize);
            const groupId = `${userDbShardId}:${subGroupId}`;
            // 재시도 하는 상황.
            if (succeedGroup && succeedGroup.findIndex((elem) => elem === groupId) !== -1) {
                continue;
            }
            pendingDirectMails.push(new mailBuilder_1.BuilderMailCreateParams(null, ex_1.REGION_REWARD_MAIL_CMS_ID, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, regionCms.id, null, null, (0, ex_1.convertRewardFixedToCustomAttachmentStr)(rewardFixedCmsId, false, curTimeUtc)).getPendingMailParam(userId));
        }
    });
}
function _calcTownReward(investmentScoreRanks, curTimeUtc, succeedGroup, pendingDirectMails, bIsTmpEvent = false) {
    const scheduleMailBatchSize = (0, scheduleMailHelper_1.getScheduleMailBatchSize)(mconf_1.default.worldId);
    lodash_1.default.forOwn(investmentScoreRanks, (ranks, townCmsIdStr) => {
        if (!lodash_1.default.isArray(ranks) || ranks.length === 0) {
            return;
        }
        const townCmsId = parseInt(townCmsIdStr, 10);
        const sortedRanking = bIsTmpEvent
            ? cmsEx.getSortedEventInvestmentRanking()
            : cmsEx.getSortedInvestmentRanking();
        let lastRewardedRank = 0;
        for (const cmsElem of sortedRanking) {
            let maxRank;
            if (cmsElem.rankingType === investCompanyRanking_1.INVESTMENT_RANKING_TYPE.RANK) {
                maxRank = cmsElem.rankingRange;
            }
            else if (cmsElem.rankingType === investCompanyRanking_1.INVESTMENT_RANKING_TYPE.PERCENT) {
                maxRank = Math.floor((ranks.length * cmsElem.rankingRange) / 100);
            }
            else {
                continue;
            }
            for (let i = lastRewardedRank + 1; i <= maxRank; i++) {
                if (!ranks[i - 1]) {
                    break;
                }
                lastRewardedRank = i;
                const rewardMailCms = cms_1.default.Mail[cmsElem.mailId];
                let expireTimeUtc = null;
                let bShouldSetExpirationWhenReceiveAttachment = 0;
                if (rewardMailCms.mailKeepTime > 0) {
                    expireTimeUtc = curTimeUtc + rewardMailCms.mailKeepTime;
                }
                else if (rewardMailCms.mailKeepTime === -1) {
                    bShouldSetExpirationWhenReceiveAttachment = 1;
                }
                const userId = ranks[i - 1];
                const userDbShardId = (0, mysqlUtil_1.getUserDbShardId)(userId);
                const subGroupId = Math.floor(userId / scheduleMailBatchSize);
                const groupId = `${userDbShardId}:${subGroupId}`;
                // 재시도 하는 상황.
                if (succeedGroup && succeedGroup.findIndex((elem) => elem === groupId) !== -1) {
                    continue;
                }
                const mail = new mailBuilder_1.BuilderMailCreateParams(null, cmsElem.mailId, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, townCmsId, null, townCmsId, null).getPendingMailParam(userId);
                mail['add_rsn'] = JSON.stringify({ rank: i });
                pendingDirectMails.push(mail);
            }
            if (!ranks[lastRewardedRank]) {
                break;
            }
        }
    });
}
function _calcMayorReward(mayors, mayorTaxes, investmentScoreRanks, investmentScores, curTimeUtc, succeedGroup, pendingDirectMails) {
    const scheduleMailBatchSize = (0, scheduleMailHelper_1.getScheduleMailBatchSize)(mconf_1.default.worldId);
    lodash_1.default.forOwn(mayors, (userId, townCmsIdStr) => {
        const rewardMailCms = cms_1.default.Mail[ex_1.MAYOR_REWARD_MAIL_CMS_ID];
        let expireTimeUtc = null;
        let bShouldSetExpirationWhenReceiveAttachment = 0;
        if (rewardMailCms.mailKeepTime > 0) {
            expireTimeUtc = curTimeUtc + rewardMailCms.mailKeepTime;
        }
        else if (rewardMailCms.mailKeepTime === -1) {
            bShouldSetExpirationWhenReceiveAttachment = 1;
        }
        const userDbShardId = (0, mysqlUtil_1.getUserDbShardId)(userId);
        const subGroupId = Math.floor(userId / scheduleMailBatchSize);
        const groupId = `${userDbShardId}:${subGroupId}`;
        // 재시도 하는 상황.
        if (succeedGroup && succeedGroup.findIndex((elem) => elem !== groupId) !== -1) {
            return;
        }
        let ducat = 0;
        if (mayorTaxes[townCmsIdStr]) {
            ducat += Math.floor((mayorTaxes[townCmsIdStr] * cms_1.default.Const.TaxConversionRate.value) / 1000);
        }
        let rawScore = 0;
        if (investmentScores[townCmsIdStr] && investmentScores[townCmsIdStr][userId]) {
            rawScore = investmentScores[townCmsIdStr][userId];
        }
        const ducatByScore = Math.floor(rawScore / cms_1.default.Define.InvestmentSocreMultiplier) *
            cms_1.default.Const.InvestCompanyPointPer.value;
        ducat += Math.floor((ducatByScore * cms_1.default.Const.PersonalInvestConversionRate.value) / 1000);
        ducat = Math.min(ducat, Math.floor((ducatByScore * cms_1.default.Const.PersonalInvestMaxConversionRate.value) / 1000));
        ducat = Math.max(ducat, cms_1.default.Const.MayorMinSalary.value);
        const reward = {
            Type: rewardDesc_1.REWARD_TYPE.POINT,
            Id: cmsEx.DucatPointCmsId,
            Quantity: ducat,
        };
        const townCmsId = parseInt(townCmsIdStr, 10);
        pendingDirectMails.push(new mailBuilder_1.BuilderMailCreateParams(null, rewardMailCms.id, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, townCmsId, null, townCmsId, JSON.stringify([reward])).getPendingMailParam(userId));
    });
    lodash_1.default.forOwn(investmentScoreRanks, (ranks, townCmsIdStr) => {
        if (!lodash_1.default.isArray(ranks) || ranks.length === 0) {
            return;
        }
        const townCmsId = parseInt(townCmsIdStr, 10);
        let sumScore = 0;
        for (let i = 1; i < Math.min(cms_1.default.Const.InvestDividendRankingLimit.value, ranks.length); i++) {
            const userId = ranks[i];
            const score = Math.floor(investmentScores[townCmsId][userId] / cms_1.default.Define.InvestmentSocreMultiplier);
            sumScore += score;
        }
        let totalRankerTax = 0;
        if (mayorTaxes[townCmsIdStr]) {
            totalRankerTax = Math.floor((mayorTaxes[townCmsIdStr] * cms_1.default.Const.InvestDividendTaxRate.value) / 1000);
        }
        for (let i = 1; i < Math.min(cms_1.default.Const.InvestDividendRankingLimit.value, ranks.length); i++) {
            const userId = ranks[i];
            const score = Math.floor(investmentScores[townCmsId][userId] / cms_1.default.Define.InvestmentSocreMultiplier);
            const rewardMailCms = cms_1.default.Mail[cmsEx.INVEST_RANKER_MAIL_CMS_ID];
            let expireTimeUtc = null;
            let bShouldSetExpirationWhenReceiveAttachment = 0;
            if (rewardMailCms.mailKeepTime > 0) {
                expireTimeUtc = curTimeUtc + rewardMailCms.mailKeepTime;
            }
            else if (rewardMailCms.mailKeepTime === -1) {
                bShouldSetExpirationWhenReceiveAttachment = 1;
            }
            const userDbShardId = (0, mysqlUtil_1.getUserDbShardId)(userId);
            const subGroupId = Math.floor(userId / scheduleMailBatchSize);
            const groupId = `${userDbShardId}:${subGroupId}`;
            // 재시도 하는 상황.
            if (succeedGroup && succeedGroup.findIndex((elem) => elem !== groupId) !== -1) {
                continue;
            }
            let ducat = Math.ceil((totalRankerTax * score) / sumScore);
            const ducatByScore = score * cms_1.default.Const.InvestCompanyPointPer.value;
            ducat = lodash_1.default.clamp(ducat, cms_1.default.Const.InvestDividendMinSalary.value, Math.floor((ducatByScore * cms_1.default.Const.InvestDividendMaxConversionRate.value) / 1000));
            const reward = {
                Type: rewardDesc_1.REWARD_TYPE.POINT,
                Id: cmsEx.DucatPointCmsId,
                Quantity: ducat,
            };
            pendingDirectMails.push(new mailBuilder_1.BuilderMailCreateParams(null, rewardMailCms.id, curTimeUtc, expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, townCmsId, null, townCmsId, JSON.stringify([reward])).getPendingMailParam(userId));
        }
    });
}
function _closeSessionIfNeeded(mayorUserIds) {
    const app = typedi_1.default.get(server_1.RealmService);
    const { townRedis } = app;
    return townRedis['loadLastHandledInvestmentSessionId']().then((ret) => {
        const lastInvestmentClosedSessionId = parseInt(ret[0], 10);
        const lastInvestmentPostProcessForNationElectionSessionId = parseInt(ret[1], 10);
        const lastInvestmentRewardedSessionId = parseInt(ret[2], 10);
        const curTimeUtc = mutil.curTimeUtc();
        let lastWeekSessionId = (0, formula_1.GetFullWeeksUsingLocalTime)(curTimeUtc, cms_1.default.Define.InvestmentWeeklySessionPivotDay) - 1;
        if (THIS_WEEK_ADJUSTMENT_FOR_TEST) {
            lastWeekSessionId++;
        }
        if (mutil.isNotANumber(lastInvestmentClosedSessionId) ||
            lastInvestmentClosedSessionId < lastWeekSessionId) {
            return _closeInvestmentSession(lastWeekSessionId, curTimeUtc, 0, mayorUserIds);
        }
        else if (mutil.isNotANumber(lastInvestmentPostProcessForNationElectionSessionId) ||
            lastInvestmentPostProcessForNationElectionSessionId < lastWeekSessionId) {
            // 지난 주의 closing 은 완료되었고 마을투자 정산결과가 선거에 반영 완료되지 않은 경우.
            return _postProcessForNationElection(lastWeekSessionId, []);
        }
        else if (mutil.isNotANumber(lastInvestmentRewardedSessionId) ||
            lastInvestmentRewardedSessionId < lastWeekSessionId) {
            // 지난 주의 closing 은 완료되었고 보상 지급만 완료되지 않은 경우.
            return _reward(lastWeekSessionId, curTimeUtc, 0);
        }
        return null;
    });
}
function _loadUserNationCmsId(userId, outUserNationCmsIds) {
    const { userCacheRedis, userDbConnPoolMgr } = typedi_1.default.get(server_1.RealmService);
    let nationCmsId;
    return userCacheRedis['getUserNationCmsId'](userId)
        .then((ret) => {
        if (ret === null || ret === undefined) {
            return (0, puUserLoadNationCmsId_1.default)(userDbConnPoolMgr.getPoolByUserId(userId), userId);
        }
        nationCmsId = parseInt(ret, 10);
    })
        .then((ret) => {
        if (ret) {
            ret = nationCmsId;
        }
        outUserNationCmsIds[userId] = nationCmsId;
        return nationCmsId;
    });
}
function _loadMayorUserIds(outMayorUserIds) {
    const { townRedis } = typedi_1.default.get(server_1.RealmService);
    const curSessionId = (0, formula_1.GetFullWeeksUsingLocalTime)(mutil.curTimeUtc(), cms_1.default.Define.InvestmentWeeklySessionPivotDay);
    return townRedis['loadAllTownMayors'](curSessionId).then((mayorRowsStr) => {
        const mayorRows = JSON.parse(mayorRowsStr);
        for (let i = 0; i < mayorRows.length; i += 2) {
            const townCmsId = parseInt(mayorRows[i], 10);
            const userId = parseInt(mayorRows[i + 1], 10);
            outMayorUserIds[townCmsId] = userId;
        }
    });
}
function start() {
    mlog_1.default.alert('townInvestment scheduling transferred to investmentManager for townInvestmemtSeason...');
    return null;
    // 투자정산 히스토리 참고용으로 남겨둠.
    _buildDefault();
    mlog_1.default.info('Initializing scheduling job townInvestment...');
    const tmpMayorUserIds = {};
    return _closeSessionIfNeeded(tmpMayorUserIds)
        .then(() => {
        if (Object.keys(tmpMayorUserIds).length !== 0) {
            mlog_1.default.alert('Abnormal town investment session is closed.', tmpMayorUserIds);
        }
        const jobs = [];
        let hours;
        if (EVERY_MIN_TEST && mconf_1.default.isDev) {
            hours = [
                0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23,
            ];
        }
        else {
            hours = cms_1.default.Define.TownNationSharePointApplyingHours;
        }
        for (const elem of hours) {
            // 0시 정산에만 점유 점수 자연 감소를 시킨다.
            const bNaturalDecreaseSp = elem === 0;
            let hour = elem - mconf_1.default.timezone;
            if (hour < 0) {
                hour += 24;
            }
            let cron;
            if (EVERY_MIN_TEST && mconf_1.default.isDev) {
                cron = `* ${hour} * * *`;
            }
            else if (!mutil.isNotANumber(cms_1.default.Define.TownNationSharePointApplyingEveryXMin) &&
                cms_1.default.Define.TownNationSharePointApplyingEveryXMin < 60) {
                cron = `*/${cms_1.default.Define.TownNationSharePointApplyingEveryXMin} ${hour} * * *`;
            }
            else {
                cron = `0 ${hour} * * *`;
            }
            const job = node_schedule_1.default.scheduleJob(cron, (fireDate) => {
                const curTimeUtc = mutil.curTimeUtc();
                let lastWeekSessionId = (0, formula_1.GetFullWeeksUsingLocalTime)(curTimeUtc, cms_1.default.Define.InvestmentWeeklySessionPivotDay) - 1;
                if (THIS_WEEK_ADJUSTMENT_FOR_TEST) {
                    lastWeekSessionId++;
                }
                mlog_1.default.info(`Starting job townInvestment for ${fireDate}... lastWeekSessionId is ${lastWeekSessionId}.`);
                const firstNations = {};
                const nativeTowns = [];
                const mayorUserIds = {};
                const mayorNationCmsIds = {};
                const myorTaxChangedPubMsg = {
                    changes: {},
                    curTimeUtc,
                };
                return _applyInvestToNationSharePoint(curTimeUtc, bNaturalDecreaseSp, firstNations, nativeTowns)
                    .then(() => {
                    return _closeSessionIfNeeded(mayorUserIds);
                })
                    .then(() => {
                    if (Object.keys(mayorUserIds).length === 0) {
                        // 아직 시장이 한 명도 없는 경우 낭비가 있을 수 있으나 개발 편의를 위해.
                        return _loadMayorUserIds(mayorUserIds);
                    }
                    return null;
                })
                    .then(() => {
                    const promises = [];
                    lodash_1.default.forOwn(mayorUserIds, (userId) => {
                        promises.push(_loadUserNationCmsId(userId, mayorNationCmsIds));
                    });
                    return Promise.all(promises);
                })
                    .then(() => {
                    // todo 건조 세금도?
                    for (const townCmsId of nativeTowns) {
                        if (!myorTaxChangedPubMsg.changes[townCmsId]) {
                            myorTaxChangedPubMsg.changes[townCmsId] = [];
                        }
                        const mayorUserId = mayorUserIds[townCmsId];
                        let mayorNationCmsId = 0;
                        if (mayorUserId) {
                            mayorNationCmsId = mayorNationCmsIds[mayorUserId];
                        }
                        for (const nationCms of cmsEx.getSelectableNations()) {
                            const tax = (0, formula_1.getDefaultMayorTax)(nationCms.id, mayorNationCmsId, cms_1.default.Town[townCmsId].nationId) * 1000;
                            myorTaxChangedPubMsg.changes[townCmsId].push({
                                nationCmsId: nationCms.id,
                                tax,
                            });
                        }
                    }
                    const mayorTaxChanges = [];
                    for (const townCmsId of townCmsIds) {
                        if (bIsUnInvestableTownByForeigner[townCmsId]) {
                            continue;
                        }
                        if (myorTaxChangedPubMsg.changes[townCmsId]) {
                            continue;
                        }
                        const mayorUserId = mayorUserIds[townCmsId];
                        if (!mayorUserId) {
                            continue;
                        }
                        const mayorNationCmsId = mayorNationCmsIds[mayorUserId];
                        const firstNation = firstNations[townCmsId];
                        if (!firstNation) {
                            mlog_1.default.error('[TEMP] mayor tax', { townCmsId });
                            continue;
                        }
                        if (mayorNationCmsId === firstNation) {
                            continue;
                        }
                        myorTaxChangedPubMsg.changes[townCmsId] = [
                            {
                                nationCmsId: firstNation,
                                tax: cms_1.default.Const.AnotherNationDefaultTaxRate.value,
                            },
                        ];
                        mayorTaxChanges.push({
                            townCmsId,
                            nationCmsId: firstNation,
                            tax: cms_1.default.Const.AnotherNationDefaultTaxRate.value,
                        });
                    }
                    if (mayorTaxChanges.length > 0) {
                        const app = typedi_1.default.get(server_1.RealmService);
                        const { townRedis } = app;
                        return townRedis['setMayorTaxes'](JSON.stringify(mayorTaxChanges));
                    }
                    return null;
                })
                    .then(() => {
                    if (Object.keys(myorTaxChangedPubMsg.changes).length > 0) {
                        const pubsub = typedi_1.default.of('pubsub-world').get(pubsub_1.default);
                        return pubsub.publish('town_mayor_tax_changed', JSON.stringify(myorTaxChangedPubMsg));
                    }
                    return null;
                })
                    .catch((err) => {
                    mlog_1.default.alert('TownInvestment schedule job is failed.', {
                        err: err.message,
                        stack: err.stack,
                    });
                });
            });
            jobs.push(job);
        }
        return jobs;
    })
        .catch((err) => {
        mlog_1.default.alert('TownInvestment is failed.', {
            err: err.message,
            stack: err.stack,
        });
    });
}
exports.start = start;
//# sourceMappingURL=_townInvestment.js.map