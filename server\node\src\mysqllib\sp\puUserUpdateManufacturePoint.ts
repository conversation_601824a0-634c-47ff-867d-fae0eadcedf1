// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MErrorCode } from '../../motiflib/merror';

export const spName = 'mp_u_user_update_manufacture_point';
export const errorCode = MErrorCode.MANUFACTURE_UPDATE_POINT_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  point: number,
  curTimeUtc: number
): Promise<void> {
  return spFunction(connection, userId, point, curTimeUtc)
    .then(() => {
      return;
    })
    .catch(catchHandler);
}
