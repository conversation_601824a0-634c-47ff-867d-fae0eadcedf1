// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import moment from 'moment';

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { RegisteredGameServer } from '../../../motiflib/mhttp/linegamesApiClient';
import mconf from '../../../motiflib/mconf';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { PLATFORM } from '../../../motiflib/model/auth/enum';
import * as mutil from '../../../motiflib/mutil';
import { DBConnPool } from '../../../mysqllib/pool';
import taGetWorld from '../../../mysqllib/txn/taGetWorld';
import glog from '../../../motiflib/gameLog';
import { MRedisConnPool } from '../../../redislib/connPool';
import { WORLD_STATE_TYPE_ID, WorldStateDesc } from '../../../cms/worldStateDesc';
import { WorldStateUtil } from '../../worldState';

interface RequestBody {}

interface World {
  worldId: string;
  nationCmsId?: number;
  worldStateCmsId?: number;
}

interface Response {
  worlds: World[];
}

export = (req: RequestAs<RequestBody>, res: ResponseAs<Response>) => {
  // mlog.info('[RX] /getWorldStates');

  let resp: Response = {
    worlds: [],
  };

  // get current users per world
  const monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
  return monitorRedis['getUserCount']()
    .then((retuserCount) => {
      const userCount = JSON.parse(retuserCount);

      for (const worldInfo of mconf.worlds) {
        if (worldInfo.disabled) {
          continue;
        }
        let elem: World = {
          worldId: worldInfo.id,
        };

        // world state
        if (userCount.user.world[elem.worldId]) {
          const curUsers = userCount.user.world[elem.worldId];

          const wsId = WorldStateUtil.getWorldState(curUsers, mconf.maxUsersPerWorld);
          elem.worldStateCmsId = wsId;
        } else {
          // [todo] 실제 월드의 상태 체크필요 ( 아직 유저가 한명도 입장안했거나 월드가 켜져있지 않는 상황 )
          elem.worldStateCmsId = WORLD_STATE_TYPE_ID.COMFORTABLE;
        }
        resp.worlds.push(elem);
      }

      // mlog.info('[TX] /getWorldStates', { body: resp });
      res.json(resp);
    })
    .catch((error: Error) => {
      throw new MError(error.message, MErrorCode.AUTH_GET_WORLD_STATES_ERR);
    });
};
