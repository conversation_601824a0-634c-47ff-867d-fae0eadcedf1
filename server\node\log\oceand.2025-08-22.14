{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T05:00:00.080Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T05:01:48.686Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:01:48.686Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:01:48.778Z"}
{"level":"info","message":"[Session] socket disposed, uXnJpVuP","timestamp":"2025-08-22T05:01:48.779Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T05:01:48.779Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:01:48.780Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:01:48.782Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T05:01:48.784Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:01:48.784Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:01:48.785Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:01:48.785Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:01:48.785Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T05:01:48.786Z"}
{"environment":"development","type":"oceand","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:01:51.602Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.748Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.749Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.749Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.749Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.749Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.749Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.750Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.750Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:02:21.750Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:02:21.757Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:02:21.760Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:02:22.012Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:02:22.012Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:02:22.013Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:02:22.015Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:02:25.796Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:02:25.797Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:02:25.798Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:02:25.805Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:02:25.805Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:02:25.821Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:02:25.911Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.931Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.946Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.959Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.974Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:25.985Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:26.002Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:26.020Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:26.035Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:02:26.052Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:02:26.131Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:02:26.132Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:02:26.136Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:02:26.263Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:02:26.266Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:02:26.267Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:02:26.267Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:02:26.269Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:02:26.269Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:02:26.270Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:02:26.270Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:02:26.272Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:02:26.272Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:02:26.273Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:02:26.273Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:02:26.274Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:02:26.276Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:02:26.276Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T05:02:26.373Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T05:02:26.416Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:26.422Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:26.422Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:26.423Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:26.423Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:02:26.484Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:02:26.495Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:02:26.498Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T05:02:26.498Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:02:26.500Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T05:02:26.500Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:02:26.502Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:02:26.502Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755838946,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:02:26.595Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:02:26.595Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:02:27.504Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:02:27.504Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:02:28.506Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:02:28.506Z"}
{"level":"warn","message":"updateServerdPing signaled to stop. begin stopping server","timestamp":"2025-08-22T05:02:29.193Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:02:29.194Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T05:02:29.194Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:02:29.195Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:02:29.198Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T05:02:29.199Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:02:29.199Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:02:29.200Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:02:29.201Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:02:29.201Z"}
{"environment":"development","type":"oceand","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:02:36.957Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.407Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.408Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.408Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.408Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.408Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.408Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.409Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.409Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:03:00.410Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:03:00.418Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:03:00.420Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:03:00.615Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:03:00.615Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:03:00.616Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:03:00.617Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:03:03.488Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:03:03.489Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:03:03.489Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:03:03.496Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:03:03.497Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:03:03.510Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:03:03.585Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.604Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.618Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.630Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.642Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.654Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.669Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.685Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.700Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:03:03.716Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:03:03.790Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:03:03.791Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:03:03.794Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:03:03.882Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:03:03.885Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:03:03.885Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:03:03.886Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:03:03.888Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:03:03.889Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:03:03.889Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:03:03.889Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:03:03.891Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:03:03.891Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:03:03.892Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:03:03.892Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:03:03.893Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:03:03.897Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:03:03.897Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T05:03:03.987Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T05:03:04.022Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:03:04.026Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:03:04.026Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:03:04.027Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:03:04.027Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:03:04.064Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:03:04.073Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:03:04.076Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T05:03:04.076Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:03:04.077Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T05:03:04.078Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:03:04.080Z"}
{"pingInterval":2000,"curDate":1755838984,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T05:03:04.083Z"}
{"level":"info","message":"[SessionManager] session created: wlUTYLka, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:03:04.478Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:03:04.479Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T05:03:04.480Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T05:03:04.480Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.481Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.484Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.485Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.485Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.485Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.486Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.486Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:03:04.486Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T05:09:19.434Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:09:19.435Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:09:19.653Z"}
{"level":"info","message":"[Session] socket disposed, wlUTYLka","timestamp":"2025-08-22T05:09:19.654Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T05:09:19.654Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:09:19.654Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:09:19.657Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T05:09:19.658Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:09:19.658Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:09:19.659Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:09:19.659Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:09:19.659Z"}
{"environment":"development","type":"oceand","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:09:22.671Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.759Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.759Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.759Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.760Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.760Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.760Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.760Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.761Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:09:50.761Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:09:50.771Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:09:50.774Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:09:50.926Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:09:50.927Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:09:50.927Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:09:50.928Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:09:54.700Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:09:54.701Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:09:54.701Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:09:54.708Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:09:54.709Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:09:54.724Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:09:54.813Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.833Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.848Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.861Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.875Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.887Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.903Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.921Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.936Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:09:54.953Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:09:55.038Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:09:55.039Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:09:55.043Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:09:55.189Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:09:55.191Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:09:55.192Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:09:55.192Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:09:55.194Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:09:55.195Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:09:55.195Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:09:55.195Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:09:55.197Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:09:55.197Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:09:55.198Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:09:55.198Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:09:55.199Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:09:55.201Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:09:55.201Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T05:09:55.304Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T05:09:55.359Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:55.364Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:55.364Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:55.364Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:55.365Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:09:55.425Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:09:55.437Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:09:55.441Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T05:09:55.442Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:09:55.444Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T05:09:55.444Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:09:55.446Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:09:55.447Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755839395,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:09:55.537Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:09:55.537Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:09:56.448Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:09:56.449Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:09:57.450Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:09:57.450Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755839398,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:09:58.045Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:09:58.045Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:09:58.451Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:09:58.451Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:09:59.524Z"}
{"pingInterval":2000,"curDate":1755839399,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T05:09:59.528Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T05:10:05.225Z"}
{"level":"info","message":"[SessionManager] session created: VEjZgkrk, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:10:05.477Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:10:05.479Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T05:10:05.480Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T05:10:05.481Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.483Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.485Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.486Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.486Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.487Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.487Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.487Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:10:05.488Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T05:24:07.332Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:24:07.333Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:24:08.053Z"}
{"level":"info","message":"[Session] socket disposed, VEjZgkrk","timestamp":"2025-08-22T05:24:08.053Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T05:24:08.054Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:24:08.055Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:24:08.057Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T05:24:08.058Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:24:08.058Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:24:08.059Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:24:08.060Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:24:08.060Z"}
{"environment":"development","type":"oceand","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:24:11.409Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.583Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.583Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.584Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.584Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.584Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.584Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.584Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.585Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:24:40.585Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:24:40.592Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:24:40.594Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:24:40.848Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:24:40.849Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:24:40.850Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:24:40.851Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:24:44.792Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:24:44.793Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:24:44.794Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:24:44.800Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:24:44.801Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:24:44.817Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:24:44.901Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:44.921Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:44.936Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:44.947Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:44.960Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:44.972Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:44.987Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:45.003Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:45.018Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:24:45.036Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:24:45.119Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:24:45.120Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:24:45.124Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:24:45.252Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:24:45.254Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:24:45.255Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:24:45.255Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:24:45.257Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:24:45.258Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:24:45.258Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:24:45.258Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:24:45.260Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:24:45.261Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:24:45.261Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:24:45.262Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:24:45.263Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:24:45.265Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:24:45.265Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T05:24:45.367Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T05:24:45.417Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:45.421Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:45.421Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:45.422Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:45.422Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:24:45.487Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:24:45.501Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:24:45.504Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T05:24:45.504Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:24:45.506Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T05:24:45.506Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:24:45.508Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:24:45.508Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755840285,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:24:45.602Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:24:45.602Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:24:46.510Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:24:46.510Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:24:47.511Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:24:47.511Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755840288,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:24:48.010Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:24:48.010Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:24:48.588Z"}
{"pingInterval":2000,"curDate":1755840288,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T05:24:48.591Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T05:24:56.457Z"}
{"level":"info","message":"[SessionManager] session created: aNY-rdEJ, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:24:56.750Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:24:56.754Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T05:24:56.754Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T05:24:56.755Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.757Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.758Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.759Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.759Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.759Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.759Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.760Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:24:56.760Z"}
{"level":"info","message":"[!] server is stopping: type=oceand, signal=SIGINT","timestamp":"2025-08-22T05:39:49.916Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T05:39:49.916Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T05:39:50.857Z"}
{"level":"info","message":"[Session] socket disposed, aNY-rdEJ","timestamp":"2025-08-22T05:39:50.857Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregister from zonelbd","timestamp":"2025-08-22T05:39:50.858Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T05:39:50.858Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:39:50.860Z"}
{"level":"info","message":"redis pool (nation-redis) destroyed","timestamp":"2025-08-22T05:39:50.861Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:39:50.861Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T05:39:50.862Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T05:39:50.862Z"}
{"name":"nation-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T05:39:50.862Z"}
{"environment":"development","type":"oceand","gitCommitHash":"3e628d45a9f","gitCommitMessage":"UWO FGT Survey 기능 추가(테스트용)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-22T14:28:39+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"oceand.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T05:39:53.148Z"}
{"fileName":"oceanPacketHandlerDev.js","mapSize":16,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.314Z"}
{"fileName":"oceanPacketHandlerDoodad.js","mapSize":19,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.314Z"}
{"fileName":"oceanPacketHandlerEncount.js","mapSize":23,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.314Z"}
{"fileName":"oceanPacketHandlerEnterOrLeave.js","mapSize":26,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.314Z"}
{"fileName":"oceanPacketHandlerInteraction.js","mapSize":29,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.314Z"}
{"fileName":"oceanPacketHandlerMove.js","mapSize":32,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.315Z"}
{"fileName":"oceanPacketHandlerQuest.js","mapSize":40,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.315Z"}
{"fileName":"oceanPacketHandlerSync.js","mapSize":65,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.315Z"}
{"fileName":"oceanpacketHandlerBattle.js","mapSize":68,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T05:40:21.315Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:40:21.322Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T05:40:21.324Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T05:40:21.513Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T05:40:21.513Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T05:40:21.514Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T05:40:21.514Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T05:40:25.205Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T05:40:25.205Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T05:40:25.206Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T05:40:25.213Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T05:40:25.214Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T05:40:25.230Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T05:40:25.321Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.341Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.356Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.367Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.380Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.391Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.408Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.424Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.441Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T05:40:25.460Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T05:40:25.540Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T05:40:25.541Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T05:40:25.545Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T05:40:25.682Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T05:40:25.684Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T05:40:25.685Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T05:40:25.685Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T05:40:25.688Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T05:40:25.688Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T05:40:25.688Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T05:40:25.688Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T05:40:25.691Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T05:40:25.691Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T05:40:25.691Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T05:40:25.692Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T05:40:25.693Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T05:40:25.695Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T05:40:25.695Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (nation-redis) initializing ...","timestamp":"2025-08-22T05:40:25.793Z"}
{"level":"info","message":"redis pool (nation-redis) initialized","timestamp":"2025-08-22T05:40:25.840Z"}
{"ch":"national_power_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:25.846Z"}
{"ch":"nation_intimacy_updated_for_ocean","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:25.846Z"}
{"ch":"nation_share_rate_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:25.846Z"}
{"ch":"raid_doodad_spawn_on_off_update","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:25.846Z"}
{"path":"/dummy","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T05:40:25.907Z"}
{"ch":"register:UWO-GL-01","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T05:40:25.921Z"}
{"bindAddress":"0.0.0.0","port":10800,"level":"info","message":"start listening ...","timestamp":"2025-08-22T05:40:25.924Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10808","timestamp":"2025-08-22T05:40:25.925Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T05:40:25.926Z"}
{"zonelbd":"http://localhost:10600","apiService":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"register to zonelbd","timestamp":"2025-08-22T05:40:25.926Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:25.928Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:25.929Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755841226,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:40:26.022Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:40:26.022Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:26.930Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:26.930Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:27.931Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:27.931Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:28.932Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:28.932Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755841229,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:40:29.027Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:40:29.027Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:29.933Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:29.934Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:30.935Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:30.935Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:31.936Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:31.936Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755841232,"zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/updateServerdPing' exception","timestamp":"2025-08-22T05:40:32.032Z"}
{"level":"warn","message":"'/updateServerdPing' api exception","timestamp":"2025-08-22T05:40:32.032Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","zoneType":2},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T05:40:32.937Z"}
{"level":"warn","message":"'/unregisterServerd' api exception","timestamp":"2025-08-22T05:40:32.937Z"}
{"level":"info","message":"unregistration succeeded.","timestamp":"2025-08-22T05:40:33.940Z"}
{"pingInterval":2000,"curDate":1755841233,"level":"info","message":"registration succeeded.","timestamp":"2025-08-22T05:40:33.943Z"}
{"nationIntimacies":[{"smallerNationCmsId":10000000,"largerNationCmsId":10000001,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000000,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000002,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000001,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000003,"intimacyValue":60000000},{"smallerNationCmsId":10000002,"largerNationCmsId":10000005,"intimacyValue":60000000},{"smallerNationCmsId":10000003,"largerNationCmsId":10000005,"intimacyValue":60000000}],"level":"info","message":"subscribe nation_intimacy_updated_for_ocean","timestamp":"2025-08-22T05:40:40.535Z"}
{"level":"info","message":"[SessionManager] session created: greKbr0e, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T05:40:40.853Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T05:40:40.854Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_REQ_CONNECTED","timestamp":"2025-08-22T05:40:40.854Z"}
{"origin":{"bNeedWorldSyncData":true},"seq":1,"level":"verbose","message":"[SocketStream] [send packet] size:9 name:OC2LB_RES_CONNECTED","timestamp":"2025-08-22T05:40:40.855Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.856Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.858Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.858Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.858Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.858Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.859Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.859Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2OC_NTF_WORLD_SYNC_DATA","timestamp":"2025-08-22T05:40:40.859Z"}
