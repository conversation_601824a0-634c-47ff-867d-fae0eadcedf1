// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mhttp from '../../../motiflib/mhttp';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';

// ----------------------------------------------------------------------------
export class Cph_Common_GetReportedBadChattingList implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    return mhttp.lgd.reportBadChatReasonList(user.lineLangCultre).then((data) => {
      return user.sendJsonPacket(packet.seqNum, 0, { result: data });
    });
  }
}
