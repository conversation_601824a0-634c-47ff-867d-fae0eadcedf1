// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { DbQuestCompletionField, DbQuestContext } from '../../lobbyd/quest';
import mlog from '../../motiflib/mlog';
import { SHIP_ASSIGNMENT } from '../../motiflib/model/lobby';

export const spName = 'mp_u_user_load';

export interface LoginResultShip {
  id: number;
  cmsId: number;
  assignment: SHIP_ASSIGNMENT;
  fleetIndex: number;
  formationIndex: number;
  sailor: number;
  durability: number;
  permanentDamage: number;
  name: string;
  life: number;
  isLocked: number;
  enchantedStatType1: number;
  enchantedStatValue1: number;
  enchantedStatType2: number;
  enchantedStatValue2: number;
  enchantedStatType3: number;
  enchantedStatValue3: number;
  enchantResult: string;
  enchantCount: number;
  rndStats: string;
  battleQuickSkill1: number;
  battleQuickSkill2: number;
  battleQuickSkill3: number;
  battleQuickSkill4: number;
  isBound: number;
  guid: string;
}
export interface Result {
  // u_users
  userId: number;
  createTimeUtc: string;
  name: string;
  lang: string;
  nationCmsId: number;
  companyJobCmsId: number;
  lastRewardedAchievementPointCmsId: number;
  noviceSupplyCount: number;
  lastNoviceSupplyTimeUtc: string;
  energy: number;
  lastUpdateEnergyTimeUtc: string;
  palaceRoyalOrderCmsId: number;
  palaceRoyalOrderRnds: string;
  lastRoyalOrderCompletedTimeUtc: string;
  palaceRoyalTitleOrderCmsId: number;
  palaceRoyalTitleOrderRnds: string;
  contractedCollectorTownBuildingCmsId: number;
  guildId: number;
  lastGuildLeftTimeUtc: string;
  lastCompanyJobUpdateTimeUtc: string;
  countryIp: string;
  lastReceiveHotTimeUtc: string;
  firstMateCmsId: number;
  lastUpdateNationTimeUtc: string;
  isAdmiralProfileOpened: number;
  isFirstFleetProfileOpened: number;
  westShipBuildLevel: number;
  westShipBuildExp: number;
  orientShipBuildLevel: number;
  orientShipBuildExp: number;
  lastCashShopDailyProductsUpdateTimeUtc: string;
  freeLeaderMateSwitchCount: number;
  freeLastLeaderMateSwitchTimeUtc: string;
  accumInvestByGem: string;
  curCargoPresetId: number;
  representedMateCmsId: number;
  lastPaidSmuggleEnterTownCmsId: number;
  lastSmuggleTransactionTownCmsId: number;
  isFriendlyBattleRequestable: number;
  lastFirstFleetPresetId: number;
  manufacturePoint: number;
  lastUpdateManufacturePointTimeUtc: string;

  // u_states
  gameState: number;
  lastGameState: number;

  // u_soft_data
  lastTownCmsId: number;
  arrivalTownCmsId: number;
  arrivalVillageCmsId: number;
  exp: number;
  level: number;
  isSlowdownEventChecked: number;
  lastCompletedQuestCmsIdForRequest: number;
  tradeResetCount: number;
  lastTradeResetTimeUtc: string;
  karma: number;
  lastKarmaUpdateTimeUtc: string;
  usedFreeTurnTakebackCount: number;
  usedFreePhaseTakebackCount: number;
  lastFreeTakebackUpdateTimeUtc: string;
  quickModeCount: number;
  lastQuickModeCountUpdateTimeUtc: string;
  usedExploreQuickModeCount: number;
  lastExploreQuickModeCountUpdateTimeUtc: string;
  usedExploreTicketCount: number;
  lastExploreTicketCountUpdateTimeUtc: string;
  usedFreeContinuousCount: number;
  lastUsedFreeContinuousUpdateTimeUtc: string;
  waypointSupplyTicketUsedCount: number;
  lastWaypointSupplyTicketUpdateTimeUtc: string;
  lastOpenHotSpotTimeUtc: string;
  smuggleResetCount: number;
  lastSmuggleResetTimeUtc: string;
  lastFoughtSessionId: number;

  mates: {
    mateNub: {
      cmsId: number;
      loyalty: number | null;
      stateFlags: number;
      adventureExp: number;
      adventureLevel: number;
      tradeExp: number;
      tradeLevel: number;
      battleExp: number;
      battleLevel: number;
      adventureFame: number;
      tradeFame: number;
      battleFame: number;
      royalTitle: number;
      injuryExpireTimeUtc: string;
      lastTalkTimeUtc: string;
      awakenLv: number;
      awakenTimeUtc: string | null;
      colorSkin: number;
      colorEye: number;
      colorHairAcc1: number;
      colorHairAcc2: number;
      colorHair: number;
      colorBody1: number;
      colorBody2: number;
      colorFaceAcc1: number;
      colorFaceAcc2: number;
      colorFaceAcc3: number;
      colorCape1: number;
      colorCape2: number;
      colorCape3: number;
      breastSize: number;
      isHideHat: number;
      isHideCape: number;
      isHideFace: number;
      trainingGrade: number;
      trainingPoints: string;
      trainingExpiredTimeUtc: string;
      equippedIllustCmsId: number;
      isFavorite: number;
      isTranscended: number;
      transcendExpiredTimeUtc: string;
    };
  }[];
  mateEquipments: {
    mateEquipmentNub: {
      id: number;
      cmsId: number;
      dye1: number;
      dye2: number;
      dye3: number;
      dye4: number;
      dye5: number;
      dye6: number;
      equippedMateCmsId: number;
      isBound: number;
      isCostume: number;
      expireTimeUtc: string;
      enchantLevel: number;
    };
  }[];

  sidekickMates: {
    slot: number;
    cmsId: number;
  }[];

  points: {
    cmsId: number;
    value: number;
  }[];
  installmentSavings: {
    accumPoint: number;
    accumRate: number;
    accumCount: number;
    lastCmsId: number;
    lastDepositTimeUtc: string;
  };
  fleets: {
    fleetIndex: number;
    battleFormationCmsId: number;
    dispatchReduceLifeRemain: number;
  }[];
  ships: LoginResultShip[];
  shipBuildings: {
    shipId: number;
    shipCmsId: number;
    rndStats: string;
    materialRndStats: string;
    expireTimeUtc: string;
    townCmsId: number;
    substituteBuild: number;
  }[];
  // shipSlot 설명
  // mateCmsId 값이 0 초과인 경우: 선실에 항해사가 타고 있는 경우
  // mateCmsId 값이 0인 경우: 선실에 항해사 타고 있지 않은 경우
  // mateCmsId 값이 널인 경우: 부품인 경우
  // shipSlotItemId 값이 0 초과인 경우: 부품 장착된 경우
  // shipSlotItemId 값이 0인 경우: 부품 장착되지 않은 경우
  // shipSlotItemId 값이 널인 경우: 선실인 경우
  shipSlots: {
    shipId: number;
    slotIndex: number;
    mateCmsId: number;
    isLocked: number;
    shipSlotItemId: number;
  }[];
  shipCargos: {
    shipId: number;
    cmsId: number;
    quantity: number;
    pointInvested: string;
  }[];
  items: {
    cmsId: number;
    count: number;
    unboundCount: number;
  }[];
  shipBlueprints: {
    cmsId: number;
    level: number;
    exp: number;
    sailMasteryLevel: number;
    sailMasteryExp: number;
    isPurchased: number;
  }[];
  shipBlueprintSlots: {
    shipBlueprintCmsId: number;
    slotIndex: number;
    shipSlotCmsId: number;
    level: number;
  }[];
  reputations: {
    nationCmsId: number;
    reputation: number;
    updateTimeUtc: number;
  }[];
  unemployedMates: {
    mateCmsId: number;
    intimacy: number;
    isMet: number;
  }[];
  directMails: {
    id: number;
    cmsId: number;
    state: number;
    createTimeUtc: string;
    readTimeUtc: string;
    expireTimeUtc: string;
    bShouldSetExpirationWhenReceiveAttachment: number;
    title: string;
    titleFormatValue: number;
    body: string;
    bodyFormatValue: number;
    attachment: string;
  }[];
  questCompletionFields: DbQuestCompletionField[];
  questContexts: DbQuestContext[];
  lastMateEquipmentId: number;
  lastShipId: number;
  lastDirectMailId: number;
  worldBuffs: {
    groupNo: number;
    cmsId: number;
    targetId: number;
    sourceType: number;
    sourceId: number;
    stack: number;
    startTimeUtc: string;
    endTimeUtc: string;
  }[];
  battleRewards: {
    type: number;
    cmsId: number;
    quantity: number;
    receivedQuantity: number;
  }[];
  gameOverLosses: string;
  multiPvpLoss: string;
  sailing: {
    daysForLoyaltyDecrease: number;
    daysForTownReset: number;
    totalSailedDays: number;
    damageForLifeDecrease: number;
  };
  insurance: {
    insuranceCmsId: number;
    unpaidTradeGoods: string;
    unpaidShip: string;
    unpaidSailor: string;
    unpaidDucat: string;
  };
  slotExpansions: {
    type: number;
    expanded: number;
  }[];
  blackMarket: {
    lastBlackMarketResetTimeUtc: string;
    blackMarketResetCount: number;
  };
  cargoLoadPresets: {
    id: number;
    name: string;
    water: number;
    food: number;
    lumber: number;
    ammo: number;
    tradeGoods: number;
    any: number;
  }[];
  revealedOceanDoodads: {
    offset: number;
    idxField: string;
  }[];
  discoveries: {
    offset: number;
    idxField: string;
  }[];
  achievements: {
    cmsId: number;
    count: string;
    isRewarded: number;
  }[];
  tasks: {
    category: number;
    lastResetTimeUtc: string;
    shipBuildLevel: number;
    cmsId1: number;
    count1: string;
    isRewarded1: number;
    cmsId2: number;
    count2: string;
    isRewarded2: number;
    cmsId3: number;
    count3: string;
    isRewarded3: number;
    cmsId4: number;
    count4: string;
    isRewarded4: number;
    cmsId5: number;
    count5: string;
    isRewarded5: number;
    cmsId6: number;
    count6: string;
    isRewarded6: number;
    cmsId7: number;
    count7: string;
    isRewarded7: number;
    cmsId8: number;
    count8: string;
    isRewarded8: number;
    cmsId9: number;
    count9: string;
    isRewarded9: number;
    cmsId10: number;
    count10: string;
    isRewarded10: number;
    isCategoryRewarded: number;
  }[];
  contentsTermsProgresses: {
    cmsId: number;
    target: number;
    count: string;
  }[];
  weeklyEvents: {
    eventPageCmsId: number;
    weeklyEventStartTimeUtc: string;
  }[];
  eventMissions: {
    eventPageCmsId: number;
    eventMissionCmsId: number;
    count: string;
    isRewarded: number;
  }[];
  passEvents: {
    eventPageCmsId: number;
    exp: number;
    level: number;
    lastDailyResetTimeUtc: string;
  }[];
  passEventMissions: {
    eventPageCmsId: number;
    eventMissionCmsId: number;
    count: string;
    repeatedRewardReceiveCount: number;
    isRewarded: number;
  }[];
  taxFreePermits: {
    cmsId: number;
    expirationTimeUtc: string;
  }[];
  revealedWorldMapTiles: {
    offset: number;
    idxField: string;
  }[];
  revealedRegions: {
    offset: number;
    idxField: string;
  }[];
  townStates: {
    cmsId: number;
    sailor: number;
    overDraftedSailor: number;
    isInvested: number;
    isTradedGoods: number;
    arrestState: number;
  }[];
  requestSlots: {
    idx: number;
    purchaseTimeUtc: string;
  }[];
  cashShopRestrictedProducts: {
    cmsId: number;
    amount: number;
    lastBuyingTimeUtc: string;
  }[];
  cashShopFixedTermProducts: {
    cmsId: number;
    startTimeUtc: string;
    endTimeUtc: string;
  }[];
  cashShopGachaBoxGuarantees: {
    cashShopCmsId: number;
    accum: number;
  }[];
  reportedDiscoveries: {
    offset: number;
    idxField: string;
  }[];
  lastReportedDiscovery: {
    hash: string;
    data: string;
    createTimeUtc: string;
  };
  soundPacks: {
    offset: number;
    idxField: string;
  }[];
  eventPageProducts: {
    offset: number;
    idxField: string;
  }[];
  questGlobalRegisters: {
    g0: number;
    g1: number;
    g2: number;
    g3: number;
    g4: number;
    g5: number;
    g6: number;
    g7: number;
    g8: number;
    g9: number;
  };
  questionPlaces: {
    offset: number;
    idxField: string;
  }[];
  reportedWorldMapTiles: {
    offset: number;
    idxField: string;
  }[];
  landExploreResult: {
    discoveryBox: number;
    boxes: string;
    result: string;
    gainItem: number;
  };
  shipSailPatterns: {
    sailPatternCmsId: number;
    color1: number;
    color2: number;
    color3: number;
  }[];
  shipSailCrests: {
    offset: number;
    idxField: string;
  }[];
  shipSailPatternColors: {
    offset: number;
    idxField: string;
  }[];
  shipBody1Colors: {
    offset: number;
    idxField: string;
  }[];
  shipBody2Colors: {
    offset: number;
    idxField: string;
  }[];
  shipCustomizing: {
    sailPatternCmsId: number;
    sailCrestCmsId: number;
    bodyColor1: number;
    bodyColor2: number;
    bodyColorTint: number;
    camouflageCmsId: number;
  };
  villages: {
    cmsId: number;
    friendship: number;
    recruitedSailor: number;
    lastRecruitedSailorTimeUtc: string;
    lastDepartureTimeUtc: string;
    maximumFriendship: number;
    friendshipFirstRewardReceiveBitflag: number;
    lastReceiveFriendshipWeeklyRewardGrade: number;
    lastReceiveFriendshipWeeklyRewardTimeUtc: string;
    lastPlunderTimeUtc: string;
  }[];
  matePassives: {
    mateCmsId: number;
    passiveCmsId: number;
    equipIndex: number;
  }[];
  matePassiveLearnings: {
    mateCmsId: number;
    passiveCmsId: number;
    learnTimeUtc: string;
  }[];
  lastQuestItemId: number;
  questItems: {
    id: number;
    itemCmsId: number;
    questCmsId: number;
    questRnds: string;
  }[];
  attendances: {
    eventPageCmsId: number;
    accum: number;
    consecutive: number;
    lastAttendanceTimeUtc: string;
    lastRewardedConsecutiveAttendanceCmsId: number;
    startTimeUtc: string;
    endTimeUtc: string;
  }[];
  challenges: {
    cmsId: number;
    clearedDifficulty: number;
    clearedMissionField: number;
  }[];
  mateEquipmentColors: {
    offset: number;
    idxField: string;
  }[];
  lastShipSlotItemId: number;
  shipSlotItems: {
    id: number;
    shipSlotCmsId: number;
    isBound: number;
    isLocked: number;
    expireTimeUtc: string;
    enchantLevel: number;
  }[];
  battle: {
    battleEndResult: string;
  };
  gameOptionPushNotification: {
    isAllowed: number;
    isNightAllowed: number;
    allowedPushNotificationGroupIds: string;
  };

  adjutantDelegation: {
    config: string;
  };
  battleFormations: {
    cmsId: number;
    expireTimeUtc: string;
  }[];
  reportedPubStaffDiscoveries: {
    offset: number;
    idxField: string;
  }[];
  pubStaffs: {
    townCmsId: number;
    intimacy: number;
    unlockInterest: number;
    dailyTalkCount: number;
    lastNominationRefreshTimeUtc: string;
    questBlockTimeUtc: string;
  }[];
  shields: {
    cmsId: number;
    dailyFreeCount: number;
    lastDailyChargeTimeUtc: string;
    nonPurchaseCount: number;
    purchaseCount: number;
    isActivated: number;
  }[];
  worldSkills: {
    mateCmsId: number;
    cmsId: number;
    coolTimeUtc: string;
  }[];
  collections: {
    collectionCmsId: number;
    slotIndex: number;
    stack: number;
  }[];

  waitingJoinGuilds: {
    guildId: number;
    regTimeUtc: string;
  }[];
  contributionShopRestrictedProducts: {
    cmsId: number;
    amount: number;
    lastBuyingTimeUtc: string;
  }[];

  arenaTickets: {
    ticketCount: number;
    lastTicketUpdateTimeUtc: string;
    ticketBoughtCount: number;
    lastTicketBuyLimitResetTimeUtc: string;
    matchListFreeRefreshableCount: number;
    lastMatchListFreeRefreshableCountResetTimeUtc: string;
  };
  arenaGradeRewards: {
    grade: number;
    isRewarded: number;
  }[];
  cashShopDailyProducts: { cmsId: number; isBought: number }[];

  mileages: {
    month: number;
    value: number;
    isExpirationNotified: number;
  }[];

  townUnionRequests: {
    questCmsId1: number;
    questRnds1: string;
    questCmsId2: number;
    questRnds2: string;
    questCmsId3: number;
    questRnds3: string;
    questCmsId4: number;
    questRnds4: string;
    questCmsId5: number;
    questRnds5: string;
  };

  townUnionEventRequests: {
    questCmsId1: number;
    questRnds1: string;
    questCmsId2: number;
    questRnds2: string;
    questCmsId3: number;
    questRnds3: string;
    questCmsId4: number;
    questRnds4: string;
    questCmsId5: number;
    questRnds5: string;
  };

  guildShopRestrictedProducts: {
    cmsId: number;
    amount: number;
    lastBuyingTimeUtc: string;
  }[];

  shipyardShopRestrictedProducts: {
    townCmsId: number;
    shipyardShopCmsId: number;
    amount: number;
    expiredTimeUtc: string;
  }[];

  shopRestrictedProducts: {
    townCmsId: number;
    shopCmsId: number;
    amount: number;
    expiredTimeUtc: string;
  }[];

  eventShopRestrictedProducts: {
    eventPageCmsId: number;
    eventShopCmsId: number;
    packageCount: number;
    expiredTimeUtc: string;
  }[];

  guildCraftProgresses: {
    slot: number;
    guildCraftCmsId: number;
    startTimeUtc: string;
    completionTimeUtc: string;
  }[];

  fleetPresets: {
    presetId: number;
    presetName: string;
    isOpen: number;
    isLocked: number;
    isFavorite: number;
    fleet: string;
  }[];

  sailWaypoints: {
    slot: number;
    name: string;
    destCmsId: number;
    destType: number;
    waypoints: string;
  }[];
  tradeArea: {
    tradeAreaCmsId: number;
    tradePoint: number;
    accumProfitDucat: number;
    lastUpdateTimeUtc: string;
  }[];

  raidTickets: {
    bossRaidCmsId: number;
    sessionId: number;
    count: number;
    buyCount: number;
  }[];

  eventGames: {
    eventPageCmsId: number;
    eventGameCmsId: number;
    position: number;
    extra: string;
  }[];

  friends: {
    friendUserId: number;
    state: number;
    regTimeUtc: string;
  }[];

  friendPoints: {
    friendUserId: number;
    lastSendDate: string;
    lastRecvDate: string;
    pickup: number;
    totalReceivedPts: number;
  }[];

  fleetDispatches: {
    fleetIndex: number;
    dispatchCmsId: number;
    state: number;
    actionCount: number;
    actions: string;
    rewards: string;
    lostRewards: string;
    lostRewardIdCount: number;
    endView: string;
    endTimeUtc: string;
    duration: number;
  }[];

  fleetDispatchSlots: {
    fleetIndex: number;
    expireTimeUtc: string;
  }[];

  battleContinuousContext: {
    state: number;
    options: string;
    count: number;
    result: string;
  };

  chatTranslationCounts: {
    freeCount: number;
    lastDailyChargeTimeUtc: string;
  };

  questGroupRegenTimeUtcs: {
    questGroupId: number;
    regenTimeUtc: string;
  }[];

  dailySubscriptions: {
    cmsId: number;
    createTimeUtc: string;
    expireTimeUtc: string;
    lastReceiveTimeUtc: string;
  }[];

  guildSynthesisProgresses: {
    slot: number;
    guildSynthesisCmsId: number;
    startTimeUtc: string;
    completionTimeUtc: string;
  }[];

  hotSpotProducts: {
    cmsId: number;
    popupCount: number;
    expireTimeUtc: string;
    coolTimeUtc: string;
    lastResetTimeUtc: string;
  }[];

  guildRaidTickets: {
    guildBossRaidCmsId: number;
    guildId: number;
    raidOpenTime: string;
    count: number;
    buyCount: number;
  }[];

  eventRankingRewards: {
    eventPageCmsId: number;
    offset: number;
    idxField: string;
  }[];

  eventRankingGuildReward: {
    eventPageCmsId: number;
    isRewarded: number;
  }[];

  discoveryReward: {
    offset: number;
    idxField: string;
  }[];

  lastSaillingDiaryLogId: number;

  mateIllusts: {
    offset: number;
    idxField: string;
  }[];

  shipCamouflages: {
    offset: number;
    idxField: string;
  }[];

  fishes: {
    fishId: number;
    maxSize: number;
    isRewarded: number;
  }[];

  openDurationProducts: {
    cmsId: number;
    startTimeUtc: string;
    endTimeUtc: string;
  }[];

  userTitles: {
    cmsId: number;
    expiredTimeUtc: string;
    isEquipped: number;
  }[];

  sweepTicket: {
    count: number;
    buyCount: number;
  };

  canals: {
    groupId: number;
    includeGoodsPaidUseCount: number;
    paidUseCount: number;
    freeUseCount: number;
    lastUseTimeUtc: string;
  }[];

  costumeShipSlots: {
    subSlotType: number;
    shipSlotItemId: number;
  }[];

  townRemoteInvest: {
    townCmsId: number;
    weeklySessionId: number;
    investCount: number;
  }[];

  cashShopConsecutiveProducts: {
    cmsId: number;
    normalSaleTimeUtc: string;
    discountSaleTimeUtc: string;
  }[];

  pets: {
    cmsId: number;
    sidekickSlot: number;
  }[];

  npcQuests: {
    cmsId: number;
    lastCompletedTimeUtc: string;
  }[];

  shipCompose: {
    groupId: number;
    turn: number;
    rewardedCount: number;
  }[];

  npcShopRestrictedProducts: {
    cmsId: number;
    count: number;
    lastBuyingTimeUtc: string;
  }[];

  blindBids: {
    cmsId: number;
    amount: number;
    isRewarded: number;
  }[];

  blindBidTicket: number;

  infiniteLighthouses: {
    stage: number;
    sessionId: number;
    usedTurn: number;
  }[];

  researchNodes: {
    groupId: number;
    level: number;
    isCompleted: number;
  }[];

  researchTasks: {
    researchCmsId: number;
    researchTaskCmsId: number;
    count: number;
  }[];

  reentrys: {
    type: number;
    lastResetTimeUtc: string;
    count: number;
    cooldownEndTimeUtc: string;
  }[];

  clash: {
    sessionId: number;
    score: number;
    isRewarded: number;
    winStreak: number;
  };
  clashRejection: {
    lastResetTimeUtc: string;
    count: number;
    cooldownEndTimeUtc: string;
  };
}

const spFunction = query.generateSPFunction(spName);

/**
 * load user informations
 * @param connection db connection
 * @param userId a user identifier
 * @returns @ref Result
 */
export default function (
  connection: query.Connection,
  userId: number,
  curTimeUtc: number
): Promise<Result> {
  return spFunction(connection, userId, curTimeUtc).then((qr) => {
    const rows = qr.rows;
    let rowIndex = 0;
    const userData = rows[rowIndex][0];

    rowIndex += 1;
    const stateData = rows[rowIndex][0];

    rowIndex += 1;
    const softData = rows[rowIndex][0];

    rowIndex += 1;
    const mateRows = rows[rowIndex];
    const mates = [];
    for (const key of Object.keys(mateRows)) {
      mates.push({
        mateNub: mateRows[key],
      });
    }

    rowIndex += 1;
    const mateEquipmentRows = rows[rowIndex];
    const mateEquipments = [];
    if (mateEquipmentRows) {
      for (const key of Object.keys(mateEquipmentRows)) {
        mateEquipments.push({
          mateEquipmentNub: mateEquipmentRows[key],
        });
      }
    }

    rowIndex += 1;
    const sidekickMates = rows[rowIndex];

    rowIndex += 1;
    const points = rows[rowIndex].map((row) =>
      Object.assign(row, { value: parseInt(row.value, 10) })
    );

    rowIndex += 1;
    const installmentSavings = Object.assign(rows[rowIndex][0], {
      accumPoint: parseInt(rows[rowIndex][0].accumPoint, 10),
    });

    rowIndex += 1;
    const fleets = rows[rowIndex];

    rowIndex += 1;
    const ships = rows[rowIndex];
    rowIndex += 1;
    const shipBuildings = rows[rowIndex];
    rowIndex += 1;
    const shipSlots = rows[rowIndex];

    rowIndex += 1;
    const shipCargos = rows[rowIndex];

    rowIndex += 1;
    const items = rows[rowIndex];

    rowIndex += 1;
    const shipBlueprints = rows[rowIndex];

    rowIndex += 1;
    const shipBlueprintSlots = rows[rowIndex];

    rowIndex += 1;
    const reputations = rows[rowIndex].map((row) => {
      const updateTimeUtc = parseInt(row.updateTimeUtc, 10);
      return Object.assign(row, { updateTimeUtc });
    });

    rowIndex += 1;
    const unemployedMates = rows[rowIndex];

    rowIndex += 1;
    const directMails = rows[rowIndex];

    rowIndex += 1;
    const questCompletionFields = rows[rowIndex];

    rowIndex += 1;
    const questContexts = rows[rowIndex];

    rowIndex += 1;
    let lastMateEquipmentId = 0;
    if (rows[rowIndex].length > 0) {
      lastMateEquipmentId = rows[rowIndex][0].mateEquipmentId;
    }

    rowIndex += 1;
    let lastShipId = 0;
    if (rows[rowIndex].length > 0) {
      lastShipId = rows[rowIndex][0].shipId;
    }

    rowIndex += 1;
    let lastDirectMailId = 0;
    if (rows[rowIndex].length > 0) {
      lastDirectMailId = rows[rowIndex][0].directMailId;
    }

    rowIndex += 1;
    const worldBuffs = rows[rowIndex];

    rowIndex += 1;
    const battleRewards = rows[rowIndex];

    rowIndex += 1;
    let gameOverLosses;
    let multiPvpLoss;
    if (rows[rowIndex].length > 0) {
      gameOverLosses = rows[rowIndex][0].losses;
      multiPvpLoss = rows[rowIndex][0].multiPvpLoss;
    }

    rowIndex += 1;
    const sailing =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            daysForLoyaltyDecrease: 0,
            daysForTownReset: 0,
            totalSailedDays: 0,
            damageForLifeDecrease: 0,
          };

    rowIndex += 1;
    const insurance =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            insuranceCmsId: 0,
            unpaidTradeGoods: '0',
            unpaidShip: '0',
            unpaidSailor: '0',
            unpaidDucat: '0',
          };

    rowIndex += 1;
    const slotExpansions = rows[rowIndex];

    rowIndex += 1;
    const blackMarket =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            lastBlackMarketResetTimeUtc: '1',
            blackMarketResetCount: 0,
          };

    rowIndex += 1;
    let cargoLoadPresets = rows[rowIndex];

    rowIndex += 1;
    const revealedOceanDoodads = rows[rowIndex];

    rowIndex += 1;
    const discoveries = rows[rowIndex];

    rowIndex += 1;
    const achievements = rows[rowIndex];

    rowIndex += 1;
    const tasks = rows[rowIndex];

    rowIndex += 1;
    const contentsTermsProgresses = rows[rowIndex];

    rowIndex += 1;
    const weeklyEvents = rows[rowIndex];

    rowIndex += 1;
    const eventMissions = rows[rowIndex];

    rowIndex += 1;
    const passEvents = rows[rowIndex];

    rowIndex += 1;
    const passEventMissions = rows[rowIndex];

    rowIndex += 1;
    const taxFreePermits = rows[rowIndex];

    rowIndex += 1;
    const revealedWorldMapTiles = rows[rowIndex];

    rowIndex += 1;
    const revealedRegions = rows[rowIndex];

    rowIndex += 1;
    const townStates = rows[rowIndex];

    rowIndex += 1;
    const requestSlots = rows[rowIndex];

    rowIndex += 1;
    const cashShopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const cashShopFixedTermProducts = rows[rowIndex];

    rowIndex += 1;
    const cashShopGachaBoxGuarantees = rows[rowIndex];

    rowIndex += 1;
    const reportedDiscoveries = rows[rowIndex];

    rowIndex += 1;
    // let lastReportedDiscovery = null;
    // if (rows[rowIndex].length > 0) {
    //   lastReportedDiscovery = rows[rowIndex][0];
    // }
    const lastReportedDiscovery = rows[rowIndex][0];

    rowIndex += 1;
    const soundPacks = rows[rowIndex];

    rowIndex += 1;
    const eventPageProducts = rows[rowIndex];

    rowIndex += 1;
    const questGlobalRegisters =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            g0: 0,
            g1: 0,
            g2: 0,
            g3: 0,
            g4: 0,
            g5: 0,
            g6: 0,
            g7: 0,
            g8: 0,
            g9: 0,
          };

    rowIndex += 1;
    const questionPlaces = rows[rowIndex];

    rowIndex += 1;
    const reportedWorldMapTiles = rows[rowIndex];

    rowIndex += 1;
    const landExploreResult =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            discoveryBox: null,
            boxes: '0',
            result: null,
            gainItem: null,
          };

    rowIndex += 1;
    const shipSailPatterns = rows[rowIndex];

    rowIndex += 1;
    const shipSailCrests = rows[rowIndex];

    rowIndex += 1;
    const shipSailPatternColors = rows[rowIndex];

    rowIndex += 1;
    const shipBody1Colors = rows[rowIndex];

    rowIndex += 1;
    const shipBody2Colors = rows[rowIndex];

    rowIndex += 1;
    const shipCustomizing =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            sailPatternCmsId: null,
            sailCrestCmsId: null,
            bodyColor1: null,
            bodyColor2: null,
            bodyColorTint: null,
            costumeShipSlotItemId: null,
          };

    rowIndex += 1;
    const villages = rows[rowIndex];

    rowIndex += 1;
    const matePassives = rows[rowIndex];

    rowIndex += 1;
    const matePassiveLearnings = rows[rowIndex];

    rowIndex += 1;
    let lastQuestItemId = 0;
    if (rows[rowIndex].length > 0) {
      lastQuestItemId = rows[rowIndex][0].questItemId;
    }

    rowIndex += 1;
    const questItems = rows[rowIndex];

    rowIndex += 1;
    const attendances = rows[rowIndex];

    rowIndex += 1;
    const challenges = rows[rowIndex];

    rowIndex += 1;
    const mateEquipmentColors = rows[rowIndex];

    rowIndex += 1;
    let lastShipSlotItemId = 0;
    if (rows[rowIndex].length > 0) {
      lastShipSlotItemId = rows[rowIndex][0].shipSlotItemId;
    }

    rowIndex += 1;
    const shipSlotItems = rows[rowIndex];

    rowIndex += 1;
    const battle =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            battleEndResult: null,
          };

    rowIndex += 1;
    const gameOptionPushNotification =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            isAllowed: 0,
            isNightAllowed: 0,
            allowedPushNotificationGroupIds: null,
          };

    rowIndex += 1;
    const adjutantDelegation = rows[rowIndex][0];

    rowIndex += 1;
    const battleFormations = rows[rowIndex];

    rowIndex += 1;
    const reportedPubStaffDiscoveries = rows[rowIndex];

    rowIndex += 1;
    const pubStaffs = rows[rowIndex];

    rowIndex += 1;
    const shields = rows[rowIndex];

    rowIndex += 1;
    const worldSkills = rows[rowIndex];

    rowIndex += 1;
    const collections = rows[rowIndex];

    rowIndex += 1;
    const waitingJoinGuilds = rows[rowIndex];

    rowIndex += 1;
    const contributionShopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const arenaTickets = rows[rowIndex][0];

    rowIndex += 1;
    const arenaGradeRewards = rows[rowIndex];

    rowIndex += 1;
    const cashShopDailyProducts = rows[rowIndex];

    rowIndex += 1;
    const mileages = rows[rowIndex];

    rowIndex += 1;
    const townUnionRequests = rows[rowIndex][0];

    rowIndex += 1;
    const guildShopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const shipyardShopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const shopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const eventShopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const townUnionEventRequests = rows[rowIndex][0];

    rowIndex += 1;
    const guildCraftProgresses = rows[rowIndex];

    rowIndex += 1;
    const fleetPresets = rows[rowIndex];

    rowIndex += 1;
    const sailWaypoints = rows[rowIndex];
    rowIndex += 1;
    const tradeArea = rows[rowIndex];

    rowIndex += 1;
    const raidTickets = rows[rowIndex];

    rowIndex += 1;
    const eventGames = rows[rowIndex];

    rowIndex += 1;
    const friends = rows[rowIndex];

    rowIndex += 1;
    const friendPoints = rows[rowIndex];

    rowIndex += 1;
    const fleetDispatches = rows[rowIndex];

    rowIndex += 1;
    const fleetDispatchSlots = rows[rowIndex];

    rowIndex += 1;
    const battleContinuousContext =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            state: 0,
            options: null,
            count: 0,
            result: null,
          };

    rowIndex += 1;
    const chatTranslationCounts = rows[rowIndex][0];

    rowIndex += 1;
    const questGroupRegenTimeUtcs = rows[rowIndex];

    rowIndex += 1;
    const dailySubscriptions = rows[rowIndex];

    rowIndex += 1;
    const guildSynthesisProgresses = rows[rowIndex];

    rowIndex += 1;
    const hotSpotProducts = rows[rowIndex];

    rowIndex += 1;
    const guildRaidTickets = rows[rowIndex];

    rowIndex += 1;
    const eventRankingRewards = rows[rowIndex];

    rowIndex += 1;
    const eventRankingGuildReward = rows[rowIndex];

    rowIndex += 1;
    const discoveryReward = rows[rowIndex];

    rowIndex += 1;
    let lastSaillingDiaryLogId: number = 0;
    if (rows[rowIndex].length > 0) {
      lastSaillingDiaryLogId = parseInt(rows[rowIndex][0].lastLogId, 10);
    }

    rowIndex += 1;
    const mateIllusts = rows[rowIndex];

    rowIndex += 1;
    const shipCamouflages = rows[rowIndex];

    rowIndex += 1;
    const fishes = rows[rowIndex];

    rowIndex += 1;
    const openDurationProducts = rows[rowIndex];

    rowIndex += 1;
    const userTitles = rows[rowIndex];

    rowIndex += 1;
    const sweepTicket = rows[rowIndex][0];

    rowIndex += 1;
    const canals = rows[rowIndex];

    rowIndex += 1;
    const costumeShipSlots = rows[rowIndex];

    rowIndex += 1;
    const townRemoteInvest = rows[rowIndex];

    rowIndex += 1;
    const cashShopConsecutiveProducts = rows[rowIndex];

    rowIndex += 1;
    const pets = rows[rowIndex];

    rowIndex += 1;
    const npcQuests = rows[rowIndex];

    rowIndex += 1;
    const shipCompose = rows[rowIndex];

    rowIndex += 1;
    const npcShopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const blindBids = rows[rowIndex];

    rowIndex += 1;
    let blindBidTicket = 0;
    if (rows[rowIndex].length > 0) {
      blindBidTicket = rows[rowIndex][0].count;
    }

    rowIndex += 1;
    const infiniteLighthouses = rows[rowIndex];

    rowIndex += 1;
    const researchNodes = rows[rowIndex];

    rowIndex += 1;
    const researchTasks = rows[rowIndex];

    rowIndex += 1;
    const reentrys = rows[rowIndex];

    rowIndex += 1;
    const clash = rows[rowIndex][0];

    rowIndex += 1;
    const clashRejection = rows[rowIndex][0];

    rowIndex += 1;
    let clashLastFoughtSessionId = null;
    if (rows[rowIndex].length > 0) {
      clashLastFoughtSessionId = rows[rowIndex][0].lastFoughtSessionId;
    }

    return {
      userId,
      createTimeUtc: userData.createTimeUtc,
      name: userData.name,
      lang: userData.lang,
      nationCmsId: userData.nationCmsId,
      companyJobCmsId: userData.companyJobCmsId,
      lastRewardedAchievementPointCmsId: userData.lastRewardedAchievementPointCmsId,
      noviceSupplyCount: userData.noviceSupplyCount,
      lastNoviceSupplyTimeUtc: userData.lastNoviceSupplyTimeUtc,
      energy: userData.energy,
      lastUpdateEnergyTimeUtc: userData.lastUpdateEnergyTimeUtc,
      palaceRoyalOrderCmsId: userData.palaceRoyalOrderCmsId,
      palaceRoyalOrderRnds: userData.palaceRoyalOrderRnds,
      lastRoyalOrderCompletedTimeUtc: userData.lastRoyalOrderCompletedTimeUtc,
      palaceRoyalTitleOrderCmsId: userData.palaceRoyalTitleOrderCmsId,
      palaceRoyalTitleOrderRnds: userData.palaceRoyalTitleOrderRnds,
      contractedCollectorTownBuildingCmsId: userData.contractedCollectorTownBuildingCmsId,
      guildId: userData.guildId,
      lastGuildLeftTimeUtc: userData.lastGuildLeftTimeUtc,
      lastCompanyJobUpdateTimeUtc: userData.lastCompanyJobUpdateTimeUtc,
      countryIp: userData.countryIp,
      lastReceiveHotTimeUtc: userData.lastReceiveHotTimeUtc,
      firstMateCmsId: userData.firstMateCmsId,
      lastUpdateNationTimeUtc: userData.lastUpdateNationTimeUtc,
      isAdmiralProfileOpened: userData.isAdmiralProfileOpened,
      isFirstFleetProfileOpened: userData.isFirstFleetProfileOpened,
      westShipBuildLevel: userData.westShipBuildLevel,
      westShipBuildExp: userData.westShipBuildExp,
      orientShipBuildLevel: userData.orientShipBuildLevel,
      orientShipBuildExp: userData.orientShipBuildExp,
      lastCashShopDailyProductsUpdateTimeUtc: userData.lastCashShopDailyProductsUpdateTimeUtc,
      isFriendlyBattleRequestable: userData.isFriendlyBattleRequestable,

      freeLeaderMateSwitchCount: userData.freeLeaderMateSwitchCount,
      freeLastLeaderMateSwitchTimeUtc: userData.freeLastLeaderMateSwitchTimeUtc,

      accumInvestByGem: userData.accumInvestByGem,
      usedExploreQuickModeCount: userData.usedExploreQuickModeCount,
      lastExploreQuickModeCountUpdateTimeUtc: userData.lastExploreQuickModeCountUpdateTimeUtc,
      usedExploreTicketCount: userData.usedExploreTicketCount,
      lastExploreTicketCountUpdateTimeUtc: userData.lastExploreTicketCountUpdateTimeUtc,
      curCargoPresetId: userData.curCargoPresetId,
      representedMateCmsId: userData.representedMateCmsId,
      lastPaidSmuggleEnterTownCmsId: userData.lastPaidSmuggleEnterTownCmsId,
      lastSmuggleTransactionTownCmsId: userData.lastSmuggleTransactionTownCmsId,
      lastFirstFleetPresetId: userData.lastFirstFleetPresetId,
      manufacturePoint: userData.manufacturePoint,
      lastUpdateManufacturePointTimeUtc: userData.lastUpdateManufacturePointTimeUtc,

      gameState: stateData.gameState,
      lastGameState: stateData.lastGameState,

      lastTownCmsId: softData.lastTownCmsId,
      arrivalTownCmsId: softData.arrivalTownCmsId,
      arrivalVillageCmsId: softData.arrivalVillageCmsId,
      exp: softData.exp,
      level: softData.level,
      isSlowdownEventChecked: softData.isSlowdownEventChecked,
      lastCompletedQuestCmsIdForRequest: softData.lastCompletedQuestCmsIdForRequest,
      tradeResetCount: softData.tradeResetCount,
      lastTradeResetTimeUtc: softData.lastTradeResetTimeUtc,
      karma: softData.karma,
      lastKarmaUpdateTimeUtc: softData.lastKarmaUpdateTimeUtc,
      usedFreeTurnTakebackCount: softData.usedFreeTurnTakebackCount,
      usedFreePhaseTakebackCount: softData.usedFreePhaseTakebackCount,
      lastFreeTakebackUpdateTimeUtc: softData.lastFreeTakebackUpdateTimeUtc,
      quickModeCount: softData.quickModeCount,
      lastQuickModeCountUpdateTimeUtc: softData.lastQuickModeCountUpdateTimeUtc,
      usedFreeContinuousCount: softData.usedFreeContinuousCount,
      lastUsedFreeContinuousUpdateTimeUtc: softData.lastUsedFreeContinuousUpdateTimeUtc,
      waypointSupplyTicketUsedCount: softData.waypointSupplyTicketUsedCount,
      lastWaypointSupplyTicketUpdateTimeUtc: softData.lastWaypointSupplyTicketUpdateTimeUtc,
      lastOpenHotSpotTimeUtc: softData.lastOpenHotSpotTimeUtc,
      smuggleResetCount: softData.smuggleResetCount,
      lastSmuggleResetTimeUtc: softData.lastSmuggleResetTimeUtc,

      mates,
      mateEquipments,
      sidekickMates,
      points,
      installmentSavings,
      fleets,
      ships,
      shipBuildings,
      shipSlots,
      shipCargos,
      items,
      shipBlueprints,
      shipBlueprintSlots,
      reputations,
      unemployedMates,
      directMails,
      questCompletionFields,
      questContexts,
      lastMateEquipmentId,
      lastShipId,
      lastDirectMailId,
      worldBuffs,
      battleRewards,
      gameOverLosses,
      multiPvpLoss,
      sailing,
      insurance,
      slotExpansions,
      blackMarket,
      cargoLoadPresets,
      revealedOceanDoodads,
      discoveries,
      achievements,
      tasks,
      contentsTermsProgresses,
      weeklyEvents,
      eventMissions,
      passEvents,
      passEventMissions,
      taxFreePermits,
      revealedWorldMapTiles,
      revealedRegions,
      townStates,
      requestSlots,
      cashShopRestrictedProducts,
      cashShopFixedTermProducts,
      cashShopGachaBoxGuarantees,
      reportedDiscoveries,
      lastReportedDiscovery,
      soundPacks,
      eventPageProducts,
      questGlobalRegisters,
      questionPlaces,
      reportedWorldMapTiles,
      landExploreResult,
      shipSailPatterns,
      shipSailCrests,
      shipSailPatternColors,
      shipBody1Colors,
      shipBody2Colors,
      shipCustomizing,
      villages,
      matePassives,
      matePassiveLearnings,
      lastQuestItemId,
      questItems,
      attendances,
      challenges,
      mateEquipmentColors,
      lastShipSlotItemId,
      shipSlotItems,
      battle,
      gameOptionPushNotification,
      adjutantDelegation,
      battleFormations,
      reportedPubStaffDiscoveries,
      pubStaffs,
      shields,
      worldSkills,
      collections,
      waitingJoinGuilds,
      contributionShopRestrictedProducts,
      arenaTickets,
      arenaGradeRewards,
      cashShopDailyProducts,
      mileages,
      townUnionRequests,
      townUnionEventRequests,
      guildShopRestrictedProducts,
      shipyardShopRestrictedProducts,
      shopRestrictedProducts,
      eventShopRestrictedProducts,
      guildCraftProgresses,
      fleetPresets,
      sailWaypoints,
      tradeArea,
      raidTickets,
      eventGames,
      friends,
      friendPoints,
      fleetDispatches,
      fleetDispatchSlots,
      battleContinuousContext,
      chatTranslationCounts,
      questGroupRegenTimeUtcs,
      dailySubscriptions,
      guildSynthesisProgresses,
      hotSpotProducts,
      guildRaidTickets,
      eventRankingRewards,
      eventRankingGuildReward,
      discoveryReward,
      lastSaillingDiaryLogId,
      mateIllusts,
      shipCamouflages,
      fishes,
      openDurationProducts,
      userTitles,
      sweepTicket,
      canals,
      costumeShipSlots,
      townRemoteInvest,
      cashShopConsecutiveProducts,
      pets,
      npcQuests,
      shipCompose,
      npcShopRestrictedProducts,
      blindBids,
      blindBidTicket,
      infiniteLighthouses,
      researchNodes,
      researchTasks,
      reentrys,
      clash,
      clashRejection,
      lastFoughtSessionId: clashLastFoughtSessionId,
    };
  });
}
