import { Cli<PERSON><PERSON>s, RemapR<PERSON>ult, AnalysisResult, VerificationResult, GroupedRemapResult } from '../types';
import { ConfigLoader } from '../config/configLoader';
import { CodebaseAnalyzer } from '../analyzer/codebaseAnalyzer';
import { CsvParser } from '../csv/csvParser';
import { DatabaseManager } from '../database/databaseManager';
import { RedisManager } from '../redis/redisManager';
import { DatabaseUpdateEngine } from '../database/databaseUpdateEngine';
import { RedisUpdateEngine } from '../redis/redisUpdateEngine';
import { VerificationEngine } from '../verification/verificationEngine';
import { HtmlReportGenerator } from '../reports/htmlReportGenerator';
import { ResultGrouper } from '../utils/resultGrouper';
import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import ora from 'ora';
import JSON5 from 'json5';

export class RemapApplication {
  private options: CliOptions;
  private configLoader: ConfigLoader;
  private analyzer: CodebaseAnalyzer;
  private csvParser: CsvParser;
  private dbManager: DatabaseManager | null = null;
  private redisManager: RedisManager | null = null;
  private config: any = null;

  constructor(options: CliOptions) {
    this.options = options;
    this.configLoader = new ConfigLoader(options.configDir);
    this.analyzer = new CodebaseAnalyzer();
    this.csvParser = new CsvParser();
  }

  async run(): Promise<void> {
    const startTime = Date.now();
    let result: RemapResult | null = null;

    try {
      await fs.ensureDir(this.options.outputDir);

      console.log(chalk.blue('📋 Step 1: Initialization'));
      await this.initialize();

      console.log(chalk.blue('\n🔍 Step 2: Codebase Analysis'));
      const analysisResult = await this.analyzeCodebase();
      await this.saveAnalysisResult(analysisResult);

      console.log(chalk.blue('\n📄 Step 3: CSV Data Parsing'));
      const remapData = await this.parseCsvData();

      if (this.options.dryRun) {
        console.log(chalk.blue('\n🧪 Step 4: Dry Run Execution'));
        result = await this.performDryRun(analysisResult, remapData);
      } else {
        console.log(chalk.blue('\n🚀 Step 4: Actual Update Execution'));
        result = await this.performActualUpdate(analysisResult, remapData);
      }

      if (!this.options.dryRun && !this.options.skipVerification) {
        console.log(chalk.blue('\n✅ Step 5: Data Verification'));
        const verificationResult = await this.performVerification(analysisResult, remapData);
        await this.saveVerificationResult(verificationResult);
      }

      await this.saveRemapResult(result, this.config);

      const duration = Date.now() - startTime;
      console.log(chalk.green(`\n🎉 Task completed! (${duration}ms)`));
      this.printSummary(result);

    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(chalk.red('\n❌ Task failed:'), error);

      result = {
        success: false,
        totalProcessed: 0,
        databaseResults: [],
        redisResults: [],
        errors: [String(error)],
        warnings: [],
        startTime: new Date(startTime),
        endTime: new Date(),
        duration,
      };

      await this.saveRemapResult(result, this.config);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  async analyzeOnly(): Promise<void> {
    try {
      await fs.ensureDir(this.options.outputDir);

      console.log(chalk.blue('🔍 Codebase Analysis Started'));
      const analysisResult = await this.analyzeCodebase();
      await this.saveAnalysisResult(analysisResult);

      console.log(chalk.green('✅ Analysis Complete'));
      this.printAnalysisSummary(analysisResult);
    } catch (error) {
      console.error(chalk.red('❌ Analysis Failed:'), error);
      throw error;
    }
  }

  async verifyOnly(): Promise<void> {
    try {
      await fs.ensureDir(this.options.outputDir);
      await this.initialize();

      const analysisResult = await this.analyzeCodebase();
      const remapData = await this.parseCsvData();

      console.log(chalk.blue('✅ Data Verification Started'));
      const verificationResult = await this.performVerification(analysisResult, remapData);
      await this.saveVerificationResult(verificationResult);

      console.log(chalk.green('✅ Verification Complete'));
      this.printVerificationSummary(verificationResult);
    } catch (error) {
      console.error(chalk.red('❌ Verification Failed:'), error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  private async initialize(): Promise<void> {
    const spinner = ora('Loading configuration...').start();

    try {
      const configExists = await this.configLoader.checkConfigFiles();
      if (!configExists) {
        throw new Error('Configuration files not found. Please run "uwo-remap init" to initialize.');
      }

      const remapConfig = await this.configLoader.loadRemapToolConfig();
      this.config = remapConfig;

      this.dbManager = new DatabaseManager(remapConfig);
      await this.dbManager.initialize();

      this.redisManager = new RedisManager(remapConfig);
      await this.redisManager.initialize();

      spinner.succeed('Initialization complete');
    } catch (error) {
      spinner.fail('Initialization failed');
      throw error;
    }
  }

  private async analyzeCodebase(): Promise<AnalysisResult> {
    const spinner = ora('Analyzing codebase...').start();

    try {
      const schemaDir = this.options.schemaDir || './schemas';
      const result = await this.analyzer.analyzeCodebase(schemaDir);

      spinner.succeed(`Analysis complete: ${result.totalFilesAnalyzed} files, ${result.databaseTables.length} tables, ${result.redisKeys.length} Redis keys`);
      return result;
    } catch (error) {
      spinner.fail('Analysis failed');
      throw error;
    }
  }

  private async parseCsvData() {
    const spinner = ora('Parsing CSV file...').start();

    try {
      const remapData = await this.csvParser.parseRemapCsv(this.options.csvFile);
      await this.csvParser.validateRemapData(remapData);

      const filteredData = this.options.worldId
        ? this.csvParser.filterByWorld(remapData, this.options.worldId)
        : remapData;

      spinner.succeed(`CSV parsing complete: ${filteredData.length} records`);
      return filteredData;
    } catch (error) {
      spinner.fail('CSV parsing failed');
      throw error;
    }
  }

  private async performDryRun(analysisResult: AnalysisResult, remapData: any[]): Promise<RemapResult> {
    const startTime = new Date();

    if (!this.dbManager || !this.redisManager) {
      throw new Error('Database or Redis connection not initialized');
    }

    const dbEngine = new DatabaseUpdateEngine(this.dbManager, {
      batchSize: this.options.batchSize,
      delayBetweenBatches: 0,
      maxRetries: 1,
      retryDelay: 0,
    });

    const redisEngine = new RedisUpdateEngine(this.redisManager, {
      batchSize: this.options.batchSize,
      delayBetweenBatches: 0,
      maxRetries: 1,
      retryDelay: 0,
    });

    console.log('📊 Database impact analysis...');
    const databaseResults = await dbEngine.dryRun(
      analysisResult.databaseTables,
      remapData,
      this.options.worldId
    );

    console.log('📊 Redis impact analysis...');
    const redisResults = await redisEngine.dryRun(
      analysisResult.redisKeys,
      remapData,
      this.options.worldId
    );

    // 샤드별 결과를 펼쳐서 추가
    const expandedDatabaseResults = [];
    for (const result of databaseResults) {
      if ((result as any).shardResults) {
        // 샤드별 결과가 있으면 각각 추가
        expandedDatabaseResults.push(...(result as any).shardResults);
      } else {
        // 일반 결과는 그대로 추가
        expandedDatabaseResults.push(result);
      }
    }

    return {
      success: true,
      totalProcessed: remapData.length,
      databaseResults: expandedDatabaseResults,
      redisResults,
      errors: [],
      warnings: [],
      startTime,
      endTime: new Date(),
      duration: Date.now() - startTime.getTime(),
    };
  }

  private async performActualUpdate(analysisResult: AnalysisResult, remapData: any[]): Promise<RemapResult> {
    const startTime = new Date();

    if (!this.dbManager || !this.redisManager) {
      throw new Error('Database or Redis connection not initialized');
    }

    const dbEngine = new DatabaseUpdateEngine(this.dbManager, {
      batchSize: this.options.batchSize,
      delayBetweenBatches: 1000,
      maxRetries: 3,
      retryDelay: 5000,
    });

    const redisEngine = new RedisUpdateEngine(this.redisManager, {
      batchSize: this.options.batchSize,
      delayBetweenBatches: 500,
      maxRetries: 3,
      retryDelay: 2000,
    });

    console.log('🗄️ Database update execution...');
    const databaseResults = await dbEngine.updateAllTables(
      analysisResult.databaseTables,
      remapData,
      this.options.worldId
    );

    console.log('🔄 Redis update execution...');
    const redisResults = await redisEngine.updateAllKeys(
      analysisResult.redisKeys,
      remapData,
      this.options.worldId,
      false
    );

    // 샤드별 결과를 펼쳐서 추가
    const expandedDatabaseResults = [];
    for (const result of databaseResults) {
      if ((result as any).shardResults) {
        // 샤드별 결과가 있으면 각각 추가
        expandedDatabaseResults.push(...(result as any).shardResults);
      } else {
        // 일반 결과는 그대로 추가
        expandedDatabaseResults.push(result);
      }
    }

    const errors = [
      ...expandedDatabaseResults.filter((r: any) => !r.success).map((r: any) => r.error || 'Unknown error'),
      ...redisResults.filter((r: any) => !r.success).map((r: any) => r.error || 'Unknown error'),
    ];

    return {
      success: errors.length === 0,
      totalProcessed: remapData.length,
      databaseResults: expandedDatabaseResults,
      redisResults,
      errors,
      warnings: [],
      startTime,
      endTime: new Date(),
      duration: Date.now() - startTime.getTime(),
    };
  }

  private async performVerification(analysisResult: AnalysisResult, remapData: any[]): Promise<VerificationResult> {
    if (!this.dbManager || !this.redisManager) {
      throw new Error('Database or Redis connection not initialized');
    }

    const verificationEngine = new VerificationEngine(this.dbManager, this.redisManager);
    return await verificationEngine.verifyAll(
      analysisResult.databaseTables,
      analysisResult.redisKeys,
      remapData,
      this.options.worldId
    );
  }

  private async saveAnalysisResult(result: AnalysisResult): Promise<void> {
    try {
      const jsonFilePath = path.join(this.options.outputDir, 'analysis-result.json5');
      const jsonContent = JSON5.stringify(result, null, 2);
      await fs.writeFile(jsonFilePath, jsonContent, 'utf8');
      console.log(chalk.gray(`Analysis result saved: ${jsonFilePath}`));

      console.log(chalk.blue('Generating HTML report...'));
      const htmlFilePath = path.join(this.options.outputDir, 'analysis-report.html');
      await HtmlReportGenerator.generateAnalysisReport(result, htmlFilePath);
      console.log(chalk.gray(`Analysis report generated: ${htmlFilePath}`));
    } catch (error) {
      console.error(chalk.red('Error saving analysis result:'), error);
      throw error;
    }
  }

  private async saveVerificationResult(result: VerificationResult): Promise<void> {
    const filePath = path.join(this.options.outputDir, 'verification-result.json5');
    const jsonContent = JSON5.stringify(result, null, 2);
    await fs.writeFile(filePath, jsonContent, 'utf8');
    console.log(chalk.gray(`Verification result saved: ${filePath}`));

    const htmlPath = path.join(this.options.outputDir, 'verification-report.html');
    await HtmlReportGenerator.generateVerificationReport(result, htmlPath);
    console.log(chalk.gray(`Verification report generated: ${htmlPath}`));
  }

  private async saveRemapResult(result: RemapResult, config?: any): Promise<void> {
    const groupedResult = ResultGrouper.groupByWorld(result);
    const groupedFilePath = path.join(this.options.outputDir, 'remap-result.json5');
    const jsonContent = JSON5.stringify(groupedResult, null, 2);
    await fs.writeFile(groupedFilePath, jsonContent, 'utf8');
    console.log(chalk.gray(`Grouped execution result saved: ${groupedFilePath}`));

    const htmlPath = path.join(this.options.outputDir, 'remap-report.html');
    await HtmlReportGenerator.generateRemapReport(groupedResult, htmlPath, config);
    console.log(chalk.gray(`Remap report generated: ${htmlPath}`));
  }

  private async cleanup(): Promise<void> {
    if (this.dbManager) {
      await this.dbManager.cleanup();
    }
    if (this.redisManager) {
      await this.redisManager.cleanup();
    }
  }

  private printSummary(result: RemapResult): void {
    console.log(chalk.bold('\n📊 Execution Summary'));
    console.log(`Processed records: ${result.totalProcessed}`);
    console.log(`Database tables: ${result.databaseResults.length}`);
    console.log(`Redis key patterns: ${result.redisResults.length}`);
    console.log(`Execution time: ${result.duration}ms`);
    console.log(`Success: ${result.success ? chalk.green('✅') : chalk.red('❌')}`);

    if (result.errors.length > 0) {
      console.log(chalk.red('\n❌ Errors:'));
      result.errors.forEach(error => console.log(`  - ${error}`));
    }
  }

  private printAnalysisSummary(result: AnalysisResult): void {
    console.log(chalk.bold('\n📊 Analysis Summary'));
    console.log(`Analyzed files: ${result.totalFilesAnalyzed}`);
    console.log(`  - SQL files: ${result.sqlFilesAnalyzed}`);
    console.log(`  - Lua files: ${result.luaFilesAnalyzed}`);
    console.log(`  - TS files: ${result.tsFilesAnalyzed}`);
    console.log(`Found tables: ${result.databaseTables.length}`);
    console.log(`Found Redis keys: ${result.redisKeys.length}`);
    console.log(`Analysis time: ${result.analysisTime}ms`);
  }

  private printVerificationSummary(result: VerificationResult): void {
    console.log(chalk.bold('\n📊 Verification Summary'));
    console.log(`Checked items: ${result.checkedItems}`);
    console.log(`Passed: ${result.passedItems}`);
    console.log(`Failed: ${result.failedItems}`);
    console.log(`Success rate: ${((result.passedItems / result.checkedItems) * 100).toFixed(2)}%`);
    console.log(`Overall result: ${result.success ? chalk.green('✅') : chalk.red('❌')}`);
  }
}
