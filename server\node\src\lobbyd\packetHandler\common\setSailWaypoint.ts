// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import * as mutil from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { PathEx } from '../../../motiflib/model/ocean';
import { LobbyService } from '../../server';
import { Sync, Resp, SailWaypoint } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import tuSetSailWaypoints from '../../../mysqllib/txn/tuSetSailWaypoints';
import { ItemDesc, ITEM_TYPE } from '../../../cms/itemDesc';
import mlog from '../../../motiflib/mlog';
import mhttp from '../../../motiflib/mhttp';

// ----------------------------------------------------------------------------
// 항해 경로 저장.
// ----------------------------------------------------------------------------
const rsn = 'set_sail_waypoint';
const add_rsn = null;

interface RequestBody {
  slot: number;
  name?: string;
  destCmsId: number;
  destType: number; // AUTO_SAIL_DEST_TYPE(1: Town  2: Village)
  waypoints: PathEx[];
}

// ----------------------------------------------------------------------------
export class Cph_Common_SetSailWaypoint implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    let { slot, name, destCmsId, destType, waypoints } = body;

    mlog.verbose('[SetSailWaypoint] rx', {
      userId: user.userId,
      slot,
      destCmsId,
      destType,
      waypoints,
    });

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const maxSlot =
      cms.Const.InitWaypointSlotCount.value +
      user.userInven.getSlotExpansion(cmsEx.INVENTORY_TYPE.SAIL_WAYPOINT);

    if (!destType) {
      destType = 0;
      destCmsId = 0;
    }

    user.userSailWaypoints.validate(slot, destCmsId, destType, waypoints, maxSlot);

    // 경로명 금칙어 체크.
    const hasBadWord = await mhttp.platformApi.hasBadWord(name);
    if (hasBadWord) {
      throw new MError('has-bad-word', MErrorCode.HAS_BAD_WORD, {
        name,
        // oldName: user.userName,
      });
    }

    // 대상 슬롯에 저장
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    await tuSetSailWaypoints(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      slot,
      name,
      destCmsId,
      destType,
      JSON.stringify(waypoints)
    );
    const resp: Resp = { sync: {} };
    _.merge<Sync, Sync>(
      resp.sync,
      user.userSailWaypoints.applyWaypoint({
        slot,
        name,
        destCmsId,
        destType,
        waypoints,
      })
    );
    return await user.sendJsonPacket<Resp>(packet.seqNum, packet.type, resp);
  }
}
