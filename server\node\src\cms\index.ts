// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import fs from 'fs';
import path from 'path';
import * as JSON5 from 'json5';

import preprocess from './preprocess';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import { CmsTable } from './cmsTable';

import { CharacterDesc } from './characterDesc';
import { CommonSkillDesc } from './commonSkillDesc';
import { CurrentRateDesc } from './currentRateDesc';
import { ItemDesc } from './itemDesc';
import { JobDesc } from './jobDesc';
import { MateDesc } from './mateDesc';
import { MateRecruitingDesc } from './mateRecruitingDesc';
import { NationDesc } from './nationDesc';
import { OceanDisasterDesc } from './oceanDisasterDesc';
import { OceanNpcDesc } from './oceanNpcDesc';
import { OceanNpcStageDesc } from './oceanNpcStageDesc';
import { RegionDesc } from './regionDesc';
import { PointDesc } from './pointDesc';
import { ShipDesc } from './shipDesc';
import { ShipBlueprintDesc } from './shipBlueprintDesc';
import { TownDesc } from './townDesc';
import { TownLocationDesc } from './townLocationDesc';
import { WaveRateDesc } from './waveRateDesc';
import { WindRateDesc } from './windRateDesc';
import { QuestDesc } from './questDesc';
import { AdmiralDesc } from './admiralDesc';
import { TownKickDesc } from './townKickDesc';
import { QuestNodeDesc } from './questNodeDesc';
import { QuestGoalDesc } from './questGoalDesc';
import { ShipSlotDesc } from './shipSlotDesc';
import { AchievementTermsDesc } from './achievementTermsDesc';
import { AchievementDesc } from './achievementDesc';
import { TradeGoodsDesc } from './tradeGoodsDesc';
import { SpecialStatDesc } from './specialStatDesc';
import { CompanyJobDesc } from './companyJobDesc';
import { LanguageDesc } from './languageDesc';
import { RequestGroupDesc } from './requestGroupDesc';
import { RoyalOrderGroupDesc } from './royalOrderGroupDesc';
import { LoadageRatioDesc } from './loadageRatioDesc';
import { NationRankingEffectDesc } from './nationRankingEffectDesc';
import { NationDiplomacyDesc } from './nationDiplomacyDesc';
import { FleetTemplateDesc } from './fleetTemplateDesc';
import { ReligionDesc } from './religionDesc';
import { ClimateDesc } from './climateDesc';
import { ContentsTermsDesc } from './contentsTermsDesc';
import { BattleContentsTermsListDesc } from './battleContentsTermsListDesc';
import { BankInstallmentSavingsDesc } from './bankInstallmentSavingsDesc';
import { BankInsuranceDesc } from './bankInsuranceDesc';
import { ShopDesc } from './shopDesc';
import { TradeDesc } from './tradeDesc';
import { CEquipDesc } from './cEquipDesc';
import { CashShopDesc } from './cashShopDesc';
import { WorldBuffDesc } from './worldBuffDesc';
import { MailDesc } from './mailDesc';
import { OceanNpcTemplateDesc } from './oceanNpcTemplateDesc';
import { MateTemplateGroupDesc } from './mateTemplateGroupDesc';
import { TaxFreePermitDesc } from './TaxFreePermitDesc';
import { CashShopBoxRatioDesc } from './cashShopBoxRatioDesc';
import { DiscoveryDesc } from './DiscoveryDesc';
import { TownBuildingDesc } from './townBuildingDesc';
import { MateRecruitingGroupDesc } from './mateRecruitingGroupDesc';
import { QuestPassDesc } from './questPassDesc';
import { DiscoveryMissionDesc } from './discoveryMissionDesc';
import { RewardDesc } from './rewardDesc';
import { RewardDropPoolDesc } from './rewardDropPoolDesc';
import { ContentsResetHourDesc } from './contentsResetHourDesc';
import { WeatherDesc } from './weatherDesc';
import { WeatherTileDesc } from './weatherTileDesc';
import { WeatherTileClimateDesc } from './weatherTileClimateDesc';
import { OceanDoodadDesc } from './oceanDoodadDesc';
import { QuestTermsDesc } from './questTermsDesc';
import { LandExploreDesc } from './landExploreDesc';
import { DiscoveryRankingDesc } from './discoveryRankingDesc';
import { RewardFixedDesc } from './rewardFixedDesc';
import { VillageDesc } from './villageDesc';
import { MatePersonalityDesc } from './matePersonalityDesc';
import { WorldTileDesc } from './worldTileDesc';
import { MateAwakenDesc } from './MateAwakenDesc';
import { CharacterExpDesc } from './characterExpDesc';
import { AutoMatePassiveDesc } from './autoMatePassiveDesc';
import { AutoMatePassiveFixedDesc } from './autoMatePassiveFixedDesc';
import { AutoJobPassiveDesc } from './autoJobPassiveDesc';
import { AutoMateOrderDesc } from './autoMateOrderDesc';
import { BurstGroupDesc } from './burstGroupDesc';
import { CompanyExpDesc } from './companyExpDesc';
import { EventPageDesc } from './eventPageDesc';
import { AttendanceDesc } from './attendanceDesc';
import { BattleChallengeDesc } from './battleChallengeDesc';
import { AnchorTemplateDesc } from './anchorTemplateDesc';
import { TaskDesc } from './taskDesc';
import { BattleMissionGroupDesc } from './battleMissionGroupDesc';
import { MateSetDesc } from './mateSetDesc';
import { DefaultEquipDyeColorDesc } from './defaultEquipDyeColorDesc';
import { EquipDyeColorDesc } from './equipDyeColorDesc';
import { WorldPassiveDesc } from './worldPassiveDesc';
import { BattlePassiveDesc } from './battlePassiveDesc';
import { BattleSkillDesc } from './battleSkillDesc';
import { BattleMapObjectDesc } from './battleMapObjectDesc';
import { ShipyardShopDesc } from './shipyardShopDesc';
import { BubbleEventDesc } from './bubbleEventDesc';
import { ShipEnchantDesc } from './shipEnchantDesc';
import { ShipEnchantStatRatioDesc } from './shipEnchantStatRatioDesc';
import { RoyalTitleDesc } from './royalTitleDesc';
import { DepartSupplyDesc } from './departSupplyDesc';
import { BuildingBuffDesc } from './buildingBuffDesc';
import { OceanNpcShapeDesc } from './oceanNpcShapeDesc';
import { PushNotificationDesc } from './pushNotificationDesc';
import { HotTimeBonusDesc } from './hotTimeBonusDesc';
import { ShipCabinRelationDesc } from './shipCabinRelationDesc';
import { FortuneDesc } from './fortuneDesc';
import { CulturalAreaDesc } from './culturalAreaDesc';
import { VillageLocationDesc } from './villageLocationDesc';
import { OceanTileDesc } from './oceanTileDesc';
import { OceanNpcBuffDesc } from './oceanNpcBuffDesc';
import { SocialAnimationDesc } from './socialAnimationDesc';
import { ShipTemplateDesc } from './shipTemplateDesc';
import { ShipTemplateGroupDesc } from './shipTemplateGroupDesc';
import { BattleFormationDesc } from './battleFormationDesc';
import { FishGradeDesc } from './fishGradeDesc';
import { PieceSwapDesc } from './pieceSwapDesc';
import { FishOceanTileDesc } from './fishOceanTileDesc';
import { FishDiscoveryGroupDesc } from './fishDiscoveryGroup';
import { WorldTriggerDesc } from './worldTriggerDesc';
import { InvestCompanyRankingDesc } from './investCompanyRanking';
import { WorldStateDesc } from './worldStateDesc';
import { BattleTermsListDesc } from './battleTermsListDesc';
import { VillageFriendshipDesc } from './villageFriendshipDesc';
import { ShieldDesc } from './shieldDesc';
import { VillageDiscoveryGroupDesc } from './villageDiscoveryGroupDesc';
import { WorldSkillDesc } from './worldSkillDesc';
import { LandExploreDiscoveryGroupDesc } from './landExploreDiscoveryGroupDesc';
import { ShipyardBuildDesc } from './shipyardBuildDesc';
import { StatOperatorDesc } from './statOperatorDesc';
import { WorldPassiveEffectDesc } from './worldPassiveEffectDesc';
import { CollectionDesc } from './CollectionDesc';
import { ShipBuildMasteryExpDesc } from './shipBuildMasteryExpDesc';
import { SlotExpansionDesc } from './SlotExpansionDesc';
import { MateReRecruitingDesc } from './mateReRecruitingDesc';
import { EventMissionDesc } from './eventMissionDesc';
import { EventTaskDesc } from './eventTaskDesc';
import { BattleFactionDesc } from './battleFactionDesc';
import { DefaultShipBlueprintDesc } from './defaultShipBlueprintDesc';
import { LandFeatureGroupDesc } from './landFeatureGroupDesc';
import { GuildDesc } from './guildDesc';
import { GuildResourceDesc } from './guildResourceDesc';
import { GuildDonationDesc } from './guildDonationDesc';
import { GuildCraftingShipPartsDesc } from './guildCraftingShipPartsDesc';
import { GuildCraftingMateEquipDesc } from './guildCraftingMateEquipDesc';
import { LandFeatureDesc } from './landFeatureDesc';
import { AdventureEventSpotDesc } from './adventureEventSpotDesc';
import { AdventureEventModeDesc } from './adventureEventModeDesc';
import { AdventureEventRewardDesc } from './adventureEventRewardDesc';
import { AdventureDiscoveryGroupDesc } from './adventureDiscoveryGroupDesc';
import { AdventureItemFunctionDesc } from './adventureItemFunctionDesc';
import { AdventureSpecialEffectDesc } from './adventureSpecialEffectDesc';
import { ContributionShopDesc } from './contributionShopDesc';
import { ArenaTierListDesc } from './arenaTierListDesc';
import { ArenaMatchBotDesc } from './arenaMatchBotDesc';
import { ArenaUseMapDesc } from './arenaUseMapDesc';
import { ShipBuildExpDesc } from './ShipBuildExpDesc';
import { CashShopLimitSaleDesc } from './cashShopLimitSale';
import { MatemanagementDesc } from './mateManagementDesc';
import { EventMissionExpDesc } from './eventMissionExpDesc';
import { EventShopDesc } from './eventShopDesc';
import { GuildRewardDesc } from './guildRewardDesc';
import { AdvSpcEffTermsGroupDesc } from './advSpcEffTermsGroupDesc';
import { AdvSpcEffTermsDesc } from './advSpcEffTermsDesc';
import { AdvSpcEffFuncDesc } from './advSpcEffFuncDesc';
import { AdvSpcEffFuncGroupDesc } from './advSpcEffFuncGroupDesc';
import { GuildCraftDesc } from './guildCraftDesc';
import { BuildingHoursDesc } from './buildingHoursDesc';
import { LiveEventDesc } from './liveEventDesc';
import { BossRaidDesc } from './BossRaidDesc';
import { BossRaidRankingDesc } from './BossRaidRankingDesc';
import { TradeAreaTileDataDesc } from './tradeAreaTileDataDesc';
import { BossRaidTicketDesc } from './BossRaidTicketDesc';
import { BossRaidValidDmgDesc } from './BossRaidValidDmgDesc';
import { TradeComboSetDesc } from './tradeComboSetDesc';
import { TradeComboDesc } from './tradeComboDesc';
import { QuestDailyLimitDesc } from './questDailyLimit';
import { EventGameListDesc } from './eventGameListDesc';
import { BoardGameRewardDesc } from './boardGameRewardDesc';
import { TradeEventDesc } from './tradeEventDesc';
import { TradeEventCategoryPriceDesc } from './tradeEventCategoryPriceDesc';
import { SailMasteryExpDesc } from './sailMasteryExpDesc';
import { FleetDispatchActionDesc } from './fleetDispatchActionDesc';
import { FleetDispatchDesc } from './fleetDispatchDesc';
import { RankingDesc } from './rankingDesc';
import { AutoMateSkillDesc } from './autoMateSkillDesc';
import { QuestGroupDesc } from './questGroupDesc';
import { DailySubscriptionDesc } from './dailySubscription';
import { GuildSynthesisDesc } from './guildSynthesisDesc';
import { GuildSynthesisGroupDesc } from './guildSynthesisGroupDesc';
import { MateTrainingDesc } from './mateTrainingDesc';
import { PubStaffDesc } from './pubStaffDesc';
import { GuildBossRaidDesc } from './guildBossRaidDesc';
import { EventRankingListDesc } from './eventRankingListDesc';
import { EventRankingRewardDesc } from './eventRankingRewardDesc';
import { PushLocalizeDesc } from './pushLocalizeDesc';
import { DiscoveryRewardDesc } from './discoveryRewardDesc';
import { KarmaDesc } from './karmaDesc';
import { IllustSkinDesc } from './illustSkinDesc';
import { ReputationDesc } from './reputationDesc';
import { ConstellationDesc } from './constellationDesc';
import { ShipCamouflageDesc } from './shipCamouflageDesc';
import { ChoiceBoxDesc } from './choiceBoxDesc';
import { FishDesc } from './fishDesc';
import { GuildBuffDesc } from './guildBuffDesc';
import { HotTimeBuffDesc } from './hotTimeBuffDesc';
import { ReturnerBuffDesc } from './ReturnerBuffDesc';
import { RotationTradeGroupDesc } from './RotationTradeGroupDesc';
import { BossRaidBuffDesc } from './bossRaidBuffDesc';
import { UserTitleDesc } from './userTitleDesc';
import { GuildAccessPermission } from './guildAccessPermissionDesc';
import { ExchangeVillageListDesc } from './ExchangeVillageListDesc';
import { ExchangeVillageStorageDesc } from './ExchangeVillageStorageDesc';
import { NationPromiseDesc } from './nationPromiseDesc';
import { NationPromiseConditionDesc } from './nationPromiseConditionDesc';
import { NationCabinetDesc } from './nationCabinetDesc';
import { NationPolicyDesc } from './nationPolicyDesc';
import { NationPolicyLvUpCostDesc } from './nationPolicyLvUpCostDesc';
import { NationPolicyBudgetCostDesc } from './nationPolicyBudgetCostDesc';
import { CanalDesc } from './canalDesc';
import { RelationShipChronicleDesc } from './relationShipChronicleDesc';
import { NationSupportShopDesc } from './nationSupportShopDesc';
import { NationDonationRankingDesc } from './nationDonationRankingDesc';
import { NationCabinetAuthorityDesc } from './NationCabinetAuthorityDesc';
import { NationCabinetRewardMailDesc } from './nationCabinetRewardMailDesc';
import { CEquipEnchantDesc } from './cEquipEnchantDesc';
import { ShipsSlotEnchantDesc } from './shipSlotEnchantDesc';
import { EnchantStatRatioDesc } from './enchantStatRatioDesc';
import { PetDesc } from './petDesc';
import { SalvageSpotDesc } from './salvageSpotDesc';
import { NpcInteractionDesc } from './npcInteractionDesc';
import { SalvageSpotTagDesc } from './salvageSpotTagDesc';
import { SalvageRewardGroupDesc } from './salvageRewardGroupDesc';
import { SalvageMinigameNodeDesc } from './salvageMinigameNodeDesc';
import { SalvageMnigameObjectDesc } from './salvageMnigameObjectDesc';
import { ShipComposeDesc } from './shipComposeDesc';
import { TownNpcGlobalDesc } from './townNpcGlobalDesc';
import { TownNpcServerDesc } from './TownNpcServerDesc';
import { TownNpcShopDesc } from './townNpcShopDesc';
import { SmuggleDesc } from './smuggleDesc';
import { SmuggleGoodsDesc } from './smuggleGoodsDesc';
import { SmuggleGoodsCategoryDesc } from './smuggleGoodsCategoryDesc';
import { TownNpcTemplateDesc } from './townNpcTemplateDesc';
import { BlindBidDesc } from './blindBidDesc';
import { TranscendenceDesc } from './transcendenceDesc';
import { InfiniteLighthouseDesc } from './infiniteLighthouseDesc';
import { InfiniteLighthouseScheduleDesc } from './infiniteLighthouseScheduleDesc';
import { FriendlyMatchUseMapsDesc } from './friendlyMatchUseMapsDesc';
import { UnpopularDecreaseRateDesc } from './UnpopularDecreaseRateDesc';
import { ResearchDesc } from './researchDesc';
import { ResearchTaskDesc } from './researchTaskDesc';
import { InvestSeasonDesc } from './investSeasonDesc';
import { ReentryCostDesc } from './reentryCostDesc';
import { ClashUseMapDesc } from './clashUseMapDesc';
import { ClashTierListDesc } from './clashTierListDesc';
import { RewardSeasonItemsDesc } from './rewardSeasonItemsDesc';
import { ClashSeasonPrizeDesc } from './clashSeasonPrize';
import { InvestSeasonRankingRewardDesc } from './investSeasonRankingRewardDesc';
import { ChangeItemsDesc } from './changeItemsDesc';
import { NeedShipSpecialStatDesc } from './needShipSpecialStatDesc';
import { TutorialTradeEventDesc } from './tutorialTradeEventDesc';
import { ManufactureRecipeDesc } from './manufactureRecipeDesc';
import { ManufactureRoomDesc } from './manufactureRoomDesc';
import { ManufactureExpDesc } from './manufactureExpDesc';
import { ManufactureGroupDesc } from './manufactureGroupDesc';

export interface Cms {
  Character: CmsTable<CharacterDesc>;
  CommonSkill: CmsTable<CommonSkillDesc>;
  CurrentRate: CmsTable<CurrentRateDesc>;
  Item: CmsTable<ItemDesc>;
  Job: CmsTable<JobDesc>;
  Mate: CmsTable<MateDesc>;
  MateRecruiting: CmsTable<MateRecruitingDesc>;
  Nation: CmsTable<NationDesc>;
  OceanDisaster: CmsTable<OceanDisasterDesc>;
  OceanNpc: CmsTable<OceanNpcDesc>;
  OceanNpcStage: CmsTable<OceanNpcStageDesc>;
  Region: CmsTable<RegionDesc>;
  Point: CmsTable<PointDesc>;
  Ship: CmsTable<ShipDesc>;
  ShipSlot: CmsTable<ShipSlotDesc>;
  ShipBlueprint: CmsTable<ShipBlueprintDesc>;
  Town: CmsTable<TownDesc>;
  TownLocation: CmsTable<TownLocationDesc>;
  WaveRate: CmsTable<WaveRateDesc>;
  WindRate: CmsTable<WindRateDesc>;
  Quest: CmsTable<QuestDesc>;
  QuestNode: CmsTable<QuestNodeDesc>;
  QuestGoal: CmsTable<QuestGoalDesc>;
  Admiral: CmsTable<AdmiralDesc>;
  TownKick: CmsTable<TownKickDesc>;
  AchievementTerms: CmsTable<AchievementTermsDesc>;
  Achievement: CmsTable<AchievementDesc>;
  TradeGoods: CmsTable<TradeGoodsDesc>;
  SpecialStat: CmsTable<SpecialStatDesc>;
  CompanyJob: CmsTable<CompanyJobDesc>;
  Language: CmsTable<LanguageDesc>;
  RequestGroup: CmsTable<RequestGroupDesc>;
  RoyalOrderGroup: CmsTable<RoyalOrderGroupDesc>;
  LoadageRatio: CmsTable<LoadageRatioDesc>;
  NationRankingEffect: CmsTable<NationRankingEffectDesc>;
  NationDiplomacy: CmsTable<NationDiplomacyDesc>;
  FleetTemplate: CmsTable<FleetTemplateDesc>;
  Religion: CmsTable<ReligionDesc>;
  Climate: CmsTable<ClimateDesc>;
  ContentsTerms: CmsTable<ContentsTermsDesc>;
  BattleContentsTermsList: CmsTable<BattleContentsTermsListDesc>;
  BankInstallmentSavings: CmsTable<BankInstallmentSavingsDesc>;
  BankInsurance: CmsTable<BankInsuranceDesc>;
  Shop: CmsTable<ShopDesc>;
  Trade: CmsTable<TradeDesc>;
  CEquip: CmsTable<CEquipDesc>;
  CashShop: CmsTable<CashShopDesc>;
  WorldBuff: CmsTable<WorldBuffDesc>;
  WorldPassive: CmsTable<WorldPassiveDesc>;
  BattlePassive: CmsTable<BattlePassiveDesc>;
  BattleSkill: CmsTable<BattleSkillDesc>;
  BattleMapObject: CmsTable<BattleMapObjectDesc>;
  Mail: CmsTable<MailDesc>;
  OceanNpcTemplate: CmsTable<OceanNpcTemplateDesc>;
  MateTemplateGroup: CmsTable<MateTemplateGroupDesc>;
  TaxFreePermit: CmsTable<TaxFreePermitDesc>;
  CashShopBoxRatio: CmsTable<CashShopBoxRatioDesc>;
  Discovery: CmsTable<DiscoveryDesc>;
  TownBuilding: CmsTable<TownBuildingDesc>;
  MateRecruitingGroup: CmsTable<MateRecruitingGroupDesc>;
  QuestPass: CmsTable<QuestPassDesc>;
  DiscoveryMission: CmsTable<DiscoveryMissionDesc>;
  Reward: CmsTable<RewardDesc>;
  RewardDropPool: CmsTable<RewardDropPoolDesc>;
  ContentsResetHour: CmsTable<ContentsResetHourDesc>;
  Weather: CmsTable<WeatherDesc>;
  WeatherTile: CmsTable<WeatherTileDesc>;
  WeatherTileClimate: CmsTable<WeatherTileClimateDesc>;
  OceanDoodad: CmsTable<OceanDoodadDesc>;
  QuestTerms: CmsTable<QuestTermsDesc>;
  LandExplore: CmsTable<LandExploreDesc>;
  LandExploreDiscoveryGroup: CmsTable<LandExploreDiscoveryGroupDesc>;
  LandFeatureGroup: CmsTable<LandFeatureGroupDesc>;
  LandFeature: CmsTable<LandFeatureDesc>;
  AdventureEventSpot: CmsTable<AdventureEventSpotDesc>;
  AdventureEventMode: CmsTable<AdventureEventModeDesc>;
  AdventureEventReward: CmsTable<AdventureEventRewardDesc>;
  AdventureDiscoveryGroup: CmsTable<AdventureDiscoveryGroupDesc>;
  AdventureItemFunction: CmsTable<AdventureItemFunctionDesc>;
  AdventureSpecialEffect: CmsTable<AdventureSpecialEffectDesc>;
  DiscoveryRanking: CmsTable<DiscoveryRankingDesc>;
  RewardFixed: CmsTable<RewardFixedDesc>;
  Village: CmsTable<VillageDesc>;
  VillageLocation: CmsTable<VillageLocationDesc>;
  VillageFriendship: CmsTable<VillageFriendshipDesc>;
  VillageDiscoveryGroup: CmsTable<VillageDiscoveryGroupDesc>;
  MatePersonality: CmsTable<MatePersonalityDesc>;
  WorldTile: CmsTable<WorldTileDesc>;
  MateAwaken: CmsTable<MateAwakenDesc>;
  CharacterExp: CmsTable<CharacterExpDesc>;
  AutoMatePassive: CmsTable<AutoMatePassiveDesc>;
  AutoMatePassiveFixed: CmsTable<AutoMatePassiveFixedDesc>;
  AutoJobPassive: CmsTable<AutoJobPassiveDesc>;
  AutoMateOrder: CmsTable<AutoMateOrderDesc>;
  BurstGroup: CmsTable<BurstGroupDesc>;
  CompanyExp: CmsTable<CompanyExpDesc>;
  EventPage: CmsTable<EventPageDesc>;
  EventMission: CmsTable<EventMissionDesc>;
  EventTask: CmsTable<EventTaskDesc>;
  EventMissionExp: CmsTable<EventMissionExpDesc>;
  Attendance: CmsTable<AttendanceDesc>;
  BattleChallenge: CmsTable<BattleChallengeDesc>;
  BattleMissionGroup: CmsTable<BattleMissionGroupDesc>;
  BattleTermsList: CmsTable<BattleTermsListDesc>;
  AnchorTemplate: CmsTable<AnchorTemplateDesc>;
  Task: CmsTable<TaskDesc>;
  MateSet: CmsTable<MateSetDesc>;
  DefaultEquipDyeColor: CmsTable<DefaultEquipDyeColorDesc>;
  DefaultShipBlueprint: CmsTable<DefaultShipBlueprintDesc>;
  EquipDyeColor: CmsTable<EquipDyeColorDesc>;
  ShipyardShop: CmsTable<ShipyardShopDesc>;
  ShipyardBuild: CmsTable<ShipyardBuildDesc>;
  BubbleEvent: CmsTable<BubbleEventDesc>;
  ShipEnchant: CmsTable<ShipEnchantDesc>;
  ShipEnchantStatRatio: CmsTable<ShipEnchantStatRatioDesc>;
  RoyalTitle: CmsTable<RoyalTitleDesc>;
  DepartSupply: CmsTable<DepartSupplyDesc>;
  BuildingBuff: CmsTable<BuildingBuffDesc>;
  OceanNpcShape: CmsTable<OceanNpcShapeDesc>;
  PushNotification: CmsTable<PushNotificationDesc>;
  HotTimeBonus: CmsTable<HotTimeBonusDesc>;
  ShipCabinRelation: CmsTable<ShipCabinRelationDesc>;
  Fortune: CmsTable<FortuneDesc>;
  CulturalArea: CmsTable<CulturalAreaDesc>;
  OceanTile: CmsTable<OceanTileDesc>;
  OceanNpcBuff: CmsTable<OceanNpcBuffDesc>;
  SocialAnimation: CmsTable<SocialAnimationDesc>;
  ShipTemplate: CmsTable<ShipTemplateDesc>;
  ShipTemplateGroup: CmsTable<ShipTemplateGroupDesc>;
  BattleFormation: CmsTable<BattleFormationDesc>;
  FishGrade: CmsTable<FishGradeDesc>;
  PieceSwap: CmsTable<PieceSwapDesc>;
  FishOceanTile: CmsTable<FishOceanTileDesc>;
  FishDiscoveryGroup: CmsTable<FishDiscoveryGroupDesc>;
  WorldTrigger: CmsTable<WorldTriggerDesc>;
  InvestCompanyRanking: CmsTable<InvestCompanyRankingDesc>;
  WorldState: CmsTable<WorldStateDesc>;
  Shield: CmsTable<ShieldDesc>;
  WorldSkill: CmsTable<WorldSkillDesc>;
  StatOperator: CmsTable<StatOperatorDesc>;
  WorldPassiveEffect: CmsTable<WorldPassiveEffectDesc>;
  Collection: CmsTable<CollectionDesc>;
  ShipBuildExp: CmsTable<ShipBuildExpDesc>;
  ShipBuildMasteryExp: CmsTable<ShipBuildMasteryExpDesc>;
  SlotExpansion: CmsTable<SlotExpansionDesc>;
  MateReRecruiting: CmsTable<MateReRecruitingDesc>;
  BattleFactionDesc: CmsTable<BattleFactionDesc>;
  Guild: CmsTable<GuildDesc>;
  GuildResource: CmsTable<GuildResourceDesc>;
  GuildDonation: CmsTable<GuildDonationDesc>;
  GuildBossRaid: CmsTable<GuildBossRaidDesc>;
  ContributionShop: CmsTable<ContributionShopDesc>;
  ArenaTierList: CmsTable<ArenaTierListDesc>;
  ArenaMatchBot: CmsTable<ArenaMatchBotDesc>;
  ArenaUseMap: CmsTable<ArenaUseMapDesc>;
  GuildCraftingShipParts: CmsTable<GuildCraftingShipPartsDesc>;
  GuildCraftingMateEquip: CmsTable<GuildCraftingMateEquipDesc>;
  CashShopLimitSale: CmsTable<CashShopLimitSaleDesc>;
  MateManagement: CmsTable<MatemanagementDesc>;
  EventShop: CmsTable<EventShopDesc>;
  GuildReward: CmsTable<GuildRewardDesc>;
  AdvSpcEffTermsGroup: CmsTable<AdvSpcEffTermsGroupDesc>;
  AdvSpcEffTerms: CmsTable<AdvSpcEffTermsDesc>;
  AdvSpcEffFuncGroup: CmsTable<AdvSpcEffFuncGroupDesc>;
  AdvSpcEffFunc: CmsTable<AdvSpcEffFuncDesc>;
  GuildCraft: CmsTable<GuildCraftDesc>;
  BuildingHours: CmsTable<BuildingHoursDesc>;
  LiveEvent: CmsTable<LiveEventDesc>;
  BossRaid: CmsTable<BossRaidDesc>;
  BossRaidRanking: CmsTable<BossRaidRankingDesc>;
  BossRaidTicket: CmsTable<BossRaidTicketDesc>;
  BossRaidValidDmg: CmsTable<BossRaidValidDmgDesc>;
  TradeAreaTileData: CmsTable<TradeAreaTileDataDesc>;
  TradeComboSet: CmsTable<TradeComboSetDesc>;
  TradeCombo: CmsTable<TradeComboDesc>;
  QuestDailyLimit: CmsTable<QuestDailyLimitDesc>;
  EventGameList: CmsTable<EventGameListDesc>;
  BoardGameReward: CmsTable<BoardGameRewardDesc>;
  TradeEvent: CmsTable<TradeEventDesc>;
  TradeEventCategoryPrice: CmsTable<TradeEventCategoryPriceDesc>;
  SailMasteryExp: CmsTable<SailMasteryExpDesc>;
  FleetDispatchAction: CmsTable<FleetDispatchActionDesc>;
  FleetDispatch: CmsTable<FleetDispatchDesc>;
  Ranking: CmsTable<RankingDesc>;
  AutoMateSkill: CmsTable<AutoMateSkillDesc>;
  QuestGroup: CmsTable<QuestGroupDesc>;
  DailySubscription: CmsTable<DailySubscriptionDesc>;
  GuildSynthesis: CmsTable<GuildSynthesisDesc>;
  GuildSynthesisGroup: CmsTable<GuildSynthesisGroupDesc>;
  MateTraining: CmsTable<MateTrainingDesc>;
  PubStaff: CmsTable<PubStaffDesc>;
  EventRankingList: CmsTable<EventRankingListDesc>;
  EventRankingReward: CmsTable<EventRankingRewardDesc>;
  PushLocalize: CmsTable<PushLocalizeDesc>;
  DiscoveryReward: CmsTable<DiscoveryRewardDesc>;
  Karma: CmsTable<KarmaDesc>;
  IllustSkin: CmsTable<IllustSkinDesc>;
  Reputation: CmsTable<ReputationDesc>;
  Constellation: CmsTable<ConstellationDesc>;
  ShipCamouflage: CmsTable<ShipCamouflageDesc>;
  ChoiceBox: CmsTable<ChoiceBoxDesc>;
  Fish: CmsTable<FishDesc>;
  GuildBuffDesc: CmsTable<GuildBuffDesc>;
  HotTimeBuff: CmsTable<HotTimeBuffDesc>;
  ReturnerBuff: CmsTable<ReturnerBuffDesc>;
  RotationTradeGroup: CmsTable<RotationTradeGroupDesc>;
  BossRaidBuff: CmsTable<BossRaidBuffDesc>;
  UserTitle: CmsTable<UserTitleDesc>;
  GuildAccessPermission: CmsTable<GuildAccessPermission>;
  ExchangeVillageList: CmsTable<ExchangeVillageListDesc>;
  ExchangeVillageStorage: CmsTable<ExchangeVillageStorageDesc>;
  NationPromise: CmsTable<NationPromiseDesc>;
  NationPromiseCondition: CmsTable<NationPromiseConditionDesc>;
  NationCabinet: CmsTable<NationCabinetDesc>;
  NationPolicy: CmsTable<NationPolicyDesc>;
  NationPolicyLvUpCost: CmsTable<NationPolicyLvUpCostDesc>;
  NationPolicyBudgetCost: CmsTable<NationPolicyBudgetCostDesc>;
  Canal: CmsTable<CanalDesc>;
  RelationShipChronicle: CmsTable<RelationShipChronicleDesc>;
  NationSupportShop: CmsTable<NationSupportShopDesc>;
  NationDonationRanking: CmsTable<NationDonationRankingDesc>;
  NationCabinetAuthority: CmsTable<NationCabinetAuthorityDesc>;
  NationCabinetRewardMail: CmsTable<NationCabinetRewardMailDesc>;
  CEquipEnchant: CmsTable<CEquipEnchantDesc>;
  ShipSlotEnchant: CmsTable<ShipsSlotEnchantDesc>;
  EnchantStatRatio: CmsTable<EnchantStatRatioDesc>;
  Pet: CmsTable<PetDesc>;
  SalvageSpot: CmsTable<SalvageSpotDesc>;
  NpcInteraction: CmsTable<NpcInteractionDesc>;
  SalvageSpotTag: CmsTable<SalvageSpotTagDesc>;
  SalvageRewardGroup: CmsTable<SalvageRewardGroupDesc>;
  SalvageMinigameNode: CmsTable<SalvageMinigameNodeDesc>;
  SalvageMnigameObject: CmsTable<SalvageMnigameObjectDesc>;
  ShipCompose: CmsTable<ShipComposeDesc>;
  TownNpcGlobal: CmsTable<TownNpcGlobalDesc>;
  TownNpcServer: CmsTable<TownNpcServerDesc>;
  TownNpcShop: CmsTable<TownNpcShopDesc>;
  SmuggleGoods: CmsTable<SmuggleGoodsDesc>;
  Smuggle: CmsTable<SmuggleDesc>;
  SmuggleGoodsCategory: CmsTable<SmuggleGoodsCategoryDesc>;
  TownNpcTemplate: CmsTable<TownNpcTemplateDesc>;
  BlindBid: CmsTable<BlindBidDesc>;
  Transcendence: CmsTable<TranscendenceDesc>;
  InfiniteLighthouse: CmsTable<InfiniteLighthouseDesc>;
  InfiniteLighthouseSchedule: CmsTable<InfiniteLighthouseScheduleDesc>;
  FriendlyMatchUseMaps: CmsTable<FriendlyMatchUseMapsDesc>;
  UnpopularDecreaseRate: CmsTable<UnpopularDecreaseRateDesc>;
  Research: CmsTable<ResearchDesc>;
  ResearchTask: CmsTable<ResearchTaskDesc>;
  InvestSeason: CmsTable<InvestSeasonDesc>;
  ReentryCost: CmsTable<ReentryCostDesc>;
  ClashUseMap: CmsTable<ClashUseMapDesc>;
  ClashTierList: CmsTable<ClashTierListDesc>;
  RewardSeasonItems: CmsTable<RewardSeasonItemsDesc>;
  ClashSeasonPrize: CmsTable<ClashSeasonPrizeDesc>;
  InvestSeasonRankingReward: CmsTable<InvestSeasonRankingRewardDesc>;
  ChangeItems: CmsTable<ChangeItemsDesc>;
  NeedShipSpecialStat: CmsTable<NeedShipSpecialStatDesc>;
  TutorialTradeEvent: CmsTable<TutorialTradeEventDesc>;
  ManufactureExp: CmsTable<ManufactureExpDesc>;
  ManufactureRecipe: CmsTable<ManufactureRecipeDesc>;
  ManufactureRoom: CmsTable<ManufactureRoomDesc>;
  ManufactureGroup: CmsTable<ManufactureGroupDesc>;

  [tableName: string]: any;
}

const cms: any = {};
const cmsRoot = path.resolve(path.join(__dirname, '..', '..', '..', '..', 'cms', 'server'));
const definePath = path.resolve(
  path.join(__dirname, '..', '..', '..', '..', 'cms', 'common', 'define.json5')
);
const localDefinePath = path.resolve(
  path.join(__dirname, '..', '..', '..', '..', 'cms', 'common', 'define.local.json5')
);

const loadFile = (filePath: string) => {
  const file = fs.readFileSync(filePath, 'utf8');
  try {
    const json = JSON.parse(file);
    Object.assign(cms, json);
  } catch (err) {
    throw new Error(`failed to parse '${filePath}': ${err.message}`);
  }
};

const loadFileByBinaryCode = (filePath: string) => {
  const file = fs.readFileSync(filePath, 'utf8');
  try {
    const json = JSON.parse(file);
    Object.assign(cms, json);
  } catch (err) {
    throw new Error(`failed to parse '${filePath}': ${err.message}`);
  }
};

const loadDefineFile = () => {
  cms.Define = {};
  let file = fs.readFileSync(definePath, 'utf8');
  try {
    const json = JSON5.parse(file);
    cms.Define = json;
  } catch (err) {
    throw new Error(`failed to parse define.json5': ${err.message}`);
  }

  if (mconf.isDev) {
    file = fs.readFileSync(localDefinePath, 'utf8');
    try {
      const json = JSON5.parse(file);
      Object.assign(cms.Define, json);
    } catch (err) {
      console.log(err);
    }
  }
};

export function load() {
  mlog.verbose('Start CMS loading');

  const files = fs.readdirSync(cmsRoot);
  const fileNamesByBC = [];
  const bcSuffix = `_BC${mconf.binaryCode}`;
  files.forEach((fileName) => {
    // [TEMP] 중국 판호용 임시 코드
    if (fileName === 'TownLocation_cn.json') {
      return;
    }

    if (fileName.includes('_BC')) {
      if (fileName.includes(bcSuffix)) {
        fileNamesByBC.push(fileName);
      }
      return;
    }

    const filePath = path.join(cmsRoot, fileName);
    if (fs.statSync(filePath).isFile()) {
      loadFile(filePath);
    }
  });

  loadDefineFile();
  for (const fileName of fileNamesByBC) {
    const filePath = path.join(cmsRoot, fileName);
    if (fs.statSync(filePath).isFile()) {
      loadFileByBinaryCode(filePath);
    }
  }

  preprocess(cms);
}

export default cms as Cms;
