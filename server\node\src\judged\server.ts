// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container, { Service } from 'typedi';
import _ from 'lodash';
import mkdirp from 'mkdirp';
import * as path from 'path';

import mlog from '../motiflib/mlog';
import mconf from '../motiflib/mconf';
import mhttp from '../motiflib/mhttp';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import * as mutil from '../motiflib/mutil';
import bodyParser from 'body-parser';
import express from 'express';
import http from 'http';
import * as expressError from '../motiflib/expressError';
import stoppable from 'stoppable';
import { JudgeLoopService } from './judgeLoop';
import * as Sentry from '@sentry/node';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// ------------------------------------------------------------------------------------------------
// 서비스 중단 플래그.
// ------------------------------------------------------------------------------------------------
let bStopping = false;

// ------------------------------------------------------------------------------------------------
// undefined 참조등으로 예외를 catch 하지 못하면 호출
// ------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// ------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// ------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// ----------------------------------------------------------------------------
// JudgeD 서버.
// ----------------------------------------------------------------------------
@Service()
export class JudgeD {
  private _bStopping: boolean; // 서버 종료 플래그.
  private _expressServer: any; // express 서버.
  private _judgeLoop: JudgeLoopService; // 검증 루프 서비스.

  // ----------------------------------------------------------------------------
  constructor() {
    this._bStopping = false;
    this._expressServer = undefined;
    this._judgeLoop = undefined;
  }

  // ----------------------------------------------------------------------------
  private async _startExpress(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Health-check 를 위한 express.
      const judgeApp = express();
      judgeApp.disable('x-powered-by');
      judgeApp.disable('etag');
      judgeApp.disable('content-type');
      judgeApp.use(bodyParser.json());
      judgeApp.use(expressError.middleware);
      this._expressServer = stoppable(http.createServer(judgeApp));

      const bindAddress = mconf.apiService.bindAddress;
      const port = mconf.apiService.port;

      mutil.registerHealthCheck(judgeApp);
      mutil.registerGarbageCollector(judgeApp);

      this._expressServer.listen(port, bindAddress, (err) => {
        if (err) {
          reject(err);
        }

        mlog.info('start listening ...', { bindAddress, port });
        resolve();
      });
    });
  }

  // ----------------------------------------------------------------------------
  private async _stopExpress(): Promise<void> {
    return new Promise((resolve, reject) => {
      this._expressServer.stop((err) => {
        if (err) {
          reject(err);
        }

        mlog.info('Express server stopped.');
        resolve();
      });
    });
  }

  // ----------------------------------------------------------------------------
  async start(): Promise<void> {
    try {
      // configd register/fetch.
      await mhttp.configd.registerInstance(undefined, mconf.appInstanceId, mconf.hostname);

      // config 동기화.
      // judged 의 경우, 설정 버전이 업데이트 된 경우, 딱히 할게 없다.
      const beforeVer = mconf.layoutVersion;
      await mhttp.configd.sync(beforeVer, undefined, undefined);

      await startTogglet();

      // Create temp folder.
      mkdirp.sync(path.join(mconf.validationHome, 'temp'));

      mutil.initSentry();

      await this._startExpress();

      // 메인 검증 서비스
      this._judgeLoop = new JudgeLoopService();
      this._judgeLoop.run(0);

      mlog.info('judged is up and running...');
    } catch (error) {
      mlog.error('failed to start judged', { error: error.message, extra: error.extra });
      mlog.error(error.stack);

      const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
      await slackNotifier.notify({ username: process.name, text: error.message });
      process.exit(1);
    }
  }

  // ----------------------------------------------------------------------------
  async stop(signalCode: string): Promise<void> {
    mlog.info('Stop signaled.', { signalCode });
    if (this._bStopping) {
      return;
    }

    this._bStopping = true;

    mlog.info('Stopping judged...');

    await this._judgeLoop.destroy();
    await this._stopExpress();  

    stopTogglet();

    mlog.info('judged stopped gracefully.');
    process.exit(0);
  }
}
