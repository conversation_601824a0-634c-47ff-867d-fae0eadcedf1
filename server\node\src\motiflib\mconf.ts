// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

/**
 * config module is organizes automatically from config/default.json5
 * to override, create config/production.json5
 */
import config from 'config';
import fs from 'fs';
import JSON5 from 'json5';
import _ from 'lodash';
import path from 'path';
import { MError, MErrorCode } from './merror';
import { PLATFORM } from './model/auth/enum';
import { resolveLocalDotJson5 } from './resolveLocalDotJson5';
import { getAppInfo } from './appInfo';

const _RequiredWorldConfig = {
  lobbyd: [
    'mysqlUserDb',
    'mysqlWorldDb',
    'worldPubsubRedis',
    'townRedis',
    'nationRedis',
    'collectorRedis',
    'sailRedis',
    'auctionRedis',
    'guildRedis',
    'guildPubsubRedis',
    'arenaRedis',
    'raidRedis',
    'userRedis',
    'clientVolanteUrl',
    'timezone',
    'countryCode',
    'rankingRedis',
    'blindBidRedis',
  ],
  realmd: [
    'mysqlUserDb',
    'mysqlWorldDb',
    'worldPubsubRedis',
    'townRedis',
    'nationRedis',
    'collectorRedis',
    'arenaRedis',
    'userRedis',
    'raidRedis',
    'timezone',
    'countryCode',
    'createDate',
    'rankingRedis',
    'blindBidRedis',
  ],
  townd: ['worldPubsubRedis', 'timezone', 'countryCode'],
  oceand: ['townRedis', 'nationRedis', 'worldPubsubRedis', 'timezone', 'countryCode'],
  saild: ['mysqlWorldDb', 'sailRedis', 'timezone', 'countryCode'],
  zonelbd: ['townLbRedis', 'oceanLbRedis', 'timezone', 'countryCode'],
};

const _OptionalWorldConfig = {
  lobbyd: ['bIsNonPK'],
  realmd: ['bIsNonPK'],
};

function _throwOnMissingConfig(cfgName: string) {
  throw new MError('Missing required config!', MErrorCode.INTERNAL_ERROR, { cfgName });
}

export interface World {
  id: string;
  address: string;
  port: number;
  disabled: boolean;
  http: {
    saild: { url: string; timeout?: number };
    zonelbd: { url: string; timeout?: number };
    realmd: { url: string; timeout?: number };
    chatd: { url: string; salt: string; timeout?: number };
    lgbillingd: { url: string; authPwd: string; timeout?: number };
    lgpayd: { url: string; authPwd: string; timeout?: number };
  };
  mysqlUserDb: any;
  mysqlWorldDb: any;
  worldPubsubRedis: any;
  townRedis: any;
  nationRedis: any;
  collectorRedis: any;
  clientVolanteUrl: string;
  bIsNonPK: boolean;
  countryCode: number;
  createDate: string;
}

// app ID. (Unique across a service realm.)
type IConfig = typeof config;
class MConf implements IConfig {
  [key: string]: any;

  appInstanceId: number;
  isDev: boolean;
  hostname: string;
  appId: string;
  binaryCode: string;
  platform: PLATFORM;
  layoutVersion: number;
  maxUsersPerWorld: number;

  worlds: World[];

  // from. default.json5
  layout?: string;
  log: {
    console: {
      level: string;
      inspectDepth: number;
      bUseStringifiedJsonPrintFormat: boolean;
    };
    file: {
      level: string;
      datePattern: string;
    };
    baseDir: string;
    bAppIdName: boolean;
  };

  // from. default.json5
  http: {
    configd: { url: string; timeout?: number };
    authd: { url: string; timeout?: number };
    oceand: { url: string; timeout?: number };
    saild: { url: string; timeout?: number };
    navigation: { url: string; timeout?: number };
    zonelbd: { url: string; timeout?: number };
    realmd: { url: string; timeout?: number };
    chatd: { url: string; salt: string; timeout?: number };
    lgd: { url: string; authPwd: string; timeout?: number };
    lgbillingd: { url: string; authPwd: string; timeout?: number };
    lglogd: { url: string; authPwd: string; timeout?: number };
    navid: { url: string; timeout?: number };
    lgpayd: { url: string; authPwd: string; timeout?: number };
  };

  // from. default.json5
  instance: {
    worldId: string;
  };

  serverdInstances: {
    appId: {
      worldId: number;
      socketServer: {
        bindAddress: string;
        port: number;
      };
      apiService: {
        bindAddress: string;
        port: number;
        url: string;
        tcpServer: {
          port: number;
          ip: string;
        };
      };
    };
  };

  constructor() {
    const appInfo = getAppInfo();

    this.appInstanceId = appInfo.instanceId;
    this.isDev = appInfo.isDev;
    this.hostname = appInfo.hostname;
    this.appId = appInfo.appId;
  }

  append(others: { [key: string]: any }) {
    _.merge(this, others);
  }

  appendWorldConfig(worldConfig: any) {
    // HTTP clients.
    _.merge(this.http, worldConfig.http);

    // 서버마다 필요한 설정 머지.
    const requiredCfgList = _RequiredWorldConfig[process.name];
    if (!requiredCfgList) {
      return;
    }
    for (const cfgName of requiredCfgList) {
      this[cfgName] = worldConfig[cfgName];

      if (this[cfgName] === undefined) {
        _throwOnMissingConfig(cfgName);
      }
    }

    const optionalCfgList = _OptionalWorldConfig[process.name];
    if (optionalCfgList) {
      for (const cfgName of optionalCfgList) {
        this[cfgName] = worldConfig[cfgName];
      }
    }
  }

  get<T>(setting: string): T {
    return config.get<T>(setting);
  }

  has(setting: string): boolean {
    return config.has(setting);
  }

  getWorldConfig(inWorldId?: string): any {
    const worldId = inWorldId ? inWorldId : this.worldId;
    if (!worldId) {
      return null;
    }

    for (const worldConfig of this.worlds) {
      if (worldConfig.id === worldId) {
        return worldConfig;
      }
    }

    return null;
  }

  getServerdInstances(serverType: string): any[] {
    let serverInstances: any[] = [];

    if (this.serverdInstances) {
      Object.keys(this.serverdInstances).forEach((appId) => {
        const nameTokens = appId.split('.');
        if (nameTokens[0] != serverType) {
          return;
        }
        serverInstances[appId] = this.serverdInstances[appId];
      });
    }
    return serverInstances;
  }

  // base 1
  getWorldNumber(): number {
    if (mconf.platform === PLATFORM.LINE) {
      // UWO-KR-01 와 같은 규칙.
      const arr = mconf.worldId.split('-');
      return parseInt(arr[arr.length - 1], 10);
    } else {
      return 1;
    }
  }

  util = config.util;
}

let mconf: MConf = new MConf();
_.merge(mconf, config);

// Merge local config.
try {
  const localFilePath = path.resolve(path.join(__dirname, '..', '..', 'config', resolveLocalDotJson5()));
  const localFile = fs.readFileSync(localFilePath, 'utf8');
  const localJson = JSON5.parse(localFile);
  _.merge(mconf, localJson);
} catch {}

export default mconf;
