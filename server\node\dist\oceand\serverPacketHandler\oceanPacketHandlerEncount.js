"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
const typedi_1 = __importDefault(require("typedi"));
const CMSConst = __importStar(require("../../cms/const"));
const merror_1 = require("../../motiflib/merror");
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const fleetManager_1 = require("../fleetManager");
const oceanCoordinate_1 = require("../../cms/oceanCoordinate");
const oceanEntityInstanceManager_1 = require("../oceanEntityInstanceManager");
const enum_1 = require("../../motiflib/model/ocean/enum");
const ocean_1 = require("../../motiflib/model/ocean");
const oceanNpcFleet_1 = require("../oceanNpcFleet");
const oceanProp_1 = require("../../cms/oceanProp");
const ex_1 = require("../../cms/ex");
const formula_1 = require("../../formula");
const multiBattle_1 = require("../../lobbyd/multiBattle");
const battleUtil_1 = require("../../lobbyd/battleUtil");
// 클라와 계산차를 허용하는 unreal unit.
//
// 배의 최대속도는 21 노트.  (3241 uu / sec)
// 1초당 동기화 되기 때문에, 최대 이 거리만큼, 실제 유저의 위치와 차이가 날 수 있다.
//const MarginOfErrorUu = 3241;
// const MarginOfErrorUu = 10000;
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
// 유저의 인카운트 요청
router.on(protocol_1.Protocol.LB2OC_REQ_ENCOUNT_BY_USER, async (req, res) => {
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 함대 체크.
    const userFleet = fleetManager.get(req.userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand.', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId: req.userId,
            targetNpcId: req.targetNpcId,
            targetUserId: req.targetUserId,
        });
    }
    const grid = userFleet.getGrid();
    if (!grid) {
        throw new merror_1.MError('User not in a grid.', merror_1.MErrorCode.USER_NOT_IN_GRID, {
            userId: req.userId,
        });
    }
    const sendFail = (codeResult) => {
        const responsePacket = new protocol_1.Protocol.OC2LB_RES_ENCOUNT_BY_USER();
        responsePacket.result = codeResult;
        res.send(responsePacket);
    };
    if (req.targetNpcId) {
        const npcFleet = grid.findVisibleNpc(req.targetNpcId);
        if (!npcFleet) {
            sendFail(merror_1.MErrorCode.ENCOUNT_TARGET_NOT_FOUND);
            return;
        }
        // npc가 인카운트 공격을 받을 수 있는지 체크.
        const result = npcFleet.canBeAttacked();
        if (result !== merror_1.MErrorCode.OK) {
            sendFail(result);
            return;
        }
        // 인카운트 거리 체크.
        const distUu = userFleet.getDistanceFrom(npcFleet.getLocation());
        if (distUu > CMSConst.get('OceanUserAttackRadius') + battleUtil_1.MARGIN_OF_ERROR_UU) {
            sendFail(merror_1.MErrorCode.ENCOUNT_TARGET_TOO_FAR_AWAY);
            return;
        }
        // 멀티인카운트 편성이 가능한 npc이면 부대 편성.
        const attackUserReinforcement = userFleet.getOceanUserReinforcement();
        if (npcFleet.ableToReinforcement() === true) {
            attackUserReinforcement.buildReinforcement(userFleet, npcFleet);
        }
        // 인카운트 상태를 주변 유저들에게 알림.
        userFleet.setStateAndBroadCast(enum_1.OCEAN_USER_STATE.ENCOUNT, true);
        // OK.
        userFleet.setEncountingNpc(req.targetNpcId);
        // 엘리트NPC의 경우 인카운트 종료시 전투진입이 아닐경우 또는, 전투패배시 즉시 스폰해야하기 때문에
        // npc의 AreaSpanwer cmsid를 저장 후, 즉시 스폰해주도록 설정.
        if (npcFleet instanceof oceanNpcFleet_1.OceanAreaNpcFleet) {
            const areaSpawnerCmsId = npcFleet.getOceanNpcAreaSpawnerCmsId();
            userFleet.setOceanNpcAreaSpawnerCmsId(areaSpawnerCmsId);
        }
        else {
            userFleet.setOceanNpcAreaSpawnerCmsId(0);
        }
        // OK.
        const responsePacket = new protocol_1.Protocol.OC2LB_RES_ENCOUNT_BY_USER();
        responsePacket.result = merror_1.MErrorCode.OK;
        responsePacket.targetType = enum_1.EncountTargetType.Npc;
        responsePacket.targetFleetData = npcFleet.getOceanFleetData();
        responsePacket.anchorInfo = (0, oceanCoordinate_1.CalcEncountAnchor)(userFleet.getLocation(), userFleet.getDegrees(), npcFleet.getLocation(), npcFleet.getDegrees(), true //bAttack
        );
        responsePacket.reinforcementFleets = attackUserReinforcement.getReinforcementFleets();
        responsePacket.paramType = 0;
        responsePacket.pveParam = {
            isQuestNpc: npcFleet.isQuestNpc(),
        };
        // 엘리트NPC의 경우 유저가 인카운트상태를 물고 있을 경우, 재스폰을 안하기 때문에
        // 로컬/엘리트NPC 모두 동일하게 인카운트시 디스폰 처리.
        typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager).deleteNpcFleet(npcFleet);
        res.send(responsePacket);
    }
    else {
        const targetUserFleet = grid.findVisibleUser(req.targetUserId);
        if (!targetUserFleet) {
            sendFail(merror_1.MErrorCode.ENCOUNT_TARGET_NOT_FOUND);
            return;
        }
        // 대상유저가 인카운트 공격을 받을 수 있는지 체크.
        const result = targetUserFleet.canBeAttacked(false);
        if (result !== merror_1.MErrorCode.OK) {
            sendFail(result);
            return;
        }
        // 안전해역에서는 PVP가 불가능하지만, 상대의 카르마에 따라 가능.
        const loc = userFleet.getLocation();
        const oceanTile = (0, oceanProp_1.LatLonToOceanTileDesc)(loc.latitude, loc.longitude);
        if (oceanTile) {
            if (oceanTile.regionCms.seaAreaType === ex_1.SEA_AREA_TYPE.SAFE) {
                const targetKarma = targetUserFleet.getKarma();
                const karmaDesc = (0, formula_1.getKarmaDescBasedOnKarmaVal)(targetKarma);
                if (karmaDesc && !karmaDesc.canAttackedAnywhere) {
                    sendFail(merror_1.MErrorCode.CANNOT_ATTACK_PVP_USER_ON_SAFE_OCEAN);
                    return;
                }
            }
        }
        // 인카운트 거리 체크.
        const distUu = targetUserFleet.getDistanceFrom(userFleet.getLocation());
        if (distUu > CMSConst.get('OceanUserAttackRadius') + battleUtil_1.MARGIN_OF_ERROR_UU) {
            sendFail(merror_1.MErrorCode.ENCOUNT_TARGET_TOO_FAR_AWAY);
            return;
        }
        // 실시간 PVP 가능 여부.
        const bMultiBattle = multiBattle_1.MultiBattleUtil.isMultiBattle(userFleet, targetUserFleet, req.pvpId);
        // 타겟 유저이 오프라인 유저는 멀티인카운트 함대구성 제외.
        // 실시간 PVP 는 faction 시스템 리워크 전까지 제외.
        const targetUserReinforcement = targetUserFleet.getOceanUserReinforcement();
        if (targetUserFleet.isOfflineAutoSailing() || bMultiBattle) {
            targetUserReinforcement.clearReinforcement();
        }
        else {
            targetUserReinforcement.buildReinforcement(targetUserFleet, userFleet);
        }
        // 피격유저의 로비서버로 통신하여, 인카운트 공격을 받을 수 있는지 확인.
        const lobbyPacket = new protocol_1.Protocol.OC2LB_REQ_ENCOUNT_BY_NET_USER();
        lobbyPacket.userId = targetUserFleet.userId;
        lobbyPacket.attackerUserId = req.userId;
        lobbyPacket.attackerUserKarmaCmsId = req.userKarmaCmsId;
        lobbyPacket.attackerAccountId = userFleet.accountId;
        lobbyPacket.attackerPubId = userFleet.pubId;
        lobbyPacket.anchorInfo = (0, oceanCoordinate_1.CalcEncountAnchor)(targetUserFleet.getLocation(), targetUserFleet.getDegrees(), userFleet.getLocation(), userFleet.getDegrees(), false //bAttack
        );
        lobbyPacket.reinforcementFleets = targetUserReinforcement.getReinforcementFleets();
        lobbyPacket.oceanFleetData = req.oceanFleetData;
        lobbyPacket.encountChoiceStats = req.encountChoiceStats;
        lobbyPacket.ducatPoint = req.ducatPoint;
        lobbyPacket.supplies = req.supplies;
        lobbyPacket.tradeGoods = req.tradeGoods;
        lobbyPacket.pvpId = req.pvpId;
        lobbyPacket.bMultiBattle = bMultiBattle;
        lobbyPacket.attackerRepresentedMateCmsId = userFleet.getRepresentedMateCmsId();
        lobbyPacket.attackerRepresentedMateIllustCmsId = userFleet.getRepresentedMateIllustCmsId();
        const resPacket = await targetUserFleet.sendToLobbyAndRecv(lobbyPacket);
        if (resPacket.result !== merror_1.MErrorCode.OK) {
            sendFail(resPacket.result);
            return;
        }
        const attackUserReinforcement = userFleet.getOceanUserReinforcement();
        if (targetUserFleet.isOfflineAutoSailing() || bMultiBattle) {
            attackUserReinforcement.clearReinforcement();
        }
        else {
            attackUserReinforcement.buildReinforcement(userFleet, targetUserFleet);
        }
        // 인카운트 상태를 주변 유저들에게 알림.
        userFleet.setStateAndBroadCast(enum_1.OCEAN_USER_STATE.ENCOUNT, true);
        targetUserFleet.setStateAndBroadCast(enum_1.OCEAN_USER_STATE.ENCOUNT, true);
        // OK.
        userFleet.setEncountingUser(targetUserFleet.userId);
        targetUserFleet.setEncountingUser(userFleet.userId);
        // 로비로 응답
        const targetFleetData = targetUserFleet.getFleetData();
        const responsePacket = new protocol_1.Protocol.OC2LB_RES_ENCOUNT_BY_USER();
        responsePacket.result = merror_1.MErrorCode.OK;
        responsePacket.targetType = targetUserFleet.isOfflineAutoSailing()
            ? enum_1.EncountTargetType.OfflineUser
            : enum_1.EncountTargetType.OnlineUser;
        responsePacket.targetFleetData = resPacket.oceanFleetData;
        responsePacket.anchorInfo = (0, oceanCoordinate_1.CalcEncountAnchor)(userFleet.getLocation(), userFleet.getDegrees(), targetUserFleet.getLocation(), targetUserFleet.getDegrees(), true //bAttack
        );
        responsePacket.reinforcementFleets = attackUserReinforcement.getReinforcementFleets();
        responsePacket.paramType = 1;
        responsePacket.pvpParam = {
            targetAccountId: targetUserFleet.accountId,
            targetPubId: targetUserFleet.pubId,
            targetDucatPoint: resPacket.ducatPoint,
            targetSupplies: resPacket.supplies,
            targetTradeGoods: resPacket.tradeGoods,
            targetKarma: targetFleetData.karma,
            targetKaramLastUpdateTimeUtc: targetFleetData.lastKarmaUpdateTimeUtc,
            targetKarmaCmsId: resPacket.karmaCmsId,
            targetNegoDucat: resPacket.negotiationCost,
            targetSurrDucat: resPacket.surrenderCost,
            targetNegoSuccessRate: resPacket.negoSuccessRate,
            targetEscapeSuccessRate: resPacket.escapeSuccessRate,
            bMultiBattle,
            targetRepresentedMateCmsId: targetUserFleet.getRepresentedMateCmsId(),
            targetRepresentedMateIllustCmsId: targetUserFleet.getRepresentedMateIllustCmsId(),
        };
        res.send(responsePacket);
    }
});
router.on(protocol_1.Protocol.LB2OC_REQ_ENCOUNT_END, async (req, res) => {
    const { userId, bAttack, encountResult } = req;
    // 유저 상태 검증.
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const grid = userFleet.getGrid();
    if (!grid) {
        throw new merror_1.MError('User not in a grid!', merror_1.MErrorCode.USER_NOT_IN_GRID, {
            userId,
        });
    }
    const userReinforcement = userFleet.getOceanUserReinforcement();
    const endcountingNpcId = userFleet.getEncountingNpc();
    const encountingUserId = userFleet.getEncountingUser();
    const bIsBattle = (0, ocean_1.isEncountBattle)(encountResult);
    userFleet.setEncountingNpc(null);
    userFleet.setEncountingUser(null);
    userReinforcement.clearReinforcement();
    // 엘리트NPC와 인카운트 종료 시 비전투 종료일 경우 마지막 디스폰 위치에 즉시 스폰.
    if (endcountingNpcId) {
        if (!bIsBattle) {
            const oceanNpcAreaSpawnerCmsId = userFleet.getOceanNpcAreaSpawnerCmsId();
            if (oceanNpcAreaSpawnerCmsId) {
                const oceanZone = userFleet.getCurrentZone();
                if (oceanZone) {
                    const areaSpawnEntry = oceanZone.getOceanTileNpcAreaSpawnEntry(oceanNpcAreaSpawnerCmsId);
                    if (areaSpawnEntry) {
                        areaSpawnEntry.revive();
                    }
                }
            }
        }
    }
    else if (encountingUserId) {
        const targetUserFleet = grid.findVisibleUser(encountingUserId);
        if (!targetUserFleet) {
            // 인카운트 중 상대유저가 로그아웃할 수 있기 때문에 그대로 응답패킷만 보내준다.
            res.send(new protocol_1.Protocol.OC2LB_RES_ENCOUNT_END());
            return;
        }
        // 난파 및 로그아웃으로 인카운트 종료 시 상대방에게도 강제로 인카운트 종료해줘야한다.
        let forcedResult;
        switch (encountResult) {
            case enum_1.EncountResult.START_BATTLE_BY_LOGOUT:
                forcedResult = enum_1.EncountResult.START_BATTLE_BY_ENEMY_LOGOUT;
                break;
            case enum_1.EncountResult.CANCELED_BY_LOGOUT:
                forcedResult = enum_1.EncountResult.CANCELED_BY_ENEMY_LOGOUT;
                break;
            case enum_1.EncountResult.CANCELED_BY_WRECK:
                forcedResult = enum_1.EncountResult.CANCELED_BY_ENEMY_WRECK;
                break;
            case enum_1.EncountResult.CANCELED_BY_ENTERING_VILLAGE:
                forcedResult = enum_1.EncountResult.CANCELED_BY_ENTERING_VILLAGE;
                break;
            case enum_1.EncountResult.CANCELED_BY_ENTERING_TOWN:
                forcedResult = enum_1.EncountResult.CANCELED_BY_ENTERING_TOWN;
                break;
        }
        if (forcedResult) {
            const packet = new protocol_1.Protocol.OC2LB_NTF_ENCOUNT_END_FORCEDLY();
            packet.userId = targetUserFleet.userId;
            packet.encountResult = forcedResult;
            targetUserFleet.sendToLobby(packet);
        }
    }
    else {
        throw new merror_1.MError('User not encounting!', merror_1.MErrorCode.USER_NOT_ENCOUNTING, {
            userId,
        });
    }
    if (!bIsBattle) {
        userFleet.setStateAndBroadCast(enum_1.OCEAN_USER_STATE.SAIL, true);
    }
    res.send(new protocol_1.Protocol.OC2LB_RES_ENCOUNT_END());
});
router.on(protocol_1.Protocol.LB2OC_NTF_CANCEL_ENCOUNT, async (packet, res) => {
    const userId = packet.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const npcId = userFleet.getEncountingNpc();
    userFleet.setEncountingNpc(null);
    userFleet.setEncountingUser(null);
    // 퀘스트 및 유저대상인 경우 npc가 없을 수 있다.
    if (!npcId) {
        return;
    }
    const grid = userFleet.getGrid();
    if (!grid) {
        return;
    }
    const npcFleet = grid.findVisibleNpc(npcId);
    if (!npcFleet) {
        return;
    }
    // NPC를 디스폰 시킨다.
    const entityInsMng = typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager);
    entityInsMng.deleteNpcFleet(npcFleet);
});
router.on(protocol_1.Protocol.LB2OC_REQ_ENCOUNT_USER_CHOICE, async (packet, res) => {
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userId = packet.userId;
    const choice = packet.choice;
    const encountResult = packet.encountResult;
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const targetUserId = userFleet.getEncountingUser();
    const targetUserFleet = fleetManager.get(targetUserId);
    if (!targetUserFleet) {
        throw new merror_1.MError('User target not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
            targetUserId,
        });
    }
    if (userId !== targetUserFleet.getEncountingUser()) {
        throw new merror_1.MError('encount user target mismatch!', merror_1.MErrorCode.INVALID_ENCOUNT_STATE, {
            userId,
            targetUserId: targetUserFleet.getEncountingUser(),
        });
    }
    const sendp = new protocol_1.Protocol.OC2LB_REQ_ENCOUNT_NET_USER_CHOICE();
    sendp.userId = targetUserFleet.userId;
    sendp.targetChoice = choice;
    sendp.encountResult = encountResult;
    await targetUserFleet.sendToLobbyAndRecv(sendp);
    const resp = new protocol_1.Protocol.OC2LB_RES_ENCOUNT_USER_CHOICE();
    res.send(resp);
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerEncount.js.map