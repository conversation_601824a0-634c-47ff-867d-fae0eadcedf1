// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Client<PERSON>acketHandler } from '../index';
import { CashShopManager } from '../../cashShopManager';
import { Container } from 'typedi/Container';
import * as mutil from '../../../motiflib/mutil';

// ----------------------------------------------------------------------------
// 단순히 LG Billing Server API 로 이어줌.
// ----------------------------------------------------------------------------

interface RequestBody {
  //
}

interface ResponseBody {
  billingApiResp: unknown; // 빌링 서버 API 의 response
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingQuerySalesList implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    const curTimeUtc = mutil.curTimeUtc();

    return Promise.resolve()
      .then(() => {
        return mhttp.lgbillingd.querySalesList(user.storeCode);
      })
      .then((billingApiResp) => {
        const cashShopManager = Container.get(CashShopManager);
        cashShopManager.updateSalesList(billingApiResp, user.storeCode, curTimeUtc);

        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          billingApiResp,
        });
      });
  }
}
