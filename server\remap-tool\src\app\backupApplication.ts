import * as path from 'path';
import chalk from 'chalk';
import { ConfigLoader } from '../config/configLoader';
import { DatabaseManager } from '../database/databaseManager';
import { RedisManager } from '../redis/redisManager';
import { BackupManager } from '../backup/backupManager';
import {
  BackupOptions,
  RestoreOptions,
  BackupResult,
  RestoreResult,
  BackupListItem
} from '../types/backup';

export interface BackupCliOptions {
  worldId?: string;
  databases?: string;
  includeRedis?: boolean;
  compress?: boolean;
  description?: string;
  configDir: string;
  backupDir: string;
}

export interface RestoreCliOptions {
  timestamp: string;
  worldId?: string;
  databases?: string;
  includeRedis?: boolean;
  force?: boolean;
  configDir: string;
  backupDir: string;
}

export interface ListBackupsCliOptions {
  backupDir: string;
}

export class BackupApplication {
  private configLoader: ConfigLoader;
  private dbManager: DatabaseManager | null = null;
  private redisManager: RedisManager | null = null;
  private backupManager: BackupManager | null = null;

  constructor(configDir: string) {
    this.configLoader = new ConfigLoader(configDir);
  }

  /**
   * 백업 생성
   */
  async createBackup(options: BackupCliOptions): Promise<BackupResult> {
    try {
      await this.initialize();

      const backupOptions: BackupOptions = {
        worldId: options.worldId,
        databases: options.databases ? options.databases.split(',') as any : undefined,
        includeRedis: options.includeRedis,
        compress: options.compress,
        description: options.description
      };

      console.log(chalk.blue('🔄 백업 시작...'));
      const result = await this.backupManager!.createBackup(backupOptions);

      if (result.success) {
        console.log(chalk.green('✅ 백업 완료'));
        console.log(`📁 백업 위치: ${result.backupPath}`);
        console.log(`📊 테이블 수: ${result.metadata.tableCount}`);
        console.log(`🔑 Redis 키 수: ${result.metadata.redisKeyCount}`);
        console.log(`💾 파일 크기: ${this.formatFileSize(result.metadata.size)}`);
        console.log(`⏱️ 소요 시간: ${(result.duration / 1000).toFixed(2)}초`);
      } else {
        console.error(chalk.red('❌ 백업 실패:'), result.error);
      }

      return result;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 백업 복원
   */
  async restoreBackup(options: RestoreCliOptions): Promise<RestoreResult> {
    try {
      await this.initialize();

      const restoreOptions: RestoreOptions = {
        timestamp: options.timestamp,
        worldId: options.worldId,
        databases: options.databases ? options.databases.split(',') as any : undefined,
        includeRedis: options.includeRedis,
        force: options.force
      };

      console.log(chalk.blue('🔄 백업 복원 시작...'));
      const result = await this.backupManager!.restoreBackup(restoreOptions);

      if (result.success) {
        console.log(chalk.green('✅ 복원 완료'));
        console.log(`📊 복원된 데이터베이스: ${result.restoredDatabases}개`);
        console.log(`📋 복원된 테이블: ${result.restoredTables}개`);
        console.log(`🔑 복원된 Redis 키: ${result.restoredRedisKeys}개`);
        console.log(`⏱️ 소요 시간: ${(result.duration / 1000).toFixed(2)}초`);
      } else {
        console.error(chalk.red('❌ 복원 실패:'), result.error);
      }

      return result;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 백업 목록 조회
   */
  async listBackups(options: ListBackupsCliOptions): Promise<BackupListItem[]> {
    try {
      // 백업 매니저만 초기화 (DB 연결 불필요)
      this.backupManager = new BackupManager(
        {} as DatabaseManager,
        {} as RedisManager,
        options.backupDir
      );

      console.log(chalk.blue('📋 백업 목록 조회...'));
      const backups = await this.backupManager.listBackups();

      if (backups.length === 0) {
        console.log(chalk.yellow('📭 백업이 없습니다.'));
        return backups;
      }

      console.log(chalk.green(`📦 총 ${backups.length}개의 백업을 찾았습니다:`));
      console.log();

      // 테이블 형태로 출력
      const headers = ['타임스탬프', '생성일시', '크기', '월드', '테이블', 'Redis 키', '설명'];
      const rows = backups.map(backup => [
        backup.timestamp,
        backup.createdAt.toLocaleString('ko-KR'),
        this.formatFileSize(backup.size),
        backup.worldCount.toString(),
        backup.tableCount.toString(),
        backup.redisKeyCount.toString(),
        backup.description || '-'
      ]);

      this.printTable(headers, rows);

      return backups;
    } catch (error) {
      console.error(chalk.red('❌ 백업 목록 조회 실패:'), error);
      return [];
    }
  }

  /**
   * 초기화
   */
  private async initialize(): Promise<void> {
    try {
      console.log('⚙️ 설정 로드 중...');
      const config = await this.configLoader.loadRemapToolConfig();

      console.log('🔌 데이터베이스 연결 초기화 중...');
      this.dbManager = new DatabaseManager(config);
      await this.dbManager.initialize();

      console.log('🔌 Redis 연결 초기화 중...');
      this.redisManager = new RedisManager(config);
      await this.redisManager.initialize();

      this.backupManager = new BackupManager(this.dbManager, this.redisManager);

      console.log(chalk.green('✅ 초기화 완료'));
    } catch (error) {
      console.error(chalk.red('❌ 초기화 실패:'), error);
      throw error;
    }
  }

  /**
   * 정리
   */
  private async cleanup(): Promise<void> {
    try {
      if (this.dbManager) {
        console.log('🔌 데이터베이스 연결 정리 중...');
        await this.dbManager.cleanup();
      }

      if (this.redisManager) {
        console.log('🔌 Redis 연결 정리 중...');
        await this.redisManager.cleanup();
      }

      console.log(chalk.gray('✅ 정리 완료'));
    } catch (error) {
      console.warn(chalk.yellow('⚠️ 정리 중 오류:'), error);
    }
  }

  /**
   * 파일 크기 포맷팅
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 테이블 출력
   */
  private printTable(headers: string[], rows: string[][]): void {
    const columnWidths = headers.map((header, index) => {
      const maxRowWidth = Math.max(...rows.map(row => (row[index] || '').length));
      return Math.max(header.length, maxRowWidth);
    });

    const headerRow = headers.map((header, index) =>
      header.padEnd(columnWidths[index] || 0)
    ).join(' | ');
    console.log(chalk.bold(headerRow));

    const separator = columnWidths.map(width => '-'.repeat(width)).join('-+-');
    console.log(separator);

    for (const row of rows) {
      const dataRow = row.map((cell, index) =>
        (cell || '').padEnd(columnWidths[index] || 0)
      ).join(' | ');
      console.log(dataRow);
    }
  }
}
