// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
import 'reflect-metadata';
import _ from 'lodash';
import Redis from 'ioredis';
import genericPool from 'generic-pool';
import * as crypto from 'crypto';

import * as scriptLoader from './scriptLoader';
import * as redisError from './redisError';
import mlog from '../motiflib/mlog';
import { Service } from 'typedi';
import { convertConfigForFailover } from './redisUtil';

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

export function generateRedisFunction(mredisPool: MRedisConnPool, redisCmd: string) {
  //  mlog.debug('[TEMP] generating redis function : ', redisCmd);
  return function bridge() {
    let redisConn = null;
    const redisArgs = arguments;
    return mredisPool
      .acquire()
      .then((conn: Redis.Redis) => {
        redisConn = conn;

        const procedure: Function = redisConn[redisCmd];
        return procedure.apply(redisConn, redisArgs);
      })
      .then((result: any) => {
        mredisPool.release(redisConn);
        return result;
      })
      .catch((err: { message: string }) => {
        if (redisConn) {
          mredisPool.release(redisConn);
        }

        mlog.error('redis error occurred during calling redis script ', { cmd: redisCmd });

        if (err instanceof (Redis as any).ReplyError) {
          // Convert to merror.
          throw redisError.convertToMError(err.message);
        }

        throw err;
      });
  };
}

// ----------------------------------------------------------------------------
// Redis connection pool class.
// ----------------------------------------------------------------------------
@Service()
export class MRedisConnPool {
  public poolImpl: genericPool.Pool<Redis.Redis> = null;
  private name: string = null;

  async init(name: string, poolCfg: any) {
    this.name = name;

    mlog.info(`redis pool (${name}) initializing ...`, { poolCfg });

    // Load and register lua script functions.
    const scripts = await scriptLoader.loadDir(poolCfg.scriptDir);

    // ioredis 에서 스크립트의 sha1 접근을 허용하지 않아서, 여기서 임시로 만들어 출력.
    for (const functionName of Object.keys(scripts)) {
      const scriptSha1 = crypto
        .createHash('sha1')
        .update(scripts[functionName].content)
        .digest('hex');
      // mlog.info('[redis-sha1-to-lua]', {
      //   functionName,
      //   sha1: scriptSha1,
      // });
    }

    // Create pool.
    this.poolImpl = genericPool.createPool(
      {
        create: () => {
          return new Promise<Redis.Redis>((resolve, reject) => {
            const redisCfgForFailover = convertConfigForFailover(poolCfg.redisCfg);
            const redisConn = new Redis(redisCfgForFailover);
            for (const functionName of Object.keys(scripts)) {
              const elem = scripts[functionName];
              redisConn.defineCommand(functionName, {
                numberOfKeys: 0,
                lua: elem.content,
              });
            }
            redisConn.on('connect', () => {
              // mlog.debug('[TEMP] ioredis connect');
            });
            redisConn.on('ready', () => {
              // mlog.debug('[TEMP] ioredis ready');
              resolve(redisConn);
            });
            redisConn.on('error', (err) => {
              mlog.error('ioredis err: ', { error: err.message });
              reject(err);
            });
            redisConn.on('close', () => {
              mlog.info('[TEMP] ioredis close', { name: this.name });
            });
            redisConn.on('reconnecting', () => {
              mlog.warn('[TEMP] ioredis reconnecting', { name: this.name });
            });
            redisConn.on('end', () => {
              mlog.info('[TEMP] ioredis end', { name: this.name });
            });
          });
        },
        destroy: (redisConn_2) => {
          return redisConn_2.quit().then((value) => {});
        },
      },
      poolCfg.pool
    );
    // Register redis built-in functions.
    // Use lazyConnect for temporary connection
    // No need to connect to the Redis server
    const tempRedisCfg = Object.assign({}, poolCfg.redisCfg, { lazyConnect: true });
    const tempRedis = new Redis(tempRedisCfg);
    const redisCommands = tempRedis.getBuiltinCommands();
    _.forEach(redisCommands, (redisCmd) => {
      this[redisCmd] = generateRedisFunction(this, redisCmd);
    });
    // Register lua script functions.
    const functionNames = Object.keys(scripts);
    _.forEach(functionNames, (functionName_1) => {
      this[functionName_1] = generateRedisFunction(this, functionName_1);
    });
    mlog.info(`redis pool (${name}) initialized`);
  }

  async destroy() {
    await this.poolImpl.drain();
    await this.poolImpl.clear();
    mlog.info(`redis pool (${this.name}) destroyed`);
  }

  async acquire(priority?: number) {
    return this.poolImpl.acquire(priority);
  }

  async release(resource: Redis.Redis) {
    return this.poolImpl.release(resource);
  }

  async drain() {
    return this.poolImpl.drain();
  }

  async clear() {
    return this.poolImpl.clear();
  }
}

@Service()
export class AuctionRedisConnPoolMgr {
  private _pools: { [index: number]: MRedisConnPool } = {};

  async init(name: string, cfg: any) {
    this._pools = {};

    for (const shard of cfg.shards) {
      const shardCfg = {
        redisCfg: shard.redisCfg,
        pool: shard.pool,
        scriptDir: cfg.scriptDir,
      };
      this._pools[shard.auctionCategory] = new MRedisConnPool();
      await this._pools[shard.auctionCategory].init(`${name}-${shard.auctionCategory}`, shardCfg);
    }
  }

  async destroy() {
    for (const pool of _.values(this._pools)) {
      await pool.destroy();
    }
  }

  getByAuctionCategory(category?: number): MRedisConnPool {
    if (this._pools[category]) {
      return this._pools[category];
    }
    // category 가 없는 경우는 auction 공통값들을 로드하는 경우. 이 경우 3번을 사용.
    return this._pools[3];
  }
}
