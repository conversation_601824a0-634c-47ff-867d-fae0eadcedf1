// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import cms from '../../../cms';
import { ClientPacketHandler } from '..';
import { User } from '../../user';
import { CPacket } from '../../userConnection';
import { ReceiveProducts } from '../common/billingReceiveInvenPurchases';
import { curTimeUtc } from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { BuffSync } from '../../userBuffs';
import { BillingUtil } from '../../userCashShop';
import EnsuredGiveItem = BillingUtil.EnsuredGiveItem;
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import { CashShopDesc } from '../../../cms/cashShopDesc';
import mlog from '../../../motiflib/mlog';
import { GiveItem } from '../../../motiflib/mhttp/linegamesBillingApiClient';

// ----------------------------------------------------------------------------
// 현금 상품 구매할때 내용물 지급하는 _receiveProducts가 정상 작동하는지 확인하기 위한 패킷
// 상품의 내용물만 지급만 하는것이고 구매하는건 아니기때문에 이 패킷 호출하고 구입했는지 확인하면 안됨
// 빌링에서 관리하는 재화(레드잼)가 있으면 로그만 남기고 지급 안됨
// ----------------------------------------------------------------------------

interface RequestBody {
  cashShopCmsId: number;
}

// billingReceiveInvenPurchase.ts의 ResponseBody
interface ResponseBody extends BuffSync {
  failStep?: string; // 일단은 클라 로그 참고용으로 보냄.
  billingApiRespForFail?: unknown;
  /** 공간 부족 등 받을 수 없어 메일로 간 것들 */
  mailIds?: number[];
}

// ----------------------------------------------------------------------------
export class Cph_Dev_ReceiveProducts implements ClientPacketHandler {
  testGameState(user: User): boolean {
    return true;
  }
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { cashShopCmsId } = body;

    const cashShopCms = cms.CashShop[cashShopCmsId];
    if (!cashShopCms) {
      throw new MError('invalid-cash-shop-cms', MErrorCode.ADMIN_INVALID_CASH_SHOP_CMS_ID, {
        cashShopCmsId,
      });
    }

    const respBody: ResponseBody = { sync: {} };
    const mailIds: number[] = [];
    const ensuredGiveItemsList: EnsuredGiveItem[][] =
      this.buildEnsuredGiveItemsListByCashShopCms(cashShopCms);

    return ReceiveProducts(ensuredGiveItemsList, respBody, mailIds, user, curTimeUtc()).then(() => {
      if (mailIds.length > 0) {
        respBody.mailIds = mailIds;
      }
      return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, respBody);
    });
  }

  buildEnsuredGiveItemsListByCashShopCms(cashShopCms: CashShopDesc): EnsuredGiveItem[][] {
    const ensuredGiveItemsList: EnsuredGiveItem[][] = [];

    if (cashShopCms.productRewardFixedId) {
      ensuredGiveItemsList.push(
        this.buildEnsuredGiveItemsByRewardFixedId(cashShopCms.productRewardFixedId)
      );
    }

    if (cashShopCms.eventPageId) {
      const ensuredGiveItems = this.buildEnsuredGiveItems('EventPage', cashShopCms.eventPageId, 1);
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.dailySubscriptionId) {
      const ensuredGiveItems = this.buildEnsuredGiveItems(
        'DailySubscription',
        cashShopCms.dailySubscriptionId,
        1
      );
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.illustSkinId) {
      const ensuredGiveItems = this.buildEnsuredGiveItems(
        'IllustSkin',
        cashShopCms.illustSkinId,
        1
      );
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.userTitleId) {
      const ensuredGiveItems = this.buildEnsuredGiveItems('UserTitle', cashShopCms.userTitleId, 1);
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    if (cashShopCms.productWorldBuffId && cashShopCms.productWorldBuffId.length > 0) {
      const ensuredGiveItems = this.buildEnsuredGiveItems('WorldBuff', cashShopCms.id, 1);
      if (ensuredGiveItems) {
        ensuredGiveItemsList.push(ensuredGiveItems);
      }
    }

    return ensuredGiveItemsList;
  }

  buildEnsuredGiveItems(
    productItemType: string,
    itemCd: number,
    amount: number
  ): EnsuredGiveItem[] {
    const ensureGiveItems: EnsuredGiveItem[] = [];
    const giveItem: GiveItem = {
      productItemType,
      itemCd: itemCd.toString(),
      coinChargeTypeCd: undefined,
      coinManageBalanceYn: 'N',
      amount,
    };
    const ret = BillingUtil.buildEnsuredGiveItem(giveItem);
    if (ret.bOk !== true) {
      mlog.warn(
        `failed to ensure give item (${BillingUtil.giveItemToString(giveItem)}). reason: ${
          ret.err?.reason
        }.`
      );
      return undefined;
    }

    ensureGiveItems.push(ret.value);
    return ensureGiveItems;
  }

  buildEnsuredGiveItemsByRewardFixedId(rewardFixedId: number): EnsuredGiveItem[] {
    const rewardFixedCms = cms.RewardFixed[rewardFixedId];
    if (!rewardFixedCms) {
      return undefined;
    }

    const ensuredGiveItems: EnsuredGiveItem[] = [];
    for (const elem of rewardFixedCms.rewardFixed) {
      const productItemType = this.rewardTypeToProductItemType(elem.Type);
      const fixedRewardEnsuredGiveItems = this.buildEnsuredGiveItems(
        productItemType,
        elem.Id,
        elem.Quantity
      );
      if (fixedRewardEnsuredGiveItems) {
        ensuredGiveItems.push(...fixedRewardEnsuredGiveItems);
      }
    }

    return ensuredGiveItems;
  }

  // REWARD_TYPE을 빌링 어드민 툴을 통해 등록되어 있는 타입으로 변환
  // userCashShop.ts의 buildEnsuredGiveItem의 함수 안에 타입 있음
  rewardTypeToProductItemType(rewardType: REWARD_TYPE): string {
    switch (rewardType) {
      case REWARD_TYPE.ITEM:
        return 'Item';
      case REWARD_TYPE.SHIP:
        return 'Ship';
      case REWARD_TYPE.MATE_EQUIP:
        return 'CEquip';
      case REWARD_TYPE.SHIP_SLOT_ITEM:
        return 'ShipSlot';
      case REWARD_TYPE.MATE:
        return 'Mate';
      case REWARD_TYPE.USER_TITLE:
        return 'UserTitle';
      case REWARD_TYPE.POINT:
        return 'Point';
      case REWARD_TYPE.PET:
        return 'Pet';
      default:
        return 'DEFAULT_TYPE';
    }
  }
}
