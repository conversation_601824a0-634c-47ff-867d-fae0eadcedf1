// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';
import assert from 'assert';

import { UserChangeSpec } from './commonChangeSpec';
import { TryData, Changes, CHANGE_TASK_RESULT, ActualGain, cloneTryData } from './userChangeTask';
import { User } from '../user';
import mlog from '../../motiflib/mlog';
import cms from '../../cms';
import * as mutil from '../../motiflib/mutil';
import * as cmsEx from '../../cms/ex';
import {
  opAddPoint,
  opAddItem,
  opLoadDepartSupply,
  opUnloadDepartSupply,
  opLoadTradeGoods,
  opUnloadTradeGoods,
  opAddMateEquip,
  opAddRewardExp,
  opAddFame,
  opAddShip,
  opAddMate,
  opAddSailor,
  opUpgradeShipBlueprint,
  opAddMateIntimacyOrLoyalty,
  opAddEnergy,
  opAddShipSlotItem,
  opSetTaskRewarded,
  opAddQuestItem,
  opAddPalaceTaxFreePermit,
  opAddShieldNonPurchaseCount,
  opAddShieldPurchaseCount,
  opAddArenaTicket,
  opAddUserShipBuildingExp,
  opAddShipCamouflage,
  opAddUserTitle,
  opAddFreeSweepTicket,
  opAddBuySweepTicket,
  opAddPet,
  opLoadSmuggleGoods,
  opUnloadSmuggleGoods,
  opSetCompleteTask,
} from './userChangeOperator';
import { LobbyService } from '../server';
import { RewardDesc, REWARD_TYPE, REWARD_WEIGHT_TYPE, RewardCmsElem } from '../../cms/rewardDesc';
import { RewardDropPoolDesc, RewardElemDesc } from '../../cms/rewardDropPoolDesc';
import { isCash } from '../../cms/pointDesc';
import { ShipEnchantedStat } from '../ship';
import { LGCashParam } from '../userPoints';
import { SHIP_CULTURE_TYPE } from '../shipBlueprint';
import { exchangeInvestSeasonReward } from '../../formula';

const ROLL_DENOM = 10_000_000;

export enum RNP_TYPE {
  NONE = 0,
  REWARD_FIXED = 1,
  REWARD = 2,
  PAYMENT = 3,
  CHOICE = 4,
}

export interface RewardInfo {
  rnpType: number;
  cmsId: number;
  gain: ActualGain;
}

export interface RnpElemResultFuncResult {
  bBreakRnpElemsLoop: boolean; // 반환값이 true 경우 rnpElems roop break
  changeTaskResultOfInternalOp?: CHANGE_TASK_RESULT; // resultFunction 안에 op 가 있을 경우 result
}

export interface RewardAndPaymentElem {
  type: RNP_TYPE;
  cmsId?: number; // reward, rewardFixed or payment cms id, choiceBoxGroup
  choiceBoxCmsId?: number; // 선택한 아이템

  amountRatio?: number; // 해당 값이 정의되어 있을 경우 amount 에 곱해서 처리한다.
  shipNames?: string[]; // 지급될 배의 이름.
  bAllowExceed?: boolean; // 인벤 공간 초과를 허용할지 여부.
  shipFixedRandomStat?: number; // 값이 존재할 경우 선박 랜덤스탯이 해당 값으로 고정
  // 지급 전 포인트가 hard cap 을 넘지 않았으나 지급 후 포인트가 hard cap 을 넘을 경우 지급 후 금액을 hard cap 금액으로 맞추지 않을지 여부
  bIsNotPermitAddToHardCapLimitLine?: boolean;
  // 가질 수 있는 허용치를 초과하는 경우, 에러 처리하지 않고 허용치까지만 추가하고 나머지는 버릴지
  // bIfExceededLimitAddMail를 true로 하면 버리지 않고 메일로 보냄
  // - 허용치: 보관함 공간, havableCount/hardCap 등
  bAllowAddToLimitIfExceeded?: boolean;

  // true      : 선창에 설정한 비율을 초과할 경우, 수령하지 않고 유저 선택으로 넘김. (전투 보상에서만 사용)
  // false     : 선창에 설정한 비율만큼 수령함.
  // undefined : 적재 비율과는 상관없이 선창 최대 크기만큼 수령.
  bSelectLoadsIfExceededRatio?: boolean;

  // 창고가 꽉찼을 경우 초과되는 보상을 우편으로 보냄 (퀘스트에서만 사용)
  bIfExceededAddMail?: boolean;

  // 가용할수 있는 허용치를 초과하는 경우, 메일로 보낼지 여부
  // - 허용치: 보관함 공간, havableCount/hardCap 등
  bIfExceededLimitAddMail?: boolean;

  // 획득 시 귀속 여부.
  bIsBound: boolean;

  // AchievementTerms
  bIsAccum: boolean;

  // accumulate 반환 값에 따른 처리.
  resultFunction?: (
    result: CHANGE_TASK_RESULT,
    rnpElem: RewardAndPaymentElem,
    user: User,
    tryData: TryData,
    changes: Changes
  ) => RnpElemResultFuncResult;
}

const defaultResultFunction = (
  result: CHANGE_TASK_RESULT,
  rnpElem: RewardAndPaymentElem,
  user: User,
  tryData: TryData,
  changes: Changes
) => {
  return { bBreakRnpElemsLoop: result > CHANGE_TASK_RESULT.OK_MAX };
};

export interface PickedRandomRewardElem {
  rewardType: REWARD_TYPE;
  cmsId: number;
  amount: number;
  id?: number;
}

export interface PickedRandomReward {
  rewardCmsId: number;
  rewards: PickedRandomRewardElem[];
}

export interface RewardCmsElemCommonExtra {
  isBound: number;
  isAccum: number;
}
export interface RewardCmsElemShipExtra extends RewardCmsElemCommonExtra {
  life: number;
  enchantedStats: ShipEnchantedStat[];
  enchantResult: string;
  enchantCount: number;
  rndStats: number[];
  guid: string;
}

export interface RewardCmsElemItemExtra extends RewardCmsElemCommonExtra {}

export interface RewardCmsElemShipSlotItemExtra extends RewardCmsElemCommonExtra {
  expireTimeUtc: number; // EXPIRED_TYPE.PROVIDE인 경우에만 값이 있음
  enchantLv: number;
}

export interface RewardCmsElemMateEquipmentExtra extends RewardCmsElemCommonExtra {
  expireTimeUtc: number; // EXPIRED_TYPE.PROVIDE인 경우에만 값이 있음
  enchantLv: number;
}

// -------------------------------------------------------------------------------------------------
// reward or payment
// -------------------------------------------------------------------------------------------------
export class RewardAndPaymentSpec implements UserChangeSpec {
  private _rnpElems: RewardAndPaymentElem[] = [];

  protected _user: User;
  protected _tryData: TryData;
  protected _changes: Changes;

  protected _pickedRandomRewards: PickedRandomReward[] = []; // glog 를 위한 선택된 random reward

  protected _lgCashParam: LGCashParam;

  constructor(rnpElems: RewardAndPaymentElem[] = [], lgCashParam: LGCashParam = undefined) {
    this._rnpElems = rnpElems;
    for (const elem of rnpElems) {
      if (!elem.resultFunction) {
        elem.resultFunction = defaultResultFunction;
      }
    }
    this._lgCashParam = lgCashParam;
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    this._user = user;
    this._tryData = tryData;
    this._changes = changes;

    let ret = CHANGE_TASK_RESULT.OK;
    for (const elem of this._rnpElems) {
      if (!elem.cmsId) {
        return CHANGE_TASK_RESULT.INTERNAL_ERROR; // TODO continue 으로 할지 고민.
      }

      // rnpElems 가 두 개 이상인 경우 roolback 을 준비해줘야 됨.
      let oriTryData;
      let oriChanges;
      if (this._rnpElems.length > 1) {
        oriTryData = cloneTryData(this._tryData);
        oriChanges = _.cloneDeep(this._changes);
      }

      switch (elem.type) {
        case RNP_TYPE.REWARD:
          // glog 를 위한 선택된 random reward
          const pickedRandomReward: PickedRandomReward = {
            rewardCmsId: elem.cmsId,
            rewards: [],
          };
          this._pickedRandomRewards.push(pickedRandomReward);
          ret = this._receiveReward(elem, pickedRandomReward);
          break;
        case RNP_TYPE.REWARD_FIXED:
          ret = this._receiveRewardFixed(elem);
          break;
        case RNP_TYPE.PAYMENT:
          ret = this._pay(elem);
          break;
        case RNP_TYPE.CHOICE:
          ret = this._choice(elem);
          break;
        default:
          return CHANGE_TASK_RESULT.INTERNAL_ERROR;
      }

      // roolback try data, changes, if failed.
      if (this._rnpElems.length > 1 && ret > CHANGE_TASK_RESULT.OK_MAX) {
        this._tryData = oriTryData;
        this._changes = oriChanges;
      }

      const resultFuncRet = elem.resultFunction(ret, elem, user, this._tryData, this._changes);
      if (resultFuncRet.changeTaskResultOfInternalOp !== undefined) {
        ret = resultFuncRet.changeTaskResultOfInternalOp;
      }
      if (resultFuncRet.bBreakRnpElemsLoop) {
        // resultFunction 안에 op가 있을 경우 내부 op 의 result 를 반환
        return resultFuncRet.changeTaskResultOfInternalOp === undefined
          ? ret
          : resultFuncRet.changeTaskResultOfInternalOp;
      }
    }
    return ret;
  }

  getPickedRandomRewards(): PickedRandomReward[] {
    return this._pickedRandomRewards;
  }

  // Receive a non-fixed reward by cms ID. (Reward.json)
  // @pickedRandomRewards glog 를 위한 선택된 random reward
  private _receiveReward(
    rnpElem: RewardAndPaymentElem,
    pickedRandomRewards: PickedRandomReward
  ): CHANGE_TASK_RESULT {
    const rewardCms = cms.Reward[rnpElem.cmsId];
    const pickeds = RewardAndPaymentSpec.pickFromRewardCms(rewardCms);

    const results: CHANGE_TASK_RESULT[] = [];
    const curTimeUtc = mutil.curTimeUtc();
    for (const picked of pickeds) {
      const res = this._tryElem(picked, rnpElem, curTimeUtc, pickedRandomRewards);
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res; // 보상 적용에 문제가 있다면 중지
      }

      results.push(res);
    }

    if (results.length === 0) {
      // Bad dice roll.
      // 또는 CMS.Reward 에 뭔가 오류가 있는 경우? 혹은 테이블에 개발용 입력..
      return CHANGE_TASK_RESULT.NOTHING;
    } else if (results.includes(CHANGE_TASK_RESULT.OK)) {
      return CHANGE_TASK_RESULT.OK;
    }

    // 뽑힌게 있지만 어떤 이유(하드캡 등)로 실제 적용된 건 없는 경우?
    // CHANGE_TASK_RESULT.EXCEEDS_POINT_HARD_CAP 도 NOTHING 으로 처리해도 될지?
    return CHANGE_TASK_RESULT.NOTHING;
  }

  /**
   * https://wiki.line.games/display/MOTIF/Reward
   * https://wiki.line.games/pages/viewpage.action?pageId=25825627
   * CMS.Reward를 이용하여 받을 수 있는 보상을 뽑습니다.
   */
  static pickFromRewardCmsGetByRewardDropPool(rewardCms: RewardDesc): RewardDropPoolDesc[] {
    // ------------------------------
    // util functions
    const pick = <Candidate>(
      candidates: Candidate[],
      getWeight: (candidate: Candidate) => number,
      totalWeight: number
    ): Candidate | undefined => {
      const rnd = Math.random() * totalWeight;
      let chanceOffset = 0;
      for (const candidate of candidates) {
        chanceOffset += getWeight(candidate);
        if (rnd < chanceOffset) {
          return candidate;
        }
      }
      return undefined;
    };
    const pickRewardElemFromRewardGroupId = (rewardDropPoolGroupId: number) => {
      const groupData = cmsEx.getRewardDropPoolDataInGroup(rewardDropPoolGroupId);
      if (!groupData) {
        assert(false, `maybe invalid rewardDropPool group id (rewardCmsId: ${rewardCms.id})`);
      }

      // 그룹의 가중치는 10_000_000 기준으로, 초과하면 CMS Validation에서 처리하는 것 참고
      // https://jira.line.games/browse/UWO-12244
      if (groupData.sumWeight > ROLL_DENOM) {
        mlog.error('invalid sum of weight in group', {
          rewardDropPoolGroupId,
          sumWeight: groupData.sumWeight,
        });
      }
      return pick(groupData.list, (elem) => elem.rewardWeight, groupData.sumWeight);
    };
    // ------------------------------

    const ratio = rewardCms.ratio;
    const weightType = rewardCms.weightType;

    if (weightType === REWARD_WEIGHT_TYPE.ABSOLUTE) {
      const results: RewardDropPoolDesc[] = [];

      for (const rewardGroupElem of rewardCms.reward) {
        const rollValue = Math.random() * ROLL_DENOM;
        const chance = ratio * rewardGroupElem.Weight;

        if (rollValue < chance) {
          const pickedRewardElem = pickRewardElemFromRewardGroupId(rewardGroupElem.GroupId);
          if (pickedRewardElem) {
            results.push(pickedRewardElem);
          }
        }
      }

      return results;
    } else if (weightType === REWARD_WEIGHT_TYPE.RELATIVE) {
      const results: RewardDropPoolDesc[] = [];

      const rollValue = Math.random() * ROLL_DENOM;
      if (rollValue < ratio) {
        const sumWeight = rewardCms.reward.reduce((acc, elem) => acc + elem.Weight, 0);
        if (sumWeight === 0) {
          // Weight가 전부 0인 경우(개발용 등)
          return results;
        }

        const pickedGroupElem = pick(rewardCms.reward, (elem) => elem.Weight, sumWeight);
        // rollValue < ratio 조건을 만족한 상태에서는 그룹은 무조건 뽑혀야 한다.

        const pickedRewardElem = pickRewardElemFromRewardGroupId(pickedGroupElem.GroupId);
        if (pickedRewardElem) {
          results.push(pickedRewardElem);
        }
      }

      return results;
    } else {
      //TODO? CMS Validation
      assert(false, `invalid CMS.Reward.weightType: ${weightType} (rewardCmsId: ${rewardCms.id})`);
    }
  }

  static pickFromRewardCms(rewardCms: RewardDesc): RewardElemDesc[] {
    const results = RewardAndPaymentSpec.pickFromRewardCmsGetByRewardDropPool(rewardCms);
    return results?.map((elem) => elem.rewardElem);
  }

  static buildMailAttachmentByReward(
    rewardDescs: RewardDesc[],
    bIsAccum: boolean,
    bIsBound: boolean = true
  ): string {
    if (!rewardDescs || rewardDescs.length === 0) {
      return null;
    }
    const pickeds: RewardElemDesc[] = [];

    rewardDescs.forEach((reward) => {
      pickeds.push(...RewardAndPaymentSpec.pickFromRewardCms(reward));
    });

    interface RewardElem {
      Type: REWARD_TYPE;
      Id: number;
      Quantity: number;
      Extra: string;
    }

    const extra: RewardCmsElemCommonExtra = {
      isBound: bIsBound ? 1 : 0,
      isAccum: bIsAccum ? 1 : 0,
    };
    const extraStr = JSON.stringify(extra);
    const rewards: RewardElem[] = [];
    for (const elem of pickeds) {
      let amount: number;
      const minQuantity = elem.MinQuantity;
      const maxQuantity = elem.MaxQuantity;
      if (minQuantity === maxQuantity) {
        amount = minQuantity;
      } else {
        amount = Math.floor(Math.random() * (maxQuantity - minQuantity)) + minQuantity;
      }

      rewards.push({
        Type: elem.Type,
        Id: elem.Id,
        Quantity: amount,
        Extra: extraStr,
      });
    }

    return JSON.stringify(rewards);
  }

  private _receiveRewardFixed(rnpElem: RewardAndPaymentElem): CHANGE_TASK_RESULT {
    const rewardFixedCms = cms.RewardFixed[rnpElem.cmsId];
    let res: CHANGE_TASK_RESULT;
    const curTimeUtc = mutil.curTimeUtc();
    for (const rewardElem of rewardFixedCms.rewardFixed) {
      // https://jira.line.games/browse/UWO-17298
      // [서버] RewardFixed로 지급하는 선박의 스탯을 85%로 고정하는 기능 추가
      if (rnpElem.shipFixedRandomStat === undefined) {
        rnpElem.shipFixedRandomStat = cms.Const.RewardFixedBlueprintRatio.value;
      }
      res = this._tryElem(rewardElem, rnpElem, curTimeUtc);
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }

    return res;
  }

  private _pay(rnpElem: RewardAndPaymentElem): CHANGE_TASK_RESULT {
    const questPaymentCms = cms.QuestPayment[rnpElem.cmsId];
    if (!questPaymentCms) {
      mlog.error('[RNP] Invalid quest payment.', {
        userId: this._user.userId,
        type: rnpElem.type,
        cmsId: rnpElem.cmsId,
      });

      return CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }

    let res: CHANGE_TASK_RESULT;
    const curTimeUtc = mutil.curTimeUtc();
    for (const paymentElem of questPaymentCms.payment) {
      res = this._tryElem(paymentElem, rnpElem, curTimeUtc);
      if (
        res !== CHANGE_TASK_RESULT.OK &&
        res !== CHANGE_TASK_RESULT.NOTHING &&
        res !== CHANGE_TASK_RESULT.EXCEEDS_POINT_HARD_CAP
      ) {
        return res;
      }
    }

    return res;
  }

  private _choice(rnpElem: RewardAndPaymentElem): CHANGE_TASK_RESULT {
    const choiceBoxGroup = cmsEx.getChoiceBoxGroup(rnpElem.cmsId);
    if (!choiceBoxGroup || !choiceBoxGroup[rnpElem.choiceBoxCmsId]) {
      mlog.error('[RNP] Invalid choice box group', {
        choiceBoxGroup: rnpElem.cmsId,
        choiceBoxCmsId: rnpElem.choiceBoxCmsId,
      });

      return CHANGE_TASK_RESULT.INTERNAL_ERROR;
    }

    if (rnpElem.shipFixedRandomStat === undefined) {
      rnpElem.shipFixedRandomStat = cms.Const.ChoiceBoxShipBlueprintRatio.value;
    }

    const choiceBoxCms = choiceBoxGroup[rnpElem.choiceBoxCmsId];
    const curTimeUtc = mutil.curTimeUtc();
    return this._tryElem(
      {
        Type: choiceBoxCms.rewardType,
        Id: choiceBoxCms.choiceBoxId,
        Quantity: choiceBoxCms.choiceBoxQuantity,
      },
      rnpElem,
      curTimeUtc
    );
  }

  // Try processing an "rnp elem". (reward-and-payment element)
  //  - 'reward' in Reward.json
  //  - 'rewardFixed' in RewardFixed.json
  //  - 'payment' in QuestPayment.json
  // @pickedRandomReward glog 를 위한 선택된 random reward
  protected _tryElem(
    rewardCmsElem: RewardCmsElem,
    rnpElem: RewardAndPaymentElem,
    curTimeUtc: number,
    pickedRandomReward?: PickedRandomReward
  ): CHANGE_TASK_RESULT {
    mlog.verbose('[REWARD-TASK] single elem:', {
      userId: this._user.userId,
      rewardElem: rewardCmsElem,
    });

    if (rewardCmsElem.Type === REWARD_TYPE.REWERD_SEASON_ITEMS) {
      rewardCmsElem = exchangeInvestSeasonReward(rewardCmsElem, curTimeUtc);
    }

    // First determine quantity.
    let amount: number = rewardCmsElem.Quantity;
    if (amount === undefined) {
      const minQuantity = rewardCmsElem.MinQuantity;
      const maxQuantity = rewardCmsElem.MaxQuantity;
      if (minQuantity === maxQuantity) {
        amount = minQuantity;
      } else {
        amount = Math.floor(Math.random() * (maxQuantity - minQuantity)) + minQuantity;
      }
    }
    // If it's a payment, negate the quantity.
    if (rnpElem.type === RNP_TYPE.PAYMENT) {
      amount = -amount;
    }

    if (rnpElem.amountRatio) {
      amount = Math.floor(amount * rnpElem.amountRatio);
    }

    // 보상이 두캇, 교역품, 보급품일 경우 국가 순위 보너스 적용
    if (
      rnpElem.type === RNP_TYPE.REWARD &&
      ((rewardCmsElem.Type === REWARD_TYPE.POINT && rewardCmsElem.Id === cmsEx.DucatPointCmsId) ||
        rewardCmsElem.Type === REWARD_TYPE.DEPART_SUPPLY ||
        rewardCmsElem.Type === REWARD_TYPE.TRADE_GOODS)
    ) {
      const { nationManager } = Container.get(LobbyService);
      const nationRank = nationManager.getPowerRank(this._user.nationCmsId);
      if (nationRank) {
        const nationRankingEffectCms =
          cms.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.REWARD_DUCAT_CARGO];
        const nationRankBonus = nationRankingEffectCms.rankingEffectVal[nationRank - 1];
        if (nationRankBonus) {
          amount = Math.floor((amount * nationRankBonus) / 1000);
        }
      }
    }

    const pickedRandomRewardElem: PickedRandomRewardElem = {
      rewardType: rewardCmsElem.Type,
      cmsId: rewardCmsElem.Id,
      amount,
    };
    if (pickedRandomReward) {
      pickedRandomReward.rewards.push(pickedRandomRewardElem);
    }
    switch (rewardCmsElem.Type) {
      case REWARD_TYPE.POINT:
        assert(
          !isCash(rewardCmsElem.Id) || this._lgCashParam,
          `Can not get ${rewardCmsElem.Id} here. Please report to #dev-qa slack channel.`
        );
        return opAddPoint(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount,
          rewardCmsElem.Id === cmsEx.DucatPointCmsId && rnpElem.amountRatio !== undefined,
          false,
          this._lgCashParam,
          rnpElem.bIsNotPermitAddToHardCapLimitLine,
          rnpElem.bIfExceededLimitAddMail
        );

      case REWARD_TYPE.ITEM:
        return opAddItem(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount,
          rnpElem.bAllowExceed,
          rnpElem.bAllowAddToLimitIfExceeded,
          rewardCmsElem.Extra,
          rnpElem.bIsAccum,
          rnpElem.bIsBound,
          rnpElem.bIfExceededAddMail,
          rnpElem.bIfExceededLimitAddMail
        );

      case REWARD_TYPE.DEPART_SUPPLY:
        if (amount >= 0) {
          return opLoadDepartSupply(
            this._user,
            this._tryData,
            this._changes,
            rewardCmsElem.Id,
            amount,
            rnpElem.bSelectLoadsIfExceededRatio,
            rnpElem.bIfExceededAddMail
          );
        } else {
          return opUnloadDepartSupply(
            this._user,
            this._tryData,
            this._changes,
            rewardCmsElem.Id,
            -amount
          );
        }

      case REWARD_TYPE.TRADE_GOODS:
        if (amount >= 0) {
          return opLoadTradeGoods(
            this._user,
            this._tryData,
            this._changes,
            rewardCmsElem.Id,
            amount,
            rnpElem.bSelectLoadsIfExceededRatio,
            rewardCmsElem.shipId,
            rewardCmsElem.pointInvested
          );
        } else {
          return opUnloadTradeGoods(
            this._user,
            this._tryData,
            this._changes,
            rewardCmsElem.Id,
            -amount,
            rewardCmsElem.shipId
          );
        }

      case REWARD_TYPE.MATE_EQUIP:
        return opAddMateEquip(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount,
          rnpElem.bAllowExceed,
          rnpElem.bAllowAddToLimitIfExceeded,
          rewardCmsElem.Extra,
          rnpElem.bIsBound,
          rnpElem.bIfExceededAddMail,
          rnpElem.bIfExceededLimitAddMail
        );

      case REWARD_TYPE.ADVENTURE_EXP:
        return opAddRewardExp(
          this._user,
          this._tryData,
          this._changes,
          cmsEx.JOB_TYPE.ADVENTURE,
          amount,
          cmsEx.FirstFleetIndex
        );

      case REWARD_TYPE.TRADE_EXP:
        return opAddRewardExp(
          this._user,
          this._tryData,
          this._changes,
          cmsEx.JOB_TYPE.TRADE,
          amount,
          cmsEx.FirstFleetIndex
        );

      case REWARD_TYPE.BATTLE_EXP:
        return opAddRewardExp(
          this._user,
          this._tryData,
          this._changes,
          cmsEx.JOB_TYPE.BATTLE,
          amount,
          cmsEx.FirstFleetIndex
        );

      case REWARD_TYPE.ADVENTURE_FAME:
        return opAddFame(
          this._user,
          this._tryData,
          this._changes,
          cmsEx.JOB_TYPE.ADVENTURE,
          amount
        );

      case REWARD_TYPE.TRADE_FAME:
        return opAddFame(this._user, this._tryData, this._changes, cmsEx.JOB_TYPE.TRADE, amount);

      case REWARD_TYPE.BATTLE_FAME:
        return opAddFame(this._user, this._tryData, this._changes, cmsEx.JOB_TYPE.BATTLE, amount);

      case REWARD_TYPE.SHIP:
        return opAddShip(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          false,
          rnpElem.shipNames && rnpElem.shipNames.length > 0 ? rnpElem.shipNames.shift() : null,
          rnpElem.bAllowExceed,
          rnpElem.shipFixedRandomStat,
          rewardCmsElem.Extra,
          rnpElem.bIsBound,
          pickedRandomRewardElem,
          rnpElem.bIfExceededAddMail
        );

      case REWARD_TYPE.CAPTURED_SHIP:
        return opAddShip(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          true,
          rnpElem.shipNames && rnpElem.shipNames.length > 0 ? rnpElem.shipNames.shift() : null,
          false,
          rnpElem.shipFixedRandomStat,
          rewardCmsElem.Extra,
          rnpElem.bIsBound,
          pickedRandomRewardElem
        );

      case REWARD_TYPE.MATE:
        return opAddMate(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          true,
          rnpElem.bAllowExceed,
          rnpElem.bAllowAddToLimitIfExceeded,
          rewardCmsElem.Extra,
          rnpElem.bIsBound,
          rnpElem.bIfExceededAddMail,
          rnpElem.bIfExceededLimitAddMail
        );

      case REWARD_TYPE.SAILOR:
        return opAddSailor(this._user, this._tryData, this._changes, amount, false);

      case REWARD_TYPE.SHIP_BLUEPRINT:
        return opUpgradeShipBlueprint(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          true
        );

      case REWARD_TYPE.MATE_INTIMACY_OR_LOYALTY:
        return opAddMateIntimacyOrLoyalty(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount
        );

      case REWARD_TYPE.ENERGY:
        return opAddEnergy(this._user, this._tryData, this._changes, amount);

      case REWARD_TYPE.SHIP_SLOT_ITEM:
        return opAddShipSlotItem(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount,
          rnpElem.bAllowExceed,
          rnpElem.bAllowAddToLimitIfExceeded,
          rewardCmsElem.Extra,
          rnpElem.bIsBound,
          rnpElem.bIfExceededAddMail
        );

      case REWARD_TYPE.QUEST_ITEM:
        return opAddQuestItem(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount,
          false,
          rnpElem.bAllowAddToLimitIfExceeded
        );

      case REWARD_TYPE.TAX_FREE_PERMIT:
        return opAddPalaceTaxFreePermit(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount
        );

      case REWARD_TYPE.SHIELD_NON_PURCHASE_COUNT:
        return opAddShieldNonPurchaseCount(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount
        );

      case REWARD_TYPE.SHIELD_PURCHASE_COUNT:
        return opAddShieldPurchaseCount(
          this._user,
          this._tryData,
          this._changes,
          rewardCmsElem.Id,
          amount
        );

      case REWARD_TYPE.ARENA_TICKET:
        return opAddArenaTicket(this._user, this._tryData, this._changes, amount);

      case REWARD_TYPE.WESTERN_SHIP_BUILD_EXP:
        return opAddUserShipBuildingExp(
          this._user,
          this._tryData,
          this._changes,
          SHIP_CULTURE_TYPE.WESTERN,
          amount
        );

      case REWARD_TYPE.ORIENTAL_SHIP_BUILD_EXP:
        return opAddUserShipBuildingExp(
          this._user,
          this._tryData,
          this._changes,
          SHIP_CULTURE_TYPE.ORIENTAL,
          amount
        );
      case REWARD_TYPE.SHIP_CAMOUFLAGE:
        return opAddShipCamouflage(this._user, this._tryData, this._changes, rewardCmsElem.Id);

      case REWARD_TYPE.USER_TITLE:
        return opAddUserTitle(this._user, this._tryData, this._changes, rewardCmsElem.Id);

      case REWARD_TYPE.FREE_SWEEP_TICKET:
        return opAddFreeSweepTicket(this._user, this._tryData, this._changes, amount);

      case REWARD_TYPE.BUY_SWEEP_TICKET:
        return opAddBuySweepTicket(this._user, this._tryData, this._changes, amount);

      case REWARD_TYPE.PET:
        return opAddPet(this._user, this._tryData, this._changes, rewardCmsElem.Id);

      case REWARD_TYPE.SMUGGLE_GOODS:
        if (amount >= 0) {
          return opLoadSmuggleGoods(
            this._user,
            this._tryData,
            this._changes,
            rewardCmsElem.Id,
            amount,
            rnpElem.bSelectLoadsIfExceededRatio,
            rewardCmsElem.shipId,
            rewardCmsElem.pointInvested
          );
        } else {
          return opUnloadSmuggleGoods(
            this._user,
            this._tryData,
            this._changes,
            rewardCmsElem.Id,
            -amount,
            rewardCmsElem.shipId
          );
        }

      default:
        return CHANGE_TASK_RESULT.NOTHING;
    }
  }
}

export class ReceiveTaskRewardSpec extends RewardAndPaymentSpec {
  constructor(
    private taskCmsId: number,
    private category: cmsEx.TASK_CATEGORY,
    private index: number,
    private bIsAutoClear: boolean,
    rewards: RewardAndPaymentElem[]
  ) {
    super(rewards);
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const res = super.accumulate(user, tryData, changes);
    if (res !== CHANGE_TASK_RESULT.OK && res !== CHANGE_TASK_RESULT.NOTHING) {
      return res;
    }

    if (this.bIsAutoClear) {
      const res = opSetCompleteTask(
        user,
        tryData,
        changes,
        this.taskCmsId,
        this.category,
        this.index
      );
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }

    return opSetTaskRewarded(user, tryData, changes, this.taskCmsId, this.category, this.index);
  }
}
