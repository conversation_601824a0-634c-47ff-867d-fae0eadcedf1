// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { Sync, Resp } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import * as mutil from '../../../motiflib/mutil';
import { Dev } from '../../../proto/lobby/proto';
import { ClientPacketHandler } from '../index';
import { MANUFACTURE_TYPE } from '../../../cms/manufactureExpDesc';
import { ManufactureExpLevelChange } from '../../../motiflib/model/lobby';
import puManufactureExpLevelUpdate from '../../../mysqllib/sp/puManufactureExpLevelUpdate';
import { UserChangeSpec } from '../../UserChangeTask/commonChangeSpec';
import {
  TryData,
  Changes,
  CHANGE_TASK_RESULT,
  CHANGE_TASK_REASON,
  UserChangeTask,
} from '../../UserChangeTask/userChangeTask';
import { opAddManufactureExp } from '../../UserChangeTask/userChangeOperator';
import mlog from '../../../motiflib/mlog';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// 생산 레벨 및 경험치를 변경하는 치트키.
// 아래 3가지 방식 중 하나를 선택하여 사용하시면 됩니다.
// 1. 요청받은 level 값으로 바로 업데이트
// 2. 요청받은 exp 값으로 증가 (buildManufactureExpLevelChanges 이용)
// 3. 요청받은 exp 값으로 증가 (UserChangeTask 이용)
// ex) 2번과 3번 테스트는 script\Etc\LDevShortcut.lua 에서 키를 맵핑하여 사용하시면 편합니다.
// ----------------------------------------------------------------------------

const rsn = 'dev_set_manufacture_level';
const add_rsn = null;

interface RequestBody {
  manufactureType: number;
  level: number;
  exp?: number;
}

// ----------------------------------------------------------------------------
export class Cph_Dev_SetManufactureLevel implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;

    const { manufactureType, level: reqLevel } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.ensureCheatAccessLevel(Dev.SET_MANUFACTURE_LEVEL);

    // Validate body
    let reqType: number = manufactureType;
    if (typeof manufactureType === 'string') {
      reqType = parseInt(manufactureType, 10);
    }

    if (mutil.isNotANumber(reqType)) {
      throw new MError(
        'invalid-manufacture-type',
        MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL,
        {
          body,
        }
      );
    }

    if (reqType < MANUFACTURE_TYPE.CASTING || reqType > MANUFACTURE_TYPE.MEDICINE) {
      throw new MError(
        'invalid-manufacture-type',
        MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL,
        {
          body,
        }
      );
    }

    if (!body.exp) {
      // body.exp 가 존재하지 않는 경우에는 현재 레벨에서 reqLevel 로 직접 변경
      return useCaseByLevel(user, packet, reqType, reqLevel);
    }

    // body.exp 가 존재하는 경우에는 현재 경험치에서 exp만큼 증가시킵니다.
    if (true) {
      return useCaseByExpBuilder(user, packet, reqType, body.exp);
    }

    return useCaseByExpWithUserChangeTask(user, packet, reqType, body.exp);
  }
}

// ---------------------------------------------------------------------------
/**
 * 요청한 레벨값으로 직접 업데이트하는 코드 샘플입니다.
 * @param user
 * @param packet
 * @param body
 * @returns
 */
function useCaseByLevel(user: User, packet: CPacket, manufactureType: number, reqLevel: number) {
  const { userDbConnPoolMgr } = Container.get(LobbyService);

  if (mutil.isNotANumber(reqLevel)) {
    throw new MError('invalid-level', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      reqLevel,
    });
  }

  if (reqLevel <= 0) {
    throw new MError('invalid-level', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      reqLevel,
    });
  }

  // maxlevel check
  const maxLevel = cms.Const.MaxManufactureLv.value;
  if (reqLevel > maxLevel) {
    throw new MError('max-level-over', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      reqLevel,
      maxLevel,
    });
  }

  const userManufacture = user.userManufacture;
  const curLevel = userManufacture.getLevel(manufactureType);
  if (curLevel === reqLevel) {
    throw new MError('already-same-level', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      reqLevel,
    });
  }

  const newExp = cms.ManufactureExp[reqLevel - 1].accumulateExp[manufactureType - 1];
  const expLevelChanges: ManufactureExpLevelChange[] = [
    {
      type: manufactureType,
      exp: newExp,
      level: reqLevel,
      oldLevel: curLevel,
    },
  ];

  return puManufactureExpLevelUpdate(
    userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
    user.userId,
    expLevelChanges
  )
    .then(() => {
      const sync: Sync = user.userManufacture.applyExpLevelChange(expLevelChanges, null);

      return sync;
    })
    .then((sync) => {
      return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
    });
}

// ---------------------------------------------------------------------------
/**
 * buildManufactureExpLevelChanges 를 이용하여 경험치를 증가시켜서 레벨업하는 코드 샘플입니다.
 * @param user
 * @param packet
 * @param body
 * @returns
 */
function useCaseByExpBuilder(user: User, packet: CPacket, manufactureType: number, exp: number) {
  const { userDbConnPoolMgr } = Container.get(LobbyService);

  if (mutil.isNotANumber(exp)) {
    throw new MError('invalid-exp', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      exp,
    });
  }

  if (exp < 0) {
    throw new MError('invalid-exp', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      exp,
    });
  }

  const curExp = user.userManufacture.getExp(manufactureType);
  const newExp = curExp + exp;
  const expLevelChanges: ManufactureExpLevelChange[] = [];
  user.userManufacture.buildManufactureExpLevelChanges(
    user,
    user.companyStat,
    manufactureType,
    newExp - curExp,
    expLevelChanges
  );

  // 강제로 복수의 타입을 업데이트해보기
  // userManufacture.buildManufactureExpLevelChanges(
  //   user,
  //   user.companyStat,
  //   5,
  //   289900,
  //   expLevelChanges
  // );

  return puManufactureExpLevelUpdate(
    userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
    user.userId,
    expLevelChanges
  ).then(() => {
    const sync: Sync = user.userManufacture.applyExpLevelChange(expLevelChanges, null);

    mlog.verbose('[dev_set_manufacture_level] useCaseByExpBuilder', {
      userId: user.userId,
      expLevelChanges,
      sync,
    });

    return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
  });
}

// ---------------------------------------------------------------------------
/**
 * UserChangeTask 을 이용하여 경험치를 증가시켜서 레벨업하는 코드 샘플입니다.
 * @param user
 * @param packet
 * @param body
 * @returns
 */
class ManufactureExpLevelSpec implements UserChangeSpec {
  constructor(private manufactureType: number, private addExp: number) {}

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    return opAddManufactureExp(user, tryData, changes, this.manufactureType, this.addExp);
  }
}

function useCaseByExpWithUserChangeTask(
  user: User,
  packet: CPacket,
  manufactureType: number,
  addExp: number
) {
  if (mutil.isNotANumber(addExp)) {
    throw new MError('invalid-exp', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      addExp,
    });
  }

  if (addExp < 0) {
    throw new MError('invalid-exp', MErrorCode.INVALID_REQ_BODY_SET_MANUFACTURE_LEVEL, {
      addExp,
    });
  }

  const changeTask = new UserChangeTask(
    user,
    CHANGE_TASK_REASON.DEV_SET_MANUFACTURE_LEVEL,
    new ManufactureExpLevelSpec(manufactureType, addExp),
    add_rsn
  );

  const res = changeTask.trySpec();
  if (res !== CHANGE_TASK_RESULT.OK) {
    throw new MError('try-specs-failed', MErrorCode.TRY_DEV_SET_MANUFACTURE_LEVEL_SPEC_FAILED, {
      res,
    });
  }

  return changeTask.apply().then((sync) => {
    mlog.verbose('[dev_set_manufacture_level] useCaseByExpWithUserChangeTask', {
      userId: user.userId,
      sync,
    });

    return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
  });
}

