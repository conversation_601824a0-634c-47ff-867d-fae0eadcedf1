// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import Container from 'typedi';
import cms from '../cms';
import * as CMSConst from '../cms/const';
import * as cmsEx from '../cms/ex';
import { getOceanNpcCms } from '../cms/ex';
import {
  GLogCoordinate,
  RoundAngleToAbsDirection,
  getGLogCoordinate,
} from '../cms/oceanCoordinate';
import * as formula from '../formula';
import { BC } from '../motiflib/battleCommon';
import { MError, MErrorCode } from '../motiflib/merror';
import mlog from '../motiflib/mlog';
import { MateEquipmentNub, MateNub, WorldBuffSmall } from '../motiflib/model/lobby';
import {
  BattleParamArena,
  BattleParamChallenge,
  BattleParamClash,
  BattleParamFreeTakebackData,
  BattleParamQuickModeData,
  OceanFleetData,
  OceanFleetNameDesc,
  OceanFleetShipSlotData,
  OptionalBattleParam,
  PvpPlunderReward,
  ReinforcementFleet,
  RequiredBattleParam,
} from '../motiflib/model/ocean';
import { EncountResult, EncountTargetType, OceanFleetNameType } from '../motiflib/model/ocean/enum';
import * as mutil from '../motiflib/mutil';
import {
  ACTION_COUNTER_TYPE,
  BattleMissionClearInfo,
  BattleResult,
  BattleResultShip,
  BattleReward,
} from './battleResult';
import Mate, { MateUtil } from './mate';
import { LobbyService } from './server';
import { All, ShipEnchantedStat, NationPolicySync } from './type/sync';
import { GLogParam, LoginInfo, User } from './user';

import { BattleChallengeDesc } from '../cms/battleChallengeDesc';
import { BattleTermsListDesc } from '../cms/battleTermsListDesc';
import { OCEAN_NPC_TYPE } from '../cms/oceanNpcDesc';
import { OceanNpcStageAddFleetDesc } from '../cms/oceanNpcStageDesc';
import { LatLonToOceanTileDesc } from '../cms/oceanProp';
import { BattleMapDesc } from '../cms/oceanTileDesc';
import { SHIP_SLOT_TYPE } from '../cms/shipSlotDesc';
import * as npcFleetBuilder from '../motiflib/model/ocean/npcFleetBuilder';
import { isNotANumber } from '../motiflib/mutil';
import { oceanNpcStatCache } from '../motiflib/stat/cache/oceanNpcStatCache';
import { TownManager } from './townManager';
import { WorldPassiveUtil } from './userPassives';
//import { add } from 'gl-matrix/src/gl-matrix/vec2';
import { BossRaidDesc, RECORD_HP } from '../cms/BossRaidDesc';
import { OceanNpcStageDesc } from '../cms/oceanNpcStageDesc';
import { BattleType } from '../motiflib/model/lobby';
import { SimpleSubRedis } from '../redislib/pubsub';
import { ArenaOceanFleetData, PlayableOpponentData } from './arena';
import BattleContinuous, { BattleContinuousChange } from './battleContinuous';
import BattleContinuousContext from './battleContinuousContext';
import { BattleUtil } from './battleUtil';
import { calcMultiPvpPlunderReward } from './encount';
import { MultiBattleUtil, MultiplayerParam } from './multiBattle';
import { EnergyChange } from './userEnergy';
import { InfiniteLighthouseDesc } from '../cms/infiniteLighthouseDesc';
import { FriendlyEncountState } from './userFriendlyEncount';
import { FleetHelper } from './fleetHelper';
import { PrData, RewardData } from '../motiflib/gameLog';
import { ResearchNode } from './userResearch';
import { ArriveTownSpec } from './UserChangeTask/commonChangeSpec';
import { ShipCargoNub, ShipCargoChange } from './ship';
import { REWARD_TYPE } from '../cms/rewardDesc';
import { ClashUtil } from './clashUtil';
import { ClashOceanFleetDataEx } from './clash';
import { BattleRedisHelper } from './battleRedisHelper';

// ----------------------------------------------------------------------------
// [운영 로그] 전투 시작 key.
// ----------------------------------------------------------------------------
const _LogColPve = 'battle_pve_start';
const _LogColPvp = 'battle_pvp_start';
const _LogColChallenge = 'battle_challenge_start';
const _LogColArena = 'battle_arena_start';
const _LogColRaid = 'boss_raid_start';
const _BattleStartRsn = 'battle_start';
const _LogColGuildRaid = 'guild_boss_raid_start';
const _LogColInfiniteLighthouse = 'lighthouse_start';
const _LogColClash = 'battle_clash_start';
// ----------------------------------------------------------------------------
// 전투 미션 추가 목표 랜덤 타입.
// ----------------------------------------------------------------------------
export enum BATTLE_MISSION_RAND_TYPE {
  FIXED = 0,
  GROUP = 1,
}

// ----------------------------------------------------------------------------
// 유저 faction ID (BattleFaction 테이블 ID)
// ----------------------------------------------------------------------------
export const UserFactionId = 67000000;

// ----------------------------------------------------------------------------
// Faction Relation
// ----------------------------------------------------------------------------
export enum FactionRelation {
  Neutral = 0,
  Friendly = 1,
  Hostile = 2,
}

// ----------------------------------------------------------------------------
// 유저 faction 과의 관계
// ----------------------------------------------------------------------------
function _GetFactionRelationWithUser(battleFactionCmsId: number): FactionRelation {
  const bfCms = cms.BattleFaction[UserFactionId];
  return bfCms[battleFactionCmsId.toString()];
}

// ----------------------------------------------------------------------------
// BattleTermsList 테이블을 그룹별로 묶어놓는 캐시 기능.
// ----------------------------------------------------------------------------

let _BattleTermsListsByGroup = undefined;

function _GetBattleTermsListsByGroup(group: number): BattleTermsListDesc[] {
  if (!_BattleTermsListsByGroup) {
    _BattleTermsListsByGroup = {};

    _.forOwn(cms.BattleTermsList, (btl) => {
      if (!_BattleTermsListsByGroup[btl.group]) {
        _BattleTermsListsByGroup[btl.group] = [];
      }

      _BattleTermsListsByGroup[btl.group].push(btl);
    });
  }

  return _BattleTermsListsByGroup[group];
}

// ----------------------------------------------------------------------------
// battle param interfaces.
/*
* 주의
   1. 클라이언트 battle_object_schema.lua도 수정해줘야한다.
   2. BattleParam 수정 시  cms/prologue에서 반드시
      PrologueBattleResume.json5, PrologueEncountEnd.json도 수정해줘야한다.
* 주의
*/
// ----------------------------------------------------------------------------

export interface AxialCoord {
  r: number;
  q: number;
}

export interface BattleInfo {
  // 전투 고유 ID
  battleId: string;

  // 전투 파라미터
  battleParam: BattleParam;

  // 멀티플레이어 key
  multiId?: string;

  // txn log 카운터
  logCount: number;

  // multi sub
  multiSubRedis?: SimpleSubRedis;

  // multi ctrl sub
  multiCtrlSubRedis?: SimpleSubRedis;
}

// 전투 무료 무르기 업데이트 정보.
export interface BattleFreeTakebackChange {
  usedFreeTurnTakebackCount: number;
  usedFreePhaseTakebackCount: number;
  updateTimeUtc: number;
}

// 전투 퀵 모드 업데이트 정보.
export interface BattleQuickModeChange {
  quickModeCount: number;
  updateTimeUtc: number;
}

export interface BattleParamMate {
  // 배치된 선실 슬롯 번호
  slotIdx: number;

  // mate cms id
  cmsId: number;

  // 교역 레벨
  tradeLevel: number;

  // 전투 레벨
  battleLevel: number;

  // 탐험 레벨
  adventureLevel: number;

  // 충성도. (없으면 100 으로 처리됨)
  loyalty?: number;

  // 상태 플래그
  stateFlags: number;

  // 승급레벨. (없으면 0 으로 처리됨)
  awakenLevel?: number;

  // 장착 아이템 정보 CEquip cms id
  equipments: {
    cmsId: number;
    dye1?: number;
    dye2?: number;
    dye3?: number;
    isCostume: number;
    enchantLv: number;
  }[];

  // 장비 노출 여부 (유저 데이터.)
  isHideHat?: number;
  isHideCape?: number;
  isHideFace?: number;

  // 훈련 레벨 (유저 데이터)
  trainingGrade?: number;

  // 훈련 포인트 (유저 데이터)
  trainingPoints?: { [type: number]: number };

  // 일러스트 id
  illustCmsId?: number;

  clearAutoMateSkillQuest?: number;

  isTranscended?: number;
}

export interface BattleParamShip {
  // ship cms id
  cmsId: number;

  // 선원 수
  sailor: number;

  // 내구도
  durability: number;

  // 선박 수명.
  life: number;

  // 설계도 정보
  blueprint: {
    cmsId: number;
    level: number;
    sailMasteryLevel: number;
  };

  // 보급 품 수량
  supplies: number[];

  // 슬롯 Overrides
  slotOverrides: { cmsId: number; slotIdx: number; enchantLv: number }[];

  // 항해사 리스트
  mates: BattleParamMate[];

  // 이 선박에 적용된 전투 패시브 목록 (BattlePassive.json ID 배열)
  battlePassives: number[];

  // 이 선박에 적용된 (전투용) 월드 버프 목록.
  bpWorldBuffs: WorldBuffSmall[];

  // enchant
  enchantedStats: ShipEnchantedStat[];

  // 건조 랜덤 스탯.
  rndStats: number[];

  // 클라이언트 전용
  sailorDl?: number;
  maxDurability?: number;

  // 선박명.
  name?: string;

  // 스탯 오버라이딩에 사용 될 npc 전용 shipTemplate cms id
  oceanNpcShipTemplateCmsId?: number;

  // 멀티타일 유닛 약점 (NPC)
  tileDmgMultipliers?: {
    r: number;
    q: number;
    cannon: number;
    ramming: number;
  }[];

  // 잔여 적재량 (User)
  cargoRemainCapacity?: number;
}

// 멀티 (실시간) pvp 에서, 대상을 이겼을때 보상 내용.
export interface MultiPvpReward {
  pvpPoint: number;
  cargo: {
    [cmsId: number]: number;
  };
}

export interface ArgBattleParamEnemyFleetExtra {
  fleetIdx?: number;
  multiPvpReward?: MultiPvpReward;
  representedMateCmsId?: number;
  representedMateIllustCmsId?: number;
}

export interface BattleParamFleet {
  // 플레이어 선단 레벨 | NPC 함대 레벨
  level: number;

  // OceanNpc.id
  oceanNpcId: number;

  // 함대 이름 정보
  fleetNameDesc: OceanFleetNameDesc;

  // 피아구분
  faction: BC.Faction;

  // default_formation 고정
  battleFormationCmsId: number;

  bIsApplyBattleFormationBuff: boolean;

  // 전투력
  combatPower: number;

  // 국가
  nationCmsId: number;

  // 앵커 정보
  anchor: number;
  anchorAc?: {
    r: number;
    q: number;
  };
  direction: number;
  distance: number;

  // 전투스킬이 있는 아이템 목록/수량
  items: {
    [itemCmsId: number]: number;
  };

  shipCustomizing: {
    sailCrestCmsId?: number;
    bodyColor1?: number;
    bodyColor2?: number;
    bodyColorTint?: number;
  };

  shipSailPattern: {
    sailPatternCmsId?: number;
    color1?: number;
    color2?: number;
    color3?: number;
  };

  // 선박 리스트
  ships: BattleParamShip[];

  // 함대에 활성화된 월드 버프.
  bpWorldBuffs: WorldBuffSmall[];

  // 완성된 수집 목록. (Collection 테이블 ID 배열)
  collections: number[];

  // 멀티 전투 시 fleetIdx 지정.
  fleetIdx?: number;

  // 멀티 (실시간 ) pvp 에서, 대상을 이겼을때 보상 내용.
  multiPvpReward?: MultiPvpReward;

  // 국가의 정책.
  nationPolicies?: { [group: number]: NationPolicySync };

  // 연구
  researchNodes?: { [group: number]: ResearchNode };

  // 선박 외형
  costumeShipSlots: { shipSlotCmsId: number; enchantLv: number }[];

  // 대표 항해사
  representedMateCmsId?: number;

  // 대표 항해사 일러스트
  representedMateIllustCmsId?: number;
}

// 멀티인카운트 함대 데이터
export interface BattleParamReinforcementFleet extends BattleParamFleet {
  // 난입 턴
  turnNum: number;
}

// 전투 미션 (추가 목표)
export interface BattleParamMission {
  groupId: number; // BattleMissionGroup 테이블 ID
  btlIds: number[]; // BattleTermsList 테이블 ID 배열
}

export interface BattleParamRaid {
  bossRaidCmsId: number; // BossRaid 테이블 ID or GuildBossRaid 테이블 ID
  // challenge: boolean; // 도전상태여부(토벌에 성공했지만 아직 레이드 진행중일 경우 도전상태로 전투에 참가한다)
  remainingHp?: number; // 남은 HP (상회 토벌)
  accumDamage?: number; // 현재 세션에서 받은 총 데미지 (일반 토벌)
}

export interface BattleParamInfiniteLighthouse {
  stageCmsId: number;
  sessionId: number;
}

export interface BattleParam {
  // 유저 ID
  userId: number;

  // 전투 모드
  battleType: BattleType;

  // 전투를 수행하는 클라이언트의 git revision
  revision: string;

  // 클라이언트의 패치 git reivision
  patchRevision: string;

  // 전투 시작시 클라이언트의 version text. (LUUwoGameInstance:GetVersionText())
  versionText: string;

  // 전투 시작시에 사용될 random seed
  randomSeed: number;

  // BattleMap.json ID
  mapCmsId: number;

  // Climate.json ID
  climateCmsId: number;

  // 전투 시작시간 (게임 상의 '일').
  startTime: number;

  // 유저가 인카운트를 거는 경우 true.
  bAttack: boolean;

  // 대상이 npc 또는 온라인유저 또는 오프라인유저
  targetType: EncountTargetType;

  // 대상이 유저일 경우 대상 유저id
  targetUserId: number;

  // 운영 로그 전용. (user id | npc id | nil)
  targetLogId?: number;

  // 인카운트 결과.
  encounterResult: number;

  // 아군 (플레이어) 함대 정보.
  ally: BattleParamFleet;

  // 적군 함대 정보.
  enemy: BattleParamFleet;

  // 추가 함대 정보.
  addFleets?: BattleParamFleet[];

  // 멀티인카운트 참전 함대 정보.
  reinforcements: BattleParamReinforcementFleet[];

  // 전투 시작시 날씨.  Weather 테이블 CMS ID.
  startWeather: number;

  // 전투 시작 시 바람 세기(0 - 10)
  startWindSpeed: number;

  // 전투 시작 시 바람 방향 AbsDirection
  startWindDirection: number;

  // 전투에서 발생하는, 퀘스트 진행상황 변경을 기록할 퀘스트 데이터복사본.
  questData: { [questCmsId: number]: { uflags: number; lflags: number } };

  // "도전" 전투시에 설정되는 오브젝트(그 외 상황엔 nil)
  // BattleChallenge 테이블 ID 와 난이도가 있다.
  challenge?: BattleParamChallenge;

  // 오프라인 항해중 발생한 전투인지에 대한 여부.
  bOfflineSailing?: boolean;

  // 개발용 (클라이언트에서 빠른 전투 시작 시 전달.)
  bTest: boolean;

  // 전투 시작시 유저 보유 포인트들.
  points: { [cmsId: number]: number };

  // 무르기 관련 데이타.
  freeTakebackData: BattleParamFreeTakebackData;

  // 권역을 점유한 국가의 국력.
  nationPower?: number;

  // 전투맵 로딩시 오브젝트 레이어 아이디 배열.
  bmoLayerIndexArray?: number[];

  // 쾌속 관련 데이타.
  quickModeData?: BattleParamQuickModeData;

  // OceanNpcStage cms에서 생성된 퀘스트.
  questCmsId?: number;

  // 퀘스트로 인한 encount 로 발생한 전투인 경우, 해당 퀘스트 cms ID 를 기록.
  encountQuestCmsId?: number;

  // pvp 승리 시 약탈 보상.
  pvpPlunderReward?: PvpPlunderReward;

  // 미션 정보
  mission: BattleParamMission;

  // "모의전" 전투시에 설정되는 오브젝트(그 외 상황엔 nil)
  arena?: BattleParamArena;

  // "레이드" 또는 "상회레이드" 전투시에 관련된 정보.
  raid?: BattleParamRaid;

  // 도전의 탑에서 사용되는 정보
  infiniteLighthouse?: BattleParamInfiniteLighthouse;

  // 실시간 멀티플레이 전투인지 여부.
  bMultiBattle?: boolean;

  // 전 함대에 부여되는 버프 (BattleBuff.id[]).
  stageBuffs?: number[];

  //
  clash?: BattleParamClash;
}

// Detailed info about each mate's exp gain. (Mostly for UI.)
export interface BattleEndMateBattleExpGain {
  mateCmsId: number;
  amount: number;
}
export interface BattleEndResult {
  mateExpGains: BattleEndMateBattleExpGain[];
  ducatGains: number;
  userExpGain: number;
  fameGain: number;
  missionsCleared: BattleMissionClearInfo;
  numTurns: number; // 종료 시점의 턴 번호
  mapCmsId: number;
  oceanNpcId: number;
  allyShips?: BattleResultShip[];
  mission: BattleParamMission;
  sunkShipIds: number[];
}

// ----------------------------------------------------------------------------
// 전투 액션 타입 Enum
// ----------------------------------------------------------------------------
export enum BattleActionType {
  Invalid = 0,
  StartTurn = 1,
  EndTurn = 2,
  Move = 3,
  Takeback = 4,
  Pass = 5,
  // Rotate = 6,
  NewAttack = 7,
  // CancelMoveRotate = 8, 사용하지 않으나, DB에 저장되기 때문에, 그대로둠
  Repair = 9,
  Heal = 10,
  StartPhase = 11,
  Cheat = 12,
  SkillCast = 13,
  GlobalSkillCast = 14,
  DuelRequest = 15,
  DuelRequestReply = 16,
  DuelStart = 17,
  DuelEnd = 18,
  EndPhase = 19,
  Escape = 20,
  GameOver = 21,
  AutoBattle = 22,
  QuickMode = 23,
  StartBattle = 24,
  AutoBattleOption = 25,
  ActionPhase = 26,

  // 특이 액션들은 100부터 시작.
  QuestFlagSet = 101,
  QuestReward = 102,
  QuestBuff = 103,
  QuestPassive = 104,
  QuestGrantSkill = 105,
  QuestGameOver = 106,
  QuestEndPhase = 107,
  QuestShowActionTiles = 108,
  QuestChangeBattleResultCondition = 109,
  QuestChangeMaxTurnNum = 110,
  QuestFocusTile = 111,
  QuestFocusUnit = 112,
  QuestChangeMapObjectLayer = 113,
  QuestPlaySound = 114,
  QuestStopSound = 115,
  QuestChangeWeather = 116,
  QuestChangeTOD = 117,
  QuestProcessReinforcement = 118,
  QuestForcedRetired = 119,
  QuestDlgChoose = 120,
  QuestRandomFlagIndex = 121,
  QuestRotate = 122,
  QuestAddMapObject = 123,
  QuestRemMapObject = 124,
  QuestChangePersonality = 125,
  QuestHoldSailState = 126,
  QuestSetOrderLayer = 127,
  QuestSpawnRandomMapObject = 128,
  QuestShowAlertTile = 129,
  QuestChangeParamPct = 130,
  QuestChangeZocRange = 131,
  QuestEnableRecoveryFriendly = 132,

  MultiplayAutoBattle = 201,
  MultiplayAutoBattleOption = 202,
}

// ----------------------------------------------------------------------------
// UserBattle object.
// ----------------------------------------------------------------------------
class UserBattle {
  private _battleInfo: BattleInfo;
  private _battleRewards: { [type: number]: { [id: number]: BattleReward } };

  // 무료 무르기 정보.
  private _usedFreeTurnTakebackCount: number;
  private _usedFreePhaseTakebackCount: number;
  private _lastFreeTakebackUpdateTimeUtc: number;

  // 무료 쾌속 정보.
  private _quickModeCount: number;
  private _lastQuickModeCountUpdateTimeUtc: number;

  // 전투 결과에서 UI 를 위한 정보
  private _battleEndResult: BattleEndResult;

  // 무료 연속 전투
  private _battleContinuous: BattleContinuous;

  // 연속 전투, 연속 소탕 부가 정보
  private _battleContinuousContext: BattleContinuousContext;

  constructor() {
    this._battleInfo = {
      battleId: undefined,
      battleParam: undefined,
      multiId: undefined,
      logCount: 0,
      multiSubRedis: undefined,
    };
    this._battleRewards = {};
    this._usedFreeTurnTakebackCount = undefined;
    this._usedFreePhaseTakebackCount = undefined;
    this._lastFreeTakebackUpdateTimeUtc = undefined;
    this._quickModeCount = undefined;
    this._lastQuickModeCountUpdateTimeUtc = undefined;
    this._battleEndResult = undefined;
    this._battleContinuous = undefined;
    this._battleContinuousContext = undefined;
  }

  initWithLoginInfo(loginInfo: LoginInfo): void {
    for (const elem of loginInfo.battleRewards) {
      if (!this._battleRewards[elem.type]) {
        this._battleRewards[elem.type] = {};
      }
      this._battleRewards[elem.type][elem.cmsId] = {
        Type: elem.type,
        Id: elem.cmsId,
        Quantity: elem.quantity,
        receivedQuantity: elem.receivedQuantity,
      };
    }

    this._usedFreeTurnTakebackCount = loginInfo.usedFreeTurnTakebackCount;
    this._usedFreePhaseTakebackCount = loginInfo.usedFreePhaseTakebackCount;
    this._lastFreeTakebackUpdateTimeUtc = parseInt(loginInfo.lastFreeTakebackUpdateTimeUtc, 10);

    this._quickModeCount = loginInfo.quickModeCount;
    this._lastQuickModeCountUpdateTimeUtc = parseInt(loginInfo.lastQuickModeCountUpdateTimeUtc, 10);

    if (loginInfo.battle) {
      if (loginInfo.battle && loginInfo.battle.battleEndResult) {
        this._battleEndResult = JSON.parse(loginInfo.battle.battleEndResult);
      }
    }

    this._battleContinuous = new BattleContinuous(
      loginInfo.usedFreeContinuousCount,
      parseInt(loginInfo.lastUsedFreeContinuousUpdateTimeUtc, 10)
    );

    this._battleContinuousContext = new BattleContinuousContext(loginInfo);
  }

  clone(): UserBattle {
    const c = new UserBattle();
    c.cloneSet(
      _.cloneDeep(this._battleInfo),
      _.cloneDeep(this._battleRewards),
      this._usedFreeTurnTakebackCount,
      this._usedFreePhaseTakebackCount,
      this._lastFreeTakebackUpdateTimeUtc,
      this._quickModeCount,
      this._lastQuickModeCountUpdateTimeUtc,
      _.cloneDeep(this._battleContinuous)
    );
    return c;
  }

  cloneSet(
    battleInfo: BattleInfo,
    battleRewards: { [type: number]: { [id: number]: BattleReward } },
    usedFreeTurnTakebackCount: number,
    usedFreePhaseTakebackCount: number,
    lastFreeTakebackUpdateTimeUtc: number,
    quickModeCount: number,
    lastQuickModeCountUpdateTimeUtc: number,
    battleContinuous: BattleContinuous
  ): void {
    this._battleInfo = battleInfo;
    this._battleRewards = battleRewards;
    this._usedFreeTurnTakebackCount = usedFreeTurnTakebackCount;
    this._usedFreePhaseTakebackCount = usedFreePhaseTakebackCount;
    this._lastFreeTakebackUpdateTimeUtc = lastFreeTakebackUpdateTimeUtc;
    this._quickModeCount = quickModeCount;
    this._lastQuickModeCountUpdateTimeUtc = lastQuickModeCountUpdateTimeUtc;
    this._battleContinuous = battleContinuous;
  }

  getBattleInfo(): BattleInfo {
    return this._battleInfo;
  }

  setBattleInfo(battleId: string, battleParam: BattleParam, multiId: string, logCount: number) {
    this._battleInfo.battleId = battleId;
    this._battleInfo.battleParam = battleParam;
    this._battleInfo.multiId = multiId;
    this._battleInfo.logCount = logCount;
  }

  getBattleRewards(): { [type: number]: { [id: number]: BattleReward } } {
    return this._battleRewards;
  }

  setBattleRewards(x: { [type: number]: { [id: number]: BattleReward } }) {
    this._battleRewards = x;
  }

  emptyBattleRewards(): void {
    this._battleRewards = {};
  }

  setMultiSubRedis(multiSubRedis: SimpleSubRedis) {
    this._battleInfo.multiSubRedis = multiSubRedis;
  }

  setMultiCtrlSubRedis(multiCtrlSubRedis: SimpleSubRedis) {
    this._battleInfo.multiCtrlSubRedis = multiCtrlSubRedis;
  }

  tryQuitMultiBattle(userId: number, bLogout?: boolean): Promise<any> {
    if (!this._battleInfo.multiId) {
      return;
    }

    const multiSubRedis = this._battleInfo.multiSubRedis;
    if (multiSubRedis) {
      multiSubRedis.quit();
    }

    this._battleInfo.multiSubRedis = undefined;

    const multiCtrlSubRedis = this._battleInfo.multiCtrlSubRedis;
    if (multiCtrlSubRedis) {
      multiCtrlSubRedis.quit();
    }

    this._battleInfo.multiCtrlSubRedis = undefined;

    if (bLogout) {
      const battleRedis = BattleRedisHelper.getBattleRedisByBattleType(
        this._battleInfo.battleParam.battleType
      );
      return battleRedis['logoutMulti'](
        userId,
        this._battleInfo.battleId,
        this._battleInfo.multiId,
        mutil.curTimeUtc()
      );
    }
  }

  get usedFreeTurnTakebackCount(): number {
    return this._usedFreeTurnTakebackCount;
  }

  get usedFreePhaseTakebackCount(): number {
    return this._usedFreePhaseTakebackCount;
  }

  get lastFreeTakebackUpdateTimeUtc(): number {
    return this._lastFreeTakebackUpdateTimeUtc;
  }

  applyFreeTakebackChange(chg: BattleFreeTakebackChange): void {
    this._usedFreeTurnTakebackCount = chg.usedFreeTurnTakebackCount;
    this._usedFreePhaseTakebackCount = chg.usedFreePhaseTakebackCount;
    this._lastFreeTakebackUpdateTimeUtc = chg.updateTimeUtc;
  }

  resetQuickModeCount(): void {
    this._quickModeCount = 0;
  }

  get quickModeCount(): number {
    return this._quickModeCount;
  }

  setQuickModeCount(quickModeCount: number) {
    this._quickModeCount = quickModeCount;
  }

  get lastQuickModeCountUpdateTimeUtc(): number {
    return this._lastQuickModeCountUpdateTimeUtc;
  }

  applyQuickModeChange(chg: BattleQuickModeChange): void {
    this._quickModeCount = chg.quickModeCount;
    this._lastQuickModeCountUpdateTimeUtc = chg.updateTimeUtc;
  }

  getSyncData(): All {
    const battleRewards = [];
    for (const type of Object.keys(this._battleRewards)) {
      for (const id of Object.keys(this._battleRewards[type])) {
        battleRewards.push(this._battleRewards[type][id]);
      }
    }

    const ret: All = {
      battleRewards,
      battleEndResult: this._battleEndResult,
      battleContinuousContext: this._battleContinuousContext.getSyncData(),

      user: {
        usedFreeContinuousCount: this._battleContinuous.usedFreeContinuousCount,
        lastUsedFreeContinuousUpdateTimeUtc:
          this._battleContinuous.lastUsedFreeContinuousUpdateTimeUtc,
      },
    };

    return ret;
  }

  // ----------------------------------------------------------------------------
  // 무료 연속 전투 관련 기능 처리
  // ----------------------------------------------------------------------------
  buildBattleContinuous(user: User, curTimeUtc: number): [BattleContinuousChange, EnergyChange] {
    return this._battleContinuous.buildConsumeChange(user, curTimeUtc);
  }
  applyBattleContinuous(change: BattleContinuousChange, glogParam: GLogParam) {
    return this._battleContinuous.applyBattleContinuous(change, glogParam);
  }
  get usedFreeContinuousCount(): number {
    return this._battleContinuous.usedFreeContinuousCount;
  }
  get lastUsedFreeContinuousUpdateTimeUtc(): number {
    return this._battleContinuous.lastUsedFreeContinuousUpdateTimeUtc;
  }

  // ----------------------------------------------------------------------------
  // 전투 시작. 레디스에 BattleParam를 기록.
  // ----------------------------------------------------------------------------
  startBattle(
    user: User,
    startTimeUtc: number,
    battleParam: BattleParam,
    mtParam?: MultiplayerParam,
    exchangeHash?: string,
    guildDataForLog?: any,
    pr_data?: PrData[]
  ): Promise<BattleParam> {
    return Promise.resolve()
      .then(() => {
        const { battleLogRedis } = Container.get(LobbyService);

        const mtParamStr = mtParam ? JSON.stringify(mtParam) : undefined;
        let questCmsId = battleParam.questCmsId;
        if (!questCmsId) {
          questCmsId = 0;
        }

        // mlog.debug('[DBG] logStartMulti', {
        //   userId: user.userId,
        //   mtParam,
        // });
        const battleRedis = BattleRedisHelper.getBattleRedisByBattleType(battleParam.battleType);
        if (!mtParamStr) {
          return battleRedis['logStart'](
            user.userId,
            startTimeUtc,
            JSON.stringify({ battleParam }),
            questCmsId,
            battleParam.battleType
          );
        } else {
          return battleRedis['logMultiStart'](
            user.userId,
            startTimeUtc,
            JSON.stringify({ battleParam }),
            questCmsId,
            mtParamStr,
            MultiBattleUtil.getRealTimeAddTimeSec(battleParam.battleType),
            battleParam.battleType
          );
        }
      })
      .then((retStr: string) => {
        const ret = JSON.parse(retStr);
        const battleId: string = ret.battleId;
        let battleParamForGLog: BattleParam = battleParam;

        // 실시간 PVP
        if (mtParam) {
          if (ret.txnLogStr) {
            const txnLog = JSON.parse(ret.txnLogStr);
            battleParam = txnLog.battleParam;
          }
          battleParamForGLog = MultiBattleUtil.rebuildBattleParamMultiPvp(user.userId, battleParam);
        }

        UserBattle.glogBattleStart(
          user,
          battleId,
          battleParamForGLog,
          exchangeHash,
          guildDataForLog,
          pr_data
        );

        // 항해일지 기록(PVE, PVP 만 기록 : 필요시 확장)
        if (battleParam.battleType === BattleType.Encount) {
          UserBattle._addSailingDiaryBattleStart(user, battleParam, mtParam, battleId);
        }

        const multiId = mtParam ? mtParam.multiId : undefined;
        this.setBattleInfo(battleId, battleParam, multiId, 1);

        return battleParam;
      });
  }

  // ----------------------------------------------------------------------------
  // 현재 시간 기준 전투를 시작할때, 저렙 무료 무르기가 가능한지.
  // ----------------------------------------------------------------------------
  static _buildFreeTakebackData(user: User, curTimeUtc: number): BattleParamFreeTakebackData {
    const fleetStat = user.userFleets.getFirstFleet().getStat(user.companyStat);

    // 캐시 상품으로 구매한 무르기 무제한인지의 여부.
    const unlimitedByCash: any = {};
    if (fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.BATTLE_TURN_TAKE_BACK_COUNT_UNLIMITED) !== 0) {
      unlimitedByCash.bTurn = true;
    }
    if (fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.BATTLE_PHASE_TAKE_BACK_COUNT_UNLIMITED) !== 0) {
      unlimitedByCash.bPhase = true;
    }

    // 사용가능하는 무료 무르기 횟수
    const freeTakebackCountInfo: any = {};
    const bReset = formula.HasContentsResetTimePassed(
      curTimeUtc,
      user.userBattle.lastFreeTakebackUpdateTimeUtc,
      cms.ContentsResetHour.BattleTakebackCostReset.hour
    );

    let accUsedFreeTurnTakebackCount: number = undefined;
    let accUsedFreePhaseTakebackCount: number = undefined;
    // 전투 시작 시간에 리셋이 된 경우에는
    // 파라미터 값만 초기화된 값을 사용하고, battleEnd시에 업데이트한다.
    if (bReset) {
      accUsedFreeTurnTakebackCount = 0;
      accUsedFreePhaseTakebackCount = 0;
    } else {
      accUsedFreeTurnTakebackCount = user.userBattle.usedFreeTurnTakebackCount;
      accUsedFreePhaseTakebackCount = user.userBattle.usedFreePhaseTakebackCount;
    }

    // 무료 무르기 최대 횟수
    const maxFreeTurnTakebackCount: number =
      CMSConst.get('BattleDailyFreeTurnTakebackCount') +
      fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.BATTLE_ADDED_MAX_TURN_TAKE_BACK_COUNT);

    const maxFreePhaseTakebackCount: number =
      CMSConst.get('BattleDailyFreePhaseTakebackCount') +
      fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.BATTLE_ADDED_MAX_PHASE_TAKE_BACK_COUNT);

    // 최대 - 지금까지 사용한 무르기 횟수를 빼서, 사용할 수 있는 무료무르기 횟수를 알아온다.
    // 무르기 횟수 증가 버프 소멸 시 음수가 될 수 있다
    const freeTurnTakebackCount = maxFreeTurnTakebackCount - accUsedFreeTurnTakebackCount;
    const freePhaseTakebackCount = maxFreePhaseTakebackCount - accUsedFreePhaseTakebackCount;

    freeTakebackCountInfo.turn = freeTurnTakebackCount;
    freeTakebackCountInfo.phase = freePhaseTakebackCount;

    return { unlimitedByCash, freeTakebackCountInfo };
  }

  setBattleEndResult(x): void {
    this._battleEndResult = x;
  }

  emptyBattleEndResult(): void {
    this._battleEndResult = undefined;
  }

  getBattleContinuousContext() {
    return this._battleContinuousContext;
  }

  // ----------------------------------------------------------------------------
  // 쾌속모드 카운트 빌드.
  // ----------------------------------------------------------------------------
  static _buildQuickModeData(user: User, curTimeUtc: number): BattleParamQuickModeData {
    const fleetStat = user.userFleets.getFirstFleet().getStat(user.companyStat);
    // 캐시 상품으로 구매한 쾌속모드 무제한인지의 여부.
    const bUnLimitedCountByCash =
      fleetStat.getWpe(cmsEx.PASSIVE_EFFECT.BATTLE_QUICK_MODE_COUNT_UNLIMITED) !== 0;

    // 무제한 쾌속 또는 미카엘 2,3급 버프 소지 시 자동 쾌속모드 가능 여부.
    let bAutoQuickPossible: boolean = false;
    if (
      bUnLimitedCountByCash ||
      user.userBuffs.hasBuffByCmsId(cmsEx.FirstFleetIndex, cmsEx.MichaelRank2CmsId) ||
      user.userBuffs.hasBuffByCmsId(cmsEx.FirstFleetIndex, cmsEx.MichaelRank3CmsId)
    ) {
      bAutoQuickPossible = true;
    }

    // 캐시 상품으로 구매한 쾌속모드 추가 최대 횟수 구매 여부.
    let addedMaxCountByCash: number = fleetStat.getWpe(
      cmsEx.PASSIVE_EFFECT.BATTLE_ADDED_MAX_QUICK_MODE_COUNT
    );
    if (addedMaxCountByCash === 0) {
      addedMaxCountByCash = undefined;
    }

    // 최근 전투 종료 시간과 현재 전투 시작시 사이에 리셋 시간이 지났는지 체크.
    const bReset = formula.HasContentsResetTimePassed(
      curTimeUtc,
      user.userBattle.lastQuickModeCountUpdateTimeUtc,
      cms.ContentsResetHour.BattleQuickModeReset.hour
    );

    let count = user.userBattle.quickModeCount;
    // 리셋 시간이 지나면 무조건 가능.
    if (bReset) {
      count = 0;
    }

    return { count, addedMaxCountByCash, bUnLimitedCountByCash, bAutoQuickPossible };
  }

  // ----------------------------------------------------------------------------
  // 전투 미션 (추가 목표)
  // ----------------------------------------------------------------------------
  static _buildBattleParamMission(
    oceanNpcId: number,
    bpChallenge: BattleParamChallenge
  ): BattleParamMission {
    // 미션 그룹을 먼저 찾는다.
    const bpMission: BattleParamMission = {
      groupId: 0,
      btlIds: [],
    };

    if (bpChallenge) {
      // 도전 전투
      const battleChallengeCms = cms.BattleChallenge[bpChallenge.cmsId];
      const bpChallengeStage = battleChallengeCms.stage[bpChallenge.difficulty - 1];
      if (bpChallengeStage.BattleMissionGroupId) {
        bpMission.groupId = bpChallengeStage.BattleMissionGroupId;
      }
    } else {
      // 일반 전투.
      if (!oceanNpcId) {
        mlog.warn('Cannot build battle terms without oceanNpcId');
        return;
      }

      const oceanNpcCms = getOceanNpcCms()[oceanNpcId];
      if (oceanNpcCms.battleMissionGroupId) {
        bpMission.groupId = oceanNpcCms.battleMissionGroupId;
      }
    }

    const battleMissionGroupCms = cms.BattleMissionGroup[bpMission.groupId];
    if (!battleMissionGroupCms) {
      return bpMission;
    }

    // 그룹의 battleTerms 를 돌면서, 목표들을 산출한다.
    for (const btElem of battleMissionGroupCms.battleTerms) {
      // 고정 케이스.
      if (btElem.RandType === BATTLE_MISSION_RAND_TYPE.FIXED) {
        bpMission.btlIds.push(btElem.Id);
        continue;
      }

      // 무한 루프 방지를 위한 안전 장치.
      let numLoops = 0;
      const _MaxLoops = 20;

      // 그룹에서 랜덤하게 뽑기. (중복 없이)
      const btlDescs = _GetBattleTermsListsByGroup(btElem.Id);
      let picked: BattleTermsListDesc;

      while (!picked) {
        ++numLoops;

        // 랜덤으로 하나 뽑아서.
        const randIdx = mutil.randIntInc(0, btlDescs.length - 1);
        picked = btlDescs[randIdx];

        // 중복 체크.
        for (const btlId of bpMission.btlIds) {
          if (btlId === picked.id) {
            picked = undefined; // 중복되서 다시 찾아야함.
            break;
          }
        }

        if (numLoops > _MaxLoops) {
          mlog.error('Max tries while finding random mission!', {
            battleMissionGroupCmsId: battleMissionGroupCms.id,
          });
          break;
        }
      }

      if (picked) {
        bpMission.btlIds.push(picked.id);
      }
    }

    return bpMission;
  }

  // ----------------------------------------------------------------------------
  // 권역을 점유한 국가의 국력 추출
  // ----------------------------------------------------------------------------
  static _buildNationPower(user: User): number {
    const sailState = user.userSailing.getSailState();
    const region = sailState.region;
    if (!region) {
      throw new MError('battle-param-invalid-region', MErrorCode.INVALID_ENCOUNT_REGION, {
        userId: user.userId,
      });
    }

    const regionCmsId = sailState.region.id;
    const townManager = Container.get(TownManager);
    // 권역을 점유한 국가 cms id
    const nationCmsId = townManager.getRegionOccupiedNationCmsId(regionCmsId);

    // 점유한 국가 없음
    if (nationCmsId === 0) {
      return undefined;
    }

    const { nationManager } = Container.get(LobbyService);
    // 국력
    const nationPower = nationManager.get(nationCmsId).power;
    return nationPower;
  }

  // ----------------------------------------------------------------------------
  // 기후 정보 추출
  // ----------------------------------------------------------------------------
  static _buildClimateCmsId(user: User, climateCmsId?: number) {
    if (climateCmsId) {
      return climateCmsId;
    }
    const sailState = user.userSailing.getSailState();
    const region = sailState.region;
    if (!region) {
      throw new MError('battle-param-invalid-region', MErrorCode.INVALID_ENCOUNT_REGION, {
        userId: user.userId,
      });
    }
    const oceanTileDesc = LatLonToOceanTileDesc(
      sailState.location.latitude,
      sailState.location.longitude
    );
    const weatherTileCms = oceanTileDesc.weatherTileCms;
    const weathertileclimateCms = cms.WeatherTileClimate[weatherTileCms.weathertileclimateId];

    // climateCmsId
    const climateIdx =
      formula.GetGameMonth(CMSConst.get('SeasonTimeScale'), mutil.curTimeUtc()) - 1;
    return weathertileclimateCms.climateId[climateIdx];
  }

  // ----------------------------------------------------------------------------
  // 전투맵 추출
  // ----------------------------------------------------------------------------
  static _buildBattleMapCmsId(user: User, forceBattleMapCmsId?: number, bMultiBattle?: boolean) {
    if (forceBattleMapCmsId) {
      return forceBattleMapCmsId;
    }

    const sailState = user.userSailing.getSailState();
    const oceanTileDesc = LatLonToOceanTileDesc(
      sailState.location.latitude,
      sailState.location.longitude
    );

    const battleMapGroupCms = oceanTileDesc.battleMapGroupCms;
    let battleMapDescs: BattleMapDesc[] = battleMapGroupCms.battleMap;
    const needLv = battleMapGroupCms.needLv;
    if (needLv) {
      if (user.level <= needLv) {
        const battleMapLowLv: BattleMapDesc[] = battleMapGroupCms.battleMapLowLv;
        if (battleMapLowLv) {
          battleMapDescs = battleMapLowLv;
        }
      }
    }
    const rates: number[] = battleMapDescs.map((battleMapDesc) => battleMapDesc.rate);
    const choiceIdx = mutil.weightedSelect(rates);
    if (choiceIdx === undefined) {
      throw new MError('invalid-map-index', MErrorCode.INTERNAL_ERROR);
    }

    let mapCmsId = battleMapDescs[choiceIdx].id;

    if (bMultiBattle) {
      const pvpMapCmsId = cms.BattleMap[mapCmsId].realTimePvPMap;
      if (pvpMapCmsId) {
        return pvpMapCmsId;
      }
    }

    return mapCmsId;
  }

  // ----------------------------------------------------------------------------
  // 풍속과 풍향을 추출
  // ----------------------------------------------------------------------------
  static _buildWindSpeedAndDirection(user: User, windSpeed?: number, windDir?: number) {
    if (!isNotANumber(windSpeed) && isNotANumber(windDir)) {
      return { startWindSpeed: windSpeed, startWindDirection: windDir };
    }
    let startWindSpeed = 0;
    let startWindDirection = 0;
    const { weatherPropGroup, oceanClock, seasonClock } = Container.get(LobbyService);
    const curTimeUtc = mutil.curTimeUtc();
    const inYear = seasonClock.rateInYear(curTimeUtc);
    const inDay = oceanClock.rateInDay(curTimeUtc);
    const oceanPropGroup = weatherPropGroup(inYear)(inDay);
    const sailState = user.userSailing.getSailState();
    if (sailState) {
      const userLocation = sailState.location;
      const { WindDirection, AdjustedWindSpeed } = oceanPropGroup.get(
        userLocation.latitude,
        userLocation.longitude
      );

      startWindSpeed = isNotANumber(windSpeed) ? AdjustedWindSpeed : windSpeed;
      startWindDirection = isNotANumber(windDir)
        ? RoundAngleToAbsDirection(WindDirection)
        : windDir;
    }
    return { startWindSpeed, startWindDirection };
  }

  // ----------------------------------------------------------------------------
  // 퀘스트 데이터 추출
  // ----------------------------------------------------------------------------
  static _buildQuestData(user: User) {
    /*
    퀘스트를 진행하는 전투의 경우, 무르기 / 이어하기를 지원하기 위해,
    최소한의 퀘스트 정보를 관리해야 한다.일단은 플래그 정보만.
  */
    const questData: { [questCmsId: number]: { uflags: number; lflags: number } } = {};
    const questManager = user.questManager;
    for (const [questCmsId, questContext] of Object.entries(questManager.contexts)) {
      questData[questCmsId] = {
        uflags: questContext.uflags,
        lflags: questContext.lflags,
      };
    }
    return questData;
  }

  // ----------------------------------------------------------------------------
  // enemy선박의 BattleParam을 구성한다.
  // ----------------------------------------------------------------------------
  static _buildBattleParamEnemyShip(
    enemyFleetData: OceanFleetData,
    userId: number
  ): BattleParamShip[] {
    const npcShipBattleParams: BattleParamShip[] = [];
    enemyFleetData.ships.forEach((ship) => {
      if (ship.sailor <= 0) {
        mlog.warn('Sailor is empty. must be more than zero.', {
          userId,
          sailor: ship.sailor,
          ship,
        });
        ship.sailor = 1;
      }

      if (ship.durability <= 0) {
        mlog.warn('durability is empty. must be more than zero.', {
          userId,
          durability: ship.durability,
          ship,
        });
        ship.durability = 1;
      }

      const slotOverrides: { cmsId: number; slotIdx: number; enchantLv: number }[] = [];
      const mates: BattleParamMate[] = [];
      ship.shipSlots.forEach((shipSlot: OceanFleetShipSlotData) => {
        if (shipSlot.override) {
          slotOverrides.push({
            cmsId: shipSlot.shipSlotCmsId,
            slotIdx: shipSlot.slotIndex,
            enchantLv: shipSlot.enchantLv,
          });
        }
        if (shipSlot.mate) {
          mates.push({
            slotIdx: shipSlot.slotIndex,
            cmsId: shipSlot.mate.mateCmsId,
            loyalty: shipSlot.mate.loyalty,
            stateFlags: shipSlot.mate.stateFlags,
            awakenLevel: shipSlot.mate.awakenLevel,
            tradeLevel: shipSlot.mate.tradeLevel,
            battleLevel: shipSlot.mate.battleLevel,
            adventureLevel: shipSlot.mate.adventureLevel,
            equipments: shipSlot.mate.equips.map((equip) => {
              return {
                cmsId: equip.cmsId,
                dye1: equip.dye1,
                dye2: equip.dye2,
                dye3: equip.dye3,
                isCostume: equip.isCostume,
                enchantLv: equip.enchantLv,
              };
            }),
            isHideHat: shipSlot.mate.isHideHat,
            isHideCape: shipSlot.mate.isHideCape,
            isHideFace: shipSlot.mate.isHideFace,
            trainingGrade: shipSlot.mate.trainingGrade,
            trainingPoints: shipSlot.mate.trainingPoints,
            illustCmsId: shipSlot.mate.illustCmsId,
            clearAutoMateSkillQuest: shipSlot.mate.clearAutoMateSkillQuest,
            isTranscended: shipSlot.mate.isTranscended,
          });
        }
      });

      const battlePassives = WorldPassiveUtil.gatherNpcBattlePassives(enemyFleetData, ship);

      const shipBattleParam: BattleParamShip = {
        cmsId: ship.shipCmsId,
        name: ship.name,
        sailor: ship.sailor,
        durability: ship.durability,
        life: cmsEx.shipDefaultLife(ship.bpCmsId),
        blueprint: {
          cmsId: ship.bpCmsId,
          level: ship.bpLevel,
          sailMasteryLevel: ship.bpSailMasteryLevel,
        },
        supplies: [ship.water, ship.food, ship.resource, ship.ammo],
        slotOverrides,
        tileDmgMultipliers: [],
        mates,
        battlePassives,
        bpWorldBuffs: ship.bpWorldBuffs,
        enchantedStats: ship.enchantedStats,
        rndStats: ship.rndStats,
        oceanNpcShipTemplateCmsId: ship.oceanNpcShipTemplateCmsId,
      };

      npcShipBattleParams.push(shipBattleParam);
    });
    return npcShipBattleParams;
  }

  // ----------------------------------------------------------------------------
  // 모의전용 유저 선박의 BattleParam을 구성한다.
  // ----------------------------------------------------------------------------
  static _buildBattleParamPlayerShipArena(
    fleetData: OceanFleetData,
    userId: number
  ): BattleParamShip[] {
    const shipBattleParams: BattleParamShip[] = [];
    fleetData.ships.forEach((ship) => {
      if (ship.sailor <= 0) {
        mlog.warn('Sailor is empty. must be more than zero.', {
          userId,
          sailor: ship.sailor,
          ship,
        });
        ship.sailor = 1;
      }

      if (ship.durability <= 0) {
        mlog.warn('durability is empty. must be more than zero.', {
          userId,
          durability: ship.durability,
          ship,
        });
        ship.durability = 1;
      }

      const slotOverrides: { cmsId: number; slotIdx: number; enchantLv: number }[] = [];
      const mates: BattleParamMate[] = [];
      ship.shipSlots.forEach((shipSlot: OceanFleetShipSlotData) => {
        if (shipSlot.override) {
          slotOverrides.push({
            cmsId: shipSlot.shipSlotCmsId,
            slotIdx: shipSlot.slotIndex,
            enchantLv: shipSlot.enchantLv,
          });
        }
        if (shipSlot.mate) {
          mates.push({
            slotIdx: shipSlot.slotIndex,
            cmsId: shipSlot.mate.mateCmsId,
            loyalty: shipSlot.mate.loyalty,
            stateFlags: shipSlot.mate.stateFlags,
            awakenLevel: shipSlot.mate.awakenLevel,
            tradeLevel: shipSlot.mate.tradeLevel,
            battleLevel: shipSlot.mate.battleLevel,
            adventureLevel: shipSlot.mate.adventureLevel,
            equipments: shipSlot.mate.equips.map((equip) => {
              return {
                cmsId: equip.cmsId,
                dye1: equip.dye1,
                dye2: equip.dye2,
                dye3: equip.dye3,
                isCostume: equip.isCostume,
                enchantLv: equip.enchantLv,
              };
            }),
            isHideHat: shipSlot.mate.isHideHat,
            isHideCape: shipSlot.mate.isHideCape,
            isHideFace: shipSlot.mate.isHideFace,
            trainingGrade: shipSlot.mate.trainingGrade,
            trainingPoints: shipSlot.mate.trainingPoints,
            illustCmsId: shipSlot.mate.illustCmsId,
            clearAutoMateSkillQuest: shipSlot.mate.clearAutoMateSkillQuest,
            isTranscended: shipSlot.mate.isTranscended,
          });
        }
      });

      const battlePassives = WorldPassiveUtil.gatherNpcBattlePassives(fleetData, ship);

      const shipBattleParam: BattleParamShip = {
        cmsId: ship.shipCmsId,
        sailor: ship.sailor,
        durability: ship.durability,
        life: cmsEx.shipDefaultLife(ship.bpCmsId), // 모의전에서는 수명최대값을 보내준다.
        blueprint: {
          cmsId: ship.bpCmsId,
          level: ship.bpLevel,
          sailMasteryLevel: ship.bpSailMasteryLevel,
        },
        supplies: [ship.water, ship.food, ship.resource, ship.ammo],
        slotOverrides,
        cargoRemainCapacity: 0,
        mates,
        battlePassives,
        bpWorldBuffs: ship.bpWorldBuffs,
        enchantedStats: ship.enchantedStats,
        rndStats: ship.rndStats,
        oceanNpcShipTemplateCmsId: ship.oceanNpcShipTemplateCmsId,
      };

      shipBattleParams.push(shipBattleParam);
    });
    return shipBattleParams;
  }

  // ----------------------------------------------------------------------------
  //  추가 함대를 구성한다.
  // ----------------------------------------------------------------------------
  static _buildBattleParamAddFleets(
    userId: number,
    addFleetDescs: OceanNpcStageAddFleetDesc[],
    battleType: BattleType
  ): BattleParamFleet[] {
    const addFleets: BattleParamFleet[] = [];

    if (!addFleetDescs) {
      return addFleets;
    }

    for (const addFleetDesc of addFleetDescs) {
      const factionId = addFleetDesc.FactionId;
      const oceanNpcCmsId = addFleetDesc.OceanNpcId;
      const anchorTemplateCmsId = addFleetDesc.AnchorTemplateId;

      let npcFleetData = npcFleetBuilder.buildAreaOceanFleetData(oceanNpcCmsId);
      const npcFleetStat = oceanNpcStatCache.buildFleetStatParam(npcFleetData);
      npcFleetData = oceanNpcStatCache.mergeOceanFleetDataStat({
        npcFleetData,
        fleetStat: npcFleetStat,
      });

      const anchorTemplateCms = cms.AnchorTemplate[anchorTemplateCmsId];
      const bpNpcFleet = UserBattle._buildBattleParamEnemyFleet(
        battleType,
        factionId,
        anchorTemplateCms.anchorType,
        anchorTemplateCms.absDirectionType,
        anchorTemplateCms.absDistanceType,
        anchorTemplateCms.anchorAc
          ? {
              r: anchorTemplateCms.anchorAc[0],
              q: anchorTemplateCms.anchorAc[1],
            }
          : undefined,
        npcFleetData,
        userId
      );

      addFleets.push(bpNpcFleet);
    }

    return addFleets;
  }

  // ----------------------------------------------------------------------------
  //  적함대의 BattleParam을 구성한다.
  // ----------------------------------------------------------------------------
  static _buildBattleParamEnemyFleet(
    battleType: BattleType,
    faction: number,
    anchor: number,
    absDirection: number,
    absDistance: number,
    anchorAc: AxialCoord,
    enemyFleetData: OceanFleetData,
    userId: number,
    extra: ArgBattleParamEnemyFleetExtra = {}
  ): BattleParamFleet {
    const { fleetIdx, multiPvpReward, representedMateCmsId, representedMateIllustCmsId } = extra;
    const npc: BattleParamFleet = {
      fleetNameDesc: enemyFleetData.fleetNameDesc,
      faction,
      oceanNpcId: enemyFleetData.npcCmsId,
      battleFormationCmsId: enemyFleetData.battleFormationCmsId,
      bIsApplyBattleFormationBuff: enemyFleetData.bIsApplyFormationBuff,
      combatPower: enemyFleetData.combatPower,
      level: enemyFleetData.level,
      nationCmsId: enemyFleetData.nationCmsId,
      items: {},
      anchor,
      anchorAc,
      direction: absDirection,
      distance: absDistance,
      shipCustomizing: enemyFleetData.shipCustomizing,
      shipSailPattern: enemyFleetData.shipSailPattern,
      ships: this._buildBattleParamEnemyShip(enemyFleetData, userId),
      bpWorldBuffs: enemyFleetData.bpWorldBuffs,
      collections: enemyFleetData.collections,
      nationPolicies:
        multiPvpReward && FleetHelper.validNationPolicesByBattleType(battleType)
          ? UserBattle._buildNationPolicies(enemyFleetData.nationCmsId)
          : undefined,
      researchNodes: enemyFleetData.researchNodes,
      fleetIdx,
      multiPvpReward,
      costumeShipSlots: enemyFleetData.costumeShipSlots,
      representedMateCmsId,
      representedMateIllustCmsId,
    };

    mlog.verbose('[Research] buildEnemyFleet', {
      researchNodes: npc.researchNodes,
    });

    return npc;
  }

  // ----------------------------------------------------------------------------
  //  모의전용 유저 함대의 BattleParam을 구성한다.
  //  모의전에는 정책 효과 적용안함(기획 요청)
  // ----------------------------------------------------------------------------
  static _buildBattleParamPlayerFleetArena(
    user: User,
    anchor: number,
    absDirection: number,
    absDistance: number,
    anchorAc: AxialCoord,
    fleetData: OceanFleetData
  ): BattleParamFleet {
    let representedMateCmsId;
    let representedMateIllustCmsId;
    const representedMate = user.userMates.getRepresentedMate();
    if (representedMate) {
      representedMateCmsId = representedMate.getNub().cmsId;
      representedMateIllustCmsId = representedMate.getEquippedIllustCmsId();
    }

    const bpPf: BattleParamFleet = {
      oceanNpcId: cmsEx.OCEAN_NPC_CMS_ID.USER_DATA_NPC_CMS_ID,
      fleetNameDesc: {
        nameType: OceanFleetNameType.USER,
        params: {
          userName: user.userName,
        },
      },
      faction: BC.Faction.Ally,
      battleFormationCmsId: fleetData.battleFormationCmsId,
      bIsApplyBattleFormationBuff: fleetData.bIsApplyFormationBuff,
      combatPower: fleetData.combatPower,
      level: fleetData.level,
      nationCmsId: fleetData.nationCmsId,
      items: {},
      anchor,
      anchorAc,
      direction: absDirection,
      distance: absDistance,
      shipCustomizing: fleetData.shipCustomizing,
      shipSailPattern: fleetData.shipSailPattern,
      ships: this._buildBattleParamPlayerShipArena(fleetData, user.userId),
      bpWorldBuffs: fleetData.bpWorldBuffs,
      collections: fleetData.collections,
      researchNodes: fleetData.researchNodes,
      costumeShipSlots: fleetData.costumeShipSlots,
      representedMateCmsId,
      representedMateIllustCmsId,
    };
    return bpPf;
  }

  // ----------------------------------------------------------------------------
  // CLASH 용. BattleParamPlayerFleet
  // ----------------------------------------------------------------------------
  static buildClashBattleParam(
    user: User,
    oceanNpcCmsId: number,
    myScore: number,
    myClashTierCmsId: number,
    myWinStreak: number,
    sessionId: number,
    opponent: { ofd: ClashOceanFleetDataEx; score: number; userId: number },
    bAttack: boolean,
    curTimeUtc: number
  ): BattleParam {
    const oceanNpcStageCms: OceanNpcStageDesc = cms.OceanNpcStage[oceanNpcCmsId];
    const allyAnchorTemplateCmsId = oceanNpcStageCms.allyAnchorTemplateId;
    const allyAnchorTemplateCms = cms.AnchorTemplate[allyAnchorTemplateCmsId];
    const allyAnchorAc = allyAnchorTemplateCms.anchorAc
      ? {
          r: allyAnchorTemplateCms.anchorAc[0],
          q: allyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const mpr: MultiPvpReward = {
      pvpPoint: 0,
      cargo: {},
    };

    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      allyAnchorTemplateCms.anchorType,
      allyAnchorTemplateCms.absDirectionType,
      allyAnchorTemplateCms.absDistanceType,
      allyAnchorAc,
      BattleType.Clash,
      curTimeUtc,
      undefined,
      BC.FirstFleetIdx,
      mpr
    );

    const enemyAnchorTemplateId = oceanNpcStageCms.enemyAnchorTemplateId;
    const enemyAnchorTemplateCms = cms.AnchorTemplate[enemyAnchorTemplateId];
    const enemyAnchorAc = enemyAnchorTemplateCms.anchorAc
      ? {
          r: enemyAnchorTemplateCms.anchorAc[0],
          q: enemyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      BattleType.Clash,
      BC.Faction.Enemy,
      enemyAnchorTemplateCms.anchorType,
      enemyAnchorTemplateCms.absDirectionType,
      enemyAnchorTemplateCms.absDistanceType,
      enemyAnchorAc,
      opponent.ofd.oceanFleetData,
      user.userId,
      {
        fleetIdx: BC.FirstFleetIdx + 1,
        representedMateCmsId: opponent.ofd.representedMateCmsId,
        representedMateIllustCmsId: opponent.ofd.representedMateIllustCmsId,
      }
    );
    // const ally = UserBattle.buildBattleParamPlayerFleetClash(user,);
    // 도전 전투에서는 난입 필요시에 퀘스트 스크립트를 사용한다.
    const reinforcements = [];
    // 추가 함대 구성.
    // const addFleets = UserBattle._buildBattleParamAddFleets(
    //   user.userId,
    //   oceanNpcStageCms.addStartFleet
    // );
    const startTime = formula.GetOceanTimeUtc(CMSConst.get('OceanTimeScale'), curTimeUtc);
    const startWeather = _rollWeather(oceanNpcStageCms.climateId);
    const bTest = false;
    const targetType: EncountTargetType = EncountTargetType.OnlineUser;
    const targetUserId: number = opponent.userId;
    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);
    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);
    const clash: BattleParamClash = {
      originData: [],
      sessionId,
    };

    clash.originData.push({
      userId: user.userId,
      score: myScore,
      clashTierCmsId: myClashTierCmsId,
      winStreak: myWinStreak,
    });

    clash.originData.push({
      userId: opponent.userId,
      score: opponent.score,
      clashTierCmsId: opponent.ofd.clashTierCmsId,
      winStreak: opponent.ofd.winStreak,
    });

    return {
      userId: user.userId,
      battleType: BattleType.Clash,
      revision: user.revision,
      patchRevision: user.patchRevision,
      versionText: user.versionText,
      mapCmsId: oceanNpcStageCms.battleMapId,
      climateCmsId: oceanNpcStageCms.climateId,
      encounterResult: EncountResult.START_BATTLE,
      randomSeed: curTimeUtc,
      bAttack,
      startWindSpeed: oceanNpcStageCms.windSpeed,
      startWindDirection: oceanNpcStageCms.windDir,
      questData: UserBattle._buildQuestData(user),
      ally,
      enemy,
      //addFleets,
      reinforcements,
      startTime,
      startWeather,
      bTest,
      targetType,
      targetUserId,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      nationPower: undefined,
      bmoLayerIndexArray: undefined,
      quickModeData,
      //questCmsId,
      mission: {
        groupId: 0,
        btlIds: [],
      },
      bMultiBattle: true,
      stageBuffs: oceanNpcStageCms.stageBuff,
      clash,
    };
  }

  // ----------------------------------------------------------------------------
  // 유저 함대의 BattleParam을 구성한다.
  // ----------------------------------------------------------------------------
  static _buildBattleParamPlayerFleet(
    user: User,
    anchorType: number,
    absDirection: number,
    absDistance: number,
    anchorAc: AxialCoord,
    battleType: BattleType,
    curTimeUtc: number,
    allyFormationId?: number,
    fleetIdx?: number,
    multiPvpReward?: MultiPvpReward
  ): BattleParamFleet {
    let representedMateCmsId;
    let representedMateIllustCmsId;
    const representedMate = user.userMates.getRepresentedMate();
    if (representedMate) {
      representedMateCmsId = representedMate.getNub().cmsId;
      representedMateIllustCmsId = representedMate.getEquippedIllustCmsId();
    }

    let pfbp: BattleParamFleet = {
      level: user.level,
      oceanNpcId: cmsEx.OCEAN_NPC_CMS_ID.USER_DATA_NPC_CMS_ID,
      fleetNameDesc: {
        nameType: OceanFleetNameType.USER,
        params: {
          userName: user.userName,
        },
      },
      faction: BC.Faction.Ally,
      combatPower: user.userFleets.getFirstFleetCombatPower(user.companyStat),
      nationCmsId: user.nationCmsId,
      battleFormationCmsId: allyFormationId
        ? allyFormationId
        : user.userFleets.getFirstFleet().getBattleFormationCmsId(),
      bIsApplyBattleFormationBuff: allyFormationId
        ? true
        : !user.userBattleFormations.isExpiredBattleFormation(
            user.userFleets.getFirstFleet().getBattleFormationCmsId(),
            curTimeUtc
          ),
      anchor: anchorType,
      anchorAc,
      direction: absDirection,
      distance: absDistance,
      items: {},
      shipCustomizing: {},
      shipSailPattern: {},
      ships: [],
      bpWorldBuffs: user.userBuffs.getFleetBattleParamWorldBuffs(cmsEx.FirstFleetIndex, battleType),
      collections: user.userCollection.getCompletedCollections(),
      researchNodes: user.userResearch.getResearchNodes(),
      fleetIdx,
      multiPvpReward,
      nationPolicies: FleetHelper.validNationPolicesByBattleType(battleType)
        ? UserBattle._buildNationPolicies(user.nationCmsId)
        : {},
      costumeShipSlots: user.userFleets.getCostumeShipSlotItemCmsIdAndEnchantLvs(user.userInven),
      representedMateCmsId,
      representedMateIllustCmsId,
    };

    user.userInven.gatherBattleItems(pfbp.items);

    const { shipCustomizing, shipSailPattern } = user.userFleets.getCustomizingAndSailPattern();

    pfbp.shipCustomizing = {
      sailCrestCmsId: shipCustomizing.sailCrestCmsId,
      bodyColor1: shipCustomizing.bodyColor1,
      bodyColor2: shipCustomizing.bodyColor2,
      bodyColorTint: shipCustomizing.bodyColorTint,
    };

    pfbp.shipSailPattern = {
      sailPatternCmsId: shipSailPattern.sailPatternCmsId,
      color1: shipSailPattern.color1,
      color2: shipSailPattern.color2,
      color3: shipSailPattern.color3,
    };

    const firstFleet = user.userFleets.getFirstFleet();
    const userMates = user.userMates;
    const firstFleetShips = firstFleet.getShipsOrderByFormationIndex();
    const userShipBlueprints = user.userShipBlueprints;

    for (const ship of firstFleetShips) {
      const shipBlueprintCmsId = cms.Ship[ship.nub.cmsId].shipBlueprintId;
      const shipBlueprint = userShipBlueprints.getUserShipBlueprint(shipBlueprintCmsId);

      const supplies: { water: number; food: number; ammo: number; lumber: number } =
        FleetHelper.getShipSuppliesQuantity(user.companyStat, battleType, ship);

      const shipBattleParam: BattleParamShip = {
        cmsId: ship.nub.cmsId,
        name: ship.nub.name,
        sailor: FleetHelper.getShipSailor(user.companyStat, battleType, ship),
        durability: FleetHelper.getShipDurability(user.companyStat, battleType, ship),
        life: FleetHelper.getShipLife(battleType, ship),
        blueprint: {
          cmsId: shipBlueprintCmsId,
          level: shipBlueprint ? shipBlueprint.level : 1,
          sailMasteryLevel: shipBlueprint ? shipBlueprint.sailMasteryLevel : 0,
        },

        // 보급품 설정
        supplies: [supplies.water, supplies.food, supplies.lumber, supplies.ammo],

        // 잔여 적재량을 첨부한다.
        cargoRemainCapacity: ship.getRemainingCapacity(user.companyStat),

        slotOverrides: [],

        //
        mates: [],

        // 활성화된 전투 패시브들.
        battlePassives: user.userPassives.getShipBattlePassiveCmsIds(ship.getNub().id),

        // 활성화된 월드 버프들.
        bpWorldBuffs: user.userBuffs.getShipBattleParamWorldBuffs(ship.getNub().id, battleType),

        // enchant
        enchantedStats: ship.getNub().enchantedStats,

        rndStats: ship.getNub().rndStats,

        oceanNpcShipTemplateCmsId: undefined,
      };

      // 개조된 선실
      if (shipBlueprint) {
        shipBattleParam.slotOverrides = shipBattleParam.slotOverrides.concat(
          shipBlueprint.getSlotOverrides()
        );
      }

      // 부품 장착된 슬롯
      for (const [key, slot] of Object.entries(ship.getSlots())) {
        if (slot.shipSlotItemId) {
          const shipsSlotItem = user.userInven.getShipSlotItem(slot.shipSlotItemId);
          if (shipsSlotItem.shipSlotCmsId) {
            shipBattleParam.slotOverrides.push({
              slotIdx: shipsSlotItem.equippedShipSlotIdx,
              cmsId: shipsSlotItem.shipSlotCmsId,
              enchantLv: shipsSlotItem.enchantLv,
            });
          }
        }
      }

      for (const [_, shipSlotNub] of Object.entries(ship.nub.slots)) {
        if (shipSlotNub.mateCmsId) {
          const mate: Mate = userMates.getMate(shipSlotNub.mateCmsId);
          const mateNub: MateNub = mate.getNub();

          shipBattleParam.mates.push({
            slotIdx: shipSlotNub.slotIndex,
            cmsId: mateNub.cmsId,
            awakenLevel: mateNub.awakenLv,
            tradeLevel: mateNub.tradeLevel,
            battleLevel: mateNub.battleLevel,
            adventureLevel: mateNub.adventureLevel,
            loyalty: mateNub.loyalty,
            stateFlags: FleetHelper.getMateStateFlags(battleType, mateNub),
            equipments: mateNub.equipments
              .map((mateEquipmentNub: MateEquipmentNub) => {
                const equipment = userMates.getMateEquipment(mateEquipmentNub.id);
                if (!equipment) {
                  return null;
                }
                return {
                  cmsId: equipment.cmsId,
                  dye1: equipment.dye1,
                  dye2: equipment.dye2,
                  dye3: equipment.dye3,
                  isCostume: equipment.isCostume,
                  enchantLv: equipment.enchantLv,
                };
              })
              .filter((equip) => equip),
            isHideHat: mateNub.isHideHat,
            isHideCape: mateNub.isHideCape,
            isHideFace: mateNub.isHideFace,
            trainingGrade: mateNub.trainingGrade,
            trainingPoints: mateNub.trainingPoints,
            illustCmsId: mateNub.equippedIllustCmsId,
            clearAutoMateSkillQuest: MateUtil.clearAutoMateSkillQuest(user, shipSlotNub.mateCmsId),
            // clearAutoMateFixedQuest 의 경우 필요 없음.
            isTranscended: mateNub.isTranscended,
          });
        }
      }

      pfbp.ships.push(shipBattleParam);
    }

    return pfbp;
  }

  // ----------------------------------------------------------------------------
  // 멀티인카운트용 참전함대 BattleParam 구성.
  // ----------------------------------------------------------------------------
  static _buildBattleParamReinforcementFleet(
    battleType: BattleType,
    reinforcementFleets: ReinforcementFleet[],
    userId: number
  ): BattleParamReinforcementFleet[] {
    const reinforcements: BattleParamReinforcementFleet[] = [];
    reinforcementFleets.forEach((rbp: ReinforcementFleet) => {
      const npcBattleParam = this._buildBattleParamEnemyFleet(
        battleType,
        rbp.faction,
        rbp.anchor,
        rbp.direction,
        rbp.distance,
        undefined,
        rbp.fleetData,
        userId
      );

      reinforcements.push(
        Object.assign(npcBattleParam, {
          turnNum: rbp.turnNum + 1, // 턴 난입은 2턴부터..,
        })
      );
    });

    return reinforcements;
  }

  // ----------------------------------------------------------------------------
  // 클라이언트에게 넘겨 줄 BattleParam을 구성.
  // ----------------------------------------------------------------------------
  static buildBattleParam(
    user: User,
    curTimeUtc: number,
    required: RequiredBattleParam,
    optional: OptionalBattleParam
  ): BattleParam {
    // 유저의 git revsion + version text
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;

    // 전투맵 설정
    const mapCmsId = UserBattle._buildBattleMapCmsId(
      user,
      optional.forceBattleMapCmsId,
      optional.bMultiBattle
    );

    // 기후 설정
    const climateCmsId = UserBattle._buildClimateCmsId(user, optional.climateCmsId);

    // 전투 시작 시간
    const startTime: number = isNotANumber(optional.startTime)
      ? formula.GetOceanTimeUtc(CMSConst.get('OceanTimeScale'), curTimeUtc)
      : optional.startTime;

    // 날씨 설정
    const startWeather = user.getCurTileWeatherCmsId();

    // 풍속/풍향 설정
    const { startWindSpeed, startWindDirection } = this._buildWindSpeedAndDirection(
      user,
      optional.windSpeed,
      optional.windDir
    );

    // 퀘스트 데이터
    const questData = this._buildQuestData(user);

    // 현재 권역을 가지고 있는 국가의 국력 설정
    const nationPower = UserBattle._buildNationPower(user);

    // 멀티플레이 전투 시 fleetIdx 지정, 공격자 쪽이 1번 함대를 사용
    let allyFleetIdx: number;
    let enemyFleetIdx: number;

    // 멀티 (실시간) pvp 보상을 내용을 각 함대에 기록하기 위한 정보
    let allyMultiPvpReward: MultiPvpReward;
    let enemyMultiPvpReward: MultiPvpReward;

    if (optional.bMultiBattle) {
      if (required.bAttack) {
        allyFleetIdx = BC.FirstFleetIdx;
        enemyFleetIdx = BC.FirstFleetIdx + 1;
      } else {
        allyFleetIdx = BC.FirstFleetIdx + 1;
        enemyFleetIdx = BC.FirstFleetIdx;
      }

      // 멀티 (실시간) pvp 에서 아군 (ally) 함대 승리시 보상.
      allyMultiPvpReward = optional.multiPvpReward;

      // 멀티 (실시간) pvp 에서 적군 (enemy) 함대 승리시 보상.
      const userKarmaDesc = user.getKarmaDesc(curTimeUtc);
      const userCargo = user.userFleets.getFirstFleet().getSuppliesAndTradeGoods();
      const userDucat = user.userPoints.getPoint(cmsEx.DucatPointCmsId);
      enemyMultiPvpReward = calcMultiPvpPlunderReward(
        required.enemyFleetData.level,
        user.level,
        userKarmaDesc ? userKarmaDesc.id : 0,
        userDucat,
        userCargo.supplies,
        userCargo.tradeGoods
      );
    }

    const battleType = BattleType.Encount;

    // 플레이어 설정
    const anchorInfo = required.anchorInfo;
    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      anchorInfo.allyAnchor,
      anchorInfo.allyAbsDirection,
      anchorInfo.allyAbsDistance,
      anchorInfo.allyAnchorAc,
      battleType,
      curTimeUtc,
      optional.allyFormationId,
      allyFleetIdx,
      allyMultiPvpReward
    );

    // 주 적 설정.
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      battleType,
      BC.Faction.Enemy,
      anchorInfo.enemyAnchor,
      anchorInfo.enemyAbsDirection,
      anchorInfo.enemyAbsDistance,
      anchorInfo.enemyAnchorAc,
      required.enemyFleetData,
      user.userId,
      {
        fleetIdx: enemyFleetIdx,
        multiPvpReward: enemyMultiPvpReward,
        representedMateCmsId: optional.representedMateCmsId,
        representedMateIllustCmsId: optional.representedMateIllustCmsId,
      }
    );

    // 추가 함대 구성.
    let addFleets: BattleParamFleet[];
    const oceanNpcCms = getOceanNpcCms()[required.enemyFleetData.npcCmsId];
    const oceanNpcStageCms = cms.OceanNpcStage[oceanNpcCms.stageId];
    if (oceanNpcStageCms) {
      addFleets = UserBattle._buildBattleParamAddFleets(
        user.userId,
        oceanNpcStageCms.addStartFleet,
        battleType
      );
    }

    // 난입 npc 설정
    const reinforcements = UserBattle._buildBattleParamReinforcementFleet(
      battleType,
      required.reinforcementFleets,
      user.userId
    );

    // 대상의 타입(npc, 온라인유저, 오프라인유저)
    const targetType: EncountTargetType = required.targetType;
    const targetUserId: number = optional.targetUserId;

    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);

    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);

    const questCmsId: number = optional.questCmsId;

    // pvp전투 시 승리할 경우 받는 약탈 보상
    const pvpPlunderReward: PvpPlunderReward = optional.pvpPlunderReward;

    // 미션 (추가 목표)
    const mission = UserBattle._buildBattleParamMission(enemy.oceanNpcId, optional.bpChallenge);

    return {
      userId: user.userId,
      battleType,
      revision,
      patchRevision,
      versionText,
      mapCmsId,
      climateCmsId,
      encounterResult: required.encountResult,
      randomSeed: curTimeUtc,
      bOfflineSailing: user.isOfflineSailingBot,
      bAttack: required.bAttack,
      startWindSpeed,
      startWindDirection,
      questData,
      ally,
      enemy,
      addFleets,
      reinforcements,
      startTime,
      startWeather,
      bTest: false,
      challenge: optional.bpChallenge,
      targetType,
      targetUserId,
      targetLogId: optional.targetLogId,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      nationPower,
      bmoLayerIndexArray: optional.bmoLayerIndexArray,
      quickModeData,
      questCmsId,
      encountQuestCmsId: required.encountQuestCmsId,
      pvpPlunderReward,
      mission,
      bMultiBattle: optional.bMultiBattle,
      stageBuffs: optional.stageBuffs,
    };
  }

  // ----------------------------------------------------------------------------
  // 클라이언트에게 넘겨 줄 "도전" BattleParam을 구성.
  //
  // bpChallenge 의 내용은 유효한 값이라고 가정한다.
  // ----------------------------------------------------------------------------
  static buildChallengeBattleParam(
    user: User,
    curTimeUtc: number,
    challengeCms: BattleChallengeDesc,
    difficulty: number,
    bRetry: boolean,
    questCmsId: number | undefined
  ): BattleParam {
    const challengeStage = challengeCms.stage[difficulty - 1];
    let npcFleetData = npcFleetBuilder.buildAreaOceanFleetData(challengeStage.OceanNpcId);
    const npcFleetStat = oceanNpcStatCache.buildFleetStatParam(npcFleetData);
    npcFleetData = oceanNpcStatCache.mergeOceanFleetDataStat({
      npcFleetData,
      fleetStat: npcFleetStat,
    });
    const oceanNpcCms = getOceanNpcCms()[challengeStage.OceanNpcId];
    const oceanNpcStageCms = cms.OceanNpcStage[oceanNpcCms.stageId];

    // battle param 에 필요한 정보들.
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;
    const battleType = BattleType.Challenge;

    // OceanNpcStage 정보들 세팅.
    const mapCmsId = oceanNpcStageCms.battleMapId;
    const climateCmsId = oceanNpcStageCms.climateId;
    let startTime = oceanNpcStageCms.startTime;
    const startWindDirection = oceanNpcStageCms.windDir;
    const startWindSpeed = oceanNpcStageCms.windSpeed;
    const allyAnchorTemplateCmsId = oceanNpcStageCms.allyAnchorTemplateId;
    const enemyAnchorTemplateCmsId = oceanNpcStageCms.enemyAnchorTemplateId;
    const stageBuffs = oceanNpcStageCms.stageBuff;

    const encounterResult = EncountResult.START_BATTLE;
    const randomSeed = curTimeUtc;
    const bAttack = challengeStage.IsAttack;

    // 현재 권역을 가지고 있는 국가의 국력 설정
    // 도전모드는 현재는 국력과 오브젝트 레이어 구분을 쓰지 않음.
    const nationPower = undefined;
    const bmoLayerIndexArray = undefined;
    const questData = UserBattle._buildQuestData(user);

    const allyAnchorTemplateCms = cms.AnchorTemplate[allyAnchorTemplateCmsId];
    const allyAnchorType = allyAnchorTemplateCms.anchorType;
    const allyAbsDistanceType = allyAnchorTemplateCms.absDistanceType;
    const allyAbsDirectionType = allyAnchorTemplateCms.absDirectionType;
    const allyAnchorAc = allyAnchorTemplateCms.anchorAc
      ? {
          r: allyAnchorTemplateCms.anchorAc[0],
          q: allyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const enemyAnchorTemplateCms = cms.AnchorTemplate[enemyAnchorTemplateCmsId];
    const enemyAnchorType = enemyAnchorTemplateCms.anchorType;
    const enemyAbsDistanceType = enemyAnchorTemplateCms.absDistanceType;
    const enemyAbsDirectionType = enemyAnchorTemplateCms.absDirectionType;
    const enemyAnchorAc = enemyAnchorTemplateCms.anchorAc
      ? {
          r: enemyAnchorTemplateCms.anchorAc[0],
          q: enemyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      allyAnchorType,
      allyAbsDirectionType,
      allyAbsDistanceType,
      allyAnchorAc,
      battleType,
      curTimeUtc
    );
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      battleType,
      BC.Faction.Enemy,
      enemyAnchorType,
      enemyAbsDirectionType,
      enemyAbsDistanceType,
      enemyAnchorAc,
      npcFleetData,
      user.userId
    );

    // 도전 전투에서는 난입 필요시에 퀘스트 스크립트를 사용한다.
    const reinforcements = [];

    // 추가 함대 구성.
    const addFleets = UserBattle._buildBattleParamAddFleets(
      user.userId,
      oceanNpcStageCms.addStartFleet,
      battleType
    );

    // challengeStage.StartTime 은 hour.
    if (startTime) {
      startTime = startTime * 3600;
    } else {
      // 도전모드 테이블에 startTime이 존재하지 않으면 다른 전투 모드와 동일.
      startTime = formula.GetOceanTimeUtc(CMSConst.get('OceanTimeScale'), curTimeUtc);
    }

    const startWeather = _rollWeather(climateCmsId);
    const bTest = false;
    const challenge: BattleParamChallenge = {
      cmsId: challengeCms.id,
      difficulty,
      bRetry,
    };

    // 도전을 클리어한적이 있으면 해당 정보도 세팅.
    const curChallenge = user.userChallenges.getChallenge(challengeCms.id);
    if (curChallenge) {
      challenge.clearedDifficulty = curChallenge.clearedDifficulty;
      challenge.clearedMissionField = curChallenge.clearedMissionField;
    }

    const targetType: EncountTargetType = EncountTargetType.Npc;
    const targetUserId: number = undefined;

    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);

    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);

    // 미션 (추가 목표)
    const mission = UserBattle._buildBattleParamMission(undefined, challenge);

    return {
      userId: user.userId,
      battleType,
      revision,
      patchRevision,
      versionText,
      mapCmsId,
      climateCmsId,
      encounterResult,
      randomSeed,
      bAttack,
      startWindSpeed,
      startWindDirection,
      questData,
      ally,
      enemy,
      addFleets,
      reinforcements,
      startTime,
      startWeather,
      bTest,
      challenge,
      targetType,
      targetUserId,
      targetLogId: oceanNpcCms.id,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      nationPower,
      bmoLayerIndexArray,
      quickModeData,
      questCmsId,
      mission,
      stageBuffs,
    };
  }

  // ----------------------------------------------------------------------------
  // 클라이언트에게 넘겨 줄 "모의전" BattleParam을 구성.
  //
  // bpArena 의 내용은 유효한 값이라고 가정한다.
  // ----------------------------------------------------------------------------
  static buildArenaBattleParam(
    user: User,
    curTimeUtc: number,
    oceanNpcStageCms: OceanNpcStageDesc,
    idx: number,
    opponentData: PlayableOpponentData,
    mode: number,
    curSessionId: number,
    playerFleetData: OceanFleetData
  ): BattleParam {
    // battle param 에 필요한 정보들.
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;
    const battleType = BattleType.Arena;

    // OceanNpcStage 정보들 세팅.
    const mapCmsId = oceanNpcStageCms.battleMapId;
    const climateCmsId = oceanNpcStageCms.climateId;
    let startTime = oceanNpcStageCms.startTime;
    const startWindDirection = oceanNpcStageCms.windDir;
    const startWindSpeed = oceanNpcStageCms.windSpeed;
    const allyAnchorTemplateCmsId = oceanNpcStageCms.allyAnchorTemplateId;
    const enemyAnchorTemplateCmsId = oceanNpcStageCms.enemyAnchorTemplateId;
    const stageBuffs = oceanNpcStageCms.stageBuff;

    const encounterResult = EncountResult.START_BATTLE;
    const randomSeed = curTimeUtc;
    //const bAttack = challengeStage.IsAttack;

    // 현재 권역을 가지고 있는 국가의 국력 설정
    // 모의전은 현재 국력과 오브젝트 레이어 구분을 쓰지 않음.
    const nationPower = undefined;
    const bmoLayerIndexArray = undefined;
    const questData = UserBattle._buildQuestData(user);

    const allyAnchorTemplateCms = cms.AnchorTemplate[allyAnchorTemplateCmsId];
    const allyAnchorType = allyAnchorTemplateCms.anchorType;
    const allyAbsDistanceType = allyAnchorTemplateCms.absDistanceType;
    const allyAbsDirectionType = allyAnchorTemplateCms.absDirectionType;
    const allyAnchorAc = allyAnchorTemplateCms.anchorAc
      ? {
          r: allyAnchorTemplateCms.anchorAc[0],
          q: allyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const enemyAnchorTemplateCms = cms.AnchorTemplate[enemyAnchorTemplateCmsId];
    const enemyAnchorType = enemyAnchorTemplateCms.anchorType;
    const enemyAbsDistanceType = enemyAnchorTemplateCms.absDistanceType;
    const enemyAbsDirectionType = enemyAnchorTemplateCms.absDirectionType;
    const enemyAnchorAc = enemyAnchorTemplateCms.anchorAc
      ? {
          r: enemyAnchorTemplateCms.anchorAc[0],
          q: enemyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const ally = UserBattle._buildBattleParamPlayerFleetArena(
      user,
      allyAnchorType,
      allyAbsDirectionType,
      allyAbsDistanceType,
      allyAnchorAc,
      playerFleetData
    );
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      battleType,
      BC.Faction.Enemy,
      enemyAnchorType,
      enemyAbsDirectionType,
      enemyAbsDistanceType,
      enemyAnchorAc,
      opponentData.enemyFleetData,
      user.userId,
      {
        representedMateCmsId: opponentData.representedMateCmsId,
        representedMateIllustCmsId: opponentData.representedMateIllustCmsId,
      }
    );

    // 도전 전투에서는 난입 필요시에 퀘스트 스크립트를 사용한다.
    const reinforcements = [];

    // 추가 함대 구성.
    // const addFleets = UserBattle._buildBattleParamAddFleets(
    //   user.userId,
    //   oceanNpcStageCms.addStartFleet
    // );

    // challengeStage.StartTime 은 hour.
    if (startTime) {
      startTime = startTime * 3600;
    } else {
      // 도전모드 테이블에 startTime이 존재하지 않으면 다른 전투 모드와 동일.
      startTime = formula.GetOceanTimeUtc(CMSConst.get('OceanTimeScale'), curTimeUtc);
    }

    const startWeather = _rollWeather(climateCmsId);
    const bTest = false;
    const arena: BattleParamArena = {
      idx,
      enemyType: opponentData.enemyType,
      opponentId: opponentData.opponentId,
      sessionId: curSessionId,
      mode,
      opponentScore: opponentData.opponentScore,
      opponentGrade: opponentData.opponentGrade,
      myScore: user.userArena.getScore(),
      myGrade: user.userArena.getGrade(),
    };

    const targetType: EncountTargetType = EncountTargetType.Npc;
    const targetUserId: number = undefined;

    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);

    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);

    // 미션 (추가 목표)
    //const mission = UserBattle._buildBattleParamMission(undefined, challenge);

    return {
      userId: user.userId,
      battleType,
      revision,
      patchRevision,
      versionText,
      mapCmsId,
      climateCmsId,
      encounterResult,
      randomSeed,
      bAttack: true,
      startWindSpeed,
      startWindDirection,
      questData,
      ally,
      enemy,
      //addFleets,
      reinforcements,
      startTime,
      startWeather,
      bTest,
      arena,
      targetType,
      targetUserId,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      nationPower,
      bmoLayerIndexArray,
      quickModeData,
      //questCmsId,
      mission: {
        groupId: 0,
        btlIds: [],
      },
      stageBuffs,
    };
  }
  static buildSweepRaidBattleParam(
    user: User,
    bossRaidCmsId: number,
    accumDamage: number
  ): BattleParam {
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;

    const bossRaidCms: BossRaidDesc = cms.BossRaid[bossRaidCmsId];
    const npcFleetData = npcFleetBuilder.buildAreaOceanFleetData(bossRaidCms.targetOceanNpc);
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      BattleType.Raid,
      BC.Faction.Enemy,
      undefined,
      undefined,
      undefined,
      undefined,
      npcFleetData,
      user.userId
    );

    const battleParamRaid: BattleParamRaid = {
      bossRaidCmsId: bossRaidCmsId,
      accumDamage: accumDamage,
    };

    return {
      userId: user.userId,
      battleType: undefined,
      revision,
      patchRevision,
      versionText,
      mapCmsId: undefined,
      climateCmsId: undefined,
      encounterResult: undefined,
      randomSeed: undefined,
      bOfflineSailing: false,
      bAttack: true,
      startWindSpeed: undefined,
      startWindDirection: undefined,
      questData: undefined,
      ally: undefined,
      enemy,
      addFleets: [],
      reinforcements: [],
      startTime: undefined,
      startWeather: undefined,
      bTest: false,
      challenge: undefined,
      targetType: undefined,
      targetUserId: undefined,
      targetLogId: undefined,
      points: user.userPoints.getPointTable(),
      freeTakebackData: undefined,
      nationPower: undefined,
      bmoLayerIndexArray: undefined,
      quickModeData: undefined,
      questCmsId: undefined,
      encountQuestCmsId: undefined,
      pvpPlunderReward: undefined,
      mission: undefined,
      bMultiBattle: undefined,
      raid: battleParamRaid,
      stageBuffs: undefined,
    };
  }

  // ----------------------------------------------------------------------------
  // 레이드 전용  BattleParam을 구성.
  // ----------------------------------------------------------------------------
  static buildRaidBattleParam(
    user: User,
    curTimeUtc: number,
    bossRaidCmsId: number,
    curHp: number,
    combatPower: number
  ): BattleParam {
    // battle param 에 필요한 정보들.
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;
    const battleType = BattleType.Raid;

    const bossRaidCms = cms.BossRaid[bossRaidCmsId];

    const fixedValues: {
      sailor?: number;
      durability?: number;
    } = {};

    if (bossRaidCms.recordHP === RECORD_HP.DURABILITY) {
      fixedValues.durability = curHp;
    } else if (bossRaidCms.recordHP === RECORD_HP.SAILOR) {
      fixedValues.sailor = curHp;
    } else if (bossRaidCms.recordHP === RECORD_HP.OUTER) {
      // 아직 정해진 기획이 없다.
    }

    let npcFleetData = npcFleetBuilder.buildAreaOceanFleetData(bossRaidCms.targetOceanNpc);
    const npcFleetStat = oceanNpcStatCache.buildFleetStatParam(npcFleetData);
    npcFleetData = oceanNpcStatCache.mergeOceanFleetDataStat({
      npcFleetData,
      fleetStat: npcFleetStat,
      fixedValues,
    });
    const oceanNpcCms = getOceanNpcCms()[bossRaidCms.targetOceanNpc];
    const oceanNpcStageCms = cms.OceanNpcStage[oceanNpcCms.stageId];

    // OceanNpcStage 정보들 세팅.
    const mapCmsId = oceanNpcStageCms.battleMapId;
    const climateCmsId = oceanNpcStageCms.climateId;
    let startTime = oceanNpcStageCms.startTime;
    const startWindDirection = oceanNpcStageCms.windDir;
    const startWindSpeed = oceanNpcStageCms.windSpeed;
    const allyAnchorTemplateCmsId = oceanNpcStageCms.allyAnchorTemplateId;
    const enemyAnchorTemplateCmsId = oceanNpcStageCms.enemyAnchorTemplateId;
    const stageBuffs = oceanNpcStageCms.stageBuff;

    const encounterResult = EncountResult.START_BATTLE;
    const randomSeed = curTimeUtc;
    const bAttack = true;

    const allyAnchorTemplateCms = cms.AnchorTemplate[allyAnchorTemplateCmsId];
    const allyAnchorType = allyAnchorTemplateCms.anchorType;
    const allyAbsDistanceType = allyAnchorTemplateCms.absDistanceType;
    const allyAbsDirectionType = allyAnchorTemplateCms.absDirectionType;
    const allyAnchorAc = allyAnchorTemplateCms.anchorAc
      ? {
          r: allyAnchorTemplateCms.anchorAc[0],
          q: allyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const enemyAnchorTemplateCms = cms.AnchorTemplate[enemyAnchorTemplateCmsId];
    const enemyAnchorType = enemyAnchorTemplateCms.anchorType;
    const enemyAbsDistanceType = enemyAnchorTemplateCms.absDistanceType;
    const enemyAbsDirectionType = enemyAnchorTemplateCms.absDirectionType;
    const enemyAnchorAc = enemyAnchorTemplateCms.anchorAc
      ? {
          r: enemyAnchorTemplateCms.anchorAc[0],
          q: enemyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    // 날씨 설정
    const startWeather = _rollWeather(climateCmsId);

    // 퀘스트 데이터
    const questData = this._buildQuestData(user);

    // 현재 권역을 가지고 있는 국가의 국력 설정
    const nationPower = UserBattle._buildNationPower(user);

    // 플레이어 설정
    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      allyAnchorType,
      allyAbsDirectionType,
      allyAbsDistanceType,
      allyAnchorAc,
      battleType,
      curTimeUtc
    );
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      battleType,
      BC.Faction.Enemy,
      enemyAnchorType,
      enemyAbsDirectionType,
      enemyAbsDistanceType,
      enemyAnchorAc,
      npcFleetData,
      user.userId
    );

    enemy.combatPower = combatPower;

    // 추가 함대 구성.
    const addFleets = UserBattle._buildBattleParamAddFleets(
      user.userId,
      oceanNpcStageCms.addStartFleet,
      battleType
    );

    // 난입 npc 설정
    const reinforcements = [];

    if (startTime) {
      startTime = startTime * 3600;
    } else {
      // 도전모드 테이블에 startTime이 존재하지 않으면 다른 전투 모드와 동일.
      startTime = formula.GetOceanTimeUtc(CMSConst.get('OceanTimeScale'), curTimeUtc);
    }

    // 대상의 타입(npc, 온라인유저, 오프라인유저)
    const targetType: EncountTargetType = EncountTargetType.Npc;
    const targetUserId: number = undefined;

    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);

    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);

    const bmoLayerIndexArray = undefined;

    const questCmsId = oceanNpcStageCms.questId;

    const battleParamRaid: BattleParamRaid = {
      bossRaidCmsId: bossRaidCmsId,
    };

    // 미션 (추가 목표)
    const mission = {
      groupId: 0,
      btlIds: [],
    };

    return {
      userId: user.userId,
      battleType,
      revision,
      patchRevision,
      versionText,
      mapCmsId,
      climateCmsId,
      encounterResult,
      randomSeed,
      bOfflineSailing: user.isOfflineSailingBot,
      bAttack,
      startWindSpeed,
      startWindDirection,
      questData,
      ally,
      enemy,
      addFleets,
      reinforcements,
      startTime,
      startWeather,
      bTest: false,
      targetType,
      targetUserId,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      nationPower,
      bmoLayerIndexArray: bmoLayerIndexArray,
      quickModeData,
      questCmsId,
      mission,
      raid: battleParamRaid,
      stageBuffs,
    };
  }

  // ----------------------------------------------------------------------------
  // 상회 레이드 전용  BattleParam을 구성.
  // ----------------------------------------------------------------------------
  static buildGuildRaidBattleParam(
    user: User,
    curTimeUtc: number,
    guildBossRaidCmsId: number,
    curHp: number,
    additionalBuffs: number[]
  ): BattleParam {
    // battle param 에 필요한 정보들.
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;
    const battleType = BattleType.GuildRaid;

    const guildBossRaidCms = cms.GuildBossRaid[guildBossRaidCmsId];

    const fixedValues: {
      sailor?: number;
      durability?: number;
    } = {};
    if (guildBossRaidCms.recordHP === RECORD_HP.DURABILITY) {
      fixedValues.durability = curHp;
    } else if (guildBossRaidCms.recordHP === RECORD_HP.SAILOR) {
      fixedValues.sailor = curHp;
    } else if (guildBossRaidCms.recordHP === RECORD_HP.OUTER) {
      // 아직 정해진 기획이 없다.
    }

    let npcFleetData = npcFleetBuilder.buildAreaOceanFleetData(guildBossRaidCms.targetOceanNpc);
    const npcFleetStat = oceanNpcStatCache.buildFleetStatParam(npcFleetData);
    npcFleetData = oceanNpcStatCache.mergeOceanFleetDataStat({
      npcFleetData,
      fleetStat: npcFleetStat,
      fixedValues,
    });
    const oceanNpcCms = getOceanNpcCms()[guildBossRaidCms.targetOceanNpc];
    const oceanNpcStageCms = cms.OceanNpcStage[oceanNpcCms.stageId];

    // OceanNpcStage 정보들 세팅.
    const mapCmsId = oceanNpcStageCms.battleMapId;
    const climateCmsId = oceanNpcStageCms.climateId;
    let startTime = oceanNpcStageCms.startTime;
    const startWindDirection = oceanNpcStageCms.windDir;
    const startWindSpeed = oceanNpcStageCms.windSpeed;
    const allyAnchorTemplateCmsId = oceanNpcStageCms.allyAnchorTemplateId;
    const enemyAnchorTemplateCmsId = oceanNpcStageCms.enemyAnchorTemplateId;
    const stageBuffs = oceanNpcStageCms.stageBuff;

    const encounterResult = EncountResult.START_BATTLE;
    const randomSeed = curTimeUtc;
    const bAttack = true;

    const allyAnchorTemplateCms = cms.AnchorTemplate[allyAnchorTemplateCmsId];
    const allyAnchorType = allyAnchorTemplateCms.anchorType;
    const allyAbsDistanceType = allyAnchorTemplateCms.absDistanceType;
    const allyAbsDirectionType = allyAnchorTemplateCms.absDirectionType;
    const allyAnchorAc = allyAnchorTemplateCms.anchorAc
      ? {
          r: allyAnchorTemplateCms.anchorAc[0],
          q: allyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const enemyAnchorTemplateCms = cms.AnchorTemplate[enemyAnchorTemplateCmsId];
    const enemyAnchorType = enemyAnchorTemplateCms.anchorType;
    const enemyAbsDistanceType = enemyAnchorTemplateCms.absDistanceType;
    const enemyAbsDirectionType = enemyAnchorTemplateCms.absDirectionType;
    const enemyAnchorAc = enemyAnchorTemplateCms.anchorAc
      ? {
          r: enemyAnchorTemplateCms.anchorAc[0],
          q: enemyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    // 날씨 설정
    const startWeather = _rollWeather(climateCmsId);

    // 퀘스트 데이터
    const questData = this._buildQuestData(user);

    // 플레이어 설정
    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      allyAnchorType,
      allyAbsDirectionType,
      allyAbsDistanceType,
      allyAnchorAc,
      battleType,
      curTimeUtc
    );
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      battleType,
      BC.Faction.Enemy,
      enemyAnchorType,
      enemyAbsDirectionType,
      enemyAbsDistanceType,
      enemyAnchorAc,
      npcFleetData,
      user.userId
    );

    // 상회 토벌 함대 버프 적용.
    additionalBuffs.forEach((wbCmsId: number) => {
      const wbCms = cms.WorldBuff[wbCmsId];
      if (
        (wbCms.battleBuffId ||
          (wbCms.childStatEffect && wbCms.childStatEffect.length > 0) ||
          wbCms.childSpecStatExpModifer) &&
        BattleUtil.isApplicableGameMode(battleType, wbCms.id, wbCms.modeBitFlag)
      ) {
        ally.bpWorldBuffs.push({
          cmsId: wbCmsId,
          stack: 1,
        });
      }
    });

    enemy.combatPower = guildBossRaidCms.combatStat;

    // 추가 함대 구성.
    const addFleets = UserBattle._buildBattleParamAddFleets(
      user.userId,
      oceanNpcStageCms.addStartFleet,
      battleType
    );

    // 난입 npc 설정
    const reinforcements = [];

    if (startTime) {
      startTime = startTime * 3600;
    } else {
      // 도전모드 테이블에 startTime이 존재하지 않으면 다른 전투 모드와 동일.
      startTime = formula.GetOceanTimeUtc(CMSConst.get('OceanTimeScale'), curTimeUtc);
    }

    // 대상의 타입(npc, 온라인유저, 오프라인유저)
    const targetType: EncountTargetType = EncountTargetType.Npc;
    const targetUserId: number = undefined;

    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);

    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);

    const bmoLayerIndexArray = undefined;

    const questCmsId = oceanNpcStageCms.questId;

    const battleParamRaid: BattleParamRaid = {
      bossRaidCmsId: guildBossRaidCmsId,
      remainingHp: curHp,
    };

    // 미션 (추가 목표)
    const mission = {
      groupId: 0,
      btlIds: [],
    };

    return {
      userId: user.userId,
      battleType: BattleType.GuildRaid,
      revision,
      patchRevision,
      versionText,
      mapCmsId,
      climateCmsId,
      encounterResult,
      randomSeed,
      bOfflineSailing: user.isOfflineSailingBot,
      bAttack,
      startWindSpeed,
      startWindDirection,
      questData,
      ally,
      enemy,
      addFleets,
      reinforcements,
      startTime,
      startWeather,
      bTest: false,
      targetType,
      targetUserId,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      bmoLayerIndexArray: bmoLayerIndexArray,
      quickModeData,
      questCmsId,
      mission,
      raid: battleParamRaid,
      stageBuffs,
    };
  }

  // ----------------------------------------------------------------------------
  // 소탕 전용.  BattleParam을 구성.
  // ----------------------------------------------------------------------------
  static buildSweepBattleParam(user: User, enemyFleetData: OceanFleetData): BattleParam {
    // 유저의 git revsion + version text
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;

    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined
    );

    const enemy = UserBattle._buildBattleParamEnemyFleet(
      BattleType.Encount,
      BC.Faction.Enemy,
      undefined,
      undefined,
      undefined,
      undefined,
      enemyFleetData,
      user.userId
    );

    return {
      userId: user.userId,
      battleType: undefined,
      revision,
      patchRevision,
      versionText,
      mapCmsId: undefined,
      climateCmsId: undefined,
      encounterResult: undefined,
      randomSeed: undefined,
      bOfflineSailing: false,
      bAttack: true,
      startWindSpeed: undefined,
      startWindDirection: undefined,
      questData: undefined,
      ally,
      enemy,
      addFleets: [],
      reinforcements: [],
      startTime: undefined,
      startWeather: undefined,
      bTest: false,
      challenge: undefined,
      targetType: EncountTargetType.Npc,
      targetUserId: undefined,
      targetLogId: undefined,
      points: user.userPoints.getPointTable(),
      freeTakebackData: undefined,
      nationPower: undefined,
      bmoLayerIndexArray: undefined,
      quickModeData: undefined,
      questCmsId: undefined,
      encountQuestCmsId: undefined,
      pvpPlunderReward: undefined,
      mission: undefined,
      bMultiBattle: undefined,
      stageBuffs: undefined,
    };
  }

  // ----------------------------------------------------------------------------
  // 도전의 탑 전용.  BattleParam을 구성.
  // ----------------------------------------------------------------------------
  static buildInfiniteLighthouseBattleParam(
    user: User,
    curTimeUtc: number,
    infiniteLighthouseCms: InfiniteLighthouseDesc,
    sessionId: number,
    questCmsId: number | undefined
  ): BattleParam {
    let npcFleetData = npcFleetBuilder.buildAreaOceanFleetData(infiniteLighthouseCms.oceanNpcId);
    const npcFleetStat = oceanNpcStatCache.buildFleetStatParam(npcFleetData);
    npcFleetData = oceanNpcStatCache.mergeOceanFleetDataStat({
      npcFleetData,
      fleetStat: npcFleetStat,
    });
    const oceanNpcCms = getOceanNpcCms()[infiniteLighthouseCms.oceanNpcId];
    const oceanNpcStageCms = cms.OceanNpcStage[oceanNpcCms.stageId];

    const mapCmsId = oceanNpcStageCms.battleMapId;
    const climateCmsId = oceanNpcStageCms.climateId;
    let startTime = oceanNpcStageCms.startTime;
    const startWindDirection = oceanNpcStageCms.windDir;
    const startWindSpeed = oceanNpcStageCms.windSpeed;
    const allyAnchorTemplateCmsId = oceanNpcStageCms.allyAnchorTemplateId;
    const enemyAnchorTemplateCmsId = oceanNpcStageCms.enemyAnchorTemplateId;
    const stageBuffs = oceanNpcStageCms.stageBuff;

    const encounterResult = EncountResult.START_BATTLE;
    const randomSeed = curTimeUtc;
    const bAttack = infiniteLighthouseCms.isAttack;

    const nationPower = undefined;
    const bmoLayerIndexArray = undefined;
    const questData = UserBattle._buildQuestData(user);

    const allyAnchorTemplateCms = cms.AnchorTemplate[allyAnchorTemplateCmsId];
    const allyAnchorType = allyAnchorTemplateCms.anchorType;
    const allyAbsDistanceType = allyAnchorTemplateCms.absDistanceType;
    const allyAbsDirectionType = allyAnchorTemplateCms.absDirectionType;
    const allyAnchorAc = allyAnchorTemplateCms.anchorAc
      ? {
          r: allyAnchorTemplateCms.anchorAc[0],
          q: allyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const enemyAnchorTemplateCms = cms.AnchorTemplate[enemyAnchorTemplateCmsId];
    const enemyAnchorType = enemyAnchorTemplateCms.anchorType;
    const enemyAbsDistanceType = enemyAnchorTemplateCms.absDistanceType;
    const enemyAbsDirectionType = enemyAnchorTemplateCms.absDirectionType;
    const enemyAnchorAc = enemyAnchorTemplateCms.anchorAc
      ? {
          r: enemyAnchorTemplateCms.anchorAc[0],
          q: enemyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      allyAnchorType,
      allyAbsDirectionType,
      allyAbsDistanceType,
      allyAnchorAc,
      BattleType.InfiniteLighthouse,
      curTimeUtc
    );
    const enemy = UserBattle._buildBattleParamEnemyFleet(
      BattleType.InfiniteLighthouse,
      BC.Faction.Enemy,
      enemyAnchorType,
      enemyAbsDirectionType,
      enemyAbsDistanceType,
      enemyAnchorAc,
      npcFleetData,
      user.userId
    );

    // 도전 전투에서는 난입 필요시에 퀘스트 스크립트를 사용한다.
    const reinforcements = [];

    // 추가 함대 구성.
    const addFleets = UserBattle._buildBattleParamAddFleets(
      user.userId,
      oceanNpcStageCms.addStartFleet,
      BattleType.InfiniteLighthouse
    );

    if (startTime) {
      startTime = startTime * 3600;
    } else {
      startTime = formula.GetOceanTimeUtc(CMSConst.get('OceanTimeScale'), curTimeUtc);
    }

    const startWeather = _rollWeather(climateCmsId);
    const bTest = false;
    const infiniteLighthouse: BattleParamInfiniteLighthouse = {
      stageCmsId: infiniteLighthouseCms.id,
      sessionId,
    };

    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);

    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);

    return {
      userId: user.userId,
      battleType: BattleType.InfiniteLighthouse,
      revision: user.revision,
      patchRevision: user.patchRevision,
      versionText: user.versionText,
      mapCmsId,
      climateCmsId,
      encounterResult,
      randomSeed,
      bAttack,
      startWindSpeed,
      startWindDirection,
      questData,
      ally,
      enemy,
      addFleets,
      reinforcements,
      startTime,
      startWeather,
      bTest,
      infiniteLighthouse,
      targetType: EncountTargetType.Npc,
      targetUserId: undefined,
      targetLogId: oceanNpcCms.id,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      nationPower,
      bmoLayerIndexArray,
      quickModeData,
      questCmsId,
      mission: {
        groupId: 0,
        btlIds: [],
      },
      stageBuffs,
    };
  }

  static buildFriendlyBattleParam(
    user: User,
    friendlyEncountState: FriendlyEncountState,
    curTimeUtc: number
  ): BattleParam {
    // battle param 에 필요한 정보들.
    const revision = user.revision;
    const patchRevision = user.patchRevision;
    const versionText = user.versionText;
    const battleType = BattleType.Friendly;

    const oceanNpcStageCms = cms.OceanNpcStage[friendlyEncountState.oceanNpcStageCmsId];

    const mapCmsId = oceanNpcStageCms.battleMapId;
    const climateCmsId = oceanNpcStageCms.climateId;
    let startTime = oceanNpcStageCms.startTime;
    const startWindDirection = oceanNpcStageCms.windDir;
    const startWindSpeed = oceanNpcStageCms.windSpeed;
    const allyAnchorTemplateCmsId = oceanNpcStageCms.allyAnchorTemplateId;
    const enemyAnchorTemplateCmsId = oceanNpcStageCms.enemyAnchorTemplateId;
    const stageBuffs = oceanNpcStageCms.stageBuff;

    const allyAnchorTemplateCms = cms.AnchorTemplate[allyAnchorTemplateCmsId];
    const allyAnchorType = allyAnchorTemplateCms.anchorType;
    const allyAbsDistanceType = allyAnchorTemplateCms.absDistanceType;
    const allyAbsDirectionType = allyAnchorTemplateCms.absDirectionType;
    const allyAnchorAc = allyAnchorTemplateCms.anchorAc
      ? {
          r: allyAnchorTemplateCms.anchorAc[0],
          q: allyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    const enemyAnchorTemplateCms = cms.AnchorTemplate[enemyAnchorTemplateCmsId];
    const enemyAnchorType = enemyAnchorTemplateCms.anchorType;
    const enemyAbsDistanceType = enemyAnchorTemplateCms.absDistanceType;
    const enemyAbsDirectionType = enemyAnchorTemplateCms.absDirectionType;
    const enemyAnchorAc = enemyAnchorTemplateCms.anchorAc
      ? {
          r: enemyAnchorTemplateCms.anchorAc[0],
          q: enemyAnchorTemplateCms.anchorAc[1],
        }
      : undefined;

    // 날씨 설정
    const startWeather = _rollWeather(climateCmsId);

    // 퀘스트 데이터
    const questData = this._buildQuestData(user);

    const randomSeed = curTimeUtc;

    let allyFleetIdx: number;
    let enemyFleetIdx: number;
    if (friendlyEncountState.bAttack) {
      allyFleetIdx = BC.FirstFleetIdx;
      enemyFleetIdx = BC.FirstFleetIdx + 1;
    } else {
      allyFleetIdx = BC.FirstFleetIdx + 1;
      enemyFleetIdx = BC.FirstFleetIdx;
    }

    const mpr: MultiPvpReward = {
      pvpPoint: 0,
      cargo: {},
    };

    const ally = UserBattle._buildBattleParamPlayerFleet(
      user,
      allyAnchorType,
      allyAbsDirectionType,
      allyAbsDistanceType,
      allyAnchorAc,
      BattleType.Friendly,
      curTimeUtc,
      undefined,
      allyFleetIdx,
      mpr
    );

    const enemy = UserBattle._buildBattleParamEnemyFleet(
      battleType,
      BC.Faction.Enemy,
      enemyAnchorType,
      enemyAbsDirectionType,
      enemyAbsDistanceType,
      enemyAnchorAc,
      friendlyEncountState.fleetData,
      user.userId,
      {
        fleetIdx: enemyFleetIdx,
        representedMateCmsId: friendlyEncountState.representedMateCmsId,
        representedMateIllustCmsId: friendlyEncountState.representedMateIllustCmsId,
      }
    );

    // 무르기 정보.
    const freeTakebackData = UserBattle._buildFreeTakebackData(user, curTimeUtc);

    // 퀵모드 정보.
    const quickModeData = UserBattle._buildQuickModeData(user, curTimeUtc);

    return {
      userId: user.userId,
      battleType,
      revision,
      patchRevision,
      versionText,
      mapCmsId,
      climateCmsId,
      encounterResult: EncountResult.START_BATTLE,
      randomSeed,
      bOfflineSailing: user.isOfflineSailingBot,
      bAttack: friendlyEncountState.bAttack,
      startWindSpeed,
      startWindDirection,
      questData,
      ally,
      enemy,
      // addFleets: [],
      reinforcements: [],
      startTime,
      startWeather,
      bTest: false,
      targetType: EncountTargetType.OnlineUser,
      targetUserId: friendlyEncountState.userId,
      points: user.userPoints.getPointTable(),
      freeTakebackData,
      nationPower: undefined,
      bmoLayerIndexArray: undefined,
      pvpPlunderReward: undefined,
      quickModeData,
      // questCmsId,
      mission: {
        groupId: 0,
        btlIds: [],
      },
      bMultiBattle: true,
      stageBuffs,
    };
  }

  // ----------------------------------------------------------------------------
  // 항해 일지 기입
  // ----------------------------------------------------------------------------
  static _addSailingDiaryBattleStart(
    user: User,
    battleParam: BattleParam,
    mtParam: MultiplayerParam,
    battleId: string
  ) {
    let bEncountByEnemy: boolean;
    let enemyUserId;
    let enemyBpFleetIdx;

    const multiId = mtParam ? mtParam.multiId : undefined;
    if (multiId) {
      // 항해일지 기입용 작업이라 에러로그만 남기고 진행한다
      if (mtParam.users.length < 2) {
        mlog.error('MultiplayerParam not having 2 users', { userId: user.userId, multiId });
      } else {
        // 상대방의 userId와 fleetIdx를 구해서 전달해야한다
        if (mtParam.users[0].userId === user.userId) {
          enemyUserId = mtParam.users[1].userId;
          enemyBpFleetIdx = mtParam.users[1].fleetIdx;
        } else if (mtParam.users[1].userId === user.userId) {
          enemyUserId = mtParam.users[0].userId;
          enemyBpFleetIdx = mtParam.users[0].fleetIdx;
        } else {
          mlog.error('MultiplayerParam not having correct userIds', {
            userId: user.userId,
            multiId,
          });
        }
      }

      // PVP에서의 강습 판단 기준
      if (battleParam.bAttack) {
        // ally 가 공격측
        if (enemyBpFleetIdx === battleParam.ally.fleetIdx) {
          bEncountByEnemy = true;
        }
      } else {
        // enemy 가 공격측
        if (enemyBpFleetIdx === battleParam.enemy.fleetIdx) {
          bEncountByEnemy = true;
        }
      }
    } else {
      // 오프라인항해 유저와의 전투인 경우
      if (battleParam.targetType !== EncountTargetType.Npc) {
        enemyUserId = battleParam.targetUserId;
        enemyBpFleetIdx = battleParam.enemy.fleetIdx;
      }

      // PVE 에서의 강습 판단 기준
      if (!battleParam.bAttack) {
        bEncountByEnemy = true;
      }
    }

    const targetData = BattleUtil.buildTargetDataForSailingDiary(
      battleParam,
      enemyUserId,
      enemyBpFleetIdx
    );

    mlog.verbose('[YMK] _addSailingDiaryBattleStart 1', {
      userId: user.userId,
      enemyUserId,
      enemyBpFleetIdx,
      mtParam,
      battleParam,
      targetData,
    });

    if (bEncountByEnemy) {
      user.userSailingDiaries.addDiaryBattleEcountByEnemy(user, battleId, targetData);
    }

    // 전투 시작
    user.userSailingDiaries.addDiaryBattleStart(user, battleId, targetData);
  }

  // ----------------------------------------------------------------------------
  // 국가의 정책 추출
  // ----------------------------------------------------------------------------
  static _buildNationPolicies(
    nationCmsId: number
  ): { [group: number]: NationPolicySync } | undefined {
    if (!nationCmsId || nationCmsId === 0) {
      return undefined;
    }

    const { nationManager } = Container.get(LobbyService);
    const curTimeUtc = mutil.curTimeUtc();
    return nationManager.getNationPolicies(nationCmsId, curTimeUtc);
  }

  // -----------------------------------------------------------------------------
  // [운영로그] 전투 시작.
  // -----------------------------------------------------------------------------
  static glogBattleStart(
    user: User,
    battleId: string,
    bp: BattleParam,
    exchangeHash?: string,
    guildDataForLog?: any,
    pr_data?: PrData[]
  ): void {
    if (bp.bTest) {
      // 개발용 모의 전투는 로그를 남기지 않는다.
      return;
    }
    const curTimeUtc: number = mutil.curTimeUtc();

    let logCollection: string;
    if (bp.challenge) {
      logCollection = _LogColChallenge;
    } else if (bp.arena) {
      logCollection = _LogColArena;
    } else if (bp.raid) {
      logCollection = _LogColRaid;
      if (bp.battleType === BattleType.Raid) {
        logCollection = _LogColRaid;
      } else if (bp.battleType === BattleType.GuildRaid) {
        logCollection = _LogColGuildRaid;
      } else {
        logCollection = 'unknown';
      }
    } else if (bp.infiniteLighthouse) {
      logCollection = _LogColInfiniteLighthouse;
    } else if (bp.clash) {
      logCollection = _LogColClash;
    } else {
      if (bp.targetType === EncountTargetType.Npc) {
        logCollection = _LogColPve;
      } else {
        logCollection = _LogColPvp;
      }
    }

    // region_id
    let region_id: number = null;

    // coordinates
    let coordinates: GLogCoordinate = null;

    const sailState = user.userSailing.getSailState();
    if (sailState) {
      region_id = sailState.region ? sailState.region.id : null;
      coordinates = getGLogCoordinate(sailState.location);
    } else {
      const townCmsId = user.userTown.getTownCmsId();
      const townCms = cms.Town[townCmsId];
      region_id = townCms.RegionId;
    }

    let battle_ship_data;
    const battle_start_data = UserBattle.buildGlog_battle_start_data(user, bp);
    if (bp.arena) {
      battle_ship_data = UserBattle.buildGlog_arena_battle_ship_data(user, bp.ally.ships);
    } else {
      battle_ship_data = UserBattle.buildGlog_battle_ship_data(user, bp.ally.ships);
    }
    const enemy_ship_data = UserBattle.buildGlog_enemy_ship_data(bp.enemy.ships);

    let battle_challenge_data: any;
    if (bp.challenge) {
      const userChallenge = user.userChallenges.getChallenge(bp.challenge.cmsId);

      battle_challenge_data = {
        challenge_id: bp.challenge.cmsId,
        difficulty: bp.challenge.difficulty,
        is_retry: bp.challenge.bRetry ? 1 : 0,
        clear_difficulty: userChallenge ? userChallenge.clearedDifficulty : null,
        clear_mission: userChallenge ? userChallenge.clearedMissionField : null,
      };
    }

    let enemy_type;
    let enemy_id;
    let arena_start_info;
    let enemy_arena_start_info;
    let is_revenge;
    let enemy_nation;
    let other_fleets;

    let clash_start_info;
    let enemy_clash_start_info;
    if (bp.arena) {
      enemy_type = bp.arena.enemyType;

      arena_start_info = UserBattle.buildGlog_arena_start_info(
        bp.arena.myGrade,
        bp.arena.myScore,
        bp.arena.sessionId
      );

      if (1 === enemy_type) {
        // user
        enemy_id = bp.arena.opponentId;

        enemy_arena_start_info = UserBattle.buildGlog_arena_start_info(
          bp.arena.opponentGrade,
          bp.arena.opponentScore,
          bp.arena.sessionId
        );
      } else {
        // npc
        const botDesc = cms.ArenaMatchBot[bp.arena.opponentId];
        enemy_id = botDesc ? botDesc.fleetId : null;

        enemy_arena_start_info = {
          tier_id: null,
          tier_name: null,
          arena_score: null,
          type: null,
          matchGroup: null,
          season_id: bp.arena.sessionId,
        };
      }

      is_revenge = bp.arena.mode === 1 ? 0 : 1;
    } else if (bp.clash) {
      const { myOrigin, opponentOrigin } = ClashUtil.convertOriginData(user.userId, bp.clash);
      enemy_type = bp.targetType;
      enemy_id = opponentOrigin.userId;
      enemy_nation = bp.enemy.nationCmsId;

      const seasonId: number = ClashUtil.getSeasonId(bp.clash.sessionId);
      clash_start_info = UserBattle.buildGlog_clash_start_info(
        myOrigin.score,
        user.userClash.getSession(curTimeUtc).score,
        myOrigin.winStreak,
        seasonId
      );

      enemy_clash_start_info = UserBattle.buildGlog_clash_enemy_start_info(
        opponentOrigin.score,
        opponentOrigin.winStreak,
        seasonId
      );
    } else {
      enemy_type = bp.targetType;
      enemy_id = bp.targetLogId;
      enemy_nation = bp.enemy.nationCmsId;
      other_fleets = UserBattle.buildGlog_other_fleets(bp);
    }

    let is_elite;
    if (logCollection === _LogColPve) {
      const npcCms = getOceanNpcCms()[bp.enemy.oceanNpcId];
      if (npcCms) {
        is_elite = npcCms.OceanNpcType === OCEAN_NPC_TYPE.ELITE ? 1 : 0;
      }
    }

    let is_offline: number = undefined;
    let is_user: number = undefined;
    if (!bp.challenge && !bp.arena && !bp.clash) {
      is_offline = bp.bOfflineSailing ? 1 : 0;
      is_user = user.isOfflineSailingBot ? 0 : 1;
    }

    const formation_id = bp.ally.battleFormationCmsId;
    const formationCms = cms.BattleFormation[formation_id];
    const formation_name = formationCms ? formationCms.name : null;

    let quest_id = undefined;
    if (!bp.challenge && !bp.clash) {
      quest_id = bp.encountQuestCmsId || null;
    }

    // for raid-battle
    let boss_raid_id;
    let boss_raid_name;
    let remaining_time;
    let remaining_hp;
    let remaining_hp_rate;
    let guild_data;
    let boss_accum_dmg;

    if (bp.battleType === BattleType.Raid) {
      const { raidManager } = Container.get(LobbyService);
      const rMNub = raidManager.getNub();
      const raidNub = raidManager.getRaid(bp.raid.bossRaidCmsId);
      boss_raid_id = bp.raid.bossRaidCmsId;
      boss_raid_name = cms.BossRaid[bp.raid.bossRaidCmsId].name;
      remaining_time = rMNub.stateEndAt - mutil.curTimeUtc();
      boss_accum_dmg = raidNub.getAccumDamage();
    } else if (bp.battleType === BattleType.GuildRaid) {
      const { guildRaidManager } = Container.get(LobbyService);
      const raid = guildRaidManager.getGuildRaid(user.userGuild.guildId, bp.raid.bossRaidCmsId);
      const raidNub = raid.getNub();
      boss_raid_id = bp.raid.bossRaidCmsId;
      boss_raid_name = cms.GuildBossRaid[bp.raid.bossRaidCmsId].name;
      remaining_time = raidNub.stateEndAt - mutil.curTimeUtc();
      remaining_hp = bp.raid.remainingHp;
      remaining_hp_rate = Math.floor((bp.raid.remainingHp / raidNub.maxHp) * 100);
      guild_data = guildDataForLog;
    }

    user.glog(logCollection, {
      rsn: _BattleStartRsn,
      add_rsn: null,
      battle_id: parseInt(battleId),
      region_id,
      coordinates,
      combat_power: bp.ally.combatPower,
      nation: user.nationCmsId,
      enemy_type,
      enemy_id,
      enemy_level: bp.enemy.level,
      enemy_combat_power: bp.enemy.combatPower,
      enemy_nation,
      battle_start_data,
      battle_ship_data,
      enemy_ship_data,
      battle_challenge_data,
      clash_start_info,
      enemy_clash_start_info,
      is_elite,
      is_offline,
      is_user,
      other_fleets,
      arena_start_info,
      enemy_arena_start_info,
      formation_name,
      formation_id,
      is_revenge,
      quest_id,
      boss_raid_id,
      boss_raid_name,
      remaining_time,
      remaining_hp,
      remaining_hp_rate,
      boss_accum_damage: boss_accum_dmg,
      guild_data,
      exchange_hash: exchangeHash ? exchangeHash : null,
      pr_data,
    });
  }

  // -----------------------------------------------------------------------------
  // [운영로그] battle_start_data
  // -----------------------------------------------------------------------------
  static buildGlog_battle_start_data(user: User, bp: BattleParam): any {
    // is_free_takeback
    let is_free_takeback: number = 0;
    if (bp.freeTakebackData) {
      if (
        bp.freeTakebackData.unlimitedByCash.bTurn ||
        bp.freeTakebackData.unlimitedByCash.bPhase ||
        bp.freeTakebackData.freeTakebackCountInfo.turn !== undefined ||
        bp.freeTakebackData.freeTakebackCountInfo.phase !== undefined
      ) {
        is_free_takeback = 1;
      }
    }

    // daily_quickmode_cnt
    let daily_quickmode_cnt: number = 0;
    if (bp.quickModeData) {
      daily_quickmode_cnt = bp.quickModeData.count;
    }

    // mission
    const mission = bp.mission.btlIds;

    // points_data
    const points_data = user.buildGlog_points_data(bp.randomSeed); // 전투 시작시간 UTC

    const bsd = {
      environment: {
        random_seed: bp.randomSeed,
        map: bp.mapCmsId,
        climate: bp.climateCmsId,
        start_time: bp.startTime,
        start_weather: bp.startWeather,
        start_wind_direction: bp.startWindDirection,
        start_wind_speed: bp.startWindSpeed,
        stage_buffs: bp.stageBuffs,
      },
      is_attack: bp.bAttack ? 1 : 0,
      encounter_result: bp.encounterResult,
      nation_power: bp.nationPower !== undefined ? bp.nationPower : null, // explicit null
      is_free_takeback,
      daily_quickmode_cnt,
      mission,
      points_data,
    };

    return bsd;
  }

  // -----------------------------------------------------------------------------
  // [운영로그] battle_start_data
  // -----------------------------------------------------------------------------
  static buildGlog_battle_ship_data(user: User, bpShips: BattleParamShip[]): any[] {
    const shipDataArray = [];

    for (let i = 0; i < bpShips.length; ++i) {
      const bpShip = bpShips[i];
      const userShip = user.userFleets.getShipByFleetIndex(1, i + 1);
      const shipStat = userShip.getStat(user.companyStat);

      const shipData = {
        ship_idx: i + 1,
        ship_id: bpShip.cmsId,
        ship_name: bpShip.name,
        ship_uid: userShip.nub.id,
        ship_level: bpShip.blueprint.level,
        sailor: bpShip.sailor,
        max_sailor: shipStat.get(cmsEx.STAT_TYPE.SHIP_MAX_SAILOR),
        durability: bpShip.durability,
        max_durability: shipStat.get(cmsEx.STAT_TYPE.SHIP_MAX_DURABILITY),
        cargo: userShip.getCurLoad(),
        max_cargo: shipStat.get(cmsEx.STAT_TYPE.SHIP_HOLD),
        supply: bpShip.supplies,

        // 아래는 추가적으로 채워질 정보들.
        slot_override: [],
        mate_data: [],
        parts_data: [],
        battle_passive: bpShip.battlePassives,
      };

      _glogFillShipCommon(shipData, bpShip);

      shipDataArray.push(shipData);
    }

    return shipDataArray;
  }

  static buildGlog_arena_battle_ship_data(user: User, bpShips: BattleParamShip[]): any[] {
    const shipDataArray = [];

    for (let i = 0; i < bpShips.length; ++i) {
      const bpShip = bpShips[i];
      const fleetData = user.userArena.getFleetData();
      const extra = fleetData.shipExtras[i];

      const shipData = {
        ship_idx: i + 1,
        ship_id: bpShip.cmsId,
        ship_name: bpShip.name,
        ship_uid: extra.id,
        ship_level: bpShip.blueprint.level,
        sailor: bpShip.sailor,
        max_sailor: extra.max_sailor,
        durability: bpShip.durability,
        max_durability: extra.max_durability,
        cargo: extra.cargo,
        max_cargo: extra.max_cargo,
        supply: bpShip.supplies,

        // 아래는 추가적으로 채워질 정보들.
        slot_override: [],
        mate_data: [],
        parts_data: [],
        battle_passive: bpShip.battlePassives,
      };

      _glogFillShipCommon(shipData, bpShip);

      shipDataArray.push(shipData);
    }

    return shipDataArray;
  }

  // -----------------------------------------------------------------------------
  // [운영로그] enemy_start_data
  // 스탯이 필요한 부분들은 CBT 이후, 추가 동기화 작업을 한 다음에 채워넣는걸로 협의됨.
  // -----------------------------------------------------------------------------
  static buildGlog_enemy_ship_data(bpShips: BattleParamShip[]): any[] {
    const shipDataArray = [];

    for (let i = 0; i < bpShips.length; ++i) {
      const bpShip = bpShips[i];

      const shipData = {
        ship_idx: i + 1,
        ship_id: bpShip.cmsId,
        ship_name: bpShip.name,
        ship_uid: null,
        ship_level: bpShip.blueprint.level,
        sailor: bpShip.sailor,
        max_sailor: null,
        durability: bpShip.durability,
        max_durability: null,
        cargo: null,
        max_cargo: null,
        supply: bpShip.supplies,

        // 아래는 추가적으로 채워질 정보들.
        slot_override: [],
        mate_data: [],
        parts_data: [],
        battle_passive: bpShip.battlePassives,
      };

      _glogFillShipCommon(shipData, bpShip);

      shipDataArray.push(shipData);
    }

    return shipDataArray;
  }

  // -----------------------------------------------------------------------------
  // [운영로그] battle_summary_data
  // -----------------------------------------------------------------------------
  static buildGlog_battle_summary_data(battleResult: BattleResult): any {
    const userActionCount = battleResult.myBattleActionCount;
    const skillUsed = battleResult.skillUsed;

    const move_cnt = userActionCount[ACTION_COUNTER_TYPE.Move] || 0;
    const rotate_cnt = 0; // 선수방향 제거 되면서 해당 카운터 산출 불가.
    const stand_cnt = battleResult.explicitEndPhaseCount;
    const item_cnt = userActionCount[ACTION_COUNTER_TYPE.UseItem] || 0;

    const cannon_attack_cnt = userActionCount[ACTION_COUNTER_TYPE.Cannon] || 0;
    const melee_attack_cnt = userActionCount[ACTION_COUNTER_TYPE.Melee] || 0;
    const ramming_attack_cnt = userActionCount[ACTION_COUNTER_TYPE.Ramming] || 0;
    const duel_attack_cnt = userActionCount[ACTION_COUNTER_TYPE.Duel] || 0;
    const attack_cnt = cannon_attack_cnt + melee_attack_cnt + ramming_attack_cnt + duel_attack_cnt;

    const durability_recovery_cnt = userActionCount[ACTION_COUNTER_TYPE.Repair] || 0;
    const sailor_recovery_cnt = userActionCount[ACTION_COUNTER_TYPE.Heal] || 0;

    const order_skill_cnt = skillUsed.orders.length;
    const active_skill_cnt = skillUsed.normalCount;

    const userDmg = battleResult.userDmg;
    const min_durability_dmg = userDmg.durability.minDmg ? userDmg.durability.minDmg : null;
    const max_durability_dmg = userDmg.durability.maxDmg;
    const total_durability_dmg = userDmg.durability.totalDmg;
    const min_sailor_dmg = userDmg.sailor.minDmg ? userDmg.sailor.minDmg : null;
    const max_sailor_dmg = userDmg.sailor.maxDmg;
    const total_sailor_dmg = userDmg.sailor.totalDmg;

    const summary = {
      move_cnt,
      rotate_cnt,
      stand_cnt,
      item_cnt,
      attack_cnt,
      cannon_attack_cnt,
      melee_attack_cnt,
      ramming_attack_cnt,
      duel_attack_cnt,
      durability_recovery_cnt,
      sailor_recovery_cnt,
      order_skill_cnt,
      active_skill_cnt,

      min_durability_dmg,
      max_durability_dmg,
      total_durability_dmg,
      min_sailor_dmg,
      max_sailor_dmg,
      total_sailor_dmg,
    };

    return summary;
  }

  // [임시방편] 현재 faction 넘버링은
  // 예전방식 (BC.Faction) 과 새로운방식 (BattleFaction 테이블 ID) 두가지 유형이 존재한다.
  // 로그에서는 새로운 방식의 faction 번호를 써야하기 때문에, 그를 보정하는 임시 함수를 작성함.

  static _fixFactionNum(factionNum: number): number {
    if (factionNum < UserFactionId) {
      // 예전방식 보정 필요.
      factionNum = factionNum + UserFactionId - 1;
    }

    return factionNum;
  }

  // -----------------------------------------------------------------------------
  // [운영로그] other_fleets
  // -----------------------------------------------------------------------------
  static buildGlog_other_fleets(bp: BattleParam): any[] {
    const other_fleets = [];

    // 전투 시작시 추가 함대.
    if (bp.addFleets) {
      for (const f of bp.addFleets) {
        const faction = UserBattle._fixFactionNum(f.faction);
        other_fleets.push({
          npc_id: f.oceanNpcId,
          enter_turn: 1, // 처음부터 등장하기 때문에, 1턴 고정.
          faction,
          faction_relation: _GetFactionRelationWithUser(faction),
        });
      }
    }

    // 난입 추가 함대.
    for (const f of bp.reinforcements) {
      const faction = UserBattle._fixFactionNum(f.faction);
      other_fleets.push({
        npc_id: f.oceanNpcId,
        enter_turn: f.turnNum,
        faction,
        faction_relation: _GetFactionRelationWithUser(faction),
      });
    }

    return other_fleets;
  }

  // -----------------------------------------------------------------------------
  // [운영로그] other_fleets 인카운트로그 전용
  // -----------------------------------------------------------------------------
  static buildGlog_other_fleets_for_encounter(reinforcements: ReinforcementFleet[]): any[] {
    const other_fleets = [];

    // 난입 추가 함대.
    for (const f of reinforcements) {
      const faction = UserBattle._fixFactionNum(f.faction);
      other_fleets.push({
        npc_id: f.fleetData.npcCmsId,
        enter_turn: f.turnNum,
        faction,
        faction_relation: _GetFactionRelationWithUser(faction),
      });
    }

    return other_fleets;
  }

  // -----------------------------------------------------------------------------
  // [운영로그] arena_start_info
  // -----------------------------------------------------------------------------
  static buildGlog_arena_start_info(grade: number, score: number, season_id: number) {
    const tierDesc = cmsEx.getArenaTierByGrade(grade);
    return {
      tier_id: grade,
      tier_name: tierDesc.name,
      arena_score: Math.floor(score),
      type: tierDesc.type,
      matchGroup: tierDesc.matchGroup,
      season_id,
    };
  }

  // -----------------------------------------------------------------------------
  // [운영로그] arena_finish_info
  // -----------------------------------------------------------------------------
  static buildGlog_arena_finish_info(
    grade: number,
    score: number,
    changedScore: number,
    season_id: number
  ) {
    const tierDesc = cmsEx.getArenaTierByGrade(grade);
    return {
      tier_id: grade,
      tier_name: tierDesc.name,
      arena_score: score,
      type: tierDesc.type,
      get_arena_score: changedScore,
      matchGroup: tierDesc.matchGroup,
      season_id,
    };
  }

  // -----------------------------------------------------------------------------
  // [운영로그] arena_battle_ship_data_for_update_fleet
  // -----------------------------------------------------------------------------
  static buildGlog_arena_battle_ship_data_for_update_fleet(
    user: User,
    oceanFleetDataEx: ArenaOceanFleetData
  ) {
    const bpShips = UserBattle._buildBattleParamPlayerShipArena(oceanFleetDataEx, user.userId);
    const battle_ship_data = UserBattle.buildGlog_arena_battle_ship_data(user, bpShips);

    return battle_ship_data;
  }

  // -----------------------------------------------------------------------------
  // [운영로그] loss_data
  // * 손실량 적용 시점은 현재가 아닌 회항 시 이므로 실제 손실량과 다를 수 있음
  // -----------------------------------------------------------------------------
  static buildGlog_loss_data(user: User, battleParam: BattleParam): RewardData[] {
    let ret: RewardData[] = [];

    let loss: {
      ducatLoss: number;
      cargoLosses: ShipCargoNub[];
      cargoChanges: ShipCargoChange[];
    };

    if (battleParam.bMultiBattle) {
      loss = ArriveTownSpec.buildMultiPvpLoss(user, mutil.curTimeUtc());
    } else {
      const firstFleet = user.userFleets.getFirstFleet();
      const firstFleetShips = firstFleet.getShipsOrderByFormationIndex();
      loss = ArriveTownSpec.buildLossByReturningToTown(
        firstFleetShips,
        user.level,
        user.userPoints.getPoint(cmsEx.DucatPointCmsId)
      );
    }

    // ducat
    ret.push({
      type: REWARD_TYPE[REWARD_TYPE.POINT],
      id: cmsEx.DucatPointCmsId,
      uid: null,
      amt: loss.ducatLoss,
    });

    // cargo
    for (const cargoLoss of loss.cargoLosses) {
      let rewardType: REWARD_TYPE;
      if (cms.TradeGoods[cargoLoss.cmsId]) {
        rewardType = REWARD_TYPE.TRADE_GOODS;
      } else if (cms.DepartSupply[cargoLoss.cmsId]) {
        rewardType = REWARD_TYPE.DEPART_SUPPLY;
      } else if (cms.SmuggleGoods[cargoLoss.cmsId]) {
        rewardType = REWARD_TYPE.SMUGGLE_GOODS;
      }
      if (rewardType) {
        ret.push({
          type: REWARD_TYPE[rewardType],
          id: cargoLoss.cmsId,
          uid: null,
          amt: cargoLoss.quantity,
        });
      }
    }

    return ret;
  }

  // ----------------------------------------------------------------------------
  static buildGlog_clash_start_info(
    originScore: number,
    curScore: number,
    winStreak: number,
    seasonId: number
  ) {
    return {
      season_id: seasonId,
      winStreak,
      old_score: originScore,
      cur_score: curScore,
    };
  }

  // ----------------------------------------------------------------------------
  static buildGlog_clash_enemy_start_info(
    originScore: number,
    winStreak: number,
    seasonId: number
  ) {
    return {
      season_id: seasonId,
      winStreak,
      old_score: originScore,
    };
  }
}

// ----------------------------------------------------------------------------
// Private helper functions.
// ----------------------------------------------------------------------------
function _rollWeather(climateCmsId: number): number {
  const climateCms = cms.Climate[climateCmsId];
  const weights: number[] = [];
  for (const weather of climateCms.weather) {
    weights.push(weather.Prob);
  }

  const idx = mutil.weightedSelect(weights);
  return climateCms.weather[idx].Id;
}

function _glogFillShipCommon(
  shipData: { slot_override: any[]; parts_data: any[]; mate_data: any[] },
  bpShip: BattleParamShip
): void {
  for (const sl of bpShip.slotOverrides) {
    const slot_idx = sl.slotIdx;
    const shipSlotCms = cms.ShipSlot[sl.cmsId];

    if (
      shipSlotCms.slotType === SHIP_SLOT_TYPE.MATE ||
      shipSlotCms.slotType === SHIP_SLOT_TYPE.NON_MATE
    ) {
      // 개조 선실들.
      shipData.slot_override.push({
        slot_idx,
        slot_name: shipSlotCms.name,
      });
    } else {
      // 장착 슬롯들.
      shipData.parts_data.push({
        slot_idx,
        parts_id: sl.cmsId,
      });
    }
  }

  for (const bpMate of bpShip.mates) {
    const slot_idx = bpMate.slotIdx;
    const mate_id = bpMate.cmsId;
    const mate_lv = [bpMate.adventureLevel, bpMate.tradeLevel, bpMate.battleLevel];
    const equip_data = [];

    for (const mateEquip of bpMate.equipments) {
      const cequipCms = cms.CEquip[mateEquip.cmsId];
      equip_data.push({
        equip_type: cequipCms.type,
        equip_id: mateEquip.cmsId,
      });
    }

    shipData.mate_data.push({
      slot_idx,
      mate_id,
      mate_lv,
      equip_data,
    });
  }
}

// ----------------------------------------------------------------------------
// Exports.
// ----------------------------------------------------------------------------

export default UserBattle;
