"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mongoDbConnection_1 = require("../mongooselib/mongoDbConnection");
// --------------------------------------------------------------------------
async function _initBySchemaType(mongoDbConn, schemaType) {
    // 인덱스가 미리 생성된 경우, 실패할수 있으니, 하나가 실패하더라도 나머지는 성공하도록.
    return mongoDbConn
        .getModel(schemaType)
        .then((model) => {
        return model.createCollection().then(() => {
            return model.createIndexes();
        });
    })
        .catch((err) => {
        mlog_1.default.warn('Init failed. This may not be a problem when trying to "recreate" index.', {
            schemaType,
            err,
        });
    });
}
// --------------------------------------------------------------------------
async function main() {
    // 로그 파일 출력 안하도록.
    mconf_1.default.log.file = undefined;
    await mhttp_1.default.configd.fetch();
    const mongoDbCfg = mconf_1.default.battleLogMongoDb;
    mongoDbCfg.connOptions.useCreateIndex = true;
    const mongoConn = new mongoDbConnection_1.MongoDbConnection();
    mongoConn.init(mongoDbCfg);
    await _initBySchemaType(mongoConn, 1 /* BATTLE_SUSPECT */);
    await _initBySchemaType(mongoConn, 2 /* BATTLE_CONVICT */);
    await _initBySchemaType(mongoConn, 3 /* BATTLE_GHOST */);
    await _initBySchemaType(mongoConn, 4 /* BATTLE_CANCEL */);
    await _initBySchemaType(mongoConn, 5 /* BATTLE_CANCEL_CONVICT */);
    await _initBySchemaType(mongoConn, 6 /* BATTLE_CANCEL_GHOST */);
    await _initBySchemaType(mongoConn, 7 /* BATTLE_USER_CANCEL */);
}
// --------------------------------------------------------------------------
main()
    .then(() => {
    mlog_1.default.info('Successfully initialized MongoDB for judged.');
    process.exit(0);
})
    .catch((err) => {
    mlog_1.default.error('Failed to init MongoDB for judged!', { err });
    process.exit(1);
});
//# sourceMappingURL=initMongoDb.js.map