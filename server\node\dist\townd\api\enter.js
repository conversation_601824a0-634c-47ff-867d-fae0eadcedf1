"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const libTown = __importStar(require("../libTown"));
module.exports = async (req, res) => {
    const { townCmsId, channelId, userId, userName, syncData, mateNub, lobbyUrl, representedMateView, sidekickMateViews, sidekickPetViews, } = req.body;
    const townUser = libTown.getTownUser(userId);
    if (townUser) {
        mlog_1.default.warn('/enter user already in town', {
            userId,
        });
        const curZone = townUser.getCurrentZone();
        if (curZone) {
            libTown.leaveTownZone(curZone, townUser);
        }
    }
    return libTown.getTownZone(townCmsId, channelId).then((tz) => {
        const townUser = new libTown.TownUser(userId, userName, syncData, mateNub, lobbyUrl, representedMateView, sidekickMateViews, sidekickPetViews);
        libTown.enterTownZone(tz, townUser);
        const resp = {
            channelId: tz.channelId,
        };
        mlog_1.default.debug('/enter resp', resp);
        return res.json(resp);
    });
};
//# sourceMappingURL=enter.js.map