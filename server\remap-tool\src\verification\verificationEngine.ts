import { DatabaseManager } from '../database/databaseManager';
import { RedisManager } from '../redis/redisManager';
import { DatabaseTableInfo, RedisKeyInfo, RemapData, VerificationResult, VerificationDetail } from '../types';
import mysql from 'mysql2/promise';

/**
 * 검증 엔진
 */
export class VerificationEngine {
  private dbManager: DatabaseManager;
  private redisManager: RedisManager;

  constructor(dbManager: DatabaseManager, redisManager: RedisManager) {
    this.dbManager = dbManager;
    this.redisManager = redisManager;
  }

  /**
   * 전체 검증 실행
   */
  async verifyAll(
    tables: DatabaseTableInfo[],
    redisKeys: RedisKeyInfo[],
    remapData: RemapData[],
    worldId?: string
  ): Promise<VerificationResult> {
    console.log('데이터 검증을 시작합니다...');

    const filteredRemapData = worldId
      ? remapData.filter(data => data.gameServerId === worldId)
      : remapData;

    const details: VerificationDetail[] = [];

    // 데이터베이스 검증
    const dbDetails = await this.verifyDatabaseTables(tables, filteredRemapData, worldId);
    details.push(...dbDetails);

    // Redis 검증
    const redisDetails = await this.verifyRedisKeys(redisKeys, filteredRemapData, worldId);
    details.push(...redisDetails);

    const checkedItems = details.length;
    const passedItems = details.filter(d => d.success).length;
    const failedItems = checkedItems - passedItems;

    const result: VerificationResult = {
      success: failedItems === 0,
      checkedItems,
      passedItems,
      failedItems,
      details,
    };

    console.log(`검증 완료: ${passedItems}/${checkedItems} 통과, ${failedItems} 실패`);

    return result;
  }

  /**
   * 데이터베이스 테이블 검증
   */
  private async verifyDatabaseTables(
    tables: DatabaseTableInfo[],
    remapData: RemapData[],
    worldId?: string
  ): Promise<VerificationDetail[]> {
    const details: VerificationDetail[] = [];

    for (const table of tables) {
      try {
        const tableDetails = await this.verifyDatabaseTable(table, remapData, worldId);
        details.push(...tableDetails);
      } catch (error) {
        details.push({
          type: 'database',
          target: `${table.database}.${table.tableName}`,
          expected: '검증 성공',
          actual: `검증 실패: ${error}`,
          success: false,
          message: `테이블 검증 중 오류 발생: ${error}`,
        });
      }
    }

    return details;
  }

  /**
   * 단일 데이터베이스 테이블 검증
   */
  private async verifyDatabaseTable(
    table: DatabaseTableInfo,
    remapData: RemapData[],
    worldId?: string
  ): Promise<VerificationDetail[]> {
    const details: VerificationDetail[] = [];

    // 데이터베이스 연결 선택
    let pool: mysql.Pool;
    let tablePrefix: string;

    switch (table.database) {
      case 'auth':
        pool = this.dbManager.getAuthConnection();
        tablePrefix = 'auth';
        break;
      case 'world':
        if (!worldId) return details;
        pool = this.dbManager.getWorldConnection(worldId);
        tablePrefix = `world-${worldId}`;
        break;
      case 'user':
        if (!worldId) return details;
        pool = this.dbManager.getUserShardConnection(worldId, 0); // 첫 번째 샤드 사용
        tablePrefix = `user-${worldId}`;
        break;
      default:
        return details;
    }

    // 테이블 존재 확인
    if (!await this.dbManager.tableExists(pool, table.tableName)) {
      details.push({
        type: 'database',
        target: `${tablePrefix}.${table.tableName}`,
        expected: '테이블 존재',
        actual: '테이블 없음',
        success: false,
        message: '테이블이 존재하지 않습니다',
      });
      return details;
    }

    // AccountId 컬럼 검증
    if (table.hasAccountId) {
      const accountIdColumns = table.columns.filter(c => c.relatedTo === 'accountId');
      for (const column of accountIdColumns) {
        const columnDetails = await this.verifyDatabaseColumn(
          pool,
          table.tableName,
          column.columnName,
          remapData.map(d => ({ old: d.uwo_Gnid, new: d.uwogl_Gnid })),
          `${tablePrefix}.${table.tableName}.${column.columnName}`
        );
        details.push(...columnDetails);
      }
    }

    // PubId 컬럼 검증
    if (table.hasPubId) {
      const pubIdColumns = table.columns.filter(c => c.relatedTo === 'pubId');
      for (const column of pubIdColumns) {
        const columnDetails = await this.verifyDatabaseColumn(
          pool,
          table.tableName,
          column.columnName,
          remapData.map(d => ({ old: d.uwo_Nid, new: d.uwogl_Nid })),
          `${tablePrefix}.${table.tableName}.${column.columnName}`
        );
        details.push(...columnDetails);
      }
    }

    return details;
  }

  /**
   * 데이터베이스 컬럼 검증
   */
  private async verifyDatabaseColumn(
    pool: mysql.Pool,
    tableName: string,
    columnName: string,
    mappings: Array<{ old: string; new: string }>,
    target: string
  ): Promise<VerificationDetail[]> {
    const details: VerificationDetail[] = [];

    for (const mapping of mappings) {
      // 기존 값이 남아있는지 확인
      const oldValueQuery = `SELECT COUNT(*) as count FROM ${tableName} WHERE ${columnName} = ?`;
      const oldResult = await this.dbManager.executeQuery(pool, oldValueQuery, [mapping.old]) as mysql.RowDataPacket[];
      const oldCount = oldResult[0]?.count || 0;

      if (oldCount > 0) {
        details.push({
          type: 'database',
          target,
          expected: 0,
          actual: oldCount,
          success: false,
          message: `기존 값 ${mapping.old}이 ${oldCount}개 남아있습니다`,
        });
      } else {
        details.push({
          type: 'database',
          target,
          expected: 0,
          actual: 0,
          success: true,
          message: `기존 값 ${mapping.old}이 성공적으로 제거되었습니다`,
        });
      }

      // 새 값이 존재하는지 확인
      const newValueQuery = `SELECT COUNT(*) as count FROM ${tableName} WHERE ${columnName} = ?`;
      const newResult = await this.dbManager.executeQuery(pool, newValueQuery, [mapping.new]) as mysql.RowDataPacket[];
      const newCount = newResult[0]?.count || 0;

      if (newCount > 0) {
        details.push({
          type: 'database',
          target,
          expected: '> 0',
          actual: newCount,
          success: true,
          message: `새 값 ${mapping.new}이 ${newCount}개 존재합니다`,
        });
      } else {
        details.push({
          type: 'database',
          target,
          expected: '> 0',
          actual: 0,
          success: false,
          message: `새 값 ${mapping.new}이 존재하지 않습니다`,
        });
      }
    }

    return details;
  }

  /**
   * Redis 키 검증
   */
  private async verifyRedisKeys(
    redisKeys: RedisKeyInfo[],
    remapData: RemapData[],
    worldId?: string
  ): Promise<VerificationDetail[]> {
    const details: VerificationDetail[] = [];

    // Redis 인스턴스별로 그룹화
    const keysByInstance = this.groupKeysByInstance(redisKeys);

    for (const [instanceName, keys] of keysByInstance) {
      for (const keyInfo of keys) {
        try {
          const keyDetails = await this.verifyRedisKeyPattern(keyInfo, remapData, instanceName);
          details.push(...keyDetails);
        } catch (error) {
          details.push({
            type: 'redis',
            target: `${instanceName}.${keyInfo.keyPattern}`,
            expected: '검증 성공',
            actual: `검증 실패: ${error}`,
            success: false,
            message: `Redis 키 검증 중 오류 발생: ${error}`,
          });
        }
      }
    }

    return details;
  }

  /**
   * Redis 키 패턴 검증
   */
  private async verifyRedisKeyPattern(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<VerificationDetail[]> {
    const details: VerificationDetail[] = [];

    // AccountId/Gnid 관련 키 검증
    if (keyInfo.usesAccountId || keyInfo.usesGnid) {
      const accountDetails = await this.verifyAccountIdKeys(keyInfo, remapData, instanceName);
      details.push(...accountDetails);
    }

    // PubId/Nid 관련 키 검증
    if (keyInfo.usesPubId || keyInfo.usesNid) {
      const pubIdDetails = await this.verifyPubIdKeys(keyInfo, remapData, instanceName);
      details.push(...pubIdDetails);
    }

    return details;
  }

  /**
   * AccountId 관련 Redis 키 검증
   */
  private async verifyAccountIdKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<VerificationDetail[]> {
    const details: VerificationDetail[] = [];

    for (const data of remapData) {
      const target = `${instanceName}.${keyInfo.keyPattern}[${data.uwo_Gnid}->${data.uwogl_Gnid}]`;

      // 기존 키가 남아있는지 확인
      const oldKeys = await this.findKeysWithAccountId(keyInfo.keyPattern, data.uwo_Gnid, instanceName);

      if (oldKeys.length > 0) {
        details.push({
          type: 'redis',
          target,
          expected: 0,
          actual: oldKeys.length,
          success: false,
          message: `기존 AccountId ${data.uwo_Gnid}를 포함한 키가 ${oldKeys.length}개 남아있습니다`,
        });
      } else {
        details.push({
          type: 'redis',
          target,
          expected: 0,
          actual: 0,
          success: true,
          message: `기존 AccountId ${data.uwo_Gnid}를 포함한 키가 성공적으로 제거되었습니다`,
        });
      }

      // 새 키가 존재하는지 확인
      const newKeys = await this.findKeysWithAccountId(keyInfo.keyPattern, data.uwogl_Gnid, instanceName);

      if (newKeys.length > 0) {
        details.push({
          type: 'redis',
          target,
          expected: '> 0',
          actual: newKeys.length,
          success: true,
          message: `새 AccountId ${data.uwogl_Gnid}를 포함한 키가 ${newKeys.length}개 존재합니다`,
        });
      } else {
        details.push({
          type: 'redis',
          target,
          expected: '> 0',
          actual: 0,
          success: false,
          message: `새 AccountId ${data.uwogl_Gnid}를 포함한 키가 존재하지 않습니다`,
        });
      }
    }

    return details;
  }

  /**
   * PubId 관련 Redis 키 검증
   */
  private async verifyPubIdKeys(
    keyInfo: RedisKeyInfo,
    remapData: RemapData[],
    instanceName: string
  ): Promise<VerificationDetail[]> {
    const details: VerificationDetail[] = [];

    for (const data of remapData) {
      const target = `${instanceName}.${keyInfo.keyPattern}[${data.uwo_Nid}->${data.uwogl_Nid}]`;

      // 기존 키가 남아있는지 확인
      const oldKeys = await this.findKeysWithPubId(keyInfo.keyPattern, data.uwo_Nid, instanceName);

      if (oldKeys.length > 0) {
        details.push({
          type: 'redis',
          target,
          expected: 0,
          actual: oldKeys.length,
          success: false,
          message: `기존 PubId ${data.uwo_Nid}를 포함한 키가 ${oldKeys.length}개 남아있습니다`,
        });
      } else {
        details.push({
          type: 'redis',
          target,
          expected: 0,
          actual: 0,
          success: true,
          message: `기존 PubId ${data.uwo_Nid}를 포함한 키가 성공적으로 제거되었습니다`,
        });
      }

      // 새 키가 존재하는지 확인
      const newKeys = await this.findKeysWithPubId(keyInfo.keyPattern, data.uwogl_Nid, instanceName);

      if (newKeys.length > 0) {
        details.push({
          type: 'redis',
          target,
          expected: '> 0',
          actual: newKeys.length,
          success: true,
          message: `새 PubId ${data.uwogl_Nid}를 포함한 키가 ${newKeys.length}개 존재합니다`,
        });
      } else {
        details.push({
          type: 'redis',
          target,
          expected: '> 0',
          actual: 0,
          success: false,
          message: `새 PubId ${data.uwogl_Nid}를 포함한 키가 존재하지 않습니다`,
        });
      }
    }

    return details;
  }

  /**
   * 키를 인스턴스별로 그룹화
   */
  private groupKeysByInstance(redisKeys: RedisKeyInfo[]): Map<string, RedisKeyInfo[]> {
    const grouped = new Map<string, RedisKeyInfo[]>();

    for (const keyInfo of redisKeys) {
      const instanceName = keyInfo.redisInstance;
      if (!grouped.has(instanceName)) {
        grouped.set(instanceName, []);
      }
      grouped.get(instanceName)!.push(keyInfo);
    }

    return grouped;
  }

  /**
   * AccountId가 포함된 키 찾기
   */
  private async findKeysWithAccountId(
    keyPattern: string,
    accountId: string,
    instanceName: string
  ): Promise<string[]> {
    const searchPattern = this.createSearchPattern(keyPattern, accountId, 'account');
    return await this.redisManager.findKeys(instanceName, searchPattern);
  }

  /**
   * PubId가 포함된 키 찾기
   */
  private async findKeysWithPubId(
    keyPattern: string,
    pubId: string,
    instanceName: string
  ): Promise<string[]> {
    const searchPattern = this.createSearchPattern(keyPattern, pubId, 'pub');
    return await this.redisManager.findKeys(instanceName, searchPattern);
  }

  /**
   * 검색 패턴 생성
   */
  private createSearchPattern(keyPattern: string, id: string, idType: 'account' | 'pub'): string {
    let pattern = keyPattern;

    if (idType === 'account') {
      pattern = pattern
        .replace(/\{?accountId\}?/gi, id)
        .replace(/\{?gnid\}?/gi, id)
        .replace(/\{?account\}?/gi, id);
    } else {
      pattern = pattern
        .replace(/\{?pubId\}?/gi, id)
        .replace(/\{?nid\}?/gi, id)
        .replace(/\{?pub\}?/gi, id);
    }

    if (!pattern.includes('*')) {
      pattern += '*';
    }

    return pattern;
  }

  /**
   * 검증 결과 요약 출력
   */
  printVerificationSummary(result: VerificationResult): void {
    console.log('\n=== 검증 결과 요약 ===');
    console.log(`전체 검증 항목: ${result.checkedItems}`);
    console.log(`통과: ${result.passedItems}`);
    console.log(`실패: ${result.failedItems}`);
    console.log(`성공률: ${((result.passedItems / result.checkedItems) * 100).toFixed(2)}%`);

    if (result.failedItems > 0) {
      console.log('\n=== 실패한 검증 항목 ===');
      const failedDetails = result.details.filter(d => !d.success);
      for (const detail of failedDetails) {
        console.log(`❌ ${detail.target}: ${detail.message}`);
      }
    }

    console.log(`\n전체 검증 결과: ${result.success ? '✅ 성공' : '❌ 실패'}`);
  }
}
