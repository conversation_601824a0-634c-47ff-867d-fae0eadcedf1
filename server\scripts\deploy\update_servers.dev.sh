#!/bin/bash

PACKAGE_NAME=$1
UPDATE_SCRIPT_FILE_NAME=$2
IS_GLOBAL=$3
IS_PVP=$4

if [ -z "$PACKAGE_NAME" ]; then
  PACKAGE_NAME="uwo.servers"
fi

if [ -z "$UPDATE_SCRIPT_FILE_NAME" ]; then
  UPDATE_SCRIPT_FILE_NAME="dev_update"
fi

PACKAGE_ARTIFACT_PATH="package_artifacts/$PACKAGE_NAME"

# Print packaging info.
echo ""
echo "========================================================================="
echo "="
echo "= Updating UWO servers ..."
echo "="
echo "= package_name: $PACKAGE_NAME"
echo "="
echo "========================================================================="
echo ""

# Make sure we're at home.
cd ~

# Stop all servers.
pm2 stop all
# pm2 flush

# Delete old package dir just in case.
rm -rf $PACKAGE_ARTIFACT_PATH

# Uncompress the package.
tar -xf $PACKAGE_NAME.tgz

# Backup old logs.
LOG_BK_DIR_NAME=`date '+%y%m%d-%H.%M.%S'`
mkdir -p uwo_old_logs
mkdir -p uwo_old_logs/$LOG_BK_DIR_NAME
mv ~/uwo/server/node/log ~/uwo_old_logs/$LOG_BK_DIR_NAME/
mv ~/uwo/server/node/glog ~/uwo_old_logs/$LOG_BK_DIR_NAME/
mv ~/uwo/server/node/btn_log ~/uwo_old_logs/$LOG_BK_DIR_NAME/

# Backup the most recent deployment.
rm -rf uwo.old
mv uwo uwo.old

# Use the new package.
mv $PACKAGE_ARTIFACT_PATH uwo

# Update!
pushd uwo/server/scripts
bash $UPDATE_SCRIPT_FILE_NAME.sh $IS_GLOBAL $IS_PVP
popd

# Copy config/local.json5
if [[ -f "uwo.old/server/node/config/local.json5" ]]; then
  cp ~/uwo.old/server/node/config/local.json5 ~/uwo/server/node/config
fi

# Start servers.
pm2 start all

echo "Update successful!"

