// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import { WorldBuffUtil, UserBuffs, BuffSync } from '../../userBuffs';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import * as itemDesc from '../../../cms/itemDesc';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import * as formula from '../../../formula';
import {
  RewardAndPaymentElem,
  RewardAndPaymentSpec,
  RNP_TYPE,
} from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import {
  ActualGain,
  Changes,
  CHANGE_TASK_REASON,
  CHANGE_TASK_RESULT,
  TryData,
  UserChangeTask,
} from '../../UserChangeTask/userChangeTask';
import { opAddItem, opApplyKarmaChange } from '../../UserChangeTask/userChangeOperator';
import { ChoiceBoxDesc } from '../../../cms/choiceBoxDesc';
import mlog from '../../../motiflib/mlog';
import { isGatherRewardType } from '../../../cms/rewardDesc';
import { ItemChange } from '../../userInven';

// ----------------------------------------------------------------------------
// 아이템 소모 후 후처리가 필요한 경우 사용되는 패킷
// ex) consume item -> addBuff, removeBuff etc..
// ----------------------------------------------------------------------------

const rsn = 'use_item';
const add_rsn = null;
const USE_TYPE = itemDesc.USE_TYPE;
const ITEM_TYPE = itemDesc.ITEM_TYPE;

interface ChoiceReward {
  choiceBoxCmsId: number;
  count: number;
}

interface RequestBody {
  cmsId: number;
  targetId: number;
  count: number;
  choiceRewards: ChoiceReward[];
}

interface Response extends BuffSync {
  gain?: ActualGain; // reward 를 통해 얻은 것.
}

interface ChoiceBoxInfo {
  choiceBoxGroup: number;
  choiceRewards: ChoiceReward[];
}

// 아이템 use type 별로 조건 검사하는 함수 관리
const conditionFuncsForUseType = {
  [USE_TYPE.ADD_BUFF]: condAddBuff,
  [USE_TYPE.REMOVE_BUFF]: condRemoveBuff,
};

// 아이템 use type 별로 실행할 함수 관리
const executeFuncsForUseType = {
  [USE_TYPE.ADD_BUFF]: addBuff,
  [USE_TYPE.REMOVE_BUFF]: removeBuff,
};

// use item 확장판. 
// 기존 타입 리팩토링
const itemTypeHandlerFuntions = {
  [ITEM_TYPE.RANDOM_BOX]: handleRandomBox,
  [ITEM_TYPE.KARMA_REDUCTION]: handleKarmaReduction,
  [ITEM_TYPE.CHOICE_BOX]: handleChoiceBox,
}


// ----------------------------------------------------------------------------
// 예비 돛, 노, 키를 사용하여 선박의 손상 버프를 해소 할 수 있는지 검사
// ----------------------------------------------------------------------------
function ensureSpareItemsOfShip(user: User, reqBody: RequestBody) {
  const { cmsId, count, targetId } = reqBody;

  // shipId를 가진 ship이 없을 경우
  const userShip = user.userFleets.getShip(targetId);
  if (!userShip) {
    throw new MError('invalid-ship-id', MErrorCode.INVALID_SHIP_ID, {
      targetId,
    });
  }

  const shipCmsId = userShip.getShipCmsId();
  const shipBlueprintId = cms.Ship[shipCmsId].shipBlueprintId;
  const shipTier = cms.ShipBlueprint[shipBlueprintId].tier;

  // 동, 서양 선박과 상관없이 tier에 따라 소모 갯수 변함.
  const buffId = cms.Item[cmsId].buffId;
  const needItemCount = formula.CalcSpareItemCountForShipRepair(shipTier);
  if (needItemCount !== count) {
    throw new MError('consume-count-is-not-matched', MErrorCode.NOT_MATCHED_COUNT, {
      targetId,
      buffId,
      needItemCount,
      count,
    });
  }
}

function isAddBuffType(itemCms: itemDesc.ItemDesc, ...types) {
  return types.includes(itemCms.type);
}

// ----------------------------------------------------------------------------
function condAddBuff(user: User, reqBody: RequestBody) {
  const { cmsId } = reqBody;
  const itemCms = cms.Item[cmsId];

  if (!isAddBuffType(itemCms, itemDesc.ITEM_TYPE.BAIT, itemDesc.ITEM_TYPE.BUFF_CONSUMABLE)) {
    throw new MError('invalid-cms-id-for-add-buff', MErrorCode.INVALID_CMS_ID_FOR_BUFF, {
      cmsId,
      useType: itemCms.useType,
    });
  }

  if (!cms.WorldBuff[itemCms.buffId]) {
    throw new MError('invalid-buff-id', MErrorCode.INVALID_BUFF_ID, {
      cmsId,
    });
  }
}

// ----------------------------------------------------------------------------
function condRemoveBuff(user: User, reqBody: RequestBody) {
  const { cmsId, targetId } = reqBody;
  const itemCms = cms.Item[cmsId];

  // removeBuff 대상이 되는 item cms id
  const targetCmsIds = {
    [itemDesc.SpareShipSailCmsId]: ensureSpareItemsOfShip,
    [itemDesc.SpareShipWheelCmsId]: ensureSpareItemsOfShip,
    [itemDesc.SpareShipPaddleCmsId]: ensureSpareItemsOfShip,
  };

  const func = targetCmsIds[cmsId];
  if (!func) {
    throw new MError('invalid-condition-func', MErrorCode.INVALID_CONDITION_FUNC, {
      cmsId,
    });
  }
  func(user, reqBody);

  const worldBuff = cms.WorldBuff[itemCms.buffId];
  if (!worldBuff) {
    throw new MError('invalid-buff-id', MErrorCode.INVALID_BUFF_ID, {
      cmsId,
    });
  }

  const bHasBuff = user.userBuffs.hasBuffByCmsId(targetId, itemCms.buffId);

  // 제거할 버프가 존재하지 않을 경우
  if (!bHasBuff) {
    throw new MError('not-has-buff', MErrorCode.NOT_HAS_BUFF, {
      targetId,
      buffId: cms.Item[cmsId].buffId,
      buffTargetType: worldBuff.buffTargetType,
    });
  }
}

// ----------------------------------------------------------------------------
function addBuff(user: User, reqBody: RequestBody, resp: Response) {
  const { cmsId, targetId } = reqBody;

  const userBuffs: UserBuffs = user.userBuffs;
  const curTimeUtc = mutil.curTimeUtc();

  const itemCms = cms.Item[cmsId];
  const worldBuffCms = cms.WorldBuff[itemCms.buffId];
  const wbNub = WorldBuffUtil.makeNub(
    worldBuffCms,
    targetId,
    cmsEx.WorldBuffSourceType.USER_ITEM,
    cmsId,
    curTimeUtc
  );

  if (wbNub) {
    userBuffs.tempUpdateBuffWithItem(wbNub, user, resp);
  }

  // 떡밥 소모 glog
  if (itemCms.type === itemDesc.ITEM_TYPE.BAIT) {
    let glogNation = null;
    if (user.nationCmsId) {
      const nationCms = cms.Nation[user.nationCmsId];
      glogNation = nationCms.name;
    }

    user.glog('fishing', {
      rsn,
      add_rsn,
      flag: 1, // 떡밥 뿌리기
      region_id: null,
      coordinates: null,
      season: null,
      fishery_type: null,
      bait_name: displayNameUtil.getItemDisplayName(itemCms),
      bait_id: cmsId,
      bait_uid: null,
      rod_name: null,
      rod_id: null,
      rod_uid: null,
      is_success: null,
      fishing_result: null,
      discovery_name: null,
      discovery_id: null,
      discovery_grade: null,
      is_first: null,
      reward_data: null,
      user_nation: glogNation,
      discovery_size: null, // 어종 크기
    });
  }

  // TODO. 소모성 버프아이템 로그
}

// ----------------------------------------------------------------------------
function removeBuff(user: User, reqBody: RequestBody, resp: Response) {
  const { cmsId, targetId } = reqBody;
  const userBuffs: UserBuffs = user.userBuffs;
  const itemCms = cms.Item[cmsId];

  userBuffs.removeBuff(itemCms.buffId, targetId, user, rsn, add_rsn, resp);
}

// ----------------------------------------------------------------------------
// item_type 분기 핸들러
// ----------------------------------------------------------------------------
function handleRandomBox(reqBody: RequestBody, rewardIds: number[], consumeItems: { cmsId: number; count }[]) {
  const {cmsId, count} = reqBody;
  const itemCms = cms.Item[cmsId];
  if (count > cms.Define.RandomRewardBoxMaxCount) {
    throw new MError('Too-many-use-count', MErrorCode.INVALID_ITEM, {
      reqBody,
    });
  }
  if (!itemCms.gachaRewardId) {
    throw new MError('not-found-gacha-reward-id', MErrorCode.TRY_USE_ITEM, {
      cmsId,
    });
  }

  for (let cnt = 0; cnt < count; cnt++) {
    rewardIds.push(itemCms.gachaRewardId);
  }

  if (itemCms.unlockKey) {
    consumeItems.push({
      cmsId: itemCms.unlockKey.Item,
      count: count * itemCms.unlockKey.Count,
    });
  }
}

function handleKarmaReduction(user: User, reqBody: RequestBody) {
  const {cmsId, count} = reqBody;
  const itemCms = cms.Item[cmsId];
  if (user.getKarma(mutil.curTimeUtc()) === 0) {
    throw new MError(
      'karma-cannot-be-decreased-as-it-is-already-zero',
      MErrorCode.TRY_USE_ITEM,
      {
        cmsId,
      }
    );
  }
  return user.getKarmaChangeWithSubtract(mutil.curTimeUtc(), itemCms.consumeValue);
}

function handleChoiceBox(reqBody: RequestBody, choiceRewards: ChoiceReward[]) {
  const {cmsId, count} = reqBody;
  const itemCms = cms.Item[cmsId];
  const choiceBoxGroup = cmsEx.getChoiceBoxGroup(itemCms.choiceBoxGroup);
  let bIsCheckMaxCount = false;
  _.forOwn(choiceBoxGroup, (choiceBoxCms) => {
    if (!isGatherRewardType(choiceBoxCms.rewardType)) {
      bIsCheckMaxCount = true;
    }
  });

  if (bIsCheckMaxCount && count > cms.Define.ChoiceRewardBoxMaxCount) {
    throw new MError('Too-many-use-count', MErrorCode.INVALID_ITEM, {
      reqBody,
    });
  }

  if (!choiceRewards || _.isEmpty(choiceRewards)) {
    throw new MError(
      'invalid-request-body-choice-box-group',
      MErrorCode.INVALID_REQ_BODY_CHOICE_BOX_GROUP,
      { reqBody }
    );
  }

  return { choiceBoxGroup: itemCms.choiceBoxGroup, choiceRewards };
}

// ----------------------------------------------------------------------------
export class Cph_Common_UseItem implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;

    const { cmsId, count, choiceRewards } = reqBody;

    // use_item 패킷 요청 시 서버 다운 원인 분석용
    mlog.info('Use item begin', {
      userId: user.userId,
      reqBody,
    });

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const itemCms = cms.Item[cmsId];
    let totalChoiceRewardCount = count;
    if (itemCms.type === itemDesc.ITEM_TYPE.CHOICE_BOX) {
      totalChoiceRewardCount = choiceRewards.reduce((acc, elem) => acc + elem.count, 0);
    }

    if (!itemCms || !count || count <= 0 || totalChoiceRewardCount !== count) {
      throw new MError(
        'invalid-request-body-use-item',
        MErrorCode.INVALID_REQ_BODY_USE_ITEM_CMS_ID,
        {
          reqBody,
        }
      );
    }

    // useType 별 조건 검사
    const useType = itemCms.useType;
    const condFunc = conditionFuncsForUseType[useType];
    if (condFunc) {
      condFunc(user, reqBody);
    }

    const rewardIds: number[] = [];
    let karmaChange: KarmaChange;
    let choiceBoxInfo: ChoiceBoxInfo = { choiceBoxGroup: 0, choiceRewards: []};

    const consumeItems: { cmsId: number; count }[] = [];
    consumeItems.push({ cmsId, count });

    const handlerFunc = itemTypeHandlerFuntions[itemCms.type];
    if (handlerFunc) {
      switch (itemCms.type) {
        case ITEM_TYPE.RANDOM_BOX:
          handlerFunc(reqBody, rewardIds, consumeItems);
          break;
        case ITEM_TYPE.KARMA_REDUCTION:
          karmaChange = handlerFunc(user, reqBody);
          break;
        case ITEM_TYPE.CHOICE_BOX:
          choiceBoxInfo = handlerFunc(reqBody, choiceRewards);
          break; 
      }
    }
    
    const changeTask = new UserChangeTask(
      user,
      CHANGE_TASK_REASON.USE_ITEM,
      new UseItemSpec(consumeItems, rewardIds, karmaChange, choiceBoxInfo)
    );

    const res = changeTask.trySpec();
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      throw new MError('failed-to-use-item', MErrorCode.TRY_USE_ITEM, {
        consumeItems,
        rewardIds,
        res,
      });
    }

    // use_item 패킷 요청 시 서버 다운 원인 분석용
    mlog.info('Use item trySpec', {
      userId: user.userId,
      consumeItems,
      rewardIds,
      karmaChange,
      choiceBoxInfo,
    });

    return changeTask.apply().then((sync) => {
      // use_item 패킷 요청 시 서버 다운 원인 분석용
      mlog.info('Use item apply', {
        userId: user.userId,
        sync,
      });

      const resp: Response = {
        sync,
      };
      // UseType에 의한 후처리 함수 실행 (addBuff, removeBuff 등)
      const executeFunc = executeFuncsForUseType[useType];
      if (executeFunc) {
        executeFunc(user, reqBody, resp);
      }

      resp.gain = changeTask.getActualGain();
      return user.sendJsonPacket<Response>(packet.seqNum, packet.type, resp);
    });
  }
}

class UseItemSpec extends RewardAndPaymentSpec {
  private consumeItems: { cmsId: number; count: number }[];
  private karmaChange: KarmaChange;

  constructor(
    consumeItems: { cmsId: number; count: number }[],
    rewardIds: number[],
    karmaChange: KarmaChange,
    choiceBox: ChoiceBoxInfo
  ) {
    const rnpElems: RewardAndPaymentElem[] = [];
    if (rewardIds && rewardIds.length > 0) {
      for (const cmsId of rewardIds) {
        // ! item.type=RANDOM_BOX 오픈: bIsBound: false, bAllowExceed: true 처리
        rnpElems.push({
          type: RNP_TYPE.REWARD,
          cmsId,
          bIsNotPermitAddToHardCapLimitLine: true,
          bIsAccum: true,
          bIsBound: false,
          bAllowExceed: true,
        });
      }
    }

    if (choiceBox) {
      for (const reward of choiceBox.choiceRewards) {
        const choiceBoxCms = cms.ChoiceBox[reward.choiceBoxCmsId];
        if (isGatherRewardType(choiceBoxCms.rewardType)) {
          rnpElems.push({
            type: RNP_TYPE.CHOICE,
            amountRatio: reward.count,
            cmsId: choiceBox.choiceBoxGroup,
            choiceBoxCmsId: reward.choiceBoxCmsId,
            bIsNotPermitAddToHardCapLimitLine: true,
            bIsAccum: true,
            bIsBound: true,
            bAllowExceed: true,
          });
        } else {
          for (let i = 0; i < reward.count; i++) {
            rnpElems.push({
              type: RNP_TYPE.CHOICE,
              cmsId: choiceBox.choiceBoxGroup,
              choiceBoxCmsId: reward.choiceBoxCmsId,
              bIsNotPermitAddToHardCapLimitLine: true,
              bIsAccum: true,
              bIsBound: true,
              bAllowExceed: true,
            });
          }
        }
      }
    }

    super(rnpElems);

    this.consumeItems = consumeItems;
    this.karmaChange = karmaChange;
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const taskResults: CHANGE_TASK_RESULT[] = [];
    for (let i = 0; i < this.consumeItems.length; i++) {
      const consumeItem: { cmsId: number; count: number } = this.consumeItems[i];
      assert(consumeItem.count > 0);

      taskResults.push(
        opAddItem(
          user,
          tryData,
          changes,
          consumeItem.cmsId,
          -consumeItem.count,
          false /** bAllowInven */,
          false /** bAllowAddToLimitIfExcceded */,
          undefined,
          true,
          true
        )
      );
    }

    if (this.karmaChange) {
      taskResults.push(opApplyKarmaChange(user, tryData, changes, this.karmaChange));
    }

    for (const res of taskResults) {
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }
    // 필요한 조각 차감 후에 보상 적용
    return super.accumulate(user, tryData, changes);
  }
}
