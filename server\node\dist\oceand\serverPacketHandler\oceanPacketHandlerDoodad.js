"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const typedi_1 = __importDefault(require("typedi"));
const fleetManager_1 = require("../fleetManager");
const cms_1 = __importDefault(require("../../cms"));
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const mutil = __importStar(require("../../motiflib/mutil"));
const oceanDoodadEntity_1 = require("../oceanDoodadEntity");
const oceanUserLocalFixedDoodadSpawnEntry_1 = require("../oceanUserLocalFixedDoodadSpawnEntry");
const locationGenerator_1 = require("../locationGenerator");
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
router.on(protocol_1.Protocol.LB2OC_NTF_SPAWN_OCEAN_LOCAL_DOODAD, async (req, res) => {
    const userId = req.userId;
    const doodadCmsId = req.doodadCmsId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        mlog_1.default.warn('User not found in this oceand!', {
            userId,
            doodadCmsId,
        });
        return;
    }
    const oceanDoodadCms = cms_1.default.OceanDoodad[doodadCmsId];
    if (!oceanDoodadCms) {
        mlog_1.default.warn('invalid doodadCmsId!', {
            userId,
            doodadCmsId,
        });
        return;
    }
    // 빈 셀을 찾아서 스폰시키는 방식은 못찾을 가능성이 항상 존재하므로 에러처리하지 않음
    const oceanDoodad = userFleet.spawnLocalDoodadByCells(oceanDoodadCms);
    if (!oceanDoodad) {
        mlog_1.default.info('[LB2OC_NTF_SPAWN_OCEAN_LOCAL_DOODAD] NONE ocean local doodad created by empty cells', {
            userId,
            doodadCmsId,
        });
        return;
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_SPAWN_CHECK_SUCCESS, async (req, res) => {
    const userId = req.userId;
    const doodadCmsIds = req.doodadCmsIds;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        mlog_1.default.warn('User not found in this oceand!', {
            userId,
            doodadCmsIds,
        });
        return;
    }
    for (const doodadCmsId of doodadCmsIds) {
        const oceanDoodadCms = cms_1.default.OceanDoodad[doodadCmsId];
        if (!oceanDoodadCms) {
            mlog_1.default.warn('invalid doodadCmsId!', {
                userId,
                doodadCmsId,
            });
            continue;
        }
        const entry = userFleet.getLocalFixedDoodadSpawnEntry(doodadCmsId);
        if (!entry) {
            mlog_1.default.error('LocalFixedDoodadSpawnEntry is not exist', {
                userId,
                doodadCmsId,
            });
            continue;
        }
        if (oceanUserLocalFixedDoodadSpawnEntry_1.LOCAL_FIXED_DOODAD_SPAWN_STATE.SPAWNING !== entry.getSpawnState()) {
            mlog_1.default.warn('local fixed  doodad spawnState is not spawning', {
                userId,
                doodadCmsId,
                spawnState: entry.getSpawnState(),
            });
            continue;
        }
        const location = locationGenerator_1.SpawnLocationGenerator[oceanDoodadCms.radiusType](userFleet.getCurrentZone(), oceanDoodadCms.latitude, oceanDoodadCms.longitude, oceanDoodadCms.radius);
        const param = {
            baseOceanDoodadParam: {
                doodadCmsId,
                location,
                degrees: 0,
            },
            spawndCellId: '',
            timeUtc: mutil.curTimeUtc(),
            spawnEntry: entry,
            type: oceanDoodadEntity_1.LOCAL_DOODAD_TYPE.NORMAL,
        };
        const oceanDoodad = userFleet.spawnLocalFixedDoodad(param);
        if (!oceanDoodad) {
            mlog_1.default.warn('can not create ocean local fixed doodad', {
                userId,
                doodadCmsId,
            });
            continue;
        }
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_LOCAL_FIXED_OCEAN_DOODAD_DESPAWN, async (req, res) => {
    const userId = req.userId;
    const doodadCmsIds = req.doodadCmsIds;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        mlog_1.default.warn('User not found in this oceand!', {
            userId,
            doodadCmsIds,
        });
        return;
    }
    for (const doodadCmsId of doodadCmsIds) {
        const oceanDoodadCms = cms_1.default.OceanDoodad[doodadCmsId];
        if (!oceanDoodadCms) {
            mlog_1.default.warn('invalid doodadCmsId!', {
                userId,
                doodadCmsId,
            });
            continue;
        }
        const entry = userFleet.getLocalFixedDoodadSpawnEntry(doodadCmsId);
        if (!entry) {
            mlog_1.default.error('LocalFixedDoodadSpawnEntry is not exist', {
                userId,
                doodadCmsId,
            });
            continue;
        }
        if (oceanUserLocalFixedDoodadSpawnEntry_1.LOCAL_FIXED_DOODAD_SPAWN_STATE.SPAWNED !== entry.getSpawnState()) {
            mlog_1.default.warn('local fixed  doodad spawnState is not spawned', {
                userId,
                doodadCmsId,
                spawnState: entry.getSpawnState(),
            });
            continue;
        }
        const spawnedDoodads = userFleet.findSpawnFixedDoodadByCmsId(doodadCmsId);
        spawnedDoodads.forEach((doodad) => userFleet.despawnLocalDoodad(doodad, true));
    }
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerDoodad.js.map