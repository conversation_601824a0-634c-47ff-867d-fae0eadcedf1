#!/bin/bash

CWD="$(dirname "$0")"

if [ ! -f $CWD/config.sh ]; then
	echo "Please create a custom 'config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/query.sh ]; then
	echo "Please create a custom 'query.sh' file at '$CWD' directory."
	exit 1
fi



if [ -z "$1" ]
  then
    echo "No argument source_db_name"
    exit 1
fi

if [ -z "$2" ]
  then
    echo "No argument target_db_name"
    exit 1
fi

source $CWD/config.sh
source $CWD/query.sh

SOURCE_DB_NAME=$1
TARGET_DB_NAME=$2
# select * 와일드 카드 변경 안되게 처리
set -f

main() 
{
  echo "===== SHOW TABLES"
  q="SHOW TABLES;"
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${SOURCE_DB_NAME}"  "${q}"


  echo "===== COPY TABLES"
  tableList=(${QUERY_RESULT})
  for tableName in ${tableList[@]}
  do
    if [[ "$tableName" == "u_"* &&  "$tableName" != "u_arena_grade_rewards" ]]
    then
      q="
        CREATE TABLE ${TARGET_DB_NAME}.${tableName} like ${tableName};
      "
      echo $q
      queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${SOURCE_DB_NAME}"  "${q}"
    fi
  done

}



main "$@"; exit

