CREATE PROCEDURE `mp_u_manufacture_exp_level_update`(
  IN inUserId INT,
  IN inCastingExp INT,
  IN inCastingLevel TINYINT,
  IN inCookingExp INT,
  IN inCookingLevel TINYINT,
  IN inSewingExp INT,
  IN inSewingLevel TINYINT,
  IN inHandmadeExp INT,
  IN inHandmadeLevel TINYINT,
  IN inMedicineExp INT,
  IN inMedicineLevel TINYINT
)
label_body:BEGIN
  -- INSERT ... ON DUPLICATE KEY UPDATE 사용으로 한 번의 쿼리로 처리
  INSERT INTO u_manufacture_exp (
    userId,
    castingExp, castingLevel,
    cookingExp, cookingLevel,
    sewingExp, sewingLevel,
    handmadeExp, handmadeLevel,
    medicineExp, medicineLevel
  ) VALUES (
    inUserId,
    COALESCE(inCastingExp, 0), COALESCE(inCastingLevel, 1),
    COALESCE(inCookingExp, 0), COALESCE(inCookingLevel, 1),
    COALESCE(inSewingExp, 0), COALESCE(inSewingLevel, 1),
    COALESCE(inHandmadeExp, 0), COALESCE(inHandmadeLevel, 1),
    COALESCE(inMedicineExp, 0), COALESCE(inMedicineLevel, 1)
  )
  ON DUPLICATE KEY UPDATE
    castingExp = CASE WHEN inCastingExp IS NOT NULL THEN VALUES(castingExp) ELSE castingExp END,
    castingLevel = CASE WHEN inCastingLevel IS NOT NULL THEN VALUES(castingLevel) ELSE castingLevel END,
    cookingExp = CASE WHEN inCookingExp IS NOT NULL THEN VALUES(cookingExp) ELSE cookingExp END,
    cookingLevel = CASE WHEN inCookingLevel IS NOT NULL THEN VALUES(cookingLevel) ELSE cookingLevel END,
    sewingExp = CASE WHEN inSewingExp IS NOT NULL THEN VALUES(sewingExp) ELSE sewingExp END,
    sewingLevel = CASE WHEN inSewingLevel IS NOT NULL THEN VALUES(sewingLevel) ELSE sewingLevel END,
    handmadeExp = CASE WHEN inHandmadeExp IS NOT NULL THEN VALUES(handmadeExp) ELSE handmadeExp END,
    handmadeLevel = CASE WHEN inHandmadeLevel IS NOT NULL THEN VALUES(handmadeLevel) ELSE handmadeLevel END,
    medicineExp = CASE WHEN inMedicineExp IS NOT NULL THEN VALUES(medicineExp) ELSE medicineExp END,
    medicineLevel = CASE WHEN inMedicineLevel IS NOT NULL THEN VALUES(medicineLevel) ELSE medicineLevel END;
    
  SELECT ROW_COUNT() as affectedRows, 
         CASE WHEN ROW_COUNT() = 1 THEN 'INSERT' ELSE 'UPDATE' END as operation;
END
