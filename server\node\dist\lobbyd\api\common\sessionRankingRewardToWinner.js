"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const proto = __importStar(require("../../../proto/lobby/proto"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const userManager_1 = require("../../userManager");
const typedi_1 = require("typedi");
const mutil_1 = require("../../../motiflib/mutil");
module.exports = async (req, res) => {
    mlog_1.default.debug('api-/common/sessionRankingRewardToWinner req -', req.body);
    const { packet } = req.body;
    const userId = packet.userId;
    const userManager = typedi_1.Container.get(userManager_1.UserManager);
    const user = userManager.getUserByUserId(userId);
    if (!user) {
        mlog_1.default.warn('not-found-user-at-friendNotification', {
            userId,
        });
        return;
    }
    const userTitles = user.userTitles;
    const newUserTitle = {
        cmsId: packet.titleCmsId,
        expiredTimeUtc: packet.expiredTimeUtc,
        isEquipped: packet.isEquipped,
    };
    const now = (0, mutil_1.curTimeUtc)();
    let buffSync = { sync: {} };
    if (newUserTitle.isEquipped) {
        userTitles.equipUserTitle(user, newUserTitle, now, buffSync);
        userTitles.notifyCurTitleChanged(user, now);
    }
    else {
        userTitles.setUserTitle(newUserTitle, buffSync);
    }
    user.sendJsonPacket(0, proto.Common.UPDATE_USER_TITLE_SC, buffSync);
    res.end();
};
//# sourceMappingURL=sessionRankingRewardToWinner.js.map