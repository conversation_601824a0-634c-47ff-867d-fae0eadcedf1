"use strict";
// ------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ------------------------------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.exec = void 0;
const proto = __importStar(require("../../proto/lobby/proto"));
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const merror_1 = require("../../motiflib/merror");
const hello_1 = require("./auth/hello");
const enterWorld_1 = require("./auth/enterWorld");
const changeName_1 = require("./auth/changeName");
const completePrologue_1 = require("./auth/completePrologue");
const revoke_1 = require("./auth/revoke");
const gameGuardCheck_1 = require("./auth/gameGuardCheck");
const appGuardCheck_1 = require("./auth/appGuardCheck");
const enter_1 = require("./town/enter");
const loadComplete_1 = require("./town/loadComplete");
const move_1 = require("./town/move");
const queryMate_1 = require("./town/queryMate");
const enterBuilding_1 = require("./town/enterBuilding");
const leaveBuilding_1 = require("./town/leaveBuilding");
const bankDeposit_1 = require("./town/bankDeposit");
const bankWithdraw_1 = require("./town/bankWithdraw");
const bankDepositInstallmentSavings_1 = require("./town/bankDepositInstallmentSavings");
const bankWithdrawInstallmentSavings_1 = require("./town/bankWithdrawInstallmentSavings");
const bankBuyInsurance_1 = require("./town/bankBuyInsurance");
const departChangeShipFleetFormation_1 = require("./town/departChangeShipFleetFormation");
const departBuySupplies_1 = require("./town/departBuySupplies");
const departDepart_1 = require("./town/departDepart");
const shipyardCreateShip_1 = require("./town/shipyardCreateShip");
const shipyardRepair_1 = require("./town/shipyardRepair");
const shipyardSellShip_1 = require("./town/shipyardSellShip");
const shipyardChangeBlueprintSlot_1 = require("./town/shipyardChangeBlueprintSlot");
const pubDraftSailor_1 = require("./town/pubDraftSailor");
const pubGetSyncData_1 = require("./town/pubGetSyncData");
const pubRecruitMate_1 = require("./town/pubRecruitMate");
const pubBuyDrink_1 = require("./town/pubBuyDrink");
const goverInvest_1 = require("./town/goverInvest");
const goverGetSyncData_1 = require("./town/goverGetSyncData");
const tradeGetSyncData_1 = require("./town/tradeGetSyncData");
const tradeBuy_1 = require("./town/tradeBuy");
const tradeSell_1 = require("./town/tradeSell");
const tradeGetAllSessionPricePercents_1 = require("./town/tradeGetAllSessionPricePercents");
const religionUpdateBuff_1 = require("./town/religionUpdateBuff");
const shopBuyEx_1 = require("./town/shopBuyEx");
const shopGetTownBlackMarket_1 = require("./town/shopGetTownBlackMarket");
const shopSell_1 = require("./town/shopSell");
const shopResetTownBlackMarket_1 = require("./town/shopResetTownBlackMarket");
const receiveInsurance_1 = require("./town/receiveInsurance");
const changeShipSlot_1 = require("./town/changeShipSlot");
const changeShipSlots_1 = require("./town/changeShipSlots");
const unionResetRequest_1 = require("./town/unionResetRequest");
const pubGift_1 = require("./town/pubGift");
const tradeTryBeginNego_1 = require("./town/tradeTryBeginNego");
const tradeTryNego_1 = require("./town/tradeTryNego");
const noviceSupply_1 = require("./town/noviceSupply");
const palaceGetRoyalOrder_1 = require("./town/palaceGetRoyalOrder");
const palaceRejectRoyalOrder_1 = require("./town/palaceRejectRoyalOrder");
const manticFortune_1 = require("./town/manticFortune");
const arrestUserChoice_1 = require("./town/arrestUserChoice");
const collectorReportDiscoveries_1 = require("./town/collectorReportDiscoveries");
const collectorContract_1 = require("./town/collectorContract");
const collectorGetRank_1 = require("./town/collectorGetRank");
const tradeResetBought_1 = require("./town/tradeResetBought");
const collectorReportWorldMapTiles_1 = require("./town/collectorReportWorldMapTiles");
const shipyardBuy_1 = require("./town/shipyardBuy");
const shipyardEnchant_1 = require("./town/shipyardEnchant");
const shipyardChoiceEnchantResult_1 = require("./town/shipyardChoiceEnchantResult");
const shipyardDismantleShip_1 = require("./town/shipyardDismantleShip");
const updateSocialAniPersist_1 = require("./town/updateSocialAniPersist");
const showSocialAniInstant_1 = require("./town/showSocialAniInstant");
const showEmoticonInstant_1 = require("./town/showEmoticonInstant");
const manticSwapPiece_1 = require("./town/manticSwapPiece");
const goverChangeMayorTax_1 = require("./town/goverChangeMayorTax");
const pubStaffNomination_1 = require("./town/pubStaffNomination");
const pubStaffTalking_1 = require("./town/pubStaffTalking");
const pubStaffBoasting_1 = require("./town/pubStaffBoasting");
const pubStaffGift_1 = require("./town/pubStaffGift");
const pubStaffCall_1 = require("./town/pubStaffCall");
const pubStaffGetSyncData_1 = require("./town/pubStaffGetSyncData");
const goverGetLastWeekRank_1 = require("./town/goverGetLastWeekRank");
const pubReduceRecruitNegoWaitTime_1 = require("./town/pubReduceRecruitNegoWaitTime");
const pubResetRecruitNegoWaitTime_1 = require("./town/pubResetRecruitNegoWaitTime");
const pubSponsorMyMate_1 = require("./town/pubSponsorMyMate");
const pubReduceMyMateSponsorWaitTime_1 = require("./town/pubReduceMyMateSponsorWaitTime");
const pubResetMyMateSponsorWaitTime_1 = require("./town/pubResetMyMateSponsorWaitTime");
const arrive_1 = require("./ocean/arrive");
const loadComplete_2 = require("./ocean/loadComplete");
const enter_2 = require("./ocean/enter");
const move_2 = require("./ocean/move");
const inspectTown_1 = require("./ocean/inspectTown");
const beginAutoSailing_1 = require("./ocean/beginAutoSailing");
const endAutoSailing_1 = require("./ocean/endAutoSailing");
const updateAutoSailing_1 = require("./ocean/updateAutoSailing");
const resolveDisaster_1 = require("./ocean/resolveDisaster");
const encountUserChoice_1 = require("./ocean/encountUserChoice");
const encountByUser_1 = require("./ocean/encountByUser");
const emergencyRecoverShips_1 = require("./ocean/emergencyRecoverShips");
const gameOverResurrect_1 = require("./ocean/gameOverResurrect");
const getUserTownState_1 = require("./ocean/getUserTownState");
const encountEnd_1 = require("./ocean/encountEnd");
const zoomInNpc_1 = require("./ocean/zoomInNpc");
const zoomInUser_1 = require("./ocean/zoomInUser");
const revealWorldMapTile_1 = require("./ocean/revealWorldMapTile");
const discoverVillage_1 = require("./ocean/discoverVillage");
const addQuestionPlace_1 = require("./ocean/addQuestionPlace");
const activateOceanDoodad_1 = require("./ocean/activateOceanDoodad");
const landEnter_1 = require("./ocean/landEnter");
const landLeave_1 = require("./ocean/landLeave");
const landExplore_1 = require("./ocean/landExplore");
const landStart_1 = require("./ocean/landStart");
const inspectVillage_1 = require("./ocean/inspectVillage");
const villageEnter_1 = require("./ocean/villageEnter");
const villageLeave_1 = require("./ocean/villageLeave");
const villageGift_1 = require("./ocean/villageGift");
const villageDraftSailor_1 = require("./ocean/villageDraftSailor");
const villageExplore_1 = require("./ocean/villageExplore");
const offlineSailingMoveDelegateStart_1 = require("./ocean/offlineSailingMoveDelegateStart");
const showEmoticonInstant_2 = require("./ocean/showEmoticonInstant");
const fishingStart_1 = require("./ocean/fishingStart");
const fishingFighting_1 = require("./ocean/fishingFighting");
const fishingEnd_1 = require("./ocean/fishingEnd");
const itemResurrect_1 = require("./ocean/itemResurrect");
const battleAction_1 = require("./battle/battleAction");
const battleEnd_1 = require("./battle/battleEnd");
const battleEndRaid_1 = require("./battle/battleEndRaid");
const battleEndChallenge_1 = require("./battle/battleEndChallenge");
const battleLoadComplete_1 = require("./battle/battleLoadComplete");
const battleResume_1 = require("./battle/battleResume");
const battleStart_1 = require("./battle/battleStart");
const battleStartChallenge_1 = require("./battle/battleStartChallenge");
const battleLose_1 = require("./battle/battleLose");
const battleStartArena_1 = require("./battle/battleStartArena");
const battleCancel_1 = require("./battle/battleCancel");
const battleQuestResumeBlock_1 = require("./battle/battleQuestResumeBlock");
const duelStart_1 = require("./duel/duelStart");
const duelAction_1 = require("./duel/duelAction");
const duelEnd_1 = require("./duel/duelEnd");
const duelResume_1 = require("./duel/duelResume");
const createFirstMate_1 = require("./common/createFirstMate");
const equipMateEquipment_1 = require("./common/equipMateEquipment");
const queryNation_1 = require("./common/queryNation");
const selectNation_1 = require("./common/selectNation");
const changeNation_1 = require("./common/changeNation");
const getUserLightInfos_1 = require("./common/getUserLightInfos");
const changeCompanyJob_1 = require("./common/changeCompanyJob");
const changeShipName_1 = require("./common/changeShipName");
const relocateSailor_1 = require("./common/relocateSailor");
const removeItem_1 = require("./common/removeItem");
const removeMateEquipment_1 = require("./common/removeMateEquipment");
const expandInventorySlot_1 = require("./common/expandInventorySlot");
const receiveMails_1 = require("./common/receiveMails");
const questAccept_1 = require("./common/questAccept");
const questStartBlock_1 = require("./common/questStartBlock");
const questEntrust_1 = require("./common/questEntrust");
const questEndBlock_1 = require("./common/questEndBlock");
const questRegStore_1 = require("./common/questRegStore");
const deleteMails_1 = require("./common/deleteMails");
const readMails_1 = require("./common/readMails");
const removeCargo_1 = require("./common/removeCargo");
const reloadCargo_1 = require("./common/reloadCargo");
const getTownTradePricePercents_1 = require("./common/getTownTradePricePercents");
const questDrop_1 = require("./common/questDrop");
const useItem_1 = require("./common/useItem");
const questGoto_1 = require("./common/questGoto");
const lockShipSlots_1 = require("./common/lockShipSlots");
const equipMateEquipments_1 = require("./common/equipMateEquipments");
const buyAdmiral_1 = require("./common/buyAdmiral");
const recoverInjury_1 = require("./common/recoverInjury");
const increaseLoyalty_1 = require("./common/increaseLoyalty");
const meetMates_1 = require("./common/meetMates");
const talkMates_1 = require("./common/talkMates");
const setCargoLoadPreset_1 = require("./common/setCargoLoadPreset");
const questOperateReg_1 = require("./common/questOperateReg");
const recoverShips_1 = require("./common/recoverShips");
const resetTask_1 = require("./common/resetTask");
const receiveAchievementReward_1 = require("./common/receiveAchievementReward");
const receiveTaskReward_1 = require("./common/receiveTaskReward");
const getTownNationSharePoints_1 = require("./common/getTownNationSharePoints");
const completeTaskImmediately_1 = require("./common/completeTaskImmediately");
const receiveTaskCategoryReward_1 = require("./common/receiveTaskCategoryReward");
const receiveAchievementPointReward_1 = require("./common/receiveAchievementPointReward");
const receiveHotTime_1 = require("./common/receiveHotTime");
const buyTaxFreePermit_1 = require("./common/buyTaxFreePermit");
const getWorldMapTownInfo_1 = require("./common/getWorldMapTownInfo");
const consumeQuestEnergy_1 = require("./common/consumeQuestEnergy");
const expandRequestSlot_1 = require("./common/expandRequestSlot");
const getNationTowns_1 = require("./common/getNationTowns");
const removeExpiredRequestSlot_1 = require("./common/removeExpiredRequestSlot");
const cashShopGetProducts_1 = require("./common/cashShopGetProducts");
const cashShopBuyWithoutPurchase_1 = require("./common/cashShopBuyWithoutPurchase");
const getRegionOccupations_1 = require("./common/getRegionOccupations");
const getUserLightInfoByName_1 = require("./common/getUserLightInfoByName");
const cashShopReceiveGachaBoxGuarantee_1 = require("./common/cashShopReceiveGachaBoxGuarantee");
const getUserLightInfosOnlyIsOnline_1 = require("./common/getUserLightInfosOnlyIsOnline");
const unlockShipCustomizing_1 = require("./common/unlockShipCustomizing");
const customizeShip_1 = require("./common/customizeShip");
const chatInit_1 = require("./common/chatInit");
const chatJoinChannel_1 = require("./common/chatJoinChannel");
const mateStartAwaken_1 = require("./common/mateStartAwaken");
const mateStartLearnPassive_1 = require("./common/mateStartLearnPassive");
const questItemSetQuest_1 = require("./common/questItemSetQuest");
const questItemUse_1 = require("./common/questItemUse");
const attendance_1 = require("./common/attendance");
const mateEquipPassives_1 = require("./common/mateEquipPassives");
const questSetPause_1 = require("./common/questSetPause");
const customizeMateEquip_1 = require("./common/customizeMateEquip");
const unlockMateEquipColor_1 = require("./common/unlockMateEquipColor");
const bubbleEventAction_1 = require("./common/bubbleEventAction");
const shipSlotItemSell_1 = require("./common/shipSlotItemSell");
const shipSlotItemRemove_1 = require("./common/shipSlotItemRemove");
const setGameOptionPushNotification_1 = require("./common/setGameOptionPushNotification");
const getGameOptionPushNotification_1 = require("./common/getGameOptionPushNotification");
const toogleEncountShieldActivation_1 = require("./common/toogleEncountShieldActivation");
const battleFormationAcquire_1 = require("./common/battleFormationAcquire");
const battleFormationChange_1 = require("./common/battleFormationChange");
const queryNearestTown_1 = require("./common/queryNearestTown");
const useWorldSkill_1 = require("./common/useWorldSkill");
const mateCompleteAwaken_1 = require("./common/mateCompleteAwaken");
const mateCompleteAwakenImmediately_1 = require("./common/mateCompleteAwakenImmediately");
const mateReduceAwakenTime_1 = require("./common/mateReduceAwakenTime");
const mateCompleteLearnPassive_1 = require("./common/mateCompleteLearnPassive");
const mateCompleteLearnPassiveImmediately_1 = require("./common/mateCompleteLearnPassiveImmediately");
const mateReduceLearnPassiveTime_1 = require("./common/mateReduceLearnPassiveTime");
const updateAdjutantDelegationConfig_1 = require("./common/updateAdjutantDelegationConfig");
const questStateChange_1 = require("./common/questStateChange");
const receiveBattleReward_1 = require("./battleReward/receiveBattleReward");
const leave_1 = require("./battleReward/leave");
const enter_3 = require("./battleReward/enter");
const loadComplete_3 = require("./battleReward/loadComplete");
const openBox_1 = require("./landExploreReward/openBox");
const receiveEnter_1 = require("./landExploreReward/receiveEnter");
const receiveLeave_1 = require("./landExploreReward/receiveLeave");
const receive_1 = require("./landExploreReward/receive");
const ping_1 = require("./etc/ping");
const setClientBackgroundState_1 = require("./etc/setClientBackgroundState");
const teleportTown_1 = require("./admin/teleportTown");
const teleportToUser_1 = require("./admin/teleportToUser");
const giveMateEquipment_1 = require("./dev/giveMateEquipment");
const addPoint_1 = require("./dev/addPoint");
const addMate_1 = require("./dev/addMate");
const setShipSailor_1 = require("./dev/setShipSailor");
const addShip_1 = require("./dev/addShip");
const updateNation_1 = require("./dev/updateNation");
const updateNationIntimacy_1 = require("./dev/updateNationIntimacy");
const setNation_1 = require("./dev/setNation");
const updateReputation_1 = require("./dev/updateReputation");
const invokeEvent_1 = require("./dev/invokeEvent");
const invokeNationIntimacyUpdateJob_1 = require("./dev/invokeNationIntimacyUpdateJob");
const removePoint_1 = require("./dev/removePoint");
const setPoint_1 = require("./dev/setPoint");
const setShipDurability_1 = require("./dev/setShipDurability");
const removeAllShipCargos_1 = require("./dev/removeAllShipCargos");
const setFirstShipCargo_1 = require("./dev/setFirstShipCargo");
const addItem_1 = require("./dev/addItem");
const setDevelopmentLevel_1 = require("./dev/setDevelopmentLevel");
const setMateFame_1 = require("./dev/setMateFame");
const setMateRoyalTitle_1 = require("./dev/setMateRoyalTitle");
const addDirectMail_1 = require("./dev/addDirectMail");
const addInstallmentSavingsLastDepositTime_1 = require("./dev/addInstallmentSavingsLastDepositTime");
const setUserLevel_1 = require("./dev/setUserLevel");
const setUserKarma_1 = require("./dev/setUserKarma");
const setUserCompanyJob_1 = require("./dev/setUserCompanyJob");
const setMateInjury_1 = require("./dev/setMateInjury");
const questSetFlags_1 = require("./dev/questSetFlags");
const questSetRegister_1 = require("./dev/questSetRegister");
const questSetTempRegister_1 = require("./dev/questSetTempRegister");
const questSetAccum_1 = require("./dev/questSetAccum");
const questSetCompleted_1 = require("./dev/questSetCompleted");
const questSetNodeIdx_1 = require("./dev/questSetNodeIdx");
const predictDisaster_1 = require("./dev/predictDisaster");
const generateDisaster_1 = require("./dev/generateDisaster");
const resolveDisaster_2 = require("./dev/resolveDisaster");
const unlimitedInvest_1 = require("./dev/unlimitedInvest");
const setIgnoreNpcEncount_1 = require("./dev/setIgnoreNpcEncount");
const statDump_1 = require("./dev/statDump");
const statSet_1 = require("./dev/statSet");
const addNearSpawner_1 = require("./dev/addNearSpawner");
const worldBuffAdd_1 = require("./dev/worldBuffAdd");
const worldBuffRem_1 = require("./dev/worldBuffRem");
const disconnectServer_1 = require("./dev/disconnectServer");
const encountNpcAttChoice_1 = require("./dev/encountNpcAttChoice");
const encountNpcDefChoice_1 = require("./dev/encountNpcDefChoice");
const changeNpcAttackRadius_1 = require("./dev/changeNpcAttackRadius");
const tradeDump_1 = require("./dev/tradeDump");
const changeNpcTickPerSec_1 = require("./dev/changeNpcTickPerSec");
const removeNearSpawner_1 = require("./dev/removeNearSpawner");
const setLoyalty_1 = require("./dev/setLoyalty");
const resetPubMates_1 = require("./dev/resetPubMates");
const addAllMates_1 = require("./dev/addAllMates");
const getNpcLocationByOceanNpcAreaSpawer_1 = require("./dev/getNpcLocationByOceanNpcAreaSpawer");
const questForceExec_1 = require("./dev/questForceExec");
const addOceanDoodadNearSpawner_1 = require("./dev/addOceanDoodadNearSpawner");
const removeOceanDoodadNearSpawner_1 = require("./dev/removeOceanDoodadNearSpawner");
const setLocalNpcSpawn_1 = require("./dev/setLocalNpcSpawn");
const setLocalDoodadSpawn_1 = require("./dev/setLocalDoodadSpawn");
const setDisasterLuck_1 = require("./dev/setDisasterLuck");
const predictProtection_1 = require("./dev/predictProtection");
const generateProtection_1 = require("./dev/generateProtection");
const resolveProtection_1 = require("./dev/resolveProtection");
const teleportToLocation_1 = require("./dev/teleportToLocation");
const setMateLevel_1 = require("./dev/setMateLevel");
const addUserDataNpcSpawner_1 = require("./dev/addUserDataNpcSpawner");
const revealAllWorldMapTIles_1 = require("./dev/revealAllWorldMapTIles");
const discoverAllTowns_1 = require("./dev/discoverAllTowns");
const addAllTownsToQuestionPlace_1 = require("./dev/addAllTownsToQuestionPlace");
const worldPassiveAdd_1 = require("./dev/worldPassiveAdd");
const worldPassiveRem_1 = require("./dev/worldPassiveRem");
const debuffImmune_1 = require("./dev/debuffImmune");
const disasterImmune_1 = require("./dev/disasterImmune");
const showDisasterStat_1 = require("./dev/showDisasterStat");
const setTradeGoodsBreedSuccess_1 = require("./dev/setTradeGoodsBreedSuccess");
const discover_1 = require("./dev/discover");
const battleResumeForUserId_1 = require("./dev/battleResumeForUserId");
const giveAllMateEquipments_1 = require("./dev/giveAllMateEquipments");
const discoverAll_1 = require("./dev/discoverAll");
const attackToMe_1 = require("./dev/attackToMe");
const addBattleFormation_1 = require("./dev/addBattleFormation");
const specialStatDump_1 = require("./dev/specialStatDump");
const setMateAwaken_1 = require("./dev/setMateAwaken");
const pubStaffReset_1 = require("./dev/pubStaffReset");
const setTownNationSharePoint_1 = require("./dev/setTownNationSharePoint");
const iAmMayor_1 = require("./dev/iAmMayor");
const changeMayorTax_1 = require("./dev/changeMayorTax");
const easyLanguage_1 = require("./dev/easyLanguage");
const changeSpawnedLocalNpcNum_1 = require("./dev/changeSpawnedLocalNpcNum");
const setSailingDaysOnOff_1 = require("./dev/setSailingDaysOnOff");
const buttonLog_1 = require("./temp/buttonLog");
const villagePlunder_1 = require("./ocean/villagePlunder");
const addParts_1 = require("./dev/addParts");
const resetCollection_1 = require("./dev/resetCollection");
const registerCollection_1 = require("./common/registerCollection");
const shipyardReceiveShip_1 = require("./town/shipyardReceiveShip");
const shipBuildingDecreaseExpireTime_1 = require("./common/shipBuildingDecreaseExpireTime");
const shipBuildingCompleteExpireTime_1 = require("./common/shipBuildingCompleteExpireTime");
const shipBuildingDeliver_1 = require("./common/shipBuildingDeliver");
const setMateTalkWaitTime_1 = require("./dev/setMateTalkWaitTime");
const receiveEventMissionReward_1 = require("./common/receiveEventMissionReward");
const findDistanceFromTileToTown_1 = require("./common/findDistanceFromTileToTown");
const getAdmiralProfile_1 = require("./common/getAdmiralProfile");
const getFlagShipProfile_1 = require("./common/getFlagShipProfile");
const setOptionProfiles_1 = require("./common/setOptionProfiles");
const shipyardVerifyTowShip_1 = require("./town/shipyardVerifyTowShip");
const refreshWeeklyEvent_1 = require("./common/refreshWeeklyEvent");
const auctionLoadMyProducts_1 = require("./common/auctionLoadMyProducts");
const auctionRegister_1 = require("./common/auctionRegister");
const auctionCancel_1 = require("./common/auctionCancel");
const auctionReregister_1 = require("./common/auctionReregister");
const auctionQueryProducts_1 = require("./common/auctionQueryProducts");
const auctionBuy_1 = require("./common/auctionBuy");
const auctionReceiveProceeds_1 = require("./common/auctionReceiveProceeds");
const shipyardRepairLifeShip_1 = require("./town/shipyardRepairLifeShip");
const load_1 = require("./dev/load");
const resetPalaceRoyalOrder_1 = require("./dev/resetPalaceRoyalOrder");
const revealRegion_1 = require("./ocean/revealRegion");
const auctionQueryShipProducts_1 = require("./common/auctionQueryShipProducts");
const revealOceanDoodads_1 = require("./ocean/revealOceanDoodads");
const discoverOceanDoodad_1 = require("./ocean/discoverOceanDoodad");
const billingQuerySalesList_1 = require("./common/billingQuerySalesList");
const billingReservePurchase_1 = require("./common/billingReservePurchase");
const billingCancelReservedPurchase_1 = require("./common/billingCancelReservedPurchase");
const billingCompleteReservedPurchaseAndGive_1 = require("./common/billingCompleteReservedPurchaseAndGive");
const billingQueryPurchaseDetail_1 = require("./common/billingQueryPurchaseDetail");
const billingQueryProductGiveItemDetail_1 = require("./common/billingQueryProductGiveItemDetail");
const billingChargeByPurchaseProduct_1 = require("./common/billingChargeByPurchaseProduct");
const questSetGlobalRegister_1 = require("./dev/questSetGlobalRegister");
const unlockAllMateAwakenAndSkill_1 = require("./dev/unlockAllMateAwakenAndSkill");
const landExploreQueryFeature_1 = require("./ocean/landExploreQueryFeature");
const guildCreate_1 = require("./guild/guildCreate");
const guildCheckForDuplicatesName_1 = require("./guild/guildCheckForDuplicatesName");
const guildDisband_1 = require("./guild/guildDisband");
const guildShowList_1 = require("./guild/guildShowList");
const guildJoin_1 = require("./guild/guildJoin");
const guildJoinCancel_1 = require("./guild/guildJoinCancel");
const guildLeave_1 = require("./guild/guildLeave");
const guildManagingAcceptJoining_1 = require("./guild/guildManagingAcceptJoining");
const guildManagingRefuseToJoin_1 = require("./guild/guildManagingRefuseToJoin");
const guildManagingChangeMemberGrade_1 = require("./guild/guildManagingChangeMemberGrade");
const guildManagingKickMember_1 = require("./guild/guildManagingKickMember");
const guildGetMyGuildInfo_1 = require("./guild/guildGetMyGuildInfo");
const guildGetDetailInfo_1 = require("./guild/guildGetDetailInfo");
const guildCraft_1 = require("./guild/guildCraft");
const guildPickupDailyReward_1 = require("./guild/guildPickupDailyReward");
const guildManagingChangeInfo_1 = require("./guild/guildManagingChangeInfo");
const guildManagingDelegateMaster_1 = require("./guild/guildManagingDelegateMaster");
const resetGuildLeftTimeTemporarily_1 = require("./dev/resetGuildLeftTimeTemporarily");
const addGuildPoint_1 = require("./dev/addGuildPoint");
const guildCheckedUpgradePopup_1 = require("./guild/guildCheckedUpgradePopup");
const guildGetLightInfo_1 = require("./guild/guildGetLightInfo");
const auctionQuerySalePrices_1 = require("./common/auctionQuerySalePrices");
const setGuildUpgradePopup_1 = require("./dev/setGuildUpgradePopup");
const setPingTimeout_1 = require("./common/setPingTimeout");
const palaceGetContributionShopSyncData_1 = require("./town/palaceGetContributionShopSyncData");
const palaceBuyContributionShopProduct_1 = require("./town/palaceBuyContributionShopProduct");
const arenaEnter_1 = require("./common/arenaEnter");
const arenaMatchListRefresh_1 = require("./common/arenaMatchListRefresh");
const arenaTicketBuy_1 = require("./common/arenaTicketBuy");
const arenaRewardReceive_1 = require("./common/arenaRewardReceive");
const setArenaScore_1 = require("./dev/setArenaScore");
const setArenaTicketCount_1 = require("./dev/setArenaTicketCount");
const setArenaTicketBoughtCount_1 = require("./dev/setArenaTicketBoughtCount");
const setArenaMatchListRefreshCount_1 = require("./dev/setArenaMatchListRefreshCount");
const enter_4 = require("./battleLobby/enter");
const leave_2 = require("./battleLobby/leave");
const arenaFleetUpdate_1 = require("./common/arenaFleetUpdate");
const resetArenaData_1 = require("./dev/resetArenaData");
const queryMyMayorTowns_1 = require("./common/queryMyMayorTowns");
const updateShields_1 = require("./common/updateShields");
const queryVillage_1 = require("./common/queryVillage");
const prologueStart_1 = require("./common/prologueStart");
const increaseLoyaltyUseItem_1 = require("./common/increaseLoyaltyUseItem");
const queryReportedWorldMapTiles_1 = require("./common/queryReportedWorldMapTiles");
const cashShopBuyDailyProduct_1 = require("./common/cashShopBuyDailyProduct");
const resetCashShopDailyProducts_1 = require("./dev/resetCashShopDailyProducts");
const setLeaderMateSwitchCount_1 = require("./dev/setLeaderMateSwitchCount");
const queryAchievements_1 = require("./common/queryAchievements");
const cashShopGetDailySale_1 = require("./common/cashShopGetDailySale");
const questSetAdminPause_1 = require("./dev/questSetAdminPause");
const generateArenaDummyUsers_1 = require("./dev/generateArenaDummyUsers");
const queryRedGemDetail_1 = require("./common/queryRedGemDetail");
const setShipBuildLevel_1 = require("./dev/setShipBuildLevel");
const setUserShipBuildLevel_1 = require("./dev/setUserShipBuildLevel");
const setVillageFriendship_1 = require("./dev/setVillageFriendship");
const auctionQueryClosed_1 = require("./common/auctionQueryClosed");
const setDebugMsgForEncountByNpc_1 = require("./dev/setDebugMsgForEncountByNpc");
const showSpawnedNpcCount_1 = require("./dev/showSpawnedNpcCount");
const queryLockedTownTradeGoods_1 = require("./common/queryLockedTownTradeGoods");
const receivePassEventMissionReward_1 = require("./common/receivePassEventMissionReward");
const buyPassEventExp_1 = require("./common/buyPassEventExp");
const initExploreTicket_1 = require("./dev/initExploreTicket");
const guildGetGuildShopSyncData_1 = require("./guild/guildGetGuildShopSyncData");
const guildBuyGuildShopProduct_1 = require("./guild/guildBuyGuildShopProduct");
const resetGuildShop_1 = require("./dev/resetGuildShop");
const setPassEventExp_1 = require("./dev/setPassEventExp");
const setEventPageProduct_1 = require("./dev/setEventPageProduct");
const estimatedSailingTime_1 = require("./dev/estimatedSailingTime");
const fixedSpeed_1 = require("./dev/fixedSpeed");
const disableHackSpeed_1 = require("./dev/disableHackSpeed");
const billingCompleteReservedPurchase_1 = require("./common/billingCompleteReservedPurchase");
const billingQueryInvenPurchaseList_1 = require("./common/billingQueryInvenPurchaseList");
const billingReceiveInvenPurchases_1 = require("./common/billingReceiveInvenPurchases");
const setExploreTimeCheck_1 = require("./dev/setExploreTimeCheck");
const eventShopBuy_1 = require("./common/eventShopBuy");
const eventShopGetProducts_1 = require("./common/eventShopGetProducts");
const unionResetEventRequest_1 = require("./town/unionResetEventRequest");
const billingQueryLatestReservedPurchase_1 = require("./common/billingQueryLatestReservedPurchase");
const reportBadChatting_1 = require("./common/reportBadChatting");
const getReportedBadChattingList_1 = require("./common/getReportedBadChattingList");
const guildReceiveWeeklyRewardMail_1 = require("./guild/guildReceiveWeeklyRewardMail");
const resetGuildDate_1 = require("./dev/resetGuildDate");
const shipSlotItemsEquip_1 = require("./town/shipSlotItemsEquip");
const setShipLife_1 = require("./dev/setShipLife");
const guildCraftCreate_1 = require("./guild/guildCraftCreate");
const guildCraftReceive_1 = require("./guild/guildCraftReceive");
const guildCraftDecreaseExpireTime_1 = require("./guild/guildCraftDecreaseExpireTime");
const guildCraftCompleteExpireTime_1 = require("./guild/guildCraftCompleteExpireTime");
const chatMuteUser_1 = require("./common/chatMuteUser");
const chatUnmuteUser_1 = require("./common/chatUnmuteUser");
const addAllShips_1 = require("./dev/addAllShips");
const addAllParts_1 = require("./dev/addAllParts");
const saveFleetPreset_1 = require("./common/saveFleetPreset");
const deleteFleetPreset_1 = require("./common/deleteFleetPreset");
const loadFleetPreset_1 = require("./common/loadFleetPreset");
const raidGetInfo_1 = require("./raid/raidGetInfo");
const noticeRaid_1 = require("./dev/noticeRaid");
const raidGetRankingPage_1 = require("./raid/raidGetRankingPage");
const addRaidDamage_1 = require("./dev/addRaidDamage");
const getRaidState_1 = require("./dev/getRaidState");
const setSailWaypoint_1 = require("./common/setSailWaypoint");
const queryAllTownInvestments_1 = require("./common/queryAllTownInvestments");
const removeSailWaypoint_1 = require("./common/removeSailWaypoint");
const queryTradeArea_1 = require("./common/queryTradeArea");
const tradeAreaRewardReceive_1 = require("./town/tradeAreaRewardReceive");
const changeFleetPresetName_1 = require("./common/changeFleetPresetName");
const battleStartRaid_1 = require("./battle/battleStartRaid");
const raidPickupReward_1 = require("./raid/raidPickupReward");
const setRaidShcheduleEnd_1 = require("./dev/setRaidShcheduleEnd");
const setTradePoint_1 = require("./dev/setTradePoint");
const resetWorldSkill_1 = require("./dev/resetWorldSkill");
const shipyardRelaunchShip_1 = require("./town/shipyardRelaunchShip");
const setNationLastUpdateTime_1 = require("./dev/setNationLastUpdateTime");
const addMateExpUseItem_1 = require("./common/addMateExpUseItem");
const questResetAllDailyLimitCompletedCount_1 = require("./dev/questResetAllDailyLimitCompletedCount");
const setMateState_1 = require("./dev/setMateState");
const lockShip_1 = require("./common/lockShip");
const eventGetMiniBoardGame_1 = require("./common/eventGetMiniBoardGame");
const eventPlayMiniBoardGame_1 = require("./common/eventPlayMiniBoardGame");
const hideEquipSlots_1 = require("./common/hideEquipSlots");
const billingSteamPurchaseInitTxn_1 = require("./common/billingSteamPurchaseInitTxn");
const queryCrazeEventBudget_1 = require("./common/queryCrazeEventBudget");
const requestFriend_1 = require("./friend/requestFriend");
const cancelFriendRequest_1 = require("./friend/cancelFriendRequest");
const deleteFriend_1 = require("./friend/deleteFriend");
const acceptFriendRequest_1 = require("./friend/acceptFriendRequest");
const denyFriendRequest_1 = require("./friend/denyFriendRequest");
const sendPoint_1 = require("./friend/sendPoint");
const pickupPoint_1 = require("./friend/pickupPoint");
const setShipSailMasteryLevel_1 = require("./dev/setShipSailMasteryLevel");
const addFleet_1 = require("./dev/addFleet");
const fleetDispatchStart_1 = require("./common/fleetDispatchStart");
const fleetDispatchEnd_1 = require("./common/fleetDispatchEnd");
const fleetDispatchRewardReceive_1 = require("./common/fleetDispatchRewardReceive");
const fleetDispatchCancel_1 = require("./common/fleetDispatchCancel");
const fleetDispatchSlotOpen_1 = require("./common/fleetDispatchSlotOpen");
const makeDispatchEnd_1 = require("./dev/makeDispatchEnd");
const addAllItems_1 = require("./dev/addAllItems");
const applyWaypointSupply_1 = require("./ocean/applyWaypointSupply");
const resetWaypointSupplyTicket_1 = require("./dev/resetWaypointSupplyTicket");
const setShipSailMasteryExp_1 = require("./dev/setShipSailMasteryExp");
const fleetDispatchDecreaseExpireTime_1 = require("./common/fleetDispatchDecreaseExpireTime");
const fleetDispatchRewardChoice_1 = require("./common/fleetDispatchRewardChoice");
const fleetDispatchQueryInProgreesState_1 = require("./common/fleetDispatchQueryInProgreesState");
const resetForTimeTravel_1 = require("./dev/resetForTimeTravel");
const unlinkCompany_1 = require("./dev/unlinkCompany");
const getWorldRanking_1 = require("./common/getWorldRanking");
const matesCompleteAwaken_1 = require("./common/matesCompleteAwaken");
const matesCompleteLearnPassive_1 = require("./common/matesCompleteLearnPassive");
const clearDiscover_1 = require("./dev/clearDiscover");
const pubResetMatesByPaid_1 = require("./town/pubResetMatesByPaid");
const removeAllItems_1 = require("./dev/removeAllItems");
const removeUnequipEquipments_1 = require("./dev/removeUnequipEquipments");
const removeUnequipParts_1 = require("./dev/removeUnequipParts");
const addReleasedMates_1 = require("./dev/addReleasedMates");
const addAllMatesAwaken_1 = require("./dev/addAllMatesAwaken");
const setAllMatesLevel_1 = require("./dev/setAllMatesLevel");
const updateBattleContinuousResult_1 = require("./common/updateBattleContinuousResult");
const toggleBattleContinuous_1 = require("./common/toggleBattleContinuous");
const translateChat_1 = require("./common/translateChat");
const buyChatTranslationCount_1 = require("./common/buyChatTranslationCount");
const recoverChatTranslation_1 = require("./dev/recoverChatTranslation");
const showDispatchActionResultStat_1 = require("./dev/showDispatchActionResultStat");
const buyAttendance_1 = require("./common/buyAttendance");
const receiveDailySubscriptionReward_1 = require("./common/receiveDailySubscriptionReward");
const guildSynthesisReceive_1 = require("./guild/guildSynthesisReceive");
const guildSynthesisCreate_1 = require("./guild/guildSynthesisCreate");
const guildSynthesisDecreaseExpireTime_1 = require("./guild/guildSynthesisDecreaseExpireTime");
const guildSynthesisCompleteExpireTime_1 = require("./guild/guildSynthesisCompleteExpireTime");
const changeGuildSynthesisProbGreatSuccess_1 = require("./dev/changeGuildSynthesisProbGreatSuccess");
const publishToastMessage_1 = require("./common/publishToastMessage");
const mateStartTraining_1 = require("./common/mateStartTraining");
const matesCompleteTraining_1 = require("./common/matesCompleteTraining");
const mateCompleteTrainingImmediately_1 = require("./common/mateCompleteTrainingImmediately");
const mateReduceTrainingTime_1 = require("./common/mateReduceTrainingTime");
const mateUseTrainingPoints_1 = require("./common/mateUseTrainingPoints");
const mateResetTrainingPoints_1 = require("./common/mateResetTrainingPoints");
const battleMultiActionUserChange_1 = require("./battle/battleMultiActionUserChange");
const openHotSpotProduct_1 = require("./common/openHotSpotProduct");
const guildDonate_1 = require("./guild/guildDonate");
const guildRaidOpen_1 = require("./guild/guildRaidOpen");
const guildRaidGetInfo_1 = require("./guild/guildRaidGetInfo");
const guildRaidGetRankingPage_1 = require("./guild/guildRaidGetRankingPage");
const guildRaidPickupReward_1 = require("./guild/guildRaidPickupReward");
const guildRaidBuyTicket_1 = require("./guild/guildRaidBuyTicket");
const resetGuildRaidTicket_1 = require("./dev/resetGuildRaidTicket");
const battleStartGuildRaid_1 = require("./battle/battleStartGuildRaid");
const guildRaidGetPrevRanking_1 = require("./guild/guildRaidGetPrevRanking");
const setGuildRaidShcheduleEnd_1 = require("./dev/setGuildRaidShcheduleEnd");
const addGuildRaidDamage_1 = require("./dev/addGuildRaidDamage");
const updateAutoSailOptionForServer_1 = require("./ocean/updateAutoSailOptionForServer");
const addGuildResource_1 = require("./dev/addGuildResource");
const battleEndMultiPvp_1 = require("./battle/battleEndMultiPvp");
const getWorldEventRanking_1 = require("./common/getWorldEventRanking");
const receiveEventRankingMissionReward_1 = require("./common/receiveEventRankingMissionReward");
const receiveEventRankingReward_1 = require("./common/receiveEventRankingReward");
const battleMultiSyncInit_1 = require("./battle/battleMultiSyncInit");
const battleMultiCtrlRequest_1 = require("./battle/battleMultiCtrlRequest");
const battleMultiAutoChangeRequest_1 = require("./battle/battleMultiAutoChangeRequest");
const battleMultiActionRequest_1 = require("./battle/battleMultiActionRequest");
const battleMultiActionPhaseConfirm_1 = require("./battle/battleMultiActionPhaseConfirm");
const battleMultiTimeoutNotification_1 = require("./battle/battleMultiTimeoutNotification");
const battleMultiGiveUp_1 = require("./battle/battleMultiGiveUp");
const resetGuildRaidOpenTime_1 = require("./dev/resetGuildRaidOpenTime");
const resetGuildRaidBattleNRewardHistory_1 = require("./dev/resetGuildRaidBattleNRewardHistory");
const shipSlotItemLock_1 = require("./common/shipSlotItemLock");
const receiveDiscoveryRewards_1 = require("./common/receiveDiscoveryRewards");
const getSailingDiaries_1 = require("./common/getSailingDiaries");
const equipMateIllust_1 = require("./common/equipMateIllust");
const palaceGetRoyalTitleOrder_1 = require("./town/palaceGetRoyalTitleOrder");
const searchConstellation_1 = require("./ocean/searchConstellation");
const discoverConstellation_1 = require("./ocean/discoverConstellation");
const goverChangeMayorShipyardTax_1 = require("./town/goverChangeMayorShipyardTax");
const addShipCamouflage_1 = require("./dev/addShipCamouflage");
const changeShipCamouflage_1 = require("./town/changeShipCamouflage");
const guildBuyDonationCount_1 = require("./guild/guildBuyDonationCount");
const addMateIllust_1 = require("./dev/addMateIllust");
const receiveFishSizeRewards_1 = require("./common/receiveFishSizeRewards");
const queryGuildTowns_1 = require("./common/queryGuildTowns");
const queryAllInvestedTownsGuildSharePoints_1 = require("./common/queryAllInvestedTownsGuildSharePoints");
const queryGuildInvestmentUserScores_1 = require("./common/queryGuildInvestmentUserScores");
const guildLearnGuildBuff_1 = require("./guild/guildLearnGuildBuff");
const guildSelectGuildBuffCategory_1 = require("./guild/guildSelectGuildBuffCategory");
const guildRegisterGuildBuffItem_1 = require("./guild/guildRegisterGuildBuffItem");
const updateHotTimeBuff_1 = require("./common/updateHotTimeBuff");
const queryTownsFirstGuildSharePoint_1 = require("./common/queryTownsFirstGuildSharePoint");
const setUserLastLoginTimeDaysAgo_1 = require("./dev/setUserLastLoginTimeDaysAgo");
const setUserAttendance_1 = require("./dev/setUserAttendance");
const guildBuyGuildRaidBuff_1 = require("./guild/guildBuyGuildRaidBuff");
const changeUserTitle_1 = require("./common/changeUserTitle");
const addUserTitle_1 = require("./dev/addUserTitle");
const equipBattleQuickSkill_1 = require("./common/equipBattleQuickSkill");
const battleMultiActionPhasePass_1 = require("./battle/battleMultiActionPhasePass");
const resetSessionRanking_1 = require("./dev/resetSessionRanking");
const applyFleetPreset_1 = require("./common/applyFleetPreset");
const shipBuildingCancel_1 = require("./common/shipBuildingCancel");
const villageExchange_1 = require("./ocean/villageExchange");
const villageExchangeTryNego_1 = require("./ocean/villageExchangeTryNego");
const villageFriendshipReward_1 = require("./ocean/villageFriendshipReward");
const shipyardBuyImprovedBlueprint_1 = require("./town/shipyardBuyImprovedBlueprint");
const goverRegisterMayorTradeEvent_1 = require("./town/goverRegisterMayorTradeEvent");
const reserveMayorTradeEvent_1 = require("./dev/reserveMayorTradeEvent");
const deleteMayorTradeEvent_1 = require("./dev/deleteMayorTradeEvent");
const changeCargoPresetName_1 = require("./common/changeCargoPresetName");
const changeEquipmentToCostume_1 = require("./common/changeEquipmentToCostume");
const changeCargoPresetIdInFleetPreset_1 = require("./common/changeCargoPresetIdInFleetPreset");
const receiveProducts_1 = require("./dev/receiveProducts");
const delegateNaviQueryResult_1 = require("./ocean/delegateNaviQueryResult");
const sweepLocalNpc_1 = require("./ocean/sweepLocalNpc");
const setSweepTicket_1 = require("./dev/setSweepTicket");
const resetExchangeHistory_1 = require("./dev/resetExchangeHistory");
const setVillageEvent_1 = require("./dev/setVillageEvent");
const shipBuildingTermsInfo_1 = require("./common/shipBuildingTermsInfo");
const remoteCreateShip_1 = require("./common/remoteCreateShip");
const deleteAllDirectMails_1 = require("./dev/deleteAllDirectMails");
const queryNationElectionSync_1 = require("./common/queryNationElectionSync");
const registerNationElectionCandidate_1 = require("./common/registerNationElectionCandidate");
const voteToNationElectionCandidate_1 = require("./common/voteToNationElectionCandidate");
const removeNationElectionCandidate_1 = require("./dev/removeNationElectionCandidate");
const receiveNationElectionVoteReward_1 = require("./common/receiveNationElectionVoteReward");
const receiveNationGoalPromiseReward_1 = require("./common/receiveNationGoalPromiseReward");
const modifyNationElectionCandidate_1 = require("./common/modifyNationElectionCandidate");
const changeNationSessionId_1 = require("./dev/changeNationSessionId");
const setNationPolicy_1 = require("./dev/setNationPolicy");
const upgradeNationPolicyStep_1 = require("./common/upgradeNationPolicyStep");
const setNationBudget_1 = require("./dev/setNationBudget");
const donateToNationBudget_1 = require("./common/donateToNationBudget");
const useCanal_1 = require("./town/useCanal");
const getNationAccumulatedTax_1 = require("./dev/getNationAccumulatedTax");
const setQuestPass_1 = require("./dev/setQuestPass");
const buyNationSupportShop_1 = require("./common/buyNationSupportShop");
const receiveNationSupportShopReward_1 = require("./common/receiveNationSupportShopReward");
const getNationWeeklyDonationRanks_1 = require("./common/getNationWeeklyDonationRanks");
const receiveNationWeeklyDonationRankReward_1 = require("./common/receiveNationWeeklyDonationRankReward");
const enableLastWeekInvestmentReward_1 = require("./common/enableLastWeekInvestmentReward");
const receiveLastWeekInvestmentReward_1 = require("./common/receiveLastWeekInvestmentReward");
const iAmPrimeMinister_1 = require("./dev/iAmPrimeMinister");
const joinToNationCabinetApplicants_1 = require("./common/joinToNationCabinetApplicants");
const appointNationCabinetMember_1 = require("./common/appointNationCabinetMember");
const dismissNationCabinetMember_1 = require("./common/dismissNationCabinetMember");
const loadNationCabinetApplicants_1 = require("./common/loadNationCabinetApplicants");
const setNationWageRate_1 = require("./common/setNationWageRate");
const writeNationNotice_1 = require("./common/writeNationNotice");
const resetNationCabinetLastAppointedTimes_1 = require("./dev/resetNationCabinetLastAppointedTimes");
const costumeShipSlotItemsEquip_1 = require("./town/costumeShipSlotItemsEquip");
const writeNationPrimeMinisterThought_1 = require("./common/writeNationPrimeMinisterThought");
const resetMayorRemoteInvest_1 = require("./dev/resetMayorRemoteInvest");
const resetRemoteInvestCount_1 = require("./dev/resetRemoteInvestCount");
const sweepRaid_1 = require("./ocean/sweepRaid");
const setOptionFleetPreset_1 = require("./common/setOptionFleetPreset");
const getOpenedFleetPresets_1 = require("./common/getOpenedFleetPresets");
const setRepresentedMate_1 = require("./common/setRepresentedMate");
const questRegUtcTime_1 = require("./common/questRegUtcTime");
const enchantEquipment_1 = require("./town/enchantEquipment");
const enchantShipSlot_1 = require("./town/enchantShipSlot");
const getMarketEventInfo_1 = require("./common/getMarketEventInfo");
const setSidekickMate_1 = require("./common/setSidekickMate");
const addPet_1 = require("./dev/addPet");
const setSidekickPet_1 = require("./common/setSidekickPet");
const resetBlackMarketRefreshCount_1 = require("./dev/resetBlackMarketRefreshCount");
const receiveEventRankingGuildReward_1 = require("./common/receiveEventRankingGuildReward");
const raidPickRaidBoss_1 = require("./raid/raidPickRaidBoss");
const salvageGameStart_1 = require("./ocean/salvageGameStart");
const salvageGameEnd_1 = require("./ocean/salvageGameEnd");
const salvageEnter_1 = require("./ocean/salvageEnter");
const salvageLeave_1 = require("./ocean/salvageLeave");
const setFavoriteMate_1 = require("./common/setFavoriteMate");
const getExchangeInfo_1 = require("./ocean/getExchangeInfo");
const shipCompose_1 = require("./town/shipCompose");
const openBuyableHotSpotProducts_1 = require("./dev/openBuyableHotSpotProducts");
const npcShopBuy_1 = require("./town/npcShopBuy");
const smuggleEnterConfirm_1 = require("./town/smuggleEnterConfirm");
const contactNpc_1 = require("./town/contactNpc");
const awayNpc_1 = require("./town/awayNpc");
const smuggleGetSyncData_1 = require("./town/smuggleGetSyncData");
const smuggleTryBeginNego_1 = require("./town/smuggleTryBeginNego");
const smuggleTryNego_1 = require("./town/smuggleTryNego");
const smuggleBuy_1 = require("./town/smuggleBuy");
const smuggleSell_1 = require("./town/smuggleSell");
const smuggleResetBought_1 = require("./town/smuggleResetBought");
const getMyBlindBidInven_1 = require("./common/getMyBlindBidInven");
const getBlindBidCompletedProducts_1 = require("./common/getBlindBidCompletedProducts");
const tryBlindBid_1 = require("./common/tryBlindBid");
const receiveBlindBidWinnerReward_1 = require("./common/receiveBlindBidWinnerReward");
const refundBlindBid_1 = require("./common/refundBlindBid");
const BlindBidTicketBuy_1 = require("./common/BlindBidTicketBuy");
const resetHotSpotProductsHistory_1 = require("./dev/resetHotSpotProductsHistory");
const test_1 = require("./dev/test");
const matesCompleteTranscendence_1 = require("./common/matesCompleteTranscendence");
const mateCompleteTranscendenceImmediately_1 = require("./common/mateCompleteTranscendenceImmediately");
const mateStartTranscendence_1 = require("./common/mateStartTranscendence");
const shipyardResetEnchantCount_1 = require("./town/shipyardResetEnchantCount");
const battleStartInfiniteLighthouse_1 = require("./battle/battleStartInfiniteLighthouse");
const battleEndInfiniteLighthouse_1 = require("./battle/battleEndInfiniteLighthouse");
const getInfiniteLighthouseFriendClearedInfo_1 = require("./common/getInfiniteLighthouseFriendClearedInfo");
const sweepInfiniteLighthouseStage_1 = require("./common/sweepInfiniteLighthouseStage");
const changeInfiniteLighthouseClearedInfoSession_1 = require("./dev/changeInfiniteLighthouseClearedInfoSession");
const addAllPubMates_1 = require("./dev/addAllPubMates");
const clearInfinitelighthouseStage_1 = require("./dev/clearInfinitelighthouseStage");
const getUserFirstFleetInfos_1 = require("./common/getUserFirstFleetInfos");
const getUserFirstFleetInfoByName_1 = require("./common/getUserFirstFleetInfoByName");
const resetMyFirstFleetInfo_1 = require("./dev/resetMyFirstFleetInfo");
const setOptionFriendlyBattle_1 = require("./common/setOptionFriendlyBattle");
const friendlyEncount_1 = require("./town/friendlyEncount");
const friendlyEncountChoice_1 = require("./town/friendlyEncountChoice");
const battleEndFriendly_1 = require("./battle/battleEndFriendly");
const friendlyEncountCancel_1 = require("./town/friendlyEncountCancel");
const queryUnpopularEvent_1 = require("./common/queryUnpopularEvent");
const reserveUnpopularTradeEvent_1 = require("./dev/reserveUnpopularTradeEvent");
const raidGetRewardStates_1 = require("./raid/raidGetRewardStates");
const researchStart_1 = require("./common/researchStart");
const researchCancel_1 = require("./common/researchCancel");
const researchReset_1 = require("./common/researchReset");
const researchReport_1 = require("./common/researchReport");
const deleteUnpopularTradeEvent_1 = require("./dev/deleteUnpopularTradeEvent");
const updateResearch_1 = require("./dev/updateResearch");
const clashRegister_1 = require("./town/clashRegister");
const clashUnregister_1 = require("./town/clashUnregister");
const clashChoice_1 = require("./town/clashChoice");
const clashClearRejectCooldown_1 = require("./town/clashClearRejectCooldown");
const getClashPage_1 = require("./common/getClashPage");
const clearReentryCooldown_1 = require("./common/clearReentryCooldown");
const battleEndClash_1 = require("./battle/battleEndClash");
const battleStartClash_1 = require("./battle/battleStartClash");
const receiveClashReward_1 = require("./common/receiveClashReward");
const setClashScoreAndWinStreak_1 = require("./dev/setClashScoreAndWinStreak");
const activeClashBattleRecord_1 = require("./dev/activeClashBattleRecord");
const getInvestmentSeasonRanking_1 = require("./common/getInvestmentSeasonRanking");
const getMyInvestmentSeasonNationRankingScore_1 = require("./common/getMyInvestmentSeasonNationRankingScore");
const generateTownUserWeeklyInvestmentScore_1 = require("./dev/generateTownUserWeeklyInvestmentScore");
const enterContinuousSweepReward_1 = require("./ocean/enterContinuousSweepReward");
const leaveContinuousSweepReward_1 = require("./ocean/leaveContinuousSweepReward");
const checkLastSeasonInvestmentRankRewardable_1 = require("./common/checkLastSeasonInvestmentRankRewardable");
const receiveLastSeasonInvestmentRankReward_1 = require("./common/receiveLastSeasonInvestmentRankReward");
const autoChangeItemsTry_1 = require("./common/autoChangeItemsTry");
const autoChangeItemsHistoryGet_1 = require("./common/autoChangeItemsHistoryGet");
const autoChangeItemsHistoryReset_1 = require("./common/autoChangeItemsHistoryReset");
const setTutorialCrazeEventBudget_1 = require("./dev/setTutorialCrazeEventBudget");
const tryForceAddCrazeEvent_1 = require("./dev/tryForceAddCrazeEvent");
const discoverAllFiltered_1 = require("./dev/discoverAllFiltered");
const cashShopStartTransaction_cn_1 = require("./common/cashShopStartTransaction_cn");
const cashShopCancelTransaction_cn_1 = require("./common/cashShopCancelTransaction_cn");
const chatMutedList_cn_1 = require("./common/chatMutedList_cn");
const chatSend_cn_1 = require("./common/chatSend_cn");
const completeSurvey_cn_1 = require("./common/completeSurvey_cn");
// ----------------------------------------------------------------------------
// 패킷별 핸들러 등록.
// ----------------------------------------------------------------------------
const handlers = {};
// Auth packets.
handlers[proto.Auth.ENTER_WORLD] = new enterWorld_1.Cph_Auth_EnterWorld();
handlers[proto.Auth.HELLO] = new hello_1.Cph_Auth_Hello();
handlers[proto.Auth.CHANGE_NAME] = new changeName_1.Cph_Auth_ChangeName();
handlers[proto.Auth.COMPLETE_PROLOGUE] = new completePrologue_1.Cph_Auth_CompletePrologue();
handlers[proto.Auth.REVOKE] = new revoke_1.Cph_Auth_Revoke();
handlers[proto.Auth.GAME_GUARD_CHECK_CS] = new gameGuardCheck_1.Cph_Auth_GameGuardCheck();
handlers[proto.Auth.APP_GUARD_CHECK_CS] = new appGuardCheck_1.Cph_Auth_AppGuardCheck();
// Town packets.
handlers[proto.Town.ENTER] = new enter_1.Cph_Town_Enter();
handlers[proto.Town.LOAD_COMPLETE] = new loadComplete_1.Cph_Town_LoadComplete();
handlers[proto.Town.MOVE_CS] = new move_1.Cph_Town_Move();
handlers[proto.Town.QUERY_MATE_CS] = new queryMate_1.Cph_Town_QueryMate();
handlers[proto.Town.ENTER_BUILDING] = new enterBuilding_1.Cph_Town_EnterBuilding();
handlers[proto.Town.LEAVE_BUILDING] = new leaveBuilding_1.Cph_Town_LeaveBuilding();
handlers[proto.Town.BANK_DEPOSIT] = new bankDeposit_1.Cph_Town_BankDeposit();
handlers[proto.Town.BANK_WITHDRAW] = new bankWithdraw_1.Cph_Town_BankWithdraw();
handlers[proto.Town.BANK_DEPOSIT_INSTALLMENT_SAVINGS] =
    new bankDepositInstallmentSavings_1.Cph_Town_BankDepositInstallmentSavings();
handlers[proto.Town.BANK_WITHDRAW_INSTALLMENT_SAVINGS] =
    new bankWithdrawInstallmentSavings_1.Cph_Town_BankWithdrawInstallmentSavings();
handlers[proto.Town.BANK_BUY_INSURANCE] = new bankBuyInsurance_1.Cph_Town_BuyInsurance();
handlers[proto.Town.DEPART_CHANGE_SHIP_FLEET_FORMATION] =
    new departChangeShipFleetFormation_1.Cph_Town_DepartChangeShipFleetFormation();
handlers[proto.Town.DEPART_BUY_SUPPLIES] = new departBuySupplies_1.Cph_Town_DepartBuySupplies();
handlers[proto.Town.DEPART_DEPART] = new departDepart_1.Cph_Town_DepartDepart();
handlers[proto.Town.SHIPYARD_CREATE_SHIP] = new shipyardCreateShip_1.Cph_Town_ShipyardCreateShip();
//handlers[proto.Town.SHIPYARD_UPGRADE_BLUEPRINT] = new Cph_Town_ShipyardUpgradeBlueprint();
handlers[proto.Town.SHIPYARD_REPAIR] = new shipyardRepair_1.Cph_Town_ShipyardRepair();
handlers[proto.Town.SHIPYARD_SELL_SHIP] = new shipyardSellShip_1.Cph_Town_ShipyardSellShip();
handlers[proto.Town.SHIPYARD_CHANGE_BLUEPRINT_SLOT] = new shipyardChangeBlueprintSlot_1.Cph_Town_ShipyardChangeBlueprintSlot();
handlers[proto.Town.PUB_DRAFT_SAILOR] = new pubDraftSailor_1.Cph_Town_PubDraftSailor();
handlers[proto.Town.PUB_GET_SYNC_DATA] = new pubGetSyncData_1.Cph_Town_PubGetSyncData();
handlers[proto.Town.PUB_RECRUIT_MATE] = new pubRecruitMate_1.Cph_Town_PubRecruitMate();
handlers[proto.Town.PUB_BUY_DRINK] = new pubBuyDrink_1.Cph_Town_PubBuyDrink();
handlers[proto.Town.GOVER_INVEST] = new goverInvest_1.Cph_Town_GoverInvest();
handlers[proto.Town.GOVER_GET_SYNC_DATA] = new goverGetSyncData_1.Cph_Town_GoverGetSyncData();
handlers[proto.Town.TRADE_GET_SYNC_DATA] = new tradeGetSyncData_1.Cph_Town_TradeGetSyncData();
handlers[proto.Town.TRADE_BUY] = new tradeBuy_1.Cph_Town_TradeBuy();
handlers[proto.Town.TRADE_SELL] = new tradeSell_1.Cph_Town_TradeSell();
handlers[proto.Town.TRADE_GET_ALL_SESSION_PRICE_PERCENTS] =
    new tradeGetAllSessionPricePercents_1.Cph_Town_TradeGetAllSessionPricePercents();
handlers[proto.Town.RELIGION_UPDATE_BUFF] = new religionUpdateBuff_1.Cph_Town_ReligionUpdateBuff();
handlers[proto.Town.SHOP_BUY_EX] = new shopBuyEx_1.Cph_Town_ShopBuyEx();
handlers[proto.Town.SHOP_GET_TOWN_BLACK_MARKET] = new shopGetTownBlackMarket_1.Cph_Town_ShopGetTownBlackMarket();
handlers[proto.Town.SHOP_SELL] = new shopSell_1.Cph_Town_ShopSell();
handlers[proto.Town.SHOP_RESET_TOWN_BLACK_MARKET] = new shopResetTownBlackMarket_1.Cph_Town_ShopResetTownBlackMarket();
handlers[proto.Town.RECEIVE_INSURANCE] = new receiveInsurance_1.Cph_Town_ReceiveInsurance();
handlers[proto.Town.CHANGE_SHIP_SLOT] = new changeShipSlot_1.Cph_Town_ChangeShipSlot();
handlers[proto.Town.CHANGE_SHIP_SLOTS] = new changeShipSlots_1.Cph_Town_ChangeShipSlots();
handlers[proto.Town.UNION_RESET_REQUEST] = new unionResetRequest_1.Cph_Town_UnionResetRequest();
handlers[proto.Town.PUB_GIFT] = new pubGift_1.Cph_Town_PubGift();
handlers[proto.Town.TRADE_TRY_BEGIN_NEGO] = new tradeTryBeginNego_1.Cph_Town_TradeTryBeginNego();
handlers[proto.Town.TRADE_TRY_NEGO] = new tradeTryNego_1.Cph_Town_TradeTryNego();
handlers[proto.Town.NOVICE_SUPPLY] = new noviceSupply_1.Cph_Town_NoviceSupply();
handlers[proto.Town.PALACE_GET_ROYAL_ORDER] = new palaceGetRoyalOrder_1.Cph_Town_PalaceGetRoyalOrder();
handlers[proto.Town.PALACE_REJECT_ROYAL_ORDER] = new palaceRejectRoyalOrder_1.Cph_Town_PalaceRejectRoyalOrder();
handlers[proto.Town.MANTIC_FORTUNE] = new manticFortune_1.Cph_Town_ManticFortune();
handlers[proto.Town.ARREST_USER_CHOICE] = new arrestUserChoice_1.Cph_Town_ArrestUserChoice();
handlers[proto.Town.COLLECTOR_REPORT_DISCOVERIES] = new collectorReportDiscoveries_1.Cph_Town_CollectorReportDiscoveries();
handlers[proto.Town.COLLECTOR_CONTRACT] = new collectorContract_1.Cph_CollectorContract();
handlers[proto.Town.COLLECTOR_GET_RANK] = new collectorGetRank_1.Cph_Town_CollectorGetRank();
handlers[proto.Town.TRADE_RESET_BOUGHT] = new tradeResetBought_1.Cph_Town_TradeResetBought();
handlers[proto.Town.COLLECTOR_REPORT_WORLD_MAP_TILES] = new collectorReportWorldMapTiles_1.Cph_Town_CollectorReportWorldMapTiles();
handlers[proto.Town.SHIPYARD_BUY] = new shipyardBuy_1.Cph_Town_ShipyardBuy();
handlers[proto.Town.SHIPYARD_ENCHANT] = new shipyardEnchant_1.Cph_Town_ShipyardEnchant();
handlers[proto.Town.SHIPYARD_CHOICE_ENCHANT_RESULT] = new shipyardChoiceEnchantResult_1.Cph_Town_ShipyardChoiceEnchantResult();
handlers[proto.Town.SHIPYARD_DISMANTLE_SHIP] = new shipyardDismantleShip_1.Cph_Town_ShipyardDismantleShip();
handlers[proto.Town.UPDATE_SOCIAL_ANI_PERSIST_CS] = new updateSocialAniPersist_1.Cph_Town_SocialAniPersist();
handlers[proto.Town.SHOW_SOCIAL_ANI_INSTANT_CS] = new showSocialAniInstant_1.Cph_Town_ShowSocialAniInstant();
handlers[proto.Town.SHOW_EMOTICON_INSTANT_CS] = new showEmoticonInstant_1.Cph_Town_ShowEmoticonInstant();
handlers[proto.Town.MANTIC_SWAP_PIECE] = new manticSwapPiece_1.Cph_Town_ManticSwapPiece();
handlers[proto.Town.GOVER_CHANGE_MAYOR_TAX] = new goverChangeMayorTax_1.Cph_Town_GoverChangeMayorTax();
handlers[proto.Town.PUB_STAFF_NOMINATION] = new pubStaffNomination_1.Cph_Town_PubStaffNomination();
handlers[proto.Town.PUB_STAFF_TALKING] = new pubStaffTalking_1.Cph_Town_PubStaffTalking();
handlers[proto.Town.PUB_STAFF_BOASTING] = new pubStaffBoasting_1.Cph_Town_PubStaffBoasting();
handlers[proto.Town.PUB_STAFF_GIFT] = new pubStaffGift_1.Cph_Town_PubStaffGift();
handlers[proto.Town.PUB_STAFF_CALL] = new pubStaffCall_1.Cph_Town_PubStaffCall();
handlers[proto.Town.PUB_STAFF_GET_SYNC_DATA] = new pubStaffGetSyncData_1.Cph_Town_PubStaffGetSyncData();
handlers[proto.Town.GOVER_GET_LAST_WEEK_RANK] = new goverGetLastWeekRank_1.Cph_Town_GoverGetLastWeekRank();
handlers[proto.Town.PUB_REDUCE_RECRUIT_NEGO_WAIT_TIME] =
    new pubReduceRecruitNegoWaitTime_1.Cph_Town_PubReduceRecruitNegoWaitTime();
handlers[proto.Town.PUB_RESET_RECRUIT_NEGO_WAIT_TIME] = new pubResetRecruitNegoWaitTime_1.Cph_Town_PubResetRecruitNegoWaitTime();
handlers[proto.Town.SHIPYARD_RECEIVE_SHIP] = new shipyardReceiveShip_1.Cph_Town_ShipyardReceiveShip();
handlers[proto.Town.PUB_SPONSOR_MY_MATE] = new pubSponsorMyMate_1.Cph_Town_PubSponsorMyMate();
handlers[proto.Town.PUB_REDUCE_MY_MATE_SPONSOR_WAIT_TIME] =
    new pubReduceMyMateSponsorWaitTime_1.Cph_Town_PubReduceMyMateSponsorWaitTime();
handlers[proto.Town.PUB_RESET_MY_MATE_SPONSOR_WAIT_TIME] =
    new pubResetMyMateSponsorWaitTime_1.Cph_Town_PubResetMyMateSponsorWaitTime();
handlers[proto.Town.SHIPYARD_VERIFY_TOW_SHIP] = new shipyardVerifyTowShip_1.Cph_Town_ShipyardVerifyTowShip();
handlers[proto.Town.SHIPYARD_REPAIR_LIFE_SHIP] = new shipyardRepairLifeShip_1.Cph_Town_ShipyardRepairLifeShip();
handlers[proto.Town.PALACE_GET_CONTRIBUTION_SHOP_SYNC_DATA] =
    new palaceGetContributionShopSyncData_1.Cph_Town_PalaceGetContributionShopSyncData();
handlers[proto.Town.PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT] =
    new palaceBuyContributionShopProduct_1.Cph_Town_PalaceBuyContributionShopProduct();
handlers[proto.Town.UNION_RESET_EVENT_REQUEST] = new unionResetEventRequest_1.Cph_Town_UnionResetEventRequest();
handlers[proto.Town.SHIP_SLOT_ITEMS_EQUIP] = new shipSlotItemsEquip_1.Cph_Town_ShipSlotItemsEquip();
handlers[proto.Town.TRADE_RECEIVE_TRADE_AREA_REWARD] = new tradeAreaRewardReceive_1.Cph_Town_TradeAreaRewardReceive();
handlers[proto.Town.SHIPYARD_RELAUNCH_SHIP] = new shipyardRelaunchShip_1.Cph_Town_ShipyardRelaunchShip();
handlers[proto.Town.PUB_RESET_MATES_BY_PAID] = new pubResetMatesByPaid_1.Cph_Town_PubResetMatesByPaid();
handlers[proto.Town.PALACE_GET_ROYAL_TITLE_ORDER] = new palaceGetRoyalTitleOrder_1.Cph_Town_PalaceGetRoyalTitleOrder();
handlers[proto.Town.GOVER_CHANGE_MAYOR_SHIPYARD_TAX] = new goverChangeMayorShipyardTax_1.Cph_Town_GoverChangeMayorShipyardTax();
handlers[proto.Town.CHANGE_SHIP_CAMOUFLAGE] = new changeShipCamouflage_1.Cph_Town_ChangeShipCamouflage();
handlers[proto.Town.SHIPYARD_BUY_IMPROVED_BLUEPRINT] = new shipyardBuyImprovedBlueprint_1.Cph_Town_ShipyardBuyImprovedBlueprint();
handlers[proto.Town.GOVER_REGISTER_MAYOR_TRADE_EVENT] = new goverRegisterMayorTradeEvent_1.Cph_Town_GoverRegisterMayorTradeEvent();
handlers[proto.Town.USE_CANAL] = new useCanal_1.Cph_Town_UseCanal();
handlers[proto.Town.COSTUME_SHIP_SLOT_ITEMS_EQUIP] = new costumeShipSlotItemsEquip_1.Cph_Town_CostumeShipSlotItemsEquip();
handlers[proto.Town.ENCHANT_EQUIPMENT] = new enchantEquipment_1.Cph_Town_EnchantEquipment();
handlers[proto.Town.ENCHANT_SHIP_SLOT] = new enchantShipSlot_1.Cph_Town_EnchantShipSlot();
handlers[proto.Town.SHIP_COMPOSE] = new shipCompose_1.Cph_Town_ShipCompose();
handlers[proto.Town.NPC_SHOP_BUY] = new npcShopBuy_1.Cph_Town_NpcShopBuy();
handlers[proto.Town.SMUGGLE_ENTER_CONFIRM] = new smuggleEnterConfirm_1.Cph_Town_SmuggleEnterConfirm();
handlers[proto.Town.CONTACT_NPC] = new contactNpc_1.Cph_Town_ContactNpc();
handlers[proto.Town.AWAY_NPC] = new awayNpc_1.Cph_Town_AwayNpc();
handlers[proto.Town.SMUGGLE_GET_SYNC_DATA] = new smuggleGetSyncData_1.Cph_Town_SmuggleGetSyncData();
handlers[proto.Town.SMUGGLE_TRY_BEGIN_NEGO] = new smuggleTryBeginNego_1.Cph_Town_SmuggleTryBeginNego();
handlers[proto.Town.SMUGGLE_TRY_NEGO] = new smuggleTryNego_1.Cph_Town_SmuggleTryNego();
handlers[proto.Town.SMUGGLE_BUY] = new smuggleBuy_1.Cph_Town_SmuggleBuy();
handlers[proto.Town.SMUGGLE_SELL] = new smuggleSell_1.Cph_Town_SmuggleSell();
handlers[proto.Town.SMUGGLE_RESET_BOUGHT] = new smuggleResetBought_1.Cph_Town_SmuggleResetBought();
handlers[proto.Town.SHIPYARD_RESET_ENCHANT_COUNT] = new shipyardResetEnchantCount_1.Cph_Town_ShipyardResetEnchantCount();
handlers[proto.Town.FRIENDLY_ENCOUNT] = new friendlyEncount_1.Cph_Town_FriendlyEncount();
handlers[proto.Town.FRIENDLY_ENCOUNT_CHOICE] = new friendlyEncountChoice_1.Cph_Town_friendlyEncountChoice();
handlers[proto.Town.FRIENDLY_ENCOUNT_CANCEL] = new friendlyEncountCancel_1.Cph_Town_FriendlyEncountCancel();
handlers[proto.Town.CLASH_REGISTER] = new clashRegister_1.Cph_Town_ClashRegister();
handlers[proto.Town.CLASH_UNREGISTER] = new clashUnregister_1.Cph_Town_ClashUnregister();
handlers[proto.Town.CLASH_CHOICE] = new clashChoice_1.Cph_Town_ClashChoice();
handlers[proto.Town.CLASH_CLEAR_REJECT_COOL_DOWN] = new clashClearRejectCooldown_1.Cph_Town_ClashClearRejectCooldown();
// Ocean packets.
handlers[proto.Ocean.ARRIVE] = new arrive_1.Cph_Ocean_Arrive();
handlers[proto.Ocean.LOAD_COMPLETE] = new loadComplete_2.Cph_Ocean_LoadComplete();
handlers[proto.Ocean.ENTER] = new enter_2.Cph_Ocean_Enter();
handlers[proto.Ocean.MOVE_CS] = new move_2.Cph_Ocean_Move();
handlers[proto.Ocean.INSPECT_TOWN] = new inspectTown_1.Cph_Ocean_InspectTown();
handlers[proto.Ocean.BEGIN_AUTO_SAILING] = new beginAutoSailing_1.Cph_Ocean_BeginAutoSailing();
handlers[proto.Ocean.END_AUTO_SAILING] = new endAutoSailing_1.Cph_Ocean_EndAutoSailing();
handlers[proto.Ocean.UPDATE_AUTO_SAILING] = new updateAutoSailing_1.Cph_Ocean_UpdateAutoSailing();
handlers[proto.Ocean.RESOLVE_DISASTER] = new resolveDisaster_1.Cph_Ocean_ResolveDisaster();
handlers[proto.Ocean.ENCOUNT_USER_CHOICE] = new encountUserChoice_1.Cph_Ocean_EncountUserChoice();
handlers[proto.Ocean.ENCOUNT_BY_USER] = new encountByUser_1.Cph_Ocean_EncountByUser();
handlers[proto.Ocean.EMERGENCY_RECOVER_SHIPS] = new emergencyRecoverShips_1.Cph_Ocean_EmergencyRecoverShips();
handlers[proto.Ocean.GAME_OVER_RESURRECT] = new gameOverResurrect_1.Cph_Ocean_GameOverResurrect();
handlers[proto.Ocean.GET_USER_TOWN_STATE] = new getUserTownState_1.Cph_Ocean_GetUserTownState();
handlers[proto.Ocean.ENCOUNT_END] = new encountEnd_1.Cph_Ocean_EncountEnd();
handlers[proto.Ocean.ZOOM_IN_NPC] = new zoomInNpc_1.Cph_Ocean_ZoomInNpc();
handlers[proto.Ocean.ZOOM_IN_USER] = new zoomInUser_1.Cph_Ocean_ZoomInUser();
handlers[proto.Ocean.REVEAL_WORLD_MAP_TILE] = new revealWorldMapTile_1.Cph_Ocean_RevealWorldMapTile();
handlers[proto.Ocean.DISCOVER_VILLAGE] = new discoverVillage_1.Cph_Ocean_DiscoverVillage();
handlers[proto.Ocean.ADD_QUESTION_PLACE] = new addQuestionPlace_1.Cph_Ocean_AddQuestionPlace();
handlers[proto.Ocean.ACTIVATE_OCEAN_DOODAD] = new activateOceanDoodad_1.Cph_Ocean_ActivateOceanDoodad();
handlers[proto.Ocean.LAND_ENTER] = new landEnter_1.Cph_Ocean_LandEnter();
handlers[proto.Ocean.LAND_LEAVE] = new landLeave_1.Cph_Ocean_LandLeave();
handlers[proto.Ocean.LAND_EXPLORE] = new landExplore_1.Cph_Ocean_LandExplore();
handlers[proto.Ocean.LAND_START_CS] = new landStart_1.Cph_Ocean_LandStart();
handlers[proto.Ocean.INSPECT_VILLAGE] = new inspectVillage_1.Cph_Ocean_InspectVillage();
handlers[proto.Ocean.VILLAGE_ENTER] = new villageEnter_1.Cph_Ocean_VillageEnter();
handlers[proto.Ocean.VILLAGE_LEAVE] = new villageLeave_1.Cph_Ocean_VillageLeave();
handlers[proto.Ocean.VILLAGE_GIFT] = new villageGift_1.Cph_Ocean_VillageGift();
handlers[proto.Ocean.VILLAGE_DRAFT_SAILOR] = new villageDraftSailor_1.Cph_Ocean_VillageDraftSailor();
handlers[proto.Ocean.VILLAGE_PLUNDER] = new villagePlunder_1.Cph_Ocean_VillagePlunder();
handlers[proto.Ocean.VILLAGE_EXPLORE] = new villageExplore_1.Cph_Ocean_VillageExplore();
handlers[proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START] =
    new offlineSailingMoveDelegateStart_1.Cph_Ocean_OfflineSailingMoveDelegateStart();
handlers[proto.Ocean.SALVAGE_ENTER] = new salvageEnter_1.Cph_Ocean_SalvageEnter();
handlers[proto.Ocean.SALVAGE_GAME_START] = new salvageGameStart_1.Cph_Ocean_SalvageGameStart();
handlers[proto.Ocean.SALVAGE_GAME_END] = new salvageGameEnd_1.Cph_Ocean_SalvageGameEnd();
handlers[proto.Ocean.SALVAGE_LEAVE] = new salvageLeave_1.Cph_Ocean_SalvageLeave();
handlers[proto.Ocean.SHOW_EMOTICON_INSTANT_CS] = new showEmoticonInstant_2.Cph_Ocean_ShowEmoticonInstant();
handlers[proto.Ocean.FISHING_START] = new fishingStart_1.Cph_Ocean_FishingStart();
handlers[proto.Ocean.FISHING_FIGHTING] = new fishingFighting_1.Cph_Ocean_FishingFighting();
handlers[proto.Ocean.FISHING_END] = new fishingEnd_1.Cph_Ocean_FishingEnd();
handlers[proto.Ocean.ITEM_RESURRECT] = new itemResurrect_1.Cph_Ocean_ItemResurrect();
handlers[proto.Ocean.REVEAL_REGION] = new revealRegion_1.Cph_Ocean_RevealRegion();
handlers[proto.Ocean.REVEAL_OCEAN_DOODADS] = new revealOceanDoodads_1.Cph_Ocean_RevealOceanDoodads();
handlers[proto.Ocean.DISCOVER_OCEAN_DOODAD] = new discoverOceanDoodad_1.Cph_Ocean_DiscoverOceanDoodad();
handlers[proto.Ocean.LAND_EXPLORE_QUERY_FEATURE] = new landExploreQueryFeature_1.Cph_Ocean_LandExploreQueryFeature();
handlers[proto.Ocean.APPLY_WAYPOINT_SUPPLY] = new applyWaypointSupply_1.Cph_Ocean_ApplyWaypointSupply();
handlers[proto.Ocean.UPDATE_AUTO_SAIL_OPTION_FOR_SERVER] =
    new updateAutoSailOptionForServer_1.Cph_Ocean_UpdateAutoSailOptionForServer();
handlers[proto.Ocean.SEARCH_CONSTELLATION] = new searchConstellation_1.Cph_Ocean_SearchConstellation();
handlers[proto.Ocean.DISCOVER_CONSTELLATION] = new discoverConstellation_1.Cph_Ocean_DiscoverConstellation();
handlers[proto.Ocean.VILLAGE_EXCHANGE] = new villageExchange_1.Cph_Ocean_Village_Exchange();
handlers[proto.Ocean.VILLAGE_EXCHANGE_TRY_NEGO] = new villageExchangeTryNego_1.Cph_Ocean_Village_Exchange_Try_Nego();
handlers[proto.Ocean.VILLAGE_FRIENDSHIP_REWARD] = new villageFriendshipReward_1.Cph_Ocean_Village_Friendship_Reward();
handlers[proto.Ocean.DELEGATE_NAVI_QUERY_RESULT] = new delegateNaviQueryResult_1.Cph_Ocean_DelegateNaviQueryResult();
handlers[proto.Ocean.SWEEP_LOCAL_NPC] = new sweepLocalNpc_1.Cph_Ocean_SweepLocalNpc();
handlers[proto.Ocean.SWEEP_RAID] = new sweepRaid_1.Cph_Ocean_SweepRaid();
handlers[proto.Ocean.GET_EXCHANGE_INFO] = new getExchangeInfo_1.Cph_Ocean_GetExchangeInfo();
handlers[proto.Ocean.ENTER_CONTINUOUS_SWEEP_REWARD] = new enterContinuousSweepReward_1.Cph_Ocean_EnterContinuousSweepReward();
handlers[proto.Ocean.LEAVE_CONTINUOUS_SWEEP_REWARD] = new leaveContinuousSweepReward_1.Cph_Ocean_LeaveContinuousSweepReward();
// Battle packets.
handlers[proto.Battle.ACTION] = new battleAction_1.Cph_Battle_Action();
handlers[proto.Battle.END] = new battleEnd_1.Cph_Battle_End();
handlers[proto.Battle.END_RAID] = new battleEndRaid_1.Cph_Battle_EndRaid();
handlers[proto.Battle.END_CHALLENGE] = new battleEndChallenge_1.Cph_Battle_EndChallenge();
handlers[proto.Battle.LOAD_COMPLETE] = new battleLoadComplete_1.Cph_Battle_LoadComplete();
handlers[proto.Battle.RESUME] = new battleResume_1.Cph_Battle_Resume();
handlers[proto.Battle.START] = new battleStart_1.Cph_Battle_Start();
handlers[proto.Battle.START_CHALLENGE] = new battleStartChallenge_1.Cph_Battle_StartChallenge();
handlers[proto.Battle.LOSE] = new battleLose_1.Cph_Battle_Lose();
handlers[proto.Battle.START_ARENA] = new battleStartArena_1.Cph_Battle_StartArena();
handlers[proto.Battle.CANCEL] = new battleCancel_1.Cph_Battle_Cancel();
handlers[proto.Battle.QUEST_RESUME_BLOCK] = new battleQuestResumeBlock_1.Cph_Battle_Quest_Resume_Block();
handlers[proto.Battle.START_RAID] = new battleStartRaid_1.Cph_Battle_StartRaid();
handlers[proto.Battle.MULTI_ACTION_USER_CHANGE_CS] = new battleMultiActionUserChange_1.Cph_Battle_MultiActionUserChange();
handlers[proto.Battle.START_GUILD_RAID] = new battleStartGuildRaid_1.Cph_Battle_StartGuildRaid();
handlers[proto.Battle.END_MULTI_PVP] = new battleEndMultiPvp_1.Cph_Battle_EndMultiPvp();
handlers[proto.Battle.MULTI_SYNC_INIT] = new battleMultiSyncInit_1.Cph_Battle_MultiSyncInit();
handlers[proto.Battle.MULTI_CTRL_REQUEST] = new battleMultiCtrlRequest_1.Cph_Battle_MultiCtrlRequest();
handlers[proto.Battle.MULTI_AUTO_CHANGE_REQUEST_CS] = new battleMultiAutoChangeRequest_1.Cph_Battle_MultiAutoChange();
handlers[proto.Battle.MULTI_ACTION_REQUEST] = new battleMultiActionRequest_1.Cph_Battle_MultiActionRequest();
handlers[proto.Battle.MULTI_TIMEOUT_NOTIFICATION] = new battleMultiTimeoutNotification_1.Cph_Battle_MultiTimeoutNotification();
handlers[proto.Battle.MULTI_ACTION_PHASE_CONFIRM_CS] = new battleMultiActionPhaseConfirm_1.Cph_Battle_MultiActionPhaseConfirm();
handlers[proto.Battle.MULTI_GIVE_UP_CS] = new battleMultiGiveUp_1.Cph_Battle_MultiGiveUp();
handlers[proto.Battle.MULTI_ACTION_PHASE_PASS_CS] = new battleMultiActionPhasePass_1.Cph_Battle_MultiActionPhasePass();
handlers[proto.Battle.START_INFINITE_LIGHT_HOUSE] = new battleStartInfiniteLighthouse_1.Cph_Battle_StartInfiniteLighthouse();
handlers[proto.Battle.END_INFINITE_LIGHT_HOUSE] = new battleEndInfiniteLighthouse_1.Cph_Battle_EndInfiniteLighthouse();
handlers[proto.Battle.END_FRIENDLY] = new battleEndFriendly_1.Cph_Battle_EndFriendly();
handlers[proto.Battle.END_CLASH] = new battleEndClash_1.Cph_Battle_EndClash();
handlers[proto.Battle.START_CLASH] = new battleStartClash_1.Cph_Battle_StartClash();
// Duel packets.
handlers[proto.Duel.START] = new duelStart_1.Cph_Duel_Start();
handlers[proto.Duel.ACTION] = new duelAction_1.Cph_Duel_Action();
handlers[proto.Duel.END] = new duelEnd_1.Cph_Duel_End();
handlers[proto.Duel.RESUME] = new duelResume_1.Cph_Duel_Resume();
// Common packets.
handlers[proto.Common.CREATE_FIRST_MATE] = new createFirstMate_1.Cph_Common_CreateFirstMate();
handlers[proto.Common.EQUIP_MATE_EQUIPMENT] = new equipMateEquipment_1.Cph_Common_EquipMateEquipment();
handlers[proto.Common.QUERY_NATION] = new queryNation_1.Cph_Common_QueryNation();
handlers[proto.Common.SELECT_NATION] = new selectNation_1.Cph_Common_SelectNation();
handlers[proto.Common.CHANGE_NATION] = new changeNation_1.Cph_Common_ChangeNation();
handlers[proto.Common.GET_USER_LIGHT_INFOS] = new getUserLightInfos_1.Cph_Common_GetUserLightInfos();
handlers[proto.Common.CHANGE_COMPANY_JOB] = new changeCompanyJob_1.Cph_Common_ChangeCompanyJob();
handlers[proto.Common.CHANGE_SHIP_NAME] = new changeShipName_1.Cph_Common_ChangeShipName();
handlers[proto.Common.RELOCATE_SAILOR] = new relocateSailor_1.Cph_Common_RelocateSailor();
handlers[proto.Common.REMOVE_ITEM] = new removeItem_1.Cph_Common_RemoveItem();
handlers[proto.Common.REMOVE_MATE_EQUIPMENT] = new removeMateEquipment_1.Cph_Common_RemoveMateEquipment();
handlers[proto.Common.EXPAND_INVENTORY_SLOT] = new expandInventorySlot_1.Cph_Common_ExpandInventorySlot();
handlers[proto.Common.RECEIVE_MAILS] = new receiveMails_1.Cph_Common_ReceiveMails();
handlers[proto.Common.QUEST_ACCEPT] = new questAccept_1.Cph_Common_QuestAccept();
handlers[proto.Common.QUEST_START_BLOCK] = new questStartBlock_1.Cph_Common_QuestStartBlock();
handlers[proto.Common.QUEST_ENTRUST] = new questEntrust_1.Cph_Common_QuestEntrust();
handlers[proto.Common.QUEST_END_BLOCK] = new questEndBlock_1.Cph_Common_QuestEndBlock();
handlers[proto.Common.QUEST_REG_STORE] = new questRegStore_1.Cph_Common_QuestRegStore();
handlers[proto.Common.DELETE_MAILS] = new deleteMails_1.Cph_Common_DeleteMails();
handlers[proto.Common.READ_MAILS] = new readMails_1.Cph_Common_ReadMails();
handlers[proto.Common.REMOVE_CARGO] = new removeCargo_1.Cph_Common_RemoveCargo();
handlers[proto.Common.RELOAD_CARGO] = new reloadCargo_1.Cph_Common_ReloadCargo();
handlers[proto.Common.GET_TOWN_TRADE_PRICE_PERCENTS] = new getTownTradePricePercents_1.Cph_Common_GetTownTradePricePercents();
handlers[proto.Common.QUEST_DROP] = new questDrop_1.Cph_Common_QuestDrop();
handlers[proto.Common.USE_ITEM] = new useItem_1.Cph_Common_UseItem();
handlers[proto.Common.QUEST_GOTO] = new questGoto_1.Cph_Common_QuestGoto();
handlers[proto.Common.LOCK_SHIP_SLOTS] = new lockShipSlots_1.Cph_Common_LockShipSlots();
handlers[proto.Common.EQUIP_MATE_EQUIPMENTS] = new equipMateEquipments_1.Cph_Common_EquipMateEquipments();
handlers[proto.Common.BUY_ADMIRAL] = new buyAdmiral_1.Cph_Common_BuyAdmiral();
handlers[proto.Common.RECOVER_INJURY] = new recoverInjury_1.Cph_Common_RecoverInjury();
handlers[proto.Common.INCREASE_ROYALTY] = new increaseLoyalty_1.Cph_Common_IncreaseLoyalty();
handlers[proto.Common.MEET_MATES] = new meetMates_1.Cph_Common_MeetMates();
handlers[proto.Common.TALK_MATES] = new talkMates_1.Cph_Common_TalkMates();
handlers[proto.Common.SET_CARGO_LOAD_PRESET] = new setCargoLoadPreset_1.Cph_Common_SetCargoLoadPreset();
handlers[proto.Common.QUEST_OPERATE_REG] = new questOperateReg_1.Cph_Common_QuestOperateReg();
handlers[proto.Common.RECOVER_SHIPS] = new recoverShips_1.Cph_Common_RecoverShips();
handlers[proto.Common.RESET_TASK] = new resetTask_1.Cph_Common_ResetTask();
handlers[proto.Common.RECEIVE_ACHIEVEMENT_REWARD] = new receiveAchievementReward_1.Cph_Common_ReceiveAchievementReward();
handlers[proto.Common.RECEIVE_TASK_REWARD] = new receiveTaskReward_1.Cph_Common_ReceiveTaskReward();
handlers[proto.Common.GET_TOWN_NATION_SHARE_POINTS] = new getTownNationSharePoints_1.Cph_Common_GetTownNationSharePoints();
handlers[proto.Common.COMPLETE_TASK_IMMEDIATELY] = new completeTaskImmediately_1.Cph_Common_CompleteTaskImmediately();
handlers[proto.Common.RECEIVE_TASK_CATEGORY_REWARD] = new receiveTaskCategoryReward_1.Cph_Common_ReceiveTaskCategoryReward();
handlers[proto.Common.RECEIVE_ACHIEVEMENT_POINT_REWARD] =
    new receiveAchievementPointReward_1.Cph_Common_ReceiveAchievementPointReward();
handlers[proto.Common.RECEIVE_HOT_TIME] = new receiveHotTime_1.Cph_Common_ReceiveHotTime();
handlers[proto.Common.BUY_TAX_FREE_PERMIT] = new buyTaxFreePermit_1.Cph_Common_BuyTaxFreePermit();
handlers[proto.Common.GET_WORLD_MAP_TOWN_INFO] = new getWorldMapTownInfo_1.Cph_Common_GetWorldMapTownInfo();
handlers[proto.Common.CONSUME_QUEST_ENERGY] = new consumeQuestEnergy_1.Cph_Common_ConsumeQuestEnergy();
handlers[proto.Common.EXPAND_REQUEST_SLOT] = new expandRequestSlot_1.Cph_Common_ExpandRequestSlot();
handlers[proto.Common.GET_NATION_TOWNS] = new getNationTowns_1.Cph_Common_GetNationTowns();
handlers[proto.Common.REMOVE_EXPIRED_REQUEST_SLOT] = new removeExpiredRequestSlot_1.Cph_Common_RemoveExpiredRequestSlot();
handlers[proto.Common.CASH_SHOP_GET_PRODUCTS] = new cashShopGetProducts_1.Cph_Common_CashShopGetProducts();
handlers[proto.Common.CASH_SHOP_BUY_WITHOUT_PURCHASE] = new cashShopBuyWithoutPurchase_1.Cph_Common_CashShopBuyWithoutPurchase();
handlers[proto.Common.GET_REGION_OCCUPATIONS] = new getRegionOccupations_1.Cph_Common_GetRegionOccupations();
handlers[proto.Common.CASH_SHOP_RECEIVE_GACHA_BOX_GUARANTEE] =
    new cashShopReceiveGachaBoxGuarantee_1.Cph_Common_CashShopReceiveGachaBoxGuarantee();
handlers[proto.Common.GET_USER_LIGHT_INFO_BY_NAME] = new getUserLightInfoByName_1.Cph_Common_GetUserLightInfoByName();
handlers[proto.Common.GET_USER_LIGHT_INFOS_ONLY_IS_ONLINE] =
    new getUserLightInfosOnlyIsOnline_1.Cph_Common_GetUserLightInfosOnlyIsOnline();
handlers[proto.Common.UNLOCK_SHIP_CUSTOMIZING] = new unlockShipCustomizing_1.Cph_Common_UnlockShipCustomizing();
handlers[proto.Common.CUSTOMIZE_SHIP] = new customizeShip_1.Cph_Common_CustomizeShip();
handlers[proto.Common.CHAT_INIT] = new chatInit_1.Cph_Common_ChatInit();
handlers[proto.Common.CHAT_JOIN_CHANNEL] = new chatJoinChannel_1.Cph_Common_ChatJoinChannel();
handlers[proto.Common.MATE_START_AWAKEN] = new mateStartAwaken_1.Cph_Common_MateStartAwaken();
handlers[proto.Common.MATE_START_LEARN_PASSIVE] = new mateStartLearnPassive_1.Cph_Common_MateStartLearnPassive();
handlers[proto.Common.QUEST_ITEM_SET_QUEST] = new questItemSetQuest_1.Cph_Common_QuestItemSetQuest();
handlers[proto.Common.QUEST_ITEM_USE] = new questItemUse_1.Cph_Common_QuestItemUse();
handlers[proto.Common.ATTENDANCE] = new attendance_1.Cph_Common_Attendance();
handlers[proto.Common.MATE_EQUIP_PASSIVES] = new mateEquipPassives_1.Cph_Common_MateEquipPassives();
handlers[proto.Common.QUEST_SET_PAUSE] = new questSetPause_1.Cph_Common_QuestSetPause();
handlers[proto.Common.CUSTOMIZE_MATE_EQUIP] = new customizeMateEquip_1.Cph_Common_CustomizeMateEquip();
handlers[proto.Common.UNLOCK_MATE_EQUIP_COLOR] = new unlockMateEquipColor_1.Cph_Common_UnlockMateEquipColor();
handlers[proto.Common.BUBBLE_EVENT_ACTION] = new bubbleEventAction_1.Cph_Common_BubbleEventAction();
handlers[proto.Common.SHIP_SLOT_ITEM_SELL] = new shipSlotItemSell_1.Cph_Common_ShipSlotItemSell();
handlers[proto.Common.SHIP_SLOT_ITEM_REMOVE] = new shipSlotItemRemove_1.Cph_Common_ShipSlotItemRemove();
// handlers[proto.Common.QUEST_ITEM_DROP] = new Cph_Common_QuestItemDrop();
handlers[proto.Common.SET_GAME_OPTION_PUSH_NOTIFICATION_CS] =
    new setGameOptionPushNotification_1.Cph_Common_SetGameOptionPushNotification();
handlers[proto.Common.GET_GAME_OPTION_PUSH_NOTIFICATION] =
    new getGameOptionPushNotification_1.Cph_Common_GetGameOptionPushNotification();
handlers[proto.Common.TOOGLE_ENCOUNT_SHIELD_ACTIVATION] =
    new toogleEncountShieldActivation_1.Cph_Common_ToogleEncountShieldActivation();
handlers[proto.Common.BATTLE_FORMATION_ACQUIRE] = new battleFormationAcquire_1.Cph_Common_BattleFormationAcquire();
handlers[proto.Common.BATTLE_FORMATION_CHANGE] = new battleFormationChange_1.Cph_Common_BattleFormationChange();
handlers[proto.Common.QUERY_NEAREST_TOWN] = new queryNearestTown_1.Cph_Common_QueryNearestTown();
handlers[proto.Common.USE_WORLD_SKILL] = new useWorldSkill_1.Cph_Common_UserWorldSkill();
handlers[proto.Common.REGISTER_COLLECTION] = new registerCollection_1.Cph_Common_RegisterCollection();
handlers[proto.Common.SHIPYARD_SHIP_BUILDING_DECREASE_EXPIRE_TIME] =
    new shipBuildingDecreaseExpireTime_1.Cph_Common_ShipBuildingDecreaseExpireTime();
handlers[proto.Common.SHIPYARD_SHIP_BUILDING_COMPLETE_EXPIRE_TIME] =
    new shipBuildingCompleteExpireTime_1.Cph_Common_ShipBuildingCompleteExpireTime();
handlers[proto.Common.SHIPYARD_SHIP_BUILDING_DELIVER] = new shipBuildingDeliver_1.Cph_Common_ShipBuildingDeliver();
handlers[proto.Common.RECEIVE_EVENT_MISSION_REWARD] = new receiveEventMissionReward_1.Cph_Common_ReceiveEventMissionReward();
handlers[proto.Common.REFRESH_WEEKLY_EVENT] = new refreshWeeklyEvent_1.Cph_Common_RefreshWeeklyEvent();
handlers[proto.Common.MATE_COMPLETE_AWAKEN] = new mateCompleteAwaken_1.Cph_Common_MateCompleteAwaken();
handlers[proto.Common.MATE_COMPLETE_AWAKEN_IMMEDIATELY] =
    new mateCompleteAwakenImmediately_1.Cph_Common_MateCompleteAwakenImmediately();
handlers[proto.Common.MATE_REDUCE_AWAKEN_TIME] = new mateReduceAwakenTime_1.Cph_Common_MateReduceAwakenTime();
handlers[proto.Common.FIND_DISTANCE_FROM_TILE_TO_TOWN] =
    new findDistanceFromTileToTown_1.Cph_Common_FindDistanceFromTileToTown();
handlers[proto.Common.GET_ADMIRAL_PROFILE] = new getAdmiralProfile_1.Cph_Common_GetAdmiralProfile();
handlers[proto.Common.GET_FLAG_SHIP_PROFILE] = new getFlagShipProfile_1.Cph_Common_GetFlagShipProfile();
handlers[proto.Common.SET_OPTION_PROFILES] = new setOptionProfiles_1.Cph_Common_SetOptionProfiles();
handlers[proto.Common.MATE_COMPLETE_LEARN_PASSIVE] = new mateCompleteLearnPassive_1.Cph_Common_MateCompleteLearnPassive();
handlers[proto.Common.MATE_COMPLETE_LEARN_PASSIVE_IMMEDIATELY] =
    new mateCompleteLearnPassiveImmediately_1.Cph_Common_MateCompleteLearnPassiveImmediately();
handlers[proto.Common.MATE_REDUCE_LEARN_PASSIVE_TIME] = new mateReduceLearnPassiveTime_1.Cph_Common_MateReduceLearnPassiveTime();
handlers[proto.Common.AUCTION_LOAD_MY_PRODUCTS] = new auctionLoadMyProducts_1.Cph_Common_AuctionLoadProducts();
handlers[proto.Common.AUCTION_REGISTER] = new auctionRegister_1.Cph_Common_AuctionRegister();
handlers[proto.Common.AUCTION_CANCEL] = new auctionCancel_1.Cph_Common_AuctionCancel();
handlers[proto.Common.AUCTION_QUERY_SALE_PRICES] = new auctionQuerySalePrices_1.Cph_Common_AuctionQuerySalePrices();
handlers[proto.Common.AUCTION_REREGISTER] = new auctionReregister_1.Cph_Common_AuctionReregister();
handlers[proto.Common.AUCTION_RECEIVE_PROCEEDS] = new auctionReceiveProceeds_1.Cph_Common_AuctionReceiveProceeds();
handlers[proto.Common.AUCTION_QUERY_PRODUCTS] = new auctionQueryProducts_1.Cph_Common_AuctionQueryProducts();
handlers[proto.Common.AUCTION_BUY] = new auctionBuy_1.Cph_Common_AuctionBuy();
handlers[proto.Common.UPDATE_ADJUTANT_DELEGATION_CONFIG] =
    new updateAdjutantDelegationConfig_1.Cph_Common_UpdateAdjutantDelegationConfig();
handlers[proto.Common.AUCTION_QUERY_SHIP_PRODUCTS] = new auctionQueryShipProducts_1.Cph_Common_AuctionQueryShipProducts();
handlers[proto.Common.BILLING_QUERY_SALES_LIST] = new billingQuerySalesList_1.Cph_Common_BillingQuerySalesList();
handlers[proto.Common.BILLING_RESERVE_PURCHASE] = new billingReservePurchase_1.Cph_Common_BillingReservePurchase();
handlers[proto.Common.BILLING_CANCEL_RESERVED_PURCHASE] =
    new billingCancelReservedPurchase_1.Cph_Common_BillingCancelReservedPurchase();
handlers[proto.Common.BILLING_COMPLETE_RESERVED_PURCHASE_AND_GIVE] =
    new billingCompleteReservedPurchaseAndGive_1.Cph_Common_BillingCompleteReservedPurchaseAndGive();
handlers[proto.Common.BILLING_QUERY_PURCHASE_DETAIL] = new billingQueryPurchaseDetail_1.Cph_Common_BillingQueryPurchaseDetail();
handlers[proto.Common.BILLING_QUERY_PRODUCT_GIVE_ITEM_DETAIL] =
    new billingQueryProductGiveItemDetail_1.Cph_Common_BillingQueryProductGiveItemDetail();
handlers[proto.Common.BILLING_CHARGE_BY_PURCHASE_PRODUCT] =
    new billingChargeByPurchaseProduct_1.Cph_Common_BillingChargeByPurchaseProduct();
handlers[proto.Common.SET_PING_TIME_OUT] = new setPingTimeout_1.Cph_Common_SetPingTimeout();
handlers[proto.Common.ARENA_ENTER] = new arenaEnter_1.Cph_Common_ArenaEnter();
handlers[proto.Common.ARENA_MATCH_LIST_REFRESH] = new arenaMatchListRefresh_1.Cph_Common_ArenaMatchListRefresh();
handlers[proto.Common.ARENA_TICKET_BUY] = new arenaTicketBuy_1.Cph_Common_ArenaTicketBuy();
handlers[proto.Common.ARENA_REWARD_RECEIVE] = new arenaRewardReceive_1.Cph_Common_ArenaRewardReceive();
handlers[proto.Common.ARENA_FLEET_UPDATE] = new arenaFleetUpdate_1.Cph_Common_ArenaFleetUpdate();
handlers[proto.Common.QUERY_MY_MAYOR_TOWNS] = new queryMyMayorTowns_1.Cph_Common_QueryMyMayorTowns();
handlers[proto.Common.UPDATE_SHIELDS] = new updateShields_1.Cph_Common_UpdateShields();
handlers[proto.Common.QUERY_VILLAGE] = new queryVillage_1.Cph_Common_QueryVillage();
handlers[proto.Common.PROLOGUE_START] = new prologueStart_1.Cph_Common_PrologueStart();
handlers[proto.Common.INCREASE_LOYALTY_USE_ITEM] = new increaseLoyaltyUseItem_1.Cph_Common_IncreaseLoyaltyUseItem();
handlers[proto.Common.QUERY_REPORTED_WORLD_MAP_TILES] = new queryReportedWorldMapTiles_1.Cph_Common_QueryReportedWorldMapTiles();
handlers[proto.Common.CASH_SHOP_BUY_DAILY_PRODUCT] = new cashShopBuyDailyProduct_1.Cph_Common_CashShopBuyDailyProduct();
handlers[proto.Common.QUERY_ACHIEVEMENTS] = new queryAchievements_1.Cph_Common_QueryAchievements();
handlers[proto.Common.CASH_SHOP_GET_DAILY_SALE] = new cashShopGetDailySale_1.Cph_Common_CashShopGetDailySale();
handlers[proto.Common.QUERY_RED_GEM_DETAIL] = new queryRedGemDetail_1.Cph_Common_QueryRedGemDetail();
handlers[proto.Common.AUCTION_QUERY_CLOSED] = new auctionQueryClosed_1.Cph_Common_AuctionQueryClosed();
handlers[proto.Common.QUERY_LOCKED_TOWN_TRADE_GOODS] =
    new queryLockedTownTradeGoods_1.Cph_Common_QUERY_LOCKED_TOWN_TRADE_GOODS();
handlers[proto.Common.RECEIVE_PASS_EVENT_MISSION_REWARD] =
    new receivePassEventMissionReward_1.Cph_Common_ReceivePassEventMissionReward();
handlers[proto.Common.BUY_PASS_EVENT_EXP] = new buyPassEventExp_1.Cph_Common_BuyPassEventExp();
handlers[proto.Common.BILLING_COMPLETE_RESERVED_PURCHASE] =
    new billingCompleteReservedPurchase_1.Cph_Common_BillingCompleteReservedPurchase();
handlers[proto.Common.BILLING_QUERY_INVEN_PURCHASE_LIST] =
    new billingQueryInvenPurchaseList_1.Cph_Common_BillingQueryInvenPurchaseList();
handlers[proto.Common.BILLING_RECEIVE_INVEN_PURCHASES] =
    new billingReceiveInvenPurchases_1.Cph_Common_BillingReceiveInvenPurchases();
handlers[proto.Common.EVENT_SHOP_BUY] = new eventShopBuy_1.Cph_Common_EventShopBuy();
handlers[proto.Common.EVENT_SHOP_GET_PRODUCTS] = new eventShopGetProducts_1.Cph_Common_EventShopGetProducts();
handlers[proto.Common.BILLING_QUERY_LATEST_RESERVED_PURCHASE] =
    new billingQueryLatestReservedPurchase_1.Cph_Common_BillingQueryLatestReservedPurchase();
handlers[proto.Common.REPORT_BAD_CHATTING] = new reportBadChatting_1.Cph_Common_ReportBadChatting();
handlers[proto.Common.GET_REPORTED_BAD_CHATTING_LIST] = new getReportedBadChattingList_1.Cph_Common_GetReportedBadChattingList();
handlers[proto.Common.CHAT_MUTE_USER] = new chatMuteUser_1.Cph_Common_ChatMuteUser();
handlers[proto.Common.CHAT_UNMUTE_USER] = new chatUnmuteUser_1.Cph_Common_ChatUnmuteUser();
handlers[proto.Common.SET_SAIL_WAYPOINT] = new setSailWaypoint_1.Cph_Common_SetSailWaypoint();
handlers[proto.Common.QUERY_ALL_TOWN_INVESTMENTS] = new queryAllTownInvestments_1.Cph_Common_QueryAllTownInvestments();
handlers[proto.Common.REMOVE_SAIL_WAYPOINT] = new removeSailWaypoint_1.Cph_Common_RemoveSailWaypoint();
handlers[proto.Common.QUERY_TRADE_AREA] = new queryTradeArea_1.Cph_Common_QueryTradeArea();
handlers[proto.Common.CHANGE_FLEET_PRESET_NAME] = new changeFleetPresetName_1.Cph_Common_ChangeFleetPresetName();
handlers[proto.Common.LOAD_FLEET_PRESET] = new loadFleetPreset_1.Cph_Common_LoadFleetPreset();
handlers[proto.Common.ADD_MATE_EXP_USE_ITEM] = new addMateExpUseItem_1.Cph_Common_AddMateExpUseItem();
handlers[proto.Common.LOCK_SHIP] = new lockShip_1.Cph_Common_LockShip();
handlers[proto.Common.EVENT_GET_MINI_BOARD_GAME] = new eventGetMiniBoardGame_1.Cph_Common_EventGetMiniBoardGame();
handlers[proto.Common.EVENT_PLAY_MINI_BOARD_GAME] = new eventPlayMiniBoardGame_1.Cph_Common_EventPlayMiniBoardGame();
handlers[proto.Common.HIDE_EQUIP_SLOTS] = new hideEquipSlots_1.Cph_Common_HideEquipSlots();
handlers[proto.Common.BILLING_STEAM_PURCHASE_INIT_TXN] =
    new billingSteamPurchaseInitTxn_1.Cph_Common_BillingSteamPurchaseInitTxn();
handlers[proto.Common.QUERY_CRAZE_EVENT_BUDGET] = new queryCrazeEventBudget_1.Cph_Common_QueryCrazeEventBudget();
handlers[proto.Common.SAVE_FLEET_PRESET] = new saveFleetPreset_1.Cph_Common_SaveFleetPreset();
handlers[proto.Common.DELETE_FLEET_PRESET] = new deleteFleetPreset_1.Cph_Common_DeleteFleetPreset();
handlers[proto.Common.FLEET_DISPATCH_START] = new fleetDispatchStart_1.Cph_Common_FleetDispatch_Start();
handlers[proto.Common.FLEET_DISPATCH_CANCEL] = new fleetDispatchCancel_1.Cph_Common_FleetDispatch_Cancel();
handlers[proto.Common.FLEET_DISPATCH_END] = new fleetDispatchEnd_1.Cph_Common_FleetDispatch_End();
handlers[proto.Common.FLEET_DISPATCH_REWARD_RECEIVE] =
    new fleetDispatchRewardReceive_1.Cph_Common_FleetDispatch_Reward_Receive();
handlers[proto.Common.FLEET_DISPATCH_SLOT_OPEN] = new fleetDispatchSlotOpen_1.Cph_Common_FleetDispatch_Slot_Open();
handlers[proto.Common.FLEET_DISPATCH_DECREASE_EXPIRE_TIME] =
    new fleetDispatchDecreaseExpireTime_1.Cph_Common_FleetDispatchDecreasExpireTime();
handlers[proto.Common.FLEET_DISPATCH_REWARD_CHOICE] = new fleetDispatchRewardChoice_1.Cph_Common_FleetDispatchRewardChoice();
handlers[proto.Common.FLEET_DISPATCH_QUERY_IN_PROGRESS_STATE] =
    new fleetDispatchQueryInProgreesState_1.Cph_Common_FleetDispatchQueryInProgreesState();
handlers[proto.Common.GET_WORLD_RANKING] = new getWorldRanking_1.Cph_Common_GetWorldRanking();
handlers[proto.Common.MATES_COMPLETE_AWAKEN] = new matesCompleteAwaken_1.Cph_Common_MatesCompleteAwaken();
handlers[proto.Common.MATES_COMPLETE_LEARN_PASSIVE] = new matesCompleteLearnPassive_1.Cph_Common_MatesCompleteLearnPassive();
handlers[proto.Common.TOGGLE_BATTLE_CONTINUOUS] = new toggleBattleContinuous_1.Cph_Common_ToggleBattleContinuous();
handlers[proto.Common.UPDATE_BATTLE_CONTINUOUS_RESULT] =
    new updateBattleContinuousResult_1.Cph_Common_UpdateBattleContinuousResult();
handlers[proto.Common.TRANSLATE_CHAT] = new translateChat_1.Cph_Common_TranslateChat();
handlers[proto.Common.BUY_CHAT_TRANSLATION_COUNT] = new buyChatTranslationCount_1.Cph_Common_BuyChatTranslationCount();
handlers[proto.Common.BUY_ATTENDANCE] = new buyAttendance_1.Cph_Common_BuyAttendance();
handlers[proto.Common.RECEIVE_DAILY_SUBSCRIPTION_REWARD] =
    new receiveDailySubscriptionReward_1.Cph_Common_receiveDailySubscriptionReward();
handlers[proto.Common.PUBLISH_TOAST_MESSAGE_CS] = new publishToastMessage_1.Cph_Common_PublishToastMessage();
handlers[proto.Common.MATE_START_TRAINING] = new mateStartTraining_1.Cph_Common_MateStartTraining();
handlers[proto.Common.MATES_COMPLETE_TRAINING] = new matesCompleteTraining_1.Cph_Common_MatesCompleteTraining();
handlers[proto.Common.MATE_COMPLETE_TRAINING_IMMEDIATELY] =
    new mateCompleteTrainingImmediately_1.Cph_Common_MateCompleteTrainingImmediately();
handlers[proto.Common.MATE_REDUCE_TRAINING_TIME] = new mateReduceTrainingTime_1.Cph_Common_MateReduceTrainingTime();
handlers[proto.Common.MATE_USE_TRAINING_POINTS] = new mateUseTrainingPoints_1.Cph_Common_MateUseTrainingPoints();
handlers[proto.Common.MATE_RESET_TRAINING_POINTS] = new mateResetTrainingPoints_1.Cph_Common_MateResetTrainingPoints();
handlers[proto.Common.OPEN_HOT_SPOT_PRODUCT] = new openHotSpotProduct_1.Cph_Common_OpenHotSpotProduct();
handlers[proto.Common.GET_WORLD_EVENT_RANKING] = new getWorldEventRanking_1.Cph_Common_GetWorldEventRanking();
handlers[proto.Common.RECEIVE_EVENT_RANKING_MISSION_REWARD] =
    new receiveEventRankingMissionReward_1.Cph_Common_ReceiveEventRankingMissionReward();
handlers[proto.Common.RECEIVE_EVENT_RANKING_REWARD] = new receiveEventRankingReward_1.Cph_Common_ReceiveEventRankingReward();
handlers[proto.Common.SHIP_SLOT_ITEM_LOCK] = new shipSlotItemLock_1.Cph_Common_ShipSlotItemLock();
handlers[proto.Common.RECEIVE_DISCOVERY_REWARD] = new receiveDiscoveryRewards_1.Cph_Common_ReceiveDiscoveryReward();
handlers[proto.Common.GET_SAILING_DIARIES] = new getSailingDiaries_1.Cph_Common_GetSailingDiaries();
handlers[proto.Common.EQUIP_MATE_ILLUST] = new equipMateIllust_1.Cph_Common_EquipMateIllust();
handlers[proto.Common.RECEIVE_FISH_SIZE_REWARDS] = new receiveFishSizeRewards_1.Cph_Common_ReceiveFishSizeRewards();
handlers[proto.Common.QUERY_ALL_INVESTED_TOWNS_GUILD_SHARE_POINTS] =
    new queryAllInvestedTownsGuildSharePoints_1.Cph_Common_QueryAllInvestedTownsGuildSharePoints();
handlers[proto.Common.QUERY_GUILD_TOWNS] = new queryGuildTowns_1.Cph_Common_QueryGuildTowns();
handlers[proto.Common.QUERY_GUILD_INVESTMENT_USER_SCORES] =
    new queryGuildInvestmentUserScores_1.Cph_Common_QueryGuildInvestmentUserScores();
handlers[proto.Common.UPDATE_HOT_TIME_BUFF] = new updateHotTimeBuff_1.Cph_Common_UpdateHotTimeBuff();
handlers[proto.Common.QUERY_TOWNS_FIRST_GUILD_SHARE_POINT] =
    new queryTownsFirstGuildSharePoint_1.Cph_Common_QueryTownsFirstGuildSharePoint();
handlers[proto.Common.CHANGE_USER_TITLE] = new changeUserTitle_1.Cph_Common_ChangeUserTitle();
handlers[proto.Common.EQUIP_BATTLE_QUICK_SKILL] = new equipBattleQuickSkill_1.Cph_Common_EquipBattleQuickSkill();
handlers[proto.Common.APPLY_FLEET_PRESET] = new applyFleetPreset_1.Cph_Common_ApplyFleetPreset();
handlers[proto.Common.SHIP_BUILDING_CANCEL] = new shipBuildingCancel_1.Cph_Common_ShipBuildingCancel();
handlers[proto.Common.CHANGE_CARGO_PRESET_NAME] = new changeCargoPresetName_1.Cph_Common_ChangeCargoPresetName();
handlers[proto.Common.CHANGE_EQUIPMENT_TO_COSTUME] = new changeEquipmentToCostume_1.Cph_Common_ChangeEquipmentToCostume();
handlers[proto.Common.CHANGE_CARGO_PRESET_ID_IN_FLEET_PRESET] =
    new changeCargoPresetIdInFleetPreset_1.Cph_Common_ChangeCargoPresetIdInFleetPreset();
handlers[proto.Common.SHIP_BUILDING_TERMS_INFO] = new shipBuildingTermsInfo_1.Cph_Common_ShipBuildingTermsInfo();
handlers[proto.Common.REMOTE_CREATE_SHIP] = new remoteCreateShip_1.Cph_Common_RemoteCreateShip();
handlers[proto.Common.QUERY_NATION_ELECTION_SYNC] = new queryNationElectionSync_1.Cph_Common_QueryNationElectionSync();
handlers[proto.Common.REGISTER_NATION_ELECTION_CANDIDATE] =
    new registerNationElectionCandidate_1.Cph_Common_RegisterNationElectionCandidate();
handlers[proto.Common.MODIFY_NATION_ELECTION_CANDIDATE_DATA] =
    new modifyNationElectionCandidate_1.Cph_Common_ModifyNationElectionCandidate();
handlers[proto.Common.VOTE_TO_NATION_ELECTION_CANDIDATE] =
    new voteToNationElectionCandidate_1.Cph_Common_VoteToNationElectionCandidate();
handlers[proto.Common.APPLY_FLEET_PRESET] = new applyFleetPreset_1.Cph_Common_ApplyFleetPreset();
handlers[proto.Common.SHIP_BUILDING_CANCEL] = new shipBuildingCancel_1.Cph_Common_ShipBuildingCancel();
handlers[proto.Common.RECEIVE_NATION_ELECTION_VOTE_REWARD] =
    new receiveNationElectionVoteReward_1.Cph_Common_ReceiveNationElectionVoteReward();
handlers[proto.Common.RECEIVE_NATION_GOAL_PROMISE_REWARD] =
    new receiveNationGoalPromiseReward_1.Cph_Common_ReceiveNationGoalPromiseReward();
handlers[proto.Common.UPGRADE_NATION_POLICY_STEP] = new upgradeNationPolicyStep_1.Cph_Common_UpgradeNationPolicyStep();
handlers[proto.Common.DONATE_TO_NATION_BUDGET] = new donateToNationBudget_1.Cph_Common_DonateToNationBudget();
handlers[proto.Common.QUEST_STATE_CHANGE] = new questStateChange_1.Cph_Common_QuestStateChange();
handlers[proto.Common.BUY_NATION_SUPPORT_SHOP] = new buyNationSupportShop_1.Cph_Common_BuyNationSupportShop();
handlers[proto.Common.RECEIVE_NATION_SUPPORT_SHOP_REWARD] =
    new receiveNationSupportShopReward_1.Cph_Common_ReceiveNationSupportShopReward();
handlers[proto.Common.GET_NATION_WEEKLY_DONATION_RANKS] =
    new getNationWeeklyDonationRanks_1.Cph_Common_GetNationWeeklyDonationRanks();
handlers[proto.Common.RECEIVE_NATION_WEEKLY_DONATION_RANK_REWARD] =
    new receiveNationWeeklyDonationRankReward_1.Cph_Common_ReceiveNationWeeklyDonationRankReward();
handlers[proto.Common.ENABLE_LAST_WEEK_INVESTMENT_REWARD] =
    new enableLastWeekInvestmentReward_1.Cph_Common_EnableLastWeekInvestmentReward();
handlers[proto.Common.RECEIVE_LAST_WEEK_INVESTMENT_REWARD] =
    new receiveLastWeekInvestmentReward_1.Cph_Common_ReceiveLastWeekInvestmentReward();
handlers[proto.Common.JOIN_TO_NATION_CABINET_APPLICANT] =
    new joinToNationCabinetApplicants_1.Cph_Common_JoinToNationCabinetApplicants();
handlers[proto.Common.APPOINT_NATION_CABINET_MEMBER] = new appointNationCabinetMember_1.Cph_Common_AppointNationCabinetMember();
handlers[proto.Common.DISMISS_NATION_CABINET_MEMBER] = new dismissNationCabinetMember_1.Cph_Common_DismissNationCabinetMember();
handlers[proto.Common.WRITE_NATION_NOTICE] = new writeNationNotice_1.Cph_Common_WriteNationNotice();
handlers[proto.Common.SET_NATION_WAGE_RATE] = new setNationWageRate_1.Cph_Common_SetNationWageRate();
handlers[proto.Common.LOAD_NATION_CABINET_APPLICANTS] =
    new loadNationCabinetApplicants_1.Cph_Common_LoadNationCabinetApplicants();
handlers[proto.Common.WRITE_NATION_PRIME_MINISTER_THOUGHT] =
    new writeNationPrimeMinisterThought_1.Cph_Common_WriteNationPrimeMinisterThought();
handlers[proto.Common.SET_OPTION_FLEET_PRESET] = new setOptionFleetPreset_1.Cph_Common_SetOptionFleetPreset();
handlers[proto.Common.GET_OPENED_FLEET_PRESET] = new getOpenedFleetPresets_1.Cph_Common_GetOpenedFleetPresets();
handlers[proto.Common.SET_REPRESENTED_MATE] = new setRepresentedMate_1.Cph_Common_SetRepresentedMate();
handlers[proto.Common.QUEST_REG_UTCTIME] = new questRegUtcTime_1.Cph_Common_QuestRegCurTimeUtc();
handlers[proto.Common.GET_MARKET_EVENT_INFO] = new getMarketEventInfo_1.Cph_Common_GetMarketEventInfo();
handlers[proto.Common.SET_SIDEKICK_MATE] = new setSidekickMate_1.Cph_Common_SetSidekickMate();
handlers[proto.Common.SET_SIDEKICK_PET] = new setSidekickPet_1.Cph_Common_SetSidekickPet();
handlers[proto.Common.RECEIVE_EVENT_RANKING_GUILD_REWARD] =
    new receiveEventRankingGuildReward_1.Cph_Common_ReceiveEventRankingGuildReward();
handlers[proto.Common.SET_FAVORITE_MATE] = new setFavoriteMate_1.Cph_Common_SetFavoriteMate();
handlers[proto.Common.GET_MY_BLIND_BID_INVEN] = new getMyBlindBidInven_1.Cph_Common_GetMyBlindBidInven();
handlers[proto.Common.GET_BLIND_BID_COMPLETED_PRODUCTS] =
    new getBlindBidCompletedProducts_1.Cph_Common_GetBlindBidCompletedProducts();
handlers[proto.Common.TRY_BLIND_BID] = new tryBlindBid_1.Cph_Common_TryBlindBid();
handlers[proto.Common.RECEIVE_BLIND_BID_WINNER_REWARD] =
    new receiveBlindBidWinnerReward_1.Cph_Common_ReceiveBlindBidWinnerReward();
handlers[proto.Common.BLIND_BID_TICKET_BUY] = new BlindBidTicketBuy_1.Cph_Common_BlindBidTicketBuy();
handlers[proto.Common.REFUND_BLIND_BID] = new refundBlindBid_1.Cph_Common_RefundBlindBid();
handlers[proto.Common.MATE_START_TRANSCENDENCE] = new mateStartTranscendence_1.Cph_Common_MateStartTranscendence();
handlers[proto.Common.MATE_COMPLETE_TRANSCENDENCE] = new matesCompleteTranscendence_1.Cph_Common_MatesCompleteTranscendence();
handlers[proto.Common.MATE_COMPLETE_TRANSCENDENCE_IMMEDIATELY] =
    new mateCompleteTranscendenceImmediately_1.Cph_Common_MateCompleteTranscendenceImmediately();
handlers[proto.Common.GET_INFINITE_LIGHT_HOUSE_FRIEND_CLEARED_INFO] =
    new getInfiniteLighthouseFriendClearedInfo_1.Cph_Common_GetInfiniteLighthouseFriendClearedInfo();
handlers[proto.Common.SWEEP_INFINITE_LIGHT_HOUSE_STAGE] =
    new sweepInfiniteLighthouseStage_1.Cph_Common_SweepInfiniteLighthouseStage();
handlers[proto.Common.GET_USER_FIRST_FLEET_INFOS] = new getUserFirstFleetInfos_1.Cph_Common_GetUserFirstFleetInfos();
handlers[proto.Common.GET_USER_FIRST_FLEET_INFO_BY_NAME] =
    new getUserFirstFleetInfoByName_1.Cph_Common_GetUserFirstFleetInfoByName();
handlers[proto.Common.SET_OPTION_FRIENDLY_BATTLE] = new setOptionFriendlyBattle_1.Cph_Common_SetOptionfriendlyBattle();
handlers[proto.Common.QUERY_UNPOPULAR_EVENT] = new queryUnpopularEvent_1.Cph_Common_QueryUnpopularEvent();
handlers[proto.Common.RESEARCH_START] = new researchStart_1.Cph_Common_ResearchStart();
handlers[proto.Common.RESEARCH_CANCEL] = new researchCancel_1.Cph_Common_ResearchCancel();
handlers[proto.Common.RESEARCH_RESET] = new researchReset_1.Cph_Common_ResearchReset();
handlers[proto.Common.RESEARCH_REPORT] = new researchReport_1.Cph_Common_ResearchReport();
handlers[proto.Common.GET_INVESTMENT_SEASON_RANKING] = new getInvestmentSeasonRanking_1.Cph_Common_GetInvestmentSeasonRanking();
handlers[proto.Common.CLEAR_REENTRY_COOLDOWN] = new clearReentryCooldown_1.Cph_Common_ClearReentryCooldown();
handlers[proto.Common.GET_CLASH_PAGE] = new getClashPage_1.Cph_Common_GetClashPage();
handlers[proto.Common.RECEIVE_CLASH_REWARD] = new receiveClashReward_1.Cph_Common_ReceiveClashReward();
handlers[proto.Common.GET_MY_INVESTMENT_SEASON_NATION_RANKING_SCORE] =
    new getMyInvestmentSeasonNationRankingScore_1.Cph_Common_GetMyInvestmentSeasonNationRankingScore();
handlers[proto.Common.CHECK_LAST_SEASON_INVESTMENT_RANK_REWARDABLE] =
    new checkLastSeasonInvestmentRankRewardable_1.Cph_Common_CheckLastSeasonInvestmentRankRewardable();
handlers[proto.Common.RECEIVE_LAST_SEASON_INVESTMENT_RANK_REWARD] =
    new receiveLastSeasonInvestmentRankReward_1.Cph_Common_ReceiveLastSeasonInvestmentRankReward();
handlers[proto.Common.AUTO_CHANGE_ITEMS_TRY] = new autoChangeItemsTry_1.Cph_Common_AutoChangeItemsTry();
handlers[proto.Common.AUTO_CHANGE_ITEMS_HISTORY_GET] = new autoChangeItemsHistoryGet_1.Cph_Common_AutoChangeItemsHistoryGet();
handlers[proto.Common.AUTO_CHANGE_ITEMS_HISTORY_RESET] =
    new autoChangeItemsHistoryReset_1.Cph_Common_AutoChangeItemsHistoryReset();
// [SDO Only] CashShop packets.
handlers[proto.Common.CASH_SHOP_START_TRANSACTION_CN] = new cashShopStartTransaction_cn_1.Cph_Common_CashShopStartTransaction_CN();
handlers[proto.Common.CASH_SHOP_CANCEL_TRANSACTION_CN] = new cashShopCancelTransaction_cn_1.Cph_Common_CashShopCancelTransaction_CN();
// [SDO Only] Chat
handlers[proto.Common.CHAT_MUTED_LIST_CN] = new chatMutedList_cn_1.Cph_Common_ChatMutedList_CN();
handlers[proto.Common.CHAT_SEND_CN] = new chatSend_cn_1.Cph_Common_ChatSend_CN();
// [SDO Only] Survey
handlers[proto.Common.COMPLETE_SURVEY_CN] = new completeSurvey_cn_1.Cph_Common_CompleteSurvey();
// BattleReward packets.
handlers[proto.BattleReward.RECEIVE_BATTLE_REWARD] = new receiveBattleReward_1.Cph_BattleReward_ReceiveBattleReward();
handlers[proto.BattleReward.LEAVE] = new leave_1.Cph_BattleReward_Leave();
handlers[proto.BattleReward.ENTER] = new enter_3.Cph_BattleReward_Enter();
handlers[proto.BattleReward.LOAD_COMPLETE] = new loadComplete_3.Cph_BattleReward_LoadComplete();
// LandExploreReward packets
handlers[proto.LandExploreReward.OPEN_BOX] = new openBox_1.Cph_LandExploreReward_OpenBox();
handlers[proto.LandExploreReward.RECEIVE_ENTER] = new receiveEnter_1.Cph_LandExploreReward_ReceiveEnter();
handlers[proto.LandExploreReward.RECEIVE_LEAVE] = new receiveLeave_1.Cph_LandExploreReward_ReceiveLeave();
handlers[proto.LandExploreReward.RECEIVE] = new receive_1.Cph_LandExploreReward_Receive();
// Etc packets.
handlers[proto.Etc.PING_CS] = new ping_1.Cph_Etc_Ping();
handlers[proto.Etc.SET_CLIENT_BACKGROUND_STATE_CS] = new setClientBackgroundState_1.Cph_Etc_SetClientBackgroundState();
// Admin packets.
handlers[proto.Admin.TELEPORT_TOWN] = new teleportTown_1.Cph_Admin_TeleportTown();
handlers[proto.Admin.TELEPORT_TO_USER] = new teleportToUser_1.Cph_Admin_TeleportToUser();
// Dev peckets.
handlers[proto.Dev.GIVE_MATE_EQUIPMENT] = new giveMateEquipment_1.Cph_Dev_GiveMateEquipment();
handlers[proto.Dev.ADD_POINT] = new addPoint_1.Cph_Dev_AddPoint();
handlers[proto.Dev.ADD_MATE] = new addMate_1.Cph_Dev_AddMate();
handlers[proto.Dev.SET_SHIP_SAILOR] = new setShipSailor_1.Cph_Dev_SetShipSailor();
handlers[proto.Dev.ADD_SHIP] = new addShip_1.Cph_Dev_AddShip();
handlers[proto.Dev.UPDATE_NATION] = new updateNation_1.Cph_Dev_UpdateNation();
handlers[proto.Dev.UPDATE_NATION_INTIMACY] = new updateNationIntimacy_1.Cph_Dev_UpdateNationIntimacy();
handlers[proto.Dev.SET_NATION] = new setNation_1.Cph_Dev_SetNation();
handlers[proto.Dev.UPDATE_REPUTATION] = new updateReputation_1.Cph_Dev_UpdateReputation();
handlers[proto.Dev.INVOKE_EVENT] = new invokeEvent_1.Cph_Dev_InvokeEvent();
handlers[proto.Dev.INVOKE_NATION_INTIMACY_UPDATE_JOB] = new invokeNationIntimacyUpdateJob_1.Cph_Dev_InvokeNationIntimacyUpdateJob();
handlers[proto.Dev.REMOVE_POINT] = new removePoint_1.Cph_Dev_RemovePoint();
handlers[proto.Dev.SET_POINT] = new setPoint_1.Cph_Dev_SetPoint();
handlers[proto.Dev.SET_SHIP_DURABILITY] = new setShipDurability_1.Cph_Dev_SetShipDurability();
handlers[proto.Dev.REMOVE_ALL_SHIP_CARGOS] = new removeAllShipCargos_1.Cph_Dev_RemoveAllShipCargos();
handlers[proto.Dev.SET_FIRST_SHIP_CARGO] = new setFirstShipCargo_1.Cph_Dev_SetFirstShipCargo();
handlers[proto.Dev.ADD_ITEM] = new addItem_1.Cph_Dev_AddItem();
handlers[proto.Dev.ADD_PARTS] = new addParts_1.Cph_Dev_AddParts();
handlers[proto.Dev.SET_DEVELOPMENT_LEVEL] = new setDevelopmentLevel_1.Cph_Dev_SetDevelopmentLevel();
handlers[proto.Dev.SET_MATE_FAME] = new setMateFame_1.Cph_Dev_SetMateFame();
handlers[proto.Dev.SET_MATE_ROYAL_TITLE] = new setMateRoyalTitle_1.Cph_Dev_SetMateRoyalTitle();
handlers[proto.Dev.ADD_DIRECT_MAIL_CS] = new addDirectMail_1.Cph_Dev_AddDirectMail();
handlers[proto.Dev.ADD_INSTALLMENT_SAVINGS_LAST_DEPOSIT_TIME] =
    new addInstallmentSavingsLastDepositTime_1.Cph_Dev_AddInstallmentSavingsLastDepositTime();
handlers[proto.Dev.SET_USER_LEVEL] = new setUserLevel_1.Cph_Dev_SetUserLevel();
handlers[proto.Dev.SET_USER_KARMA] = new setUserKarma_1.Cph_Dev_SetUserKarma();
handlers[proto.Dev.SET_USER_COMPANY_JOB] = new setUserCompanyJob_1.Cph_Dev_SetUserCompanyJob();
handlers[proto.Dev.SET_MATE_INJERY] = new setMateInjury_1.Cph_Dev_SetMateInjury();
handlers[proto.Dev.QUEST_SET_FLAGS] = new questSetFlags_1.Cph_Dev_QuestSetFlags();
handlers[proto.Dev.QUEST_SET_REGISTER] = new questSetRegister_1.Cph_Dev_QuestSetRegister();
handlers[proto.Dev.QUEST_SET_TEMP_REGISTER] = new questSetTempRegister_1.Cph_Dev_QuestSetTempRegister();
handlers[proto.Dev.QUEST_SET_ACCUM] = new questSetAccum_1.Cph_Dev_QuestSetAccum();
handlers[proto.Dev.QUEST_SET_COMPLETED] = new questSetCompleted_1.Cph_Dev_QuestSetCompleted();
handlers[proto.Dev.QUEST_SET_NODE_IDX] = new questSetNodeIdx_1.Cph_Dev_QuestSetNodeIdx();
handlers[proto.Dev.PREDICT_DISASTER] = new predictDisaster_1.Cph_Dev_PredictDisaster();
handlers[proto.Dev.GENERATE_DISASTER] = new generateDisaster_1.Cph_Dev_GenerateDisaster();
handlers[proto.Dev.RESOLVE_DISASTER] = new resolveDisaster_2.Cph_Dev_ResolveDisaster();
handlers[proto.Dev.UNLIMITED_INVEST] = new unlimitedInvest_1.Cph_Dev_UnlimitedInvest();
handlers[proto.Dev.SET_IGNORE_NPC_ENCOUNT_CS] = new setIgnoreNpcEncount_1.Cph_Dev_SetIgnoreNpcEncount();
handlers[proto.Dev.STAT_DUMP] = new statDump_1.Cph_Dev_StatDump();
handlers[proto.Dev.STAT_SET] = new statSet_1.Cph_Dev_StatSet();
handlers[proto.Dev.ADD_NEAR_SPAWNER] = new addNearSpawner_1.Cph_Dev_AddNearSpawner();
handlers[proto.Dev.WORLD_BUFF_ADD_CS] = new worldBuffAdd_1.Cph_Dev_WorldBuffAdd();
handlers[proto.Dev.WORLD_BUFF_REM_CS] = new worldBuffRem_1.Cph_Dev_WorldBuffRem();
handlers[proto.Dev.DISCONNECT_SERVER_CS] = new disconnectServer_1.Cph_Dev_DisconnectServer();
handlers[proto.Dev.ENCOUNT_NPC_ATT_CHOICE] = new encountNpcAttChoice_1.Cph_Dev_EncountNpcAttChoice();
handlers[proto.Dev.ENCOUNT_NPC_DEF_CHOICE] = new encountNpcDefChoice_1.Cph_Dev_EncountNpcDevChoice();
handlers[proto.Dev.CHANGE_NPC_ATTACK_RADIUS] = new changeNpcAttackRadius_1.Cph_Dev_ChangeNpcAttackRadius();
handlers[proto.Dev.TRADE_DUMP] = new tradeDump_1.Cph_Dev_TradeDump();
handlers[proto.Dev.CHANGE_NPC_TICK_PER_SEC] = new changeNpcTickPerSec_1.Cph_Dev_ChangeNpcTickPerSec();
handlers[proto.Dev.REMOVE_NEAR_SPAWNER] = new removeNearSpawner_1.Cph_Dev_RemoveNearSpawner();
handlers[proto.Dev.SET_LOYALTY] = new setLoyalty_1.Cph_Dev_SetLoyalty();
handlers[proto.Dev.RESET_PUB_MATES] = new resetPubMates_1.Cph_Dev_ResetPubMates();
handlers[proto.Dev.ADD_ALL_MATES] = new addAllMates_1.Cph_Dev_AddAllMates();
handlers[proto.Dev.GET_NPC_LOCATION_BY_OCEAN_NPC_AREA_SPAWNER] =
    new getNpcLocationByOceanNpcAreaSpawer_1.Cph_Dev_GetNpcLocationByOceanNpcAreaSpawner();
handlers[proto.Dev.QUEST_FORCE_EXEC] = new questForceExec_1.Cph_Dev_QuestForceExec();
handlers[proto.Dev.ADD_OCEAN_DOODAD_NEAR_SPAWNER] = new addOceanDoodadNearSpawner_1.Cph_Dev_AddOceanDoodadNearSpawner();
handlers[proto.Dev.REMOVE_OCEAN_DOODAD_NEAR_SPAWNER] = new removeOceanDoodadNearSpawner_1.Cph_Dev_RemoveOceanDoodadNearSpawner();
handlers[proto.Dev.SET_LOCAL_NPC_SPAWN] = new setLocalNpcSpawn_1.Cph_Dev_SetLocalNpcSpawn();
handlers[proto.Dev.SET_LOCAL_DOODAD_SPAWN] = new setLocalDoodadSpawn_1.Cph_Dev_SetLocalDoodadSpawn();
handlers[proto.Dev.SET_DISASTER_LUCK] = new setDisasterLuck_1.Cph_Dev_SetDisasterLuck();
handlers[proto.Dev.PREDICT_PROTECTION] = new predictProtection_1.Cph_Dev_PredictProtection();
handlers[proto.Dev.GENERATE_PROTECTION] = new generateProtection_1.Cph_Dev_GenerateProtection();
handlers[proto.Dev.RESOLVE_PROTECTION] = new resolveProtection_1.Cph_Dev_ResolveProtection();
handlers[proto.Dev.TELEPORT_TO_LOCATION_CS] = new teleportToLocation_1.Cph_Dev_TeleportToLocation();
handlers[proto.Dev.SET_MATE_LEVEL] = new setMateLevel_1.Cph_Dev_SetMateLevel();
handlers[proto.Dev.ADD_USER_DATA_NPC_SPAWNER] = new addUserDataNpcSpawner_1.Cph_Dev_AddUserDataNpcSpawner();
handlers[proto.Dev.REVEAL_ALL_WORLD_MAP_TILES] = new revealAllWorldMapTIles_1.Cph_Dev_RevealAllWorldMapTiles();
handlers[proto.Dev.DISCOVER_ALL_TOWNS] = new discoverAllTowns_1.Cph_Dev_DiscoverAllTowns();
handlers[proto.Dev.ADD_ALL_TOWNS_TO_QUESTION_PLACE] = new addAllTownsToQuestionPlace_1.Cph_Dev_AddAllTownsToQuestionPlace();
handlers[proto.Dev.WORLD_PASSIVE_ADD_CS] = new worldPassiveAdd_1.Cph_Dev_WorldPassiveAdd();
handlers[proto.Dev.WORLD_PASSIVE_REM_CS] = new worldPassiveRem_1.Cph_Dev_WorldPassiveRem();
handlers[proto.Dev.DEBUFF_IMMUNE_CS] = new debuffImmune_1.Cph_Dev_DebuffImmune();
handlers[proto.Dev.DISASTER_IMMUNE_CS] = new disasterImmune_1.Cph_Dev_DisasterImmune();
handlers[proto.Dev.SHOW_DISASTER_STAT_CS] = new showDisasterStat_1.Cph_Dev_ShowDisasterStat();
handlers[proto.Dev.SET_TRADE_GOODS_BREED_SUCCESS_CS] = new setTradeGoodsBreedSuccess_1.Cph_Dev_SetTradeGoodsBreedSuccess();
handlers[proto.Dev.DISCOVER] = new discover_1.Cph_Dev_Discover();
handlers[proto.Dev.BATTLE_RESUME_FOR_USER_ID] = new battleResumeForUserId_1.Cph_Dev_BattleResumeForUserId();
handlers[proto.Dev.GIVE_ALL_MATE_EQUIPMENTS] = new giveAllMateEquipments_1.Cph_Dev_GiveAllMateEquipments();
handlers[proto.Dev.DISCOVER_ALL] = new discoverAll_1.Cph_Dev_DiscoverAll();
handlers[proto.Dev.DISCOVER_ALL_FILTERED] = new discoverAllFiltered_1.Cph_Dev_DiscoverAllFiltered();
handlers[proto.Dev.ATTACK_TO_ME_CS] = new attackToMe_1.Cph_Dev_AttackToMe();
handlers[proto.Dev.ADD_BATTLE_FORMATION] = new addBattleFormation_1.Cph_Dev_AddBattleFormation();
handlers[proto.Dev.SPECIAL_STAT_DUMP] = new specialStatDump_1.Cph_Dev_SpecialStatDump();
handlers[proto.Dev.SET_MATE_AWAKEN] = new setMateAwaken_1.Cph_Dev_SetMateAwaken();
handlers[proto.Dev.PUB_STAFF_RESET] = new pubStaffReset_1.Cph_Dev_PubStaffReset();
handlers[proto.Dev.SET_TOWN_NATION_SHARE_POINT_CS] = new setTownNationSharePoint_1.Cph_Dev_SetTownNationSharePoint();
handlers[proto.Dev.I_AM_MAYOR_CS] = new iAmMayor_1.Cph_Dev_IAmMayor();
handlers[proto.Dev.CHANGE_MAYOR_TAX] = new changeMayorTax_1.Cph_Dev_ChangeMayorTax();
handlers[proto.Dev.EASY_LANGUAGE] = new easyLanguage_1.Cph_Dev_EasyLanguage();
handlers[proto.Dev.CHANGE_SPAWNED_LOCAL_NPC_NUM_CS] = new changeSpawnedLocalNpcNum_1.Cph_Dev_ChangeSpawnedLocalNpcNum();
handlers[proto.Dev.SET_SAILING_DAYS_ONOFF_CS] = new setSailingDaysOnOff_1.Cph_Dev_SetSailingDaysOnOff();
handlers[proto.Dev.RESET_COLLECTION] = new resetCollection_1.Cph_Dev_ResetCollection();
handlers[proto.Dev.SET_MATE_TALK_WAIT_TIME] = new setMateTalkWaitTime_1.Cph_Dev_SetMateTalkWaitTime();
handlers[proto.Dev.LOAD] = new load_1.Cph_Dev_Load();
handlers[proto.Dev.RESET_PALACE_ROYAL_ORDER] = new resetPalaceRoyalOrder_1.Cph_Dev_ResetPalaceRoyalOrder();
handlers[proto.Dev.QUEST_SET_GLOBAL_REGISTER] = new questSetGlobalRegister_1.Cph_Dev_QuestSetGlobalRegister();
handlers[proto.Dev.UNLOCK_ALL_MATE_AWAKEN_AND_SKILL] = new unlockAllMateAwakenAndSkill_1.Cph_Dev_UnlockAllMateAwakenAndSkill();
handlers[proto.Dev.RESET_GUILD_LEFT_TIME_TEMPORARILY] = new resetGuildLeftTimeTemporarily_1.Cph_Dev_ResetGuildLeftTimeTemporarily();
handlers[proto.Dev.RESET_GUILD_RAID_OPEN_TIME] = new resetGuildRaidOpenTime_1.Cph_Dev_ResetGuildRaidOpenTime();
handlers[proto.Dev.RESET_GUILD_RAID_BATTLE_N_REWARD_HISTORY] =
    new resetGuildRaidBattleNRewardHistory_1.Cph_Dev_ResetGuildRaidBattleNRewardHistory();
handlers[proto.Dev.SET_GUILD_UPGRADE_POPUP] = new setGuildUpgradePopup_1.Cph_Dev_SetGuildUpgradePopup();
handlers[proto.Dev.SET_ARENA_SCORE] = new setArenaScore_1.Cph_Dev_SetArenaScore();
handlers[proto.Dev.SET_ARENA_TICKET_COUNT] = new setArenaTicketCount_1.Cph_Dev_SetArenaTicketCount();
handlers[proto.Dev.SET_ARENA_TICKET_BOUGHT_COUNT] = new setArenaTicketBoughtCount_1.Cph_Dev_SetArenaTicketBoughtCount();
handlers[proto.Dev.SET_ARENA_MATCH_LIST_REFRESH_COUNT] =
    new setArenaMatchListRefreshCount_1.Cph_Dev_SetArenaMatchListRefreshCount();
handlers[proto.Dev.ADD_GUILD_POINT] = new addGuildPoint_1.Cph_Dev_AddGuildPoint();
handlers[proto.Dev.RESET_ARENA_DATA] = new resetArenaData_1.Cph_Dev_ResetArenaData();
handlers[proto.Dev.RESET_CASH_SHOP_DAILY_PRODUCTS] = new resetCashShopDailyProducts_1.Cph_Dev_ResetCashShopDailyProducts();
handlers[proto.Dev.SET_LEADER_MATE_SWITCH_COUNT] = new setLeaderMateSwitchCount_1.Cph_Dev_SetLeaderMateSwitchCount();
handlers[proto.Dev.QUEST_SET_ADMIN_PAUSE] = new questSetAdminPause_1.Cph_Dev_QuestSetAdminPause();
handlers[proto.Dev.GENERATE_ARENA_DUMMY_USERS] = new generateArenaDummyUsers_1.Cph_Dev_GenerateArenaDummyUsers();
handlers[proto.Dev.SET_SHIP_BUILD_LEVEL] = new setShipBuildLevel_1.Cph_Dev_SetShipBuildLevel();
handlers[proto.Dev.SET_USER_SHIP_BUILD_LEVEL] = new setUserShipBuildLevel_1.Cph_Dev_SetUserShipBuildLevel();
handlers[proto.Dev.SET_VILLAGE_FRIENDSHIP] = new setVillageFriendship_1.Cph_Dev_SetVillageFriendship();
handlers[proto.Dev.SET_DEBUG_MSG_FOR_ENCOUNT_BY_NPC_CS] = new setDebugMsgForEncountByNpc_1.Cph_Dev_SetDebugMsgForEncountByNpc();
handlers[proto.Dev.SHOW_SPAWNED_NPC_COUNT_CS] = new showSpawnedNpcCount_1.Cph_Dev_ShowSpawnedNpcCount();
handlers[proto.Dev.INIT_EXPLORE_TICKET] = new initExploreTicket_1.Cph_Dev_InitExploreTicket();
handlers[proto.Dev.RESET_GUILD_SHOP] = new resetGuildShop_1.Cph_Dev_ResetGuildShop();
handlers[proto.Dev.SET_EVENT_PAGE_PRODUCT] = new setEventPageProduct_1.Cph_Dev_SetEventPageProduct();
handlers[proto.Dev.SET_PASS_EVENT_EXP] = new setPassEventExp_1.Cph_Dev_SetPassEventExp();
handlers[proto.Dev.ESTIMATED_SAILING_TIME] = new estimatedSailingTime_1.Cph_Dev_EstimatedSailingTime();
handlers[proto.Dev.FIXED_SPEED_CS] = new fixedSpeed_1.Cph_Dev_FixedSpeed();
handlers[proto.Dev.DISABLE_SPEED_HACK_CS] = new disableHackSpeed_1.Cph_Dev_DisableHackSpeed();
handlers[proto.Dev.SET_EXPLORE_TIME_CHECK] = new setExploreTimeCheck_1.Cph_Dev_SetExploreTimeCheck();
handlers[proto.Dev.RESET_GUILD_DATE] = new resetGuildDate_1.Cph_Dev_ResetGuildDate();
handlers[proto.Dev.SET_SHIP_LIFE] = new setShipLife_1.Cph_Dev_SetShipLife();
handlers[proto.Dev.ADD_ALL_SHIPS] = new addAllShips_1.Cph_Dev_AddAllShips();
handlers[proto.Dev.ADD_ALL_PARTS] = new addAllParts_1.Cph_Dev_AddAllParts();
handlers[proto.Dev.NOTICE_RAID] = new noticeRaid_1.Cph_Dev_NoticeRaid();
handlers[proto.Dev.ADD_RAID_DAMAGE] = new addRaidDamage_1.Cph_Dev_AddRaidDamage();
// handlers[proto.Dev.KILL_RAID_BOSS] = new Cph_Dev_KillRaidBoss();
handlers[proto.Dev.GET_RAID_STATE] = new getRaidState_1.Cph_Dev_GetRaidState();
handlers[proto.Dev.SET_RAID_SCHEDULE_END] = new setRaidShcheduleEnd_1.Cph_Dev_SetRaidShcheduleEnd();
handlers[proto.Dev.SET_TRADE_POINT] = new setTradePoint_1.Cph_Dev_SetTradePoint();
handlers[proto.Dev.RESET_WORLD_SKILL] = new resetWorldSkill_1.Cph_Dev_ResetWorldSkill();
handlers[proto.Dev.SET_NATION_LAST_UPDATE_TIME] = new setNationLastUpdateTime_1.Cph_Dev_SetNationLastUpdateTime();
handlers[proto.Dev.QUEST_RESET_ALL_DAILY_LIMIT_COMPLETED_COUNT] =
    new questResetAllDailyLimitCompletedCount_1.Cph_Dev_QuestResetAllDailyLimitCompletedCount();
handlers[proto.Dev.SET_MATE_STATE] = new setMateState_1.Cph_Dev_SetMateState();
handlers[proto.Dev.SET_SHIP_SAILMASTERY_LEVEL] = new setShipSailMasteryLevel_1.Cph_Dev_SetShipSailMasteryLevel();
handlers[proto.Dev.ADD_FLEET] = new addFleet_1.Cph_Dev_AddFleet();
handlers[proto.Dev.MAKE_DIPATCH_END] = new makeDispatchEnd_1.Cph_Dev_MakeDispatchEnd();
handlers[proto.Dev.ADD_ALL_ITEMS] = new addAllItems_1.Cph_Dev_AddAllItems();
handlers[proto.Dev.RESET_WAYPOINT_SUPPLY_TICKET] = new resetWaypointSupplyTicket_1.Cph_Dev_ResetWaypointSupplyTicket();
handlers[proto.Dev.SET_SHIP_SAILMASTERY_EXP] = new setShipSailMasteryExp_1.Cph_Dev_SetShipSailMasteryExp();
handlers[proto.Dev.RESET_FOR_TIME_TRAVEL] = new resetForTimeTravel_1.Cph_Dev_ResetForTimeTravel();
handlers[proto.Dev.UNLINK_COMPANY] = new unlinkCompany_1.Cph_Dev_UnlinkCompany();
handlers[proto.Dev.CLEAR_DISCOVER] = new clearDiscover_1.Cph_Dev_ClearDiscover();
handlers[proto.Dev.REMOVE_ALL_ITEMS] = new removeAllItems_1.Cph_Dev_RemoveAllItems();
handlers[proto.Dev.REMOVE_UNEQUIP_EQUIPMENTS] = new removeUnequipEquipments_1.Cph_Dev_RemoveUnequipEquipments();
handlers[proto.Dev.REMOVE_UNEQUIP_PARTS] = new removeUnequipParts_1.Cph_Dev_RemoveUnequipParts();
handlers[proto.Dev.ADD_RELEASED_MATES] = new addReleasedMates_1.Cph_Dev_AddReleasedMates();
handlers[proto.Dev.ADD_ALL_MATES_AWAKEN] = new addAllMatesAwaken_1.Cph_Dev_AddAllMatesAwaken();
handlers[proto.Dev.SET_ALL_MATES_LEVEL] = new setAllMatesLevel_1.Cph_Dev_SetAllMatesLevel();
handlers[proto.Dev.RECOVER_CHAT_TRANSLATION] = new recoverChatTranslation_1.Cph_Dev_RecoverChatTranslation();
handlers[proto.Dev.SHOW_DISPATCH_ACTION_RESULT_STAT_CS] =
    new showDispatchActionResultStat_1.Cph_Dev_ShowDispatchActionResultStat();
handlers[proto.Dev.CHANGE_GUILD_SYNTHESIS_PROB_GREAT_SUCCESS] =
    new changeGuildSynthesisProbGreatSuccess_1.Cph_Dev_ChangeGuildSynthesisProbGreatSuccess();
handlers[proto.Dev.RESET_GUILD_RAID_TICKET] = new resetGuildRaidTicket_1.Cph_Reset_Guild_Raid_Ticket();
handlers[proto.Dev.SET_GUILD_RAID_SCHEDULE_END] = new setGuildRaidShcheduleEnd_1.Cph_Dev_SetGuildRaidShcheduleEnd();
handlers[proto.Dev.ADD_GUILD_RAID_DAMAGE] = new addGuildRaidDamage_1.Cph_Dev_AddGulidRaidDamage();
handlers[proto.Dev.ADD_GUILD_RESOURCE] = new addGuildResource_1.Cph_Dev_AddGuildResource();
handlers[proto.Dev.ADD_SHIP_CAMOUFLAGE] = new addShipCamouflage_1.Cph_Dev_AddShipCamouflage();
handlers[proto.Dev.ADD_MATE_ILLUST] = new addMateIllust_1.Cph_Dev_AddMateIllust();
handlers[proto.Dev.SET_USER_LAST_LOGIN_TIME_DAYS_AGO] = new setUserLastLoginTimeDaysAgo_1.Cph_Dev_SetUserLastLoginTimeDaysAgo();
handlers[proto.Dev.SET_USER_ATTENDANCE] = new setUserAttendance_1.Cph_Dev_SetUserAttendance();
handlers[proto.Dev.ADD_USER_TITLE] = new addUserTitle_1.Cph_Dev_AddUserTitle();
handlers[proto.Dev.RESET_SESSION_RANKING] = new resetSessionRanking_1.Cph_Dev_ResetSessionRanking();
handlers[proto.Dev.RESERVE_MAYOR_TRADE_EVENT] = new reserveMayorTradeEvent_1.Cph_Dev_ReserveMayorTradeEvent();
handlers[proto.Dev.DELETE_MAYOR_TRADE_EVENT] = new deleteMayorTradeEvent_1.Cph_Dev_DeleteMayorTradeEvent();
handlers[proto.Dev.RECEIVE_PRODUCTS] = new receiveProducts_1.Cph_Dev_ReceiveProducts();
handlers[proto.Dev.SET_SWEEP_TICKET] = new setSweepTicket_1.Cph_Dev_SetSweepTicket();
handlers[proto.Dev.RESET_EXCHANGE_HISTORY] = new resetExchangeHistory_1.Cph_Dev_ResetExchangeHistory();
handlers[proto.Dev.SET_VILLAGE_EVENT] = new setVillageEvent_1.Cph_Dev_SetVillageEvent();
handlers[proto.Dev.DELETE_ALL_DIRECT_MAILS] = new deleteAllDirectMails_1.Cph_Dev_DeleteAllDirectMails();
handlers[proto.Dev.REMOVE_NATION_ELECTION_CANDIDATE] = new removeNationElectionCandidate_1.Cph_Dev_RemoveNationElectionCandidate();
handlers[proto.Dev.CHANGE_NATION_SESSIONID] = new changeNationSessionId_1.Cph_Dev_ChangeNationSessionId();
handlers[proto.Dev.SET_NATION_POLICY] = new setNationPolicy_1.Cph_Dev_SetNationPolicy();
handlers[proto.Dev.SET_NATION_BUDGET] = new setNationBudget_1.Cph_Dev_SetNationBudget();
handlers[proto.Dev.GET_NATION_ACCUMULATED_TAX] = new getNationAccumulatedTax_1.Cph_Dev_GetNationAccumulatedTax();
handlers[proto.Dev.SET_QUEST_PASS] = new setQuestPass_1.Cph_Dev_SetQuestPass();
handlers[proto.Dev.I_AM_PRIME_MINISTER] = new iAmPrimeMinister_1.Cph_Dev_IAmNationPrimeMinister();
handlers[proto.Dev.RESET_NATION_CABINET_LAST_APPOINTED_TIMES] =
    new resetNationCabinetLastAppointedTimes_1.Cph_Dev_ResetNationCabinetLastAppointedTimes();
handlers[proto.Dev.RESET_MAYOR_REMOTE_INVEST] = new resetMayorRemoteInvest_1.Cph_Dev_ResetMayorRemoteInvest();
handlers[proto.Dev.RESET_REMOTE_INVEST_COUNT] = new resetRemoteInvestCount_1.Cph_Dev_ResetRemoteInvestCount();
handlers[proto.Dev.ADD_PET] = new addPet_1.Cph_Dev_AddPet();
handlers[proto.Dev.RESET_BLACK_MARKET_REFRESH_COUNT] = new resetBlackMarketRefreshCount_1.Cph_Dev_ResetBlackMarketRefreshCount();
handlers[proto.Dev.OPEN_HOT_SPOT_BUYABLE_PRODUCTS] = new openBuyableHotSpotProducts_1.Cph_Dev_OpenBuyableHotSpotProducts();
handlers[proto.Dev.TEST] = new test_1.Cph_Dev_Test();
handlers[proto.Dev.RESET_HOT_SPOT_PRODUCTS_HISTORY] = new resetHotSpotProductsHistory_1.Cph_Dev_ResetHotSpotProductsHistory();
handlers[proto.Dev.CHANGE_INFINITE_LIGHTHOUSE_CLEARED_INFO_SESSION] =
    new changeInfiniteLighthouseClearedInfoSession_1.Cph_Dev_ChangeInfiniteLighthouseClearedInfoSession();
handlers[proto.Dev.ADD_ALL_PUB_MATES] = new addAllPubMates_1.Cph_Dev_AddAllPubMates();
handlers[proto.Dev.CLEAR_INFINITE_LIGHTHOUSE_STAGE] = new clearInfinitelighthouseStage_1.Cph_Dev_ClearInfiniteLighthouseStage();
handlers[proto.Dev.RESET_MY_FIRST_FLEET_INFO] = new resetMyFirstFleetInfo_1.Cph_Dev_ResetMyFirstFleetInfo();
handlers[proto.Dev.RESERVE_UNPOPULAR_TRADE_EVENT] = new reserveUnpopularTradeEvent_1.Cph_Dev_ReserveUnpopularTradeEvent();
handlers[proto.Dev.DELETE_UNPOPULAR_TRADE_EVENT] = new deleteUnpopularTradeEvent_1.Cph_Dev_DeleteUnpopularTradeEvent();
handlers[proto.Dev.UPDATE_RESEARCH] = new updateResearch_1.Cph_Dev_UpdateResearch();
handlers[proto.Dev.SET_CLASH_SCORE_AND_WINSTREAK] = new setClashScoreAndWinStreak_1.Cph_Dev_SetClashScoreAndWinStreak();
handlers[proto.Dev.ACTIVE_CLASH_BATTLE_RECORD] = new activeClashBattleRecord_1.Cph_Dev_ActiveClashBattleRecord();
handlers[proto.Dev.GENERATE_TOWN_USER_WEEKLY_INVESTMENT_SCORE] =
    new generateTownUserWeeklyInvestmentScore_1.Cph_Dev_GenerateTownUserWeeklyInvestmentScore();
handlers[proto.Dev.SET_TUTORIAL_CRAZE_EVENT_BUDGET] = new setTutorialCrazeEventBudget_1.Cph_Dev_SetTutorialCrazeEventBudget();
handlers[proto.Dev.TRY_FORCE_ADD_CRAZE_EVENT] = new tryForceAddCrazeEvent_1.Cph_Dev_TryForceAddCrazeEvent();
// Temp packets.
handlers[proto.Temp.BUTTON_LOG_CS] = new buttonLog_1.Cph_Temp_ButtonLog(); // [TEMP] CBT 까지 사용 후 제거
// guild packets
handlers[proto.Guild.CHECK_DUPLICATE_NAME] = new guildCheckForDuplicatesName_1.Cph_Guild_CheckForDuplicatesName();
handlers[proto.Guild.CREATE] = new guildCreate_1.Cph_Guild_Create();
handlers[proto.Guild.DISBAND] = new guildDisband_1.Cph_Guild_Disband();
handlers[proto.Guild.SHOW_LIST] = new guildShowList_1.Cph_Guild_ShowList();
handlers[proto.Guild.JOIN] = new guildJoin_1.Cph_Guild_Join();
handlers[proto.Guild.JOIN_CANCEL] = new guildJoinCancel_1.Cph_Guild_JoinCancel();
handlers[proto.Guild.LEAVE] = new guildLeave_1.Cph_Guild_Leave();
handlers[proto.Guild.GET_MY_GUILD_INFO] = new guildGetMyGuildInfo_1.Cph_Guild_GetMyGuildInfo();
handlers[proto.Guild.CHECKED_UPGRADE_POPUP] = new guildCheckedUpgradePopup_1.Cph_Guild_CheckedUpgradePopup();
handlers[proto.Guild.GET_DETAIL_INFO] = new guildGetDetailInfo_1.Cph_Guild_GetDetailInfo();
handlers[proto.Guild.GET_LIGHT_INFO] = new guildGetLightInfo_1.Cph_Guild_GetLightInfo();
handlers[proto.Guild.CRAFT] = new guildCraft_1.Cph_Guild_Craft();
handlers[proto.Guild.PICK_UP_DAILY_REWARD] = new guildPickupDailyReward_1.Cph_Guild_PickupDailyReward();
handlers[proto.Guild.GUILD_RECEIVE_WEEKLY_REWARD_MAIL] = new guildReceiveWeeklyRewardMail_1.Cph_Guild_ReceiveWeeklyRewardMail();
handlers[proto.Guild.CRAFT_CREATE] = new guildCraftCreate_1.Cph_Guild_Craft_Create();
handlers[proto.Guild.CRAFT_RECEIVE] = new guildCraftReceive_1.Cph_Guild_Craft_Receive();
handlers[proto.Guild.CRAFT_DECREASE_EXPIRE_TIME] = new guildCraftDecreaseExpireTime_1.Cph_Guild_CraftDecreaseExpireTime();
handlers[proto.Guild.CRAFT_COMPLETE_EXPIRE_TIME] = new guildCraftCompleteExpireTime_1.Cph_Guild_CraftCompleteExpireTime();
handlers[proto.Guild.SYNTHESIS_CREATE] = new guildSynthesisCreate_1.Cph_Guild_Synthesis_Create();
handlers[proto.Guild.SYNTHESIS_RECEIVE] = new guildSynthesisReceive_1.Cph_Guild_Synthesis_Receive();
handlers[proto.Guild.SYNTHESIS_DECREASE_EXPIRE_TIME] = new guildSynthesisDecreaseExpireTime_1.Cph_Guild_SynthesisDecreaseExpireTime();
handlers[proto.Guild.SYNTHESIS_COMPLETE_EXPIRE_TIME] = new guildSynthesisCompleteExpireTime_1.Cph_Guild_SynthesisCompleteExpireTime();
handlers[proto.Guild.DONATE] = new guildDonate_1.Cph_Guild_Donate();
handlers[proto.Guild.BUY_DONATION_COUNT] = new guildBuyDonationCount_1.Cph_Guild_BuyDonationCount();
handlers[proto.Guild.SELECT_GUILD_BUFF_CATEGORY] = new guildSelectGuildBuffCategory_1.Cph_Guild_SelectGuildBuffCategory();
handlers[proto.Guild.LEARN_GUILD_BUFF] = new guildLearnGuildBuff_1.Cph_Guild_LearnGuildBuff();
handlers[proto.Guild.REGISTER_GUILD_BUFF_ITEM_FOR_UPGRADE] = new guildRegisterGuildBuffItem_1.Cph_Guild_RegisterGuildBuffItem();
handlers[proto.Guild.RAID_OPEN] = new guildRaidOpen_1.Cph_Guild_RaidOpen();
handlers[proto.Guild.RAID_GET_INFO] = new guildRaidGetInfo_1.Cph_Guild_RaidGetInfo();
handlers[proto.Guild.RAID_GET_RANKING_PAGE] = new guildRaidGetRankingPage_1.Cph_Guild_Raid_GetRankingPage();
handlers[proto.Guild.RAID_PICKUP_REWARD] = new guildRaidPickupReward_1.Cph_Guild_Raid_PickupReward();
handlers[proto.Guild.RAID_BUY_TICKET] = new guildRaidBuyTicket_1.Cph_Guild_Raid_BuyTicket();
handlers[proto.Guild.RAID_GET_PREV_RANKING] = new guildRaidGetPrevRanking_1.Cph_Guild_RaidGetPrevRanking();
handlers[proto.Guild.MANAGING_ACCEPT_JOIN] = new guildManagingAcceptJoining_1.Cph_Guild_ManagingAcceptJoining();
handlers[proto.Guild.MANAGING_REFUSE_JOIN] = new guildManagingRefuseToJoin_1.Cph_Guild_ManagingRefuseToJoin();
handlers[proto.Guild.MANAGING_CHANGE_MEMBER_GRADE] = new guildManagingChangeMemberGrade_1.Cph_Guild_ManagingChangeMemberGrade();
handlers[proto.Guild.MANAGING_KICK_MEMBER] = new guildManagingKickMember_1.Cph_Guild_ManagingKickMember();
handlers[proto.Guild.MANAGING_CHANGE_INFO] = new guildManagingChangeInfo_1.Cph_Guild_ManagingChangeInfo();
handlers[proto.Guild.MANAGING_DELEGATE_MASTER] = new guildManagingDelegateMaster_1.Cph_Guild_ManagingDelegateMaster();
handlers[proto.Guild.GET_GULID_SHOP_SYNC_DATA] = new guildGetGuildShopSyncData_1.Cph_Guild_GetGuildShopSyncData();
handlers[proto.Guild.BUY_GULID_SHOP_PRODUCT] = new guildBuyGuildShopProduct_1.Cph_Guild_BuyGuildShopProduct();
handlers[proto.Guild.BUY_GUILD_RAID_BUFF] = new guildBuyGuildRaidBuff_1.Cph_Guild_BuyGuildRaidBuff();
// BattleLobby packets.
handlers[proto.BattleLobby.LEAVE] = new leave_2.Cph_BattleLobby_Leave();
handlers[proto.BattleLobby.ENTER] = new enter_4.Cph_BattleLobby_Enter();
// raid packets.
handlers[proto.Raid.GET_RAID_INFO] = new raidGetInfo_1.Cph_Raid_GetInfo();
handlers[proto.Raid.GET_RANKING_PAGE] = new raidGetRankingPage_1.Cph_Raid_GetRankingPage();
handlers[proto.Raid.PICKUP_REWARD] = new raidPickupReward_1.Cph_Raid_PickupReward();
handlers[proto.Raid.PICK_RAID_BOSS] = new raidPickRaidBoss_1.Cph_Raid_PickRaidBoss();
handlers[proto.Raid.GET_REWARD_STATES] = new raidGetRewardStates_1.Cph_Raid_GetRewardStates();
// friend packets.
handlers[proto.Friend.REQUEST_FRIEND] = new requestFriend_1.Cph_Friend_RequestFriend();
handlers[proto.Friend.CANCEL_FRIEND_REQUEST] = new cancelFriendRequest_1.Cph_Friend_CancelFriendRequest();
handlers[proto.Friend.DELETE_FRIEND] = new deleteFriend_1.Cph_Friend_DeleteFriend();
handlers[proto.Friend.ACCEPT_FRIEND_REQUEST] = new acceptFriendRequest_1.Cph_Friend_AcceptFriendRequest();
handlers[proto.Friend.DENY_FRIEND_REQUEST] = new denyFriendRequest_1.Cph_Friend_DenyFriendRequest();
handlers[proto.Friend.SEND_POINT] = new sendPoint_1.Cph_Friend_SendPoint();
handlers[proto.Friend.PICKUP_POINT] = new pickupPoint_1.Cph_Friend_PickupPoint();
// ----------------------------------------------------------------------------
// packetsForBackToLastGameState
// gameState 는 유저의 현재 상태를 나타내며 로그인 시 NONE(0)으로 초기화 되며
// 유저의 마지막 상태까지 진입을 시켜주기 위해 lastGameState 가 존재하고
// lastGameState 이 gameState 와 동일해지는 시점에 NONE(0)으로 초기화 됩니다.
// 유저의 마지막 상태까지 진입을 하는데 사용되는 packet 을
// packetsForBackToLastGameState 에 정의 해둔다.
// ----------------------------------------------------------------------------
const packetsForBackToLastGameState = {
    [proto.Auth.ENTER_WORLD]: true,
    [proto.Auth.APP_GUARD_CHECK_CS]: true,
    [proto.Town.ENTER]: true,
    [proto.Town.LOAD_COMPLETE]: true,
    [proto.Ocean.ARRIVE]: true,
    [proto.Ocean.ENTER]: true,
    [proto.Ocean.LAND_ENTER]: true,
    [proto.Ocean.ARRIVE_VILLAGE]: true,
    [proto.Ocean.VILLAGE_ENTER]: true,
    [proto.Ocean.LOAD_COMPLETE]: true,
    [proto.Ocean.SALVAGE_ENTER]: true,
    [proto.Battle.LOAD_COMPLETE]: true,
    [proto.Battle.RESUME]: true,
    [proto.Battle.START]: true,
    [proto.Battle.START_CHALLENGE]: true,
    [proto.Battle.START_ARENA]: true,
    [proto.Battle.START_RAID]: true,
    [proto.Duel.START]: true,
    [proto.Duel.RESUME]: true,
    [proto.BattleReward.ENTER]: true,
    [proto.BattleReward.LOAD_COMPLETE]: true,
    [proto.LandExploreReward.RECEIVE_ENTER]: true,
    [proto.BattleLobby.ENTER]: true,
    [proto.Battle.CANCEL]: true,
    [proto.Ocean.ENTER_CONTINUOUS_SWEEP_REWARD]: true,
};
// ------------------------------------------------------------------------------------------------
// packetsCanBeCalledAlways
// 로그인 후 lastGameState 까지 복구가 되지 않아도 사용될 수 있는 packet
// ------------------------------------------------------------------------------------------------
const packetsCanBeCalledAlways = {
    [proto.Town.MOVE_CS]: true,
    [proto.Town.BANK_WITHDRAW_INSTALLMENT_SAVINGS]: true,
    [proto.Ocean.MOVE_CS]: true,
    [proto.Ocean.END_AUTO_SAILING]: true,
    [proto.Ocean.REVEAL_REGION]: true,
    [proto.Ocean.INSPECT_TOWN]: true,
    [proto.Ocean.ADD_QUESTION_PLACE]: true,
    [proto.Ocean.INSPECT_VILLAGE]: true,
    [proto.Ocean.REVEAL_WORLD_MAP_TILE]: true,
    [proto.Raid.GET_RAID_INFO]: true,
    [proto.Raid.GET_REWARD_STATES]: true,
};
// ------------------------------------------------------------------------------------------------
// Pulblic functions.
// ---------------------------------------------------------------------------------------------------
const exec = (user, packet) => {
    const handler = handlers[packet.type];
    const packetType = packet.type;
    if (!handler) {
        mlog_1.default.error('Unknown packet type.', {
            userId: user.userId,
            packetType: packetType,
        });
        throw new merror_1.MError('unknown-packet-type', merror_1.MErrorCode.UNKNOWN_PACKET_TYPE, packet.type);
    }
    const rawLastGameState = user.userState.getRawLastGameState();
    if (rawLastGameState) {
        // 로그인 후 lastGameState 까지 복구가 안 된 상황. 복구가 완료된 경우 rawLastGameState 값은 NONE(0)
        // 로그인 후 lastGameState 까지 복구가 안 된 상황에서는 아래 조건에 맞지 않는 경우
        // 서버로 요청할 수 없다.
        if (!packetsForBackToLastGameState[packetType] &&
            !Object.values(proto.Common).includes(packetType) &&
            !Object.values(proto.Etc).includes(packetType) &&
            !Object.values(proto.Temp).includes(packetType) &&
            !Object.values(proto.Admin).includes(packetType) &&
            !Object.values(proto.Dev).includes(packetType) &&
            !packetsCanBeCalledAlways[packetType]) {
            throw new merror_1.MError('can-not-be-used-packet-while-back-to-last-game-state', merror_1.MErrorCode.CANNOT_BE_USED_PACKET_WHILE_BACK_TO_LAST_GAME_STATE, {
                packetType: packet.type,
                lastGameState: rawLastGameState,
            });
        }
    }
    // 게임 상태 검증.
    if (!handler.testGameState(user)) {
        // mconf.invalidGameStateTolerance 에 지정된 milisec 이내에 잘못된 상태의 패킷이 오는 경우엔
        // 접속 종료가 되지 않도록, 너그러운 에러 처리를 한다.
        if (user.userState.canTolerateInvalidateGameState()) {
            throw new merror_1.MError('invalid-game-state-tolerated.', merror_1.MErrorCode.INVALID_GAME_STATE_TOLERATED, {
                packetType: packet.type,
                gameState: user.userState.getGameState(),
                lastGameState: user.userState.getLastGameState(),
                enterState: user.userState.getGameEnterState(),
                bEncounting: user.userEncount.getEncountState() ? true : false,
                bFriendlyEncounting: user.userFriendlyEncount.getEncountState() ? true : false,
                clashState: user.userClash.getMatchingState(),
            });
        }
        // 그 외의 경우엔 치명적 에러 처리.
        throw new merror_1.MError('invalid-game-state.', merror_1.MErrorCode.INVALID_GAME_STATE, {
            packetType: packet.type,
            gameState: user.userState.getGameState(),
            lastGameState: user.userState.getLastGameState(),
            enterState: user.userState.getGameEnterState(),
            bEncounting: user.userEncount.getEncountState() ? true : false,
            bFriendlyEncounting: user.userFriendlyEncount.getEncountState() ? true : false,
            clashState: user.userClash.getMatchingState(),
        });
    }
    return handler.exec(user, packet);
};
exports.exec = exec;
//# sourceMappingURL=index.js.map