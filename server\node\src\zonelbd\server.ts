// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import morgan from 'morgan';
import path from 'path';
import Container, { Service } from 'typedi';
import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import { MRedisConnPool } from '../redislib/connPool';
import { MError, MErrorCode } from '../motiflib/merror';
import * as cmsEx from '../cms/ex';
import stoppable from 'stoppable';
import PingChecker from './pingChecker';
import * as Sentry from '@sentry/node';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});
// -------------------------------------------------------------------------------------------------
// Api interfaces.
// -------------------------------------------------------------------------------------------------
@Service()
export class ZonelbService {
  townLbRedis: MRedisConnPool;
  oceanLbRedis: MRedisConnPool;
  monitorRedis: MRedisConnPool;
  pingChecker: PingChecker;

  constructor() {
    this.pingChecker = new PingChecker();
  }

  async init() {
    this.monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
    await this.monitorRedis.init('monitor-redis', mconf.monitorRedis);

    this.townLbRedis = Container.of('town-lb-redis').get(MRedisConnPool);
    await this.townLbRedis.init('town-lb-redis', mconf.townLbRedis);

    this.oceanLbRedis = Container.of('ocean-lb-redis').get(MRedisConnPool);
    await this.oceanLbRedis.init('ocean-lb-redis', mconf.oceanLbRedis);
  }

  async destory() {
    this.pingChecker.stopTick();
    await this.townLbRedis.destroy();
    await this.oceanLbRedis.destroy();
    await this.monitorRedis.destroy();
  }

  get(zoneType: cmsEx.ZoneType): MRedisConnPool {
    if (cmsEx.ZoneType.TOWN == zoneType) return this.townLbRedis;
    else if (cmsEx.ZoneType.OCEAN == zoneType) return this.oceanLbRedis;
    else {
      throw new MError('invalid-redis-zone-type', MErrorCode.INTERNAL_ERROR);
    }
  }
}

// -------------------------------------------------------------------------------------------------
// Module variables.
// -------------------------------------------------------------------------------------------------
const app = express();

app.disable('x-powered-by');
app.disable('etag');
app.disable('content-type');

const server = stoppable(http.createServer(app));
server.keepAliveTimeout = 0;

let stopping = false;

// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

function zonelbReqLog(tokens, req, res) {
  if (
    req.url === '/updateServerdPing' ||
    req.url === '/updateLobbydPing' ||
    req.url === '/health'
  ) {
    return;
  }

  mlog.info('zonelbd-req', {
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  });
  return null;
}

async function closeServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    server.stop((err) => {
      if (err) return reject(err);
      resolve();
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');
    await closeServer();

    mlog.info('unregister all serverd');

    const zonelbServie = Container.get(ZonelbService);
    await zonelbServie.destory();

    stopTogglet();

    mlog.info('server stopped');
    process.exitCode = 0;
  } catch (error) {
    mlog.error('graceful shutdown failed', { error: error.message });
    process.exit(1);
  }
}

// -------------------------------------------------------------------------------------------------
// Public functions.
// -------------------------------------------------------------------------------------------------

export async function start() {
  try {
    await mhttp.configd.registerInstance(
      process.env.WORLD_ID ? process.env.WORLD_ID : mconf.instance.worldId,
      mconf.appInstanceId,
      mconf.hostname
    );

    mutil.initSentry();

    // Init http clients.
    mhttp.init();

    await startTogglet();

    const zonelbServie = Container.get(ZonelbService);
    await zonelbServie.init();

    const bindAddress = mconf.apiService.bindAddress;
    const port = mconf.apiService.port;

    app.use(morgan(zonelbReqLog));
    app.use(bodyParser.json());
    mutil.registerHealthCheck(app);
    mutil.registerGarbageCollector(app);
    await dirAsApi.register(app, path.join(__dirname, 'api'));

    app.use(expressError.middleware);

    server.listen(port, bindAddress, () => {
      mlog.info('start listening ...', { bindAddress, port });
    });

    // config final sync
    const beforeVer = mconf.layoutVersion;
    await mhttp.configd.sync(beforeVer, isStopping, stop).then(() => {
      if (beforeVer < mconf.layoutVersion) {
        // do something
      }
    });

    setTimeout(() => {
      if (stopping) {
        return;
      }
      // zonelbd시작후 일정시간동안 타임아웃 처리를 유예하여
      // 이미 동작중인 존서버들의 ping처리가 가능하도록 한다.
      zonelbServie.pingChecker.startTick();
    }, mconf.ping.timeout);
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
}

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;
  await stopServer();
}
