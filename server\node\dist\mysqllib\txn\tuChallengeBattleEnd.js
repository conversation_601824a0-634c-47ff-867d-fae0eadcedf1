"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cmsEx = __importStar(require("../../cms/ex"));
const merror_1 = require("../../motiflib/merror");
const mysqlUtil_1 = require("../mysqlUtil");
const puStateUpdateGameState_1 = __importDefault(require("../sp/puStateUpdateGameState"));
const puPointUpdate_1 = __importDefault(require("../sp/puPointUpdate"));
const puBattleRewardCreate_1 = __importDefault(require("../sp/puBattleRewardCreate"));
const lodash_1 = __importDefault(require("lodash"));
const puQuestContextDelete_1 = __importDefault(require("../sp/puQuestContextDelete"));
const puMateUpdateExpLevel_1 = __importDefault(require("../sp/puMateUpdateExpLevel"));
const puMateUpdateFame_1 = __importDefault(require("../sp/puMateUpdateFame"));
const puUserUpdateEnergy_1 = __importDefault(require("../sp/puUserUpdateEnergy"));
const puSoftDataUpdateExpLevel_1 = __importDefault(require("../sp/puSoftDataUpdateExpLevel"));
const puSoftDataUpdateFreeTakeback_1 = __importDefault(require("../sp/puSoftDataUpdateFreeTakeback"));
const puChallengeUpdate_1 = __importDefault(require("../sp/puChallengeUpdate"));
function queryImpl(connection, userId, gameState, challengeUpdate, challengeQuestCmsId, mateExpChanges, userExpLevelChange, freeTakebackChange, lossEnergyChange, ducatChange, blueGemChange, leaderMateCmsId, battleFame, rewards) {
    return Promise.resolve()
        .then(() => {
        // uwo.u_users
        if (userExpLevelChange && userExpLevelChange.energyChange) {
            return (0, puUserUpdateEnergy_1.default)(connection, userId, userExpLevelChange.energyChange.energy, userExpLevelChange.energyChange.lastUpdateTimeUtc);
        }
        else if (lossEnergyChange) {
            return (0, puUserUpdateEnergy_1.default)(connection, userId, lossEnergyChange.energy, lossEnergyChange.lastUpdateTimeUtc);
        }
    })
        .then(() => {
        // uwo.u_states
        return (0, puStateUpdateGameState_1.default)(connection, userId, gameState);
    })
        .then(() => {
        // uwo.u_soft_data
        if (userExpLevelChange) {
            return (0, puSoftDataUpdateExpLevel_1.default)(connection, userId, userExpLevelChange.exp, userExpLevelChange.level);
        }
    })
        .then(() => {
        // uwo.u_soft_data
        if (freeTakebackChange) {
            return (0, puSoftDataUpdateFreeTakeback_1.default)(connection, userId, freeTakebackChange.usedFreeTurnTakebackCount, freeTakebackChange.usedFreePhaseTakebackCount, freeTakebackChange.updateTimeUtc);
        }
    })
        .then(() => {
        // uwo.u_points
        if (ducatChange) {
            return (0, puPointUpdate_1.default)(connection, userId, ducatChange);
        }
    })
        .then(() => {
        if (blueGemChange) {
            return (0, puPointUpdate_1.default)(connection, userId, blueGemChange);
        }
    })
        .then(() => {
        // uwo.u_challenges
        if (challengeUpdate) {
            return (0, puChallengeUpdate_1.default)(connection, userId, challengeUpdate);
        }
    })
        .then(() => {
        // uwo.u_quest_contexts
        return (0, puQuestContextDelete_1.default)(connection, userId, challengeQuestCmsId);
    })
        .then(() => {
        // uwo.u_mates
        if (mateExpChanges && mateExpChanges.length > 0) {
            return (0, puMateUpdateExpLevel_1.default)(connection, userId, cmsEx.JOB_TYPE.BATTLE, mateExpChanges);
        }
    })
        .then(() => {
        // uwo.u_mates
        if (leaderMateCmsId) {
            return (0, puMateUpdateFame_1.default)(connection, userId, leaderMateCmsId, cmsEx.JOB_TYPE.BATTLE, battleFame);
        }
    })
        .then(() => {
        // uwo.u_battle_rewards
        if (!lodash_1.default.isEmpty(rewards)) {
            const arr = [];
            for (const type of Object.keys(rewards)) {
                for (const id of Object.keys(rewards[type])) {
                    arr.push(rewards[type][id]);
                }
            }
            return (0, puBattleRewardCreate_1.default)(connection, userId, arr);
        }
        return undefined;
    })
        .catch((err) => {
        if (err instanceof merror_1.MError) {
            throw err;
        }
        else {
            throw new merror_1.MError(err.message, merror_1.MErrorCode.BATTLE_END_TXN_ERROR);
        }
    });
}
function tuChallengeBattleEnd(dbConnPool, userId, gameState, challengeUpdate, challengeQuestCmsId, mateExpChanges, userExpLevelChange, freeTakebackChange, lossEnergyChange, blueGemChange, ducatChange, leaderMateCmsId, battleFame, rewards) {
    return (0, mysqlUtil_1.withTxn)(dbConnPool, __filename, (connection) => {
        return queryImpl(connection, userId, gameState, challengeUpdate, challengeQuestCmsId, mateExpChanges, userExpLevelChange, freeTakebackChange, lossEnergyChange, ducatChange, blueGemChange, leaderMateCmsId, battleFame, rewards);
    });
}
exports.default = tuChallengeBattleEnd;
//# sourceMappingURL=tuChallengeBattleEnd.js.map