"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfflineSailingJob = void 0;
const lodash_1 = __importDefault(require("lodash"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mutil = __importStar(require("../motiflib/mutil"));
const sailConst_1 = require("./sailConst");
const typedi_1 = require("typedi");
const mutil_1 = require("../motiflib/mutil");
const server_1 = require("./server");
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const pwAutoSailingUpdateIsOfflineSailingDeactivated_1 = __importDefault(require("../mysqllib/sp/pwAutoSailingUpdateIsOfflineSailingDeactivated"));
const pwAutoSailingUpdate_1 = __importDefault(require("../mysqllib/sp/pwAutoSailingUpdate"));
const offlineSailingBotClient_1 = require("./offlineSailingBotClient");
const CMSConst = __importStar(require("../cms/const"));
const server_2 = require("./server");
// ----------------------------------------------------------------------------
// OfflineSailingJob
// ----------------------------------------------------------------------------
class OfflineSailingJob {
    constructor(autoSailingInfo) {
        this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.REGISTERED_TO_REDIS;
        this._bot = undefined;
        this._lastTcikTimeInMs = Date.now();
        this._lastOfflineSailingHeartBeatTimeUtc = (0, mutil_1.curTimeUtc)();
        this._startBotBeforeConnectWaitTimeUtc = 0;
        this._accountId = autoSailingInfo.accountId;
        this._userId = autoSailingInfo.userId;
        this._pubId = autoSailingInfo.pubId;
        this._fleetIndex = autoSailingInfo.fleetIndex;
        this._startTimeUtc = autoSailingInfo.startTimeUtc;
        this._destCmsId = autoSailingInfo.destCmsId;
        this._destType = autoSailingInfo.destType;
        this._path = JSON.parse(autoSailingInfo.path);
        this._bot = new offlineSailingBotClient_1.OfflineSailingBotClient(autoSailingInfo);
    }
    // ----------------------------------------------------------------------------
    get offlineSailingState() {
        return this._offlineSailingState;
    }
    // ----------------------------------------------------------------------------
    set offlineSailingState(value) {
        this._offlineSailingState = value;
    }
    // ----------------------------------------------------------------------------
    get userId() {
        return this._userId;
    }
    // ----------------------------------------------------------------------------
    set userId(value) {
        this._userId = value;
    }
    // ----------------------------------------------------------------------------
    getBot() {
        return this._bot;
    }
    //----------------------------------------------------------
    selectRandomLobbyd() {
        const sessions = server_2.tcpServer.getSessionManager().getSessions();
        if (sessions.length === 0) {
            return undefined;
        }
        // [todo] lobbyd 이외에 다른종류의 서버로 tcp 연결이 추가되면 서버타입체크도 추가되어야함
        const selectedIdx = mutil.randIntInc(0, sessions.length - 1);
        const selectedSession = sessions[selectedIdx];
        const url = selectedSession.getSegments().get('url');
        if (typeof url !== 'string') {
            return undefined;
        }
        return url;
    }
    //----------------------------------------------------------
    close() {
        mlog_1.default.verbose('closing job', { userId: this._userId });
        return Promise.resolve().then(() => {
            //[todo] lobbyd-saild간 서버간연결로 변경하면 lobbyd에 연결종료요청해서 응답받은뒤 반환한다
            this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
            return this._bot.close();
        });
    }
    //----------------------------------------------------------
    tickOnRegisteredToRedis() {
        // 먼저 자동항해 데이터의 무결성 검증
        // path가 비었는데 startTimeUtc값이 0이 아니어서 오프항해가 시작되는 경우 방어
        // 이게 발생하는 경우 자동항해 정보 저장방식에 구멍이 있다는 의미이므로 원인 추적 필요해서 에러로그 남김
        if (!this._path || lodash_1.default.isEmpty(this._path) || 0 === this._path.length) {
            mlog_1.default.error('[tickOnRegisteredToRedis] EMPTY path.. closing job', {
                accountId: this._accountId,
                userId: this._userId,
                startTimeUtc: this._startTimeUtc,
            });
            this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
            //실패인경우 worlddb의 autosailing 정보를 deactive시킨다..
            const { worldDbConnPool } = typedi_1.Container.get(server_1.SailService);
            (0, pwAutoSailingUpdate_1.default)(worldDbConnPool.getPool(), this._userId, this._fleetIndex, this._accountId, this._pubId, 0, 0, 0, '{}', '{}', '{}');
            return;
        }
        this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CHECKING_WORLD;
        mlog_1.default.verbose('requesting auth/getUserLastWorldId', {
            accountId: this._accountId,
            userId: this._userId,
        });
        mhttp_1.default.authd
            .getUserLastWorldId({
            sessionToken: this._accountId,
        })
            .then((res) => {
            mlog_1.default.verbose('received auth/getUserLastWorldId reply', {
                accountId: this._accountId,
                userId: this._userId,
                lastWorldId: res.lastWorldId,
            });
            if (!res.lastWorldId || res.lastWorldId != mconf_1.default.worldId) {
                mlog_1.default.info('/getUserLasWorldId failed closing bot');
                this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
                //실패인경우 worlddb의 autosailing 정보를 deactive시킨다..
                const { worldDbConnPool } = typedi_1.Container.get(server_1.SailService);
                return (0, pwAutoSailingUpdateIsOfflineSailingDeactivated_1.default)(worldDbConnPool.getPool(), this._userId, this._fleetIndex, 1 /**IsOfflineSailingDeactivated*/);
            }
            else {
                const url = this.selectRandomLobbyd();
                if (!url) {
                    mlog_1.default.warn('selectRandomLobbyd failed closing bot');
                    this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
                    return;
                }
                this._bot.setLobbydUrl(url);
                mlog_1.default.info('/lobbyd selected. starting before connect wait..', {
                    accountId: this._accountId,
                    userId: this._userId,
                    lobbydUrl: url,
                });
                this._startBotBeforeConnectWaitTimeUtc = (0, mutil_1.curTimeUtc)();
                this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.WORLD_CHECKED;
            }
        })
            .catch((err) => {
            mlog_1.default.error('error tickOnRegisteredToRedis:', {
                accountId: this._accountId,
                userId: this._userId,
                err: err.message,
            });
            this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
        });
    }
    // ----------------------------------------------------------------------------
    tickOnWorldChecked(curTimeUtc) {
        // wait before connect wait time
        const elapseTimeUtc = curTimeUtc - this._startBotBeforeConnectWaitTimeUtc;
        const delay = CMSConst.get('OfflineSailingStartDelay');
        if (elapseTimeUtc > delay) {
            this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.BOT_ACTIVATING;
            mlog_1.default.info('tickOnWorldChecked. before connect wait time over. activating bot!', {
                accountId: this._accountId,
                userId: this._userId,
            });
            return;
        }
        // after wait time passed select lobbyd and update redis data
        // [todo] redis slave(read only)
        const { userCacheRedis, sailRedis } = typedi_1.Container.get(server_1.SailService);
        const minHeartBeatTs = curTimeUtc - mconf_1.default.userHeartBeatInterval;
        userCacheRedis['getUserHeartBeat'](this._accountId, minHeartBeatTs)
            .then((bValid) => {
            if (bValid) {
                // loggedIn bot user should get out.
                this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
                mlog_1.default.info('tickOnWorldChecked. user logged in.. clsoing job', {
                    accountId: this._accountId,
                    userId: this._userId,
                });
                return;
            }
        })
            .catch((err) => mlog_1.default.error('heartBeatTick failed..', err.message));
    }
    // ----------------------------------------------------------------------------
    // 종료시 밖에서 작업 등록해제 처리등 할것
    tick(curTimeInMs) {
        let jobTickInterval = sailConst_1.DEFAULT_OFFLINE_SAILING_JOB_TICK_INTERVAL;
        if (mconf_1.default.offlineSailingTick && mconf_1.default.offlineSailingTick.jobTickInterval) {
            jobTickInterval = mconf_1.default.offlineSailingTick.jobTickInterval;
        }
        const elapsedTimeInMs = curTimeInMs - this._lastTcikTimeInMs;
        if (elapsedTimeInMs < jobTickInterval) {
            return true;
        }
        this._lastTcikTimeInMs = curTimeInMs;
        const curTimeUtc = Math.floor(curTimeInMs / 1000);
        const { userCacheRedis, sailRedis, bMaintenance } = typedi_1.Container.get(server_1.SailService);
        if (bMaintenance) {
            mlog_1.default.info('[OfflineSailingJob] tick. server in maintenance', {
                userId: this._userId,
            });
            return false;
        }
        if (this._bot.isDisconnected()) {
            //스케쥴종료체크(봇 전멸/접속끊어짐/로그인실패)
            mlog_1.default.info('OfflineSailingJob tick. bot disconnected', {
                userId: this._userId,
            });
            return false;
        }
        try {
            switch (this.offlineSailingState) {
                case sailConst_1.OFFLINE_SAILING_STATE.REGISTERED_TO_REDIS:
                    // do world check
                    this.tickOnRegisteredToRedis();
                    break;
                case sailConst_1.OFFLINE_SAILING_STATE.CHECKING_WORLD:
                    break;
                case sailConst_1.OFFLINE_SAILING_STATE.WORLD_CHECKED:
                    // wait before connect wait time
                    this.tickOnWorldChecked(curTimeUtc);
                    break;
                case sailConst_1.OFFLINE_SAILING_STATE.BOT_ACTIVATING:
                    // bot will connect to lobbyd and will execute enterworldasbot
                    this._bot.tick(curTimeInMs);
                    // will do stand-by for after connect wait time
                    break;
                case sailConst_1.OFFLINE_SAILING_STATE.CLOSING:
                    //[todo] 종료시 레디스 작업등록해제
                    mlog_1.default.info('OfflineSailingJob tick. clsoing job', {
                        userId: this._userId,
                    });
                    return false;
                default:
                    return false;
            }
        }
        catch (e) {
            this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
            mlog_1.default.error('OfflineSailingJob tick error!', {
                userId: this._userId,
                error: e.message,
                stack: e.stack,
            });
        }
        //레디스 업데이트 tick
        const updateInterval = Math.max(mconf_1.default.offlineSailingHeartBeatInterval / 4, 1);
        const elapsedSecs = curTimeUtc - this._lastOfflineSailingHeartBeatTimeUtc;
        if (elapsedSecs < updateInterval) {
            return true;
        }
        this._lastOfflineSailingHeartBeatTimeUtc = curTimeUtc;
        //[todo] userheartBeat체크는  redis read only connection(slave)에서만 하도록하자()
        const minOffSailTs = curTimeUtc - mconf_1.default.offlineSailingHeartBeatInterval;
        userCacheRedis['updateOfflineSailing'](this._accountId, this._userId, curTimeUtc, minOffSailTs, mconf_1.default.appId)
            .then((ret) => {
            if (0 < ret) {
                this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
                mlog_1.default.info('OfflineSailingJob tick. updateOfflineSailing failed', {
                    userId: this._userId,
                    ret,
                });
            }
        })
            .catch((error) => {
            this._offlineSailingState = sailConst_1.OFFLINE_SAILING_STATE.CLOSING;
            mlog_1.default.info('OfflineSailingJob tick. updateOfflineSailing error', {
                userId: this._userId,
                error: error.message,
                stack: error.stack,
            });
        });
        return true;
    }
}
exports.OfflineSailingJob = OfflineSailingJob;
//# sourceMappingURL=offlineSailingJob.js.map