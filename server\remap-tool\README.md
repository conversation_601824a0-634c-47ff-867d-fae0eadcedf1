# UWO GNID/NID Remapping Tool

UWO 게임의 데이터베이스와 Redis에서 사용자 ID(gnid/accountId, nid/pubId)를 일괄 변경하는 도구입니다.

## 시스템 요구사항

### 필수 환경

- **Node.js**: 16.0.0 이상 (권장: 18.x LTS 또는 20.x LTS)
- **TypeScript**: 5.8.3 이상
- **npm**: 7.0.0 이상 (Node.js와 함께 설치됨)

**주의**: Windows에서는 일부 command가 실행되지 않을수 있습니다. 가급적, linux/mac에서 실행하세요.

## 주요 기능

- **실제 데이터베이스 스키마 추출**: INFORMATION_SCHEMA를 사용하여 실제 데이터베이스에서 정확한 스키마 정보를 추출합니다
- **코드베이스 자동 분석**: SQL, Lua, TypeScript 파일을 분석하여 ID가 사용되는 모든 위치를 자동으로 찾습니다
- **CSV 자동 요약 분석**: 리맵핑 실행 시 CSV 데이터를 자동으로 분석하여 문제점을 사전에 발견합니다
- **CSV 기반 매핑**: 변경할 ID 매핑 정보를 CSV 파일로 관리합니다
- **다중 데이터베이스 지원**: Auth, World, User(샤딩) 데이터베이스를 모두 지원합니다
- **Redis 지원**: 여러 Redis 인스턴스의 키 업데이트를 지원합니다
  - **account:{accountId}**: 계정 정보 (토큰, 월드ID, 하트비트 등)
  - **user:{userId}**: 유저 캐시 정보
  - **prologueGnids:{worldId}**: 월드별 프롤로그 상태 관리 (Sorted Set)
  - **deletionPubIds**: 삭제 대상 pubId 목록 (List)
- **월드별 처리**: 특정 월드만 선택하여 처리할 수 있습니다
- **드라이런 모드**: 실제 변경 없이 영향도를 미리 확인할 수 있습니다
- **자동 검증**: 변경 후 데이터 일관성을 자동으로 검증합니다
- **배치 처리**: 대용량 데이터를 안전하게 배치 단위로 처리합니다
- **백업/복원**: 데이터베이스와 Redis 데이터를 안전하게 백업하고 복원할 수 있습니다
  - **압축 백업**: ZIP 형식으로 압축하여 저장 공간을 절약합니다
  - **타임스탬프 관리**: 백업 시점을 정확하게 관리합니다
  - **선택적 복원**: 특정 데이터베이스나 월드만 선택하여 복원할 수 있습니다
- **🔧 프로덕션급 스크립트 생성**: 실제 운영환경에서 사용 가능한 고품질 스크립트
  - **🔍 실제 스키마 기반**: `analyze` 명령어로 분석된 실제 테이블/Redis 패턴 사용
  - **⚡ 성능 최적화**: 인덱스 필드 업데이트 최적화 및 배치 처리
  - **🛡️ 안전한 실행**: 트랜잭션 기반 롤백 가능한 SQL 스크립트
  - **🔍 SCAN 기반**: Redis 블로킹 방지를 위한 SCAN 명령어 사용
  - **📊 모니터링**: 실시간 업데이트 결과 확인 쿼리 포함
  - **🗂️ 자동 관리**: 스크립트 폴더 자동 정리 및 월드별 분리
  - **🎯 remap 동등성**: run 명령어와 동일한 결과 보장

## 환경 확인

설치하기 전에 시스템 환경을 확인하세요:

```bash
# Node.js 버전 확인 (16.0.0 이상 필요)
node --version

# npm 버전 확인 (7.0.0 이상 필요)
npm --version

# TypeScript 전역 설치 (선택사항)
npm install -g typescript

# TypeScript 버전 확인
tsc --version
```

## 📁 프로젝트 구조

```
remap-tool/
├── src/                    # TypeScript 소스 코드
├── dist/                   # 컴파일된 JavaScript 코드
├── config/                 # 설정 파일들
├── artifacts/              # 생성된 모든 아티팩트
│   ├── schemas/            # 추출된 데이터베이스 스키마
│   ├── reports/            # 분석 결과 및 보고서
│   └── scripts/            # 생성된 SQL/Lua 스크립트
├── backups/                # 백업 파일들
├── package.json            # Node.js 의존성
├── tsconfig.json           # TypeScript 설정
└── README.md               # 이 파일
```

## 설치 및 설정

### 1. 의존성 설치

```bash
cd server/remap-tool
npm install
```

### 2. 설정 파일 초기화

```bash
npm run build
npm start -- init
```

### 3. 설정 파일 편집

`config/database.json5` 파일을 편집하여 연결 정보를 설정합니다.

> **JSON5 형식 사용**: 설정 파일은 JSON5 형식을 사용하여 주석과 trailing comma를 지원합니다.

#### database.json5 예제 (service_layout/local.json5 호환됨)

```json5
{
  // UWO Remapping Tool Configuration
  // This file uses JSON5 format which supports comments and trailing commas
  "sharedConfig": {
    "mysqlAuthDb": {
      "host": "localhost",
      "port": 3306,
      "user": "motif_dev",
      "password": "dev123$",
      "database": "uwo_auth",
      "multipleStatements": true,
      "supportBigNumbers": true,
      "bigNumberStrings": true,
      "connectTimeout": 3000,
      "connectionLimit": 10,
      "flags": "-FOUND_ROWS",
      "driver": "mysql"
    },
    "authRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 19
      },
      "scriptDir": "auth",
      "pool": {
        "min": 1,
        "max": 4
      }
    },
    "monitorRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 3
      },
      "scriptDir": "monitor",
      "pool": {
        "min": 1,
        "max": 4
      }
    },
    "orderRedis": {
      "redisCfg": {
        "host": "localhost",
        "port": 6379,
        "db": 18
      },
      "scriptDir": "order",
      "pool": {
        "min": 1,
        "max": 4
      }
    }
  },
  "worlds": [
    {
      "id": "UWO-KR-01",
      "mysqlUserDb": {
        "shardFunction": "userDbShardDev",
        "sqlDefaultCfg": {
          "host": "localhost",
          "port": 3306,
          "user": "motif_dev",
          "password": "dev123$",
          "database": "uwo_user",
          "multipleStatements": true,
          "supportBigNumbers": true,
          "bigNumberStrings": true,
          "connectTimeout": 3000,
          "connectionLimit": 10,
          "flags": "-FOUND_ROWS",
          "driver": "mysql"
        },
        "shards": [
          {
            "shardId": 0,
            "sqlCfg": {
              "database": "uwo_user_00"
            }
          },
          {
            "shardId": 1,
            "sqlCfg": {
              "database": "uwo_user_01"
            }
          }
        ]
      },
      "mysqlWorldDb": {
        "host": "localhost",
        "port": 3306,
        "user": "motif_dev",
        "password": "dev123$",
        "database": "uwo_world",
        "multipleStatements": true,
        "supportBigNumbers": true,
        "bigNumberStrings": true,
        "connectTimeout": 3000,
        "connectionLimit": 10,
        "flags": "-FOUND_ROWS",
        "driver": "mysql"
      },
      "userCacheRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 13
        },
        "pool": {
          "min": 1,
          "max": 4
        },
        "scriptDir": "userCache"
      },
      "nationRedis": {
        "redisCfg": {
          "host": "localhost",
          "port": 6379,
          "db": 10
        },
        "scriptDir": "nation",
        "pool": {
          "min": 1,
          "max": 4
        }
      }
    }
  ]
}
```

## 사용법

> **참고**: `npm start -- [명령어]` 또는 `node dist/index.js [명령어]` 형태로 실행할 수 있습니다.

### 1. 데이터베이스 스키마 추출

먼저 실제 데이터베이스에서 스키마를 추출해야 합니다.

#### 연결 테스트

```bash
npm start -- extract-schema --test-only
# 또는
node dist/index.js extract-schema --test-only
```

#### 스키마 추출

```bash
npm start -- extract-schema
# 또는
node dist/index.js extract-schema -o ./artifacts/schemas
```

> **참고**:
> - `config/database.json5` 설정을 사용하여 데이터베이스에 연결합니다
> - World와 User 데이터베이스는 동일한 스키마를 가지므로 첫 번째 인스턴스만 추출합니다
> - Auth, World, User 각각의 스키마가 JSON 파일로 저장됩니다

### 2. 연결 상태 확인 (리맵핑용)

```bash
npm start -- status
# 또는
node dist/index.js status
```

### 3. 코드베이스 분석

```bash
npm start -- analyze -s ./artifacts/schemas
# 또는
npm start -- analyze --schema-dir ./artifacts/schemas

# 스키마 디렉토리를 지정하지 않으면 기본적으로 ./artifacts/schemas 사용
npm start -- analyze
```

### 4. CSV 파일 준비

`remap.csv` 파일을 다음 형식으로 준비합니다:

```csv
seq,uwo_Gnid,uwo_Nid,uwogl_Gnid,uwogl_Nid,gameServerId
1,old_gnid_1,old_nid_1,new_gnid_1,new_nid_1,world1
2,old_gnid_2,old_nid_2,new_gnid_2,new_nid_2,world1
```

### 5. CSV 데이터 요약 (자동 실행)

**리맵핑 실행 시 자동으로 CSV 요약 분석이 먼저 수행됩니다.**

수동으로 요약만 실행하려면:

**Linux/macOS:**
```bash
npm start -- summary -f remap.csv
```

**Windows (권장):**
```bash
node dist/index.js summary -f remap.csv
```

**직접 실행 (모든 OS - 가장 확실한 방법):**
```bash
node dist/index.js summary -f remap.csv
```

이 분석은 다음을 포함합니다:
- **기본 통계**: 총 레코드 수, 고유 계정/게임계정 수
- **월드별 분포**: 각 게임 서버별 데이터 분포
- **중복 검사**: seq, 계정-게임계정 조합, 타겟 조합 중복
- **데이터 검증**: 필수 필드 누락, 빈 값, 형식 오류
- **계정 매핑**: 소스-타겟 계정 매핑 관계

> **참고**: `run` 명령어 실행 시 1단계로 자동 실행되므로 별도로 실행할 필요가 없습니다.

### 6. 🔧 SQL/Lua 스크립트 생성 (실제 스키마 기반)

실제 데이터베이스를 업데이트하지 않고 SQL 및 Lua 스크립트 파일만 생성합니다.
**중요**: `analyze` 명령어로 분석된 실제 테이블 구조와 Redis 키 패턴을 기반으로 생성됩니다.

**기본 사용법:**
```bash
# 1단계: 코드베이스 분석 (필수)
node dist/index.js analyze

# 2단계: 분석 결과 기반 스크립트 생성
node dist/index.js generate -f remap.csv

# 특정 월드만 스크립트 생성
node dist/index.js generate -f remap.csv -w UWO-KR-01

# 출력 디렉토리 지정
node dist/index.js generate -f remap.csv -o ./artifacts/scripts
```

**스크립트 생성 과정:**
1. **🔍 코드베이스 분석**: 실제 테이블 구조와 Redis 키 패턴 분석
2. **📊 CSV 데이터 분석**: 리맵핑 대상 데이터 분석
3. **🗑️ 폴더 정리**: 기존 artifacts/scripts 폴더 완전 삭제 후 재생성
4. **🔧 스크립트 생성**: 분석된 실제 구조 기반으로 정확한 스크립트 생성

**생성되는 파일 구조 (올바른 분리):**
```
artifacts/scripts/
├── 📁 전역 스크립트 (월드 독립적)
│   ├── auth_update.sql              (9,788개 레코드)
│   ├── world_update.sql             (9,788개 레코드)
│   ├── auth_redis_update.lua        (1개 패턴)
│   ├── userCache_redis_update.lua   (3개 패턴)
│   └── order_redis_update.lua       (1개 패턴)
├── 📁 UWO-KR-07/ (월드별 user 샤드)
│   ├── user_shard_0_update.sql      (65개 레코드)
│   ├── user_shard_1_update.sql      (64개 레코드)
│   ├── user_shard_2_update.sql      (74개 레코드)
│   └── user_shard_3_update.sql      (56개 레코드)
├── 📁 UWO-KR-04/ (가장 큰 월드)
│   ├── user_shard_0_update.sql      (562개 레코드)
│   ├── user_shard_1_update.sql      (638개 레코드)
│   ├── user_shard_2_update.sql      (588개 레코드)
│   └── user_shard_3_update.sql      (572개 레코드)
└── ... (다른 월드들)
```

**스크립트 특징:**
- **🔍 실제 스키마 기반**: `analyze` 명령어로 분석된 실제 테이블과 Redis 패턴 사용
- **🎯 정확한 분리**: 전역 스크립트(auth, world)와 월드별 스크립트(user 샤드) 올바른 분리
- **⚡ 성능 최적화**: 인덱스 필드 업데이트 최적화 전략 적용
- **🛡️ 안전한 처리**: 트랜잭션 기반 롤백 가능한 업데이트
- **🔍 SCAN 기반**: Redis KEYS 대신 SCAN 명령어 사용으로 블로킹 방지
- **📊 실시간 모니터링**: 업데이트 결과 확인 쿼리 포함
- **🗂️ 자동 정리**: 스크립트 생성 전 기존 폴더 자동 정리
- **📈 배치 처리**: 대용량 데이터 안전한 배치 단위 처리
- **🌐 전역 관리**: auth, world, userCache, order는 전역 스크립트로 생성
- **👥 월드별 관리**: user 샤드는 월드별 폴더에 독립적으로 생성

### 7. 드라이런 실행

실제 변경 전에 영향도를 확인합니다:

```bash
npm start -- run -f remap.csv --dry-run
```

특정 월드만 처리:

```bash
npm start -- run -f remap.csv --dry-run -w world1
```

### 8. 고급 스크립트 생성 기능

생성된 스크립트는 프로덕션 환경에서 안전하게 사용할 수 있도록 다음과 같은 고급 기능들을 포함합니다:

#### **SQL 스크립트 최적화 (실제 분석 기반)**
```sql
-- 실제 분석된 테이블 구조 기반 업데이트
-- 경고: 분석된 사용자 테이블이 없습니다. 기본 테이블을 사용합니다.

-- 기본 사용자 테이블 업데이트
UPDATE user SET accountId = '1677744640600024751' WHERE accountId = '1653034315044004192';
UPDATE user SET accountId = '1677826942518022136' WHERE accountId = '1661316147585001988';

-- 인덱스 필드 업데이트 최적화
SET SESSION foreign_key_checks = 0;
SET SESSION unique_checks = 0;

-- 통계 정보 업데이트
ANALYZE TABLE user;
```

**분석 결과 반영:**
- 실제 존재하는 테이블만 업데이트 대상에 포함
- 분석된 컬럼명 (accountId, pubId 등) 정확히 사용
- 존재하지 않는 테이블은 자동으로 제외

#### **Redis Lua 스크립트 최적화 (실제 분석 기반)**
```lua
-- 분석된 키 패턴들 (실제 코드베이스에서 발견된 패턴)
-- 패턴 1: account:{accountId}
updated_keys = updated_keys + scan_and_update_keys("account:" .. old_gnid .. "", old_gnid, new_gnid)
-- 패턴 2: townUserWeeklyInvestmentReport:{nid}
updated_keys = updated_keys + scan_and_update_keys("townUserWeeklyInvestmentReport:{nid}", old_nid, new_nid)
-- 패턴 3: user:{userId}
updated_keys = updated_keys + scan_and_update_keys("user:{userId}", old_nid, new_nid)

-- SCAN 기반 안전한 키 검색 (KEYS 명령어 대신)
local function scan_and_update_keys(pattern, old_value, new_value)
    local cursor = "0"
    repeat
        local result = redis.call('SCAN', cursor, 'MATCH', pattern, 'COUNT', 100)
        cursor = result[1]
        local keys = result[2]

        for _, key in ipairs(keys) do
            local key_type = redis.call('TYPE', key)['ok']
            if key_type == 'string' then
                -- String 타입 처리
            else
                -- 복합 타입 RENAME 처리
                redis.call('RENAME', key, new_key)
            end
        end
    until cursor == "0"
end
```

**분석 결과 반영:**
- **실제 발견된 키 패턴만 사용**: 추측이 아닌 실제 코드에서 발견된 패턴
- **Redis 인스턴스별 분리**: userCache, order, auth 등 실제 인스턴스별 스크립트
- **정확한 키 구조**: {accountId}, {nid} 등 실제 사용되는 플레이스홀더

#### **스크립트 실행 가이드 (올바른 순서)**

**1단계: 전역 스크립트 실행**
```bash
# auth 데이터베이스 업데이트
mysql -u username -p auth < artifacts/scripts/auth_update.sql

# world 데이터베이스 업데이트
mysql -u username -p world < artifacts/scripts/world_update.sql

# 전역 Redis 업데이트
redis-cli -h auth-redis --eval artifacts/scripts/auth_redis_update.lua
redis-cli -h user-cache-redis --eval artifacts/scripts/userCache_redis_update.lua
redis-cli -h order-redis --eval artifacts/scripts/order_redis_update.lua
```

**2단계: 월드별 user 샤드 실행**
```bash
# UWO-KR-07 월드 예시
mysql -u username -p user_shard_0 < artifacts/scripts/UWO-KR-07/user_shard_0_update.sql
mysql -u username -p user_shard_1 < artifacts/scripts/UWO-KR-07/user_shard_1_update.sql
mysql -u username -p user_shard_2 < artifacts/scripts/UWO-KR-07/user_shard_2_update.sql
mysql -u username -p user_shard_3 < artifacts/scripts/UWO-KR-07/user_shard_3_update.sql
```

**3단계: 실행 전 체크리스트**
- [ ] 전역 데이터베이스 백업 완료 (auth, world)
- [ ] 전역 Redis 백업 완료 (auth, userCache, order)
- [ ] 월드별 user 샤드 백업 완료
- [ ] 스크립트 내용 검토 완료
- [ ] 테스트 환경에서 검증 완료

### 9. 실제 실행

⚠️ **주의**: 실제 데이터가 변경됩니다!

```bash
npm start -- run -f remap.csv
```

### 10. 검증만 실행

기존 데이터의 일관성을 확인합니다:

```bash
npm start -- verify -f remap.csv
```

### 11. 백업 생성

리맵핑 작업 전에 데이터를 안전하게 백업합니다:

```bash
# 전체 백업 (모든 데이터베이스 + Redis)
npm start -- backup --description "리맵핑 작업 전 백업"

# 특정 월드만 백업
npm start -- backup -w UWO-KR-01 --description "UWO-KR-01 백업"

# 특정 데이터베이스만 백업
npm start -- backup -d auth,user --description "Auth, User DB 백업"

# Redis 제외하고 백업
npm start -- backup --no-redis --description "DB만 백업"

# 압축하지 않고 백업
npm start -- backup --no-compress --description "압축 없는 백업"
```

### 9. 백업 목록 조회

생성된 백업 목록을 확인합니다:

```bash
npm start -- list-backups
```

### 10. 백업 복원

문제 발생 시 백업에서 데이터를 복원합니다:

```bash
# 전체 복원
npm start -- restore -t 2024-01-15_14-30-00

# 특정 월드만 복원
npm start -- restore -t 2024-01-15_14-30-00 -w UWO-KR-01

# 특정 데이터베이스만 복원
npm start -- restore -t 2024-01-15_14-30-00 -d user

# Redis 제외하고 복원
npm start -- restore -t 2024-01-15_14-30-00 --no-redis

# 강제 복원 (확인 없이)
npm start -- restore -t 2024-01-15_14-30-00 --force
```

## 권장 워크플로우

### 🔄 전체 프로세스

```bash
# 1. 데이터베이스 스키마 추출
npm start -- extract-schema --test-only  # 연결 테스트
npm start -- extract-schema              # 스키마 추출

# 2. 코드베이스 분석 (스크립트 생성시 필수)
npm start -- analyze  # 기본적으로 ./artifacts/schemas 사용

# 3. CSV 파일 준비 (수동)
# remap.csv 파일을 작성합니다. (플랫폼팀 제공)

# 4. CSV 요약 분석 (선택사항)
node dist/index.js summary -f remap.csv

# 5. 스크립트 생성 (선택사항 - 수동 실행용, analyze 결과 기반)
node dist/index.js generate -f remap.csv

# 6. 드라이런으로 영향도 확인 (CSV 요약 자동 포함)
# Linux/macOS: npm start -- run -f remap.csv --dry-run
# Windows:
node dist/index.js run -f remap.csv --dry-run

# 7. 백업 생성 (중요!)
# Linux/macOS: npm start -- backup --description "리맵핑 작업 전 백업"
# Windows:
node dist/index.js backup --description "리맵핑 작업 전 백업"

# 8. 실제 리맵핑 실행 (자동 실행) 또는 스크립트 실행 (수동 실행)
# 자동 실행:
# Linux/macOS: npm start -- run -f remap.csv
# Windows:
node dist/index.js run -f remap.csv

# 수동 실행 (분석 기반 스크립트):
# 1) 스크립트 생성: node dist/index.js generate -f remap.csv
# 2) 전역 SQL 실행: mysql -u user -p auth < artifacts/scripts/auth_update.sql
# 3) 전역 Redis 실행: redis-cli --eval artifacts/scripts/userCache_redis_update.lua
# 4) 월드별 실행: mysql -u user -p user_shard_0 < artifacts/scripts/UWO-KR-07/user_shard_0_update.sql

# 9. 결과 검증
# Linux/macOS: npm start -- verify -f remap.csv
# Windows:
node dist/index.js verify -f remap.csv
```

### ⚡ 빠른 시작

#### **Linux/macOS**
```bash
# 설정 파일 초기화 및 편집
npm start -- init
# config/database.json5 파일을 편집하여 DB 연결 정보 설정

# 스키마 추출 및 분석
npm start -- extract-schema && npm start -- analyze

# 리맵핑 실행 (CSV 파일 준비 후) - CSV 요약 자동 포함
npm start -- run -f remap.csv --dry-run
```

#### **Windows (권장)**
```bash
# 설정 파일 초기화 및 편집
npm run init
# config/database.json 파일을 편집하여 DB 연결 정보 설정

# 스키마 추출 및 분석
npm run extract-schema && npm run analyze

# CSV 요약 분석
node dist/index.js summary -f remap.csv

# 스크립트 생성 (선택사항)
node dist/index.js generate -f remap.csv

# 리맵핑 실행 (CSV 파일 준비 후) - CSV 요약 자동 포함
node dist/index.js run -f remap.csv --dry-run
```

#### **직접 실행 (모든 OS - 가장 확실한 방법)**
```bash
# remap-tool 디렉토리에서
node dist/index.js summary -f remap.csv
node dist/index.js generate -f remap.csv
node dist/index.js run -f remap.csv --dry-run
node dist/index.js backup --description "백업 설명"
node dist/index.js verify -f remap.csv
```

## 명령어 옵션

### extract-schema 명령어

```bash
npm start -- extract-schema [옵션]
```

- `-o, --output-dir <dir>`: 스키마 파일 출력 디렉토리 (기본값: ./artifacts/schemas)
- `--test-only`: 데이터베이스 연결 테스트만 수행

### analyze 명령어

```bash
npm start -- analyze [옵션]
```

- `-s, --schema-dir <dir>`: 스키마 파일 디렉토리 (기본값: ./artifacts/schemas)
- `-c, --config-dir <dir>`: 설정 파일 디렉토리 (기본값: ./config)
- `-l, --log-level <level>`: 로그 레벨 (debug/info/warn/error)
- `-o, --output-dir <dir>`: 출력 디렉토리 (기본값: ./artifacts/reports)

### summary 명령어

```bash
npm start -- summary [옵션]
```

- `-f, --csv-file <file>`: CSV 파일 경로 (필수)
- `-o, --output-dir <dir>`: 출력 디렉토리 (기본값: ./artifacts/reports)
- `-l, --log-level <level>`: 로그 레벨 (debug/info/warn/error)

### run 명령어

```bash
npm start -- run [옵션]
```

- `-f, --csv-file <file>`: CSV 파일 경로 (필수)
- `-w, --world-id <worldId>`: 특정 월드만 처리
- `-d, --dry-run`: 드라이런 모드 (실제 변경 없음)
- `-s, --skip-verification`: 검증 단계 건너뛰기
- `-b, --batch-size <size>`: 배치 크기 (기본값: 100)
- `-c, --config-dir <dir>`: 설정 파일 디렉토리 (기본값: ./config)
- `-l, --log-level <level>`: 로그 레벨 (debug/info/warn/error)
- `-o, --output-dir <dir>`: 출력 디렉토리 (기본값: ./artifacts/reports)

### verify 명령어

```bash
npm start -- verify [옵션]
```

- `-f, --csv-file <file>`: CSV 파일 경로 (필수)
- `-w, --world-id <worldId>`: 특정 월드만 검증
- `-c, --config-dir <dir>`: 설정 파일 디렉토리 (기본값: ./config)
- `-l, --log-level <level>`: 로그 레벨 (debug/info/warn/error)
- `-o, --output-dir <dir>`: 출력 디렉토리 (기본값: ./artifacts/reports)

### clean 명령어

```bash
npm start -- clean [옵션]
```

- `-o, --output-dir <dir>`: 정리할 출력 디렉토리 (기본값: ./artifacts/reports)

출력 디렉토리의 모든 파일을 삭제합니다.

## Redis 키 타입별 처리

도구는 코드베이스 분석을 통해 Redis 키 타입을 정적으로 추론하고 타입에 맞는 처리를 수행합니다:

#### 분석 방법
1. **Lua 스크립트 분석**: Redis 명령어 사용 패턴 분석
2. **TypeScript 코드 분석**: Redis 클라이언트 메서드 호출 패턴 분석
3. **정적 매핑**: 알려진 키 패턴과 사용 명령어 매핑

#### 명령어 패턴 기반 타입 추론
- **Hash 타입**: `HSET`, `HGET`, `HMSET`, `HMGET`, `HGETALL` 명령어 사용
- **Sorted Set 타입**: `ZADD`, `ZREM`, `ZSCORE`, `ZCARD` 명령어 사용
- **List 타입**: `RPUSH`, `LRANGE`, `LREM` 명령어 사용
- **String 타입**: `SET`, `GET` 명령어 사용

#### 실제 분석 결과
- `account:{accountId}` → Hash (HMSET, HMGET 사용)
- `prologueGnids:{worldId}` → Sorted Set (ZADD, ZREM 사용)
- `deletionPubIds` → List (RPUSH, LRANGE 사용)
- `townUserWeeklyInvestmentReport:{nid}` → String (SET 사용)

### 지원되는 Redis 키 타입
- **String**: 단순 문자열 값
- **Hash**: 필드-값 쌍의 맵
- **List**: 순서가 있는 문자열 목록
- **Set**: 중복 없는 문자열 집합
- **Sorted Set (zset)**: 점수가 있는 정렬된 집합
- **Stream**: 로그 형태의 데이터 스트림

### TypeScript 코드 분석 개선
도구는 TypeScript 파일에서 다음과 같은 Redis 키 패턴을 자동으로 감지합니다:

- **문자열 리터럴**: `'townUserWeeklyInvestmentReport:' + nid`
- **템플릿 리터럴**: `` `account:${accountId}` ``
- **스캔 패턴**: `'townUserWeeklyInvestmentReport:*'`
- **문자열 연결**: 동적으로 생성되는 키 패턴

### Hash 필드 처리 상세

#### `user:{userId}` 키 처리
- **키 패턴**: `user:*` (모든 user 키 검색)
- **처리 대상**: Hash 타입 키만 처리
- **필드 처리**:
  - `pubId` 필드: 값이 `uwo_Nid`와 일치하면 `uwogl_Nid`로 변경
  - `accountId` 필드: 값이 `uwo_Gnid`와 일치하면 `uwogl_Gnid`로 변경
  - 기타 필드: 값에 ID가 포함된 경우 문자열 교체

## 출력 파일

실행 후 `artifacts` 디렉토리에 다음 파일들이 생성됩니다:

### 스키마 파일 (artifacts/schemas 디렉토리)
- **`schema-uwo_auth.json5`**: Auth 데이터베이스 스키마 (JSON5 형식)
- **`schema-uwo_world.json5`**: World 데이터베이스 스키마 (JSON5 형식)
- **`schema-uwo_user_00.json5`**: User 데이터베이스 스키마 (JSON5 형식, 첫 번째 샤드 기준)

### JSON5 결과 파일
- **`analysis-result.json5`**: 코드베이스 분석 결과 (JSON5 형식)
- **`remap-result.json5`**: 리맵핑 실행 결과 (JSON5 형식)
- **`verification-result.json5`**: 검증 결과 (JSON5 형식, 검증 실행 시)
- **`summary-result.json`**: CSV 데이터 요약 결과 (JSON 형식)

### HTML 보고서 파일
- **`analysis-report.html`**: 코드베이스 분석 보고서 (HTML)
- **`summary-report.html`**: CSV 데이터 요약 보고서 (HTML)
- **`remap-report.html`**: 리맵핑 실행 보고서 (HTML)
- **`verification-report.html`**: 검증 결과 보고서 (HTML)

### 백업 파일 (backup 디렉토리)
- **`YYYY-MM-DD_HH-mm-ss-sssZ.zip`**: 압축된 백업 파일
  - `metadata.json5`: 백업 메타데이터 (JSON5 형식)
  - `data.json5`: 백업 데이터 (JSON5 형식)

> **JSON5 형식**: 모든 JSON 파일은 JSON5 형식을 사용하여 주석과 trailing comma를 지원합니다.

### HTML 보고서 내용

#### 분석 보고서 (`analysis-report.html`)
- **분석 통계**: 데이터베이스 테이블 수, Redis 키 패턴 수, 분석 시간
- **데이터베이스 테이블**: gnid/nid를 사용하는 테이블 목록과 상세 정보
- **Redis 키 패턴**: 리맵핑 대상 Redis 키 패턴, 타입, 키 수, 설명

#### 리맵핑 보고서 (`remap-report.html`)
- **실행 요약**: 처리된 레코드 수, 성공률, 실행 시간
- **통합 테이블**: 월드별로 구분된 데이터베이스 및 Redis 업데이트 결과
  - 월드 컬럼에 해당 월드 표시 (월드 무관한 경우 "-" 표시)
  - 동일 월드의 여러 행은 rowspan으로 셀 병합
- **오류 및 경고**: 발생한 오류와 경고 목록

#### 검증 보고서 (`verification-report.html`)
- **검증 요약**: 검증 항목 수, 통과/실패 수, 성공률
- **검증 세부사항**: 각 검증 항목의 상세 결과

## CSV 파일 형식

CSV 파일은 다음과 같은 형식을 사용합니다:

```csv
seq,uwo_Gnid,uwo_Nid,uwogl_Gnid,uwogl_Nid,gameServerId
1,1653034315044004192,1661316412016004425,1677744640600024751,1750954724682024352,UWO-KR-07
2,1653034315044004192,1662106676349004645,1677744640600024751,1750954724685024564,UWO-KR-09
3,1653034315044004192,1693796335741004046,1677744640600024751,1750954724687024588,UWO-KR-03
4,1653034315044004192,1665987421405004785,1677744640600024751,1750954724689024127,UWO-KR-04
    .
    .
    .
```

### 컬럼 설명

- **seq**: 순서 번호 (중복 불가)
- **uwo_Gnid**: 원본 계정 ID (gnid/accountId)
- **uwo_Nid**: 원본 NID (nid/pubId)
- **uwogl_Gnid**: 새로운 계정 ID (gnid/accountId)
- **uwogl_Nid**: 새로운 NID (nid/pubId)
- **gameServerId**: 게임 서버 ID (예: UWO-KR-01, UWO-KR-04, UWO-AU-01)

### 중복 검사 규칙

- **seq**: 개별 중복 검사
- **(uwo_Gnid, uwo_Nid)**: 조합 키로 중복 검사
- **(uwogl_Gnid, uwogl_Nid)**: 조합 키로 중복 검사

### gameServerId 형식

하이픈(`-`)을 포함한 다음 형식들을 지원합니다:
- `UWO-KR-01` (한국 서버)
- `UWO-AU-01` (호주 서버)
- 기타 영숫자와 하이픈 조합 (최대 32자)

## 지원하는 ID 타입

- **gnid / accountId**: 계정 식별자 (플랫폼 서비스에서 발급, 이 도구에서는 동일한 의미)
- **nid / pubId**: 공개 사용자 식별자 (플랫폼 서비스에서 발급, 이 도구에서는 동일한 의미)
- **userId**: 게임서버 내부 사용자 식별자 (게임서버에서 발급, pubId(nid)와는 다른 값)

## 주의사항

1. **백업 필수**: 실행 전 반드시 데이터베이스와 Redis 백업을 수행하세요
2. **테스트 환경**: 먼저 테스트 환경에서 충분히 검증하세요
3. **서비스 중단**: 실행 중에는 해당 서비스를 중단하는 것을 권장합니다
4. **권한 확인**: 데이터베이스와 Redis에 대한 적절한 권한이 있는지 확인하세요
5. **디스크 공간**: 충분한 디스크 공간이 있는지 확인하세요

## 문제 해결

### 데이터베이스 연결 오류

스키마 추출 시 연결 오류가 발생하면:

```bash
# 연결 테스트
npm start -- extract-schema --test-only

# 설정 파일 확인
cat config/database.json5
```

`config/database.json5` 파일의 데이터베이스 연결 정보를 확인하고 수정하세요.

### 리맵핑 연결 오류

```bash
npm start -- status
```

명령어로 연결 상태를 확인하고 설정 파일을 점검하세요.

### 스키마 파일 없음

분석 시 스키마 파일이 없다면:

```bash
# 먼저 스키마 추출
npm start -- extract-schema

# 그 다음 분석
npm start -- analyze -s ./artifacts/schemas
```

### 메모리 부족

배치 크기를 줄여서 실행하세요:

```bash
npm start -- run -f remap.csv -b 50
```

### 로그 확인

로그 레벨을 debug로 설정하여 자세한 정보를 확인하세요:

```bash
npm start -- run -f remap.csv -l debug
```

## 명령어 참조

### 백업 관련 명령어

#### backup
데이터베이스와 Redis 데이터를 백업합니다.

```bash
npm start -- backup [옵션]
```

**옵션:**
- `-w, --world-id <worldId>`: 특정 월드만 백업
- `-d, --databases <types>`: 백업할 데이터베이스 타입 (auth,world,user)
- `--no-redis`: Redis 데이터 제외
- `--no-compress`: 압축하지 않음
- `--description <desc>`: 백업 설명
- `-c, --config-dir <dir>`: 설정 파일 디렉토리 (기본값: ./config)
- `-b, --backup-dir <dir>`: 백업 디렉토리 (기본값: ./backup)

**예제:**
```bash
# 전체 백업
npm start -- backup --description "리맵핑 전 백업"

# 특정 월드 백업
npm start -- backup -w UWO-KR-01

# Auth, User DB만 백업
npm start -- backup -d auth,user
```

#### restore
백업에서 데이터를 복원합니다.

```bash
npm start -- restore -t <timestamp> [옵션]
```

**옵션:**
- `-t, --timestamp <timestamp>`: 복원할 백업 타임스탬프 (필수)
- `-w, --world-id <worldId>`: 특정 월드만 복원
- `-d, --databases <types>`: 복원할 데이터베이스 타입
- `--no-redis`: Redis 데이터 제외
- `-f, --force`: 확인 없이 강제 복원
- `-c, --config-dir <dir>`: 설정 파일 디렉토리
- `-b, --backup-dir <dir>`: 백업 디렉토리

**예제:**
```bash
# 전체 복원
npm start -- restore -t 2024-01-15_14-30-00

# 특정 월드만 복원
npm start -- restore -t 2024-01-15_14-30-00 -w UWO-KR-01

# User DB만 복원
npm start -- restore -t 2024-01-15_14-30-00 -d user
```

#### list-backups
생성된 백업 목록을 조회합니다.

```bash
npm start -- list-backups [옵션]
```

**옵션:**
- `-b, --backup-dir <dir>`: 백업 디렉토리 (기본값: ./backup)

### 백업 파일 구조

백업은 다음과 같은 구조로 저장됩니다:

```
backup/
├── 2024-01-15_14-30-00.zip          # 압축된 백업 파일
├── 2024-01-15_15-45-30/             # 압축되지 않은 백업 폴더
│   ├── metadata.json5               # 백업 메타데이터
│   └── data.json5                   # 실제 백업 데이터
└── 2024-01-15_16-20-15.zip
```

**metadata.json5 예제:**
```json
{
  "timestamp": "2024-01-15_14-30-00",
  "createdAt": "2024-01-15T14:30:00.000Z",
  "description": "리맵핑 작업 전 백업",
  "worlds": ["UWO-KR-01"],
  "databases": ["auth", "world", "user"],
  "includesRedis": true,
  "compressed": true,
  "size": 1048576,
  "tableCount": 150,
  "redisKeyCount": 1250
}
```
