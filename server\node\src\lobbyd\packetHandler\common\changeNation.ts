// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';
import assert from 'assert';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import * as sync from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import {
  UserChangeTask,
  CHANGE_TASK_REASON,
  Changes,
  TryData,
  CHANGE_TASK_RESULT,
} from '../../UserChangeTask/userChangeTask';
import { SetNationSpec } from '../../UserChangeTask/commonChangeSpec';
import {
  opAddPoint,
  opRecordNationEventOccur,
  opSetReputation,
  opSetFame,
  opAddItem,
  opResetPalaceRoyalOrder,
} from '../../UserChangeTask/userChangeOperator';
import { UserReputationChange } from '../../userReputation';
import mlog from '../../../motiflib/mlog';
import { TownManager } from '../../townManager';
import * as mutil from '../../../motiflib/mutil';
import { ClientPacketHandler } from '../index';
import { RewardData } from '../../../motiflib/gameLog';
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import { Sync } from '../../type/sync';
import { GuildUtil } from '../../guildUtil';
import { GetFullWeeksUsingLocalTime } from '../../../formula';
import { TownInvestedPubMsg, TownMayorChangedPubMsg } from '../../../motiflib/model/lobby';
import { NationUtil } from '../../../motiflib/model/lobby/nationUtil';
import { onChangedUserNation } from '../../userNation';
import { ClashPrizeManager } from '../../clashPrizeManager';
import Pubsub from '../../../redislib/pubsub';

// ----------------------------------------------------------------------------
// 이민 할때 호출되는 패킷 (첫 이민시  국가보상을 획득가능함)
// ----------------------------------------------------------------------------

const rsn = 'change_nation';
const add_rsn = null;

interface Request {
  nationCmsId: number;
}

interface Response {
  sync?: sync.Sync;
  bRestricted?: boolean;
  bPopulationRatioOver?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_ChangeNation implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    // validate connection state
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const body: Request = packet.bodyObj;

    const { nationCmsId } = body;

    const { nationManager, townRedis, worldPubsub } = Container.get(LobbyService);
    const townManager = Container.get(TownManager);

    const curTime = mutil.curTimeUtc();

    // validate request body
    if (nationCmsId === 0 || !nationManager.has(nationCmsId)) {
      throw new MError('no-key-in-nation-cms', MErrorCode.NO_KEY_IN_CMS);
    }

    const nation = nationManager.get(nationCmsId);
    // 1. 유저의 조건이 국가 변경 조건과 맞아야 합니다.
    // if (!T()) {
    //   throw new MError('nation-change-not-allowed', MErrorCode.NATION_CHANGE_NOT_ALLOWED_ERROR);
    // }

    // 2. 유저가 국가를 변경할 수 있어야 합니다.
    const oldNationCmsId = user.nationCmsId;
    if (oldNationCmsId === 0 || oldNationCmsId === nationCmsId) {
      throw new MError('invalid-change-nation-cms', MErrorCode.NATION_INVALID_CHANGE_NATION_ERROR);
    }

    // 3. 왕궁이 있는 도시인지 체크
    if (user.userTown.getTownBldg() !== cmsEx.BUILDING_TYPE.PALACE) {
      throw new MError('not-palace-town', MErrorCode.NOT_PALACE_TOWN, {
        body,
      });
    }

    // 4. 왕궁의 언어레벨 조건을 만족 해야 한다
    user.userContentsTerms.ensureBuildingContentsUnlock(
      cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.CHANGE_NATION,
      user
    );

    // 5. 망명 갱신 시간 조건 체크
    if (
      user.lastUpdateNationTimeUtc &&
      user.lastUpdateNationTimeUtc + cms.Const.NationChooseSelectCooltimeSec.value > curTime
    ) {
      throw new MError('not-condition-change-nation', MErrorCode.INVALID_CHANGE_NATION, {
        body,
        curTime,
      });
    }

    // 시장 포기, 투자 금액 반환
    const refundTownCmsIds: number[] = [];
    const mayorTownCmsIds: number[] = [];
    const mayorTownCmsIdsObj: { [townCmsId: number]: boolean } = {};
    const refundedUnmayorTownCmsIds: number[] = [];
    const weeklySessionId = GetFullWeeksUsingLocalTime(
      curTime,
      cms.Define.InvestmentWeeklySessionPivotDay
    );

    // 정산 시간에는 이민 불가(시즌 랭킹 도입하면서 이민 제한 확대)
    if (NationUtil.isClosingInvestmentWeeklySession(curTime)) {
      throw new MError(
        'it-is-weekly-investment-cleaning-time',
        MErrorCode.IT_IS_WEEKLY_INVESTMENT_CLEANING_TIME,
        { nationCmsId, weeklySessionId }
      );
    }

    for (const townCms of _.values(cms.Town)) {
      if (townManager.getTownMayorUserId(townCms.id, curTime) === user.userId) {
        // 시장인 경우 투자 금액 반환 됨.
        refundTownCmsIds.push(townCms.id);
        // 시장인 경우 포기 됨.
        mayorTownCmsIds.push(townCms.id);
        mayorTownCmsIdsObj[townCms.id] = true;
      } else if (
        townCms.ownType === cmsEx.TOWN_OWN_TYPE.CAPITAL_TOWN ||
        townCms.ownType === cmsEx.TOWN_OWN_TYPE.NATIONAL_TOWN
      ) {
        // 본거지, 영지 투자 금액 반환 됨.
        refundTownCmsIds.push(townCms.id);
      }
    }

    let outTrace: any = {};
    if (
      NationUtil.isClosingElectionSession(
        nationManager.getLastClosedElectionSessionId(nationCmsId),
        curTime,
        outTrace
      )
    ) {
      throw new MError(
        'it-is-nation-election-cleaning-time',
        MErrorCode.IT_IS_NATION_ELECTION_CLEANING_TIME,
        {
          curTime,
          outTrace,
        }
      );
    }

    // 해당 국가가 선택 제한 상태가 아니어야 합니다.
    if (nation.isRestricted(nationManager)) {
      return user.sendJsonPacket<Response>(packet.seqNum, packet.type, { bRestricted: true });
    }

    // 이민국가 인구 비율 OVER
    if (nationManager.isOverPopulationRatio(user.nationCmsId, nationCmsId)) {
      return user.sendJsonPacket<Response>(packet.seqNum, packet.type, {
        bPopulationRatioOver: true,
      });
    }

    // TODO: reputationSpec 뭔지 알아봐야 됨.
    // const reputationSpec = CreateReputationSpec(cms.Const);
    // 해당 국가에 대한 평판이 +50% 이상이어야 합니다.
    const reputation = user.userReputation.get(nationCmsId, curTime);
    if (reputation < nationManager.requiredReputationToChangeNation) {
      throw new MError('not-enough-reputation', MErrorCode.NOT_ENOUGH_REPUTATION);
    }

    // 칙명이 있는 경우 국가를 변경할 수 없다.
    const royalOrderQuest = user.questManager.getContextByQuestCategory(
      cmsEx.QUEST_CATEGORY.ROYAL_ORDER
    );
    if (royalOrderQuest) {
      throw new MError(
        'cant-change-nation-if-has-royal-order',
        MErrorCode.CANT_CHANGE_NATION_IF_HAS_ROYAL_ORDER,
        {
          royalOrderQuestCmsId: royalOrderQuest.cmsId,
        }
      );
    }

    //
    const nationRank = nationManager.getPowerRank(nationCmsId);

    // 평판, 우호도 변동
    const reputationChanges: UserReputationChange[] = [];
    const nationDiplomacyCmsIdToRecordIntimacyEvent =
      user.userReputation.buildReputationIntimacyChanges(
        nationCmsId,
        oldNationCmsId,
        cmsEx.NATION_DIPLOMACY_CMS_ID.CHANGE_NATION,
        reputationChanges,
        curTime
      );

    // 제독 명성 변동
    // 강대국으로 이민 시 명성 차감( https://jira.line.games/browse/UWO-16270 )
    const leaderMateFameChangesForLoss: { jobType: cmsEx.JOB_TYPE; fame: number }[] = (() => {
      if (!nationManager.get(oldNationCmsId)) {
        mlog.error(
          `[${rsn}] failed-to-get-old-nation-so-fame-loss-wil-be-skipped. nationCmsId: ${oldNationCmsId}`,
          { userId: user.userId }
        );
        return undefined;
      }
      const oldNationRank = nationManager.getPowerRank(oldNationCmsId);
      if (!Number.isInteger(nationRank) || !Number.isInteger(oldNationRank)) {
        mlog.warn(`[${rsn}] unexpected-nation-rank-so-fame-loss-will-be-skipped.`, {
          userId: user.userId,
          nationCmsId,
          nationRank,
          oldNationCmsId,
          oldNationRank,
        });
        return undefined;
      }
      const bNewNationStrong = nationRank < oldNationRank; // 숫자가 작을수록 강대국
      if (!bNewNationStrong) {
        return undefined;
      }

      const RATIO: number = cms.Const.ExileFame.value; // 음수. 단위: %.
      const ALL_FAME_TYPES = [
        cmsEx.JOB_TYPE.ADVENTURE,
        cmsEx.JOB_TYPE.TRADE,
        cmsEx.JOB_TYPE.BATTLE,
      ] as const;
      const leaderMate = user.userMates.getLeaderMate(user.userFleets);

      return ALL_FAME_TYPES.map((jobType) => {
        const curFame = leaderMate.getFame(jobType);
        return {
          jobType,
          fame: Math.max(0, curFame + Math.floor((curFame * RATIO) / 100)),
        };
      }).filter((elem) => elem.fame !== leaderMate.getFame(elem.jobType));
    })();

    // 첫 이민시 국가 보상 지급
    let nationRankBonus: number;
    if (!user.lastUpdateNationTimeUtc) {
      const nationRankingEffectCms =
        cms.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.SELECT_NATION];
      nationRankBonus = nationRankingEffectCms.rankingEffectVal[nationRank - 1];
    }

    let cost: number = nation.GetChangeNationCost(nationManager);
    // 첫 이민시 무료 이민
    if (!user.lastUpdateNationTimeUtc) {
      cost = 0;
    }

    // 투자 점수 기반 시즌 투자 증서 지급
    const investSeasonItemCmsId = cmsEx.getRefundInvestItemCmsId(curTime);
    if (!investSeasonItemCmsId) {
      throw new MError('empty-refund-invest-item-cms-id', MErrorCode.INVALID_ITEM);
    }

    const investCompensationItemCms = cms.Item[investSeasonItemCmsId];

    const reward_data: RewardData[] = [];
    const resp: Response = {};
    let investCompensationItemAmount = 0;
    let changeTask: UserChangeTask;
    const promises = [];
    for (const townCmsId of refundTownCmsIds) {
      // [TODO] loadInvestmentMyAccumPoints 으로 교체.
      promises.push(
        townRedis['loadInvestmentMyAccumPoint'](
          user.userId,
          townCmsId,
          weeklySessionId,
          user.userGuild.guildId
        ).then((ret) => {
          const rawScore = parseInt(ret[1], 10);
          const score = Math.floor(rawScore / cms.Define.InvestmentSocreMultiplier);
          if (score > 0) {
            investCompensationItemAmount += Math.ceil(
              (score *
                cms.Const.InvestCompanyPointPer.value *
                cms.Const.ImmigrationInvestmentRefund.value) /
                1000 /
                investCompensationItemCms.custom
            );
            if (!mayorTownCmsIdsObj[townCmsId]) {
              refundedUnmayorTownCmsIds.push(townCmsId);
            }
          }
        })
      );
    }

    return Promise.all(promises)
      .then(() => {
        changeTask = new UserChangeTask(
          user,
          CHANGE_TASK_REASON.CHANGE_NATION,
          new ChangeNationSpec(
            nationCmsId,
            curTime,
            cms.Const.NationChangePointId.value,
            cost,
            nationRankBonus,
            {
              cmsId1: oldNationCmsId,
              cmsId2: nationCmsId,
              nationDiplomacyCmsId: nationDiplomacyCmsIdToRecordIntimacyEvent,
            },
            reputationChanges,
            leaderMateFameChangesForLoss,
            reward_data,
            investCompensationItemCms.id,
            investCompensationItemAmount
          )
        );
        const res = changeTask.trySpec();
        if (res !== CHANGE_TASK_RESULT.OK) {
          throw new MError(
            'try-change-nation-spec-failed',
            MErrorCode.TRY_CHANGE_NATION_SPEC_FAILED,
            {
              res,
            }
          );
        }

        // 시장, 투자 점수 제거.
        const promises = [];
        mlog.info('Try change nation.', {
          userId: user.userId,
          nationCmsId,
          cost,
          nationRankBonus,
          leaderMateFameChangesForLoss,
          investCompensationItemAmount,
          refundTownCmsIds,
          mayorTownCmsIds,
        });
        for (const townCmsId of mayorTownCmsIds) {
          promises.push(townRedis['resignMayor'](user.userId, townCmsId, weeklySessionId));
        }
        for (const townCmsId of refundedUnmayorTownCmsIds) {
          promises.push(
            townRedis['removeTownUserWeeklyInvestmentScore'](
              user.userId,
              townCmsId,
              weeklySessionId
            )
          );
        }

        return Promise.all(promises);
      })
      .then(() => {
        return changeTask.apply();
      })
      .then((sync) => {
        resp.sync = sync;
        const townInfo = user.userTown.getTownInfo();
        if (townInfo) {
          const townApi = mhttp.townpx.channel(townInfo.url);
          townApi.updateTownUserSyncData(user.userId, { user: { nationCmsId } }).catch((err) => {
            mlog.error('Town api updateTownUserSyncData is failed.', {
              userId: user.userId,
              err: err.message,
            });
          });
        }

        mhttp.authd.changeUserNation(user.userId, nationCmsId).catch((err) => {
          mlog.error('Auth api changeUserNation is failed.', {
            userId: user.userId,
            nationCmsId,
            err: err.message,
          });
        });
        mhttp.chatd.updateVolanteUser(user);

        return GuildUtil.onChangedUserNation(user);
      })
      .then((guildSync: Sync) => {
        _.merge<Sync, Sync>(resp.sync, guildSync);

        return onChangedUserNation(user, oldNationCmsId, resp.sync);
      })
      .then(() => {
        const clashPrizeManager = Container.get(ClashPrizeManager);
        const pubsub = Container.of('pubsub-world').get(Pubsub);

        if (clashPrizeManager.isRanker(user.userId)) {
          return pubsub.publish('clash_season_rankers_updated', {});
        }
        return null;
      })
      .then(() => {
        // glog
        const leaderMate = user.userMates.getLeaderMate(user.userFleets);
        const royalTitle = leaderMate.getNub().royalTitle;
        const royalTitleCms = cms.RoyalTitle[royalTitle];
        const oldNationCms = cms.Nation[oldNationCmsId];
        const curNationCms = cms.Nation[nationCmsId];

        user.glog('nation_change', {
          rsn,
          add_rsn,
          old_nation: oldNationCms.name,
          cur_nation: curNationCms.name,
          pr_data: [
            {
              type: cms.Const.NationChangePointId.value,
              amt: cost,
            },
          ],
          reward_data: reward_data,
          exchange_hash: changeTask.getExchangeHash(),
        });

        user.glog('admiral_royaltitle', {
          rsn,
          add_rsn,
          id: leaderMate.getNub().royalTitle,
          nation: nationCmsId,
          grade: royalTitleCms.grade,
          name: royalTitleCms.titleNames[curNationCms.royalTitleNameType],
        });

        // 시장 포기.
        for (const townCmsId of mayorTownCmsIds) {
          _.merge<Sync, Sync>(resp.sync, {
            add: {
              allTownInvestments: {
                [townCmsId]: {
                  score: 0,
                },
              },
            },
          });

          const msg: TownMayorChangedPubMsg = {
            townCmsId,
            mayorUserId: null,
            mayorUserName: null,
            mayorNationCmsId: null,
            updateTimeUtc: curTime,
          };
          worldPubsub.publish('town_mayor_changed', JSON.stringify(msg)).catch((err) => {
            mlog.alert('changeNation town_mayor_changed publish is failed.', {
              err: err.message,
              townCmsId,
              userId: user.userId,
            });
          });
        }
        for (const townCmsId of refundedUnmayorTownCmsIds) {
          _.merge<Sync, Sync>(resp.sync, {
            add: {
              allTownInvestments: {
                [townCmsId]: {
                  score: 0,
                },
              },
            },
          });

          const msgObj: TownInvestedPubMsg = {
            townCmsId,
            developmentType: cmsEx.DEVELOPMENT_TYPE.industry,
            oldDevelopmentLevel: townManager.getDevelopmentLevel(
              townCmsId,
              cmsEx.DEVELOPMENT_TYPE.industry
            ),
            sessionId: weeklySessionId,
            updateTimeUtc: curTime,
          };

          worldPubsub.publish('town_invested', JSON.stringify(msgObj)).catch((err) => {
            mlog.alert('changeNation town_invested publish is failed.', {
              err: err.message,
              townCmsId,
              userId: user.userId,
            });
          });
        }

        return user.sendJsonPacket<Response>(packet.seqNum, packet.type, resp);
      });
  }
}

class ChangeNationSpec extends SetNationSpec {
  constructor(
    nationCmsId: number,
    lastUpdateNationTimeUtc: number,
    private pointCmsId: number,
    private cost: number,
    private nationRankBonusPointVal: number,
    private nationEvent: {
      cmsId1: number;
      cmsId2: number;
      nationDiplomacyCmsId: cmsEx.NATION_DIPLOMACY_CMS_ID;
    },
    private reputationChanges: UserReputationChange[],
    private leaderMateFameChanges: { jobType: cmsEx.JOB_TYPE; fame: number }[] | undefined,
    private reward_data: RewardData[],
    private investCompensationItemCmsId: number,
    private investCompensationItemAmount: number
  ) {
    super(nationCmsId, lastUpdateNationTimeUtc);
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const res = super.accumulate(user, tryData, changes);
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      return res;
    }

    const ops = [];
    // 첫 이민시 이민 비용 무료
    if (this.cost) {
      ops.push(
        opAddPoint(
          user,
          tryData,
          changes,
          this.pointCmsId,
          -this.cost,
          false,
          true,
          { itemId: rsn },
          false
        )
      );
    }

    // 이민시 국가 보너스 제공
    if (this.nationRankBonusPointVal) {
      ops.push(
        opAddPoint(
          user,
          tryData,
          changes,
          cms.Const.NationPickBonusPointId.value,
          this.nationRankBonusPointVal,
          false,
          false,
          { gainReason: rsn },
          false
        )
      );
    }

    if (this.nationEvent.nationDiplomacyCmsId !== undefined) {
      ops.push(
        opRecordNationEventOccur(
          user,
          tryData,
          changes,
          this.nationEvent.cmsId1,
          this.nationEvent.cmsId2,
          this.nationEvent.nationDiplomacyCmsId
        )
      );
    }
    for (const elem of this.reputationChanges) {
      ops.push(
        opSetReputation(
          user,
          tryData,
          changes,
          elem.nationCmsId,
          elem.reputation,
          elem.updateTimeUtc
        )
      );
    }
    if (this.leaderMateFameChanges) {
      for (const elem of this.leaderMateFameChanges) {
        ops.push(opSetFame(user, tryData, changes, elem.jobType, elem.fame));
      }
    }

    if (
      user.questManager.getPalaceRoyalOrderCmsId() ||
      user.questManager.getPalaceRoyalTitleOrderCmsId()
    ) {
      ops.push(opResetPalaceRoyalOrder(user, tryData, changes));
    }

    if (this.investCompensationItemAmount) {
      ops.push(
        opAddItem(
          user,
          tryData,
          changes,
          this.investCompensationItemCmsId,
          this.investCompensationItemAmount,
          true,
          false,
          undefined,
          true,
          true
        )
      );
    }

    for (const res of ops) {
      if (res !== CHANGE_TASK_RESULT.OK) {
        return res;
      }
    }
    if (this.nationRankBonusPointVal) {
      this.reward_data.push({
        type: REWARD_TYPE[REWARD_TYPE.POINT],
        id: cms.Const.NationPickBonusPointId.value,
        uid: null,
        amt: this.nationRankBonusPointVal,
      });
    }

    return CHANGE_TASK_RESULT.OK;
  }
}
