/**
 * CSV 파일에서 읽어온 리맵 데이터 구조
 */
export interface RemapData {
  seq: number;
  uwo_Gnid: string;
  uwo_Nid: string;
  uwogl_Gnid: string;
  uwogl_Nid: string;
  gameServerId: string;
}

/**
 * 분석된 데이터베이스 테이블 정보
 */
export interface DatabaseTableInfo {
  tableName: string;
  database: 'auth' | 'world' | 'user';
  columns: DatabaseColumnInfo[];
  primaryKeys: string[];
  indexes: DatabaseIndexInfo[];
  hasAccountId: boolean;
  hasPubId: boolean;
  shardingRequired: boolean;
  filePath?: string;
  comment?: string;
  engine?: string;
  collation?: string;
}

/**
 * 데이터베이스 컬럼 정보
 */
export interface DatabaseColumnInfo {
  columnName: string;
  dataType: string;
  isNullable: boolean;
  isPrimaryKey: boolean;
  isIndexed: boolean;
  relatedTo: 'accountId' | 'pubId' | 'none';
  defaultValue?: string;
  fullType?: string;
  comment?: string;
  extra?: string;
  position?: number;
}

/**
 * 데이터베이스 인덱스 정보
 */
export interface DatabaseIndexInfo {
  indexName: string;
  columns: string[];
  isUnique: boolean;
  isPrimary: boolean;
  type?: string;
  comment?: string;
}

/**
 * 레거시 데이터베이스 컬럼 정보 (호환성을 위해 유지)
 */
export interface LegacyDatabaseColumnInfo {
  columnName: string;
  dataType: string;
  isNullable: boolean;
  isPrimaryKey: boolean;
  isIndexed: boolean;
  relatedTo: 'accountId' | 'pubId' | 'none';
}

/**
 * 레거시 데이터베이스 인덱스 정보 (호환성을 위해 유지)
 */
export interface LegacyDatabaseIndexInfo {
  indexName: string;
  columns: string[];
  isUnique: boolean;
  isPrimary: boolean;
}

/**
 * 분석된 Redis 키 정보
 */
export interface RedisKeyInfo {
  keyPattern: string;
  redisInstance: string;
  usesAccountId: boolean;
  usesPubId: boolean;
  usesUserId: boolean;
  usesGnid: boolean;
  usesNid: boolean;
  description: string;
  luaScriptPath?: string;
  tsFilePath?: string;
  keyType?: string; // Redis 키 타입 (string, hash, list, set, zset, stream)
  keyCount?: number; // 실제 발견된 키 개수
  hashFields?: string[]; // Hash 타입 키에서 업데이트할 특정 필드들
}

/**
 * 기본 MySQL 설정 (service_layout/local.json5 구조 기반)
 */
export interface MySqlConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  multipleStatements?: boolean;
  supportBigNumbers?: boolean;
  bigNumberStrings?: boolean;
  connectTimeout?: number;
  connectionLimit?: number;
  flags?: string;
  driver?: string;
}

/**
 * 사용자 데이터베이스 샤드 설정
 */
export interface UserShardConfig {
  shardId: number;
  sqlCfg: Partial<MySqlConfig>; // 기본 설정에서 오버라이드할 부분만
}

/**
 * 사용자 데이터베이스 설정 (월드별)
 */
export interface UserDatabaseConfig {
  shardFunction: string;
  sqlDefaultCfg: MySqlConfig;
  shards: UserShardConfig[];
}

/**
 * Redis 설정 (service_layout/local.json5 구조 기반)
 */
export interface RedisInstanceConfig {
  redisCfg: {
    host: string;
    port: number;
    db: number;
    password?: string;
  };
  scriptDir?: string;
  pool?: {
    min: number;
    max: number;
  };
}

/**
 * 월드 설정
 */
export interface WorldConfig {
  id: string; // 월드 ID (예: UWO-GL-01)
  mysqlUserDb: UserDatabaseConfig;
  mysqlWorldDb: MySqlConfig;
  userCacheRedis: RedisInstanceConfig;
  townRedis: RedisInstanceConfig;
  nationRedis: RedisInstanceConfig;
  collectorRedis: RedisInstanceConfig;
  sailRedis: RedisInstanceConfig;
  guildRedis: RedisInstanceConfig;
  arenaRedis: RedisInstanceConfig;
  raidRedis: RedisInstanceConfig;
  rankingRedis: RedisInstanceConfig;
  userRedis: RedisInstanceConfig;
  townLbRedis: RedisInstanceConfig;
  oceanLbRedis: RedisInstanceConfig;
  blindBidRedis: RedisInstanceConfig;
}

/**
 * 공유 설정
 */
export interface SharedConfig {
  mysqlAuthDb: MySqlConfig;
  authRedis: RedisInstanceConfig;
  monitorRedis: RedisInstanceConfig;
  orderRedis: RedisInstanceConfig;
  globalMatchRedis: RedisInstanceConfig;
  globalBattleLogRedis: RedisInstanceConfig;
}

/**
 * 전체 설정 구조
 */
export interface RemapToolConfig {
  sharedConfig: SharedConfig;
  worlds: WorldConfig[];
}

/**
 * 레거시 Redis 설정 (호환성을 위해 유지)
 */
export interface RedisConfig {
  host: string;
  port: number;
  db: number;
  password?: string;
  connectTimeout: number;
  lazyConnect: boolean;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
  maxLoadingTimeout: number;
}

/**
 * 레거시 Redis 설정들 (호환성을 위해 유지)
 */
export interface RedisConfigs {
  userCache: RedisConfig;
  monitor: RedisConfig;
  order: RedisConfig;
  nation: RedisConfig;
  auth: RedisConfig;
}

/**
 * 작업 진행 상태
 */
export interface ProgressInfo {
  total: number;
  current: number;
  phase: string;
  message: string;
  errors: string[];
  warnings: string[];
}

/**
 * 데이터베이스 변경 결과
 */
export interface DatabaseUpdateResult {
  tableName: string;
  affectedRows: number;
  success: boolean;
  error?: string;
  executionTime: number;
}



/**
 * Redis 변경 결과
 */
export interface RedisUpdateResult {
  keyPattern: string;
  updatedKeys: number;
  success: boolean;
  error?: string;
  executionTime: number;
  keyType?: string; // Redis 키 타입
  instanceName?: string; // Redis 인스턴스명
}

/**
 * 월드별 결과
 */
export interface WorldResult {
  worldId: string;
  databaseResults: DatabaseUpdateResult[];
  redisResults: RedisUpdateResult[];
}

/**
 * 전체 작업 결과 (그룹핑된 형태)
 */
export interface GroupedRemapResult {
  success: boolean;
  totalProcessed: number;
  sharedResults: {
    databaseResults: DatabaseUpdateResult[];
    redisResults: RedisUpdateResult[];
  };
  worldResults: WorldResult[];
  errors: string[];
  warnings: string[];
  startTime: Date;
  endTime: Date;
  duration: number;
}

/**
 * 전체 작업 결과 (기존 형태 - 호환성 유지)
 */
export interface RemapResult {
  success: boolean;
  totalProcessed: number;
  databaseResults: DatabaseUpdateResult[];
  redisResults: RedisUpdateResult[];
  errors: string[];
  warnings: string[];
  startTime: Date;
  endTime: Date;
  duration: number;
}

/**
 * 검증 결과
 */
export interface VerificationResult {
  success: boolean;
  checkedItems: number;
  passedItems: number;
  failedItems: number;
  details: VerificationDetail[];
}

/**
 * 검증 세부 정보
 */
export interface VerificationDetail {
  type: 'database' | 'redis';
  target: string;
  expected: string | number;
  actual: string | number;
  success: boolean;
  message: string;
}

/**
 * CLI 옵션
 */
export interface CliOptions {
  csvFile: string;
  worldId?: string;
  dryRun: boolean;
  skipVerification: boolean;
  batchSize: number;
  configDir: string;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  outputDir: string;
  schemaDir?: string;
}

/**
 * 로그 레벨
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * 작업 단계
 */
export type WorkPhase =
  | 'initialization'
  | 'codebase_analysis'
  | 'csv_parsing'
  | 'data_validation'
  | 'database_connection'
  | 'redis_connection'
  | 'database_update'
  | 'redis_update'
  | 'verification'
  | 'cleanup'
  | 'completed'
  | 'failed';

/**
 * 분석 결과
 */
export interface AnalysisResult {
  databaseTables: DatabaseTableInfo[];
  redisKeys: RedisKeyInfo[];
  totalFilesAnalyzed: number;
  sqlFilesAnalyzed: number;
  luaFilesAnalyzed: number;
  tsFilesAnalyzed: number;
  analysisTime: number;
}

/**
 * 월드별 샤드 매핑 정보 (새로운 구조)
 */
export interface WorldShardMapping {
  worldId: string;
  shardId: number;
  userDatabase: string;
  worldDatabase: string;
}

/**
 * 레거시 데이터베이스 설정 (호환성을 위해 유지)
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit: number;
  acquireTimeout: number;
  timeout: number;
  reconnect: boolean;
  charset: string;
}

/**
 * 새로운 데이터베이스 설정 구조
 */
export interface DatabaseConfigs {
  sharedConfig: SharedConfig;
  worlds: WorldConfig[];
}

/**
 * 레거시 데이터베이스 설정 구조 (호환성을 위해 유지)
 */
export interface LegacyDatabaseConfigs {
  auth: DatabaseConfig;
  world: DatabaseConfig;
  userShards: Array<DatabaseConfig & { shardId: number; worldIds: string[] }>;
}

/**
 * 배치 처리 옵션
 */
export interface BatchOptions {
  batchSize: number;
  delayBetweenBatches: number;
  maxRetries: number;
  retryDelay: number;
}

/**
 * 롤백 정보
 */
export interface RollbackInfo {
  timestamp: Date;
  remapData: RemapData[];
  databaseBackups: DatabaseBackupInfo[];
  redisBackups: RedisBackupInfo[];
}

/**
 * 데이터베이스 백업 정보
 */
export interface DatabaseBackupInfo {
  tableName: string;
  database: string;
  backupQuery: string;
  affectedRows: number;
}

/**
 * Redis 백업 정보
 */
export interface RedisBackupInfo {
  keyPattern: string;
  redisInstance: string;
  backupData: Record<string, string>;
}
