#!/bin/bash

CWD="$(dirname "$0")"

if [ ! -f $CWD/_config.sh ]; then
	echo "Please create a custom '_config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/_query.sh ]; then
	echo "Please create a custom '_query.sh' file at '$CWD' directory."
	exit 1
fi

if [ -z "$1" ]
  then
    echo "No argument multiple database"
    exit 1
fi

SECONDS=0

source $CWD/_config.sh
source $CWD/_query.sh

USER_DB_NAME=$1
main() 
{
  echo "===== CREATE TABLE integration_duplicate_name"
  q="
    DROP TABLE IF EXISTS integration_duplicate_name;

    CREATE TABLE integration_duplicate_name (
      userId int NOT NULL,
      prevName varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      name varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      PRIMARY KEY (userId)
    )
    ENGINE=InnoDB DEFAULT
    CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${USER_DB_NAME}" "${q}"


  echo "===== SELECT TABLE uwo_auth.integration_duplicte_name"
  q="
    SELECT userId, prevName, name
      FROM integration_duplicate_name;
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"
  duplicateList=(${QUERY_RESULT})

  let dataList
  for ((idx=0; idx < ${#duplicateList[@]}; idx+=3))
  do
    dataList+="(${duplicateList[idx]}, \"${duplicateList[idx+1]}\", \"${duplicateList[idx+2]}\"),"
  done


  echo "===== COPY TABLE uwo_user.integration_duplicte_name"
  q="
    INSERT INTO integration_duplicate_name(userId, prevName, name)
    VALUES ${dataList:0:-1};
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${USER_DB_NAME}" "${q}"


  echo "===== UPDATE u_users.name"
  q="
    UPDATE u_users
      JOIN integration_duplicate_name AS TMP
      ON u_users.id = TMP.userId
    SET u_users.name=TMP.name;
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${USER_DB_NAME}" "${q}"
}



main "$@"; 

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
exit
