"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cmsEx = __importStar(require("../../cms/ex"));
const merror_1 = require("../../motiflib/merror");
const puBattleRewardCreate_1 = __importDefault(require("../sp/puBattleRewardCreate"));
const puGameOverLossUpdate_1 = __importDefault(require("../sp/puGameOverLossUpdate"));
const puInsuranceUpdateUnpaid_1 = __importDefault(require("../sp/puInsuranceUpdateUnpaid"));
const puMateUpdateExpLevel_1 = __importDefault(require("../sp/puMateUpdateExpLevel"));
const puMateUpdateFame_1 = __importDefault(require("../sp/puMateUpdateFame"));
const puPointUpdate_1 = __importDefault(require("../sp/puPointUpdate"));
const puShipCargoUpdate_1 = __importDefault(require("../sp/puShipCargoUpdate"));
const puSoftDataUpdateExpLevel_1 = __importDefault(require("../sp/puSoftDataUpdateExpLevel"));
const puUserUpdateEnergy_1 = __importDefault(require("../sp/puUserUpdateEnergy"));
const mysqlUtil_1 = require("../mysqlUtil");
const puQuestContextUpdateAllFlags_1 = __importDefault(require("../sp/puQuestContextUpdateAllFlags"));
const puStateUpdateGameState_1 = __importDefault(require("../sp/puStateUpdateGameState"));
const puItemUpdate_1 = __importDefault(require("../sp/puItemUpdate"));
const puReputationUpdate_1 = __importDefault(require("../sp/puReputationUpdate"));
const puSoftDataUpdateFreeTakeback_1 = __importDefault(require("../sp/puSoftDataUpdateFreeTakeback"));
const puShipUpdateDurabilityAndSailor_1 = __importDefault(require("../sp/puShipUpdateDurabilityAndSailor"));
function queryImpl(connection, userId, mateExpChanges, userExpLevelChange, freeTakebackChange, ducatChange, blueGemChange, leaderMateCmsId, battleFame, gameState, rewards, losses, shipChanges, cargoChanges, insuranceChange, questContextChanges, itemChanges, reputationChanges) {
    return Promise.resolve()
        .then(() => {
        // uwo.u_users
        if (userExpLevelChange && userExpLevelChange.energyChange) {
            return (0, puUserUpdateEnergy_1.default)(connection, userId, userExpLevelChange.energyChange.energy, userExpLevelChange.energyChange.lastUpdateTimeUtc);
        }
    })
        .then(() => {
        // uwo.u_states
        return (0, puStateUpdateGameState_1.default)(connection, userId, gameState);
    })
        .then(() => {
        // uwo.u_soft_data
        if (userExpLevelChange) {
            return (0, puSoftDataUpdateExpLevel_1.default)(connection, userId, userExpLevelChange.exp, userExpLevelChange.level);
        }
        return null;
    })
        .then(() => {
        // uwo.u_soft_data
        if (freeTakebackChange) {
            return (0, puSoftDataUpdateFreeTakeback_1.default)(connection, userId, freeTakebackChange.usedFreeTurnTakebackCount, freeTakebackChange.usedFreePhaseTakebackCount, freeTakebackChange.updateTimeUtc);
        }
    })
        .then(() => {
        // uwo.u_points
        if (ducatChange) {
            return (0, puPointUpdate_1.default)(connection, userId, ducatChange);
        }
    })
        .then(() => {
        if (blueGemChange) {
            return (0, puPointUpdate_1.default)(connection, userId, blueGemChange);
        }
    })
        .then(() => {
        // uwo.u_items
        if (itemChanges && itemChanges.length > 0) {
            const promises = [];
            for (const change of itemChanges) {
                promises.push((0, puItemUpdate_1.default)(connection, userId, change.cmsId, change.count, change.unboundCount));
            }
            return Promise.all(promises);
        }
        return [];
    })
        .then(() => {
        if (reputationChanges && reputationChanges.length > 0) {
            const promises = [];
            for (const elem of reputationChanges) {
                promises.push((0, puReputationUpdate_1.default)(connection, userId, elem.nationCmsId, elem.reputation, elem.updateTimeUtc));
            }
            return Promise.all(promises);
        }
        return [];
    })
        .then(() => {
        // u_quest_contexts
        if (questContextChanges) {
            const promises = [];
            for (const qcc of questContextChanges) {
                promises.push((0, puQuestContextUpdateAllFlags_1.default)(connection, userId, qcc.cmsId, qcc.uflags, qcc.lflags));
            }
            return Promise.all(promises);
        }
        return [];
    })
        .then(() => {
        // uwo.u_insurances
        if (insuranceChange) {
            return (0, puInsuranceUpdateUnpaid_1.default)(connection, userId, insuranceChange);
        }
    })
        .then(() => {
        if (mateExpChanges && mateExpChanges.length > 0) {
            return (0, puMateUpdateExpLevel_1.default)(connection, userId, cmsEx.JOB_TYPE.BATTLE, mateExpChanges);
        }
    })
        .then(() => {
        if (leaderMateCmsId !== undefined) {
            return (0, puMateUpdateFame_1.default)(connection, userId, leaderMateCmsId, cmsEx.JOB_TYPE.BATTLE, battleFame);
        }
        return null;
    })
        .then(() => {
        if (shipChanges.length > 0) {
            const promises = [];
            for (const change of shipChanges) {
                promises.push((0, puShipUpdateDurabilityAndSailor_1.default)(connection, userId, change.id, change.durability, change.sailor));
            }
            return Promise.all(promises);
        }
        return [];
    })
        .then(() => {
        if (cargoChanges.length > 0) {
            const promises = [];
            for (const change of cargoChanges) {
                promises.push((0, puShipCargoUpdate_1.default)(connection, userId, change));
            }
            return Promise.all(promises);
        }
        return [];
    })
        .then(() => {
        if (rewards) {
            const arr = [];
            for (const type of Object.keys(rewards)) {
                for (const id of Object.keys(rewards[type])) {
                    arr.push(rewards[type][id]);
                }
            }
            if (arr.length > 0) {
                return (0, puBattleRewardCreate_1.default)(connection, userId, arr);
            }
        }
        return null;
    })
        .then(() => {
        if (losses && (losses.sunkShipIds || losses.injuredMateCmsIds || losses.recoverableSailors)) {
            return (0, puGameOverLossUpdate_1.default)(connection, userId, JSON.stringify(losses));
        }
        return null;
    })
        .catch((err) => {
        if (err instanceof merror_1.MError) {
            throw err;
        }
        else {
            throw new merror_1.MError(err.message, merror_1.MErrorCode.BATTLE_END_TXN_ERROR);
        }
    });
}
function tuBattleEnd(dbConnPool, userId, mateExpChanges, userExpLevelChange, freeTakebackChange, ducatChange, blueGemChange, leaderMateCmsId, battleFame, gameState, rewards, losses, shipChanges, cargoChanges, insuranceChange, questContextChanges, itemChanges, reputationChanges) {
    return (0, mysqlUtil_1.withTxn)(dbConnPool, __filename, (connection) => {
        return queryImpl(connection, userId, mateExpChanges, userExpLevelChange, freeTakebackChange, ducatChange, blueGemChange, leaderMateCmsId, battleFame, gameState, rewards, losses, shipChanges, cargoChanges, insuranceChange, questContextChanges, itemChanges, reputationChanges);
    });
}
exports.default = tuBattleEnd;
//# sourceMappingURL=tuBattleEnd.js.map