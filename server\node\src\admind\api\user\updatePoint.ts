// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import moment from 'moment';

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import { AdminService } from '../../server';
import { <PERSON><PERSON><PERSON><PERSON>nse<PERSON><PERSON>, ensureBlockedByAdmin, ensureCompanyCreated } from '../../adminCommon';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { isCash } from '../../../cms/pointDesc';
import { DbConnPoolManager } from '../../../mysqllib/pool';
import paAdminLoadAccountIdPubIdUserIdByName from '../../../mysqllib/sp/paAdminLoadAccountIdPubIdUserIdByName';
import paAdminLoadAccountIdPubIdWorldIdNameByUserId from '../../../mysqllib/sp/paAdminLoadAccountIdPubIdWorldIdNameByUserId';
import paAdminAccountLoadBlockTimeUtcByAdmin from '../../../mysqllib/sp/paAdminAccountLoadBlockTimeUtcByAdmin';
import puAdminUserLoad from '../../../mysqllib/sp/puAdminUserLoad';
import puAdminPointUpdate from '../../../mysqllib/sp/puAdminPointUpdate';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';

interface RequestBody {
  gameUserId?: string;

  gameServerId?: string;
  nick?: string;

  flag: string; // 지급: 1, 삭제: 2
  pointCmsId: number;
  amount: string;
}

interface ResponseBody extends CommonResponseBody {
  data?: {
    updateDate: string;
    nid: string;
    nick: string;
    gameUserId: number;

    pointCmsId: number;
    oldValue: number;
    amount: number;
    newValue: number;
  };
}

export = (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /user/updatePoint', { body: req.body });

  const curTimeUtc = mutil.curTimeUtc();

  const { gameUserId, flag, pointCmsId }: RequestBody = req.body;
  let { nick, gameServerId: worldId }: RequestBody = req.body;
  const amount = parseInt(req.body.amount, 10);

  if (!gameUserId && !nick) {
    throw new MError('no gameUserId no nick', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }
  if (nick && !worldId) {
    throw new MError('no gameServerId', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }

  if (mutil.isNotANumber(amount) || amount <= 0) {
    throw new MError('invalid amount', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }

  const pointCms = cms.Point[pointCmsId];
  if (!pointCms) {
    throw new MError('invalid point cms id', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }
  if (
    isCash(pointCms.id) ||
    pointCms.id === cmsEx.EnergyPointCmsId ||
    pointCms.id === cmsEx.CashShopMileage
  ) {
    throw new MError(
      `not supported pointCmsId ${pointCms.id}`,
      MErrorCode.ADMIN_INVALID_PARAMETER,
      req.body
    );
  }

  if (flag != '1' && flag != '2') {
    throw new MError('invalid flag', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }

  const { authDbConnPool, userDbConnPoolMgrs, serviceLayoutMgr } = Container.get(AdminService);
  let userDbConnPoolMgr: DbConnPoolManager;

  let userId: number;
  let accountId: string;
  let pubId: string;

  let oldValue = 0;
  let newValue: number;

  return Promise.resolve()
    .then(() => {
      // load userId, pubId, accountId, worldId.
      if (gameUserId) {
        return paAdminLoadAccountIdPubIdWorldIdNameByUserId(
          authDbConnPool.getPool(),
          parseInt(gameUserId, 10),
          worldId
        );
      } else {
        return paAdminLoadAccountIdPubIdUserIdByName(authDbConnPool.getPool(), nick, worldId);
      }
    })
    .then((result) => {
      if (!result) {
        throw new MError('no user', MErrorCode.ADMIN_USER_NOT_FOUND, req.body);
      }
      accountId = result.accountId;
      pubId = result.pubId;
      nick = nick || result.name;
      userId = result.userId;
      worldId = result.worldId;

      userDbConnPoolMgr = userDbConnPoolMgrs[worldId];
      if (!userDbConnPoolMgr) {
        throw new MError('no gameServerId in service layout', MErrorCode.ADMIN_INVALID_PARAMETER, {
          worldId,
        });
      }

      // load blockTimeUtcByAdmin
      return paAdminAccountLoadBlockTimeUtcByAdmin(authDbConnPool.getPool(), accountId);
    })
    .then((result) => {
      ensureBlockedByAdmin(curTimeUtc, result);

      // load user
      return puAdminUserLoad(
        userDbConnPoolMgr.getPoolByUserIdAndShardFuncName(
          userId,
          serviceLayoutMgr.getUserDbShardFunction(worldId),
          worldId
        ),
        userId,
        mutil.curTimeUtc()
      );
    })
    .then((result) => {
      ensureCompanyCreated(
        result.gameState === GAME_STATE.NONE ? result.lastGameState : result.gameState
      );

      if (result.points) {
        const point = result.points.find((elem) => elem.cmsId == pointCmsId);
        if (point) {
          oldValue = point.value;
        }
      }
      if (flag == '1') {
        newValue = oldValue + amount;
      } else {
        newValue = oldValue - amount;
      }

      // check hard cap or enough
      if (newValue > pointCms.hardCap) {
        throw new MError('exceeds hard cap', MErrorCode.ADMIN_EXCEEDS_HARD_CAP, {
          body: req.body,
          oldValue,
          newValue,
        });
      }
      if (newValue < 0) {
        mlog.info('[/user/updatePoint] requested to apply negative point', {
          reqBody: req.body,
          oldValue,
          newValue,
        });
      }

      // update point
      return puAdminPointUpdate(
        userDbConnPoolMgr.getPoolByUserIdAndShardFuncName(
          userId,
          serviceLayoutMgr.getUserDbShardFunction(worldId),
          worldId
        ),
        userId,
        { cmsId: pointCmsId, value: newValue }
      );
    })
    .then(() => {
      const resp: ResponseBody = {
        isSuccess: true,
        data: {
          updateDate: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          nid: pubId,
          nick,
          gameUserId: userId,
          pointCmsId,
          oldValue,
          amount,
          newValue,
        },
      };

      mlog.info('[TX] /user/updatePoint', { body: resp });
      res.json(resp);
    });
};
