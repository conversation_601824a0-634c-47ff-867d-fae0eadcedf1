// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import moment from 'moment';

import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import mhttp from '../../../motiflib/mhttp';
import { RegisteredGameServer } from '../../../motiflib/mhttp/linegamesApiClient';
import mconf from '../../../motiflib/mconf';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import { PLATFORM } from '../../../motiflib/model/auth/enum';
import * as mutil from '../../../motiflib/mutil';
import { DBConnPool } from '../../../mysqllib/pool';
import taGetWorld from '../../../mysqllib/txn/taGetWorld';
import glog from '../../../motiflib/gameLog';
import { MRedisConnPool } from '../../../redislib/connPool';
import { WORLD_STATE_TYPE_ID, WorldStateDesc } from '../../../cms/worldStateDesc';
import { WorldStateUtil } from '../../worldState';
import { MysqlReqRepCounter } from '../../../mysqllib/mysqlReqRepCounter';

interface RequestBody {
  platform: number;
  gnidSessionToken: string;

  // for glog
  deviceType: string;
  osv: string; // OS 버전 값
  dm: string; // 기기 값. 기기 모델 값.
  deviceLang: string; // language-culture 값으로 디바이스 언어값
  lineLang: string; // 인게임 언어값
  v: string; // 클라이언트 마켓 바이너리 버전
  sk: string; // 스토어 타입
  country_ip: string; // IP 기반 국가 코드
  os_modulation: boolean; // OS 변조 유무. 변조일 경우 True, 아닌 경우 False
  emulator: boolean; // 에뮬레이터 분류. 에뮬레이터일 경우 True, 아닌 경우 False
  loading_time: number; // 게임 실행 시간부터 해당 로그인까지의 시간 (millisecond)
  loginPlatform: string; // ex) 구글, 페이스북
}

interface World {
  worldId: string;
  address?: string;
  port?: number;
  userId?: number;
  pubId?: string;
  nationCmsId?: number;
  worldStateCmsId?: number;
  order?: number;
  bIsNonPK?: boolean;
}

interface Response {
  worlds: World[];
  lastWorldId: string;
  errorCd?: number; //
}

export = (req: RequestAs<RequestBody>, res: ResponseAs<Response>) => {
  // [TEMP] production 환경에서 에디터 로그인 가능하게 하는 코드
  // const platform = req.body.platform !== undefined ? req.body.platform : mconf.platform;

  const platform =
    mconf.isDev && req.body.platform !== undefined ? req.body.platform : mconf.platform;

  mlog.info('[RX] /getWorlds', { body: req.body, platform: PLATFORM[platform] });

  const {
    gnidSessionToken,
    deviceType,
    osv,
    dm,
    deviceLang,
    lineLang,
    v,
    sk,
    country_ip,
    os_modulation,
    emulator,
    loading_time,
    loginPlatform,
  }: RequestBody = req.body;

  let resp: Response = {
    worlds: undefined,
    lastWorldId: undefined,
  };

  const reqRepCounter = Container.get(MysqlReqRepCounter);
  if (reqRepCounter.isLimitOver()) {
    resp.errorCd = MErrorCode.AUTH_LOGIN_FAILED_WITH_SERVER_BUSY;

    const limit = reqRepCounter.getLimitCount();
    const cnt = reqRepCounter.getCount();
    mlog.warn(`request-respons-count over`, { cnt, limit });

    res.status(503).json(resp);
    return;
  }

  let accountId: string; // (lg:gnid, editor:로그인 창에서 입력하는 아이디)
  let registeredGameServerList: RegisteredGameServer[];

  return Promise.resolve()
    .then(() => {
      if (platform === PLATFORM.LINE) {
        return mhttp.lgd.getNidAndServerInfoByGnid(gnidSessionToken);
      }
      return null;
    })
    .then((result) => {
      if (platform === PLATFORM.LINE) {
        accountId = result.gnid;
        registeredGameServerList = result.registeredGameServerList;
      } else if (platform === PLATFORM.DEV) {
        accountId = gnidSessionToken;

        const krPattern = /[ㄱ-ㅎ|ㅏ-ㅣ|가-힣]/;
        const blankPattern = /[\s]/g;
        if (krPattern.test(accountId) || blankPattern.test(accountId)) {
          throw new MError('invalid-id', MErrorCode.AUTH_INVALID_PUB_ID, req.body);
        }
      }

      const dbConnPool = Container.get(DBConnPool);
      return taGetWorld(dbConnPool.getPool(), accountId, mutil.curTimeUtc());
    })
    .then((result) => {
      resp.worlds = result.worlds || [];
      resp.lastWorldId = result.lastWorldId;

      // get current users per world
      const monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
      return monitorRedis['getUserCount']().then((retuserCount) => {
        const userCount = JSON.parse(retuserCount);

        // TODO: 리팩토링 필요함. (라인인 경우 registeredGameServerList 위주로)
        for (const worldInfo of mconf.worlds) {
          let order = 0;
          if (platform === PLATFORM.LINE) {
            order = registeredGameServerList.findIndex(
              (item) => item.gameServerId === worldInfo.id
            );

            if (order === -1) {
              continue;
            }
          }

          if (worldInfo.disabled) {
            continue;
          }

          let elem = resp.worlds.find((elem) => elem.worldId === worldInfo.id);
          if (!elem) {
            // 만들어진 선단이 없는 월드인 경우
            elem = {
              worldId: worldInfo.id,
            };
            resp.worlds.push(elem);
          }

          elem.order = order;

          elem.address = worldInfo.address;
          elem.port = worldInfo.port;

          elem.bIsNonPK = worldInfo.bIsNonPK;

          // world state
          if (userCount.user.world[elem.worldId]) {
            const curUsers = userCount.user.world[elem.worldId];

            const wsId = WorldStateUtil.getWorldState(curUsers, mconf.maxUsersPerWorld);
            elem.worldStateCmsId = wsId;
          } else {
            // 아직 유저가 한명도 입장안했거나 월드가 켜져있지 않는 상황
            // [todo] 실제 월드의 상태 체크필요
            elem.worldStateCmsId = WORLD_STATE_TYPE_ID.COMFORTABLE;
          }
        }

        // 라인일 경우 정렬
        if (platform === PLATFORM.LINE) {
          resp.worlds.sort((a, b) => a.order - b.order);
        }

        // glog
        glog('common_gnid_login', {
          _time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          os: deviceType ? deviceType.toUpperCase() : null,
          osv,
          dm,
          lang: deviceLang,
          lang_game: lineLang,
          v,
          sk,
          platform: loginPlatform,
          country_ip,
          os_modulation,
          emulator,
          loading_time,
          gnid: accountId,
        });

        mlog.info('[TX] /getWorlds', { body: resp });
        res.json(resp);
      });
    })
    .catch((error: Error) => {
      mlog.error('/getWorlds failed', {
        msg: error.message,
      });

      if (error instanceof MError) {
        throw new MError(`'/getWorlds' api err`, error.mcode, error.message);
      }
      throw new MError(error.message, MErrorCode.AUTH_GET_WORLD_ERR, undefined, error.stack);
    });
};
