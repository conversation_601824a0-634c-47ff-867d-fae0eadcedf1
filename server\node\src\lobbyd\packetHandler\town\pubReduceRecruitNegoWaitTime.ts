// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import mlog from '../../../motiflib/mlog';
import tuReduceRecruitNegoWaitTime from '../../../mysqllib/txn/tuReudceRecruitNegoWaitTime';
import { ClientPacketHandler } from '../index';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { SECONDS_PER_MINUTE } from '../../../formula';
import { MError, MErrorCode } from '../../../motiflib/merror';
import * as mutil from '../../../motiflib/mutil';
import { LobbyService } from '../../server';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Sync, Resp } from '../../type/sync';
import { ItemChange } from '../../userInven';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import { CostData, MakePubMateNegoTimeReductionData } from '../../../motiflib/gameLog';
import { ITEM_TYPE } from '../../../cms/itemDesc';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { needMateRecruitingTermsCheck } from '../../../cms/mateRecruitingGroupDesc';
import { AccumulateParam } from '../../userAchievement';

// ----------------------------------------------------------------------------
// 여관-고용 협상 대기 시간을 줄인다.
// ----------------------------------------------------------------------------

const rsn = 'pub_reduce_recruit_nego_wait_time';
const add_rsn = null;

interface RequestBody {
  mateCmsId: number;
  itemCmsId: number;
  count: number;
}

// ----------------------------------------------------------------------------
export class Cph_Town_PubReduceRecruitNegoWaitTime implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureInTown();

    const body: RequestBody = packet.bodyObj;
    const { mateCmsId, itemCmsId, count } = body;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    // 여관-고용 메뉴는 잠금되지 않음
    // user.userContentsTerms.ensureBuildingContentsUnlock(
    //   cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID,
    //   user
    // );

    if (!count || !_.isInteger(count)) {
      throw new MError(
        'invalid-count',
        MErrorCode.INVALID_REQ_BODY_PUB_REDUCE_RECRUIT_NEGO_WAIT_TIME,
        {
          count,
        }
      );
    }

    const townCmsId = user.userTown.getTownCmsId();
    const curTimeUtc = mutil.curTimeUtc();

    const itemCms = cms.Item[itemCmsId];
    if (!itemCms) {
      throw new MError('invalid-item-cms-id', MErrorCode.NO_KEY_IN_CMS, {
        itemCmsId,
      });
    }

    const buildingMenuCmsId = cmsEx.BUILDING_MENU_CMS_ID.PUB_RECRUITING_MATE;
    if (
      itemCms.timeCostValMin === undefined ||
      !itemCms.timeCostBuildingMenu?.includes(buildingMenuCmsId)
    ) {
      throw new MError('invalid-item', MErrorCode.INVALID_ITEM, {
        itemCmsId,
      });
    }

    const townMates = user.userMates.getCurTownMates(user.userTown);

    // 항해사 확인
    const mateCms = cms.Mate[mateCmsId];
    const townMate = townMates.mates[mateCmsId];
    if (!mateCms || !townMate) {
      throw new MError('invalid-mate-cms-id', MErrorCode.INVALID_MATE_CMS_ID, {
        mateCmsId,
      });
    }

    // Check time. 서버 클라간 시간이 정확히 같지 않기 때문에 100초의 여유를 둔다.
    const curLastUpdateTimeUtc = townMates.lastUpdateTimeUtc;
    const mateResetSeconds = cms.Const.RecruitResetCycleSec.value;
    if (curLastUpdateTimeUtc + mateResetSeconds <= curTimeUtc - 100) {
      throw new MError('time-is-expired', MErrorCode.PUB_MATE_TIME_EXPIRED, {
        mateCmsId,
        curLastUpdateTimeUtc,
        curTimeUtc,
        diff: curTimeUtc - curLastUpdateTimeUtc,
      });
    }

    // https://jira.line.games/browse/UWO-7925
    // https://jira.line.games/browse/UWO-19699 특권 보너스,
    // cms.MateRecruitingGroup.isMustAppear 이 true 또는 mustAppearEvent 조건 만족인 경우
    // cms.MateRecruitingGroup.contentsTerms 을 검사해야됨
    const townCms = cms.Town[user.userTown.getTownCmsId()];
    const mateRecruitingGroupCms = cmsEx.getMateRecruitingGroupByGroupAndMateCmsId(
      townCms.mateRecruitingGroup,
      mateCmsId
    );
    const eventPageProducts = user.userCashShop.getEventPageProducts();
    if (
      !mateRecruitingGroupCms ||
      (needMateRecruitingTermsCheck(mateRecruitingGroupCms, eventPageProducts, curTimeUtc) &&
        !user.userContentsTerms.isValidContentsTerms(mateRecruitingGroupCms.contentsTerms, user))
    ) {
      throw new MError('contents-terms-invalid', MErrorCode.CONTENTS_TERMS_INVALID, {
        mateCmsId,
        townCmsId: townCms.id,
        mateRecruitingGroupCms,
        contentsTerms: mateRecruitingGroupCms.contentsTerms,
      });
    }

    // Check contents terms
    const mateRecruitingCms = mateCms.mateRecruiting;
    user.userContentsTerms.ensureContentsTerms(mateRecruitingCms.contentsTerms, user);

    // 협상 대기 중이 아닌 경우
    if (!townMate.negoWaitExpirationTimeUtc || curTimeUtc >= townMate.negoWaitExpirationTimeUtc) {
      throw new MError(
        'not-waiting-for-negotiations',
        MErrorCode.NOT_IN_MATE_RECRUITING_NEGO_WAIT_TIME,
        {
          mateCmsId,
          negoWaitExpirationTimeUtc: townMate.negoWaitExpirationTimeUtc,
          curTimeUtc,
        }
      );
    }

    // 가속 아이템
    const itemChange: ItemChange = user.userInven.itemInven.buildItemChange(
      itemCmsId,
      -count,
      true
    );
    const cost_data: CostData[] = [
      {
        id: itemCmsId,
        amt: count,
        type: ITEM_TYPE[itemCms.type],
      },
    ];
    if (itemChange.count < 0) {
      throw new MError('not-enough-item', MErrorCode.NOT_ENOUGH_ITEM, {
        itemChange,
      });
    }

    // 클라 SyncData, null 관련해서 만료 처리를 현재 시간으로 보내줌..
    const reduceSecOfOnce = itemCms.timeCostValMin * SECONDS_PER_MINUTE;
    const oldRemainingTimeSec = townMate.negoWaitExpirationTimeUtc - curTimeUtc;

    // 최대 사용 가능한 갯수를 구함
    const maxCount: number = Math.ceil(oldRemainingTimeSec / reduceSecOfOnce);
    if (count > maxCount) {
      throw new MError(
        'over-userd-reduce-item',
        MErrorCode.INVALID_REQ_BODY_PUB_REDUCE_RECRUIT_NEGO_WAIT_TIME,
        {
          itemCmsId,
          count,
          maxCount,
        }
      );
    }

    const newNegoWaitExpirationTimeUtc: number = Math.max(
      townMate.negoWaitExpirationTimeUtc - count * reduceSecOfOnce,
      curTimeUtc
    );

    const resp: Resp = { sync: {} };
    const accums: AccumulateParam[] = [];

    return Promise.resolve()
      .then(() => {
        return tuReduceRecruitNegoWaitTime(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          undefined, // PointChange[]
          itemChange,
          townCmsId,
          townMate.index,
          newNegoWaitExpirationTimeUtc
        );
      })
      .then(() => {
        user.glog(
          'time_reduction',
          MakePubMateNegoTimeReductionData({
            rsn,
            add_rsn,

            old_duration: townMate.negoWaitExpirationTimeUtc - curTimeUtc,
            cur_duration: newNegoWaitExpirationTimeUtc - curTimeUtc,
            pr_data: null,
            cost_data,
            mate_recruit_data: {
              town_id: townCmsId,
              town_name: townCms.name,
              mate_id: mateCmsId,
              mate_name: displayNameUtil.getMateDisplayName(mateCmsId),
            },
          })
        );

        _.merge<Sync, Sync>(
          resp.sync,
          user.userInven.itemInven.applyItemChange(itemChange, accums, {
            user,
            rsn,
            add_rsn,
          })
        );

        townMate.negoWaitExpirationTimeUtc = newNegoWaitExpirationTimeUtc;
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            towns: {
              [townCmsId]: {
                myPubMates: {
                  mates: {
                    [mateCmsId]: {
                      negoWaitExpirationTimeUtc: newNegoWaitExpirationTimeUtc,
                    },
                  },
                },
              },
            },
          },
        });

        if (accums.length > 0) {
          return user.userAchievement.accumulate(accums, user, resp.sync, { user, rsn, add_rsn });
        }
      })
      .then(() => {
        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, resp);
      });
  }
}
