// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';

import cms from '../cms';
import * as cmsEx from '../cms/ex';
import { LoginInfo, GLogParam, User } from './user';
import * as sync from './type/sync';
import mlog from '../motiflib/mlog';
import { MError, MErrorCode } from '../motiflib/merror';
import {
  ItemDesc,
  ITEM_TYPE,
  ensureNotQuestStartingItem,
  ensureQuestStartingItem,
} from '../cms/itemDesc';
import mconf from '../motiflib/mconf';
import * as displayNameUtil from '../motiflib/displayNameUtil';
import { AccumulateParam } from './userAchievement';
import { SHIP_SLOT_SUB_TYPE } from '../cms/shipSlotDesc';
import Container from 'typedi';
import { LobbyService } from './server';
import { InvestSeasonDesc } from '../cms/investSeasonDesc';
import { RANKING_CMS_ID } from '../cms/rankingDesc';
import * as mutil from '../motiflib/mutil';
import * as formula from '../formula';
import { InvestmentSeasonRankSessionChangedPubMsg } from '../motiflib/model/realmd';
import { NationUtil } from '../motiflib/model/lobby/nationUtil';

// ----------------------------------------------------------------------------
// Public interfaces.
// ----------------------------------------------------------------------------

export interface Item {
  cmsId: number;
  count: number;
  unboundCount: number;
}

export interface QuestItem {
  id: number;
  cmsId: number;
  questCmsId: number;
  questRnds?: number[];
}

export interface ItemChange {
  cmsId: number;
  count: number;
  unboundCount: number;
  bIsAccum: boolean;
}

export interface QuestItemChange {
  id: number;
  questCmsId: number;
  questRnds: number[]; // null|undefined인 경우 빈 배열로 간주. 바뀌지 않음을 의미하는게 아님.
}

export interface ShipSlotItem {
  id: number;
  shipSlotCmsId: number;
  equippedShipId?: number;
  equippedShipSlotIdx?: number;
  isEquippedCostumeShipSlot?: boolean;
  isBound: number;
  isLocked: number;
  expireTimeUtc?: number;
  enchantLv: number;
}

interface ReceivableResult {
  /**
   * 여러 조건들을 고려해서 최종적으로 추가할 수 있는 수량
   * MaxHavable({@link ItemDesc.havableCount}/CMS.Const.UserItemHardCap), 인벤 공간 순으로 계산
   */
  receivable: number;
  /**
   * 최대 소지 개수에 대한 초과분
   */
  excessMaxHavable: number;
  /**
   * 인벤 공간 부족에 대한 초과분
   */
  excessSpace: number;

  /** 디버그용 데이터.. */
  extraForLog?: unknown;
}

interface LimitedTimeShipSlotItems {
  expireTimeUtc: number;
  ids: Set<number>;
}

// ----------------------------------------------------------------------------
// item inven (item manager) class.
// ----------------------------------------------------------------------------

export class ItemInven {
  private _userId: number;
  private _expanded: number;
  private _slotsOccupied: number;
  private _items: { [cmsId: number]: Item } = {};
  private _lastQuestItemId: number;
  private _questItems: { [id: number]: QuestItem } = {};

  constructor() {
    //
  }

  clone(): ItemInven {
    const deepCopy = new ItemInven();
    deepCopy.cloneSet(
      this._userId,
      this._expanded,
      this._slotsOccupied,
      _.cloneDeep(this._items),
      this._lastQuestItemId,
      _.cloneDeep(this._questItems)
    );
    return deepCopy;
  }

  cloneSet(
    userId: number,
    expanded: number,
    slotsOccupied: number,
    items: { [cmsId: number]: Item },
    lastQuestItemId: number,
    questItems: { [cmsId: number]: QuestItem }
  ): void {
    this._userId = userId;
    this._expanded = expanded;
    this._slotsOccupied = slotsOccupied;
    this._items = items;
    this._lastQuestItemId = lastQuestItemId;
    this._questItems = questItems;
  }

  initWithLoginInfo(loginInfo: LoginInfo, expanded: number) {
    this._userId = loginInfo.userId;
    this._expanded = expanded;
    this._slotsOccupied = 0;

    for (const item of loginInfo.items) {
      this._items[item.cmsId] = {
        cmsId: item.cmsId,
        count: item.count,
        unboundCount: item.unboundCount,
      };

      this._slotsOccupied += this.calcSlotOccupied(item.cmsId);
    }

    this._lastQuestItemId = loginInfo.lastQuestItemId;
    for (const item of loginInfo.questItems) {
      const itemCms = cms.Item[item.itemCmsId];

      this._questItems[item.id] = {
        id: item.id,
        cmsId: itemCms.id,
        questCmsId: item.questCmsId,
        questRnds: item.questRnds?.split(':').map(Number),
      };

      if (itemCms.isSlotOccupied) {
        // 퀘스트 아이템은 스택되지 않는다.
        this._slotsOccupied++;
      }
    }
  }

  setExpanded(expandSlot: number) {
    this._expanded = expandSlot;
  }

  private _getMaxSlotCount(): number {
    return cms.Const.BaseItemSlot.value + this._expanded;
  }

  /**
   * 남은 슬롯 개수를 구함.
   */
  private _getRemainingSlotCount(): number {
    return Math.max(this._getMaxSlotCount() - this._slotsOccupied, 0);
    // 인벤 넘칠 수 있는 경우가 있어서 음수 방지
  }

  getRemainingSlotCount(): number {
    return this._getRemainingSlotCount();
  }

  /**
   * @returns number (0 이상)
   */
  private _calcRemainingStackableSpace(curCount: number, maxStack: number): number {
    assert(curCount >= 0);
    assert(maxStack > 0);

    // 인벤 슬롯이 이미 초과된 상태(ex: 52/50)라도 스택이 남았다면 공간이 있는 걸로 보는 기획이라함.

    const slotsRemain = this._getRemainingSlotCount();

    // 스택할 수 있는 남은 개수. ( 꽉 차지 않은 스택이 있는 경우 )
    const stackRemain = curCount % maxStack > 0 ? maxStack - (curCount % maxStack) : 0;

    if (slotsRemain === 0 && stackRemain === 0) {
      // 남은 슬롯이 없고 남은 스택 공간도 없다면.
      return 0;
    }
    return slotsRemain * maxStack + stackRemain;
  }

  getItem(cmsId: number): Item {
    return this._items[cmsId];
  }

  getCount(cmsId: number, bOnlyUnbound: boolean = false): number {
    ensureNotQuestStartingItem(cms.Item[cmsId]);
    return this._getCount(cmsId, bOnlyUnbound);
  }

  private _getCount(cmsId: number, bOnlyUnbound: boolean = false): number {
    const item = this._items[cmsId];
    if (item) {
      return (bOnlyUnbound ? 0 : item.count) + item.unboundCount;
    }

    return 0;
  }

  // @param amount 양수인 경우 획득, 음수인 경우 소모.
  // @param bBound 아이템을 제거하는 경우에 값이 false일 경우 only ubbound 의미로 사용 됨.
  // true일 경우 귀속부터 차감 이후 비귀속 차감
  buildItemChange(
    cmsId: number,
    amount: number,
    bIsAccum: boolean,
    bBound: boolean = true
  ): ItemChange {
    if (!this._items[cmsId]) {
      this._items[cmsId] = {
        cmsId,
        count: 0,
        unboundCount: 0,
      };
    }
    const item = this._items[cmsId];
    if (amount < 0 && this._getCount(cmsId, !bBound) < -amount) {
      throw new MError('not-enough-item-count', MErrorCode.NOT_ENOUGH_ITEM, {
        item,
        amount,
      });
    }

    if (amount < 0) {
      const boundCountToUse = bBound ? Math.min(item.count, -amount) : 0;
      return {
        cmsId,
        count: item.count - boundCountToUse,
        unboundCount: item.unboundCount + amount + boundCountToUse,
        bIsAccum,
      };
    } else {
      const oldCount = this._getCount(cmsId);
      const hardCap = cms.Const.UserItemHardCap.value;
      if (oldCount >= hardCap) {
        return undefined;
      } else if (oldCount + amount > hardCap) {
        mlog.error('Item count is greater than hard cap.', {
          userId: this._userId,
          item,
          cmsId,
          amount,
          isBound: bBound,
        });
        amount = hardCap - oldCount;
      }

      return {
        cmsId,
        count: bBound ? item.count + amount : item.count,
        unboundCount: bBound ? item.unboundCount : item.unboundCount + amount,
        bIsAccum,
      };
    }
  }

  buildItemChangesForRemovingAll(itemCmsIds: Set<number>, bIsAccum: boolean): ItemChange[] {
    const itemChanges: ItemChange[] = [];
    for (const itemCmsId of itemCmsIds) {
      ensureNotQuestStartingItem(cms.Item[itemCmsId]);
      const count = this._getCount(itemCmsId);
      if (count > 0) {
        itemChanges.push(this.buildItemChange(itemCmsId, -count, bIsAccum));
      }
    }
    return itemChanges;
  }

  calcSlotOccupied(itemCmsId: number): number {
    const item = this._items[itemCmsId];
    if (!item) {
      return 0;
    }

    return ItemInven.calcSlotOccupied(itemCmsId, item.count, item.unboundCount);
  }

  static calcSlotOccupied(itemCmsId: number, boundCount: number, unbountCount: number): number {
    const itemCms = cms.Item[itemCmsId];

    if (!itemCms.isSlotOccupied) {
      return 0;
    }
    return Math.ceil(boundCount / itemCms.maxStack) + Math.ceil(unbountCount / itemCms.maxStack);
  }

  // !! 획득 시 랭킹 집계 대상인 경우에만 true로 유지한다. 아닌 경우에는 명시적으로 false 로 넘겨줘야한다.
  // accums가 null이 아닐 경우에만 업적
  applyItemChange(
    itemChange: ItemChange,
    accums: AccumulateParam[],
    glogParam: GLogParam | null
  ): sync.Sync {
    if (!itemChange) {
      return {};
    }
    const itemCms = cms.Item[itemChange.cmsId];
    if (itemCms.type === ITEM_TYPE.RANDOM_QUEST) {
      throw new MError(
        'quest-item-can-not-insert-to-items',
        MErrorCode.QUEST_ITEM_CAN_NOT_INSERT_TO_ITEMS_TABLE,
        {
          itemChange,
        }
      );
    }

    const curTimeUtc: number = mutil.curTimeUtc();
    const oldSlots = this.calcSlotOccupied(itemCms.id);
    const oldCount = this._getCount(itemCms.id);

    this._items[itemChange.cmsId] = itemChange;

    const newCount = this._getCount(itemCms.id);

    // Update slots occupied.
    if (itemCms.isSlotOccupied) {
      const newSlots = this.calcSlotOccupied(itemCms.id);
      this._slotsOccupied += newSlots - oldSlots;
    }

    const addedValue = newCount - oldCount;
    if (addedValue > 0) {
      if (itemChange.bIsAccum && accums) {
        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ACCUMULATED_ITEM_COUNT,
          targets: [itemCms.id],
          addedValue,
        });
      }
    }

    // glog
    if (glogParam) {
      glogParam.user.glog('item', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        flag: newCount > oldCount ? 1 : 2,
        type: itemCms.type,
        id: itemCms.id,
        name: displayNameUtil.getItemDisplayName(itemCms),
        cv: newCount - oldCount,
        rv: newCount,
      });
    }

    return {
      add: {
        items: {
          [itemChange.cmsId]: itemChange,
        },
      },
    };
  }

  calcAddable(cmsId: number, amount: number, bIsBound: boolean): number {
    const itemCms = cms.Item[cmsId];
    ensureNotQuestStartingItem(itemCms);

    if (amount === 0) {
      return amount;
    }

    // If we're removing, check if it goes below zero.
    if (amount < 0) {
      const curCount = this._getCount(cmsId);
      if (curCount + amount < 0) {
        return -curCount;
      }

      return amount;
    }

    const result = this._calcReceivable(itemCms, amount, bIsBound);
    return result.receivable;
  }

  canAdd(itemCmsId: number, addAmount: number, bIsBound: boolean): boolean {
    const itemCms = cms.Item[itemCmsId];
    ensureNotQuestStartingItem(itemCms);
    assert(addAmount > 0);

    const result = this._calcReceivable(itemCms, addAmount, bIsBound);
    return result.receivable === addAmount;
  }

  ensureCanAdd(itemCmsId: number, addAmount: number, bIsBound: boolean): void {
    const itemCms = cms.Item[itemCmsId];
    ensureNotQuestStartingItem(itemCms);
    assert(addAmount > 0);

    const result = this._calcReceivable(itemCms, addAmount, bIsBound);
    if (result.receivable === addAmount) {
      return;
    }

    const curCount = this._getCount(itemCmsId);

    if (result.excessMaxHavable) {
      throw new MError('can-not-add-item-because-of-max-havable', MErrorCode.ITEM_MAX_HAVABLE, {
        itemCmsId,
        addAmount,
        curCount,
        receivable: result,
      });
    }

    if (result.excessSpace) {
      throw new MError(
        'can-not-add-item-because-slot-is-not-enough',
        MErrorCode.USER_ITEM_INVENTORY_SLOT_IS_NOT_ENOUGH,
        { itemCmsId, addAmount, curCount, receivable: result }
      );
    }

    throw new MError('add-item-error-reason-should-be-implemented', MErrorCode.INTERNAL_ERROR, {
      itemCmsId,
      addAmount,
      curCount,
      receivable: result,
    });
  }

  calcReceivable(
    cmsId: number,
    addAmount: number,
    bIsBound: boolean,
    bAllowOverInven: boolean = false
  ): ReceivableResult {
    const itemCms = cms.Item[cmsId];
    ensureNotQuestStartingItem(itemCms);
    assert(addAmount > 0);
    return this._calcReceivable(itemCms, addAmount, bIsBound, bAllowOverInven);
  }

  /**
   * 아이템 개수 증가가 가능한지 조건을 순서대로 확인
   * @param itemCms 퀘스트 아이템이 아니어야함
   * @param addAmount 0 보다 커야함
   */
  private _calcReceivable(
    itemCms: ItemDesc,
    addAmount: number,
    bIsBound: boolean,
    bAllowOverInven: boolean = false
  ): ReceivableResult {
    const result: ReceivableResult = {
      receivable: 0,
      excessMaxHavable: 0,
      excessSpace: 0,

      extraForLog: {
        slotOccupied: this._slotsOccupied,
        maxSlotCount: this._getMaxSlotCount(),
        item: _.cloneDeep(this._items[itemCms.id]),
      },
    };

    let curCount = 0;

    const userItem = this._items[itemCms.id];
    if (userItem) {
      curCount = bIsBound ? userItem.count : userItem.unboundCount;
    }

    // #. havableCount, hardCap
    const maxHavable = itemCms.havableCount ?? cms.Const.UserItemHardCap.value;
    assert(maxHavable <= cms.Const.UserItemHardCap.value); // TODO? Validation
    const remainMaxHavable = Math.max(0, maxHavable - curCount);
    if (addAmount > remainMaxHavable) {
      result.excessMaxHavable = addAmount - remainMaxHavable;
      addAmount = remainMaxHavable;
    }

    if (bAllowOverInven || !itemCms.isSlotOccupied) {
      result.receivable = addAmount;
      return result;
    }

    // #. inven space
    const remainSpace = this._calcRemainingStackableSpace(curCount, itemCms.maxStack);
    if (addAmount > remainSpace) {
      result.excessSpace = addAmount - remainSpace;
      addAmount = remainSpace;
    }

    result.receivable = addAmount;
    return result;
  }

  gatherBattleItems(outItems: { [itemCmsId: number]: number }): void {
    _.forOwn(this._items, (item) => {
      const itemCms = cms.Item[item.cmsId];
      if (itemCms.battleSkillId) {
        outItems[item.cmsId] = this._getCount(item.cmsId);
      }
    });
  }

  getLastQuestItemId(): number {
    return this._lastQuestItemId;
  }

  getNewQuestItemId(): number {
    return this._lastQuestItemId + 1;
  }

  getQuestItem(id: number): QuestItem {
    return this._questItems[id];
  }

  getQuestItemCount(cmsId: number): number {
    ensureQuestStartingItem(cms.Item[cmsId]);
    return this._getQuestItemCount(cmsId);
  }

  /**
   * 퀘스트 아이템을 많이 가진 상태에서 자주 호출된다면 과도한 순회가 일어날 수 있는 것 참고
   * 낱개로 하나씩 추가될 때마다 glog 등 에서 현재 수량이 기록된다면..
   */
  private _getQuestItemCount(cmsId: number): number {
    return _.reduce(this._questItems, (acc, elem) => acc + (elem.cmsId === cmsId ? 1 : 0), 0);
  }

  // TODO glog 심는 부분이 없어서 추가해야 될 듯 한데 퀘스트 아이템은 uid 를 가짐. 확인 필요.
  addQuestItem(item: QuestItem): void {
    const itemCms = cms.Item[item.cmsId];
    ensureQuestStartingItem(itemCms);

    this._questItems[item.id] = item;

    if (item.id > this._lastQuestItemId) {
      this._lastQuestItemId = item.id;
    }

    if (itemCms.isSlotOccupied) {
      // 퀘스트 아이템은 스택되지 않는다.
      this._slotsOccupied++;
    }
  }

  removeQuestItem(id: number): void {
    const questItem = this._questItems[id];
    assert(questItem);

    delete this._questItems[id];

    const itemCms = cms.Item[questItem.cmsId];
    if (itemCms.isSlotOccupied) {
      // 퀘스트 아이템은 스택되지 않는다.
      this._slotsOccupied--;
    }
  }

  ensureCanAddQuestItem(itemCmsId: number, addAmount: number): void {
    const itemCms = cms.Item[itemCmsId];
    ensureQuestStartingItem(itemCms);
    assert(addAmount > 0);

    const result = this._calcQuestItemReceivable(itemCms, addAmount);
    if (result.receivable === addAmount) {
      return;
    }

    const curCount = this._getQuestItemCount(itemCmsId);

    if (result.excessMaxHavable) {
      throw new MError(
        'can-not-add-quest-item-because-of-havable-count-or-hard-cap',
        MErrorCode.ITEM_MAX_HAVABLE,
        { itemCmsId, addAmount, curCount, receivable: result }
      );
    }

    if (result.excessSpace) {
      throw new MError(
        'can-not-add-quest-item-because-slot-is-not-enough',
        MErrorCode.USER_ITEM_INVENTORY_SLOT_IS_NOT_ENOUGH,
        { itemCmsId, addAmount, curCount, receivable: result }
      );
    }

    throw new MError('quest-item-error-reason-should-be-implemented', MErrorCode.INTERNAL_ERROR, {
      itemCmsId,
      addAmount,
      curCount,
      receivable: result,
    });
  }

  calcQuestItemReceivable(
    itemCmsId: number,
    addAmount: number,
    bAllowExceedSpace: boolean = false
  ): ReceivableResult {
    const itemCms = cms.Item[itemCmsId];
    ensureQuestStartingItem(itemCms);
    assert(addAmount > 0);
    return this._calcQuestItemReceivable(itemCms, addAmount, bAllowExceedSpace);
  }

  /**
   * 퀘스트 아이템 개수 증가가 가능한 지 조건을 순서대로 확인
   * @param itemCms 퀘스트 아이템이어야함.
   * @param addAmount 0 보다 커야함
   */
  private _calcQuestItemReceivable(
    itemCms: ItemDesc,
    addAmount: number,
    bAllowExceedSpace: boolean = false
  ): ReceivableResult {
    const result: ReceivableResult = {
      receivable: 0,
      excessMaxHavable: 0,
      excessSpace: 0,

      extraForLog: {
        slotOccupied: this._slotsOccupied,
        maxSlotCount: this._getMaxSlotCount(),
      },
    };

    const curCount = this._getQuestItemCount(itemCms.id);

    // #. havableCount, hardCap
    const maxHavable = itemCms.havableCount ?? cms.Const.UserItemHardCap.value;
    // TODO? 퀘스트 시작 아이템 에서도 UserItemHardCap 이 유효한지 확인? 퀘스트 시작 아이템은 CEquip 처럼 id 로 관리되는데..
    const remainMaxHavable = Math.max(0, maxHavable - curCount);
    if (addAmount > remainMaxHavable) {
      result.excessMaxHavable = addAmount - remainMaxHavable;
      addAmount = remainMaxHavable;
    }

    if (bAllowExceedSpace || !itemCms.isSlotOccupied) {
      result.receivable = addAmount;
      return result;
    }

    // #. inven space
    // - 퀘스트 아이템은 스택되지 않음.
    const remainSpace = this._getRemainingSlotCount();
    if (addAmount > remainSpace) {
      result.excessSpace = addAmount - remainSpace;
      addAmount = remainSpace;
    }

    result.receivable = addAmount;
    return result;
  }

  getSyncData(): sync.All {
    const ret: sync.All = {
      items: this._items,
      questItems: this._questItems,
    };

    return ret;
  }

  // 치트용
  removeAllItemsForCheat() {
    this._items = {};
    this._questItems = {};
    this._slotsOccupied = 0;
  }
}

export class UserInven {
  itemInven: ItemInven = new ItemInven();
  private _slotExpansions: { [type: number]: number } = {};
  private _shipSlotItems: { [id: number]: ShipSlotItem } = {};
  // 기간이 지난 부품에 대해선 장착 해제만 시켜주므로 장착된 기간제 부품만 만료시간으로 정렬해서 캐싱
  private _equippedLimitedTimeShipSlotItems: LimitedTimeShipSlotItems[] = [];
  private _lastShipSlotItemId: number = 0;

  constructor() {}

  clone(): UserInven {
    const c = new UserInven();
    c.cloneSet(
      this.itemInven.clone(),
      _.cloneDeep(this._slotExpansions),
      _.cloneDeep(this._shipSlotItems),
      _.cloneDeep(this._equippedLimitedTimeShipSlotItems),
      this._lastShipSlotItemId
    );
    return c;
  }

  cloneSet(
    itemInven: ItemInven,
    slotExpansions: { [type: number]: number },
    shipSlotItems: { [id: number]: ShipSlotItem },
    equippedLimitedTimeShipSlotItems: LimitedTimeShipSlotItems[],
    lastShipSlotItemId: number
  ): void {
    this.itemInven = itemInven;
    this._slotExpansions = slotExpansions;
    this._shipSlotItems = shipSlotItems;
    this._equippedLimitedTimeShipSlotItems = equippedLimitedTimeShipSlotItems;
    this._lastShipSlotItemId = lastShipSlotItemId;
  }

  initWithLoginInfo(loginInfo: LoginInfo) {
    for (const elem of loginInfo.slotExpansions) {
      this._slotExpansions[elem.type] = elem.expanded;
    }

    this.itemInven.initWithLoginInfo(
      loginInfo,
      this.getSlotExpansion(cmsEx.INVENTORY_TYPE.USER_ITEM)
    );

    const equippedCostumeShipSlotItemIds = loginInfo.costumeShipSlots.map((e) => e.shipSlotItemId);
    for (const elem of loginInfo.shipSlotItems) {
      const shipSlotItem = {
        id: elem.id,
        shipSlotCmsId: elem.shipSlotCmsId,
        isBound: elem.isBound,
        isLocked: elem.isLocked,
        expireTimeUtc: elem.expireTimeUtc ? parseInt(elem.expireTimeUtc, 10) : undefined,
        enchantLv: elem.enchantLevel,
      };
      this._shipSlotItems[elem.id] = shipSlotItem;

      if (equippedCostumeShipSlotItemIds.includes(elem.id)) {
        this._shipSlotItems[elem.id].isEquippedCostumeShipSlot = true;
        if (elem.expireTimeUtc) {
          this.addLimitedTimeShipSlotItemId(shipSlotItem.id, shipSlotItem.expireTimeUtc);
        }
      }
    }
    for (const elem of loginInfo.shipSlots) {
      if (elem.shipSlotItemId) {
        this._shipSlotItems[elem.shipSlotItemId].equippedShipId = elem.shipId;
        this._shipSlotItems[elem.shipSlotItemId].equippedShipSlotIdx = elem.slotIndex;

        if (this._shipSlotItems[elem.shipSlotItemId].expireTimeUtc) {
          this.addLimitedTimeShipSlotItemId(
            elem.shipSlotItemId,
            this._shipSlotItems[elem.shipSlotItemId].expireTimeUtc
          );
        }
      }
    }

    this._lastShipSlotItemId = loginInfo.lastShipSlotItemId;
  }

  getSlotExpansions(): { [type: number]: number } {
    return this._slotExpansions;
  }

  getSlotExpansion(type: cmsEx.INVENTORY_TYPE): number {
    if (this._slotExpansions[type] === undefined) {
      return 0;
    }
    return this._slotExpansions[type];
  }

  setSlotExpansion(type: cmsEx.INVENTORY_TYPE, expanded: number): void {
    this._slotExpansions[type] = expanded;
    switch (type) {
      case cmsEx.INVENTORY_TYPE.USER_ITEM:
        this.itemInven.setExpanded(expanded);
        break;
    }
  }

  gatherBattleItems(outItems: { [itemCmsId: number]: number }): void {
    this.itemInven.gatherBattleItems(outItems);
  }

  // ----------------------------------------------------------------------------
  // ship slot item
  // ----------------------------------------------------------------------------
  getNewShipSlotItemId(): number {
    return this._lastShipSlotItemId + 1;
  }

  generateNewShipSlotItemId(): number {
    return ++this._lastShipSlotItemId;
  }

  getLastShipSlotItemId(): number {
    return this._lastShipSlotItemId;
  }

  getShipSlotItem(id: number): ShipSlotItem {
    return this._shipSlotItems[id];
  }

  getShipSlotItems(): { [id: number]: ShipSlotItem } {
    return this._shipSlotItems;
  }

  getExpiredEquippedShipSlotItemIds(curTimeUtc: number): number[] {
    const expiredIds: number[] = [];
    for (const limitedTimeShipSlotItem of this._equippedLimitedTimeShipSlotItems) {
      if (curTimeUtc < limitedTimeShipSlotItem.expireTimeUtc) {
        break;
      }

      expiredIds.push(...limitedTimeShipSlotItem.ids);
    }

    return expiredIds;
  }

  addShipSlotItemForSync(shipSlotItem: sync.ShipSlotItem, glogParam: GLogParam) {
    this.addShipSlotItem(
      {
        id: shipSlotItem.id,
        shipSlotCmsId: shipSlotItem.shipSlotCmsId,
        isBound: shipSlotItem.isBound,
        isLocked: shipSlotItem.isLocked,
        equippedShipId: shipSlotItem.equippedShipId,
        equippedShipSlotIdx: shipSlotItem.equippedShipSlotIdx,
        expireTimeUtc: shipSlotItem.expireTimeUtc,
        isEquippedCostumeShipSlot: false,
        enchantLv: shipSlotItem.enchantLv,
      },
      glogParam
    );
  }

  addShipSlotItem(shipSlotItem: ShipSlotItem, glogParam: GLogParam) {
    this._shipSlotItems[shipSlotItem.id] = shipSlotItem;
    if (shipSlotItem.id > this._lastShipSlotItemId) {
      this._lastShipSlotItemId = shipSlotItem.id;
    }

    if (
      (shipSlotItem.equippedShipId ||
        shipSlotItem.equippedShipSlotIdx ||
        shipSlotItem.isEquippedCostumeShipSlot) &&
      shipSlotItem.expireTimeUtc
    ) {
      this.addLimitedTimeShipSlotItemId(shipSlotItem.id, shipSlotItem.expireTimeUtc);
    }

    if (glogParam) {
      const shipSlotCms = cms.ShipSlot[shipSlotItem.shipSlotCmsId];
      glogParam.user.glog('parts', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        flag: 1, // 1: create, 2: remove
        id: shipSlotItem.shipSlotCmsId,
        name: shipSlotCms.name,
        uid: shipSlotItem.id,
      });
    }
  }

  removeShipSlotItem(id: number, glogParam: GLogParam): ShipSlotItem {
    const removedShipSlotItem = this._shipSlotItems[id];

    // glog
    if (glogParam) {
      const shipSlotCms = cms.ShipSlot[removedShipSlotItem.shipSlotCmsId];

      glogParam.user.glog('parts', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        flag: 2, // 1: create, 2: remove
        id: removedShipSlotItem.shipSlotCmsId,
        name: shipSlotCms.name,
        uid: id,
      });
    }

    if (removedShipSlotItem.expireTimeUtc) {
      this.deleteLimitedTimeShipSlotItemId(removedShipSlotItem.id);
    }

    delete this._shipSlotItems[id];
    return removedShipSlotItem;
  }

  setShipSlotItemLock(id: number, isLocked: number) {
    if (!this.getShipSlotItem(id)) {
      throw new MError('invalid-ship-slot-item', MErrorCode.INVALID_SHIP_SLOT_ITEM, {
        id,
      });
    }

    this._shipSlotItems[id].isLocked = isLocked;
  }

  setShipSlotItemEnchantLv(user: User, id: number, enchantLv: number, sync: sync.Sync) {
    const shipSlotItem = this._shipSlotItems[id];
    if (!shipSlotItem) {
      return;
    }

    const oldEnchantLv = shipSlotItem.enchantLv;
    shipSlotItem.enchantLv = enchantLv;
    // 장착되어 있다면 스탯 갱신
    if (shipSlotItem.equippedShipId) {
      const ship = user.userFleets.getShip(shipSlotItem.equippedShipId);
      ship.enchantSlotItem(
        shipSlotItem.equippedShipSlotIdx,
        oldEnchantLv,
        enchantLv,
        user.userFleets,
        user.userInven,
        user.companyStat,
        user.userPassives,
        user.userSailing,
        user.userTriggers,
        user.userBuffs,
        sync
      );
    }

    if (shipSlotItem.isEquippedCostumeShipSlot) {
      user.userFleets.enchantCostumeShipSlotItem(
        shipSlotItem.id,
        oldEnchantLv,
        enchantLv,
        this,
        user.userPassives,
        user.userSailing,
        user.userTriggers,
        user.userBuffs,
        user.companyStat,
        sync
      );
    }
  }

  calcShipSlotItemAddable(count: number): number {
    assert(count >= 0);
    const capacity =
      cms.Const.BaseShipSlot.value + this.getSlotExpansion(cmsEx.INVENTORY_TYPE.SHIP_SLOT_ITEM);
    let curNum = 0;
    _.forOwn(this._shipSlotItems, (elem) => {
      if (elem.equippedShipId || elem.isEquippedCostumeShipSlot) {
        return;
      }
      curNum++;
    });
    const remaining = capacity - curNum;
    if (remaining >= count) {
      return count;
    } else {
      return remaining < 0 ? 0 : remaining;
    }
  }

  canAddShipSlotItem(count: number): boolean {
    assert(count >= 0);
    if (this.calcShipSlotItemAddable(count) === count) {
      return true;
    }
    return false;
  }

  ensureShipSlotItemSpaceForAdd(count: number): void {
    if (!this.canAddShipSlotItem(count)) {
      let curNum = 0;
      _.forOwn(this._shipSlotItems, (elem) => {
        if (elem.equippedShipId || elem.isEquippedCostumeShipSlot) {
          return;
        }
        curNum++;
      });
      const capacity =
        cms.Const.BaseShipSlot.value + this.getSlotExpansion(cmsEx.INVENTORY_TYPE.SHIP_SLOT_ITEM);
      throw new MError(
        'not-enough-ship-slot-item-inven-space',
        MErrorCode.NOT_ENOUGH_SHIP_SLOT_ITEM_INVEN_SPACE,
        { capacity, cur: curNum, countToAdd: count }
      );
    }
  }

  equipShipSlotItem(shipSlotItemId: number, shipId: number, shipSlotIdx: number): ShipSlotItem {
    const slotItem = this._shipSlotItems[shipSlotItemId];
    assert(slotItem);

    slotItem.equippedShipId = shipId;
    slotItem.equippedShipSlotIdx = shipSlotIdx;

    if (slotItem.expireTimeUtc) {
      this.addLimitedTimeShipSlotItemId(slotItem.id, slotItem.expireTimeUtc);
    }

    return slotItem;
  }

  unequipShipSlotItem(shipSlotItemId: number): ShipSlotItem {
    const slotItem = this._shipSlotItems[shipSlotItemId];
    assert(slotItem);

    delete this._shipSlotItems[shipSlotItemId].equippedShipId;
    delete this._shipSlotItems[shipSlotItemId].equippedShipSlotIdx;

    if (slotItem.expireTimeUtc) {
      this.deleteLimitedTimeShipSlotItemId(slotItem.id);
    }

    return slotItem;
  }

  equipCostumeShipSlotItem(itemId: number) {
    const shipSlotItem = this._shipSlotItems[itemId];
    assert(shipSlotItem);

    shipSlotItem.isEquippedCostumeShipSlot = true;

    if (shipSlotItem.expireTimeUtc) {
      this.addLimitedTimeShipSlotItemId(itemId, shipSlotItem.expireTimeUtc);
    }

    return shipSlotItem;
  }

  unequipCostumeShipSlotItem(itemId: number) {
    const shipSlotItem = this._shipSlotItems[itemId];
    assert(shipSlotItem);

    shipSlotItem.isEquippedCostumeShipSlot = false;

    if (shipSlotItem.expireTimeUtc) {
      this.deleteLimitedTimeShipSlotItemId(itemId);
    }

    return shipSlotItem;
  }

  buildShipSlotItem(
    shipSlotCmsId: number,
    isBound: number,
    isLocked: number,
    curTimeUtc: number,
    expireTimeUtc?: number,
    enchantLv?: number,
    equippedShipId?: number,
    equippedShipSlotIdx?: number,
    isEquippedCostumeShipSlot?: boolean
  ): ShipSlotItem {
    if (!expireTimeUtc && curTimeUtc) {
      const shipSlotCms = cms.ShipSlot[shipSlotCmsId];
      expireTimeUtc = shipSlotCms.expireType ? curTimeUtc + shipSlotCms.expireTime : undefined;
    }

    if (expireTimeUtc) {
      isBound = 1;
    }

    return {
      id: this.getNewShipSlotItemId(),
      shipSlotCmsId,
      isBound,
      isLocked,
      expireTimeUtc,
      enchantLv: enchantLv ?? 0,
      equippedShipId,
      equippedShipSlotIdx,
      isEquippedCostumeShipSlot,
    };
  }

  private addLimitedTimeShipSlotItemId(itemId: number, expireTimeUtc: number) {
    let index = -1;
    const limitedShipSlotCount = this._equippedLimitedTimeShipSlotItems.length;
    for (let i = 0; i < limitedShipSlotCount; i++) {
      const limitedTimeShipSlotItems = this._equippedLimitedTimeShipSlotItems[i];
      if (expireTimeUtc < limitedTimeShipSlotItems.expireTimeUtc) {
        const ids = new Set<number>();
        ids.add(itemId);
        this._equippedLimitedTimeShipSlotItems.splice(i, 0, {
          expireTimeUtc,
          ids,
        });
        index = i;
        break;
      } else if (expireTimeUtc === limitedTimeShipSlotItems.expireTimeUtc) {
        limitedTimeShipSlotItems.ids.add(itemId);
        index = i;
        break;
      }
    }

    if (index === -1) {
      const ids = new Set<number>();
      ids.add(itemId);
      this._equippedLimitedTimeShipSlotItems.push({ expireTimeUtc, ids });
    }
  }

  private deleteLimitedTimeShipSlotItemId(itemId: number) {
    let index = -1;
    const limitedShipSlotCount = this._equippedLimitedTimeShipSlotItems.length;
    for (let i = 0; i < limitedShipSlotCount; i++) {
      const limitedTimeShipSlotItems = this._equippedLimitedTimeShipSlotItems[i];
      if (limitedTimeShipSlotItems.ids.has(itemId)) {
        if (limitedTimeShipSlotItems.ids.size === 1) {
          index = i;
          break;
        } else {
          limitedTimeShipSlotItems.ids.delete(itemId);
          break;
        }
      }
    }

    if (index !== -1) {
      this._equippedLimitedTimeShipSlotItems.splice(index, 1);
    }
  }

  // ----------------------------------------------------------------------------
  // sync data
  // ----------------------------------------------------------------------------

  getSyncData(): sync.All {
    const ret: sync.All = {
      slotExpansions: this._slotExpansions,
      shipSlotItems: this._shipSlotItems,
    };

    _.merge(ret, this.itemInven.getSyncData());

    return ret;
  }
}
