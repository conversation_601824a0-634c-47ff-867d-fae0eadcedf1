// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { MateNub, MateEquipmentNub } from '../../../motiflib/model/lobby';
import tuBuyMate from '../../../mysqllib/txn/tuBuyMate';
import Mate, { Mate<PERSON><PERSON> } from '../../mate';
import { LobbyService } from '../../server';
import { Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { AccumulateParam } from '../../userAchievement';
import { isCash } from '../../../cms/pointDesc';
import { BuffSync } from '../../userBuffs';
import { ClientPacketHandler } from '../index';
import mconf from '../../../motiflib/mconf';
import UserPoints from '../../userPoints';
import * as mutil from '../../../motiflib/mutil';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'buy_admiral';
const add_rsn = null;

interface RequestBody {
  admiralCmsId: number;
  bPermitExchange?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BuyAdmiral implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const reqBody: RequestBody = packet.bodyObj;

    const { admiralCmsId, bPermitExchange } = reqBody;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const admiralCms = cms.Admiral[admiralCmsId];
    if (!admiralCms) {
      throw new MError('invalid-admiral-cms-id', MErrorCode.INVALID_REQ_BODY_BUY_ADMIRAL_CMS_ID, {
        admiralCmsId,
      });
    }

    const mateCms = cms.Mate[admiralCms.mateId];
    if (user.userMates.hasMate(mateCms.id)) {
      throw new MError('already-has-same-mate', MErrorCode.ALREADY_HAS_SAME_MATE, {
        mateCmsId: mateCms.id,
      });
    }

    if (cmsEx.isFilteredByCountryCode(mateCms.localBitFlag)) {
      throw new MError('invalid-local-bit-flag', MErrorCode.BUY_ADMIRAL_INVALID_LOCAL_BIT_FLAG, {
        mateCmsId: mateCms.id,
        localBitFlag: mateCms.localBitFlag,
        countryCode: mconf.countryCode,
      });
    }

    if (admiralCms.recruitingPoint === undefined) {
      throw new MError('admiral-recruiting-point-is-undefined', MErrorCode.NOT_BUYABLE_ADMIRAL, {
        admiralCmsId,
      });
    }

    user.userContentsTerms.ensureContentsTerms(admiralCms.contentsTerms, user);

    const curTimeUtc = mutil.curTimeUtc();
    const curDate = new Date(curTimeUtc * 1000);
    if (admiralCms.saleStartDate && curDate < mutil.newDateByCmsDateStr(admiralCms.saleStartDate)) {
      throw new MError('admiral-unbuyable-date', MErrorCode.ADMIRAL_UNBUYABLE_DATE, {
        admiralCmsId,
        saleStartDate: admiralCms.saleStartDate,
        saleEndDate: admiralCms.saleEndDate,
      });
    }

    if (admiralCms.saleEndDate && curDate > mutil.newDateByCmsDateStr(admiralCms.saleEndDate)) {
      throw new MError('admiral-unbuyable-date', MErrorCode.ADMIRAL_UNBUYABLE_DATE, {
        admiralCmsId,
        saleStartDate: admiralCms.saleStartDate,
        saleEndDate: admiralCms.saleEndDate,
      });
    }

    let pointType: number = admiralCms.recruitingPoint;
    let pointValue: number = admiralCms.recruitingPointValue;
    let basicAdmiralCnt: number = 0;
    _.forOwn(user.userMates.getMates(), (mate) => {
      const admiralCms = cmsEx.getAdmiralByMateCmsId(mate.getNub().cmsId);
      if (admiralCms && admiralCms.isBasicAdmiral) {
        basicAdmiralCnt++;
      }
    });
    if (admiralCms.isBasicAdmiral && basicAdmiralCnt >= cms.Const.IsBasicAdmiralHaveCount.value) {
      pointType = cms.Const.ThirdBasicAdmiralPurchasePoint.value;
      pointValue = cms.Const.ThirdBasicAdmiralPurchaseValue.value;
    }

    const lgCashParam: any = {};
    if (pointType === cmsEx.RedGemPointCmsId) {
      lgCashParam.productId =
        mconf.binaryCode === 'GL' ? `uwogl_${admiralCms.id}` : `uwo_${admiralCms.id}`;
    } else {
      lgCashParam.itemId = rsn;
    }
    const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
      [{ cmsId: pointType, cost: pointValue }],
      bPermitExchange,
      lgCashParam,
      true
    );

    // 항해사 기본 장비 지급
    let mateEquipsToAdd: MateEquipmentNub[];
    if (mateCms.CEquipId && mateCms.CEquipId.length > 0) {
      mateEquipsToAdd = [];
      for (let i = 0; i < mateCms.CEquipId.length; i++) {
        const cequipCmsId = mateCms.CEquipId[i];
        const mateEquipmentNub = user.userMates.buildMateEquipmentNub(
          cequipCmsId,
          mateCms.id,
          1,
          0,
          curTimeUtc
        );
        mateEquipmentNub.id += i;
        mateEquipsToAdd.push(mateEquipmentNub);
      }
    }

    // 인연 연대기 클리어 시 얻는 효과들.
    const addedPassives = MateUtil.getRelationChronicleLearnablePassives(
      user.userMates,
      user.questManager,
      mateCms.id
    );

    const resp: BuffSync = {
      sync: {},
    };

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return user.userPoints
      .tryConsumeCashs(pcChanges.cashPayments, resp.sync, user, {
        user,
        rsn,
        add_rsn,
        exchangeHash,
      })
      .then(() => {
        return tuBuyMate(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          mateCms.id,
          null,
          admiralCms.startRoyalTitelId,
          pcChanges.pointChanges,
          mateEquipsToAdd,
          addedPassives
        );
      })
      .then(() => {
        _.merge<Sync, Sync>(
          resp.sync,
          user.userPoints.applyPointChanges(pcChanges.pointChanges, { user, rsn, add_rsn })
        );

        // accumulate achievement
        const accums: AccumulateParam[] = [
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_ADMIRAL,
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_ADMIRAL,
            targets: [mateCms.id],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_MATE,
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_NATION_MATE,
            targets: [mateCms.nationId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_MATE,
            targets: [mateCms.id],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
            targets: [mateCms.id],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
            targets: [mateCms.id],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
            targets: [mateCms.id],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
            targets: [mateCms.id],
            addedValue: 3,
          },
        ];

        for (let i = 1; i <= cmsEx.getMateHighestLanguageLevel(admiralCms.mateId); i++) {
          const accum: AccumulateParam = {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.MATE_LANGUAGE_LEVEL,
            targets: [i],
            addedValue: 1,
          };
          accums.push(accum);
        }

        const mateRoyalTitle = admiralCms.startRoyalTitelId;
        let targets = cmsEx.getAchievementTermsTargets(cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE, 0);

        if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
          for (const target of targets) {
            if (mateRoyalTitle < target) {
              break;
            }
            if (target !== mateRoyalTitle) {
              continue;
            }

            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE,
              targets: [target],
              addedValue: 1,
            });
          }
        }

        targets = cmsEx.getAchievementTermsTargets(
          cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
          1
        );
        if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
          for (const target of targets) {
            if (mateRoyalTitle < target) {
              break;
            }
            if (target !== mateRoyalTitle) {
              continue;
            }

            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
              targets: [mateCms.id, target],
              addedValue: 1,
            });
          }
        }

        return user.userAchievement.accumulate(accums, user, resp.sync, { user, rsn, add_rsn });
      })
      .then(() => {
        // glog
        user.glog('admiral_scout', {
          rsn,
          add_rsn,
          buy_admiral_id: mateCms.id,
          pr_data: [
            {
              type: pointType,
              amt: pointValue,
            },
          ],
          exchange_hash: exchangeHash,
        });

        const mateNub: MateNub = Mate.defaultNub(mateCms.id);
        user.userMates.addNewMate(mateNub, user.companyStat, user, { user, rsn, add_rsn }, resp);
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            mates: {
              [mateCms.id]: user.userMates.getMate(mateCms.id).getNub(),
            },
          },
        });

        if (mateEquipsToAdd) {
          for (const elem of mateEquipsToAdd) {
            _.merge<Sync, Sync>(
              resp.sync,
              user.userMates.addMateEquipment(elem, { user, rsn, add_rsn })
            );
            if (elem.equippedMateCmsId) {
              user.userMates.equipMateEquipment(
                elem.equippedMateCmsId,
                elem.id,
                user.companyStat,
                user.userPassives,
                user.userFleets,
                user.userSailing,
                user.userTriggers,
                user.userBuffs,
                resp.sync,
                { user, rsn, add_rsn }
              );
            }
          }
        }

        if (addedPassives) {
          for (const elem of addedPassives) {
            const userMate: Mate = user.userMates.getMate(elem.mateCmsId);
            userMate.addPassive(elem.passiveCmsId, 0, resp.sync);
          }
        }

        return user.sendJsonPacket(packet.seqNum, packet.type, resp);
      });
  }
}
