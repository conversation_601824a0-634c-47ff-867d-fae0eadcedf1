// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE, DisconnectReason } from '../../userConnection';
import { Resp } from '../../type/sync';
import mhttp from '../../../motiflib/mhttp';
import mconf from '../../../motiflib/mconf';
import { PLATFORM } from '../../../motiflib/model/auth/enum';
import { <PERSON><PERSON><PERSON><PERSON>etHandler } from '../index';
import { Container } from 'typedi';
import { LobbyService } from '../../server';
import mlog from '../../../motiflib/mlog';
import { MErrorCode } from '../../../motiflib/merrorCode';
import { MError } from '../../../motiflib/merror';
import { NationUtil } from '../../../motiflib/model/lobby/nationUtil';
import * as mutil from '../../../motiflib/mutil';
import { Sync } from '../../type/sync';
import { removeFromNationElection } from '../../userNation';
import { amICandidate } from '../../nationManager';

// ----------------------------------------------------------------------------
// 계정 탈퇴 처리.
// ----------------------------------------------------------------------------

interface RequestBody {
  platform: number; // dev 환경에서만 유효. 값이 있을 경우 mconf.platform 대신 사용하여 로그인 진행한다.
}

interface ResponseBody {
  sync?: Sync;
  success: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Auth_Revoke implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const platform = mconf.isDev && body.platform !== undefined ? body.platform : mconf.platform;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const { nationManager } = Container.get(LobbyService);

    if (user.userGuild.guildId) {
      throw new MError('failed-to-revoke', MErrorCode.CAN_NOT_REVOKE_WHILE_IN_GUILD, {
        userId: user.userId,
        guildId: user.userGuild.guildId,
        guildName: user.userGuild.guildApperance.guildName,
      });
    }

    const curTimeUtc = mutil.curTimeUtc();

    let outTrace: any = {};
    if (
      NationUtil.isClosingElectionSession(
        nationManager.getLastClosedElectionSessionId(user.nationCmsId),
        curTimeUtc,
        outTrace
      )
    ) {
      // 정산중에는 투표했거나 입후보한 유저는 탈퇴할 수 없다.
      // lg 요청으로 탈퇴차단에 대해서는 정산중으로 인한 오류 메시지를 보내지않고 일반 오류로 처리한다(2023/11/28)
      const nextElectionSessionId = nationManager.getNextElectionSessionId(curTimeUtc);
      if (0 < nextElectionSessionId) {
        const election = nationManager.getElection(user.nationCmsId, nextElectionSessionId);
        if (election) {
          // 선거 후보인경우 자격박탈
          if (amICandidate(election, user.userId)) {
            throw new MError(
              'it-is-nation-election-cleaning-time-and-i-am-candidate',
              MErrorCode.INVALID_REQUEST,
              {
                curTimeUtc,
                outTrace,
              }
            );
          }

          const votedCandidateUserId =
            user.userNation.getMyVotedCandidateUserId(nextElectionSessionId);

          if (votedCandidateUserId && votedCandidateUserId > 0) {
            throw new MError(
              'it-is-nation-election-cleaning-time-and-voted-user',
              MErrorCode.INVALID_REQUEST,
              {
                votedCandidateUserId,
                curTimeUtc,
                outTrace,
              }
            );
          }
        }
      }
    }

    const resp: ResponseBody = { success: false };
    return Promise.resolve()
      .then(() => {
        if (platform === PLATFORM.LINE) {
          return mhttp.lgd.revoke(user.pubId);
        }
        return true;
      })
      .then((success) => {
        resp.success = success;

        if (success) {
          // 모의전 유저를 탈퇴 신청 상태로 변경
          const { arenaRedis } = Container.get(LobbyService);
          arenaRedis['setUserRevoke'](user.userId, 1);

          mlog.info('arena.setUserRevoke', { userId: user.userId });

          // 총리관련 삭제처리
          resp.sync = {};
          return removeFromNationElection(user, user.nationCmsId, curTimeUtc, resp.sync);
        }
      })
      .then(() => {
        //user.disconnect(DisconnectReason.Revoke);
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, resp);
      });
  }
}
