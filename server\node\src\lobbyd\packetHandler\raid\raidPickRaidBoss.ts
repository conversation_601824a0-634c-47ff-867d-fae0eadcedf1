// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';
import cms from '../../../cms';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import { RaidProgressNub, RAID_STATE } from '../../../motiflib/model/lobby';
import { RaidTicketNub } from '../../userRaidTicket';
import puRaidTicketUpdateSessionIdAndCountAndBuyCount from '../../../mysqllib/sp/puRaidTicketUpdateSessionIdAndCountAndBuyCount';
import { CommonResponseBody } from '../../../admind/adminCommon';
import mlog from '../../../motiflib/mlog';

// ----------------------------------------------------------------------------
// 도전권 (보스 도전할 수 있도록 설정)
// ----------------------------------------------------------------------------
const rsn = 'raid_pick_raid_boss';
const add_rsn = null;

interface RequestBody {
  bossRaidCmsId: number;
}

interface ResponseBody extends CommonResponseBody {
  pickedBossList: number[];
}

// ----------------------------------------------------------------------------
export class Cph_Raid_PickRaidBoss implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() { }

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const { raidRedis, raidManager, userDbConnPoolMgr } = Container.get(LobbyService);
    const body: RequestBody = packet.bodyObj;
    const { bossRaidCmsId } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const rMNub = raidManager.getNub();
    if (!rMNub) {
      throw new MError('invalid-raid-manager-nub', MErrorCode.INVALID_RAID_MANAGER_NUB, {});
    }

    if (rMNub.state !== RAID_STATE.INPROGRESS) {
      throw new MError('raid-has-closed', MErrorCode.RAID_HAS_CLOSED, {
        rMNub,
        bossRaidCmsId,
      });
    }

    const raid = raidManager.getRaid(bossRaidCmsId);
    if (!raid) {
      throw new MError('invalid-boss-raid-cmsid', MErrorCode.INVALID_BOSS_RAID_CMS_ID, {
        bossRaidCmsId,
      });
    }

    const sync: Sync = {};
    let newRaidTicketNub: RaidTicketNub;
    let needRollBackRedis = true;

    let pickedRaidBosses: number[];
    return raidRedis['pickRaidBoss'](
      user.userId,
      bossRaidCmsId,
      cms.Const.BossRaidChallengeCnt.value,
      1 // add
    )
      .then((_pickedBossList: number[]) => {
        pickedRaidBosses = _pickedBossList;

        // ticket 충전.
        const raidTicketNub: RaidTicketNub = user.userRaidTicket.getRaidTicketNub(bossRaidCmsId);
        if (!raidTicketNub || raidTicketNub.sessionId !== rMNub.sessionId) {
          newRaidTicketNub = {
            sessionId: rMNub.sessionId,
            count: cms.BossRaid[bossRaidCmsId].bossRaidTicketCms.initCnt,
            buyCount: 0,
          };

          return puRaidTicketUpdateSessionIdAndCountAndBuyCount(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId,
            bossRaidCmsId,
            newRaidTicketNub.sessionId,
            newRaidTicketNub.count,
            newRaidTicketNub.buyCount
          );
        }
        return null;
      })
      .then(() => {
        needRollBackRedis = false;
        if (newRaidTicketNub) {
          _.merge<Sync, Sync>(
            sync,
            user.userRaidTicket.setRaidTicketNub(
              user,
              bossRaidCmsId,
              newRaidTicketNub,
              null,
              'free_charging'
            )
          );
        }

        user.glog('boss_raid_pick', {
          rsn,
          add_rsn,
          boss_raid_id: bossRaidCmsId,
        });

        mlog.info('[RAID] raidPickRaidBoss', {
          userId: user.userId,
          bossRaidCmsId,
          pickedRaidBosses,
        });

        return user.sendJsonPacket(packet.seqNum, packet.type, {
          sync,
          pickedRaidBosses,
        });
      })
      .catch((e) => {
        if (needRollBackRedis) {
          mlog.error('[RAID] raidPickRaidBoss. an-error-occurred. rollback-updated-redis-data. ', {
            bossRaidCmsId,
            message: e.message,
          });
          return raidRedis['pickRaidBoss'](
            user.userId,
            bossRaidCmsId,
            cms.Const.BossRaidChallengeCnt.value,
            0 //
          ).then((a) => {
            throw e;
          });
        }
        throw e;
      });
  }
}
