CREATE PROCEDURE `mp_u_user_load`(
  IN inUserId INT,
  IN inCurTImeUtc INT
)
label_body:BEGIN

  SELECT
      UNIX_TIMESTAMP(createTimeUtc) AS createTimeUtc,
      name,
      lang,
      nationCmsId,
      companyJobCmsId,
      lastRewardedAchievementPointCmsId,
      noviceSupplyCount,
      UNIX_TIMESTAMP(lastNoviceSupplyTimeUtc) AS lastNoviceSupplyTimeUtc,
      energy,
      UNIX_TIMESTAMP(lastUpdateEnergyTimeUtc) AS lastUpdateEnergyTimeUtc,
      usedExploreTicketCount,
      UNIX_TIMESTAMP(lastExploreTicketCountUpdateTimeUtc) AS lastExploreTicketCountUpdateTimeUtc,
      usedExploreQuickModeCount,
      UNIX_TIMESTAMP(lastExploreQuickModeCountUpdateTimeUtc) AS lastExploreQuickModeCountUpdateTimeUtc,
      palaceRoyalOrderCmsId,
      palaceRoyalOrderRnds,
      UNIX_TIMESTAMP(lastRoyalOrderCompletedTimeUtc) AS lastRoyalOrderCompletedTimeUtc,
      palaceRoyalTitleOrderCmsId,
      palaceRoyalTitleOrderRnds,
      contractedCollectorTownBuildingCmsId,
      guildId,
      UNIX_TIMESTAMP(lastGuildLeftTimeUtc) AS lastGuildLeftTimeUtc,
      UNIX_TIMESTAMP(lastCompanyJobUpdateTimeUtc) AS lastCompanyJobUpdateTimeUtc,
      countryIp,
      UNIX_TIMESTAMP(lastReceiveHotTimeUtc) AS lastReceiveHotTimeUtc,
      firstMateCmsId,
      UNIX_TIMESTAMP(lastUpdateNationTimeUtc) AS lastUpdateNationTimeUtc,
      isAdmiralProfileOpened,
      isFirstFleetProfileOpened,
      westShipBuildLevel,
      westShipBuildExp,
      orientShipBuildLevel,
      orientShipBuildExp,
      UNIX_TIMESTAMP(lastCashShopDailyProductsUpdateTimeUtc) AS lastCashShopDailyProductsUpdateTimeUtc,
      freeLeaderMateSwitchCount,
      UNIX_TIMESTAMP(freeLastLeaderMateSwitchTimeUtc) AS freeLastLeaderMateSwitchTimeUtc,
      accumInvestByGem,
      curCargoPresetId,
      representedMateCmsId,
      lastPaidSmuggleEnterTownCmsId,
      lastSmuggleTransactionTownCmsId,
      isFriendlyBattleRequestable,
      lastFirstFleetPresetId,
      manufacturePoint,
      UNIX_TIMESTAMP(lastUpdateManufacturePointTimeUtc) AS lastUpdateManufacturePointTimeUtc
    FROM
      u_users
    WHERE
      u_users.id = inUserId;

  SELECT
      gameState,
      lastGameState
    FROM
      u_states
    WHERE
      u_states.userId = inUserId;

  SELECT
      lastTownCmsId,
      arrivalTownCmsId,
      arrivalVillageCmsId,
      exp,
      level,
      isSlowdownEventChecked,
      lastCompletedQuestCmsIdForRequest,
      tradeResetCount,
      UNIX_TIMESTAMP(lastTradeResetTimeUtc) AS lastTradeResetTimeUtc,
      karma,
      UNIX_TIMESTAMP(lastKarmaUpdateTimeUtc) AS lastKarmaUpdateTimeUtc,
      usedFreeTurnTakebackCount,
      usedFreePhaseTakebackCount,
      UNIX_TIMESTAMP(lastFreeTakebackUpdateTimeUtc) AS lastFreeTakebackUpdateTimeUtc,
      quickModeCount,
      UNIX_TIMESTAMP(lastQuickModeCountUpdateTimeUtc) AS lastQuickModeCountUpdateTimeUtc,
      usedFreeContinuousCount,
      UNIX_TIMESTAMP(lastUsedFreeContinuousUpdateTimeUtc) AS lastUsedFreeContinuousUpdateTimeUtc,
      waypointSupplyTicketUsedCount,
      UNIX_TIMESTAMP(lastWaypointSupplyTicketUpdateTimeUtc) AS lastWaypointSupplyTicketUpdateTimeUtc,
      UNIX_TIMESTAMP(lastOpenHotSpotTimeUtc) AS lastOpenHotSpotTimeUtc,
      smuggleResetCount,
      UNIX_TIMESTAMP(lastSmuggleResetTimeUtc) AS lastSmuggleResetTimeUtc
    FROM
      u_soft_data
    WHERE
      u_soft_data.userId = inUserId;

  SELECT
      cmsId,
      loyalty,
      stateFlags,
      adventureExp,
      adventureLevel,
      tradeExp,
      tradeLevel,
      battleExp,
      battleLevel,
      adventureFame,
      tradeFame,
      battleFame,
      royalTitle,
      UNIX_TIMESTAMP(injuryExpireTimeUtc) AS injuryExpireTimeUtc,
      UNIX_TIMESTAMP(lastTalkTimeUtc) AS lastTalkTimeUtc,
      awakenLv,
      UNIX_TIMESTAMP(awakenTimeUtc) AS awakenTimeUtc,
      colorSkin,
      colorEye,
      colorHairAcc1,
      colorHairAcc2,
      colorHair,
      colorBody1,
      colorBody2,
      colorFaceAcc1,
      colorFaceAcc2,
      colorFaceAcc3,
      colorCape1,
      colorCape2,
      colorCape3,
      breastSize,
      isHideHat,
      isHideCape,
      isHideFace,
      trainingGrade,
      UNIX_TIMESTAMP(trainingExpiredTimeUtc) AS trainingExpiredTimeUtc,
      trainingPoints,
      equippedIllustCmsId,
      isFavorite,
      isTranscended,
      UNIX_TIMESTAMP(transcendExpiredTimeUtc) AS transcendExpiredTimeUtc
    FROM
      u_mates
    WHERE
      userId = inUserId;

  SELECT
      id,
      cmsId,
      dye1,
      dye2,
      dye3,
      dye4,
      dye5,
      dye6,
      equippedMateCmsId,
      isBound,
      isCostume,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      enchantLevel
    FROM
      u_mate_equipments
    WHERE
      userId = inUserId;

  SELECT
      slot,
      cmsId
    FROM
      u_sidekick_mates
    WHERE
      userId = inUserId AND cmsId > 0;    

  SELECT
      cmsId,
      value
    FROM
      u_points
    WHERE
      userId = inUserId;

  SELECT
      accumPoint,
      accumRate,
      accumCount,
      lastCmsId,
      UNIX_TIMESTAMP(lastDepositTimeUtc) AS lastDepositTimeUtc
    FROM
      u_installment_savings
    WHERE
      userId = inUserId;

  SELECT
      fleetIndex,
      battleFormationCmsId,
      dispatchReduceLifeRemain
    FROM
      u_fleets
    WHERE
      userId = inUserId;

  SELECT
      id,
      cmsId,
      assignment,
      fleetIndex,
      formationIndex,
      sailor,
      durability,
      permanentDamage,
      name,
      life,
      isLocked,
      enchantedStatType1,
      enchantedStatValue1,
      enchantedStatType2,
      enchantedStatValue2,
      enchantedStatType3,
      enchantedStatValue3,
      enchantResult,
      enchantCount,
      rndStats,
      battleQuickSkill1,
      battleQuickSkill2,
      battleQuickSkill3,
      battleQuickSkill4,
      isBound,
      guid
    FROM
      u_ships
    WHERE
      userId = inUserId;

  SELECT
      shipId,
      shipCmsId,
      rndStats,
      materialRndStats,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      townCmsId,
      substituteBuild
    FROM
      u_ship_buildings
    WHERE
      userId = inUserId;

  SELECT
      shipId,
      slotIndex,
      mateCmsId,
      isLocked,
      shipSlotItemId
    FROM
      u_ship_slots
    WHERE
      userId = inUserId;

   SELECT
      shipId,
      cmsId,
      quantity,
      pointInvested
    FROM
      u_ship_cargos
    WHERE
      userId = inUserId AND quantity > 0;


  SELECT
      cmsId,
      count,
      unboundCount
    FROM
      u_items
    WHERE
      userId = inUserId AND (count > 0 OR unboundCount > 0);

  SELECT
      cmsId,
      level,
      exp,
      sailMasteryLevel,
      sailMasteryExp,
      isPurchased
    FROM
      u_ship_blueprints
    WHERE
      userId = inUserId;

  SELECT
      shipBlueprintCmsId,
      slotIndex,
      shipSlotCmsId
    FROM
      u_ship_blueprint_slots
    WHERE
      userId = inUserId;

  SELECT
      nationCmsId,
      reputation,
      UNIX_TIMESTAMP(updateTimeUtc) AS updateTimeUtc
    FROM
      u_reputations
    WHERE
      userId = inUserId;

  SELECT
      mateCmsId,
      intimacy,
      isMet
    FROM
      u_unemployed_mates
    WHERE
      userId = inUserId;

  SELECT
      id,
      cmsId,
      state,
      UNIX_TIMESTAMP(createTimeUtc) AS createTimeUtc,
      UNIX_TIMESTAMP(readTimeUtc) AS readTimeUtc,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      bShouldSetExpirationWhenReceiveAttachment,
      title,
      titleFormatValue,
      body,
      bodyFormatValue,
      attachment
    FROM
      u_direct_mails
    WHERE
      userId = inUserId AND state <> 3 AND
      (expireTimeUtc IS NULL OR expireTimeUtc > FROM_UNIXTIME(inCurTImeUtc));

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_quest_completion_fields
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      nodeIdx,
      blockId,
      state,
      blockState,
      UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
      uflags,
      lflags,
      r0, r1, r2, r3, r4, r5, r6, r7, r8, r9,
      userLevel,
      rnd1, rnd2, rnd3,
      accum1, accum2, accum3, accum4, accum5,
      requestSlotIdx,
      requestNationCmsId,
      startTotalSailedDays,
      isPaused,
      isAdminPaused
    FROM
      u_quest_contexts
    WHERE
      userId = inUserId;

  SELECT
      mateEquipmentId
    FROM
      u_mate_equipment_last_ids
    WHERE
      userId = inUserId;

  SELECT
      shipId
    FROM
      u_ship_last_ids
    WHERE
      userId = inUserId;

  SELECT
      directMailId
    FROM
      u_direct_mail_last_ids
    WHERE
      userId = inUserId;

  DELETE FROM u_world_buffs WHERE userId = inUserId AND endTimeUtc <= FROM_UNIXTIME(inCurTimeUtc);
  SELECT
      groupNo,
      cmsId,
      targetId,
      sourceType,
      sourceId,
      stack,
      UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
      UNIX_TIMESTAMP(endTimeUtc) AS endTimeUtc
    FROM
      u_world_buffs
    WHERE
      userId = inUserId;

  SELECT
      type,
      cmsId,
      quantity,
      receivedQuantity
    FROM
      u_battle_rewards
    WHERE
      userId = inUserId;

  SELECT
      losses,
      multiPvpLoss
    FROM
      u_game_over_losses
    WHERE
      userId = inUserId;

  SELECT
    daysForLoyaltyDecrease,
    daysForTownReset,
    totalSailedDays,
    damageForLifeDecrease
  FROM
    u_sailings
  WHERE
    userId = inUserId;

  SELECT
      insuranceCmsId,
      unpaidTradeGoods,
      unpaidShip,
      unpaidSailor,
      unpaidDucat
    FROM
      u_insurances
    WHERE
      userId = inUserId;

  SELECT
      type,
      expanded
    FROM
      u_slot_expansions
    WHERE
      userId = inUserId;

  SELECT
      UNIX_TIMESTAMP(lastBlackMarketResetTimeUtc) AS lastBlackMarketResetTimeUtc,
      blackMarketResetCount
    FROM
      u_black_markets
    WHERE
      userId = inUserId;

  SELECT
      id,
      name,
      water,
      food,
      lumber,
      ammo,
      tradeGoods,
      any
    FROM
      u_cargo_load_presets
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_revealed_ocean_doodads
    WHERE
      userId = inUserId;


  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_discoveries
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      count,
      isRewarded
    FROM
      u_achievements
    WHERE
      userId = inUserId;

  SELECT
      category,
      UNIX_TIMESTAMP(lastResetTimeUtc) AS lastResetTimeUtc,
      shipBuildLevel,
      cmsId1,
      count1,
      isRewarded1,
      cmsId2,
      count2,
      isRewarded2,
      cmsId3,
      count3,
      isRewarded3,
      cmsId4,
      count4,
      isRewarded4,
      cmsId5,
      count5,
      isRewarded5,
      cmsId6,
      count6,
      isRewarded6,
      cmsId7,
      count7,
      isRewarded7,
      cmsId8,
      count8,
      isRewarded8,
      cmsId9,
      count9,
      isRewarded9,
      cmsId10,
      count10,
      isRewarded10,
      isCategoryRewarded
    FROM
      u_tasks
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      target,
      count
    FROM
      u_contents_terms_progresses
    WHERE
      userId = inUserId;

  SELECT
      eventPageCmsId,
      UNIX_TIMESTAMP(weeklyEventStartTimeUtc) AS weeklyEventStartTimeUtc
    FROM
      u_weekly_events
    WHERE
      userId = inUserId;

  SELECT
      eventPageCmsId,
      eventMissionCmsId,
      count,
      isRewarded
    FROM
      u_event_missions
    WHERE
      userId = inUserId;

  SELECT
      eventPageCmsId,
      exp,
      level,
      UNIX_TIMESTAMP(lastDailyResetTimeUtc) AS lastDailyResetTimeUtc
    FROM
      u_pass_events
    WHERE
      userId = inUserId;

  SELECT
      eventPageCmsId,
      eventMissionCmsId,
      count,
      repeatedRewardReceiveCount,
      isRewarded
    FROM
      u_pass_event_missions
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      UNIX_TIMESTAMP(expirationTimeUtc) AS expirationTimeUtc
    FROM
      u_tax_free_permits
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_revealed_world_map_tiles
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_revealed_regions
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      sailor,
      overDraftedSailor,
      isInvested,
      isTradedGoods,
      arrestState
    FROM
      u_town_states
    WHERE
      userId = inUserId;

  SELECT
      idx,
      UNIX_TIMESTAMP(purchaseTimeUtc) AS purchaseTimeUtc
    FROM
      u_request_slots
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      amount,
      UNIX_TIMESTAMP(lastBuyingTimeUtc) AS lastBuyingTimeUtc
    FROM
      u_cash_shop_restricted_products
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
      UNIX_TIMESTAMP(endTimeUtc) AS endTimeUtc
    FROM
      u_cash_shop_fixed_term_products
    WHERE
      userId = inUserId;

  SELECT
      cashShopCmsId,
      accum
    FROM
      u_cash_shop_gacha_box_guarantees
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_reported_discoveries
    WHERE
      userId = inUserId;

  SELECT
      hash,
      data,
      UNIX_TIMESTAMP(createTimeUtc) AS createTimeUtc
    FROM
      u_last_reported_discoveries
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_sound_packs
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_event_page_products
    WHERE
      userId = inUserId;

  SELECT
      g0, g1, g2, g3, g4, g5, g6, g7, g8, g9
    FROM
      u_quest_global_registers
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_question_places
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_reported_world_map_tiles
    WHERE
      userId = inUserId;

  SELECT
      discoveryBox,
      boxes+0 as boxes,
      result,
      gainItem
    FROM
      u_land_explore_results
    WHERE
      userId = inUserId;

  SELECT
      sailPatternCmsId,
      color1,
      color2,
      color3
    FROM
      u_ship_sail_patterns
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_sail_crests
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_sail_pattern_colors
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_body_1_colors
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_ship_body_2_colors
    WHERE
      userId = inUserId;

  SELECT
      sailPatternCmsId,
      sailCrestCmsId,
      bodyColor1,
      bodyColor2,
      bodyColorTint,
      camouflageCmsId
    FROM
      u_ship_customizings
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      friendship,
      recruitedSailor,
      UNIX_TIMESTAMP(lastRecruitedSailorTimeUtc) AS lastRecruitedSailorTimeUtc,
      UNIX_TIMESTAMP(lastDepartureTimeUtc) AS lastDepartureTimeUtc,
      friendshipFirstRewardReceiveBitflag,
      lastReceiveFriendshipWeeklyRewardGrade,
      UNIX_TIMESTAMP(lastReceiveFriendshipWeeklyRewardTimeUtc) AS lastReceiveFriendshipWeeklyRewardTimeUtc,
      UNIX_TIMESTAMP(lastPlunderTimeUtc) AS lastPlunderTimeUtc,
      maximumFriendship
    FROM
      u_villages
    WHERE
      userId = inUserId;

  SELECT
      mateCmsId,
      passiveCmsId,
      equipIndex
    FROM
      u_mate_passives
    WHERE
      userId = inUserId;

  SELECT
      mateCmsId,
      passiveCmsId,
      UNIX_TIMESTAMP(learnTimeUtc) AS learnTimeUtc
    FROM
      u_mate_passive_learnings
    WHERE
      userId = inUserId;

  SELECT
      questItemId
    FROM
      u_quest_item_last_ids
    WHERE
      userId = inUserId;

  SELECT
      id,
      itemCmsId,
      questCmsId,
      questRnds
    FROM
      u_quest_items
    WHERE
      userId = inUserId;

  SELECT
      eventPageCmsId,
      accum,
      consecutive,
      UNIX_TIMESTAMP(lastAttendanceTimeUtc) AS lastAttendanceTimeUtc,
      lastRewardedConsecutiveAttendanceCmsId,
      UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
      UNIX_TIMESTAMP(endTimeUtc) AS endTimeUtc
    FROM
      u_attendances
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      clearedDifficulty,
      clearedMissionField
    FROM
      u_challenges
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_mate_equipment_colors
    WHERE
      userId = inUserId;

  SELECT
      shipSlotItemId
    FROM
      u_ship_slot_item_last_ids
    WHERE
      userId = inUserId;

  SELECT
      id,
      shipSlotCmsId,
      isBound,
      isLocked,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
      enchantLevel
    FROM
      u_ship_slot_items
    WHERE
      userId = inUserId;

  SELECT
      battleEndResult
    FROM
      u_battles
    WHERE
      userId = inUserId;

  SELECT
      isAllowed,
      isNightAllowed,
      allowedPushNotificationGroupIds
    FROM
      u_game_option_push_notifications
    WHERE
      userId = inUserId;

  SELECT
      config
    FROM
      u_adjutant_delegation
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc
    FROM
      u_battle_formations
    WHERE
      userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_reported_pub_staff_discoveries
    WHERE
      userId = inUserId;

  SELECT
      townCmsId,
      intimacy,
      unlockInterest,
      dailyTalkCount,
      UNIX_TIMESTAMP(lastNominationRefreshTimeUtc) AS lastNominationRefreshTimeUtc,
      UNIX_TIMESTAMP(questBlockTimeUtc) AS questBlockTimeUtc
    FROM
      u_pub_staffs
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      dailyFreeCount,
      UNIX_TIMESTAMP(lastDailyChargeTimeUtc) AS lastDailyChargeTimeUtc,
      nonPurchaseCount,
      purchaseCount,
      isActivated
    FROM
      u_shields
    WHERE
      userId = inUserId;

  DELETE FROM u_world_skills WHERE userId = inUserId AND coolTimeUtc <= FROM_UNIXTIME(inCurTimeUtc);
  SELECT
      mateCmsId,
      cmsId,
      UNIX_TIMESTAMP(coolTimeUtc) AS coolTimeUtc
    FROM
      u_world_skills
    WHERE
      userId = inUserId;

  SELECT
      userId,
      collectionCmsId,
      slotIndex,
      stack
    FROM
      u_collections
    WHERE
      userId = inUserId;

  SELECT
      guildId,
      UNIX_TIMESTAMP(regTimeUtc) AS regTimeUtc
    FROM
      u_waiting_join_guilds
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      amount,
      UNIX_TIMESTAMP(lastBuyingTimeUtc) AS lastBuyingTimeUtc
    FROM
      u_contribution_shop_restricted_products
    WHERE
      userId = inUserId;

  SELECT
      ticketCount,
      UNIX_TIMESTAMP(lastTicketUpdateTimeUtc) AS lastTicketUpdateTimeUtc,
      ticketBoughtCount,
      UNIX_TIMESTAMP(lastTicketBuyLimitResetTimeUtc) AS lastTicketBuyLimitResetTimeUtc,
      matchListFreeRefreshableCount,
      UNIX_TIMESTAMP(lastMatchListFreeRefreshableCountResetTimeUtc) AS lastMatchListFreeRefreshableCountResetTimeUtc
    FROM
      u_arena
    WHERE
      userId = inUserId;

  SELECT
      grade,
      isRewarded
    FROM
      u_arena_grade_rewards
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      isBought
    FROM
      u_cash_shop_daily_products
    WHERE
      userId = inUserId;

  SELECT
      month,
      value,
      isExpirationNotified
    FROM
      u_mileages
    WHERE
      userId = inUserId;

  SELECT
      questCmsId1,
      questRnds1,
      questCmsId2,
      questRnds2,
      questCmsId3,
      questRnds3,
      questCmsId4,
      questRnds4,
      questCmsId5,
      questRnds5
    FROM
      u_town_union_requests
    WHERE
      userId = inUserId;

  SELECT
      cmsId,
      amount,
      UNIX_TIMESTAMP(lastBuyingTimeUtc) AS lastBuyingTimeUtc
    FROM
      u_guild_shop_restricted_products
    WHERE
      userId = inUserId;

  SELECT
      townCmsId,
      shipyardShopCmsId,
      amount,
      UNIX_TIMESTAMP(expiredTimeUtc) AS expiredTimeUtc
    FROM
      u_shipyard_shop_restricted_products
    WHERE
      userId = inUserId;
    
  SELECT
      townCmsId,
      shopCmsId,
      amount,
      UNIX_TIMESTAMP(expiredTimeUtc) AS expiredTimeUtc
    FROM
      u_shop_restricted_products
    WHERE
      userId = inUserId;

  SELECT
      eventPageCmsId,
      eventShopCmsId,
      packageCount,
      UNIX_TIMESTAMP(expiredTimeUtc) AS expiredTimeUtc
  FROM
      u_event_shop_restricted_products
    WHERE
      userId = inUserId;

  SELECT
      questCmsId1,
      questRnds1,
      questCmsId2,
      questRnds2,
      questCmsId3,
      questRnds3,
      questCmsId4,
      questRnds4,
      questCmsId5,
      questRnds5
    FROM
      u_town_union_event_requests
    WHERE
      userId = inUserId;

  SELECT
		slot,
    guildCraftCmsId,
    UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
    UNIX_TIMESTAMP(completionTimeUtc) AS completionTimeUtc
    FROM
      u_guild_craft_progress
    WHERE
      userId = inUserId;

  SELECT
    presetId,
    presetName,
    isOpen,
    isLocked,
    isFavorite,
    fleet
    FROM
      u_fleet_presets
    WHERE
      userId = inUserId;

  SELECT
		slot,
    name,
    destCmsId,
    destType,
    waypoints
    FROM
      u_sail_waypoints
    WHERE
      userId = inUserId;


  SELECT
    tradeAreaCmsId,
    tradePoint,
    accumProfitDucat,
    UNIX_TIMESTAMP(lastUpdateTimeUtc) AS lastUpdateTimeUtc
    FROM
      u_trade_area
    WHERE
      userId = inUserId;

  SELECT
    bossRaidCmsId,
    sessionId,
    count,
    buyCount
    FROM
      u_raid_tickets
    WHERE
      userId = inUserId;

  SELECT
    eventPageCmsId,
    eventGameCmsId,
    position,
    extra
    FROM
      u_event_games
    WHERE
      userId = inUserId;

  SELECT
    friendUserId,
    state,
    UNIX_TIMESTAMP(regTimeUtc) AS regTimeUtc
    FROM
      u_friends
    WHERE
      userId = inUserId;

  SELECT
    friendUserId,
    UNIX_TIMESTAMP(lastSendDate) AS lastSendDate,
    UNIX_TIMESTAMP(lastRecvDate) AS lastRecvDate,
    pickup,
    totalReceivedPts
    FROM
      u_friend_points
    WHERE
      userId = inUserId;

  SELECT
    fleetIndex,
    dispatchCmsId,
    state,
    actionCount,
    actions,
    rewards,
    lostRewards,
    lostRewardIdCount,
    endView,
    UNIX_TIMESTAMP(endTimeUtc) AS endTimeUtc,
    duration
    FROM
      u_fleet_dispatches
    WHERE
      userId = inUserId;

  SELECT
    fleetIndex,
    UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc
    FROM
      u_fleet_dispatch_slots
    WHERE
      userId = inUserId;

  SELECT
    state,
    options,
    count,
    result
    FROM
      u_battle_continuous_context
    WHERE
      userId = inUserId;

  SELECT
    freeCount,
    UNIX_TIMESTAMP(lastDailyChargeTimeUtc) AS lastDailyChargeTimeUtc
    FROM
      u_chat_translation_counts
    WHERE
      userId = inUserId;

  SELECT
    questGroupId,
    UNIX_TIMESTAMP(regenTimeUtc) AS regenTimeUtc
    FROM
      u_quest_group_regen_times
    WHERE
      userId = inUserId;

  SELECT
    cmsId,
    UNIX_TIMESTAMP(createTimeUtc) AS createTimeUtc,
    UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
    UNIX_TIMESTAMP(lastReceiveTimeUtc) AS lastReceiveTimeUtc
  FROM
    u_daily_subscriptions
  WHERE
    userId = inUserId;
    
  SELECT
		slot,
    guildSynthesisCmsId,
    UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
    UNIX_TIMESTAMP(completionTimeUtc) AS completionTimeUtc
    FROM
      u_guild_synthesis_progress
    WHERE
      userId = inUserId;   
      
  SELECT
    cmsId,
    popupCount,
    UNIX_TIMESTAMP(expireTimeUtc) AS expireTimeUtc,
    UNIX_TIMESTAMP(coolTimeUtc) AS coolTimeUtc,
    UNIX_TIMESTAMP(lastResetTimeUtc) AS lastResetTimeUtc
    FROM
      u_hot_spots
    WHERE
      userId = inUserId;
    
  SELECT
    guildBossRaidCmsId,
    guildId,
    UNIX_TIMESTAMP(raidOpenTime) AS raidOpenTime,
    count,
    buyCount
    FROM
      u_guild_raid_tickets
    WHERE
      userId = inUserId;
      
  SELECT
      eventPageCmsId,
      offset,
      idxField+0 as idxField
    FROM
      u_event_ranking_received_rewards
    WHERE
      userId = inUserId;

  SELECT
    eventPageCmsId,
    isRewarded
  FROM
    u_event_ranking_guild_reward
  WHERE
    userId = inUserId;

  SELECT
      offset,
      idxField+0 as idxField
    FROM
      u_discovery_rewards
    WHERE
      userId = inuserId;

  SELECT
      IFNULL(MAX(logId), 0) as lastLogId
    FROM
      u_sailing_diaries
    WHERE
      userId = inUserId;
      
  SELECT
    offset,
    idxField+0 as idxField
    FROM
      u_mate_illusts
    WHERE
      userId = inUserId;
  
  SELECT
    offset,
    idxField+0 as idxField
    FROM
      u_ship_camouflages
    WHERE
      userId = inUserId;

  SELECT
    fishId,
    maxSize,
    isRewarded
    FROM
      u_fishes
    WHERE
      userId = inUserId;

  SELECT
    cmsId,
    UNIX_TIMESTAMP(startTimeUtc) AS startTimeUtc,
    UNIX_TIMESTAMP(endTimeUtc) AS endTimeUtc
  FROM
    u_cash_shop_open_duration_products
  WHERE
    userId = inUserId;

  SELECT 
    cmsId,
    UNIX_TIMESTAMP(expiredTimeUtc) AS expiredTimeUtc,
    isEquipped
  FROM
    u_user_titles
  WHERE
    userId = inUserId;

  SELECT
    count,
    buyCount
  FROM
    u_sweep_tickets
  WHERE 
    userId = inUserId;

  SELECT
    groupId,
    includeGoodsPaidUseCount,
    paidUseCount,
    freeUseCount,
    UNIX_TIMESTAMP(lastUseTimeUtc) AS lastUseTimeUtc
  FROM
    u_canal
  WHERE
    userId = inUserId;
    
  SELECT
    subSlotType,
    shipSlotItemId
  FROM
    u_costume_ship_slots
  WHERE
    userId = inUserId;

  SELECT
    townCmsId,
    weeklySessionId,
    investCount
  FROM
    u_town_remote_invest
  WHERE
    userId = inUserId;

  SELECT
    cmsId,
    UNIX_TIMESTAMP(normalSaleTimeUtc) AS normalSaleTimeUtc,
    UNIX_TIMESTAMP(discountSaleTimeUtc) AS discountSaleTimeUtc
  FROM
    u_cash_shop_consecutive_products
  WHERE
    userId = inUserId;

  SELECT
    cmsId,  
    sidekickSlot
  FROM
    u_pets 
   WHERE
    userId = inUserId;

  SELECT
    cmsId,
    UNIX_TIMESTAMP(lastCompletedTimeUtc) AS lastCompletedTimeUtc
  FROM
    u_npc_quests
  WHERE
    userId = inUserId;

  SELECT
    groupId,
    turn,
    rewardedCount
  FROM
    u_ship_compose
  WHERE
    userId = inUserId;

  SELECT
    cmsId,
    count,
    UNIX_TIMESTAMP(lastBuyingTimeUtc) AS lastBuyingTimeUtc
  FROM
    u_npc_shop_restricted_products
  WHERE
    userId = inUserId;

  SELECT
    cmsId,
    amount
  FROM
    u_blind_bids
  WHERE
    userId = inUserId;
  
  SELECT
    count
  FROM
    u_blind_bid_tickets
  WHERE
    userId = inUserId;

  SELECT
    stage,
    sessionId,
    usedTurn
  FROM
    u_infinite_light_house
  WHERE
    userId = inUserId;

  SELECT 
    groupId,
    level,
    isCompleted
  FROM
    u_research_nodes
  WHERE
    userId = inUserId;
  
  SELECT
    researchCmsId,
    researchTaskCmsId,
    count
  FROM 
    u_research_tasks
  WHERE
    userId = inUserId;
    
  SELECT
    type,
    UNIX_TIMESTAMP(lastResetTimeUtc) AS lastResetTimeUtc,
    count,
    UNIX_TIMESTAMP(cooldownEndTimeUtc) AS cooldownEndTimeUtc
  FROM 
    u_reentrys
  WHERE
    userId = inUserId;

  SELECT 
    sessionId,
    score,
    isRewarded,
    winStreak
  FROM 
    u_clashs
  WHERE
    userId = inUserId;
  
  SELECT
    UNIX_TIMESTAMP(lastResetTimeUtc) AS lastResetTimeUtc,
    count,
    UNIX_TIMESTAMP(cooldownEndTimeUtc) AS cooldownEndTimeUtc
  FROM 
    u_clash_rejections
  WHERE
    userId = inUserId;

  SELECT
    lastFoughtSessionId
  FROM 
    u_clash_last_fought_sessions
  WHERE
    userId = inUserId;
END
