// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import Container from 'typedi';
import { Promise as promise } from 'bluebird';
import _ from 'lodash';

import cms from '../../../cms';
import mhttp, { LobbyGroup } from '../../../motiflib/mhttp';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import { AdminService } from '../../server';
import { CommonResponseBody } from '../../adminCommon';
import { DBConnPool, DbConnPoolManager } from '../../../mysqllib/pool';
import { getUserDbShardId } from '../../../mysqllib/mysqlUtil';
import {
  SERVER_MIGRATION_STEP,
  canMigrate,
  getUserTables,
  userDbDeleteQuery,
  userDbSelectQuery,
} from '../../serverMigrationUtil';
import { GetFullWeeksUsingLocalTime, SECONDS_PER_HOUR } from '../../../formula';
import { GsUtil } from '../../../motiflib/model/lobby/gameState';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import {
  TownMayorChangedPubMsg,
  NationElectionRemovedCandidatesPubMsg,
  NationCabinetMembersRemovedPubMsg,
  NationElectionVotesUpdatedPubMsg,
} from '../../../motiflib/model/lobby';
import { ArenaUtil } from '../../../motiflib/model/lobby/arenaUtil';
import { NationUtil } from '../../../motiflib/model/lobby/nationUtil';
import taAdminTryReserveName from '../../../mysqllib/txn/taAdminTryReserveName';
import puAdminStateLoadGameState from '../../../mysqllib/sp/puAdminStateLoadGameState';
import paAdminPubIdLoadByAccountIdAndWorldId from '../../../mysqllib/sp/paAdminPubIdLoadByAccountIdAndWorldId';
import puAdminStateUpdateGameState from '../../../mysqllib/sp/puAdminStateUpdateGameState';
import puAdminUserLoadContractedCollectorTownBuildingCmsId from '../../../mysqllib/sp/puAdminUserLoadContractedCollectorTownBuildingCmsId';
import puAdminUserLoadNationCmsId from '../../../mysqllib/sp/puAdminUserLoadNationCmsId';
import paAdminPubIdUpdateWorldId from '../../../mysqllib/sp/paAdminPubIdUpdateWorldId';
import paWorldUserUpdateWorldIdAndName from '../../../mysqllib/sp/paWorldUserUpdateWorldIdAndName';
import puAdminUserUpdateName from '../../../mysqllib/sp/puAdminUserUpdateName';
import paAdminReservedNameDelete from '../../../mysqllib/sp/paAdminReservedNameDelete';
import pwDeleteUser from '../../../mysqllib/sp/pwDeleteUser';
import paAdminWorldUserLoadName from '../../../mysqllib/sp/paAdminWorldUserLoadName';
import puFriendLoad from '../../../mysqllib/sp/puFriendLoad';
import puFriendDelete from '../../../mysqllib/sp/puFriendDelete';
import puQuestContextDelete from '../../../mysqllib/sp/puQuestContextDelete';
import paAdminAccountLoadIsOnline from '../../../mysqllib/sp/paAdminAccountLoadIsOnline';

// https://wiki.line.games/pages/viewpage.action?pageId=*********

const TEST = false;

interface RequestBody {
  fromGameServerId: string;
  toGameServerId: string;
  gameUserId: string;
  name: string;
  gnid: string;
  nid: string;
}

interface ResponseBody extends CommonResponseBody {
  failRsn?: number;
}

let townCmsIdsStr: string;

/*
   서버이전 단계별 정리 
   1. 조건체크 
     0) In-Game 온라인상태 여부 
     1) 정산시간 앞뒤1시간 및 총리 선출시간 앞/뒤 30분 내에 이전 불가
     2) 선단 생성 여부. lastGameState기준으로 GAME_STATE.CREATE_ACCOUNT_MAX 이상이면 성공
     3) 목적지 서버에 선단 유무( a_pub_ids 테이블로 pub_id가 있는지 확인)
     4) canMigrate()함수 체크 
        4-1) 상회탈퇴여부 (u_users의 guildId)
        4-2) 거래소등록상품검사 (w_auction_products에 유무)
        4-3) 거래소정산완료검사 (mp_w_admin_auction_expired_product_load에서 state <> 2)
        4-4) 유료보관함유무 (LineGames API queryInventoryPurchaseList )
        4-5) 메일 미삭제유무(u_direct_mails)
        4-6) 팬딩메일유무 (u_direct_mail_pendings)
        4-7) 라인메일유무(u_line_mails)
     5) 선거 정산중인지 체크 (loadLastClosedGlobalElectionSessionId)
     6) 중복이름 체크 후 없으면 추가 (a_reserved_names)

   2. 업데이트 
     7) _deleteFriends()호출로 userRedis에서 userIdsByName 키 제거 
     8) _migrateUserDb()호출로 user DB를 목적지 db로 마이그레이션. 테이블마다 저장된 정보를 authRedis에 기록.
     9) gameState를 IN_TOWN으로 강제 변경
    10) 저택보고 (u_users테이블의 contractedCollectorTownBuildingCmsId컬럼을 collectorRank랭크에 옮긴 후
        이전 collectorRank는 삭제
    11) 인구수 from서버 인구수 감소, to서버 인구수 증가처리 레디스(nationPopulation:diff)
        총리관련 정리 removeElectionCandidate, removeElectionVote, removeNationCabinet 
    12) 나의 누적투자를 이전 to서버로 loadInvestmentMyAccumPoints 
    13) from서버의 투자정보및 시장 삭제 deleteForServerMigration
    14) w_auto_sailings 삭제
    15) 레디스 삭제 
       fromArenaRedis['removeRevokeUser'](userId, sessionId);
       userCacheRedis['removeUser'](userId, gnid)
       fromRankingRedis['removeUserAllRankings'](userId);
       fromRaidRedis['removeUserRaid'](userId, bossRaidDesc.id));
       battleRedis['getBattleIdInProgress'] expire값을 0으로 설정하여 강제로 전투 삭제 
    16)월드번호(u_world_users, a_pub_ids) 업데이트 및 라인게임즈 worldId 변경 API 요청
    17) 유저명 변경 (u_users) 및 reserveName 테이블 삭제  
*/

/*
      테스트 가이드 
      1. 개발PC에서 멀티월드 띄우기는 아래 링크 참조하세요.
        https://wiki.line.games/pages/viewpage.action?pageId=136852875
     
      2. TEST 플래그값 활성
         canMigrate.ts 및 migrate.ts에  TEST 값을 true로 변경하세요.
         만약 "TEST === false" 일 경우  라인게임즈 api로  world변경 요청하게 되는데 
         정식으로 라인플랫폼 로그인으로 생성된 계정이 아닐 경우 실패하게 됩니다. 

      3. postman과 같은 툴을 이용하여 "/migrate" api를 요청하여 테스트를 진행합니다. 
 */
/*
    1. 전투 중 접속 종료 후 서버이전을 할 경우 이전한 서버에 접속 불가 현상 수정 (https://jira.line.games/browse/TPMUWO-1186)
    2. dev버전(라인플랫폼X) 서버이전 시 이전서버에 캐릭터 생성 불가 수정 
       -원인: pubID 생성시 worldId를 조합으로 생성하기때문에 mySql에서 duplicate primary key 발생.
       - 수정위치: /home/<USER>/work/uwo/game/server/node/src/authd/api/private/enterWorld.ts
                  77라인 resp.pubId = crypto.randomBytes(16).toString('hex'); // sessionToken + ':' + worldId;
    3. 총리기간에 이전 금지 (https://jira.line.games/browse/TPMUWO-1189)
    4. 이전 후 이전 전 서버에 선단 시장정보 유지되는 증상 수정 (https://jira.line.games/browse/TPMUWO-1183)
*/

export = async (req: RequestAs<RequestBody>, res: ResponseAs<ResponseBody>) => {
  mlog.info('[RX] /serverMigration/migrate', { body: req.body });

  const curTimeUtc = mutil.curTimeUtc();
  const {
    fromGameServerId,
    toGameServerId,
    gameUserId,
    name: newName,
    gnid,
    nid,
  }: RequestBody = req.body;

  if (!fromGameServerId || !toGameServerId || !gameUserId || !newName || !gnid || !nid) {
    throw new MError('invalid-parameter', MErrorCode.ADMIN_INVALID_PARAMETER, req.body);
  }
  const userId = parseInt(gameUserId, 10);
  const { userDbConnPoolMgrs, nationRedises, authRedis, authDbConnPool, serviceLayoutMgr } =
    Container.get(AdminService);

  const fromUserDbConnPoolMgr: DbConnPoolManager = userDbConnPoolMgrs[fromGameServerId];
  const fromUserDbShardId = getUserDbShardId(userId, fromGameServerId);
  const fromUserDbPool: DBConnPool =
    fromUserDbConnPoolMgr.getDBConnPoolByShardId(fromUserDbShardId);

  const toUserDbConnPoolMgr: DbConnPoolManager = userDbConnPoolMgrs[toGameServerId];
  const toUserDbShardId = getUserDbShardId(userId, toGameServerId);
  const toUserDbPool: DBConnPool = toUserDbConnPoolMgr.getDBConnPoolByShardId(toUserDbShardId);

  const fromTimezone = serviceLayoutMgr.getWorldCfg(fromGameServerId).timezone;
  const weeklySessionId = GetFullWeeksUsingLocalTime(
    curTimeUtc,
    cms.Define.DiscoveryRankWeeklySessionPivotDay,
    fromTimezone
  );

  if (!townCmsIdsStr) {
    townCmsIdsStr = JSON.stringify(Object.keys(cms.Town));
  }

  // 정산 앞뒤로 이전 못함
  const weeklyInvestSessionId = GetFullWeeksUsingLocalTime(
    curTimeUtc,
    cms.Define.InvestmentWeeklySessionPivotDay,
    fromTimezone
  );
  const weeklyInvestSessionIdAfter1Hour = GetFullWeeksUsingLocalTime(
    curTimeUtc + SECONDS_PER_HOUR,
    cms.Define.InvestmentWeeklySessionPivotDay,
    fromTimezone
  );
  const weeklyInvestSessionIdBefore1Hour = GetFullWeeksUsingLocalTime(
    curTimeUtc - SECONDS_PER_HOUR,
    cms.Define.InvestmentWeeklySessionPivotDay,
    fromTimezone
  );
  if (
    weeklyInvestSessionId !== weeklyInvestSessionIdAfter1Hour ||
    weeklyInvestSessionId !== weeklyInvestSessionIdBefore1Hour
  ) {
    throw new MError(
      'it-is-weekly-investment-cleaning-time',
      MErrorCode.ADMIN_SERVER_MIGRATION_CLEANING_TIME,
      {
        userId,
        weeklyInvestSessionId,
        weeklyInvestSessionIdAfter1Hour,
        weeklyInvestSessionIdBefore1Hour,
      }
    );
  }

  // 선거정산 세션여부체크
  const fromNationRedis = nationRedises[fromGameServerId];
  const rets: string = await fromNationRedis['loadLastClosedGlobalElectionSessionId']();
  if (rets) {
    let lastClosedSessionId = parseInt(rets[0], 10);
    let outTrace: any = {};
    if (
      NationUtil.isClosingElectionSession(lastClosedSessionId, curTimeUtc, outTrace, fromTimezone)
    ) {
      throw new MError(
        'it-is-nation-election-cleaning-time',
        MErrorCode.IT_IS_NATION_ELECTION_CLEANING_TIME,
        {
          curTimeUtc,
          outTrace,
        }
      );
    }
  }

  // 오프라인 여부 확인 추가해야한다. 신규로 a_accounts에서 isOnline인지 확인필요.
  const isOnline: boolean = await paAdminAccountLoadIsOnline(authDbConnPool.getPool(), gnid);
  if (isOnline) {
    throw new MError('User-is-online', MErrorCode.ADMIN_SERVER_MIGRATION_ONLINE_USER, {
      gnid,
    });
  }

  let step = 0;
  let userTables;
  let lastClosedElectionSessionId;

  return getUserTables(fromUserDbPool)
    .then((ret) => {
      userTables = ret;

      // Load step.
      return authRedis['migrateLogLoadStep'](userId).then((ret) => {
        step = parseInt(ret, 10);
        if (step !== SERVER_MIGRATION_STEP.NONE) {
          mlog.warn('Resume migration.', { userId, step });
        }
      });
    })
    .then(() => {
      // 선단 존재하는지 검사
      if (step !== SERVER_MIGRATION_STEP.NONE) {
        return null;
      }

      return puAdminStateLoadGameState(fromUserDbPool.getPool(), userId).then((ret) => {
        if (!ret) {
          throw new MError('no-company', MErrorCode.ADMIN_SERVER_MIGRATION_NO_COMPANY, {
            userId,
            fromGameServerId,
            toGameServerId,
          });
        } else {
          if (!GsUtil.isCompanyCreated(ret.lastGameState ? ret.lastGameState : ret.gameState)) {
            throw new MError('no-admiral', MErrorCode.ADMIN_SERVER_MIGRATION_NO_COMPANY, {
              userId,
              fromGameServerId,
              toGameServerId,
              ret,
            });
          }
        }
      });
    })
    .then(() => {
      // to server 에 선단 있는지 확인
      return paAdminPubIdLoadByAccountIdAndWorldId(
        authDbConnPool.getPool(),
        gnid,
        toGameServerId
      ).then((ret) => {
        if (ret) {
          throw new MError(
            'exist-company-on-to-server',
            MErrorCode.ADMIN_SERVER_MIGRATION_EXIST_COMPANY_ON_TO_SERVER,
            {
              userId,
              fromGameServerId,
              toGameServerId,
              gnid,
            }
          );
        }
      });
    })
    .then(() => {
      // 서버 이전 가능한지 확인.
      return canMigrate(userId, fromGameServerId, TEST)
        .then((failRsn) => {
          if (failRsn !== 0) {
            throw new MError('has-fail-reason', MErrorCode.ADMIN_SERVER_MIGRATION_HAS_FAIL_REASON, {
              failRsn,
              userId,
              fromGameServerId,
              toGameServerId,
            });
          }
        })
        .then(() => {
          // 선거정산 세션여부체크
          const { nationRedises } = Container.get(AdminService);
          const fromNationRedis = nationRedises[fromGameServerId];
          return fromNationRedis['loadLastClosedGlobalElectionSessionId']().then((rets) => {
            if (rets) {
              let lastClosedSessionId = parseInt(rets[0], 10);
              let outTrace: any = {};
              if (
                NationUtil.isClosingElectionSession(
                  lastClosedSessionId,
                  curTimeUtc,
                  outTrace,
                  fromTimezone
                )
              ) {
                throw new MError(
                  'it-is-nation-election-cleaning-time',
                  MErrorCode.IT_IS_NATION_ELECTION_CLEANING_TIME,
                  {
                    curTimeUtc,
                    outTrace,
                  }
                );
              }
            }
          });
        });
    })
    .then(() => {
      // 선단명 중복 확인, reserve name
      return taAdminTryReserveName(
        authDbConnPool.getPool(),
        userId,
        newName,
        toGameServerId,
        curTimeUtc,
        curTimeUtc + SECONDS_PER_HOUR
      ).catch((err) => {
        throw new MError('duplicated-name', MErrorCode.ADMIN_SERVER_MIGRATION_DUPLICATED_NAME, {
          userId,
          newName,
          fromGameServerId,
          toGameServerId,
          err: err.message,
        });
      });
    })
    .then(() => {
      // delete from user redis
      if (step >= SERVER_MIGRATION_STEP.USER_REDIS_IS_DELETED) {
        return null;
      }
      return _deleteUserRedis(userId, fromGameServerId).then(() => {
        step = SERVER_MIGRATION_STEP.USER_REDIS_IS_DELETED;
        return authRedis['migrateLogUpdateStep'](userId, step);
      });
    })
    .then(() => {
      // 친구 삭제
      // todo 삭제된 친구에게 노티
      if (step >= SERVER_MIGRATION_STEP.FRIENDS_IS_DELETED) {
        return null;
      }
      return _deleteFriends(userId, fromGameServerId, fromUserDbPool, fromUserDbConnPoolMgr).then(
        () => {
          step = SERVER_MIGRATION_STEP.FRIENDS_IS_DELETED;
          return authRedis['migrateLogUpdateStep'](userId, step);
        }
      );
    })
    .then(() => {
      // user db 데이터 이동.
      if (step >= SERVER_MIGRATION_STEP.USER_RDB_IS_MIGRATED) {
        return null;
      }
      return _migrateUserDb(userTables, fromUserDbPool, toUserDbPool, userId).then(() => {
        step = SERVER_MIGRATION_STEP.USER_RDB_IS_MIGRATED;
        return authRedis['migrateLogUpdateStep'](userId, step);
      });
    })
    .then(() => {
      // gameState 를 in town 으로 변경.
      return puAdminStateUpdateGameState(
        toUserDbPool.getPool(),
        userId,
        GAME_STATE.IN_TOWN,
        GAME_STATE.NONE,
        false
      );
    })
    .then(() => {
      // 저택 보고 데이터 이동
      if (step >= SERVER_MIGRATION_STEP.COLLECTOR_REDIS_IS_MIGRATED) {
        return null;
      }
      return _migrateCollectorRedis(
        userId,
        fromGameServerId,
        toGameServerId,
        toUserDbPool,
        weeklySessionId
      ).then(() => {
        step = SERVER_MIGRATION_STEP.COLLECTOR_REDIS_IS_MIGRATED;
        return authRedis['migrateLogUpdateStep'](userId, step);
      });
    })
    .then(() => {
      // nationRedis 인구 가감
      if (step >= SERVER_MIGRATION_STEP.NATION_REDIS_IS_MIGRATED) {
        return null;
      }
      return _migrateNationRedis(
        userId,
        fromGameServerId,
        toGameServerId,
        toUserDbPool,
        lastClosedElectionSessionId,
        curTimeUtc,
        fromTimezone
      ).then(() => {
        step = SERVER_MIGRATION_STEP.NATION_REDIS_IS_MIGRATED;
        return authRedis['migrateLogUpdateStep'](userId, step);
      });
    })
    .then(() => {
      // townRedis townUserInvestmentAccumPoints (타운 누적 투자 금액) 이동
      if (step >= SERVER_MIGRATION_STEP.TOWN_REDIS_ACCUM_INVEST_IS_MIGRATED) {
        return null;
      }
      return _migrateTownRedis(userId, fromGameServerId, toGameServerId, weeklySessionId).then(
        () => {
          step = SERVER_MIGRATION_STEP.TOWN_REDIS_ACCUM_INVEST_IS_MIGRATED;
          return authRedis['migrateLogUpdateStep'](userId, step);
        }
      );
    })
    .then(() => {
      // townRedis townUserWeeklyInvestmentScore, townUserWeeklyInvestment, townMayor 제거
      if (step >= SERVER_MIGRATION_STEP.TOWN_REDIS_IS_DELETED) {
        return null;
      }

      const { townRedises } = Container.get(AdminService);
      const fromTownRedis = townRedises[fromGameServerId];
      return fromTownRedis['deleteForServerMigration'](userId, townCmsIdsStr, weeklySessionId).then(
        (ret) => {
          const mayorResignedTownCmsIds = JSON.parse(ret);
          if (_.isArray(mayorResignedTownCmsIds) && mayorResignedTownCmsIds.length > 0) {
            const { worldPubsubRedises } = Container.get(AdminService);
            const worldPubsub = worldPubsubRedises[fromGameServerId];
            for (const townCmsId of mayorResignedTownCmsIds) {
              const msg: TownMayorChangedPubMsg = {
                townCmsId,
                mayorUserId: null,
                mayorUserName: null,
                mayorNationCmsId: null,
                updateTimeUtc: curTimeUtc,
              };
              worldPubsub.publish('town_mayor_changed', JSON.stringify(msg)).catch((err) => {
                mlog.alert('migrate town_mayor_changed publish is failed.', {
                  err: err.message,
                  townCmsId,
                });
              });
            }
          }

          step = SERVER_MIGRATION_STEP.TOWN_REDIS_IS_DELETED;
          return authRedis['migrateLogUpdateStep'](userId, step);
        }
      );
    })
    .then(() => {
      // delete from world db
      const { worldDbConnPools } = Container.get(AdminService);
      const worldDbConnPool = worldDbConnPools[fromGameServerId];
      return pwDeleteUser(worldDbConnPool.getPool(), userId);
    })
    .then(() => {
      // delete from arena redis
      const sessionId = ArenaUtil.getCurrentSessionId(curTimeUtc);
      const { arenaRedises } = Container.get(AdminService);
      const fromArenaRedis = arenaRedises[fromGameServerId];
      return fromArenaRedis['removeRevokeUser'](userId, sessionId);
    })
    .then(() => {
      // delete from user cache redis
      const { userCacheRedis } = Container.get(AdminService);
      return userCacheRedis['removeUser'](userId, gnid);
    })
    .then(() => {
      // delete from ranking redis
      const { rankingRedises } = Container.get(AdminService);
      const fromRankingRedis = rankingRedises[fromGameServerId];
      return fromRankingRedis['removeUserAllRankings'](userId);
    })
    .then(() => {
      // delete from raid redis
      const { raidRedises } = Container.get(AdminService);
      const fromRaidRedis = raidRedises[fromGameServerId];
      const promises = [];
      _.forOwn(cms.BossRaid, (bossRaidDesc) => {
        promises.push(fromRaidRedis['removeUserRaid'](userId, bossRaidDesc.id));
      });
      return Promise.all(promises);
    })
    .then(() => {
      const { battleLogRedis } = Container.get(AdminService);
      const toUserDbConnPoolMgr: DbConnPoolManager = userDbConnPoolMgrs[toGameServerId];
      const toUserDbShardId = getUserDbShardId(userId, toGameServerId);

      // 즉시 전투 삭제
      return battleLogRedis['getBattleIdInProgress'](userId, curTimeUtc, 0, null).then((ret) => {
        if (ret) {
          const retObj = JSON.parse(ret);

          if (retObj.expired) {
            const droppedQuestCmsId = retObj.expired.questCmsId;

            if (droppedQuestCmsId) {
              return puQuestContextDelete(
                toUserDbConnPoolMgr.getPoolByShardId(toUserDbShardId),
                userId,
                droppedQuestCmsId
              );
            }
          }
        }

        return null;
      });
    })
    .then(() => {
      const { globalBattleLogRedis } = Container.get(AdminService);
      const toUserDbConnPoolMgr: DbConnPoolManager = userDbConnPoolMgrs[toGameServerId];
      const toUserDbShardId = getUserDbShardId(userId, toGameServerId);

      // 즉시 전투 삭제
      return globalBattleLogRedis['getBattleIdInProgress'](userId, curTimeUtc, 0, null).then(
        (ret) => {
          if (ret) {
            const retObj = JSON.parse(ret);

            if (retObj.expired) {
              const droppedQuestCmsId = retObj.expired.questCmsId;

              if (droppedQuestCmsId) {
                return puQuestContextDelete(
                  toUserDbConnPoolMgr.getPoolByShardId(toUserDbShardId),
                  userId,
                  droppedQuestCmsId
                );
              }
            }
          }

          return null;
        }
      );
    })

    .then(() => {
      if (step >= SERVER_MIGRATION_STEP.AUTH_RDB_IS_UPDATED) {
        return null;
      }
      return _updateAuthDb(userId, gnid, nid, toGameServerId, newName).then(() => {
        step = SERVER_MIGRATION_STEP.AUTH_RDB_IS_UPDATED;
        return authRedis['migrateLogUpdateStep'](userId, step);
      });
    })
    .then(() => {
      // name 변경 후 reserve 에서 제거
      return puAdminUserUpdateName(toUserDbPool.getPool(), userId, newName).then(() => {
        return paAdminReservedNameDelete(authDbConnPool.getPool(), toGameServerId, newName);
      });
    })
    .then(() => {
      // redis 에서 진행 상황 제거
      return authRedis['migrateLogDeleteStep'](userId, step);
    })
    .then(() => {
      const resp: ResponseBody = {
        isSuccess: true,
        msg: 'request success',
      };

      // todo glog

      mlog.info('[TX] /serverMigration/migrate', { body: resp });
      res.json(resp);
    })
    .catch((err) => {
      mlog.alert('migrate is failed.', {
        err: err.message,
        stack: err.stack,
        userId,
        newName,
        step,
        fromGameServerId,
        toGameServerId,
      });
      if (err.mcode === MErrorCode.ADMIN_SERVER_MIGRATION_HAS_FAIL_REASON) {
        mlog.error('migrate is failed.', {
          mcode: err.mcode,
          failRsn: err.extra?.failRsn,
          msg: err.message,
          userId,
        });
        return res.status(400).json({
          isSuccess: false,
          msg: err.message,
          errorCd: MErrorCode[err.mcode],
          failRsn: err.extra?.failRsn,
        });
      } else {
        throw err;
      }
    });
};

function _updateAuthDb(
  userId: number,
  gnid: string,
  nid: string,
  toGameServerId: string,
  newName: string
): Promise<any> {
  const { authDbConnPool } = Container.get(AdminService);
  return paAdminPubIdUpdateWorldId(authDbConnPool.getPool(), gnid, nid, toGameServerId)
    .then(() => {
      return paWorldUserUpdateWorldIdAndName(
        authDbConnPool.getPool(),
        userId,
        toGameServerId,
        newName
      );
    })
    .then(() => {
      if (!TEST) {
        // 라인 디비에 nid 에 연결된 worldId 를 변경 요청.
        return mhttp.lgd.changeNidForServerMigration(nid, toGameServerId);
      }
    });
}

function _migrateUserDb(
  userTables: string[],
  fromUserDbPool: DBConnPool,
  toUserDbPool: DBConnPool,
  userId: number
) {
  const { authRedis } = Container.get(AdminService);
  return authRedis['migrateLogLoadMigratedUserTable'](userId).then((ret) => {
    const migratedUserTables = {};
    if (ret) {
      const migratedUserTableArr = JSON.parse(ret);
      for (const elem of migratedUserTableArr) {
        migratedUserTables[elem] = true;
      }
    }

    return promise.each(userTables, (tableName: string) => {
      return _migrateUserTable(
        fromUserDbPool,
        toUserDbPool,
        tableName,
        userId,
        migratedUserTables[tableName] !== undefined
      );
    });
  });
}

function _migrateUserTable(
  fromDbPool: DBConnPool,
  toUserDbPool: DBConnPool,
  tableName: string,
  userId: number,
  bInserted: boolean
) {
  const { authRedis } = Container.get(AdminService);

  if (TEST) {
    // const testUserTables = {
    //   // ['u_discoveries']: true,
    // };
    // if (!testUserTables[tableName]) {
    //   return Promise.resolve();
    // }
  }

  return Promise.resolve()
    .then(() => {
      if (bInserted) {
        return null;
      }
      return fromDbPool.queryByStmt(userDbSelectQuery(tableName, userId));
    })
    .then((qr) => {
      if (bInserted || qr.length === 0) {
        return null;
      }

      const promises = [];
      for (const row of qr) {
        const keys: string[] = [];
        const values: string[] = [];
        _.forOwn(row, (v, k) => {
          keys.push(k);
          let strV;
          if (v === null) {
            strV = 'NULL';
          } else if (typeof v === 'number') {
            strV = v.toString();
          } else if (Object.prototype.toString.call(v) === '[object Date]') {
            strV = `FROM_UNIXTIME(${mutil.dateToUtc(v)})`;
          } else if (Object.prototype.toString.call(v) === '[object Uint8Array]') {
            const length = v.length;
            const buf = Buffer.from(v);
            strV = buf.readUIntBE(0, length).toString();
          } else if (typeof v === 'string') {
            strV = `'${v}'`;
          } else {
            mlog.error('invalid type', {
              tableName,
              k,
              type: typeof v,
              v,
              type2: Object.prototype.toString.call(v),
            });
            strV = v;
          }
          values.push(strV);
        });

        const query = `INSERT INTO ${tableName} (${keys.toString()}) VALUES (${values.toString()});`;
        if (TEST) {
          mlog.info('query', { query });
        }

        promises.push(toUserDbPool.queryByStmt(query));
      }
      return Promise.all(promises);
    })
    .then(() => {
      if (bInserted) {
        return null;
      }
      return authRedis['migrateLogAddUserTable'](userId, tableName);
    })
    .then(() => {
      return fromDbPool.queryByStmt(userDbDeleteQuery(tableName, userId));
    });
}

function _deleteFriends(
  userId: number,
  fromGameServerId: string,
  fromUserDbPool: DBConnPool,
  fromUserDbConnPoolMgr: DbConnPoolManager
) {
  return puFriendLoad(fromUserDbPool.getPool(), userId).then((friends) => {
    const delFriendPromises = [];
    if (friends) {
      // 상대방의 친구목록에서 탈퇴유저를 제거.
      friends.forEach((elem) => {
        const friendUserDbShardId = getUserDbShardId(elem.friendUserId, fromGameServerId);
        delFriendPromises.push(
          puFriendDelete(
            fromUserDbConnPoolMgr.getPoolByShardId(friendUserDbShardId),
            elem.friendUserId,
            userId
          )
        );
      });
    }

    return Promise.all(delFriendPromises);
  });
}

function _deleteUserRedis(userId: number, fromGameServerId: string) {
  const { authDbConnPool } = Container.get(AdminService);
  return paAdminWorldUserLoadName(authDbConnPool.getPool(), userId).then((oldName) => {
    const { userRedises } = Container.get(AdminService);
    const fromUserRedis = userRedises[fromGameServerId];
    return fromUserRedis['deleteUsers'](JSON.stringify([oldName]));
  });
}

function _migrateCollectorRedis(
  userId: number,
  fromGameServerId: string,
  toGameServerId: string,
  toUserDbPool: DBConnPool,
  sessionId: number
) {
  const { collectorRedises } = Container.get(AdminService);
  const fromCollectorRedis = collectorRedises[fromGameServerId];
  const toCollectorRedis = collectorRedises[toGameServerId];
  let collectorTownBuildingCmsId;
  let score = 0;

  return puAdminUserLoadContractedCollectorTownBuildingCmsId(toUserDbPool.getPool(), userId)
    .then((ret) => {
      if (ret) {
        collectorTownBuildingCmsId = ret;
      }

      if (collectorTownBuildingCmsId) {
        return fromCollectorRedis['getDiscoveryScore'](
          userId,
          collectorTownBuildingCmsId,
          sessionId
        );
      }
      return null;
    })
    .then((scoreStr) => {
      const rawScore = parseInt(scoreStr, 10);
      if (!mutil.isNotANumber(rawScore)) {
        score = rawScore;
      }
      if (collectorTownBuildingCmsId && score) {
        return toCollectorRedis['setDiscoveryScore'](
          userId,
          collectorTownBuildingCmsId,
          sessionId,
          rawScore
        );
      }
      return null;
    })
    .then(() => {
      if (collectorTownBuildingCmsId && score) {
        return fromCollectorRedis['deleteCollectorScore'](
          userId,
          collectorTownBuildingCmsId,
          sessionId
        );
      }
      return null;
    });
}

function _migrateNationRedis(
  userId: number,
  fromGameServerId: string,
  toGameServerId: string,
  toUserDbPool: DBConnPool,
  lastClosedElectionSessionId: number,
  curTimeUtc: number,
  fromTimezone: number
) {
  const { nationRedises, worldPubsubRedises } = Container.get(AdminService);
  const fromNationRedis = nationRedises[fromGameServerId];
  const toNationRedis = nationRedises[toGameServerId];

  const fromPub = worldPubsubRedises[fromGameServerId];
  let nationCmsId;

  const curWeekSessionId = GetFullWeeksUsingLocalTime(
    curTimeUtc,
    cms.Define.NationElectionWeeklySessionPivotDay,
    fromTimezone
  );

  const nextElectionSessionId = NationUtil.getNextElectionSessionId(
    lastClosedElectionSessionId,
    curWeekSessionId,
    cms.Define.NationElectionSessionInterval
  );

  return puAdminUserLoadNationCmsId(toUserDbPool.getPool(), userId).then((ret) => {
    if (!ret) {
      throw new MError('no-nation', MErrorCode.ADMIN_SERVER_MIGRATION_NO_COMPANY, {
        userId,
        fromGameServerId,
        toGameServerId,
        ret,
      });
    }
    nationCmsId = ret;

    return fromNationRedis['changeNationPopulation'](nationCmsId, 0)
      .then(() => {
        return toNationRedis['changeNationPopulation'](0, nationCmsId);
      })
      .then(() => {
        // 유저 정보로드해서 확인하지않고 그냥 있을수있는정보들 삭제처리를 시도한다
        if (nextElectionSessionId) {
          return fromNationRedis['removeElectionCandidate'](
            userId,
            nationCmsId,
            nextElectionSessionId
          )
            .then((isExist) => {
              if (isExist) {
                const msgObj: NationElectionRemovedCandidatesPubMsg = {
                  nationCmsId,
                  electionSessionId: nextElectionSessionId,
                  candidateUserIds: [userId],
                  updateTimeUtc: curTimeUtc,
                };

                fromPub.publish('nation_election_candidates_removed', JSON.stringify(msgObj));
              }

              return fromNationRedis['removeElectionVotes'](
                nationCmsId,
                userId,
                nextElectionSessionId
              );
            })
            .then((retCandidateUserId) => {
              if (retCandidateUserId) {
                const candidateUserId = parseInt(retCandidateUserId, 10);
                const msgObj: NationElectionVotesUpdatedPubMsg = {
                  nationCmsId,
                  electionSessionId: nextElectionSessionId,
                  candidateUserId,
                  updateTimeUtc: curTimeUtc,
                };

                fromPub.publish('nation_election_votes_updated', JSON.stringify(msgObj));
              }
            });
        }
      })
      .then(() => {
        const curCabinetSessionId = NationUtil.getCabinetSessionId(
          lastClosedElectionSessionId,
          curWeekSessionId,
          cms.Define.NationElectionSessionInterval
        );
        return fromNationRedis['removeNationCabinet'](
          userId,
          nationCmsId,
          curCabinetSessionId
        ).then((isExist) => {
          if (isExist) {
            const msgObj: NationCabinetMembersRemovedPubMsg = {
              nationCmsId,
              sessionId: curCabinetSessionId,
              memberUserIds: [userId],
              updateTimeUtc: curTimeUtc,
            };

            fromPub.publish('nation_cabinet_member_removed', JSON.stringify(msgObj));
          }
        });
      });
  });
}

function _migrateTownRedis(
  userId: number,
  fromGameServerId: string,
  toGameServerId: string,
  sessionId: number
) {
  const { townRedises } = Container.get(AdminService);
  const fromTownRedis = townRedises[fromGameServerId];
  const toTownRedis = townRedises[toGameServerId];

  return fromTownRedis['loadInvestmentMyAccumPoints'](userId, townCmsIdsStr, sessionId).then(
    (ret) => {
      return toTownRedis['setInvestmentMyAccumPoints'](userId, ret);
    }
  );
}
