// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import os from 'os';
import JSON5 from 'json5';
import bodyParser from 'body-parser';
import express from 'express';
import http from 'http';
import _ from 'lodash';
import morgan from 'morgan';
import path from 'path';
import { Container, Service } from 'typedi';
import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mconf from '../motiflib/mconf';
import { resolveLocalDotJson5 } from '../motiflib/resolveLocalDotJson5';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import { MError, MErrorCode } from '../motiflib/merror';
import stoppable from 'stoppable';
import { MRedisConnPool } from '../redislib/connPool';
import Pubsub from '../redislib/pubsub';
import { ConfigUtil } from './configUtil';
import * as Sentry from '@sentry/node';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------

// Main configd app.
const configApp = express();

configApp.disable('x-powered-by');
configApp.disable('etag');
configApp.disable('content-type');
configApp.use(
  morgan((tokens, req, res) => {
    mlog.info('configd-req', {
      url: tokens['url'](req, res),
      status: tokens['status'](req, res),
      'response-time': tokens['response-time'](req, res),
      mcode: tokens['mcode'](req, res),
    });
    return null;
  })
);
configApp.use(bodyParser.json());
configApp.use(bodyParser.urlencoded({ extended: true }));

const configServer = stoppable(http.createServer(configApp));

let stopping = false;

@Service()
export class ConfigService {
  configData: object; // Main config object.
  configRedis: MRedisConnPool;
  configPubsub: Pubsub;

  async start() {
    // Init redis pool.
    const configRedis = ConfigUtil.getFromConfig(this.configData, 'configd/common/configRedis');
    this.configRedis = Container.of('config-redis').get(MRedisConnPool);
    await this.configRedis.init('config-redis', configRedis);

    const configPubsubRedis = ConfigUtil.getFromConfig(
      this.configData,
      'sharedConfig/configPubsubRedis'
    );
    this.configPubsub = Container.of('pubsub-config').get(Pubsub);
    this.configPubsub.init(configPubsubRedis);

    await this.loadInstances();
    await this.loadMaxUsersPerWorld();

    // Init api server.
    const bindAddress = '0.0.0.0';
    const tokens = mconf.http.configd.url.split(':');
    const port = parseInt(tokens[tokens.length - 1], 10);

    mutil.registerHealthCheck(configApp);
    mutil.registerGarbageCollector(configApp);
    await dirAsApi.register(configApp, path.join(__dirname, 'api'));
    configApp.use(expressError.middleware);

    return configServer.listen(port, bindAddress, () => {
      mlog.info('start listening ...', { bindAddress, port });
    });
  }

  async reload(): Promise<object> {
    const ServiceLayoutFolder = 'service_layout';
    const layoutFileName = mconf.layout || resolveLocalDotJson5();
    const layoutFilePath = path.join(ServiceLayoutFolder, layoutFileName);

    mlog.info('Reloading layout...', {
      layoutFileName,
    });

    return mutil
      .readFile(layoutFilePath, 'utf8')
      .then((layoutData) => {
        // Replace '__HOSTNAME__'.
        const replaceRe = new RegExp('__HOSTNAME__', 'g');
        const newData = layoutData.replace(replaceRe, os.hostname());
        this.configData = JSON5.parse(newData.toString());

        mlog.info('Layout reloaded successfully from json file.', { dump: this.configData });

        return this.configData;
      })
      .catch((err) => {
        mlog.error('Failed to reload layout!', {
          layoutFileName,
          errMsg: err.message,
        });

        return null;
      });
  }

  async loadInstances() {
    // load instances from config_redis
    return this.configRedis['getAllInstances']()
      .then((ret) => {
        // version 설정
        const layoutVersion: number = ret[0];
        this.configData['sharedConfig']['layoutVersion'] = layoutVersion;

        mlog.info('loadInstances getAllInstances success', { layoutVersion });

        if (!_.isEmpty(ret[1])) {
          const instances = JSON.parse(ret[1]);
          // traverse instances
          const appIds = Object.keys(instances);
          appIds.forEach((appId) => {
            const instance = instances[appId];
            // get server type from instance
            const tokens = appId.split('.');
            const serverType = tokens[0];

            // inject to each server type's instances in layout object
            const server = this.configData[serverType];
            if (!server['instances']) {
              server['instances'] = {};
            }
            server['instances'][appId] = JSON.parse(instance.body);

            mlog.info('loadInstances added', {
              appId,
              instance,
            });
          });
        }
      })
      .catch((err) => {
        mlog.error('loadInstances failed', {
          msg: err.message,
        });

        throw new MError('loadInstances failed!', MErrorCode.INTERNAL_ERROR);
      });
  }

  async loadMaxUsersPerWorld() {
    // load instances from config_redis
    return this.configRedis['getMaxUsersPerWorld']()
      .then((ret) => {
        if (ret) {
          let value;
          // redis에 정보가 있는경우 레이아웃값을 업데이트
          if (typeof ret == 'string') {
            value = parseInt(ret, 10);
          } else {
            value = ret;
          }
          this.configData['sharedConfig']['maxUsersPerWorld'] = value;
          mlog.info(`got maxUsersPerWorld from redis`, { value });
        } else {
          // redis에 정보가 없는 경우 레이아웃의 값으로 초기화한다
          const value: number = this.configData['sharedConfig']['maxUsersPerWorld'];
          return this.configRedis['setMaxUsersPerWorld'](value).then(() => {
            mlog.info(`setted initial maxUsersPerWorld`, { value });
          });
        }
      })
      .catch((err) => {
        mlog.error('loadMaxUsersPerWorld failed', {
          error: err.message,
          extra: err.extra,
        });

        throw new MError(err.message, MErrorCode.INTERNAL_ERROR, null, err.stack);
      });
  }

  async destroy() {
    await this.configPubsub.quit();
    await this.configRedis.destroy();
  }
}

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

async function closeServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    configServer.stop((err) => {
      if (err) return reject(err);
      resolve();
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');
    await closeServer();

    const app = Container.get(ConfigService);
    await app.destroy();

    stopTogglet();

    mlog.info('server stopped');
    process.exitCode = 0;
  } catch (error) {
    mlog.exception('graceful shutdown failed', error);
    process.exit(1);
  }
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export async function start() {
  try {
    const app = Container.get(ConfigService);
    const bOk: any = await app.reload();
    if (!bOk) {
      throw new MError('Failed to load initial service layout!', MErrorCode.INTERNAL_ERROR);
    }

    if (bOk.configd.common.sentry.dsn) {
      mutil.initSentry(bOk.configd.common.sentry.dsn);
    }

    await startTogglet();

    await app.start();
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await stopServer();
}
