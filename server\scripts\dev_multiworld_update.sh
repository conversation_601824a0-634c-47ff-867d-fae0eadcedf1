#!/bin/bash
set -e

# Transpile typescript
echo '========================================================================='
echo '= Transpiling typescript ...'
echo '========================================================================='

pushd ../node
yarn
yarn build
popd

# Update server layout.
echo '========================================================================='
echo '= Updating server layout ...'
echo '========================================================================='

pushd ../node
cp service_layout/multiworld.json5 service_layout/local.json5
popd

# Migrate database
echo '========================================================================='
echo '= Migrating database ...'
echo '========================================================================='

pushd ../node
yarn mig
yarn mig-world-1
yarn mig-world-2
popd
