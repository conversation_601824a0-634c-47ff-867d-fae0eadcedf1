#!/bin/bash

CWD="$(dirname "$0")"
QUERY_RESULT=""

if [ ! -f $CWD/_config.sh ]; then
	echo "Please create a custom '_config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/_query.sh ]; then
	echo "Please create a custom '_query.sh' file at '$CWD' directory."
	exit 1
fi

if [ -z "$1" ]
then
  echo "No argument backup database name"
  exit 1
fi

source $CWD/_config.sh
source $CWD/_query.sh

SECONDS=0

BACKUP_DB_NAME=$1
main() 
{
  echo "===== SELECT integration_garbage_users"
  q="
    SELECT userId FROM integration_garbage_users;
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
  deleteUserIds=$(echo ${QUERY_RESULT} | tr ' ', ',')


  echo "===== DELETE u_*  userId"
  q="SHOW TABLES;"
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
  tableList=(${QUERY_RESULT})
  for tableName in ${tableList[@]}
  do
    if [[ "$tableName" == "u_"* && "$tableName" != "u_users" ]]
    then
      q="
        DELETE FROM ${tableName} WHERE userId IN (${deleteUserIds});
      "
      queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
    fi
  done


  echo "===== DELETE u_users  id"
  q="
    DELETE FROM u_users WHERE id IN (${deleteUserIds});
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"
}

main "$@"; 

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
exit
