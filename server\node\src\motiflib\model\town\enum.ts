export * from './enum';

export enum TRADE_EVENT_TYPE {
  SOAR = 1, // 폭등
  SLUMP = 2, // 폭락
  FAD = 3, // 유행
  GLUT = 4, // 과잉
  UNPOPULAR = 5, // 폭증

  // 대유행
  FESTIVAL = 6, // 축제
  INFECTION = 7, // 전염병
  CLIMATE_DISASTER = 8, //기상 이변
  WAR = 9, // 전쟁
  SPONSORSHIP = 10, // 후원
  DEVELOPMENT_BOOM_ALCOHOL = 11, // 개발
  DEVELOPMENT_BOOM_FOOD = 12, // 개발
  ECONOMIC_BOOM = 13, // 호황
  LUXURY = 14, // 사치

  GOODS_QUICK_BUY = 15, // 긴급 구매 (교역품)
  GOODS_QUICK_SELL = 16, // 긴급 처분 (교역품)
  CATEGORY_STIMULUS = 17, // 경기 부양 (카테고리)
  CATEGORY_SALE = 18, // 재고 처리 (카테고리)

  // 마을
  PEACE = 19, // 평온
  STORM = 20, // 폭풍
  DROUGHT = 21, // 가뭄
  INSECT = 22, // 해충
  DEPRESSION = 23, // 불황
  ENVOY = 24, // 사절
  REBELLION = 25, // 내란
  NOBILITY = 26, // 귀족
}

export function isCategoryEvent(category: TRADE_EVENT_TYPE): boolean {
  if (
    category === TRADE_EVENT_TYPE.SOAR ||
    category === TRADE_EVENT_TYPE.SLUMP ||
    category === TRADE_EVENT_TYPE.UNPOPULAR ||
    category === TRADE_EVENT_TYPE.GOODS_QUICK_BUY ||
    category === TRADE_EVENT_TYPE.GOODS_QUICK_SELL
  ) {
    return false;
  }
  return true;
}

export function isCrazeEvnet(category: TRADE_EVENT_TYPE): boolean {
  if (
    category === TRADE_EVENT_TYPE.FESTIVAL ||
    category === TRADE_EVENT_TYPE.INFECTION ||
    category === TRADE_EVENT_TYPE.CLIMATE_DISASTER ||
    category === TRADE_EVENT_TYPE.WAR ||
    category === TRADE_EVENT_TYPE.SPONSORSHIP ||
    category === TRADE_EVENT_TYPE.DEVELOPMENT_BOOM_ALCOHOL ||
    category === TRADE_EVENT_TYPE.DEVELOPMENT_BOOM_FOOD ||
    category === TRADE_EVENT_TYPE.ECONOMIC_BOOM ||
    category === TRADE_EVENT_TYPE.LUXURY
  ) {
    return true;
  }
  return false;
}

export function isVillageEvent(category: TRADE_EVENT_TYPE): boolean {
  switch (category) {
    case TRADE_EVENT_TYPE.PEACE:
    case TRADE_EVENT_TYPE.STORM:
    case TRADE_EVENT_TYPE.DROUGHT:
    case TRADE_EVENT_TYPE.INSECT:
    case TRADE_EVENT_TYPE.DEPRESSION:
    case TRADE_EVENT_TYPE.ENVOY:
    case TRADE_EVENT_TYPE.REBELLION:
    case TRADE_EVENT_TYPE.NOBILITY:
      return true;
    default:
      return false;
  }
}

export function isMayorTradeEvent(category: TRADE_EVENT_TYPE): boolean {
  if (
    category === TRADE_EVENT_TYPE.GOODS_QUICK_BUY ||
    category === TRADE_EVENT_TYPE.GOODS_QUICK_SELL ||
    category === TRADE_EVENT_TYPE.CATEGORY_STIMULUS ||
    category === TRADE_EVENT_TYPE.CATEGORY_SALE
  ) {
    return true;
  } else {
    return false;
  }
}
