// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import { RequestAs, ResponseAs } from '../../../motiflib/expressEx';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { DBConnPool } from '../../../mysqllib/pool';
import taLogout from '../../../mysqllib/txn/taLogout';
import mconf, { World } from '../../../motiflib/mconf';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import { MRedisConnPool } from '../../../redislib/connPool';

interface RequestBody {
  accountId: string;
}

// Request from motiflib/authApiClient.js::logout
export = async (req: RequestAs<RequestBody>, res: ResponseAs<JsonEmpty>) => {
  mlog.info('/logout', req.body);

  const { accountId }: RequestBody = req.body;

  const dbConnPool = Container.get(DBConnPool);

  return taLogout(dbConnPool.getPool(), accountId)
    .then(() => {
      // make userHeartBeat expired
      const curTimeUtc = mutil.curTimeUtc();
      const minHeartBeatTs = curTimeUtc - mconf.userHeartBeatInterval;
      const userCacheRedis = Container.get(MRedisConnPool);
      return userCacheRedis['updateUserHeartBeatWhenLogout'](accountId, minHeartBeatTs);
    })
    .then(() => {
      res.end();
    })
    .catch((error: Error) => {
      throw new MError(error.message, MErrorCode.AUTH_API_LOGOUT_ERROR);
    });
};
