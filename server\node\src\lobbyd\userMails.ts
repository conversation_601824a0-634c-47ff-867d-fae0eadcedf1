// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { mapValues, omit } from 'lodash/fp';
import { Container } from 'typedi';
import moment from 'moment';

import { MAIL_STATE } from './const';
import { LoginInfo, User, GLogParam } from './user';
import * as sync from './type/sync';
import { LobbyService } from './server';
import tuGetPendingDirectMailCount from '../mysqllib/txn/tuGetPendingDirectMailCount';
import tuResolvePendingDirectMails from '../mysqllib/txn/tuResolvePendingDirectMails';
import * as proto from '../proto/lobby/proto';
import mlog from '../motiflib/mlog';
import cms from '../cms';
import * as cmsEx from '../cms/ex';
import mconf from '../motiflib/mconf';
import { RewardData } from '../motiflib/gameLog';
import { REWARD_TYPE } from '../cms/rewardDesc';
import { RewardFixedElemDesc } from '../cms/rewardFixedDesc';
import mhttp from '../motiflib/mhttp';
import * as mutil from '../motiflib/mutil';
import { LineMailManager, WorldLineMail } from './lineMailManager';
import { SECONDS_PER_HOUR } from '../formula';
import tuInsertLineMails, {
  LineMail as LineMailForInsertToUserDb,
} from '../mysqllib/txn/tuInsertLineMails';
import puLineMailLoad, { Result as puLineMailLoadResult } from '../mysqllib/sp/puLineMailLoad';
import { PLATFORM } from '../motiflib/model/auth/enum';
import { LineMailCmsId } from '../cms/mailDesc';
import { PendingDirectMail } from '../motiflib/model/town';
import { MailCreatingParams } from '../motiflib/mailBuilder';

//---------------------------------------------------------------------
// !! 2025.02.21 이후로는 메일 생성 시 mailBuilder.ts를 통해서만 생성해야함!!
//---------------------------------------------------------------------
export interface DirectMail {
  id: number;
  cmsId: number;
  state: MAIL_STATE;
  createTimeUtc: number;
  readTimeUtc: number | null;
  expireTimeUtc: number;
  bShouldSetExpirationWhenReceiveAttachment: number;
  title?: string;
  titleFormatValue?: number;
  body?: string;
  bodyFormatValue?: number;
  attachment?: string;
}

export interface DirectMailChange {
  id?: number;
  cmsId?: number;
  state?: MAIL_STATE;
  createTimeUtc?: number;
  readTimeUtc?: number | null;
  expireTimeUtc?: number;
  bShouldSetExpirationWhenReceiveAttachment?: number;
  title?: string;
  titleFormatValue?: number;
  body?: string;
  bodyFormatValue?: number;
  attachment?: string;
}

export interface GLogMailData {
  id: number;
  uid: number;
  idx: number;
  title: string;
  description: string;
  expiredate: string;
  createdate: string;
  titleFormatValue?: number;
  bodyFormatValue?: number;
}

export interface UserLineMail {
  id: number;
  state: MAIL_STATE;
  title: string;
  body: string;
  createTimeUtc: number;
  readTimeUtc: number;
  deleteTimeUtc: number;
  expireTimeUtc: number;
}

export interface UserLineMailChange {
  id: number;
  state?: MAIL_STATE;
  createTimeUtc?: number;
  readTimeUtc?: number;
  deleteTimeUtc?: number;
  expireTimeUtc?: number;
}

// api/mailboxG/current/list 을 통해 얻어지는 line mail
export interface RawLineMail {
  mailId: string;
  title: string;
  textContent: string;
  expireHour: number;
  giveItemList: { [key: string]: string };
}

// ----------------------------------------------------------------------------
// Mail object.
// ----------------------------------------------------------------------------
class UserMails {
  private _directMails: { [id: number]: DirectMail };
  private _lastDirectMailId: number;
  private _userId: number;
  private _lastQueryLineMailTimeUtc: number;
  private _lineMails: { [id: number]: UserLineMail };

  constructor() {
    this._directMails = {};
    this._lastDirectMailId = 0;
    this._userId = 0;
    this._lastQueryLineMailTimeUtc = 0;
    this._lineMails = {};
  }

  clone(): UserMails {
    const c = new UserMails();
    c.cloneSet(
      _.cloneDeep(this._directMails),
      this._lastDirectMailId,
      this._userId,
      Number.MAX_SAFE_INTEGER,
      this._lineMails
    );
    return c;
  }

  cloneSet(
    directMails: { [id: number]: DirectMail },
    lastDirectMailId: number,
    userId,
    lastQueryLineMailTimeUtc,
    lineMails
  ): void {
    this._directMails = directMails;
    this._lastDirectMailId = lastDirectMailId;
    this._userId = userId;
    this._lastQueryLineMailTimeUtc = lastQueryLineMailTimeUtc;
    this._lineMails = lineMails;
  }

  initWithLoginInfo(loginInfo: LoginInfo): void {
    for (const elem of loginInfo.directMails) {
      const directMail: DirectMail = {
        id: elem.id,
        cmsId: elem.cmsId,
        state: elem.state,
        createTimeUtc: parseInt(elem.createTimeUtc, 10),
        readTimeUtc: elem.readTimeUtc ? parseInt(elem.readTimeUtc, 10) : null,
        expireTimeUtc: elem.expireTimeUtc ? parseInt(elem.expireTimeUtc, 10) : null,
        bShouldSetExpirationWhenReceiveAttachment: elem.bShouldSetExpirationWhenReceiveAttachment,
        title: elem.title,
        titleFormatValue: elem.titleFormatValue,
        body: elem.body,
        bodyFormatValue: elem.bodyFormatValue,
        attachment: elem.attachment,
      };
      this._directMails[elem.id] = directMail;
    }

    this._lastDirectMailId = loginInfo.lastDirectMailId;

    this._userId = loginInfo.userId;
  }

  getDirectMails(): { [id: number]: DirectMail } {
    return this._directMails;
  }

  addDirectMail(params: MailCreatingParams, glogParam: GLogParam | null): DirectMail {
    const directMail: DirectMail = {
      id: params.id,
      cmsId: params.cmsId,
      state: MAIL_STATE.UNREAD,
      createTimeUtc: params.createTimeUtc,
      readTimeUtc: null,
      expireTimeUtc: params.expireTimeUtc,
      bShouldSetExpirationWhenReceiveAttachment: params.bShouldSetExpirationWhenReceiveAttachment,
      title: params.title,
      titleFormatValue: params.titleFormatValue,
      body: params.body,
      bodyFormatValue: params.bodyFormatValue,
      attachment: params.attachment,
    };

    this._directMails[directMail.id] = directMail;
    if (directMail.id > this._lastDirectMailId) {
      this._lastDirectMailId = directMail.id;
    }

    // glog
    if (glogParam) {
      const mailCms = cms.Mail[directMail.cmsId];
      glogParam.user.glog('common_mail_create', {
        rsn: mailCms.rsn,
        add_rsn: null,
        mail_data: UserMails.getGLogMailDataForDirectMail(directMail),
        reward_data: directMail.attachment
          ? UserMails.convertCustomAttachmentToGLogRewardData(directMail.attachment)
          : mailCms.rewardFixed
          ? cmsEx.convertRewardFixedToGLogRewardData(mailCms.rewardFixed)
          : null,
      });
    }

    return this._directMails[directMail.id];
  }

  static convertCustomAttachmentToGLogRewardData(attachment: string): RewardData[] {
    if (!attachment) {
      return [];
    }
    const attachmentObj: RewardFixedElemDesc[] = JSON.parse(attachment);
    const ret: RewardData[] = [];

    for (const elem of attachmentObj) {
      ret.push({
        type: REWARD_TYPE[elem.Type],
        id: elem.Id === undefined ? null : elem.Id,
        uid: null,
        amt: elem.Quantity,
      });
    }

    return ret;
  }

  getDirectMail(id: number): DirectMail {
    return this._directMails[id];
  }

  getLastDirectMailId(): number {
    return this._lastDirectMailId;
  }

  generateNewDirectMailId(): number {
    return ++this._lastDirectMailId;
  }

  // 새로운 id를 발급하면서 메모리에 저장하기 때문에 setLastUerDirectMailId 은 필요치 않음.
  // (id 가 중간에 빌 수 있는 점은 감수한다)
  // setLastUerDirectMailId(id: number): void {
  //   this._lastDirectMailId = id;
  // }

  setDirectMailState(id: number, state: MAIL_STATE) {
    this.getDirectMail(id).state = state;
  }

  setDirectMailExpireTimeUtc(id: number, expireTimeUtc: number) {
    this.getDirectMail(id).expireTimeUtc = expireTimeUtc;
  }

  readDirectMail(change: DirectMailChange) {
    const mail = this.getDirectMail(change.id);
    mail.state = change.state;
    mail.readTimeUtc = change.readTimeUtc;
  }

  deleteDirectMail(id: number) {
    delete this._directMails[id];
  }

  resolvePendingDirectMails(bBroadcast: boolean, user: User): Promise<void> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const mailIdsToBroadcast = [];
    let count;
    return tuGetPendingDirectMailCount(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      this._userId
    )
      .then((ret) => {
        count = ret;
        if (!count) {
          return;
        }

        const ids = [];
        for (let i = 0; i < count; i++) {
          ids.push(this.generateNewDirectMailId());
        }

        return tuResolvePendingDirectMails(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          this._userId,
          ids
        ).then((ret) => {
          if (!ret) {
            return;
          }
          for (const elem of ret) {
            mailIdsToBroadcast.push(elem.id);
            this.addDirectMail(
              {
                id: elem.id,
                cmsId: elem.cmsId,
                createTimeUtc: elem.createTimeUtc,
                expireTimeUtc: elem.expireTimeUtc,
                bShouldSetExpirationWhenReceiveAttachment:
                  elem.bShouldSetExpirationWhenReceiveAttachment,
                title: elem.title,
                titleFormatValue: elem.titleFormatValue,
                body: elem.body,
                bodyFormatValue: elem.bodyFormatValue,
                attachment: elem.attachment,
                getParam: null,
              },
              { user }
            );
          }
        });
      })
      .then(() => {
        if (bBroadcast && mailIdsToBroadcast.length > 0) {
          const userDirectMails = {};
          for (const mailId of mailIdsToBroadcast) {
            const mail = _.cloneDeep(this.getDirectMail(mailId));
            delete mail.bShouldSetExpirationWhenReceiveAttachment;
            userDirectMails[mailId] = mail;
          }
          const sync: sync.Sync = {
            add: {
              userDirectMails,
            },
          };
          user.sendJsonPacket<sync.Resp>(0, proto.Common.NEW_MAIL_SC, { sync }).catch((err) => {
            mlog.error('resolvePendingDirectMails is failed 2', {
              userId: user.userId,
              err: err.message,
            });
          });
        }
        return null;
      })
      .catch((err) => {
        mlog.error('resolvePendingDirectMails is failed 1', {
          userId: user.userId,
          err: err.message,
        });
      });
  }

  static getGLogMailDataForDirectMail(mail: DirectMail): GLogMailData {
    const mailCms = cms.Mail[mail.cmsId];
    return {
      id: mail.cmsId,
      uid: mail.id,
      idx: mailCms.mailType,
      title: mail.title ? mail.title : mailCms.languageMailTitle[mconf.countryCode],
      titleFormatValue: mail.titleFormatValue ? mail.titleFormatValue : null,
      description: mail.body ? mail.body : mailCms.languageMailDesc[mconf.countryCode],
      bodyFormatValue: mail.bodyFormatValue ? mail.bodyFormatValue : null,
      expiredate: mail.expireTimeUtc
        ? moment(new Date(mail.expireTimeUtc * 1000)).format('YYYY-MM-DD HH:mm:ss')
        : null,
      createdate: moment(new Date(mail.createTimeUtc * 1000)).format('YYYY-MM-DD HH:mm:ss'),
    };
  }

  static getGLogMailDataForPendingDirectMail(mail: PendingDirectMail): GLogMailData {
    return UserMails.getGLogMailDataForDirectMail({
      id: mail.id,
      cmsId: mail.cmsId,
      state: MAIL_STATE.UNREAD,
      createTimeUtc: mail.createTimeUtc,
      readTimeUtc: null,
      expireTimeUtc: mail.expireTimeUtc,
      bShouldSetExpirationWhenReceiveAttachment: mail.bShouldSetExpirationWhenReceiveAttachment,
      title: mail.title,
      titleFormatValue: mail.titleFormatValue,
      body: mail.body,
      bodyFormatValue: mail.bodyFormatValue,
      attachment: mail.attachment,
    });
  }

  static getGLogMailDataForLineMail(
    userMail: UserLineMail,
    worldLineMail: WorldLineMail
  ): GLogMailData {
    const mailCms = cms.Mail[LineMailCmsId];
    return {
      id: mailCms.id,
      uid: userMail.id,
      idx: mailCms.mailType,
      title: userMail.title,
      description: userMail.body,
      expiredate: moment(new Date(userMail.expireTimeUtc * 1000)).format('YYYY-MM-DD HH:mm:ss'),
      createdate: moment(new Date(userMail.createTimeUtc * 1000)).format('YYYY-MM-DD HH:mm:ss'),
    };
  }

  getDirectMailSyncData(id: number): sync.UserMail {
    const mail = _.cloneDeep(this.getDirectMail(id));
    delete mail.bShouldSetExpirationWhenReceiveAttachment;
    return mail;
  }

  tick(user: User) {
    const curTimeUtc = mutil.curTimeUtc();

    // query line mail
    if (mconf.platform === PLATFORM.LINE) {
      if (curTimeUtc - this._lastQueryLineMailTimeUtc > mconf.userTick.lineMailPoolingIntervalSec) {
        const bIsFirstQuery = this._lastQueryLineMailTimeUtc === 0;
        this._lastQueryLineMailTimeUtc = curTimeUtc;
        this.queryLineMails(user, bIsFirstQuery);
      }
    }
  }

  // ret.mailBoxList element
  // {
  //   mailId: '209',
  //   mailType: 'SHORT_TEXT',
  //   title: 'test1',
  //   textContent: 'test1',
  //   imageUrl: '',
  //   linkUrlIos: '',
  //   linkUrlAndroid: '',
  //   expireHour: 1000,
  //   specialApplyStartHhmm: '',
  //   specialApplyEndHhmm: '',
  //   specialApplyDayOfWeek: '',
  //   applyStartYmdt: 20210106000000,
  //   applyStartYmdtUnixTS: 1609858800,
  //   applyEndYmdt: 20210131000000,
  //   applyEndYmdtUnixTS: 1612018800,
  //   applyMinPlayerLevel: 1,
  //   applyMaxPlayerLevel: 60,
  //   giveItemList: null
  // },
  queryLineMails(user: User, bIsFirstQuery: boolean): Promise<any> {
    if (user.isTestBot()) {
      return;
    }

    const curTimeUtc = mutil.curTimeUtc();
    const pubId = user.pubId;
    const lineLangCultre = user.lineLangCultre;
    const level = user.level;

    const sync: sync.Sync = {
      add: {
        lineMails: {},
      },
    };
    const newMailsToInsertToDb: LineMailForInsertToUserDb[] = [];
    const worldLineMails: { [id: number]: WorldLineMail } = {};
    let userLineMailsForFirst: puLineMailLoadResult[];
    const newRawLineMails: RawLineMail[] = [];
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const lineMailManager = Container.get(LineMailManager);

    return Promise.resolve()
      .then(() => {
        if (bIsFirstQuery) {
          // 첫 번째 query 인 경우 user db 에서 기존 line mails 를 로드해서 this._lineMails 에 넣어두고 클라로 보내준다.
          return puLineMailLoad(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId
          );
        }
        return null;
      })
      .then((ret) => {
        if (ret) {
          userLineMailsForFirst = ret;
        }
        if (bIsFirstQuery) {
          const promises = [];
          for (const elem of ret) {
            promises.push(lineMailManager.getWorldLineMailAsync(elem.id));
          }

          return Promise.all(promises);
        }
        return [];
      })
      .then((ret) => {
        if (ret.length > 0) {
          for (const elem of ret) {
            if (!elem) {
              continue;
            }
            worldLineMails[elem.id] = elem;
          }
        }

        if (bIsFirstQuery) {
          for (const elem of userLineMailsForFirst) {
            const worldLineMail = worldLineMails[elem.id];
            if (!worldLineMail) {
              mlog.error('UserMail:queryLineMails world line mail is not found', {
                userId: user.userId,
                elem,
              });
              continue;
            }

            const mail: UserLineMail = {
              id: elem.id,
              state: elem.state,
              title: elem.title ? elem.title : worldLineMail.title,
              body: elem.body ? elem.body : worldLineMail.title,
              createTimeUtc: parseInt(elem.createTimeUtc, 10),
              readTimeUtc: parseInt(elem.readTimeUtc, 10),
              deleteTimeUtc: parseInt(elem.deleteTimeUtc, 10),
              expireTimeUtc: parseInt(elem.expireTimeUtc, 10),
            };
            this._lineMails[elem.id] = mail;

            if (mail.state !== MAIL_STATE.DELETED && mail.expireTimeUtc > curTimeUtc) {
              _.merge<sync.Sync, sync.Sync>(sync, {
                add: {
                  lineMails: {
                    [mail.id]: {
                      id: mail.id,
                      title: mail.title,
                      body: mail.body,
                      attachment: worldLineMail.attachment,
                      state: mail.state,
                      createTimeUtc: mail.createTimeUtc,
                      readTimeUtc: mail.readTimeUtc,
                      expireTimeUtc: mail.expireTimeUtc,
                    },
                  },
                },
              });
            }
          }
        }

        return mhttp.platformApi.getMails(user.userId, lineLangCultre, level);
      })
      .then((ret) => {
        if (!ret || !_.isArray(ret.mailBoxList) || ret.mailBoxList.length === 0) {
          return [];
        }

        for (const elem of ret.mailBoxList) {
          if (this._lineMails[elem.mailId]) {
            continue;
          }

          newRawLineMails.push(elem);
        }

        if (newRawLineMails.length === 0) {
          return [];
        }

        lineMailManager.insertWorldMailsIfNeeded(newRawLineMails);

        const promises = [];
        for (const newMail of newRawLineMails) {
          if (worldLineMails[newMail.mailId]) {
            continue;
          }
          promises.push(lineMailManager.getWorldLineMailAsync(parseInt(newMail.mailId, 10)));
        }

        return Promise.all(promises);
      })
      .then((ret) => {
        if (ret.length > 0) {
          for (const elem of ret) {
            if (!elem) {
              continue;
            }
            worldLineMails[elem.id] = elem;
          }
        }

        for (const newMail of newRawLineMails) {
          const worldLineMail = worldLineMails[newMail.mailId];
          if (!worldLineMail) {
            continue;
          }

          newMailsToInsertToDb.push({
            id: worldLineMail.id,
            state: MAIL_STATE.UNREAD,
            title: newMail.title,
            body: newMail.textContent,
            createTimeUtc: curTimeUtc,
            expireTimeUtc: curTimeUtc + newMail.expireHour * SECONDS_PER_HOUR,
          });
        }

        if (newMailsToInsertToDb.length > 0) {
          return tuInsertLineMails(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId,
            newMailsToInsertToDb
          );
        }
        return null;
      })
      .then(() => {
        if (newMailsToInsertToDb.length > 0) {
          for (const elem of newMailsToInsertToDb) {
            this._lineMails[elem.id] = {
              id: elem.id,
              state: elem.state,
              title: elem.title,
              body: elem.body,
              createTimeUtc: elem.createTimeUtc,
              readTimeUtc: null,
              deleteTimeUtc: null,
              expireTimeUtc: elem.expireTimeUtc,
            };

            const worldLineMail = worldLineMails[elem.id];
            _.merge<sync.Sync, sync.Sync>(sync, {
              add: {
                lineMails: {
                  [elem.id]: {
                    id: elem.id,
                    title: elem.title,
                    body: elem.body,
                    attachment: worldLineMail.attachment,
                    state: elem.state,
                    createTimeUtc: elem.createTimeUtc,
                    expireTimeUtc: elem.expireTimeUtc,
                  },
                },
              },
            });
          }
        }

        if (Object.keys(sync.add.lineMails).length > 0) {
          return user.sendJsonPacket(0, proto.Common.NEW_LINE_MAIL_SC, { sync });
        }
        return null;
      })
      .catch((err) => {
        mlog.error('UserMail:queryLineMails is failed', {
          userId: user.userId,
          pubId,
          lineLangCultre,
          level,
          err: err.message,
        });
      });
  }
  /*
  queryLineMails2(user: User, bIsFirstQuery: boolean): Promise<any> {
    if (user.isTestBot()) {
      return;
    }

    const curTimeUtc = mutil.curTimeUtc();
    const pubId = user.pubId;
    const lineLangCultre = user.lineLangCultre;
    const level = user.level;

    const sync: sync.Sync = {
      add: {
        lineMails: {},
      },
    };
    const newMailsToInsertToDb: LineMailForInsertToUserDb[] = [];
    const userDbLineMailsForFirst: { [id: number]: puLineMailLoadResult } = {};
    const rawLineMails: { [id: string]: RawLineMail } = {};
    const { userDbConnPoolMgr } = Container.get(LobbyService);

    return mhttp.lgd
      .getMails(user.userId, lineLangCultre, level)
      .then((ret) => {
        if (!ret || !_.isArray(ret.mailBoxList) || ret.mailBoxList.length === 0) {
          return null;
        }

        for (const elem of ret.mailBoxList) {
          rawLineMails[elem.mailId] = elem;
        }

        if (bIsFirstQuery) {
          // 첫 번째 query 인 경우 user db 에서 기존 line mails 을 로드해야 된다.
          return puLineMailLoad(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId
          ).then((ret) => {
            for (const elem of ret) {
              userDbLineMailsForFirst[elem.id] = elem;
            }
          });
        }
        return null;
      })
      .then(() => {
        _.forOwn(rawLineMails, (rawLineMail) => {
          const mailId = parseInt(rawLineMail.mailId, 10);
          if (!userDbLineMailsForFirst[rawLineMail.mailId]) {
            const mail: UserLineMail = {
              id: mailId,
              state: MAIL_STATE.UNREAD,
              createTimeUtc: curTimeUtc,
              readTimeUtc: null,
              deleteTimeUtc: null,
              expireTimeUtc: curTimeUtc + rawLineMail.expireHour * SECONDS_PER_HOUR,
            };
            this._lineMails[mailId] = mail;

            newMailsToInsertToDb.push({
              id: mailId,
              state: mail.state,
              createTimeUtc: mail.createTimeUtc,
              expireTimeUtc: mail.expireTimeUtc,
            });
          } else {
            const userLineMail: puLineMailLoadResult = userDbLineMailsForFirst[rawLineMail.mailId];
            const mail: UserLineMail = {
              id: userLineMail.id,
              state: userLineMail.state,
              createTimeUtc: parseInt(userLineMail.createTimeUtc, 10),
              readTimeUtc: parseInt(userLineMail.readTimeUtc, 10),
              deleteTimeUtc: parseInt(userLineMail.deleteTimeUtc, 10),
              expireTimeUtc: parseInt(userLineMail.expireTimeUtc, 10),
            };
            this._lineMails[mailId] = mail;
          }

          const mail = this._lineMails[mailId];
          const attachment = this._convertLineGiveItmeListToAttachment(rawLineMail.giveItemList);
          _.merge<sync.Sync, sync.Sync>(sync, {
            add: {
              lineMails: {
                [mailId]: {
                  id: mailId,
                  title: rawLineMail.title,
                  body: rawLineMail.textContent,
                  attachment,
                  state: mail.state,
                  createTimeUtc: mail.createTimeUtc,
                  expireTimeUtc: mail.expireTimeUtc,
                },
              },
            },
          });
        });

        if (newMailsToInsertToDb.length > 0) {
          return tuInsertLineMails(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            user.userId,
            newMailsToInsertToDb
          );
        }
        return null;
      })
      .then(() => {
        if (Object.keys(sync.add.lineMails).length > 0) {
          return user.sendJsonPacket(0, proto.Common.NEW_LINE_MAIL_SC, { sync });
        }
        return null;
      })
      .catch((err) => {
        mlog.error('UserMail:queryLineMails is failed', {
          userId: user.userId,
          pubId,
          lineLangCultre,
          level,
          err: err.message,
        });
      });
  }

  private _convertLineGiveItmeListToAttachment(giveItemList: { [key: string]: string }): string {
    if (!giveItemList) {
      return null;
    }
    const attachment: RewardFixedElemDesc[] = [];

    // 첨부의 형식이 유효하지 않을 경우 에러 로그 남긴 후 해당 메일 무시한다.
    let isValidMail = true;

    // @key 'rewardType:cmsId'
    _.forOwn(giveItemList, (amountStr, key) => {
      const keyArr = key.split(':');
      if (keyArr.length !== 2) {
        isValidMail = false;
        return;
      }

      const rewardType = parseInt(keyArr[0], 10);
      if (!Object.values(REWARD_TYPE).includes(rewardType)) {
        isValidMail = false;
        return;
      }

      const cmsTable = getCmsTableByRewardType(rewardType);
      let cmsId: number;
      if (cmsTable) {
        if (!cmsTable[keyArr[1]]) {
          isValidMail = false;
          return;
        }
        cmsId = parseInt(keyArr[1], 10);
      }

      const amount = parseInt(amountStr, 10);
      if (mutil.isNotANumber(amount)) {
        isValidMail = false;
        return;
      }

      attachment.push({
        Type: rewardType,
        Id: cmsId ? cmsId : undefined,
        Quantity: amount,
      });
    });

    if (!isValidMail) {
      mlog.alert('Invalid give item list.', {
        giveItemList,
      });
    }

    if (attachment.length > 0) {
      return JSON.stringify(attachment);
    }
    return null;
  }
*/
  getUserLineMail(id: number): UserLineMail {
    return this._lineMails[id];
  }

  readLineMail(change: UserLineMailChange): void {
    const mail = this.getUserLineMail(change.id);
    mail.state = change.state;
    mail.readTimeUtc = change.readTimeUtc;
  }

  setLineMailState(id: number, state: MAIL_STATE): void {
    const mail = this.getUserLineMail(id);
    mail.state = state;
  }

  deleteLineMail(id: number) {
    // line mail 은 삭제를 해도 메모리에서 지우지 않고 상태만 바꿔서 들고 있는다.
    const mail = this.getUserLineMail(id);
    mail.state = MAIL_STATE.DELETED;
  }

  getSyncData(): sync.All {
    const userDirectMails = mapValues(
      omit('bShouldSetExpirationWhenReceiveAttachment'),
      this._directMails
    );

    const ret: sync.All = {
      userDirectMails: _.cloneDeep(userDirectMails),
    };

    // null인 값은 제거.
    _.forOwn(ret.userDirectMails, (mail) => {
      _.forOwn(mail, (elem, key) => {
        if (elem === null) {
          delete mail[key];
        }
      });
    });
    return ret;
  }
}

// ----------------------------------------------------------------------------
// Exports.
// ----------------------------------------------------------------------------

export default UserMails;
