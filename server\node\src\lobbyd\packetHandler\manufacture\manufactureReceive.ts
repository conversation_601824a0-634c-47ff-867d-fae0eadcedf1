// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';
import { Promise as promise } from 'bluebird';
import cms from '../../../cms';
import * as mutil from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { Sync, Resp } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import { ManufactureProgress } from '../../userManufacture';
import { ManufactureRecipeDesc, MANUFACTURE_PROGRESS_TYPE } from '../../../cms/manufactureRecipeDesc';
import { ManufactureExpLevelChange } from '../../../motiflib/model/lobby';
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import { RewardData } from '../../../motiflib/gameLog';
import { RewardFixedDesc, RewardFixedElemDesc } from '../../../cms/rewardFixedDesc';
import mlog from '../../../motiflib/mlog';
import {
  ActualGain,
  CHANGE_TASK_REASON,
  CHANGE_TASK_RESULT,
  Changes,
  TryData,
  UserChangeTask,
} from '../../UserChangeTask/userChangeTask';
import { RNP_TYPE, RewardAndPaymentSpec, RewardAndPaymentElem } from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import { opAddManufactureExp, opDeleteManufactureProgress, opAddMateEquip, opAddShipSlotItem, opAddItem, opApplyShipCargoChange } from '../../UserChangeTask/userChangeOperator';
import { ShipCargoChange } from '../../ship';
import { RestoreCmsElemExtra } from './manufactureStart';

// ----------------------------------------------------------------------------
// 제조 완료 받기
// ----------------------------------------------------------------------------

const rsn = 'manufacture_receive';
const add_rsn = null;

// 상수 정의
const MANUFACTURE_CONSTANTS = {
  COMPLETION_TIME_BUFFER_SECONDS: 20, // 서버-클라이언트 타임 동기화를 위한 여유 시간
  RESTORE_TYPES: {
    ITEM: 0,
    EQUIP_ID: 1,
    EQUIP_GRADE: 2,
    SHIP_PARTS_ID: 3,
    SHIP_PARTS_GRADE: 4,
    TRADE_GOODS: 5,
  },
} as const;

interface RequestBody {
  roomId: number;
  slot?: number; // slot이 없으면 모든 완료된 슬롯 처리
}

interface SlotResult {
  cmsId: number;
  roomId: number;
  slotNo: number;
  isGreatSuccess: boolean;
}

interface ManufactureSlot {
  roomId: number;
  slot_idx: number;
  critical: boolean;
}

interface ProductForLog {
  product_id: number;
  product_uid: number;
  product_name: string;
}

// ----------------------------------------------------------------------------
export class Cph_Manufacture_Receive implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  // 여기 시범적으로 async 적용.
  async exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { roomId, slot } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const roomProgress = this.validateRoomProgress(user, roomId, body);

    // slot이 지정된 경우 단일 슬롯 처리, 아니면 모든 완료된 슬롯 처리
    if (slot !== undefined) {
      return this.processSingleSlot(user, roomId, slot, roomProgress, body, packet);
    }
    return this.processAllCompletedSlots(user, roomId, roomProgress, body, packet);
  }

  // --------------------------------------------------------------------------
  private validateRoomProgress(user: User, roomId: number, body: RequestBody): ManufactureProgress {
    const roomProgress: ManufactureProgress = user.userManufacture.getRoom(roomId);
    if (!roomProgress) {
      throw new MError(
        'not-in-progress-manufacture-room',
        MErrorCode.NOT_IN_PROGRESS_MANUFACTURE_ROOM,
        {
          userId: user.userId,
          roomId,
          body,
        }
      );
    }
    return roomProgress;
  }

  // --------------------------------------------------------------------------
  private async processSingleSlot(user: User, roomId: number, slot: number, roomProgress: ManufactureProgress, body: RequestBody, packet: CPacket): Promise<any> {
    const slotData = this.validateSlotData(user, roomId, slot, roomProgress, body);
    const manufactureRecipeDesc = this.validateRecipeDesc(user, roomId, slot, slotData.recipeId, body);
    
    this.validateCompletionTime(user, roomId, slot, slotData.completionTimeUtc, body);

    const { rewardCmsId, isGreatSuccess, isFailure } = this.calculateReward(slotData, manufactureRecipeDesc);
    const rewardFixedCms = this.getRewardFixedCms(user, roomId, slot, rewardCmsId, isFailure, body);

    const userChangeTask = this.createUserChangeTask(
      user, roomId, slot, rewardCmsId, manufactureRecipeDesc, isGreatSuccess, isFailure, slotData.extra
    );

    const gain = await this.executeUserChangeTask(userChangeTask, body);

    return this.handleSingleSlotResult(user, roomId, slot, slotData, isFailure, gain, packet);
  }

  // --------------------------------------------------------------------------
  private async processAllCompletedSlots(user: User, roomId: number, roomProgress: ManufactureProgress, body: RequestBody, packet: CPacket): Promise<any> {
    const curTimeUtc = mutil.curTimeUtc();
    const completedSlots = this.findCompletedSlots(user, roomId, roomProgress, curTimeUtc);

    if (completedSlots.length === 0) {
      throw new MError('no-completed-manufacture-slots', MErrorCode.MANUFACTURE_NOT_FINISHED_YET, {
        userId: user.userId,
        roomId,
        body,
      });
    }

    const { changeTasks, slotResults } = this.createChangeTasksForCompletedSlots(user, roomId, completedSlots);
    const { sync, gains } = await this.executeChangeTasks(changeTasks);

    return this.handleAllSlotsResult(user, roomId, roomProgress, slotResults, gains, sync, packet);
  }

  // --------------------------------------------------------------------------
  private validateSlotData(user: User, roomId: number, slot: number, roomProgress: ManufactureProgress, body: RequestBody) {
    const slotData = roomProgress[slot];
    if (!slotData) {
      throw new MError(
        'not-in-progress-manufacture-slot',
        MErrorCode.NOT_IN_PROGRESS_MANUFACTURE_SLOT,
        {
          userId: user.userId,
          roomId,
          slot,
          body,
        }
      );
    }
    return slotData;
  }

  // --------------------------------------------------------------------------
  private validateRecipeDesc(user: User, roomId: number, slot: number, recipeId: number, body: RequestBody): ManufactureRecipeDesc {
    const manufactureRecipeDesc: ManufactureRecipeDesc = cms.ManufactureRecipe[recipeId];
    if (!manufactureRecipeDesc) {
      throw new MError(
        'invalid-manufacture-recipe-cms-id',
        MErrorCode.INVALID_MANUFACTURE_RECIPE_CMS_ID,
        {
          userId: user.userId,
          roomId,
          slot,
          body,
        }
      );
    }
    return manufactureRecipeDesc;
  }

  // --------------------------------------------------------------------------
  private validateCompletionTime(user: User, roomId: number, slot: number, completionTimeUtc: number, body: RequestBody): void {
    const adjustedCompletionTime = completionTimeUtc - MANUFACTURE_CONSTANTS.COMPLETION_TIME_BUFFER_SECONDS;
    const curTimeUtc = mutil.curTimeUtc();
    
    if (adjustedCompletionTime > curTimeUtc) {
      throw new MError('not-finished-yet', MErrorCode.MANUFACTURE_NOT_FINISHED_YET, {
        userId: user.userId,
        roomId,
        slot,
        body,
      });
    }
  }

  // --------------------------------------------------------------------------
  private calculateReward(slotData: any, manufactureRecipeDesc: ManufactureRecipeDesc) {
    const isGreatSuccess = slotData.resultType === MANUFACTURE_PROGRESS_TYPE.GREAT_SUCCESS;
    const isFailure = slotData.resultType === MANUFACTURE_PROGRESS_TYPE.FAILURE;
    
    let rewardCmsId: number;
    if (isFailure) {
      rewardCmsId = 0;
    } else {
      rewardCmsId = isGreatSuccess
        ? manufactureRecipeDesc.criticalRewardFixedId
        : manufactureRecipeDesc.normalRewardFixedId;
    }

    return { rewardCmsId, isGreatSuccess, isFailure };
  }

  // --------------------------------------------------------------------------
  private getRewardFixedCms(user: User, roomId: number, slot: number, rewardCmsId: number, isFailure: boolean, body: RequestBody): RewardFixedDesc | null {
    if (isFailure) {
      return null;
    }

    const rewardFixedCms: RewardFixedDesc = cms.RewardFixed[rewardCmsId];
    if (!rewardFixedCms) {
      throw new MError(
        'invalid-reward-fixed-cms-id',
        MErrorCode.INVALID_MANUFACTURE_RECIPE_CMS_ID,
        {
          userId: user.userId,
          roomId,
          slot,
          rewardCmsId,
          body,
        }
      );
    }

    return rewardFixedCms;
  }

  // --------------------------------------------------------------------------
  private createUserChangeTask(
    user: User,
    roomId: number,
    slot: number,
    rewardCmsId: number,
    manufactureRecipeDesc: ManufactureRecipeDesc,
    isGreatSuccess: boolean,
    isFailure: boolean,
    extra: string
  ): UserChangeTask {
    return new UserChangeTask(
      user,
      CHANGE_TASK_REASON.MANUFACTURE_RECEIVE,
      new ManufactureReceiveSpec(
        roomId,
        slot,
        rewardCmsId,
        manufactureRecipeDesc.manufactureType,
        manufactureRecipeDesc.manufactureExp,
        isGreatSuccess,
        isFailure,
        extra
      )
    );
  }

  // --------------------------------------------------------------------------
  private async executeUserChangeTask(userChangeTask: UserChangeTask, body: RequestBody): Promise<ActualGain> {
    const res = userChangeTask.trySpec();
    if (res !== CHANGE_TASK_RESULT.OK) {
      throw new MError(
        'failed-to-receive-manufacture-reward',
        MErrorCode.INVALID_MANUFACTURE_RECIPE_CMS_ID,
        {
          res,
          body,
        }
      );
    }

    await userChangeTask.apply();
    return userChangeTask.getActualGain();
  }

  // --------------------------------------------------------------------------
  private async handleSingleSlotResult(
    user: User,
    roomId: number,
    slot: number,
    slotData: any,
    isFailure: boolean,
    gain: ActualGain,
    packet: CPacket
  ): Promise<any> {
    const sync = await this.createSyncForSingleSlot(user, roomId, slot, slotData, isFailure);
    const productsForLog = this.buildProductsForLog(gain);
    const manufacture_slots = this.buildManufactureSlots(roomId, slot, !isFailure);
    const slotResults = this.buildSlotResults(roomId, slot, !isFailure);

    this.logManufactureReceive(user, manufacture_slots, productsForLog, slotResults, gain);

    return user.sendJsonPacket(packet.seqNum, packet.type, {
      gain,
      sync
    });
  }

  // --------------------------------------------------------------------------
  private findCompletedSlots(user: User, roomId: number, roomProgress: ManufactureProgress, curTimeUtc: number) {
    const completedSlots: { slot: number; slotData: any; recipeDesc: ManufactureRecipeDesc }[] = [];

    for (const [slotStr, slotData] of Object.entries(roomProgress)) {
      const slot = parseInt(slotStr, 10);
      
      // 제조 시간 만료 체크
      const adjustedCompletionTime = slotData.completionTimeUtc - MANUFACTURE_CONSTANTS.COMPLETION_TIME_BUFFER_SECONDS;
      if (adjustedCompletionTime > curTimeUtc) {
        continue;
      }

      const manufactureRecipeDesc: ManufactureRecipeDesc = cms.ManufactureRecipe[slotData.recipeId];
      if (!manufactureRecipeDesc) {
        mlog.error('[MANUFACTURE] Invalid recipe CMS ID', {
          userId: user.userId,
          roomId,
          slot,
          recipeId: slotData.recipeId,
        });
        continue;
      }

      completedSlots.push({ slot, slotData, recipeDesc: manufactureRecipeDesc });
    }

    return completedSlots;
  }

  // --------------------------------------------------------------------------
  private createChangeTasksForCompletedSlots(
    user: User,
    roomId: number,
    completedSlots: { slot: number; slotData: any; recipeDesc: ManufactureRecipeDesc }[]
  ) {
    const changeTasks: UserChangeTask[] = [];
    const slotResults: SlotResult[] = [];

    completedSlots.forEach(({ slot, slotData, recipeDesc }) => {
      const { rewardCmsId, isGreatSuccess, isFailure } = this.calculateReward(slotData, recipeDesc);

      const userChangeTask = this.createUserChangeTask(
        user, roomId, slot, rewardCmsId, recipeDesc, isGreatSuccess, isFailure, slotData.extra
      );

      changeTasks.push(userChangeTask);
      slotResults.push({
        cmsId: recipeDesc.id,
        roomId,
        slotNo: slot,
        isGreatSuccess,
      });
    });

    return { changeTasks, slotResults };
  }

  // --------------------------------------------------------------------------
  private async executeChangeTasks(changeTasks: UserChangeTask[]): Promise<{ sync: Sync; gains: ActualGain[] }> {
    const gains: ActualGain[] = [];
    
    const sync = await promise.reduce(
      changeTasks,
      (sync, task) => {
        return promise.resolve(task.trySpec()).then((res) => {
          if (res === CHANGE_TASK_RESULT.OK) {
            return task.apply().then((s) => {
              gains.push(task.getActualGain());
              return _.merge<Sync, Sync>(sync, s);
            });
          } else {
            return sync;
          }
        });
      },
      {}
    );

    return { sync, gains };
  }

  // --------------------------------------------------------------------------
  private async handleAllSlotsResult(
    user: User,
    roomId: number,
    roomProgress: ManufactureProgress,
    slotResults: SlotResult[],
    gains: ActualGain[],
    sync: Sync,
    packet: CPacket
  ): Promise<any> {
    // 실패한 슬롯들의 extra 정보에서 아이템 복구
    this.restoreFailedSlots(user, roomProgress, sync);

    const allProductsForLog = this.buildAllProductsForLog(gains);
    const manufacture_slots = this.buildManufactureSlotsFromResults(slotResults);

    this.logManufactureReceiveAll(user, manufacture_slots, allProductsForLog, slotResults, gains);

    return user.sendJsonPacket(packet.seqNum, packet.type, {
      gains,
      sync
    });
  }

  // --------------------------------------------------------------------------
  private async createSyncForSingleSlot(user: User, roomId: number, slot: number, slotData: any, isFailure: boolean): Promise<Sync> {
    const sync: Sync = { add: {}, remove: {} };

    // 실패한 경우 extra 정보에서 아이템 복구
    if (isFailure && slotData.extra) {
      this.restoreItemsFromExtra(user, slotData.extra, sync);
    }

    return sync;
  }

  // --------------------------------------------------------------------------
  private buildProductsForLog(gain: ActualGain): ProductForLog[] {
    if (!gain.items) {
      return [];
    }

    return Object.keys(gain.items).map((itemId) => {
      const itemCmsId = parseInt(itemId, 10);
      const itemDesc = cms.Item[itemCmsId];
      const product_name = itemDesc ? itemDesc.name : 'unknown';
      
      return {
        product_id: itemCmsId,
        product_uid: 0,
        product_name,
      };
    });
  }

  // --------------------------------------------------------------------------
  private buildManufactureSlots(roomId: number, slot: number, isGreatSuccess: boolean): ManufactureSlot[] {
    return [{
      roomId,
      slot_idx: slot,
      critical: isGreatSuccess,
    }];
  }

  // --------------------------------------------------------------------------
  private buildSlotResults(roomId: number, slot: number, isGreatSuccess: boolean): SlotResult[] {
    return [{
      cmsId: 0, // 실제로는 recipeDesc.id를 사용해야 하지만 여기서는 단순화
      roomId,
      slotNo: slot,
      isGreatSuccess,
    }];
  }

  // --------------------------------------------------------------------------
  private buildManufactureSlotsFromResults(slotResults: SlotResult[]): ManufactureSlot[] {
    return slotResults.map(result => ({
      roomId: result.roomId,
      slot_idx: result.slotNo,
      critical: result.isGreatSuccess,
    }));
  }

  // --------------------------------------------------------------------------
  private buildAllProductsForLog(gains: ActualGain[]): ProductForLog[] {
    const allProductsForLog: ProductForLog[] = [];
    
    gains.forEach((gain) => {
      if (gain.items) {
        Object.keys(gain.items).forEach((itemId) => {
          const itemCmsId = parseInt(itemId, 10);
          const itemDesc = cms.Item[itemCmsId];
          const product_name = itemDesc ? itemDesc.name : 'unknown';
          
          allProductsForLog.push({
            product_id: itemCmsId,
            product_uid: 0,
            product_name,
          });
        });
      }
    });

    return allProductsForLog;
  }

  // --------------------------------------------------------------------------
  private restoreFailedSlots(user: User, roomProgress: ManufactureProgress, sync: Sync): void {
    for (const [slotStr, slotData] of Object.entries(roomProgress)) {
      const isFailure = slotData.resultType === MANUFACTURE_PROGRESS_TYPE.FAILURE;
      if (isFailure && slotData.extra) {
        this.restoreItemsFromExtra(user, slotData.extra, sync);
      }
    }
  }

  // --------------------------------------------------------------------------
  private logManufactureReceive(
    user: User,
    manufacture_slots: ManufactureSlot[],
    products: ProductForLog[],
    slotResults: SlotResult[],
    gain: ActualGain
  ): void {
    user.glog('manufacture_receive', {
      rsn,
      add_rsn,
      manufacture_slots,
      products,
    });

    mlog.info('[MANUFACTURE] result', {
      userId: user.userId,
      slotResults,
      reward_data: gain,
    });
  }

  // --------------------------------------------------------------------------
  private logManufactureReceiveAll(
    user: User,
    manufacture_slots: ManufactureSlot[],
    products: ProductForLog[],
    slotResults: SlotResult[],
    gains: ActualGain[]
  ): void {
    user.glog('manufacture_receive_all', {
      rsn,
      add_rsn,
      manufacture_slots,
      products,
    });

    mlog.info('[MANUFACTURE] All slots result', {
      userId: user.userId,
      slotResults,
      gains,
    });
  }

  // --------------------------------------------------------------------------
  private restoreItemsFromExtra(user: User, extra: string, sync: Sync): void {
    try {
      const restoreExtras: RestoreCmsElemExtra[] = JSON.parse(extra);
      
      restoreExtras.forEach((restoreExtra) => {
        this.restoreItemByType(user, restoreExtra, sync);
      });

      mlog.info('[MANUFACTURE_FAILURE] Successfully restored items from extra data', {
        userId: user.userId,
        restoreExtras,
      });
    } catch (error) {
      mlog.error('[MANUFACTURE_FAILURE] Error parsing extra data for recovery', {
        userId: user.userId,
        error: error.message,
        extra,
      });
    }
  }

  // --------------------------------------------------------------------------
  private restoreItemByType(user: User, restoreExtra: RestoreCmsElemExtra, sync: Sync): void {
    const { RESTORE_TYPES } = MANUFACTURE_CONSTANTS;
    
    switch (restoreExtra.restoreType) {
      case RESTORE_TYPES.ITEM:
        this.restoreItem(user, restoreExtra, sync);
        break;
      case RESTORE_TYPES.EQUIP_ID:
      case RESTORE_TYPES.EQUIP_GRADE:
        this.restoreMateEquipment(user, restoreExtra, sync);
        break;
      case RESTORE_TYPES.SHIP_PARTS_ID:
      case RESTORE_TYPES.SHIP_PARTS_GRADE:
        this.restoreShipSlotItem(user, restoreExtra, sync);
        break;
      case RESTORE_TYPES.TRADE_GOODS:
        this.restoreTradeGoods(user, restoreExtra, sync);
        break;
      default:
        mlog.error('[MANUFACTURE_FAILURE] Unknown restore type', {
          userId: user.userId,
          restoreType: restoreExtra.restoreType,
          restoreExtra,
        });
    }
  }

  // --------------------------------------------------------------------------
  private restoreItem(user: User, restoreExtra: RestoreCmsElemExtra, sync: Sync): void {
    const itemChange = user.userInven.itemInven.buildItemChange(restoreExtra.restoreCmsId, restoreExtra.count, true);
    user.userInven.itemInven.applyItemChange(itemChange, [], { user, rsn, add_rsn });
    
    this.addItemToSync(sync, restoreExtra.restoreCmsId, restoreExtra.count);
  }

  // --------------------------------------------------------------------------
  private restoreMateEquipment(user: User, restoreExtra: RestoreCmsElemExtra, sync: Sync): void {
    const mateEquipment = user.userMates.buildMateEquipmentNub(
      restoreExtra.restoreCmsId,
      0, // equippedMateCmsId
      restoreExtra.isBound,
      0, // isCostume
      mutil.curTimeUtc(),
      restoreExtra.expireTimeUtc,
      restoreExtra.enchantLv
    );
    user.userMates.addMateEquipment(mateEquipment, { user, rsn, add_rsn });
    
    this.addMateEquipmentToSync(sync, mateEquipment);
  }

  // --------------------------------------------------------------------------
  private restoreShipSlotItem(user: User, restoreExtra: RestoreCmsElemExtra, sync: Sync): void {
    const shipSlotItem = user.userInven.buildShipSlotItem(
      restoreExtra.restoreCmsId,
      restoreExtra.isBound,
      0, // isLocked
      mutil.curTimeUtc(),
      restoreExtra.expireTimeUtc,
      restoreExtra.enchantLv
    );
    user.userInven.addShipSlotItem(shipSlotItem, { user, rsn, add_rsn });
    
    this.addShipSlotItemToSync(sync, shipSlotItem);
  }

  // --------------------------------------------------------------------------
  private restoreTradeGoods(user: User, restoreExtra: RestoreCmsElemExtra, sync: Sync): void {
    if (restoreExtra.shipId) {
      const userShip = user.userFleets.getShip(restoreExtra.shipId);
      if (userShip) {
        const cargoChange: ShipCargoChange = {
          shipId: restoreExtra.shipId,
          cmsId: restoreExtra.restoreCmsId,
          quantity: restoreExtra.count,
          pointInvested: 0,
        };
        userShip.applyCargoChange(cargoChange, user, { user, rsn, add_rsn });
        
        this.addCargoToSync(sync, restoreExtra.shipId, restoreExtra.restoreCmsId, restoreExtra.count);
      }
    }
  }

  // --------------------------------------------------------------------------
  private addItemToSync(sync: Sync, cmsId: number, count: number): void {
    if (!sync.add.items) {
      sync.add.items = {};
    }
    sync.add.items[cmsId] = {
      cmsId,
      count,
    };
  }

  // --------------------------------------------------------------------------
  private addMateEquipmentToSync(sync: Sync, mateEquipment: any): void {
    if (!sync.add.mateEquipments) {
      sync.add.mateEquipments = {};
    }
    sync.add.mateEquipments[mateEquipment.id] = {
      id: mateEquipment.id,
      cmsId: mateEquipment.cmsId,
      isBound: mateEquipment.isBound,
      expireTimeUtc: mateEquipment.expireTimeUtc,
      enchantLv: mateEquipment.enchantLv,
    };
  }

  // --------------------------------------------------------------------------
  private addShipSlotItemToSync(sync: Sync, shipSlotItem: any): void {
    if (!sync.add.shipSlotItems) {
      sync.add.shipSlotItems = {};
    }
    sync.add.shipSlotItems[shipSlotItem.id] = {
      id: shipSlotItem.id,
      shipSlotCmsId: shipSlotItem.shipSlotCmsId,
      isBound: shipSlotItem.isBound,
      expireTimeUtc: shipSlotItem.expireTimeUtc,
      enchantLv: shipSlotItem.enchantLv,
    };
  }

  // --------------------------------------------------------------------------
  private addCargoToSync(sync: Sync, shipId: number, cmsId: number, quantity: number): void {
    if (!sync.add.ships) {
      sync.add.ships = {};
    }
    if (!sync.add.ships[shipId]) {
      sync.add.ships[shipId] = { cargos: {} };
    }
    sync.add.ships[shipId].cargos[cmsId] = {
      cmsId,
      quantity,
      pointInvested: 0,
    };
  }
}

// ----------------------------------------------------------------------------
class ManufactureReceiveSpec extends RewardAndPaymentSpec {
  constructor(
    private roomId: number,
    private slot: number,
    rewardCmsId: number,
    private manufactureType: number,
    private manufactureExp: number,
    private isGreatSuccess: boolean,
    private isFailure: boolean,
    private extra: string
  ) {
    // 실패한 경우에는 보상 없음 (rewardCmsId가 0이면 빈 배열 전달)
    super(rewardCmsId > 0 ? [{
      type: RNP_TYPE.REWARD_FIXED,
      cmsId: rewardCmsId,
      bIsAccum: true,
      bIsBound: !isGreatSuccess,
    }] : []);
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    const res = super.accumulate(user, tryData, changes);
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      return res;
    }

    // 제조 진행상황 삭제
    const deleteRes = opDeleteManufactureProgress(user, tryData, changes, this.roomId, this.slot);
    if (deleteRes !== CHANGE_TASK_RESULT.OK) {
      return deleteRes;
    }

    // 제조 경험치 추가
    const expRes = opAddManufactureExp(user, tryData, changes, this.manufactureType, this.manufactureExp);
    if (expRes !== CHANGE_TASK_RESULT.OK) {
      return expRes;
    }

    return CHANGE_TASK_RESULT.OK;
  }
}