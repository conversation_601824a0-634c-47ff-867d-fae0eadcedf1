/**
 * 백업/복원 관련 타입 정의
 */

export interface BackupOptions {
  /** 백업할 월드 ID (선택사항, 지정하지 않으면 모든 월드) */
  worldId?: string | undefined;
  /** 백업에 포함할 데이터베이스 타입들 */
  databases?: ('auth' | 'world' | 'user')[] | undefined;
  /** Redis 백업 포함 여부 */
  includeRedis?: boolean | undefined;
  /** 백업 설명 */
  description?: string | undefined;
  /** 압축 여부 */
  compress?: boolean | undefined;
}

export interface RestoreOptions {
  /** 복원할 백업 타임스탬프 */
  timestamp: string;
  /** 복원할 월드 ID (선택사항) */
  worldId?: string | undefined;
  /** 복원할 데이터베이스 타입들 */
  databases?: ('auth' | 'world' | 'user')[] | undefined;
  /** Redis 복원 포함 여부 */
  includeRedis?: boolean | undefined;
  /** 기존 데이터 덮어쓰기 확인 */
  force?: boolean | undefined;
}

export interface BackupMetadata {
  /** 백업 타임스탬프 */
  timestamp: string;
  /** 백업 생성 시간 */
  createdAt: Date;
  /** 백업 설명 */
  description?: string | undefined;
  /** 백업된 월드 목록 */
  worlds: string[];
  /** 백업된 데이터베이스 타입들 */
  databases: ('auth' | 'world' | 'user')[];
  /** Redis 백업 포함 여부 */
  includesRedis: boolean;
  /** 압축 여부 */
  compressed: boolean;
  /** 백업 파일 크기 (bytes) */
  size: number;
  /** 백업된 테이블 수 */
  tableCount: number;
  /** 백업된 Redis 키 수 */
  redisKeyCount: number;
}

export interface DatabaseBackupData {
  /** 데이터베이스 이름 */
  database: string;
  /** 테이블별 데이터 */
  tables: {
    [tableName: string]: {
      /** 테이블 스키마 정보 */
      schema: any;
      /** 테이블 데이터 */
      data: any[];
      /** 레코드 수 */
      recordCount: number;
      /** AUTO_INCREMENT 값 */
      autoIncrement?: number | null;
      /** 테이블 코멘트 */
      tableComment?: string | null;
      /** 스토리지 엔진 */
      engine?: string | null;
      /** 문자 집합 */
      collation?: string | null;
    };
  };
  /** 뷰 정의 */
  views: {
    [viewName: string]: {
      /** 뷰 생성 SQL */
      createSql: string;
      /** 뷰 정의 */
      definition: string;
      /** 뷰 코멘트 */
      comment?: string | null;
    };
  };
  /** 저장 프로시저 */
  procedures: {
    [procedureName: string]: {
      /** 프로시저 생성 SQL */
      createSql: string;
      /** 프로시저 정의 */
      definition: string;
      /** 프로시저 코멘트 */
      comment?: string | null;
    };
  };
  /** 함수 */
  functions: {
    [functionName: string]: {
      /** 함수 생성 SQL */
      createSql: string;
      /** 함수 정의 */
      definition: string;
      /** 함수 코멘트 */
      comment?: string | null;
    };
  };
  /** 트리거 */
  triggers: {
    [triggerName: string]: {
      /** 트리거 생성 SQL */
      createSql: string;
      /** 트리거 정의 */
      definition: string;
      /** 연결된 테이블 */
      tableName: string;
    };
  };
  /** 이벤트 */
  events: {
    [eventName: string]: {
      /** 이벤트 생성 SQL */
      createSql: string;
      /** 이벤트 정의 */
      definition: string;
      /** 이벤트 상태 */
      status: string;
    };
  };
}

export interface RedisBackupData {
  /** Redis 인스턴스 이름 */
  instance: string;
  /** 데이터베이스 번호 */
  database: number;
  /** 키-값 데이터 */
  data: {
    [key: string]: {
      /** 값 타입 */
      type: string;
      /** 값 데이터 */
      value: any;
      /** TTL (초, -1은 무제한) */
      ttl: number;
    };
  };
  /** 키 수 */
  keyCount: number;
}

export interface BackupResult {
  /** 백업 성공 여부 */
  success: boolean;
  /** 백업 메타데이터 */
  metadata: BackupMetadata;
  /** 백업 파일 경로 */
  backupPath: string;
  /** 에러 메시지 (실패시) */
  error?: string;
  /** 경고 메시지들 */
  warnings: string[];
  /** 백업 소요 시간 (ms) */
  duration: number;
}

export interface RestoreResult {
  /** 복원 성공 여부 */
  success: boolean;
  /** 복원된 백업 메타데이터 */
  metadata: BackupMetadata;
  /** 복원된 데이터베이스 수 */
  restoredDatabases: number;
  /** 복원된 테이블 수 */
  restoredTables: number;
  /** 복원된 Redis 키 수 */
  restoredRedisKeys: number;
  /** 에러 메시지 (실패시) */
  error?: string;
  /** 경고 메시지들 */
  warnings: string[];
  /** 복원 소요 시간 (ms) */
  duration: number;
}

export interface BackupListItem {
  /** 백업 타임스탬프 */
  timestamp: string;
  /** 백업 생성 시간 */
  createdAt: Date;
  /** 백업 설명 */
  description?: string | undefined;
  /** 백업 크기 (bytes) */
  size: number;
  /** 압축 여부 */
  compressed: boolean;
  /** 백업된 월드 수 */
  worldCount: number;
  /** 백업된 테이블 수 */
  tableCount: number;
  /** 백업된 Redis 키 수 */
  redisKeyCount: number;
}
