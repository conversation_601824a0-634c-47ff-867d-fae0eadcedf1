"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/saild-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const typedi_1 = __importDefault(require("typedi"));
const offlineSailingManager_1 = require("../offlineSailingManager");
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
// ----------------------------------------------------------------------------
// 오프라인 봇 접속 종료
// ----------------------------------------------------------------------------
router.on(protocol_1.SailProtocol.LB2SA_NTF_BOT_USER_DISCONNECT, async (req, res) => {
    const offlineSailingManager = typedi_1.default.get(offlineSailingManager_1.OfflineSailingManager);
    const userId = req.userId;
    const bot = offlineSailingManager.getBot(userId);
    if (bot) {
        bot.close(false);
    }
});
module.exports = router;
//# sourceMappingURL=sailPacketHandlerOffSail.js.map