// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { <PERSON>lientPacketHandler } from '../index';
import { LobbyService } from '../../server';
import { curTimeUtc } from '../../../motiflib/mutil';
import { Container } from 'typedi/Container';
import { GuildData } from '../../../motiflib/model/lobby';
import { Sync, UserLightInfo } from '../../type/sync';
import _ from 'lodash';
import tuGuildLeave from '../../../mysqllib/txn/tuGuildLeave';
import mhttp from '../../../motiflib/mhttp';
import cms from '../../../cms';
import { GuildLogUtil, GuildUtil } from '../../guildUtil';
import { RANKING_CMS_ID } from '../../../cms/rankingDesc';
import mconf from '../../../motiflib/mconf';
import { SdoGLogEvents } from '../../../motiflib/sdoGLogs.generated';

const rsn = 'guild_delete';
const add_rsn = null;
// ----------------------------------------------------------------------------
interface RequestBody {
  // empty
}

/**
 * 길드 해산
 */
// ----------------------------------------------------------------------------
export class Cph_Guild_Disband implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const { guildRedis, userCacheRedis, userDbConnPoolMgr, rankingManager, townRedis } =
      Container.get(LobbyService);
    const userId: number = user.userId;
    const guildId: number = user.userGuild.guildId;
    const now: number = curTimeUtc();

    if (!guildId) {
      throw new MError('there-is-no-guild-joined.', MErrorCode.GUILD_NOT_JOINED, {
        userId: user.userId,
      });
    }

    let guildData: GuildData;
    let userLightInfos: { [userId: number]: UserLightInfo };

    //======================================================================================================
    return (
      GuildUtil.GetGuildDataWithMemberLightInfo(user, guildId)
        .then((result) => {
          if (!result) {
            throw new MError('cannot-find-guild.', MErrorCode.GUILD_CANNOT_FIND_GUILD_IN_REDIS, {
              userId,
              guildId,
            });
          }

          guildData = result.guildData;
          userLightInfos = result.userLightInfos;

          const numMembers = _.keys(guildData.members).length;
          const numApplicants = _.keys(guildData.applicants).length;
          if (numMembers !== 1) {
            // 길드장만 있어야 해체 가능.
            throw new MError('guild-member-exists.', MErrorCode.GUILD_MEMBER_EXISTS, {
              userId,
              guildId,
              numMembers,
            });
          }

          if (numApplicants > 0) {
            throw new MError('guild-applicant-exists.', MErrorCode.GUILD_APPLICANT_EXISTS, {
              userId,
              guildId,
              numApplicants,
            });
          }
        })
        //======================================================================================================
        // 레디스에서 제거
        //======================================================================================================
        .then(() => {
          return guildRedis['deleteGuild'](guildId);
        })

        //======================================================================================================
        // RDB 길드 제거
        //======================================================================================================
        .then(() => {
          return tuGuildLeave(
            userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
            userId,
            0, // guildId,
            now
          );
        })
        //======================================================================================================
        // user cache redis에서 길드 제거
        //======================================================================================================
        .then(() => {
          userCacheRedis['setUserGuild'](user.userId, 0).catch((err) => {
            mlog.error('userCacheRedis setUserGuild is failed at guildDisband.', {
              err: err.message,
              userId: user.userId,
            });
          });
        })
        //======================================================================================================
        // 타운 길드 유저 점수 제거
        //======================================================================================================
        .then(() => {
          return townRedis['deleteTownGuildUserScore'](
            JSON.stringify(Object.keys(cms.Town)),
            guildId,
            user.userId,
            cms.Const.InvestGuildPointPer.value,
            cms.Const.InvestGuildSharePointPer.value
          );
        })
        //======================================================================================================
        // 타운 길드 점유 점수 제거를 위해 추가
        //======================================================================================================
        .then((result: string) => {
          if (result) {
            mlog.info('[UPDATE-GUILD-SCORE-BY-LEAVING] disband', {
              guildId,
              userId: user.userId,
              changes: JSON.parse(result),
            });
          }
          return townRedis['addGuildToDisbandedGuilds'](guildId);
        })

        //======================================================================================================
        // 응답
        //======================================================================================================
        .then(() => {
          // 랭킹에서 제외.
          rankingManager.removeRanking(RANKING_CMS_ID.GUILD_CONTRIBUTION, guildId, userId);

          const sync: Sync = user.userGuild.leaveGuild(user, true, true);

          gLog_guildDelete(user, guildId, guildData, userLightInfos);

          return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
        })
    );
  }
}

function gLog_guildDelete(
  user: User,
  guildId: number,
  guildData: GuildData,
  userLightInfos: { [userId: number]: UserLightInfo }
) {
  const guild_data = GuildLogUtil.buildGLogGuildSchema(guildId, guildData, userLightInfos);

  let nation: string = null;
  if (user.nationCmsId) {
    const nationCms = cms.Nation[user.nationCmsId];
    nation = nationCms ? nationCms.name : null;
  }

  user.glog('guild_delete', {
    nation,
    rsn,
    add_rsn,
    guild_data,
  });

  if (mconf.isSDO) {
    SdoGLogEvents.uwo_guild_glog({
      guild_id: guildId.toString(),
      guild_level: guild_data.lv,
      opt_type: 'delete',
      guild_name: guild_data.name,
      total_num: guild_data.members,
      total_exp: guildData.guild.exp,
      total_money: guildData.resources[cms.Const.GuildResourceMoneyCmsId.value], //TODO
      guild_buff: '<TODO>', //guild_data.buff,
      guild_intro: guildData.guild.introduction,
    });
  }
}
