"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
// Set process name.
process.name = 'testd';
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('ELASTIC_APM_SERVER_URL:', process.env.ELASTIC_APM_SERVER_URL);
console.log('ELASTIC_APM_LOG_LEVEL:', process.env.ELASTIC_APM_LOG_LEVEL);
// import apm from 'elastic-apm-node/start';
// console.log('apm started?', apm.isStarted());
// apm.start({
//   secretToken: '',
//   apiKey: '',
//   serverUrl: 'http://orca.motifgames.in:8200',
//   transactionIgnoreUrls: [],
//   transactionSampleRate: 1,
//   disableInstrumentations: [],
//   logLevel: 'trace',
//   serviceName: 'testd',
//   serviceNodeName: 'testd.0@DESKTOP-SOV6RIA',
//   captureExceptions: false,
//   active: true,
//   environment: undefined,
// });
// import * as mapm from '../motiflib/mapm';
// mapm.start();
const server = __importStar(require("./server"));
server.start();
['SIGTERM', 'SIGINT', 'SIGHUP', 'SIGQUIT'].forEach((signal) => {
    process.on(signal, server.stop);
});
//# sourceMappingURL=testd.js.map