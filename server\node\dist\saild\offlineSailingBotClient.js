"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfflineSailingBotClient = void 0;
const offlineSailingBotConnection_1 = __importDefault(require("./offlineSailingBotConnection"));
const proto = __importStar(require("../proto/lobby/proto"));
const typedi_1 = require("typedi");
const server_1 = require("./server");
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mutil = __importStar(require("../motiflib/mutil"));
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const lodash_1 = __importStar(require("lodash"));
const cmsEx = __importStar(require("../cms/ex"));
const sailConst_1 = require("./sailConst");
const reqSenders = __importStar(require("./offlineSailingBotReqSenders"));
const offlineSailingBotScenarioSailToDestination = __importStar(require("./offlineSailingBotScenarioSailToDestination"));
const CMSConst = __importStar(require("../cms/const"));
const gameState_1 = require("../motiflib/model/lobby/gameState");
const cms_1 = __importDefault(require("../cms"));
const pushNotificationDesc_1 = require("../cms/pushNotificationDesc");
const serverPushNotification_1 = require("./serverPushNotification");
const bot_1 = require("../motiflib/model/bot");
const const_1 = require("../motiflib/const");
const server_2 = require("./server");
const protocol_1 = require("../proto/saild-lobbyd/protocol");
const enum_1 = require("../motiflib/model/ocean/enum");
const ocean_1 = require("../motiflib/model/ocean");
const lobby_1 = require("../motiflib/model/lobby");
const pwAutoSailingUpdateIsOfflineSailingDeactivated_1 = __importDefault(require("../mysqllib/sp/pwAutoSailingUpdateIsOfflineSailingDeactivated"));
const ocean_2 = require("../motiflib/model/ocean");
//import { GAME_STATE } from '../lobbyd/userState';
//const { GAME_STATE } = require('../lobbyd/userState');
// ----------------------------------------------------------------------------
// Constants.
// ----------------------------------------------------------------------------
class BehaviorRequest {
    constructor() {
        this.actionType = 0;
        this.requiredPacketTypes = [];
        this.result = 0;
    }
    getRequiredPacketTypes() {
        return this.requiredPacketTypes;
    }
    setRequiredPacketTypes(values) {
        this.requiredPacketTypes = values;
    }
    get getActionType() {
        return this.actionType;
    }
    set setActionType(val) {
        this.actionType = val;
    }
    get getResult() {
        return this.result;
    }
    set setResult(val) {
        this.result = val;
    }
    reset() {
        this.setActionType = sailConst_1.BehaviorActionType.None;
        this.setResult = sailConst_1.BehaviorRequestResultType.Success;
        this.requiredPacketTypes = [];
    }
}
class OfflineSailingBotClient {
    constructor(autoSailingInfo) {
        this._isPushNotificationAllowed = 0; // 0 or 1
        this._isNightPushNotificationAllowed = 0; // 0 or 1
        this._allowedPushNotificationGroupIds = [];
        this._curRequest = new BehaviorRequest();
        this._lobbydUrl = undefined;
        this._sailId = undefined;
        this._isEncountOccurred = false;
        this._encountResult = enum_1.EncountResult.WAITING_FOR_CHOICE;
        this._battleId = undefined;
        this._battleParam = undefined;
        this._fleetShipIds = []; // 항해중인 함대의 선박Id들
        this._fleetSupplyEmptied = {}; // 보급품별 재고 소진여부[소진됨: true, 소진아님: false]
        this._stopReason = const_1.OFFLINE_SAILING_STOP_REASON.NONE;
        this._remainingPathForSupply = undefined; // Ocean.GET_USER_TOWN_STATE 에 이어서 보급 요청처리중인 경우
        // User info.
        this._accountId = autoSailingInfo.accountId;
        this._pubId = autoSailingInfo.pubId;
        this._userId = autoSailingInfo.userId;
        this._fleetIndex = autoSailingInfo.fleetIndex;
        this._userName = 'noname';
        this._lang = '';
        // auto sailing
        this.startTimeUtc = autoSailingInfo.startTimeUtc;
        this._destCmsId = autoSailingInfo.destCmsId;
        this._destType = autoSailingInfo.destType;
        this._path = JSON.parse(autoSailingInfo.path);
        this._extra = autoSailingInfo.extra ? JSON.parse(autoSailingInfo.extra) : undefined;
        this._optionForServer = autoSailingInfo.optionForServer
            ? JSON.parse(autoSailingInfo.optionForServer)
            : undefined;
        // Socket.
        this._conn = new offlineSailingBotConnection_1.default(this._accountId, this._pubId, this._userId);
        // State.
        this._botMode = sailConst_1.BotMode.LOGIN_MODE;
        this._loginPhase = sailConst_1.LoginPhase.UNAUTHORIZED;
        // sync Data
        this._syncAll = {};
        // battle
        // behavior tree setting
        this._behaviorStat = {
            waitCount: 0,
            waitMax: 0,
            startWaitTimeUtc: 0,
        };
        this.initScenario();
    }
    //----------------------------------------------------------
    // getter.setter
    //----------------------------------------------------------
    get SyncAll() {
        return this._syncAll;
    }
    get userId() {
        return this._userId;
    }
    get sailId() {
        return this._sailId;
    }
    set sailId(value) {
        this._sailId = value;
    }
    get destCmsId() {
        return this._destCmsId;
    }
    set destCmsId(value) {
        this._destCmsId = value;
    }
    get isEncountOccurred() {
        return this._isEncountOccurred;
    }
    set isEncountOccurred(value) {
        this._isEncountOccurred = value;
    }
    get battleId() {
        return this._battleId;
    }
    set battleId(value) {
        this._battleId = value;
    }
    get conn() {
        return this._conn;
    }
    //----------------------------------------------------------
    // member functions
    //----------------------------------------------------------
    getGameState() {
        if (this._syncAll && this._syncAll.user) {
            return this._syncAll.user.gameState;
        }
        return gameState_1.GAME_STATE.NONE;
    }
    getRawLastGameState() {
        if (this._syncAll && this._syncAll.user) {
            return this._syncAll.user.lastGameState;
        }
        return gameState_1.GAME_STATE.NONE;
    }
    //----------------------------------------------------------------------------------------------------
    // getLastGameState
    // gameState 는 유저의 현재 상태를 나타내며 로그인 시 NONE(0)으로 초기화 된다.
    // 하지만 유저의 마지막 상태를 유지 시켜줘야 될 필요가 있기에 lastGameState 가 존재하고
    // lastGameState 이 gameState 와 동일해지는 시점에 lastGameState 은 NONE 으로 초기화 된다.
    // 따라서 lastGameState 값이 NONE 일 경우 lastGameState 값은 gameState 을 통해 얻어오면 된다.
    //----------------------------------------------------------------------------------------------------
    getLastGameState() {
        const lastGameState = this.getRawLastGameState();
        if (lastGameState !== gameState_1.GAME_STATE.NONE) {
            return lastGameState;
        }
        else {
            return this.getGameState();
        }
        return undefined;
    }
    getGameEnterState() {
        if (this._syncAll && this._syncAll.user) {
            return this._syncAll.user.gameEnterState;
        }
        return gameState_1.GAME_ENTER_STATE.NONE;
    }
    isGameOver() {
        if (this._syncAll && this._syncAll.user) {
            return this._syncAll.user.bGameOver;
        }
        return false;
    }
    //----------------------------------------------------------
    isInTown(bIncludeEntering) {
        const gameState = this.getLastGameState();
        const gameEnterState = this.getGameEnterState();
        if (bIncludeEntering) {
            return gameState >= gameState_1.GAME_STATE.TOWN_MIN && gameState <= gameState_1.GAME_STATE.TOWN_MAX;
        }
        else {
            return (gameState >= gameState_1.GAME_STATE.TOWN_MIN &&
                gameState <= gameState_1.GAME_STATE.TOWN_MAX &&
                gameEnterState !== gameState_1.GAME_ENTER_STATE.ENTERING);
        }
    }
    //----------------------------------------------------------
    isInOceanVillage() {
        const gameState = this.getLastGameState();
        return gameState >= gameState_1.GAME_STATE.OCEAN_VILLAGE_MIN && gameState <= gameState_1.GAME_STATE.OCEAN_VILLAGE_MAX;
    }
    //----------------------------------------------------------
    isInPureOcean() {
        const gameState = this.getLastGameState();
        return gameState === gameState_1.GAME_STATE.IN_OCEAN;
    }
    //----------------------------------------------------------
    isInOcean(excludes = []) {
        const gameState = this.getLastGameState();
        const gameEnterState = this.getGameEnterState();
        for (const elem of excludes) {
            if (gameState === elem) {
                return false;
            }
        }
        return gameState >= gameState_1.GAME_STATE.OCEAN_MIN && gameState <= gameState_1.GAME_STATE.OCEAN_MAX;
    }
    //----------------------------------------------------------
    isInOceanBattle() {
        const gameState = this.getLastGameState();
        return gameState >= gameState_1.GAME_STATE.OCEAN_BATTLE_MIN && gameState <= gameState_1.GAME_STATE.OCEAN_BATTLE_MAX;
    }
    //----------------------------------------------------------
    isInOceanBattleReward() {
        const gameState = this.getLastGameState();
        return (gameState >= gameState_1.GAME_STATE.OCEAN_BATTLE_REWARD_MIN &&
            gameState <= gameState_1.GAME_STATE.OCEAN_BATTLE_REWARD_MAX);
    }
    //----------------------------------------------------------
    isWrecked() {
        if (this.SyncAll.user) {
            return this.SyncAll.user.bGameOver;
        }
        return false;
    }
    //----------------------------------------------------------
    isAutoSailingStopped() {
        return this._stopReason !== const_1.OFFLINE_SAILING_STOP_REASON.NONE;
    }
    //----------------------------------------------------------
    isAutoSailingPathLeft() {
        if (this._path) {
            return 0 < this._path.length;
        }
        return false;
    }
    //----------------------------------------------------------
    isAutoSailingAble() {
        let ret = this.isWrecked();
        if (ret) {
            return false;
        }
        if (!this.isInPureOcean()) {
            return false;
        }
        // is encount occured
        if (this.isEncountOccurred) {
            return false;
        }
        if (this.isAutoSailingStopped()) {
            return false;
        }
        return true;
    }
    isWorldBuffExist(buffCmsId) {
        const worldBuffs = this.SyncAll.worldBuffs;
        if (!worldBuffs) {
            return false;
        }
        const found = lodash_1.default.values(worldBuffs).find((buff) => {
            return (0, lodash_1.toNumber)(buff.cmsId) == buffCmsId;
        });
        mlog_1.default.info(`worldBuff [${buffCmsId}] :${found}`);
        return found != undefined;
    }
    //----------------------------------------------------------
    setGameOptionPushNotification(isAllowed, isNightAllowed, allowedPushNotificationGroupIds) {
        this._isPushNotificationAllowed = isAllowed;
        this._isNightPushNotificationAllowed = isNightAllowed;
        this._allowedPushNotificationGroupIds = allowedPushNotificationGroupIds;
    }
    //----------------------------------------------------------
    resetBehaviorStat() {
        this._behaviorStat.waitCount = 0;
        this._behaviorStat.waitMax = 0;
    }
    //----------------------------------------------------------
    getBehaviorStat() {
        return this._behaviorStat;
    }
    //----------------------------------------------------------
    getBehaviorRequestResult() {
        return this._curRequest.getResult;
    }
    //----------------------------------------------------------
    setLobbydUrl(lobbydUrl) {
        this._lobbydUrl = lobbydUrl;
        this.conn.setLobbydUrl(lobbydUrl);
    }
    //----------------------------------------------------------
    initGameData(resBody) {
        if (resBody.sailId) {
            this.sailId = resBody.sailId;
        }
        if (resBody.lang) {
            this._lang = resBody.lang;
        }
        const syncAll = resBody.sync.add;
        const user = syncAll.user;
        this._userName = user.name;
        this._isPushNotificationAllowed = resBody.isPushNotificationAllowed;
        this._isNightPushNotificationAllowed = resBody.isNightPushNotificationAllowed;
        this._allowedPushNotificationGroupIds = resBody.allowedPushNotificationGroupIds
            ? resBody.allowedPushNotificationGroupIds
            : null;
        this.setFleetShipIds(syncAll);
        this.initFleetSupplyLastState();
    }
    //----------------------------------------------------------
    setFleetShipIds(syncAll) {
        this._fleetShipIds = [];
        if (syncAll.ships) {
            lodash_1.default.forOwn(syncAll.ships, (ship) => {
                if (ship.fleetIndex !== this._fleetIndex) {
                    return;
                }
                this._fleetShipIds.push(ship.id);
            });
        }
    }
    //----------------------------------------------------------
    getShipCargos(shipId) {
        if (this._syncAll.ships) {
            const ship = this._syncAll.ships[shipId];
            if (ship && ship.cargos) {
                return ship.cargos;
            }
        }
        return undefined;
    }
    //----------------------------------------------------------
    isSupplyEmptied(shipId, cmsId) {
        const shipCargos = this.getShipCargos(shipId);
        if (shipCargos) {
            const cargo = shipCargos[cmsId];
            if (cargo && cargo.quantity) {
                if (cargo.quantity > 0) {
                    // mlog.info('[TEMP] isSupplyEmptied', {
                    //   userId: this._userId,
                    //   shipId,
                    //   cmsId,
                    //   quantity: cargo.quantity,
                    // });
                    return false;
                }
            }
        }
        return true;
    }
    //----------------------------------------------------------
    getSupplyQuantity(shipId, cmsId) {
        const shipCargos = this.getShipCargos(shipId);
        if (shipCargos) {
            const cargo = shipCargos[cmsId];
            if (cargo && cargo.quantity) {
                return cargo.quantity;
            }
        }
        return 0;
    }
    //----------------------------------------------------------
    isShipWaterEmptied(shipId) {
        return this.isSupplyEmptied(shipId, cmsEx.SUPPLY_CMS_ID.WATER);
    }
    //----------------------------------------------------------
    isShiFoodEmptied(shipId) {
        return this.isSupplyEmptied(shipId, cmsEx.SUPPLY_CMS_ID.WATER);
    }
    //----------------------------------------------------------
    isFleetWaterEmptied() {
        for (let k = 0; k < this._fleetShipIds.length; k++) {
            const shipId = this._fleetShipIds[k];
            if (!this.isShipWaterEmptied(k)) {
                return false;
            }
        }
        return true;
    }
    //----------------------------------------------------------
    isFleetSupplyEmptied(cmsId) {
        for (let k = 0; k < this._fleetShipIds.length; k++) {
            const shipId = this._fleetShipIds[k];
            if (!this.isSupplyEmptied(shipId, cmsId)) {
                return false;
            }
        }
        return true;
    }
    //----------------------------------------------------------
    getFleetSupplyQuantity(cmsId) {
        let sum = 0;
        for (let k = 0; k < this._fleetShipIds.length; k++) {
            const shipId = this._fleetShipIds[k];
            sum += this.getSupplyQuantity(shipId, cmsId);
        }
        return sum;
    }
    //----------------------------------------------------------
    initFleetSupplyLastState() {
        this._fleetSupplyEmptied = {};
        this.updateFleetSupplyEmptiedAll();
    }
    //----------------------------------------------------------
    updateFleetSupplyEmptiedAll() {
        this._updateFleetSupplyEmptied(cmsEx.SUPPLY_CMS_ID.WATER);
        this._updateFleetSupplyEmptied(cmsEx.SUPPLY_CMS_ID.FOOD);
        //this._updateFleetSupplyLastState(cmsEx.SUPPLY_CMS_ID.LUMBER);
        //this._updateFleetSupplyLastState(cmsEx.SUPPLY_CMS_ID.AMMO);
    }
    //----------------------------------------------------------
    isFleetSupplyEmptiedAll() {
        return (this._fleetSupplyEmptied[cmsEx.SUPPLY_CMS_ID.WATER] &&
            this._fleetSupplyEmptied[cmsEx.SUPPLY_CMS_ID.FOOD]);
    }
    //----------------------------------------------------------
    _updateFleetSupplyEmptied(cmsId) {
        this._fleetSupplyEmptied[cmsId] = this.isFleetSupplyEmptied(cmsId);
    }
    //----------------------------------------------------------
    _checkFleetSupplyNewlyEmptied(cmsId) {
        if (this._fleetSupplyEmptied[cmsId]) {
            return false;
        }
        const isEmpty = this.isFleetSupplyEmptied(cmsId);
        if (this._fleetSupplyEmptied[cmsId] !== isEmpty) {
            // mlog.info('[TEMP] _checkFleetSupplyNewlyEmptied occurred', {
            //   userId: this._userId,
            //   cmsId,
            // });
            return true;
        }
        return false;
    }
    //----------------------------------------------------------
    // 하나라도 새로 소진되면 알림 보내면서 전체 재고를 전달한다
    checkFleetSupplyNewlyEmptiedEvents() {
        let isNewlyEmptied = false;
        if (this._checkFleetSupplyNewlyEmptied(cmsEx.SUPPLY_CMS_ID.WATER)) {
            isNewlyEmptied = true;
        }
        if (this._checkFleetSupplyNewlyEmptied(cmsEx.SUPPLY_CMS_ID.FOOD)) {
            isNewlyEmptied = true;
        }
        if (isNewlyEmptied) {
            const waterSum = this.getFleetSupplyQuantity(cmsEx.SUPPLY_CMS_ID.WATER);
            const foodSum = this.getFleetSupplyQuantity(cmsEx.SUPPLY_CMS_ID.FOOD);
            let args = [`${foodSum}`, `${waterSum}`];
            this.processPushNotification(pushNotificationDesc_1.PUSH_NOTIFICATION_ID.SUPPLY_EXHAUSTED, args);
        }
    }
    //----------------------------------------------------------
    isShipUpdate(resBody) {
        if (resBody.sync && resBody.sync.add && resBody.sync.add.ships) {
            return true;
        }
        return false;
    }
    //----------------------------------------------------------
    isDisconnected() {
        return this._conn.isDisconnected();
    }
    //----------------------------------------------------------
    // 로그인과정에서 종료되더라도 lobbyd에 종료요청한다
    close(bSend = true) {
        if (this._conn) {
            return this._conn.disconnect(bSend);
        }
    }
    initScenario() {
        if (this._scenarioBehaviorTree) {
            delete this._scenarioBehaviorTree;
        }
        this._curRequest.reset();
        this.resetBehaviorStat();
        mlog_1.default.info(`applying default scenario [${sailConst_1.ScenarioType[sailConst_1.ScenarioType.SailToDestination]}] ...`);
        this._scenarioBehaviorTree = offlineSailingBotScenarioSailToDestination.createScenario(this);
    }
    //----------------------------------------------------------
    processPushNotification(pushId, args) {
        const pushCms = cms_1.default.PushNotification[pushId];
        if (!pushCms) {
            mlog_1.default.warn('invalid PushNotification id call designer', {
                userId: this._userId,
                pushId,
            });
            return;
        }
        // 유저의 알람 옵션 체크
        if (!(0, serverPushNotification_1.isAllowNotification)(pushCms.pushGroup, {
            isPushNotificationAllowed: this._isPushNotificationAllowed,
            isNightPushNotificationAllowed: this._isNightPushNotificationAllowed,
            allowedPushNotificationGroupIds: this._allowedPushNotificationGroupIds,
        })) {
            mlog_1.default.info('processPushNotification N/A', {
                userId: this._userId,
                pushId,
            });
            return;
        }
        try {
            // 전송할 메시지 선택
            const sourceMsg = (0, serverPushNotification_1.getPushMsg)(this._lang, pushCms);
            let resultArgs = [this._userName];
            if (args && args.length > 0) {
                resultArgs = resultArgs.concat(args);
                // mlog.info('[TEMP] sendPushNotification args', {
                //   userId: this._userId,
                //   args,
                //   resultArgs,
                // });
            }
            const sendMsg = mutil.stringFormat(sourceMsg, resultArgs);
            // mlog.verbose('[TEMP] processPushNotification result msg', {
            //   userId: this._userId,
            //   sendMsg,
            // });
            mhttp_1.default.platformApi
                .sendPushNotification(sendMsg, this._pubId)
                .then(() => {
                mlog_1.default.info('called sendPushNotification', {
                    userId: this._userId,
                    pushId,
                    lang: this._lang,
                    sendMsg,
                });
            })
                .catch((err) => {
                mlog_1.default.warn('sendPushNotification failed', {
                    userId: this._userId,
                    pushId,
                    err: err.message,
                    stack: err.stack,
                });
            });
        }
        catch (err) {
            mlog_1.default.error('sendPushNotification failed', {
                userId: this._userId,
                pushId,
                lang: this._lang,
                errormsg: err.message,
            });
        }
    }
    //----------------------------------------------------------
    getPushLocalForDisaster(disasterCmsId) {
        const pushLocalizeLookupTable = typedi_1.Container.get(server_1.SailService).pushLocalizeLookupTable;
        const pushLocalCms = pushLocalizeLookupTable.pushLocalizes['OceanDisaster'][disasterCmsId];
        if (pushLocalCms) {
            return (0, serverPushNotification_1.getPushLocal)(this._lang, pushLocalCms);
        }
        // 언어에 해당하는 재해이름이 없음
        mlog_1.default.warn('invalid PushLocalForDisaster', { disasterCmsId, lang: this._lang });
        return '';
    }
    //----------------------------------------------------------
    tick(curTimeInMs) {
        if (this._conn.isDisconnected()) {
            return;
        }
        switch (this._botMode) {
            case sailConst_1.BotMode.LOGIN_MODE:
                this._tickLoginMode();
                break;
            case sailConst_1.BotMode.SCENARIO_MODE:
                this._tickScenarioMode(curTimeInMs);
                break;
            default:
                // invalid mode
                break;
        }
    }
    //----------------------------------------------------------
    // Login Mode Processes
    //----------------------------------------------------------
    _tickLoginMode() {
        //handle login mode packets
        this.handleLoginModePacket();
        switch (this._loginPhase) {
            case sailConst_1.LoginPhase.UNAUTHORIZED:
                this.tickOnUnauthorized();
                break;
            case sailConst_1.LoginPhase.AUTHRIZING:
                break;
            case sailConst_1.LoginPhase.AUTHORIZED:
                this.tickOnAuthorized();
                break;
            case sailConst_1.LoginPhase.LOGGING_IN:
                this.tickOnLoggingIn();
                break;
            case sailConst_1.LoginPhase.AFTER_LOGGED_IN:
                this.tickOnAfterLoggedIn();
                break;
            case sailConst_1.LoginPhase.MAP_LOADING:
                break;
            case sailConst_1.LoginPhase.MAP_LOADING_COMPLETE:
                // end of login mode. changing to scenario mode
                this._botMode = sailConst_1.BotMode.SCENARIO_MODE;
                break;
            default:
                break;
        }
    }
    //----------------------------------------------------------
    // botClient의 LoginMode용 패킷핸들러는 이곳에 추가.
    handleLoginModePacket() {
        const packet = this._conn.popPacket();
        if (!packet || !packet.type) {
            return;
        }
        if (!this.updateSyncLoginMode(packet)) {
            this.close();
            return;
        }
        switch (packet.type) {
            // case proto.Auth.HELLO:
            //   this.onRecvHello(packet);
            //   break;
            // case proto.Auth.ENTER_WORLD_AS_BOT:
            //   this.onRecvEnterWorldAsBot(packet);
            //   break;
            case proto.Town.ENTER:
                this.onRecvTownEnter(packet);
                break;
            case proto.Town.LOAD_COMPLETE:
                this.onRecvTownLoadComplete();
                break;
            case proto.Ocean.ENTER:
                this.onRecvOceanEnter(packet);
                break;
            case proto.Ocean.LOAD_COMPLETE:
                this.onRecvOceanLoadComplete();
                break;
            default:
                // mlog.info('handlePacket unhandled packet', {
                //   userId: this._userId,
                //   packetType: packet.type,
                // });
                break;
        }
    }
    //----------------------------------------------------------
    // 항상 최신의 sync data를 업데이트 받아놓는다.
    updateSyncLoginMode(packet) {
        const resBody = JSON.parse(packet.body);
        if (resBody.sync && resBody.sync.add) {
            lodash_1.default.merge(this._syncAll, resBody.sync.add);
            return true;
        }
        if (resBody.errCode) {
            mlog_1.default.error('received error', {
                errCode: resBody.errCode,
                errMessage: resBody.errMessage ? resBody.errMessage : null,
            });
            return false;
        }
        return true;
    }
    //----------------------------------------------------------
    tickOnUnauthorized() {
        // authorization 은 job에서 처리하고 봇의 tick이 시작되므로 바로 인증완료상태로 진입.
        this._loginPhase = sailConst_1.LoginPhase.AUTHORIZED;
    }
    //----------------------------------------------------------
    tickOnAuthorized() {
        this._loginPhase = sailConst_1.LoginPhase.LOGGING_IN;
        //this._conn.reconnect();
        this.requestEnterWorldAsBot().catch((err) => {
            mlog_1.default.info('requestEnterWorldAsBot failed', { userId: this._userId });
            this.close();
            return;
        });
    }
    async requestEnterWorldAsBot() {
        const packet = new protocol_1.SailProtocol.SA2LB_REQ_ENTER_WORLD_AS_BOT();
        packet.userId = this._userId;
        packet.accountId = this._accountId;
        packet.pubId = this._pubId;
        packet.sailUrl = mconf_1.default.apiService.url;
        const response = await this.sendToLobbyAndRecv(packet);
        this.onRecvEnterWorldAsBot(response);
    }
    //----------------------------------------------------------
    tickOnLoggingIn() {
        if (gameState_1.GAME_STATE.TOWN_MIN > this.getLastGameState()) {
            return;
        }
        mlog_1.default.info('logged-in', { userId: this._userId });
        this._loginPhase = sailConst_1.LoginPhase.AFTER_LOGGED_IN;
    }
    //----------------------------------------------------------
    // 로그인완료 시점. 이곳에서 필요한 분기를 시킨다.
    //----------------------------------------------------------
    tickOnAfterLoggedIn() {
        // [todo] act differently based on the game state
        if (this.isInOcean()) {
            this.startOceanEnterProcess();
        }
        else {
            mlog_1.default.error('invalid gamestate', {
                userId: this._userId,
                gamestate: this.getLastGameState(),
            });
            this.close();
        }
    }
    //----------------------------------------------------------
    startTownEnterProcess() {
        this._loginPhase = sailConst_1.LoginPhase.MAP_LOADING;
        reqSenders.sendTownEnter(this);
    }
    //----------------------------------------------------------
    startOceanEnterProcess() {
        this._loginPhase = sailConst_1.LoginPhase.MAP_LOADING;
        reqSenders.sendOceanEnter(this);
    }
    //----------------------------------------------------------
    // login handlers
    //---------------------------------------------------
    // private onRecvHello(packet): void {
    //   // First take the double quotes from both ends of the 'body'.
    //   const peerPublicKey = packet.body.replace(/"/g, '');
    //   //this.conn.getCryptoCtx.computeSecret(Buffer.from(peerPublicKey, 'hex'));
    //   // Now send login.
    //   const body = {
    //     enterWorldToken: this.enterWorldToken,
    //     isDevLogin: 1,
    //     sessionToken: this.userName,
    //     lang: 'ko',
    //     reconnect: 0,
    //     deviceType: 'Windows',
    //   };
    //   const loginPacket = {
    //     type: proto.Auth.ENTER_WORLD_AS_BOT,
    //     seqNum: 0,
    //     body: JSON.stringify(body),
    //   };
    //   this.conn.sendJsonPacket(loginPacket, NaN);
    // }
    //----------------------------------------------------------
    onRecvEnterWorldAsBot(packet) {
        const resBody = JSON.parse(packet.body);
        if (resBody.kickReason) {
            mlog_1.default.info('onRecvEnterWorld kicked from server', {
                userId: this._userId,
                kickReason: resBody.kickReason,
            });
            this.close();
            return;
        }
        if (!this.updateSyncLoginMode(packet)) {
            mlog_1.default.info('onRecvEnterWorld failed', {
                userId: this._userId,
                errCode: resBody.errCode,
            });
            this.close();
            return;
        }
        this.initGameData(resBody);
        const user = resBody.sync.add.user;
        const gameState = this.getLastGameState();
        switch (gameState) {
            default:
                // 모의전 로비상태를 제외하고 해양인 경우 정상
                if (this.isInOcean([gameState_1.GAME_STATE.IN_OCEAN_BATTLE_VERSUS_LOBBY])) {
                    this._loginPhase = sailConst_1.LoginPhase.AFTER_LOGGED_IN;
                    break;
                }
                // 로그인은 성공했지만 gameState가 비정상인경우 오프항해가 무한반복하는 경우 방어
                // 원래대로라면 오프항해가 여기까지 오면 안된다
                Promise.resolve()
                    .then(() => {
                    mlog_1.default.warn('onRecvEnterWorld deactivating offsail', {
                        userId: this._userId,
                        gameState,
                        lastGameState: this.getLastGameState(),
                    });
                    const { worldDbConnPool } = typedi_1.Container.get(server_1.SailService);
                    return (0, pwAutoSailingUpdateIsOfflineSailingDeactivated_1.default)(worldDbConnPool.getPool(), this._userId, this._fleetIndex, 1 /**IsOfflineSailingDeactivated*/);
                })
                    .catch((err) => { })
                    .finally(() => {
                    this.close();
                });
                break;
        }
    }
    //----------------------------------------------------------
    //----------------------------------------------------------
    onRecvTownEnter(packet) {
        const body = JSON.parse(packet.body);
        if (body.errCode) {
            mlog_1.default.error('received error', {
                errCode: body.errCode,
                errMessage: body.errMessage ? body.errMessage : null,
            });
            this._conn.disconnect();
            return;
        }
        mlog_1.default.info('entered-town', { userId: this._userId });
        reqSenders.sendTownLoadComplete(this);
    }
    //----------------------------------------------------------
    onRecvTownLoadComplete() {
        this._loginPhase = sailConst_1.LoginPhase.MAP_LOADING_COMPLETE;
        mlog_1.default.info('loadcomplete-town', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvOceanEnter(packet) {
        const body = JSON.parse(packet.body);
        if (body.errCode) {
            mlog_1.default.error('received error', {
                errCode: body.errCode,
                errMessage: body.errMessage ? body.errMessage : null,
            });
            this._conn.disconnect();
            return;
        }
        this._battleId = body.battleId;
        mlog_1.default.info('entered-ocean', { userId: this._userId });
        reqSenders.sendOceanLoadComplete(this);
    }
    //----------------------------------------------------------
    onRecvOceanLoadComplete() {
        this._loginPhase = sailConst_1.LoginPhase.MAP_LOADING_COMPLETE;
        mlog_1.default.info('loadcomplete-ocean', { userId: this._userId });
        //디버깅용 [todo] 실제 버프가 존재할때만 보내도록 개선필요
        if (sailConst_1.CHEAT_ENCOUNT_PROTECTION_DISABLE) {
            // 38001010: 선단LV 10 미만 보호모드
            const protectBuffCmsId = 38001010;
            mlog_1.default.info('sendCheatWorldBuffRem protect buff.. removing..', {
                userId: this._userId,
                protectBuffCmsId,
            });
            reqSenders.sendCheatWorldBuffRem(this, protectBuffCmsId, 1);
        }
    }
    //----------------------------------------------------------
    //----------------------------------------------------------
    // Scenario Mode 함수들
    //----------------------------------------------------------
    _tickScenarioMode(curTimeInMs) {
        //handle scenario mode packets
        this.handleScenarioModePacket(curTimeInMs);
        this.tickOnPlaySecenario();
    }
    //----------------------------------------------------------
    // botClient의 ScenarioMode용 패킷핸들러는 이곳에 추가.
    handleScenarioModePacket(curTimeInMs) {
        const curTimeUtc = Math.floor(curTimeInMs / 1000);
        for (let k = 0; k < 5; k++) {
            const packet = this._conn.popPacket();
            if (!packet) {
                return;
            }
            if (!packet.type) {
                continue;
            }
            const resBody = JSON.parse(packet.body);
            if (!this.updateSyncScenarioMode(resBody, curTimeUtc)) {
                // [todo] 목적지 도착 상태인경우 푸시알람 전송(한번만 전송되는지 확인 필요)
                if (!this.isAutoSailingPathLeft()) {
                    mlog_1.default.warn('[handleScenarioModePacket] got error when path empty', {
                        userId: this._userId,
                    });
                }
                this.close();
                return;
            }
            if (this.isShipUpdate(resBody)) {
                // mlog.info('[TEMP] ShipUpdate', {
                //   userId: this._userId,
                //   ships: resBody.sync.add.ships,
                // });
                // firstly check for newly emptied supplies
                this.checkFleetSupplyNewlyEmptiedEvents();
                // update supply states
                this.updateFleetSupplyEmptiedAll();
            }
            // 물빵이 모두 소모된 경우 오프항해 중단
            if (this._stopReason === const_1.OFFLINE_SAILING_STOP_REASON.NONE && this.isFleetSupplyEmptiedAll()) {
                this._stopReason = const_1.OFFLINE_SAILING_STOP_REASON.WATER_FOOD_EXHAUSTED;
                mlog_1.default.info('water and food exhausted.. stopping offsail', {
                    userId: this._userId,
                });
            }
            // update latest request's state
            let ret = true;
            if (resBody.errCode) {
                ret = 0 == resBody.errCode ? true : false;
            }
            this.updateBehaviorRequest(packet.type, ret);
            //add individual packet handlers here
            switch (packet.type) {
                case proto.Ocean.ARRIVE:
                    this.onRecvArrive(packet);
                    break;
                case proto.Ocean.ADD_DISASTER_SC:
                    this.onRecvAddDisaster(packet);
                    break;
                case proto.Ocean.REMOVE_DISASTER_SC:
                    break;
                case proto.Ocean.RESOLVE_DISASTER_BY_DELEGATION_SC:
                    this.onRecvResolveDisasterByDelegation(packet);
                    break;
                case proto.Ocean.ENCOUNT_BY_NPC_SC:
                    this.onRecvEncountByNpcSc(packet);
                    break;
                case proto.Ocean.ENCOUNT_BY_NET_USER_SC:
                    this.onRecvEncountByNetUser(packet);
                    break;
                case proto.Ocean.ENCOUNT_USER_CHOICE:
                    this.onRecvEncountUserChoice(packet);
                    break;
                case proto.Ocean.ENCOUNT_ENEMY_CHOICE_SC:
                    this.onRecvEncountEnemyChoiceSc(packet);
                    break;
                case proto.Ocean.ENCOUNT_END_FORCEDLY_SC:
                    this.onRecvEncountEndForcedlySc(packet);
                    break;
                case proto.Ocean.ENCOUNT_CANCEL_SC:
                    this.onRecvEncountCancelSc(packet);
                    break;
                case proto.Ocean.ENCOUNT_END:
                    this.onRecvEncountEnd(packet);
                    break;
                case proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START:
                    this.RecvOfflineSailingMoveDelegateStart(packet);
                    break;
                case proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_UPDATE_SC:
                    this.onRecvOfflineSailingMoveDelegateUpdateSc(packet);
                    break;
                case proto.Ocean.OFFLINE_SAILING_STOP_SC:
                    this.onRecvOfflineSailingStopSc(packet);
                    break;
                case proto.Ocean.GET_USER_TOWN_STATE:
                    this.onRecvGetUserTownState(packet);
                    break;
                case proto.Ocean.APPLY_WAYPOINT_SUPPLY:
                    this.onRecvApplyWaypointSupply(packet);
                    break;
                case proto.Ocean.UPDATE_AUTO_SAILING:
                    this.onRecvUpdateAutoSailing(packet);
                    break;
                case proto.Ocean.UPDATE_AUTO_SAIL_OPTION_FOR_SERVER:
                    this.onRecvupdateAutoSailOptionForServer(packet);
                    break;
                case proto.Ocean.WRECK_FLEET_SC:
                    this.onRecvWreckFleetSc(packet);
                    break;
                case proto.Battle.RESUME:
                    this.onRecvBattleResume(packet);
                    break;
                case proto.Battle.END:
                    this.onRecvBattleEnd(packet);
                    break;
                default:
                    if (!bot_1.ResponseTypesNotToLog.includes(packet.type)) {
                        // mlog.info('handleScenarioModePacket unhandled packet', {
                        //   userId: this._userId,
                        //   packetType: packet.type,
                        //   typeStr: proto.toString(packet.type),
                        // });
                    }
                    break;
            }
        }
    }
    refreshEnterWorldToken(prevGameState, curGameState, curTimeUtc) {
        const prevScope = Math.floor(prevGameState / 100);
        const curScope = Math.floor(curGameState / 100);
        if (prevScope !== curScope) {
            // changed world?!.. refresh enterWorldToken
            mlog_1.default.info('refreshEnterWorldToken', {
                userId: this._userId,
                prevGameState,
                curGameState,
            });
            const { userCacheRedis } = typedi_1.Container.get(server_1.SailService);
            const newToken = mutil.generateEnterWorldToken(this._accountId);
            return userCacheRedis['setEnterWorldToken'](this._accountId, newToken, curTimeUtc);
        }
    }
    //----------------------------------------------------------
    // 항상 최신의 sync data를 업데이트 받아놓는다.
    updateSyncScenarioMode(resBody, curTimeUtc) {
        if (resBody.sync && resBody.sync.add) {
            const prevGameState = this.getGameState();
            lodash_1.default.merge(this._syncAll, resBody.sync.add);
            const curGameState = this.getGameState();
            this.refreshEnterWorldToken(prevGameState, curGameState, curTimeUtc);
            if (this.SyncAll.sailing && this.SyncAll.sailing.sailId) {
                this.sailId = this.SyncAll.sailing.sailId;
            }
            return true;
        }
        if (resBody.errCode) {
            mlog_1.default.error('received error', {
                userId: this._userId,
                errCode: resBody.errCode,
                errMessage: resBody.errMessage ? resBody.errMessage : null,
            });
            return false;
        }
        return true;
    }
    //----------------------------------------------------------
    updateBehaviorRequest(packetType, isSuccess) {
        let requiredPacketTypes = this._curRequest.getRequiredPacketTypes();
        if (0 == requiredPacketTypes.length) {
            return;
        }
        let found = false;
        let k = 0;
        for (; k < requiredPacketTypes.length; k++) {
            if (packetType == requiredPacketTypes[k]) {
                found = true;
                break;
            }
        }
        if (!found) {
            return;
        }
        if (!isSuccess) {
            //anyone fails all fail
            this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Failure;
            mlog_1.default.error('failed request', { userId: this._userId, packetType });
        }
        else {
            //if last packet is success, it's request is complete
            if (k == requiredPacketTypes.length - 1) {
                this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Success;
            }
            else {
                //send next request packet
                const nextPacketType = requiredPacketTypes[k + 1];
                if (!nextPacketType) {
                    return;
                }
                const func = reqSenders.sendMap[nextPacketType];
                if (func) {
                    mlog_1.default.debug('next request', {
                        userId: this._userId,
                        nextPacketType,
                        typeStr: proto.toString(nextPacketType),
                        func,
                    });
                    func(this);
                }
            }
        }
    }
    //----------------------------------------------------------
    tickOnPlaySecenario() {
        // [todo] act based on gamestate
        this._scenarioBehaviorTree.step();
        //console.log(this.scenarioBehaviorTree.lastRundData);
    }
    // ----------------------------------------------------------------------------
    sendToLobby(packet) {
        server_2.tcpServer.sendPacket(this._lobbydUrl, packet);
    }
    // ----------------------------------------------------------------------------
    async sendToLobbyAndRecv(packet) {
        return server_2.tcpServer.sendAndRecv(this._lobbydUrl, packet);
    }
    //----------------------------------------------------------
    onRecvArrive(packet) {
        //const body = JSON.parse(packet.body);
        mlog_1.default.info('arrived', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvAddDisaster(packet) {
        const body = JSON.parse(packet.body);
        if (body.sync.add && body.sync.add.sailing && body.sync.add.sailing.shipsInDisaster) {
            const shipsInDisaster = body.sync.add.sailing.shipsInDisaster;
            const disasterCmsIds = Object.values(shipsInDisaster);
            if (disasterCmsIds && disasterCmsIds.length > 0) {
                const cmsId = disasterCmsIds[0];
                const disasterName = this.getPushLocalForDisaster(cmsId);
                this.processPushNotification(pushNotificationDesc_1.PUSH_NOTIFICATION_ID.DISASTER_OCCURRED, [disasterName]);
            }
        }
        mlog_1.default.info('add-disaster', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvResolveDisasterByDelegation(packet) {
        //this.processPushNotification(PUSH_NOTIFICATION_ID.RESOLVE_DISASTER_BY_DELEGATION);
        mlog_1.default.info('resolved-disaster-by-delegation', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvEncountByNpcSc(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-by-npc', { userId: this._userId });
        //set encout flag
        this.isEncountOccurred = true;
    }
    //----------------------------------------------------------
    onRecvEncountByNetUser(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-by-user', { userId: this._userId });
        //set encout flag
        this.isEncountOccurred = true;
    }
    //----------------------------------------------------------
    onRecvEncountUserChoice(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-user-choice', { userId: this._userId });
        if (body.flowType) {
            this._encountResult = body.flowType;
        }
    }
    //----------------------------------------------------------
    onRecvEncountEnemyChoiceSc(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-enemy-choice', { userId: this._userId });
        if (body.encountResult) {
            this._encountResult = body.encountResult;
        }
    }
    //----------------------------------------------------------
    onRecvEncountEndForcedlySc(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-end-forcedly', { userId: this._userId });
        if (body.encountResult) {
            this._encountResult = body.encountResult;
        }
        if (body.battleParam) {
            this._battleParam = body.battleParam;
        }
        // encountEnd 를 보내고 난 이후의 흐름으로 진행하면 됨
        this.isEncountOccurred = false;
    }
    //----------------------------------------------------------
    onRecvEncountCancelSc(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('encount-canceled', { userId: this._userId });
        if (body.encountResult) {
            this._encountResult = body.encountResult;
        }
        if (body.battleParam) {
            this._battleParam = body.battleParam;
        }
        // encountEnd 를 보내고 난 이후의 흐름으로 진행하면 됨
        this.isEncountOccurred = false;
    }
    //----------------------------------------------------------
    onRecvWreckFleetSc(packet) {
        mlog_1.default.info('WreckFleetSc', { userId: this._userId });
    }
    //----------------------------------------------------------
    RecvOfflineSailingMoveDelegateStart(packet) {
        var _a;
        mlog_1.default.info('OfflineSailingMoveDelegateStart', { userId: this._userId });
        // 오프라인 항해는 최초 실행시에만 한번 푸시 발송한다
        if ((_a = this._optionForServer) === null || _a === void 0 ? void 0 : _a.bPushSent) {
            return;
        }
        this.processPushNotification(pushNotificationDesc_1.PUSH_NOTIFICATION_ID.OFF_SAIL_STARTED);
        // 서버로 this._optionForServer 내용을 업데이트 요청
        reqSenders.sendUpdateAutoSailOptionForServer(this, true);
    }
    //----------------------------------------------------------
    onRecvOfflineSailingMoveDelegateUpdateSc(packet) {
        const body = JSON.parse(packet.body);
        const remainingPath = body.remainingPath;
        mlog_1.default.verbose('OfflineSailingMoveDelegateUpdateSc', { userId: this._userId, remainingPath });
        const townCmsId = (0, ocean_2.checkWaypointSupply)(this._path, remainingPath);
        if (townCmsId) {
            // 보급 요청 전처리
            this._remainingPathForSupply = remainingPath;
            reqSenders.sendGetUserTownState(this, townCmsId);
        }
        else {
            // 그냥업데이트 요청
            reqSenders.sendUpdateAutoSailing(this, remainingPath);
        }
    }
    //----------------------------------------------------------
    onRecvGetUserTownState(packet) {
        // 보급요청 전처리 완료체크
        if (this._remainingPathForSupply) {
            mlog_1.default.info('GetUserTownState success. before supply', {
                userId: this._userId,
                remainingPath: this._remainingPathForSupply,
            });
            const townCmsId = (0, ocean_2.checkWaypointSupply)(this._path, this._remainingPathForSupply);
            if (townCmsId) {
                reqSenders.sendApplyWaypointSupply(this, this._remainingPathForSupply, townCmsId);
            }
        }
        else {
            mlog_1.default.error('GetUserTownState received. but _remainingPath is undefined', {
                userId: this._userId,
                remainingPath: this._remainingPathForSupply,
            });
        }
    }
    //----------------------------------------------------------
    onRecvApplyWaypointSupply(packet) {
        const body = JSON.parse(packet.body);
        const remainingPath = body.remainingPathPoints;
        mlog_1.default.info('onRecvApplyWaypointSupply', { userId: this._userId, remainingPath });
        this._path = 0 === remainingPath ? [] : this._path.slice(-remainingPath);
        this._remainingPathForSupply = undefined;
    }
    //----------------------------------------------------------
    onRecvUpdateAutoSailing(packet) {
        const body = JSON.parse(packet.body);
        const remainingPath = body.remainingPathPoints;
        mlog_1.default.verbose('onRecvUpdateAutoSailing', { userId: this._userId, remainingPath });
        this._path = 0 === remainingPath ? [] : this._path.slice(-remainingPath);
    }
    //----------------------------------------------------------
    onRecvOfflineSailingStopSc(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('onRecvOfflineSailingStopSc', { userId: this._userId, stopReason: body.reason });
        this._stopReason = body.reason;
    }
    //----------------------------------------------------------
    onRecvupdateAutoSailOptionForServer(packet) {
        const body = JSON.parse(packet.body);
        mlog_1.default.info('onRecvupdateAutoSailOptionForServer', {
            userId: this._userId,
            forServer: body.forServer,
        });
        if (!this._optionForServer) {
            this._optionForServer = {};
        }
        // _optionForServer 는 이곳에서만 업데이트한다
        this._optionForServer = body.forServer;
    }
    //----------------------------------------------------------
    onRecvEncountEnd(packet) {
        const body = JSON.parse(packet.body);
        this._battleParam = body.battleParam;
        if (this._battleParam) {
            // 딱한번만 서버푸시알람을 보낸다
            this.processPushNotification(pushNotificationDesc_1.PUSH_NOTIFICATION_ID.BATTLE_OCCURRED);
        }
        mlog_1.default.info('EncountEnd', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvBattleResume(packet) {
        const body = JSON.parse(packet.body);
        if (body.txnLog) {
            const battleParam = JSON.parse(body.txnLog[0]).battleParam;
            this._battleParam = battleParam;
            mlog_1.default.info('BattleResume updated battleParam', { userId: this._userId });
        }
        mlog_1.default.info('BattleResume ok', { userId: this._userId });
    }
    //----------------------------------------------------------
    onRecvBattleEnd(packet) {
        //const body: sync.Resp = JSON.parse(packet.body);
        const body = JSON.parse(packet.body);
        mlog_1.default.info('onRecvBattleEnd', { userId: this._userId });
        const battleHistory = {};
        if (body.sync && body.sync.add) {
            const battleEndResult = body.sync.add.battleEndResult;
            if (battleEndResult) {
                if (battleEndResult.ducatGains) {
                    battleHistory.addedDucat = battleEndResult.ducatGains;
                }
                if (battleEndResult.userExpGain) {
                    battleHistory.addedUserExp = battleEndResult.userExpGain;
                }
                if (battleEndResult.fameGain) {
                    battleHistory.fameGain = battleEndResult.fameGain;
                }
                if (battleEndResult.mateExpGains) {
                    battleEndResult.mateExpGains.forEach((mateExp) => {
                        if (!battleHistory.mates) {
                            battleHistory.mates = [];
                        }
                        battleHistory.mates.push({
                            mateCmsId: mateExp.mateCmsId,
                            addedExp: mateExp.amount,
                        });
                    });
                }
            }
            const battleRewards = body.sync.add.battleRewards;
            if (battleRewards) {
                battleRewards.forEach((reward) => {
                    if (reward.receivedQuantity) {
                        if (!battleHistory.rewards) {
                            battleHistory.rewards = [];
                        }
                        battleHistory.rewards.push({
                            type: reward.Type,
                            cmsId: reward.Id,
                            quantity: reward.receivedQuantity,
                        });
                    }
                });
            }
            const { sailRedis } = typedi_1.Container.get(server_1.SailService);
            Promise.resolve().then(async () => {
                if (Object.keys(battleHistory).length > 0) {
                    battleHistory.battleEndTimeUtc = mutil.curTimeUtc();
                    await sailRedis['setOfflineBattleHistories'](this._userId, JSON.stringify(battleHistory));
                    mlog_1.default.info('BattleEnd ok', {
                        userId: this._userId,
                        battleHistory,
                    });
                }
            });
        }
    }
    //----------------------------------------------------------
    //----------------------------------------------------------
    // BehaviorTree 함수들
    //----------------------------------------------------------
    _btWaitSet(count) {
        this._behaviorStat.waitCount = 0;
        this._behaviorStat.waitMax = count;
    }
    //----------------------------------------------------------
    btTickWait(count) {
        if (this._behaviorStat.waitCount < 0) {
            this._btWaitSet(count);
            return 0;
        }
        else {
            this._behaviorStat.waitCount++;
            if (this._behaviorStat.waitCount >= this._behaviorStat.waitMax) {
                this._behaviorStat.waitCount = -1;
                return 1;
            }
            return 0;
        }
    }
    //----------------------------------------------------------
    btTickWaitRandom(maxCount) {
        const val = Math.floor(Math.random() * maxCount) + 1;
        if (this._behaviorStat.waitCount < 0) {
            this._btWaitSet(val);
            return 0;
        }
        else {
            this._behaviorStat.waitCount++;
            if (this._behaviorStat.waitCount >= this._behaviorStat.waitMax) {
                this._behaviorStat.waitCount = -1;
                return 1;
            }
            return 0;
        }
    }
    _btWaitTimeoutSet(curTimeUtc, actionType) {
        this._behaviorStat.startWaitTimeUtc = curTimeUtc;
        this._curRequest.setActionType = actionType;
    }
    //----------------------------------------------------------
    btIsInTown() {
        return this.isInTown(true);
    }
    //----------------------------------------------------------
    btisInOceanVillage() {
        return this.isInOceanVillage();
    }
    //----------------------------------------------------------
    btIsInOcean() {
        return this.isInOcean();
    }
    //----------------------------------------------------------
    btIsInOceanLoading() {
        return (this.getLastGameState() === gameState_1.GAME_STATE.IN_OCEAN &&
            this.getGameEnterState() === gameState_1.GAME_ENTER_STATE.LOADING);
    }
    //----------------------------------------------------------
    btIsInOceanBattle() {
        mlog_1.default.debug('btIsInOceanBattle', { userId: this._userId });
        return this.isInOceanBattle();
    }
    //----------------------------------------------------------
    btIsNeedBattleResume() {
        mlog_1.default.debug('btIsNeedBattleResume', { userId: this._userId });
        return this.isInOceanBattle() && !this._battleParam && this._battleId;
    }
    //----------------------------------------------------------
    btIsWrecked() {
        return this.isWrecked();
    }
    //----------------------------------------------------------
    _btStartChangeToTown() {
        mlog_1.default.debug('btStartChangeToTown', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([
            proto.Ocean.ARRIVE,
            proto.Town.ENTER,
            proto.Town.LOAD_COMPLETE,
        ]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.MoveToTown;
        reqSenders.sendOceanArrive(this);
    }
    //----------------------------------------------------------
    btChangeToTown() {
        if (sailConst_1.BehaviorActionType.MoveToTown != this._curRequest.getActionType) {
            //init
            this._btStartChangeToTown();
            return 0;
        }
        else {
            //update
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return 0;
            }
            mlog_1.default.info('btChangeToTown fin', { userId: this._userId, ret });
            return 1;
        }
    }
    //----------------------------------------------------------
    _btStartChangeToVillage() {
        mlog_1.default.debug('btStartChangeToVillage', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Ocean.VILLAGE_ENTER]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.MoveToVillage;
        reqSenders.sendOceanVillageEnter(this);
    }
    //----------------------------------------------------------
    btChangeToVillage() {
        if (sailConst_1.BehaviorActionType.MoveToVillage != this._curRequest.getActionType) {
            //init
            this._btStartChangeToVillage();
            return 0;
        }
        else {
            //update
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return 0;
            }
            mlog_1.default.info('btChangeToVillage fin', { userId: this._userId, ret });
            return 1;
        }
    }
    //----------------------------------------------------------
    _btStartChangeToOcean() {
        mlog_1.default.debug('btStartChangeToOcean', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([
            proto.Town.ENTER_BUILDING,
            proto.Town.DEPART_DEPART,
            proto.Ocean.ENTER,
            proto.Ocean.LOAD_COMPLETE,
        ]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.MoveToOcean;
        reqSenders.sendTownEnterBuilding(this);
    }
    //----------------------------------------------------------
    btChangeToOcean() {
        if (sailConst_1.BehaviorActionType.MoveToOcean != this._curRequest.getActionType) {
            //init
            this._btStartChangeToOcean();
            return 0;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return 0;
            }
            mlog_1.default.info('btChangeToOcean fin', { userId: this._userId, ret });
            return 1;
        }
    }
    //----------------------------------------------------------
    _btStartMakeOceanLoadComplete() {
        mlog_1.default.debug('_btStartMakeOceanLoadComplete', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Ocean.LOAD_COMPLETE]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.MakeOceanLoadComplete;
        reqSenders.sendOceanLoadComplete(this);
    }
    //----------------------------------------------------------
    btMakeOceanLoadComplete() {
        if (sailConst_1.BehaviorActionType.MakeOceanLoadComplete != this._curRequest.getActionType) {
            //init
            this._btStartMakeOceanLoadComplete();
            return 0;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return 0;
            }
            mlog_1.default.info('btMakeOceanLoadComplete fin', { userId: this._userId, ret });
            return 1;
        }
    }
    //----------------------------------------------------------
    _btStartCheatTeleportTown() {
        mlog_1.default.debug('_btStartCheatTeleportTown', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Admin.TELEPORT_TOWN]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.ApplyCheat;
        reqSenders.sendCheatTeleportTown(this);
    }
    //----------------------------------------------------------
    btCheatTeleportTown() {
        if (sailConst_1.BehaviorActionType.ApplyCheat != this._curRequest.getActionType) {
            //init
            this._btStartCheatTeleportTown();
            return 0;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return 0;
            }
            mlog_1.default.info('btCheatTeleportTown fin', { userId: this._userId, ret });
            return 1;
        }
    }
    //----------------------------------------------------------
    btCheatRemoveProtectBuff() {
        mlog_1.default.debug('btCheatRemoveProtectBuff', { userId: this._userId });
        // 38001010: 선단LV 10 미만 보호모드
        const protectBuffCmsId = 38001010;
        if (!this.isWorldBuffExist(protectBuffCmsId)) {
            return sailConst_1.BehaviorRequestResultType.Failure;
        }
        mlog_1.default.debug('btCheatRemoveProtectBuff protect buff exist.. removing..', {
            userId: this._userId,
            protectBuffCmsId,
        });
        reqSenders.sendCheatWorldBuffRem(this, protectBuffCmsId, 1);
        return sailConst_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    btIsAutoSailingAble() {
        mlog_1.default.debug('btIsAutoSailingAble', { userId: this._userId });
        return this.isAutoSailingAble();
    }
    //----------------------------------------------------------
    _btStartAutoSailingToDestination() {
        mlog_1.default.debug('_btStartAutoSailingToDestination', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.AutoSailingToDestination;
        reqSenders.sendReqOfflineSailingMoveDelegateStart(this);
    }
    //----------------------------------------------------------
    btAutoSailingToDestination() {
        if (sailConst_1.BehaviorActionType.AutoSailingToDestination != this._curRequest.getActionType) {
            //init
            this._btStartAutoSailingToDestination();
            return 0;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return 0;
            }
            else if (sailConst_1.BehaviorRequestResultType.Failure == ret) {
                return 2;
            }
            // autosailing successfully started..
            //check if anything(encount, wreck, etc) has occoured
            if (!this.isAutoSailingAble()) {
                return 2;
            }
            //microjob.2. wait until the path list is empty
            if (this.isAutoSailingPathLeft()) {
                return 0;
            }
            mlog_1.default.info('btAutoSailingToDestination fin', { userId: this._userId, ret });
            return 1;
        }
    }
    //----------------------------------------------------------
    btLogout() {
        mlog_1.default.info('btLogout', { userId: this._userId });
        this.close();
    }
    //----------------------------------------------------------
    btIsDestTownExist() {
        mlog_1.default.debug('btIsDestTownExist', { userId: this._userId });
        if (this._destCmsId && lobby_1.AUTO_SAIL_DEST_TYPE.TOWN === this._destType) {
            return true;
        }
        return false;
    }
    //----------------------------------------------------------
    btIsDestVillageExist() {
        mlog_1.default.debug('btIsDestVillageExist', { userId: this._userId });
        if (this._destCmsId && lobby_1.AUTO_SAIL_DEST_TYPE.VILLAGE === this._destType) {
            return true;
        }
        return false;
    }
    //----------------------------------------------------------
    btIsEncountOccurred() {
        mlog_1.default.debug('btIsEncountOccurred', { userId: this._userId });
        return this.isEncountOccurred;
    }
    //----------------------------------------------------------
    _btStartEndAutoSailing() {
        mlog_1.default.debug('_btStartEndAutoSailing', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Ocean.END_AUTO_SAILING]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.EndAutoSailing;
        reqSenders.sendEndAutoSailing(this);
    }
    //----------------------------------------------------------
    btEndAutoSailing() {
        if (sailConst_1.BehaviorActionType.EndAutoSailing != this._curRequest.getActionType) {
            //init
            this._btStartEndAutoSailing();
            return 0;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return 0;
            }
            mlog_1.default.info('btEndAutoSailing fin', { userId: this._userId, ret });
            return 1;
        }
    }
    //----------------------------------------------------------
    btIsAutoSailingStopped() {
        mlog_1.default.debug('btIsAutoSailingStopped', { userId: this._userId });
        return this.isAutoSailingStopped();
    }
    //----------------------------------------------------------
    btIsAutoSailingCompleted() {
        mlog_1.default.debug('btIsAutoSailingCompleted', { userId: this._userId });
        return !this.isAutoSailingPathLeft() && !this.btIsDestTownExist();
    }
    //----------------------------------------------------------
    _btStartHandleEncountChoice() {
        mlog_1.default.debug('_btStartHandleEncountChoice', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Ocean.ENCOUNT_USER_CHOICE]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.HandleEncountChoice;
        this._encountResult = enum_1.EncountResult.WAITING_FOR_CHOICE;
        reqSenders.sendEncountUserChoice(this);
    }
    //----------------------------------------------------------
    btHandleEncountChoice() {
        mlog_1.default.debug('btHandleEncountChoice', { userId: this._userId });
        if (sailConst_1.BehaviorActionType.HandleEncountChoice != this._curRequest.getActionType) {
            //init
            this._btStartHandleEncountChoice();
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return ret;
            }
            else if (sailConst_1.BehaviorRequestResultType.Failure == ret) {
                return ret;
            }
            mlog_1.default.info('btHandleEncountChoice fin', { userId: this._userId, ret });
            return sailConst_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    btWaitForEncountEnemyChoice() {
        if (!this.isEncountOccurred) {
            mlog_1.default.info('btWaitForEncountEnemyChoice stops with fail', { userId: this._userId });
            return sailConst_1.BehaviorRequestResultType.Failure;
        }
        if (this._encountResult === enum_1.EncountResult.WAITING_FOR_CHOICE) {
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        const bIsBattle = (0, ocean_1.isEncountBattle)(this._encountResult);
        mlog_1.default.info('btWaitForEncountEnemyChoice fin', { userId: this._userId, bIsBattle });
        return sailConst_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    _btStartHandleEncountEnd() {
        mlog_1.default.debug('_btStartHandleEncountEnd', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Ocean.ENCOUNT_END]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.HandleEncountEnd;
        reqSenders.sendEncountEnd(this);
    }
    //----------------------------------------------------------
    btHandleEncountEnd() {
        mlog_1.default.debug('btHandleEncountEnd', { userId: this._userId });
        if (sailConst_1.BehaviorActionType.HandleEncountEnd != this._curRequest.getActionType) {
            //init
            this._btStartHandleEncountEnd();
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return ret;
            }
            else if (sailConst_1.BehaviorRequestResultType.Failure == ret) {
                return ret;
            }
            // 인카운트를 해소시킨다
            this.isEncountOccurred = false;
            mlog_1.default.info('btHandleEncountEnd fin', { userId: this._userId, ret });
            return sailConst_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    _btStartBattleResume() {
        mlog_1.default.debug('_btStartBattleResume', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Battle.RESUME]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.BattleResume;
        reqSenders.sendBattleResume(this);
    }
    //----------------------------------------------------------
    btBattleResume() {
        mlog_1.default.debug('btBattleResume', { userId: this._userId });
        if (sailConst_1.BehaviorActionType.BattleResume != this._curRequest.getActionType) {
            //init
            this._btStartBattleResume();
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return ret;
            }
            else if (sailConst_1.BehaviorRequestResultType.Failure == ret) {
                return ret;
            }
            mlog_1.default.info('btBattleResume fin', { userId: this._userId, ret });
            return sailConst_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    _btStartEnterBattle() {
        mlog_1.default.debug('_btStartEnterBattle', { userId: this._userId });
        const curGameState = this.getLastGameState();
        const curGameEnterState = this.getGameEnterState();
        let packetIds = [];
        if (gameState_1.GAME_STATE.IN_OCEAN_BATTLE == curGameState &&
            curGameEnterState === gameState_1.GAME_ENTER_STATE.ENTERING) {
            packetIds = [proto.Battle.START, proto.Battle.LOAD_COMPLETE];
        }
        else if (gameState_1.GAME_STATE.IN_OCEAN_BATTLE === curGameState &&
            curGameEnterState === gameState_1.GAME_ENTER_STATE.LOADING) {
            packetIds = [proto.Battle.LOAD_COMPLETE];
        }
        else {
            mlog_1.default.error('_btStartEnterBattle unhandled gamestate', { userId: this._userId, curGameState });
            return false;
        }
        this._curRequest.setRequiredPacketTypes(packetIds);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        const startPacketId = packetIds[0];
        const func = reqSenders.sendMap[startPacketId];
        if (func) {
            mlog_1.default.debug('_btStartEnterBattle sending', {
                userId: this._userId,
                startPacketId,
                typeStr: proto.toString(startPacketId),
                func,
            });
            this._curRequest.setActionType = sailConst_1.BehaviorActionType.EnterBattle;
            func(this);
        }
        else {
            return false;
        }
        return true;
    }
    //----------------------------------------------------------
    btEnterBattle() {
        mlog_1.default.debug('btEnterBattle', { userId: this._userId });
        if (sailConst_1.BehaviorActionType.EnterBattle != this._curRequest.getActionType) {
            //init
            if (!this._btStartEnterBattle()) {
                return sailConst_1.BehaviorRequestResultType.Failure;
            }
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return ret;
            }
            else if (sailConst_1.BehaviorRequestResultType.Failure == ret) {
                return ret;
            }
            mlog_1.default.info('btEnterBattle fin', { userId: this._userId, ret });
            return sailConst_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    _btStartApplySimpleBattleEnd() {
        mlog_1.default.debug('_btStartApplySimpleBattleEnd', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([proto.Battle.END]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.ApplySimpleBattleEnd;
        reqSenders.sendBattleEnd(this);
    }
    //----------------------------------------------------------
    btApplySimpleBattleEnd() {
        mlog_1.default.debug('btApplySimpleBattleEnd', { userId: this._userId });
        if (sailConst_1.BehaviorActionType.ApplySimpleBattleEnd != this._curRequest.getActionType) {
            //init
            this._btStartApplySimpleBattleEnd();
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return ret;
            }
            else if (sailConst_1.BehaviorRequestResultType.Failure == ret) {
                return ret;
            }
            mlog_1.default.info('btApplySimpleBattleEnd fin', { userId: this._userId, ret });
            return sailConst_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    btIsInOceanBattleReward() {
        mlog_1.default.debug('btIsInOceanBattleReward', { userId: this._userId });
        return this.isInOceanBattleReward();
    }
    //----------------------------------------------------------
    _btStartHandleReward() {
        mlog_1.default.debug('_btStartHandleReward', { userId: this._userId });
        this._curRequest.setRequiredPacketTypes([
            proto.BattleReward.ENTER,
            proto.BattleReward.LOAD_COMPLETE,
            proto.BattleReward.LEAVE,
        ]);
        this._curRequest.setResult = sailConst_1.BehaviorRequestResultType.Pending;
        this._curRequest.setActionType = sailConst_1.BehaviorActionType.HandleBattleReward;
        reqSenders.sendBattleRewardEnter(this);
    }
    //----------------------------------------------------------
    btHandleReward() {
        mlog_1.default.debug('btHandleReward', { userId: this._userId });
        if (sailConst_1.BehaviorActionType.HandleBattleReward != this._curRequest.getActionType) {
            //init
            this._btStartHandleReward();
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        else {
            const ret = this.getBehaviorRequestResult();
            if (sailConst_1.BehaviorRequestResultType.Pending == ret) {
                return ret;
            }
            else if (sailConst_1.BehaviorRequestResultType.Failure == ret) {
                return ret;
            }
            mlog_1.default.info('btHandleReward fin', { userId: this._userId, ret });
            return sailConst_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    btWaitForUserEnterBattleTimeout() {
        const curTimeUtc = mutil.curTimeUtc();
        if (sailConst_1.BehaviorActionType.WaitTimeout != this._curRequest.getActionType) {
            //init
            this._btWaitTimeoutSet(curTimeUtc, sailConst_1.BehaviorActionType.WaitTimeout);
            mlog_1.default.debug('btWaitForUserEnterBattleTimeout start', { userId: this._userId });
            return sailConst_1.BehaviorRequestResultType.Pending;
        }
        else {
            const waitTime = CMSConst.get('BattleEncounterUserWaitTime'); //sec
            if (this._behaviorStat.startWaitTimeUtc + waitTime > curTimeUtc) {
                return sailConst_1.BehaviorRequestResultType.Pending;
            }
            mlog_1.default.info('btWaitForUserEnterBattleTimeout fin', { userId: this._userId });
            return sailConst_1.BehaviorRequestResultType.Success;
        }
    }
    //----------------------------------------------------------
    btPushNotificationAccordingToStopReason() {
        let pushId = pushNotificationDesc_1.PUSH_NOTIFICATION_ID.IN_OCEAN_ANCHORED;
        switch (this._stopReason) {
            case const_1.OFFLINE_SAILING_STOP_REASON.WORLD_TILE_UNENTERABLE:
            case const_1.OFFLINE_SAILING_STOP_REASON.WATER_FOOD_EXHAUSTED:
                pushId = pushNotificationDesc_1.PUSH_NOTIFICATION_ID.SAIL_STOPPED;
                break;
            default:
                break;
        }
        this.processPushNotification(pushId);
        mlog_1.default.info('btPushNotificationAccordingToStopReason fin', { userId: this._userId, pushId });
        return sailConst_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    btPushNotificationOfAnchored() {
        this.processPushNotification(pushNotificationDesc_1.PUSH_NOTIFICATION_ID.IN_OCEAN_ANCHORED);
        mlog_1.default.info('btPushNotificationOfAnchored fin', { userId: this._userId });
        return sailConst_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    btPushNotificationOfArriveToDestination() {
        this.processPushNotification(pushNotificationDesc_1.PUSH_NOTIFICATION_ID.ARRIVED_TO_TOWN);
        mlog_1.default.info('btPushNotificationOfArriveToDestination fin', { userId: this._userId });
        return sailConst_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    btPushNotificationOfShipWrecked() {
        this.processPushNotification(pushNotificationDesc_1.PUSH_NOTIFICATION_ID.SHIP_WRECKED);
        mlog_1.default.info('btPushNotificationOfShipWrecked fin', { userId: this._userId });
        return sailConst_1.BehaviorRequestResultType.Success;
    }
    //----------------------------------------------------------
    // [todo] 미구현 BT API 들
    //----------------------------------------------------------
    btIsStartBotWaitTimeNotOver() {
        mlog_1.default.debug('btIsStartBotWaitTimeNotOver', { userId: this._userId });
        return false;
    }
    //----------------------------------------------------------
    btWaitForStartBotWaitTimeout() {
        mlog_1.default.debug('btWaitForStartBotWaitTimeout', { userId: this._userId });
        return false;
    }
}
exports.OfflineSailingBotClient = OfflineSailingBotClient;
exports.default = OfflineSailingBotClient;
//# sourceMappingURL=offlineSailingBotClient.js.map