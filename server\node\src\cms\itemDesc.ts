// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

import { ME<PERSON>r, MErrorCode } from '../motiflib/merror';
import { DISCOVERY_TYPE } from './DiscoveryDesc';

export enum ITEM_TYPE {
  COMSUMABLE = 1,
  MATERIAL = 2,
  BLUEPRINT = 3,
  PIECE = 4,
  RESOURCE = 5,
  ETC = 6,
  RANDOM_QUEST = 7,
  ROD = 8,
  BAIT = 9,
  LAND_EXPLORE = 16,
  ADVENTURE = 17,
  PROMOTION = 19,
  APPOINTMENT = 20,
  MANUAL = 21,
  RANDOM_BOX = 23,
  EMOTION = 24,
  MATE_POTION = 25,
  TRAINING_BOOK = 26,
  CHOICE_BOX = 27,
  KARMA_REDUCTION = 29,
  SLAVAGE = 35,
  BUFF_CONSUMABLE = 36, // 소모아이템 사용 시, 효과가 적용되는 아이템 타입

  GUILD_SYNTHESIS_MATERIAL = 32,
}

export enum FILTER_TYPE {
  COMSUMABLE = 1,
  MATERIAL = 2,
  BLUEPRINT = 3,
  PIECE = 4,
  RESOURCE = 5,
  ETC = 6,
  ADVENTURE = 7,
  MANUFACTURE = 8,
}

export enum USE_TYPE {
  ADD_BUFF = 1,
  REMOVE_BUFF = 2,
}

export enum ITEM_GRADE {
  S = 5,
  A = 4,
  B = 3,
  C = 2,
  D = 1,
}

export enum ITEM_REPORT_DIFFICULTY {
  EASY = 1,
  NORMAL = 2,
  HARD = 3,
}

export interface PreferenceItem {
  itemCmsId: number;
  difficulty: ITEM_REPORT_DIFFICULTY;
}

//useItem.ts에서 아이템 사용 시 후처리가 필요한 item 목록
export const SpareShipSailCmsId = 23000009; // 예비 돛
export const SpareShipWheelCmsId = 23000010; // 예비 키
export const SpareShipPaddleCmsId = 23400028; // 예비 노

// uninterest fields are omitted
export interface ItemDesc {
  id: number;
  name: string;
  type: ITEM_TYPE;
  filterType: FILTER_TYPE;
  mainGrade: ITEM_GRADE;
  mateExp: number;
  sellPrice: number;
  maxStack: number;
  isNotSell: boolean;
  isNotDelete: boolean;
  isSlotOccupied: boolean;
  isCashMarket: boolean;
  CashMarketLowerLimitPrice: number;
  CashMarketUpperLimitPrice: number;
  useType: USE_TYPE; // useItem 프로토콜에서 사용
  buffId: number;
  battleSkillId: number;
  pubGiftGrade: number;
  pubGiftJobType: number;
  pubGiftVal1: number;
  pubGiftVal2: number;
  canRefresh: boolean;
  burstGroups: number[];
  discoveryType: DISCOVERY_TYPE;
  discoveryAdventurerExp: number;
  discoveryAdventureFame: number; // 모험명성 지급량
  discoveryContributiveness: number; // 공헌도 지급량
  discoveryPoint: number;
  discoveryDucat: number;
  requiredLv: number; // [퀘스트 시작 아이템] 사용시 필요 레벨, https://jira.line.games/browse/UWO-9510
  timeCostValMin: number; // [가속권] 사용시 차감되는 시간(분)
  timeCostBuildingMenu: number[]; // [가속권] 사용할 수 있는 빌딩 메뉴
  havableCount: number;
  advFunc: number; // AdventureItremFunction cms id
  descFormatType: { target: number }[];
  custom: number;
  gachaRewardId: number; // 랜덤 상자 UWO-20216
  rewardNotice: boolean; // true 이면 획득시 브로드케스트 알림처리 https://jira.line.games/browse/UWO-20815
  consumeValue: number; //  ITEM_TYPE.KARMA_REDUCTION 일 경우 감소할 카르마 수치.
  choiceBoxGroup: number; // 선택 상자 보상 그룹
  enchantKeepRate: number; // 강화 실패 시 보호 확률
  enchantCEquipKeepRate: number; // 장비 강화 실패시 보호 확률
  enchantSlotKeepRate: number; // 부품 강화 실패시 보호 확률
  redueceSalvageScore: number;
  unlockKey: { Item: number; Count: number };
}

// ----------
// 중복되는 부분을 함수로 만들었는데 함수 내에서 if 검사 후 throw, return 를 하면
// 함수밖에서 narrow 기능을 못 쓰는 게 아쉬워서 '인터페이스 extends' 와 'asserts is' 기능을 사용했습니다.
// 필수적인 기능은 아닙니다.
interface QuestStartingItemDesc extends ItemDesc {
  type: ITEM_TYPE.RANDOM_QUEST;
}

/**
 * 퀘스트 시작 아이템( https://wiki.line.games/pages/viewpage.action?pageId=45790562 )인지 검증
 * @param itemCms
 */
export function ensureQuestStartingItem(
  itemCms: ItemDesc
): asserts itemCms is QuestStartingItemDesc {
  if (itemCms.type !== ITEM_TYPE.RANDOM_QUEST) {
    throw new MError('expected-quest-starting-item', MErrorCode.INTERNAL_ERROR);
  }
}

interface NonQuestStartingItemDesc extends ItemDesc {
  type: Exclude<ITEM_TYPE, ITEM_TYPE.RANDOM_QUEST>;
}

export function ensureNotQuestStartingItem(
  itemCms: ItemDesc
): asserts itemCms is NonQuestStartingItemDesc {
  if (itemCms.type === ITEM_TYPE.RANDOM_QUEST) {
    throw new MError('expected-non-quest-starting-item', MErrorCode.INTERNAL_ERROR);
  }
}
// ----------
