import fs from 'fs';
import path from 'path';

let _cachedOverridedBinaryCode: string | null = null;

export function getOverridedBinaryCode() {
  if (_cachedOverridedBinaryCode !== null) {
    return _cachedOverridedBinaryCode;
  }

  const localFilePath = path.resolve(path.join(__dirname, '..', '..', '..', '.override-binarycode'));
  let result: string;
  try {
    result = fs.readFileSync(localFilePath, 'utf8').trim();
  } catch (err) {
    result = '';
  }

  _cachedOverridedBinaryCode = result;
  return result;
}

export function getOverridedBinaryCodeSuffix() {
  const binaryCode = getOverridedBinaryCode();
  return (binaryCode && binaryCode.length > 0) ? `.${binaryCode.toLowerCase()}` : '';
}

export function resolveLocalDotJson5() {
  const suffix = getOverridedBinaryCodeSuffix();
  return `local${suffix}.json5`;
}
