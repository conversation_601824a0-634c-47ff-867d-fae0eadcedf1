// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import morgan from 'morgan';
import path from 'path';
import { Container, Service } from 'typedi';
import * as cms from '../cms';
import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import Pubsub from '../redislib/pubsub';
import * as townPubsub from './townPubsub';
import * as cmsEx from '../cms/ex';
import { TownPerfmonManager } from './townPerfmonManager';
import { PacketPerfmon, PacketRecvType, UnitPacketRecvStat } from '../motiflib/packetPerfmon';
import stoppable from 'stoppable';
import * as tcp from '../tcplib';
import { Segments } from '../tcplib/shared/segments';
import * as libTown from './libTown';
import * as Sentry from '@sentry/node';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------
@Service()
export class TownService {
  worldPubsub: Pubsub;
  private _pingInterval: NodeJS.Timeout;

  constructor() {}

  async init() {
    await startTogglet();

    this.worldPubsub = Container.of('pubsub-world').get(Pubsub);
    this.worldPubsub.init(mconf.getWorldConfig().worldPubsubRedis);
    townPubsub.init(this.worldPubsub);
  }

  async destroy() {
    await this.worldPubsub.quit();
    this._stopPing();

    stopTogglet();
  }

  startPing() {
    if (stopping) {
      return;
    }

    mlog.info('startPing', { pingInterval });

    this._pingInterval = setInterval(() => {
      const curTimeUtc = mutil.curTimeUtc();
      this._updateServerdPing(mconf.apiService.url, curTimeUtc);
    }, pingInterval);
  }

  private _stopPing() {
    if (this._pingInterval) {
      clearInterval(this._pingInterval);
    }
  }

  private async _updateServerdPing(appServiceUrl: string, curDate: number): Promise<void> {
    try {
      if (stopping) {
        return;
      }

      const resp = await mhttp.zonelbd.updateServerdPing({
        appServiceUrl,
        curDate,
        zoneType: cmsEx.ZoneType.TOWN,
      });

      if (resp.bStop) {
        Sentry.captureMessage('updateServerdPing signaled to stop. begin stopping townd server');

        mlog.warn('updateServerdPing signaled to stop. begin stopping server');
        stop();
      } else {
        // mlog.info('updateServerdPing succeeded.');
      }
    } catch (err) {
      mlog.warn(err.message);
    }
  }
}

// Main townd app.
const townApp = express();

townApp.disable('x-powered-by');
townApp.disable('etag');
townApp.disable('content-type');

const townServer = stoppable(http.createServer(townApp));
townServer.keepAliveTimeout = 0;

export const tcpServer = tcp.server();
tcpServer.routeEx(__dirname, './serverPacketHandler');
tcp.logger.setMoudle(mlog); // Setup tcp log writer

let stopping = false;
let pingInterval: number = 2000;

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

function townReqLog(tokens, req, res) {
  const netPerfmon = Container.get(PacketPerfmon);
  const unitPacketStat: UnitPacketRecvStat = {
    packetId: 0,
    packetIdStr: req.url,
    size: parseInt(tokens['res'](req, res, 'content-length'), 10),
    duration: parseFloat(tokens['response-time'](req, res)),
  };
  netPerfmon.addApiRecvStat(PacketRecvType.SERVER_API_RECEIVED, unitPacketStat);

  if (
    req.url === '/move' ||
    req.url === '/enterBuilding' ||
    req.url === '/leaveBuilding' ||
    req.url === '/health'
  ) {
    return;
  }

  mlog.info('townd-req', {
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  });
  return null;
}

async function closeServer() {
  return new Promise((resolve, reject) => {
    townServer.stop((err) => {
      if (err) return reject(err);
      resolve(null);
    });
  });
}

export async function stopServer() {
  try {
    mlog.info('stopping server ...');

    tcpServer.dispose();

    await unregisterServerd(mconf.apiService.url);

    // town service
    const townService = Container.get(TownService);
    await townService.destroy();

    const townPerfmonManager = Container.get(TownPerfmonManager);
    townPerfmonManager.stopPerfmonTick();

    await closeServer();
    await mutil.sleep(1001); // timeout of 1000ms exceeded

    mlog.info('server stopped');

    process.exitCode = 0;
  } catch (error) {
    mlog.error('graceful shutdown failed', { error: error.message });
    process.exit(1);
  }
}

async function unregisterServerd(appServiceUrl) {
  try {
    await mhttp.zonelbd.unregisterServerd({ appServiceUrl, zoneType: cmsEx.ZoneType.TOWN });
  } catch (err) {
    mlog.warn('Failed to unregister.', { err: err.message });

    if (stopping) {
      return;
    }

    await mutil.sleep(1001); // timeout of 1000ms exceeded
    await unregisterServerd(appServiceUrl);
  }
}

async function registerServerd(appServiceUrl, curDate) {
  try {
    if (stopping) {
      return;
    }

    const resp = await mhttp.zonelbd.registerServerd({
      appServiceUrl,
      curDate,
      zoneType: cmsEx.ZoneType.TOWN,
    });

    pingInterval = resp.pingInterval;

    mlog.info('registerServerd succeeded', { pingInterval, curDate });
  } catch (err) {
    mlog.warn('Failed to register.', { err: err.message });

    if (stopping) {
      return;
    }

    await mutil.sleep(1001); // timeout of 1000ms exceeded
    await registerServerd(appServiceUrl, mutil.curTimeUtc());
  }
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export async function start() {
  try {
    await mhttp.configd.registerInstance(
      process.env.WORLD_ID ? process.env.WORLD_ID : mconf.instance.worldId,
      mconf.appInstanceId,
      mconf.hostname
    );

    mutil.initSentry();

    // Init http clients.
    mhttp.init();

    cms.load();

    // town service
    const townService = Container.get(TownService);
    await townService.init();

    const bindAddress = mconf.apiService.bindAddress;
    const port = mconf.apiService.port;

    townApp.use(morgan(townReqLog));
    townApp.use(bodyParser.json());
    mutil.registerHealthCheck(townApp);
    mutil.registerGarbageCollector(townApp);

    await dirAsApi.register(townApp, path.join(__dirname, 'api'));
    townApp.use(expressError.middleware);

    const onDisconnected = (segment: Segments): void => {
      const disconnectedServerURL = segment.get('url');

      mlog.info('disconnected from lobbyd:', { url: disconnectedServerURL });

      // 접속종료된 로비서버와 연결된 유저를 제거한다.
      const townUsers = libTown.getTownUserAll();
      townUsers.forEach((townUser) => {
        if (disconnectedServerURL === townUser.lobbyUrl) {
          mlog.warn('disconnect lobbyd user', {
            userId: townUser.userId,
          });

          const townZone = townUser.getCurrentZone();
          if (townZone) {
            libTown.leaveTownZone(townZone, townUser);
          }
        }
      });
    };

    const tcpconf = mconf.apiService.tcpServer;
    tcpServer.start(tcpconf.port, tcpconf.ip, onDisconnected);

    const townPerfmonManager = Container.get(TownPerfmonManager);
    townPerfmonManager.startPerfmonTick();

    townServer.listen(port, bindAddress, () => {
      mlog.info('start listening ...', { bindAddress, port });
    });

    // config final sync
    const beforeVer = mconf.layoutVersion;
    await mhttp.configd.sync(beforeVer, isStopping, stop).then(() => {
      if (beforeVer < mconf.layoutVersion) {
        // do something
      }
    });

    await unregisterServerd(mconf.apiService.url);
    await registerServerd(mconf.apiService.url, mutil.curTimeUtc());

    townService.startPing();

    // start ping tick
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
}

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await stopServer();
  process.exit(0);
}
