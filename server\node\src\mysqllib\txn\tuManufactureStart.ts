// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Pool, PoolConnection } from 'promise-mysql';
import { withTxn } from '../mysqlUtil';
import puPointUpdate from '../sp/puPointUpdate';
import { PointChange } from '../../lobbyd/userPoints';
import { MError, MErrorCode } from '../../motiflib/merror';
import puMateEquipmentDelete from '../sp/puMateEquipmentDelete';
import puShipSlotItemDelete from '../sp/puShipSlotItemDelete';
import puManufactureProgressUpdate from '../sp/puManufactureProgressUpdate';
import { ItemChange } from '../../lobbyd/userInven';
import puItemsBatchUpdate from '../sp/puItemsBatchUpdate';
import { ManufactureProgress, ManufacturePointChange } from '../../lobbyd/userManufacture';
import { EnergyChange } from '../../lobbyd/userEnergy';
import puUserUpdateEnergy from '../sp/puUserUpdateEnergy';
import puUserUpdateManufacturePoint from '../sp/puUserUpdateManufacturePoint';
import puManufactureExpLevelUpdate from '../sp/puManufactureExpLevelUpdate';
import { ManufactureExpLevelChange } from '../../motiflib/model/lobby';
import { curTimeUtc } from '../../motiflib/mutil';
import puPointsBatchUpdate from '../sp/puPointsBatchUpdate';
import { ShipCargoChange } from '../../lobbyd/ship';
import puShipCargoUpdate from '../sp/puShipCargoUpdate';

function queryImpl(
  connection: PoolConnection,
  userId: number,
  manufactureProgress: ManufactureProgress | null,
  roomCmsId: number | null,
  userPointChanges: PointChange[],
  deleteMateEquipments: number[],
  deleteShipSlots: number[],
  itemChanges: ItemChange[],
  energyChange: EnergyChange,
  manufacturePointChange: ManufacturePointChange | null,
  shipCargoChange: ShipCargoChange[]
) {
  return Promise.resolve()

    .then(() => {
      if (energyChange) {
        return puUserUpdateEnergy(connection, userId, energyChange.energy, curTimeUtc());
      }
      return;
    })
    .then(() => {
      if (userPointChanges && userPointChanges.length > 0) {
        return puPointsBatchUpdate(connection, userId, userPointChanges);
      }
      return;
    })
    .then(() => {
      if (itemChanges && itemChanges.length > 0) {
        return puItemsBatchUpdate(connection, userId, itemChanges);
      }
      return;
    })
    .then(() => {
      if (deleteMateEquipments && deleteMateEquipments.length > 0) {
        return Promise.all(
          deleteMateEquipments.map((equipId) => puMateEquipmentDelete(connection, userId, equipId))
        );
      }
      return [];
    })
    .then(() => {
      if (deleteShipSlots && deleteShipSlots.length > 0) {
        return Promise.all(
          deleteShipSlots.map((shipSlotId) => puShipSlotItemDelete(connection, userId, shipSlotId))
        );
      }
      return [];
    })
    .then(() => {
      if (manufactureProgress && roomCmsId !== null) {
        // 여러 슬롯을 처리할 수 있도록 수정
        const slotPromises: Promise<any>[] = [];
        
        Object.keys(manufactureProgress).forEach((slotStr) => {
          const slot = parseInt(slotStr, 10);
          const slotData = manufactureProgress[slot];
          if (slotData) {
            slotPromises.push(
              puManufactureProgressUpdate(
                connection,
                userId,
                roomCmsId,
                slot,
                slotData
              )
            );
          }
        });
        
        if (slotPromises.length > 0) {
          return Promise.all(slotPromises);
        }
      }
    })
    .then(() => {
      if (manufacturePointChange) {
        return puUserUpdateManufacturePoint(
          connection,
          userId,
          manufacturePointChange.point,
          manufacturePointChange.lastUpdatePointTimeUtc
        );
      }
    })
    .then(() => {
      if (shipCargoChange && shipCargoChange.length > 0) {
        return Promise.all(
          shipCargoChange.map((cargoChange) => 
            puShipCargoUpdate(connection, userId, cargoChange)
          )
        );
      }
    })

    .catch((err) => {
      if (err instanceof MError) {
        throw err;
      } else {
        throw new MError(err.message, MErrorCode.GUILD_SYNTHESIS_CREATE_TXN_ERROR);
      }
    });
}

// 통합된 데이터 구조 타입 정의
interface ManufactureDbUpdateData {
  manufactureProgress: ManufactureProgress | null;
  roomCmsId: number | null;
  userPointChanges: PointChange[];
  deleteEquipItems: number[];
  deleteShipSlots: number[];
  itemChanges: ItemChange[];
  energyChange: EnergyChange;
  manufacturePointChange: ManufacturePointChange | null;
  shipCargoChanges: ShipCargoChange[];
}

// 통합된 데이터 구조를 받는 새로운 함수
export default function (
  dbConnPool: Pool,
  userId: number,
  updateData: ManufactureDbUpdateData
) {
  return withTxn(dbConnPool, __filename, (connection: PoolConnection) => {
    return queryImpl(
      connection,
      userId,
      updateData.manufactureProgress,
      updateData.roomCmsId,
      updateData.userPointChanges,
      updateData.deleteEquipItems,
      updateData.deleteShipSlots,
      updateData.itemChanges,
      updateData.energyChange,
      updateData.manufacturePointChange,
      updateData.shipCargoChanges
    );
  });
}