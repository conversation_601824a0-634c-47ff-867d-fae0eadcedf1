// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import schedule from 'node-schedule';
import Container from 'typedi';
import mhttp, { LobbyGroup } from '../../motiflib/mhttp';
import { Promise as promise } from 'bluebird';
import _ from 'lodash';

import mlog from '../../motiflib/mlog';
import * as mutil from '../../motiflib/mutil';
import { getUserDbShardId } from '../../mysqllib/mysqlUtil';
import { RealmService } from '../server';
import cms from '../../cms';
import { RewardFixedElemDesc } from '../../cms/rewardFixedDesc';
import { getCmsTableByRewardType, REWARD_TYPE } from '../../cms/rewardDesc';
import { Result as taLoadLastLobbyOfOnlineUsersResult } from '../../mysqllib/txn/taLoadLastLobbyOfOnlineUsers';
import { LineMailCmsId } from '../../cms/mailDesc';
import { DBConnPool } from '../../mysqllib/pool';
import { exchangeInvestSeasonReward } from '../../formula';

// https://developer.line.games/pages/viewpage.action?pageId=19600123

const BATCH_SIZE = 200;

interface Mail {
  gameUserId: number;
  title: string;
  contents: string;
  itemCd: string;
  itemCnt: number;
  expireYmdt: string;
}

function _exec(
  bulkMailId: number,
  count: number,
  successUserList: string[],
  failUserList: { userTarget: string; reasonCd: string }[]
): Promise<any> {
  const app = Container.get(RealmService);
  const { userDbConnPoolMgr, userRedis, monitorRedis } = app;

  const tmpArr = [];

  for (let i = 0; i < Math.ceil(count / BATCH_SIZE); i++) {
    tmpArr.push(0);
  }

  const curTimeUtc = mutil.curTimeUtc();
  let numMails = 0;
  return promise.reduce(
    tmpArr,
    () => {
      const successUserIdSet = new Set<number>();
      const userIdsByLobbydId: { [id: string]: number[] } = {};

      let bShouldPopBulkMailIfError = false;
      return userRedis['getBulkMailsByIdx'](bulkMailId, 0, BATCH_SIZE - 1)
        .then((ret) => {
          bShouldPopBulkMailIfError = true;
          const mails = JSON.parse(ret);
          numMails = mails.length;

          const mailByShard: { [shardId: string]: Mail[] } = {};
          for (const mailStr of mails) {
            const mail = JSON.parse(mailStr);
            const shardId = getUserDbShardId(mail.gameUserId);
            if (!mailByShard[shardId]) {
              mailByShard[shardId] = [];
            }
            mailByShard[shardId].push(mail);
          }

          const promises = [];
          const pools = userDbConnPoolMgr.getDbConnPools();
          for (const shardIdStr of Object.keys(pools)) {
            const pool: DBConnPool = pools[shardIdStr];

            if (!mailByShard[shardIdStr]) {
              continue;
            }

            let query =
              'INSERT INTO u_direct_mail_pendings (userId,cmsId,createTimeUtc,expireTimeUtc,title,body,attachment) VALUES ';

            const tryingUserIds = [];
            for (let i = 0; i < mailByShard[shardIdStr].length; i++) {
              if (i !== 0) {
                query += ',';
              }
              const elem = mailByShard[shardIdStr][i];

              const createTimeUtc = curTimeUtc;

              // https://developer.line.games/pages/viewpage.action?pageId=19600116
              // elem.expireYmdt 은 201910305959 와 같은 포멧.
              const expireYmdt = elem.expireYmdt;
              const expireDate = new Date(
                parseInt(expireYmdt.substring(0, 4), 10),
                parseInt(expireYmdt.substring(4, 6), 10) - 1,
                parseInt(expireYmdt.substring(6, 8), 10),
                parseInt(expireYmdt.substring(8, 10), 10),
                parseInt(expireYmdt.substring(10, 12), 10)
              );
              const expireTimeUtc = Math.floor(expireDate.getTime() / 1000);

              const attachment: RewardFixedElemDesc[] = [];

              const keyArr = elem.itemCd.split(':');
              if (keyArr.length !== 2) {
                failUserList.push({
                  userTarget: elem.gameUserId.toString(),
                  reasonCd: 'invalid-item-cd',
                });
                continue;
              }

              const rewardType = parseInt(keyArr[0], 10);

              if (!Object.values(REWARD_TYPE).includes(rewardType)) {
                failUserList.push({
                  userTarget: elem.gameUserId.toString(),
                  reasonCd: 'invalid-reward-type',
                });
                continue;
              }

              const cmsTable = getCmsTableByRewardType(rewardType);
              let cmsId: number;
              if (cmsTable) {
                if (!cmsTable[keyArr[1]]) {
                  failUserList.push({
                    userTarget: elem.gameUserId.toString(),
                    reasonCd: 'invalid-cms-id',
                  });
                  continue;
                }
                cmsId = parseInt(keyArr[1], 10);
              }

              const amount = elem.itemCnt;
              if (mutil.isNotANumber(amount)) {
                failUserList.push({
                  userTarget: elem.gameUserId.toString(),
                  reasonCd: 'invalid-item-cnt',
                });
                continue;
              }

              const rewardElem = {
                Type: rewardType,
                Id: cmsId ? cmsId : undefined,
                Quantity: amount,
              };
              if (rewardType === REWARD_TYPE.REWERD_SEASON_ITEMS) {
                const changeElem = exchangeInvestSeasonReward(rewardElem, curTimeUtc);
                attachment.push({
                  Type: changeElem.Type,
                  Id: changeElem.Id,
                  Quantity: changeElem.Quantity,
                });
              } else {
                attachment.push(rewardElem);
              }

              const attachmentStr = attachment.length > 0 ? JSON.stringify(attachment) : null;

              const values = `(${elem.gameUserId},${LineMailCmsId},FROM_UNIXTIME(${createTimeUtc}),FROM_UNIXTIME(${expireTimeUtc}),"${elem.title}","${elem.contents}",'${attachmentStr}')`;
              query += values;

              tryingUserIds.push(elem.gameUserId);
            }

            query += ';';

            const promise = pool
              .queryByStmt(query)
              .then(() => {
                mlog.info('[DEBUG] /bulkMail query', { query });
                for (const userId of tryingUserIds) {
                  successUserList.push(userId.toString());
                  successUserIdSet.add(userId);
                }
              })
              .catch((err) => {
                mlog.alert('/bulkMail query is failed', { err: err.message, query });
                for (const userId of tryingUserIds) {
                  failUserList.push({
                    userTarget: userId,
                    reasonCd: 'query-error',
                  });
                }
              });
            promises.push(promise);
          }

          return Promise.all(promises);
        })
        .then(() => {
          return userRedis['popBulkMail'](bulkMailId, numMails);
        })
        .then(() => {
          bShouldPopBulkMailIfError = false;
          if (successUserIdSet.size > 0) {
            return mhttp.authd.getLastLobbyOfOnlineUsers(Array.from(successUserIdSet));
          }
          return null;
        })
        .then((ret: taLoadLastLobbyOfOnlineUsersResult) => {
          if (ret && ret.userLobbies && ret.userLobbies.length > 0) {
            for (const userLobby of ret.userLobbies) {
              if (!userIdsByLobbydId[userLobby.lastLobby]) {
                userIdsByLobbydId[userLobby.lastLobby] = [];
              }
              userIdsByLobbydId[userLobby.lastLobby].push(userLobby.userId);
            }

            return monitorRedis['getLobbydUrls'](JSON.stringify(Object.keys(userIdsByLobbydId)));
          }
          return null;
        })
        .then((result) => {
          if (result) {
            const lobbyGroups: LobbyGroup = {};
            const lobbydUrls: { [lobbydId: string]: string } = JSON.parse(result);

            if (lobbydUrls) {
              for (const lobbydId of Object.keys(lobbydUrls)) {
                lobbyGroups[lobbydUrls[lobbydId]] = userIdsByLobbydId[lobbydId];
              }
            }

            if (Object.keys(lobbyGroups).length !== 0) {
              return mhttp.lobbypx.notifyPendingDirectMail(lobbyGroups, {});
            }
          }

          return null;
        })
        .catch((err) => {
          mlog.alert('BulkMail schedule is failed.', {
            bulkMailId,
            count,
            bShouldPopBulkMailIfError,
            err: err.message,
            stack: err.stack,
          });
          if (bShouldPopBulkMailIfError) {
            return userRedis['popBulkMail'](bulkMailId, BATCH_SIZE);
          }
        });
    },
    {}
  );
}

export function start(): schedule.Job {
  mlog.info('Scheduling job bulkMail...');

  let bInProcess = false;

  const cron = '* * * * *';
  const job = schedule.scheduleJob(cron, (fireDate: Date) => {
    if (!bInProcess) {
      const successUserList: string[] = [];
      const failUserList: { userTarget: string; reasonCd: string }[] = [];

      const app = Container.get(RealmService);
      const { userRedis } = app;
      return userRedis['getBulkMailId']()
        .then((ret) => {
          if (ret) {
            bInProcess = true;

            const bulkMailId = parseInt(ret[0], 10);
            const count = parseInt(ret[1], 10);

            return _exec(bulkMailId, count, successUserList, failUserList)
              .then(() => {
                mlog.info('BulkMail schedule is done.', {
                  bulkMailId,
                  count,
                  successUserList,
                  failUserList,
                });
                return mhttp.lgd.reportBulkMailResult(successUserList, failUserList, bulkMailId);
              })
              .then(() => {
                return userRedis['deleteBulkMailIdIfNoMail'](bulkMailId);
              })
              .then(() => {
                bInProcess = false;
              });
          }
        })
        .catch((err) => {
          mlog.alert('BulkMail schedule is failed.', {
            err: err.message,
            stack: err.stack,
          });
          bInProcess = false;
        });
    }
  });

  return job;
}
