CREATE PROCEDURE `mp_u_manufacture_recipe_unlock`(
  IN inUserId INT,
  IN inRecipeCmsId INT
)
label_body:BEGIN
  -- INSERT ... ON DUPLICATE KEY UPDATE 사용으로 한 번의 쿼리로 처리
  INSERT INTO u_manufacture_unlocked_recipes (
    userId,
    recipeCmsId,
    unlockTimeUtc
  ) VALUES (
    inUserId,
    inRecipeCmsId,
    FROM_UNIXTIME(UNIX_TIMESTAMP())
  )
  ON DUPLICATE KEY UPDATE
    unlockTimeUtc = VALUES(unlockTimeUtc);
    
  SELECT ROW_COUNT() as affectedRows, 
         CASE WHEN ROW_COUNT() = 1 THEN 'INSERT' ELSE 'UPDATE' END as operation;
END 