"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
// 레디스 함수에서 nil을 반환하는 경우 해당 lobbyd가 online상태가 아니라는 의미
// online 상태가 아닌경우의 처리 방침은 bStop 값 으로 true를 반환하여
// lobbyd 를 재시작 시킨다.
//
// updateLobbydPing api를 zonelbd에 위치시킨 이유는 realmd는 api에 대한 밸런싱이 불가능하기때문.
const typedi_1 = __importDefault(require("typedi"));
const server_1 = require("../server");
const merror_1 = require("../../motiflib/merror");
const mutil = __importStar(require("../../motiflib/mutil"));
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
module.exports = async (req, res) => {
    const { id, curTimeUtc: sentTs, worldId, userCount, botCount } = req.body;
    const curTs = mutil.curTimeUtc();
    const app = typedi_1.default.get(server_1.ZonelbService);
    const { monitorRedis } = app;
    mlog_1.default.info('[PING] recv', {
        body: req.body,
        curTs,
    });
    return monitorRedis['updateLobbydPing'](id, curTs, worldId, userCount, botCount)
        .then((online) => {
        let stop = false;
        const afterTs = mutil.curTimeUtc();
        if (online) {
            // 현재시간과 받은시간의 차이가 기준값 이상인 경우 로그남기기
            // 레디스 스크립트 호출완료에 걸리는 시간이 10초 이상이면 로그남기기
            const diff = curTs - sentTs;
            if (Math.abs(diff) > 2 || Math.abs(afterTs - curTs) > 10) {
                mlog_1.default.warn(`updateLobbydPing delayed`, {
                    id,
                    curTs,
                    sentTs,
                    afterTs,
                    diff,
                    online,
                    worldId,
                    userCount,
                    botCount,
                });
            }
        }
        else {
            stop = true;
            mlog_1.default.warn('[updateLobbydPing] lobbyd is not online', { id, curTs, sentTs, worldId });
        }
        const resp = {
            bStop: stop,
        };
        mlog_1.default.info('[PING] recv ack', {
            id,
            worldId,
            online,
            stop,
            curTs,
            afterTs,
        });
        res.json(resp);
    })
        .catch((err) => {
        mlog_1.default.warn('/updateLobbydPing error', { err: err.message, id });
        if (err instanceof merror_1.MError) {
            throw err;
        }
        else {
            let errCode = merror_1.MErrorCode.LOBBYD_SERVER_UPDATE_PING_ERROR;
            throw new merror_1.MError(err.message, errCode, err.message);
        }
    });
};
//# sourceMappingURL=updateLobbydPing.js.map