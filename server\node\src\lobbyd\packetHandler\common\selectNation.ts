// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';

import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { LobbyService } from '../../server';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { UserChangeSpec } from '../../UserChangeTask/commonChangeSpec';
import {
  TryData,
  Changes,
  CHANGE_TASK_RESULT,
  UserChangeTask,
  CHANGE_TASK_REASON,
} from '../../UserChangeTask/userChangeTask';
import { opSetNation, opAddPoint } from '../../UserChangeTask/userChangeOperator';
import { Resp } from '../../type/sync';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import mlog from '../../../motiflib/mlog';
import { RewardData } from '../../../motiflib/gameLog';
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import { curTimeUtc } from '../../../motiflib/mutil';
import { ClientPacketHandler } from '../index';

// ----------------------------------------------------------------------------
// !! 이민 => changeNation 패킷으로 대체 되었습니다.
// ----------------------------------------------------------------------------

const rsn = 'select_nation';
const add_rsn = null;

interface Request {
  nationCmsId: number;
}

interface Response {
  sync?: {
    add?: {
      user?: {
        nationCmsId?: number;
      };
    };
  };
  bRestricted?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_SelectNation implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    // validate connection state
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const body: Request = packet.bodyObj;

    const { nationCmsId } = body;
    const { nationManager } = Container.get(LobbyService);

    // validate request body
    if (nationCmsId === 0 || !nationManager.has(nationCmsId)) {
      throw new MError('no-key-in-nation-cms', MErrorCode.NO_KEY_IN_CMS);
    }

    const nation = nationManager.get(nationCmsId);
    // 1. 유저의 조건이 국가 선택 조건과 맞아야 합니다.
    // 기획에서 레벨 조건 필요없다고 함
    // if (user.level < cms.Const.NationChooseFleetLv.value) {
    //   throw new MError('nation-select-not-allowed', MErrorCode.NATION_SELECT_NOT_ALLOWED_ERROR, {
    //     level: user.level,
    //   });
    // }

    // 2. 왕궁이 있는 도시인지 체크
    if (user.userTown.getTownBldg() !== cmsEx.BUILDING_TYPE.PALACE) {
      throw new MError('not-palace-town', MErrorCode.NOT_PALACE_TOWN, {
        body,
      });
    }

    // 3. 왕궁의 언어레벨 조건을 만족 해야 한다
    user.userContentsTerms.ensureBuildingContentsUnlock(
      cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.CHANGE_NATION,
      user
    );

    // 4. 첫 망명 인지 체크
    if (user.lastUpdateNationTimeUtc) {
      throw new MError('already-change-nation', MErrorCode.INVALID_CHANGE_NATION, {
        body,
        lastUpdateNationTimeUtc: user.lastUpdateNationTimeUtc,
      });
    }

    // 해당 국가가 선택 제한 상태가 아니어야 합니다.
    if (nation.isRestricted(nationManager)) {
      return user.sendJsonPacket<Response>(packet.seqNum, packet.type, { bRestricted: true });
    }

    const reward_data: RewardData[] = [];
    const changeTask = new UserChangeTask(
      user,
      CHANGE_TASK_REASON.SELECT_NATION,
      new SelectNationSpec(
        nationCmsId,
        curTimeUtc(),
        nationManager.getPowerRank(nationCmsId),
        reward_data
      )
    );
    const res = changeTask.trySpec();
    if (res !== CHANGE_TASK_RESULT.OK) {
      throw new MError('try-spec-failed', MErrorCode.TRY_SELECT_NATION_SPEC_FAILED, {
        res,
      });
    }
    const resp: Resp = {};
    return changeTask.apply().then((sync) => {
      resp.sync = sync;

      const townInfo = user.userTown.getTownInfo();
      if (townInfo) {
        const townApi = mhttp.townpx.channel(townInfo.url);
        townApi.updateTownUserSyncData(user.userId, { user: { nationCmsId } }).catch((err) => {
          mlog.error('Town api updateTownUserSyncData is failed.', {
            userId: user.userId,
            err: err.message,
          });
        });
      }

      mhttp.authd.changeUserNation(user.userId, nationCmsId).catch((err) => {
        mlog.error('Auth api changeUserNation is failed.', {
          userId: user.userId,
          nationCmsId,
          err: err.message,
        });
      });
      mhttp.chatd.updateVolanteUser(user);

      // glog
      user.glog('nation_change', {
        rsn,
        add_rsn,
        old_nation: null,
        cur_nation: cms.Nation[nationCmsId].name,
        pr_data: null,
        reward_data,
      });

      return user.sendJsonPacket<Response>(packet.seqNum, packet.type, resp);
    });
  }
}

class SelectNationSpec implements UserChangeSpec {
  constructor(
    private nationCmsId: number,
    private lastUpdateNationTimeUtc: number,
    private nationRank: number,
    private reward_data: RewardData[]
  ) {}
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    let res = opSetNation(user, tryData, changes, this.nationCmsId, this.lastUpdateNationTimeUtc);
    if (res === CHANGE_TASK_RESULT.OK) {
      if (this.nationRank) {
        const nationRankingEffectCms =
          cms.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.SELECT_NATION];
        const nationRankBonus = nationRankingEffectCms.rankingEffectVal[this.nationRank - 1];
        if (nationRankBonus) {
          res = opAddPoint(
            user,
            tryData,
            changes,
            cms.Const.NationPickBonusPointId.value,
            nationRankBonus,
            false,
            false,
            undefined,
            false
          );
          this.reward_data.push({
            type: REWARD_TYPE[REWARD_TYPE.POINT],
            id: cms.Const.NationPickBonusPointId.value,
            uid: null,
            amt: nationRankBonus,
          });
        }
      }
    }
    return res;
  }
}
