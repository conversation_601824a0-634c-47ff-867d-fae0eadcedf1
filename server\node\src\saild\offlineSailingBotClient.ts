// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
import { OfflineBattleHistories } from '../motiflib/model/lobby';
import OfflineSailingBotConnection, {
  State as ConnectionState,
} from './offlineSailingBotConnection';
import * as proto from '../proto/lobby/proto';
import { Container } from 'typedi';
import { SailService } from './server';
import mlog from '../motiflib/mlog';
import mconf from '../motiflib/mconf';
import * as mutil from '../motiflib/mutil';
import mhttp from '../motiflib/mhttp';
import * as sync from '../lobbyd/type/sync';
import _, { toNumber } from 'lodash';
import { BehaviorTree } from 'behaviortree';
import * as cmsEx from '../cms/ex';
import { Result as AutoSailingInfo } from '../mysqllib/sp/pwAutoSailingScan';
import {
  LoginPhase,
  BehaviorActionType,
  BehaviorRequestResultType,
  BotMode,
  ScenarioType,
  CHEAT_ENCOUNT_PROTECTION_DISABLE,
} from './sailConst';
import * as reqSenders from './offlineSailingBotReqSenders';
import * as offlineSailingBotScenarioSailToDestination from './offlineSailingBotScenarioSailToDestination';
import * as CMSConst from '../cms/const';
import { GAME_ENTER_STATE, GAME_STATE } from '../motiflib/model/lobby/gameState';
import cms from '../cms';
import { PUSH_NOTIFICATION_ID } from '../cms/pushNotificationDesc';

import { isAllowNotification, getPushMsg, getPushLocal } from './serverPushNotification';
import { ResponseTypesNotToLog } from '../motiflib/model/bot';
import { OFFLINE_SAILING_STOP_REASON } from '../motiflib/const';
import { tcpServer } from './server';
import { IPacket } from '../tcplib/shared/basic-packets';
import { SailProtocol } from '../proto/saild-lobbyd/protocol';
import { EncountResult } from '../motiflib/model/ocean/enum';
import { isEncountBattle } from '../motiflib/model/ocean';
import { AUTO_SAIL_DEST_TYPE } from '../motiflib/model/lobby';
import pwAutoSailingUpdateIsOfflineSailingDeactivated from '../mysqllib/sp/pwAutoSailingUpdateIsOfflineSailingDeactivated';
import {
  PathEx,
  checkWaypointSupply,
  WaypointExtra,
  AutoSailOptionForServer,
} from '../motiflib/model/ocean';

//import { GAME_STATE } from '../lobbyd/userState';
//const { GAME_STATE } = require('../lobbyd/userState');

// ----------------------------------------------------------------------------
// Constants.
// ----------------------------------------------------------------------------
class BehaviorRequest {
  private actionType: BehaviorActionType = 0;
  private requiredPacketTypes: number[] = [];
  private result: BehaviorRequestResultType = 0;

  constructor() {}

  getRequiredPacketTypes() {
    return this.requiredPacketTypes;
  }
  setRequiredPacketTypes(values: number[]) {
    this.requiredPacketTypes = values;
  }

  get getActionType() {
    return this.actionType;
  }
  set setActionType(val: BehaviorActionType) {
    this.actionType = val;
  }

  get getResult() {
    return this.result;
  }
  set setResult(val: BehaviorRequestResultType) {
    this.result = val;
  }

  reset() {
    this.setActionType = BehaviorActionType.None;
    this.setResult = BehaviorRequestResultType.Success;
    this.requiredPacketTypes = [];
  }
}

interface BehaviorStat {
  waitCount: number;
  waitMax: number;
  startWaitTimeUtc: number;
}

interface FleetSupplyEmptied {
  [supplyCmsId: number]: boolean;
}

export class OfflineSailingBotClient {
  private _accountId: string;
  private _pubId: string;
  private _userId: number;

  private _fleetIndex: number;
  private _userName: string;
  private _lang: string;

  private _isPushNotificationAllowed: number = 0; // 0 or 1
  private _isNightPushNotificationAllowed: number = 0; // 0 or 1
  private _allowedPushNotificationGroupIds: number[] = [];

  private _conn: OfflineSailingBotConnection;
  private _botMode: BotMode;
  private _loginPhase: LoginPhase;
  private _syncAll: sync.All;
  private _behaviorStat: BehaviorStat;
  private _curRequest: BehaviorRequest = new BehaviorRequest();
  private _scenarioBehaviorTree: BehaviorTree;
  private _lobbydUrl: string = undefined;

  private _sailId: number = undefined;
  private startTimeUtc: number;
  private _destCmsId: number;
  private _destType: number;
  private _path: PathEx[];
  private _extra: WaypointExtra;
  private _optionForServer: AutoSailOptionForServer;
  private _isEncountOccurred: boolean = false;
  private _encountResult: EncountResult = EncountResult.WAITING_FOR_CHOICE;
  private _battleId: number = undefined;
  private _battleParam: any = undefined;
  private _fleetShipIds: number[] = []; // 항해중인 함대의 선박Id들
  private _fleetSupplyEmptied: FleetSupplyEmptied = {}; // 보급품별 재고 소진여부[소진됨: true, 소진아님: false]
  private _stopReason: OFFLINE_SAILING_STOP_REASON = OFFLINE_SAILING_STOP_REASON.NONE;
  private _remainingPathForSupply: number = undefined; // Ocean.GET_USER_TOWN_STATE 에 이어서 보급 요청처리중인 경우

  constructor(autoSailingInfo: AutoSailingInfo) {
    // User info.
    this._accountId = autoSailingInfo.accountId;
    this._pubId = autoSailingInfo.pubId;
    this._userId = autoSailingInfo.userId;
    this._fleetIndex = autoSailingInfo.fleetIndex;
    this._userName = 'noname';
    this._lang = '';

    // auto sailing
    this.startTimeUtc = autoSailingInfo.startTimeUtc;
    this._destCmsId = autoSailingInfo.destCmsId;
    this._destType = autoSailingInfo.destType;
    this._path = JSON.parse(autoSailingInfo.path);
    this._extra = autoSailingInfo.extra ? JSON.parse(autoSailingInfo.extra) : undefined;
    this._optionForServer = autoSailingInfo.optionForServer
      ? JSON.parse(autoSailingInfo.optionForServer)
      : undefined;

    // Socket.
    this._conn = new OfflineSailingBotConnection(this._accountId, this._pubId, this._userId);

    // State.
    this._botMode = BotMode.LOGIN_MODE;
    this._loginPhase = LoginPhase.UNAUTHORIZED;

    // sync Data
    this._syncAll = {};

    // battle

    // behavior tree setting
    this._behaviorStat = {
      waitCount: 0,
      waitMax: 0,
      startWaitTimeUtc: 0,
    };

    this.initScenario();
  }

  //----------------------------------------------------------
  // getter.setter
  //----------------------------------------------------------
  get SyncAll() {
    return this._syncAll;
  }

  public get userId(): number {
    return this._userId;
  }

  public get sailId(): number {
    return this._sailId;
  }
  public set sailId(value: number) {
    this._sailId = value;
  }

  public get destCmsId(): number {
    return this._destCmsId;
  }
  public set destCmsId(value: number) {
    this._destCmsId = value;
  }

  public get isEncountOccurred(): boolean {
    return this._isEncountOccurred;
  }
  public set isEncountOccurred(value: boolean) {
    this._isEncountOccurred = value;
  }

  public get battleId(): number {
    return this._battleId;
  }
  public set battleId(value: number) {
    this._battleId = value;
  }

  public get conn(): OfflineSailingBotConnection {
    return this._conn;
  }

  //----------------------------------------------------------
  // member functions
  //----------------------------------------------------------
  getGameState(): GAME_STATE {
    if (this._syncAll && this._syncAll.user) {
      return this._syncAll.user.gameState;
    }
    return GAME_STATE.NONE;
  }

  getRawLastGameState(): GAME_STATE {
    if (this._syncAll && this._syncAll.user) {
      return this._syncAll.user.lastGameState;
    }
    return GAME_STATE.NONE;
  }

  //----------------------------------------------------------------------------------------------------
  // getLastGameState
  // gameState 는 유저의 현재 상태를 나타내며 로그인 시 NONE(0)으로 초기화 된다.
  // 하지만 유저의 마지막 상태를 유지 시켜줘야 될 필요가 있기에 lastGameState 가 존재하고
  // lastGameState 이 gameState 와 동일해지는 시점에 lastGameState 은 NONE 으로 초기화 된다.
  // 따라서 lastGameState 값이 NONE 일 경우 lastGameState 값은 gameState 을 통해 얻어오면 된다.
  //----------------------------------------------------------------------------------------------------
  getLastGameState() {
    const lastGameState = this.getRawLastGameState();
    if (lastGameState !== GAME_STATE.NONE) {
      return lastGameState;
    } else {
      return this.getGameState();
    }

    return undefined;
  }

  getGameEnterState(): GAME_ENTER_STATE {
    if (this._syncAll && this._syncAll.user) {
      return this._syncAll.user.gameEnterState;
    }
    return GAME_ENTER_STATE.NONE;
  }

  isGameOver(): boolean {
    if (this._syncAll && this._syncAll.user) {
      return this._syncAll.user.bGameOver;
    }
    return false;
  }

  //----------------------------------------------------------
  isInTown(bIncludeEntering: boolean): boolean {
    const gameState = this.getLastGameState();
    const gameEnterState = this.getGameEnterState();

    if (bIncludeEntering) {
      return gameState >= GAME_STATE.TOWN_MIN && gameState <= GAME_STATE.TOWN_MAX;
    } else {
      return (
        gameState >= GAME_STATE.TOWN_MIN &&
        gameState <= GAME_STATE.TOWN_MAX &&
        gameEnterState !== GAME_ENTER_STATE.ENTERING
      );
    }
  }

  //----------------------------------------------------------
  isInOceanVillage(): boolean {
    const gameState = this.getLastGameState();

    return gameState >= GAME_STATE.OCEAN_VILLAGE_MIN && gameState <= GAME_STATE.OCEAN_VILLAGE_MAX;
  }

  //----------------------------------------------------------
  isInPureOcean(): boolean {
    const gameState = this.getLastGameState();

    return gameState === GAME_STATE.IN_OCEAN;
  }

  //----------------------------------------------------------
  isInOcean(excludes: GAME_STATE[] = []): boolean {
    const gameState = this.getLastGameState();
    const gameEnterState = this.getGameEnterState();

    for (const elem of excludes) {
      if (gameState === elem) {
        return false;
      }
    }

    return gameState >= GAME_STATE.OCEAN_MIN && gameState <= GAME_STATE.OCEAN_MAX;
  }

  //----------------------------------------------------------
  isInOceanBattle(): boolean {
    const gameState = this.getLastGameState();
    return gameState >= GAME_STATE.OCEAN_BATTLE_MIN && gameState <= GAME_STATE.OCEAN_BATTLE_MAX;
  }

  //----------------------------------------------------------
  isInOceanBattleReward(): boolean {
    const gameState = this.getLastGameState();

    return (
      gameState >= GAME_STATE.OCEAN_BATTLE_REWARD_MIN &&
      gameState <= GAME_STATE.OCEAN_BATTLE_REWARD_MAX
    );
  }

  //----------------------------------------------------------
  isWrecked(): boolean {
    if (this.SyncAll.user) {
      return this.SyncAll.user.bGameOver;
    }
    return false;
  }

  //----------------------------------------------------------
  isAutoSailingStopped(): boolean {
    return this._stopReason !== OFFLINE_SAILING_STOP_REASON.NONE;
  }

  //----------------------------------------------------------
  isAutoSailingPathLeft(): boolean {
    if (this._path) {
      return 0 < this._path.length;
    }
    return false;
  }

  //----------------------------------------------------------
  isAutoSailingAble(): boolean {
    let ret = this.isWrecked();
    if (ret) {
      return false;
    }

    if (!this.isInPureOcean()) {
      return false;
    }

    // is encount occured
    if (this.isEncountOccurred) {
      return false;
    }

    if (this.isAutoSailingStopped()) {
      return false;
    }

    return true;
  }

  isWorldBuffExist(buffCmsId: number): boolean {
    const worldBuffs = this.SyncAll.worldBuffs;
    if (!worldBuffs) {
      return false;
    }

    const found = _.values(worldBuffs).find((buff) => {
      return toNumber(buff.cmsId) == buffCmsId;
    });

    mlog.info(`worldBuff [${buffCmsId}] :${found}`);

    return found != undefined;
  }

  //----------------------------------------------------------
  setGameOptionPushNotification(
    isAllowed: number,
    isNightAllowed: number,
    allowedPushNotificationGroupIds: number[]
  ): void {
    this._isPushNotificationAllowed = isAllowed;
    this._isNightPushNotificationAllowed = isNightAllowed;
    this._allowedPushNotificationGroupIds = allowedPushNotificationGroupIds;
  }

  //----------------------------------------------------------
  resetBehaviorStat() {
    this._behaviorStat.waitCount = 0;
    this._behaviorStat.waitMax = 0;
  }

  //----------------------------------------------------------
  getBehaviorStat(): BehaviorStat {
    return this._behaviorStat;
  }

  //----------------------------------------------------------
  getBehaviorRequestResult(): BehaviorRequestResultType {
    return this._curRequest.getResult;
  }

  //----------------------------------------------------------
  setLobbydUrl(lobbydUrl: string) {
    this._lobbydUrl = lobbydUrl;
    this.conn.setLobbydUrl(lobbydUrl);
  }

  //----------------------------------------------------------
  initGameData(resBody: any) {
    if (resBody.sailId) {
      this.sailId = resBody.sailId;
    }
    if (resBody.lang) {
      this._lang = resBody.lang;
    }

    const syncAll: sync.All = resBody.sync.add;
    const user = syncAll.user;

    this._userName = user.name;
    this._isPushNotificationAllowed = resBody.isPushNotificationAllowed;
    this._isNightPushNotificationAllowed = resBody.isNightPushNotificationAllowed;
    this._allowedPushNotificationGroupIds = resBody.allowedPushNotificationGroupIds
      ? resBody.allowedPushNotificationGroupIds
      : null;

    this.setFleetShipIds(syncAll);
    this.initFleetSupplyLastState();
  }

  //----------------------------------------------------------
  setFleetShipIds(syncAll: sync.All) {
    this._fleetShipIds = [];
    if (syncAll.ships) {
      _.forOwn(syncAll.ships, (ship) => {
        if (ship.fleetIndex !== this._fleetIndex) {
          return;
        }
        this._fleetShipIds.push(ship.id);
      });
    }
  }

  //----------------------------------------------------------
  getShipCargos(shipId: number): sync.ShipCargos | undefined {
    if (this._syncAll.ships) {
      const ship = this._syncAll.ships[shipId];
      if (ship && ship.cargos) {
        return ship.cargos;
      }
    }
    return undefined;
  }

  //----------------------------------------------------------
  isSupplyEmptied(shipId: number, cmsId: cmsEx.SUPPLY_CMS_ID): boolean {
    const shipCargos = this.getShipCargos(shipId);
    if (shipCargos) {
      const cargo = shipCargos[cmsId];
      if (cargo && cargo.quantity) {
        if (cargo.quantity > 0) {
          // mlog.info('[TEMP] isSupplyEmptied', {
          //   userId: this._userId,
          //   shipId,
          //   cmsId,
          //   quantity: cargo.quantity,
          // });

          return false;
        }
      }
    }
    return true;
  }

  //----------------------------------------------------------
  getSupplyQuantity(shipId: number, cmsId: cmsEx.SUPPLY_CMS_ID): number {
    const shipCargos = this.getShipCargos(shipId);
    if (shipCargos) {
      const cargo = shipCargos[cmsId];
      if (cargo && cargo.quantity) {
        return cargo.quantity;
      }
    }
    return 0;
  }

  //----------------------------------------------------------
  isShipWaterEmptied(shipId: number): boolean {
    return this.isSupplyEmptied(shipId, cmsEx.SUPPLY_CMS_ID.WATER);
  }

  //----------------------------------------------------------
  isShiFoodEmptied(shipId: number): boolean {
    return this.isSupplyEmptied(shipId, cmsEx.SUPPLY_CMS_ID.WATER);
  }

  //----------------------------------------------------------
  isFleetWaterEmptied() {
    for (let k = 0; k < this._fleetShipIds.length; k++) {
      const shipId = this._fleetShipIds[k];
      if (!this.isShipWaterEmptied(k)) {
        return false;
      }
    }
    return true;
  }

  //----------------------------------------------------------
  isFleetSupplyEmptied(cmsId: cmsEx.SUPPLY_CMS_ID) {
    for (let k = 0; k < this._fleetShipIds.length; k++) {
      const shipId = this._fleetShipIds[k];
      if (!this.isSupplyEmptied(shipId, cmsId)) {
        return false;
      }
    }
    return true;
  }

  //----------------------------------------------------------
  getFleetSupplyQuantity(cmsId: cmsEx.SUPPLY_CMS_ID) {
    let sum: number = 0;
    for (let k = 0; k < this._fleetShipIds.length; k++) {
      const shipId = this._fleetShipIds[k];
      sum += this.getSupplyQuantity(shipId, cmsId);
    }
    return sum;
  }

  //----------------------------------------------------------
  initFleetSupplyLastState() {
    this._fleetSupplyEmptied = {};

    this.updateFleetSupplyEmptiedAll();
  }

  //----------------------------------------------------------
  updateFleetSupplyEmptiedAll() {
    this._updateFleetSupplyEmptied(cmsEx.SUPPLY_CMS_ID.WATER);
    this._updateFleetSupplyEmptied(cmsEx.SUPPLY_CMS_ID.FOOD);
    //this._updateFleetSupplyLastState(cmsEx.SUPPLY_CMS_ID.LUMBER);
    //this._updateFleetSupplyLastState(cmsEx.SUPPLY_CMS_ID.AMMO);
  }

  //----------------------------------------------------------
  isFleetSupplyEmptiedAll() {
    return (
      this._fleetSupplyEmptied[cmsEx.SUPPLY_CMS_ID.WATER] &&
      this._fleetSupplyEmptied[cmsEx.SUPPLY_CMS_ID.FOOD]
    );
  }

  //----------------------------------------------------------
  private _updateFleetSupplyEmptied(cmsId: cmsEx.SUPPLY_CMS_ID) {
    this._fleetSupplyEmptied[cmsId] = this.isFleetSupplyEmptied(cmsId);
  }

  //----------------------------------------------------------
  private _checkFleetSupplyNewlyEmptied(cmsId: cmsEx.SUPPLY_CMS_ID): boolean {
    if (this._fleetSupplyEmptied[cmsId]) {
      return false;
    }
    const isEmpty = this.isFleetSupplyEmptied(cmsId);
    if (this._fleetSupplyEmptied[cmsId] !== isEmpty) {
      // mlog.info('[TEMP] _checkFleetSupplyNewlyEmptied occurred', {
      //   userId: this._userId,
      //   cmsId,
      // });

      return true;
    }
    return false;
  }

  //----------------------------------------------------------
  // 하나라도 새로 소진되면 알림 보내면서 전체 재고를 전달한다
  checkFleetSupplyNewlyEmptiedEvents() {
    let isNewlyEmptied = false;
    if (this._checkFleetSupplyNewlyEmptied(cmsEx.SUPPLY_CMS_ID.WATER)) {
      isNewlyEmptied = true;
    }
    if (this._checkFleetSupplyNewlyEmptied(cmsEx.SUPPLY_CMS_ID.FOOD)) {
      isNewlyEmptied = true;
    }

    if (isNewlyEmptied) {
      const waterSum = this.getFleetSupplyQuantity(cmsEx.SUPPLY_CMS_ID.WATER);
      const foodSum = this.getFleetSupplyQuantity(cmsEx.SUPPLY_CMS_ID.FOOD);
      let args: string[] = [`${foodSum}`, `${waterSum}`];

      this.processPushNotification(PUSH_NOTIFICATION_ID.SUPPLY_EXHAUSTED, args);
    }
  }

  //----------------------------------------------------------
  isShipUpdate(resBody: any): boolean {
    if (resBody.sync && resBody.sync.add && resBody.sync.add.ships) {
      return true;
    }

    return false;
  }

  //----------------------------------------------------------
  isDisconnected(): boolean {
    return this._conn.isDisconnected();
  }

  //----------------------------------------------------------
  // 로그인과정에서 종료되더라도 lobbyd에 종료요청한다
  close(bSend: boolean = true): Promise<void> {
    if (this._conn) {
      return this._conn.disconnect(bSend);
    }
  }

  initScenario() {
    if (this._scenarioBehaviorTree) {
      delete this._scenarioBehaviorTree;
    }
    this._curRequest.reset();
    this.resetBehaviorStat();

    mlog.info(`applying default scenario [${ScenarioType[ScenarioType.SailToDestination]}] ...`);
    this._scenarioBehaviorTree = offlineSailingBotScenarioSailToDestination.createScenario(this);
  }

  //----------------------------------------------------------
  processPushNotification(pushId: PUSH_NOTIFICATION_ID, args?: string[]) {
    const pushCms = cms.PushNotification[pushId];
    if (!pushCms) {
      mlog.warn('invalid PushNotification id call designer', {
        userId: this._userId,
        pushId,
      });
      return;
    }

    // 유저의 알람 옵션 체크
    if (
      !isAllowNotification(pushCms.pushGroup, {
        isPushNotificationAllowed: this._isPushNotificationAllowed,
        isNightPushNotificationAllowed: this._isNightPushNotificationAllowed,
        allowedPushNotificationGroupIds: this._allowedPushNotificationGroupIds,
      })
    ) {
      mlog.info('processPushNotification N/A', {
        userId: this._userId,
        pushId,
      });

      return;
    }

    try {
      // 전송할 메시지 선택
      const sourceMsg = getPushMsg(this._lang, pushCms);
      let resultArgs: string[] = [this._userName];
      if (args && args.length > 0) {
        resultArgs = resultArgs.concat(args);

        // mlog.info('[TEMP] sendPushNotification args', {
        //   userId: this._userId,
        //   args,
        //   resultArgs,
        // });
      }
      const sendMsg = mutil.stringFormat(sourceMsg, resultArgs);

      // mlog.verbose('[TEMP] processPushNotification result msg', {
      //   userId: this._userId,
      //   sendMsg,
      // });

      mhttp.lgd
        .sendPushNotification(sendMsg, this._pubId)
        .then(() => {
          mlog.info('called sendPushNotification', {
            userId: this._userId,
            pushId,
            lang: this._lang,
            sendMsg,
          });
        })
        .catch((err) => {
          mlog.warn('sendPushNotification failed', {
            userId: this._userId,
            pushId,
            err: err.message,
            stack: err.stack,
          });
        });
    } catch (err) {
      mlog.error('sendPushNotification failed', {
        userId: this._userId,
        pushId,
        lang: this._lang,
        errormsg: err.message,
      });
    }
  }

  //----------------------------------------------------------
  getPushLocalForDisaster(disasterCmsId: number): string {
    const pushLocalizeLookupTable = Container.get(SailService).pushLocalizeLookupTable;
    const pushLocalCms = pushLocalizeLookupTable.pushLocalizes['OceanDisaster'][disasterCmsId];
    if (pushLocalCms) {
      return getPushLocal(this._lang, pushLocalCms);
    }

    // 언어에 해당하는 재해이름이 없음
    mlog.warn('invalid PushLocalForDisaster', { disasterCmsId, lang: this._lang });
    return '';
  }

  //----------------------------------------------------------
  tick(curTimeInMs: number): void {
    if (this._conn.isDisconnected()) {
      return;
    }

    switch (this._botMode) {
      case BotMode.LOGIN_MODE:
        this._tickLoginMode();
        break;
      case BotMode.SCENARIO_MODE:
        this._tickScenarioMode(curTimeInMs);
        break;
      default:
        // invalid mode
        break;
    }
  }

  //----------------------------------------------------------
  // Login Mode Processes
  //----------------------------------------------------------
  private _tickLoginMode() {
    //handle login mode packets
    this.handleLoginModePacket();

    switch (this._loginPhase) {
      case LoginPhase.UNAUTHORIZED:
        this.tickOnUnauthorized();
        break;
      case LoginPhase.AUTHRIZING:
        break;
      case LoginPhase.AUTHORIZED:
        this.tickOnAuthorized();
        break;
      case LoginPhase.LOGGING_IN:
        this.tickOnLoggingIn();
        break;

      case LoginPhase.AFTER_LOGGED_IN:
        this.tickOnAfterLoggedIn();
        break;
      case LoginPhase.MAP_LOADING:
        break;
      case LoginPhase.MAP_LOADING_COMPLETE:
        // end of login mode. changing to scenario mode
        this._botMode = BotMode.SCENARIO_MODE;
        break;

      default:
        break;
    }
  }

  //----------------------------------------------------------
  // botClient의 LoginMode용 패킷핸들러는 이곳에 추가.
  private handleLoginModePacket() {
    const packet = this._conn.popPacket();
    if (!packet || !packet.type) {
      return;
    }
    if (!this.updateSyncLoginMode(packet)) {
      this.close();
      return;
    }

    switch (packet.type) {
      // case proto.Auth.HELLO:
      //   this.onRecvHello(packet);
      //   break;
      // case proto.Auth.ENTER_WORLD_AS_BOT:
      //   this.onRecvEnterWorldAsBot(packet);
      //   break;
      case proto.Town.ENTER:
        this.onRecvTownEnter(packet);
        break;
      case proto.Town.LOAD_COMPLETE:
        this.onRecvTownLoadComplete();
        break;

      case proto.Ocean.ENTER:
        this.onRecvOceanEnter(packet);
        break;
      case proto.Ocean.LOAD_COMPLETE:
        this.onRecvOceanLoadComplete();
        break;

      default:
        // mlog.info('handlePacket unhandled packet', {
        //   userId: this._userId,
        //   packetType: packet.type,
        // });
        break;
    }
  }

  //----------------------------------------------------------
  // 항상 최신의 sync data를 업데이트 받아놓는다.
  private updateSyncLoginMode(packet): boolean {
    const resBody = JSON.parse(packet.body);
    if (resBody.sync && resBody.sync.add) {
      _.merge(this._syncAll, resBody.sync.add);

      return true;
    }

    if (resBody.errCode) {
      mlog.error('received error', {
        errCode: resBody.errCode,
        errMessage: resBody.errMessage ? resBody.errMessage : null,
      });
      return false;
    }
    return true;
  }

  //----------------------------------------------------------
  private tickOnUnauthorized(): void {
    // authorization 은 job에서 처리하고 봇의 tick이 시작되므로 바로 인증완료상태로 진입.
    this._loginPhase = LoginPhase.AUTHORIZED;
  }
  //----------------------------------------------------------
  private tickOnAuthorized(): void {
    this._loginPhase = LoginPhase.LOGGING_IN;
    //this._conn.reconnect();

    this.requestEnterWorldAsBot().catch((err) => {
      mlog.info('requestEnterWorldAsBot failed', { userId: this._userId });
      this.close();
      return;
    });
  }

  private async requestEnterWorldAsBot() {
    const packet = new SailProtocol.SA2LB_REQ_ENTER_WORLD_AS_BOT();
    packet.userId = this._userId;
    packet.accountId = this._accountId;
    packet.pubId = this._pubId;
    packet.sailUrl = mconf.apiService.url;

    const response: SailProtocol.LB2SA_RES_ENTER_WORLD_AS_BOT = await this.sendToLobbyAndRecv(
      packet
    );
    this.onRecvEnterWorldAsBot(response);
  }

  //----------------------------------------------------------
  private tickOnLoggingIn(): void {
    if (GAME_STATE.TOWN_MIN > this.getLastGameState()) {
      return;
    }

    mlog.info('logged-in', { userId: this._userId });

    this._loginPhase = LoginPhase.AFTER_LOGGED_IN;
  }

  //----------------------------------------------------------
  // 로그인완료 시점. 이곳에서 필요한 분기를 시킨다.
  //----------------------------------------------------------
  private tickOnAfterLoggedIn(): void {
    // [todo] act differently based on the game state
    if (this.isInOcean()) {
      this.startOceanEnterProcess();
    } else {
      mlog.error('invalid gamestate', {
        userId: this._userId,
        gamestate: this.getLastGameState(),
      });
      this.close();
    }
  }

  //----------------------------------------------------------
  private startTownEnterProcess() {
    this._loginPhase = LoginPhase.MAP_LOADING;

    reqSenders.sendTownEnter(this);
  }

  //----------------------------------------------------------
  private startOceanEnterProcess() {
    this._loginPhase = LoginPhase.MAP_LOADING;

    reqSenders.sendOceanEnter(this);
  }

  //----------------------------------------------------------
  // login handlers
  //---------------------------------------------------
  // private onRecvHello(packet): void {
  //   // First take the double quotes from both ends of the 'body'.
  //   const peerPublicKey = packet.body.replace(/"/g, '');
  //   //this.conn.getCryptoCtx.computeSecret(Buffer.from(peerPublicKey, 'hex'));

  //   // Now send login.
  //   const body = {
  //     enterWorldToken: this.enterWorldToken,
  //     isDevLogin: 1,
  //     sessionToken: this.userName,
  //     lang: 'ko',
  //     reconnect: 0,
  //     deviceType: 'Windows',
  //   };

  //   const loginPacket = {
  //     type: proto.Auth.ENTER_WORLD_AS_BOT,
  //     seqNum: 0,
  //     body: JSON.stringify(body),
  //   };

  //   this.conn.sendJsonPacket(loginPacket, NaN);
  // }

  //----------------------------------------------------------
  private onRecvEnterWorldAsBot(packet): void {
    const resBody = JSON.parse(packet.body);
    if (resBody.kickReason) {
      mlog.info('onRecvEnterWorld kicked from server', {
        userId: this._userId,
        kickReason: resBody.kickReason,
      });
      this.close();
      return;
    }

    if (!this.updateSyncLoginMode(packet)) {
      mlog.info('onRecvEnterWorld failed', {
        userId: this._userId,
        errCode: resBody.errCode,
      });
      this.close();
      return;
    }

    this.initGameData(resBody);

    const user = resBody.sync.add.user;
    const gameState = this.getLastGameState();
    switch (gameState) {
      default:
        // 모의전 로비상태를 제외하고 해양인 경우 정상
        if (this.isInOcean([GAME_STATE.IN_OCEAN_BATTLE_VERSUS_LOBBY])) {
          this._loginPhase = LoginPhase.AFTER_LOGGED_IN;
          break;
        }

        // 로그인은 성공했지만 gameState가 비정상인경우 오프항해가 무한반복하는 경우 방어
        // 원래대로라면 오프항해가 여기까지 오면 안된다
        Promise.resolve()
          .then(() => {
            mlog.warn('onRecvEnterWorld deactivating offsail', {
              userId: this._userId,
              gameState,
              lastGameState: this.getLastGameState(),
            });

            const { worldDbConnPool } = Container.get(SailService);
            return pwAutoSailingUpdateIsOfflineSailingDeactivated(
              worldDbConnPool.getPool(),
              this._userId,
              this._fleetIndex,
              1 /**IsOfflineSailingDeactivated*/
            );
          })
          .catch((err) => {})
          .finally(() => {
            this.close();
          });

        break;
    }
  }

  //----------------------------------------------------------
  //----------------------------------------------------------
  private onRecvTownEnter(packet): void {
    const body = JSON.parse(packet.body);
    if (body.errCode) {
      mlog.error('received error', {
        errCode: body.errCode,
        errMessage: body.errMessage ? body.errMessage : null,
      });

      this._conn.disconnect();
      return;
    }

    mlog.info('entered-town', { userId: this._userId });

    reqSenders.sendTownLoadComplete(this);
  }

  //----------------------------------------------------------
  private onRecvTownLoadComplete(): void {
    this._loginPhase = LoginPhase.MAP_LOADING_COMPLETE;

    mlog.info('loadcomplete-town', { userId: this._userId });
  }

  //----------------------------------------------------------
  private onRecvOceanEnter(packet): void {
    const body = JSON.parse(packet.body);
    if (body.errCode) {
      mlog.error('received error', {
        errCode: body.errCode,
        errMessage: body.errMessage ? body.errMessage : null,
      });

      this._conn.disconnect();
      return;
    }
    this._battleId = body.battleId;

    mlog.info('entered-ocean', { userId: this._userId });

    reqSenders.sendOceanLoadComplete(this);
  }

  //----------------------------------------------------------
  private onRecvOceanLoadComplete(): void {
    this._loginPhase = LoginPhase.MAP_LOADING_COMPLETE;

    mlog.info('loadcomplete-ocean', { userId: this._userId });

    //디버깅용 [todo] 실제 버프가 존재할때만 보내도록 개선필요
    if (CHEAT_ENCOUNT_PROTECTION_DISABLE) {
      // 38001010: 선단LV 10 미만 보호모드
      const protectBuffCmsId = 38001010;
      mlog.info('sendCheatWorldBuffRem protect buff.. removing..', {
        userId: this._userId,
        protectBuffCmsId,
      });
      reqSenders.sendCheatWorldBuffRem(this, protectBuffCmsId, 1);
    }
  }

  //----------------------------------------------------------
  //----------------------------------------------------------
  // Scenario Mode 함수들
  //----------------------------------------------------------
  private _tickScenarioMode(curTimeInMs: number) {
    //handle scenario mode packets
    this.handleScenarioModePacket(curTimeInMs);

    this.tickOnPlaySecenario();
  }

  //----------------------------------------------------------
  // botClient의 ScenarioMode용 패킷핸들러는 이곳에 추가.
  private handleScenarioModePacket(curTimeInMs: number) {
    const curTimeUtc = Math.floor(curTimeInMs / 1000);
    for (let k = 0; k < 5; k++) {
      const packet = this._conn.popPacket();
      if (!packet) {
        return;
      }
      if (!packet.type) {
        continue;
      }

      const resBody = JSON.parse(packet.body);
      if (!this.updateSyncScenarioMode(resBody, curTimeUtc)) {
        // [todo] 목적지 도착 상태인경우 푸시알람 전송(한번만 전송되는지 확인 필요)
        if (!this.isAutoSailingPathLeft()) {
          mlog.warn('[handleScenarioModePacket] got error when path empty', {
            userId: this._userId,
          });
        }
        this.close();
        return;
      }

      if (this.isShipUpdate(resBody)) {
        // mlog.info('[TEMP] ShipUpdate', {
        //   userId: this._userId,
        //   ships: resBody.sync.add.ships,
        // });

        // firstly check for newly emptied supplies
        this.checkFleetSupplyNewlyEmptiedEvents();

        // update supply states
        this.updateFleetSupplyEmptiedAll();
      }

      // 물빵이 모두 소모된 경우 오프항해 중단
      if (this._stopReason === OFFLINE_SAILING_STOP_REASON.NONE && this.isFleetSupplyEmptiedAll()) {
        this._stopReason = OFFLINE_SAILING_STOP_REASON.WATER_FOOD_EXHAUSTED;

        mlog.info('water and food exhausted.. stopping offsail', {
          userId: this._userId,
        });
      }

      // update latest request's state
      let ret = true;
      if (resBody.errCode) {
        ret = 0 == resBody.errCode ? true : false;
      }
      this.updateBehaviorRequest(packet.type, ret);

      //add individual packet handlers here
      switch (packet.type) {
        case proto.Ocean.ARRIVE:
          this.onRecvArrive(packet);
          break;
        case proto.Ocean.ADD_DISASTER_SC:
          this.onRecvAddDisaster(packet);
          break;
        case proto.Ocean.REMOVE_DISASTER_SC:
          break;
        case proto.Ocean.RESOLVE_DISASTER_BY_DELEGATION_SC:
          this.onRecvResolveDisasterByDelegation(packet);
          break;
        case proto.Ocean.ENCOUNT_BY_NPC_SC:
          this.onRecvEncountByNpcSc(packet);
          break;
        case proto.Ocean.ENCOUNT_BY_NET_USER_SC:
          this.onRecvEncountByNetUser(packet);
          break;

        case proto.Ocean.ENCOUNT_USER_CHOICE:
          this.onRecvEncountUserChoice(packet);
          break;
        case proto.Ocean.ENCOUNT_ENEMY_CHOICE_SC:
          this.onRecvEncountEnemyChoiceSc(packet);
          break;
        case proto.Ocean.ENCOUNT_END_FORCEDLY_SC:
          this.onRecvEncountEndForcedlySc(packet);
          break;
        case proto.Ocean.ENCOUNT_CANCEL_SC:
          this.onRecvEncountCancelSc(packet);
          break;
        case proto.Ocean.ENCOUNT_END:
          this.onRecvEncountEnd(packet);
          break;

        case proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START:
          this.RecvOfflineSailingMoveDelegateStart(packet);
          break;
        case proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_UPDATE_SC:
          this.onRecvOfflineSailingMoveDelegateUpdateSc(packet);
          break;
        case proto.Ocean.OFFLINE_SAILING_STOP_SC:
          this.onRecvOfflineSailingStopSc(packet);
          break;
        case proto.Ocean.GET_USER_TOWN_STATE:
          this.onRecvGetUserTownState(packet);
          break;
        case proto.Ocean.APPLY_WAYPOINT_SUPPLY:
          this.onRecvApplyWaypointSupply(packet);
          break;
        case proto.Ocean.UPDATE_AUTO_SAILING:
          this.onRecvUpdateAutoSailing(packet);
          break;
        case proto.Ocean.UPDATE_AUTO_SAIL_OPTION_FOR_SERVER:
          this.onRecvupdateAutoSailOptionForServer(packet);
          break;

        case proto.Ocean.WRECK_FLEET_SC:
          this.onRecvWreckFleetSc(packet);
          break;
        case proto.Battle.RESUME:
          this.onRecvBattleResume(packet);
          break;

        case proto.Battle.END:
          this.onRecvBattleEnd(packet);
          break;

        default:
          if (!ResponseTypesNotToLog.includes(packet.type)) {
            // mlog.info('handleScenarioModePacket unhandled packet', {
            //   userId: this._userId,
            //   packetType: packet.type,
            //   typeStr: proto.toString(packet.type),
            // });
          }
          break;
      }
    }
  }

  private refreshEnterWorldToken(prevGameState: number, curGameState: number, curTimeUtc: number) {
    const prevScope = Math.floor(prevGameState / 100);
    const curScope = Math.floor(curGameState / 100);
    if (prevScope !== curScope) {
      // changed world?!.. refresh enterWorldToken
      mlog.info('refreshEnterWorldToken', {
        userId: this._userId,
        prevGameState,
        curGameState,
      });
      const { userCacheRedis } = Container.get(SailService);
      const newToken = mutil.generateEnterWorldToken(this._accountId);
      return userCacheRedis['setEnterWorldToken'](this._accountId, newToken, curTimeUtc);
    }
  }

  //----------------------------------------------------------
  // 항상 최신의 sync data를 업데이트 받아놓는다.
  private updateSyncScenarioMode(resBody: any, curTimeUtc: number): boolean {
    if (resBody.sync && resBody.sync.add) {
      const prevGameState = this.getGameState();
      _.merge(this._syncAll, resBody.sync.add);

      const curGameState = this.getGameState();
      this.refreshEnterWorldToken(prevGameState, curGameState, curTimeUtc);

      if (this.SyncAll.sailing && this.SyncAll.sailing.sailId) {
        this.sailId = this.SyncAll.sailing.sailId;
      }

      return true;
    }
    if (resBody.errCode) {
      mlog.error('received error', {
        userId: this._userId,
        errCode: resBody.errCode,
        errMessage: resBody.errMessage ? resBody.errMessage : null,
      });
      return false;
    }
    return true;
  }

  //----------------------------------------------------------
  private updateBehaviorRequest(packetType: number, isSuccess: boolean) {
    let requiredPacketTypes = this._curRequest.getRequiredPacketTypes();
    if (0 == requiredPacketTypes.length) {
      return;
    }
    let found: boolean = false;
    let k = 0;
    for (; k < requiredPacketTypes.length; k++) {
      if (packetType == requiredPacketTypes[k]) {
        found = true;
        break;
      }
    }
    if (!found) {
      return;
    }

    if (!isSuccess) {
      //anyone fails all fail
      this._curRequest.setResult = BehaviorRequestResultType.Failure;

      mlog.error('failed request', { userId: this._userId, packetType });
    } else {
      //if last packet is success, it's request is complete
      if (k == requiredPacketTypes.length - 1) {
        this._curRequest.setResult = BehaviorRequestResultType.Success;
      } else {
        //send next request packet
        const nextPacketType = requiredPacketTypes[k + 1];
        if (!nextPacketType) {
          return;
        }
        const func = reqSenders.sendMap[nextPacketType];
        if (func) {
          mlog.debug('next request', {
            userId: this._userId,
            nextPacketType,
            typeStr: proto.toString(nextPacketType),
            func,
          });

          func(this);
        }
      }
    }
  }

  //----------------------------------------------------------
  private tickOnPlaySecenario(): void {
    // [todo] act based on gamestate
    this._scenarioBehaviorTree.step();
    //console.log(this.scenarioBehaviorTree.lastRundData);
  }

  // ----------------------------------------------------------------------------
  sendToLobby(packet: IPacket) {
    tcpServer.sendPacket(this._lobbydUrl, packet);
  }

  // ----------------------------------------------------------------------------
  async sendToLobbyAndRecv(packet: IPacket): Promise<any> {
    return tcpServer.sendAndRecv(this._lobbydUrl, packet);
  }

  //----------------------------------------------------------
  private onRecvArrive(packet): void {
    //const body = JSON.parse(packet.body);

    mlog.info('arrived', { userId: this._userId });
  }

  //----------------------------------------------------------
  private onRecvAddDisaster(packet): void {
    const body = JSON.parse(packet.body);
    if (body.sync.add && body.sync.add.sailing && body.sync.add.sailing.shipsInDisaster) {
      const shipsInDisaster = body.sync.add.sailing.shipsInDisaster;
      const disasterCmsIds: number[] = Object.values(shipsInDisaster);
      if (disasterCmsIds && disasterCmsIds.length > 0) {
        const cmsId = disasterCmsIds[0];
        const disasterName = this.getPushLocalForDisaster(cmsId);
        this.processPushNotification(PUSH_NOTIFICATION_ID.DISASTER_OCCURRED, [disasterName]);
      }
    }

    mlog.info('add-disaster', { userId: this._userId });
  }

  //----------------------------------------------------------
  private onRecvResolveDisasterByDelegation(packet): void {
    //this.processPushNotification(PUSH_NOTIFICATION_ID.RESOLVE_DISASTER_BY_DELEGATION);

    mlog.info('resolved-disaster-by-delegation', { userId: this._userId });
  }

  //----------------------------------------------------------
  private onRecvEncountByNpcSc(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('encount-by-npc', { userId: this._userId });

    //set encout flag
    this.isEncountOccurred = true;
  }

  //----------------------------------------------------------
  private onRecvEncountByNetUser(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('encount-by-user', { userId: this._userId });

    //set encout flag
    this.isEncountOccurred = true;
  }

  //----------------------------------------------------------
  private onRecvEncountUserChoice(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('encount-user-choice', { userId: this._userId });

    if (body.flowType) {
      this._encountResult = body.flowType;
    }
  }

  //----------------------------------------------------------
  private onRecvEncountEnemyChoiceSc(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('encount-enemy-choice', { userId: this._userId });

    if (body.encountResult) {
      this._encountResult = body.encountResult;
    }
  }

  //----------------------------------------------------------
  private onRecvEncountEndForcedlySc(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('encount-end-forcedly', { userId: this._userId });

    if (body.encountResult) {
      this._encountResult = body.encountResult;
    }
    if (body.battleParam) {
      this._battleParam = body.battleParam;
    }

    // encountEnd 를 보내고 난 이후의 흐름으로 진행하면 됨
    this.isEncountOccurred = false;
  }

  //----------------------------------------------------------
  private onRecvEncountCancelSc(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('encount-canceled', { userId: this._userId });

    if (body.encountResult) {
      this._encountResult = body.encountResult;
    }
    if (body.battleParam) {
      this._battleParam = body.battleParam;
    }

    // encountEnd 를 보내고 난 이후의 흐름으로 진행하면 됨
    this.isEncountOccurred = false;
  }

  //----------------------------------------------------------
  private onRecvWreckFleetSc(packet): void {
    mlog.info('WreckFleetSc', { userId: this._userId });
  }

  //----------------------------------------------------------
  private RecvOfflineSailingMoveDelegateStart(packet): void {
    mlog.info('OfflineSailingMoveDelegateStart', { userId: this._userId });

    // 오프라인 항해는 최초 실행시에만 한번 푸시 발송한다
    if (this._optionForServer?.bPushSent) {
      return;
    }

    this.processPushNotification(PUSH_NOTIFICATION_ID.OFF_SAIL_STARTED);

    // 서버로 this._optionForServer 내용을 업데이트 요청
    reqSenders.sendUpdateAutoSailOptionForServer(this, true);
  }

  //----------------------------------------------------------
  private onRecvOfflineSailingMoveDelegateUpdateSc(packet): void {
    const body = JSON.parse(packet.body);

    const remainingPath = body.remainingPath;

    mlog.verbose('OfflineSailingMoveDelegateUpdateSc', { userId: this._userId, remainingPath });

    const townCmsId = checkWaypointSupply(this._path, remainingPath);
    if (townCmsId) {
      // 보급 요청 전처리
      this._remainingPathForSupply = remainingPath;
      reqSenders.sendGetUserTownState(this, townCmsId);
    } else {
      // 그냥업데이트 요청
      reqSenders.sendUpdateAutoSailing(this, remainingPath);
    }
  }

  //----------------------------------------------------------
  private onRecvGetUserTownState(packet): void {
    // 보급요청 전처리 완료체크
    if (this._remainingPathForSupply) {
      mlog.info('GetUserTownState success. before supply', {
        userId: this._userId,
        remainingPath: this._remainingPathForSupply,
      });
      const townCmsId = checkWaypointSupply(this._path, this._remainingPathForSupply);
      if (townCmsId) {
        reqSenders.sendApplyWaypointSupply(this, this._remainingPathForSupply, townCmsId);
      }
    } else {
      mlog.error('GetUserTownState received. but _remainingPath is undefined', {
        userId: this._userId,
        remainingPath: this._remainingPathForSupply,
      });
    }
  }

  //----------------------------------------------------------
  private onRecvApplyWaypointSupply(packet): void {
    const body = JSON.parse(packet.body);
    const remainingPath = body.remainingPathPoints;

    mlog.info('onRecvApplyWaypointSupply', { userId: this._userId, remainingPath });

    this._path = 0 === remainingPath ? [] : this._path.slice(-remainingPath);
    this._remainingPathForSupply = undefined;
  }

  //----------------------------------------------------------
  private onRecvUpdateAutoSailing(packet): void {
    const body = JSON.parse(packet.body);

    const remainingPath = body.remainingPathPoints;
    mlog.verbose('onRecvUpdateAutoSailing', { userId: this._userId, remainingPath });

    this._path = 0 === remainingPath ? [] : this._path.slice(-remainingPath);
  }

  //----------------------------------------------------------
  private onRecvOfflineSailingStopSc(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('onRecvOfflineSailingStopSc', { userId: this._userId, stopReason: body.reason });

    this._stopReason = body.reason;
  }

  //----------------------------------------------------------
  private onRecvupdateAutoSailOptionForServer(packet): void {
    const body = JSON.parse(packet.body);

    mlog.info('onRecvupdateAutoSailOptionForServer', {
      userId: this._userId,
      forServer: body.forServer,
    });

    if (!this._optionForServer) {
      this._optionForServer = {};
    }

    // _optionForServer 는 이곳에서만 업데이트한다
    this._optionForServer = body.forServer;
  }

  //----------------------------------------------------------
  private onRecvEncountEnd(packet): void {
    const body = JSON.parse(packet.body);

    this._battleParam = body.battleParam;

    if (this._battleParam) {
      // 딱한번만 서버푸시알람을 보낸다
      this.processPushNotification(PUSH_NOTIFICATION_ID.BATTLE_OCCURRED);
    }
    mlog.info('EncountEnd', { userId: this._userId });
  }

  //----------------------------------------------------------
  private onRecvBattleResume(packet): void {
    const body = JSON.parse(packet.body);

    if (body.txnLog) {
      const battleParam = JSON.parse(body.txnLog[0]).battleParam;
      this._battleParam = battleParam;

      mlog.info('BattleResume updated battleParam', { userId: this._userId });
    }

    mlog.info('BattleResume ok', { userId: this._userId });
  }

  //----------------------------------------------------------

  private onRecvBattleEnd(packet): void {
    //const body: sync.Resp = JSON.parse(packet.body);
    const body = JSON.parse(packet.body);

    mlog.info('onRecvBattleEnd', { userId: this._userId });

    const battleHistory: OfflineBattleHistories = {};
    if (body.sync && body.sync.add) {
      const battleEndResult = body.sync.add.battleEndResult;
      if (battleEndResult) {
        if (battleEndResult.ducatGains) {
          battleHistory.addedDucat = battleEndResult.ducatGains;
        }
        if (battleEndResult.userExpGain) {
          battleHistory.addedUserExp = battleEndResult.userExpGain;
        }
        if (battleEndResult.fameGain) {
          battleHistory.fameGain = battleEndResult.fameGain;
        }

        if (battleEndResult.mateExpGains) {
          battleEndResult.mateExpGains.forEach((mateExp) => {
            if (!battleHistory.mates) {
              battleHistory.mates = [];
            }
            battleHistory.mates.push({
              mateCmsId: mateExp.mateCmsId,
              addedExp: mateExp.amount,
            });
          });
        }
      }
      const battleRewards = body.sync.add.battleRewards;
      if (battleRewards) {
        battleRewards.forEach((reward) => {
          if (reward.receivedQuantity) {
            if (!battleHistory.rewards) {
              battleHistory.rewards = [];
            }
            battleHistory.rewards.push({
              type: reward.Type,
              cmsId: reward.Id,
              quantity: reward.receivedQuantity,
            });
          }
        });
      }

      const { sailRedis } = Container.get(SailService);
      Promise.resolve().then(async () => {
        if (Object.keys(battleHistory).length > 0) {
          battleHistory.battleEndTimeUtc = mutil.curTimeUtc();
          await sailRedis['setOfflineBattleHistories'](this._userId, JSON.stringify(battleHistory));
          mlog.info('BattleEnd ok', {
            userId: this._userId,
            battleHistory,
          });
        }
      });
    }
  }

  //----------------------------------------------------------
  //----------------------------------------------------------
  // BehaviorTree 함수들
  //----------------------------------------------------------
  private _btWaitSet(count: number) {
    this._behaviorStat.waitCount = 0;
    this._behaviorStat.waitMax = count;
  }

  //----------------------------------------------------------
  btTickWait(count: number) {
    if (this._behaviorStat.waitCount < 0) {
      this._btWaitSet(count);
      return 0;
    } else {
      this._behaviorStat.waitCount++;
      if (this._behaviorStat.waitCount >= this._behaviorStat.waitMax) {
        this._behaviorStat.waitCount = -1;
        return 1;
      }
      return 0;
    }
  }

  //----------------------------------------------------------
  btTickWaitRandom(maxCount: number) {
    const val = Math.floor(Math.random() * maxCount) + 1;
    if (this._behaviorStat.waitCount < 0) {
      this._btWaitSet(val);
      return 0;
    } else {
      this._behaviorStat.waitCount++;
      if (this._behaviorStat.waitCount >= this._behaviorStat.waitMax) {
        this._behaviorStat.waitCount = -1;
        return 1;
      }
      return 0;
    }
  }

  private _btWaitTimeoutSet(curTimeUtc: number, actionType: BehaviorActionType) {
    this._behaviorStat.startWaitTimeUtc = curTimeUtc;
    this._curRequest.setActionType = actionType;
  }

  //----------------------------------------------------------
  btIsInTown() {
    return this.isInTown(true);
  }

  //----------------------------------------------------------
  btisInOceanVillage() {
    return this.isInOceanVillage();
  }

  //----------------------------------------------------------
  btIsInOcean() {
    return this.isInOcean();
  }

  //----------------------------------------------------------
  btIsInOceanLoading() {
    return (
      this.getLastGameState() === GAME_STATE.IN_OCEAN &&
      this.getGameEnterState() === GAME_ENTER_STATE.LOADING
    );
  }

  //----------------------------------------------------------
  btIsInOceanBattle() {
    mlog.debug('btIsInOceanBattle', { userId: this._userId });
    return this.isInOceanBattle();
  }

  //----------------------------------------------------------
  btIsNeedBattleResume() {
    mlog.debug('btIsNeedBattleResume', { userId: this._userId });
    return this.isInOceanBattle() && !this._battleParam && this._battleId;
  }

  //----------------------------------------------------------
  btIsWrecked() {
    return this.isWrecked();
  }

  //----------------------------------------------------------
  private _btStartChangeToTown() {
    mlog.debug('btStartChangeToTown', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([
      proto.Ocean.ARRIVE,
      proto.Town.ENTER,
      proto.Town.LOAD_COMPLETE,
    ]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.MoveToTown;

    reqSenders.sendOceanArrive(this);
  }

  //----------------------------------------------------------
  btChangeToTown() {
    if (BehaviorActionType.MoveToTown != this._curRequest.getActionType) {
      //init
      this._btStartChangeToTown();
      return 0;
    } else {
      //update
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return 0;
      }
      mlog.info('btChangeToTown fin', { userId: this._userId, ret });
      return 1;
    }
  }

  //----------------------------------------------------------
  private _btStartChangeToVillage() {
    mlog.debug('btStartChangeToVillage', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Ocean.VILLAGE_ENTER]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.MoveToVillage;

    reqSenders.sendOceanVillageEnter(this);
  }

  //----------------------------------------------------------
  btChangeToVillage() {
    if (BehaviorActionType.MoveToVillage != this._curRequest.getActionType) {
      //init
      this._btStartChangeToVillage();
      return 0;
    } else {
      //update
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return 0;
      }
      mlog.info('btChangeToVillage fin', { userId: this._userId, ret });
      return 1;
    }
  }

  //----------------------------------------------------------
  private _btStartChangeToOcean() {
    mlog.debug('btStartChangeToOcean', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([
      proto.Town.ENTER_BUILDING,
      proto.Town.DEPART_DEPART,
      proto.Ocean.ENTER,
      proto.Ocean.LOAD_COMPLETE,
    ]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.MoveToOcean;

    reqSenders.sendTownEnterBuilding(this);
  }

  //----------------------------------------------------------
  btChangeToOcean() {
    if (BehaviorActionType.MoveToOcean != this._curRequest.getActionType) {
      //init
      this._btStartChangeToOcean();
      return 0;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return 0;
      }
      mlog.info('btChangeToOcean fin', { userId: this._userId, ret });
      return 1;
    }
  }

  //----------------------------------------------------------
  private _btStartMakeOceanLoadComplete() {
    mlog.debug('_btStartMakeOceanLoadComplete', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Ocean.LOAD_COMPLETE]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.MakeOceanLoadComplete;

    reqSenders.sendOceanLoadComplete(this);
  }

  //----------------------------------------------------------
  btMakeOceanLoadComplete(): number {
    if (BehaviorActionType.MakeOceanLoadComplete != this._curRequest.getActionType) {
      //init
      this._btStartMakeOceanLoadComplete();
      return 0;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return 0;
      }
      mlog.info('btMakeOceanLoadComplete fin', { userId: this._userId, ret });
      return 1;
    }
  }

  //----------------------------------------------------------
  private _btStartCheatTeleportTown() {
    mlog.debug('_btStartCheatTeleportTown', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Admin.TELEPORT_TOWN]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.ApplyCheat;

    reqSenders.sendCheatTeleportTown(this);
  }

  //----------------------------------------------------------
  btCheatTeleportTown(): number {
    if (BehaviorActionType.ApplyCheat != this._curRequest.getActionType) {
      //init
      this._btStartCheatTeleportTown();
      return 0;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return 0;
      }
      mlog.info('btCheatTeleportTown fin', { userId: this._userId, ret });
      return 1;
    }
  }

  //----------------------------------------------------------
  btCheatRemoveProtectBuff() {
    mlog.debug('btCheatRemoveProtectBuff', { userId: this._userId });

    // 38001010: 선단LV 10 미만 보호모드
    const protectBuffCmsId = 38001010;
    if (!this.isWorldBuffExist(protectBuffCmsId)) {
      return BehaviorRequestResultType.Failure;
    }

    mlog.debug('btCheatRemoveProtectBuff protect buff exist.. removing..', {
      userId: this._userId,
      protectBuffCmsId,
    });
    reqSenders.sendCheatWorldBuffRem(this, protectBuffCmsId, 1);

    return BehaviorRequestResultType.Success;
  }

  //----------------------------------------------------------
  btIsAutoSailingAble() {
    mlog.debug('btIsAutoSailingAble', { userId: this._userId });
    return this.isAutoSailingAble();
  }

  //----------------------------------------------------------
  private _btStartAutoSailingToDestination() {
    mlog.debug('_btStartAutoSailingToDestination', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Ocean.OFFLINE_SAILING_MOVE_DELEGATE_START]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.AutoSailingToDestination;

    reqSenders.sendReqOfflineSailingMoveDelegateStart(this);
  }

  //----------------------------------------------------------
  btAutoSailingToDestination() {
    if (BehaviorActionType.AutoSailingToDestination != this._curRequest.getActionType) {
      //init
      this._btStartAutoSailingToDestination();
      return 0;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return 0;
      } else if (BehaviorRequestResultType.Failure == ret) {
        return 2;
      }
      // autosailing successfully started..

      //check if anything(encount, wreck, etc) has occoured
      if (!this.isAutoSailingAble()) {
        return 2;
      }

      //microjob.2. wait until the path list is empty
      if (this.isAutoSailingPathLeft()) {
        return 0;
      }

      mlog.info('btAutoSailingToDestination fin', { userId: this._userId, ret });
      return 1;
    }
  }

  //----------------------------------------------------------
  btLogout() {
    mlog.info('btLogout', { userId: this._userId });
    this.close();
  }

  //----------------------------------------------------------
  btIsDestTownExist() {
    mlog.debug('btIsDestTownExist', { userId: this._userId });

    if (this._destCmsId && AUTO_SAIL_DEST_TYPE.TOWN === this._destType) {
      return true;
    }
    return false;
  }

  //----------------------------------------------------------
  btIsDestVillageExist() {
    mlog.debug('btIsDestVillageExist', { userId: this._userId });

    if (this._destCmsId && AUTO_SAIL_DEST_TYPE.VILLAGE === this._destType) {
      return true;
    }
    return false;
  }

  //----------------------------------------------------------
  btIsEncountOccurred(): boolean {
    mlog.debug('btIsEncountOccurred', { userId: this._userId });
    return this.isEncountOccurred;
  }

  //----------------------------------------------------------
  private _btStartEndAutoSailing() {
    mlog.debug('_btStartEndAutoSailing', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Ocean.END_AUTO_SAILING]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.EndAutoSailing;

    reqSenders.sendEndAutoSailing(this);
  }

  //----------------------------------------------------------
  btEndAutoSailing() {
    if (BehaviorActionType.EndAutoSailing != this._curRequest.getActionType) {
      //init
      this._btStartEndAutoSailing();
      return 0;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return 0;
      }
      mlog.info('btEndAutoSailing fin', { userId: this._userId, ret });
      return 1;
    }
  }

  //----------------------------------------------------------
  btIsAutoSailingStopped(): boolean {
    mlog.debug('btIsAutoSailingStopped', { userId: this._userId });
    return this.isAutoSailingStopped();
  }
  //----------------------------------------------------------
  btIsAutoSailingCompleted(): boolean {
    mlog.debug('btIsAutoSailingCompleted', { userId: this._userId });
    return !this.isAutoSailingPathLeft() && !this.btIsDestTownExist();
  }

  //----------------------------------------------------------
  private _btStartHandleEncountChoice() {
    mlog.debug('_btStartHandleEncountChoice', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Ocean.ENCOUNT_USER_CHOICE]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.HandleEncountChoice;

    this._encountResult = EncountResult.WAITING_FOR_CHOICE;

    reqSenders.sendEncountUserChoice(this);
  }

  //----------------------------------------------------------
  btHandleEncountChoice() {
    mlog.debug('btHandleEncountChoice', { userId: this._userId });

    if (BehaviorActionType.HandleEncountChoice != this._curRequest.getActionType) {
      //init
      this._btStartHandleEncountChoice();
      return BehaviorRequestResultType.Pending;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return ret;
      } else if (BehaviorRequestResultType.Failure == ret) {
        return ret;
      }

      mlog.info('btHandleEncountChoice fin', { userId: this._userId, ret });
      return BehaviorRequestResultType.Success;
    }
  }

  //----------------------------------------------------------
  btWaitForEncountEnemyChoice() {
    if (!this.isEncountOccurred) {
      mlog.info('btWaitForEncountEnemyChoice stops with fail', { userId: this._userId });

      return BehaviorRequestResultType.Failure;
    }

    if (this._encountResult === EncountResult.WAITING_FOR_CHOICE) {
      return BehaviorRequestResultType.Pending;
    }

    const bIsBattle = isEncountBattle(this._encountResult);

    mlog.info('btWaitForEncountEnemyChoice fin', { userId: this._userId, bIsBattle });
    return BehaviorRequestResultType.Success;
  }

  //----------------------------------------------------------
  private _btStartHandleEncountEnd() {
    mlog.debug('_btStartHandleEncountEnd', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Ocean.ENCOUNT_END]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.HandleEncountEnd;

    reqSenders.sendEncountEnd(this);
  }

  //----------------------------------------------------------
  btHandleEncountEnd() {
    mlog.debug('btHandleEncountEnd', { userId: this._userId });

    if (BehaviorActionType.HandleEncountEnd != this._curRequest.getActionType) {
      //init
      this._btStartHandleEncountEnd();
      return BehaviorRequestResultType.Pending;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return ret;
      } else if (BehaviorRequestResultType.Failure == ret) {
        return ret;
      }

      // 인카운트를 해소시킨다
      this.isEncountOccurred = false;

      mlog.info('btHandleEncountEnd fin', { userId: this._userId, ret });
      return BehaviorRequestResultType.Success;
    }
  }

  //----------------------------------------------------------
  private _btStartBattleResume() {
    mlog.debug('_btStartBattleResume', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Battle.RESUME]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.BattleResume;

    reqSenders.sendBattleResume(this);
  }

  //----------------------------------------------------------
  btBattleResume() {
    mlog.debug('btBattleResume', { userId: this._userId });

    if (BehaviorActionType.BattleResume != this._curRequest.getActionType) {
      //init
      this._btStartBattleResume();
      return BehaviorRequestResultType.Pending;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return ret;
      } else if (BehaviorRequestResultType.Failure == ret) {
        return ret;
      }

      mlog.info('btBattleResume fin', { userId: this._userId, ret });
      return BehaviorRequestResultType.Success;
    }
  }

  //----------------------------------------------------------
  private _btStartEnterBattle(): boolean {
    mlog.debug('_btStartEnterBattle', { userId: this._userId });

    const curGameState = this.getLastGameState();
    const curGameEnterState = this.getGameEnterState();
    let packetIds: number[] = [];
    if (
      GAME_STATE.IN_OCEAN_BATTLE == curGameState &&
      curGameEnterState === GAME_ENTER_STATE.ENTERING
    ) {
      packetIds = [proto.Battle.START, proto.Battle.LOAD_COMPLETE];
    } else if (
      GAME_STATE.IN_OCEAN_BATTLE === curGameState &&
      curGameEnterState === GAME_ENTER_STATE.LOADING
    ) {
      packetIds = [proto.Battle.LOAD_COMPLETE];
    } else {
      mlog.error('_btStartEnterBattle unhandled gamestate', { userId: this._userId, curGameState });
      return false;
    }

    this._curRequest.setRequiredPacketTypes(packetIds);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;

    const startPacketId = packetIds[0];
    const func = reqSenders.sendMap[startPacketId];
    if (func) {
      mlog.debug('_btStartEnterBattle sending', {
        userId: this._userId,
        startPacketId,
        typeStr: proto.toString(startPacketId),
        func,
      });

      this._curRequest.setActionType = BehaviorActionType.EnterBattle;

      func(this);
    } else {
      return false;
    }

    return true;
  }

  //----------------------------------------------------------
  btEnterBattle() {
    mlog.debug('btEnterBattle', { userId: this._userId });

    if (BehaviorActionType.EnterBattle != this._curRequest.getActionType) {
      //init
      if (!this._btStartEnterBattle()) {
        return BehaviorRequestResultType.Failure;
      }

      return BehaviorRequestResultType.Pending;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return ret;
      } else if (BehaviorRequestResultType.Failure == ret) {
        return ret;
      }

      mlog.info('btEnterBattle fin', { userId: this._userId, ret });
      return BehaviorRequestResultType.Success;
    }
  }

  //----------------------------------------------------------
  private _btStartApplySimpleBattleEnd() {
    mlog.debug('_btStartApplySimpleBattleEnd', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([proto.Battle.END]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.ApplySimpleBattleEnd;

    reqSenders.sendBattleEnd(this);
  }

  //----------------------------------------------------------
  btApplySimpleBattleEnd() {
    mlog.debug('btApplySimpleBattleEnd', { userId: this._userId });

    if (BehaviorActionType.ApplySimpleBattleEnd != this._curRequest.getActionType) {
      //init
      this._btStartApplySimpleBattleEnd();
      return BehaviorRequestResultType.Pending;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return ret;
      } else if (BehaviorRequestResultType.Failure == ret) {
        return ret;
      }

      mlog.info('btApplySimpleBattleEnd fin', { userId: this._userId, ret });
      return BehaviorRequestResultType.Success;
    }
  }

  //----------------------------------------------------------
  btIsInOceanBattleReward(): boolean {
    mlog.debug('btIsInOceanBattleReward', { userId: this._userId });
    return this.isInOceanBattleReward();
  }

  //----------------------------------------------------------
  private _btStartHandleReward() {
    mlog.debug('_btStartHandleReward', { userId: this._userId });

    this._curRequest.setRequiredPacketTypes([
      proto.BattleReward.ENTER,
      proto.BattleReward.LOAD_COMPLETE,
      proto.BattleReward.LEAVE,
    ]);
    this._curRequest.setResult = BehaviorRequestResultType.Pending;
    this._curRequest.setActionType = BehaviorActionType.HandleBattleReward;

    reqSenders.sendBattleRewardEnter(this);
  }

  //----------------------------------------------------------
  btHandleReward() {
    mlog.debug('btHandleReward', { userId: this._userId });

    if (BehaviorActionType.HandleBattleReward != this._curRequest.getActionType) {
      //init
      this._btStartHandleReward();
      return BehaviorRequestResultType.Pending;
    } else {
      const ret = this.getBehaviorRequestResult();
      if (BehaviorRequestResultType.Pending == ret) {
        return ret;
      } else if (BehaviorRequestResultType.Failure == ret) {
        return ret;
      }

      mlog.info('btHandleReward fin', { userId: this._userId, ret });
      return BehaviorRequestResultType.Success;
    }
  }

  //----------------------------------------------------------
  btWaitForUserEnterBattleTimeout() {
    const curTimeUtc = mutil.curTimeUtc();
    if (BehaviorActionType.WaitTimeout != this._curRequest.getActionType) {
      //init
      this._btWaitTimeoutSet(curTimeUtc, BehaviorActionType.WaitTimeout);

      mlog.debug('btWaitForUserEnterBattleTimeout start', { userId: this._userId });

      return BehaviorRequestResultType.Pending;
    } else {
      const waitTime = CMSConst.get('BattleEncounterUserWaitTime'); //sec
      if (this._behaviorStat.startWaitTimeUtc + waitTime > curTimeUtc) {
        return BehaviorRequestResultType.Pending;
      }

      mlog.info('btWaitForUserEnterBattleTimeout fin', { userId: this._userId });
      return BehaviorRequestResultType.Success;
    }
  }

  //----------------------------------------------------------
  btPushNotificationAccordingToStopReason() {
    let pushId = PUSH_NOTIFICATION_ID.IN_OCEAN_ANCHORED;

    switch (this._stopReason) {
      case OFFLINE_SAILING_STOP_REASON.WORLD_TILE_UNENTERABLE:
      case OFFLINE_SAILING_STOP_REASON.WATER_FOOD_EXHAUSTED:
        pushId = PUSH_NOTIFICATION_ID.SAIL_STOPPED;
        break;
      default:
        break;
    }
    this.processPushNotification(pushId);

    mlog.info('btPushNotificationAccordingToStopReason fin', { userId: this._userId, pushId });
    return BehaviorRequestResultType.Success;
  }

  //----------------------------------------------------------
  btPushNotificationOfAnchored() {
    this.processPushNotification(PUSH_NOTIFICATION_ID.IN_OCEAN_ANCHORED);

    mlog.info('btPushNotificationOfAnchored fin', { userId: this._userId });
    return BehaviorRequestResultType.Success;
  }

  //----------------------------------------------------------
  btPushNotificationOfArriveToDestination() {
    this.processPushNotification(PUSH_NOTIFICATION_ID.ARRIVED_TO_TOWN);

    mlog.info('btPushNotificationOfArriveToDestination fin', { userId: this._userId });
    return BehaviorRequestResultType.Success;
  }

  //----------------------------------------------------------
  btPushNotificationOfShipWrecked() {
    this.processPushNotification(PUSH_NOTIFICATION_ID.SHIP_WRECKED);

    mlog.info('btPushNotificationOfShipWrecked fin', { userId: this._userId });
    return BehaviorRequestResultType.Success;
  }

  //----------------------------------------------------------
  // [todo] 미구현 BT API 들
  //----------------------------------------------------------
  btIsStartBotWaitTimeNotOver(): boolean {
    mlog.debug('btIsStartBotWaitTimeNotOver', { userId: this._userId });
    return false;
  }
  //----------------------------------------------------------
  btWaitForStartBotWaitTimeout(): boolean {
    mlog.debug('btWaitForStartBotWaitTimeout', { userId: this._userId });
    return false;
  }

  //----------------------------------------------------------
}

export default OfflineSailingBotClient;
