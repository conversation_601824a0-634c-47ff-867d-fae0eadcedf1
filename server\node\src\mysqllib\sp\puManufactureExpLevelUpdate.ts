// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { MError, MErrorCode } from '../../motiflib/merror';
import { MANUFACTURE_TYPE } from '../../cms/manufactureExpDesc';
import { ManufactureExpLevelChange } from '../../motiflib/model/lobby';

export const spName = 'mp_u_manufacture_exp_level_update';
export const errorCode = MErrorCode.MANUFACTURE_EXP_LEVEL_UPDATE_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  changes: ManufactureExpLevelChange[]
): Promise<void> {
  // 초기값 설정
  let castingExp: number | undefined;
  let castingLevel: number | undefined;
  let cookingExp: number | undefined;
  let cookingLevel: number | undefined;
  let sewingExp: number | undefined;
  let sewingLevel: number | undefined;
  let handmadeExp: number | undefined;
  let handmadeLevel: number | undefined;
  let medicineExp: number | undefined;
  let medicineLevel: number | undefined;

  // ManufactureExpLevelChange 배열을 순회하며 해당 타입에 맞게 값 설정
  for (const change of changes) {
    switch (change.type) {
      case MANUFACTURE_TYPE.CASTING:
        castingExp = change.exp;
        castingLevel = change.level;
        break;
      case MANUFACTURE_TYPE.COOKING:
        cookingExp = change.exp;
        cookingLevel = change.level;
        break;
      case MANUFACTURE_TYPE.SEWING:
        sewingExp = change.exp;
        sewingLevel = change.level;
        break;
      case MANUFACTURE_TYPE.HANDMADE:
        handmadeExp = change.exp;
        handmadeLevel = change.level;
        break;
      case MANUFACTURE_TYPE.MEDICINE:
        medicineExp = change.exp;
        medicineLevel = change.level;
        break;
    }
  }

  return spFunction(
    connection,
    userId,
    castingExp,
    castingLevel,
    cookingExp,
    cookingLevel,
    sewingExp,
    sewingLevel,
    handmadeExp,
    handmadeLevel,
    medicineExp,
    medicineLevel
  )
    .then((qr) => {
      return;
    })
    .catch(catchHandler);
}
