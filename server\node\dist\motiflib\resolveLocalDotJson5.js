"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveLocalDotJson5 = exports.getOverridedBinaryCodeSuffix = exports.getOverridedBinaryCode = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
let _cachedOverridedBinaryCode = null;
function getOverridedBinaryCode() {
    if (_cachedOverridedBinaryCode !== null) {
        return _cachedOverridedBinaryCode;
    }
    const localFilePath = path_1.default.resolve(path_1.default.join(__dirname, '..', '..', '..', '.override-binarycode'));
    let result;
    try {
        result = fs_1.default.readFileSync(localFilePath, 'utf8').trim();
    }
    catch (err) {
        result = '';
    }
    _cachedOverridedBinaryCode = result;
    return result;
}
exports.getOverridedBinaryCode = getOverridedBinaryCode;
function getOverridedBinaryCodeSuffix() {
    const binaryCode = getOverridedBinaryCode();
    return binaryCode ? `.${binaryCode.toLowerCase()}` : '';
}
exports.getOverridedBinaryCodeSuffix = getOverridedBinaryCodeSuffix;
function resolveLocalDotJson5() {
    const suffix = getOverridedBinaryCodeSuffix();
    return `local${suffix}.json5`;
}
exports.resolveLocalDotJson5 = resolveLocalDotJson5;
//# sourceMappingURL=resolveLocalDotJson5.js.map