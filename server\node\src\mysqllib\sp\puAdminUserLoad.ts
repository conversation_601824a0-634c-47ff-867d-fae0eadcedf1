// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

// admind 에서만 사용되어야 됨

import * as query from '../query';
import { DbQuestCompletionField, DbQuestContext } from '../../lobbyd/quest';
import mlog from '../../motiflib/mlog';

export const spName = 'mp_u_admin_user_load';

export interface LoginResultShip {
  id: number;
  cmsId: number;
  assignment: number;
  fleetIndex: number;
  formationIndex: number;
  sailor: number;
  durability: number;
  permanentDamage: number;
  name: string;
  life: number;
  enchantedStatType1: number;
  enchantedStatValue1: number;
  enchantedStatType2: number;
  enchantedStatValue2: number;
  enchantedStatType3: number;
  enchantedStatValue3: number;
  enchantResult: string;
  enchantCount: number;
  rndStats: string;
  isBound: number;
  guid: string;
}
export interface Result {
  userId: number;
  name: string;
  lang: string;
  gameState: number;
  lastGameState: number;
  isOnline: number;
  lastTownCmsId: number;
  arrivalTownCmsId: number;
  nationCmsId: number;
  companyJobCmsId: number;
  exp: number;
  level: number;
  isSlowdownEventChecked: number;
  lastCompletedQuestCmsIdForRequest: number;
  lastRewardedAchievementPointCmsId: number;
  noviceSupplyCount: number;
  lastNoviceSupplyTimeUtc: string;
  energy: number;
  lastUpdateEnergyTimeUtc: string;
  palaceRoyalOrderCmsId: number;
  palaceRoyalOrderRnds: string;
  lastRoyalOrderCompletedTimeUtc: string;
  contractedCollectorTownBuildingCmsId: number;
  guildId: number;
  tradeResetCount: number;
  lastTradeResetTimeUtc: string;
  karma: number;
  lastKarmaUpdateTimeUtc: string;
  usedFreeTurnTakebackCount: number;
  usedFreePhaseTakebackCount: number;
  lastFreeTakebackUpdateTimeUtc: string;
  quickModeCount: number;
  lastQuickModeCountUpdateTimeUtc: string;
  lastCompanyJobUpdateTimeUtc: string;
  countryIp: string;
  firstMateCmsId: number;
  westShipBuildLevel: number;
  westShipBuildExp: number;
  orientShipBuildLevel: number;
  orientShipBuildExp: number;
  representedMateCmsId: number;
  manufacturePoint: number;
  lastUpdateManufacturePointTimeUtc: string;
  mates: {
    mateNub: {
      cmsId: number;
      loyalty: number | null;
      stateFlags: number;
      adventureExp: number;
      adventureLevel: number;
      tradeExp: number;
      tradeLevel: number;
      battleExp: number;
      battleLevel: number;
      adventureFame: number;
      tradeFame: number;
      battleFame: number;
      royalTitle: number;
      injuryExpireTimeUtc: string;
      lastTalkTimeUtc: string;
      awakenLv: number;
      awakenTimeUtc: string;
      colorSkin: number;
      colorEye: number;
      colorHairAcc1: number;
      colorHairAcc2: number;
      colorHair: number;
      colorBody1: number;
      colorBody2: number;
      colorFaceAcc1: number;
      colorFaceAcc2: number;
      colorFaceAcc3: number;
      colorCape1: number;
      colorCape2: number;
      colorCape3: number;
      breastSize: number;
      trainingGrade: number;
      trainingPoints: string;
      equippedIllustCmsId: number;
    };
  }[];
  mateEquipments: {
    mateEquipmentNub: {
      id: number;
      cmsId: number;
      dye1: number;
      dye2: number;
      dye3: number;
      dye4: number;
      dye5: number;
      dye6: number;
      equippedMateCmsId: number;
      isBound: number;
      isCostume: number;
      expireTimeUtc: string;
      enchantLevel: number;
    };
  }[];
  sidekickMates: {
    slot: number;
    cmsId: number;
  }[];
  points: {
    cmsId: number;
    value: number;
  }[];
  installmentSavings: {
    accumPoint: string;
    accumRate: number;
    accumCount: number;
    lastCmsId: number;
    lastDepositTimeUtc: string;
  };
  fleets: {
    fleetIndex: number;
    battleFormationCmsId: number;
  }[];
  shipBuildings: {
    shipId: number;
    shipCmsId: number;
    rndStats: string;
    expireTimeUtc: string;
    townCmsId: number;
  }[];
  ships: LoginResultShip[];
  // shipSlot 설명
  // mateCmsId 값이 0 초과인 경우: 선실에 항해사가 타고 있는 경우
  // mateCmsId 값이 0인 경우: 선실에 항해사 타고 있지 않은 경우
  // mateCmsId 값이 널인 경우: 부품인 경우
  // shipSlotItemId 값이 0 초과인 경우: 부품 장착된 경우
  // shipSlotItemId 값이 0인 경우: 부품 장착되지 않은 경우
  // shipSlotItemId 값이 널인 경우: 선실인 경우
  shipSlots: {
    shipId: number;
    slotIndex: number;
    mateCmsId: number;
    isLocked: number;
    shipSlotItemId: number;
  }[];
  shipCargos: {
    shipId: number;
    cmsId: number;
    quantity: number;
    pointInvested: string;
  }[];
  items: {
    cmsId: number;
    count: number;
    unboundCount: number;
  }[];
  shipBlueprints: {
    cmsId: number;
    level: number;
    exp: number;
    sailMasteryLevel: number;
    sailMasteryExp: number;
  }[];
  shipBlueprintSlots: {
    shipBlueprintCmsId: number;
    slotIndex: number;
    shipSlotCmsId: number;
  }[];
  reputations: {
    nationCmsId: number;
    reputation: number;
    updateTimeUtc: number;
  }[];
  unemployedMates: {
    mateCmsId: number;
    intimacy: number;
    isMet: number;
  }[];
  directMails: {
    id: number;
    cmsId: number;
    state: number;
    createTimeUtc: string;
    readTimeUtc: string;
    deleteTimeUtc: string;
    expireTimeUtc: string;
    bShouldSetExpirationWhenReceiveAttachment: number;
    title: string;
    titleFormatValue: number;
    body: string;
    bodyFormatValue: number;
    attachment: string;
  }[];
  questCompletionFields: DbQuestCompletionField[];
  questContexts: DbQuestContext[];
  lastMateEquipmentId: number;
  lastShipId: number;
  lastDirectMailId: number;
  worldBuffs: {
    groupNo: number;
    cmsId: number;
    targetId: number;
    sourceType: number;
    sourceId: number;
    stack: number;
    startTimeUtc: string;
    endTimeUtc: string;
  }[];
  battleRewards: {
    type: number;
    cmsId: number;
    quantity: number;
    receivedQuantity: number;
  }[];
  gameOverLosses: string;
  multiPvpLoss: string;
  sailing: {
    daysForLoyaltyDecrease: number;
    daysForTownReset: number;
    totalSailedDays: number;
  };
  insurance: {
    insuranceCmsId: number;
    unpaidTradeGoods: string;
    unpaidShip: string;
    unpaidSailor: string;
    unpaidDucat: string;
  };
  slotExpansions: {
    type: number;
    expanded: number;
  }[];
  blackMarket: {
    lastBlackMarketResetTimeUtc: string;
    blackMarketResetCount: number;
  };
  collections: {
    collectionCmsId: number;
    slotIndex: number;
    stack: number;
  }[];
  cargoLoadPreset: {
    water: number;
    food: number;
    lumber: number;
    ammo: number;
    tradeGoods: number;
    any: number;
  };
  discoveries: {
    offset: number;
    idxField: string;
  }[];
  achievements: {
    cmsId: number;
    count: string;
    isRewarded: number;
  }[];
  tasks: {
    category: number;
    lastResetTimeUtc: string;
    cmsId1: number;
    count1: string;
    isRewarded1: number;
    cmsId2: number;
    count2: string;
    isRewarded2: number;
    cmsId3: number;
    count3: string;
    isRewarded3: number;
    cmsId4: number;
    count4: string;
    isRewarded4: number;
    cmsId5: number;
    count5: string;
    isRewarded5: number;
    cmsId6: number;
    count6: string;
    isRewarded6: number;
    cmsId7: number;
    count7: string;
    isRewarded7: number;
    cmsId8: number;
    count8: string;
    isRewarded8: number;
    cmsId9: number;
    count9: string;
    isRewarded9: number;
    cmsId10: number;
    count10: string;
    isRewarded10: number;
    isCategoryRewarded: number;
  }[];
  contentsTermsProgresses: {
    cmsId: number;
    target: number;
    count: string;
  }[];
  taxFreePermits: {
    cmsId: number;
    expirationTimeUtc: string;
  }[];
  revealedWorldMapTiles: {
    offset: number;
    idxField: string;
  }[];
  townStates: {
    cmsId: number;
    sailor: number;
    overDraftedSailor: number;
    isInvested: number;
    isTradedGoods: number;
    arrestState: number;
  }[];
  requestSlots: {
    idx: number;
    purchaseTimeUtc: string;
  }[];
  cashShopRestrictedProducts: {
    cmsId: number;
    amount: number;
    lastBuyingTimeUtc: string;
  }[];
  cashShopFixedTermProducts: {
    cmsId: number;
    startTimeUtc: string;
    endTimeUtc: string;
  }[];
  cashShopGachaBoxGuarantees: {
    cashShopCmsId: number;
    accum: number;
  }[];
  reportedDiscoveries: {
    offset: number;
    idxField: string;
  }[];
  lastReportedDiscovery: {
    hash: string;
    data: string;
    createTimeUtc: string;
  };
  soundPacks: {
    offset: number;
    idxField: string;
  }[];
  questGlobalRegisters: {
    g0: number;
    g1: number;
    g2: number;
    g3: number;
    g4: number;
    g5: number;
    g6: number;
    g7: number;
    g8: number;
    g9: number;
  };
  questionPlaces: {
    offset: number;
    idxField: string;
  }[];
  reportedWorldMapTiles: {
    offset: number;
    idxField: string;
  }[];
  landExploreResult: {
    discoveryBox: number;
    boxes: string;
    result: string;
    gainItem: number;
  };
  shipSailPatterns: {
    sailPatternCmsId: number;
    color1: number;
    color2: number;
    color3: number;
  }[];
  shipSailCrests: {
    offset: number;
    idxField: string;
  }[];
  shipSailPatternColors: {
    offset: number;
    idxField: string;
  }[];
  shipBody1Colors: {
    offset: number;
    idxField: string;
  }[];
  shipBody2Colors: {
    offset: number;
    idxField: string;
  }[];
  shipCustomizing: {
    sailPatternCmsId: number;
    sailCrestCmsId: number;
    bodyColor1: number;
    bodyColor2: number;
    bodyColorTint: number;
    camouflageCmsId: number;
  };
  villages: {
    cmsId: number;
    friendship: number;
    lastDepartureTimeUtc: string;
  }[];
  matePassives: {
    mateCmsId: number;
    passiveCmsId: number;
    equipIndex: number;
  }[];
  matePassiveLearnings: {
    mateCmsId: number;
    passiveCmsId: number;
    learnTimeUtc: string;
  }[];
  lastQuestItemId: number;
  questItems: {
    id: number;
    itemCmsId: number;
    questCmsId: number;
    questRnds: string;
  }[];
  attendances: {
    eventPageCmsId: number;
    accum: number;
    consecutive: number;
    lastAttendanceTimeUtc: string;
    lastRewardedConsecutiveAttendanceCmsId: number;
  }[];
  challenges: {
    cmsId: number;
    clearedDifficulty: number;
    clearedMissionField: number;
  }[];
  mateEquipmentColors: {
    offset: number;
    idxField: string;
  }[];
  lastShipSlotItemId: number;
  shipSlotItems: {
    id: number;
    shipSlotCmsId: number;
    isBound: number;
    isLocked: number;
    expireTimeUtc: string;
    enchantLevel: number;
  }[];
  battle: {
    battleEndResult: string;
  };
  battleFormations: {
    cmsId: number;
  }[];
  pubStaffs: {
    townCmsId: number;
    intimacy: number;
    unlockInterest: number;
    dailyTalkCount: number;
    lastNominationRefreshTimeUtc: string;
    questBlockTimeUtc: string;
  }[];
  mileages: {
    month: number;
    value: number;
  }[];
  userTitles: {
    cmsId: number;
    expiredTimeUtc: string;
    isEquipped: number;
  }[];
  eventPageProducts: {
    offset: number;
    idxField: string;
  }[];
  shipCamouflages: {
    offset: number;
    idxField: string;
  }[];
  costumeShipSlots: {
    subSlotType: number;
    shipSlotItemId: number;
  }[];
  hotSpotProducts: {
    cmsId: number;
    popupCount: number;
    expireTimeUtc: string;
    coolTimeUtc: string;
  }[];
  manufactureExpLevel: {
    castingExp: number;
    castingLevel: number;
    cookingExp: number;
    cookingLevel: number;
    sewingExp: number;
    sewingLevel: number;
    handmadeExp: number;
    handmadeLevel: number;
    medicineExp: number;
    medicineLevel: number;
  };
}

const spFunction = query.generateSPFunction(spName);

export default function (
  connection: query.Connection,
  userId: number,
  curTimeUtc: number
): Promise<Result> {
  return spFunction(connection, userId, curTimeUtc).then((qr) => {
    const rows = qr.rows;
    let rowIndex = 0;
    const userData = rows[rowIndex][0];

    rowIndex += 1;
    const mateRows = rows[rowIndex];
    const mates = [];
    for (const key of Object.keys(mateRows)) {
      mates.push({
        mateNub: mateRows[key],
      });
    }

    rowIndex += 1;
    const mateEquipmentRows = rows[rowIndex];
    const mateEquipments = [];
    if (mateEquipmentRows) {
      for (const key of Object.keys(mateEquipmentRows)) {
        mateEquipments.push({
          mateEquipmentNub: mateEquipmentRows[key],
        });
      }
    }

    rowIndex += 1;
    const sidekickMates = rows[rowIndex];

    rowIndex += 1;
    const points = rows[rowIndex].map((row) =>
      Object.assign(row, { value: parseInt(row.value, 10) })
    );

    rowIndex += 1;
    const installmentSavings = Object.assign(rows[rowIndex][0], {
      accumPoint: parseInt(rows[rowIndex][0].accumPoint, 10),
    });

    rowIndex += 1;
    const fleets = rows[rowIndex];

    rowIndex += 1;
    const shipBuildings = rows[rowIndex];

    rowIndex += 1;
    const ships = rows[rowIndex];
    rowIndex += 1;
    const shipSlots = rows[rowIndex];

    rowIndex += 1;
    const shipCargos = rows[rowIndex];

    rowIndex += 1;
    const items = rows[rowIndex];

    rowIndex += 1;
    const shipBlueprints = rows[rowIndex];

    rowIndex += 1;
    const shipBlueprintSlots = rows[rowIndex];

    rowIndex += 1;
    const reputations = rows[rowIndex].map((row) => {
      const updateTimeUtc = parseInt(row.updateTimeUtc, 10);
      return Object.assign(row, { updateTimeUtc });
    });

    rowIndex += 1;
    const unemployedMates = rows[rowIndex];

    rowIndex += 1;
    const directMails = rows[rowIndex];

    rowIndex += 1;
    const questCompletionFields = rows[rowIndex];

    rowIndex += 1;
    const questContexts = rows[rowIndex];

    rowIndex += 1;
    let lastMateEquipmentId = 0;
    if (rows[rowIndex].length > 0) {
      lastMateEquipmentId = rows[rowIndex][0].mateEquipmentId;
    }

    rowIndex += 1;
    let lastShipId = 0;
    if (rows[rowIndex].length > 0) {
      lastShipId = rows[rowIndex][0].shipId;
    }

    rowIndex += 1;
    let lastDirectMailId = 0;
    if (rows[rowIndex].length > 0) {
      lastDirectMailId = rows[rowIndex][0].directMailId;
    }

    rowIndex += 1;
    const worldBuffs = rows[rowIndex];

    rowIndex += 1;
    const battleRewards = rows[rowIndex];

    rowIndex += 1;
    let gameOverLosses;
    let multiPvpLoss;
    if (rows[rowIndex].length > 0) {
      gameOverLosses = rows[rowIndex][0].losses;
      multiPvpLoss = rows[rowIndex][0].multiPvpLoss;
    }

    rowIndex += 1;
    const sailing =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            daysForLoyaltyDecrease: 0,
            daysForTownReset: 0,
            totalSailedDays: 0,
          };

    rowIndex += 1;
    const insurance =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            insuranceCmsId: 0,
            unpaidTradeGoods: '0',
            unpaidShip: '0',
            unpaidSailor: '0',
            unpaidDucat: '0',
          };

    rowIndex += 1;
    const slotExpansions = rows[rowIndex];

    rowIndex += 1;
    const blackMarket =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            lastBlackMarketResetTimeUtc: '1',
            blackMarketResetCount: 0,
          };

    rowIndex += 1;
    const collections = rows[rowIndex];

    rowIndex += 1;
    let cargoLoadPreset = null;
    if (rows[rowIndex].length > 0) {
      cargoLoadPreset = rows[rowIndex][0];
    }

    rowIndex += 1;
    const discoveries = rows[rowIndex];

    rowIndex += 1;
    const achievements = rows[rowIndex];

    rowIndex += 1;
    const tasks = rows[rowIndex];

    rowIndex += 1;
    const contentsTermsProgresses = rows[rowIndex];

    rowIndex += 1;
    const taxFreePermits = rows[rowIndex];

    rowIndex += 1;
    const revealedWorldMapTiles = rows[rowIndex];

    rowIndex += 1;
    const townStates = rows[rowIndex];

    rowIndex += 1;
    const requestSlots = rows[rowIndex];

    rowIndex += 1;
    const cashShopRestrictedProducts = rows[rowIndex];

    rowIndex += 1;
    const cashShopFixedTermProducts = rows[rowIndex];

    rowIndex += 1;
    const cashShopGachaBoxGuarantees = rows[rowIndex];

    rowIndex += 1;
    const reportedDiscoveries = rows[rowIndex];

    rowIndex += 1;
    // let lastReportedDiscovery = null;
    // if (rows[rowIndex].length > 0) {
    //   lastReportedDiscovery = rows[rowIndex][0];
    // }
    const lastReportedDiscovery = rows[rowIndex][0];

    rowIndex += 1;
    const soundPacks = rows[rowIndex];

    rowIndex += 1;
    const questGlobalRegisters =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            g0: 0,
            g1: 0,
            g2: 0,
            g3: 0,
            g4: 0,
            g5: 0,
            g6: 0,
            g7: 0,
            g8: 0,
            g9: 0,
          };

    rowIndex += 1;
    const questionPlaces = rows[rowIndex];

    rowIndex += 1;
    const reportedWorldMapTiles = rows[rowIndex];

    rowIndex += 1;
    const landExploreResult =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            discoveryBox: null,
            boxes: '0',
            result: null,
            gainItem: null,
          };

    rowIndex += 1;
    const shipSailPatterns = rows[rowIndex];

    rowIndex += 1;
    const shipSailCrests = rows[rowIndex];

    rowIndex += 1;
    const shipSailPatternColors = rows[rowIndex];

    rowIndex += 1;
    const shipBody1Colors = rows[rowIndex];

    rowIndex += 1;
    const shipBody2Colors = rows[rowIndex];

    rowIndex += 1;
    const shipCustomizing =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            sailPatternCmsId: null,
            sailCrestCmsId: null,
            bodyColor1: null,
            bodyColor2: null,
            bodyColorTint: null,
          };

    rowIndex += 1;
    const villages = rows[rowIndex];

    rowIndex += 1;
    const matePassives = rows[rowIndex];

    rowIndex += 1;
    const matePassiveLearnings = rows[rowIndex];

    rowIndex += 1;
    let lastQuestItemId = 0;
    if (rows[rowIndex].length > 0) {
      lastQuestItemId = rows[rowIndex][0].questItemId;
    }

    rowIndex += 1;
    const questItems = rows[rowIndex];

    rowIndex += 1;
    const attendances = rows[rowIndex];

    rowIndex += 1;
    const challenges = rows[rowIndex];

    rowIndex += 1;
    const mateEquipmentColors = rows[rowIndex];

    rowIndex += 1;
    let lastShipSlotItemId = 0;
    if (rows[rowIndex].length > 0) {
      lastShipSlotItemId = rows[rowIndex][0].shipSlotItemId;
    }

    rowIndex += 1;
    const shipSlotItems = rows[rowIndex];

    rowIndex += 1;
    const battle =
      rows[rowIndex].length > 0
        ? rows[rowIndex][0]
        : {
            battleEndResult: null,
          };

    rowIndex += 1;
    const battleFormations = rows[rowIndex];

    rowIndex += 1;
    const pubStaffs = rows[rowIndex];

    rowIndex += 1;
    const mileages = rows[rowIndex];

    rowIndex += 1;
    const userTitles = rows[rowIndex];

    rowIndex += 1;
    const eventPageProducts = rows[rowIndex];

    rowIndex += 1;
    const shipCamouflages = rows[rowIndex];

    rowIndex += 1;
    const costumeShipSlots = rows[rowIndex];

    rowIndex += 1;
    const hotSpotProducts = rows[rowIndex];

    rowIndex += 1;
    let manufactureExpLevel = null;
    if (rows[rowIndex].length > 0) {
      manufactureExpLevel = rows[rowIndex][0];
    } else {
      manufactureExpLevel = {
        castingExp: 0,
        castingLevel: 1,
        cookingExp: 0,
        cookingLevel: 1,
        sewingExp: 0,
        sewingLevel: 1,
        handmadeExp: 0,
        handmadeLevel: 1,
        medicineExp: 0,
        medicineLevel: 1,
      };
    }

    return {
      userId,
      name: userData.name,
      lang: userData.lang,
      gameState: userData.gameState,
      lastGameState: userData.lastGameState,
      isOnline: userData.isOnline,
      lastTownCmsId: userData.lastTownCmsId,
      arrivalTownCmsId: userData.arrivalTownCmsId,
      companyJobCmsId: userData.companyJobCmsId,
      nationCmsId: userData.nationCmsId,
      exp: userData.exp,
      level: userData.level,
      lastBlackMarketResetTimeUtc: userData.lastBlackMarketResetTimeUtc,
      blackMarketResetCount: userData.blackMarketResetCount,
      isSlowdownEventChecked: userData.isSlowdownEventChecked,
      lastCompletedQuestCmsIdForRequest: userData.lastCompletedQuestCmsIdForRequest,
      lastRewardedAchievementPointCmsId: userData.lastRewardedAchievementPointCmsId,
      noviceSupplyCount: userData.noviceSupplyCount,
      lastNoviceSupplyTimeUtc: userData.lastNoviceSupplyTimeUtc,
      energy: userData.energy,
      lastUpdateEnergyTimeUtc: userData.lastUpdateEnergyTimeUtc,
      palaceRoyalOrderCmsId: userData.palaceRoyalOrderCmsId,
      palaceRoyalOrderRnds: userData.palaceRoyalOrderRnds,
      lastRoyalOrderCompletedTimeUtc: userData.lastRoyalOrderCompletedTimeUtc,
      contractedCollectorTownBuildingCmsId: userData.contractedCollectorTownBuildingCmsId,
      guildId: userData.guildId,
      tradeResetCount: userData.tradeResetCount,
      lastTradeResetTimeUtc: userData.lastTradeResetTimeUtc,
      karma: userData.karma,
      lastKarmaUpdateTimeUtc: userData.lastKarmaUpdateTimeUtc,
      usedFreeTurnTakebackCount: userData.usedFreeTurnTakebackCount,
      usedFreePhaseTakebackCount: userData.usedFreePhaseTakebackCount,
      lastFreeTakebackUpdateTimeUtc: userData.lastFreeTakebackUpdateTimeUtc,
      quickModeCount: userData.quickModeCount,
      lastQuickModeCountUpdateTimeUtc: userData.lastQuickModeCountUpdateTimeUtc,
      lastCompanyJobUpdateTimeUtc: userData.lastCompanyJobUpdateTimeUtc,
      countryIp: userData.countryIp,
      firstMateCmsId: userData.firstMateCmsId,
      westShipBuildLevel: userData.westShipBuildLevel,
      westShipBuildExp: userData.westShipBuildExp,
      orientShipBuildLevel: userData.orientShipBuildLevel,
      orientShipBuildExp: userData.orientShipBuildExp,
      representedMateCmsId: userData.representedMateCmsId,
      manufacturePoint: userData.manufacturePoint,
      lastUpdateManufacturePointTimeUtc: userData.lastUpdateManufacturePointTimeUtc,
      mates,
      mateEquipments,
      sidekickMates,
      points,
      installmentSavings,
      fleets,
      shipBuildings,
      ships,
      shipSlots,
      shipCargos,
      items,
      shipBlueprints,
      shipBlueprintSlots,
      reputations,
      unemployedMates,
      directMails,
      questCompletionFields,
      questContexts,
      lastMateEquipmentId,
      lastShipId,
      lastDirectMailId,
      worldBuffs,
      battleRewards,
      gameOverLosses,
      multiPvpLoss,
      sailing,
      insurance,
      slotExpansions,
      blackMarket,
      collections,
      cargoLoadPreset,
      discoveries,
      achievements,
      tasks,
      contentsTermsProgresses,
      taxFreePermits,
      revealedWorldMapTiles,
      townStates,
      requestSlots,
      cashShopRestrictedProducts,
      cashShopFixedTermProducts,
      cashShopGachaBoxGuarantees,
      reportedDiscoveries,
      lastReportedDiscovery,
      soundPacks,
      questGlobalRegisters,
      questionPlaces,
      reportedWorldMapTiles,
      landExploreResult,
      shipSailPatterns,
      shipSailCrests,
      shipSailPatternColors,
      shipBody1Colors,
      shipBody2Colors,
      shipCustomizing,
      villages,
      matePassives,
      matePassiveLearnings,
      lastQuestItemId,
      questItems,
      attendances,
      challenges,
      mateEquipmentColors,
      lastShipSlotItemId,
      shipSlotItems,
      battle,
      battleFormations,
      pubStaffs,
      mileages,
      userTitles,
      eventPageProducts,
      shipCamouflages,
      costumeShipSlots,
      hotSpotProducts,
      manufactureExpLevel,
    };
  });
}
