// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';

import { Resp } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { ClientPacketHandler } from '../index';
import { TownDesc } from '../../../cms/townDesc';
import {
  Changes,
  CHANGE_TASK_REASON,
  CHANGE_TASK_RESULT,
  TryData,
  UserChangeTask,
} from '../../UserChangeTask/userChangeTask';
import {
  RewardAndPaymentElem,
  RewardAndPaymentSpec,
  RNP_TYPE,
} from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import {
  opAddPoint,
  opDeleteContributionShopRestrictedProduct,
  opSetContributionShopRestrictedProduct,
} from '../../UserChangeTask/userChangeOperator';
import {
  ContributionShopRestrictedProduct,
  UNBUYABLE_REASON,
  ContributionShopUtil as ContributionShopUtil,
} from '../../userContributionShop';
import { PrData, RewardData } from '../../../motiflib/gameLog';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';

// ----------------------------------------------------------------------------
// 왕궁 공헌도 상점에서 구매
// ----------------------------------------------------------------------------

const rsn = 'palace_buy_contribution_shop_product';
const add_rsn = null;

interface RequestBody {
  contributionShopCmsId: number;
  amount: number;
  bPermitExchange?: boolean;
}

interface ResponseBody extends Resp {
  //
}

// ----------------------------------------------------------------------------
export class Cph_Town_PalaceBuyContributionShopProduct implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const body: RequestBody = packet.bodyObj;
    const { contributionShopCmsId, bPermitExchange } = body;
    const amount = body.amount ?? 1;

    const contributionShopCms = cms.ContributionShop[contributionShopCmsId];

    // 인자
    if (!contributionShopCms) {
      throw new MError(
        'invalid-contribution-shop-cms-id',
        MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
        { reqBody: body }
      );
    }
    if (!Number.isInteger(amount)) {
      throw new MError(
        'invalid-amount',
        MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
        {
          reqBody: body,
        }
      );
    }
    if (amount < 1 || amount > cms.Const.ProductVolumeMaxVal.value) {
      // 최대치 Const 값은 단순히 UI 프로그래스바에서 사용할 목적일 법한데,
      // 추후 최대치 관련해서 성능 문제 확인 필요.
      throw new MError(
        'invalid-amount-range',
        MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
        { reqBody: body, max: cms.Const.ProductVolumeMaxVal.value }
      );
    }

    const curTimeUtc = mutil.curTimeUtc();

    // 항구
    user.userState.ensureInTown();
    const townCmsId = user.userTown.getTownCmsId();
    const townCms = cms.Town[townCmsId];

    // 건물
    ensurePalace(user, townCms);
    // 메뉴
    user.userContentsTerms.ensureBuildingContentsUnlock(
      cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
      user
    );

    // 상품
    if (!user.userContentsTerms.isValidContentsTerms(contributionShopCms.contentsTerms, user)) {
      throw new MError('contents-terms-invalid', MErrorCode.CONTENTS_TERMS_INVALID, {
        cmsContentsTerms: contributionShopCms.contentsTerms,
        reqBody: body,
      });
    }
    if (contributionShopCms.productNationId !== undefined) {
      // 본거지(왕궁이 있는 항구)의 국가는 바뀌지 않는다는 기획이 있어서
      // 굳이 TownManager 에서 항구의 국가를 불러오진 않음.
      if (contributionShopCms.productNationId !== townCms.nationId) {
        throw new MError(
          'not-sold-in-this-town-nation',
          MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
          { reqBody: body, townNationCmsId: townCms.nationId }
        );
      }
    }

    const restrictedProducts = user.userContributionShop.getRestrictedProducts();
    const restrictedProductBoughtCounts: { [cmsId: number]: number } = {};
    let restrictedProductCmsIdsToDelete: Set<number>;

    const curSessionSeqNum = ContributionShopUtil.calcSessionSequenceNum(curTimeUtc);
    if (curSessionSeqNum === undefined) {
      throw new MError(
        'contribution-shop-session-has-not-started',
        MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
        { reqBody: body }
      );
    }
    const lastRestrictedProductBoughtSessionSeqNum =
      user.userContributionShop.getLastRestrictedProductBoughtSessionSequenceNum();

    // 예전 세션 기록들을 DB 에서 청소(?)해 준다.
    // 세션 시작 시간(Define)을 변경해서 구입 기록이 의미 없어진 경우도 있는데, 이 때도 지워준다.
    if (
      lastRestrictedProductBoughtSessionSeqNum === undefined ||
      curSessionSeqNum !== lastRestrictedProductBoughtSessionSeqNum
    ) {
      restrictedProductCmsIdsToDelete = new Set(
        user.userContributionShop.getResetableRestrictedProductCmsIds()
      );
    } else {
      const expiredRestrictedProduct =
        user.userContributionShop.getExpiredRestrictedProducts(curTimeUtc);

      _.forOwn(restrictedProducts, (elem) => {
        if (!expiredRestrictedProduct.has(elem.cmsId)) {
          restrictedProductBoughtCounts[elem.cmsId] = elem.amount;
        }
      });
      restrictedProductCmsIdsToDelete = expiredRestrictedProduct;
    }

    // contentsTerms, productNationId 는 위에서 검증해서 일관성이 없어 보이는데..
    // 어쩌다 보니 분리됐는데 적어도 productNationId 는 isBuyableProduct 에 넣는게 좋을듯?
    const unbuyableResult = ContributionShopUtil.isBuyableProduct(
      contributionShopCms,
      curSessionSeqNum,
      restrictedProductBoughtCounts,
      amount
    );
    if (unbuyableResult !== UNBUYABLE_REASON.BUYABLE) {
      throw new MError(
        `can-not-buy-contribution-shop-product, reason: ${UNBUYABLE_REASON[unbuyableResult]}`,
        MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
        {
          reqBody: body,
          restrictedProductBoughtCounts,
          curSessionSeqNum,
          curGroup: ContributionShopUtil.getGroup(curSessionSeqNum),
          amount,
        }
      );
    }

    let restrictedProductChange: ContributionShopRestrictedProduct;

    if (ContributionShopUtil.isRestrictionSaleType(contributionShopCms.saleType)) {
      if (
        restrictedProductCmsIdsToDelete.has(contributionShopCms.id) ||
        !restrictedProducts[contributionShopCms.id]
      ) {
        restrictedProductChange = {
          cmsId: contributionShopCms.id,
          amount,
          lastBuyingTimeUtc: curTimeUtc,
        };
      } else {
        restrictedProductChange = {
          cmsId: contributionShopCms.id,
          amount: restrictedProducts[contributionShopCms.id].amount + amount,
          lastBuyingTimeUtc: curTimeUtc,
        };
      }
    } else if (
      cmsEx.isContributionShopPreviousId(contributionShopCms.id) &&
      !restrictedProducts[contributionShopCms.id]
    ) {
      // 하나라도 구입한 적이 있는지 플래그 역할만 함.
      restrictedProductChange = {
        cmsId: contributionShopCms.id,
        amount: 1,
        lastBuyingTimeUtc: curTimeUtc,
      };
    }

    const resp: ResponseBody = { sync: {} };

    const pointCost = {
      cmsId: contributionShopCms.salePointId,
      cost: contributionShopCms.salePointVal * amount,
    };

    const userChangeTask = new UserChangeTask(
      user,
      CHANGE_TASK_REASON.PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
      new PalaceBuyContributionShopProductSpec(
        new Array(amount).fill(contributionShopCms.productRewardFixedId),
        pointCost,
        bPermitExchange,
        restrictedProductCmsIdsToDelete,
        restrictedProductChange
      ),
      add_rsn
    );

    return Promise.resolve()
      .then(() => {
        const res = userChangeTask.trySpec();
        if (res > CHANGE_TASK_RESULT.OK_MAX) {
          throw new MError(
            'failed-to-buy-contribution-shop-product',
            MErrorCode.FAILED_TO_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT_USER_CHANGE_TASK,
            { res, reqBody: body }
          );
        }

        return userChangeTask.apply();
      })
      .then((sync) => {
        const gainForRewardData = userChangeTask.getActualGain();
        const pr_data: PrData[] = [{ type: pointCost.cmsId, amt: pointCost.cost }];
        const reward_data: RewardData[] =
          UserChangeTask.covertActualGainToGLogRewardData(gainForRewardData);

        user.glog('contribution_shop_buy', {
          rsn,
          add_rsn,
          id: contributionShopCms.id,
          name: displayNameUtil.getContributionShopProductDisplayName(contributionShopCms),
          pr_data: pr_data.length > 0 ? pr_data : null,
          reward_data: reward_data.length > 0 ? reward_data : null,
        });

        resp.sync = sync;

        // CMS.RewardFixed 라서 굳이 gains 는 안 보내도 될 듯.
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, resp);
      });
  }
}

function ensurePalace(user: User, townCms: TownDesc) {
  if (townCms.ownType !== cmsEx.TOWN_OWN_TYPE.CAPITAL_TOWN) {
    throw new MError(
      'only-can-buy-in-capital',
      MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
      { townCmsId: townCms.id }
    );
  }

  // 본거지에 왕궁은 항상 존재해서 왕궁 있는지는 굳이 필요 없을 지도?
  // (하면 발전도 검사도 넣어놓는게 좋으려나? 왕궁은 발전도 조건이 지금은 없긴 한데..)
  if (!cmsEx.getTownBuilding(townCms.id, cmsEx.BUILDING_TYPE.PALACE)?.[0]) {
    throw new MError(
      'no-palace-in-this-town',
      MErrorCode.INVALID_REQ_BODY_PALACE_BUY_CONTRIBUTION_SHOP_PRODUCT,
      { townCmsId: townCms.id }
    );
  }
}

class PalaceBuyContributionShopProductSpec extends RewardAndPaymentSpec {
  private _pointCost: { cost: number; cmsId: number };
  private _bPermitExchange: boolean;
  private _restrictedProductsToDelete: Set<number>;
  private _restrictedProductChange: ContributionShopRestrictedProduct | undefined;

  constructor(
    rewardFixedCmsIds: number[],
    pointCost: { cost: number; cmsId: number },
    bPermitExchage: boolean,
    restrictedProductsToDelete: Set<number>,
    restrictedProductChange: ContributionShopRestrictedProduct | undefined
  ) {
    super(
      rewardFixedCmsIds.map<RewardAndPaymentElem>((elem) => ({
        cmsId: elem,
        type: RNP_TYPE.REWARD_FIXED,
        bIsNotPermitAddToHardCapLimitLine: true,
        bIsAccum: true,
        bIsBound: true,
      }))
    );
    this._pointCost = pointCost;
    this._bPermitExchange = bPermitExchage;
    this._restrictedProductsToDelete = restrictedProductsToDelete;
    this._restrictedProductChange = restrictedProductChange;
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    let res: CHANGE_TASK_RESULT;

    for (const cmsId of this._restrictedProductsToDelete) {
      res = opDeleteContributionShopRestrictedProduct(user, tryData, changes, cmsId);
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }

    if (this._restrictedProductChange) {
      res = opSetContributionShopRestrictedProduct(
        user,
        tryData,
        changes,
        this._restrictedProductChange
      );
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }

    // 재화 소모
    assert(-this._pointCost.cost < 0);
    res = opAddPoint(
      user,
      tryData,
      changes,
      this._pointCost.cmsId,
      -this._pointCost.cost,
      false,
      this._bPermitExchange,
      undefined,
      true
    );
    if (res > CHANGE_TASK_RESULT.OK_MAX) {
      return res;
    }

    return super.accumulate(user, tryData, changes);
  }
}
