import mysql from 'mysql2/promise';
import { RemapToolConfig, MySqlConfig, WorldShardMapping } from '../types';

/**
 * Database manager based on new structure
 */
export class DatabaseManager {
  private authPool: mysql.Pool | null = null;
  private worldPools: Map<string, mysql.Pool> = new Map();
  private userShardPools: Map<string, mysql.Pool> = new Map(); // worldId:shardId -> Pool
  private config: RemapToolConfig;

  constructor(config: RemapToolConfig) {
    this.config = config;
  }

  /**
   * Initialize all database connections
   */
  async initialize(): Promise<void> {
    console.log('Initializing database connections...');

    try {
      // Auth database connection
      this.authPool = this.createPoolFromMySqlConfig(this.config.sharedConfig.mysqlAuthDb);
      await this.testConnection(this.authPool, 'auth');

      // Database connections for each world
      for (const world of this.config.worlds) {
        // World database connection
        const worldPool = this.createPoolFromMySqlConfig(world.mysqlWorldDb);
        await this.testConnection(worldPool, `world-${world.id}`);
        this.worldPools.set(world.id, worldPool);

        // User shard connections for the world
        for (const shard of world.mysqlUserDb.shards) {
          const shardConfig = {
            ...world.mysqlUserDb.sqlDefaultCfg,
            ...shard.sqlCfg
          };

          const shardPool = this.createPoolFromMySqlConfig(shardConfig);
          const shardKey = `${world.id}:${shard.shardId}`;
          await this.testConnection(shardPool, `user-${shardKey}`);
          this.userShardPools.set(shardKey, shardPool);
        }
      }

      console.log('All database connections initialized successfully.');
    } catch (error) {
      await this.cleanup();
      throw new Error(`Database initialization failed: ${error}`);
    }
  }

  /**
   * Create connection pool from MySQL config
   */
  private createPoolFromMySqlConfig(config: MySqlConfig): mysql.Pool {
    return mysql.createPool({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      database: config.database,
      connectionLimit: config.connectionLimit || 10,
      connectTimeout: config.connectTimeout || 60000,
      charset: 'utf8mb4',
      multipleStatements: config.multipleStatements || false,
      supportBigNumbers: config.supportBigNumbers || false,
      bigNumberStrings: config.bigNumberStrings || false,
      // flags: config.flags,
    });
  }

  /**
   * Test connection
   */
  private async testConnection(pool: mysql.Pool, name: string): Promise<void> {
    try {
      const connection = await pool.getConnection();
      await connection.ping();
      connection.release();
      // console.log(`${name} database connection successful`);
    } catch (error) {
      throw new Error(`${name} database connection failed: ${error}`);
    }
  }

  /**
   * Get Auth database connection
   */
  getAuthConnection(): mysql.Pool {
    if (!this.authPool) {
      throw new Error('Auth database is not initialized');
    }
    return this.authPool;
  }

  /**
   * Get database connection for specific world
   */
  getWorldConnection(worldId: string): mysql.Pool {
    const pool = this.worldPools.get(worldId);
    if (!pool) {
      throw new Error(`Cannot find database connection for world ${worldId}`);
    }
    return pool;
  }

  /**
   * Get user shard connection for specific world
   */
  getUserShardConnection(worldId: string, shardId: number = 0): mysql.Pool {
    const shardKey = `${worldId}:${shardId}`;
    const pool = this.userShardPools.get(shardKey);
    if (!pool) {
      throw new Error(`Cannot find database connection for world ${worldId} shard ${shardId}`);
    }
    return pool;
  }

  /**
   * Get all user shard connections for specific world
   */
  getWorldUserShardConnections(worldId: string): Map<number, mysql.Pool> {
    const worldShards = new Map<number, mysql.Pool>();

    for (const [shardKey, pool] of this.userShardPools) {
      if (shardKey.startsWith(`${worldId}:`)) {
        const shardIdStr = shardKey.split(':')[1];
        if (!shardIdStr) {
          console.warn(`Invalid shard key format: ${shardKey}`);
          continue;
        }
        const shardId = parseInt(shardIdStr);
        worldShards.set(shardId, pool);
      }
    }

    return worldShards;
  }

  /**
   * Generate world-shard mapping
   */
  generateWorldShardMapping(): WorldShardMapping[] {
    const mappings: WorldShardMapping[] = [];

    for (const world of this.config.worlds) {
      for (const shard of world.mysqlUserDb.shards) {
        const userDatabase = shard.sqlCfg.database ||
          world.mysqlUserDb.sqlDefaultCfg.database + '_' + shard.shardId.toString().padStart(2, '0');

        mappings.push({
          worldId: world.id,
          shardId: shard.shardId,
          userDatabase,
          worldDatabase: world.mysqlWorldDb.database,
        });
      }
    }

    return mappings;
  }

  /**
   * Execute transaction
   */
  async executeTransaction<T>(
    pool: mysql.Pool,
    callback: (connection: mysql.PoolConnection) => Promise<T>
  ): Promise<T> {
    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Execute query
   */
  async executeQuery(
    pool: mysql.Pool,
    query: string,
    params?: any[]
  ): Promise<mysql.RowDataPacket[] | mysql.ResultSetHeader> {
    try {
      const [results] = await pool.execute(query, params);
      return results as mysql.RowDataPacket[] | mysql.ResultSetHeader;
    } catch (error) {
      throw new Error(`Query execution failed: ${query} - ${error}`);
    }
  }

  /**
   * Check if table exists
   */
  async tableExists(pool: mysql.Pool, tableName: string): Promise<boolean> {
    const query = `
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name = ?
    `;

    const results = await this.executeQuery(pool, query, [tableName]) as mysql.RowDataPacket[];
    return results[0]?.count > 0;
  }

  /**
   * Check connections status
   */
  async checkConnections(): Promise<{
    auth: boolean;
    worlds: Record<string, boolean>;
    userShards: Record<string, boolean>;
  }> {
    const result = {
      auth: false,
      worlds: {} as Record<string, boolean>,
      userShards: {} as Record<string, boolean>,
    };

    // Check Auth connection
    if (this.authPool) {
      try {
        await this.testConnection(this.authPool, 'auth');
        result.auth = true;
      } catch {
        result.auth = false;
      }
    }

    // Check World connections
    for (const [worldId, pool] of this.worldPools) {
      try {
        await this.testConnection(pool, `world-${worldId}`);
        result.worlds[worldId] = true;
      } catch {
        result.worlds[worldId] = false;
      }
    }

    // Check User shard connections
    for (const [shardKey, pool] of this.userShardPools) {
      try {
        await this.testConnection(pool, `user-shard-${shardKey}`);
        result.userShards[shardKey] = true;
      } catch {
        result.userShards[shardKey] = false;
      }
    }

    return result;
  }

  /**
   * Cleanup all connections
   */
  async cleanup(): Promise<void> {
    console.log('Cleaning up database connections...');

    const cleanupPromises: Promise<void>[] = [];

    if (this.authPool) {
      cleanupPromises.push(this.authPool.end());
    }

    for (const pool of this.worldPools.values()) {
      cleanupPromises.push(pool.end());
    }

    for (const pool of this.userShardPools.values()) {
      cleanupPromises.push(pool.end());
    }

    await Promise.all(cleanupPromises);

    this.authPool = null;
    this.worldPools.clear();
    this.userShardPools.clear();

    console.log('All database connections have been cleaned up.');
  }

  /**
   * Get world IDs
   */
  getWorldIds(): string[] {
    return this.config.worlds.map(world => world.id);
  }

  /**
   * Get shard info for specific world
   */
  getShardInfoForWorld(worldId: string): Array<{ shardId: number; database: string }> {
    const world = this.config.worlds.find(w => w.id === worldId);
    if (!world) {
      return [];
    }

    return world.mysqlUserDb.shards.map(shard => {
      const database = shard.sqlCfg.database ||
        world.mysqlUserDb.sqlDefaultCfg.database + '_' + shard.shardId.toString().padStart(2, '0');

      return {
        shardId: shard.shardId,
        database,
      };
    });
  }
}
