// ----------------------------------------------------------------------------
// COPYRIGHT (C)2023 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

// ----------------------------------------------------------------------------
// local.json5 파일을 설정을 일괄 변경해주는 스크립트.
//
// yarn reconfigure [IS_GLOBAL] [IS_PVP] [IS_CHINA] [STAGE=dev]
//
// IS_GLOBAL: 글로벌 서버 여부 ("true" 이외의 값은 falsy 로 판정)
// IS_PVP:  분쟁서버 여부 ("true" 이외의 값은 falsy 로 판정)
// IS_CHINA:  중국 서버 여부 ("true" 이외의 값은 falsy 로 판정)  <- 추가됨
// STAGE:  dev, beta, live <- 추가됨
//
// ----------------------------------------------------------------------------

import path from 'path';
import fs from 'fs';
import JSON5 from 'json5';
import { COUNTRY_CODE } from '../motiflib/const';
import { resolveLocalDotJson5 } from '../motiflib/resolveLocalDotJson5';

function main() {
  if (process.argv.length < 4) {
    console.error('[RECONFIGURE] Invalid number of arguments!');
    console.info('    Usage: yarn reconfigure IS_GLOBAL IS_PVP {IS_CHINA=false}');
    console.log();

    process.exitCode = 1;
    return;
  }

  const bIsGlobal = process.argv[2] === 'true';
  const bIsPvp = process.argv[3] === 'true';
  const bIsChina = process.argv.length > 4 && process.argv[4] === 'true';

  // https://jira.line.games/browse/UWO-20984
  //
  // 글로벌 분쟁의 경우
  // world.worlds[n].countryCode => 3
  // world.worlds[n].bIsNonPK => false
  //
  // 글로벌 평화의 경우
  // world.worlds[n].countryCode => 4
  // world.worlds[n].bIsNonPK => true
  //
  // 글로벌:
  // sharedConfig.binaryCode => 'GL'
  // sharedConfig.LineGameCode => 'UWOGL'
  // sharedConfig.http.lgd.authPwd => 'dev-rmffhqjfeoqkr_wkfgoqhwk*6849@3'
  // world.worlds[].http.lgbillingd.authPwd => 'beta_uwogl_jkTRwehj!$%@!#412'
  //
  // 한국:
  // sharedConfig.binaryCode => 'KR'
  // sharedConfig.LineGameCode => 'UWO'
  // sharedConfig.http.lgd.authPwd => 'beta_eogkdgotleo_roakr#5'
  // world.worlds[].http.lgbillingd.authPwd => 'UWO_beta_eoqkrqlqjs@34$'

  let countryCode: COUNTRY_CODE;
  let binaryCode: string;
  let lgGameCode: string;
  let lgdAuthPwd: string;
  let lgBillAuthPwd: string;

  if (bIsChina) {
    countryCode = bIsPvp ? COUNTRY_CODE.CHINA : COUNTRY_CODE.CHINA_NON_PK;
    binaryCode = 'CN';
    lgGameCode = 'UWO';
    lgdAuthPwd = 'beta_eogkdgotleo_roakr#5';
    lgBillAuthPwd = 'UWO_beta_eoqkrqlqjs@34$';
  } else {
    if (bIsGlobal) {
      countryCode = bIsPvp ? COUNTRY_CODE.GLOBAL : COUNTRY_CODE.GLOBAL_NON_PK;
      binaryCode = 'GL';
      lgGameCode = 'UWOGL';
      lgdAuthPwd = 'dev-rmffhqjfeoqkr_wkfgoqhwk*6849@3';
      lgBillAuthPwd = 'beta_uwogl_jkTRwehj!$%@!#412';
    } else {
      countryCode = bIsPvp ? COUNTRY_CODE.KOREA : COUNTRY_CODE.KOREA_NON_PK;
      binaryCode = 'KR';
      lgGameCode = 'UWO';
      lgdAuthPwd = 'beta_eogkdgotleo_roakr#5';
      lgBillAuthPwd = 'UWO_beta_eoqkrqlqjs@34$';
    }
  }

  const bIsNonPK = !bIsPvp;

  console.log('[RECONFIGURE] Modifying ...', {
    countryCode,
    bIsNonPK,
    binaryCode,
    LineGameCode: lgGameCode,
    'http.lgd.authPwd': lgdAuthPwd,
    'world[].http.lgbillingd.authPwd': lgBillAuthPwd,
  });

  const localCfgPath = path.resolve(
    path.join(__dirname, '..', '..', 'service_layout', resolveLocalDotJson5())
  );

  const fileData = fs.readFileSync(localCfgPath, 'utf8');
  const localJson = JSON5.parse(fileData);

  const sharedConfig = localJson.sharedConfig;
  sharedConfig.binaryCode = binaryCode;
  sharedConfig.LineGameCode = lgGameCode;
  sharedConfig.http.lgd.authPwd = lgdAuthPwd;

  const worlds = localJson.world.worlds;
  for (const w of worlds) {
    w.countryCode = countryCode;
    w.bIsNonPK = bIsNonPK;
    w.http.lgbillingd.authPwd = lgBillAuthPwd;
  }

  fs.writeFileSync(localCfgPath, JSON5.stringify(localJson, null, 2));
}

main();
