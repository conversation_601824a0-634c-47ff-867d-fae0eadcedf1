// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import mhttp from '../../../motiflib/mhttp';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { Mate<PERSON><PERSON>, SHIP_ASSIGNMENT, MateEquipmentNub } from '../../../motiflib/model/lobby';
import * as mutil from '../../../motiflib/mutil';
import tuCreateFirstMate from '../../../mysqllib/txn/tuCreateFirstMate';
import Mate from '../../mate';
import { LobbyService } from '../../server';
import Ship, { makeShipRndStats, ShipNub } from '../../ship';
import { Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mconf from '../../../motiflib/mconf';
import { AccumulateParam } from '../../userAchievement';
import { GAME_STATE } from '../../../motiflib/model/lobby/gameState';
import { PointChange } from '../../userPoints';
import mlog from '../../../motiflib/mlog';
import { EnergyChange } from '../../userEnergy';
import { BuffSync } from '../../userBuffs';
import { ClientPacketHandler } from '../index';
import ShipBlueprint from '../../shipBlueprint';
import { MateDesc } from '../../../cms/mateDesc';
import { RANKING_CMS_ID } from '../../../cms/rankingDesc';
import { BuilderMailCreateParams, MailCreatingParams } from '../../../motiflib/mailBuilder';
import { ShipBuildUtil } from '../../../motiflib/model/lobby/shipBuildUtil';
import { ManufacturePointChange } from '../../userManufacture';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'create_account';
const add_rsn = null;

interface ResponseBody extends BuffSync {
  isSuccess: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_CreateFirstMate implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() { }

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body = packet.bodyObj;

    const admiralCmsId: number = body.admiralCmsId;
    const cNationPopRank: number = body.cNationPopRank;

    let startTownCmsId = 0;

    const { userDbConnPoolMgr, userCacheRedis, nationManager, nationRedis } =
      Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureGameState(GAME_STATE.TUTORIAL_COMPLETED);

    const admiralCms = cms.Admiral[admiralCmsId];
    if (!admiralCms) {
      throw new MError('no-mate-in-new-mate-templet', MErrorCode.INVALID_ADMIRAL_CMS_ID, {
        body,
      });
    }

    if (!admiralCms.isBasicAdmiral) {
      throw new MError('invalid-cms-id', MErrorCode.INVALID_ADMIRAL_CMS_ID, {
        body,
      });
    }

    if (Object.keys(user.userMates.getMates()).length > 0) {
      throw new MError('has-mate-already', MErrorCode.HAS_MATE_ALREADY, {
        body,
      });
    }

    const mateCms: MateDesc = cms.Mate[admiralCms.mateId];
    if (!mateCms) {
      throw new MError('invalid-mate-cms-id', MErrorCode.INVALID_MATE_CMS_ID, {
        admiralCmsId: admiralCms.id,
        mateCmsId: admiralCms.mateId,
      });
    }

    if (cmsEx.isFilteredByCountryCode(mateCms.localBitFlag)) {
      throw new MError(
        'invalid-local-bit-flag',
        MErrorCode.CREATE_FIRST_MATE_INVALID_LOCAL_BIT_FLAG,
        {
          mateCmsId: mateCms.id,
          localBitFlag: mateCms.localBitFlag,
          countryCode: mconf.countryCode,
        }
      );
    }

    const mateCmsId: number = admiralCms.mateId;
    const mateNub: MateNub = Mate.defaultNub(mateCmsId);
    const quests: { questCmsId: number; r0: number }[] = [];
    const scenarioQuestCmsId = admiralCms['questId' + mconf.countryCode];
    if (scenarioQuestCmsId) {
      quests.push({ questCmsId: scenarioQuestCmsId, r0: mateCmsId });
    }

    const curTimeUtc = mutil.curTimeUtc();

    // defaults
    const defaultQuestCms = cms.DefaultQuest;
    _.forOwn(defaultQuestCms, (elem) => {
      quests.push({ questCmsId: elem.questId, r0: 0 });
    });

    // 각 트리별 최초 선박은 계정 생성 시 해금
    const defaultShipBlueprints: ShipBlueprint[] = [];

    for (const key of Object.keys(cms.DefaultShipBlueprint)) {
      const blueprintCmsId = cms.DefaultShipBlueprint[key].shipBlueprintId;
      defaultShipBlueprints.push(new ShipBlueprint(blueprintCmsId));
    }

    const defaultShips: ShipNub[] = [];
    let newShipId = user.userFleets.getNewShipId();
    for (const key of Object.keys(cms.DefaultShip)) {
      const defaultShip = cms.DefaultShip[key];
      // 선장으로 할 항해사가 없는 경우 배를 추가하지 않는다.
      if (defaultShip.mateId === mateCmsId) {
        const bpCmsId = cms.Ship[defaultShip.shipId].shipBlueprintId;
        const rndStats = makeShipRndStats(bpCmsId);
        const defaultDurability = cmsEx.shipDefaultDurability(bpCmsId, rndStats);

        const id = newShipId++;
        // defaultShipBlueprints으로 설계도 지급하기 때문에 level이 더 높은걸로 덮어쓰기
        const { shipNub, bpExpLevelChange } = ShipBuildUtil.buildShipNubAndShipBlueprint(
          user.companyStat,
          user.userShipBlueprints,
          false,
          id,
          defaultShip.shipId,
          SHIP_ASSIGNMENT.FLEET,
          cmsEx.FirstFleetIndex,
          defaultShip.formationIndex,
          cmsEx.shipDefaultLife(bpCmsId),
          defaultDurability,
          rndStats,
          [],
          null,
          0,
          1,
          `${user.userId}:${id}`,
          null,
          defaultShip.defaultSailor
        );

        if (bpExpLevelChange) {
          const findDefaultBp = _.find(
            defaultShipBlueprints,
            (elem) => elem.cmsId === bpExpLevelChange.cmsId
          );
          if (findDefaultBp) {
            if (findDefaultBp.level < bpExpLevelChange.level) {
              findDefaultBp.level = bpExpLevelChange.level;
              findDefaultBp.exp = bpExpLevelChange.exp;
            }
          } else {
            const newBp: ShipBlueprint = new ShipBlueprint(bpCmsId);
            newBp.level = bpExpLevelChange.level;
            newBp.exp = bpExpLevelChange.exp;
            defaultShipBlueprints.push(newBp);
          }
        }

        shipNub.slots[cmsEx.ShipSlotIndexCaptainRoom] = {
          slotIndex: cmsEx.ShipSlotIndexCaptainRoom,
          mateCmsId,
          isLocked: 0,
          shipSlotItemId: null,
        };

        defaultShips.push(shipNub);
      }
    }
    const defaultPoint = cmsEx.getDefaultPoint(mateCmsId);
    const pointChanges: PointChange[] = [];
    let energyChange: EnergyChange;
    let manufacturePointChange: ManufacturePointChange;
    for (const elem of defaultPoint) {
      // if (!elem.value) {
      //   continue;
      // }

      if (elem.cmsId === cmsEx.EnergyPointCmsId) {
        energyChange = {
          energy: elem.value,
          lastUpdateTimeUtc: curTimeUtc,
        };
        continue;
      }

      if (elem.cmsId === cmsEx.ManufacturePointCmsId) {
        manufacturePointChange = {
          point: elem.value,
          lastUpdatePointTimeUtc: curTimeUtc,
        };
        continue;
      }

      const change = {
        cmsId: elem.cmsId,
        value: elem.value,
      };
      if (elem.cmsId !== cmsEx.EnergyPointCmsId) {
        change.value += user.userPoints.getPoint(elem.cmsId);
      }
      if (user.userPoints.getPoint(elem.cmsId) === change.value) {
        continue;
      }
      pointChanges.push(change);
    }

    // 최초 제독 선택 시 국가 순위에 따라 블루젬 지급 https://jira.line.games/browse/UWO-17895
    const nationCmsId = cms.Mate[mateCmsId].nationId;
    let bonusBlueGem = 0;

    // 스트레스 테스트인 경우 블루젬 지급안함
    if (!mconf.stressTest?.enabled) {
      if (nationManager.getTotalPopulation() === 0) {
        bonusBlueGem = cms.Const.AdmiralChooseBonusDefalutPointValue.value;
      } else {
        const targetNationCmsIds = cmsEx.getFirstAdmiralNationCmsIds();
        targetNationCmsIds.sort((a, b) => {
          const aNation = nationManager.get(a);
          const bNation = nationManager.get(b);
          if (aNation.population !== bNation.population) {
            return aNation.population - bNation.population;
          }

          const aPowerRank = nationManager.getPowerRank(a);
          const bPowerRank = nationManager.getPowerRank(b);
          return bPowerRank - aPowerRank;
        });
        const sNationPopRank = targetNationCmsIds.findIndex((elem) => elem === nationCmsId) + 1;
        if (cNationPopRank !== sNationPopRank) {
          const resp: ResponseBody = {
            isSuccess: false,
            sync: {
              add: {
                nations: nationManager.getSyncDataOfSelectableNations().nations,
              },
            },
          };
          return user.sendJsonPacket(packet.seqNum, packet.type, resp);
        }
        bonusBlueGem = cms.Const[`AdmiralChooseBonusPointValue${sNationPopRank}`].value;
      }
    }

    if (bonusBlueGem > 0) {
      const blueGemIdx = pointChanges.findIndex((elem) => elem.cmsId === cmsEx.BlueGemPointCmsId);
      if (blueGemIdx === -1) {
        pointChanges.push({
          cmsId: cmsEx.BlueGemPointCmsId,
          value: user.userPoints.getPoint(cmsEx.BlueGemPointCmsId) + bonusBlueGem,
        });
      } else {
        pointChanges[blueGemIdx].value += bonusBlueGem;
      }
    }

    const defaultMails: MailCreatingParams[] = [];
    for (const key of Object.keys(cms.DefaultMail)) {
      const mailCms = cms.Mail[cms.DefaultMail[key].mailId];
      let expireTimeUtc = null;
      let bShouldSetExpirationWhenReceiveAttachment = 0;
      if (mailCms.mailKeepTime > 0) {
        expireTimeUtc = curTimeUtc + mailCms.mailKeepTime;
      } else if (mailCms.mailKeepTime === -1) {
        bShouldSetExpirationWhenReceiveAttachment = 1;
      }

      defaultMails.push(
        new BuilderMailCreateParams(
          user.userMails.generateNewDirectMailId(),
          cms.DefaultMail[key].mailId,
          curTimeUtc,
          expireTimeUtc,
          bShouldSetExpirationWhenReceiveAttachment,
          null,
          null,
          null,
          null,
          null
        ).getParam()
      );
    }

    const defaultShipSailPatterns = _.values(cms.DefaultSailPattern).map(
      (elem) => elem.defaultsailpatternId
    );
    const defaultShipSailCrests = cmsEx.getDefaultShipSailCrests();
    const defaultShipSailPatternColors = cmsEx.getDefaultShipSailPatternColors();
    const defaultShipBody1Colors = cmsEx.getDefaultShipBody1Colors();
    const defaultShipBody2Colors = cmsEx.getDefaultShipBody2Colors();
    // 돗 패턴 장착
    const defaultEquipSailPatternCmsId = _.values(cms.ShipCustomizeData)[0].defaultSailPatternId;

    const defaultMateEquipmentColors = cmsEx.getDefaultMateEquipmentColors();

    startTownCmsId = admiralCms.startTownId;
    mateNub.royalTitle = admiralCms.startRoyalTitelId;

    // 항해사 기본 장비 지급
    const mateEquipsToAdd: MateEquipmentNub[] = createDefaultEquipment(user, mateCmsId, curTimeUtc);

    const resp: ResponseBody = {
      isSuccess: true,
      sync: {},
    };

    const gameStateChange = user.userState.buildGameStateChange(GAME_STATE.FIRST_MATE_CREATED);
    const defaultFormationCmsId = cms.Const.DefaultFormation.value;
    const battleFormation = user.userBattleFormations.buildBattleFormation(
      defaultFormationCmsId,
      curTimeUtc
    );

    const arenaPlayTicketMax = cms.Const.ArenaPlayTicketMax.value;
    return tuCreateFirstMate(
      userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
      user.userId,
      curTimeUtc,
      mateNub.cmsId,
      defaultShipBlueprints,
      defaultShips,
      pointChanges,
      energyChange,
      manufacturePointChange,
      defaultMails,
      quests,
      startTownCmsId,
      cms.Const.DefaultInsurance.value,
      mateNub.royalTitle,
      user.level,
      admiralCms.startOpenFOWTileIds,
      admiralCms.startOpenRegionInfo,
      defaultShipSailPatterns, // u_ship_sail_patterns
      defaultShipSailCrests, // u_ship_sail_crests
      defaultShipSailPatternColors, // u_ship_sail_pattern_colors
      defaultShipBody1Colors, // u_ship_body_1_colors
      defaultShipBody2Colors, // u_ship_body_2_colors
      defaultMateEquipmentColors, // u_mate_equipment_colors
      defaultEquipSailPatternCmsId, // u_ship_customizings
      mateEquipsToAdd,
      admiralCms.defaultTowns,
      gameStateChange,
      battleFormation,
      cmsEx.FirstFleetIndex,
      nationCmsId,
      arenaPlayTicketMax
    )
      .then(() => {
        user.userMates.addNewMate(
          mateNub,
          user.companyStat,
          user,
          { user, rsn, add_rsn },
          resp,
          true
        );
        user.userPoints.setInsuranceCmsId(cms.Const.DefaultInsurance.value);
        user.userTown.arrivalTownCmsId = startTownCmsId;
        user.userBattleFormations.addFormation(battleFormation);
        const fleet = user.userFleets.getFleet(cmsEx.FirstFleetIndex);
        fleet.setBattleFormationCmsId(defaultFormationCmsId);

        _.merge<Sync, Sync>(resp.sync, {
          add: {
            mates: {
              [mateNub.cmsId]: user.userMates.getMate(mateCmsId).getNub(),
            },
            ships: mutil.arrayToDictionary(defaultShips, ({ id }) => id.toString()),
            insurance: {
              insuranceCmsId: cms.Const.DefaultInsurance.value,
            },
            battleFormations: {
              [defaultFormationCmsId]:
                user.userBattleFormations.getFormation(defaultFormationCmsId),
            },
            fleets: {
              [fleet.fleetIndex]: {
                battleFormationCmsId: defaultFormationCmsId,
              },
            },
          },
        });

        _.merge<Sync, Sync>(
          resp.sync,
          user.userState.applyGameStateChange(gameStateChange, { user })
        );

        if (defaultShipBlueprints && defaultShipBlueprints.length > 0) {
          for (const bp of defaultShipBlueprints) {
            user.userShipBlueprints.createUserShipBlueprint(
              user.companyStat,
              bp.cmsId,
              bp.level,
              bp.exp,
              1,
              0,
              { user, rsn, add_rsn },
              resp.sync
            );
          }
        }

        for (const ship of defaultShips) {
          user.userFleets.addNewShip(
            ship,
            user.companyStat,
            user.userShipBlueprints,
            user.userMates,
            user.userFleets,
            user.userInven,
            user.userBuffs,
            user.userPassives,
            user.userCollection,
            user.questManager,
            user.userSailing,
            user.userTriggers,
            user.userNation,
            user.userResearch,
            { user, rsn, add_rsn },
            resp.sync
          );
        }

        _.merge<Sync, Sync>(
          resp.sync,
          user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
        );

        _.merge<Sync, Sync>(
          resp.sync,
          user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn })
        );

        // 최초 행동력 획득 로그.
        if (energyChange) {
          user.glog('energy', {
            rsn,
            add_rsn,
            cv: energyChange.energy,
            rv: energyChange.energy,
          });
        }

        _.merge<Sync, Sync>(
          resp.sync,
          user.userManufacture.applyPointChange(manufacturePointChange, { user, rsn, add_rsn })
        );

        if (quests && quests.length > 0) {
          for (const quest of quests) {
            const questCtx = user.questManager.acceptQuest(
              quest.questCmsId,
              curTimeUtc,
              quest.r0,
              user.level,
              0
            );

            _.merge<Sync, Sync>(resp.sync, {
              add: {
                questData: {
                  contexts: {
                    [quest.questCmsId]: questCtx,
                  },
                },
              },
            });

            // glog
            const questCms = cms.Quest[quest.questCmsId];
            const collection =
              questCms.category === cmsEx.QUEST_CATEGORY.SCENARIO ||
                questCms.category === cmsEx.QUEST_CATEGORY.RELATIONSHIP
                ? 'admiral_story'
                : 'quest';
            const questNodeCms = cms.QuestNode[questCms.nodes[0]];
            user.glog(collection, {
              rsn,
              add_rsn,
              category: questCms.category,
              flag: 1, // 1: 수락, 2: 노드 완료
              id: questCms.id,
              name: collection === 'admiral_story' ? questNodeCms.name : questCms.name,
              step: 0,
              progress: '0/' + questCms.nodes.length,
              reward_data: null,
              pr_data: collection === 'admiral_story' ? null : undefined,
              chapter: questNodeCms ? questNodeCms.name : null,
              quest_node_id: questNodeCms ? questNodeCms.id : null,
              script_name: questNodeCms ? questNodeCms.script : null,
            });
          }
        }

        if (defaultMails.length > 0) {
          for (const mail of defaultMails) {
            user.userMails.addDirectMail(mail, { user });
          }
          resp.sync.add.userDirectMails = user.userMails.getSyncData().userDirectMails;
        }

        if (admiralCms.startOpenFOWTileIds) {
          for (const id of admiralCms.startOpenFOWTileIds) {
            user.userDiscovery.revealWorldMapTile(id);

            const offset = Math.floor(id / 32);
            _.merge<Sync, Sync>(resp.sync, {
              add: {
                revealedWorldMapTiles: {
                  [offset]: user.userDiscovery.getRevealedWorldMapTileSyncData()[offset],
                },
              },
            });
          }
        }

        user.userFleets.shipCustomizing.sailPatternCmsId = defaultEquipSailPatternCmsId;
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            shipCustomizing: {
              sailPatternCmsId: defaultEquipSailPatternCmsId,
            },
          },
        });

        for (const cmsId of defaultShipSailPatterns) {
          user.userFleets.addShipSailPattern(cmsId);

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              shipSailPatterns: {
                [cmsId]: {
                  sailPatternCmsId: cmsId,
                },
              },
            },
          });
        }

        _.forOwn(defaultShipSailCrests, (idxField, offsetStr) => {
          user.userFleets.applyShipSailCrestIdxField(offsetStr, idxField);

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              shipSailCrests: {
                [offsetStr]: idxField,
              },
            },
          });
        });

        _.forOwn(defaultShipSailPatternColors, (idxField, offsetStr) => {
          user.userFleets.applyShipSailPatternColorIdxField(offsetStr, idxField);

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              shipSailPatternColors: {
                [offsetStr]: idxField,
              },
            },
          });
        });

        _.forOwn(defaultShipBody1Colors, (idxField, offsetStr) => {
          user.userFleets.applyShipBody1ColorIdxField(offsetStr, idxField);

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              shipBody1Colors: {
                [offsetStr]: idxField,
              },
            },
          });
        });

        _.forOwn(defaultShipBody2Colors, (idxField, offsetStr) => {
          user.userFleets.applyShipBody2ColorIdxField(offsetStr, idxField);

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              shipBody2Colors: {
                [offsetStr]: idxField,
              },
            },
          });
        });

        _.forOwn(defaultMateEquipmentColors, (idxField, offsetStr) => {
          user.userMates.applyMateEquipmentColorIdxField(offsetStr, idxField);

          _.merge<Sync, Sync>(resp.sync, {
            add: {
              mateEquipmentColors: {
                [offsetStr]: idxField,
              },
            },
          });
        });

        if (mateEquipsToAdd) {
          for (const elem of mateEquipsToAdd) {
            _.merge<Sync, Sync>(
              resp.sync,
              user.userMates.addMateEquipment(elem, { user, rsn, add_rsn })
            );
            if (elem.equippedMateCmsId) {
              user.userMates.equipMateEquipment(
                elem.equippedMateCmsId,
                elem.id,
                user.companyStat,
                user.userPassives,
                user.userFleets,
                user.userSailing,
                user.userTriggers,
                user.userBuffs,
                resp.sync,
                { user, rsn, add_rsn }
              );
            }
          }
        }

        // 첫 제독 선택시 일부 도시 발견 처리
        if (admiralCms.defaultTowns) {
          for (const discoveryCmsId of admiralCms.defaultTowns) {
            const idxField = user.userDiscovery.discover(discoveryCmsId, user, true);
            _.merge<Sync, Sync>(resp.sync, {
              add: {
                discoveries: {
                  [Math.floor(discoveryCmsId / 32)]: idxField,
                },
              },
            });
          }
        }

        mhttp.authd.changeUserNation(user.userId, nationCmsId).catch((err) => {
          mlog.alert('Auth api changeUserNation is failed.', {
            userId: user.userId,
            nationCmsId,
            err: err.message,
          });
        });
        mhttp.chatd.updateVolanteUser(user);

        user.nationCmsId = nationCmsId;
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            user: {
              nationCmsId,
            },
          },
        });

        user.userArena.setTickets(arenaPlayTicketMax);

        // accumulate achievement
        const accums: AccumulateParam[] = [
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_ADMIRAL,
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_ADMIRAL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_MATE,
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_NATION_MATE,
            targets: [mateCms.nationId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_MATE,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_ADVENTURE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TRADE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_BATTLE_LEVEL,
            targets: [mateCmsId],
            addedValue: 1,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SPECIFIC_MATE_TOTAL_LEVEL,
            targets: [mateCmsId],
            addedValue: 3,
          },
          {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.COMPANY_LEVEL,
            addedValue: 1,
          },
        ];

        // language
        for (let i = 1; i <= cmsEx.getMateHighestLanguageLevel(mateCmsId); i++) {
          const accum: AccumulateParam = {
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.MATE_LANGUAGE_LEVEL,
            targets: [i],
            addedValue: 1,
          };
          accums.push(accum);
        }

        // royal title
        const mateRoyalTitle = admiralCms.startRoyalTitelId;
        let targets = cmsEx.getAchievementTermsTargets(cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE, 0);

        if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
          for (const target of targets) {
            if (mateRoyalTitle < target) {
              break;
            }
            if (target !== mateRoyalTitle) {
              continue;
            }

            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE,
              targets: [target],
              addedValue: 1,
            });
          }
        }

        targets = cmsEx.getAchievementTermsTargets(
          cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
          1
        );
        if (targets.length > 0 && targets[0] <= mateRoyalTitle) {
          for (const target of targets) {
            if (mateRoyalTitle < target) {
              break;
            }
            if (target !== mateRoyalTitle) {
              continue;
            }

            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.ROYAL_TITLE_SPECIFIC_ADMIRAL,
              targets: [mateCmsId, target],
              addedValue: 1,
            });
          }
        }

        // ship
        for (const ship of defaultShips) {
          accums.push(
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SHIP,
              addedValue: 1,
            },
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_SHIP,
              targets: [ship.cmsId],
              addedValue: 1,
            }
          );

          const shipCms = cms.Ship[ship.cmsId];
          const userBP = user.userShipBlueprints.getUserShipBlueprint(shipCms.shipBlueprintId);
          const bpLv = userBP ? userBP.level : 1;

          for (let i = 1; i <= bpLv; i++) {
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SHIP,
              targets: [i],
              addedValue: 1,
            });
            accums.push({
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_SPECIFIC_BP_LEVEL_SPECIFIC_SHIP,
              targets: [ship.cmsId, i],
              addedValue: 1,
            });
          }
        }

        // 첫 제독 선택시 일부 도시 발견 처리
        if (admiralCms.defaultTowns) {
          for (const discoveryCmsId of admiralCms.defaultTowns) {
            const discoveryCms = cms.Discovery[discoveryCmsId];
            accums.push(
              {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DISCOVER_SPECIFIC_CATEGORY,
                targets: [discoveryCms.discoveryType],
                addedValue: 1,
              },
              {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DISCOVER,
                addedValue: 1,
              },
              {
                achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DISCOVER_SPECIFIC,
                targets: [discoveryCms.id],
                addedValue: 1,
              }
            );
          }
        }

        return user.userAchievement.accumulate(accums, user, resp.sync, { user, rsn, add_rsn });
      })
      .then((sync) => {
        const mate = user.userMates.getMate(mateCmsId);
        const awakenLv = mate.getAwakenLv();
        const trainingGrade = mate.getTrainingGrade();
        const equippedIllustCmsId = mate.getEquippedIllustCmsId();
        const isTranscended = mate.isTranscended();

        userCacheRedis['setUserLeaderMateLightInfo'](
          user.userId,
          mateCmsId,
          awakenLv,
          trainingGrade,
          equippedIllustCmsId,
          isTranscended
        ).catch((err) => {
          mlog.error('userCacheRedis setUserLeaderMateLightInfo is failed.', {
            userId: user.userId,
            mateCmsId,
            awakenLv,
            trainingGrade,
            equippedIllustCmsId,
            isTranscended,
            err: err.message,
          });
        });

        userCacheRedis['setUserNationCmsId'](user.userId, nationCmsId).catch((err) => {
          mlog.error('userCacheRedis setUserNationCmsId is failed.', {
            userId: user.userId,
            err: err.message,
          });
        });

        nationRedis['changeNationPopulation'](0, nationCmsId);
        user.glog('nation_change', {
          rsn,
          add_rsn,
          old_nation: null,
          cur_nation: cms.Nation[nationCmsId].name,
          pr_data: null,
          reward_data: null,
        });

        user.glog('admiral_scout', {
          rsn,
          add_rsn,
          buy_admiral_id: mateCmsId,
          pr_data: null,
        });

        return user.sendJsonPacket(packet.seqNum, packet.type, resp);
      });
  }
}

function createDefaultEquipment(
  user: User,
  mateCmsId: number,
  curTimeUtc: number
): MateEquipmentNub[] {
  const mateEquipsToAdd: MateEquipmentNub[] = [];
  const mateCms = cms.Mate[mateCmsId];
  if (mateCms.CEquipId && mateCms.CEquipId.length > 0) {
    for (let i = 0; i < mateCms.CEquipId.length; i++) {
      const cequipCmsId = mateCms.CEquipId[i];
      const mateEquipNub = user.userMates.buildMateEquipmentNub(
        cequipCmsId,
        mateCmsId,
        1,
        0,
        curTimeUtc
      );
      mateEquipNub.id += i;

      const randomColors: number[] = cmsEx.getRandomEquipDyeColors(
        cms.CEquip[cequipCmsId],
        mateCms.character.gender
      );

      for (let idx = 0; idx < randomColors.length; idx++) {
        mateEquipNub['dye' + (idx + 1)] = randomColors[idx];
      }
      mateEquipsToAdd.push(mateEquipNub);
    }
  }
  if (mateEquipsToAdd.length === 0) {
    return null;
  }
  return mateEquipsToAdd;
}
