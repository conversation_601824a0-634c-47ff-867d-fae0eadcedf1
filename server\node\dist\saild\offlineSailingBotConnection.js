"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.State = void 0;
//import * as Queue from 'fifo';
const queue_fifo_1 = __importDefault(require("queue-fifo"));
const proto = __importStar(require("../proto/lobby/proto"));
const mutil = __importStar(require("../motiflib/mutil"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const bot_1 = require("../motiflib/model/bot");
const lobby_1 = require("../motiflib/model/lobby");
const basic_packets_1 = require("../tcplib/shared/basic-packets");
const server_1 = require("./server");
const protocol_1 = require("../proto/saild-lobbyd/protocol");
const csNet_1 = require("../proto/csNet");
// ----------------------------------------------------------------------------
// Constants.
// ----------------------------------------------------------------------------
var Const;
(function (Const) {
    Const[Const["HeaderLen"] = 4] = "HeaderLen";
    Const[Const["RecvBufSize"] = 32768] = "RecvBufSize";
})(Const || (Const = {}));
var State;
(function (State) {
    State[State["INITIALIZED"] = 0] = "INITIALIZED";
    State[State["CONNECTING"] = 1] = "CONNECTING";
    State[State["CONNECTED"] = 2] = "CONNECTED";
    //EXCHANGING_KEYS = 3,
    State[State["LOGGING_IN"] = 4] = "LOGGING_IN";
    State[State["LOGGED_IN"] = 5] = "LOGGED_IN";
    State[State["DISCONNECTED"] = 10] = "DISCONNECTED";
    State[State["ERROR"] = 100] = "ERROR";
})(State = exports.State || (exports.State = {}));
// ----------------------------------------------------------------------------
// OfflineSailingBotConnection object.
// ----------------------------------------------------------------------------
class OfflineSailingBotConnection {
    constructor(accountId, pubId, userId) {
        this.lobbydUrl = undefined;
        // User.
        this.accountId = accountId;
        this.pubId = pubId;
        this.userId = userId;
        // State.
        this.state = State.INITIALIZED;
        // Received packet queue.
        this.packetQueue = new queue_fifo_1.default();
    }
    setLobbydUrl(lobbydUrl) {
        this.lobbydUrl = lobbydUrl;
    }
    getLobbydUrl() {
        return this.lobbydUrl;
    }
    // connect(): void {
    //   if (this.sock) {
    //     return;
    //   }
    //   this.setState(State.CONNECTING);
    //   this.sock = net.createConnection({ host: this.lobbydHost, port: this.lobbydPort }, () => {
    //     mlog.verbose('connected to lobbyd', {
    //       accountId: this.accountId,
    //     });
    //     this.setState(State.CONNECTED);
    //     this.onConnected();
    //   });
    //   //this.bindSocketEvents();
    // }
    async disconnect(bSend = true) {
        if (this.state === State.DISCONNECTED) {
            return;
        }
        mlog_1.default.info('bot-con disconnect', { userId: this.userId });
        this.setState(State.DISCONNECTED);
        // lobbyd로 접속종료패킷을 보낸다
        if (bSend) {
            const url = this.lobbydUrl;
            const packet = new protocol_1.SailProtocol.SA2LB_REQ_BOT_USER_DISCONNECT();
            packet.userId = this.userId;
            return server_1.tcpServer
                .sendAndRecv(url, packet)
                .then((res) => {
                mlog_1.default.info('received LB2SA_RES_BOT_USER_DISCONNECT', {
                    userId: res.userId,
                });
            })
                .catch((err) => {
                // nothing to do
            });
        }
        return Promise.resolve();
    }
    isDisconnected() {
        return this.state === State.DISCONNECTED;
    }
    clearPacketQueue() {
        this.packetQueue.clear();
    }
    popPacket() {
        return this.packetQueue.dequeue();
    }
    sendJsonPacket(packet, payloadFlags) {
        if (mutil.isNotANumber(payloadFlags)) {
            payloadFlags = lobby_1.PayloadFlag.Compress;
        }
        const packetJson = JSON.stringify(packet);
        const packetBuf = Buffer.from(packetJson, 'utf-8');
        mlog_1.default.verbose('sending packet', {
            userId: this.userId,
            packetType: packet.type,
            typeStr: proto.toString(packet.type),
            packetJson,
        });
        return Promise.resolve()
            .then(() => {
            if (payloadFlags & lobby_1.PayloadFlag.Compress) {
                return mutil.compress(packetBuf);
            }
            return packetBuf;
        })
            .then((buf) => {
            if (payloadFlags & lobby_1.PayloadFlag.Encrypt) {
                mlog_1.default.error('invalid encrypt send request to offlineSailingBotConnection', {
                    accountId: this.accountId,
                    packetType: packet.type,
                    typeStr: proto.toString(packet.type),
                });
            }
            return buf;
        })
            .then((buf) => {
            // mlog.info('sendJsonPacket after compress', {
            //   size: buf.byteLength,
            //   buf,
            // });
            const packetSize = buf.byteLength;
            const sendBuf = Buffer.alloc(Const.HeaderLen + packetSize);
            csNet_1.CsNet.writePacketHeader(sendBuf, packetSize, payloadFlags);
            sendBuf.fill(buf, Const.HeaderLen);
            //  여기서 proxy에 넣어서전송한다
            const sendPacket = new basic_packets_1.BufferPacket(sendBuf);
            server_1.tcpServer.sendLobbySailProxyPacket(this.lobbydUrl, this.userId, sendPacket);
        })
            .catch((err) => {
            mlog_1.default.error('failed to send', {
                error: err.message,
                packetType: packet.type,
                typeStr: proto.toString(packet.type),
            });
            this.setState(State.ERROR);
        });
    }
    onRecvBufferProxy(packet) {
        const data = packet.data;
        const payload = this.readBufferProxyPayload(data);
        if (!payload) {
            return;
        }
        return this.processPayload(payload);
    }
    processPayload(payload) {
        return this.parsePacket(payload)
            .then((packet) => {
            if (!packet) {
                // 현재 binary packet들은 처리안하고 있음
                return;
            }
            // 라이브에서는 info 이상만 로그가 남는다
            if (!bot_1.ResponseTypesNotToLog.includes(packet.type)) {
                mlog_1.default.debug('packet received', {
                    userId: this.userId,
                    // packet,
                    typeStr: proto.toString(packet.type),
                });
            }
            return this.packetQueue.enqueue(packet);
            /////////////////////////////
            // below codes moved to botClient
        })
            .catch((err) => {
            mlog_1.default.error('processPayload failed', {
                userId: this.userId,
                error: err.message,
                stack: err.stack,
            });
            this.disconnect();
        });
    }
    // ----------------------------------------------------------------------------
    readBufferProxyPayload(buf) {
        if (buf.byteLength < Const.HeaderLen) {
            // critical error
            mlog_1.default.error('readBufferProxyPayload buff incomplete', {
                userId: this.userId,
                incommingBytesLength: buf.byteLength,
            });
            this.disconnect();
            return undefined;
        }
        const { payloadSize, payloadFlags } = csNet_1.CsNet.readPacketHeader(buf);
        if (buf.byteLength < Const.HeaderLen + payloadSize) {
            mlog_1.default.error('readBufferProxyPayload buff incomplete', {
                userId: this.userId,
                incommingBytesLength: buf.byteLength,
            });
            this.disconnect();
            return undefined;
        }
        // We have enough to read to make a packet.
        // Copy the payload to separate buffer,
        const payloadEnd = Const.HeaderLen + payloadSize;
        const payloadBuffer = Buffer.alloc(payloadSize);
        buf.copy(payloadBuffer, 0, Const.HeaderLen, payloadEnd);
        // mlog.debug('[TEMP] readPayload', {
        //   accountId: this.accountId,
        //   bytesReceived: this.bytesReceived,
        // });
        const payload = {
            flags: payloadFlags,
            buffer: null,
        };
        if (payloadFlags & lobby_1.PayloadFlag.Compress) {
            // + 4 for 'uncompressed size' of the payload.(S->C 에서만 보내고 있음)
            payload.buffer = Buffer.from(payloadBuffer.buffer, 4);
            // mlog.info('[TEMP] readBufferProxyPayload diff Buffer.from', {
            //   size: payloadBuffer.byteLength,
            //   payloadBuffer,
            //   afterBuff: payload.buffer,
            // });
        }
        else {
            payload.buffer = payloadBuffer;
        }
        return payload;
    }
    // ----------------------------------------------------------------------------
    parsePacket(payload) {
        const flags = payload.flags;
        const payloadBuf = payload.buffer;
        return Promise.resolve()
            .then(() => {
            // Decrypt if necessary.
            if (flags & lobby_1.PayloadFlag.Encrypt) {
                mlog_1.default.error('invalid decrypt recv request to offlineSailingBotConnection', {
                    userId: this.userId,
                });
            }
            return payloadBuf;
        })
            .then((buf) => {
            // Unzip.
            if (flags & lobby_1.PayloadFlag.Compress) {
                // mlog.info('parsePacket before uncompress', {
                //   size: payloadBuf.byteLength,
                //   payloadBuf,
                // });
                return mutil.uncompress(buf);
            }
            return buf;
        })
            .then((buf) => {
            // Return parsed packet.
            if (flags & lobby_1.PayloadFlag.Binary) {
                return this.parseBinaryPacket(buf);
            }
            return JSON.parse(buf);
        });
    }
    parseBinaryPacket(buf) {
        const type = buf.readInt32LE(0);
        // const packetBody = PacketFactory.Create(type);
        // const bodyBuf = buf.subarray(Const.BinaryPacketHeadLen);
        // const bodyReader = SmartBuffer.fromBuffer(bodyBuf);
        // packetBody.deserialize(bodyReader);
        // const packet = {
        //   seqNum: 0,
        //   type,
        //   body: packetBody,
        // };
        // mlog.info('[TEMP] parsed binary packet', { typeStr: proto.toString(type) });
        return null;
    }
    setState(newState) {
        // mlog.debug('state change', {
        //   oldState: this.state,
        //   newState,
        // });
        this.state = newState;
    }
}
function parseMoveInTown(buf) {
    let pos = 4;
    const userId = buf.readInt32LE(pos);
    pos += 4;
    const x = buf.readInt32LE(pos);
    pos += 4;
    const y = buf.readInt32LE(pos);
    pos += 4;
    const degrees = buf.readInt32LE(pos);
    pos += 4;
    const speed = buf.readInt32LE(pos);
    const packet = {
        seqNum: 0,
        type: proto.Town.USER_MOVE_SC,
        userId,
        x,
        y,
        degrees,
        speed,
    };
    // mlog.verbose('received move-in-town - ', JSON.stringify(packet));
    return packet;
}
exports.default = OfflineSailingBotConnection;
//# sourceMappingURL=offlineSailingBotConnection.js.map