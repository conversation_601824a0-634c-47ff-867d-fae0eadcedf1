// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';

import mhttp from '../motiflib/mhttp';
import { RealmService } from './server';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';

// ---------------------------------------------------------
// 1. allKick 메시지를 받으면 시작하고 점검 상태가 끝나면 종료한다
// 2. realmd 시작시 항상 한번 시작한다
// ---------------------------------------------------------
export default class MaintenanceChecker {
  private _bMaintenance: boolean = false;
  private _bForceOnce: boolean = false; // 점검 체크때 강제로 lg api호출 필요시 사용
  private _timeoutId: NodeJS.Timeout | null = null;

  constructor() {}

  // ---------------------------------------------------------
  async tick() {
    this._timeoutId = setTimeout(() => {
      this.checkMaintenance();
    }, 1000);
  }

  // ---------------------------------------------------------
  async checkMaintenance() {
    let bForce = false;
    if (this._bForceOnce) {
      bForce = true;
      this._bForceOnce = false;
    }
    return mhttp.lgd
      .isWorldInMaintenance(mconf.worldId, bForce)
      .then((ret) => {
        if (!ret) {
          mlog.info('world maintenance is over');

          // redis update
          this._bMaintenance = false;
          const app = Container.get(RealmService);
          const { monitorRedis } = app;
          return monitorRedis['updateMaintenance'](mconf.worldId, 0);
        }
      })
      .catch((error) => {
        mlog.error('failed to check maintenance', {
          error: error.message,
          extra: error.extra,
        });
      })
      .finally(() => {
        if (this._bMaintenance) {
          this.tick();
        }
      });
  }

  // ---------------------------------------------------------
  startCheck() {
    if (this._bMaintenance) {
      mlog.warn('alreay in maintenance! skipping request..');
      return;
    }

    this._bMaintenance = true;
    this._bForceOnce = true;

    mlog.info('start MaintenanceChecker');

    const app = Container.get(RealmService);
    const { monitorRedis } = app;
    return monitorRedis['updateMaintenance'](mconf.worldId, 1).then(() => {
      return this.tick();
    });
  }

  // ---------------------------------------------------------
  stopCheck() {
    if (this._timeoutId) {
      clearTimeout(this._timeoutId);
      this._timeoutId = null;
    }

    if (this._bMaintenance) {
      mlog.info('Maintenance check stopped manually');
      this._bMaintenance = false;
    }
  }
}
