{"url":"/leave","status":"200","response-time":"0.591","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:15:17.061Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T06:16:09.201Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T06:16:09.202Z"}
{"level":"info","message":"[Session] socket disposed, XBiyuyGp","timestamp":"2025-08-22T06:16:09.202Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T06:16:09.202Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T06:16:09.370Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T06:16:09.370Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T06:16:09.553Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T06:16:09.553Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:16:09.556Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T06:16:10.558Z"}
{"environment":"development","type":"townd","gitCommitHash":"3e628d45a9f","gitCommitMessage":"UWO FGT Survey 기능 추가(테스트용)","gitCommitter":"jhseo <<EMAIL>>","gitCommitDate":"2025-08-22T14:28:39+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T06:16:13.177Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T06:16:41.692Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:16:41.700Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T06:16:41.702Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T06:16:41.899Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T06:16:41.899Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T06:16:41.900Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T06:16:41.901Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T06:16:45.654Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T06:16:45.654Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T06:16:45.655Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T06:16:45.662Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T06:16:45.663Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T06:16:45.678Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T06:16:45.770Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.790Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.805Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.816Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.829Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.841Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.858Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.875Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.890Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:16:45.907Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T06:16:45.986Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T06:16:45.987Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T06:16:45.990Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T06:16:46.125Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T06:16:46.127Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T06:16:46.128Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T06:16:46.128Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T06:16:46.131Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T06:16:46.132Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T06:16:46.132Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T06:16:46.132Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T06:16:46.134Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T06:16:46.134Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T06:16:46.135Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T06:16:46.135Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T06:16:46.136Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T06:16:46.138Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T06:16:46.138Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.140Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.141Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.141Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:16:46.141Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.150Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.161Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.169Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.181Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.186Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.200Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.207Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.216Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.226Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.233Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.240Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.246Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.253Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.260Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.266Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.273Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.279Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.285Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:16:46.294Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T06:16:46.305Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T06:16:46.306Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:16:46.308Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:16:46.310Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:16:46.311Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:16:47.314Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:16:47.314Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:16:48.316Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:16:48.317Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:16:49.319Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:16:49.320Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:16:50.322Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:16:50.322Z"}
{"pingInterval":2000,"curDate":1755843411,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T06:16:51.329Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T06:16:51.329Z"}
{"level":"info","message":"[SessionManager] session created: qTuvER8b, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T06:17:02.058Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T06:17:02.061Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T06:17:02.061Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T06:17:02.062Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T06:26:56.672Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T06:26:56.673Z"}
{"level":"info","message":"[Session] socket disposed, qTuvER8b","timestamp":"2025-08-22T06:26:56.673Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T06:26:56.673Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T06:26:57.421Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T06:26:57.422Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T06:26:57.978Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T06:26:57.978Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:26:57.981Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T06:26:58.983Z"}
{"environment":"development","type":"townd","gitCommitHash":"192d4f2758f","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T15:25:40+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T06:27:02.884Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T06:27:31.827Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:27:31.839Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T06:27:31.841Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T06:27:32.078Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T06:27:32.079Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T06:27:32.079Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T06:27:32.081Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T06:27:35.821Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T06:27:35.822Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T06:27:35.822Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T06:27:35.830Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T06:27:35.830Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T06:27:35.846Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T06:27:35.932Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:35.952Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:35.968Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:35.979Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:35.993Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:36.004Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:36.020Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:36.035Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:36.049Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:27:36.065Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T06:27:36.143Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T06:27:36.144Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T06:27:36.148Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T06:27:36.271Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T06:27:36.274Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T06:27:36.275Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T06:27:36.275Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T06:27:36.278Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T06:27:36.279Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T06:27:36.279Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T06:27:36.279Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T06:27:36.281Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T06:27:36.282Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T06:27:36.282Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T06:27:36.282Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T06:27:36.283Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T06:27:36.285Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T06:27:36.285Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.287Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.287Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.287Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.287Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.287Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.287Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.288Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.288Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.288Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.288Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.288Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:27:36.288Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.296Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.308Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.316Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.322Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.331Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.345Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.352Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.358Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.367Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.374Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.380Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.385Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.391Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.398Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.405Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.412Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.419Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.427Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:27:36.434Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T06:27:36.445Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T06:27:36.447Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:27:36.448Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:27:36.450Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:27:36.451Z"}
{"pingInterval":2000,"curDate":1755844057,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T06:27:37.559Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T06:27:37.559Z"}
{"level":"info","message":"[SessionManager] session created: 8wTF6jT-, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T06:27:47.547Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T06:27:47.549Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T06:27:47.550Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T06:27:47.550Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T06:28:02.048Z"}
{"townCmsId":11000000,"channelId":"LP9AUJKc","level":"info","message":"townZone created","timestamp":"2025-08-22T06:28:02.048Z"}
{"url":"/enter","status":"200","response-time":"85.727","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:28:02.050Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T06:28:02.051Z"}
{"url":"/loadComplete","status":"200","response-time":"0.758","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:28:14.147Z"}
{"url":"/leave","status":"200","response-time":"0.525","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:33:08.222Z"}
{"url":"/enter","status":"200","response-time":"0.564","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:33:43.412Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T06:33:43.412Z"}
{"url":"/loadComplete","status":"200","response-time":"0.268","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:33:55.538Z"}
{"url":"/leave","status":"200","response-time":"0.347","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:34:37.073Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T06:40:44.968Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T06:40:44.968Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T06:40:45.197Z"}
{"level":"info","message":"[Session] socket disposed, 8wTF6jT-","timestamp":"2025-08-22T06:40:45.197Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T06:40:45.198Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T06:40:45.200Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T06:40:46.203Z"}
{"environment":"development","type":"townd","gitCommitHash":"640c4d70ab6","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T15:30:35+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T06:40:49.506Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T06:41:13.600Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:41:13.607Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T06:41:13.609Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T06:41:13.853Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T06:41:13.854Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T06:41:13.855Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T06:41:13.856Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T06:41:17.392Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T06:41:17.392Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T06:41:17.393Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T06:41:17.399Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T06:41:17.400Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T06:41:17.414Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T06:41:17.495Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.515Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.530Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.542Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.557Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.568Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.584Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.600Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.615Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T06:41:17.632Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T06:41:17.710Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T06:41:17.711Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T06:41:17.714Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T06:41:17.844Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T06:41:17.846Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T06:41:17.847Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T06:41:17.847Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T06:41:17.850Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T06:41:17.850Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T06:41:17.850Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T06:41:17.851Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T06:41:17.853Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T06:41:17.853Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T06:41:17.854Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T06:41:17.854Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T06:41:17.855Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T06:41:17.857Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T06:41:17.857Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.859Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.859Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.859Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.859Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.860Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.860Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.860Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.860Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.860Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.860Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.861Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T06:41:17.861Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.869Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.880Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.887Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.895Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.902Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.915Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.922Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.928Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.937Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.943Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.949Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.956Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.963Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.970Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.976Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.983Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.990Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:17.996Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T06:41:18.003Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T06:41:18.012Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T06:41:18.014Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T06:41:18.015Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:41:18.018Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:41:18.018Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T06:41:19.022Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T06:41:19.022Z"}
{"pingInterval":2000,"curDate":1755844880,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T06:41:20.029Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T06:41:20.029Z"}
{"level":"info","message":"[SessionManager] session created: cZfLa0EI, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T06:41:28.910Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T06:41:28.913Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T06:41:28.914Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T06:41:28.914Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T06:41:41.666Z"}
{"townCmsId":11000000,"channelId":"Ykxq7cj6","level":"info","message":"townZone created","timestamp":"2025-08-22T06:41:41.666Z"}
{"url":"/enter","status":"200","response-time":"74.137","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:41:41.668Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T06:41:41.669Z"}
{"url":"/loadComplete","status":"200","response-time":"0.648","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:41:53.948Z"}
{"url":"/leave","status":"200","response-time":"0.673","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:42:16.667Z"}
{"url":"/enter","status":"200","response-time":"0.542","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:42:52.125Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T06:42:52.125Z"}
{"url":"/loadComplete","status":"200","response-time":"0.309","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:43:04.349Z"}
{"url":"/leave","status":"200","response-time":"1.835","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T06:44:05.762Z"}
