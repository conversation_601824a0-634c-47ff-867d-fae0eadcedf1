"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OfflineSailingManager = void 0;
const typedi_1 = require("typedi");
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const typedi_2 = require("typedi");
const offlineSailingJob_1 = require("./offlineSailingJob");
const server_1 = require("./server");
const sailConst_1 = require("./sailConst");
const dbScanner_1 = require("./dbScanner");
const saildPubsub = __importStar(require("./sailPubsub"));
// ----------------------------------------------------------------------------
// OfflineSailingManager object.
// ----------------------------------------------------------------------------
let OfflineSailingManager = class OfflineSailingManager {
    constructor() {
        this._offlineSailingJobs = {};
        this._lastDBScanTimeInMs = Date.now();
    }
    // ----------------------------------------------------------------------------
    addJob(autoSailingInfo) {
        if (this._offlineSailingJobs[autoSailingInfo.userId]) {
            mlog_1.default.error('duplicate job', {
                accountId: autoSailingInfo.accountId,
                userId: autoSailingInfo.userId,
            });
            return null;
        }
        mlog_1.default.verbose('OfflineSailingManager.addJob', { autoSailingInfo });
        const newJob = new offlineSailingJob_1.OfflineSailingJob(autoSailingInfo);
        this._offlineSailingJobs[autoSailingInfo.userId] = newJob;
        return newJob;
    }
    // ----------------------------------------------------------------------------
    removeJob(userId) {
        delete this._offlineSailingJobs[userId];
    }
    // ----------------------------------------------------------------------------
    getJob(userId) {
        const job = this._offlineSailingJobs[userId];
        if (job) {
            return job;
        }
        return undefined;
    }
    // ----------------------------------------------------------------------------
    kickUser(userId, reason, authdId) {
        return Promise.resolve().then(() => {
            const jobToKick = this._offlineSailingJobs[userId];
            if (!jobToKick) {
                mlog_1.default.warn('job to kick is already gone', {
                    userId,
                });
                return saildPubsub.pubUserKicked(userId, authdId);
            }
            return jobToKick.close();
        });
    }
    // ----------------------------------------------------------------------------
    getAllJobs() {
        let jobs = [];
        jobs = Object.values(this._offlineSailingJobs);
        return jobs;
    }
    // ----------------------------------------------------------------------------
    getBot(userId) {
        const job = this._offlineSailingJobs[userId];
        if (job) {
            return job.getBot();
        }
        return undefined;
    }
    // ----------------------------------------------------------------------------
    getAllBots() {
        const bots = [];
        Object.values(this._offlineSailingJobs).forEach((job) => {
            bots.push(job.getBot());
        });
        return bots;
    }
    // ----------------------------------------------------------------------------
    hadlePacketForBot(userId, packet) {
        const job = this._offlineSailingJobs[userId];
        if (job) {
            const bot = job.getBot();
            bot.conn.onRecvBufferProxy(packet);
            return Promise.resolve(true);
        }
        return Promise.resolve(false);
    }
    // ----------------------------------------------------------------------------
    tickDBScan(curTimeInMs) {
        let dbScanTickInterval = sailConst_1.DEFAULT_OFFLINE_SAILING_DB_SCAN_TICK_INTERVAL;
        if (mconf_1.default.offlineSailingTick && mconf_1.default.offlineSailingTick.dbScanTickInterval) {
            dbScanTickInterval = mconf_1.default.offlineSailingTick.dbScanTickInterval;
        }
        const elapsedTimeInMs = curTimeInMs - this._lastDBScanTimeInMs;
        if (elapsedTimeInMs < dbScanTickInterval) {
            return;
        }
        this._lastDBScanTimeInMs = curTimeInMs;
        return (0, dbScanner_1.dbScanner)();
    }
    // ----------------------------------------------------------------------------
    tickJobs(curTimeInMs) {
        try {
            let removableJobs = [];
            const jobIds = Object.keys(this._offlineSailingJobs);
            for (const key of jobIds) {
                if (!this._offlineSailingJobs[key].tick(curTimeInMs)) {
                    removableJobs.push(this._offlineSailingJobs[key]);
                }
            }
            for (const job of removableJobs) {
                job.close();
                this.removeJob(job.userId);
            }
        }
        catch (e) {
            mlog_1.default.error('offlineSailingManager tick error!', {
                error: e.message,
                stack: e.stack,
            });
        }
        // Check elapsed time.
        // const elapsedTimeInMs = Date.now() - curTimeInMs;
        // if (elapsedTimeInMs > mconf.userTick.warningElapsedTime) {
        //   mlog.warn(`offlineSailingManager Elapsed time exceeded, elapsed: ${elapsedTimeInMs}ms`);
        // }
    }
    // ----------------------------------------------------------------------------
    startTick() {
        let managerTickInterval = sailConst_1.DEFAULT_OFFLINE_SAILING_MANAGER_TICK_INTERVAL;
        if (mconf_1.default.offlineSailingTick && mconf_1.default.offlineSailingTick.managerTickInterval) {
            managerTickInterval = mconf_1.default.offlineSailingTick.managerTickInterval;
        }
        mlog_1.default.info('[offlineSailingManager] tick started', {
            managerTickInterval,
        });
        this._offlineSailingTickInterval = setInterval(() => {
            const curTimeInMs = Date.now();
            const sailService = typedi_1.Container.get(server_1.SailService);
            if (sailService.bMaintenance) {
                if (sailService.lastMaintenanceTick + 30 < curTimeInMs / 1000) {
                    mlog_1.default.info('maintenance in progress', {
                        lastMaintenanceTick: sailService.lastMaintenanceTick,
                    });
                    sailService.lastMaintenanceTick = Math.floor(curTimeInMs / 1000);
                    // redis 의 상태값 체크
                    const { monitorRedis } = typedi_1.Container.get(server_1.SailService);
                    monitorRedis['getMaintenace'](mconf_1.default.worldId)
                        .then((ret) => {
                        if (1 !== ret) {
                            mlog_1.default.info('maintenance is over', {
                                ret,
                            });
                            sailService.bMaintenance = false;
                        }
                    })
                        .catch((error) => {
                        mlog_1.default.error('[offlineSailingManager] getMaintenace error!', {
                            error: error.message,
                            stack: error.stack,
                        });
                    });
                }
            }
            else {
                this.tickDBScan(curTimeInMs);
            }
            this.tickJobs(curTimeInMs);
        }, managerTickInterval);
    }
    stopTick() {
        if (this._offlineSailingTickInterval) {
            clearInterval(this._offlineSailingTickInterval);
        }
    }
};
OfflineSailingManager = __decorate([
    (0, typedi_2.Service)()
], OfflineSailingManager);
exports.OfflineSailingManager = OfflineSailingManager;
//# sourceMappingURL=offlineSailingManager.js.map