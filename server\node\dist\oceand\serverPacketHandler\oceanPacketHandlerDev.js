"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const protocol_1 = require("../../proto/oceand-lobbyd/protocol");
const tcp = __importStar(require("../../tcplib"));
const merror_1 = require("../../motiflib/merror");
const mlog_1 = __importDefault(require("../../motiflib/mlog"));
const typedi_1 = __importDefault(require("typedi"));
const fleetManager_1 = require("../fleetManager");
const oceanCheatParam_1 = require("../oceanCheatParam");
const cms_1 = __importDefault(require("../../cms"));
const ex_1 = require("../../cms/ex");
const oceanNpcFleet_1 = require("../oceanNpcFleet");
const oceanEntityInstanceManager_1 = require("../oceanEntityInstanceManager");
const oceanDoodadEntity_1 = require("../oceanDoodadEntity");
// ----------------------------------------------------------------------------
// 패킷 콜백 함수 등록
// ----------------------------------------------------------------------------
const router = tcp.createPacketRouter();
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_IGNORE_NPC_ENCOUNT, async (req, res) => {
    const userId = req.userId;
    const bFlag = req.bFlag;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    const fleet = fleetManager.get(userId);
    if (!fleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    fleet.setIgnoringNpcEncount(bFlag);
    mlog_1.default.warn(`Set ignore-npc-encount for user [${userId}]: ${bFlag}`, { userId });
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_ADD_NEAR_SPAWNER, async (req, res) => {
    const userId = req.userId;
    const npcCmsId = req.npcCmsId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const oceanNpcCms = (0, ex_1.getOceanNpcCms)()[npcCmsId];
    if (!oceanNpcCms) {
        throw new merror_1.MError('invalid npcCmsId!', merror_1.MErrorCode.INVALID_OCEAN_NPC_CMS_ID, {
            userId,
            npcCmsId,
        });
    }
    // userFleet
    //   .getLocalNpcSpawnEntry()
    //   .spawnQuestNpc(userFleet, npcCmsId, userFleet.getLocation(), userFleet.getDegrees());
    const entityInsMng = typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager);
    const npcFleet = entityInsMng.newDevNpc(userFleet.getCurrentZone(), npcCmsId, userFleet.getLocation(), userFleet.getDegrees());
    if (!npcFleet) {
        throw new merror_1.MError('can not create npcFleet!', merror_1.MErrorCode.INTERNAL_ERROR, {
            userId,
            npcCmsId,
        });
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_ADD_USER_DATA_SPAWNER, async (req, res) => {
    const userId = req.userId;
    const userFleetData = req.userFleetData;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const entityInsMng = typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager);
    const npcFleet = entityInsMng.newAreaUserNpc(userFleet.getCurrentZone(), userFleetData, userFleet.getLocation(), userFleet.getDegrees());
    if (!npcFleet) {
        throw new merror_1.MError('can not create npcFleet!', merror_1.MErrorCode.INTERNAL_ERROR, {
            userId,
        });
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_REMOVE_NEAR_SPAWNER, async (req, res) => {
    const userId = req.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    // userFleet.getLocalNpcSpawnEntry().despawnQuestNpc(userFleet, undefined);
    const entityInsMng = typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager);
    const npcFleets = userFleet.getCurrentTile().getNpcFleets();
    npcFleets.forEach((npcFleet) => entityInsMng.deleteNpcFleet(npcFleet));
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_NPC_ATTACK_RADIUS, async (packet, res) => {
    if (packet.radius === 1) {
        packet.radius = -1;
    }
    oceanCheatParam_1.CheatParam.npcEncountRadius = packet.radius;
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_NPC_TICK_PER_SEC, async (packet, res) => {
    oceanCheatParam_1.CheatParam.tickPerSec = 1000 / packet.tickPerSec;
});
router.on(protocol_1.Protocol.LB2OC_REQ_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER, async (packet, res) => {
    const userId = packet.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const zone = userFleet.getCurrentZone();
    if (!zone) {
        throw new merror_1.MError('User not in ocean zone!', merror_1.MErrorCode.USER_NOT_IN_OCEAN_ZONE, {
            userId,
        });
    }
    let sendPacket = new protocol_1.Protocol.OC2LB_RES_DEV_GET_NPC_LOCATION_BY_AREA_SPAWNER();
    const oceanTileArray = zone.getOceanTileArray();
    for (const tile of oceanTileArray) {
        const grids = tile.getGrids();
        for (const grid of grids) {
            const npcFleetsInGrid = grid.getNpcFleets();
            for (const npcFleet of npcFleetsInGrid) {
                if (npcFleet instanceof oceanNpcFleet_1.OceanAreaNpcFleet) {
                    sendPacket.npcs.push({
                        npcCmsId: npcFleet.getNpcCmsId(),
                        latitude: npcFleet.getLocation().latitude,
                        longitude: npcFleet.getLocation().longitude,
                    });
                }
            }
        }
    }
    res.send(sendPacket);
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_ADD_OCEAN_DOODAD_NEAR_SPAWNER, async (req, res) => {
    const userId = req.userId;
    const doodadCmsId = req.doodadCmsId;
    const isGlobal = false;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const oceanDoodadCms = cms_1.default.OceanDoodad[doodadCmsId];
    if (!oceanDoodadCms) {
        throw new merror_1.MError('invalid doodadCmsId!', merror_1.MErrorCode.INVALID_OCEAN_DOODAD_CMS_ID, {
            userId,
            doodadCmsId,
        });
    }
    let oceanDoodad;
    if (!isGlobal) {
        const param = {
            baseOceanDoodadParam: {
                doodadCmsId,
                location: userFleet.getLocation(),
                degrees: userFleet.getDegrees(),
            },
            spawndCellId: '',
            type: oceanDoodadEntity_1.LOCAL_DOODAD_TYPE.NORMAL,
        };
        oceanDoodad = userFleet.spawnLocalNormalDoodad(param);
    }
    else {
        mlog_1.default.error('unsupported request yet...can not create manually global ocean doodad', {
            userId,
            doodadCmsId,
        });
    }
    if (!oceanDoodad) {
        throw new merror_1.MError('can not create oceanDoodad!', merror_1.MErrorCode.INTERNAL_ERROR, {
            userId,
            doodadCmsId,
        });
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_REMOVE_OCEAN_DOODAD_NEAR_SPAWNER, async (req, res) => {
    const userId = req.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const spawnedDoodads = userFleet.getSpawnedDoodads();
    spawnedDoodads.forEach((doodad) => userFleet.despawnLocalDoodad(doodad, true));
});
router.on(protocol_1.Protocol.LB2OC_NTF_SET_LOCAL_NPC_SPAWN, async (packet, res) => {
    const userId = packet.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const zone = userFleet.getCurrentZone();
    if (!zone) {
        throw new merror_1.MError('User not in ocean zone!', merror_1.MErrorCode.USER_NOT_IN_OCEAN_ZONE, {
            userId,
        });
    }
    userFleet.getLocalNpcSpawnEntry().devOnOff = packet.flag === 0 ? false : true;
    const destroyNpc = (ownerUserId) => {
        return (npc) => {
            if (npc instanceof oceanNpcFleet_1.OceanLocalNpcFleet) {
                if (ownerUserId === npc.ownerUserId) {
                    typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager).deleteNpcFleet(npc);
                }
            }
        };
    };
    if (userFleet.getLocalNpcSpawnEntry().devOnOff === false) {
        const func = destroyNpc(userFleet.userId);
        const oceanTileArray = zone.getOceanTileArray();
        for (const tile of oceanTileArray) {
            const grids = tile.getGrids();
            for (const grid of grids) {
                const npcFleetsInGrid = grid.getNpcFleets();
                for (const npcFleet of npcFleetsInGrid) {
                    func(npcFleet);
                }
            }
        }
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_SET_LOCAL_DOODAD_SPAWN, async (packet, res) => {
    const userId = packet.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const zone = userFleet.getCurrentZone();
    if (!zone) {
        throw new merror_1.MError('User not in ocean zone!', merror_1.MErrorCode.USER_NOT_IN_OCEAN_ZONE, {
            userId,
        });
    }
    userFleet.getLocalDoodadSpawnEntry().devOnOff = packet.flag === 0 ? false : true;
    const destroyDoodad = (ownerUserId) => {
        return (doodad) => {
            if (doodad instanceof oceanDoodadEntity_1.OceanLocalDoodad) {
                if (ownerUserId === doodad.ownerUserId) {
                    typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager).deleteDoodad(doodad);
                }
            }
        };
    };
    if (userFleet.getLocalDoodadSpawnEntry().devOnOff === false) {
        const func = destroyDoodad(userFleet.userId);
        const oceanTileArray = zone.getOceanTileArray();
        for (const tile of oceanTileArray) {
            const grids = tile.getGrids();
            for (const grid of grids) {
                const doodadsInGrid = grid.getDoodads();
                for (const doodad of doodadsInGrid) {
                    func(doodad);
                }
            }
        }
    }
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_ATTACK_TO_ME, async (packet, res) => {
    const userId = packet.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const zone = userFleet.getCurrentZone();
    if (!zone) {
        throw new merror_1.MError('User not in ocean zone!', merror_1.MErrorCode.USER_NOT_IN_OCEAN_ZONE, {
            userId,
        });
    }
    userFleet.forDev.bDevAttackMe = packet.flag;
});
router.on(protocol_1.Protocol.LB2OC_NTF_DEV_LOCAL_NPC_SPAWN_NUM, async (packet, res) => {
    const userId = packet.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const zone = userFleet.getCurrentZone();
    if (!zone) {
        throw new merror_1.MError('User not in ocean zone!', merror_1.MErrorCode.USER_NOT_IN_OCEAN_ZONE, {
            userId,
        });
    }
    const entityInsMng = typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager);
    const npcFleets = userFleet.getCurrentTile().getNpcFleets();
    npcFleets.forEach((npcFleet) => entityInsMng.deleteNpcFleet(npcFleet));
    userFleet.forDev.enableNumSpawn = packet.on;
    userFleet.forDev.occupyingBattle = packet.occupyingBattle;
    userFleet.forDev.occupyingTrade = packet.occupyingTrade;
    userFleet.forDev.occupyingExploration = packet.occupyingExploration;
    userFleet.forDev.occupiedBattle = packet.occupiedBattle;
    userFleet.forDev.occupiedTrade = packet.occupiedTrade;
    userFleet.forDev.occupiedExploration = packet.occupiedExploration;
    userFleet.forDev.pirateBattle = packet.pirateBattle;
});
router.on(protocol_1.Protocol.LB2OC_NTF_SERVER_DEBUG_MSG, async (packet, res) => {
    const userId = packet.userId;
    const fleetManager = typedi_1.default.get(fleetManager_1.FleetManager);
    // 유저 상태 검증.
    const userFleet = fleetManager.get(userId);
    if (!userFleet) {
        throw new merror_1.MError('User not found in this oceand!', merror_1.MErrorCode.USER_FLEET_NOT_FOUND, {
            userId,
        });
    }
    const zone = userFleet.getCurrentZone();
    if (!zone) {
        throw new merror_1.MError('User not in ocean zone!', merror_1.MErrorCode.USER_NOT_IN_OCEAN_ZONE, {
            userId,
        });
    }
    if (packet.type === 1) {
        const entityInsMng = typedi_1.default.get(oceanEntityInstanceManager_1.OceanEntityInstanceManager);
        const npcFleets = userFleet.getCurrentTile().getNpcFleets();
        npcFleets.forEach((npcFleet) => entityInsMng.deleteNpcFleet(npcFleet));
        userFleet.forDev.npcNoEncountMsg = packet.on;
    }
    else if (packet.type === 2) {
        const localNpcSpawnEntry = userFleet.getLocalNpcSpawnEntry();
        localNpcSpawnEntry.sendDebugMsg(userFleet);
    }
});
module.exports = router;
//# sourceMappingURL=oceanPacketHandlerDev.js.map