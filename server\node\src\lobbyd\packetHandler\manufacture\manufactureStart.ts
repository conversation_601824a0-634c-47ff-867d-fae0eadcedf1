// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import { Container } from 'typedi';
import cms from '../../../cms';
import mconf from '../../../motiflib/mconf';
import * as cmsEx from '../../../cms/ex';
import * as mutil from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../../../motiflib/model/lobby';
import { LobbyService } from '../../server';
import { Sync, Resp } from '../../type/sync';
import { BuffSync } from '../../userBuffs';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ItemChange, ShipSlotItem } from '../../userInven';
import { CartGoods } from '../town/tradeBuy';
import { ShipCargoChange } from '../../ship';
import { EnergyChange } from '../../userEnergy';
import { CEQUIP_TYPE } from '../../../cms/cEquipDesc';
import { ShipSlotDesc, SHIP_SLOT_TYPE } from '../../../cms/shipSlotDesc';
import { ClientPacketHandler } from '../index';
import { ManufactureRoomDesc } from '../../../cms/manufactureRoomDesc';
import { MANUFACTURE_MATERIAL_TYPE, MANUFACTURE_RECIPE_CATEGORY, MANUFACTURE_PROGRESS_TYPE, ManufactureRecipeDesc } from '../../../cms/manufactureRecipeDesc';
import UserFleets from '../../userFleets';
import UserPoints, { PointAndCashChanges, PointConsumptionCostParam, PointChange } from '../../userPoints';
import tuManufactureStart from '../../../mysqllib/txn/tuManufactureStart';
import { ManufacturePointChange, ManufactureProgress } from '../../userManufacture';
import { AccumulateParam } from '../../userAchievement';
import { randIntInc } from '../../../motiflib/mutil';
import { RewardCmsElemItemExtra, RewardCmsElemShipSlotItemExtra, RewardCmsElemMateEquipmentExtra } from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import { MANUFACTURE_RECIPE_UNLOCKTYPE } from '../../../cms/manufactureGroupDesc';

// ----------------------------------------------------------------------------
// 제조 시작
// ----------------------------------------------------------------------------
const rsn = 'manufacture_start';
const add_rsn = null;

// 제조 관련 상수
const MANUFACTURE_CONSTANTS = {
  MAX_TRY_COUNT: 10,
  MAX_SLOT_COUNT: 10,
  PROBABILITY_BASE: 10000,
} as const;

interface RequestBody {
  roomId: number; // 제조실 id
  recipeCmsId: number;
  tryCount: number; // 제작할 개수
  mateCmsIds: number[];     // 배치될 항해사 ids
  mainIngredientsIds: {
    mateEquipmentIds?: number[];
    shipSlotIds?: number[];
    cartGoods?: CartGoods[];
  }[]; // tryCount만큼의 메인 재료 세트
  subIngredientsIds: {
    mateEquipmentIds?: number[];
    shipSlotIds?: number[];
    cartGoods?: CartGoods[];
  }[]; // tryCount만큼의 서브 재료 세트
  bPermitExchange: boolean;
}

interface ManufactureResult {
  progressType: MANUFACTURE_PROGRESS_TYPE;
  successRate: number;
  greatSuccessRate: number;
  mergedEquips: number[];
  mergedShipParts: number[];
  mergedCargoChanges: ShipCargoChange[];
  mergedItemChanges: ItemChange[];
}

// DB 업데이트를 위한 통합 데이터 구조
interface ManufactureDbUpdateData {
  manufactureProgress: ManufactureProgress;
  roomCmsId: number;
  userPointChanges: PointChange[];
  deleteEquipItems: number[];
  deleteShipSlots: number[];
  itemChanges: ItemChange[];
  energyChange: EnergyChange;
  manufacturePointChange: ManufacturePointChange;
  shipCargoChanges: ShipCargoChange[];
}

// 재료 타입 정의
interface Material {
  Type: number;
  Target: number;
  EnchantLv: number;
  Quantity: number;
}

// 포인트 타입 정의
interface PointCost {
  Type: number;
  Value: number;
}

interface ValidatedIngredients {
  mateEquipIds: number[];
  shipSlotItemIds: number[];
  shipCargoChanges: ShipCargoChange[];
  itemChanges: ItemChange[];
  requiredItems: { [cmsId: number]: number };
  extra?: string; // 실패 시 아이템 복구를 위한 extra 정보
}

// extra 공용 인터페이스. manufactureReceive.ts에서 사용.
export interface RestoreCmsElemExtra {
  restoreType: number;
  restoreCmsId: number;
  count: number;
  isBound: number;
  isAccum: number;
  expireTimeUtc?: number;
  enchantLv?: number;
  shipId?: number; // 교역품 복구 시 필요
}

export class Cph_Manufacture_Start implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() { }

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
      return true;
  }

    // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const { userDbConnPoolMgr } = Container.get(LobbyService);
    const body: RequestBody = packet.bodyObj;
    const { roomId, recipeCmsId, tryCount, mateCmsIds, mainIngredientsIds, subIngredientsIds, bPermitExchange } = body;

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    // =============================================
    // 1. 사전 검증 및 데이터 준비
    // =============================================
    const validationResult = this.performPreValidation(user, tryCount, mainIngredientsIds, subIngredientsIds, recipeCmsId, roomId, bPermitExchange);
    const { recipeCms, roomCms, energyChange, pcChanges, manufacturePointChange, addMateRate } = validationResult;

    // =============================================
    // 2. 제조 실행
    // =============================================
    const manufactureResult = this.executeManufactureProcess(
      user, roomCms, recipeCms, tryCount, mateCmsIds, addMateRate, mainIngredientsIds, subIngredientsIds
    );
    const { manufactureResults, allManufactureProgress, allConsumedMainItems, allConsumedSubItems, mergedResult } = manufactureResult;

    // =============================================
    // 3. DB 처리 및 Sync 적용
    // =============================================
    return this.processDatabaseAndSync(
      user, userDbConnPoolMgr, roomCms, allManufactureProgress, pcChanges, mergedResult, 
      energyChange, manufacturePointChange, packet
    );
  }

  // --------------------------------------------------------------------------
  private performPreValidation(
    user: User, 
    tryCount: number, 
    mainIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[],
    subIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[],
    recipeCmsId: number,
    roomId: number,
    bPermitExchange: boolean
  ) {
    // tryCount 유효성 검사 및 재료 배열 크기 검증
    this.validateTryCountAndIngredients(user, tryCount, mainIngredientsIds, subIngredientsIds);

    // 레시피 데이터 validation
    const recipeCms = this.validateRecipe(recipeCmsId, user);

    // 행동력, 생산력 체크 (tryCount만큼)
    const { energyChange, pcChanges, manufacturePointChange } = this.validateAndBuildCosts(user, recipeCms, bPermitExchange, tryCount);

    // 생산실 체크
    const roomCms = this.validateManufactureRoom(roomId, user);

    // 재료 validation (한 번만 검증)
    validateMainIngredients(user, recipeCms, mainIngredientsIds);
    validateSubIngredients(user, recipeCms, subIngredientsIds);

    // 항해사 배치 체크 및 대성공 rate 계산
    const mateCmsIds = mainIngredientsIds.reduce((acc, ing) => acc.concat(ing.mateEquipmentIds || []), [] as number[]);
    const addMateRate = calcAddMateSuccessRate(user, recipeCms, mateCmsIds);

    return { recipeCms, roomCms, energyChange, pcChanges, manufacturePointChange, addMateRate };
  }

  // --------------------------------------------------------------------------
  private executeManufactureProcess(
    user: User,
    roomCms: ManufactureRoomDesc,
    recipeCms: ManufactureRecipeDesc,
    tryCount: number,
    mateCmsIds: number[],
    addMateRate: number,
    mainIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[],
    subIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[]
  ) {
    // tryCount만큼 제작 실행
    const { manufactureResults, allManufactureProgress, allConsumedMainItems, allConsumedSubItems }
      = this.executeManufactureAttempts(
      user, roomCms, recipeCms, tryCount, mateCmsIds, addMateRate, mainIngredientsIds, subIngredientsIds
    );

    // 모든 제작 결과를 하나로 병합
    const mergedResult = this.mergeManufactureResults(manufactureResults, allConsumedMainItems, allConsumedSubItems);

    return { manufactureResults, allManufactureProgress, allConsumedMainItems, allConsumedSubItems, mergedResult };
  }

  // --------------------------------------------------------------------------
  private async processDatabaseAndSync(
    user: User,
    userDbConnPoolMgr: any,
    roomCms: ManufactureRoomDesc,
    allManufactureProgress: ManufactureProgress,
    pcChanges: PointAndCashChanges,
    mergedResult: ManufactureResult,
    energyChange: EnergyChange,
    manufacturePointChange: ManufacturePointChange,
    packet: CPacket
  ): Promise<any> {
    const sync: Sync = {
      add: {},
      remove: {},
    };

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    
    return user.userPoints
      .tryConsumeCashs(pcChanges.cashPayments, sync, user, { user, rsn, add_rsn, exchangeHash })
      .then(() => {
        // 통합된 데이터 구조 생성
        const updateData: ManufactureDbUpdateData = {
          manufactureProgress: allManufactureProgress,
          roomCmsId: roomCms.id,
          userPointChanges: pcChanges.pointChanges,
          deleteEquipItems: mergedResult.mergedEquips,
          deleteShipSlots: mergedResult.mergedShipParts,
          itemChanges: mergedResult.mergedItemChanges,
          energyChange,
          manufacturePointChange,
          shipCargoChanges: mergedResult.mergedCargoChanges,
        };

        return tuManufactureStart(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          updateData
        );
      })
      .then(() => {
        this.applySyncChanges(user, sync, roomCms, allManufactureProgress, pcChanges, mergedResult, energyChange, manufacturePointChange);
        return user.sendJsonPacket(packet.seqNum, packet.type, { sync });
      });
  }

  // --------------------------------------------------------------------------
  private applySyncChanges(
    user: User,
    sync: Sync,
    roomCms: ManufactureRoomDesc,
    allManufactureProgress: ManufactureProgress,
    pcChanges: PointAndCashChanges,
    mergedResult: ManufactureResult,
    energyChange: EnergyChange,
    manufacturePointChange: ManufacturePointChange
  ): void {
    // 에너지 변경사항을 sync에 적용
    _.merge<Sync, Sync>(
      sync,
      user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn })
    );

    // 포인트 변경사항을 sync에 적용
    _.merge<Sync, Sync>(
      sync,
      user.userPoints.applyPointChanges(pcChanges.pointChanges, { user, rsn, add_rsn })
    );

    // 제조 포인트 변경사항을 sync에 적용
    if (manufacturePointChange) {
      _.merge<Sync, Sync>(
        sync,
        user.userManufacture.applyPointChange(manufacturePointChange, { user, rsn, add_rsn })
      );
    }

    // 새로운 제조 진행상황을 sync에 추가
    if (Object.keys(allManufactureProgress).length > 0) {
      user.userManufacture.applyManufactureRoomInfo(allManufactureProgress, roomCms.id);
      _.merge<Sync, Sync>(sync, {
        add: {
          manufacture: {
            roomInfo: {
              [roomCms.id]: allManufactureProgress,
            },
          },
        },
      });
    }

    // 아이템 변경사항을 sync에 적용
    this.applyItemChanges(user, sync, mergedResult);
  }

  // --------------------------------------------------------------------------
  private applyItemChanges(user: User, sync: Sync, mergedResult: ManufactureResult): void {
    const accums: AccumulateParam[] = [];

    // 아이템 삭제 (mergedItemChanges)
    for (const itemChange of mergedResult.mergedItemChanges) {
      _.merge<Sync, Sync>(
        sync,
        user.userInven.itemInven.applyItemChange(itemChange, accums, { user, rsn, add_rsn })
      );
    }

    // 장착아이템 삭제 (mergedEquips)
    sync.remove.mateEquipments = [];
    for (const id of mergedResult.mergedEquips) {
      user.userMates.removeMateEquipment(
        id,
        user.companyStat,
        user.userPassives,
        user.userFleets,
        user.userSailing,
        user.userTriggers,
        user.userBuffs,
        { user, rsn, add_rsn },
        sync
      );
      sync.remove.mateEquipments.push(id.toString());
    }

    // 선박슬롯 삭제 (mergedShipParts)
    sync.remove.shipSlotItems = [];
    for (const shipSlotItemId of mergedResult.mergedShipParts) {
      user.userInven.removeShipSlotItem(shipSlotItemId, { user, rsn, add_rsn });
      sync.remove.shipSlotItems.push(shipSlotItemId.toString());
    }

    // 선박 화물 삭제 (mergedCargoChanges)
    const buffSync: BuffSync = { sync: {} };
    user.userFleets.applyCargoChanges(mergedResult.mergedCargoChanges, user, { user, rsn, add_rsn }, buffSync);
    _.merge<Sync, Sync>(sync, buffSync.sync);

    // 업적 누적
    user.userAchievement.accumulate(accums, user, sync, { user, rsn, add_rsn });
  }

  // --------------------------------------------------------------------------
  private validateRecipe(recipeCmsId: number, user: User): ManufactureRecipeDesc {
    const recipeCms: ManufactureRecipeDesc = cms.ManufactureRecipe[recipeCmsId];
    if (!recipeCms) {
      throw new MError('invalid-manufacture-recipe-cms-id', MErrorCode.INVALID_MANUFACTURE_RECIPE_CMS_ID, {
        userId: user.userId,
        recipeCmsId,
      });
    }

    // 라이브 이벤트 중이면 아래 체크로직 검사안함.
    let isLiveEvent = false;
    const recipeGroupCms = cms.ManufactureGroup[recipeCms.manufactureGroupId];
    if (recipeGroupCms && recipeGroupCms.unlockType === MANUFACTURE_RECIPE_UNLOCKTYPE.LIVE_EVENT) {
      isLiveEvent = cmsEx.isLiveEvent(recipeGroupCms.unlockTargetId, mutil.curTimeUtc());
    } 
    if (!isLiveEvent) {
      // 레시피가 해금되어 있는지 확인
      if (!user.userManufacture.isRecipeGroupUnlocked(recipeCms.manufactureGroupId)) {
        throw new MError('recipe-not-unlocked', MErrorCode.RECIPE_NOT_UNLOCKED, {
          userId: user.userId,
          recipeCmsId,
        });
      }
    }

    return recipeCms;
  }

  // --------------------------------------------------------------------------
  private validateAndBuildCosts(user: User, recipeCms: ManufactureRecipeDesc, bPermitExchange: boolean, tryCount: number):
    {
      energyChange: EnergyChange,
      pcChanges: PointAndCashChanges,
      manufacturePointChange: ManufacturePointChange
    } {
    const curTimeUtc: number = mutil.curTimeUtc();
    const curEnergy: number = user.userEnergy.getCurrentEnergy(curTimeUtc, user.level);
    const currentManufacturePoint = user.userManufacture.getCurrentPoint(curTimeUtc);
    
    // tryCount만큼 비용 계산
    const totalRequiredEnergy = recipeCms.costEnergy * tryCount;
    const totalRequiredManufacturePoints = recipeCms.costManufacturePoint * tryCount;
    
    if (totalRequiredEnergy > curEnergy || totalRequiredManufacturePoints > currentManufacturePoint) {
      throw new MError('insufficient-energy-or-manufacture-points', MErrorCode.INSUFFICIENT_MANUFACTURE_ENERGY_OR_POINTS, {
        userId: user.userId,
        recipeCmsId: recipeCms.id,
        requiredEnergy: totalRequiredEnergy,
        currentEnergy: curEnergy,
        requiredManufacturePoints: totalRequiredManufacturePoints,
        currentManufacturePoints: currentManufacturePoint,
        tryCount,
      });
    }

    // 생산 레시피 레벨 체크
    const manufactureExp = user.userManufacture.exps;
    const manufactureExpInfo = manufactureExp[recipeCms.manufactureType];
    if (!manufactureExpInfo || recipeCms.manufactureLv > manufactureExpInfo.level) {
      throw new MError('insufficient-manufacture-level', MErrorCode.INSUFFICIENT_MANUFACTURE_LEVEL, {
        userId: user.userId,
        recipeCmsId: recipeCms.id,
        requiredLevel: recipeCms.manufactureLv,
        currentLevel: manufactureExpInfo?.level || 0,
      });
    }

    const energyChange: EnergyChange = user.userEnergy.buildEnergyChangeWithConsume(
      curTimeUtc,
      user.level,
      user.level,
      totalRequiredEnergy,
      true
    );

         const mCost: number = (recipeCms.costPoint as PointCost).Value * tryCount;
    if (isNaN(mCost) || mCost <= 0) {
      throw new MError(
        'invalid-cost-for-manufacture',
        MErrorCode.INVALID_MANUFACTURE_COST,
        {
          userId: user.userId,
          cost: mCost,
          recipeCmsId: recipeCms.id,
          tryCount,
        }
      );
    }
    
         let pointCmsId = (recipeCms.costPoint as PointCost).Type;
    // if (recipeCms.costPoint.Type === 1) {
    //   pointCmsId = cmsEx.DucatPointCmsId;
    // } else {
    //   pointCmsId = cmsEx.BlueGemPointCmsId;
    // }
    const pointCost: PointConsumptionCostParam = {
      cmsId: pointCmsId,
      cost: mCost,
    };

    const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
      [pointCost],
      bPermitExchange,
      { itemId: rsn },
      true
    );

    const currentUtcTime = mutil.curTimeUtc();
    const [success, manufacturePointChange] = user.userManufacture.buildPointChange(currentUtcTime, totalRequiredManufacturePoints);
    if (!success) {
      throw new MError(
        'invalid-cost-for-manufacture',
        MErrorCode.INVALID_MANUFACTURE_COST,
        {
          userId: user.userId,
          cost: mCost,
          recipeCmsId: recipeCms.id,
          tryCount,
        }
      );
    }

    return { energyChange, pcChanges, manufacturePointChange };
  }

  // --------------------------------------------------------------------------
  private validateTryCountAndIngredients(
    user: User, 
    tryCount: number, 
    mainIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[],
    subIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[]
  ): void {
    // 재료 배열이 존재하고 비어있지 않을 때만 tryCount 검증
    const hasMainIngredients = mainIngredientsIds?.length > 0;
    const hasSubIngredients = subIngredientsIds?.length > 0;

    // tryCount 유효성 검사 (재료가 있을 때만)
    if (hasMainIngredients || hasSubIngredients) {
      if (tryCount <= 0 || tryCount > MANUFACTURE_CONSTANTS.MAX_TRY_COUNT) {
        throw new MError('invalid-try-count', MErrorCode.INVALID_MANUFACTURE_TRY_COUNT, {
          userId: user.userId,
          tryCount,
        });
      }
    }

    // 메인 재료 배열 크기 검증 (재료가 있을 때만)
    if (hasMainIngredients && mainIngredientsIds.length !== tryCount) {
      throw new MError('invalid-main-ingredients-count', MErrorCode.INVALID_MANUFACTURE_TRY_COUNT, {
        userId: user.userId,
        tryCount, 
        mainIngredientsCount: mainIngredientsIds.length,
      });
    }

    // 서브 재료 배열 크기 검증 (재료가 있을 때만)
    if (hasSubIngredients && subIngredientsIds.length !== tryCount) {
      throw new MError('invalid-sub-ingredients-count', MErrorCode.INVALID_MANUFACTURE_TRY_COUNT, {
        userId: user.userId,
        tryCount,
        subIngredientsCount: subIngredientsIds.length,
      });
    }
  }

  // --------------------------------------------------------------------------
  private validateManufactureRoom(roomId: number, user: User): ManufactureRoomDesc {
    const roomCms: ManufactureRoomDesc = cms.ManufactureRoom[roomId];
    if (!roomCms) {
      throw new MError('invalid-manufacture-room-cms-id', MErrorCode.INVALID_MANUFACTURE_ROOM_CMS_ID, {
        userId: user.userId,
        roomId,
      });
    }

    // 이미 생산중이면 에러.
    const isOpened = user.userManufacture.getRoom(roomId);
    if (isOpened) {
      throw new MError('manufacture-room-already-in-use', MErrorCode.MANUFACTURE_ROOM_ALREADY_IN_USE, {
        userId: user.userId,
        roomId,
      });
    }

    return roomCms;
  }

  // --------------------------------------------------------------------------
  private calculateManufactureResult(
    recipeCms: ManufactureRecipeDesc, 
    addMateRate: number, 
    mainIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }, 
    subIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }
  ): ManufactureResult {
    const randomValue = randIntInc(1, MANUFACTURE_CONSTANTS.PROBABILITY_BASE);
    
    // 생산 실패 확률 = (PROBABILITY_BASE - recipeCms.defaultSuccessRate) / PROBABILITY_BASE
    const failureThreshold = MANUFACTURE_CONSTANTS.PROBABILITY_BASE - recipeCms.defaultSuccessRate;
    
    // 생산 대성공 확률 = recipeCms.defaultGreatSuccessRate + addMateRate (addMateRate는 퍼센트 단위)
    let greatSuccessThreshold = recipeCms.defaultGreatSuccessRate + addMateRate;
    if (greatSuccessThreshold > recipeCms.defaultSuccessRate) {
      greatSuccessThreshold = recipeCms.defaultSuccessRate;
    }
    
    // 생산 성공 확률 = 나머지 (recipeCms.defaultSuccessRate - greatSuccessThreshold)
    const normalSuccessThreshold = recipeCms.defaultSuccessRate - greatSuccessThreshold;
    
    // 확률에 따른 결과 타입 결정
    let progressType: MANUFACTURE_PROGRESS_TYPE;
    if (randomValue <= failureThreshold) {
      progressType = MANUFACTURE_PROGRESS_TYPE.FAILURE;
    } else if (randomValue <= failureThreshold + greatSuccessThreshold) {
      progressType = MANUFACTURE_PROGRESS_TYPE.GREAT_SUCCESS;
    } else {
      progressType = MANUFACTURE_PROGRESS_TYPE.SUCCESS;
    }
    
    return {
      progressType,
      successRate: normalSuccessThreshold,
      greatSuccessRate: greatSuccessThreshold,
      mergedEquips: [], // 성공 시에만 실제 값으로 업데이트됨
      mergedShipParts: [], // 성공 시에만 실제 값으로 업데이트됨
      mergedCargoChanges: [], // 성공 시에만 실제 값으로 업데이트됨
      mergedItemChanges: [], // 성공 시에만 실제 값으로 업데이트됨
    };
  }

  // --------------------------------------------------------------------------
  private createManufactureProgress(
    user: User,
    roomCms: ManufactureRoomDesc, 
    recipeCms: ManufactureRecipeDesc, 
    manufactureResult: ManufactureResult, 
    mateCmsIds: number[],
    addMateRate: number,
    slot: number,
    startTimeUtc: number,

    mainExtra?: string
  ): ManufactureProgress {
    const curTimeUtc: number = startTimeUtc;

    // 새로운 구조 반환: 슬롯의 맵
    const progressType = manufactureResult.progressType;
    const successRate = manufactureResult.successRate;
    const greatSuccessRate = manufactureResult.greatSuccessRate;

    // 교역품 타입만 즉시완료로 변경
    let completionTimeUtc: number;
    if (recipeCms.recipeCategory === MANUFACTURE_RECIPE_CATEGORY.TRADE_GOODS) {
      completionTimeUtc = 0;
    } else {
      completionTimeUtc = curTimeUtc + recipeCms.manufactureTime;
    }

    // 실패한 경우 메인 재료 extra 정보만 사용 (서브 재료는 복구하지 않음)
    let extra: string = null;
    if (progressType === MANUFACTURE_PROGRESS_TYPE.FAILURE) {
      if (mainExtra) {
        extra = mainExtra;
      }
    }

    return {
      [slot]: {
        slot: slot,
        recipeId: recipeCms.id,
        startTimeUtc: curTimeUtc,
        completionTimeUtc,
        resultType: progressType,
        mateCmsIds,
        successRate,
        greatSuccessRate,
        extra,
      }
    };
  }

       // --------------------------------------------------------------------------
  private mergeManufactureResults(
    manufactureResults: ManufactureResult[], 
    allConsumedMainItems: ValidatedIngredients, 
    allConsumedSubItems: ValidatedIngredients
  ): ManufactureResult {
    const mergedEquips: number[] = [];
    const mergedShipParts: number[] = [];
    const mergedCargoChanges: ShipCargoChange[] = [];
    const mergedItemChanges: ItemChange[] = [];

    // 모든 재료 소모 결과를 한 번에 병합
    mergedEquips.push(...allConsumedMainItems.mateEquipIds, ...allConsumedSubItems.mateEquipIds);
    mergedShipParts.push(...allConsumedMainItems.shipSlotItemIds, ...allConsumedSubItems.shipSlotItemIds);
    mergedItemChanges.push(...allConsumedMainItems.itemChanges, ...allConsumedSubItems.itemChanges);

    // 교역품 차감은 이미 consumeMainIngredients와 consumeSubIngredients에서 
    // cargoMap을 통해 누적 처리되어 shipCargoChanges에 포함되어 있음
    mergedCargoChanges.push(...allConsumedMainItems.shipCargoChanges, ...allConsumedSubItems.shipCargoChanges);

    // 전체 결과 타입 결정: 하나라도 성공이면 성공, 하나라도 대성공이면 대성공
    const hasGreatSuccess = manufactureResults.some(r => r.progressType === MANUFACTURE_PROGRESS_TYPE.GREAT_SUCCESS);
    const hasSuccess = manufactureResults.some(r => r.progressType === MANUFACTURE_PROGRESS_TYPE.SUCCESS || r.progressType === MANUFACTURE_PROGRESS_TYPE.GREAT_SUCCESS);
    
    const overallProgressType = hasGreatSuccess 
      ? MANUFACTURE_PROGRESS_TYPE.GREAT_SUCCESS 
      : hasSuccess 
        ? MANUFACTURE_PROGRESS_TYPE.SUCCESS 
        : MANUFACTURE_PROGRESS_TYPE.FAILURE;

    return {
      progressType: overallProgressType,
      successRate: manufactureResults.reduce((sum, r) => sum + r.successRate, 0) / manufactureResults.length,
      greatSuccessRate: manufactureResults.reduce((sum, r) => sum + r.greatSuccessRate, 0) / manufactureResults.length,
      mergedEquips,
      mergedShipParts,
      mergedCargoChanges,
      mergedItemChanges,
    };
  }

  // --------------------------------------------------------------------------
  private executeManufactureAttempts(
    user: User,
    roomCms: ManufactureRoomDesc,
    recipeCms: ManufactureRecipeDesc,
    tryCount: number,
    mateCmsIds: number[],
    addMateRate: number,
    mainIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[],
    subIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[]
     ): { manufactureResults: ManufactureResult[], allManufactureProgress: ManufactureProgress, allConsumedMainItems: ValidatedIngredients, allConsumedSubItems: ValidatedIngredients } {
    const manufactureResults: ManufactureResult[] = [];
    const allManufactureProgress: ManufactureProgress = {};
    
    // 먼저 tryCount만큼 빈 슬롯이 있는지 확인
    const checkAvailableSlots = (): number[] => {
      const currentProgress = user.userManufacture.getRoom(roomCms.id);
      const usedSlotNumbers = currentProgress ? 
        Object.keys(currentProgress).map(slot => parseInt(slot, 10)).sort((a, b) => a - b) : [];
      
      const availableSlots: number[] = [];
      let slotToCheck = 0;
      
      // 0부터 시작해서 빈 슬롯들을 찾음
      while (availableSlots.length < tryCount && slotToCheck < MANUFACTURE_CONSTANTS.MAX_SLOT_COUNT) {
        if (!usedSlotNumbers.includes(slotToCheck)) {
          availableSlots.push(slotToCheck);
        }
        slotToCheck++;
      }
      
      if (availableSlots.length < tryCount) {
        throw new MError('not-enough-manufacture-slot', MErrorCode.NOT_ENOUGH_MANUFACTURE_SLOT, {
          userId: user.userId,
          roomId: roomCms.id,
          requiredSlots: tryCount,
          availableSlots: availableSlots.length,
        });
      }
      
      return availableSlots;
    };
    
    const availableSlots = checkAvailableSlots();
    let currentTimeUtc = mutil.curTimeUtc();
     
    // 메인 재료와 서브 재료를 한 번에 처리 (tryCount만큼 누적)
    const allConsumedMainItems = consumeMainIngredients(user, recipeCms, mainIngredientsIds, tryCount);
    const allConsumedSubItems = consumeSubIngredients(user, recipeCms, subIngredientsIds, tryCount);
     
    // tryCount만큼 제작 실행 (확률 계산과 슬롯 할당만)
    for (let i = 0; i < tryCount; i++) {
      const currentSlot = availableSlots[i];
       
      // 먼저 확률 계산 및 결과 결정 (아이템 삭제 전)
      const manufactureResult = this.calculateManufactureResult(recipeCms, addMateRate, mainIngredientsIds[i], subIngredientsIds[i]);
      manufactureResults.push(manufactureResult);

      // 생산 진행 정보 생성 (모든 결과 저장)
      const newManufactureProgress = this.createManufactureProgress(
        user, 
        roomCms, 
        recipeCms, 
        manufactureResult, 
        mateCmsIds, 
        addMateRate,
        currentSlot,
        currentTimeUtc,
                          
        allConsumedMainItems.extra // 실패 시 메인 재료 정보를 extra에 저장
      );
      
      // allManufactureProgress에 병합
      Object.assign(allManufactureProgress, newManufactureProgress);
      
      // 메모리 상태도 즉시 업데이트 (다음 슬롯 찾기를 위해)
      user.userManufacture.applyManufactureRoomInfo(newManufactureProgress, roomCms.id);
      
      // 다음 제작의 시작 시간을 현재 제작의 완료 시간으로 설정
      const slotData = newManufactureProgress[currentSlot];
      if (slotData.completionTimeUtc > 0) {
        currentTimeUtc = slotData.completionTimeUtc;
      }
    }

    return { manufactureResults, allManufactureProgress, allConsumedMainItems, allConsumedSubItems };
  }
}

//-----------------------------------------
// 재료 처리 전략 인터페이스
// 함수가 너무 중구난방이라 따로 processor로 처리
//-----------------------------------------
interface MaterialContext {
  user: User;
  userFleetsClone: UserFleets;
  shipCargoChanges: ShipCargoChange[];
  mateEquipIds: number[];
  shipSlotItemIds: number[];
  requiredItems: { [cmsId: number]: number };
  restoreExtras?: RestoreCmsElemExtra[];
  cargoMap?: Map<string, ShipCargoChange>;
}

interface MaterialProcessor {
  process(
    material: Material,
    ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] },
    context: MaterialContext
  ): void;
}

// 교역품 처리기
class TradeGoodsProcessor implements MaterialProcessor {
  process(material: Material, ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }, context: MaterialContext): void {
    if (ingredientsIds.cartGoods?.length) {
      // cargoMap이 전달되면 사용, 없으면 새로 생성
      const cargoMap = context.cargoMap || new Map<string, ShipCargoChange>();
      
      _buildTradeGoodsIngredient(context.userFleetsClone, cargoMap, ingredientsIds.cartGoods);
      
      // context에 cargoMap 저장 (consumeSubIngredients에서 사용)
      context.cargoMap = cargoMap;
      
      // extra 정보 수집 (실패 시에만)
      if (context.restoreExtras) {
        ingredientsIds.cartGoods.forEach(cartGood => {
          context.restoreExtras.push({
            restoreType: MANUFACTURE_MATERIAL_TYPE.TRADE_GOODS,
            restoreCmsId: cartGood.cmsId,
            count: cartGood.quantity,
            isBound: 0,
            isAccum: 0,
            shipId: cartGood.shipId,
          });
        });
      }
    }
  }
}

// 장비 처리기
class EquipmentProcessor implements MaterialProcessor {
  process(material: Material, ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }, context: MaterialContext): void {
    if (ingredientsIds.mateEquipmentIds?.length) {
      _buildEquipIngredient(ingredientsIds.mateEquipmentIds, context.mateEquipIds);
      
      // extra 정보 수집 (실패 시에만)
      if (context.restoreExtras) {
        ingredientsIds.mateEquipmentIds.forEach(id => {
          const userMateEquipment = context.user.userMates.getMateEquipment(id);
          context.restoreExtras.push({
            restoreType: material.Type,
            restoreCmsId: userMateEquipment.cmsId,
            count: 1,
            isBound: userMateEquipment.isBound,
            isAccum: 0,
            expireTimeUtc: userMateEquipment.expireTimeUtc,
            enchantLv: userMateEquipment.enchantLv,
          });
        });
      }
    }
  }
}

// 선박 부품 처리기
class ShipPartsProcessor implements MaterialProcessor {
  process(material: Material, ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }, context: MaterialContext): void {
    if (ingredientsIds.shipSlotIds?.length) {
      _buildShipPartsIngredient(ingredientsIds.shipSlotIds, context.shipSlotItemIds);
      
      // extra 정보 수집 (실패 시에만)
      if (context.restoreExtras) {
        ingredientsIds.shipSlotIds.forEach(id => {
          const userShipSlotItem = context.user.userInven.getShipSlotItem(id);
          context.restoreExtras.push({
            restoreType: material.Type,
            restoreCmsId: userShipSlotItem.shipSlotCmsId,
            count: 1,
            isBound: userShipSlotItem.isBound,
            isAccum: 0,
            expireTimeUtc: userShipSlotItem.expireTimeUtc,
            enchantLv: userShipSlotItem.enchantLv,
          });
        });
      }
    }
  }
}

// 일반 아이템 처리기
class ItemProcessor implements MaterialProcessor {
  process(material: Material, ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }, context: MaterialContext): void {
    const itemIngredient = _buildItemIngredient(context.user, material);
    
    // 일반 아이템은 수량으로 누적
    _.forOwn(itemIngredient, (amt: number, cmsIdStr: string) => {
      const cmsId: number = parseInt(cmsIdStr, 10);
      context.requiredItems[cmsId] = (context.requiredItems[cmsId] || 0) + amt;
    });
    
    // extra 정보 수집 (실패 시에만)
    if (context.restoreExtras) {
      const userItem = context.user.userInven.itemInven.getItem(material.Target);
      if (userItem && userItem.count >= material.Quantity) {
        context.restoreExtras.push({
          restoreType: material.Type,
          restoreCmsId: material.Target,
          count: material.Quantity,
          isBound: 0,
          isAccum: 0,
        });
      }
    }
  }
}

// 재료 처리기 팩토리
const materialProcessors: { [key: number]: MaterialProcessor } = {
  [MANUFACTURE_MATERIAL_TYPE.TRADE_GOODS]: new TradeGoodsProcessor(),
  [MANUFACTURE_MATERIAL_TYPE.EQUIP_ID]: new EquipmentProcessor(),
  [MANUFACTURE_MATERIAL_TYPE.EQUIP_GRADE]: new EquipmentProcessor(),
  [MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_ID]: new ShipPartsProcessor(),
  [MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_GRADE]: new ShipPartsProcessor(),
  [MANUFACTURE_MATERIAL_TYPE.ITEM]: new ItemProcessor(),
};

// 재료 처리 헬퍼 함수
function processMaterial(
  material: Material,
  ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] },
  context: MaterialContext
): void {
  const processor = materialProcessors[material.Type];
  if (processor) {
    processor.process(material, ingredientsIds, context);
  }
}

// 공통 재료 소비 함수
function consumeIngredients(
  user: User, 
  recipeDesc: ManufactureRecipeDesc, 
  ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[], 
  ingredientType: 'main' | 'sub',
  progressType?: MANUFACTURE_PROGRESS_TYPE,
  tryCount: number = 1
): ValidatedIngredients {
  const mateEquipIds: number[] = [];              
  const shipSlotItemIds: number[] = [];           
  const itemChanges: ItemChange[] = [];
  const shipCargoChanges: ShipCargoChange[] = [];

  // 플릿 클론
  const userFleetsClone = user.userFleets.clone();

  // 일반 아이템 취합
  let requiredItems: { [cmsId: number]: number } = {};

  // 실패 시 extra 정보를 저장할 배열 (메인 재료만)
  let extra: string = null;
  const restoreExtras: RestoreCmsElemExtra[] = [];

  const recipeIngredients = ingredientType === 'main' ? recipeDesc.mainIngredient : recipeDesc.subIngredient;

  // recipeIngredients가 존재하고 비어있지 않을 때만 처리
  if (recipeIngredients?.length) {
    // 교역품 누적 차감을 위한 Map을 tryCount 레벨에서 관리
    const cargoMap = new Map<string, ShipCargoChange>();
    
    const context = {
      user,
      userFleetsClone,
      shipCargoChanges,
      mateEquipIds,
      shipSlotItemIds,
      requiredItems,
      restoreExtras: (ingredientType === 'main' && progressType === MANUFACTURE_PROGRESS_TYPE.FAILURE) ? restoreExtras : undefined,
      cargoMap: cargoMap, // 교역품 누적 차감을 위한 Map
    };

    // tryCount만큼 반복하여 처리
    for (let attempt = 0; attempt < tryCount; attempt++) {
      // ingredientsIds[attempt]에 해당하는 재료만 처리
      const currentIngredients = ingredientsIds[attempt];
      
      // 각 material 타입에 대해 해당하는 재료가 있는지 확인하고 처리
      if (currentIngredients.cartGoods?.length) {
        // 교역품 처리
        const tradeGoodsMaterial = recipeIngredients.find(m => m.Type === MANUFACTURE_MATERIAL_TYPE.TRADE_GOODS);
        if (tradeGoodsMaterial) {
          processMaterial(tradeGoodsMaterial, currentIngredients, context);
        }
      }
      
      if (currentIngredients.mateEquipmentIds?.length) {
        // 장비 처리 - 검증은 이미 validate 함수에서 완료되었으므로 바로 처리
        const equipIdMaterial = recipeIngredients.find(m => m.Type === MANUFACTURE_MATERIAL_TYPE.EQUIP_ID);
        const equipGradeMaterial = recipeIngredients.find(m => m.Type === MANUFACTURE_MATERIAL_TYPE.EQUIP_GRADE);
        
        if (equipIdMaterial) {
          processMaterial(equipIdMaterial, currentIngredients, context);
        }
        
        if (equipGradeMaterial) {
          processMaterial(equipGradeMaterial, currentIngredients, context);
        }
      }
      
      if (currentIngredients.shipSlotIds?.length) {
        // 선박 부품 처리 - SHIP_PARTS_ID와 SHIP_PARTS_GRADE를 분리해서 처리
        const shipPartsIdMaterial = recipeIngredients.find(m => m.Type === MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_ID);
        const shipPartsGradeMaterial = recipeIngredients.find(m => m.Type === MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_GRADE);
        
        if (shipPartsIdMaterial) {
          processMaterial(shipPartsIdMaterial, currentIngredients, context);
        }
        
        if (shipPartsGradeMaterial) {
          processMaterial(shipPartsGradeMaterial, currentIngredients, context);
        }
      }
      
      // 일반 아이템은 항상 처리 (수량 기반)
      const itemMaterial = recipeIngredients.find(m => m.Type === MANUFACTURE_MATERIAL_TYPE.ITEM);
      if (itemMaterial) {
        processMaterial(itemMaterial, currentIngredients, context);
      }
    }

    // cargoMap의 값들을 shipCargoChanges에 추가
    shipCargoChanges.push(...cargoMap.values());

    // 실패 시에만 extra 정보 생성 (메인 재료만)
    if (ingredientType === 'main' && progressType === MANUFACTURE_PROGRESS_TYPE.FAILURE && restoreExtras.length > 0) {
      extra = JSON.stringify(restoreExtras);
    }
  }

  // requiredItems를 itemChanges로 변환
  _.forOwn(requiredItems, (amt: number, cmsIdStr: string) => {
    const cmsId: number = parseInt(cmsIdStr, 10);
    const itemChange = user.userInven.itemInven.buildItemChange(cmsId, -amt, true);
    itemChanges.push(itemChange);
  });

  return { mateEquipIds, shipSlotItemIds, shipCargoChanges, itemChanges, requiredItems, extra };
}

function consumeMainIngredients(user: User, recipeDesc: ManufactureRecipeDesc, ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[], progressType: MANUFACTURE_PROGRESS_TYPE, tryCount: number = 1): ValidatedIngredients {
  return consumeIngredients(user, recipeDesc, ingredientsIds, 'main', progressType, tryCount);
}

function consumeSubIngredients(user: User, recipeDesc: ManufactureRecipeDesc, ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[], tryCount: number = 1): ValidatedIngredients {
  return consumeIngredients(user, recipeDesc, ingredientsIds, 'sub', undefined, tryCount);
}

// 공통 재료 검증 함수
function validateIngredients(
  user: User, 
  recipeDesc: ManufactureRecipeDesc, 
  ingredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[], 
  ingredientType: 'main' | 'sub'
): void {
  const ingredients = ingredientType === 'main' ? ingredientsIds : ingredientsIds;
  const recipeIngredients = ingredientType === 'main' ? recipeDesc.mainIngredient : recipeDesc.subIngredient;

  // ingredientsIds가 비어있거나 null이면 검증할 필요 없음
  if (!ingredients?.length) {
    return;
  }

  // recipeIngredients가 비어있거나 null이면 검증할 필요 없음
  if (!recipeIngredients?.length) {
    return;
  }

  // 각 재료 세트별로 검증만 수행
  ingredients.forEach((ingredientSet) => {
    recipeIngredients.forEach((material) => {
      if (material.Type === MANUFACTURE_MATERIAL_TYPE.TRADE_GOODS) {
        if (!ingredientSet.cartGoods || ingredientSet.cartGoods.length === 0) {
          throw new MError('missing-trade-goods', MErrorCode.MISSING_MANUFACTURE_TRADE_GOODS, {
            userId: user.userId,
            recipeCmsId: recipeDesc.id,
          });
        }
        // 교역품 검증
        _validateTradeGoodsIngredient(user, ingredientSet.cartGoods);
      } else if (material.Type === MANUFACTURE_MATERIAL_TYPE.EQUIP_ID) {
        // 장비 검증
        _validateEquipIngredient(user, recipeDesc.id, material, ingredientSet.mateEquipmentIds || []);
      } else if (material.Type === MANUFACTURE_MATERIAL_TYPE.EQUIP_GRADE) {
        // EQUIP_GRADE 검증 - 모든 장비가 요구 등급에 맞아야 함
        _validateEquipGradeIngredient(user, recipeDesc.id, material, ingredientSet.mateEquipmentIds || []);
      } else if (material.Type === MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_ID) {
        // 선박 부품 검증
        _validateShipPartsIngredient(user, recipeDesc.id, material, ingredientSet.shipSlotIds || []);
      } else if (material.Type === MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_GRADE) {
        // SHIP_PARTS_GRADE 검증 - 모든 선박 부품이 요구 등급에 맞아야 함
        _validateShipPartsGradeIngredient(user, recipeDesc.id, material, ingredientSet.shipSlotIds || []);
      } else if (material.Type === MANUFACTURE_MATERIAL_TYPE.ITEM) {
        // 일반 아이템 검증
        _validateItemIngredient(user, material);
      } else {
        throw new MError('invalid-material-type', MErrorCode.INVALID_MANUFACTURE_MATERIAL_TYPE, {
          userId: user.userId,
          recipeCmsId: recipeDesc.id,
          materialType: material.Type,
        });
      }
    });
  });
}

function validateMainIngredients(user: User, recipeDesc: ManufactureRecipeDesc, mainIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[]): void {
  validateIngredients(user, recipeDesc, mainIngredientsIds, 'main');
}

function validateSubIngredients(user: User, recipeDesc: ManufactureRecipeDesc, subIngredientsIds: { mateEquipmentIds?: number[], shipSlotIds?: number[], cartGoods?: CartGoods[] }[]): void {
  validateIngredients(user, recipeDesc, subIngredientsIds, 'sub');
}

function calcAddMateSuccessRate(user: User, recipeDesc: ManufactureRecipeDesc, mateIds: number[]): number {
  const specialStat = recipeDesc.workingMateSpecialStat;
  const statConvertValue = recipeDesc.statConvertValue; // 차후 데이터에서 받아올 예정, 일단 1로 고정
  let totalSsExp = 0; // 모든 항해사의 ssExp 합계를 저장할 변수
  
  mateIds.forEach((mateCmsId) => {
    const userMate = user.userMates.getMate(mateCmsId);
    if (!userMate) {
      throw new MError('manufacture-mate-not-found', MErrorCode.MANUFACTURE_MATE_NOT_FOUND, {
        userId: user.userId,
        recipeCmsId: recipeDesc.id,
        mateCmsId,
      });
    }

    const mateCms = cms.Mate[mateCmsId];
    const characterCms = cms.Character[mateCms.characterId];
    
    recipeDesc.workingMate.forEach((workMate) => {
      if (workMate.JobId === characterCms.jobId) {
        // workMate.AwakenLv < userMate.getAwakenLv() 조건일 때만 ssExp 저장
        if (workMate.AwakenLv <= userMate.getAwakenLv()) {
          const mateStat = user.companyStat.getMateStatByCmsId(mateCmsId);
          const ssExp = mateStat.getSpecialStatExp().getExp(specialStat.Type);
          totalSsExp += ssExp;
        }
      }
    });
  });
  
  // mateIds.forEach 밖에서 초과되는 수에 대해 계산
  let addMateSuccessRate: number = 0;
  
  // totalSsExp가 specialStat.Val보다 작으면 에러 발생
  if (totalSsExp < specialStat.Val) {
    return addMateSuccessRate;
    // 일단 테스트를 위해 임시 아래 주석.
    // throw new MError('insufficient-special-stat-exp', MErrorCode.INSUFFICIENT_MANUFACTURE_LEVEL, {
    //   userId: user.userId,
    //   recipeCmsId: recipeDesc.id,
    //   requiredSpecialStatExp: specialStat.Val,
    //   currentSpecialStatExp: totalSsExp,
    //   specialStatType: specialStat.Type,
    // });
  }
  
  if (totalSsExp > specialStat.Val) {
    const overValue = totalSsExp - specialStat.Val;
    const quotient = Math.floor(overValue / statConvertValue);
    const additionalRate = quotient * recipeDesc.mateAdditionalRate;
    addMateSuccessRate = additionalRate;
  }

  return addMateSuccessRate;
}

//=============================================
// ensure 함수
function ensureMaterialMateEquipment(
  user: User,
  equipmentId: number,
  recipeCmsId: number
) {
  const mateEquip: MateEquipmentNub = user.userMates.getMateEquipment(equipmentId);
  if (!mateEquip) {
    throw new MError(
      'player-has-no-a-material-item(mate-equip)',
      MErrorCode.HAVE_NO_MATERIAL_ITEM,
      {
        userId: user.userId,
        recipeCmsId,
        equipmentId,
      }
    );
  }
  // 장착한 항해사가 있으면 안된다.
  if (mateEquip.equippedMateCmsId) {
    throw new MError('equipped-item', MErrorCode.EQUIPPED_ITEM, {
      userId: user.userId,
      recipeCmsId,
      equipmentId,
      equipCmsId: mateEquip.cmsId,
      mateCmsId: mateEquip.equippedMateCmsId,
    });
  }

  // 실제 CMS 없는경우는 없지만 검사
  const cEquipDesc = cms.CEquip[mateEquip.cmsId];
  if (!cEquipDesc) {
    throw new MError('cannot-find-CEquip-cms', MErrorCode.CANNOT_FIND_CEQUIP_CMS, {
      userId: user.userId,
      recipeCmsId,
      equipmentId,
      equipCmsId: mateEquip.cmsId,
    });
  }

  // 코스튬으로 변경한것은 재료로 사용 불가능
  if (mateEquip.isCostume === 1) {
    throw new MError('can-not-use-costume-equip-item', MErrorCode.CAN_NOT_USE_COSTUME_EQUIP_ITEM, {
      userId: user.userId,
      recipeCmsId,
      equipmentId,
      equipCmsId: mateEquip.cmsId,
    });
  }

  // 정해진 타입만 등록 가능.
  if (
    cEquipDesc.type != CEQUIP_TYPE.WEAPON &&
    cEquipDesc.type != CEQUIP_TYPE.BODY &&
    cEquipDesc.type != CEQUIP_TYPE.HAT &&
    cEquipDesc.type != CEQUIP_TYPE.CAPE &&
    cEquipDesc.type != CEQUIP_TYPE.ACCESSARY &&
    cEquipDesc.type != CEQUIP_TYPE.FACE
    //&& cEquipDesc.type != CEQUIP_TYPE.MATERIAL
  ) {
      throw new MError(
        'invalid-manufacture-material-type(equipment)',
        MErrorCode.INVALID_MANUFACTURE_MATERIAL_TYPE,
        {
          userId: user.userId,
          recipeCmsId,
          equipmentId,
          equipCmsId: mateEquip.id,
          slotType: cEquipDesc.type,
        }
      );
  }

  // 전용 장비는 합성 X
  if (cEquipDesc.exclusiveMateId) {
    throw new MError(
      'can-not-use-exclusive-equip-item',
      MErrorCode.CAN_NOT_USE_EXCLUSIVE_EQUIP_ITEM,
      {
        userId: user.userId,
        recipeCmsId,
        equipmentId,
        equipCmsId: mateEquip.cmsId,
      }
    );
  }
}

function ensureMaterialShipSlotItem(
  user: User,
  shipSlotItemId: number,
  recipeCmsId: number
) {
  const shipSlotItem = user.userInven.getShipSlotItem(shipSlotItemId);
  if (!shipSlotItem) {
    throw new MError('player-has-no-a-material-item', MErrorCode.HAVE_NO_MATERIAL_ITEM, {
      userId: user.userId,
      recipeCmsId,
      materialId: shipSlotItemId,
    });
  }

  if (shipSlotItem.isLocked === 1) {
    throw new MError('ship-slot-item-is-locked', MErrorCode.SHIP_SLOT_ITEM_IS_LOCKED, {
      recipeCmsId,
    });
  }

  const shipSlotDesc: ShipSlotDesc = cms.ShipSlot[shipSlotItem.shipSlotCmsId];
  if (!shipSlotDesc) {
    throw new MError('cannot-find-ShipSlot-cms', MErrorCode.CANNOT_FIND_SHIPSLOT_CMS, {
      userId: user.userId,
      recipeCmsId,
      shipSlotId: shipSlotItemId,
      equipCmsId: shipSlotItem.shipSlotCmsId,
    });
  }

  // 정해진 타입만 등록 가능.
  if (
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.FIGUREHEAD &&
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.SAIL &&
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.RAM &&
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.SPECIAL &&
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.CANNON &&
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.ARMOR &&
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.ANCHOR &&
    //shipSlotDesc.slotType != SHIP_SLOT_TYPE.MATERIAL &&
    shipSlotDesc.slotType != SHIP_SLOT_TYPE.FLAG
  ) {
      throw new MError(
      'invalid-manufacture-material-type(ship-slot)',
      MErrorCode.INVALID_MANUFACTURE_MATERIAL_TYPE,
      {
        userId: user.userId,
        recipeCmsId,
        shipSlotId: shipSlotItemId,
        shipSlotCmsId: shipSlotDesc.id,
        slotType: shipSlotDesc.slotType,
      }
    );
  }
}

//=============================================
    // _build 함수
function _buildEquipIngredient(materialIds: number[], mateEquipIds: number[]) {
  materialIds.forEach((id: number) => {
    // 검증은 이미 완료되었으므로 바로 추가
    mateEquipIds.push(id);
  });
}

function _buildShipPartsIngredient(materialIds: number[], shipSlotItemIds: number[]) {
  materialIds.forEach((id: number) => {
    // 검증은 이미 완료되었으므로 바로 추가
    shipSlotItemIds.push(id);
  });
}

// 교역품 검증
function _validateTradeGoodsIngredient(user: User, materialGoods: CartGoods[]): void {
  _.forEach(materialGoods, (priceCartGoods) => {
    const priceGoodsCms = cms.TradeGoods[priceCartGoods.cmsId];
    if (!priceGoodsCms || (priceGoodsCms.localBitFlag & (1 << mconf.countryCode)) === 0) {
      throw new MError(
        'invalid-exchange-goods-cms-id',
        MErrorCode.INVALID_REQ_BODY_VILLAGE_EXCHANGE_PRICE_CART,
        { priceCartGoods }
      );
    }

    const userShip = user.userFleets.getShip(priceCartGoods.shipId);
    const preQuantity = userShip.getCargoQuantity(priceCartGoods.cmsId);
    const quantity = preQuantity - priceCartGoods.quantity;
    if (quantity < 0) {
      throw new MError('not-enough-price-goods', MErrorCode.NOT_ENOUGH_PRICE_GOODS, {
        id: priceCartGoods.cmsId,
        cartQuantity: priceCartGoods.quantity,
        quantity,
      });
    }
  });
}

// EQUIP_GRADE 검증 - 모든 장비가 요구 등급에 맞아야 함
function _validateEquipGradeIngredient(user: User, recipeCmsId: number, material: Material, materialIds: number[]): void {
  if (materialIds.length === 0) {
    throw new MError('missing-equipment', MErrorCode.HAVE_NO_MATERIAL_ITEM, {
      userId: user.userId,
      recipeCmsId,
      requiredGrade: material.Target,
    });
  }

  const requiredGrade = material.Target;
  const invalidEquipmentIds = materialIds.filter(equipId => {
    const userMateEquipment = user.userMates.getMateEquipment(equipId);
    if (!userMateEquipment) {
      return true; // 장비가 없으면 invalid
    }
    const cEquipDesc = cms.CEquip[userMateEquipment.cmsId];
    if (!cEquipDesc) {
      return true; // CMS가 없으면 invalid
    }
    return cEquipDesc.grade !== requiredGrade;
  });

  if (invalidEquipmentIds.length > 0) {
    throw new MError('equipment-grade-mismatch', MErrorCode.INVALID_MATERIAL_GRADE, {
      userId: user.userId,
      recipeCmsId,
      requiredGrade,
      invalidEquipmentIds,
      providedEquipmentIds: materialIds,
    });
  }

  // 모든 장비가 조건에 맞으면 개별 검증도 수행
  materialIds.forEach((id: number) => {
    ensureMaterialMateEquipment(user, id, recipeCmsId);
  });
}

// 장비 검증
function _validateEquipIngredient(user: User, recipeCmsId: number, material: Material, materialIds: number[]): void {
  materialIds.forEach((id: number) => {
    ensureMaterialMateEquipment(user, id, recipeCmsId);
    const mateEquip: MateEquipmentNub = user.userMates.getMateEquipment(id);
    const cEquipDesc = cms.CEquip[mateEquip.cmsId];
    
    if (material.Type === MANUFACTURE_MATERIAL_TYPE.EQUIP_ID) {
      // 도구점에서 판매 못하는 상품은 재료로 사용 불가능.
      if (cEquipDesc.isNotSell) {
        throw new MError('isNotSell-equip-item', MErrorCode.ISNOTSELL_EQUIP_ITEM_MATERIAL, {
          userId: user.userId,
          recipeCmsId,
          materialId: id,
          equipCmsId: mateEquip.cmsId,
        });
      }
    }

    if (material.Type === MANUFACTURE_MATERIAL_TYPE.EQUIP_GRADE) {
      if (material.Target !== cEquipDesc.grade) {
        throw new MError('invalid-material-grade', MErrorCode.INVALID_MATERIAL_GRADE, {
          userId: user.userId,
          recipeCmsId,
          equipCmsId: cEquipDesc.id,
          needGrade: material.Target,
          slotGrade: cEquipDesc.grade,
        });
      }
    }
  });
}

// SHIP_PARTS_GRADE 검증 - 모든 선박 부품이 요구 등급에 맞아야 함
function _validateShipPartsGradeIngredient(user: User, recipeCmsId: number, material: Material, materialIds: number[]): void {
  if (materialIds.length === 0) {
    throw new MError('missing-ship-parts', MErrorCode.HAVE_NO_MATERIAL_ITEM, {
      userId: user.userId,
      recipeCmsId,
      requiredGrade: material.Target,
    });
  }

  const requiredGrade = material.Target;
  const invalidShipPartsIds = materialIds.filter(shipSlotId => {
    const userShipSlotItem = user.userInven.getShipSlotItem(shipSlotId);
    if (!userShipSlotItem) {
      return true; // 선박 부품이 없으면 invalid
    }
    const shipSlotDesc = cms.ShipSlot[userShipSlotItem.shipSlotCmsId];
    if (!shipSlotDesc) {
      return true; // CMS가 없으면 invalid
    }
    return shipSlotDesc.shipSlotGrade !== requiredGrade;
  });

  if (invalidShipPartsIds.length > 0) {
    throw new MError('ship-parts-grade-mismatch', MErrorCode.INVALID_MATERIAL_GRADE, {
      userId: user.userId,
      recipeCmsId,
      requiredGrade,
      invalidShipPartsIds,
      providedShipPartsIds: materialIds,
    });
  }

  // 모든 선박 부품이 조건에 맞으면 개별 검증도 수행
  materialIds.forEach((id: number) => {
    ensureMaterialShipSlotItem(user, id, recipeCmsId);
  });
}

// 선박 부품 검증
function _validateShipPartsIngredient(user: User, recipeCmsId: number, material: Material, materialIds: number[]): void {
  materialIds.forEach((id: number) => {
    ensureMaterialShipSlotItem(user, id, recipeCmsId);
    const shipSlotItem = user.userInven.getShipSlotItem(id);
    const shipSlotDesc: ShipSlotDesc = cms.ShipSlot[shipSlotItem.shipSlotCmsId];

    if (material.Type === MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_ID) {
      // 도구점에서 판매 못하는 상품은 재료로 사용 불가능.
      if (shipSlotDesc.isNotSell) {
        throw new MError('isNotSell-equip-item', MErrorCode.ISNOTSELL_EQUIP_ITEM_MATERIAL, {
          userId: user.userId,
          recipeCmsId,
          shipSlotId: id,
          shipSlotCmsId: shipSlotDesc.id,
        });
      }
    }

    if (material.Type === MANUFACTURE_MATERIAL_TYPE.SHIP_PARTS_GRADE) {
      if (material.Target !== shipSlotDesc.shipSlotGrade) {
        throw new MError('invalid-material-grade', MErrorCode.INVALID_MATERIAL_GRADE, {
          userId: user.userId,
          recipeCmsId,
          shipSlotId: id,
          shipSlotCmsId: shipSlotDesc.id,
          needGrade: material.Target,
          slotGrade: shipSlotDesc.shipSlotGrade,
        });
      }
    }
  });
}

// 일반 아이템 보유 중인지만 체크
function _validateItemIngredient(user: User, material: Material): void {
  if (material.Type === MANUFACTURE_MATERIAL_TYPE.ITEM) {
    const userItem = user.userInven.itemInven.getItem(material.Target);
    if (!userItem) {
      throw new MError('not-enough-item', MErrorCode.HAVE_NO_MATERIAL_ITEM, {
        userId: user.userId,
        itemCmsId: material.Target,
        requiredQuantity: material.Quantity,
        currentQuantity: userItem?.count || 0,
      });
    }
  }
}

// 선박에 실린 교역품 차감 계산
function _buildTradeGoodsIngredient(cloneFleets: UserFleets, cargoMap: Map<string, ShipCargoChange>, materialGoods: CartGoods[]) {
  _.forEach(materialGoods, (priceCartGoods) => {
    const key = `${priceCartGoods.shipId}-${priceCartGoods.cmsId}`;
    
    // 기존 change가 있는지 확인
    let existingChange = cargoMap.get(key);
    if (existingChange) {
      // 기존 change의 quantity를 감소 (누적 차감)
      existingChange.quantity -= priceCartGoods.quantity;
    } else {
      // 새로운 change 생성
      const userShip = cloneFleets.getShip(priceCartGoods.shipId);
      const preQuantity = userShip.getCargoQuantity(priceCartGoods.cmsId);
      const quantity = preQuantity - priceCartGoods.quantity;
      
      existingChange = {
        shipId: priceCartGoods.shipId,
        cmsId: priceCartGoods.cmsId,
        quantity,
        pointInvested: 0,
      };
      cargoMap.set(key, existingChange);
    }
  });
}

// 음 다른 곳에서는 클라에게서 아이템을 받지 않네? 서버가 알아서 체크...
function _buildItemIngredient(user: User, material: Material) {
  // ===============================================================================================
  // 인벤아이템을 소모해야하는 경우 필요 수량을 알아낸다.
  // ===============================================================================================
  let requiredItems: { [cmsId: number]: number } = {};
  if (material.Type === MANUFACTURE_MATERIAL_TYPE.ITEM) {
    requiredItems[material.Target] = !requiredItems[material.Target]
      ? material.Quantity // * amount
      : requiredItems[material.Target] + material.Quantity; // * amount;
  }

  return requiredItems;
}
