// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import * as mutil from '../../../motiflib/mutil';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { Resp, Sync } from '../../type/sync';
import { ClientPacketHandler } from '../index';
import { SHIELD_CMS_ID } from '../../../cms/shieldDesc';
import { Shield } from '../../userShield';
import { LobbyService } from '../../server';
import tuUpdateShields from '../../../mysqllib/txn/tuUpdateShields';
import { Survey } from '../../survey/survey';
import mlog from '../../../motiflib/mlog';

const rsn = 'complete_survey_cn';
const add_rsn = null;

interface RequestBody {
    surveyId: string;
}

// 메일을 보내야함.
interface ResponseBody extends Resp {

}

export class Cph_Common_CompleteSurvey implements ClientPacketHandler {
  constructor() {}

  testGameState(user: User): boolean {
    return true;
  }

  async exec(user: User, packet: CPacket): Promise<any> {
    mlog.info('[CompleteSurvey] rx', {
      body: packet.bodyObj,
    });

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const body: RequestBody = packet.bodyObj;
    const surveyId = body.surveyId;
    await Survey.completeSurvey(surveyId, user);
  }
}
