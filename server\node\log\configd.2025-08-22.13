{"level":"info","message":"[!] server is stopping: type=configd, signal=SIGINT","timestamp":"2025-08-22T04:56:48.608Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T04:56:48.615Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T04:56:48.616Z"}
{"level":"info","message":"redis pool (config-redis) destroyed","timestamp":"2025-08-22T04:56:48.619Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T04:56:48.619Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T04:56:48.621Z"}
{"name":"config-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T04:56:48.621Z"}
{"environment":"development","type":"configd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"configd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T04:56:52.375Z"}
{"layoutFileName":"local.cn.json5","level":"info","message":"Reloading layout...","timestamp":"2025-08-22T04:57:00.507Z"}
{"dump":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Layout reloaded successfully from json file.","timestamp":"2025-08-22T04:57:00.520Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"},"level":"info","message":"redis pool (config-redis) initializing ...","timestamp":"2025-08-22T04:57:00.521Z"}
{"level":"info","message":"redis pool (config-redis) initialized","timestamp":"2025-08-22T04:57:00.537Z"}
{"layoutVersion":7,"level":"info","message":"loadInstances getAllInstances success","timestamp":"2025-08-22T04:57:00.543Z"}
{"appId":"townd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10500,\"url\":\"http://DESKTOP-2FFOGVN:10500\",\"tcpServer\":{\"port\":10508,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T04:57:00.543Z"}
{"appId":"saild.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":11100,\"url\":\"http://DESKTOP-2FFOGVN:11100\",\"tcpServer\":{\"port\":11109,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T04:57:00.544Z"}
{"appId":"realmd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10900,\"url\":\"http://DESKTOP-2FFOGVN:10900\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T04:57:00.544Z"}
{"appId":"zonelbd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10600,\"url\":\"http://DESKTOP-2FFOGVN:10600\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T04:57:00.544Z"}
{"appId":"lobbyd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"socketServer\":{\"bindAddress\":\"0.0.0.0\",\"port\":10100},\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10200,\"url\":\"http://DESKTOP-2FFOGVN:10200\"}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T04:57:00.544Z"}
{"appId":"oceand.0@DESKTOP-2FFOGVN","instance":{"body":"{\"worldId\":\"UWO-GL-01\",\"apiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10800,\"url\":\"http://DESKTOP-2FFOGVN:10800\",\"tcpServer\":{\"port\":10808,\"ip\":\"DESKTOP-2FFOGVN\"}}}","worldId":"UWO-GL-01"},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T04:57:00.545Z"}
{"appId":"authd.0@DESKTOP-2FFOGVN","instance":{"body":"{\"publicApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10701},\"privateApiService\":{\"bindAddress\":\"0.0.0.0\",\"port\":10700,\"url\":\"http://DESKTOP-2FFOGVN:10700\"}}","worldId":""},"level":"info","message":"loadInstances added","timestamp":"2025-08-22T04:57:00.545Z"}
{"value":5000,"level":"info","message":"got maxUsersPerWorld from redis","timestamp":"2025-08-22T04:57:00.545Z"}
{"path":"/getAllInstances","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.552Z"}
{"path":"/fetch","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.588Z"}
{"path":"/getMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.597Z"}
{"path":"/registerInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.607Z"}
{"path":"/reload","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.619Z"}
{"path":"/setMaxUsersPerWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.629Z"}
{"path":"/sync","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.641Z"}
{"path":"/unregisterInstance","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:00.651Z"}
{"bindAddress":"0.0.0.0","port":10001,"level":"info","message":"start listening ...","timestamp":"2025-08-22T04:57:00.664Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"lobbyd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T04:57:15.278Z"}
{"url":"/registerInstance","status":"200","response-time":"116.443","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:15.280Z"}
{"body":{"serverType":"authd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T04:57:15.309Z"}
{"url":"/registerInstance","status":"200","response-time":"0.942","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:15.310Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"oceand","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T04:57:21.475Z"}
{"url":"/registerInstance","status":"200","response-time":"0.817","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:21.476Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"saild","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T04:57:22.827Z"}
{"url":"/registerInstance","status":"200","response-time":"0.650","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:22.827Z"}
{"url":"/sync","status":"200","response-time":"0.430","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:23.054Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"townd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T04:57:23.164Z"}
{"url":"/registerInstance","status":"200","response-time":"0.627","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:23.164Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"realmd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T04:57:25.161Z"}
{"url":"/registerInstance","status":"200","response-time":"0.769","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:25.162Z"}
{"url":"/sync","status":"200","response-time":"0.893","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:26.230Z"}
{"body":{"worldId":"UWO-GL-01","serverType":"zonelbd","num":0,"hostname":"DESKTOP-2FFOGVN"},"level":"info","message":"start registerInstance","timestamp":"2025-08-22T04:57:27.218Z"}
{"url":"/registerInstance","status":"200","response-time":"0.744","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:27.219Z"}
{"url":"/sync","status":"200","response-time":"0.632","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:27.487Z"}
{"url":"/sync","status":"200","response-time":"0.554","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:27.637Z"}
{"url":"/sync","status":"200","response-time":"0.303","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:27.967Z"}
{"url":"/sync","status":"200","response-time":"0.338","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:57:29.941Z"}
{"url":"/sync","status":"200","response-time":"0.339","mcode":0,"level":"info","message":"configd-req","timestamp":"2025-08-22T04:58:01.424Z"}
