import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import { RemapRecord } from '../analyzer/remapDataSummaryAnalyzer';
import { WorldShardMapping, AnalysisResult, DatabaseTableInfo } from '../types';
import { DatabaseConfigReader, WorldDatabaseConfig } from '../utils/databaseConfigReader';
export class DatabaseScriptGenerator {
  
  async generateScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('🗄️ SQL 스크립트 생성 중...');

    // 월드별로 레코드 그룹화
    const worldGroups = this.groupRecordsByWorld(records);

    for (const [worldId, worldRecords] of worldGroups.entries()) {
      if (worldRecords.length === 0) continue;

      // 월드별 폴더 생성 (소문자로 통일)
      const worldDir = path.join(outputDir, worldId.toLowerCase());
      await fs.ensureDir(worldDir);

      // 사용자 데이터베이스별로 그룹화
      const shardGroups = this.groupRecordsByUserShard(worldRecords, worldMapping);

      for (const [shardIndex, shardRecords] of shardGroups.entries()) {
        if (shardRecords.length === 0) continue;

        const shardName = `user_shard_${shardIndex}`;
        await this.generateUserShardScript(shardRecords, shardName, worldDir, codebaseAnalysisResult);
      }

      // 월드 데이터베이스 스크립트 생성
      await this.generateWorldDatabaseScript(worldRecords, worldMapping, worldDir, codebaseAnalysisResult);
    }

    console.log(`✅ SQL 스크립트 생성 완료 (${records.length}개 레코드)`);

    // Shell script 생성
    try {
      await this.generateShellScripts(records, worldMapping, outputDir, codebaseAnalysisResult);
    } catch (error) {
      console.error('❌ Shell 스크립트 생성 중 오류 발생:', error);
    }
  }

  async generateGlobalScripts(
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('🌐 전역 SQL 스크립트 생성 중 (auth만)...');

    // SQL 파일들을 sqls 폴더에 생성
    const sqlsDir = path.join(outputDir, 'sqls');
    await fs.ensureDir(sqlsDir);

    // auth 데이터베이스 스크립트 생성
    await this.generateAuthDatabaseScript(records, sqlsDir, codebaseAnalysisResult);

    console.log(`✅ 전역 SQL 스크립트 생성 완료 (${records.length}개 레코드)`);
  }

  async generateUserShardScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('👥 사용자 샤드 SQL 스크립트 생성 중...');

    // 월드별로 레코드 그룹화
    const worldGroups = this.groupRecordsByWorld(records);

    for (const [worldId, worldRecords] of worldGroups.entries()) {
      if (worldRecords.length === 0) continue;

      // 월드별 SQL 폴더 생성: sqls/{worldId}/ (소문자로 통일)
      const worldSqlsDir = path.join(outputDir, 'sqls', worldId.toLowerCase());
      await fs.ensureDir(worldSqlsDir);

      // 사용자 데이터베이스별로 그룹화
      const shardGroups = this.groupRecordsByUserShard(worldRecords, worldMapping);

      for (const [shardIndex, shardRecords] of shardGroups.entries()) {
        if (shardRecords.length === 0) continue;

        const shardName = `user_shard_${shardIndex}`;
        await this.generateUserShardScript(shardRecords, shardName, worldSqlsDir, codebaseAnalysisResult);
      }

      // 월드 데이터베이스 스크립트도 월드별 폴더에 생성
      await this.generateWorldDatabaseScript(worldRecords, worldMapping, worldSqlsDir, codebaseAnalysisResult);
    }

    console.log(`✅ 사용자 샤드 SQL 스크립트 생성 완료 (${records.length}개 레코드)`);
  }

  private groupRecordsByWorld(records: RemapRecord[]): Map<string, RemapRecord[]> {
    const worldGroups = new Map<string, RemapRecord[]>();

    records.forEach(record => {
      const worldId = record.gameServerId;
      if (!worldGroups.has(worldId)) {
        worldGroups.set(worldId, []);
      }
      worldGroups.get(worldId)!.push(record);
    });

    return worldGroups;
  }

  private groupRecordsByUserShard(
    records: RemapRecord[],
    worldMapping: WorldShardMapping
  ): Map<number, RemapRecord[]> {
    const shardGroups = new Map<number, RemapRecord[]>();

    // 설정에서 샤드 수를 가져오거나 기본값 사용
    // 현재는 모든 샤드에 모든 레코드를 포함 (샤딩하지 않음)
    const userShardCount = 4; // 기본값, 나중에 설정에서 가져오도록 개선 가능

    // 샤드 수만큼 초기화
    for (let i = 0; i < userShardCount; i++) {
      shardGroups.set(i, []);
    }

    // 모든 샤드에 모든 레코드를 포함 (GNID 기반 샤딩 제거)
    // 각 샤드는 독립적으로 모든 업데이트를 수행해야 함
    records.forEach(record => {
      for (let shardIndex = 0; shardIndex < userShardCount; shardIndex++) {
        const shardRecords = shardGroups.get(shardIndex) || [];
        shardRecords.push(record);
        shardGroups.set(shardIndex, shardRecords);
      }
    });

    return shardGroups;
  }

  private async generateUserShardScript(
    records: RemapRecord[],
    shardName: string,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    const fileName = `${shardName}_update.sql`;
    const filePath = path.join(outputDir, fileName);

    let sqlContent = this.generateSqlHeader(shardName, records.length);

    // 업데이트된 테이블 목록 수집
    const updatedTables = this.getUpdatedTableNames(codebaseAnalysisResult, 'user');

    // 배치 단위로 처리
    const batchSize = 1000;
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      sqlContent += this.generateUserUpdateBatch(batch, i / batchSize + 1, codebaseAnalysisResult);
    }

    // 샤드 번호 추출 (예: "user_shard_3" -> 3)
    const shardNumber = parseInt(shardName.split('_').pop() || '0');

    sqlContent += this.generateSqlFooter(updatedTables, {
      scriptType: '사용자 샤드',
      shardNumber: shardNumber,
      recordCount: records.length,
      tableCount: updatedTables.length
    });

    await fs.writeFile(filePath, sqlContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  async generateWorldDatabaseScript(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    const fileName = `world_update.sql`;
    const filePath = path.join(outputDir, fileName);

    let sqlContent = this.generateSqlHeader(`world_${worldMapping.worldId}`, records.length);

    // 업데이트된 테이블 목록 수집
    const updatedTables = this.getUpdatedTableNames(codebaseAnalysisResult, 'world');

    // 월드 데이터베이스 테이블 업데이트
    sqlContent += this.generateWorldTableUpdates(records, codebaseAnalysisResult);

    sqlContent += this.generateSqlFooter(updatedTables, {
      scriptType: '월드 데이터',
      worldId: worldMapping.worldId,
      recordCount: records.length,
      tableCount: updatedTables.length
    });

    await fs.writeFile(filePath, sqlContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드)`);
  }

  private generateSqlHeader(databaseName: string, recordCount: number): string {
    const timestamp = new Date().toISOString();
    return `-- =====================================================
-- UWO GNID/NID 리맵핑 SQL 스크립트
-- 데이터베이스: ${databaseName}
-- 생성일시: ${timestamp}
-- 레코드 수: ${recordCount}
-- =====================================================

`;
  }

  private generateSqlFooter(
    updatedTables?: string[],
    scriptInfo?: {
      scriptType: string;
      worldId?: string;
      shardNumber?: number;
      recordCount: number;
      tableCount: number;
    }
  ): string {
    return '';
  }

  private generateUserUpdateBatch(records: RemapRecord[], batchNumber: number, codebaseAnalysisResult: AnalysisResult): string {
    let sql = `\n-- ===== 배치 ${batchNumber} =====\n`;

    // 사용자 테이블 업데이트
    sql += this.generateUserTableUpdate(records, codebaseAnalysisResult);

    // 캐릭터 관련 테이블 업데이트
    sql += this.generateCharacterTableUpdates(records, codebaseAnalysisResult);

    return sql;
  }

  private generateUserTableUpdate(records: RemapRecord[], codebaseAnalysisResult: AnalysisResult): string {
    // 분석된 테이블에서 user 데이터베이스의 테이블들 찾기
    let userTables = codebaseAnalysisResult.databaseTables.filter(table =>
      (table.database === 'user' || table.database.startsWith('user_shard_')) &&
      table.columns.some(col => col.relatedTo === 'accountId' || col.relatedTo === 'pubId')
    );

    if (userTables.length === 0) {
      // 임시로 주석 처리 - Redis 테스트용
      console.log('⚠️ 사용자 테이블이 분석되지 않았습니다. 빈 SQL을 반환합니다.');
      return '\n-- 경고: 사용자 테이블이 분석되지 않았습니다.\n';
    }

    // 실제 분석된 테이블별로 업데이트 생성
    return this.generateUserTableUpdates(records, codebaseAnalysisResult);
  }

  private generateUserTableUpdates(records: RemapRecord[], codebaseAnalysisResult: AnalysisResult): string {
    let sql = '\n-- 사용자 테이블 업데이트\n';

    // 분석된 테이블에서 user 관련 테이블들 찾기
    let userTables = codebaseAnalysisResult.databaseTables.filter(table =>
      (table.database === 'user' || table.database.startsWith('user_shard_')) &&
      table.columns.some(col => col.relatedTo === 'accountId' || col.relatedTo === 'pubId')
    );

    if (userTables.length === 0) {
      sql += '-- 경고: 분석된 사용자 테이블이 없습니다.\n';
      return sql;
    }

    // GNID별로 그룹화하여 중복 업데이트 방지
    const gnidMap = new Map<string, string>();
    const nidMap = new Map<string, string>();
    records.forEach(record => {
      gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
      nidMap.set(record.uwo_Nid, record.uwogl_Nid);
    });

    // 실제 분석된 테이블별로 업데이트 생성
    userTables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

      // w_auto_sailings 테이블은 accountId와 pubId가 복합키가 아니므로 개별 업데이트
      if (table.tableName === 'w_auto_sailings') {
        sql += `-- 개별 업데이트: accountId와 pubId는 복합키가 아님\n`;

        if (accountIdCol && gnidMap.size > 0) {
          sql += this.generateCaseWhenUpdate(
            table.tableName,
            accountIdCol.columnName,
            gnidMap
          );
        }

        if (pubIdCol && nidMap.size > 0) {
          sql += this.generateCaseWhenUpdate(
            table.tableName,
            pubIdCol.columnName,
            nidMap
          );
        }
      } else {
        // 다른 테이블들은 accountId와 pubId를 모두 가진 경우 복합 업데이트 수행
        if (accountIdCol && pubIdCol) {
          sql += `-- 복합 업데이트: (${accountIdCol.columnName}, ${pubIdCol.columnName})\n`;
          sql += this.generateComplexCaseWhenUpdate(
            table.tableName,
            accountIdCol.columnName,
            pubIdCol.columnName,
            records
          );
        } else {
          // 개별 컬럼 업데이트
          if (accountIdCol && gnidMap.size > 0) {
            sql += this.generateCaseWhenUpdate(
              table.tableName,
              accountIdCol.columnName,
              gnidMap
            );
          }

          if (pubIdCol && nidMap.size > 0) {
            sql += this.generateCaseWhenUpdate(
              table.tableName,
              pubIdCol.columnName,
              nidMap
            );
          }
        }
      }

      sql += '\n';
    });

    return sql;
  }

  private generateCaseWhenUpdate(
    tableName: string,
    columnName: string,
    idMap: Map<string, string>
  ): string {
    let sql = `UPDATE ${tableName}\n`;
    sql += `SET ${columnName} = CASE ${columnName}\n`;

    // a_accounts 테이블의 경우 중복 키 방지 로직 적용
    if (tableName === 'a_accounts' && columnName === 'id') {
      // 중복된 newId 값들을 제거하고 첫 번째만 유지
      const uniqueIdMap = new Map<string, string>();
      const seenNewIds = new Set<string>();
      
      idMap.forEach((newId, oldId) => {
        if (!seenNewIds.has(newId)) {
          uniqueIdMap.set(oldId, newId);
          seenNewIds.add(newId);
        } else {
          console.log(`⚠️ 중복 키 방지: ${oldId} -> ${newId} (이미 ${newId}로 매핑된 레코드가 존재)`);
        }
      });
      
      // 중복 제거된 맵으로 CASE WHEN 구문 생성
      uniqueIdMap.forEach((newId, oldId) => {
        sql += `    WHEN '${oldId}' THEN '${newId}'\n`;
      });
      
      sql += `END\n`;
      sql += `WHERE ${columnName} IN (\n`;
      
      // IN 절 생성 (20개씩 줄바꿈)
      const oldIds = Array.from(uniqueIdMap.keys());
      const chunks = [];
      for (let i = 0; i < oldIds.length; i += 20) {
        const chunk = oldIds.slice(i, i + 20);
        chunks.push(`    ${chunk.map(id => `'${id}'`).join(', ')}`);
      }
      sql += chunks.join(',\n') + '\n';
      
    } else {
      // 다른 테이블들은 기존 로직 유지
      idMap.forEach((newId, oldId) => {
        sql += `    WHEN '${oldId}' THEN '${newId}'\n`;
      });
      
      sql += `END\n`;
      sql += `WHERE ${columnName} IN (\n`;
      
      // IN 절 생성 (20개씩 줄바꿈)
      const oldIds = Array.from(idMap.keys());
      const chunks = [];
      for (let i = 0; i < oldIds.length; i += 20) {
        const chunk = oldIds.slice(i, i + 20);
        chunks.push(`    ${chunk.map(id => `'${id}'`).join(', ')}`);
      }
      sql += chunks.join(',\n') + '\n';
    }

    sql += ');\n';

    return sql;
  }

  private generateComplexCaseWhenUpdate(
    tableName: string,
    accountIdColumn: string,
    pubIdColumn: string,
    records: RemapRecord[]
  ): string {
    let sql = `UPDATE ${tableName}\n`;
    sql += `SET ${accountIdColumn} = CASE ${accountIdColumn}\n`;

    // accountId CASE WHEN 구문
    records.forEach(record => {
      sql += `    WHEN '${record.uwo_Gnid}' THEN '${record.uwogl_Gnid}'\n`;
    });
    sql += `END,\n`;

    // pubId CASE WHEN 구문
    sql += `    ${pubIdColumn} = CASE ${pubIdColumn}\n`;
    records.forEach(record => {
      sql += `    WHEN '${record.uwo_Nid}' THEN '${record.uwogl_Nid}'\n`;
    });
    sql += `END\n`;

    // WHERE 절 - accountId와 pubId 조합으로 매칭
    sql += `WHERE (${accountIdColumn}, ${pubIdColumn}) IN (\n`;
    const pairs = records.map(record =>
      `    ('${record.uwo_Gnid}', '${record.uwo_Nid}')`
    );
    sql += pairs.join(',\n') + '\n';
    sql += ');\n';

    // 영향받은 행 수를 누적 변수에 추가
    // sql += `SET @total_affected_rows = @total_affected_rows + ROW_COUNT();\n`;

    return sql;
  }



  private generateCharacterTableUpdates(records: RemapRecord[], codebaseAnalysisResult: AnalysisResult): string {
    let sql = '\n-- 캐릭터 관련 테이블 업데이트 (실제 분석된 스키마 기반)\n';

    // 분석된 테이블에서 user 데이터베이스의 테이블들 중 accountId와 pubId를 모두 가진 테이블들 찾기
    const characterTables = codebaseAnalysisResult.databaseTables.filter(table =>
      table.database === 'user' &&
      table.columns.some(col => col.relatedTo === 'accountId') &&
      table.columns.some(col => col.relatedTo === 'pubId')
    );

    if (characterTables.length === 0) {
      sql += '-- 경고: 분석된 캐릭터 테이블이 없습니다.\n';
      return sql;
    }

    characterTables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

      if (accountIdCol && pubIdCol) {
        sql += `-- 복합 업데이트: (${accountIdCol.columnName}, ${pubIdCol.columnName})\n`;
        sql += this.generateComplexCaseWhenUpdate(
          table.tableName,
          accountIdCol.columnName,
          pubIdCol.columnName,
          records
        );
      } else {
        sql += `-- ${table.tableName}: accountId와 pubId 컬럼이 모두 필요합니다\n`;
        sql += '\n';
      }
    });

    return sql;
  }

  private generateWorldTableUpdates(records: RemapRecord[], codebaseAnalysisResult: AnalysisResult): string {
    let sql = '\n-- 월드 데이터베이스 테이블 업데이트 (실제 분석된 스키마 기반)\n';

    // 분석된 테이블에서 world 데이터베이스의 테이블들 찾기
    const worldTables = codebaseAnalysisResult.databaseTables.filter(table =>
      table.database === 'world' &&
      (table.columns.some(col => col.relatedTo === 'accountId') ||
       table.columns.some(col => col.relatedTo === 'pubId'))
    );

    if (worldTables.length === 0) {
      sql += '-- 경고: 분석된 월드 테이블이 없습니다.\n';
      return sql;
    }

    worldTables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

      // w_auto_sailings 테이블은 accountId와 pubId가 복합키가 아니므로 개별 업데이트
      if (table.tableName === 'w_auto_sailings') {
        sql += `-- 개별 업데이트: accountId와 pubId는 복합키가 아님\n`;

        if (accountIdCol) {
          const gnidMap = new Map<string, string>();
          records.forEach(record => {
            gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
          });
          sql += this.generateCaseWhenUpdate(
            table.tableName,
            accountIdCol.columnName,
            gnidMap
          );
        }

        if (pubIdCol) {
          const nidMap = new Map<string, string>();
          records.forEach(record => {
            nidMap.set(record.uwo_Nid, record.uwogl_Nid);
          });
          sql += this.generateCaseWhenUpdate(
            table.tableName,
            pubIdCol.columnName,
            nidMap
          );
        }
      } else {
        // 다른 테이블들은 accountId와 pubId를 모두 가진 경우 복합 업데이트 수행
        if (accountIdCol && pubIdCol) {
          sql += `-- 복합 업데이트: (${accountIdCol.columnName}, ${pubIdCol.columnName})\n`;
          sql += this.generateComplexCaseWhenUpdate(
            table.tableName,
            accountIdCol.columnName,
            pubIdCol.columnName,
            records
          );
        } else {
          // 개별 컬럼 업데이트
          if (accountIdCol) {
            const gnidMap = new Map<string, string>();
            records.forEach(record => {
              gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
            });
            sql += this.generateCaseWhenUpdate(
              table.tableName,
              accountIdCol.columnName,
              gnidMap
            );
          }

          if (pubIdCol) {
            const nidMap = new Map<string, string>();
            records.forEach(record => {
              nidMap.set(record.uwo_Nid, record.uwogl_Nid);
            });
            sql += this.generateCaseWhenUpdate(
              table.tableName,
              pubIdCol.columnName,
              nidMap
            );
          }
        }
      }

      sql += '\n';
    });

    return sql;
  }



  /**
   * 업데이트될 테이블 목록을 반환
   */
  private getUpdatedTableNames(codebaseAnalysisResult: AnalysisResult, databaseType?: string): string[] {
    if (!codebaseAnalysisResult?.databaseTables) {
      return [];
    }

    let tables = codebaseAnalysisResult.databaseTables;

    // 데이터베이스 타입으로 필터링
    if (databaseType) {
      tables = tables.filter(table => table.database === databaseType);
    }

    // accountId 또는 pubId 관련 컬럼이 있는 테이블만 선택
    const relevantTables = tables.filter(table =>
      table.columns.some(col => col.relatedTo === 'accountId') ||
      table.columns.some(col => col.relatedTo === 'pubId')
    );

    return relevantTables.map(table => table.tableName);
  }

  private async generateAuthDatabaseScript(
    records: RemapRecord[],
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    const fileName = 'auth_update.sql';
    const filePath = path.join(outputDir, fileName);

    let sqlContent = this.generateSqlHeader('auth', records.length);

    // 분석된 테이블에서 auth 데이터베이스의 테이블들 찾기
    const authTables = codebaseAnalysisResult.databaseTables.filter(table =>
      table.database === 'auth' &&
      (table.columns.some(col => col.relatedTo === 'accountId') ||
       table.columns.some(col => col.relatedTo === 'pubId'))
    );

    // 업데이트된 테이블 목록 수집
    const updatedTables = this.getUpdatedTableNames(codebaseAnalysisResult, 'auth');

    if (authTables.length === 0) {
      sqlContent += '-- 경고: 분석된 auth 테이블이 없습니다.\n';
    } else {
      // 배치 단위로 처리
      const batchSize = 1000;
      for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize);
        sqlContent += this.generateAuthUpdateBatch(batch, i / batchSize + 1, authTables);
      }
    }

    sqlContent += this.generateSqlFooter(updatedTables, {
      scriptType: 'Auth 데이터베이스',
      recordCount: records.length,
      tableCount: authTables.length
    });

    await fs.writeFile(filePath, sqlContent, 'utf8');
    console.log(`📄 생성됨: ${fileName} (${records.length}개 레코드, ${authTables.length}개 테이블)`);
  }

  private generateAuthUpdateBatch(records: RemapRecord[], batchNumber: number, authTables: DatabaseTableInfo[]): string {
    let sql = `\n-- ===== 배치 ${batchNumber} =====\n`;

    // Auth 데이터베이스 테이블 업데이트
    sql += this.generateDatabaseTableUpdates(records, authTables, 'auth');

    return sql;
  }

  private generateDatabaseTableUpdates(records: RemapRecord[], tables: DatabaseTableInfo[], databaseName: string): string {
    let sql = `\n-- ${databaseName} 데이터베이스 테이블 업데이트 (실제 분석된 스키마 기반)\n`;

    console.log('=== DEBUG: generateDatabaseTableUpdates ===');
    tables.forEach(table => {
      sql += `\n-- ${table.tableName} 테이블 업데이트\n`;

      const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
      const pubIdCol = table.columns.find(col => col.relatedTo === 'pubId');

             // a_accounts 테이블의 특별 처리 (id 필드) - 중복 방지 (배치 단위)
       let skipAccountIdUpdate = false;
       if (table.tableName === 'a_accounts') {
         const idCol = table.columns.find(col => col.columnName === 'id');
         const accountIdCol = table.columns.find(col => col.relatedTo === 'accountId');
         
         // id 컬럼이 있고, accountId 컬럼도 있는 경우 중복 방지
         if (idCol && accountIdCol) {
           console.log('=== a_accounts 중복 방지: id와 accountId 컬럼이 모두 존재함 (배치 단위) ===');
           console.log('id 컬럼:', idCol.columnName);
           console.log('accountId 컬럼:', accountIdCol.columnName);
           console.log('배치 레코드 수:', records.length);
           
           // id 컬럼만 업데이트하고 accountId는 건너뛰기 (배치 단위)
           const gnidMap = new Map<string, string>();
           records.forEach(record => {
             gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
           });
           sql += this.generateCaseWhenUpdate(
             table.tableName,
             'id',
             gnidMap
           );
           skipAccountIdUpdate = true;
         }
       }

      // a_pub_ids 테이블의 특별 처리 - CSV 데이터와 일치하는 레코드만 업데이트
      if (table.tableName === 'a_pub_ids') {
        console.log('=== a_pub_ids 특별 처리: CSV 데이터와 일치하는 레코드만 업데이트 ===');
        
        if (accountIdCol && pubIdCol) {
          // 각 레코드별로 개별 UPDATE 문 생성 (CSV 데이터와 정확히 일치하는 경우만)
          sql += `-- a_pub_ids 테이블 업데이트 (CSV 데이터와 일치하는 레코드만)\n`;
          
          records.forEach((record, index) => {
            sql += `-- 레코드 ${index + 1}: ${record.uwo_Gnid} -> ${record.uwogl_Gnid}, ${record.uwo_Nid} -> ${record.uwogl_Nid}\n`;
            sql += `UPDATE a_pub_ids SET ${accountIdCol.columnName} = '${record.uwogl_Gnid}', ${pubIdCol.columnName} = '${record.uwogl_Nid}' `;
            sql += `WHERE ${accountIdCol.columnName} = '${record.uwo_Gnid}' AND ${pubIdCol.columnName} = '${record.uwo_Nid}';\n`;
          });
          
          sql += '\n';
          
          // 개별 업데이트는 건너뛰기
          skipAccountIdUpdate = true;
        }
      }

      console.log('table:', table.tableName);
      console.log('accountIdCol:', accountIdCol);
      console.log('pubIdCol:', pubIdCol);
      console.log('skipAccountIdUpdate:', skipAccountIdUpdate);

      if (accountIdCol && !skipAccountIdUpdate) {
        const gnidMap = new Map<string, string>();
        records.forEach(record => {
          gnidMap.set(record.uwo_Gnid, record.uwogl_Gnid);
        });
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          accountIdCol.columnName,
          gnidMap
        );
      }

      if (pubIdCol && table.tableName !== 'a_pub_ids') {
        const nidMap = new Map<string, string>();
        records.forEach(record => {
          nidMap.set(record.uwo_Nid, record.uwogl_Nid);
        });
        sql += this.generateCaseWhenUpdate(
          table.tableName,
          pubIdCol.columnName,
          nidMap
        );
      }

      sql += '\n';
    });

    return sql;
  }

  // Shell Script 생성 메서드들
  async generateShellScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    outputDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    console.log('🐚 Shell 스크립트 생성 중...');

    // Shell 스크립트 폴더 생성
    await fs.ensureDir(outputDir);

    // 전체 업데이트 스크립트 생성
    await this.generateMasterUpdateScript(records, worldMapping, outputDir, codebaseAnalysisResult);

    // 월드별 업데이트 스크립트 생성
    await this.generateWorldUpdateScripts(records, worldMapping, outputDir, codebaseAnalysisResult);

    console.log('✅ Shell 스크립트 생성 완료');
  }

  private async generateMasterUpdateScript(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    scriptsDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    const fileName = 'update_all_mysql.sh';
    const filePath = path.join(scriptsDir, fileName);

    // 데이터베이스 설정 읽기
    const configReader = new DatabaseConfigReader();
    await configReader.loadConfig();

    // 월드별 레코드 그룹화
    const worldGroups = this.groupRecordsByWorld(records);
    const worldIds = Array.from(worldGroups.keys());
    const worldConfigs = await configReader.getWorldDatabaseConfigs(worldIds);
    const authConfig = configReader.getAuthDatabaseConfig();

    let script = this.generateShellHeader('전체 데이터베이스 업데이트', records.length);

    // 하드코딩된 MySQL Auth 설정 (NEW VERSION - DatabaseScriptGenerator)
    script += `
# MySQL Auth 데이터베이스 연결 설정 (database.json5에서 읽어온 값)
MYSQL_AUTH_HOST="${authConfig.host}"
MYSQL_AUTH_PORT="${authConfig.port}"
MYSQL_AUTH_USER="${authConfig.user}"
MYSQL_AUTH_PASSWORD="${authConfig.password}"
MYSQL_AUTH_DATABASE="${authConfig.database}"

# 스크립트 디렉토리
SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="\$SCRIPT_DIR"

echo "🚀 전체 데이터베이스 업데이트 시작..."
echo "📁 스크립트 디렉토리: \$BASE_DIR"

# 에러 발생시 중단
set -e

`;

    // Auth 데이터베이스 업데이트 (전역)
    script += `
echo "🔐 Auth 데이터베이스 업데이트 중..."
if [ -f "\$BASE_DIR/sqls/auth_update.sql" ]; then
    mysql -h"\$MYSQL_AUTH_HOST" -P"\$MYSQL_AUTH_PORT" -u"\$MYSQL_AUTH_USER" -p"\$MYSQL_AUTH_PASSWORD" "\$MYSQL_AUTH_DATABASE" < "\$BASE_DIR/sqls/auth_update.sql"
    echo "✅ Auth 데이터베이스 업데이트 완료"
else
    echo "⚠️ sqls/auth_update.sql 파일을 찾을 수 없습니다."
fi

`;

    // 월드별 데이터베이스 업데이트
    for (const worldConfig of worldConfigs) {
      const worldId = worldConfig.worldId;
      const worldDb = worldConfig.mysqlWorld;

      script += `
# ${worldId} 월드 데이터베이스 연결 설정
MYSQL_WORLD_${worldId.replace(/-/g, '_')}_HOST="${worldDb.host}"
MYSQL_WORLD_${worldId.replace(/-/g, '_')}_PORT="${worldDb.port}"
MYSQL_WORLD_${worldId.replace(/-/g, '_')}_USER="${worldDb.user}"
MYSQL_WORLD_${worldId.replace(/-/g, '_')}_PASSWORD="${worldDb.password}"
MYSQL_WORLD_${worldId.replace(/-/g, '_')}_DATABASE="${worldDb.database}"

echo "🌍 ${worldId} 월드 데이터베이스 업데이트 중..."
if [ -f "\$BASE_DIR/sqls/${worldId.toLowerCase()}/world_update.sql" ]; then
    mysql -h"\$MYSQL_WORLD_${worldId.replace(/-/g, '_')}_HOST" -P"\$MYSQL_WORLD_${worldId.replace(/-/g, '_')}_PORT" -u"\$MYSQL_WORLD_${worldId.replace(/-/g, '_')}_USER" -p"\$MYSQL_WORLD_${worldId.replace(/-/g, '_')}_PASSWORD" "\$MYSQL_WORLD_${worldId.replace(/-/g, '_')}_DATABASE" < "\$BASE_DIR/sqls/${worldId.toLowerCase()}/world_update.sql"
    echo "✅ ${worldId} 월드 데이터베이스 업데이트 완료"
else
    echo "⚠️ sqls/${worldId.toLowerCase()}/world_update.sql 파일을 찾을 수 없습니다."
fi

`;
    }

    // User Shard 데이터베이스 업데이트 (월드별)
    for (const worldConfig of worldConfigs) {
      const worldId = worldConfig.worldId;

      worldConfig.mysqlUserShards.forEach((shardDb, shardIndex) => {
        const shardName = `user_shard_${shardIndex}`;
        script += `
# ${worldId} - ${shardName} 데이터베이스 연결 설정
MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_HOST="${shardDb.host}"
MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_PORT="${shardDb.port}"
MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_USER="${shardDb.user}"
MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_PASSWORD="${shardDb.password}"
MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_DATABASE="${shardDb.database}"

echo "👥 ${worldId} - ${shardName} 데이터베이스 업데이트 중..."
if [ -f "\$BASE_DIR/sqls/${worldId.toLowerCase()}/${shardName}_update.sql" ]; then
    mysql -h"\$MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_HOST" -P"\$MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_PORT" -u"\$MYSQL_USER_${worldId.replace(/-/g, '_')}_USER" -p"\$MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_PASSWORD" "\$MYSQL_USER_${worldId.replace(/-/g, '_')}_${shardIndex}_DATABASE" < "\$BASE_DIR/sqls/${worldId.toLowerCase()}/${shardName}_update.sql"
    echo "✅ ${worldId} - ${shardName} 데이터베이스 업데이트 완료"
else
    echo "⚠️ sqls/${worldId.toLowerCase()}/${shardName}_update.sql 파일을 찾을 수 없습니다."
fi

`;
      });
    }



    script += `
echo "🎉 전체 데이터베이스 업데이트 완료!"
echo "📊 총 ${records.length}개 레코드가 업데이트되었습니다."
`;

    await fs.writeFile(filePath, script, 'utf8');
    await fs.chmod(filePath, 0o755); // 실행 권한 부여
    console.log(`📄 생성됨: ${fileName} (전체 업데이트 스크립트)`);
  }

  private async generateWorldUpdateScripts(
    records: RemapRecord[],
    worldMapping: WorldShardMapping,
    scriptsDir: string,
    codebaseAnalysisResult: AnalysisResult
  ): Promise<void> {
    // 월드별로 레코드 그룹화
    const worldGroups = new Map<string, RemapRecord[]>();
    records.forEach(record => {
      const worldId = record.gameServerId;
      if (!worldGroups.has(worldId)) {
        worldGroups.set(worldId, []);
      }
      worldGroups.get(worldId)!.push(record);
    });

    // 각 월드별로 스크립트 생성
    for (const [worldId, worldRecords] of worldGroups.entries()) {
      await this.generateSingleWorldUpdateScript(worldRecords, worldId, worldMapping, scriptsDir);
    }
  }

  private async generateSingleWorldUpdateScript(
    records: RemapRecord[],
    worldId: string,
    worldMapping: WorldShardMapping,
    scriptsDir: string
  ): Promise<void> {
    const fileName = `update_${worldId.toLowerCase().replace(/-/g, '_')}_mysql.sh`;
    const filePath = path.join(scriptsDir, fileName);

    // 데이터베이스 설정 읽기
    const configReader = new DatabaseConfigReader();
    await configReader.loadConfig();
    const worldConfig = configReader.getWorldDatabaseConfig(worldId);

    let script = this.generateShellHeader(`${worldId} 월드 데이터베이스 업데이트`, records.length);

    // 하드코딩된 데이터베이스 설정
    script += `
# ${worldId} 월드 데이터베이스 연결 설정 (database.json5에서 읽어온 값)
MYSQL_WORLD_HOST="${worldConfig.mysqlWorld.host}"
MYSQL_WORLD_PORT="${worldConfig.mysqlWorld.port}"
MYSQL_WORLD_USER="${worldConfig.mysqlWorld.user}"
MYSQL_WORLD_PASSWORD="${worldConfig.mysqlWorld.password}"
MYSQL_WORLD_DATABASE="${worldConfig.mysqlWorld.database}"

`;

    // 사용자 샤드 데이터베이스 설정
    worldConfig.mysqlUserShards.forEach((shardDb, shardIndex) => {
      script += `
# User Shard ${shardIndex} 데이터베이스 연결 설정
MYSQL_USER_${shardIndex}_HOST="${shardDb.host}"
MYSQL_USER_${shardIndex}_PORT="${shardDb.port}"
MYSQL_USER_${shardIndex}_USER="${shardDb.user}"
MYSQL_USER_${shardIndex}_PASSWORD="${shardDb.password}"
MYSQL_USER_${shardIndex}_DATABASE="${shardDb.database}"

`;
    });

    script += `
# 스크립트 디렉토리
SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
WORLD_SQLS_DIR="\$SCRIPT_DIR/sqls/${worldId.toLowerCase()}"

echo "🌍 ${worldId} 월드 데이터베이스 업데이트 시작..."
echo "📁 월드 SQL 스크립트 디렉토리: \$WORLD_SQLS_DIR"

# 에러 발생시 중단
set -e

`;

    // World 데이터베이스 업데이트
    script += `
echo "🌍 World 데이터베이스 업데이트 중..."
if [ -f "\$WORLD_SQLS_DIR/world_update.sql" ]; then
    mysql -h"\$MYSQL_WORLD_HOST" -P"\$MYSQL_WORLD_PORT" -u"\$MYSQL_WORLD_USER" -p"\$MYSQL_WORLD_PASSWORD" "\$MYSQL_WORLD_DATABASE" < "\$WORLD_SQLS_DIR/world_update.sql"
    echo "✅ World 데이터베이스 업데이트 완료"
else
    echo "⚠️ \$WORLD_SQLS_DIR/world_update.sql 파일을 찾을 수 없습니다."
fi

`;

    // User Shard 데이터베이스 업데이트
    worldConfig.mysqlUserShards.forEach((shardDb, shardIndex) => {
      const shardName = `user_shard_${shardIndex}`;
      script += `
echo "👥 ${shardName} 데이터베이스 업데이트 중..."
if [ -f "\$WORLD_SQLS_DIR/${shardName}_update.sql" ]; then
    mysql -h"\$MYSQL_USER_${shardIndex}_HOST" -P"\$MYSQL_USER_${shardIndex}_PORT" -u"\$MYSQL_USER_${shardIndex}_USER" -p"\$MYSQL_USER_${shardIndex}_PASSWORD" "\$MYSQL_USER_${shardIndex}_DATABASE" < "\$WORLD_SQLS_DIR/${shardName}_update.sql"
    echo "✅ ${shardName} 데이터베이스 업데이트 완료"
else
    echo "⚠️ \$WORLD_SQLS_DIR/${shardName}_update.sql 파일을 찾을 수 없습니다."
fi

`;
    });

    script += `
echo "🎉 ${worldId} 월드 데이터베이스 업데이트 완료!"
echo "📊 총 ${records.length}개 레코드가 업데이트되었습니다."
`;

    await fs.writeFile(filePath, script, 'utf8');
    await fs.chmod(filePath, 0o755); // 실행 권한 부여
    console.log(`📄 생성됨: ${fileName} (${worldId} 월드 업데이트 스크립트)`);
  }

  private generateShellHeader(description: string, recordCount: number): string {
    const timestamp = new Date().toISOString();
    return `#!/bin/bash
# =====================================================
# UWO GNID/NID 리맵핑 Shell 스크립트
# 설명: ${description}
# 생성일시: ${timestamp}
# 레코드 수: ${recordCount}
# =====================================================

`;
  }
}
