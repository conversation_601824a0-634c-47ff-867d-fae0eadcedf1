// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as query from '../query';
import { PointChange } from '../../lobbyd/userPoints';
import { MError, MErrorCode } from '../../motiflib/merror';

export const spName = 'mp_u_points_batch_update';
export const errorCode = MErrorCode.USER_UPDATE_POINT_QUERY_ERROR;

const spFunction = query.generateSPFunction(spName);
const catchHandler = query.generateMErrorRejection(errorCode);

export default function (
  connection: query.Connection,
  userId: number,
  pointChanges: PointChange[]
): Promise<void> {
  return spFunction(connection, userId, JSON.stringify(pointChanges))
    .then((qr) => {
      if (qr.rows[0][0]['affectedRows'] === 0) {
        throw new MError(spName + ' is failed', errorCode, {
          userId,
          pointChanges,
        });
      }
      return;
    })
    .catch(catchHandler);
} 