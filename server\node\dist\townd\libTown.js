"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.notifyTradeUnpopularChanged = exports.notifyTradeCrazeEventBudgetChanged = exports.notifySomeTradeGoodsUpdated = exports.notifyDevTownMayorChanged = exports.notifyDevTownNationSharePointChanged = exports.notifyTownMayorShipyardTaxChanged = exports.notifyTownMayorTaxChanged = exports.notifyInvestmentSessionClosed = exports.notifyDevelopmentNationSharePointChanged = exports.notifyTownInvested = exports.showEmoticonInstant = exports.showSocialAniInstant = exports.notifySmuggleAllUpdated = exports.notifyTradeAllUpdated = exports.updateTownUserSyncData = exports.updateFriendlyBattle = exports.endFriendlyEncount = exports.updateFriendlyEncount = exports.updateSocialAniPersist = exports.awayNpc = exports.contactNpc = exports.leaveBuilding = exports.enterBuilding = exports.changeSidekickPets = exports.changeSidekickMates = exports.changeRepresentedMate = exports.changeMate = exports.moveUser = exports.getTownUserAll = exports.getTownUser = exports.leaveTownZone = exports.townLoadedByUser = exports.enterTownZone = exports.getTownZones = exports.getTownZone = exports.TownZone = exports.TownUser = void 0;
const lodash_1 = __importDefault(require("lodash"));
const nanoid_1 = require("nanoid");
const cms_1 = __importDefault(require("../cms"));
const ex_1 = require("../cms/ex");
const mhttp_1 = __importDefault(require("../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../motiflib/mconf"));
const mlog_1 = __importDefault(require("../motiflib/mlog"));
const mutil = __importStar(require("../motiflib/mutil"));
const townUserState_1 = require("./townUserState");
const cmsEx = __importStar(require("../cms/ex"));
const townGrid_1 = require("./townGrid");
const TC = __importStar(require("./townCoordinate"));
const townVisibility_1 = __importDefault(require("./townVisibility"));
const nationUtil_1 = require("../motiflib/model/lobby/nationUtil");
const nationCabinetDesc_1 = require("../cms/nationCabinetDesc");
const npcInteractionDesc_1 = require("../cms/npcInteractionDesc");
const userFriendlyEncount_1 = require("../lobbyd/userFriendlyEncount");
// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------
// All users.
const allUsers = new Map();
// All towns.
// {
//   (townCmsId): {
//     (channelId): (TownZone instance)
//     .
//     .
//   },
//   .
//   .
// }
const allTownZones = {};
// ----------------------------------------------------------------------------
// Town user class.
// 다른 유저에게 항해사정보 동기화 방식 - 브로드캐스트시 representedMate 이 비어있으면 mateNub 을 전송한다.
// TownUser 에 mateNub, representedMate 둘다를 관리하는 이유는 차후 기획이 변경될 경우 작업편리성을 위함.
// 우선순위 1. representedMate (대표 항해사 상세정보)
// 우선순위 2. mateNub (제독 항해사 상세정보)
// ----------------------------------------------------------------------------
class TownUser {
    constructor(userId, userName, syncData, mateNub, lobbyUrl, representedMate, sidekickMates, sidekickPets) {
        this.userId = userId;
        this.userName = userName;
        this.syncData = syncData;
        this.mateNub = mateNub;
        this.lobbyUrl = lobbyUrl;
        this.representedMate = representedMate;
        this.sidekickMates = sidekickMates;
        this.sidekickPets = sidekickPets;
        // TOWN_USER_STATE 초기화.
        this.syncData.user.townUserState = townUserState_1.TOWN_USER_STATE.NONE;
        // current town zone
        this.curTownCmsId = 0;
        this.curTownChannelId = '';
        // move state
        this.moveState = {
            userId,
            x: 0,
            y: 0,
            degrees: 0,
            speed: 0,
        };
        this.buildingType = ex_1.BUILDING_TYPE.NONE;
        this.grid = null;
    }
    // ----------------------------------------------------------------------------
    getTownUserState() {
        return this.syncData.user.townUserState;
    }
    // ----------------------------------------------------------------------------
    setTownUserState(newState) {
        this.syncData.user.townUserState = newState;
    }
    // ----------------------------------------------------------------------------
    getGrid() {
        return this.grid;
    }
    // ----------------------------------------------------------------------------
    setGrid(grid) {
        this.grid = grid;
    }
    // ----------------------------------------------------------------------------
    getCurrentZone() {
        return this.grid.getParentZone();
    }
    // ----------------------------------------------------------------------------
    updateMoveState(moveState) {
        this.moveState = moveState;
    }
    // ----------------------------------------------------------------------------
    isMoveStateChanged(moveState) {
        return !lodash_1.default.isEqual(this.moveState, moveState);
    }
    // ----------------------------------------------------------------------------
    isVisible() {
        const townUserState = this.syncData.user.townUserState;
        return (townUserState === townUserState_1.TOWN_USER_STATE.IN_TOWN ||
            townUserState === townUserState_1.TOWN_USER_STATE.IN_BUILDING ||
            townUserState === townUserState_1.TOWN_USER_STATE.IN_FRIENDLY_ENCOUNT);
    }
    // ----------------------------------------------------------------------------
    checkFriendlyEncount() {
        if (this.buildingType > ex_1.BUILDING_TYPE.NONE) {
            return userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_ENTER_BUILDING;
        }
        if (this.syncData.user.bQuestSingleMode) {
            return userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_SINGLE_MODE;
        }
        if (this.syncData.user.townUserState > townUserState_1.TOWN_USER_STATE.IN_TOWN) {
            return userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_INVALID_TOWN_USER_STATE;
        }
        if (this.friendlyEncountUserId) {
            return userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_HAS_FRIENDLY_ENCOUNT_USER_ID;
        }
        return userFriendlyEncount_1.FriendlyEncountResult.WAITING_FOR_CHOICE;
    }
    checkFriendlyBattle(isAttacker) {
        if (this.buildingType > ex_1.BUILDING_TYPE.NONE) {
            return isAttacker
                ? userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_ENTER_BUILDING
                : userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_ENTER_BUILDING;
        }
        if (this.syncData.user.bQuestSingleMode) {
            return isAttacker
                ? userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_SINGLE_MODE
                : userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_SINGLE_MODE;
        }
        if (this.syncData.user.townUserState !== townUserState_1.TOWN_USER_STATE.IN_FRIENDLY_ENCOUNT) {
            return isAttacker
                ? userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_INVALID_TOWN_USER_STATE
                : userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_INVALID_TOWN_USER_STATE;
        }
        if (this.friendlyEncountUserId === undefined) {
            return isAttacker
                ? userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_HAS_FRIENDLY_ENCOUNT_USER_ID
                : userFriendlyEncount_1.FriendlyEncountResult.CANCELED_BY_DEFENSER_HAS_FRIENDLY_ENCOUNT_USER_ID;
        }
        return userFriendlyEncount_1.FriendlyEncountResult.START_BATTLE;
    }
    setFriendlyEncountUserId(encountUserId) {
        this.friendlyEncountUserId = encountUserId;
    }
    getFriendlyEncountUserId() {
        return this.friendlyEncountUserId;
    }
}
exports.TownUser = TownUser;
// ----------------------------------------------------------------------------
// Town class.
// ----------------------------------------------------------------------------
class TownZone {
    //------------------------------------------------------------------------------
    constructor(cmsId, channelId) {
        this.cmsId = cmsId;
        this.channelId = channelId;
        this.users = {};
        this.numUsers = 0;
        // 그리드 초기화.
        this._initGrids();
    }
    // ----------------------------------------------------------------------------
    addUser(townUser) {
        townUser.curTownCmsId = this.cmsId;
        townUser.curTownChannelId = this.channelId;
        townUser.setTownUserState(townUserState_1.TOWN_USER_STATE.IN_TOWN);
        const townSpawnCms = cms_1.default.TownSpawn[this.cmsId];
        townUser.moveState.x = Math.floor(townSpawnCms.start.position.x * 100);
        townUser.moveState.y = Math.floor(townSpawnCms.start.position.y * 100);
        this.users[townUser.userId] = townUser;
        ++this.numUsers;
        if (this.numUsers > mconf_1.default.maxUsersPerChannel) {
            mlog_1.default.warn('too many users per channel', {
                userId: townUser.userId,
                townCmsId: this.cmsId,
                channelId: this.channelId,
                numUsers: this.numUsers,
            });
        }
        // 어느 그리드에 들어갈지 알아낸다.
        const gridCoord = TC.Ue2Grid(townUser.moveState.x, townUser.moveState.y, mconf_1.default.visibility.gridDimension.x, mconf_1.default.visibility.gridDimension.y, mconf_1.default.visibility.gridExtentScale);
        const grid = this.getOrCreateTownGrid(gridCoord);
        if (!grid) {
            mlog_1.default.error('User location cannot be translated to a grid!', {
                userId: townUser.userId,
                cmsId: this.cmsId,
                moveState: townUser.moveState,
            });
            return;
        }
        // grid 에 추가.
        grid.addUser(townUser);
        mhttp_1.default.zonelbd
            .incUserCount({
            cmsId: this.cmsId,
            channelId: this.channelId,
            url: mconf_1.default.apiService.url,
            zoneType: cmsEx.ZoneType.TOWN,
        })
            .catch((err) => {
            mlog_1.default.warn('Failed to notify inc-user-count.', {
                cmsId: this.cmsId,
                channleId: this.channelId,
                errMsg: err.message,
            });
        });
        mlog_1.default.debug('user added to town zone', {
            userId: townUser.userId,
            curTownCmsId: townUser.curTownCmsId,
            curTownChannelId: townUser.curTownChannelId,
        });
    }
    // ----------------------------------------------------------------------------
    removeUser(townUser) {
        townUser.curTownCmsId = 0;
        townUser.curTownChannelId = '';
        delete this.users[townUser.userId];
        --this.numUsers;
        const curGrid = townUser.getGrid();
        curGrid.removeUser(townUser.userId);
        mhttp_1.default.zonelbd
            .decUserCount({
            cmsId: this.cmsId,
            channelId: this.channelId,
            url: mconf_1.default.apiService.url,
            zoneType: cmsEx.ZoneType.TOWN,
        })
            .catch((err) => {
            mlog_1.default.warn('Failed to notify dec-user-count.', {
                cmsId: this.cmsId,
                channleId: this.channelId,
                errMsg: err.message,
            });
        });
        mlog_1.default.debug('user removed from town zone', {
            userId: townUser.userId,
            curTownCmsId: townUser.curTownCmsId,
            curTownChannelId: townUser.curTownChannelId,
        });
    }
    // ----------------------------------------------------------------------------
    getNearbyUsers(targetUser) {
        let townUsers = [];
        if (!targetUser.getGrid()) {
            mlog_1.default.warn('fail getNearbyUsers. null grid', {
                userId: targetUser.userId,
            });
            return townUsers;
        }
        const excepUserIds = [targetUser.userId];
        const vis = targetUser.getGrid().getVisibility();
        townUsers = vis.getTownUsers(excepUserIds);
        return townUsers;
    }
    // ----------------------------------------------------------------------------
    getLobbyGroups(targetUser) {
        if (!targetUser.getGrid()) {
            mlog_1.default.warn('fail getLobbyGroups. null grid', {
                userId: targetUser.userId,
            });
            return null;
        }
        const vis = targetUser.getGrid().getVisibility();
        const bcGroup = townVisibility_1.default.getBroadcastGroupForGrids(vis.getGrids(), targetUser.userId);
        return bcGroup;
    }
    // ----------------------------------------------------------------------------
    //if not exist, create it! (for add/move)
    getOrCreateTownGrid(gridCoord) {
        if (!this.gridTable[gridCoord.row] || !this.gridTable[gridCoord.row][gridCoord.col]) {
            const grid = this._createGrid(gridCoord);
            this._rebuildNearbyGrids(grid);
            if (mconf_1.default.isDev) {
                this.showGridFullView();
            }
        }
        return this.getTownGridByRowCol(gridCoord.row, gridCoord.col);
    }
    // ----------------------------------------------------------------------------
    //if not exist, return null! (for visibility check)
    getTownGridByRowCol(r, c) {
        if (!this.gridTable[r] || !this.gridTable[r][c]) {
            return null;
        }
        return this.gridTable[r][c];
    }
    // ----------------------------------------------------------------------------
    _rebuildNearbyGrids(sourceGrid) {
        const radius = mconf_1.default.visibility.radius;
        const gridCoord = sourceGrid.getRelCoord();
        mlog_1.default.verbose('[DUMP] _rebuildNearbyGrids of.. ', { gridCoord: JSON.stringify(gridCoord) });
        for (let r = gridCoord.row - radius; r <= gridCoord.row + radius; ++r) {
            for (let c = gridCoord.col - radius; c <= gridCoord.col + radius; ++c) {
                const nearGrid = this.getTownGridByRowCol(r, c);
                if (nearGrid) {
                    nearGrid.getVisibility().addVisibleGrid(sourceGrid);
                    if (mconf_1.default.isDev) {
                        mlog_1.default.verbose('[DUMP] addVisibleGrid to.. ', {
                            gridCoord: JSON.stringify(nearGrid.getRelCoord()),
                        });
                    }
                }
            }
        }
    }
    // ----------------------------------------------------------------------------
    _createGrid(gridCoord) {
        const grid = new townGrid_1.TownGrid(this, gridCoord);
        lodash_1.default.merge(this.gridTable, { [gridCoord.row]: { [gridCoord.col]: grid } });
        // 테스트로 가끔씩 덤프.
        // if (mutil.randIntInc(0, 500) === 0) {
        // mlog.verbose('[DUMP] grid:', { gridInfo: JSON.stringify(grid.dumpObj()) });
        // }
        return grid;
    }
    // ----------------------------------------------------------------------------
    _initGrids() {
        const dimX = mconf_1.default.visibility.gridDimension.x;
        const dimY = mconf_1.default.visibility.gridDimension.y;
        const gridExtentScale = mconf_1.default.visibility.gridExtentScale;
        const coord = TC.GetGridStartDefaultCoordinate(dimX, dimY, gridExtentScale);
        this.gridTable = {};
        for (let r = 0; r < dimX; ++r) {
            for (let c = 0; c < dimY; ++c) {
                const relCoord = {
                    row: r + coord.row,
                    col: c + coord.col,
                };
                this._createGrid(relCoord);
            }
        }
    }
    // ----------------------------------------------------------------------------
    dumpObj() {
        return {
            region: this.cmsId,
            channel: this.channelId,
            numUsers: this.numUsers,
        };
    }
    // ----------------------------------------------------------------------------
    onUpdateMove(targetUser, moveState) {
        if (!this.users[targetUser.userId]) {
            mlog_1.default.error('User does not belong to this zone!', {
                userId: targetUser.userId,
                zone: this.dumpObj(),
            });
            return;
        }
        const curGrid = targetUser.getGrid();
        if (!curGrid) {
            mlog_1.default.warn('User doesnt have a valid grid!', {
                userId: targetUser.userId,
                zone: this.dumpObj(),
            });
            return;
        }
        // 우선 주변에 브로드캐스트
        this.onUserMove(targetUser, moveState);
        // 그리드에 변경이 있었는지 체크.
        const gridCoord = TC.Ue2Grid(moveState.x, moveState.y, mconf_1.default.visibility.gridDimension.x, mconf_1.default.visibility.gridDimension.y, mconf_1.default.visibility.gridExtentScale);
        const newGrid = this.getOrCreateTownGrid(gridCoord);
        if (!newGrid) {
            // 유저의 존이 변경 될 때, 현재 존에서는 유효하지 않은 타일로 여겨질 수 있다.
            // 이럴 땐 무시하고, 존 변경 과정에서 처리 되도록.
            mlog_1.default.warn('User moving to an invalid grid!', {
                userId: targetUser.userId,
                location,
            });
            return;
        }
        // 그리드의 이동이 있었다면 유저의 그리드를 변경.
        if (curGrid !== newGrid) {
            curGrid.removeUser(targetUser.userId, newGrid);
            newGrid.addUser(targetUser, curGrid);
            this._sendUserSyncDataOnGridChange(targetUser, curGrid, newGrid);
        }
    }
    // ----------------------------------------------------------------------------
    _sendUserSyncDataOnGridChange(targetUser, oldGrid, newGrid) {
        // Just in case.
        if (oldGrid === newGrid) {
            return;
        }
        // 새롭게 시야에 들어온 그리드들을 찾고.
        const oldVis = oldGrid.getVisibility();
        const newVis = newGrid.getVisibility();
        const newlyVisibleGrids = newVis.subtractGrids(oldVis);
        const oldlyVisibleGrids = oldVis.subtractGrids(newVis);
        if (mconf_1.default.isDev) {
            const newGridCoordinates = newlyVisibleGrids.map((g) => g.getRelCoord());
            const oldGridCoordinates = oldlyVisibleGrids.map((g) => g.getRelCoord());
            // mlog.verbose('[TEMP] User grid changed.', {
            //   userId: targetUser.userId,
            //   from: oldGrid.getRelCoord(),
            //   to: newGrid.getRelCoord(),
            //   newlyVisibleGrids: newGridCoordinates,
            //   oldlyVisibleGrids: oldGridCoordinates,
            // });
        }
        {
            // 새로운 그리드들의 정보를 해당 유저에게 보내고.
            const nearbyUserInfos = [];
            for (const grid of newlyVisibleGrids) {
                //iterate visible grids to gather user info.
                const gridTownUsers = grid.getUsers();
                gridTownUsers.forEach((townUser) => {
                    if (townUser.userId != targetUser.userId && townUser.isVisible()) {
                        nearbyUserInfos.push({
                            userId: townUser.userId,
                            name: townUser.userName,
                            syncData: townUser.syncData,
                            mateNub: getMateNubFromTownUser(townUser),
                            moveState: townUser.moveState,
                            sidekickMates: townUser.sidekickMates,
                            sidekickPets: townUser.sidekickPets,
                        });
                        // mlog.verbose('[TEMP] nearbyUserInfos - add new user ', {
                        //   userId: targetUser.userId,
                        //   newUserId: townUser.userId,
                        //   name: townUser.userName,
                        // });
                    }
                });
            }
            // mlog.verbose('[TEMP] nearbyUserInfos - ', JSON.stringify(nearbyUserInfos));
            // Send nearby user infos to the target user.
            if (0 < nearbyUserInfos.length) {
                const channel = mhttp_1.default.lobbypx.channel(targetUser.lobbyUrl);
                channel.sendNearbyUsers(targetUser.userId, nearbyUserInfos);
            }
        }
        {
            // 해당 유저의 추가를 새로운 그리드들의 유저들에게 브로드캐스트.
            const newLobbyGroups = townVisibility_1.default.getBroadcastGroupForGrids(newlyVisibleGrids, targetUser.userId);
            // Build town user update packet.
            const townUserUpdatePacket = {
                userId: targetUser.userId,
                name: targetUser.userName,
                syncData: targetUser.syncData,
                mateNub: getMateNubFromTownUser(targetUser),
                moveState: targetUser.moveState,
                sidekickMates: targetUser.sidekickMates,
                sidekickPets: targetUser.sidekickPets,
            };
            // Broadcast user spawn.
            mhttp_1.default.lobbypx.notifyTownUserUpdate(newLobbyGroups, townUserUpdatePacket);
        }
        {
            // 지워진 그리드들의 정보를 해당 유저에게 보내고.
            const removedUserIds = [];
            for (const grid of oldlyVisibleGrids) {
                //iterate visible grids to gather user info.
                const gridTownUsers = grid.getUsers();
                gridTownUsers.forEach((townUser) => {
                    if (townUser.userId != targetUser.userId) {
                        removedUserIds.push(townUser.userId);
                    }
                });
            }
            if (0 < removedUserIds.length) {
                // mlog.verbose('[TEMP] nearbyUserInfos - ', JSON.stringify(nearbyUserInfos));
                const lobbyGroup = {};
                lobbyGroup[targetUser.lobbyUrl] = [];
                lobbyGroup[targetUser.lobbyUrl].push(targetUser.userId);
                // Build leave packet.
                const packet = removedUserIds;
                mhttp_1.default.lobbypx.notifyUserLeave(lobbyGroup, packet);
            }
        }
        {
            // 해당 유저의 제거를 오래된 그리드들의 유저들에게 브로드캐스트.
            const oldLobbyGroups = townVisibility_1.default.getBroadcastGroupForGrids(oldlyVisibleGrids, targetUser.userId);
            // Build leave packet.
            const removedUserIds = [];
            removedUserIds.push(targetUser.userId);
            const packet = removedUserIds;
            // Broadcast user leave.
            mhttp_1.default.lobbypx.notifyUserLeave(oldLobbyGroups, packet);
        }
    }
    // ----------------------------------------------------------------------------
    showGridFullView() {
        const gridDimX = mconf_1.default.visibility.gridDimension.x;
        const gridDimY = mconf_1.default.visibility.gridDimension.y;
        const gridExtentScale = mconf_1.default.visibility.gridExtentScale;
        const townWorldSizeExtentX = TC.TownWorldSizeDefaultX * gridExtentScale;
        const townWorldSizeExtentY = TC.TownWorldSizeDefaultY * gridExtentScale;
        const gridSizeX = TC.TownWorldSizeDefaultX / gridDimX; // 여기까지는 기본 그리드크기를 이용(defualt: 140000)
        const gridDimMaxX = townWorldSizeExtentX / gridSizeX;
        const gridSizeY = TC.TownWorldSizeDefaultY / gridDimY; // 여기까지는 기본 그리드크기를 이용 (defualt: 175000)
        const gridDimMaxY = townWorldSizeExtentY / gridSizeY;
        mlog_1.default.verbose('[DUMP] !showGridFullView!');
        for (let r = 0; r < gridDimMaxX; ++r) {
            let row = '';
            for (let c = 0; c < gridDimMaxY; ++c) {
                const grid = this.getTownGridByRowCol(r, c);
                if (grid) {
                    row = row.concat('*');
                    //row.push('*');
                }
                else {
                    row = row.concat('.');
                }
            }
            mlog_1.default.verbose(row);
            // mlog.verbose(JSON.stringify(row));
        }
    }
    // ----------------------------------------------------------------------------
    onUserMove(targetUser, moveState) {
        const lobbyGroups = this.getLobbyGroups(targetUser);
        // const packet = {
        //   userId: targetUser.userId,
        //   moveState,
        // };
        mhttp_1.default.lobbypx.notifyUserMove(lobbyGroups, moveState);
    }
    // ----------------------------------------------------------------------------
    onUserEnter(targetUser) {
        const lobbyGroups = this.getLobbyGroups(targetUser);
        // Build town user update packet.
        const townUserUpdatePacket = {
            userId: targetUser.userId,
            name: targetUser.userName,
            syncData: targetUser.syncData,
            mateNub: getMateNubFromTownUser(targetUser),
            moveState: targetUser.moveState,
            sidekickMates: targetUser.sidekickMates,
            sidekickPets: targetUser.sidekickPets,
        };
        mlog_1.default.verbose('[TEMP] onUserEnter', {
            userId: townUserUpdatePacket.userId,
            representedMate: targetUser.representedMate,
            sidekickMates: targetUser.sidekickMates,
        });
        // Broadcast user spawn.
        mhttp_1.default.lobbypx.notifyTownUserUpdate(lobbyGroups, townUserUpdatePacket);
    }
    // ----------------------------------------------------------------------------
    onUserLoadComplete(targetUser) {
        var _a, _b, _c;
        // Gather nearby user infos.
        const nearbyUsers = this.getNearbyUsers(targetUser);
        const nearbyUserInfos = [];
        for (const user of nearbyUsers) {
            nearbyUserInfos.push({
                userId: user.userId,
                name: user.userName,
                syncData: user.syncData,
                mateNub: getMateNubFromTownUser(user),
                moveState: user.moveState,
                sidekickMates: user.sidekickMates,
                sidekickPets: user.sidekickPets,
            });
        }
        // mlog.verbose('[TEMP] nearbyUserInfos - ', JSON.stringify(nearbyUserInfos));
        if (0 < nearbyUserInfos.length) {
            // Send nearby user infos to the target user.
            const channel = mhttp_1.default.lobbypx.channel(targetUser.lobbyUrl);
            channel.sendNearbyUsers(targetUser.userId, nearbyUserInfos);
        }
        // Broadcast 관직 유저 입장 안내
        const nationCabinetCmsId = (_c = (_b = (_a = targetUser === null || targetUser === void 0 ? void 0 : targetUser.syncData) === null || _a === void 0 ? void 0 : _a.user) === null || _b === void 0 ? void 0 : _b.nationCabinet) === null || _c === void 0 ? void 0 : _c.cabinetCmsId;
        if (nationCabinetCmsId) {
            const cabinetCms = cms_1.default.NationCabinet[nationCabinetCmsId];
            if (cabinetCms &&
                nationUtil_1.NationUtil.hasCabinetGradeBenefit(nationCabinetDesc_1.NATION_CABINET_GRADE_BENEFIT_TYPE.TownEnterNotice, cabinetCms.nationCabinetGradeBenefit)) {
                mlog_1.default.verbose('[TEMP] onUserLoadComplete - calling onNationCabinetMemberEnter', {
                    userId: targetUser.userId,
                    nationCabinetCmsId,
                });
                this.onNationCabinetMemberEnter(targetUser, nationCabinetCmsId);
            }
        }
    }
    // ----------------------------------------------------------------------------
    onUserLeave(targetUser) {
        const lobbyGroups = this.getLobbyGroups(targetUser);
        // Build leave packet.
        const removedUserIds = [];
        removedUserIds.push(targetUser.userId);
        const packet = removedUserIds;
        // Broadcast user leave.
        mhttp_1.default.lobbypx.notifyUserLeave(lobbyGroups, packet);
    }
    // ----------------------------------------------------------------------------
    queryMate(reqUser, targetUserId) {
        let targetUser;
        if (reqUser.getGrid()) {
            const vis = reqUser.getGrid().getVisibility();
            targetUser = vis.findUser(targetUserId);
        }
        // Build response.
        const packet = {
            found: false,
            userId: targetUserId,
        };
        if (targetUser) {
            packet.found = true;
            packet.name = targetUser.userName;
            packet.syncData = targetUser.syncData;
            packet.mateNub = getMateNubFromTownUser(targetUser);
            packet.moveState = targetUser.moveState;
            packet.sidekickMates = targetUser.sidekickMates;
            packet.sidekickPets = targetUser.sidekickPets;
        }
        else {
            mlog_1.default.warn('query town mate not found', {
                userId: reqUser.userId,
                reqUserId: reqUser.userId,
                targetUserId,
            });
        }
        const lobbyApi = mhttp_1.default.lobbypx.channel(reqUser.lobbyUrl);
        lobbyApi.sendQueryMateRes(reqUser.userId, packet);
    }
    // ----------------------------------------------------------------------------
    onMateChanged(targetUser, bIsAdmiralChanged) {
        // 항상 대표항해사 정보가 우선이므로 대표항해사 정보가 없을 경우에만 제독항해사를 동기화한다.
        if (!lodash_1.default.isEmpty(targetUser.representedMate)) {
            return;
        }
        const lobbyGroups = this.getLobbyGroups(targetUser);
        // Build town user update packet.
        const townUserUpdatePacket = {
            userId: targetUser.userId,
            name: targetUser.userName,
            mateNub: targetUser.mateNub,
            moveState: targetUser.moveState,
            bIsAdmiralChanged,
        };
        // Broadcast user update.
        mhttp_1.default.lobbypx.notifyTownUserUpdate(lobbyGroups, townUserUpdatePacket);
    }
    // ----------------------------------------------------------------------------
    onRepresentedMateChanged(targetUser) {
        const lobbyGroups = this.getLobbyGroups(targetUser);
        // Build town user update packet.
        const townUserUpdatePacket = {
            userId: targetUser.userId,
            name: targetUser.userName,
            mateNub: getMateNubFromTownUser(targetUser),
            moveState: targetUser.moveState,
        };
        mlog_1.default.verbose('[TEMP] TownZone.onRepresentedMateChanged', {
            userId: townUserUpdatePacket.userId,
            representedMate: targetUser.representedMate,
        });
        // Broadcast user update.
        mhttp_1.default.lobbypx.notifyTownUserUpdate(lobbyGroups, townUserUpdatePacket);
    }
    // ----------------------------------------------------------------------------
    onSidekickMateChanged(targetUser, slots) {
        const lobbyGroups = this.getLobbyGroups(targetUser);
        const sidekickMates = {};
        for (const slot of slots) {
            sidekickMates[slot] = targetUser.sidekickMates[slot];
        }
        // Build town user update packet.
        const townUserUpdatePacket = {
            userId: targetUser.userId,
            name: targetUser.userName,
            sidekickMates,
            moveState: targetUser.moveState,
        };
        mlog_1.default.verbose('[TEMP] TownZone.onSidekickMateChanged', {
            userId: townUserUpdatePacket.userId,
            changedSlots: slots,
            sidekickMates: targetUser.sidekickMates,
        });
        // Broadcast user update.
        mhttp_1.default.lobbypx.notifyTownUserUpdate(lobbyGroups, townUserUpdatePacket);
    }
    // ----------------------------------------------------------------------------
    onSidekickPetChanged(targetUser, slots) {
        const lobbyGroups = this.getLobbyGroups(targetUser);
        const sidekickPets = {};
        for (const slot of slots) {
            sidekickPets[slot] = targetUser.sidekickPets[slot];
        }
        // Build town user update packet.
        const townUserUpdatePacket = {
            userId: targetUser.userId,
            name: targetUser.userName,
            sidekickPets,
            moveState: targetUser.moveState,
        };
        mlog_1.default.verbose('[TEMP] TownZone.onSidekickPetChanged', {
            userId: townUserUpdatePacket.userId,
            changedSlots: slots,
            sidekickPets: targetUser.sidekickPets,
        });
        // Broadcast user update.
        mhttp_1.default.lobbypx.notifyTownUserUpdate(lobbyGroups, townUserUpdatePacket);
    }
    // ----------------------------------------------------------------------------
    onTownStatesChanged(sync) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Build packet.
        const packet = {
            sync,
        };
        // Broadcast town user state update.
        mhttp_1.default.lobbypx.notifyTownStatesUpdate(lobbyGroups, packet);
    }
    // ----------------------------------------------------------------------------
    onTownTradeAllChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.buildingType !== ex_1.BUILDING_TYPE.TRADE) {
                continue;
            }
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast.
        mhttp_1.default.lobbypx.notifyTownTradeAllUpdate(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onTownSmuggleAllChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.npcInteractionFunctionType !== npcInteractionDesc_1.NPC_INTERACTION_FUNCTION_TYPE.TRADE) {
                continue;
            }
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast.
        mhttp_1.default.lobbypx.notifyTownSmuggleAllUpdate(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onTownInvested(msg) {
        // Group by lobby urls.
        const insideLobbyGroups = {};
        const outsideLobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.buildingType === ex_1.BUILDING_TYPE.GOVER ||
                user.buildingType === ex_1.BUILDING_TYPE.EVENT_GOVER) {
                const group = insideLobbyGroups[user.lobbyUrl];
                if (!group) {
                    insideLobbyGroups[user.lobbyUrl] = [user.userId];
                }
                else {
                    group.push(user.userId);
                }
            }
            else {
                const group = outsideLobbyGroups[user.lobbyUrl];
                if (!group) {
                    outsideLobbyGroups[user.lobbyUrl] = [user.userId];
                }
                else {
                    group.push(user.userId);
                }
            }
        }
        // Broadcast.
        msg.bUsersInGover = true;
        mhttp_1.default.lobbypx.notifyTownInvested(insideLobbyGroups, msg);
        const outsideGroupMsg = lodash_1.default.cloneDeep(msg);
        outsideGroupMsg.bUsersInGover = false;
        mhttp_1.default.lobbypx.notifyTownInvested(outsideLobbyGroups, outsideGroupMsg);
    }
    // ----------------------------------------------------------------------------
    onDevelopmentNationSharePointChanged(msg) {
        // Group by lobby urls.
        const insideLobbyGroups = {};
        const outsideLobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.buildingType === ex_1.BUILDING_TYPE.GOVER ||
                user.buildingType === ex_1.BUILDING_TYPE.EVENT_GOVER) {
                const group = insideLobbyGroups[user.lobbyUrl];
                if (!group) {
                    insideLobbyGroups[user.lobbyUrl] = [user.userId];
                }
                else {
                    group.push(user.userId);
                }
            }
            else {
                const group = outsideLobbyGroups[user.lobbyUrl];
                if (!group) {
                    outsideLobbyGroups[user.lobbyUrl] = [user.userId];
                }
                else {
                    group.push(user.userId);
                }
            }
        }
        // Broadcast.
        msg.bUsersInGover = true;
        mhttp_1.default.lobbypx.notifyDevelopmentNationSharePointChanged(insideLobbyGroups, msg);
        const outsideGroupMsg = lodash_1.default.cloneDeep(msg);
        outsideGroupMsg.bUsersInGover = false;
        mhttp_1.default.lobbypx.notifyDevelopmentNationSharePointChanged(outsideLobbyGroups, outsideGroupMsg);
    }
    // ----------------------------------------------------------------------------
    onInvestmentSessionClosed(msg) {
        // Group by lobby urls.
        const insideLobbyGroups = {};
        const outsideLobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.buildingType === ex_1.BUILDING_TYPE.GOVER ||
                user.buildingType === ex_1.BUILDING_TYPE.EVENT_GOVER) {
                const group = insideLobbyGroups[user.lobbyUrl];
                if (!group) {
                    insideLobbyGroups[user.lobbyUrl] = [user.userId];
                }
                else {
                    group.push(user.userId);
                }
            }
            else {
                const group = outsideLobbyGroups[user.lobbyUrl];
                if (!group) {
                    outsideLobbyGroups[user.lobbyUrl] = [user.userId];
                }
                else {
                    group.push(user.userId);
                }
            }
        }
        // Broadcast.
        msg.bUsersInGover = true;
        mhttp_1.default.lobbypx.notifyInvestmentSessionClosed(insideLobbyGroups, msg);
        const outsideGroupMsg = lodash_1.default.cloneDeep(msg);
        outsideGroupMsg.bUsersInGover = false;
        mhttp_1.default.lobbypx.notifyInvestmentSessionClosed(outsideLobbyGroups, outsideGroupMsg);
    }
    // ----------------------------------------------------------------------------
    onTownMayorTaxChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast town user state update.
        mhttp_1.default.lobbypx.notifyTownMayorTaxChanged(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onTownMayorShipyardTaxChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast town user state update.
        mhttp_1.default.lobbypx.notifyTownMayorShipyardTaxChanged(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onDevTownNationSharePointChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast town user state update.
        mhttp_1.default.lobbypx.notifyDevTownNationSharePointChanged(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onTownMayorChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast town user state update.
        mhttp_1.default.lobbypx.notifyTownMayorChanged(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onSomeTradeGoodsUpdated(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.buildingType !== ex_1.BUILDING_TYPE.TRADE) {
                continue;
            }
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast.
        mhttp_1.default.lobbypx.notifyTownTradeUpdate(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onTradeCrazeEventBudgetChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.buildingType !== ex_1.BUILDING_TYPE.TRADE) {
                continue;
            }
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast.
        mhttp_1.default.lobbypx.notifyTradeCrazeEventBudgetChanged(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onTradeUnpopularChanged(msg) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.buildingType !== ex_1.BUILDING_TYPE.TRADE) {
                continue;
            }
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        // Broadcast.
        mhttp_1.default.lobbypx.notifyTradeUnpopularChanged(lobbyGroups, msg);
    }
    // ----------------------------------------------------------------------------
    onShowSocialAniInstant(targetUser, socialAniCmsId) {
        if (!this.users[targetUser.userId]) {
            mlog_1.default.warn('onShowSocialAniInstant: User does not belong to this zone!', {
                userId: targetUser.userId,
                zone: this.dumpObj(),
            });
            return;
        }
        const lobbyGroups = this.getLobbyGroups(targetUser);
        const sendPacket = {
            userId: targetUser.userId,
            socialAniCmsId,
        };
        mhttp_1.default.lobbypx.notifyTownShowSocialAniInstant(lobbyGroups, sendPacket);
    }
    // ----------------------------------------------------------------------------
    onShowEmoticonInstant(targetUser, emoticonCmsId) {
        if (!this.users[targetUser.userId]) {
            mlog_1.default.warn('onShowEmoticonInstant: User does not belong to this zone!', {
                userId: targetUser.userId,
                zone: this.dumpObj(),
            });
            return;
        }
        const lobbyGroups = this.getLobbyGroups(targetUser);
        const sendPacket = {
            userId: targetUser.userId,
            emoticonCmsId,
        };
        mhttp_1.default.lobbypx.notifyTownShowEmoticonInstant(lobbyGroups, sendPacket);
    }
    // ----------------------------------------------------------------------------
    // 관직을 가진 유저가 타운에 입장했을 때, 관직 유저 입장을 마을내 모든 유저에게 브로드캐스트 한다.
    onNationCabinetMemberEnter(targetUser, cabinetCmsId) {
        // Group by lobby urls.
        const lobbyGroups = {};
        for (const key of Object.keys(this.users)) {
            const user = this.users[key];
            if (user.userId === targetUser.userId)
                continue;
            const group = lobbyGroups[user.lobbyUrl];
            if (!group) {
                lobbyGroups[user.lobbyUrl] = [user.userId];
            }
            else {
                group.push(user.userId);
            }
        }
        const sendPacket = {
            userId: targetUser.userId,
            name: targetUser.userName,
            nationCmsId: targetUser.syncData.user.nationCmsId,
            nationCabinetCmsId: cabinetCmsId,
        };
        // Broadcast town user state update.
        mhttp_1.default.lobbypx.notifyNationCabinetMemberEnterNotice(lobbyGroups, sendPacket);
    }
    async onNotifyFriendlyEncountRequested(targetUser, msg) {
        return mhttp_1.default.lobbypx.notifyFriendlyEncountRequested(targetUser.lobbyUrl, targetUser.userId, msg);
    }
    async onNotifyFriendlyEncountEnd(targetUser, msg) {
        return mhttp_1.default.lobbypx.notifyFriendlyEncountEnd(targetUser.lobbyUrl, targetUser.userId, msg);
    }
}
exports.TownZone = TownZone;
// ----------------------------------------------------------------------------
// Private helper functions.
// ----------------------------------------------------------------------------
/**
 *
 * @param townCmsId : 생성할 town
 * @param allocatedChannelId : 이미 성성된 채널Id
 *
 * 비동기상황에서 createTownZone 이 여러번 호출될 수 있다.
 * 그러한 경우 레디스에 있는 채널정보와 서버 메모리상의 존객체정보를 동기화 시키는 로직 적용됨
 *
 * 유저난입시 존-채널을 못찾고 새로운 채널이 계속 만들어질 수 있고
 * 이로 인해 유저들이 새로생성된 채널에 홀로 존재할 수 있다.
 * 이를 최소화하기 위해 createZone 호출시 레디스에서 대상 서버에 여유 채널이 있는 경우
 * 요청한 채널을 생성하지 않고 여유 채널의 아이디를 반환하도록 한다
 *
 */
function createTownZone(townCmsId, allocatedChannelId) {
    let channelId;
    if (allocatedChannelId) {
        channelId = allocatedChannelId;
    }
    else {
        channelId = (0, nanoid_1.nanoid)(8);
    }
    const curTimeUtc = mutil.curTimeUtc();
    const url = mconf_1.default.apiService.url;
    return mhttp_1.default.zonelbd
        .createZone({
        cmsId: townCmsId,
        channelId,
        url,
        curTimeUtc,
        zoneType: cmsEx.ZoneType.TOWN,
    })
        .then((retChannelId) => {
        if (retChannelId) {
            channelId = retChannelId;
            mlog_1.default.info('createZone returned existing Channel', {
                townCmsId,
                channelId,
            });
        }
        // Add to 'allTownZones'.
        let newTownZone;
        const channels = allTownZones[townCmsId];
        if (channels) {
            if (!channels[channelId]) {
                newTownZone = new TownZone(townCmsId, channelId);
                channels[channelId] = newTownZone;
            }
            else {
                mlog_1.default.warn('townZone already created.. just handing over info', {
                    townCmsId,
                    channelId,
                });
                newTownZone = channels[channelId];
            }
        }
        else {
            const channels = {};
            newTownZone = new TownZone(townCmsId, channelId);
            channels[channelId] = newTownZone;
            allTownZones[townCmsId] = channels;
        }
        mlog_1.default.info('townZone created', {
            townCmsId,
            channelId,
        });
        return newTownZone;
    });
}
// 대표항해사를 제독항해사에 우선해서 동기화한다.
function getMateNubFromTownUser(townUser) {
    return lodash_1.default.isEmpty(townUser.representedMate) ? townUser.mateNub : townUser.representedMate;
}
// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------
/**
 *
 * @param townCmsId : 가져올 타운의 cmsId
 * @param channelId : 입장할 채널Id
 *
 * [예외처리 추가사항]
 * channelId값이 있는데 아래 예외상황들이 발생하는 상황은 레디스에는 채널이 생성되어 있는데
 * 서버 메모리에 존객체가 생성이 안된 경우이다.
 * 그런 경우 존생성로직을 한번더 호출하여 레디스와 서버를 동기화 시킨다.
 * allTownZones[townCmsId] 이 없는경우
 * allTownZones[townCmsId][channelId] 이 없는경우
 *
 */
function getTownZone(townCmsId, channelId) {
    if (!channelId) {
        // If 'channelId' is not supplied, create a new town zone.
        return createTownZone(townCmsId);
    }
    // 예외처리상황들
    if (!allTownZones[townCmsId] || !allTownZones[townCmsId][channelId]) {
        mlog_1.default.warn('not found townZone.. trying to create', {
            townCmsId,
            channelId,
        });
        return createTownZone(townCmsId, channelId);
    }
    const townZone = allTownZones[townCmsId][channelId];
    return Promise.resolve(townZone);
}
exports.getTownZone = getTownZone;
// ----------------------------------------------------------------------------
function getTownZones(townCmsId) {
    let townZones = allTownZones[townCmsId];
    if (!townZones) {
        townZones = {};
    }
    return Promise.resolve(townZones);
}
exports.getTownZones = getTownZones;
// ----------------------------------------------------------------------------
function enterTownZone(townZone, townUser) {
    // Add the user to the town zone.
    townZone.addUser(townUser);
    // Add the user to 'allUsers'.
    allUsers.set(townUser.userId, townUser);
    setImmediate(() => {
        townZone.onUserEnter(townUser);
    });
}
exports.enterTownZone = enterTownZone;
// ----------------------------------------------------------------------------
function townLoadedByUser(townZone, townUser) {
    townUser.setTownUserState(townUserState_1.TOWN_USER_STATE.IN_TOWN);
    setImmediate(() => {
        townZone.onUserLoadComplete(townUser);
    });
}
exports.townLoadedByUser = townLoadedByUser;
// ----------------------------------------------------------------------------
function leaveTownZone(townZone, townUser) {
    townZone.onUserLeave(townUser);
    // Remove the user from the town zone.
    townZone.removeUser(townUser);
    // Remove from 'allUsers'.
    allUsers.delete(townUser.userId);
}
exports.leaveTownZone = leaveTownZone;
// ----------------------------------------------------------------------------
function getTownUser(userId) {
    return allUsers.get(userId);
}
exports.getTownUser = getTownUser;
// ----------------------------------------------------------------------------
function getTownUserAll() {
    return Array.from(allUsers.values());
}
exports.getTownUserAll = getTownUserAll;
// ----------------------------------------------------------------------------
function moveUser(townZone, townUser, moveState) {
    // Update user move state.
    townUser.updateMoveState(moveState);
    setImmediate(() => {
        townZone.onUpdateMove(townUser, moveState);
    });
}
exports.moveUser = moveUser;
// ----------------------------------------------------------------------------
function changeMate(townZone, townUser, mateNub, bIsAdmiralChanged) {
    townUser.mateNub = mateNub;
    setImmediate(() => {
        townZone.onMateChanged(townUser, bIsAdmiralChanged);
    });
}
exports.changeMate = changeMate;
// ----------------------------------------------------------------------------
function changeRepresentedMate(townZone, townUser, representedMate) {
    townUser.representedMate = representedMate;
    setImmediate(() => {
        townZone.onRepresentedMateChanged(townUser);
    });
}
exports.changeRepresentedMate = changeRepresentedMate;
// ----------------------------------------------------------------------------
function changeSidekickMates(townZone, townUser, changes) {
    const slots = [];
    for (const change of changes) {
        const { slot, sidekickMateView } = change;
        townUser.sidekickMates[slot] = {
            slot,
            mateView: sidekickMateView,
        };
        slots.push(slot);
    }
    setImmediate(() => {
        townZone.onSidekickMateChanged(townUser, slots);
    });
}
exports.changeSidekickMates = changeSidekickMates;
// ----------------------------------------------------------------------------
function changeSidekickPets(townZone, townUser, changes) {
    const slots = [];
    for (const change of changes) {
        const { slot, sidekickPetView } = change;
        townUser.sidekickPets[slot] = {
            slot,
            petView: sidekickPetView,
        };
        slots.push(slot);
    }
    setImmediate(() => {
        townZone.onSidekickPetChanged(townUser, slots);
    });
}
exports.changeSidekickPets = changeSidekickPets;
// ----------------------------------------------------------------------------
function enterBuilding(townZone, townUser, buildingType) {
    townUser.buildingType = buildingType;
    const tusd = {
        user: { townUserState: townUserState_1.TOWN_USER_STATE.IN_BUILDING },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.enterBuilding = enterBuilding;
// ----------------------------------------------------------------------------
function leaveBuilding(townZone, townUser) {
    const tusd = {
        user: {
            townUserState: townUserState_1.TOWN_USER_STATE.IN_TOWN,
        },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.leaveBuilding = leaveBuilding;
// ----------------------------------------------------------------------------
function contactNpc(townZone, townUser, npcInteractionFunctionType) {
    townUser.npcInteractionFunctionType = npcInteractionFunctionType;
    const tusd = {
        user: { townUserState: townUserState_1.TOWN_USER_STATE.IN_NPC },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.contactNpc = contactNpc;
// ----------------------------------------------------------------------------
function awayNpc(townZone, townUser) {
    townUser.npcInteractionFunctionType = npcInteractionDesc_1.NPC_INTERACTION_FUNCTION_TYPE.NONE;
    const tusd = {
        user: { townUserState: townUserState_1.TOWN_USER_STATE.IN_TOWN },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.awayNpc = awayNpc;
// ----------------------------------------------------------------------------
function updateSocialAniPersist(townZone, townUser, socialAniCmsId) {
    // 동일한 경우 스킵처리
    if (socialAniCmsId === townUser.syncData.user.socialAniCmsId) {
        return;
    }
    const tusd = {
        user: {
            socialAniCmsId,
        },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.updateSocialAniPersist = updateSocialAniPersist;
// ----------------------------------------------------------------------------
function updateFriendlyEncount(townZone, townUser) {
    const tusd = {
        user: {
            townUserState: townUserState_1.TOWN_USER_STATE.IN_FRIENDLY_ENCOUNT,
        },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.updateFriendlyEncount = updateFriendlyEncount;
// ----------------------------------------------------------------------------
function endFriendlyEncount(townZone, townUser) {
    const tusd = {
        user: {
            townUserState: townUserState_1.TOWN_USER_STATE.IN_TOWN,
        },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.endFriendlyEncount = endFriendlyEncount;
// ----------------------------------------------------------------------------
function updateFriendlyBattle(townZone, townUser) {
    const tusd = {
        user: {
            townUserState: townUserState_1.TOWN_USER_STATE.IN_FRIENDLY_BATTLE,
        },
    };
    setImmediate(() => {
        updateTownUserSyncData(townZone, townUser, tusd);
    });
}
exports.updateFriendlyBattle = updateFriendlyBattle;
// ----------------------------------------------------------------------------
function updateTownUserSyncData(townZone, townUser, syncData) {
    lodash_1.default.merge(townUser.syncData, syncData);
    const lobbyGroups = townZone.getLobbyGroups(townUser);
    const packet = {
        userId: townUser.userId,
        syncData: townUser.syncData,
    };
    mhttp_1.default.lobbypx.notifyTownUserSyncDataUpdate(lobbyGroups, packet);
}
exports.updateTownUserSyncData = updateTownUserSyncData;
// ----------------------------------------------------------------------------
function notifyTradeAllUpdated(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTownTradeAllChanged(msg);
        }
    });
}
exports.notifyTradeAllUpdated = notifyTradeAllUpdated;
// ----------------------------------------------------------------------------
function notifySmuggleAllUpdated(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTownSmuggleAllChanged(msg);
        }
    });
}
exports.notifySmuggleAllUpdated = notifySmuggleAllUpdated;
// ----------------------------------------------------------------------------
function showSocialAniInstant(townZone, townUser, socialAniCmsId) {
    setImmediate(() => {
        townZone.onShowSocialAniInstant(townUser, socialAniCmsId);
    });
}
exports.showSocialAniInstant = showSocialAniInstant;
// ----------------------------------------------------------------------------
function showEmoticonInstant(townZone, townUser, emoticonCmsId) {
    setImmediate(() => {
        townZone.onShowEmoticonInstant(townUser, emoticonCmsId);
    });
}
exports.showEmoticonInstant = showEmoticonInstant;
// ----------------------------------------------------------------------------
function notifyTownInvested(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTownInvested(msg);
        }
    });
}
exports.notifyTownInvested = notifyTownInvested;
// ----------------------------------------------------------------------------
function notifyDevelopmentNationSharePointChanged(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onDevelopmentNationSharePointChanged(msg);
        }
    });
}
exports.notifyDevelopmentNationSharePointChanged = notifyDevelopmentNationSharePointChanged;
// ----------------------------------------------------------------------------
function notifyInvestmentSessionClosed(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onInvestmentSessionClosed(msg);
        }
    });
}
exports.notifyInvestmentSessionClosed = notifyInvestmentSessionClosed;
// ----------------------------------------------------------------------------
function notifyTownMayorTaxChanged(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTownMayorTaxChanged(msg);
        }
    });
}
exports.notifyTownMayorTaxChanged = notifyTownMayorTaxChanged;
// ----------------------------------------------------------------------------
function notifyTownMayorShipyardTaxChanged(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTownMayorShipyardTaxChanged(msg);
        }
    });
}
exports.notifyTownMayorShipyardTaxChanged = notifyTownMayorShipyardTaxChanged;
// ----------------------------------------------------------------------------
function notifyDevTownNationSharePointChanged(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onDevTownNationSharePointChanged(msg);
        }
    });
}
exports.notifyDevTownNationSharePointChanged = notifyDevTownNationSharePointChanged;
// ----------------------------------------------------------------------------
function notifyDevTownMayorChanged(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTownMayorChanged(msg);
        }
    });
}
exports.notifyDevTownMayorChanged = notifyDevTownMayorChanged;
// ----------------------------------------------------------------------------
function notifySomeTradeGoodsUpdated(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onSomeTradeGoodsUpdated(msg);
        }
    });
}
exports.notifySomeTradeGoodsUpdated = notifySomeTradeGoodsUpdated;
// ----------------------------------------------------------------------------
function notifyTradeCrazeEventBudgetChanged(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTradeCrazeEventBudgetChanged(msg);
        }
    });
}
exports.notifyTradeCrazeEventBudgetChanged = notifyTradeCrazeEventBudgetChanged;
// ----------------------------------------------------------------------------
function notifyTradeUnpopularChanged(townZones, msg) {
    setImmediate(() => {
        for (const key of Object.keys(townZones)) {
            townZones[key].onTradeUnpopularChanged(msg);
        }
    });
}
exports.notifyTradeUnpopularChanged = notifyTradeUnpopularChanged;
//# sourceMappingURL=libTown.js.map