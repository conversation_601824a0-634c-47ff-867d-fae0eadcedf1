// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';
import moment from 'moment';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import tuBuyTaxFreePermit from '../../../mysqllib/txn/tuBuyTaxFreePermit';
import { LobbyService } from '../../server';
import { Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import UserPoints, { CashPayment, LGCashParam, Mileage, PointChange } from '../../userPoints';
import * as mutil from '../../../motiflib/mutil';
import {
  CASH_SHOP_SALE_POINT_TYPE,
  CASH_SHOP_SALE_TYPE,
  CASH_SHOP_PRODUCT_TYPE,
  getCashShopMileageBonus,
  isExistWorldBuffTimeField,
  getWorldBuffAddTime,
} from '../../../cms/cashShopDesc';
import { UNBUYABLE_REASON, RestrictedProduct } from '../../userCashShop';
import { SECONDS_PER_DAY, SECONDS_PER_HOUR } from '../../../formula';
import { PrData } from '../../../motiflib/gameLog';
import * as displayNameUtil from '../../../motiflib/displayNameUtil';
import { ClientPacketHandler } from '../index';
import { REWARD_TYPE } from '../../../cms/rewardDesc';
import mconf from '../../../motiflib/mconf';

// ----------------------------------------------------------------------------
// [패킷 용도 간단 설명]
// ----------------------------------------------------------------------------

const rsn = 'buy_tax_free_permit';
const add_rsn = null;

interface RequestBody {
  cmsId: number;
  cashShopCmsId?: number;
  bPermitExchange?: boolean;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BuyTaxFreePermit implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body: RequestBody = packet.bodyObj;
    const { cmsId, cashShopCmsId, bPermitExchange } = body;

    const { userDbConnPoolMgr, nationIntimacy } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const taxFreePermitCms = cms.TaxFreePermit[cmsId];
    if (!taxFreePermitCms) {
      throw new MError('invalid-cms-id', MErrorCode.INVALID_REQ_BODY_BUY_TAX_FREE_PERMIT_CMS_ID, {
        cmsId,
      });
    }

    let pointChanges: PointChange[];
    let cashPayments: CashPayment[];
    let mileageChanges: Mileage[];
    let newExpirationTimeUtc: number = taxFreePermitCms.expiredTimeValue;

    const curTimeUtc = mutil.curTimeUtc();
    let expiredRestrictedProducts: Set<number>;
    let restrictedProductChange: RestrictedProduct;
    const pr_data: PrData[] = [];

    if (cashShopCmsId) {
      // cashShop 구매
      const cashShopCms = cms.CashShop[cashShopCmsId];
      if (
        !cashShopCms ||
        cashShopCms.salePointType !== CASH_SHOP_SALE_POINT_TYPE.POINT ||
        cashShopCms.productType !== CASH_SHOP_PRODUCT_TYPE.TAX_FREE_PERMIT
      ) {
        throw new MError(
          'invalid-cms-id',
          MErrorCode.INVALID_REQ_BODY_CASH_SHOP_BUY_WITHOUT_PURCHASE,
          {
            cmsId,
          }
        );
      }

      const curTimeUtc = mutil.curTimeUtc();
      expiredRestrictedProducts = user.userCashShop.getExpiredRestrictedProducts(curTimeUtc);

      // 구매할 수 있는 상품인지 검사
      const curDate = new Date(curTimeUtc * 1000);
      const restrictedProducts = user.userCashShop.getRestrictedProducts();
      const unbuyableReason = user.userCashShop.isBuyableProduct(
        user,
        cashShopCmsId,
        curDate,
        expiredRestrictedProducts
      );
      if (unbuyableReason !== UNBUYABLE_REASON.BUYABLE) {
        throw new MError('unbuyable-product', MErrorCode.UNBUYABLE_CASH_SHOP_PRODUCT, {
          unbuyableReason,
          curDate,
          restrictedProducts,
        });
      }

      // build restrictedProductChange
      if (cashShopCms.saleType !== CASH_SHOP_SALE_TYPE.UNLIMITED) {
        if (expiredRestrictedProducts.has(cashShopCmsId) || !restrictedProducts[cashShopCmsId]) {
          restrictedProductChange = {
            cmsId: cashShopCmsId,
            amount: 1,
            lastBuyingTimeUtc: curTimeUtc,
          };
        } else {
          restrictedProductChange = {
            cmsId: cashShopCmsId,
            amount: restrictedProducts[cashShopCmsId].amount + 1,
            lastBuyingTimeUtc: curTimeUtc,
          };
        }
      } else if (cmsEx.isCashShopPreviousId(cashShopCmsId) && !restrictedProducts[cashShopCmsId]) {
        restrictedProductChange = {
          cmsId: cashShopCmsId,
          amount: 1,
          lastBuyingTimeUtc: curTimeUtc,
        };
      }

      const lgCashParma: LGCashParam = {};
      if (cashShopCms.salePointId === cmsEx.RedGemPointCmsId) {
        lgCashParma.productId =
          mconf.binaryCode === 'GL' ? `uwogl_${cashShopCms.id}` : `uwo_${cashShopCms.id}`;
      } else {
        lgCashParma.itemId = rsn;
      }
      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [{ cmsId: cashShopCms.salePointId, cost: cashShopCms.salePointVal }],
        bPermitExchange,
        lgCashParma,
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;
      pr_data.push({ type: cashShopCms.salePointId, amt: cashShopCms.salePointVal });

      if (cashShopCms.durationDays) {
        newExpirationTimeUtc = cashShopCms.durationDays * SECONDS_PER_DAY;
      } else if (cashShopCms.durationHours) {
        newExpirationTimeUtc = cashShopCms.durationHours * SECONDS_PER_HOUR;
      } else {
        throw new MError(
          'invalid-duration-days-and-duration-hours',
          MErrorCode.INVALID_DURATION_DAYS_AND_DURATION_HOURS,
          {
            cmsId,
          }
        );
      }

      // cash2 로 구매할 경우 마일리지 적립.
      const mileageBonus = getCashShopMileageBonus(cashShopCms, cashShopCms.salePointVal);
      if (mileageBonus !== undefined && mileageBonus !== 0) {
        const mileagePointCms = cms.Point[cmsEx.CashShopMileage];
        const curUserMileage = user.userPoints.getMileage(curTimeUtc);
        if (curUserMileage + mileageBonus > mileagePointCms.hardCap) {
          throw new MError('mileage-exceeds-hard-cap', MErrorCode.EXCEEDS_HARD_CAP, {
            curUserMileage,
            mileageBonus,
            hardCap: mileagePointCms.hardCap,
            cashShopCmsId,
          });
        }

        mileageChanges = user.userPoints.buildMileageChanges(mileageBonus, curTimeUtc);
      }
    } else {
      // 왕궁에서 구매
      user.userState.ensureInTown();

      user.userContentsTerms.ensureBuildingContentsUnlock(
        cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.PALACE_BUY_TAX_FREE_PERMIT,
        user
      );

      if (cmsEx.getTaxFreePermitCashShopCmsId(cmsId)) {
        throw new MError(
          'only-can-buy-in-cash-shop',
          MErrorCode.INVALID_REQ_BODY_BUY_TAX_FREE_PERMIT_CMS_ID,
          {
            cmsId,
          }
        );
      }

      const userTownCmsId = user.userTown.getTownCmsId();
      const townCms = cms.Town[userTownCmsId];
      if (
        townCms.ownType !== cmsEx.TOWN_OWN_TYPE.CAPITAL_TOWN ||
        taxFreePermitCms.nationId !== townCms.nationId
      ) {
        throw new MError('cant-buy-in-town', MErrorCode.CANT_BUY_TAX_FREE_PERMIT_IN_TOWN, {
          cmsId,
          userTownCmsId,
        });
      }

      if (
        user.nationCmsId &&
        user.nationCmsId !== townCms.nationId &&
        taxFreePermitCms.intimacyTerms &&
        nationIntimacy.getIntimacyValue(user.nationCmsId, townCms.nationId) <
          taxFreePermitCms.intimacyTerms
      ) {
        throw new MError(
          'not-enough-intimacy',
          MErrorCode.CANT_BUY_TAX_FREE_PERMIT_BECAUSE_NOT_ENOUGH_INTIMACY,
          {
            cmsId,
            userTownCmsId,
          }
        );
      }
      if (
        taxFreePermitCms.reputationTerms &&
        user.userReputation.get(townCms.nationId, curTimeUtc) < taxFreePermitCms.reputationTerms
      ) {
        throw new MError(
          'not-enough-reputation',
          MErrorCode.CANT_BUY_TAX_FREE_PERMIT_BECAUSE_NOT_ENOUGH_REPUTATION,
          {
            cmsId,
            userTownCmsId,
          }
        );
      }

      const cost = Math.ceil(
        taxFreePermitCms.basisPointVal / (cms.Const.TaxFreePermitPointVal.value / user.level)
      );
      const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
        [{ cmsId: taxFreePermitCms.basisPointId, cost }],
        bPermitExchange,
        { itemId: rsn },
        true
      );
      pointChanges = pcChanges.pointChanges;
      cashPayments = pcChanges.cashPayments;
      pr_data.push({ type: taxFreePermitCms.basisPointId, amt: cost });

      newExpirationTimeUtc = taxFreePermitCms.expiredTimeValue;
    }

    const oldExpirationTimeUtc = user.userTrade.getTaxFreePermitExpiration(cmsId);
    if (oldExpirationTimeUtc && oldExpirationTimeUtc > curTimeUtc) {
      newExpirationTimeUtc += oldExpirationTimeUtc;
    } else {
      newExpirationTimeUtc += curTimeUtc;
    }

    // 90일 이상 누적 불가. (캐시샵, 왕궁 둘 다)
    if (
      newExpirationTimeUtc - curTimeUtc >=
      cms.Const.CashShopDurationDaysLimit.value * SECONDS_PER_DAY
    ) {
      throw new MError(
        'exceeds-expiration-time',
        MErrorCode.EXCEEDS_EXPIRATION_TIME_OF_CASH_SHOP_PRODUCT,
        {
          cmsId,
          newExpirationTimeUtc,
          curTimeUtc,
        }
      );
    }

    // 해당 기획 제거됨
    // 면세증은 기간제로 정해진 기간에만 효력이 발생한다.
    // 기간제의 시작은 획득 시점부터 바로 적용된다.
    // 기간제의 종료는 종료되는 시점 기준 다음날 0시이다.
    // let newExpirationDate = new Date(newExpirationTimeUtc * 1000);
    // let date = newExpirationDate.getDate();

    // if (
    //   newExpirationDate.valueOf() !==
    //   new Date(newExpirationDate.getFullYear(), newExpirationDate.getMonth(), date).valueOf()
    // ) {
    //   newExpirationDate = new Date(
    //     newExpirationDate.getFullYear(),
    //     newExpirationDate.getMonth(),
    //     date + 1
    //   );
    // }

    // newExpirationTimeUtc = Math.floor(newExpirationDate.valueOf() / 1000);

    const sync: Sync = {};

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return user.userPoints
      .tryConsumeCashs(cashPayments, sync, user, { user, rsn, add_rsn, exchangeHash })
      .then(() => {
        return tuBuyTaxFreePermit(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          pointChanges,
          cmsId,
          newExpirationTimeUtc,
          expiredRestrictedProducts,
          restrictedProductChange,
          mileageChanges
        );
      })
      .then(() => {
        user.userTrade.setTaxFreePermitExpirationWithGlogForAdding(
          cmsId,
          newExpirationTimeUtc,
          {
            user,
            rsn,
            add_rsn,
            exchangeHash,
          },
          pr_data
        );

        _.merge<Sync, Sync>(sync, {
          add: {
            taxFreePermits: {
              [cmsId]: {
                cmsId,
                expirationTimeUtc: newExpirationTimeUtc,
              },
            },
          },
        });

        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyPointChanges(pointChanges, { user, rsn, add_rsn })
        );

        _.merge<Sync, Sync>(
          sync,
          user.userPoints.applyMileageChanges(mileageChanges, { user, rsn, add_rsn })
        );

        if (expiredRestrictedProducts && expiredRestrictedProducts.size > 0) {
          const arr = Array.from(expiredRestrictedProducts);
          user.userCashShop.deleteRestrictedProducts(Array.from(expiredRestrictedProducts));
          sync.remove = {
            cashShopRestrictedProducts: arr.map(String),
          };
        }
        if (restrictedProductChange) {
          user.userCashShop.setRestrictedProduct(restrictedProductChange);
          sync.add.cashShopRestrictedProducts = {
            [restrictedProductChange.cmsId]: restrictedProductChange,
          };
        }

        // glog
        if (cashShopCmsId) {
          const cashShopCms = cms.CashShop[cashShopCmsId];
          user.glog('cash_shop_buy', {
            rsn,
            add_rsn,
            category: cashShopCms.productCategory,
            id: cmsId,
            name: displayNameUtil.getCashShopProductDisplayName(cashShopCms),
            type: cashShopCms.productType,
            limit_type: cashShopCms.saleType,
            limit_period: cashShopCms.saleTypeVal ? cashShopCms.saleTypeVal : null,
            remain_cnt: null,
            amt: 1,
            expiredate: moment(new Date(newExpirationTimeUtc * 1000)).format('YYYY-MM-DD HH:mm:ss'),
            buff_buy_type: null,
            pr_data,
            reward_data: [
              {
                type: REWARD_TYPE[REWARD_TYPE.TAX_FREE_PERMIT],
                id: taxFreePermitCms.id,
                uid: null,
                amt: 1,
              },
            ],
            exchange_hash: exchangeHash,
          });
        }

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }
}
