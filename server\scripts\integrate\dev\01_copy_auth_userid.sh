#!/bin/bash

CWD="$(dirname "$0")"

if [ ! -f $CWD/_config.sh ]; then
	echo "Please create a custom '_config.sh' file at '$CWD' directory."
	exit 1
fi

if [ ! -f $CWD/_query.sh ]; then
	echo "Please create a custom '_query.sh' file at '$CWD' directory."
	exit 1
fi

if [ -z "$1" ]
  then
    echo "No argument worldId"
    exit 1
fi

if [ -z "$2" ]
  then
    echo "No argument backup database name"
    exit 1
fi

SECONDS=0

source $CWD/_config.sh
source $CWD/_query.sh

WORLD_ID=$1
BACKUP_DB_NAME=$2
main() 
{
  echo "===== CREATE TABLE integration_auth_users"
  q="
    DROP TABLE IF EXISTS integration_auth_users;

    CREATE TABLE integration_auth_users (
      userId INT NOT NULL,
      PRIMARY KEY (userId)
    ) 
    ENGINE=InnoDB DEFAULT
    CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"


  echo "===== ALTER ADD INDEX uwo_auth.a_world_users"
  q="
    ALTER TABLE a_world_users
      ADD INDEX IDX_world_id (worldId);
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"


  echo "===== SELECT uwo_auth.a_world_users"
  q="
    SELECT userId
      FROM a_world_users
    WHERE worldId=\"${WORLD_ID}\";
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"
  userIds=(${QUERY_RESULT})

  let multipleUserIds
  for userId in ${userIds[@]}
  do
    multipleUserIds+=\($userId\)\,
  done

  echo "===== INSERT integration_auth_users"
  q="
    INSERT INTO integration_auth_users(userId)
    VALUES ${multipleUserIds:0:-1};
  "
  queryExecute "${USER_DB_HOST}" "${USER_DB_USER}" "${USER_DB_PASSWD}" "${BACKUP_DB_NAME}" "${q}"



  echo "===== ALTER DROP INDEX uwo_auth.a_world_users"
  q="
    ALTER TABLE a_world_users
      DROP INDEX IDX_world_id;
  "
  queryExecute "${AUTH_DB_HOST}" "${AUTH_DB_USER}" "${AUTH_DB_PASSWD}" "${AUTH_DB_NAME}" "${q}"
}



main "$@"; 

duration=$SECONDS
echo "$((duration / 60)) minutes and $((duration % 60)) seconds elapsed."
exit
