import { LineGamesApiClient } from '../../motiflib/mhttp/linegamesApiClient';
import { ProxyAdminHealthApiClient } from './adminHealthApiClient';
import mconf from '../../motiflib/mconf';
import { AdminProxyLobbyApiClient } from './adminLobbyApiClient';
import { AdminProxySailApiClient } from './adminSailApiClient';
import { AdminProxyRealmApiClient } from './adminRealmApiClient';

export namespace AdminHttpUtils {
  export const lgd = new LineGamesApiClient();
  export const proxyHealth = new ProxyAdminHealthApiClient();
  export const proxyLobby = new AdminProxyLobbyApiClient();
  export const proxySail = new AdminProxySailApiClient();
  export const proxyRealm = new AdminProxyRealmApiClient();

  export function init() {
    if (mconf.http.lgd) {
      lgd.init(mconf.http.lgd.url);
    }
  }
}
