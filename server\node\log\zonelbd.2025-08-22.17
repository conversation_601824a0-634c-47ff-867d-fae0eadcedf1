{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755849877,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755849877,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T08:04:37.335Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755849877,"afterTs":1755849877,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T08:04:37.340Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755850477,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755850477,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T08:14:37.336Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755850477,"afterTs":1755850477,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T08:14:37.338Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755851077,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755851077,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T08:24:37.336Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755851077,"afterTs":1755851077,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T08:24:37.338Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755851677,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755851677,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T08:34:37.337Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755851677,"afterTs":1755851677,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T08:34:37.338Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T08:37:34.675Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.043","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:37:34.676Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T08:37:35.416Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.856","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:37:35.416Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T08:37:36.510Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T08:37:36.510Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T08:37:36.754Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T08:37:36.754Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T08:37:36.756Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:37:36.756Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:37:36.756Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T08:37:36.756Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:37:36.757Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:37:36.757Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T08:37:36.757Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T08:37:36.757Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:37:36.757Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:37:36.757Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:37:36.757Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:37:36.757Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:37:36.758Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:37:36.758Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T08:37:41.114Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:38:09.351Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T08:38:09.353Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T08:38:09.605Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T08:38:09.605Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T08:38:09.606Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T08:38:09.608Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T08:38:09.620Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T08:38:09.621Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T08:38:09.634Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T08:38:09.634Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T08:38:09.648Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.655Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.691Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.701Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.710Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.719Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.730Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.743Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.756Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.766Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:38:09.778Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T08:38:09.790Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:38:09.792Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T08:38:10.942Z"}
{"url":"/unregisterServerd","status":"200","response-time":"98.743","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:38:10.943Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755851890,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T08:38:10.947Z"}
{"url":"/registerServerd","status":"200","response-time":"3.666","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:38:10.949Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T08:38:11.611Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.036","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:38:11.612Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755851891,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T08:38:11.614Z"}
{"url":"/registerServerd","status":"200","response-time":"1.151","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:38:11.615Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T08:38:19.792Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T08:45:28.960Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.958","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:45:28.960Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T08:45:30.147Z"}
{"url":"/unregisterServerd","status":"200","response-time":"0.922","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:45:30.147Z"}
{"level":"info","message":"[!] server is stopping: type=zonelbd, signal=SIGINT","timestamp":"2025-08-22T08:45:30.835Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T08:45:30.835Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T08:45:31.654Z"}
{"level":"info","message":"unregister all serverd","timestamp":"2025-08-22T08:45:31.654Z"}
{"level":"info","message":"redis pool (town-lb-redis) destroyed","timestamp":"2025-08-22T08:45:31.656Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) destroyed","timestamp":"2025-08-22T08:45:31.656Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:45:31.657Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:45:31.657Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:45:31.657Z"}
{"name":"town-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:45:31.657Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T08:45:31.657Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T08:45:31.657Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:45:31.658Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:45:31.658Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:45:31.658Z"}
{"name":"ocean-lb-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:45:31.658Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T08:45:31.658Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T08:45:31.658Z"}
{"environment":"development","type":"zonelbd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"zonelbd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T08:45:37.292Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:46:09.314Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T08:46:09.316Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T08:46:09.539Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T08:46:09.540Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T08:46:09.541Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T08:46:09.542Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T08:46:09.550Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (town-lb-redis) initializing ...","timestamp":"2025-08-22T08:46:09.550Z"}
{"level":"info","message":"redis pool (town-lb-redis) initialized","timestamp":"2025-08-22T08:46:09.558Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"level":"info","message":"redis pool (ocean-lb-redis) initializing ...","timestamp":"2025-08-22T08:46:09.559Z"}
{"level":"info","message":"redis pool (ocean-lb-redis) initialized","timestamp":"2025-08-22T08:46:09.566Z"}
{"path":"/decUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.571Z"}
{"path":"/createZone","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.594Z"}
{"path":"/findOcean","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.604Z"}
{"path":"/findTown","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.612Z"}
{"path":"/getServerdUrlFromChannelId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.619Z"}
{"path":"/incUserCount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.629Z"}
{"path":"/registerServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.638Z"}
{"path":"/unregisterServerd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.646Z"}
{"path":"/updateLobbydPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.655Z"}
{"path":"/updateServerdPing","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T08:46:09.665Z"}
{"bindAddress":"0.0.0.0","port":10600,"level":"info","message":"start listening ...","timestamp":"2025-08-22T08:46:09.678Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T08:46:09.680Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","level":"info","message":"unregisterServerd success OCEAN","timestamp":"2025-08-22T08:46:10.003Z"}
{"url":"/unregisterServerd","status":"200","response-time":"78.743","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:46:10.004Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10800","curDate":1755852370,"level":"info","message":"registerServerd success OCEAN","timestamp":"2025-08-22T08:46:10.007Z"}
{"url":"/registerServerd","status":"200","response-time":"2.000","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:46:10.008Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","level":"info","message":"unregisterServerd success TOWN","timestamp":"2025-08-22T08:46:10.360Z"}
{"url":"/unregisterServerd","status":"200","response-time":"1.089","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:46:10.361Z"}
{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","curDate":1755852370,"level":"info","message":"registerServerd success TOWN","timestamp":"2025-08-22T08:46:10.363Z"}
{"url":"/registerServerd","status":"200","response-time":"0.927","mcode":0,"level":"info","message":"zonelbd-req","timestamp":"2025-08-22T08:46:10.363Z"}
{"interval":2000,"timeout":10000,"level":"info","message":"start PingChecker ...","timestamp":"2025-08-22T08:46:19.681Z"}
{"body":{"id":"lobbyd.0@DESKTOP-2FFOGVN","curTimeUtc":1755853184,"worldId":"UWO-GL-01","userCount":0,"botCount":0},"curTs":1755853184,"level":"info","message":"[PING] recv","timestamp":"2025-08-22T08:59:44.278Z"}
{"id":"lobbyd.0@DESKTOP-2FFOGVN","worldId":"UWO-GL-01","online":1,"stop":false,"curTs":1755853184,"afterTs":1755853184,"level":"info","message":"[PING] recv ack","timestamp":"2025-08-22T08:59:44.280Z"}
