// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import assert from 'assert';
import { Promise as promise } from 'bluebird';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import mlog from '../../../motiflib/mlog';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { Resp, Sync } from '../../type/sync';
import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import {
  PieceSwapDesc,
  PIECE_TYPE,
  PIECE_SWAP_TYPE,
  getPieceGradeFromMateGrade,
} from '../../../cms/pieceSwapDesc';
import { RewardAndPaymentSpec, RNP_TYPE } from '../../UserChangeTask/rewardAndPaymentChangeSpec';
import {
  ActualGain,
  Changes,
  CHANGE_TASK_REASON,
  CHANGE_TASK_RESULT,
  TryData,
  UserChangeTask,
} from '../../UserChangeTask/userChangeTask';
import { opAddItem } from '../../UserChangeTask/userChangeOperator';
import { ClientPacketHandler } from '../index';
import { ItemDesc, ITEM_TYPE } from '../../../cms/itemDesc';
import { CostData, RewardData } from '../../../motiflib/gameLog';

// ----------------------------------------------------------------------------
// 점술관에서 항해사/선박 조각을 교환.
// ----------------------------------------------------------------------------

const rsn = 'mantic_swap_piece';
const add_rsn = null;

interface Reagent {
  itemCmsId: number;
  amount: number;
}

interface RequestBody {
  cmsId: number; // CMS.PieceSwap.id
  reagents: Reagent[];
}

interface Response extends Resp {
  gains: ActualGain[]; // 아이템 소모 내역도 같이 전달되는 것 참고.
}

// ----------------------------------------------------------------------------
export class Cph_Town_ManticSwapPiece implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    user.userState.ensureInTown();

    const body: RequestBody = packet.bodyObj;
    const { cmsId, reagents } = body;

    const pieceSwapCms = cms.PieceSwap[cmsId];
    if (!pieceSwapCms) {
      throw new MError('invalid-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
        pieceSwapCmsId: cmsId,
      });
    }
    const pieceType = pieceSwapCms.pieceType;
    if (pieceType === PIECE_TYPE.MATE) {
      user.userContentsTerms.ensureBuildingContentsUnlock(
        cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.MANTIC_SWAP_PIECE_MATE,
        user
      );
    } else if (pieceType === PIECE_TYPE.PLAN) {
      user.userContentsTerms.ensureBuildingContentsUnlock(
        cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.MANTIC_SWAP_PIECE_SHIP_BLUEPRINT,
        user
      );
    } else if (pieceType === PIECE_TYPE.GACHA_TICKET) {
      user.userContentsTerms.ensureBuildingContentsUnlock(
        cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.MANTIC_SWAP_PIECE_GACHA_TICKET,
        user
      );
    } else if (pieceType === PIECE_TYPE.TOOL) {
      user.userContentsTerms.ensureBuildingContentsUnlock(
        cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.MANTIC_SWAP_PIECE_TOOL,
        user
      );
    } else if (pieceType === PIECE_TYPE.TOKEN) {
      user.userContentsTerms.ensureBuildingContentsUnlock(
        cmsEx.BUILDING_CONTENTS_UNLOCK_CMS_ID.MANTIC_SWAP_PIECE_TOKEN,
        user
      );
    } else {
      assert.fail('undefined-pieceType-for-building-contents-unlock');
    }

    // 우선 묶음으로 만든다.
    const bundles = _buildBundles(pieceSwapCms.pieceSwapVal, reagents);

    if (bundles.length === 0) {
      // 아무것도 교환될게 없다면 에러처리
      throw new MError('nothing-to-swap', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
        reqBody: body,
        bundleSize: pieceSwapCms.pieceSwapVal,
      });
    }
    if (bundles.length > cms.Define.MaxManticPieceSwapBundleCount) {
      // 성능 문제 관련해, 최대 개수가 얼마 정도가 적당한지 확인 필요.
      // 리워드 시스템을 사용하기 때문에 애매하긴 함. CMS.Reward,... 테이블 입력도 최적화(?)가 필요할듯?
      throw new MError('exceed-max-bundle-count', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
        reqBody: body,
        bundleSize: pieceSwapCms.pieceSwapVal,
        bundleCount: bundles.length,
        maxBundleCount: cms.Define.MaxManticPieceSwapBundleCount,
      });
    }

    return Promise.resolve()
      .then(() => {
        let _ensureValidItem: (itemCmsId: number) => void;

        const pieceType = pieceSwapCms.pieceType;
        if (pieceType === PIECE_TYPE.MATE) {
          _ensureValidItem = _buildPieceTypeMatePieceValidator(pieceSwapCms, user);
        } else if (pieceType === PIECE_TYPE.PLAN) {
          _ensureValidItem = _buildPieceTypeShipPlanValidator(pieceSwapCms, user);
        } else if (pieceType === PIECE_TYPE.GACHA_TICKET) {
          _ensureValidItem = _buildPieceTypeGachaTicketValidator(pieceSwapCms, user);
        } else if (pieceType === PIECE_TYPE.TOOL) {
          _ensureValidItem = _buildPieceTypeToolValidator(pieceSwapCms, user);
        } else if (pieceType === PIECE_TYPE.TOKEN) {
          _ensureValidItem = _buildPieceTypeTokenValidator(pieceSwapCms, user);
        } else {
          assert.fail(`item cannot be verified. undefined pieceType: ${pieceType}, ${cmsId}`);
        }

        const _ensureItemEnough = (itemCmsId: number, amountToExchange: number): void => {
          const curCount = user.userInven.itemInven.getCount(itemCmsId);
          if (curCount < amountToExchange) {
            throw new MError('not-enough-item', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
              itemCmsId,
              curCount,
              amountToExchange,
            });
          }
        };

        const forValidation: Map<number, number> = new Map();
        for (const bundle of bundles) {
          for (const [itemCmsId, amount] of bundle.reagents) {
            _accumMap(forValidation, itemCmsId, amount);
          }
        }
        forValidation.forEach((amountToExchange, itemCmsId) => {
          _ensureValidItem(itemCmsId);
          _ensureItemEnough(itemCmsId, amountToExchange);
        });

        return _processSwap(pieceSwapCms, user, bundles).then((ret) => {
          const resp: Response = { sync: ret.sync, gains: ret.gains };
          return resp;
        });
      })
      .then((resp: Response) => {
        return user.sendJsonPacket<Response>(packet.seqNum, packet.type, resp);
      });
  }
}

/**
 * @param pieceSwapCms CMS.PieceSwap.pieceType이 항해사인 것
 */
function _buildPieceTypeMatePieceValidator(
  pieceSwapCms: PieceSwapDesc,
  user: User
): (itemCmsId: number) => void {
  const swapType = pieceSwapCms.swapType;
  if (swapType === PIECE_SWAP_TYPE.BASIC) {
    return (itemCmsId) => _ensureMatePieceSwapTypeBasic(pieceSwapCms, itemCmsId, user);
  } else if (swapType === PIECE_SWAP_TYPE.ADVANCED) {
    return (itemCmsId) => _ensureMatePieceSwapTypeAdvanced(pieceSwapCms, itemCmsId);
  } else {
    assert.fail(`item cannot be verified. undefined swapType: ${swapType}, ${pieceSwapCms.id}`);
  }
}

/**
 * @param pieceSwapCms CMS.PieceSwap.pieceType이 항해사이고 CMS.PieceSwap.swapType이 기본인 경우
 */
function _ensureMatePieceSwapTypeBasic(
  pieceSwapCms: PieceSwapDesc,
  itemCmsId: number,
  user: User
): void {
  const mateCms = cmsEx.getMateCmsFromAwakenPieceItem(itemCmsId);
  if (!mateCms) {
    throw new MError(
      'can-not-get-mate-cms-from-item-cms-id',
      MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE,
      {
        pieceSwapCmsId: pieceSwapCms.id,
        itemCmsId,
      }
    );
  }

  // https://jira.line.games/browse/UWO-21445
  // 최증 승급 완료 제약을 제거함.

  // const userMate = user.userMates.getMate(mateCms.id);
  // if (!userMate) {
  //   throw new MError('has-not-mate', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
  //     pieceSwapCmsId: pieceSwapCms.id,
  //     itemCmsId,
  //     mateCmsId: mateCms.id,
  //   });
  // }

  // // 최대 승급 여부
  // const awakenLv = userMate.getAwakenLv();
  // if (!Number.isInteger(awakenLv) || awakenLv < cms.Const.AwakenMaxLv.value) {
  //   throw new MError('invalid-awaken-lv', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
  //     pieceSwapCmsId: pieceSwapCms.id,
  //     itemCmsId,
  //     mateCmsId: mateCms.id,
  //     awakenLv,
  //   });
  // }

  const pieceGrade = pieceSwapCms.pieceGrade;
  if (pieceGrade === undefined || pieceGrade !== getPieceGradeFromMateGrade(mateCms.mateGrade)) {
    throw new MError('invalid-grade', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      pieceSwapCmsId: pieceSwapCms.id,
      itemCmsId,
      pieceGrade,
      mateCmsId: mateCms.id,
      mateGrade: mateCms.mateGrade,
    });
  }
}

/**
 * @param pieceSwapCms CMS.PieceSwap.pieceType이 항해사이고 CMS.PieceSwap.swapType이 고급인 경우
 * CMS.PieceSwap.exchangableItemId 가 itemCmsId를 포함하는지 확인
 */
function _ensureMatePieceSwapTypeAdvanced(pieceSwapCms: PieceSwapDesc, itemCmsId: number): void {
  if (!pieceSwapCms.exchangableItemId.includes(itemCmsId)) {
    throw new MError('non-exchangable-item-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      cmsId: pieceSwapCms.id,
      pieceType: pieceSwapCms.pieceType,
      swapType: pieceSwapCms.swapType,
      exchangableItemId: pieceSwapCms.exchangableItemId,
      itemCmsId,
    });
  }
}

/**
 * @param pieceSwapCms CMS.PieceSwap.pieceType이 선박 도면인 것
 */
function _buildPieceTypeShipPlanValidator(
  pieceSwapCms: PieceSwapDesc,
  user: User
): (itemCmsId: number) => void {
  const swapType = pieceSwapCms.swapType;
  if (swapType === PIECE_SWAP_TYPE.BLUEPRINT) {
    return (itemCmsId) => _ensureShipPlanSwapType(pieceSwapCms, itemCmsId, user);
  } else {
    assert.fail(`item cannot be verified. undefined swapType: ${swapType}, ${pieceSwapCms.id}`);
  }
}

/**
 * @param pieceSwapCms CMS.PieceSwap.pieceType이 도면이고 CMS.PieceSwap.swapType이 도면 계약인 경우
 */
function _ensureShipPlanSwapType(pieceSwapCms: PieceSwapDesc, itemCmsId: number, user: User): void {
  const shipBlueprintCms = cmsEx.getShipBlueprintCmsFromMaterialBpId(itemCmsId);
  if (!shipBlueprintCms) {
    throw new MError(
      'can-not-get-ship-blueprint-cms-from-item-cms-id',
      MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE,
      { pieceSwapCmsId: pieceSwapCms.id, itemCmsId }
    );
  }

  // const userShipBlueprint = user.userShipBlueprints.getUserShipBlueprint(shipBlueprintCms.id);
  // if (!userShipBlueprint) {
  //   throw new MError('has-not-blueprint', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
  //     pieceSwapCmsId: pieceSwapCms.id,
  //     shipBlueprintCmsId: shipBlueprintCms.id,
  //   });
  // }

  // const MAX_BP_LEVEL = cmsEx.getMaxBpLevel();
  // const bpLevel = userShipBlueprint.level;
  // if (MAX_BP_LEVEL === undefined || bpLevel < MAX_BP_LEVEL) {
  //   throw new MError('invalid-blueprint-lv', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
  //     pieceSwapCmsId: pieceSwapCms.id,
  //     itemCmsId,
  //     shipBlueprintCmsId: shipBlueprintCms.id,
  //     bpLevel,
  //     MAX_BP_LEVEL,
  //   });
  // }

  const pieceSwapBlueprintMaxLv = cms.Const.PieceSwapBlueprintMaxLv.value;
  if (
    !pieceSwapCms.pieceTier ||
    shipBlueprintCms.tier > pieceSwapBlueprintMaxLv ||
    shipBlueprintCms.tier !== pieceSwapCms.pieceTier
  ) {
    throw new MError('invalid-ship-tier', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      pieceSwapCmsId: pieceSwapCms.id,
      itemCmsId,
      shipTier: shipBlueprintCms.tier,
      pieceSwapBlueprintMaxLv,
    });
  }
}

function _buildPieceTypeGachaTicketValidator(
  pieceSwapCms: PieceSwapDesc,
  user: User
): (itemCmsId: number) => void {
  const swapType = pieceSwapCms.swapType;
  if (swapType === PIECE_SWAP_TYPE.GACHA_TICKET) {
    return (itemCmsId) => _ensureGachaTicketSwapType(pieceSwapCms, itemCmsId);
  } else {
    assert.fail(`item cannot be verified. undefined swapType: ${swapType}, ${pieceSwapCms.id}`);
  }
}

function _ensureGachaTicketSwapType(pieceSwapCms: PieceSwapDesc, itemCmsId: number): void {
  if (!cms.Item[itemCmsId]) {
    throw new MError('invalid-item-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      cmsId: pieceSwapCms.id,
      pieceType: pieceSwapCms.pieceType,
      swapType: pieceSwapCms.swapType,
      exchangableItemId: pieceSwapCms.exchangableItemId,
      itemCmsId,
    });
  }

  // 티켓을 교환하기 위한 item 은 가지고 있어야 한다
  if (pieceSwapCms.exchangableItemId === undefined || pieceSwapCms.exchangableItemId.length === 0) {
    throw new MError(
      'empty-exchangable-item-cms-id',
      MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE,
      {
        cmsId: pieceSwapCms.id,
        pieceType: pieceSwapCms.pieceType,
        swapType: pieceSwapCms.swapType,
        exchangableItemId: pieceSwapCms.exchangableItemId,
        itemCmsId,
      }
    );
  }

  if (!pieceSwapCms.exchangableItemId.includes(itemCmsId)) {
    throw new MError('non-exchangable-item-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      cmsId: pieceSwapCms.id,
      pieceType: pieceSwapCms.pieceType,
      swapType: pieceSwapCms.swapType,
      exchangableItemId: pieceSwapCms.exchangableItemId,
      itemCmsId,
    });
  }
}

// 승급 도구, 교본, 임명장 swapType 검사.
function _ensureAwakenToolSwapType(pieceSwapCms: PieceSwapDesc, itemCmsId: number) {
  const itemCms: ItemDesc = cms.Item[itemCmsId];
  if (!itemCms) {
    throw new MError('invalid-item-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      cmsId: pieceSwapCms.id,
      pieceType: pieceSwapCms.pieceType,
      swapType: pieceSwapCms.swapType,
      itemCmsId,
    });
  }

  // 티켓을 교환하기 위한 item 은 가지고 있어야 한다
  if (pieceSwapCms.exchangableItemId === undefined || pieceSwapCms.exchangableItemId.length === 0) {
    throw new MError(
      'empty-exchangable-item-cms-id',
      MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE,
      {
        cmsId: pieceSwapCms.id,
        pieceType: pieceSwapCms.pieceType,
        swapType: pieceSwapCms.swapType,
        exchangableItemId: pieceSwapCms.exchangableItemId,
        itemCmsId,
      }
    );
  }

  if (!pieceSwapCms.exchangableItemId.includes(itemCmsId)) {
    throw new MError('non-exchangable-item-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      cmsId: pieceSwapCms.id,
      pieceType: pieceSwapCms.pieceType,
      swapType: pieceSwapCms.swapType,
      exchangableItemId: pieceSwapCms.exchangableItemId,
      itemCmsId,
    });
  }
}

function _ensureTokenSwapType(pieceSwapCms: PieceSwapDesc, itemCmsId: number) {
  const itemCms: ItemDesc = cms.Item[itemCmsId];
  if (!itemCms) {
    throw new MError('invalid-item-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      cmsId: pieceSwapCms.id,
      pieceType: pieceSwapCms.pieceType,
      swapType: pieceSwapCms.swapType,
      itemCmsId,
    });
  }

  // 티켓을 교환하기 위한 item 은 가지고 있어야 한다
  if (pieceSwapCms.exchangableItemId === undefined || pieceSwapCms.exchangableItemId.length === 0) {
    throw new MError(
      'empty-exchangable-item-cms-id',
      MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE,
      {
        cmsId: pieceSwapCms.id,
        pieceType: pieceSwapCms.pieceType,
        swapType: pieceSwapCms.swapType,
        exchangableItemId: pieceSwapCms.exchangableItemId,
        itemCmsId,
      }
    );
  }

  if (!pieceSwapCms.exchangableItemId.includes(itemCmsId)) {
    throw new MError('non-exchangable-item-cms-id', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
      cmsId: pieceSwapCms.id,
      pieceType: pieceSwapCms.pieceType,
      swapType: pieceSwapCms.swapType,
      exchangableItemId: pieceSwapCms.exchangableItemId,
      itemCmsId,
    });
  }
}

function _buildPieceTypeToolValidator(
  pieceSwapCms: PieceSwapDesc,
  user: User
): (itemCmsId: number) => void {
  const swapType = pieceSwapCms.swapType;
  if (
    swapType === PIECE_SWAP_TYPE.PROMOTION ||
    swapType === PIECE_SWAP_TYPE.MANUAL ||
    swapType === PIECE_SWAP_TYPE.APPOINTMENT
  ) {
    return (itemCmsId) => _ensureAwakenToolSwapType(pieceSwapCms, itemCmsId);
  } else {
    assert.fail(`item cannot be verified. undefined swapType: ${swapType}, ${pieceSwapCms.id}`);
  }
}

function _buildPieceTypeTokenValidator(
  pieceSwapCms: PieceSwapDesc,
  user: User
): (itemCmsId: number) => void {
  const swapType = pieceSwapCms.swapType;
  if (swapType === PIECE_SWAP_TYPE.TOKEN) {
    return (itemCmsId) => _ensureTokenSwapType(pieceSwapCms, itemCmsId);
  } else {
    assert.fail(`item cannot be verified. undefined swapType: ${swapType}, ${pieceSwapCms.id}`);
  }
}

function _buildBundles(bundleSize: number, reagents: unknown): Bundle[] {
  if (!Array.isArray(reagents) || reagents.length === 0) {
    throw new MError(
      'invalid-reagents-non-empty-array-expected',
      MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE,
      { reagents }
    );
  }

  const bundles: Bundle[] = [];

  reagents.forEach((reagent, idx) => {
    if (!reagent || typeof reagent !== 'object') {
      throw new MError('invalid-reagent', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
        reagent,
        idx,
      });
    }

    const { itemCmsId, amount } = reagent;
    if (Number.isInteger(amount) && amount > 0 && itemCmsId !== undefined) {
      _accumBundle(bundles, bundleSize, itemCmsId, amount);
    } else {
      throw new MError('invalid-reagent', MErrorCode.INVALID_REQ_BODY_MANTIC_SWAP_PIECE, {
        reagent,
        idx,
      });
    }
  });

  mlog.verbose('[PIECE_SWAP] reagent bundles', { bundles });

  // 클라에서 자투리가 남게 보낸 경우는 조심스럽게 잘라낸다.
  if (bundles.length > 0 && bundles[bundles.length - 1].sum < bundleSize) {
    const insufficientBundle = bundles.pop();
    mlog.verbose('[PIECE_SWAP], remaining reagent ignored', {
      insufficientBundle,
    });
  }

  return bundles;
}

/**
 * @param amount amount가 0인 경우도 키,값이 할당되는 것 주의
 */
function _accumMap(map: Map<number, number>, itemCmsId: number, amount: number) {
  map.set(itemCmsId, (map.get(itemCmsId) || 0) + amount);
}

type Bundle = { reagents: Map<number, number>; sum: number };
/**
 * 배열의 마지막 원소에서 부터 bundleSize 만큼씩 누적한다.
 * bundleSize를 다르게 넣거나 Bundle의 sum을 수정할 수 있는 문제가 있는데, 클래스로 만드는게 나을지?
 * @param amount 정수이며 양수(0보다 큼)이어야함.
 */
function _accumBundle(bundles: Bundle[], bundleSize: number, itemCmsId: number, amount: number) {
  const createBundle = (itemCmsId: number, initVal: number) => ({
    reagents: new Map([[itemCmsId, initVal]]),
    sum: initVal,
  });

  if (amount === 0) {
    return;
  }

  const lastBundle = bundles[bundles.length - 1];
  if (lastBundle) {
    const reaminingOfBundle = bundleSize - lastBundle.sum;
    if (reaminingOfBundle >= amount) {
      _accumMap(lastBundle.reagents, itemCmsId, amount);
      lastBundle.sum += amount;
      return;
    }

    _accumMap(lastBundle.reagents, itemCmsId, reaminingOfBundle);
    lastBundle.sum += reaminingOfBundle;
    amount -= reaminingOfBundle;
  }

  const quotient = Math.floor(amount / bundleSize);
  for (let i = 0; i < quotient; i += 1) {
    bundles.push(createBundle(itemCmsId, bundleSize));
  }

  const rest = amount % bundleSize;
  if (rest > 0) {
    bundles.push(createBundle(itemCmsId, rest));
  }
}

function _processSwap(
  pieceSwapCms: PieceSwapDesc,
  user: User,
  bundles: Bundle[]
): Promise<{ sync: Sync; gains: ActualGain[] }> {
  const rewardCmsId = pieceSwapCms.rewardId;
  return promise.reduce<Bundle, { sync: Sync; gains: ActualGain[] }>(
    bundles,
    (acc, bundle, idx) => {
      const changeTask = new UserChangeTask(
        user,
        CHANGE_TASK_REASON.MANTIC_SWAP_PIECE,
        new PieceSwapRewardSpec(bundle.reagents, rewardCmsId)
      );

      const res = changeTask.trySpec();
      if (res <= CHANGE_TASK_RESULT.OK_MAX) {
        return changeTask.apply().then((s) => {
          _.merge<Sync, Sync>(acc.sync, s);
          // 아이템 소모 내역도 같이 전달되는 것 참고.
          const gain = changeTask.getActualGain();
          acc.gains.push(gain);

          // glog
          // actualGain.items 는 '변동' 수치로, 소모한 내역도 합산되어 있다.
          // 소모한 것을 다시 지급 받을 지 랜덤 보상이라 애매하긴 한데, 일단 소모된 것들을 더해준다.
          // reward_data 를 UserChangeTask 에서 깔끔하게 얻어낼 수 있으면 좋을 듯..
          const gainForRewardData = _.cloneDeep(gain);
          const cost_data: CostData[] = [];
          for (const [itemCmsId, amount] of bundle.reagents) {
            const itemCms = cms.Item[itemCmsId];

            cost_data.push({
              id: itemCmsId,
              amt: amount,
              type: ITEM_TYPE[itemCms.type],
            });

            if (!gainForRewardData.items) {
              gainForRewardData.items = {};
            }
            gainForRewardData.items[itemCmsId] = (gainForRewardData.items[itemCmsId] ?? 0) + amount;
          }
          const reward_data: RewardData[] =
            UserChangeTask.covertActualGainToGLogRewardData(gainForRewardData);

          _glogSwapPiece(
            user,
            pieceSwapCms,
            cost_data.length > 0 ? cost_data : null,
            reward_data.length > 0 ? reward_data : null
          );
          //

          return acc;
        });
      } else {
        // 인벤이 꽉 찼을 때 우편으로 보내는 작업이 시간이 걸릴듯 하고, BM 정책에 따라 어떻게 바뀔지 몰라서
        // 당장은 기획적으로 인벤 공간을 차지하지 않는 보상만 지급한다고 함.
        // 테이블에 값이 잘 못 들어간 경우로, fatal error 로그아웃 시킨다.
        // 아이템이 부족한 경우는 이 함수 밖에서 미리 검사한다.
        // (혹여나 보상으로 받은 것이 재료로 쓰이는 것을 방지 등)
        throw new MError(
          'failed-to-receive-reward',
          MErrorCode.FAILED_TO_MANTIC_SWAP_PIECE_CHANGE_TASK,
          {
            taskResult: res,
            taskIdx: idx,
          }
        );
      }
    },
    { sync: {}, gains: [] }
  );
}

function _glogSwapPiece(
  user: User,
  pieceSwapCms: PieceSwapDesc,
  cost_data: CostData[] | null = null,
  reward_data: RewardData[] | null = null
) {
  user.glog('fortune_exchange', {
    rsn,
    add_rsn,

    category: pieceSwapCms.pieceType, // 계약서/도면/..
    exchange_type: pieceSwapCms.swapType, // 다면계약/고급계약/..
    exchange_item_data: cost_data,
    exchange_reward_data: reward_data,
  });
}

class PieceSwapRewardSpec extends RewardAndPaymentSpec {
  private _reagents: Map<number, number>;

  /**
   * @param reagents value는 0보다 큰 정수이어야 한다.
   */
  constructor(reagents: Map<number, number>, rewardCmsId: number) {
    super([
      {
        type: RNP_TYPE.REWARD,
        cmsId: rewardCmsId,
        bIsNotPermitAddToHardCapLimitLine: true,
        bIsAccum: true,
        bIsBound: false,
      },
    ]);
    this._reagents = reagents;
  }
  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    // 먼저 필요 아이템들을 차감
    for (const [itemCmsId, amount] of this._reagents) {
      assert(amount >= 0);
      const res = opAddItem(
        user,
        tryData,
        changes,
        itemCmsId,
        -amount,
        false /** bAllowInven */,
        false /** bAllowAddToLimitIfExcceded */,
        undefined,
        true,
        true
      );
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }

    // 필요한 조각 차감 후에 보상 적용
    return super.accumulate(user, tryData, changes);
  }
}
