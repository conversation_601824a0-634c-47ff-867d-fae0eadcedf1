{"accountId":"**********","level":"info","message":"/logout","timestamp":"2025-08-22T04:54:29.670Z"}
{"url":"/logout","status":"200","response-time":"15.664","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:54:29.685Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T04:54:29.687Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"1.872","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:54:29.689Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T04:56:48.607Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T04:56:48.607Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T04:56:49.496Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T04:56:49.499Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T04:56:49.500Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T04:56:49.501Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T04:56:49.502Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T04:56:49.502Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T04:56:49.502Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T04:56:49.503Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T04:56:49.504Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T04:56:49.504Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T04:56:49.504Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T04:56:49.505Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T04:56:49.505Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T04:56:49.505Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T04:56:49.506Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T04:56:49.669Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T04:56:49.669Z"}
{"environment":"development","type":"authd","gitCommitHash":"ae652caf24a","gitCommitMessage":"QAUWO-18306 [UWOCN] 일부 항구에서 출항 시 오류 발생","gitCommitter":"Kim-Kyunghwan <<EMAIL>>","gitCommitDate":"2025-08-22T09:37:14+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T04:56:53.317Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T04:57:15.315Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T04:57:15.584Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T04:57:15.585Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T04:57:15.586Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T04:57:15.587Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T04:57:20.174Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T04:57:20.174Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T04:57:20.175Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T04:57:20.181Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T04:57:20.182Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T04:57:20.199Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T04:57:20.282Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.301Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.318Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.330Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.344Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.355Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.374Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.392Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.407Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T04:57:20.425Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T04:57:20.506Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T04:57:20.507Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T04:57:20.512Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T04:57:20.605Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T04:57:20.607Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T04:57:20.607Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T04:57:20.607Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T04:57:20.610Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T04:57:20.611Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T04:57:20.611Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T04:57:20.611Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T04:57:20.615Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T04:57:20.615Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T04:57:20.616Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T04:57:20.616Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T04:57:20.617Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T04:57:20.619Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T04:57:20.620Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T04:57:21.691Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T04:57:21.692Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T04:57:21.693Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T04:57:21.693Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T04:57:21.763Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T04:57:21.763Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T04:57:21.773Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T04:57:21.773Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T04:57:21.782Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T04:57:21.782Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T04:57:21.797Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T04:57:21.798Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:21.896Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:21.918Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:21.947Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:21.981Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.030Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.069Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.119Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.167Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.196Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T04:57:22.208Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.214Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.243Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.294Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.322Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.338Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.761Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.783Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.815Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.836Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.854Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.880Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.909Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.953Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:22.993Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:23.009Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T04:57:23.031Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T04:57:23.053Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T04:57:23.145Z"}
{"url":"/getWorldStates","status":"200","response-time":"91.532","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:23.147Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.931","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:26.779Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.664","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:31.989Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.140","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:36.806Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.555","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:42.022Z"}
{"body":{"platform":2,"gnidSessionToken":"8334269**********1755838662","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":41932,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T04:57:43.654Z"}
{"_time":"2025-08-22 13:57:43","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":41932,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T04:57:43.682Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T04:57:43.683Z"}
{"url":"/getWorlds","status":"200","response-time":"78.218","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:43.683Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.007","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:46.856Z"}
{"body":{"platform":2,"sessionToken":"8334269**********1755838662","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T04:57:47.203Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 13:57:47&endpointip=127.0.0.1&endpointport=0&guid=7ceb427472594b11ba09290363fb3c11&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=5af7c7bf44047add06a6c6acbc87945d","timestamp":"2025-08-22T04:57:47.620Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T04:57:48.055Z"}
{"loginDbResult":{"isOnline":0,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8334269**********1755838662","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T04:57:48.067Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T04:57:48.069Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"2e5aee789954df5aea22247797707724e4b8e8c6","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T04:57:48.070Z"}
{"url":"/login","status":"200","response-time":"914.018","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:48.070Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.647","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:51.863Z"}
{"url":"/getWorldStates","status":"200","response-time":"5.223","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:57:59.053Z"}
{"url":"/getWorldStates","status":"200","response-time":"3.933","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:03.856Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.866","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:09.094Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.976","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:13.918Z"}
{"body":{"platform":2,"gnidSessionToken":"8050911**********1755838691","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":19509,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T04:58:16.453Z"}
{"_time":"2025-08-22 13:58:16","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":19509,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T04:58:16.461Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T04:58:16.462Z"}
{"url":"/getWorlds","status":"200","response-time":"55.177","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:16.462Z"}
{"body":{"platform":2,"sessionToken":"8050911**********1755838691","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T04:58:17.423Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 13:58:17&endpointip=127.0.0.1&endpointport=0&guid=594d39fa698a433a8e19b45d983d874b&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=5a8640bf255e59ac83ceffa823094ecf","timestamp":"2025-08-22T04:58:17.512Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T04:58:17.588Z"}
{"loginDbResult":{"isOnline":1,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8050911**********1755838691","revision":"ae652caf24a2922017e4efc54e6a992a8c56f334","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T04:58:17.595Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T04:58:17.596Z"}
{"accountId":"**********","userId":1000,"lastLobby":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/login user already online","timestamp":"2025-08-22T04:58:17.598Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-22T04:58:17.598Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-22T04:58:17.600Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"bd95b57e82a472ce9012fe05c014ebd537d568b6","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T04:58:17.601Z"}
{"url":"/login","status":"200","response-time":"221.566","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:17.601Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"bd95b57e82a472ce9012fe05c014ebd537d568b6","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T04:58:17.668Z"}
{"url":"/enterWorld","status":"200","response-time":"6.501","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:17.674Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T04:58:17.821Z"}
{"url":"/getUserNames","status":"200","response-time":"1.042","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:17.822Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T04:58:17.837Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.630","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:17.839Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.461","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T04:58:20.112Z"}
