// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import amqp from 'amqplib';
import bodyParser from 'body-parser';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import _, { some } from 'lodash';
import morgan from 'morgan';
import path from 'path';
import { Container, Service } from 'typedi';
import cms, { load as cmsLoad } from '../cms';
import * as dirAsApi from '../motiflib/directoryAsApi';
import * as expressError from '../motiflib/expressError';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import { CreateSlackNotifier } from '../motiflib/slackNotifier';
import { DBConnPool, DbConnPoolManager } from '../mysqllib/pool';
import { MRedisConnPool } from '../redislib/connPool';
import Pubsub from '../redislib/pubsub';
import LobbydHealthChecker from './lobbydHealthChecker';
import * as realmPubsub from './realmPubsub';
import { ScheduledJobs } from './schedule';
import { getSelectableNations } from '../cms/ex';
import stoppable from 'stoppable';
import MaintenanceChecker from './maintenaceChecker';
import * as Sentry from '@sentry/node';
import fs from 'fs';
import { CHANNEL_TYPE, getAliasName } from '../motiflib/mhttp/chatApiClient';
import { NationCabinetRewardMailLookupTable } from '../motiflib/model/cmsKeyGroup';
import { startTogglet, stopTogglet } from '../motiflib/togglet';

// -------------------------------------------------------------------------------------------------
// // undefined 참조등으로 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('uncaughtException', (err) => {
  Sentry.captureException(err);
  mlog.error('uncaught Exception', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Promise의 then 에서 예외를 catch 하지 못하면 호출
// -------------------------------------------------------------------------------------------------
process.on('unhandledRejection', (err: Error) => {
  Sentry.captureException(err);
  mlog.error('unhandled Rejection', {
    msg: err.message,
    stack: err.stack,
  });
  // 위에 error로그가 파일기록이 비동기라서 약 1초간의 딜레이를 준 후 종료 시킨다.
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

// -------------------------------------------------------------------------------------------------
// Private functions.
// -------------------------------------------------------------------------------------------------
function realmReqLog(tokens, req, res) {
  if (req.url === '/health') {
    return;
  }

  mlog.info('realmd-req', {
    url: tokens['url'](req, res),
    status: tokens['status'](req, res),
    'response-time': tokens['response-time'](req, res),
    mcode: tokens['mcode'](req, res),
  });
  return null;
}

// ----------------------------------------------------------------------------
// Module variables.
// ----------------------------------------------------------------------------

// Main realmd app.
const realmApp = express();

realmApp.disable('x-powered-by');
realmApp.disable('etag');
realmApp.disable('content-type');
realmApp.use(morgan(realmReqLog));
realmApp.use(bodyParser.json());
realmApp.use(expressError.middleware);

const realmServer = stoppable(http.createServer(realmApp));

let stopping = false;
let healthCheckTimeout: NodeJS.Timer = null;

// ----------------------------------------------------------------------------
// Service
// ----------------------------------------------------------------------------
@Service()
export class RealmService {
  nationRedis: MRedisConnPool;
  townRedis: MRedisConnPool;
  collectorRedis: MRedisConnPool;
  monitorRedis: MRedisConnPool;
  arenaRedis: MRedisConnPool;
  userCacheRedis: MRedisConnPool;
  authRedis: MRedisConnPool;
  userRedis: MRedisConnPool;
  orderRedis: MRedisConnPool;
  rankingRedis: MRedisConnPool;
  blindBidRedis: MRedisConnPool;

  amqpConn: amqp.Connection | null;
  amqpCh: amqp.Channel | null;
  userDbConnPoolMgr: DbConnPoolManager;
  worldDbConnPool: DBConnPool;
  lobbydHealthChecker: LobbydHealthChecker;
  maintenanceChecker: MaintenanceChecker;
  nationCabinetRewardMailLookupTable: NationCabinetRewardMailLookupTable;

  async init() {
    await startTogglet();

    this.townRedis = Container.of('town-redis').get(MRedisConnPool);
    await this.townRedis.init('town-redis', mconf.townRedis);

    this.nationRedis = Container.of('nation-redis').get(MRedisConnPool);
    await this.nationRedis.init('nation-redis', mconf.nationRedis);

    this.collectorRedis = Container.of('collector-redis').get(MRedisConnPool);
    await this.collectorRedis.init('collector-redis', mconf.collectorRedis);

    this.monitorRedis = Container.of('monitor-redis').get(MRedisConnPool);
    await this.monitorRedis.init('monitor-redis', mconf.monitorRedis);

    this.arenaRedis = Container.of('arena-redis').get(MRedisConnPool);
    await this.arenaRedis.init('arena-redis', mconf.arenaRedis);

    this.userCacheRedis = Container.of('user-cache-redis').get(MRedisConnPool);
    await this.userCacheRedis.init('user-cache-redis', mconf.userCacheRedis);

    this.authRedis = Container.of('auth-redis').get(MRedisConnPool);
    await this.authRedis.init('auth-redis', mconf.authRedis);

    this.userRedis = Container.of('user-redis').get(MRedisConnPool);
    await this.userRedis.init('user-redis', mconf.userRedis);

    this.orderRedis = Container.of('order-redis').get(MRedisConnPool);
    await this.orderRedis.init('order-redis', mconf.orderRedis);

    this.rankingRedis = Container.of('ranking-redis').get(MRedisConnPool);
    await this.rankingRedis.init('ranking-redis', mconf.rankingRedis);

    this.blindBidRedis = Container.of('blind-bid-redis').get(MRedisConnPool);
    await this.blindBidRedis.init('blind-bid-redis', mconf.blindBidRedis);

    const worldPubsub = Container.of('pubsub-world').get(Pubsub);
    worldPubsub.init(mconf.getWorldConfig().worldPubsubRedis);
    realmPubsub.init(worldPubsub, this.townRedis);

    // cms관련 데이터 로드
    this.nationCabinetRewardMailLookupTable = new NationCabinetRewardMailLookupTable(cms);

    await this.connectToRabbitMq();

    // Init mysql connection pool.
    this.userDbConnPoolMgr = Container.of('user').get(DbConnPoolManager);
    await this.userDbConnPoolMgr.init(mconf.mysqlUserDb);
    this.worldDbConnPool = Container.of('world').get(DBConnPool);
    await this.worldDbConnPool.init(mconf.mysqlWorldDb);

    // Init LobbydHealthChecker
    this.lobbydHealthChecker = new LobbydHealthChecker();

    // init MaintenanceChecker
    this.maintenanceChecker = new MaintenanceChecker();

    // Init api server.
    const bindAddress = mconf.apiService.bindAddress;
    const port = mconf.apiService.port;

    mutil.registerHealthCheck(realmApp);
    mutil.registerGarbageCollector(realmApp);
    await dirAsApi.register(realmApp, path.join(__dirname, 'api'));

    realmServer.listen(port, bindAddress, () => {
      mlog.info('start listening ...', { bindAddress, port });
    });

    const schedule = Container.get(ScheduledJobs);
    schedule.start();

    healthCheckTimeout = setTimeout(() => {
      if (stopping) {
        return;
      }
      // zonelbd시작후 일정시간동안 타임아웃 처리를 유예하여
      // 이미 동작중인 존서버들의 ping처리가 가능하도록 한다.
      this.lobbydHealthChecker.startTick();
    }, mconf.lobbydHealthCheck.timeout);

    this.maintenanceChecker.startCheck();
  }

  async connectToRabbitMq(reconnect: boolean = false) {
    try {
      this.amqpConn = await amqp.connect(mconf.amqp);
    } catch (err) {
      mlog.error('Failed to connect to RabbitMQ server.', {
        message: err.message,
        stack: err.stack,
      });
      if (reconnect) {
        await mutil.sleep(1000);
        this.connectToRabbitMq(reconnect);
        return;
      } else {
        throw err;
      }
    }

    mlog.info('connected to RabbitMQ', {
      config: mconf.amqp,
    });

    this.amqpConn.on('error', (err) => {
      mlog.error('The rabbitmq error occurs.', {
        message: err.message,
        stack: err.stack,
      });
    });

    this.amqpConn.on('close', async (err) => {
      if (err) {
        mlog.error('The RabbitMQ has been disconnected by server', {
          message: err.message,
          stack: err.stack,
        });
      } else {
        mlog.info('The RabbitMQ has been disconnected by server');
      }

      await mutil.sleep(1000);
      this.connectToRabbitMq(true);
    });
    this.amqpCh = await this.amqpConn.createChannel();
  }

  async destroy() {
    this.maintenanceChecker.stopCheck();
    this.lobbydHealthChecker.stopTick();

    await this.townRedis.destroy();
    await this.nationRedis.destroy();
    await this.collectorRedis.destroy();
    await this.monitorRedis.destroy();
    await this.arenaRedis.destroy();
    await this.userCacheRedis.destroy();
    await this.authRedis.destroy();
    await this.userRedis.destroy();
    await this.orderRedis.destroy();
    await this.rankingRedis.destroy();
    await this.blindBidRedis.destroy();

    const pubsub = Container.of('pubsub-world').get(Pubsub);
    await pubsub.quit();

    await this.amqpConn.close();

    await this.userDbConnPoolMgr.destroy();
    await this.worldDbConnPool.destroy();

    stopTogglet();
  }
}

// ----------------------------------------------------------------------------
// Private functions.
// ----------------------------------------------------------------------------

morgan.token('mcode', (_req, res: any) => res.mcode || 0);

async function closeServer(): Promise<void> {
  return new Promise((resolve, reject) => {
    realmServer.stop((err) => {
      if (err) return reject(err);
      resolve();
    });
  });
}

async function stopServer() {
  try {
    mlog.info('stopping server ...');
    await closeServer();

    if (healthCheckTimeout) {
      clearTimeout(healthCheckTimeout);
      healthCheckTimeout = null;
    }
    const app = Container.get(RealmService);
    await app.destroy();

    mlog.info('server stopped');
    // explicitly calls the exit function, since node-schedule job is not cancelled
    process.exit(0);
  } catch (error) {
    mlog.exception('graceful shutdown failed', error);
    process.exit(1);
  }
}

/**
 * LINE GAMES CHATTING
 * 기본 채널 생성.
 * 타운, 지역 ID와 기본 시스템 채널을 (없으면) 생성한다.
 */
async function initVolanteChannels() {
  try {
    // 채널명과 맵핑되는 별칭명 excel파일로 추출.(라인게임즈 전달용)
    // makeChannelNAliasFile();
    const exists = await mhttp.chatd.getAllChannels();
    const listOfNameNAlias: { name: string; alias?: string }[] = [];

    let required = _.without(_.keys(cms.Town), ...exists);
    const towns = required.map((townCmsIdStr: string) => {
      return {
        name: townCmsIdStr,
        alias: getAliasName(CHANNEL_TYPE.REGION, townCmsIdStr),
      };
    });
    listOfNameNAlias.push(...towns);

    required = _.without(_.keys(cms.Region), ...exists);
    const regions = required.map((regionCmsIdStr: string) => {
      return {
        name: regionCmsIdStr,
        alias: getAliasName(CHANNEL_TYPE.REGION, regionCmsIdStr),
      };
    });
    listOfNameNAlias.push(...regions);

    required = _.without(
      getSelectableNations().map((elem) => elem.id.toString()),
      ...exists
    );
    required = getSelectableNations().map((elem) => elem.id.toString());
    const nations = required.map((nationCmsIdStr: string) => {
      return {
        name: nationCmsIdStr,
        alias: getAliasName(CHANNEL_TYPE.NATION, nationCmsIdStr),
      };
    });
    listOfNameNAlias.push(...nations);

    listOfNameNAlias.push({
      name: 'SYSTEM',
      alias: getAliasName(CHANNEL_TYPE.SYSTEM),
    });

    listOfNameNAlias.push({
      name: 'WORLD',
      alias: getAliasName(CHANNEL_TYPE.WORLD),
    });

    for (let i = 0; i < listOfNameNAlias.length; i++) {
      const channelName = listOfNameNAlias[i].name;
      const channelAlias = listOfNameNAlias[i].alias;
      const result = await mhttp.chatd.createPublicChannel(channelName, channelAlias);
      // mlog.info('create volante channel success', result);
    }
    mlog.info('all volante channel prepared');
  } catch (error) {
    // 예외 발생 시 1분 후 재요청
    mlog.exception('create public channel failed', error);
    setTimeout(() => initVolanteChannels(), 1000 * 60);
  }
}

// ----------------------------------------------------------------------------
// Public functions.
// ----------------------------------------------------------------------------

export async function start() {
  try {
    await mhttp.configd.registerInstance(
      process.env.WORLD_ID ? process.env.WORLD_ID : mconf.instance.worldId,
      mconf.appInstanceId,
      mconf.hostname
    );

    mutil.initSentry();

    // Init http clients.
    mhttp.init();
    // realmd glog 성격 상 다량의 로그가 한 번에 발생하기 때문에 미리 토큰을 받아놓는다.
    await mhttp.lglogd.refreshTokenIfExpired();

    cmsLoad();

    const app = Container.get(RealmService);
    await app.init();

    // config final sync
    const beforeVer = mconf.layoutVersion;
    await mhttp.configd.sync(beforeVer, isStopping, stop).then(() => {
      if (beforeVer < mconf.layoutVersion) {
        // do something
      }
    });
    await initVolanteChannels();
  } catch (error) {
    mlog.error('failed to start', { error: error.message, extra: error.extra });
    mlog.error(error.stack);
    const slackNotifier = await CreateSlackNotifier(mconf.slackNotify);
    await slackNotifier.notify({ username: process.name, text: error.message });
    process.exit(1);
  }
}

function isStopping() {
  return stopping;
}

export async function stop() {
  if (stopping) {
    return;
  }

  stopping = true;

  await stopServer();
}

function makeChannelNAliasFile() {
  const f = (channelName: string, aliasName: string) => {
    return {
      '기존 채널명(channel_name)': channelName,
      '입력할 채널 별칭(alias)': aliasName,
    };
  };
  let results = [];

  results.push(f('SYSTEM', getAliasName(CHANNEL_TYPE.SYSTEM)));
  results.push(f('WORLD', getAliasName(CHANNEL_TYPE.WORLD)));

  const towns = _.keys(cms.Town).map((cmsIdStr: string) =>
    f(cmsIdStr, getAliasName(CHANNEL_TYPE.REGION, cmsIdStr))
  );
  results.push(...towns);

  const regions = _.keys(cms.Region).map((cmsIdStr: string) =>
    f(cmsIdStr, getAliasName(CHANNEL_TYPE.REGION, cmsIdStr))
  );
  results.push(...regions);

  const nations = getSelectableNations().map((elem) =>
    f(elem.id.toString(), getAliasName(CHANNEL_TYPE.NATION, elem.id.toString()))
  );
  results.push(...nations);

  // 추출된 json파일은 http://convertcsv.com/json-to-csv.htm에서 엑셀파일로 컨버팅가능.
  fs.writeFile('alias.json', JSON.stringify(results), (err) => {
    if (err) {
      throw err;
    }
  });
}
