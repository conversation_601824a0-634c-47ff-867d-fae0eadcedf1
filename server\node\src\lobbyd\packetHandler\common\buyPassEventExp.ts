// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';
import _ from 'lodash';
import assert from 'assert';

import cms from '../../../cms';
import * as cmsEx from '../../../cms/ex';
import { MError, MErrorCode } from '../../../motiflib/merror';
import mconf from '../../../motiflib/mconf';
import mlog from '../../../motiflib/mlog';
import * as mutil from '../../../motiflib/mutil';
import { PrData } from '../../../motiflib/gameLog';
import tuBuyPassEventExp from '../../../mysqllib/txn/tuBuyPassEventExp';
import { LobbyService } from '../../server';
import { Resp, Sync } from '../../type/sync';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { ClientPacketHandler } from '../index';
import UserPoints, { PointConsumptionCostParam } from '../../userPoints';
import { EventPageType } from '../../../cms/eventPageDesc';

// ----------------------------------------------------------------------------
// 패스 이벤트 EXP 구매
// ----------------------------------------------------------------------------

const rsn = 'buy_pass_event_exp';
const add_rsn = null;

interface RequestBody {
  eventPageCmsId: number;
  bPermitExchange?: boolean;
}

interface ResponseBody extends Resp {
  //
}

// ----------------------------------------------------------------------------
export class Cph_Common_BuyPassEventExp implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);
    const { userDbConnPoolMgr } = Container.get(LobbyService);

    const reqBody: RequestBody = packet.bodyObj;
    const { bPermitExchange } = reqBody;

    const curTimeUtc = mutil.curTimeUtc();

    const passEventPageCms = cms.EventPage[reqBody.eventPageCmsId];
    if (!passEventPageCms) {
      throw new MError(
        'invalid-event-page-cms-id',
        MErrorCode.INVALID_REQ_BODY_BUY_PASS_EVENT_EXP,
        { reqBody }
      );
    }
    if (passEventPageCms.type !== EventPageType.PASS_EVENT) {
      throw new MError('expected-pass-event-page', MErrorCode.INVALID_REQ_BODY_BUY_PASS_EVENT_EXP, {
        eventPageCmsId: passEventPageCms.id,
        eventPageType: passEventPageCms.type,
      });
    }

    if (cmsEx.isFilteredByCountryCode(passEventPageCms.localBitflag)) {
      throw new MError('invalid-local-bit-flag', MErrorCode.NOT_ALLOWED_IN_COUNTRY, {
        eventPageCmsId: passEventPageCms.id,
        localBitflag: passEventPageCms.localBitflag,
      });
    }

    if (cmsEx.isEventPageExpired(curTimeUtc, passEventPageCms)) {
      // 이벤트 페이지 종료 후 보상 수령 기간이 추가로 있을 수 있는데, 그 기간에도 EXP 구매는 못 한다는 것 참고.
      throw new MError('event-page-expired', MErrorCode.INVALID_REQ_BODY_BUY_PASS_EVENT_EXP, {
        eventPageCmsId: passEventPageCms.id,
        curTimeUtc,
      });
    }

    if (user.userPassEvent.isPassEventCompleted(passEventPageCms.id)) {
      throw new MError(
        'pass-event-page-already-completed',
        MErrorCode.INVALID_REQ_BODY_BUY_PASS_EVENT_EXP,
        { eventPageCmsId: passEventPageCms.id }
      );
    }

    const { exp: curAccExp, level: curLevel } =
      user.userPassEvent.getPassEventExpLevel(passEventPageCms);
    assert(curLevel > 0);
    // 반복 보상이 존재하면 최대 레벨이며 유저의 레벨이 최대 레벨일때 구매 불가
    if (curLevel >= cmsEx.getPassEventMaxLevel(passEventPageCms)) {
      throw new MError(
        'already-above-max-pass-event-lv',
        MErrorCode.INVALID_REQ_BODY_BUY_PASS_EVENT_EXP,
        {
          eventPageCmsId: passEventPageCms.id,
          curLevel,
          curExp: curAccExp,
          maxLevel: cmsEx.getPassEventMaxLevel(passEventPageCms),
        }
      );
    }

    const eventMissionExpCms = cms.EventMissionExp[curLevel];
    assert(curAccExp < eventMissionExpCms.accumulateExp);

    const expToBuy = eventMissionExpCms.accumulateExp - curAccExp;
    const newPassEventExp = eventMissionExpCms.accumulateExp;
    const newPassEventLevel = eventMissionExpCms.id + 1;
    assert(curAccExp !== newPassEventExp && curLevel !== newPassEventLevel);

    const pointCosts: PointConsumptionCostParam[] = [
      {
        cmsId: cms.Const.EventMissionExpPurphaseCostType.value,
        cost: Math.ceil(cms.Const.EventMissionExpPurphaseCostVal.value * expToBuy),
      },
    ];

    const pcChanges = user.userPoints.buildPointAndCashChangesByPayment(
      pointCosts,
      bPermitExchange,
      { itemId: rsn },
      true
    );

    const resp: ResponseBody = { sync: {} };

    const exchangeHash = UserPoints.generateExchangeHash(user.userId);
    return Promise.resolve()
      .then(() => {
        return user.userPoints.tryConsumeCashs(pcChanges.cashPayments, resp.sync, user, {
          user,
          rsn,
          add_rsn,
          exchangeHash,
        });
      })
      .then(() => {
        return tuBuyPassEventExp(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          { eventPageCmsId: passEventPageCms.id, exp: newPassEventExp, level: newPassEventLevel },
          pcChanges.pointChanges
        );
      })
      .then(() => {
        user.userPassEvent.setPassEventExpLevel(
          passEventPageCms,
          newPassEventExp,
          newPassEventLevel,
          {
            user,
            rsn,
            add_rsn,
            pr_data: pointCosts.map<PrData>((elem) => {
              return { type: elem.cmsId, amt: elem.cost };
            }),
            exchangeHash,
          }
        );
        _.merge<Sync, Sync>(resp.sync, {
          add: {
            passEvents: {
              [passEventPageCms.id]: {
                eventPageCmsId: passEventPageCms.id,
                exp: newPassEventExp,
                level: newPassEventLevel,
              },
            },
          },
        });

        _.merge<Sync, Sync>(
          resp.sync,
          user.userPoints.applyPointChanges(pcChanges.pointChanges, { user, rsn, add_rsn })
        );

        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, resp);
      });
  }
}
