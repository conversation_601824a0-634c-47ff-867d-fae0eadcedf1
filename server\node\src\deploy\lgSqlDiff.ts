// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as _ from 'lodash';
import * as spawn from 'child_process';
import * as path from 'path';
import moment from 'moment';
import * as fs from 'fs';
import mkdirp from 'mkdirp';
import os from 'os';

// ----------------------------------------------------------------------------
// [WARNING] [WARNING] [WARNING] [WARNING] [WARNING]
//
// LG 에 배포하는 서버 패키지의 파일명이 통일 된 이후 버전에서만 사용 가능합니다.
// (v0.0.5 이후)
//
// ----------------------------------------------------------------------------

// ----------------------------------------------------------------------------
// LG용 SQL Diff 배포 포맷.
//
//  /
//  |auth/
//  |--/procedure
//  |--migration.sql
//  |--procedure_diff.txt
//  |
//  |user/
//  |--/procedure
//  |--migration.sql
//  |--procedure_diff.txt
//  |
//  |world/
//  |--/procedure
//  |--migration.sql
//  |--procedure_diff.txt
//  |
//  |INFO.json
//
// ----------------------------------------------------------------------------

// ----------------------------------------------------------------------------
// Local constants.
// ----------------------------------------------------------------------------
const PACKAGE_NAME = 'uwo_servers.lg';
const PACKAGE_FILE_NAME = PACKAGE_NAME + '.tgz';
const INFO_FILE_NAME = 'INFO.json';
const PROCEDURE_DIFF_FILE_NAME = 'procedure_diff.txt';
const TEMP_TO_PROCEDURE_DIR = 'tmptoprocedures_';
const GIT_DIFF_RENAME_LIMIT_OPTION = '-l99999'; //  [대응] exhaustive rename detection was skipped due to too many files.

const DB_TYPES: string[] = ['auth', 'user', 'world'];

const isWindows = os.platform() === 'win32';

// ----------------------------------------------------------------------------
// 에러 발생시, 로그 이후 프로세스 종료.
// ----------------------------------------------------------------------------
function onFatalError(msg: string): void {
  console.log(msg);
  process.exit(1);
}

// ----------------------------------------------------------------------------
// 변경 내역을 저장하는 구조체.
// auth/user/world 각각 xxx_up.sql 파일 path 를 저장한다. (이름순으로 정렬)
// ----------------------------------------------------------------------------
interface SchemaMigration {
  [dbType: string]: string[];
}

// ----------------------------------------------------------------------------
// git diff 의 출력 라인 하나를 분석해서, 'diff' 에 축척한다.
//
// Git Status:
//  A: addition of a file
//  C: copy of a file into a new one
//  D: deletion of a file
//  M: modification of the contents or mode of a file
//  R: renaming of a file
//  T: change in the type of the file
//  U: file is unmerged (you must complete the merge before it can be committed)
//  X: "unknown" change type (most probably a bug, please report it)
//
// ----------------------------------------------------------------------------
function processDiffLine(mig: SchemaMigration, line: string): void {
  const lineTokens = line.split(/(\s+)/).filter((e) => e.trim().length > 0);
  const status = lineTokens[0];
  const gitFilePath = lineTokens[1];

  if (status === 'D') {
    return;
  }

  // 파일명 형태: 'server/node/migrations/user/sqls/20201118024234-xxx-up.sql'
  const pathTokens = gitFilePath.split('/');
  const dbType = pathTokens[3];
  const filePath = path.join('.', pathTokens[2], pathTokens[3], pathTokens[4], pathTokens[5]);

  // 중복 파일 검사.
  const sourceFilePathArray = mig[dbType];
  for (const srcFilePath of sourceFilePathArray) {
    if (srcFilePath === filePath) {
      return;
    }
  }

  sourceFilePathArray.push(filePath);
}

// ----------------------------------------------------------------------------
// 특정 DB의 스키마 변경사항들을 하나의 파일로 출력하기 위한 문자열을 만든다.
// ----------------------------------------------------------------------------
function buildMigrationFileContent(
  mig: SchemaMigration,
  dbType: string,
  fromTag: string,
  fromRev: string,
  toTag: string,
  toRev: string
): string {
  let fileContent: string = '';

  fileContent += `--------------------------------------------------------------------------------\n`;
  fileContent += `-- UWO SQL schema migration auto-generated by deploy:lgsqldiff\n`;
  fileContent += `--  DB: ${dbType}\n`;
  fileContent += `--  Tag: (${fromTag}) => (${toTag})\n`;
  fileContent += `--  Revision: (${fromRev}) => (${toRev})\n`;
  fileContent += `--------------------------------------------------------------------------------\n`;

  const srcFilePaths = mig[dbType];
  for (const srcFilePath of srcFilePaths) {
    const srcFileContent = fs.readFileSync(srcFilePath).toString();

    fileContent += '\n';
    fileContent += `--------------------------------------------------------------------------------\n`;
    fileContent += `-- SRC: ${srcFilePath}\n`;
    fileContent += `--------------------------------------------------------------------------------\n`;
    fileContent += srcFileContent;
  }

  fileContent += '\n';

  return fileContent;
}

// ----------------------------------------------------------------------------
// 특정 revision 2개 사이의 DB 스키마 변경 내역을 추출한다.
// ----------------------------------------------------------------------------
function gatherMigrations(mig: SchemaMigration, fromRev: string, toRev: string): void {
  const gitDiffCmd = `git diff ${GIT_DIFF_RENAME_LIMIT_OPTION} --name-status ${fromRev} ${toRev}`;
  const cmdBufferSize = 1024 * 1024 * 10; // 많은 파일 변경을 대비해서 넉넉하게 10메가.
  const cmdOut = spawn.execSync(gitDiffCmd, { maxBuffer: cmdBufferSize }).toString();
  const outLines = cmdOut.split(/\r?\n/);

  for (const l of outLines) {
    const line = l.trim();

    if (line.substring(line.length - 7) !== '-up.sql') {
      continue;
    }

    if (line.length === 0) {
      continue;
    }

    processDiffLine(mig, line);
  }

  // 이름순으로 정렬.
  for (const dbType of DB_TYPES) {
    mig[dbType].sort();
  }
}

// ----------------------------------------------------------------------------
// procedure 변경 내역을 저장하는 구조체.
// auth/user/world 각각 .sql 파일명을 저장한다. (이름순으로 정렬)
// ----------------------------------------------------------------------------
interface ChangedSpUnit {
  fileName: string;
  bDelete?: boolean;
}

interface ChangedSps {
  [dbType: string]: ChangedSpUnit[];
}

// ----------------------------------------------------------------------------
// git diff 의 출력 라인 하나를 분석해서, 'diff' 에 축척한다.
// Drop인 파일은 복사하지 않도록 bDelete를 true 로 설정.
function processSpDiffLineInternal(sps: ChangedSps, gitFilePath: string, status: string): void {
  // 파일명 형태: 'server/node/procedures/user/mp_***.sql'
  const pathTokens = gitFilePath.split('/');
  const dbType = pathTokens[3];

  if (!pathTokens[4]) {
    console.log('processSpDiffLineInternal error', {
      gitFilePath,
      pathTokens,
      status,
    });
  }

  let fileName = pathTokens[4].trim();

  // 중복 파일 검사.
  const sourceChangesArray = sps[dbType];
  for (const srcChange of sourceChangesArray) {
    if (srcChange.fileName === fileName) {
      return;
    }
  }

  // 이걸로는 해결이 안됨 결국 실제파일명에 있는 공백을 유지해야함
  if (fileName === 'mp_u_friend_point_delete') {
    fileName = 'mp_u_friend_point_delete .sql';
  }

  const unit: ChangedSpUnit = {
    fileName,
    bDelete: status === 'D' ? true : undefined,
  };
  //console.log('sp fileName: ' + unit.fileName + ', bDelete: ', unit.bDelete);

  sourceChangesArray.push(unit);
}

// ----------------------------------------------------------------------------
// git diff 의 출력 라인 하나를 분석해서, 'diff' 에 축척한다.
//
// Git Status:
//  A: addition of a file
//  C: copy of a file into a new one
//  D: deletion of a file
//  M: modification of the contents or mode of a file
//  R: renaming of a file
//  T: change in the type of the file
//  U: file is unmerged (you must complete the merge before it can be committed)
//  X: "unknown" change type (most probably a bug, please report it)
//
// ----------------------------------------------------------------------------
function processSpDiffLine(sps: ChangedSps, line: string): void {
  //line = line.replace(/\s/g, '');
  //line = line.replace(/((\s*\S+)*)\s*/, '$1');
  //console.log('processSpDiffLine: replace whitespaces : ' + line);

  const lineTokens = line.split(/(\s+)/).filter((e) => e.trim().length > 0);
  const status = lineTokens[0];

  // for (let i = 0; i < lineTokens.length; i++) {
  //   console.log('processSpDiffLine: 1 tokens[' + i + ']: ' + lineTokens[i]);
  // }

  if ('R' === status[0]) {
    // rename인 경우는 lineTokens[1], lineTokens[2] 가 파일경로
    // 기존 이름은 삭제로 새로운 이름은 rename으로 표시한다
    if (lineTokens[1] === 'server/node/procedures/user/mp_u_friend_point_delete') {
      console.log('processSpDiffLine correcting for mp_u_friend_point_delete');

      processSpDiffLineInternal(sps, lineTokens[1], 'D');
      processSpDiffLineInternal(sps, lineTokens[3], status);
    } else {
      processSpDiffLineInternal(sps, lineTokens[1], 'D');
      processSpDiffLineInternal(sps, lineTokens[2], status);
    }
  } else {
    // 보통은 lineTokens[1]만 파일경로
    processSpDiffLineInternal(sps, lineTokens[1], status);
  }
}

// ----------------------------------------------------------------------------
// 특정 revision 2개 사이의 DB sp 변경 내역을 추출한다.
// ----------------------------------------------------------------------------
function gatherSps(sps: ChangedSps, fromRev: string, toRev: string, srcPath: string): void {
  const gitDiffCmd = `git diff ${GIT_DIFF_RENAME_LIMIT_OPTION} --name-status ${fromRev} ${toRev} ${srcPath}`;

  //console.log('gitDiffCmd: ' + gitDiffCmd);

  const cmdBufferSize = 1024 * 1024 * 10; // 많은 파일 변경을 대비해서 넉넉하게 10메가.
  const cmdOut = spawn.execSync(gitDiffCmd, { maxBuffer: cmdBufferSize }).toString();
  const outLines = cmdOut.split(/\r?\n/);

  for (const l of outLines) {
    const line = l.trim();

    if (line.substring(line.length - 4) !== '.sql') {
      continue;
    }

    if (line.length === 0) {
      continue;
    }

    //console.log('gatherSps: 2 : ' + line);

    processSpDiffLine(sps, line);
  }

  // 이름순으로 정렬.
  for (const dbType of DB_TYPES) {
    sps[dbType].sort((a, b) => {
      return a.fileName.localeCompare(b.fileName);
    });
  }
}

// ----------------------------------------------------------------------------
// INFO.json 파일 내용 생성.
// ----------------------------------------------------------------------------
function buildInfoFileContent(
  curDate: Date,
  fromTag: string,
  fromRev: string,
  toTag: string,
  toRev: string
): string {
  const gitHash = spawn.execSync('git rev-parse HEAD').toString().trim();

  const contentObj = {
    gitHash,
    time: moment(curDate).format('YYYYMMDD-HHMMSS'),
    tag: {
      from: fromTag,
      to: toTag,
    },
    revision: {
      from: fromRev,
      to: toRev,
    },
  };

  return JSON.stringify(contentObj, null, 2);
}

// ----------------------------------------------------------------------------
// LG 배포시 사용한 tag 에 맞는 버전의 패키지 파일을 열어서,
// 그 안의 INFO.json 을 읽어, 'git_rev' 정보를 읽는다.
// ----------------------------------------------------------------------------
function convertTagToRevision(lgGitHome: string, tagName: string): string {
  // 중간에 실패시 잔여물이 남을 수 있으므로 삭제한다.
  if (isWindows) {
    spawn.execSync(`if exist ${PACKAGE_NAME} (rmdir /s /q ${PACKAGE_NAME})`);
    spawn.execSync(`if exist ${PACKAGE_FILE_NAME} (del /q ${PACKAGE_FILE_NAME})`);
  } else {
    spawn.execSync(`rm -rf ${PACKAGE_NAME}`);
    spawn.execSync(`rm -f ${PACKAGE_FILE_NAME}`);
  }

  // 해당 tag 의 패키지를 받고.
  // const gitCmd = `git --git-dir uwo_gameserver/.git checkout tags/v0.0.5 uwo_servers.lg.tgz`;
  const gitCmd = `git --git-dir ${lgGitHome} checkout tags/${tagName} ${PACKAGE_FILE_NAME}`;
  spawn.execSync(gitCmd);

  // 패키지를 임시 폴더로 옮긴다.
  const tempPackageFilePath = PACKAGE_FILE_NAME;
  // spawn.execSync(`mv ${PACKAGE_FILE_NAME} ${tempPackageFilePath}`);

  // 압축을 풀어서, revision 정보를 얻는다.
  spawn.execSync(`tar zxf ${tempPackageFilePath}`);

  const infoFilePath = path.join(PACKAGE_NAME, 'INFO.json');
  const infoObj = JSON.parse(fs.readFileSync(infoFilePath).toString());

  // console.log('infoObj:', infoObj);

  // 작업이 끝나면 라인깃으로부터 파싱된 파일을 삭제한다.(uwo_servers.lg 폴더와 uwo_servers.lg.tgz)
  if (isWindows) {
    spawn.execSync(`if exist ${PACKAGE_NAME} (rmdir /s /q ${PACKAGE_NAME})`);
    spawn.execSync(`if exist ${PACKAGE_FILE_NAME} (del /q ${PACKAGE_FILE_NAME})`);
  } else {
    spawn.execSync(`rm -rf ${PACKAGE_NAME}`);
    spawn.execSync(`rm -f ${PACKAGE_FILE_NAME}`);
  }

  console.log('[info] git_revision', {
    git_rev: infoObj.git_rev,
    tagName,
  });
  return infoObj.git_rev;
}

// ----------------------------------------------------------------------------
// 스키마 및 sp중 하나라도 변경사항이 있는지 체크한다
// ----------------------------------------------------------------------------
function checkChanged(mig: SchemaMigration, sps: ChangedSps): boolean {
  for (const dbType of DB_TYPES) {
    if (mig[dbType].length > 0 || sps[dbType].length > 0) {
      return true;
    }
  }

  return false;
}

// ----------------------------------------------------------------------------
// LG용 SQL Diff 배포용 포맷에 맞는 폴더/파일들을 만든다.
// ----------------------------------------------------------------------------
function buildDiff(
  mig: SchemaMigration,
  sps: ChangedSps,
  fromTag: string,
  fromRev: string,
  toTag: string,
  toRev: string,
  bKeepWorkRemnants: boolean
): void {
  const curDate = new Date();
  const dateTimeStr = moment(curDate).format('YYYYMMD_DHHMMSS');

  // 최상위 폴더를 만들고.
  const rootDirPath = `uwo_db_diff_${fromTag}_${toTag}`;

  if (isWindows) {
    spawn.execSync(`if exist ${rootDirPath} ( rmdir /s /q ${rootDirPath})`);
  } else {
    spawn.execSync(`rm -rf ${rootDirPath}`);
  }
  mkdirp.sync(rootDirPath);

  // toRev 의 procedures 들을 임시 폴더로 복사해놓고.
  spawn.execSync(
    `git --git-dir=../../.git archive --prefix=${TEMP_TO_PROCEDURE_DIR}/ --format=tar ${toRev}:server/node/procedures | tar -xf -`
  );

  // auth/user/world DB 별로 폴더를 만들어서 내용을 채운다.
  for (const dbType of DB_TYPES) {
    const dbDirPath = path.join(rootDirPath, dbType);
    mkdirp.sync(dbDirPath);

    // procedure 폴더를 만들고, sp 파일들을 복사한다. (위에서 만든 임시 폴더로부터)
    const procedureDirPath = path.join(dbDirPath, 'procedure');
    mkdirp.sync(procedureDirPath);
    const spPath = `procedures/${dbType}/*.sql`;

    // toProcedures 에 존재하는 파일들만 복사한다
    const changedSpUnitArray = sps[dbType];
    for (let i = 0; i < changedSpUnitArray.length; i++) {
      const spUnit = changedSpUnitArray[i];
      if (spUnit.bDelete) {
        continue;
      }

      // 'cp' 커맨드 특성상, Windows 의 mingw 환경에서 잘못되서, 아래처럼 풀어서 사용.

      if (isWindows) {
        spawn.execSync(
          `copy ${TEMP_TO_PROCEDURE_DIR}\\${dbType}\\${spUnit.fileName} ${dbDirPath}\\procedure`
        );
      } else {
        spawn.execSync(
          `cp '${TEMP_TO_PROCEDURE_DIR}/${dbType}/${spUnit.fileName}' ${dbDirPath}/procedure`
        );
      }
    }

    // schema migration 파일을 만든다.
    const migrationFileContent = buildMigrationFileContent(
      mig,
      dbType,
      fromTag,
      fromRev,
      toTag,
      toRev
    );

    const fileName = `${dbType}_${dateTimeStr}_${fromTag}_${toTag}.sql`;
    const outFilePath = path.join(dbDirPath, fileName);
    fs.writeFileSync(outFilePath, migrationFileContent);

    // procedure_diff.txt 생성.
    const spDiffOutput = spawn.execSync(
      `git diff ${GIT_DIFF_RENAME_LIMIT_OPTION} ${fromRev}..${toRev} ${spPath}`
    );

    const spDiffFilePath = path.join(dbDirPath, PROCEDURE_DIFF_FILE_NAME);
    fs.writeFileSync(spDiffFilePath, spDiffOutput);

    // drop procedures query 생성.
    let dropQueryContent = '';

    changedSpUnitArray.forEach((spUnit) => {
      const fileName = spUnit.fileName;
      const procedureName = fileName.substring(0, fileName.length - 4);
      const q = `DROP PROCEDURE IF EXISTS ${procedureName};`;
      dropQueryContent += q;
      dropQueryContent += '\n';
    });
    const dropQueryFileName = 'drop.sql';
    const dropQueryOutFilePath = path.join(procedureDirPath, dropQueryFileName);
    fs.writeFileSync(dropQueryOutFilePath, dropQueryContent);
  }

  // INFO.json 파일 생성.
  const infoFileContent = buildInfoFileContent(curDate, fromTag, fromRev, toTag, toRev);
  const infoFilePath = path.join(rootDirPath, INFO_FILE_NAME);
  fs.writeFileSync(infoFilePath, infoFileContent);

  // zip 으로 묶기.
  spawn.execSync(`tar -zcf ${rootDirPath}.tar.gz ${rootDirPath}`);

  // 임시 작업물 지워야 하는 경우.
  if (!bKeepWorkRemnants) {
    if (isWindows) {
      spawn.execSync(`if exist ${rootDirPath} (rmdir /s /q ${rootDirPath})`);
      spawn.execSync(`if exist ${TEMP_TO_PROCEDURE_DIR} (rmdir /s /q ${TEMP_TO_PROCEDURE_DIR})`);
    } else {
      spawn.execSync(`rm -rf ${rootDirPath}`);
      spawn.execSync(`rm -rf ${TEMP_TO_PROCEDURE_DIR}`);
    }
  }
}

// ----------------------------------------------------------------------------
// Main function.
// ----------------------------------------------------------------------------
function main() {
  const lgGitHome = process.argv[2];
  const fromTag = process.argv[3];
  const toTag = process.argv[4];
  const keepWorkRemnants = process.argv[5] ? true : false;

  if (!lgGitHome || !fromTag || !toTag) {
    onFatalError('(Usage) yarn deploy:lgsqldiff [LG_GIT_HOME] [FROM_TAG] [TO_TAG]');
  }

  console.log('[info] argv', {
    lgGitHome: process.argv[2],
    fromTag: process.argv[3],
    toTag: process.argv[4],
    keepWorkRemnants: process.argv[5],
  });
  // from/to tag 를 revision 으로 변환.
  const fromRev = convertTagToRevision(lgGitHome, fromTag);
  const toRev = convertTagToRevision(lgGitHome, toTag);

  // console.log('fromRev:', fromRev, ', toRev:', toRev);

  // 버전 사이 필요한 스키마 마이그레이션 수집.
  const mig: SchemaMigration = {};
  for (const dbType of DB_TYPES) {
    mig[dbType] = [];
  }
  gatherMigrations(mig, fromRev, toRev);

  // 버전 사이 필요한 sp 변경사항 수집.
  const sps: ChangedSps = {};
  for (const dbType of DB_TYPES) {
    sps[dbType] = [];
  }
  gatherSps(sps, fromRev, toRev, 'procedures');

  // LG용 SQL Diff 배포 포맷 맞춘 결과물 만들기.
  buildDiff(mig, sps, fromTag, fromRev, toTag, toRev, keepWorkRemnants);

  // 스키마 및 sp에서 변경사항이 있는지 여부 출력.
  let bChanged = checkChanged(mig, sps);

  //bChanged = false; // for test
  
  // 만약 변경사항이 없을 경우 생성된 압축파일은 제거한다.
  if (!bChanged) {
    const outputFile = `uwo_db_diff_${fromTag}_${toTag}.tar.gz`;
    if (isWindows) {
      spawn.execSync(`if exist ${outputFile} ( del /q ${outputFile})`);
    } else {
      spawn.execSync(`rm -rf ${outputFile}`);
    }
  }

  console.log('**********************************************');
  console.log('**********************************************');
  console.log('[info] changed: ', bChanged);
  console.log('**********************************************');
  console.log('**********************************************');
}

main();
