{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T09:00:36.192Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:00:36.202Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:00:37.193Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:00:37.200Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:00:37.201Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T09:00:37.203Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T09:00:37.204Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:37.204Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:37.205Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T09:00:37.205Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:37.205Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:37.206Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T09:00:37.206Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:37.207Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:37.207Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T09:00:37.207Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T09:00:37.207Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:00:37.207Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:00:37.208Z"}
{"environment":"development","type":"authd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"authd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T09:00:39.509Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"*********","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_*********_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:01:11.820Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T09:01:12.247Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T09:01:12.249Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T09:01:12.251Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T09:01:12.252Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T09:01:15.568Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T09:01:15.569Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T09:01:15.569Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T09:01:15.576Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T09:01:15.576Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T09:01:15.591Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T09:01:15.684Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.704Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.721Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.734Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.746Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.760Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.777Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.797Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.817Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T09:01:15.835Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T09:01:15.920Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T09:01:15.921Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T09:01:15.926Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T09:01:16.032Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T09:01:16.035Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T09:01:16.036Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T09:01:16.036Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T09:01:16.039Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T09:01:16.039Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T09:01:16.039Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T09:01:16.039Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T09:01:16.044Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T09:01:16.044Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T09:01:16.045Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T09:01:16.045Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T09:01:16.046Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T09:01:16.048Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T09:01:16.048Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"verbose","message":"PUBSUB register","timestamp":"2025-08-22T09:01:16.931Z"}
{"ch":"kick:authd.0@DESKTOP-2FFOGVN","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:01:16.932Z"}
{"ch":"maxUsersPerWorld","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T09:01:16.932Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"level":"info","message":"redis pool (user-cache-redis) initializing ...","timestamp":"2025-08-22T09:01:16.933Z"}
{"level":"info","message":"redis pool (user-cache-redis) initialized","timestamp":"2025-08-22T09:01:16.973Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (monitor-redis) initializing ...","timestamp":"2025-08-22T09:01:16.974Z"}
{"level":"info","message":"redis pool (monitor-redis) initialized","timestamp":"2025-08-22T09:01:16.984Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (order-redis) initializing ...","timestamp":"2025-08-22T09:01:16.984Z"}
{"level":"info","message":"redis pool (order-redis) initialized","timestamp":"2025-08-22T09:01:16.993Z"}
{"poolCfg":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"level":"info","message":"redis pool (auth-redis) initializing ...","timestamp":"2025-08-22T09:01:16.993Z"}
{"level":"info","message":"redis pool (auth-redis) initialized","timestamp":"2025-08-22T09:01:17.000Z"}
{"level":"info","message":"[MysqlReqRepCounter] setLimit: 100","timestamp":"2025-08-22T09:01:17.000Z"}
{"path":"/checkOrder","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.091Z"}
{"path":"/cancelRevoke","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.121Z"}
{"path":"/checkPreoccupancyCode","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.136Z"}
{"path":"/deleteAccounts5239","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.166Z"}
{"path":"/getWorldStates","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.209Z"}
{"path":"/getWorlds","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.254Z"}
{"path":"/login","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.327Z"}
{"path":"/notifyBoughtWebShopProduct","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.383Z"}
{"path":"/refreshPrologue","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.410Z"}
{"bindAddress":"0.0.0.0","port":10701,"level":"info","message":"start public auth server listening ...","timestamp":"2025-08-22T09:01:17.419Z"}
{"path":"/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.424Z"}
{"path":"/changeUserIdForDevLoad","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.492Z"}
{"path":"/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.508Z"}
{"path":"/deleteAccount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.532Z"}
{"path":"/enterWorld","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:17.548Z"}
{"path":"/getLastLobbyOfOnlineUsers","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.007Z"}
{"path":"/getUserIdByName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.030Z"}
{"path":"/getUserIdsByPubId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.056Z"}
{"path":"/getUserLastWorldId","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.080Z"}
{"path":"/getUserNames","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.096Z"}
{"path":"/logout","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.116Z"}
{"path":"/admin/changeUserNation","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.148Z"}
{"path":"/admin/changeUserName","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.199Z"}
{"path":"/admin/updateUserAccessLevel","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.252Z"}
{"path":"/admin/kick","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.271Z"}
{"path":"/admin/updateUserBlockTimeUtcByAdmin","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T09:01:18.286Z"}
{"bindAddress":"0.0.0.0","port":10700,"level":"info","message":"start private auth server listening ...","timestamp":"2025-08-22T09:01:18.310Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T09:01:18.313Z"}
{"url":"/getWorldStates","status":"200","response-time":"56.427","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:01:37.447Z"}
{"url":"/getWorldStates","status":"200","response-time":"5.422","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:01:42.146Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.494","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:01:47.367Z"}
{"url":"/getWorldStates","status":"200","response-time":"4.895","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:01:52.175Z"}
{"url":"/getWorldStates","status":"200","response-time":"13.521","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:01:57.392Z"}
{"url":"/getWorldStates","status":"200","response-time":"5.119","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:02.212Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.560","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:07.416Z"}
{"url":"/getWorldStates","status":"200","response-time":"2.549","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:12.155Z"}
{"body":{"platform":2,"gnidSessionToken":"8910194**********1755853332","revision":"6dc01e1721a74e7dd6699ce47cf7129c4415b855","patchRevision":"Unknown","deviceType":"Windows","osv":"10.0.26100.1.256.64bit","dm":"","deviceLang":"","lang":"ko","v":"","sk":"","country_ip":"","os_modulation":false,"emulator":false,"loading_time":37400,"loginPlatform":"None"},"platform":"SDO","level":"info","message":"[RX] /getWorlds","timestamp":"2025-08-22T09:02:13.364Z"}
{"_time":"2025-08-22 18:02:13","os":"WINDOWS","osv":"10.0.26100.1.256.64bit","dm":"","lang":"","v":"","sk":"","platform":"None","country_ip":"","os_modulation":false,"emulator":false,"loading_time":37400,"level":"info","message":"[GLOG] collection=common_gnid_login:","timestamp":"2025-08-22T09:02:13.410Z"}
{"body":{"worlds":[{"worldId":"UWO-GL-01","worldName":"UWO-GL-01","order":0,"address":"localhost","port":10100,"bIsNonPK":false,"worldStateCmsId":1}]},"level":"info","message":"[TX] /getWorlds","timestamp":"2025-08-22T09:02:13.410Z"}
{"url":"/getWorlds","status":"200","response-time":"93.131","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:13.411Z"}
{"body":{"platform":2,"sessionToken":"8910194**********1755853332","revision":"6dc01e1721a74e7dd6699ce47cf7129c4415b855","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"platform":"SDO","level":"info","message":"[RX] /login","timestamp":"2025-08-22T09:02:15.447Z"}
{"level":"verbose","message":"request: /useronline?appid=*********&accounttype=25&areaid=1910&groupid=-1&eventtime=2025-08-22 18:02:15&endpointip=127.0.0.1&endpointport=0&guid=6dc372e5b108426fbaab4fb5e8b4dd74&userid=**********&characterid=****************&merchant_name=MEIYU_*********_7864&deviceid=&timestamp=**********&signature_method=md5&signature=87257b1046898b1bb2f7330a03b3b869","timestamp":"2025-08-22T09:02:15.708Z"}
{"return_code":0,"error_type":0,"return_message":"","data":{"appId":*********,"areaId":1910,"groupId":-1,"msg":"","onlineTimeVal":0,"remainingTime":**********,"resultCode":0,"userId":"**********"},"level":"verbose","message":"response:","timestamp":"2025-08-22T09:02:15.959Z"}
{"loginDbResult":{"isOnline":1,"worlds":[{"worldId":"UWO-GL-01","userId":1000,"pubId":"****************","name":"가나다라아아","nationCmsId":********}],"blockTimeUtcByAdmin":null,"bBlockedByAdmin":false,"bIsNewUser":false,"lastWorldId":"UWO-GL-01","lastLobby":"lobbyd.0@DESKTOP-2FFOGVN"},"reqBody":{"platform":2,"sessionToken":"8910194**********1755853332","revision":"6dc01e1721a74e7dd6699ce47cf7129c4415b855","patchRevision":"Unknown","worldId":"UWO-GL-01","cachedAccountId":"","isNewGnid":false,"bPrologue":false,"os":"Windows","osv":"10.0.26100.1.256.64bit","dm":"GenuineIntel|13th Gen Intel(R) Core(TM) i5-13600KF","deviceLang":"ko","sk":"0","udid":"08EDCDEB5D1A9A629E01AFA233CBC2C3","channel":"PC","subChannel":"PC"},"level":"info","message":"loginDbResult","timestamp":"2025-08-22T09:02:15.970Z"}
{"accountId":"**********","maxUsersPerWorld":5000,"curWorldUsers":0,"orderIdCheckThreshold":4000,"debug":{"mode":1},"orderId":0,"lastAllowedOrderId":0,"orderWait":0,"level":"info","message":"[TEMP] login order","timestamp":"2025-08-22T09:02:15.972Z"}
{"accountId":"**********","userId":1000,"lastLobby":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/login user already online","timestamp":"2025-08-22T09:02:15.972Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-22T09:02:15.972Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-22T09:02:15.975Z"}
{"body":{"gnid":"**********","nid":"****************","enterWorldToken":"db02cf265dee1d2ee0b1bbc597fc0869c2c061b7","isNewUser":false,"worldAddresss":"localhost","worldPort":10100,"gnidStatus":"NORMAL","orderWait":0},"level":"info","message":"[TX] /login","timestamp":"2025-08-22T09:02:15.976Z"}
{"url":"/login","status":"200","response-time":"529.868","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:15.976Z"}
{"isDevLogin":0,"sessionToken":"**********","worldId":"UWO-GL-01","enterWorldToken":"db02cf265dee1d2ee0b1bbc597fc0869c2c061b7","lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reconnect":0,"level":"info","message":"/enterWorld","timestamp":"2025-08-22T09:02:16.051Z"}
{"accountId":"**********","userId":1000,"lastLobbyAppId":"lobbyd.0@DESKTOP-2FFOGVN","level":"warn","message":"/user is still online","timestamp":"2025-08-22T09:02:16.052Z"}
{"userId":1000,"lobbyId":"lobbyd.0@DESKTOP-2FFOGVN","reason":1,"level":"info","message":"kicking user ...","timestamp":"2025-08-22T09:02:16.052Z"}
{"msgStr":"{\"userId\":1000}","level":"verbose","message":"auth pubsub kick confirmed","timestamp":"2025-08-22T09:02:16.053Z"}
{"url":"/enterWorld","status":"200","response-time":"10.204","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:16.059Z"}
{"userIds":[],"level":"info","message":"/getUserNames","timestamp":"2025-08-22T09:02:16.169Z"}
{"url":"/getUserNames","status":"200","response-time":"0.984","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:16.169Z"}
{"userIds":[],"level":"info","message":"/getLastLobbyOfOnlineUsers","timestamp":"2025-08-22T09:02:16.179Z"}
{"url":"/getLastLobbyOfOnlineUsers","status":"200","response-time":"2.048","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:16.180Z"}
{"url":"/getWorldStates","status":"200","response-time":"1.585","mcode":0,"level":"info","message":"authd-req","timestamp":"2025-08-22T09:02:18.297Z"}
{"level":"info","message":"[!] server is stopping: type=authd, signal=SIGINT","timestamp":"2025-08-22T09:04:27.689Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T09:04:27.689Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T09:04:28.265Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:04:28.266Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T09:04:28.267Z"}
{"level":"info","message":"redis pool (monitor-redis) destroyed","timestamp":"2025-08-22T09:04:28.268Z"}
{"level":"info","message":"redis pool (order-redis) destroyed","timestamp":"2025-08-22T09:04:28.269Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:28.269Z"}
{"name":"monitor-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:28.269Z"}
{"level":"info","message":"redis pool (auth-redis) destroyed","timestamp":"2025-08-22T09:04:28.269Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:28.269Z"}
{"name":"order-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:28.270Z"}
{"level":"info","message":"redis pool (user-cache-redis) destroyed","timestamp":"2025-08-22T09:04:28.271Z"}
{"level":"info","message":"auth-server closed","timestamp":"2025-08-22T09:04:28.271Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T09:04:28.271Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:28.271Z"}
{"name":"auth-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:28.272Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis close","timestamp":"2025-08-22T09:04:28.272Z"}
{"name":"user-cache-redis","level":"info","message":"[TEMP] ioredis end","timestamp":"2025-08-22T09:04:28.272Z"}
