// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import { Container } from 'typedi';

import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import tuChangeShipName from '../../../mysqllib/txn/tuChangeShipName';
import { Sync, Resp } from '../../type/sync';
import { LobbyService } from '../../server';
import { MError, MErrorCode } from '../../../motiflib/merror';
import cms from '../../../cms';
import mhttp from '../../../motiflib/mhttp';
import * as mutilLanguage from '../../../motiflib/mutilLanguage';

import { ClientPacketHandler } from '../index';
import puCargoLoadPresetUpdate from '../../../mysqllib/sp/puCargoLoadPresetUpdate';
import UserFleets, { CargoLoadPreset } from '../../userFleets';
import mlog from '../../../motiflib/mlog';

// ----------------------------------------------------------------------------
// 적재 비율 프리셋 이름 변경.
// ----------------------------------------------------------------------------

const rsn = 'change_cargo_preset_name';
const add_rsn = null;

// ----------------------------------------------------------------------------
export class Cph_Common_ChangeCargoPresetName implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  exec(user: User, packet: CPacket): Promise<any> {
    const body = packet.bodyObj;

    const id = body.id;
    const newName = body.name;

    const { userDbConnPoolMgr } = Container.get(LobbyService);

    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    if (!user.userFleets.getCargoLoadPresetInfo(id)) {
      throw new MError(
        'invalid-cargo-load-preset',
        MErrorCode.INVALID_REQ_BODY_CHANGE_CARGO_PRESET_NAME,
        {
          id,
        }
      );
    }

    mutilLanguage.ensureNameLength(
      newName,
      cms.Const.LoadagePresetNameMin.value,
      cms.Const.LoadagePresetNameMax.value
    );

    const userFleets: UserFleets = user.userFleets;
    const curName: string = userFleets.getCargoLoadPresetName(id);
    const cargoLoadPreset: CargoLoadPreset = userFleets.getCargoLoadPresetRatios(id);

    if (curName === newName) {
      throw new MError('there-is-no-change', MErrorCode.INVALID_REQ_BODY_CHANGE_CARGO_PRESET_NAME, {
        newName,
        curName,
      });
    }

    return mhttp.lgd
      .hasBadWord(newName)
      .then((bHas) => {
        if (bHas) {
          throw new MError('has-bad-word', MErrorCode.HAS_BAD_WORD, {
            newName,
            curName,
          });
        }
        return puCargoLoadPresetUpdate(
          userDbConnPoolMgr.getPoolByShardId(user.getUserDbShardId()),
          user.userId,
          id,
          newName,
          cargoLoadPreset
        );
      })
      .then(() => {
        // glog
        const sync: Sync = {};
        userFleets.setCargoLoadPresetInfo(
          {
            id,
            name: newName,
            ratios: cargoLoadPreset,
          },
          sync
        );

        return user.sendJsonPacket<Resp>(packet.seqNum, packet.type, { sync });
      });
  }
}
