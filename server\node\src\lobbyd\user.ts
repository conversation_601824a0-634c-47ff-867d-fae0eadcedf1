// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';
import * as net from 'net';
import { Container } from 'typedi';
import moment from 'moment';

import cms from '../cms';
import * as CMSConst from '../cms/const';
import * as cmsEx from '../cms/ex';
import { EncountChoice, ZoneType, isFoodOrWater } from '../cms/ex';
import mhttp from '../motiflib/mhttp';
import mconf from '../motiflib/mconf';
import { MError, MErrorCode } from '../motiflib/merror';
import mlog from '../motiflib/mlog';
import {
  FRIEND_NOTIFICATION_TYPE,
  GuildAppearance,
  GuildData,
  SHIP_ASSIGNMENT,
  TownInfo,
  NationElectionCandidatesModifiedPubMsg,
} from '../motiflib/model/lobby';
import { curTimeUtc, generateEnterWorldToken } from '../motiflib/mutil';
import { EncountResult } from '../motiflib/model/ocean/enum';
import { CompanyStat, CompanyStatParam } from '../motiflib/stat/companyStat';
import * as puUserLoad from '../mysqllib/sp/puUserLoad';
import * as puUserLoad2 from '../mysqllib/sp/puUserLoad2';
import * as proto from '../proto/lobby/proto';
import CryptoContext from './cryptoContext';
import { EncountState, EncountUtil, UserEncount } from './encount';
import * as lobbyPubsub from './lobbyPubsub';
import * as packetHandler from './packetHandler';
import { UserQuestManager } from './quest';
import * as sailingState from './sailing/sailingState';
import * as sync from './type/sync';
import UserAchievement from './userAchievement';
import UserBattle from './userBattle';
import UserDuel from './userDuel';
import { BuffSync, UserBuffs, WorldBuff, WorldBuffNub, WorldBuffUtil } from './userBuffs';
import * as UserConnection from './userConnection';
import UserContentsTerms from './userContentsTerms';
import UserDiscovery from './userDiscovery';
import UserFleets from './userFleets';
import { UserInven } from './userInven';
import UserMails from './userMails';
import { UserManager } from './userManager';
import UserMates from './userMates';
import UserPoints from './userPoints';
import { UserReputation } from './userReputation';
import UserSailing from './userSailing';
import UserShipBlueprints from './userShipBlueprints';
import UserTown from './userTown';
import UserTradeNego from './userTradeNego';
import UserTrade from './userTrade';
import Ship, { ShipNub } from './ship';
import Fleet from './fleet';
import {
  KICK_REASON,
  CHINA_AGE,
  CHINA_UNDER_18_WEEKDAY_PLAY_TIME,
  kickReasonThatShouldChangeIsOnline,
} from '../motiflib/const';
import { LobbyService } from './server';
import UserCashShop from './userCashShop';
import UserState, { GAME_MODE } from './userState';
import puStateUpdateIsOnline from '../mysqllib/sp/puStateUpdateIsOnline';
import {
  CalcKarmaAndGetDecreaseTimesEx,
  GetCurWeatherCmsId,
  SECONDS_PER_HOUR,
  getKarmaDescBasedOnKarmaVal,
  getDateOfSpecificLocalHourOfToday,
  GetFullWeeksUsingLocalTime,
} from '../formula';
import { UserVillage } from './userVillage';
import { getUserDbShardId } from '../mysqllib/mysqlUtil';
import { applyWorldTileBuff as applyWorldTileBuff } from './sailing/worldTileMove';
import UserAttendance from './userAttendance';
import UserPassEvent from './userPassEvent';
import { UserChallenges } from './userChallenges';
import glog from '../motiflib/gameLog';
import { UserPassives } from './userPassives';
import { PacketPerfmon, PacketRecvType, UnitPacketRecvStat } from '../motiflib/packetPerfmon';
import { IOPerfmon, IO_CONNCETION_TYPE, IOType } from '../motiflib/ioPerfmon';
import * as mutil from '../motiflib/mutil';
import { Location } from '../motiflib/model/ocean';
import { getCheatAccessLevel } from '../proto/lobby/cheatAccessLevel';
import puCashShopFixedTermProductDelete from '../mysqllib/sp/puCashShopFixedTermProductDelete';
import { TownManager } from './townManager';
import { WorldEventNotificationData } from './type/worldEventNotification';
import { PayloadFlag } from '../motiflib/model/lobby';
import { EventEmitter } from 'events';
import { LatLonToOceanTileDesc } from '../cms/oceanProp';
import { EnergyChange, UserEnergy } from './userEnergy';
import { UserTickYard } from './userTickYard';
import { UserAdjutantDelegation } from './userAdjutantDelegation';
import { GAME_ENTER_STATE, GAME_STATE } from '../motiflib/model/lobby/gameState';
import { UserBattleFormations } from './userBattleFormations';
import { applyBattleFormationBuff } from './sailing/battleFormation';
import { UserFishing } from './userFishing';
import { UserTriggers } from './userTriggers';
import UserShield from './userShield';
import { UserWorldSkills } from './userWorldSkills';
import { TownUserSyncData } from '../motiflib/model/town';
import { Protocol } from '../proto/oceand-lobbyd/protocol';
import { UserCollection } from './userCollection';
import UserAuction from './userAuction';
import { DisconnectReason, LogoutTask, ITask } from './userConnection';
import { PacketLogger } from './packetLogger';
import { UserGuild } from './userGuild';
import { Sync } from './type/sync';
import { onGuildPublish } from './guildPubsub';
import UserContributionShop from './userContributionShop';
import { UserArena } from './userArena';
import { ArenaHelper } from './arena';
import tuShipReduceLifeAndDurability from '../mysqllib/txn/tuShipReduceLifeAndDurability';
import { LobbyUserGameGuard } from './lobbyGameGuard';
import { UserAppGuard } from './appGuard';
import UserGuildShop from './userGuildShop';
import { RegionOccupation } from './type/worldEventNotification';
import UserShipyardShop from './userShipyardShop';
import UserEventShop from './userEventShop';
import UserExplore from './userExplore';
import { GuildUtil } from './guildUtil';
import { UserSailWaypoints } from './userSailWaypoints';
import { UserTradeArea } from './userTradeArea';
import UserFleetPreset from './userFleetPreset';
import UserRaidTicket from './userRaidTicket';
import { UserEventGames } from './userEventGames';
import UserFriends, { FRIEND_STATE } from './userFriends';
import { FriendUtil } from './friendUtil';
import { UserFleetDispatch } from './userFleetDispatch';
import { UserFleetDispatchSlot } from './userFleetDispatchSlot';
import { RANKING_CMS_ID } from '../cms/rankingDesc';
import UserRanking from './userRanking';
import { getGLogCoordinate } from '../cms/oceanCoordinate';
import { GLOG_LOGIN_OUT_SAVE_TYPE } from './const';
import UserChatTranslationCount from './userChatTranslationCount';
import { UserToast } from './userToast';
import { UserLiveEvents } from './userLiveEvents';
import UserGuildRaidTicket from './userGuildRaidTicket';
import UserEventRanking from './userEventRanking';
import UserDiscoveryReward from './userDiscoveryReward';
import { KarmaDesc } from '../cms/karmaDesc';
import { UserSailingDiaries } from './userSailingDiaries';
import { getHotTimeBuff, HotTimeBuffDesc } from '../cms/hotTimeBuffDesc';
import { getHotTimeBuffCms } from '../cms/ex';
import { UserTitles } from './userTitles';
import { UserNation } from './userNation';
import { GuildBuffDesc } from '../cms/guildBuffDesc';
import { TownDesc } from '../cms/townDesc';
import { amICandidate } from './nationManager';
import { NationUtil } from '../motiflib/model/lobby/nationUtil';
import UserSweepTicket from './userSweepTicket';
import { UserPets } from './userPets';
import { UserSmuggle } from './userSmuggle';
import UserShopRestricted from './userShopRestricted';
import { UserNpcShop } from './userNpcShop';
import { UserBlindBid } from './userBlindBid';
import { UserInfiniteLighthouse } from './userInfiniteLighthouse';
import {
  FriendlyEncountResult,
  FriendlyEncountState,
  UserFriendlyEncount,
} from './userFriendlyEncount';
import { RegionDesc } from '../cms/regionDesc';
import { UserResearch } from './userResearch';
import { UserReentry } from './userReentry';
import { UserClash } from './userClash';
import { CLASH_MATCHING_STATE } from './clash';
import { ClashPrizeManager } from './clashPrizeManager';
import { error } from 'console';
import UserTutorial from './userTutorial';
import { UserManufacture } from './userManufacture';
// import { togglet } from '../motiflib/togglet';
// import { Context } from 'unleash-client/lib/context';

// ----------------------------------------------------------------------------
// User object.
// ----------------------------------------------------------------------------
export class UserSocket extends net.Socket {
  id: string;
  user: User;
}

export interface LoginInfo extends puUserLoad.Result, puUserLoad2.Result {
  deviceType?: string;
}

export interface UserExpLevelChange {
  exp: number;
  addedExp?: number;
  oldLevel: number;
  level: number;
  energyChange?: EnergyChange;
}

export interface UserDevParams {
  // 유저가 인카운트 피겨 후 choice에 대한 결과값
  // 1: 허용(allow), 2: 거부(deny)
  devChoiceResult: number;
  //1:전투시작 2:도주: 3:항복 4:교섭
  devEncountTargetDefChoice: EncountChoice;

  // 디버프 면역
  debuffImmune: boolean;

  // 재해 면역
  disasterImmune: boolean;

  // 재해 스텟 클라 출력
  showDisasterStat: boolean;

  // 교역품 번식 확률  100%
  tradeGoodsBreedSuccess: boolean;

  // 로컬NPC 스폰 수량 조절.
  enableNumSpawn: boolean;
  occupyingBattle: number;
  occupyingTrade: number;
  occupyingExploration: number;
  occupiedBattle: number;
  occupiedTrade: number;
  occupiedExploration: number;
  randomBattle: number;
  randomTrade: number;
  randomExploration: number;
  pirateBattle: number;
  npcNoEncountMsg: boolean;
  guildDailyReset: boolean;
  guildWeeklyReset: boolean;

  // 파견에서 액션들의 결과 합산값 클라 출력
  showDispatchActionResultStat: boolean;

  // 합성 대성공 확률
  guildSynthesisProb: number;
}

export interface KarmaChange {
  karma: number;
  lastUpdateTimeUtc: number;
}

export interface GLogParam {
  user: User;
  rsn?: string;
  add_rsn?: string;
}

// ----------------------------------------------------------------------------
// User object.
// ----------------------------------------------------------------------------
export class User {
  private _userConn: UserConnection.UserConnection;
  private _bWorkingTask: boolean = false;

  private _kicked: { reason: KICK_REASON; authdId: string } = null;
  private _disconnectReason: UserConnection.DisconnectReason | null = null;

  private _lastPingTimeUtc: number = curTimeUtc();
  private _lastPingPongTickTImeUtc: number = 0;
  private _lastRefreshEnterTokenTimeUtc: number = 0;

  private _accountId: string = ''; // 라인의 경우 gnid
  private _pubId: string = ''; // 라인의 경우 nid
  private _userId: number = 0;
  // 2022년 11월10에 만들어졌고 라이브의 경우 11월23일에 업데이트 됨.
  // 따라서 그 이전에 만들어진 서버의 경우 정확하지 않은 값이 들어가있음.
  private _createTimeUtc: number = 0;
  private _userDbShardId: number;
  private _accessLevel: number = 0;
  private _revision: string = null; // git hash
  private _patchRevision: string = null; // patch git hash
  private _versionText: string = null; // [ReleaseVersion].[PatchNumber].[BuildNumber]
  private _appVersion: string | undefined | null;
  private _countryCreated: string = 'KR'; // 계정생성시 국가코드 (라인 게임즈)
  private _userName: string = null;
  private _lang: string = ''; // WBP_LanguagePopup 의 CultureNames
  private _lineLangCulture: string = ''; // 라인에서 사용하는 ko_KR 와 같은 형식의 언어지역값
  private _countryIp: string = null; // ex) KR
  private _deviceType: string = '';
  private _storeCode: string = 'FLOOR_STORE';
  private _companyJobCmsId: number = 0;
  private _nationCmsId: number = 0;
  private _exp: number = 0;
  private _level: number = 1;
  private _loginTimeUtc: number = 0;
  private _lastGlogLoginOutSaveTimeUtc: number = 0;

  // 클라이언트 SDK 에서 로그인할 때 보내는 정보들. ( adjust, glog 등 관련 )
  // lobbyd/packetHandler/auth/enterWorld.ts RequestBody 참고
  private _osv: string | undefined | null;
  private _deviceLang: string | undefined | null;
  private _adjustId: string | undefined | null;
  private _gpsAdid: string | undefined | null;
  private _udid: string | undefined | null;
  private _idfa: string | undefined | null;
  private _idfv: string | undefined | null;
  private _steamAppId: number | undefined | null;
  private _steamUserId: BigInt;

  private _chinaAge: CHINA_AGE = CHINA_AGE.ADULT;
  private _chinaUnder18LastPlayTimeSec: number = 0;

  private _karma: number = 0;
  private _lastKarmaUpdateTimeUtc: number = 0;

  private _lastCompanyJobUpdateTimeUtc = 0;

  private _freeLeaderMateSwitchCount: number = 0;
  private _freeLastLeaderMateSwitchTimeUtc: number | null = null;

  // 클라이언트가 백그라운드 상태로 진입할 경우 ping pong timeout 유예.
  private _bIsOnBackgroundClient: boolean = false;

  private _isPushNotificationAllowed: number = 0; // 0 or 1
  private _isNightPushNotificationAllowed: number = 0; // 0 or 1
  private _allowedPushNotificationGroupIds: number[] = [];

  private _lastReceiveHotTimeUtc: number = 0;

  private _lastUpdateHotTimeBuffUtc: number = 0;

  private _lastUpdateNationTimeUtc: number = 0;

  private _lastTickTimeInMs: number = Date.now();

  private _isAdmiralProfileOpened: number = 0;
  private _isFirstFleetProfileOpened: number = 0;
  private _isFriendlyBattleRequestable: number = 0;

  // offlineSailing bot 관련
  private _lastUserHeartBeatTimeUtc: number = curTimeUtc();
  private _isOfflineSailingBot: boolean = false;

  // 테스트용 더미봇 관련
  private _isTestBot: boolean = false;

  // 로그아웃을 기다려야 하는 경우 사용하는 이벤트 오브젝트.
  private _logoutEvent: EventEmitter;

  // 빌링을 위한 timeout
  private _timeoutForBilling: number = undefined;

  // 로그인 후 첫 로딩인지
  private _bFirstLoadComplete: boolean = true;

  // 연속 전투 여부
  private _isBattleContinuous: number = 0;

  private _hasLoggedIn: boolean = false;

  // User sub objects.
  userGameGuard: LobbyUserGameGuard;
  userAppGuard: UserAppGuard;
  userFleets: UserFleets = new UserFleets();
  userShipBlueprints: UserShipBlueprints = new UserShipBlueprints();
  userMates: UserMates = new UserMates();
  userInven: UserInven = new UserInven();
  userContentsTerms: UserContentsTerms = new UserContentsTerms();
  userMails: UserMails = new UserMails();
  questManager: UserQuestManager = new UserQuestManager();
  userBuffs: UserBuffs = new UserBuffs();
  userPassives: UserPassives = new UserPassives();
  userPoints: UserPoints = new UserPoints();
  userTown = new UserTown();
  userReputation = new UserReputation();
  userEncount: UserEncount = new UserEncount();
  userDiscovery: UserDiscovery = new UserDiscovery();
  userSailing: UserSailing = new UserSailing();
  userBattle: UserBattle = new UserBattle();
  userDuel: UserDuel = new UserDuel();
  companyStat: CompanyStat = new CompanyStat();
  userAchievement: UserAchievement = new UserAchievement();
  userTradeNego: UserTradeNego = new UserTradeNego();
  userTrade: UserTrade = new UserTrade();
  userCashShop: UserCashShop = new UserCashShop();
  userShipyardShop: UserShipyardShop = new UserShipyardShop();
  userShopRestricted: UserShopRestricted = new UserShopRestricted();
  userEventShop: UserEventShop = new UserEventShop();
  userDevParams: UserDevParams = {
    devChoiceResult: 0,
    devEncountTargetDefChoice: 0,
    debuffImmune: false,
    disasterImmune: false,
    showDisasterStat: false,
    tradeGoodsBreedSuccess: false,
    enableNumSpawn: false,
    occupyingBattle: 0,
    occupyingTrade: 0,
    occupyingExploration: 0,
    occupiedBattle: 0,
    occupiedTrade: 0,
    occupiedExploration: 0,
    randomBattle: 0,
    randomTrade: 0,
    randomExploration: 0,
    pirateBattle: 0,
    npcNoEncountMsg: false,
    guildDailyReset: false,
    guildWeeklyReset: false,
    guildSynthesisProb: -1,
    showDispatchActionResultStat: false,
  };
  userState: UserState = new UserState();
  userVillage: UserVillage = new UserVillage();
  userAttendance: UserAttendance = new UserAttendance();
  userPassEvent: UserPassEvent = new UserPassEvent();
  userChallenges: UserChallenges = new UserChallenges();
  userEnergy: UserEnergy = new UserEnergy();
  userAdjutantDelegation = new UserAdjutantDelegation();
  userBattleFormations = new UserBattleFormations();
  userFishing = new UserFishing();
  userTriggers: UserTriggers = new UserTriggers();
  userShield: UserShield = new UserShield();
  userWorldSkills: UserWorldSkills = new UserWorldSkills();
  userCollection: UserCollection = new UserCollection();
  userAuction: UserAuction = new UserAuction();
  userGuild: UserGuild = new UserGuild();
  userContributionShop: UserContributionShop = new UserContributionShop();
  userGuildShop: UserGuildShop = new UserGuildShop();
  userArena: UserArena = new UserArena();
  userExplore: UserExplore = new UserExplore();
  userSailWaypoints: UserSailWaypoints = new UserSailWaypoints();
  userTradeArea: UserTradeArea = new UserTradeArea();
  userFleetPreset: UserFleetPreset = new UserFleetPreset();
  userRaidTicket: UserRaidTicket = new UserRaidTicket();
  userGuildRaidTicket: UserGuildRaidTicket = new UserGuildRaidTicket();
  userEventGames: UserEventGames = new UserEventGames();
  userFriends: UserFriends = new UserFriends();
  userFleetDispatch: UserFleetDispatch = new UserFleetDispatch();
  userFleetDispatchSlot: UserFleetDispatchSlot = new UserFleetDispatchSlot();
  userRanking: UserRanking = new UserRanking();
  userChatTranslationCount: UserChatTranslationCount = new UserChatTranslationCount();
  userToast: UserToast = new UserToast();
  userLiveEvents: UserLiveEvents = new UserLiveEvents();
  userEventRanking: UserEventRanking = new UserEventRanking();
  userDiscoveryReward: UserDiscoveryReward = new UserDiscoveryReward();
  userSailingDiaries: UserSailingDiaries = new UserSailingDiaries();
  userTitles: UserTitles = new UserTitles();
  userNation: UserNation = new UserNation();
  userSweepTicket: UserSweepTicket = new UserSweepTicket();
  userPets: UserPets = new UserPets();
  userSmuggle: UserSmuggle = new UserSmuggle();
  userNpcShop: UserNpcShop = new UserNpcShop();
  userBlindBid: UserBlindBid = new UserBlindBid();
  userInfiniteLighthouse: UserInfiniteLighthouse = new UserInfiniteLighthouse();
  userFriendlyEncount: UserFriendlyEncount = new UserFriendlyEncount();
  userResearch: UserResearch = new UserResearch();
  userReentry: UserReentry = new UserReentry();
  userClash: UserClash = new UserClash();
  userTutorial: UserTutorial = new UserTutorial();
  userManufacture: UserManufacture = new UserManufacture();

  constructor(sock: UserSocket, botId?: string, userId?: number) {
    if (sock) {
      this._userConn = new UserConnection.UserRealConnection(sock);
    } else {
      this._userConn = new UserConnection.UserBotConnection(botId, userId);
    }
  }

  // [WARN]: 만든이 입장에서 필요한 property 만 clone 되어있습니다.
  // 따라서 사용 시 clone 이 안 된 부분이 있을 경우 추가 구현이 필요합니다.
  incompleteClone(): User {
    const c = new User(null);
    c.userId = this.userId;
    c.nationCmsId = this.nationCmsId;
    c._exp = this.exp;
    c._level = this.level;
    c.companyJobCmsId = this.companyJobCmsId;
    c.userFleets = this.userFleets.clone();
    c.userShipBlueprints = this.userShipBlueprints.clone();
    c.userMates = this.userMates.clone();
    c.userInven = this.userInven.clone();
    c.userPoints = this.userPoints.clone();
    c.userReputation = this.userReputation.clone();
    c.companyStat = this.companyStat.clone();
    return c;
  }

  incompleteCloneSet(): void {}

  getConnId(): string {
    return this.userConn.id;
  }

  getConnState(): UserConnection.CONNECTION_STATE {
    return this.userConn.state;
  }

  setConnState(connState: UserConnection.CONNECTION_STATE): void {
    this.userConn.state = connState;
  }

  getUserDbShardId(): number {
    if (this._userDbShardId === undefined) {
      if (!this.userId) {
        throw new MError('invalid-user-id-for-get-user-db-shard-id', MErrorCode.INVALID_USER_ID);
      }
      this._userDbShardId = getUserDbShardId(this.userId);
    }

    return this._userDbShardId;
  }

  public isTestBot(): boolean {
    return this._isTestBot;
  }
  public setIsTestBot(value: boolean) {
    this._isTestBot = value;
  }

  isWaterOrFoodEmpty() {
    const userFirstFleet: Fleet = this.userFleets.getFirstFleet();
    const ships: { [id: number]: Ship } = userFirstFleet.getShips();
    for (const [, ship] of Object.entries(ships)) {
      if (!ship.isWaterOrFoodEmpty()) {
        return false;
      }
    }
    return true;
  }

  isWaterAndFoodEmpty() {
    const userFirstFleet: Fleet = this.userFleets.getFirstFleet();
    const ships: { [id: number]: Ship } = userFirstFleet.getShips();
    for (const [, ship] of Object.entries(ships)) {
      if (!ship.isWaterAndFoodEmpty()) {
        return false;
      }
    }
    return true;
  }

  getCryptoContext(): CryptoContext {
    return this.userConn.getCryptoContext();
  }

  disconnect(disconnectReason: UserConnection.DisconnectReason): void {
    mlog.verbose('Disconnecting user.', {
      userId: this.userId,
      connId: this.userConn.id,
      disconnectReason,
    });

    if (this._disconnectReason) {
      mlog.warn('Trying to set disconnect reason more than once.', {
        userId: this.userId,
        connId: this.userConn.id,
        old: this._disconnectReason,
        disconnectReason,
      });
    } else {
      this._disconnectReason = disconnectReason;
    }

    this.userConn.disconnect();
  }

  async waitForLogout(): Promise<boolean> {
    this._logoutEvent = new EventEmitter();

    const successPromise = new Promise((resolve) => {
      this._logoutEvent.on('', () => {
        resolve(true); // Successful.
      });
    });

    const timeoutPromise = new Promise((resolve) => {
      setTimeout(() => {
        resolve(false);
      }, 3000); // Wait for 3 seconds.
    });

    return Promise.race([successPromise, timeoutPromise]).then((bOk: boolean) => {
      if (!bOk) {
        mlog.error('Timed out while waiting for logout!', {
          userId: this._userId,
        });
      }

      return bOk;
    });
  }

  onSocketRecv(buf: Buffer) {
    try {
      mlog.debug('received from client', {
        connId: this.userConn.id,
        bytes: buf.byteLength,
      });

      const ioPerfmon = Container.get(IOPerfmon);
      ioPerfmon.addIOBytes(IO_CONNCETION_TYPE.USER, IOType.READ, buf.byteLength);

      const payloadQueuedSize: number = this.userConn.getTaskQueued();

      // 패킷 점점 쌓일경우 경우..(처리되는 양보다 쌓이는 양이 많거나, 클라이언트가 필요이상으로 과도한 패킷을 보내는경우)
      if (payloadQueuedSize > (mconf.maximumPayloadQueueSize || 10)) {
        mlog.error('[onRecv] payloadSize-has-been-exceeded', {
          userId: this.userId,
          connId: this.userConn.id,
          payloadQueuedSize,
          maximumPayloadQueueSize: mconf.maximumPayloadQueueSize,
        });

        this.userConn.printTaskDump(this);

        this.disconnect(UserConnection.DisconnectReason.ExceededPayload);
        return;
      }

      this.userConn.readPayloads(buf);
    } catch (error) {
      mlog.error('onRecv failed', {
        connId: this.userConn.id,
        userId: this.userId,
        msg: error.message,
        stack: error.stack,
      });

      this.disconnect(UserConnection.DisconnectReason.OnRecvError);
    }
  }

  onRecvBufferProxy(packetType: number, packet: any) {
    try {
      const payloadQueuedSize: number = this.userConn.getTaskQueued();

      // 패킷 처리량이 점점 많아질 경우..
      if (payloadQueuedSize > (mconf.maximumPayloadQueueSize || 10)) {
        mlog.error('[onRecvBuffProxy] payloadSize-has-been-exceeded', {
          userId: this.userId,
          connId: this.userConn.id,
          payloadQueuedSize,
          maximumPayloadQueueSize: mconf.maximumPayloadQueueSize,
        });

        this.disconnect(UserConnection.DisconnectReason.ExceededPayload);
        return;
      }

      this.userConn.readBufferProxyPayload(this.userId, packet.data);
    } catch (error) {
      mlog.error('onRecvBuffProxy failed', {
        connId: this.userConn.id,
        userId: this.userId,
        msg: error.message,
        stack: error.stack,
      });

      this.disconnect(UserConnection.DisconnectReason.OnRecvError);
    }
  }

  onSocketClose(): void {
    if (!this._disconnectReason) {
      this._disconnectReason = UserConnection.DisconnectReason.SocketClosed;
    }

    mlog.info('onSocketClose', {
      userId: this.userId,
      isBot: this.isOfflineSailingBot,
    });

    // 로그아웃 전용 payload 를 만들어 queue 에 넣는다.
    // [주의] payload 를 처리하는 문맥에서는 호출되면 안된다.
    this.enqueueTask(new LogoutTask());
  }

  // 패킷 및 등록 된 task 처리용 tick
  private _taskTick(): void {
    // 아직 task 처리 중 일때..
    if (this._bWorkingTask) {
      return;
    }

    const task = this.userConn.dequeueTask();
    if (!task) {
      return;
    }

    this._bWorkingTask = true;

    Promise.resolve()
      .then(() => {
        return task.execute(this);
      })
      .catch((e) => {
        // execute함수 내부에 예외처리를 안할 경우 이곳으로 호출된다.
        mlog.error('[failed to execute at taskTick', {
          userId: this.userId,
          msg: e.message,
          stack: e.stack,
          extra: e.extra,
        });
      })
      .finally(() => {
        this._bWorkingTask = false;
      });
  }

  processPayload(payload: UserConnection.Payload): Promise<boolean | void> {
    const dStart = new Date();
    let packet: UserConnection.CPacket = null;

    return this.userConn
      .parsePacket(payload.flags, payload.buffer)
      .then((parsedPacket: UserConnection.CPacket) => {
        packet = parsedPacket;

        PacketLogger.logRx(this.userId, packet.type, payload.buffer.byteLength, packet.bodyObj);

        // process packet
        return this.processPacket(packet);
      })
      .then(() => {
        const netPerfmon = Container.get(PacketPerfmon);
        const unitPacketStat: UnitPacketRecvStat = {
          packetId: packet.type,
          packetIdStr: proto.toString(packet.type),
          size: payload.buffer.byteLength,
          duration: Math.abs(new Date().getTime() - dStart.getTime()),
        };
        netPerfmon.addPacketRecvStat(PacketRecvType.USER_PACKET_RECEIVED, unitPacketStat);
      })
      .catch((err) => {
        const packetType = packet ? packet.type : 0;
        mlog.warn('packet processed with error', {
          userId: this.userId,
          packetType,
          typeStr: proto.toString(packetType),
          mcode: err.mcode,
          error: err.message,
          extra: err.extra,
          stack: err.stack,
          time: Math.abs(new Date().getTime() - dStart.getTime()),
          connId: this.getConnId(),
        });

        if (!(err instanceof MError)) {
          mlog.error('unidentified exception', {
            connId: this.userConn.id,
            userId: this.userId,
            error: err.message,
            stack: err.stack,
          });

          this.disconnect(UserConnection.DisconnectReason.UnidentifiedException);
          return;
        }

        if (
          err instanceof MError &&
          (mconf.isDev || err.mcode >= MErrorCode.NON_FATAL_ERROR_MARK)
        ) {
          return this.userConn.sendMError(
            this.userId,
            packet ? packet.seqNum : -1,
            packet ? packet.type : -1,
            err,
            this.isOfflineSailingBot
          );
        }

        this.disconnect(UserConnection.DisconnectReason.ErrorOccurred);
      })
      .catch((err) => {
        // Just in case something goes wrong.
        mlog.error('caught again', {
          connId: this.userConn.id,
          userId: this.userId,
          error: err.message,
          stack: err.stack,
        });

        this.disconnect(UserConnection.DisconnectReason.ErrorOccurred);
      });
  }

  enqueueTask(task: ITask) {
    this.userConn.enqueueTask(task);
  }

  login(
    loginInfo: LoginInfo,
    loginTimeUtc: number,
    deviceType: string,
    sk: string,
    countryIp: string,
    appVersion: string | undefined | null,
    lineLangCulture: string,
    deviceData?: {
      osv: string;
      deviceLang: string;
      adjsutId: string;
      gpsAdid: string;
      udid: string;
      idfa: string;
      idfv: string;
      steamAppId: number;
      steamUserId: BigInt;
    }
  ): void {
    mlog.debug('loginInfo:', loginInfo);

    this._userId = loginInfo.userId;
    this._createTimeUtc = parseInt(loginInfo.createTimeUtc, 10);
    this.userName = loginInfo.name;
    this._lang = loginInfo.lang;
    this._countryIp = countryIp;
    this._deviceType = deviceType;
    this._appVersion = appVersion;

    if (deviceData) {
      this._osv = deviceData.osv;
      this._deviceLang = deviceData.deviceLang;
      this._adjustId = deviceData.adjsutId;
      this._gpsAdid = deviceData.gpsAdid;
      this._udid = deviceData.udid;
      this._idfa = deviceData.idfa;
      this._idfv = deviceData.idfv;
      this._steamAppId = deviceData.steamAppId;
      this._steamUserId = deviceData.steamUserId;
    }

    this._lineLangCulture = lineLangCulture ? lineLangCulture.replace('-', '_') : 'ko_KR';

    if ((mconf.isDev && sk) || !mconf.isDev) {
      this._storeCode = sk;
    }

    // 로그인 하자마자 refrsh token 을 하지 않게 하기 위해.
    this._lastRefreshEnterTokenTimeUtc = Math.floor(Date.now() / 1000);

    this.setConnState(UserConnection.CONNECTION_STATE.LOGGED_IN);

    // 로그인 상태가 되면, ping-pong 으로 이상을 감지하기 때문에,
    // socket timeout 을 초기화 한다. (0으로)
    this._userConn.clearSocketTimeout();

    this.companyJobCmsId = loginInfo.companyJobCmsId;
    this._exp = loginInfo.exp;
    this._level = loginInfo.level;

    this.nationCmsId = loginInfo.nationCmsId;

    this._loginTimeUtc = loginTimeUtc;
    this._lastGlogLoginOutSaveTimeUtc = loginTimeUtc;

    this._karma = loginInfo.karma;
    this._lastKarmaUpdateTimeUtc = loginInfo.lastKarmaUpdateTimeUtc
      ? parseInt(loginInfo.lastKarmaUpdateTimeUtc, 10)
      : null;

    this._lastCompanyJobUpdateTimeUtc = parseInt(loginInfo.lastCompanyJobUpdateTimeUtc, 10);

    this._freeLeaderMateSwitchCount = loginInfo.freeLeaderMateSwitchCount;
    this._freeLastLeaderMateSwitchTimeUtc = loginInfo.freeLastLeaderMateSwitchTimeUtc
      ? parseInt(loginInfo.freeLastLeaderMateSwitchTimeUtc, 10)
      : null;

    this._isPushNotificationAllowed = loginInfo.gameOptionPushNotification.isAllowed;
    this._isNightPushNotificationAllowed = loginInfo.gameOptionPushNotification.isNightAllowed;
    this._allowedPushNotificationGroupIds = loginInfo.gameOptionPushNotification
      .allowedPushNotificationGroupIds
      ? JSON.parse(loginInfo.gameOptionPushNotification.allowedPushNotificationGroupIds)
      : null;

    this._lastReceiveHotTimeUtc = loginInfo.lastReceiveHotTimeUtc
      ? parseInt(loginInfo.lastReceiveHotTimeUtc, 10)
      : null;
    this._lastUpdateNationTimeUtc = loginInfo.lastUpdateNationTimeUtc
      ? parseInt(loginInfo.lastUpdateNationTimeUtc, 10)
      : null;

    this._isAdmiralProfileOpened = loginInfo.isAdmiralProfileOpened;
    this._isFirstFleetProfileOpened = loginInfo.isFirstFleetProfileOpened;
    this._isFriendlyBattleRequestable = loginInfo.isFriendlyBattleRequestable;

    this.userReputation.initWithLoginInfo(loginInfo);
    this.userInven.initWithLoginInfo(loginInfo);
    this.userMates.initWithLoginInfo(loginInfo, this.companyStat);
    this.userFleets.initWithLoginInfo(loginInfo, this);
    this.userShipBlueprints.initWithLoginInfo(loginInfo);
    this.userMails.initWithLoginInfo(loginInfo);
    this.userBuffs.initWithLoginInfo(loginInfo, this);
    this.questManager.initWithLoginInfo(loginInfo);
    this.userSailing.initWithLoginInfo(loginInfo);
    this.userTriggers.initWithLoginInfo(loginInfo);
    this.userPassives.initWithLoginInfo(
      loginInfo,
      this.userMates,
      this.userFleets,
      this.userInven,
      this.userShipBlueprints,
      this.questManager,
      this.userSailing,
      this.userBuffs,
      this.userTriggers
    );
    this.userDiscovery.initWithLoginInfo(loginInfo);
    this.userPoints.initWithLoginInfo(loginInfo);
    this.userTown.initWithLoginInfo(loginInfo);
    this.userBattle.initWithLoginInfo(loginInfo);
    this.companyStat.setUserId(this.userId);
    this.userContentsTerms.initWithLoginInfo(loginInfo);
    this.userAchievement.initWithLoginInfo(loginInfo);
    this.userTrade.initWithLoginInfo(loginInfo);
    this.userCashShop.initWithLoginInfo(loginInfo);
    this.userState.initWithLoginInfo(loginInfo, this);
    this.userVillage.initWithLoginInfo(loginInfo);
    this.userAttendance.initWithLoginInfo(loginInfo);
    this.userPassEvent.initWithLoginInfo(loginInfo);
    this.userChallenges.initWithLoginInfo(loginInfo);
    this.userEncount.initWithLoginInfo(loginInfo);
    this.userEnergy.initWithLoginInfo(loginInfo);
    this.userAdjutantDelegation.initWithLoginInfo(loginInfo);
    this.userBattleFormations.initWithLoginInfo(loginInfo);
    this.userShield.initWithLoginInfo(loginInfo);
    this.userWorldSkills.initWithLoginInfo(loginInfo);
    this.userCollection.initWithLoginInfo(this, loginInfo);
    this.userGuild.initWithLoginInfo(loginInfo);
    this.userContributionShop.initWithLoginInfo(loginInfo);
    this.userGuildShop.initWithLoginInfo(loginInfo);
    this.userArena.initWithLoginInfo(loginInfo);
    this.userShipyardShop.initWithLoginInfo(loginInfo);
    this.userShopRestricted.initWithLoginInfo(loginInfo);
    this.userExplore.initWithLoginInfo(loginInfo);
    this.userEventShop.initWithLoginInfo(loginInfo);
    this.userSailWaypoints.initWithLoginInfo(loginInfo);
    this.userTradeArea.initWithLoginInfo(loginInfo);
    this.userFleetPreset.initWithLoginInfo(loginInfo);
    this.userRaidTicket.initWithLoginInfo(loginInfo, this);
    this.userGuildRaidTicket.initWithLoginInfo(loginInfo, this);
    this.userEventGames.initWithLoginInfo(loginInfo);
    this.userFriends.initWithLoginInfo(loginInfo, this);
    this.userFleetDispatch.initWithLoginInfo(loginInfo);
    this.userFleetDispatchSlot.initWithLoginInfo(loginInfo);
    this.userToast.initWithLoginInfo();
    this.userEventRanking.initWithLoginInfo(loginInfo, this._userId);
    this.userDiscoveryReward.initWithLoginInfo(loginInfo);
    this.userSailingDiaries.initWithLoginInfo(loginInfo, this);
    this.userFishing.initWithLoginInfo(loginInfo);
    this.userTitles.initWithLoginInfo(loginInfo);
    this.userNation.initWithLoginInfo(loginInfo);
    this.userSweepTicket.initWithLoginInfo(loginInfo);
    this.userPets.initWithLoginInfo(loginInfo);
    this.userSmuggle.initWithLoginInfo(loginInfo);
    this.userNpcShop.initWithLoginInfo(loginInfo);
    this.userBlindBid.initWithLoginInfo(loginInfo);
    this.userInfiniteLighthouse.initWithLoginInfo(loginInfo);
    this.userResearch.initWithLoginInfo(loginInfo);
    this.userReentry.initWithLoginInfo(loginInfo);
    this.userClash.initWithLoginInfo(loginInfo);
    this.userTutorial.initWithLoginInfo(loginInfo);
    this.userManufacture.initWithLoginInfo(loginInfo);

    // 스탯 초기화.
    this.rebuildStat();

    // 혹시 게임오버 상태인지?
    this.userSailing.updateGameOver(this, this.userState.isInTown(), false, undefined, true);

    mlog.info('user login', {
      userId: this._userId,
      pubId: this.pubId,
      connId: this.userConn.id,
      isBot: this.isOfflineSailingBot,
      // loginInfo: JSON.stringify(loginInfo),
    });

    const userManager = Container.get(UserManager);
    userManager.addLoggedInUser(this);

    this.userRanking.initWorldRankingScore(this);
    this.userChatTranslationCount.initWithLoginInfo(loginInfo);

    // 게임가드 + 앱가드
    this.initGameGuard();
    this.initAppGuard();
  }

  initGameGuard(): void {
    if (this._isOfflineSailingBot) {
      return;
    }

    if (this._deviceType !== 'Windows') {
      return;
    }

    if (!mconf.gameGuard.active) {
      return;
    }

    this.userGameGuard = new LobbyUserGameGuard(this._userId);
    this.userGameGuard.init();
  }

  initAppGuard(): void {
    if (this._isOfflineSailingBot) {
      return;
    }

    if (!mconf.appGuard || !mconf.appGuard.active) {
      return;
    }

    if (this._deviceType !== 'Android' && this._deviceType !== 'IOS') {
      return;
    }

    this.userAppGuard = new UserAppGuard(this._deviceType);

    mlog.debug('AppGuard initialized', {
      userId: this._userId,
      appGuardId: this.userAppGuard.getId(),
    });
  }

  getAppGuardId(): string {
    if (!this.userAppGuard) {
      return undefined;
    }

    return this.userAppGuard.getId();
  }

  hasLoggedIn(): boolean {
    return this._hasLoggedIn;
  }

  setLoggedIn(): void {
    this._hasLoggedIn = true;
  }

  async logout(): Promise<void> {
    const userManager = Container.get(UserManager);
    userManager.removeUser(this._userConn.id);

    const now = curTimeUtc();
    if (this.getConnState() === UserConnection.CONNECTION_STATE.LOGGED_OUT) {
      mlog.warn('User logout when already logged out', {
        userId: this.userId,
        connState: this.getConnState(),
      });
      return Promise.resolve();
    }

    this.setConnState(UserConnection.CONNECTION_STATE.LOGGED_OUT);

    const { userId } = this;

    if (userId === 0) {
      // 아직 로그인이 안된 유저.
      return Promise.resolve();
    }
    /* 
    if (this.userState.getGameState() === GAME_STATE.ACCOUNT_CREATED) {
      // 아직 로그인이 안된 유저.
      return Promise.resolve();
    }
    */

    this.userState.commonDurationGlogForLogout(this);

    this.glogLoginOutSaveForLogout();

    // (로그인된) 유저 관리자에서 제거.
    if (this.isOfflineSailingBot) {
      // bot 이 로그아웃시 실제유저를 로그아웃시키지않게하기
      const prevUser = userManager.getUserByUserId(userId);
      if (prevUser && prevUser.isOfflineSailingBot) {
        userManager.removeLoggedInUser(userId);
      }
    } else {
      userManager.removeLoggedInUser(userId);
    }

    if (this.userGameGuard) {
      this.userGameGuard.destroy();
    }

    const { userCacheRedis, userDbConnPoolMgr } = Container.get(LobbyService);

    return Promise.resolve()
      .then(() => {
        return this.userFriendlyEncount.endEncount(
          this,
          FriendlyEncountResult.CANCELED_BY_LOGOUT,
          null,
          null,
          null,
          true
        );
      })
      .then(() => {
        // town/ocean 상태에 따라, 그만의 '나가기' 처리.
        if (this.userState.isInTown()) {
          const townInfo = this.userTown.getTownInfo();
          if (townInfo) {
            const townApi = mhttp.townpx.channel(townInfo.url);
            return townApi.leave(this.userId).catch((e) => {
              // 실패를 하더라도 로그아웃 처리는 그대로 진행 되어야한다.
              mlog.error('failed-town-Leave-while-processing-logout ', {
                userId,
                msg: e.message,
                stack: e.stack,
              });
            });
          }
          return null;
        } else if (this.userState.isInOcean()) {
          return this.userSailing.logout(this);
        }
        return null;
      })
      .then(() => {
        // 모의전 캐싱정보 저장
        if (this.userArena.isActivated()) {
          return this.userArena.saveDirtyInArena();
        }
      })
      .then(() => {
        const state: CLASH_MATCHING_STATE = this.userClash.getMatchingState();
        if (state === CLASH_MATCHING_STATE.SEARCHING) {
          return this.userClash.unregister(this, null, 'logout');
        }
        return null;
      })
      .then(() => {
        // searching 상태에서 로그아웃할때 타이밍 이슈로 매칭이 잡혀버릴 수 있음.
        const state: CLASH_MATCHING_STATE = this.userClash.getMatchingState();
        if (state === CLASH_MATCHING_STATE.CHOICE) {
          return this.userClash.choice(this, CLASH_MATCHING_STATE.REJECT, now, null, 'logout');
        }
      })
      .then(() => {
        // 일반적인 로그아웃인 경우이거나 offline 상태가 되는 킥인 경우 online 상태 업데이트 이후 authd 에 logout 요청.
        if (
          (userId > 0 && !this.isOfflineSailingBot) ||
          (this._kicked &&
            this._kicked.authdId &&
            kickReasonThatShouldChangeIsOnline[this._kicked.reason])
        ) {
          return userCacheRedis['setOnline'](userId, 0)
            .then(() => {
              return puStateUpdateIsOnline(
                userDbConnPoolMgr.getPoolByShardId(this.getUserDbShardId()),
                userId,
                0
              );
            })
            .then(() => {
              return mhttp.authd.logout(this.accountId);
            });
        }

        return null;
      })
      .then(() => {
        if (this._kicked && this._kicked.authdId) {
          return lobbyPubsub.pubUserKicked(userId, this._kicked.authdId);
        }
        return null;
      })
      .then(() => {
        return userCacheRedis['setLastLogoutTimeUtc'](userId, now);
      })
      .then(() => {
        // 서버 점검(Maintenance) 시 allKick 명령으로 접속종료하는 경우
        if (UserConnection.DisconnectReason.Maintenance === this._disconnectReason) {
          return userCacheRedis['setEnterWorldToken'](this.accountId, '', 0);
        }
      })
      .then(() => {
        //
        if (this.userGuild.guildId && !this.isOfflineSailingBot) {
          return GuildUtil.GetGuildDataWithMemberLightInfo(this, this.userGuild.guildId).then(
            (result) => {
              if (result) {
                const sync: Sync = {
                  add: {
                    userGuild: {
                      guild: {
                        members: {
                          [userId]: {
                            isOnline: false,
                            logoutTimeUtc: now,
                          },
                        },
                      },
                    },
                  },
                };

                onGuildPublish(result.guildData, result.userLightInfos, [this.userId], sync);
              }
            }
          );
        }
      })
      .then(() => {
        // '멀티' 전투일 경우 pubsub 구독 취소
        return this.userBattle.tryQuitMultiBattle(this.userId, true);
      })
      .then(() => {
        // 친구들에게 로그아웃 알림 통보.
        if (!this.isOfflineSailingBot) {
          const friendIds = this.userFriends.getFriendIdsByState(FRIEND_STATE.ESTABLISHED);
          return FriendUtil.notifyOnlineFriends(
            this.userId,
            this.userName,
            friendIds,
            FRIEND_NOTIFICATION_TYPE.LOGOUT,
            mutil.curTimeUtc()
          );
        }
      })
      .catch((e) => {
        mlog.error('user logout error', {
          userId,
          error: e.message,

          // MError 의 경우 추가 정보
          mcode: e.mode,
          stack: e.stack,
        });
      })
      .finally(() => {
        mlog.info('user logout', {
          userId,
          connId: this.userConn.id,
          disconnectReason: this._disconnectReason,
          kicked: this._kicked,
          gameState: this.userState.getGameState(),
          lastGameState: this.userState.getRawLastGameState(),
          isBot: this.isOfflineSailingBot,
        });

        userManager.removeLoggingOutUser(userId);

        if (this._logoutEvent) {
          this._logoutEvent.emit('');
        }
      });
  }

  getLoginSyncData(): sync.All {
    const user: sync.User = {
      userId: this.userId,
      pubId: this.pubId,
      name: this.userName,
      nationCmsId: this.nationCmsId,
      lastUpdateNationTimeUtc: this.lastUpdateNationTimeUtc,
      companyJobCmsId: this.companyJobCmsId,
      exp: this.exp,
      level: this.level,
      karma: this.karma,
      lastKarmaUpdateTimeUtc: this._lastKarmaUpdateTimeUtc,
      usedFreeTurnTakebackCount: this.userBattle.usedFreeTurnTakebackCount,
      usedFreePhaseTakebackCount: this.userBattle.usedFreePhaseTakebackCount,
      lastFreeTakebackUpdateTimeUtc: this.userBattle.lastFreeTakebackUpdateTimeUtc,
      quickModeCount: this.userBattle.quickModeCount,
      lastQuickModeCountUpdateTimeUtc: this.userBattle.lastQuickModeCountUpdateTimeUtc,
      lastCompanyJobUpdateTimeUtc: this._lastCompanyJobUpdateTimeUtc,
      lastReceiveHotTimeUtc: this._lastReceiveHotTimeUtc,
      isAdmiralProfileOpened: this._isAdmiralProfileOpened,
      isFirstFleetProfileOpened: this._isFirstFleetProfileOpened,
      westShipBuildLevel: this.userShipBlueprints.westShipBuildLevel,
      westShipBuildExp: this.userShipBlueprints.westShipBuildExp,
      orientShipBuildLevel: this.userShipBlueprints.orientShipBuildLevel,
      orientShipBuildExp: this.userShipBlueprints.orientShipBuildExp,
      freeLeaderMateSwitchCount: this._freeLeaderMateSwitchCount,
      freeLastLeaderMateSwitchTimeUtc: this._freeLastLeaderMateSwitchTimeUtc,
      usedFreeContinuousCount: this.userBattle.usedFreeContinuousCount,
      lastUsedFreeContinuousUpdateTimeUtc: this.userBattle.lastUsedFreeContinuousUpdateTimeUtc,
      waypointSupplyTicketUsedCount: this.userSailWaypoints.waypointSupplyTicketUsedCount,
      lastWaypointSupplyTicketUpdateTimeUtc:
        this.userSailWaypoints.lastWaypointSupplyTicketUpdateTimeUtc,
      lastHotTimeBuffUpdateTimeUtc: this._lastUpdateHotTimeBuffUtc,
      curCargoPresetId: this.userFleets.getCurCargoPresetId(),
      sweepTicketCount: this.userSweepTicket.count,
      buySweepTicketCount: this.userSweepTicket.buyCount,
      isFriendlyBattleRequestable: this.isFriendlyBattleRequestable,
      lastFirstFleetPresetId: this.userFleets.getLastFirstFleetPresetId(),
    };

    const sync: sync.All = {
      user,
      countryCode: mconf.countryCode,
    };

    _.merge(
      sync,
      this.userPoints.getSyncData(),
      this.userSailing.getSyncData(),
      this.userFleets.getSyncData(),
      this.userTown.getSyncData(),
      this.userBattle.getSyncData(),
      this.userInven.getSyncData(),
      this.userMates.getSyncData(),
      this.questManager.getSyncData(),
      this.userShipBlueprints.getSyncData(),
      this.userContentsTerms.getSyncData(),
      this.userBuffs.getSyncData(),
      this.userPassives.getSyncData(),
      this.userDiscovery.getSyncData(),
      this.userAchievement.getSyncData(),
      this.userTrade.getSyncData(),
      this.userCashShop.getSyncData(),
      this.userMails.getSyncData(),
      this.userReputation.getSyncData(),
      this.userState.getSyncData(),
      this.userVillage.getSyncData(),
      this.userAttendance.getSyncData(),
      this.userPassEvent.getSyncData(),
      this.userChallenges.getSyncData(),
      this.userEncount.getSyncData(),
      this.userEnergy.getSyncData(),
      this.userAdjutantDelegation.getSyncData(),
      this.userBattleFormations.getSyncData(),
      this.userShield.getSyncData(),
      this.userWorldSkills.getSyncData(),
      this.userCollection.getSyncData(),
      this.userGuild.getSyncData(),
      this.userContributionShop.getSyncData(),
      this.userArena.getSyncData(),
      this.userGuildShop.getSyncData(),
      this.userExplore.getSyncData(),
      this.userEventShop.getSyncData(),
      this.userSailWaypoints.getSyncData(),
      this.userRaidTicket.getSyncData(),
      this.userGuildRaidTicket.getSyncData(),
      this.userFriends.getSyncData(),
      this.userFleetDispatch.getSyncData(),
      this.userFleetDispatchSlot.getSyncData(),
      this.userChatTranslationCount.getSyncData(),
      this.userEventRanking.getSyncData(),
      this.userDiscoveryReward.getSyncData(),
      this.userFishing.getSyncData(),
      this.userTitles.getSyncData(),
      this.userPets.getSyncData(),
      this.userSmuggle.getSyncData(),
      this.userNpcShop.getSyncData(),
      this.userInfiniteLighthouse.getSyncData(),
      this.userResearch.getSyncData(),
      this.userReentry.getSyncData(),
      this.userTutorial.getSyncData(),
      this.userManufacture.getSyncData()
    );

    return sync;
  }

  processPacket(packet: UserConnection.CPacket): Promise<any> {
    mlog.debug('processing packet ... ', { packet });

    return packetHandler.exec(this, packet);
  }

  async sendJsonPacket<T = any>(
    seqNum: number,
    type: number,
    body: T,
    payloadFlags?: number
  ): Promise<boolean> {
    if (this.isOfflineSailingBot) {
      if (mutil.isNotANumber(payloadFlags)) {
        payloadFlags = PayloadFlag.Compress;
      } else {
        payloadFlags ^= PayloadFlag.Encrypt;
      }
    }
    return this.userConn.sendJsonPacket(this.userId, seqNum, type, body, payloadFlags);
  }

  // 매 초 마다 호출되는 tick.
  tick(curTimeInMs: number): void {
    this._taskTick();

    const elapsedTimeInMs = curTimeInMs - this._lastTickTimeInMs;
    if (elapsedTimeInMs < mconf.userTick.interval) {
      return;
    }

    // mlog.info('user ticking', {
    //   userId: this.userId,
    //   lastTickTimeInMs: this._lastTickTimeInMs,
    //   curTimeInMs,
    //   elapsedTimeInMs,
    // });

    this._lastTickTimeInMs = curTimeInMs;

    if (!this.hasLoggedIn()) {
      return;
    }

    const curTimeUtc = Math.floor(curTimeInMs / 1000);

    this.heartBeatTick(curTimeUtc);

    if (curTimeUtc - this._lastPingPongTickTImeUtc >= mconf.userTick.pingPongIntervalSec) {
      this._lastPingPongTickTImeUtc = curTimeUtc;
      this.pingPongTick(curTimeUtc);
    }

    if (
      !this.isOfflineSailingBot &&
      curTimeUtc - this._lastRefreshEnterTokenTimeUtc >= mconf.enterWorldTokenRefreshmentIntervalSec
    ) {
      this._lastRefreshEnterTokenTimeUtc = curTimeUtc;
      this.refreshEnterWorldToken(curTimeUtc);
    }

    // glog - login_out_save 저장 주기
    if (
      !this.isOfflineSailingBot &&
      curTimeUtc - this._lastGlogLoginOutSaveTimeUtc >= mconf.userTick.glogSaveIntervalSec
    ) {
      const pt = curTimeUtc - this._lastGlogLoginOutSaveTimeUtc;
      this._lastGlogLoginOutSaveTimeUtc = curTimeUtc;

      this.glogLoginOutSave(
        GLOG_LOGIN_OUT_SAVE_TYPE.TickSession,
        curTimeUtc,
        pt,
        'tickSession',
        null
      );
    }

    if (this.userState.isInLoading()) {
      return;
    }

    this.userBuffs.tick(curTimeUtc, this);

    // tick mate 관련 update 용도
    const tickYard: UserTickYard = new UserTickYard();

    this.userMates.tickRemoveInjury(tickYard, this.companyStat);

    if (!this.isOfflineSailingBot) {
      this.userMails.tick(this);
      this.userArena.tick(curTimeUtc, this);
      this.userClash.tick(this, curTimeUtc);
    }

    if (
      this.userState.getGameState() === GAME_STATE.IN_OCEAN &&
      this.userState.getGameEnterState() === GAME_ENTER_STATE.LOAD_COMPLETED
    ) {
      this.userFleets.tickInOcean(curTimeUtc, this, tickYard);

      sailingState
        .tick(this, curTimeUtc, elapsedTimeInMs, tickYard)
        .then(() => {
          //bot move tick
          if (this.isOfflineSailingBot) {
            this.userSailing.tickOfflineSailingBot(this, curTimeUtc);
          }
          return null;
        })
        .catch((e: MError) => {
          mlog.error('sailingState tick error', {
            userId: this._userId,
            message: e.message,
            stack: e.stack,
          });
        });
    }

    tickYard.applyInjury(this);

    // if (mconf.countryCode === COUNTRY_CODE.CHINA && this.chinaAge !== CHINA_AGE.ADULT) {
    //   if (deltaTimeSecFromLogin % mconf.userTick.chinaUnder18PlayTimeIntervalSec === 0) {
    //     this.chinaUnder18PlayTimeTick(curTimeUtc);
    //   }
    // }

    if (this.userGameGuard) {
      this.userGameGuard.tick(this, curTimeUtc);
    }

    // UWO-18805 [앱가드] 서버인증 방식 변경
    // 앱가드쪽 담당자가 바뀌면서, 동작 방식이 바뀌는건데..
    // 혹시 또 바뀔까봐 코드 제거 대신 tick 만 돌지 않도록 주석처리함.
    // if (this.userAppGuard) {
    //   this.userAppGuard.tick(this, curTimeUtc);
    // }

    this.userNation.tickNationBroadcast(this, curTimeUtc);
  }

  set timeoutForBilling(x: number) {
    this._timeoutForBilling = x;
  }

  pingPongTick(curTimeUtc: number): void {
    if (this.getConnState() !== UserConnection.CONNECTION_STATE.LOGGED_IN) {
      return;
    }

    if (this.isOfflineSailingBot) {
      return;
    }

    let timeout;
    if (this._timeoutForBilling !== undefined) {
      timeout = this._timeoutForBilling;
    } else if (this._deviceType === 'Windows') {
      timeout = mconf.ping.editorTimeout;
    } else if (this.bIsOnBackgroundClient) {
      timeout = mconf.ping.timeoutOnClientBackground;
    } else if (this.userState.isInLoading()) {
      timeout = mconf.ping.timeoutInLoading;
    } else {
      timeout = mconf.ping.timeout;
    }

    const elapsedSecs = curTimeUtc - this._lastPingTimeUtc;
    if (elapsedSecs * 1000 > timeout) {
      mlog.warn('ping timeout', {
        userId: this.userId,
        elapsedSecs,
      });

      this.disconnect(UserConnection.DisconnectReason.PingTimeout);
    }
  }

  refreshEnterWorldToken(curTimeUtc: number): void {
    if (this.getConnState() !== UserConnection.CONNECTION_STATE.LOGGED_IN) {
      return;
    }

    const newToken = generateEnterWorldToken(this.accountId);
    const { userCacheRedis } = Container.get(LobbyService);
    return userCacheRedis['setEnterWorldToken'](this.accountId, newToken, curTimeUtc)
      .then(() => {
        const sync: sync.Sync = {
          add: {
            user: {
              enterWorldToken: newToken,
            },
          },
        };

        return this.sendJsonPacket<sync.Resp>(0, proto.Auth.REFRESH_ENTER_WORLD_TOKEN_SC, { sync });
      })
      .catch((err) => {
        mlog.error('refreshEnterWorldToken is failed.', {
          userId: this.userId,
          accountId: this.accountId,
          newToken,
          error: err.message,
        });
      });
  }

  chinaUnder18PlayTimeTick(curTimeUtc: number): void {
    const playTime = this.chinaUnder18LastPlayTimeSec + curTimeUtc - this._loginTimeUtc;
    let maxPlayTime;
    maxPlayTime = CHINA_UNDER_18_WEEKDAY_PLAY_TIME; // TODO

    if (playTime >= maxPlayTime) {
      this.kick(KICK_REASON.CHINA_UNDER_18_EXCEED_PLAYTIME, null);
      return;
    }
  }

  enterTown(townInfo: TownInfo): void {
    this.userTown.enterTown(townInfo);

    mlog.info('user enter town', {
      userId: this.userId,
      townInfo,
    });
  }

  leaveTown(): void {
    mlog.info('user leave town', {
      userId: this.userId,
      townInfo: this.userTown.getTownInfo(),
    });

    this.userTown.leaveTown();
  }

  enterOcean(sailState: sailingState.SailingState): void {
    mlog.info('user enter ocean', {
      userId: this.userId,
      sailState: sailState.save(),
    });

    this.userSailing.setSailState(sailState);
    this.userSailing.setSailPositions([]);
    sailState.speedHackDetector.enterOcean(this);
  }

  leaveOcean(): void {
    mlog.info('user leave ocean', {
      userId: this.userId,
      sailState: this.userSailing.getSailState().save(),
    });

    this.onLeaveOcean();

    this.userSailing.setSailState(undefined);
  }

  changeOcean(url: string, channelId: string) {
    const sailState = this.userSailing.getSailState();
    sailState.setChannel(url, channelId);

    this.onChangeOcean();

    mlog.info('user change ocean', {
      userId: this.userId,
      sailState: sailState.save(),
    });
  }

  getZoneInfo(type: ZoneType) {
    if (ZoneType.TOWN === type) {
      const townInfo = this.userTown.getTownInfo();
      if (townInfo && townInfo.url) {
        return townInfo.url;
      }
    } else if (ZoneType.OCEAN === type) {
      const sailState = this.userSailing.getSailState();
      if (sailState) {
        const { channel } = sailState;
        if (channel && channel.url) {
          return channel.url;
        }
      }
    }
    return undefined;
  }

  kick(reason: KICK_REASON, authdId: string): Promise<any> {
    this._kicked = {
      reason,
      authdId,
    };
    return this.sendJsonPacket(0, proto.Auth.KICK_SC, { reason }).then(() => {
      this.disconnect(UserConnection.DisconnectReason.Kick);
    });
  }

  updateLastPingTime(): void {
    this._lastPingTimeUtc = curTimeUtc();
  }

  ensureConnState(inState: UserConnection.CONNECTION_STATE) {
    const curConnState = this.getConnState();
    if (curConnState !== inState) {
      throw new MError('invalid-conn-state', MErrorCode.INVALID_CONN_STATE, {
        curConnState,
        inState,
      });
    }
  }

  ensureCheatAccessLevel(proto: proto.Dev): void {
    const cheatAccessLevel = getCheatAccessLevel(proto);
    if (this.accessLevel < cheatAccessLevel) {
      throw new MError('no-permission', MErrorCode.NO_PERMISSION_TO_CHEAT, {
        proto,
        userAccessLevel: this.accessLevel,
      });
    }
  }

  // 모든 스탯 재빌드
  rebuildStat(): void {
    const companyStatParam = this.buildStatParam();
    this.companyStat.updateParam(companyStatParam);

    this.companyStat.dumpParam();
  }

  private buildStatParam(): CompanyStatParam {
    const allMateStats = this.userMates.buildAllMateStats(this.userBuffs);
    const param: CompanyStatParam = {
      worldBuffs: {},
      companyJobCmsId: this.companyJobCmsId,
      allMateStats,
      fleetStatParams: this.userFleets.buildStatParams(
        allMateStats,
        this.userShipBlueprints,
        this.userMates,
        this.userInven,
        this.level,
        this.userBuffs,
        this.userPassives,
        this.userCollection,
        this.userNation,
        this.userResearch
      ),
    };

    // 선단에 적용된 버프 효과 적용.
    const buffTable = this.userBuffs.getCompanyBuffTable();
    _.forOwn(buffTable, (wb, groupNoStr) => {
      const groupNo = parseInt(groupNoStr, 10);
      param.worldBuffs[groupNo] = {
        cmsId: wb.nub.cmsId,
        stack: wb.nub.stack,
      };
    });

    return param;
  }

  applyExpLevelChange(
    userExpLevelChange: UserExpLevelChange,
    rsn: string,
    add_rsn: string,
    buffSync?: BuffSync
  ): sync.Sync {
    const ret: sync.Sync = {};

    if (userExpLevelChange) {
      if (this.level !== userExpLevelChange.level) {
        const { userCacheRedis } = Container.get(LobbyService);
        userCacheRedis['setUserLevel'](this.userId, userExpLevelChange.level).catch((err) => {
          mlog.error('userCacheRedis setUserLevel is failed.', {
            userId: this.userId,
            level: userExpLevelChange.level,
            err: err.message,
          });
        });
      }

      this.setExpLevel(userExpLevelChange.exp, userExpLevelChange.level, rsn, add_rsn, buffSync);
      _.merge<sync.Sync, sync.Sync>(ret, {
        add: {
          user: {
            exp: userExpLevelChange.exp,
            level: userExpLevelChange.level,
          },
        },
      });
    }

    const user: User = this;
    if (userExpLevelChange && userExpLevelChange.energyChange) {
      _.merge<sync.Sync, sync.Sync>(
        ret,
        this.userEnergy.applyEnergyChange(userExpLevelChange.energyChange, {
          user,
          rsn,
          add_rsn,
        })
      );
    }
    return ret;
  }

  // calcExpLevelWithConsumeEnergy(
  //   addedExp: number,
  //   curTimeUtc: number,
  //   energyToConsume: number
  // ): UserExpLevelChange {
  //   return User.calcExpLevel(
  //     addedExp,
  //     curTimeUtc,
  //     this.exp,
  //     this.level,
  //     this.companyStat,
  //     copyUserEnergy
  //   );
  // }

  calcExpLevel(
    addedExp: number,
    curTimeUtc: number,
    energyToConsume: number = 0,
    bEnsurePointIsEnough: boolean = false
  ): UserExpLevelChange {
    return User.calcExpLevel(
      addedExp,
      curTimeUtc,
      this.exp,
      this.level,
      this.companyStat,
      this.userEnergy,
      energyToConsume,
      bEnsurePointIsEnough
    );
  }

  static calcExpLevel(
    addedExp: number,
    curTimeUtc: number,
    curExp: number,
    curLevel: number,
    companyStat: CompanyStat,
    userEnergy: UserEnergy,
    energyToConsume: number = 0,
    bEnsurePointIsEnough: boolean = false
  ): UserExpLevelChange {
    const maxUserExp = cmsEx.getMaxUserExp();
    if (curExp >= maxUserExp || !addedExp) {
      // no change.
      return null;
    }

    addedExp = Math.ceil((addedExp * cms.Const.CompanyExpRatio.value) / 100);
    const addedEffect = companyStat.getWpe(cmsEx.PASSIVE_EFFECT.COMPANY_ADDED_EXP);
    const pctEffect =
      companyStat.getWpe(cmsEx.PASSIVE_EFFECT.COMPANY_ADDED_EXP_PCT) +
      companyStat.getWpe(cmsEx.PASSIVE_EFFECT.COMPANY_ADDED_EXP_WITH_MATES_PCT);
    if (pctEffect) {
      addedExp += Math.floor((addedExp * pctEffect) / 1000);
    }
    if (addedEffect) {
      addedExp += addedEffect;
    }
    if (!addedExp) {
      return null;
    }

    const ret: UserExpLevelChange = {
      exp: Math.min(curExp + addedExp, maxUserExp),
      oldLevel: curLevel,
      level: curLevel,
    };

    ret.level = cmsEx.calcUserLevel(ret.exp);
    if (curTimeUtc) {
      if (energyToConsume) {
        ret.energyChange = userEnergy.buildEnergyChangeWithConsume(
          curTimeUtc,
          curLevel,
          ret.level,
          energyToConsume,
          bEnsurePointIsEnough
        );
      } else {
        ret.energyChange = userEnergy.buildEnergyChange(curTimeUtc, curLevel, ret.level);
      }
    }
    return ret;
  }

  getCurRegionCmsId(): number {
    if (
      this.userState.isInTown() &&
      this.userState.getGameEnterState() !== GAME_ENTER_STATE.ENTERING
    ) {
      return cms.Town[this.userTown.getLastTownCmsId()].RegionId;
    } else if (
      this.userState.isInOcean() &&
      this.userState.getGameEnterState() !== GAME_ENTER_STATE.ENTERING
    ) {
      const sailingState = this.userSailing.getSailState();
      if (sailingState?.region) {
        return sailingState.region.id;
      }
    }
    return null;
  }

  getCurLocation(): Location | undefined {
    if (
      this.userState.isInTown() &&
      this.userState.getGameEnterState() !== GAME_ENTER_STATE.ENTERING
    ) {
      return cms.TownLocation[this.userTown.getLastTownCmsId()].location;
    } else if (
      this.userState.isInOcean() &&
      this.userState.getGameEnterState() !== GAME_ENTER_STATE.ENTERING
    ) {
      const location = this.userSailing.getSailState().location;
      if (location) {
        return location;
      }
    }
    return undefined;
  }

  getCurTownCmsId(): number {
    if (
      this.userState.isInTown() &&
      this.userState.getGameEnterState() !== GAME_ENTER_STATE.ENTERING
    ) {
      return this.userTown.getTownCmsId();
    } else if (
      this.userState.isInTown() &&
      this.userState.getGameEnterState() === GAME_ENTER_STATE.ENTERING
    ) {
      return this.userTown.arrivalTownCmsId;
    }
    return null;
  }

  getCurWeatherTileCmsId(): number {
    if (
      this.userState.isInTown() &&
      this.userState.getGameEnterState() !== GAME_ENTER_STATE.ENTERING
    ) {
      return cms.Town[this.userTown.getLastTownCmsId()].WeatherTileId;
    } else if (
      this.userState.isInOcean() &&
      this.userState.getGameEnterState() !== GAME_ENTER_STATE.ENTERING
    ) {
      const location = this.userSailing.getSailState().location;
      const oceanTileDesc = LatLonToOceanTileDesc(location.latitude, location.longitude);
      return oceanTileDesc.weatherTileCms.id;
    }
    return null;
  }

  getWeatherTileCmsIdAtPosition(location: Location): number {
    const oceanTileDesc = LatLonToOceanTileDesc(location.latitude, location.longitude);
    return oceanTileDesc.weatherTileCms.id;
  }

  getWeatherTypeBuffeCmsId(): number {
    const weatherBuffGroupNo = CMSConst.get('WeatherBuffGroupNo');
    const weatherBuff = this.userBuffs.getCompanyBuffByGroup(weatherBuffGroupNo);
    if (!weatherBuff) {
      return 0;
    }
    return weatherBuff.nub.cmsId;
  }

  getBuffedWeatherCmsId(): number {
    const buffCmsId = this.getWeatherTypeBuffeCmsId();
    if (buffCmsId) {
      let buffedWeatherCmsId = 0;
      const sunWorldBuffId = CMSConst.get('SunWorldBuffId');
      const cloudWorldBuffId = CMSConst.get('CloudWorldBuffId');
      const rainWorldBuffId = CMSConst.get('RainWorldBuffId');
      switch (buffCmsId) {
        case sunWorldBuffId:
          buffedWeatherCmsId = CMSConst.get('SunWeatherId');
          break;
        case cloudWorldBuffId:
          buffedWeatherCmsId = CMSConst.get('CloudWeatherId');
          break;
        case rainWorldBuffId:
          buffedWeatherCmsId = CMSConst.get('RainWeatherId');
          break;
        default:
          break;
      }
      if (buffedWeatherCmsId) {
        return buffedWeatherCmsId;
      }
    }
    return 0;
  }

  getCurTileWeatherCmsId(): number {
    const buffedWeatherCmsId = this.getBuffedWeatherCmsId();
    if (buffedWeatherCmsId) {
      return buffedWeatherCmsId;
    }
    return GetCurWeatherCmsId(this.getCurWeatherTileCmsId(), curTimeUtc());
  }

  // 출항전에 해상의 날씨 정보를 얻어오기 위한 용도
  getCurWeatherCmsIdAtPosition(location: Location): number {
    const buffedWeatherCmsId = this.getBuffedWeatherCmsId();
    if (buffedWeatherCmsId) {
      return buffedWeatherCmsId;
    }
    return GetCurWeatherCmsId(this.getWeatherTileCmsIdAtPosition(location), curTimeUtc());
  }

  getKarmaChangeWithAdd(curTimeUtc: number, karmaToAdd: number): KarmaChange {
    let change = this.getKarmaChange(curTimeUtc);
    if (!karmaToAdd) {
      return change;
    }

    if (!change) {
      change = {
        karma: this.karma,
        lastUpdateTimeUtc: this.lastKarmaUpdateTimeUtc || curTimeUtc,
      };
    }

    change.karma = Math.min(
      cms.Const.UserMaxKarma.value,
      Math.max(cms.Const.UserMinKarma.value, change.karma + karmaToAdd)
    );

    return change;
  }

  getKarmaChangeWithSubtract(curTimeUtc: number, karmaToSubtract: number): KarmaChange {
    let change = this.getKarmaChange(curTimeUtc);
    if (!karmaToSubtract) {
      return change;
    }

    if (!change) {
      change = {
        karma: this.karma,
        lastUpdateTimeUtc: this.lastKarmaUpdateTimeUtc || curTimeUtc,
      };
    }

    change.karma = Math.min(
      cms.Const.UserMaxKarma.value,
      Math.max(cms.Const.UserMinKarma.value, change.karma - karmaToSubtract)
    );

    return change;
  }

  getKarmaDesc(curTimeUtc: number): KarmaDesc {
    const karmaChange = this.getKarmaChange(curTimeUtc);
    if (karmaChange) {
      return getKarmaDescBasedOnKarmaVal(karmaChange.karma);
    } else {
      return getKarmaDescBasedOnKarmaVal(this.karma);
    }
  }

  // curTimeUtc 를 계산하여  갱신된 카르마 수치를 가져온다
  getKarma(curTimeUtc: number): number {
    let change = this.getKarmaChange(curTimeUtc);
    if (change) {
      return change.karma;
    }
    return this.karma;
  }

  getKarmaChange(curTimeUtc: number): KarmaChange {
    return User.buildKaraChange(this.karma, this.lastKarmaUpdateTimeUtc, curTimeUtc);
  }

  static buildKaraChange(
    uncalculatedKarma: number,
    lastUpdateTimeUtc: number,
    curTimeUtc: number
  ): KarmaChange {
    if (!lastUpdateTimeUtc) {
      // 값이 없는 경우 카르마가 최소라고 판단
      return null;
    }

    const ret = CalcKarmaAndGetDecreaseTimesEx(uncalculatedKarma, lastUpdateTimeUtc, curTimeUtc);
    if (!ret) {
      // 변동 없음
      return null;
    }

    const { karma, decreaseTimes } = ret;
    const karmaChange: KarmaChange = {
      karma,
      lastUpdateTimeUtc:
        karma > cms.Const.UserMinKarma.value
          ? lastUpdateTimeUtc + decreaseTimes * SECONDS_PER_HOUR
          : null /* karma 가 최소치일 경우 lastUpdateTimeUtc 은 null 로 한다. */,
    };

    return karmaChange;
  }

  applyKarma(change: KarmaChange, glogParam: GLogParam): void {
    if (!change) {
      return;
    }

    const oldKarma: number = this.getKarma(mutil.curTimeUtc());
    if (!mutil.isNotANumber(change.karma)) {
      this.karma = change.karma;
    }
    this._lastKarmaUpdateTimeUtc = change.lastUpdateTimeUtc;

    if (this.userSailing.getSailState()) {
      const { tcpClientSessionManager } = Container.get(LobbyService);
      const sendp = new Protocol.LB2OC_NTF_SYNC_KARMA();
      sendp.userId = this.userId;
      sendp.karma = this.karma;
      sendp.lastKarmaUpdateTimeUtc = this.lastKarmaUpdateTimeUtc;

      tcpClientSessionManager.send(this.getZoneInfo(ZoneType.OCEAN), sendp);
    }

    // 1: 카르마 상승, 2: 카르마 하락
    const flag = change.karma > oldKarma ? 1 : 0;

    // glog
    if (glogParam) {
      glogParam.user.glog('karma', {
        rsn: glogParam.rsn,
        add_rsn: glogParam.add_rsn ? glogParam.add_rsn : null,
        flag, // 1: 상승, 2: 하락
        amt: change.karma - oldKarma,
        old_karma: oldKarma,
        cur_karma: change.karma,
      });
    }
  }

  setExpLevel(
    exp: number,
    level: number,
    glogRsn: string,
    glogAddRsn: string,
    buffSync?: BuffSync
  ) {
    const prevExp = this._exp;
    const prevLevel = this._level;
    this._exp = exp;
    this._level = level;

    if (prevLevel !== level) {
      this._onLevelChanged(prevLevel, level, buffSync);
    }

    // 선단레벨 랭킹 업데이트
    if (prevExp !== this._exp) {
      const { rankingManager } = Container.get(LobbyService);
      rankingManager.updateRanking(
        RANKING_CMS_ID.COMPANY_LEVEL,
        this.userId,
        this._exp,
        this.userId
      );
    }

    // glog
    if (glogRsn) {
      this.glog('company_exp', {
        rsn: glogRsn,
        add_rsn: glogAddRsn,
        old_lv: prevLevel,
        old_exp: prevExp,
        exp: exp - prevExp,
        cur_lv: level,
        cur_exp: exp,
      });
    }
  }

  get userId(): number {
    return this._userId;
  }

  set userId(x: number) {
    this._userId = x;
  }

  get userConn(): UserConnection.UserConnection {
    return this._userConn;
  }

  get accountId(): string {
    return this._accountId;
  }
  set accountId(x: string) {
    this._accountId = x;
  }

  get pubId(): string {
    return this._pubId;
  }
  set pubId(x: string) {
    this._pubId = x;
  }

  get accessLevel(): number {
    return this._accessLevel;
  }
  set accessLevel(x: number) {
    this._accessLevel = x;
  }

  get patchRevision(): string {
    return this._patchRevision;
  }
  set patchRevision(x: string) {
    this._patchRevision = x;
  }

  get revision(): string {
    return this._revision;
  }
  set revision(x: string) {
    this._revision = x;
  }

  get versionText(): string {
    return this._versionText;
  }
  set versionText(x: string) {
    this._versionText = x;
  }

  get appVersion(): string | undefined | null {
    return this._appVersion;
  }

  get deviceType(): string {
    return this._deviceType;
  }

  get countryCreated(): string {
    return this._countryCreated;
  }
  set countryCreated(x: string) {
    // 개발 환경일 경우 x 가 없으면 default('KR') 유지한다.
    if (!x && mconf.isDev) {
      return;
    }
    this._countryCreated = x;
  }

  get countryIp(): string {
    return this._countryIp;
  }

  get storeCode(): string {
    return this._storeCode;
  }
  set storeCode(x: string) {
    this._storeCode = x;
  }

  get osv(): string | undefined | null {
    return this._osv;
  }
  get deviceLang(): string | undefined | null {
    return this._deviceLang;
  }
  get adjustId(): string | undefined | null {
    return this._adjustId;
  }
  get gpsAdid(): string | undefined | null {
    return this._gpsAdid;
  }
  get udid(): string | undefined | null {
    return this._udid;
  }
  get idfa(): string | undefined | null {
    return this._idfa;
  }
  get idfv(): string | undefined | null {
    return this._idfv;
  }
  get steamAppId(): number | undefined | null {
    return this._steamAppId;
  }
  get steamUserId(): BigInt {
    return this._steamUserId;
  }

  get userName(): string {
    return this._userName;
  }
  set userName(x: string) {
    this._userName = x;
  }

  get companyJobCmsId(): number {
    return this._companyJobCmsId;
  }
  set companyJobCmsId(x: number) {
    this._companyJobCmsId = x;
  }

  get nationCmsId(): number {
    return this._nationCmsId;
  }
  set nationCmsId(x: number) {
    this._nationCmsId = x;
  }

  get exp(): number {
    return this._exp;
  }

  get level(): number {
    return this._level;
  }

  get chinaAge(): CHINA_AGE {
    return this._chinaAge;
  }
  set chinaAge(x: CHINA_AGE) {
    this._chinaAge = x;
  }

  get chinaUnder18LastPlayTimeSec(): number {
    return this._chinaUnder18LastPlayTimeSec;
  }
  set chinaUnder18LastPlayTimeSec(x: number) {
    this._chinaUnder18LastPlayTimeSec = x;
  }

  get lastCompanyJobUpdateTimeUtc(): number {
    return this._lastCompanyJobUpdateTimeUtc;
  }
  set lastCompanyJobUpdateTimeUtc(x: number) {
    this._lastCompanyJobUpdateTimeUtc = x;
  }

  get freeLastLeaderMateSwitchTimeUtc(): number | null {
    return this._freeLastLeaderMateSwitchTimeUtc;
  }

  set freeLastLeaderMateSwitchTimeUtc(x: number) {
    this._freeLastLeaderMateSwitchTimeUtc = x;
  }

  get freeLeaderMateSwitchCount(): number {
    return this._freeLeaderMateSwitchCount;
  }

  set freeLeaderMateSwitchCount(x: number) {
    this._freeLeaderMateSwitchCount = x;
  }

  get karma(): number {
    return this._karma;
  }
  set karma(x: number) {
    this._karma = x;
  }

  get lastKarmaUpdateTimeUtc() {
    return this._lastKarmaUpdateTimeUtc;
  }

  get bIsOnBackgroundClient(): boolean {
    return this._bIsOnBackgroundClient;
  }
  set bIsOnBackgroundClient(x: boolean) {
    this._bIsOnBackgroundClient = x;
  }

  get isOfflineSailingBot(): boolean {
    return this._isOfflineSailingBot;
  }
  set isOfflineSailingBot(value: boolean) {
    this._isOfflineSailingBot = value;
  }

  get lastReceiveHotTimeUtc(): number {
    return this._lastReceiveHotTimeUtc;
  }
  set lastReceiveHotTimeUtc(x: number) {
    this._lastReceiveHotTimeUtc = x;
  }

  get lastUpdateNationTimeUtc(): number {
    return this._lastUpdateNationTimeUtc;
  }

  set lastUpdateNationTimeUtc(x: number) {
    this._lastUpdateNationTimeUtc = x;
  }

  get isAdmiralProfileOpened(): number {
    return this._isAdmiralProfileOpened;
  }

  set isAdmiralProfileOpened(x: number) {
    this._isAdmiralProfileOpened = x;
  }

  get isFirstFleetProfileOpened(): number {
    return this._isFirstFleetProfileOpened;
  }

  set isFirstFleetProfileOpened(x: number) {
    this._isFirstFleetProfileOpened = x;
  }

  get isFriendlyBattleRequestable(): number {
    return this._isFriendlyBattleRequestable;
  }

  set isFriendlyBattleRequestable(x: number) {
    this._isFriendlyBattleRequestable = x;
  }

  private _removeEncountProtectBuffAtChangedLevel(prevLevel: number, buffSync?: BuffSync) {
    const protectLv = cms.Const.ProtectLv.value;
    const buffCmsId = CMSConst.get('EncountProtectLvBuff');
    const targetId = cmsEx.FirstFleetIndex;
    const sourceType = cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT;

    if (this.level >= protectLv) {
      if (this.userBuffs.getFleetBuffByCmsId(targetId, buffCmsId)) {
        this.userBuffs.removeBuff(
          buffCmsId,
          targetId,
          this,
          'encount_protection_removed_as_level_changed',
          null,
          buffSync
        );
      }
    }

    // 레벨 다운 시 보호버프 재적용
    if (prevLevel >= protectLv && this.level < protectLv) {
      this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
    }
  }

  private _addEncountProtectBuffAtOceanLoadComplete() {
    let buffCmsId = null;
    const protectLv = cms.Const.ProtectLv.value;
    const targetId = cmsEx.FirstFleetIndex;
    const sourceType = cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT;

    // 선단 랭크 cms.Const.ProtectLv 미만는 인카운트 보호 버프를 넣어준다.
    if (this.level < protectLv) {
      buffCmsId = CMSConst.get('EncountProtectLvBuff');
      this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
    }

    // 보호 버프가 있는 리전은 보호 버프를 넣어준다.
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    buffCmsId = sailState.region ? sailState.region.baseWorldBuffId : 0;
    if (buffCmsId) {
      this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
    }

    const preGameMode = this.userState.getPreGameMode();
    if (preGameMode === GAME_MODE.BATTLE || preGameMode === GAME_MODE.BATTLE_REWARD) {
      buffCmsId = CMSConst.get('EncountProtectBattleEndBuff');
      this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
    } else if (preGameMode === GAME_MODE.TOWN) {
      buffCmsId = CMSConst.get('EncountProtectDepartureBuff');
      this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
    } else {
      // LIVEUWO-2459 보물선단 전투 이후, PVP 발생 관련
      // 전투 완료 직후 클라이언트와의 네트웍연결이 끊어지고 다시 로그인하는 과정에서 전투 보호 버프가 적용되지 않는 이슈 대응
      // PreGameMode가 휘발성이라 발생하는 이슈이므로 이에 대한 보완책으로 sailState에 이벤트 발생시간을 저장하여 보완한다.
      // 재접속시에도 보호버프가 필요한 경우 미리 ElapsedSecsFromLastEncountProtectNeeded 값을 설정해놓고 해양입장시 적용해주는 예외처리를 한다.
      buffCmsId = CMSConst.get('EncountProtectBattleEndBuff');
      const elapsedTimeFrom = sailState.getElapsedSecsFromLastEncountProtectNeeded(curTimeUtc());
      const buffDuration = cms.WorldBuff[buffCmsId]?.duration || 0;

      // 임의로 전투종료직후로부터의 경과시간이 버프유지시간의 절반 미만인 경우 버프를 넣어준다.
      if (buffDuration / 2 > elapsedTimeFrom) {
        mlog.verbose(
          `_addEncountProtectBuffAtOceanLoadComplete adding EncountProtectBattleEndBuff`,
          {
            userId: this.userId,
            buffCmsId,
            buffDuration,
            elapsedTimeFrom,
          }
        );

        this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
      }
    }
  }

  // 인카운트 종료 후 방어자가 항복수락 or 도망성공 or 교섭성공 시 인카운트 보호 버프를 넣어준다.
  private _addEncountProtectDefenderBuffAtEncountEnd(result: EncountResult) {
    switch (result) {
      case EncountResult.ESCAPE_SUCCESS:
      case EncountResult.ESCAPE_ALLOW:
      case EncountResult.SURRENDER_SUCCESS:
      case EncountResult.NEGOTIATE_SUCCESS:
      case EncountResult.SURRENDER_SUCCESS_AND_DONATION:
        const targetId = cmsEx.FirstFleetIndex;
        const buffCmsId = CMSConst.get('EncountProtectDefenderEncountEndBuff');
        const sourceType = cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT;
        this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
        break;
    }
  }

  // 인카운트 종료 후 공격자에게 인카운트 보호 버프를 넣어준다.
  private _addEncountProtectAttackerBuffAtEncountEnd() {
    const targetId = cmsEx.FirstFleetIndex;
    const buffCmsId = CMSConst.get('EncountProtectAttackerEncountEndBuff');
    const sourceType = cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT;
    this.userBuffs.addSingleBuff(buffCmsId, targetId, 0, sourceType, this);
  }

  // 인카운트 시작 시 인카운트 보호버프를 제거해준다.
  private _removeEncountProtectBuffAtEncountStart() {
    const { userDbConnPoolMgr } = Container.get(LobbyService);

    const encountProtectBuffs: number[] = [
      CMSConst.get('EncountProtectAttackerEncountEndBuff'),
      CMSConst.get('EncountProtectDefenderEncountEndBuff'),
      CMSConst.get('EncountProtectBattleEndBuff'),
      CMSConst.get('EncountProtectDepartureBuff'),
    ];

    encountProtectBuffs.forEach((buffCmsId) => {
      if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, buffCmsId)) {
        this.userBuffs.removeBuff(
          buffCmsId,
          cmsEx.FirstFleetIndex,
          this,
          'remove_encount_protect_buff_at_encount_start'
        );
      }
    });

    // 보호버프와 연계된 캐시상품이 있을 경우 제거.
    const cashShopCmsId = cms.Const.EncountDeleteBuffProductId.value;
    if (this.userCashShop.getFixedTermProduct(cashShopCmsId, mutil.curTimeUtc())) {
      puCashShopFixedTermProductDelete(
        userDbConnPoolMgr.getPoolByShardId(this.getUserDbShardId()),
        this.userId,
        cashShopCmsId
      )
        .then(() => {
          this.userCashShop.deleteFixedTermProducts([cashShopCmsId]);

          const sync: sync.Sync = {
            remove: {
              cashShopFixedTermProducts: [cashShopCmsId.toString()],
            },
          };
          return this.sendJsonPacket<sync.Resp>(0, proto.Common.CASH_SHOP_SYNC_SC, { sync });
        })
        .catch((e) => {
          mlog.error('puCashShopFixedTermProductDelete error', {
            userId: this._userId,
            cashShopCmsId,
            error: e.message,
          });
        });
    }
  }

  // 리전 변경 시 기존 리전의 인카운트 보호 버프 해제 및 신규 리전의
  // 인카운트 보호버프 적용 (기존과 신규가 동일한 버프일 경우 스킵.)
  private _addOrRemoveEncountProtectBuffAtChangedRegion(
    prevRegionCmsId: number,
    regionCmsId: number
  ) {
    const prevRegionBuffCmsId = cms.Region[prevRegionCmsId].baseWorldBuffId || 0;
    const curRegionBuffCmsId = cms.Region[regionCmsId].baseWorldBuffId || 0;
    if (prevRegionBuffCmsId === curRegionBuffCmsId) {
      return;
    }

    if (prevRegionBuffCmsId) {
      if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, prevRegionBuffCmsId)) {
        this.userBuffs.removeBuff(
          prevRegionBuffCmsId,
          cmsEx.FirstFleetIndex,
          this,
          'remove_encount_protect_buff_at_changed_region'
        );
      }
    }

    if (curRegionBuffCmsId) {
      const targetId = cmsEx.FirstFleetIndex;
      const sourceType = cmsEx.WorldBuffSourceType.PROTECT_ENCOUNT;
      this.userBuffs.addSingleBuff(curRegionBuffCmsId, targetId, 0, sourceType, this);
    }
  }

  // 유저의 국가가 중립 ==>기존 점유 버프를 모두 지워준다
  // 유저의 국가가 점유/점령 ==>차집합들 처리
  private _applyRegionOccupationBuff(
    regionCmsId: number,
    regionOccupation: RegionOccupation,
    glogRsn: string,
    glogAddRsn: string = null
  ) {
    const substact = (source: number[], target: number[]): number[] => {
      if (!source || source.length === 0) {
        return [];
      }
      if (!target || target.length === 0) {
        return source;
      }
      return source.filter((a) => target.findIndex((b: number) => a === b) === -1);
    };

    const oldWorldBuffs = this.userBuffs.getFleetBuffsBySourceType(
      cmsEx.FirstFleetIndex,
      cmsEx.WorldBuffSourceType.OCCUPIED_NATION
    );
    let oldBuffCmsIds = oldWorldBuffs.map((buf) => {
      return buf.nub.cmsId;
    });

    // 마을에 있을 경우 현재 마을의 CMS
    let townCms: TownDesc = null;
    const townInfo = this.userTown.getTownInfo();
    if (townInfo) {
      townCms = cms.Town[townInfo.cmsId];
    }
    let newRegionBuffCmsIds: number[] = [];
    if (this._nationCmsId) {
      const regionCms: RegionDesc = cms.Region[regionCmsId];
      if (this._nationCmsId === regionOccupation.nationCmsId && regionOccupation.bComplete) {
        // 유저의 국가가 점령(100%)
        if (this.userState.isInOcean() && regionCms.dominationRegionEffect) {
          newRegionBuffCmsIds = regionCms.dominationRegionEffect;
        }

        // 마을에 있을 경우 점령버프 추가
        if (townCms && townCms.dominationRegionEffect) {
          newRegionBuffCmsIds = newRegionBuffCmsIds.concat(townCms.dominationRegionEffect);
        }
      } else if (
        // 유저의 국가가 점유(50% 초과)
        this._nationCmsId === regionOccupation.nationCmsId &&
        !regionOccupation.bComplete
      ) {
        if (this.userState.isInOcean() && regionCms.subordinationRegionEffect) {
          newRegionBuffCmsIds = regionCms.subordinationRegionEffect;
        }
        // 마을에 있을 경우 점유버프 추가
        if (townCms && townCms.subordinationRegionEffect) {
          newRegionBuffCmsIds = newRegionBuffCmsIds.concat(townCms.subordinationRegionEffect);
        }
      }
    }

    mlog.verbose('_applyRegionOccupationBuff start', {
      userId: this.userId,
      userNationCmsId: this._nationCmsId,
      regionCmsId,
      regionOccupation,
      newRegionBuffCmsIds,
      oldBuffCmsIds,
    });

    if (newRegionBuffCmsIds.length == 0 && oldBuffCmsIds.length == 0) {
      return;
    }

    let newBuffCmsIds = [];
    if (newRegionBuffCmsIds.length > 0) {
      newBuffCmsIds = substact(newRegionBuffCmsIds, oldBuffCmsIds);
      oldBuffCmsIds = substact(oldBuffCmsIds, newRegionBuffCmsIds);
    }

    for (const buffCmsId of oldBuffCmsIds) {
      if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, buffCmsId)) {
        this.userBuffs.removeBuff(buffCmsId, cmsEx.FirstFleetIndex, this, glogRsn, glogAddRsn);
      }
    }

    for (const buffCmsId of newBuffCmsIds) {
      this.userBuffs.addSingleBuff(
        buffCmsId,
        cmsEx.FirstFleetIndex,
        0,
        cmsEx.WorldBuffSourceType.OCCUPIED_NATION,
        this
      );
    }

    mlog.verbose('_applyRegionOccupationBuff applied', {
      userId: this.userId,
      regionCmsId,
      regionOccupation,
      newBuffCmsIds,
      oldBuffCmsIds,
    });
  }

  private _addOrRemoveOccupiedNationBuffAtChangedRegion(regionCmsId: number) {
    const townManager = Container.get(TownManager);
    const regionOccupation = townManager.getRegionOccupation(regionCmsId);

    this._applyRegionOccupationBuff(
      regionCmsId,
      regionOccupation,
      'remove_occupied_nation_buff_at_changed_region'
    );
  }

  private _addOrRemoveOccupiedNationBuffAtLoadComplete() {
    let regionCmsId: number = 0;
    const townInfo = this.userTown.getTownInfo();
    if (townInfo) {
      const townCms = cms.Town[townInfo.cmsId];
      if (townCms) {
        regionCmsId = townCms.RegionId;
      }
    } else {
      if (this.userState.isInOcean()) {
        const sailState = this.userSailing.getSailState();
        if (sailState) {
          regionCmsId = sailState.region.id;
        }
      }
    }
    const townManager = Container.get(TownManager);
    const regionOccupation = townManager.getRegionOccupation(regionCmsId);

    this._applyRegionOccupationBuff(
      regionCmsId,
      regionOccupation,
      'remove_occupied_nation_buff_at_LoadComplete'
    );
  }

  private _addAnxietyDebuff() {
    if (!this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, cms.Const.AnxietyId.value)) {
      this.userBuffs.addSingleBuff(
        cms.Const.AnxietyId.value,
        cmsEx.FirstFleetIndex,
        0,
        cmsEx.WorldBuffSourceType.ANXIETY,
        this
      );
    }
  }

  private _addHungerDebuff() {
    if (!this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, cms.Const.HungerId.value)) {
      this.userBuffs.addSingleBuff(
        cms.Const.HungerId.value,
        cmsEx.FirstFleetIndex,
        0,
        cmsEx.WorldBuffSourceType.HUNGER,
        this
      );
    }
  }

  private _removeAnxietyDebuff() {
    if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, cms.Const.AnxietyId.value)) {
      this.userBuffs.removeBuff(
        cms.Const.AnxietyId.value,
        cmsEx.FirstFleetIndex,
        this,
        'remove_anxiety_debuff'
      );
    }
  }
  private _removeHungerDebuff() {
    if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, cms.Const.HungerId.value)) {
      this.userBuffs.removeBuff(
        cms.Const.HungerId.value,
        cmsEx.FirstFleetIndex,
        this,
        'remove_hunger_debuff'
      );
    }
  }

  private _updateHungerOrAnxietyDebuff() {
    const firstFleet = this.userFleets.getFirstFleet();
    const waterQuantity = firstFleet.getCargoQuantity(cmsEx.SUPPLY_CMS_ID.WATER);
    const foodQuantity = firstFleet.getCargoQuantity(cmsEx.SUPPLY_CMS_ID.FOOD);

    /*
      물빵이 모두 없을 때에는 굶주림 디버프만 적용하며,
      하나만 있을 경우 불안감 디버프만 적용한다.
      모두 있으면  굶주림 & 불안감 미적용.
    */
    // 물과 음식 모두 있을 경우 불안감 & 굶주림 디버프 제거
    if (waterQuantity > 0 && foodQuantity > 0) {
      this._removeAnxietyDebuff();
      this._removeHungerDebuff();
      return;
    }

    // 물과 음식 중 하나만 있을 경우  굶주림 디버프 제거 & 불안감 추가
    if (waterQuantity > 0 || foodQuantity > 0) {
      this._removeHungerDebuff();
      this._addAnxietyDebuff();
    }

    // 물과 음식 모두 없을 경우 불안감 제거 & 굶주림 디버프 추가
    if (waterQuantity === 0 && foodQuantity === 0) {
      this._removeAnxietyDebuff();
      this._addHungerDebuff();
    }
  }

  private _addOrRemoveHungerDebuffAtLoadComplete() {
    const firstFleet = this.userFleets.getFirstFleet();
    if (!firstFleet.isWrecked()) {
      this._updateHungerOrAnxietyDebuff();
    }
  }

  private _addWorldTileDebuffAtLoadComplete() {
    // 현위치 월드타일에 적용받는 디버프를 넣어준다.
    const sailState = this.userSailing.getSailState();
    const { location } = sailState;

    const oceanTileDesc = LatLonToOceanTileDesc(location.latitude, location.longitude);
    if (oceanTileDesc) {
      applyWorldTileBuff(this, oceanTileDesc, false);
    }
  }

  private _addBattleFormationBuffAtLoadComplete() {
    // 전투 포메이션 버프를 넣어준다
    // [todo] 현재는 1함대만 적용
    applyBattleFormationBuff(this, cmsEx.FirstFleetIndex);
  }

  private _removeHungerDebuffAtLeaveOcean() {
    this._removeHungerDebuff();
  }

  private _addTowingDebuffAtLoadComplete() {
    /*
      https://jira.line.games/browse/UWO-27134 [서버] 예인 > 항해 속도 패널티 제거
      속도제거 일감으로  코드에서 바로 리턴 처리
      코드 재사용 가능성이 있어 주석 처리
    */
    return;

    /*
    const towDebuffCmsId: number = cms.Const.TowingSpaceDebuffId.value;
    if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, towDebuffCmsId)) {
      return;
    }
    let hasTowShip = false;
    const dockFleet: Fleet = this.userFleets.getFleet(cmsEx.NoFleetIndex);
    const ships: { [id: number]: Ship } = dockFleet.getShips();
    _.forOwn(ships, (elem) => {
      if (hasTowShip === false) {
        const nub = elem.getNub();
        if (nub.assignment === SHIP_ASSIGNMENT.CAPTURED) {
          hasTowShip = true;
        }
      }
    });

    if (hasTowShip) {
      this.userBuffs.addSingleBuff(
        towDebuffCmsId,
        cmsEx.FirstFleetIndex,
        0,
        cmsEx.WorldBuffSourceType.TOWING,
        this
      );
    }
    */
  }

  private _addMateBuffLiveEventAtLoadComplete() {
    this.userLiveEvents.addMateBuffsIfNeeded(this, mutil.curTimeUtc());
  }

  private _removeWorldTileBuffAtLeaveOcean() {
    this.userBuffs.removeFleetBuffsBySourceType(
      cmsEx.FirstFleetIndex,
      cmsEx.WorldBuffSourceType.WORLD_TILE,
      this,
      'remove_world_tile_buff_at_leave_ocean'
    );
  }

  private _removeWeatherBuff(cmsId: number) {
    this.userBuffs.removeBuff(cmsId, cmsEx.FirstFleetIndex, this, 'remove_weather_buff');
  }

  private _removeRegionProtectBuff() {
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    const buffCmsId = sailState.region ? sailState.region.baseWorldBuffId : 0;
    if (buffCmsId) {
      if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, buffCmsId)) {
        this.userBuffs.removeBuff(
          buffCmsId,
          cmsEx.FirstFleetIndex,
          this,
          'remove_region_protect_buff'
        );
      }
    }
  }

  private _removeBattleFormationBuffAtLeaveOcean() {
    this.userBuffs.removeFleetBuffsBySourceType(
      cmsEx.FirstFleetIndex,
      cmsEx.WorldBuffSourceType.BATTLE_FORMATION,
      this,
      'remove_battle_formation_buff_at_leave_ocean'
    );
  }

  private _reduceShipLife(shipId: number) {
    const ship = this.userFleets.getShip(shipId);
    if (!ship) {
      mlog.error('cannot-find-ship-on-_reduceShipLife.', {
        userId: this.userId,
        shipId,
      });
      return;
    }
    const nub: ShipNub = ship.getNub();
    const oldLife: number = nub.life;
    const newLife: number = Math.max(0, oldLife - cms.Const.WreckLossShipLifeValue.value);
    if (oldLife == newLife) {
      // no change.
      return;
    }

    // 수명으로 인한 최대 내구도 감소된 상태에서 현재 내구도가 크다면 최대 내구도로 변경된다
    let durability: number;
    if (oldLife !== 0 && newLife === 0) {
      const expectMaxDurability = Math.floor(ship.getMaxDurability(this.companyStat) * 0.5);
      if (ship.getNub().durability > expectMaxDurability) {
        durability = expectMaxDurability;
      }
    }

    const { userDbConnPoolMgr } = Container.get(LobbyService);
    return tuShipReduceLifeAndDurability(
      userDbConnPoolMgr.getPoolByShardId(this.getUserDbShardId()),
      this.userId,
      shipId,
      newLife,
      durability
    )
      .then(() => {
        ship.setLife(newLife, { user: this, rsn: 'ship_wrecked', add_rsn: null }, this.companyStat);
        const sync: sync.Sync = {
          add: {
            ships: {
              [shipId]: {
                life: nub.life,
              },
            },
          },
        };
        if (durability) {
          ship.setDurability(durability, this, { user: this, rsn: 'ship_wrecked', add_rsn: null });
          _.merge(sync, {
            add: {
              ships: {
                [shipId]: {
                  durability,
                },
              },
            },
          });
        }

        mlog.verbose('a life of wrecked ship has been reduced.', {
          userId: this.userId,
          shipId,
          oldLife,
          newLife,
        });

        return this.sendJsonPacket<sync.Resp>(0, proto.Ocean.SHIP_WRECKED_SC, { sync });
      })
      .catch((e) => {
        mlog.error('ship life update query error', {
          userId: this._userId,
          shipId,
          error: e.message,
          stack: e.stack,
        });
      });
  }

  addLocalFixedDoodadSpawnEntries(cmsId: number) {
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    if (sailState) {
      sailState.localFixedDooadSpawner.addLocalFixedDoodadSpawnEntries(this.userId, cmsId);
    }
  }

  private _removeAllLocalFixedDoodadSpawnEntries() {
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    if (sailState) {
      sailState.localFixedDooadSpawner.removeAllLocalFixedDoodadSpawnEntries();

      mlog.debug('_removeAllLocalFixedDoodadSpawnEntries', {
        userId: this.userId,
      });
    }
  }

  addCurLevelCompanyExpBuff(buffSync?: BuffSync): void {
    // CompanyExp buff 은 1함대 고정
    const newBuffs = cms.CompanyExp[this.level].worldBuffId;
    if (!newBuffs) {
      return;
    }
    for (const cmsId of newBuffs) {
      this.userBuffs.addSingleBuff(
        cmsId,
        cmsEx.FirstFleetIndex,
        this.level,
        cmsEx.WorldBuffSourceType.COMPANY_LEVEL,
        this,
        buffSync
      );
    }
  }

  // [Callback: 로딩 완료]
  onTownLoadComplete(): void {
    this._addMateBuffLiveEventAtLoadComplete();

    const { raidManager } = Container.get(LobbyService);

    if (this._bFirstLoadComplete === true) {
      this._bFirstLoadComplete = false;
      raidManager.onUserFirstLoadComplete(this);
    }

    this._addOrRemoveOccupiedNationBuffAtLoadComplete();
    const curTimeUtc = mutil.curTimeUtc();
    this.updateNationEffectPromiseBuffIfNeeded(curTimeUtc);
  }

  // [Callback: 로딩 완료]
  onBattleLoadComplete(): void {
    const { raidManager } = Container.get(LobbyService);

    if (this._bFirstLoadComplete === true) {
      this._bFirstLoadComplete = false;
      raidManager.onUserFirstLoadComplete(this);
    }
  }

  // [Callback: 로딩 완료]
  onOceanLoadComplete(): void {
    const { raidManager } = Container.get(LobbyService);

    this._addEncountProtectBuffAtOceanLoadComplete();
    this._addOrRemoveHungerDebuffAtLoadComplete();
    this._addWorldTileDebuffAtLoadComplete();
    this._addOrRemoveOccupiedNationBuffAtLoadComplete();
    this._addBattleFormationBuffAtLoadComplete();
    this._addTowingDebuffAtLoadComplete();
    this._addMateBuffLiveEventAtLoadComplete();

    const sailState = this.userSailing.getSailState();
    if (sailState) {
      sailState.bubbleEvent.onLoadComplete(this);
    }

    if (this._bFirstLoadComplete === true) {
      this._bFirstLoadComplete = false;
      raidManager.onUserFirstLoadComplete(this);
    }

    const curTimeUtc = mutil.curTimeUtc();
    this.updateNationEffectPromiseBuffIfNeeded(curTimeUtc);
  }

  // [Callback: 선단 레벨변경]
  private _onLevelChanged(prevLevel: number, newLevel: number, buffSync?: BuffSync) {
    // 인카운트 보호 버프를 적용 및 해제.
    this._removeEncountProtectBuffAtChangedLevel(prevLevel, buffSync);

    // update stat
    for (const elem of _.values(this.userFleets.getFleets())) {
      this.companyStat.getFleetStat(elem.fleetIndex).chg_UpdateCompanyLevel(newLevel);
    }

    // CompanyExp buff
    {
      const oldBuffs = cms.CompanyExp[prevLevel].worldBuffId;
      const newBuffs = cms.CompanyExp[newLevel].worldBuffId;
      if (!_.isEqual(oldBuffs, newBuffs)) {
        // CompanyExp buff 은 1함대 고정

        if (oldBuffs) {
          for (const cmsId of oldBuffs) {
            this.userBuffs.removeBuff(
              cmsId,
              cmsEx.FirstFleetIndex,
              this,
              'changed_level',
              null,
              buffSync
            );
          }
        }
        this.addCurLevelCompanyExpBuff(buffSync);
      }
    }

    if (this.userState.isInTown()) {
      const townInfo = this.userTown.getTownInfo();
      if (townInfo) {
        const townUserSyncData: TownUserSyncData = {
          user: {
            companyLevel: this.level,
          },
        };

        const townApi = mhttp.townpx.channel(townInfo.url);
        townApi.updateTownUserSyncData(this.userId, townUserSyncData).catch((err) => {
          mlog.error('Town api updateTownUserSyncData is failed at _onLevelChanged.', {
            userId: this.userId,
            err: err.message,
          });
        });
      }
    } else if (this.userState.isInOcean()) {
      const app = Container.get(LobbyService);
      const packet = new Protocol.LB2OC_NTF_SYNC_COMPANY_LEVEL();
      packet.userId = this.userId;
      packet.companyLevel = this.level;
      app.tcpClientSessionManager.send(this.getZoneInfo(ZoneType.OCEAN), packet);
    }

    // 선거기간이고 정산중이 아니며 후보인 경우. 레벨업데이트
    const { nationManager, nationRedis, worldPubsub: pubsub } = Container.get(LobbyService);
    const curTimeUtc = mutil.curTimeUtc();
    const userId = this._userId;
    const nationCmsId = this._nationCmsId;
    const nextElectionSessionId = nationManager.getNextElectionSessionId(curTimeUtc);
    if (
      0 < nextElectionSessionId &&
      !NationUtil.isClosingElectionSession(
        nationManager.getLastClosedElectionSessionId(nationCmsId),
        curTimeUtc
      )
    ) {
      const election = nationManager.getElection(this.nationCmsId, nextElectionSessionId);
      if (election) {
        if (amICandidate(election, userId)) {
          mlog.verbose('[ELECTION] _onLevelChanged. I am a candidate', {
            userId,
            nationCmsId,
            nextElectionSessionId,
            newLevel,
          });

          return nationRedis['updateNationElectionCandidateLevel'](
            userId,
            nextElectionSessionId,
            nationCmsId,
            newLevel
          )
            .then(() => {
              const msgObj: NationElectionCandidatesModifiedPubMsg = {
                nationCmsId,
                electionSessionId: nextElectionSessionId,
                candidateUserIds: [userId],
                updateTimeUtc: curTimeUtc,
              };

              pubsub.publish('nation_election_candidates_modified', JSON.stringify(msgObj));
            })
            .catch((err) => {
              mlog.error('_onLevelChanged.updateNationElectionCandidateLevel failed', {
                msg: err.message,
                stack: err.stack,
              });
            });
        }
      }
    }

    // glog
    this.glog('common_player_level', {
      old_pl: prevLevel,
      cur_pl: newLevel,
    });
  }

  _removeRevoltDebuffAllShips() {
    const firstFleet = this.userFleets.getFirstFleet();
    const ships = firstFleet.getShips();
    _.forOwn(ships, (ship) => {
      this._removeRevoltDebuff(ship.getNub().id);
    });
  }
  _removeRevoltDebuff(shipId: number) {
    const userBuffs = this.userBuffs;
    const revoltDebuffCmsId = cms.Const.munityWorldBuffId.value;
    if (userBuffs.getShipBuffByCmsId(shipId, revoltDebuffCmsId)) {
      userBuffs.removeBuff(revoltDebuffCmsId, shipId, this, 'ship_wrecked');
    }
  }

  // 예인 디버프 해제(항해 퇴장시 제거)
  _removeTowDebuff() {
    const userBuffs = this.userBuffs;
    const towDebuffCmsId: number = cms.Const.TowingSpaceDebuffId.value;
    if (userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, towDebuffCmsId)) {
      userBuffs.removeBuff(towDebuffCmsId, cmsEx.FirstFleetIndex, this, 'towing');
    }
  }

  // [Callback: 인카운트 시작]
  onEncountStart(isAttack: boolean, byQuest?: boolean) {
    this.userState.onEncountStateSet();

    if (byQuest) {
      // 퀘스트로 발생한 인카운트의 경우 보호버프를 해제하지 않는다.
      return;
    }

    if (isAttack) {
      // 인카운트 공격 시도 시 인카운트 보호버프 제거
      this._removeEncountProtectBuffAtEncountStart();
    }
  }

  // [Callback: 인카운트 종료]
  onEncountEnd(isAttack: boolean, result: EncountResult) {
    if (isAttack === false) {
      this._addEncountProtectDefenderBuffAtEncountEnd(result);
    } else {
      this._addEncountProtectAttackerBuffAtEncountEnd();
    }
  }

  // [Callback: 리전 변경]
  onChangedRegion(prevRegionCmsId: number, regionCmsId: number) {
    this._addOrRemoveEncountProtectBuffAtChangedRegion(prevRegionCmsId, regionCmsId);
    this._addOrRemoveOccupiedNationBuffAtChangedRegion(regionCmsId);
  }

  async addOrRemoveClashBuff(buffSync: BuffSync) {
    const { clashSeasonPrizeLookupTable } = Container.get(LobbyService);

    const substact = (source: number[], target: number[]): number[] => {
      if (!source || source.length === 0) {
        return [];
      }
      if (!target || target.length === 0) {
        return source;
      }
      return source.filter((a) => target.findIndex((b: number) => a === b) === -1);
    };

    const clashPrizeManager = Container.get(ClashPrizeManager);
    for (let i = 1; i <= clashSeasonPrizeLookupTable.getRankSize(); i++) {
      const nationCmsId = await clashPrizeManager.getNationCmsId(i);
      if (nationCmsId !== this._nationCmsId) {
        continue;
      }

      const buffs: WorldBuff[] = this.userBuffs.getFleetBuffsBySourceTypeAndSourceId(
        cmsEx.FirstFleetIndex,
        cmsEx.WorldBuffSourceType.CLASH_BUFF,
        i
      );

      const curBuffCmsIds: number[] = buffs.map((elem) => elem.nub.cmsId);
      const newBuffCmsIds: number[] = clashSeasonPrizeLookupTable.getBuffCmsIds(i);

      const addedBuffCmsIds = substact(newBuffCmsIds, curBuffCmsIds);
      const deletedBuffCmsIds = substact(curBuffCmsIds, newBuffCmsIds);
      mlog.verbose('[CLASH] addOrRemoveClashBuff', {
        rank: i,
        addedBuffCmsIds,
        deletedBuffCmsIds,
      });

      for (const buffCmsId of deletedBuffCmsIds) {
        if (this.userBuffs.getFleetBuffByCmsId(cmsEx.FirstFleetIndex, buffCmsId)) {
          this.userBuffs.removeBuff(buffCmsId, cmsEx.FirstFleetIndex, this, null, null, buffSync);
        }
      }

      for (const buffCmsId of addedBuffCmsIds) {
        this.userBuffs.addSingleBuff(
          buffCmsId,
          cmsEx.FirstFleetIndex,
          i,
          cmsEx.WorldBuffSourceType.CLASH_BUFF,
          this,
          buffSync
        );
      }
    }
  }

  // [Callback: ocean존 변경]
  onChangeOcean() {
    // oceanZone에 종속적인 체크리스트 초기화
    this._removeAllLocalFixedDoodadSpawnEntries();
  }

  UnpauseOceanBuffForVillageLeave() {
    // 굶주림 , 불안감 디버프 발생조건 생성
    this._addOrRemoveHungerDebuffAtLoadComplete();
  }

  pauseOceanBuffForVillageEnter(): void {
    // step1. 재해 모두 제거 및 재해의 디버프를 제거해준다.
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    sailState.removeDisasterAll(this, 'user_leave_ocean');

    //step2. 굶주림 디버프를 제거해준다.
    this._removeHungerDebuffAtLeaveOcean();

    // step3. 모든 가호를 해제시킨다.
    sailState.removeProtectionAll(this, 'user_leave_ocean');

    // step4. dirty 데이터를 저장한다.
    const app = Container.get(LobbyService);
    const { userDbConnPoolMgr } = app;
    const firstFleet = this.userFleets.getFleet(cmsEx.FirstFleetIndex);
    firstFleet.saveDirtyInOcean(
      userDbConnPoolMgr.getDBConnPoolByUserId(this.getUserDbShardId()),
      this
    );
  }

  oceanSalvageEnter(doodadCmsId: number, curTimeUtc: number): void {
    this.userDiscovery.setSalvageParam({
      salvageDoodadCmsId: doodadCmsId,
      salvageSpotCmsId: cms.OceanDoodad[doodadCmsId].eventDependencyId,
    });

    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    // encount 안걸림 , 해상 시간 흐름 멈춤
    sailState.pauseSailing(curTimeUtc);

    // step1. 재해 모두 제거 및 재해의 디버프를 제거해준다.
    sailState.removeDisasterAll(this, 'user_leave_ocean');

    //step2. 굶주림 디버프를 제거해준다.
    this._removeHungerDebuffAtLeaveOcean();

    // step3. 모든 가호를 해제시킨다.
    sailState.removeProtectionAll(this, 'user_salvage_enter');

    // step4. dirty 데이터를 저장한다.
    const app = Container.get(LobbyService);
    const { userDbConnPoolMgr } = app;
    const firstFleet = this.userFleets.getFleet(cmsEx.FirstFleetIndex);
    firstFleet.saveDirtyInOcean(
      userDbConnPoolMgr.getDBConnPoolByUserId(this.getUserDbShardId()),
      this
    );
  }

  oceanSalvageLeave(curTimeUtc: number) {
    this.userDiscovery.setSalvageParam(null);

    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    sailState.startSailing(curTimeUtc);
  }

  onEnterContinuousSweepReward(curTimeUtc: number) {
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    // encount 안걸림 , 해상 시간 흐름 멈춤
    sailState.pauseSailing(curTimeUtc);

    // step1. 재해 모두 제거 및 재해의 디버프를 제거해준다.
    sailState.removeDisasterAll(this, 'user_leave_ocean');

    //step2. 굶주림 디버프를 제거해준다.
    this._removeHungerDebuffAtLeaveOcean();

    // step3. 모든 가호를 해제시킨다.
    sailState.removeProtectionAll(this, 'user_concentive_local_npc_reward_enter');

    // step4. dirty 데이터를 저장한다.
    const app = Container.get(LobbyService);
    const { userDbConnPoolMgr } = app;
    const firstFleet = this.userFleets.getFleet(cmsEx.FirstFleetIndex);
    firstFleet.saveDirtyInOcean(
      userDbConnPoolMgr.getDBConnPoolByUserId(this.getUserDbShardId()),
      this
    );
  }

  onLeaveContinuousSweepReward(curTimeUtc: number) {
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    sailState.startSailing(curTimeUtc);
  }

  // [Callback: 오션 퇴장]
  onLeaveOcean(): void {
    // step1. 재해 모두 제거 및 재해의 디버프를 제거해준다.
    const sailState: sailingState.SailingState = this.userSailing.getSailState();
    sailState.removeDisasterAll(this, 'user_leave_ocean');

    //step2. 굶주림 디버프를 제거해준다.
    this._removeHungerDebuffAtLeaveOcean();

    // step3. 모든 가호를 해제시킨다.
    sailState.removeProtectionAll(this, 'user_leave_ocean');

    // step4. 월드타일 디버프를 해제시킨다. .
    this._removeWorldTileBuffAtLeaveOcean();

    // step4. 날씨 버프를 해제시킨다.
    if (sailState.lastWeatherBuffCmsId) {
      this._removeWeatherBuff(sailState.lastWeatherBuffCmsId);
    }

    // step5. 리전 보호 버프를 해제시킨다.
    this._removeRegionProtectBuff();

    // step6. dirty 데이터를 저장한다.
    const app = Container.get(LobbyService);
    const { userDbConnPoolMgr } = app;
    const firstFleet = this.userFleets.getFleet(cmsEx.FirstFleetIndex);
    firstFleet.saveDirtyInOcean(
      userDbConnPoolMgr.getDBConnPoolByUserId(this.getUserDbShardId()),
      this
    );

    // step7. oceanZone에 종속적인 체크리스트 초기화
    this._removeAllLocalFixedDoodadSpawnEntries();

    // step8. 전투 포메이션 버프를 해제시킨다.
    this._removeBattleFormationBuffAtLeaveOcean();

    // step9. 모든선박에 적용된 반란 디버프를 해제시킨다.
    this._removeRevoltDebuffAllShips();

    // step10. 예인 디버프를 해제시킨다.
    this._removeTowDebuff();
  }

  //[Callback: 선박의 cargo 변경 시 호출]
  onChangedCargo(shipId: number, cargoCmsId?: number): void {
    // 항해 중 모든 선박들의 식량/물이 없을 경우 굶주림 디버프적용
    // 또는, 선박 중 한대라도 식량/물이 있을 경우 굶주림 디버프해제
    if (cargoCmsId && !isFoodOrWater(cargoCmsId)) {
      return;
    }
    if (!this.userState.isInLoading()) {
      if (this.userState.isInOcean()) {
        this._updateHungerOrAnxietyDebuff();
      }
    }
  }

  // [Callback: 선박 난파]
  onWreckedShip(shipId: number) {
    if (!this.userState.isInLoading()) {
      if (this.userState.isInOcean()) {
        mlog.verbose('ship-wreck-in-ocean', {
          userId: this.userId,
          shipId,
        });

        // 선박에 적용된 재해 제거
        const sailState = this.userSailing.getSailState();
        sailState.removeDisaster(this, shipId, 'ship_wrecked');

        // 선박에 적용된 반란디버프 제거
        this._removeRevoltDebuff(shipId);

        const firstFleet = this.userFleets.getFirstFleet();
        if (firstFleet.isWrecked()) {
          this._removeHungerDebuff();

          // 모든 재해 해소
          sailState.removeDisasterAll(this, 'ship_wrecked', null);

          // 모든 가호 해제
          sailState.removeProtectionAll(this, 'ship_wrecked');
        }

        // 난파된 선박은 수명을 감소한다.
        this._reduceShipLife(shipId);

        // 항해 일지에 기록
        this.userSailingDiaries.addDiaryOceanShipWreck(this, shipId);
      }
    }
  }

  // [Callback: 선박 복원]
  onRecoverWreckedShip(shipId: number) {
    if (!this.userState.isInLoading()) {
      if (this.userState.isInOcean()) {
        mlog.verbose('ship-recover-wreck-in-ocean', {
          userId: this.userId,
          shipId,
        });

        this._updateHungerOrAnxietyDebuff();
      }
    }
  }

  // [Callback: 선박 내구도 변경]
  onChangedShipDurability(shipId: number, max: number, cur: number) {
    const sailState = this.userSailing.getSailState();
    if (sailState) {
      if (cur > 0 && (cur / max) * 1000 <= cms.Const.BubbleShipDamage.value) {
        sailState.bubbleEvent.needToScan();
      }
    }
  }

  // [Callback: 항해사 충성도 변경]
  onChangedMateLoyalty(cmsId: number, loyalty: number) {
    const sailState = this.userSailing.getSailState();
    if (sailState) {
      if ((loyalty / cms.Const.MaxLoyalty.value) * 1000 <= cms.Const.BubbleMateLoyality.value) {
        sailState.bubbleEvent.needToScan();
      }
    }
  }

  // [Callback: 리전 점령국 변경]
  onChangedRegionNation(regionCmsId: number, regionOccupation: RegionOccupation) {
    // 영역내 항해중 + 영역내 항구에 위치하는 유저 대상 적용.
    if (this.userState.getGameEnterState() === GAME_ENTER_STATE.ENTERING) {
      return;
    }

    if (this.userState.isInLoading()) {
      return;
    }

    if (!this._nationCmsId || 0 === this._nationCmsId) {
      return;
    }

    mlog.verbose('onChangedRegionNation', { userId: this.userId, regionCmsId, regionOccupation });

    if (this.userState.isInOcean()) {
      const sailState = this.userSailing.getSailState();
      if (!sailState) {
        return;
      }

      if (!sailState.region) {
        return;
      }

      // 동일한 위치의 리전만 점령국 버프를 추가/제거 적용
      if (regionCmsId !== sailState.region.id) {
        return;
      }
    } else if (this.userState.isInTown()) {
      const townInfo = this.userTown.getTownInfo();
      if (!townInfo) {
        return;
      }

      const townCms = cms.Town[townInfo.cmsId];
      if (!townCms) {
        return;
      }

      // 동일한 위치의 리전만 점령국 버프를 추가/제거 적용
      if (regionCmsId !== townCms.RegionId) {
        return;
      }
    } else {
      return;
    }

    this._applyRegionOccupationBuff(regionCmsId, regionOccupation, 'occupied_nation_protect_buff');
  }

  // 유저가 결투중인지 여부.  상태 체크에서 사용 가능할듯.
  isInDuel(): boolean {
    return this.userDuel.getDuelParam() !== undefined;
  }

  heartBeatTick(curTimeUtc: number): void {
    if (this.getConnState() !== UserConnection.CONNECTION_STATE.LOGGED_IN) {
      return;
    }

    const updateInterval = Math.max(mconf.userHeartBeatInterval / 4, 1);
    const elapsedSecs = curTimeUtc - this._lastUserHeartBeatTimeUtc;
    if (elapsedSecs < updateInterval) {
      return;
    }
    this._lastUserHeartBeatTimeUtc = curTimeUtc;

    const { userCacheRedis } = Container.get(LobbyService);

    if (!this.isOfflineSailingBot) {
      // 실제유저만 heartbeat을 유지시킵니다.
      return userCacheRedis['updateUserHeartBeat'](this.accountId, curTimeUtc).catch((err) =>
        mlog.error('updateUserHeartBeat failed..', err.message)
      );
    } else {
      // bot의 heartBeat처리
      // [todo] redis slave(read only)
      const minHeartBeatTs = curTimeUtc - mconf.userHeartBeatInterval;
      userCacheRedis['getUserHeartBeat'](this.accountId, minHeartBeatTs)
        .then((heartBeat) => {
          if (heartBeat) {
            // loggedIn bot user should get out.
            this.disconnect(UserConnection.DisconnectReason.HeartBeatExpired);
            return;
          }
        })
        .catch((err) => mlog.error('heartBeatTick failed..', err.message));
    }
  }

  setGameOptionPushNotification(
    isAllowed: number,
    isNightAllowed: number,
    allowedPushNotificationGroupIds: number[]
  ): void {
    this._isPushNotificationAllowed = isAllowed;
    this._isNightPushNotificationAllowed = isNightAllowed;
    this._allowedPushNotificationGroupIds = allowedPushNotificationGroupIds;
  }

  // @return 0 or 1
  get isPushNotificationAllowed(): number {
    return this._isPushNotificationAllowed;
  }
  // @return 0 or 1
  get isNightPushNotificationAllowed(): number {
    return this._isNightPushNotificationAllowed;
  }
  // @return nullable
  get allowedPushNotificationGroupIds(): number[] {
    return this._allowedPushNotificationGroupIds;
  }
  // @return WBP_LanguagePopup 의 CultureNames
  get lang(): string {
    return this._lang;
  }

  get lineLangCultre(): string {
    return this._lineLangCulture;
  }

  get createTimeUtc(): number {
    return this._createTimeUtc;
  }

  onWorldEventNotification(wend: WorldEventNotificationData) {
    // 점령국 변경
    if (wend.changedRegionNation) {
      this.onChangedRegionNation(
        wend.changedRegionNation.regionCmsId,
        wend.changedRegionNation.regionOccupation
      );
    }
  }

  // 제독 변경시 기존 월드스킬을 모두 제거한다.
  onChangedLeaderMate() {
    this.userBuffs.removeFleetBuffsBySourceType(
      cmsEx.FirstFleetIndex,
      cmsEx.WorldBuffSourceType.WORLD_SKILL,
      this,
      'change_leader_mate'
    );
  }

  onGuildJoinOrChange() {
    const townInfo = this.userTown.getTownInfo();
    if (townInfo) {
      const guildSync: GuildAppearance = this.userGuild.getGuildAppearance();
      const townApi = mhttp.townpx.channel(townInfo.url);
      townApi.updateTownUserSyncData(this.userId, { user: { guild: guildSync } }).catch((err) => {
        mlog.error('Town api updateTownUserSyncData is failed.', {
          userId: this.userId,
          err: err.message,
          stack: err.stack,
        });
      });
    } else {
      if (this.userState.isInOcean()) {
        const { tcpClientSessionManager } = Container.get(LobbyService);
        const sendp = new Protocol.LB2OC_NTF_GUILD_UPDATE();
        sendp.userId = this.userId;
        sendp.guild = this.userGuild.getGuildAppearance();
        tcpClientSessionManager.send(this.getZoneInfo(ZoneType.OCEAN), sendp);
      }
    }
  }

  onGuildLeave() {
    const townInfo = this.userTown.getTownInfo();
    if (townInfo) {
      const townApi = mhttp.townpx.channel(townInfo.url);
      townApi.updateTownUserSyncData(this.userId, { user: { guild: null } }).catch((err) => {
        mlog.error('Town api updateTownUserSyncData is failed.', {
          userId: this.userId,
          err: err.message,
          stack: err.stack,
        });
      });
    } else {
      if (this.userState.isInOcean()) {
        const { tcpClientSessionManager } = Container.get(LobbyService);

        const sendp = new Protocol.LB2OC_NTF_GUILD_LEAVE();
        sendp.userId = this.userId;
        tcpClientSessionManager.send(this.getZoneInfo(ZoneType.OCEAN), sendp);
      }
    }
  }

  onNationCabinetJoinOrChange() {
    const curTimeUtc = mutil.curTimeUtc();
    const townInfo = this.userTown.getTownInfo();
    const cabinetAppearance = this.userNation.getCabinetAppearance(this.nationCmsId, curTimeUtc);

    mlog.info('[ELECTION] onNationCabinetJoinOrChange', {
      userId: this._userId,
      nationCmsId: this._nationCmsId,
      cabinetAppearance,
    });

    if (townInfo) {
      const townApi = mhttp.townpx.channel(townInfo.url);
      townApi
        .updateTownUserSyncData(this.userId, { user: { nationCabinet: cabinetAppearance } })
        .catch((err) => {
          mlog.error('Town api updateTownUserSyncData for cabinet is failed.', {
            userId: this.userId,
            err: err.message,
            stack: err.stack,
          });
        });
    } else {
      if (this.userState.isInOcean()) {
        const { tcpClientSessionManager } = Container.get(LobbyService);
        const sendp = new Protocol.LB2OC_NTF_NATION_CABINET_UPDATE();
        sendp.userId = this.userId;
        sendp.nationCabinet = cabinetAppearance;
        tcpClientSessionManager.send(this.getZoneInfo(ZoneType.OCEAN), sendp);
      }
    }
  }

  onNationCabinetLeave() {
    mlog.info('[ELECTION] onNationCabinetLeave', {
      userId: this._userId,
      nationCmsId: this._nationCmsId,
    });

    const townInfo = this.userTown.getTownInfo();
    if (townInfo) {
      const townApi = mhttp.townpx.channel(townInfo.url);
      townApi
        .updateTownUserSyncData(this.userId, { user: { nationCabinet: null } })
        .catch((err) => {
          mlog.error('Town api updateTownUserSyncData for cabinet is failed.', {
            userId: this.userId,
            err: err.message,
            stack: err.stack,
          });
        });
    } else {
      if (this.userState.isInOcean()) {
        const { tcpClientSessionManager } = Container.get(LobbyService);
        const sendp = new Protocol.LB2OC_NTF_NATION_CABINET_LEAVE();
        sendp.userId = this.userId;
        tcpClientSessionManager.send(this.getZoneInfo(ZoneType.OCEAN), sendp);
      }
    }
  }

  tickPayInsurance(elapsedDays: number): Promise<void> {
    const firstFleet = this.userFleets.getFirstFleet();
    return firstFleet.payInsurance(elapsedDays, this, false).catch((e) => {
      mlog.error('[payInsurance] process error', {
        userId: this._userId,
        error: e.message,
        stack: mconf.isDev ? undefined : e.stack,
        extra: e.extra,
      });
      this.disconnect(DisconnectReason.TickPayInsuranceError);
    });
  }

  async accumulateQuestTermsSailingDaysAndNotify(elapsedDays: number): Promise<void> {
    if (elapsedDays <= 0) {
      // assert?
      return;
    }

    const user: User = this;
    return Promise.resolve()
      .then(() =>
        user.questManager.accumulateAchievementActionsAndApply(
          [
            {
              achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.SAILING_DAYS_FOR_QUEST,
              addedValue: elapsedDays,
            },
          ],
          user
        )
      )
      .then((resp) => {
        if (resp) {
          return user.sendJsonPacket<sync.Resp>(0, proto.Common.UPDATE_ACHIEVEMNT_SC, resp);
          // 프로토콜 이름이 CMS.Achievement 관련된 것만 의미할 거 같은데,
          // 기존 사용처 보면 CMS.AchievementTerms 이용하는 곳에서 전반적으로 사용하는 패킷인 듯함.
          // 이름을 바꾸거나 QUEST_UPDATE_ACCUMS_SC 같은 걸로 분리하는 게 나을 수도.
        }
        return;
      })
      .catch((e) => {
        mlog.error('[accumulateQuestTerms] process error', {
          userId: this._userId,
          error: e.message,
          stack: mconf.isDev ? undefined : e.stack,
          extra: e.extra,
        });
        this.disconnect(DisconnectReason.AccumulateQuestTermsError);
      })
      .then((): void => {});
  }

  // -----------------------------------------------------------------------------
  glogLoginOutSave(
    type: GLOG_LOGIN_OUT_SAVE_TYPE,
    curTimeUtc: number,
    pt: number,
    rsn: string,
    add_rsn?: string
  ) {
    const location = this.getCurLocation();
    const coordinates = getGLogCoordinate(location);

    const points_data = this.buildGlog_points_data(curTimeUtc);
    const redgem_data = { frv: this.userPoints.freeRedGem, prv: this.userPoints.paidRedGem };

    // [todo] 현재 제독 연대기 달성도
    const max_step: number = null;

    this.glog('login_out_save', {
      rsn,
      add_rsn,
      type,
      pt,
      is_offline: this.isOfflineSailingBot,
      exp: this.exp,
      max_step,
      ship_build_level_western: this.userShipBlueprints.westShipBuildLevel,
      ship_build_level_oriental: this.userShipBlueprints.orientShipBuildLevel,
      region_id: this.getCurRegionCmsId(),
      town_id: this.getCurTownCmsId(),
      coordinates,
      points_data,
      redgem_data,
    });
  }

  // -----------------------------------------------------------------------------
  glogLoginOutSaveForLogout() {
    let gType: GLOG_LOGIN_OUT_SAVE_TYPE;
    let rsn: string;

    if (this.isOfflineSailingBot) {
      gType = GLOG_LOGIN_OUT_SAVE_TYPE.OffSailLogout;
      rsn = 'offsail_logout';
    } else {
      gType = GLOG_LOGIN_OUT_SAVE_TYPE.UserLogout;
      rsn = 'user_logout';
    }
    const curUtc = curTimeUtc();
    const pt = curUtc - this._lastGlogLoginOutSaveTimeUtc;
    this.glogLoginOutSave(gType, curUtc, pt, rsn, null);
  }

  // -----------------------------------------------------------------------------
  // [운영로그] points_data
  // -----------------------------------------------------------------------------
  buildGlog_points_data(curTimeUtc: number): any {
    const points_data = {};
    _.forOwn(cms.Point, (elem) => {
      if (elem.id === cmsEx.EnergyPointCmsId || elem.id === cmsEx.ArenaTicketCmsId) {
        // https://jira.line.games/browse/UWO-9584
        // 행동력이 포인트가 아니게 변경됐지만, CMS.Point 에서 관리됨.
        return;
      }

      const pointKey: string = '_' + elem.id.toString();
      if (elem.id === cmsEx.CashShopMileage) {
        // https://jira.line.games/browse/UWO-16496
        points_data[pointKey] = this.userPoints.getMileage(curTimeUtc);
      } else {
        points_data[pointKey] = this.userPoints.getPoint(elem.id);
      }
    });

    return points_data;
  }

  // -----------------------------------------------------------------------------
  glog(collection: string, data: any): void {
    // set common data.
    const curDate = new Date();
    data._time = moment(curDate).format('YYYY-MM-DD HH:mm:ss');

    data.server_id = mconf.worldId;

    data.nid = this.pubId;
    data.gnid = this.accountId;
    data.pl = this.level;
    data.gameUserId = this.userId;
    data.user_nation = null;
    if (this.nationCmsId) {
      const nationCms = cms.Nation[this.nationCmsId];
      data.user_nation = nationCms.name;
    }

    if (collection !== 'common_register') {
      const leaderMate = this.userMates.getLeaderMate(this.userFleets);
      data.admiral_id = leaderMate ? leaderMate.getNub().cmsId : null;
      data.admiral_lv = leaderMate
        ? [
            leaderMate.getLevel(cmsEx.JOB_TYPE.ADVENTURE),
            leaderMate.getLevel(cmsEx.JOB_TYPE.TRADE),
            leaderMate.getLevel(cmsEx.JOB_TYPE.BATTLE),
          ]
        : null;
    }

    glog(collection, data);
  }

  applyLastUpdateHotTimeBuffUtc(updateTimeUtc: number, buffSync: BuffSync) {
    this._lastUpdateHotTimeBuffUtc = updateTimeUtc;
    _.merge<BuffSync, BuffSync>(buffSync, {
      sync: {
        add: {
          user: {
            lastHotTimeBuffUpdateTimeUtc: updateTimeUtc,
          },
        },
      },
    });
  }

  addHotTimeBuffIfNeeded(curTimeUtc: number, resp: BuffSync, clientTimeUtc: number = null) {
    const availableHotTimeBuffs: { [cmsId: number]: number } = getHotTimeBuff(
      this.level,
      this._lastUpdateHotTimeBuffUtc,
      curTimeUtc
    );

    const buffSync: BuffSync = {};
    for (const [cmsIdStr, endTimeUtc] of Object.entries(availableHotTimeBuffs)) {
      const cmsId = parseInt(cmsIdStr);
      const hotTimeBuffCms = getHotTimeBuffCms()[cmsId];
      for (const buffId of hotTimeBuffCms.worldBuffId) {
        const worldBuffCms = cms.WorldBuff[buffId];
        if (!worldBuffCms) {
          throw new MError('invalid-world-buff-cms', MErrorCode.INVALID_HOT_TIME_BUFF_ID, {
            hotTimeBuffCmsId: hotTimeBuffCms.id,
            buffId,
          });
        }

        // hotTimeBuff는 선단 또는 함대를 타겟으로 함.
        if (
          worldBuffCms.buffTargetType === cmsEx.WorldTargetType.SHIP ||
          worldBuffCms.buffTargetType === cmsEx.WorldTargetType.MATE
        ) {
          throw new MError(
            'invalid-world-target-type',
            MErrorCode.INVALID_HOT_TIME_BUFF_TARGET_TYPE,
            {
              hotTimeBuffCmsId: hotTimeBuffCms.id,
              buffTargetType: worldBuffCms.buffTargetType,
            }
          );
        }

        const nub: WorldBuffNub = WorldBuffUtil.makeNubWithCustomEndTime(
          worldBuffCms,
          worldBuffCms.buffTargetType === cmsEx.WorldTargetType.FLEET ? 1 : 0,
          cmsEx.WorldBuffSourceType.HOT_TIME_BUFF,
          cmsId,
          curTimeUtc,
          endTimeUtc
        );

        this.userBuffs.addSingleBuffByWBNub(nub, this, buffSync);
      }
    }

    _.merge<BuffSync, BuffSync>(resp, buffSync);

    if (Object.keys(availableHotTimeBuffs).length > 0) {
      this.applyLastUpdateHotTimeBuffUtc(curTimeUtc, resp);
      mlog.info('added-hot-time-buffs.', {
        userId: this.userId,
        lastUpdateHotTimeBuffUtc: this._lastUpdateHotTimeBuffUtc,
        clientTimeUtc: clientTimeUtc ? clientTimeUtc : null,
        availableHotTimeBuffs,
        buffSync,
      });
    }
  }

  addReturnerBuffIfNeeded(curTimeUtc: number, buffSync: BuffSync) {
    const curDate = new Date(curTimeUtc * 1000);

    _.forOwn(cms.ReturnerBuff, (elem) => {
      const startDate = mutil.newDateByCmsDateStr(elem.startDate);
      const endDate = mutil.newDateByCmsDateStr(elem.endDate);

      if (
        elem.minLv > this.level ||
        elem.maxLv < this.level ||
        startDate > curDate ||
        endDate < curDate
      ) {
        return;
      }

      for (const buffId of elem.worldBuffId) {
        const worldBuffCms = cms.WorldBuff[buffId];
        if (!worldBuffCms) {
          throw new MError('invalid-world-buff-cms', MErrorCode.INVALID_RETURNER_BUFF_ID, {
            returnerBuffCmsId: elem.id,
            buffId,
          });
        }

        if (!worldBuffCms.duration) {
          throw new MError(
            'invalid-world-buff-duration',
            MErrorCode.INVALID_RETURNER_BUFF_DURATION,
            {
              returnerBuffCmsId: elem.id,
              buffId,
            }
          );
        }

        const endTimeUtc = curTimeUtc + worldBuffCms.duration;

        const nub: WorldBuffNub = WorldBuffUtil.makeNubWithCustomEndTime(
          worldBuffCms,
          worldBuffCms.buffTargetType === cmsEx.WorldTargetType.FLEET ? 1 : 0,
          cmsEx.WorldBuffSourceType.RETURNER_BUFF,
          elem.id,
          curTimeUtc,
          endTimeUtc
        );

        this.userBuffs.addSingleBuffByWBNub(nub, this, buffSync);
      }
    });
  }

  updateGuildBuffs(gbCmses: GuildBuffDesc[], sync?: BuffSync): void {
    const buffSync: BuffSync = {};

    // 이미 적용중인 버프 목록
    const takenBuffs: number[] = [];

    // 먼저 기존 버프는 제거.
    const worldBuffs = this.userBuffs.getFleetBuffsBySourceType(
      cmsEx.FirstFleetIndex,
      cmsEx.WorldBuffSourceType.GUILD_BUFF
    );

    worldBuffs.forEach((wb: WorldBuff) => {
      const index = gbCmses.findIndex((gb) => {
        return gb.worldBuffId === wb.nub.cmsId;
      });
      if (index === -1) {
        this.userBuffs.removeBuff(
          wb.nub.cmsId,
          cmsEx.FirstFleetIndex,
          this,
          'update_guild_buff',
          null,
          buffSync
        );
      } else {
        takenBuffs.push(wb.nub.cmsId);
      }
    });

    gbCmses.forEach((guildBuffCms: GuildBuffDesc) => {
      // 이미 보유한 버프는 스킵.
      const index = takenBuffs.findIndex((takenBuffCmsId) => {
        return guildBuffCms.worldBuffId === takenBuffCmsId;
      });
      if (-1 !== index) {
        return;
      }

      const worldBuffCms = cms.WorldBuff[guildBuffCms.worldBuffId];
      if (!worldBuffCms) {
        return;
      }
      if (worldBuffCms.buffTargetType === cmsEx.WorldTargetType.COMPANY) {
        this.userBuffs.addSingleBuff(
          worldBuffCms.id,
          0,
          0,
          cmsEx.WorldBuffSourceType.GUILD_BUFF,
          this,
          buffSync
        );
      } else if (worldBuffCms.buffTargetType === cmsEx.WorldTargetType.FLEET) {
        this.userBuffs.addSingleBuff(
          worldBuffCms.id,
          cmsEx.FirstFleetIndex,
          0,
          cmsEx.WorldBuffSourceType.GUILD_BUFF,
          this,
          buffSync
        );
      }
    });

    if (sync) {
      _.merge(sync, buffSync);
      return;
    }

    this.sendJsonPacket(0, proto.Common.BUFF_UPDATE_SC, buffSync);
  }

  // -----------------------------------------------------------------------------
  updateNationEffectPromiseBuffIfNeeded(curTimeUtc: number, sync?: BuffSync) {
    const { nationManager } = Container.get(LobbyService);

    const glogRsnForEffectPromise = 'nation_cabinet_session_changed';
    const lastAppliedSessionId = this.userNation.getLastEffectPromiseAppliedCabinetSessionId();

    const curWeekSessionId = GetFullWeeksUsingLocalTime(
      curTimeUtc,
      cms.Define.NationElectionWeeklySessionPivotDay
    );
    const curCabinetSessionId = nationManager.getCabinetSessionIdByWeeksession(curWeekSessionId);

    // 이미 이번 세션의 공약효과가 적용된경우는 패스
    if (0 < lastAppliedSessionId && lastAppliedSessionId === curCabinetSessionId) {
      return;
    }

    mlog.verbose('[ELECTION] updateNationEffectPromiseBuffIfNeeded start', {
      userId: this.userId,
      nationCmsId: this.nationCmsId,
      lastAppliedSessionId,
      curCabinetSessionId,
    });

    // todo. 공약이 하나도 없는 경우는 패스
    // 이미 버프를 적용한 상태에서 세션변경이 있었다면 기존 버프삭제
    if (0 < lastAppliedSessionId && lastAppliedSessionId !== curCabinetSessionId) {
      // 선단버프 삭제
      this.userBuffs.removeCompanyBuffsBySourceType(
        cmsEx.WorldBuffSourceType.NATION_EFFECT_PROMISE,
        this,
        glogRsnForEffectPromise
      );

      // 함대버프 삭제
      this.userBuffs.removeFleetBuffsBySourceType(
        cmsEx.FirstFleetIndex,
        cmsEx.WorldBuffSourceType.NATION_EFFECT_PROMISE,
        this,
        glogRsnForEffectPromise
      );
    }

    if (!this.nationCmsId || 0 === this.nationCmsId) {
      return;
    }

    // 새로운 버프 추가
    const nation = nationManager.get(this.nationCmsId);
    const cabinet = nation.getCabinet(curCabinetSessionId);
    if (cabinet?.effectPromiseCmsIds && cabinet.effectPromiseCmsIds.length > 0) {
      const buffSync: BuffSync = {};

      for (const promiseCmsId of cabinet.effectPromiseCmsIds) {
        const promiseCms = cms.NationPromise[promiseCmsId];
        if (!promiseCms) {
          mlog.warn('[ELECTION] invalid effect promise', {
            userId: this.userId,
            nationCmsId: this.nationCmsId,
            promiseCmsId,
          });
          continue;
        }

        if (promiseCms.buffId) {
          const buffId = promiseCms.buffId;
          const worldBuffCms = cms.WorldBuff[buffId];
          if (!worldBuffCms) {
            mlog.warn('[ELECTION] invalid-world-buff-cms', {
              userId: this.userId,
              nationCmsId: this.nationCmsId,
              promiseCmsId,
              buffId,
            });
            continue;
          }

          // 기획에 확인한결과 효과형 공약의 버프들은 선단과 함대 버프만 사용한다.
          if (
            worldBuffCms.buffTargetType !== cmsEx.WorldTargetType.COMPANY &&
            worldBuffCms.buffTargetType !== cmsEx.WorldTargetType.FLEET
          ) {
            mlog.warn('[ELECTION] invalid-world-buff-target-type', {
              userId: this.userId,
              nationCmsId: this.nationCmsId,
              promiseCmsId,
              buffId,
              buffTargetType: worldBuffCms.buffTargetType,
            });
            continue;
          }

          const nub: WorldBuffNub = WorldBuffUtil.makeNubWithCustomEndTime(
            worldBuffCms,
            worldBuffCms.buffTargetType === cmsEx.WorldTargetType.FLEET ? 1 : 0,
            cmsEx.WorldBuffSourceType.NATION_EFFECT_PROMISE,
            promiseCmsId,
            curTimeUtc,
            cmsEx.TheEndTimeUtc
          );

          mlog.verbose('[ELECTION] updateNationEffectPromiseBuffIfNeeded applying', {
            userId: this.userId,
            nationCmsId: this.nationCmsId,
            curCabinetSessionId,
            promiseCmsId,
            buffId,
          });

          this.userBuffs.addSingleBuffByWBNub(nub, this, buffSync);
        }
      }

      this.userNation.setLastEffectPromiseAppliedCabinetSessionId(curCabinetSessionId);

      if (sync) {
        _.merge(sync, buffSync);
        return;
      }

      this.sendJsonPacket(0, proto.Common.BUFF_UPDATE_SC, buffSync);
    }
  }

  // -----------------------------------------------------------------------------
  //! 로그인시 온라인 유저나 오프라인 유저에 공통으로 적용되어야 하는 버프들은 아래 함수에 추가
  // -----------------------------------------------------------------------------
  async addAllBuffsIfNeeded(
    curTimeUtc: number,
    guildData: GuildData,
    bIsReturnUser: boolean,
    resp: BuffSync
  ) {
    // 선단 성향에 따른 world buff
    if (this.companyJobCmsId) {
      const companyJobWorldBuffIds = cms.CompanyJob[this.companyJobCmsId].worldBuffIds || [];
      for (const cmsId of companyJobWorldBuffIds) {
        const worldBuffCms = cms.WorldBuff[cmsId];
        if (worldBuffCms.saveToDb) {
          // 기획 테이블 버그
          continue;
        }
        const buffCms = cms.WorldBuff[cmsId];
        this.userBuffs.addSingleBuff(
          cmsId,
          buffCms.buffTargetType === cmsEx.WorldTargetType.FLEET ? 1 : 0,
          this.companyJobCmsId,
          cmsEx.WorldBuffSourceType.COMPANY_JOB,
          this,
          resp
        );
      }
    }

    // mateset 에 따른 버프 추가
    this.userMates.addMateSetBuffIfNeeded(this, resp);
    // MATE_STATE_FLAG 에 따른  버프 추가 (glog 부상 발생 시점에만 데이터 추가해야함 null 처리)
    this.userMates.addBuffByStateIfNeeded(this, null, resp);
    // CompanyExp buff
    this.addCurLevelCompanyExpBuff(resp);

    // 라이브 이벤트 적용
    this.userLiveEvents.addMateBuffsIfNeeded(this, curTimeUtc, resp);
    // hotTimeBuff
    this.addHotTimeBuffIfNeeded(curTimeUtc, resp);

    // 복귀 유저 버프
    if (bIsReturnUser) {
      this.addReturnerBuffIfNeeded(curTimeUtc, resp);
    }

    // 칭호 버프.
    this.userTitles.addUserTitleBuffIfNeeded(this, curTimeUtc, resp);

    // 상회 버프
    if (guildData) {
      const guidBuffs: GuildBuffDesc[] = GuildUtil.getGuildBuffsByGuildData(guildData);
      this.updateGuildBuffs(guidBuffs, resp);
    }

    // 국가 효과형 공약 버프
    this.updateNationEffectPromiseBuffIfNeeded(curTimeUtc, resp);

    // 펫 버프
    this.userPets.applyActivePetBuffs(this, resp);

    // 밀수품
    const bIsHasSmuggleGoods = this.userFleets.getFirstFleet().hasSmuggleGoods();
    if (bIsHasSmuggleGoods) {
      this.userSmuggle.addSmuggleWorldBuff(this, resp);
    }

    // 대격돌
    await this.addOrRemoveClashBuff(resp);
  }

  // -----------------------------------------------------------------------------
  isEncounting() {
    if (this.userEncount.getEncountState()) {
      return true;
    }

    if (this.userFriendlyEncount.getEncountState()) {
      return true;
    }

    const state: CLASH_MATCHING_STATE = this.userClash.getMatchingState();
    if (state >= CLASH_MATCHING_STATE.SEARCHING && CLASH_MATCHING_STATE.REJECT >= state) {
      return true;
    }

    return false;
  }

  // -----------------------------------------------------------------------------
  // 패킷 처리 도중에 일어나면 안되는 상황을 예외처리.
  handleUnexpectedException(
    friendlyEncountResult: FriendlyEncountResult,
    curTimeUtc: number,
    rsn: string
  ) {
    return Promise.resolve(() => {
      if (friendlyEncountResult) {
        return this.userFriendlyEncount.endEncount(
          this,
          friendlyEncountResult,
          curTimeUtc,
          sync,
          rsn,
          true
        );
      }
      return null;
    });
  }

  // getToggletUserContext(extraContext?: { [key: string]: string | Date | undefined | number }): Context {
  //   const curTimeUtc = mutil.curTimeUtc();

  //   return {
  //     accountId: this.accountId,                    // Unique identifier for the user's account
  //     userId: this.pubId.toString(),                // Public user identifier used for external systems
  //     sessionId: this.pubId.toString(),             // Session identifier (same as public user ID)

  //     // 이건 proxy에서 추가해주지 않을까?
  //     remoteAddress: this.countryIp,                // User's IP address with country information
  //     currentTime: new Date(),                      // Current time
  //     properties: {
  //       userDbId: this.userId,                      // Internal database ID for the user
  //       userName: this.userName,                    // Display name of the user in game
  //       userNationCmsId: this.nationCmsId,          // Content ID of user's chosen nation
  //       userLevel: this.level,                      // Current experience level of the user
  //       userGuildName: this.userGuild.getGuildName(), // Name of the guild/clan the user belongs to
  //       userDeviceType: this.deviceType,            // Type of device user is playing on (mobile, tablet, etc.)
  //       userCountryCreated: this.countryCreated,    // Country where the user account was created
  //       userLang: this.lang,                        // User's preferred language setting
  //       userCompanyJobCmsId: this.companyJobCmsId,  // Content ID of user's company/job role
  //       userExp: this.exp,                          // Current experience points of the user
  //       userKarma: this.karma,                      // User's karma/reputation value in game
  //       useIsOfflineSailingBot: this.isOfflineSailingBot.toString(), // Whether offline sailing bot is enabled
  //       userStoreCode: this.storeCode,              // App store code where the app was downloaded
  //       userAppVersion: this.appVersion,            // Version of the application user is running
  //       userAccessLevel: this.accessLevel,          // User's access/permission level in the system
  //       userDeviceLang: this.deviceLang,            // Language setting of user's device
  //       userIsInDuel: this.isInDuel().toString(),   // Whether user is currently in a duel
  //       userIsEncounting: this.isEncounting().toString(), // Whether user is in an encounter event
  //       userIp: this.countryIp,                     // User's IP address with country information
  //       hoursSinceJoin: this.getHoursSinceJoin(curTimeUtc),         // Hours since user registration
  //       hoursSinceLastLogin: this.getHoursSinceLastLogin(curTimeUtc), // Hours since last login
  //       ...extraContext,
  //     },
  //   };
  // }

  ensureFeatureEnabled(featureName: string) {
    // const userToggletContext = this.getToggletUserContext();
    // if (!togglet.isEnabled(featureName, userToggletContext)) {
    //   throw new MError(
    //     `Feature '${featureName}' is disabled.`,
    //     MErrorCode.G_FEATURE_DISABLED, {
    //     featureName: featureName,
    //   }
    //   );
    // }
  }

  // /**
  //  * Calculate hours passed since user registration
  //  * @param curTimeUtc Current UTC time in seconds
  //  * @returns Number of hours since user joined
  //  */
  // getHoursSinceJoin(curTimeUtc: number): number {
  //   if (!this.createTimeUtc || this.createTimeUtc === 0) {
  //     return 0; // Return 0 if creation time is not available
  //   }
  //   const secondsPassed = curTimeUtc - this.createTimeUtc;
  //   return Math.floor(secondsPassed / SECONDS_PER_HOUR);
  // }

  // /**
  //  * Calculate hours passed since user's last login
  //  * @param curTimeUtc Current UTC time in seconds
  //  * @returns Number of hours since last login
  //  */
  // getHoursSinceLastLogin(curTimeUtc: number): number {
  //   // Use lastLoginTimeUtc from database if available, otherwise use current login time
  //   const lastLoginTime = this._loginTimeUtc;
  //   if (!lastLoginTime || lastLoginTime === 0) {
  //     return 0; // Return 0 if last login time is not available
  //   }

  //   const secondsPassed = curTimeUtc - lastLoginTime;
  //   return Math.floor(secondsPassed / SECONDS_PER_HOUR);
  // }
}
