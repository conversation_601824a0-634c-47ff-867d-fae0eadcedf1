"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const minimist_1 = __importDefault(require("minimist"));
const mysql = __importStar(require("promise-mysql"));
const util_1 = require("util");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const procedureNameFilter = /create .*procedure `(.*)`/i;
function getConnection(connectionOption) {
    return mysql.createConnection(connectionOption).disposer((connection) => {
        connection.end();
    });
}
async function apply(connectionOption, databaseName, files) {
    const dbConn = await mysql.createConnection(connectionOption);
    const rows = await dbConn.query(`
      SELECT ROUTINE_NAME
        FROM INFORMATION_SCHEMA.ROUTINES
       WHERE ROUTINE_SCHEMA = '${databaseName}'
         AND ROUTINE_TYPE = 'PROCEDURE';
    `);
    if (rows.length === 0) {
        console.log(`No procedure in '${databaseName}' database`);
    }
    else {
        console.log(`Dropping procedures for '${databaseName}' database`);
        const dropQueries = rows
            .map((row) => {
            const procedureName = row.ROUTINE_NAME;
            return `DROP PROCEDURE \`${databaseName}\`.\`${procedureName}\``;
        })
            .join(';');
        await dbConn.query(dropQueries);
    }
    for (const filename of files) {
        const createProcedure = await (0, util_1.promisify)(fs_1.default.readFile)(filename, { encoding: 'utf-8' });
        const matched = createProcedure.match(procedureNameFilter);
        const procedureName = matched[1];
        console.log(`Creating procedure '${procedureName}'`);
        await dbConn.query(createProcedure);
    }
    await dbConn.end();
}
// async function apply(connectionOption, databaseName, files) {
//   console.log(`Connecting to '${connectionOption.host}:${connectionOption.port}'`);
//   return using(getConnection(connectionOption), async (connection) => {
//     const rows = await connection.query(`
//       SELECT ROUTINE_NAME
//         FROM INFORMATION_SCHEMA.ROUTINES
//        WHERE ROUTINE_SCHEMA = '${databaseName}'
//          AND ROUTINE_TYPE = 'PROCEDURE';
//     `);
//     if (rows.length === 0) {
//       console.log(`No procedure in '${databaseName}' database`);
//     } else {
//       console.log(`Dropping procedures for '${databaseName}' database`);
//       const dropQueries = rows
//         .map((row) => {
//           const procedureName = row.ROUTINE_NAME;
//           return `DROP PROCEDURE \`${databaseName}\`.\`${procedureName}\``;
//         })
//         .join(';');
//       await connection.query(dropQueries);
//     }
//     for (const filename of files) {
//       const createProcedure = await promisify(fs.readFile)(filename, { encoding: 'utf-8' });
//       const matched = createProcedure.match(procedureNameFilter);
//       const procedureName = matched[1];
//       console.log(`Creating procedure '${procedureName}'`);
//       await connection.query(createProcedure);
//     }
//   });
// }
function main() {
    const argv = (0, minimist_1.default)(process.argv.slice(2));
    const configPath = argv.config;
    console.log(`Loading config from '${configPath}'`);
    // --config 로 설정 파일 지정
    const config = JSON.parse(fs_1.default.readFileSync(configPath, { encoding: 'utf-8' }));
    // --env 로 설정 파일 내의 환경 지정
    const environment = argv.env;
    const connectionOption = config[environment];
    const databaseName = connectionOption.database;
    const inputDirectory = argv.input || 'procedures';
    const fileFilter = /.*\.sql/;
    const files = fs_1.default
        .readdirSync(inputDirectory)
        .filter((file) => fileFilter.test(file))
        .map((file) => path_1.default.join(inputDirectory, file));
    return apply(connectionOption, databaseName, files);
}
main()
    .then(() => {
    console.log('Finished');
    process.exit(0);
})
    .catch((err) => {
    console.log(err.message);
    console.error(err.stack);
    process.exit(-1);
});
//# sourceMappingURL=index.js.map