{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T07:22:57.974Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T07:22:57.980Z"}
{"level":"info","message":"[Session] socket disposed, cZfLa0EI","timestamp":"2025-08-22T07:22:57.981Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T07:22:57.981Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T07:22:58.075Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T07:22:58.075Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:22:58.220Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T07:22:58.221Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:22:58.224Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T07:22:59.227Z"}
{"environment":"development","type":"townd","gitCommitHash":"58e140f834d","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T15:41:41+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:23:03.225Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:23:29.396Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:23:29.404Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T07:23:29.405Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T07:23:29.677Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T07:23:29.678Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T07:23:29.679Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T07:23:29.680Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T07:23:33.454Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T07:23:33.455Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T07:23:33.455Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T07:23:33.462Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T07:23:33.463Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T07:23:33.476Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T07:23:33.558Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.579Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.595Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.607Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.621Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.632Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.649Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.666Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.682Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:23:33.699Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T07:23:33.776Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T07:23:33.777Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T07:23:33.781Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T07:23:33.874Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T07:23:33.876Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T07:23:33.877Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T07:23:33.877Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T07:23:33.880Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T07:23:33.880Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T07:23:33.880Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T07:23:33.881Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T07:23:33.883Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T07:23:33.883Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T07:23:33.884Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T07:23:33.884Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T07:23:33.885Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T07:23:33.889Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T07:23:33.889Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.891Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.891Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.891Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:23:33.892Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.901Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.911Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.918Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.927Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.934Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.947Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.954Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.960Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.968Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.974Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.980Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.988Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:33.995Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:34.002Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:34.008Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:34.015Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:34.022Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:34.029Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:23:34.036Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:23:34.046Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T07:23:34.047Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:23:34.049Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:34.051Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:23:34.052Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:35.054Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:23:35.055Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:23:36.057Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:23:36.057Z"}
{"pingInterval":2000,"curDate":1755847417,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T07:23:37.145Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T07:23:37.146Z"}
{"level":"info","message":"[SessionManager] session created: TxpAulua, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T07:23:43.347Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T07:23:43.352Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T07:23:43.353Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T07:23:43.353Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T07:26:47.999Z"}
{"townCmsId":11000000,"channelId":"YlmTF6NH","level":"info","message":"townZone created","timestamp":"2025-08-22T07:26:48.000Z"}
{"url":"/enter","status":"200","response-time":"59.801","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:26:48.002Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T07:26:48.002Z"}
{"url":"/loadComplete","status":"200","response-time":"0.788","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:27:00.356Z"}
{"url":"/updateTownUserSyncData","status":"200","response-time":"0.474","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:27:27.586Z"}
{"url":"/leave","status":"200","response-time":"0.816","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:27:32.448Z"}
{"url":"/enter","status":"200","response-time":"0.620","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:27:58.108Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T07:27:58.109Z"}
{"url":"/loadComplete","status":"200","response-time":"0.362","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:28:10.040Z"}
{"url":"/leave","status":"200","response-time":"0.450","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:31:16.317Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T07:31:17.110Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T07:31:17.110Z"}
{"level":"info","message":"[Session] socket disposed, TxpAulua","timestamp":"2025-08-22T07:31:17.111Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T07:31:17.111Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T07:31:17.447Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T07:31:17.447Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:31:17.771Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T07:31:17.772Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:31:17.773Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T07:31:18.775Z"}
{"environment":"development","type":"townd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:31:22.299Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:31:43.444Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:31:43.455Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T07:31:43.457Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T07:31:43.745Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T07:31:43.745Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T07:31:43.746Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T07:31:43.747Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T07:31:47.878Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T07:31:47.879Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T07:31:47.880Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T07:31:47.888Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T07:31:47.889Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T07:31:47.905Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T07:31:47.987Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.008Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.023Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.034Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.048Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.062Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.080Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.097Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.115Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:31:48.132Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T07:31:48.208Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T07:31:48.209Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T07:31:48.213Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T07:31:48.307Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T07:31:48.309Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T07:31:48.311Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T07:31:48.311Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T07:31:48.314Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T07:31:48.314Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T07:31:48.314Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T07:31:48.314Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T07:31:48.316Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T07:31:48.317Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T07:31:48.317Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T07:31:48.318Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T07:31:48.319Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T07:31:48.323Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T07:31:48.324Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.325Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.325Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.326Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.326Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.326Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.326Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.326Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.326Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.326Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.327Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.327Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:31:48.327Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.336Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.348Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.358Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.368Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.376Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.393Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.400Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.408Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.417Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.425Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.433Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.441Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.449Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.458Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.465Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.473Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.481Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.489Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:31:48.497Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:31:48.509Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T07:31:48.510Z"}
{"baseUrl":"http://localhost:10001","body":{"layoutVersion":7},"err":"socket hang up","level":"error","message":"'/sync' exception","timestamp":"2025-08-22T07:31:48.510Z"}
{"msg":"'/sync' api exception","tries":1,"level":"warn","message":"sync try failed","timestamp":"2025-08-22T07:31:48.511Z"}
{"tries":1,"level":"warn","message":"Retry sync.","timestamp":"2025-08-22T07:31:48.511Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:31:49.515Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:49.517Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:49.517Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:50.519Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:50.519Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:51.521Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:51.522Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:52.524Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:52.524Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:53.526Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:53.526Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:54.529Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:54.530Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:55.534Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:55.535Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:56.538Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:56.538Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:31:57.541Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:31:57.541Z"}
{"pingInterval":2000,"curDate":1755847918,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T07:31:58.637Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T07:31:58.638Z"}
{"level":"info","message":"[SessionManager] session created: 49yqu0MP, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T07:32:02.921Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T07:32:02.924Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T07:32:02.925Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T07:32:02.926Z"}
{"gridDimX":10,"gridDimY":8,"gridSizeX":140000,"gridSizeY":175000,"gridStartRow":15,"gridStartCol":12,"level":"verbose","message":"[TEMP] GetGridStartDefaultCoordinate ","timestamp":"2025-08-22T07:32:14.158Z"}
{"townCmsId":11000000,"channelId":"Z17E2KVI","level":"info","message":"townZone created","timestamp":"2025-08-22T07:32:14.160Z"}
{"url":"/enter","status":"200","response-time":"64.445","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:32:14.162Z"}
{"userId":1000,"representedMate":{"cmsId":21200001,"equipments":[{"id":1,"cmsId":22711024,"dye1":null,"dye2":null,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0},{"id":2,"cmsId":22721001,"dye1":14408410,"dye2":3538062,"dye3":null,"dye4":null,"dye5":null,"dye6":null,"isCostume":0}],"colorSkin":null,"colorEye":null,"colorHairAcc1":null,"colorHairAcc2":null,"colorHair":null,"colorBody1":null,"colorBody2":null,"colorFaceAcc1":null,"colorFaceAcc2":null,"colorFaceAcc3":null,"colorCape1":null,"colorCape2":null,"colorCape3":null,"breastSize":null,"isHideHat":0,"isHideCape":0,"isHideFace":0,"equippedIllustCmsId":0},"sidekickMates":{},"level":"verbose","message":"[TEMP] onUserEnter","timestamp":"2025-08-22T07:32:14.162Z"}
{"url":"/loadComplete","status":"200","response-time":"0.850","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:32:26.750Z"}
{"url":"/leave","status":"200","response-time":"0.722","mcode":0,"level":"info","message":"townd-req","timestamp":"2025-08-22T07:33:18.894Z"}
{"level":"info","message":"[Session] socket end.","timestamp":"2025-08-22T07:33:51.208Z"}
{"level":"info","message":"[SessionManager] socket close, session remains: 0","timestamp":"2025-08-22T07:33:51.208Z"}
{"level":"info","message":"[Session] socket disposed, 49yqu0MP","timestamp":"2025-08-22T07:33:51.208Z"}
{"url":"http://DESKTOP-2FFOGVN:10200","level":"info","message":"disconnected from lobbyd:","timestamp":"2025-08-22T07:33:51.208Z"}
{"level":"info","message":"[!] server is stopping: type=townd, signal=SIGINT","timestamp":"2025-08-22T07:33:51.445Z"}
{"level":"verbose","message":"[Finalizer] call finalizer 'maskword' ...","timestamp":"2025-08-22T07:33:51.445Z"}
{"level":"info","message":"stopping server ...","timestamp":"2025-08-22T07:33:51.756Z"}
{"level":"info","message":"[TcpServer] server closed and disposed.","timestamp":"2025-08-22T07:33:51.756Z"}
{"level":"info","message":"redis-pubsub: quiting","timestamp":"2025-08-22T07:33:51.759Z"}
{"level":"info","message":"server stopped","timestamp":"2025-08-22T07:33:52.762Z"}
{"environment":"development","type":"townd","gitCommitHash":"6dc01e1721a","gitCommitMessage":"UWO-30548 - [UWOCN] [클라] 설문지 연동 작업","gitCommitter":"zwang80 <<EMAIL>>","gitCommitDate":"2025-08-22T16:01:07+09:00","gitBranch":"cn_fgt","gitTag":"","gitDescribe":"","gitRepoUrl":"http://orca.motifgames.in:30080/uwo-dev/game.git","packaged":false,"packagedDate":"<not packaged>","binaryCode":"CN","platformName":"SDO","platform":2,"instanceId":0,"hostname":"DESKTOP-2FFOGVN","appId":"townd.0@DESKTOP-2FFOGVN","isDev":true,"level":"info","message":"[!] server is starting...","timestamp":"2025-08-22T07:33:56.455Z"}
{"fileName":"townPacketHandlerSync.js","mapSize":3,"level":"info","message":"[TcpServer] add packetHandler","timestamp":"2025-08-22T07:34:25.508Z"}
{"cfg":{"sharedConfig":{"maxUsersPerChannel":40,"amqp":"amqp://localhost:5673","gameLog":{"FGT":true},"platform":2,"LineGameCode":"UWO","binaryCode":"CN","userHeartBeatInterval":10,"offlineSailingHeartBeatInterval":10,"authPubsubRedis":{"host":"localhost","port":6380},"globalPubsubRedis":{"host":"localhost","port":6380},"monitorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":3},"scriptDir":"monitor","pool":{"min":1,"max":4}},"orderRedis":{"redisCfg":{"host":"localhost","port":6380,"db":18},"scriptDir":"order","pool":{"min":1,"max":4}},"authRedis":{"redisCfg":{"host":"localhost","port":6380,"db":19},"scriptDir":"auth","pool":{"min":1,"max":4}},"mysqlAuthDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_auth","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"userCacheRedis":{"redisCfg":{"host":"localhost","port":6380,"db":13},"pool":{"min":1,"max":4},"scriptDir":"userCache"},"globalMatchRedis":{"redisCfg":{"host":"localhost","port":6380,"db":23},"pool":{"min":1,"max":4},"scriptDir":"globalMatch"},"globalBattleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":24},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleLogMongoDb":{"url":"mongodb://localhost:27018","connOptions":{"dbName":"uwo_judge","poolSize":5,"useNewUrlParser":true,"reconnectTries":1000000}},"configPubsubRedis":{"host":"localhost","port":6380},"sdo":{"appId":"791000809","appSecretKey":"b5d5a46567db40c8912c0d676109bd27","merchantName":"MEIYU_791000809_7864","areaId":1910,"groupId":-1,"maskword":{"url":"http://gdc.jijiagames.com/maskword","timeout":3000},"antiAddiction":{"logCallbackPath":"/sdo-anti-addiction","kickoutCallbackPath":"/sdo-anti-addiction/kickout"},"payment":{"callbackPath":"/sdo-payment-callback"}},"http":{"authd":{"url":"http://localhost:10700"},"navid":{"url":"http://************:34568"},"lgd":{"url":"https://dev-cf-api-integ.line.games","authPwd":"beta_eogkdgotleo_roakr#5"},"lglogd":{},"sdo":{"url":"https://mservice.sdo.com","timeout":30000},"sdoaa":{"url":"https://fcmds.sdo.com","timeout":30000,"remainingTimeWarnThresholdInSec":900}},"enterWorldTokenExpireSec":5400,"maxUsersPerWorld":5000,"stressTest":{"enabled":false,"townInvestCloseTime":30},"bShrinkPacketLog":true,"orderIdCheckWorldUserRate":80,"maxAllowOrderIdPerSec":10,"enterWorldTokenRefreshmentIntervalSec":300,"prologueGnidTimeout":90,"popCountingCronExp":"*/10 * * * *","nationElectionInitSessionId":2800,"AcquiredVotesRefreshIntervalTime":1,"layoutVersion":7},"world":{"worlds":[{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"}]},"configd":{"common":{"sentry":{},"configRedis":{"redisCfg":{"host":"localhost","port":6380,"db":1},"pool":{"min":1,"max":4},"scriptDir":"config"}}},"authd":{"common":{"sentry":{},"defaultAccessLevel":127,"instanceTemplate":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}},"mysqlReqRepLimit":100},"instances":{"authd.0@DESKTOP-2FFOGVN":{"publicApiService":{"bindAddress":"0.0.0.0","port":10701},"privateApiService":{"bindAddress":"0.0.0.0","port":10700,"url":"http://DESKTOP-2FFOGVN:10700"}}}},"lobbyd":{"common":{"sentry":{},"maximumPayloadQueueSize":30,"loginTimeout":8000,"battleAudit":{"active":false,"combatPowerDiffPct":10,"minTurnFactor":0.08,"minActionTimeSec":0.8,"maxRewardBmos":10,"alertCancelsPerHour":3},"ping":{"interval":2000,"timeout":10000,"timeoutInLoading":60000,"timeoutOnClientBackground":10000,"editorTimeout":600000},"userTick":{"interval":1000,"warningElapsedTime":100,"pingPongIntervalSec":5,"chinaUnder18PlayTimeIntervalSec":5,"lineMailPoolingIntervalSec":300,"glogSaveIntervalSec":300},"battleLogRedis":{"redisCfg":{"host":"localhost","port":6380,"db":12},"scriptDir":"battleLog","pool":{"min":1,"max":4}},"battleExpireSec":21600,"battleCancelCooldownSec":180,"invalidGameStateTolerance":1000,"arenaTickSaveInterval":60,"gameGuard":{"active":false,"authCheckIntervalSec":240,"bGenerous":false,"ffidShards":["http://localhost:11300"]},"appGuard":{"active":false,"androidLicenseKey":"TAIBAQE=","iosLicenseKey":"TAIBAQI=","authCheckIntervalSec":180,"authUrl":"https://auth.appguard.co.kr/queryStatus","bGenerous":false},"instanceTemplate":{"worldId":"_DUMMY_","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}},"instances":{"lobbyd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","socketServer":{"bindAddress":"0.0.0.0","port":10100},"apiService":{"bindAddress":"0.0.0.0","port":10200,"url":"http://DESKTOP-2FFOGVN:10200"}}}},"oceand":{"common":{"sentry":{},"zoneTick":{"maxZoneTickSlot":10,"interval":1000,"warningElapsedTime":100},"visibility":{"gridDimension":{"x":2,"y":3},"radius":1},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"0.0.0.0"}}}},"instances":{"oceand.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10800,"url":"http://DESKTOP-2FFOGVN:10800","tcpServer":{"port":10808,"ip":"DESKTOP-2FFOGVN"}}}}},"saild":{"common":{"sentry":{},"offlineSailingTick":{"managerTickInterval":1000,"dbScanTickInterval":1000,"jobTickInterval":1000},"dbAutoSailingScanLimit":10,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"0.0.0.0"}}}},"instances":{"saild.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":11100,"url":"http://DESKTOP-2FFOGVN:11100","tcpServer":{"port":11109,"ip":"DESKTOP-2FFOGVN"}}}}},"townd":{"common":{"sentry":{},"visibility":{"gridDimension":{"x":10,"y":8},"radius":1,"gridExtentScale":4},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"0.0.0.0"}}}},"instances":{"townd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10500,"url":"http://DESKTOP-2FFOGVN:10500","tcpServer":{"port":10508,"ip":"DESKTOP-2FFOGVN"}}}}},"zonelbd":{"common":{"sentry":{},"ping":{"interval":2000,"timeout":10000},"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}},"instances":{"zonelbd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10600,"url":"http://DESKTOP-2FFOGVN:10600"}}}},"realmd":{"common":{"sentry":{},"tickInterval":60000,"lobbydHealthCheck":{"interval":600000,"timeout":6000000},"arenaWeeklyUpdateScanLimit":10000,"instanceTemplate":{"worldId":"_DUMMY_","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}},"instances":{"realmd.0@DESKTOP-2FFOGVN":{"worldId":"UWO-GL-01","apiService":{"bindAddress":"0.0.0.0","port":10900,"url":"http://DESKTOP-2FFOGVN:10900"}}}},"admind":{"sentry":{},"serverHealthCheck":{"timeout":1500},"apiService":{"bindAddress":"0.0.0.0","port":11000,"allowIp":["********","127.0.0.1","***********","***********"]}},"judged":{"common":{"sentry":{},"loopDelayMax":16000,"validationHome":"home/uwo/battle_validation","validationPkg":"home/uwo/battle_validation_pkg","syncDumpIntervalSec":600,"instanceTemplate":{"apiService":{"bindAddress":"0.0.0.0","port":11200,"url":"http://DESKTOP-2FFOGVN:11200"}}}},"navid":{"common":{"instanceTemplate":{"apiService":{"port":34568,"url":"http://************:34568"}}}},"ffid":{"common":{"sentry":{},"gameGuard":{"homeDir":"/home/<USER>/gameguard/"},"instanceTemplate":{"apiService":{"port":11300,"url":"http://************:11300"}}}}},"level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:34:25.517Z"}
{"worldId":"UWO-GL-01","worldConfig":{"id":"UWO-GL-01","address":"localhost","port":10100,"sentry":{},"disabled":false,"bIsNonPK":false,"timezone":8,"countryCode":6,"createDate":"2023-01-16","http":{"saild":{"url":"http://localhost:11100"},"zonelbd":{"url":"http://localhost:10600"},"realmd":{"url":"http://localhost:10900"},"chatd":{"url":"https://dev-volante-chat-api.line.games","salt":"AC65F7D6D0A1D843E9DB6AF855CA"},"lgbillingd":{"url":"https://beta-api.billing.line.games","authPwd":"UWO_beta_eoqkrqlqjs@34$"},"lgpayd":{"url":"https://beta-pay.billing.line.games","authPwd":"UWO_beta_payeoqkrqlqjs@34$"}},"mysqlUserDb":{"shardFunction":"userDbShardDev","sqlDefaultCfg":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_user","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"shards":[{"shardId":0,"sqlCfg":{"database":"uwo_user_00"}},{"shardId":1,"sqlCfg":{"database":"uwo_user_01"}}]},"mysqlWorldDb":{"host":"localhost","port":3307,"user":"motif_dev","password":"dev123$","database":"uwo_world","multipleStatements":true,"supportBigNumbers":true,"bigNumberStrings":true,"connectTimeout":3000,"connectionLimit":10,"flags":"-FOUND_ROWS","driver":"mysql"},"worldPubsubRedis":{"host":"localhost","port":6380},"guildPubsubRedis":{"host":"localhost","port":6380},"townRedis":{"redisCfg":{"host":"localhost","port":6380,"db":11},"scriptDir":"town","pool":{"min":1,"max":4}},"nationRedis":{"redisCfg":{"host":"localhost","port":6380,"db":10},"scriptDir":"nation","pool":{"min":1,"max":4}},"collectorRedis":{"redisCfg":{"host":"localhost","port":6380,"db":4},"scriptDir":"collector","pool":{"min":1,"max":4}},"sailRedis":{"redisCfg":{"host":"localhost","port":6380,"db":15},"scriptDir":"sail","pool":{"min":1,"max":4}},"auctionRedis":{"scriptDir":"auction","shards":[{"auctionCategory":1,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":2,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":3,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":4,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":5,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}},{"auctionCategory":6,"redisCfg":{"host":"localhost","port":6380,"db":2},"pool":{"min":1,"max":4}}]},"guildRedis":{"redisCfg":{"host":"localhost","port":6380,"db":16},"scriptDir":"guild","pool":{"min":1,"max":4}},"arenaRedis":{"redisCfg":{"host":"localhost","port":6380,"db":17},"scriptDir":"arena","pool":{"min":1,"max":4}},"raidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":20},"scriptDir":"raid","pool":{"min":1,"max":4}},"rankingRedis":{"redisCfg":{"host":"localhost","port":6380,"db":21},"scriptDir":"ranking","pool":{"min":1,"max":4}},"userRedis":{"redisCfg":{"host":"localhost","port":6380,"db":9},"scriptDir":"user","pool":{"min":1,"max":4}},"townLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":14},"pool":{"min":2,"max":4},"scriptDir":"zoneLb"},"oceanLbRedis":{"redisCfg":{"host":"localhost","port":6380,"db":5},"pool":{"min":2,"max":2},"scriptDir":"zoneLb"},"blindBidRedis":{"redisCfg":{"host":"localhost","port":6380,"db":22},"pool":{"min":1,"max":4},"scriptDir":"blindBid"},"clientVolanteUrl":"https://dev-volante-chat.line.games/"},"level":"info","message":"Merging world config ...","timestamp":"2025-08-22T07:34:25.518Z"}
{"level":"info","message":"init maskword, result=OK","timestamp":"2025-08-22T07:34:25.755Z"}
{"level":"verbose","message":"[Finalizer] add a finalizer 'maskword' ...","timestamp":"2025-08-22T07:34:25.756Z"}
{"level":"info","message":"SdoAntiAddictionApiClient.init: url=https://fcmds.sdo.com, remainingTimeWarnThresholdInSec=30000, timeout=900, gameServerAddress=localhost","timestamp":"2025-08-22T07:34:25.757Z"}
{"level":"verbose","message":"Start CMS loading","timestamp":"2025-08-22T07:34:25.758Z"}
{"level":"info","message":"cms preprocess started","timestamp":"2025-08-22T07:34:29.312Z"}
{"level":"verbose","message":"preprocessing MateRecruiting","timestamp":"2025-08-22T07:34:29.313Z"}
{"level":"verbose","message":"preprocessing Character","timestamp":"2025-08-22T07:34:29.313Z"}
{"level":"verbose","message":"preprocessing OceanDisaster","timestamp":"2025-08-22T07:34:29.320Z"}
{"level":"verbose","message":"preprocessing EventMission","timestamp":"2025-08-22T07:34:29.320Z"}
{"level":"verbose","message":"preprocessing OceanNpc","timestamp":"2025-08-22T07:34:29.334Z"}
{"level":"verbose","message":"preprocessing OceanNpcMerge","timestamp":"2025-08-22T07:34:29.419Z"}
{"key":"OceanNpcLocal01","lenBeforeMerge":0,"lenBeforeAfter":3999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.438Z"}
{"key":"OceanNpcLocal02","lenBeforeMerge":3999,"lenBeforeAfter":7999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.453Z"}
{"key":"OceanNpcLocal03","lenBeforeMerge":7999,"lenBeforeAfter":11999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.464Z"}
{"key":"OceanNpcLocal04","lenBeforeMerge":11999,"lenBeforeAfter":15999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.477Z"}
{"key":"OceanNpcLocal05","lenBeforeMerge":15999,"lenBeforeAfter":19999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.488Z"}
{"key":"OceanNpcLocal06","lenBeforeMerge":19999,"lenBeforeAfter":23999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.505Z"}
{"key":"OceanNpcLocal07","lenBeforeMerge":23999,"lenBeforeAfter":27999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.521Z"}
{"key":"OceanNpcLocal08","lenBeforeMerge":27999,"lenBeforeAfter":31999,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.536Z"}
{"key":"OceanNpcLocal09","lenBeforeMerge":31999,"lenBeforeAfter":35480,"level":"info","message":"OceanNpcLocal Merge","timestamp":"2025-08-22T07:34:29.553Z"}
{"level":"verbose","message":"preprocessing OceanDoodad","timestamp":"2025-08-22T07:34:29.629Z"}
{"level":"verbose","message":"preprocessing OceanZone","timestamp":"2025-08-22T07:34:29.630Z"}
{"level":"verbose","message":"preprocessing OceanTileDesc","timestamp":"2025-08-22T07:34:29.634Z"}
{"level":"verbose","message":"preprocessing Ship","timestamp":"2025-08-22T07:34:29.734Z"}
{"level":"verbose","message":"preprocessing Town","timestamp":"2025-08-22T07:34:29.736Z"}
{"level":"verbose","message":"preprocessing OceanNpcTemplate","timestamp":"2025-08-22T07:34:29.738Z"}
{"level":"verbose","message":"preprocessing ShipTemplateGroup","timestamp":"2025-08-22T07:34:29.738Z"}
{"level":"verbose","message":"preprocessing Region","timestamp":"2025-08-22T07:34:29.740Z"}
{"level":"verbose","message":"preprocessing BossRaid","timestamp":"2025-08-22T07:34:29.741Z"}
{"level":"verbose","message":"[TEMP] processBossRaid","timestamp":"2025-08-22T07:34:29.741Z"}
{"level":"verbose","message":"preprocessing FleetDispatch","timestamp":"2025-08-22T07:34:29.741Z"}
{"level":"verbose","message":"preprocessing GuildBossRaid","timestamp":"2025-08-22T07:34:29.743Z"}
{"level":"verbose","message":"preprocessing SmuggleGoods","timestamp":"2025-08-22T07:34:29.743Z"}
{"level":"verbose","message":"preprocessing ChangeItems","timestamp":"2025-08-22T07:34:29.743Z"}
{"level":"verbose","message":"preprocessing CashShop","timestamp":"2025-08-22T07:34:29.744Z"}
{"level":"verbose","message":"preprocessing Mate","timestamp":"2025-08-22T07:34:29.745Z"}
{"level":"verbose","message":"preprocessing TownLocation","timestamp":"2025-08-22T07:34:29.748Z"}
{"level":"info","message":"cms preprocess finished","timestamp":"2025-08-22T07:34:29.749Z"}
{"ch":"town_invested","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.751Z"}
{"ch":"development_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.751Z"}
{"ch":"investment_session_closed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.751Z"}
{"ch":"town_mayor_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.751Z"}
{"ch":"town_mayor_shipyard_tax_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.751Z"}
{"ch":"dev_town_nation_share_point_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.752Z"}
{"ch":"town_mayor_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.752Z"}
{"ch":"trade_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.752Z"}
{"ch":"some_trade_goods_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.752Z"}
{"ch":"trade_craze_event_budget_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.752Z"}
{"ch":"smuggle_all_updated","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.752Z"}
{"ch":"trade_unpopular_event_changed","level":"info","message":"redis-pubsub: subscribing ...","timestamp":"2025-08-22T07:34:29.752Z"}
{"path":"/awayNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.761Z"}
{"path":"/contactNpc","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.770Z"}
{"path":"/enter","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.777Z"}
{"path":"/enterBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.784Z"}
{"path":"/friendlyEncountEnd","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.790Z"}
{"path":"/friendlyEncount","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.799Z"}
{"path":"/leave","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.813Z"}
{"path":"/leaveBuilding","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.821Z"}
{"path":"/loadComplete","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.829Z"}
{"path":"/move","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.838Z"}
{"path":"/onTownMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.845Z"}
{"path":"/onTownRepresentedMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.852Z"}
{"path":"/onTownSidekickMateChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.859Z"}
{"path":"/onTownSidekickPetChanged","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.865Z"}
{"path":"/queryMate","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.871Z"}
{"path":"/showEmoticonInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.878Z"}
{"path":"/showSocialAniInstant","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.886Z"}
{"path":"/updateSocialAniPersist","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.898Z"}
{"path":"/updateTownUserSyncData","level":"verbose","message":"Registering api ...","timestamp":"2025-08-22T07:34:29.911Z"}
{"bindAddress":"0.0.0.0","port":10500,"level":"info","message":"start listening ...","timestamp":"2025-08-22T07:34:29.925Z"}
{"level":"info","message":"[TcpServer] bind to, DESKTOP-2FFOGVN : 10508","timestamp":"2025-08-22T07:34:29.927Z"}
{"cfg":"","level":"info","message":"Merging [[object Object]] from configd ...","timestamp":"2025-08-22T07:34:29.929Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:29.931Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:34:29.932Z"}
{"baseUrl":"http://localhost:10600","body":{"appServiceUrl":"http://DESKTOP-2FFOGVN:10500","zoneType":1},"err":"connect ECONNREFUSED 127.0.0.1:10600","level":"error","message":"'/unregisterServerd' exception","timestamp":"2025-08-22T07:34:30.934Z"}
{"err":"'/unregisterServerd' api exception","level":"warn","message":"Failed to unregister.","timestamp":"2025-08-22T07:34:30.934Z"}
{"pingInterval":2000,"curDate":1755848071,"level":"info","message":"registerServerd succeeded","timestamp":"2025-08-22T07:34:31.940Z"}
{"pingInterval":2000,"level":"info","message":"startPing","timestamp":"2025-08-22T07:34:31.940Z"}
{"level":"info","message":"[SessionManager] session created: UUdIz7_t, for: 127.0.0.1, session count: 1","timestamp":"2025-08-22T07:34:36.344Z"}
{"data":{"segments":{"data":{"url":"http://DESKTOP-2FFOGVN:10200"}}},"level":"verbose","message":"[SocketStream] [recv packet], name:SegmentPacket","timestamp":"2025-08-22T07:34:36.349Z"}
{"level":"verbose","message":"[SocketStream] [recv packet], name:LB2TO_REQ_CONNECTED","timestamp":"2025-08-22T07:34:36.350Z"}
{"origin":{},"seq":3,"level":"verbose","message":"[SocketStream] [send packet] size:8 name:TO2LB_RES_CONNECTED","timestamp":"2025-08-22T07:34:36.351Z"}
