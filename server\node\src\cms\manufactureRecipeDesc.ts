// -------------------------------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// -------------------------------------------------------------------------------------------------

// 생산 재료 분류
export enum MANUFACTURE_MATERIAL_TYPE {
    ITEM = 0,
    TRADE_GOODS = 1,
    EQUIP_ID = 2,
    SHIP_PARTS_ID = 3,
    EQUIP_GRADE = 4,
    SHIP_PARTS_GRADE = 5
}

// 생산 진행 상태 타입
export enum MANUFACTURE_PROGRESS_TYPE {
    FAILURE = 0,    // 실패
    SUCCESS = 1,    // 성공
    GREAT_SUCCESS = 2  // 대성공
}

export enum MANUFACTURE_RECIPE_CATEGORY {
    TRADE_GOODS = 1,
    ITEM = 2,
    CEQUIP = 3,
    SHIP_PARTS = 4
}

export enum MANUFACTURE_RECIPE_GRADE {
    GRADE_D = 1,
    GRADE_C = 2,
    GRADE_B = 3,
    GRADE_A = 4,
    GRADE_S = 5
}

export interface ManufactureRecipeDesc {
    id: number;
    manufactureRecipeGrade: number;
    manufactureType: number;
    manufactureLv: number
    recipeCategory: number;
    manufactureGroupId: number;
    contentsTerms: { Id: number; Target: number; Value: number }[];
    mainIngredient: { Type: number; Target: number; Quantity: number; EnchantLv: number }[];
    subIngredient: { Type: number; Target: number; Quantity: number; EnchantLv: number }[];
    workingMateSpecialStat: { Type: number; Val: number };
    workingMate: { AwakenLv: number; JobId: number }[];
    normalRewardFixedId: number;
    normalRewardIsCashMarket: boolean;
    criticalRewardFixedId: number;
    criticalRewardIsCashMarket: boolean;
    manufactureExp: number;
    manufactureTime: number;
    costEnergy: number;
    costManufacturePoint: number;
    costPoint: { Type: number; Value: number };
    defaultFailRate: number;
    defaultSuccessRate: number;
    defaultGreatSuccessRate: number;
    mateAdditionalRate: number;
    statConvertValue: number;
}