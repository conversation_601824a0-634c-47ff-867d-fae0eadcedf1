# 시간 기반 Feature Flag Context 활용 가이드

## 개요

시간 기반 Feature Flag는 날짜, 시간, 요일, 계절 등 시간적 요소를 기반으로 기능의 활성화 여부를 결정합니다. 이 문서는 `splitDateTime` 함수에서 제공하는 시간 관련 필드들을 Feature Flag Context로 활용하는 방법을 설명합니다.

## 주요 시간 Context 필드

### 날짜 관련 필드
- `year`: 연도 (예: 2023)
- `month`: 월 (1-12)
- `day`: 일
- `quarter`: 분기 (1-4)
- `isLeapYear`: 윤년 여부
- `dayOfWeek`: 요일 (1-7, 1:월요일)
- `dayOfYear`: 연중 일수 (1-366)
- `weekNumber`: 연중 주차

### 시간 관련 필드
- `hour`: 시간 (0-23)
- `minute`: 분 (0-59)
- `second`: 초 (0-59)
- `isAM`: 오전 여부
- `isPM`: 오후 여부
- `isMorning`: 아침 시간대 (5-11시)
- `isAfternoon`: 오후 시간대 (12-16시)
- `isEvening`: 저녁 시간대 (17-20시)
- `isNight`: 밤 시간대 (21-4시)

### 업무 관련 필드
- `isWeekend`: 주말 여부
- `isBusinessDay`: 평일 여부
- `isBusinessHours`: 업무시간 여부 (평일 9-18시)
- `isLunchTime`: 점심시간 여부 (12시)
- `isAfterHours`: 업무시간 외 여부

### 특별 기간 필드
- `season`: 계절 (spring, summer, autumn, winter)
- `isStartOfMonth`: 월초 여부
- `isEndOfMonth`: 월말 여부
- `isStartOfYear`: 연초 여부
- `isEndOfYear`: 연말 여부
- `isStartOfQuarter`: 분기 시작 여부
- `isEndOfQuarter`: 분기 말 여부

## 활용 시나리오

### 1. 시간대별 서비스 최적화

#### 피크 타임 최적화
```typescript
// 피크 타임에 경량 버전의 서비스 제공
function getServiceVersion(context) {
  if (context.isBusinessHours && context.isBusinessDay && !context.isLunchTime) {
    return togglet.isEnabled('lightweight-service', context) 
      ? 'lightweight' 
      : 'standard';
  }
  return 'standard';
}
```

#### 야간 배치 작업
```typescript
// 야간에만 무거운 배치 작업 실행
function shouldRunBatchJob(context) {
  return context.isNight && togglet.isEnabled('night-batch-processing', context);
}
```

### 2. 계절 및 이벤트 기반 기능

#### 계절별 테마
```typescript
// 계절에 맞는 UI 테마 적용
function getSeasonalTheme(context) {
  const toggle = togglet.getToggle('seasonal-themes', context);
  if (!toggle.isEnabled()) return 'default';
  
  switch (context.season) {
    case 'spring': return 'spring-blossom';
    case 'summer': return 'summer-ocean';
    case 'autumn': return 'autumn-leaves';
    case 'winter': return 'winter-snow';
    default: return 'default';
  }
}
```

#### 특별 이벤트 기간
```typescript
// 월말/연말 특별 프로모션
function getActivePromotions(context) {
  const promotions = [];
  
  // 월말 프로모션
  if (context.isEndOfMonth && togglet.isEnabled('month-end-promotion', context)) {
    promotions.push('MONTH_END_SPECIAL');
  }
  
  // 연말 프로모션
  if (context.isEndOfYear && togglet.isEnabled('year-end-promotion', context)) {
    promotions.push('YEAR_END_SPECIAL');
  }
  
  // 분기말 재고 정리 세일
  if (context.isEndOfQuarter && togglet.isEnabled('quarter-end-sale', context)) {
    promotions.push('QUARTER_END_CLEARANCE');
  }
  
  return promotions;
}
```

### 3. 업무 시간 기반 기능

#### 고객 지원 가용성
```typescript
// 업무 시간에 따른 고객 지원 채널 결정
function getAvailableSupportChannels(context) {
  const channels = ['faq', 'email'];
  
  // 업무 시간 내 라이브 채팅 활성화
  if (context.isBusinessHours && togglet.isEnabled('live-chat-support', context)) {
    channels.push('live-chat');
  }
  
  // 24/7 전화 지원 (특정 지역만)
  if (togglet.isEnabled('24-7-phone-support', context)) {
    channels.push('phone');
  }
  
  return channels;
}
```

#### 점심 시간 특별 할인
```typescript
// 점심 시간 특별 할인 적용
function getLunchTimeDiscount(context, regularPrice) {
  if (context.isLunchTime && context.isBusinessDay && 
      togglet.isEnabled('lunch-time-discount', context)) {
    return regularPrice * 0.9; // 10% 할인
  }
  return regularPrice;
}
```

### 4. 요일 기반 기능

#### 주말 전용 기능
```typescript
// 주말에만 제공되는 특별 콘텐츠
function getWeekendSpecialContent(context) {
  if (context.isWeekend && togglet.isEnabled('weekend-special-content', context)) {
    return fetchWeekendContent();
  }
  return null;
}
```

#### 요일별 추천 콘텐츠
```typescript
// 요일별 맞춤 추천 콘텐츠
function getDailyRecommendations(context) {
  const toggle = togglet.getToggle('daily-recommendations', context);
  if (!toggle.isEnabled()) return getDefaultRecommendations();
  
  switch (context.dayOfWeek) {
    case 1: return getMondayMotivation();
    case 3: return getWednesdayWisdom();
    case 5: return getFridayFun();
    case 6:
    case 7: return getWeekendSpecials();
    default: return getRegularRecommendations();
  }
}
```

### 5. 시간대별 UI/UX 최적화

#### 다크 모드 자동 전환
```typescript
// 밤 시간대 자동 다크 모드 전환
function shouldEnableDarkMode(context, userPreference) {
  // 사용자 설정이 있으면 우선 적용
  if (userPreference === 'always') return true;
  if (userPreference === 'never') return false;
  
  // 'auto' 설정이거나 설정이 없는 경우 시간대 기반으로 결정
  return (context.isNight || context.isEvening) && 
         togglet.isEnabled('auto-dark-mode', context);
}
```

#### 시간대별 콘텐츠 필터링
```typescript
// 시간대에 따른 콘텐츠 필터링 (예: 아이들을 위한 콘텐츠)
function getContentFilters(context) {
  const filters = { violence: 'moderate', language: 'moderate' };
  
  // 아침/오후 시간대에는 가족 친화적 콘텐츠
  if ((context.isMorning || context.isAfternoon) && 
      togglet.isEnabled('family-friendly-daytime', context)) {
    filters.violence = 'low';
    filters.language = 'clean';
  }
  
  // 밤 시간대에는 성인 콘텐츠 허용
  if (context.isNight && togglet.isEnabled('adult-content-night', context)) {
    filters.violence = 'high';
    filters.language = 'unrestricted';
  }
  
  return filters;
}
```

### 6. 분기/연도 기반 비즈니스 로직

#### 분기별 목표 설정
```typescript
// 분기 시작 시 새로운 목표 설정
function shouldSetNewQuarterlyGoals(context) {
  return context.isStartOfQuarter && 
         togglet.isEnabled('quarterly-goal-setting', context);
}
```

#### 연말 결산 프로세스
```typescript
// 연말 결산 프로세스 활성화
function shouldActivateYearEndProcess(context) {
  // 12월 마지막 주에 연말 결산 프로세스 시작
  const isLastWeekOfYear = context.month === 12 && 
                          context.day > (context.daysInMonth - 7);
  
  return isLastWeekOfYear && 
         togglet.isEnabled('year-end-closing-process', context);
}
```

### 7. 복합 시간 조건

#### 특정 기간 캠페인
```typescript
// 여름 방학 기간 특별 캠페인
function isSummerVacationCampaignActive(context) {
  // 7-8월 여름 방학 기간
  const isSummerVacation = context.season === 'summer' && 
                          (context.month === 7 || context.month === 8);
  
  return isSummerVacation && 
         togglet.isEnabled('summer-vacation-campaign', context);
}
```

#### 특정 시간대 성능 최적화
```typescript
// 트래픽이 많은 시간대에 캐싱 강화
function shouldEnhanceCaching(context) {
  // 업무 시간 중 트래픽이 많은 시간대 (오전 9-11시, 오후 1-3시)
  const isPeakHour = context.isBusinessDay && 
                    ((context.hour >= 9 && context.hour < 11) || 
                     (context.hour >= 13 && context.hour < 15));
  
  return isPeakHour && 
         togglet.isEnabled('enhanced-caching', context);
}
```

## 구현 예시

### ToggletContext에 시간 정보 추가하기

```typescript
// 사용자 컨텍스트에 시간 정보 추가
function createUserContextWithTime(user, timezone = 9) { // 기본값: 한국 시간대
  // 기본 사용자 컨텍스트
  const baseContext = {
    userId: user.id,
    sessionId: user.sessionId,
    properties: {
      country: user.country,
      language: user.language,
      deviceType: user.deviceType
    }
  };
  
  // 시간 정보 추가
  const timeContext = splitDateTime(undefined, timezone);
  
  // 시간 정보를 properties에 추가
  return {
    ...baseContext,
    properties: {
      ...baseContext.properties,
      hour: timeContext.hour,
      isBusinessHours: timeContext.isBusinessHours,
      isWeekend: timeContext.isWeekend,
      season: timeContext.season,
      isEndOfMonth: timeContext.isEndOfMonth,
      // 필요한 다른 시간 속성들 추가
    }
  };
}
```

### 시간 기반 Feature Flag 결정 예시

```typescript
// 시간 기반 Feature Flag 결정
function processUserRequest(user, requestData) {
  // 사용자의 시간대에 맞는 컨텍스트 생성
  const timezone = getUserTimezone(user); // 사용자 시간대 가져오기
  const context = createUserContextWithTime(user, timezone);
  
  // 시간 기반 기능 결정
  const uiTheme = context.properties.isNight 
    ? 'dark' 
    : 'light';
  
  const supportOptions = context.properties.isBusinessHours 
    ? ['chat', 'phone', 'email'] 
    : ['email'];
  
  const specialOffers = [];
  if (context.properties.isWeekend && togglet.isEnabled('weekend-deals', context)) {
    specialOffers.push('WEEKEND_SPECIAL');
  }
  
  if (context.properties.isEndOfMonth && togglet.isEnabled('month-end-deals', context)) {
    specialOffers.push('MONTH_END_SPECIAL');
  }
  
  // 결과 반환
  return {
    uiTheme,
    supportOptions,
    specialOffers,
    // 기타 처리된 데이터
  };
}
```

## 주의사항

1. **시간대(Timezone) 고려**: 사용자의 지역에 맞는 시간대를 적용하여 정확한 시간 기반 결정을 내려야 합니다.

2. **성능 최적화**: 모든 시간 필드를 Context에 포함시키기보다는 실제로 필요한 필드만 선택적으로 사용하세요.

3. **캐싱 고려**: 짧은 시간 내에 여러 번 호출되는 경우, 시간 Context를 적절히 캐싱하여 성능을 최적화할 수 있습니다.

4. **테스트 용이성**: 시간 기반 기능을 테스트할 때는 시간을 모킹(mocking)하여 다양한 시나리오를 테스트할 수 있어야 합니다.

5. **점진적 롤아웃과 결합**: 시간 기반 조건과 사용자 ID 기반 점진적 롤아웃을 결합하여 더 세밀한 기능 제어가 가능합니다.