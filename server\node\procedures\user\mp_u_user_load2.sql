CREATE PROCEDURE `mp_u_user_load2`(
  IN inUserId INT,
  IN inCurTimeUtc INT
)
label_body:BEGIN
  -- 튜토리얼 대유행 이벤트 데이터 로드
  SELECT
    tutorialTradeEventCmsId,
    townCmsId,
    usedBudget,
    isActive,
    isQuestPaused
  FROM
    u_tutorial_craze_trade_events
  WHERE
    userId = inUserId;

  -- 제조 경험치 데이터 로드
  SELECT
    castingExp,
    castingLevel,
    cookingExp,
    cookingLevel,
    sewingExp,
    sewingLevel,
    handmadeExp,
    handmadeLevel,
    medicineExp,
    medicineLevel
  FROM
    u_manufacture_exp
  WHERE
    userId = inUserId;

  -- 제조 진행 정보 로드
  SELECT
    roomCmsId,
    slot,
    recipeId,
    UNIX_TIMESTAMP(startTimeUtc) as startTimeUtc,
    UNIX_TIMESTAMP(completionTimeUtc) as completionTimeUtc,
    progressType,
    mateCmsIds,
    successRate,
    greatSuccessRate,
    extra
  FROM
    u_manufacture_progress
  WHERE
    userId = inUserId
  ORDER BY roomCmsId, slot;

  -- 해금된 레시피 정보 로드
  SELECT
    recipeCmsId,
    unlockTimeUtc
  FROM
    u_manufacture_unlocked_recipes
  WHERE
    userId = inUserId
  ORDER BY recipeCmsId;

END