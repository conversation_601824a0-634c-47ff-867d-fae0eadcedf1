import { DateTime, FixedOffsetZone } from 'luxon';

/**
 * Split DateTime into components
 *
 * @param {string | Date | undefined} input - ISO string, Date object, or undefined (defaults to current time) (optional)
 * @param {number} offsetHours - Time offset (e.g., +9, -5, etc.) (optional)
 *
 * @returns {object} Split result
 */
export default function splitDateTime(
  input?: string | Date,
  offsetHours: number = 0
) {
  const offsetMinutes = offsetHours * 60;
  const zone = FixedOffsetZone.instance(offsetMinutes);
  const dt = input
    ? DateTime.fromJSDate(new Date(input), { zone })
    : DateTime.now().setZone(zone);

  const hour = dt.hour;

  const getSeason = (month: number) => {
    if (month === 12 || month === 1 || month === 2) return 'winter';
    if (month === 3 || month === 4 || month === 5) return 'spring';
    if (month === 6 || month === 7 || month === 8) return 'summer';
    if (month === 9 || month === 10 || month === 11) return 'autumn';
    return null;
  };

  return {
    iso: dt.toISO(),                       // ISO 형식 날짜/시간 문자열
    utc: dt.toUTC().toISO(),               // UTC 기준 ISO 형식 날짜/시간 문자열
    offsetHours,                           // 시간대 오프셋(시간)
    offsetMinutes: dt.offset,              // 시간대 오프셋(분)
    zoneOffsetHours: dt.offset / 60,       // 시간대 오프셋(시간, 소수점)

    // 날짜 관련
    year: dt.year,                         // 연도
    month: dt.month,                       // 월(1-12)
    monthLong: dt.monthLong,               // 월 이름
    day: dt.day,                          // 일
    dayOfWeek: dt.weekday,                // 요일(1-7, 1:월요일)
    dayOfWeekName: dt.weekdayLong,        // 요일 이름 (Monday, Tuesday, ...)
    dayOfYear: dt.ordinal,                // 연중 일수(1-366)
    daysInMonth: dt.daysInMonth,          // 해당 월의 총 일수
    weekNumber: dt.weekNumber,            // 연중 주차
    weekYear: dt.weekYear,                // 주차 기준 연도 (1부터 시작)
    quarter: dt.quarter,                  // 분기(1-4)
    isLeapYear: dt.isInLeapYear,          // 윤년 여부
    isEndOfMonth: dt.day === dt.daysInMonth, // 월말 여부

    // 시간 관련
    hour,                                 // 시(0-23)
    minute: dt.minute,                    // 분(0-59)
    second: dt.second,                    // 초(0-59)
    isAM: hour < 12,                      // 오전 여부
    isPM: hour >= 12,                     // 오후 여부
    isMorning: hour >= 5 && hour < 12,    // 아침 시간대(5-11시)
    isAfternoon: hour >= 12 && hour < 17, // 오후 시간대(12-16시)
    isEvening: hour >= 17 && hour < 21,   // 저녁 시간대(17-20시)
    isNight: hour >= 21 || hour < 5,      // 밤 시간대(21-4시)
    isMidnight: hour === 0,               // 자정 여부
    isNoon: hour === 12,                  // 정오 여부

    // 주말/업무 관련
    isWeekend: dt.weekday >= 6,           // 주말 여부
    isBusinessDay: dt.weekday <= 5,        // 평일 여부
    isBusinessHours: dt.weekday <= 5 && hour >= 9 && hour < 18, // 업무시간 여부
    isLunchTime: hour === 12,             // 점심시간 여부
    isAfterHours: hour >= 18 || hour < 9, // 업무시간 외 여부

    // 계절 및 이벤트
    season: getSeason(dt.month),          // 계절
    isStartOfMonth: dt.day === 1,         // 월초 여부
    isStartOfWeek: dt.weekday === 1,      // 주초(월요일) 여부
    isEndOfWeek: dt.weekday === 7,        // 주말(일요일) 여부
    isStartOfQuarter: [1, 4, 7, 10].includes(dt.month) && dt.day === 1, // 분기 시작 여부
    isEndOfQuarter: [3, 6, 9, 12].includes(dt.month) && dt.day === dt.daysInMonth, // 분기 말 여부
    isStartOfYear: dt.month === 1 && dt.day === 1, // 연초 여부
    isEndOfYear: dt.month === 12 && dt.day === 31, // 연말 여부

    // 기타
    timestamp: dt.toSeconds(),            // UNIX 타임스탬프(초)
    epochMs: dt.toMillis(),               // UNIX 타임스탬프(밀리초)
    isoDate: dt.toISODate(),             // ISO 형식 날짜
    isoTime: dt.toISOTime(),             // ISO 형식 시간
  };
}
