"use strict";
// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cph_Auth_EnterWorld = void 0;
const lodash_1 = __importDefault(require("lodash"));
const typedi_1 = require("typedi");
const mhttp_1 = __importDefault(require("../../../motiflib/mhttp"));
const mconf_1 = __importDefault(require("../../../motiflib/mconf"));
const mutil = __importStar(require("../../../motiflib/mutil"));
const puUserLoad_1 = __importDefault(require("../../../mysqllib/sp/puUserLoad"));
const tuEnterWorld_1 = __importDefault(require("../../../mysqllib/txn/tuEnterWorld"));
const UserConnection = __importStar(require("../../userConnection"));
const offlineSailingBotLogicInLobby = __importStar(require("../../sailing/offlineSailingBotLogicInLobby"));
const server_1 = require("../../server");
const townManager_1 = require("../../townManager");
const userManager_1 = require("../../userManager");
const userConnection_1 = require("../../userConnection");
const const_1 = require("../../../motiflib/const");
const cmsEx = __importStar(require("../../../cms/ex"));
const mlog_1 = __importDefault(require("../../../motiflib/mlog"));
const formula_1 = require("../../../formula");
const tuEnterWorldPostProcess_1 = __importDefault(require("../../../mysqllib/txn/tuEnterWorldPostProcess"));
const cms_1 = __importDefault(require("../../../cms"));
const lobby_1 = require("../../../motiflib/model/lobby");
const oceanCoordinate_1 = require("../../../cms/oceanCoordinate");
const gameState_1 = require("../../../motiflib/model/lobby/gameState");
const userCacheRedisHelper_1 = require("../../../motiflib/userCacheRedisHelper");
const puUserUpdateGuildId_1 = __importDefault(require("../../../mysqllib/sp/puUserUpdateGuildId"));
const guildPubsub_1 = require("../../guildPubsub");
const eventPageDesc_1 = require("../../../cms/eventPageDesc");
const guildUtil_1 = require("../../guildUtil");
const mailDesc_1 = require("../../../cms/mailDesc");
const puDirectMailPendingsDelete_1 = __importDefault(require("../../../mysqllib/sp/puDirectMailPendingsDelete"));
const userFriends_1 = require("../../userFriends");
const friendUtil_1 = require("../../friendUtil");
const const_2 = require("../../const");
const rankingDesc_1 = require("../../../cms/rankingDesc");
const tuUpdateNewOrRevivalUserBenefit_1 = __importDefault(require("../../../mysqllib/txn/tuUpdateNewOrRevivalUserBenefit"));
const userCashShop_1 = require("../../userCashShop");
const userNation_1 = require("../../userNation");
const userEventRanking_1 = require("../../userEventRanking");
const mailBuilder_1 = require("../../../motiflib/mailBuilder");
const puUserLoad2_1 = __importDefault(require("../../../mysqllib/sp/puUserLoad2"));
const sdoGLogs_1 = require("../../../motiflib/sdoGLogs");
const ex_1 = require("../../../cms/ex");
const survey_1 = require("../../survey/survey");
// ----------------------------------------------------------------------------
// 월드 진입 처리.
// ----------------------------------------------------------------------------
const rsn = 'enter_world';
const add_rsn = null;
// ----------------------------------------------------------------------------
class Cph_Auth_EnterWorld {
    // --------------------------------------------------------------------------
    constructor() { }
    // --------------------------------------------------------------------------
    testGameState(user) {
        return true;
    }
    // --------------------------------------------------------------------------
    async exec(user, packet) {
        const body = packet.bodyObj;
        const { isDevLogin, sessionToken, lang, reconnect, enterWorldToken, deviceType, isTestBot, lparam, } = body;
        user.ensureConnState(userConnection_1.CONNECTION_STATE.KEYS_EXCHANGED);
        // 빌드 타이밍에 의해 없을수도 있으니 잠시 이렇게 가드 해둠.
        if (lparam) {
            const luaParam = JSON.parse(lparam);
            user.versionText = luaParam.vertxt;
        }
        return _login(user, packet, isDevLogin, reconnect, sessionToken, enterWorldToken, lang, deviceType, isTestBot, body);
    }
}
exports.Cph_Auth_EnterWorld = Cph_Auth_EnterWorld;
function _login(user, packet, isDevLogin, reconnect, sessionToken, enterWorldToken, lang, deviceType, isTestBot, body) {
    let ewr;
    return mhttp_1.default.authd
        .enterWorld(isDevLogin, sessionToken, mconf_1.default.worldId, enterWorldToken, reconnect)
        .then((resp) => {
        ewr = resp;
        if (ewr.kickReason && ewr.kickReason === const_1.KICK_REASON.WORLD_STATE_FULL) {
            // 월드 포화
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                kickReason: const_1.KICK_REASON.WORLD_STATE_FULL,
                worldId: mconf_1.default.worldId,
                sync: {},
            });
        }
        else if (ewr.kickReason) {
            // 재접속인데 다른 월드로 접속 차단
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                kickReason: ewr.kickReason,
                sync: {},
            });
        }
        if (!ewr.bValidEnterWorldToken) {
            // 중복 로그인 또는 토큰 만료
            return user.sendJsonPacket(packet.seqNum, packet.type, {
                kickReason: const_1.KICK_REASON.INVALID_ENTER_WORLD_TOKEN,
                sync: {},
            });
        }
        return Promise.resolve()
            .then(() => {
            // check if bot user is logged-in to this lobbyd
            const userManager = typedi_1.Container.get(userManager_1.UserManager);
            const prevUser = userManager.getUserByUserId(ewr.userId);
            if (prevUser) {
                userManager.removeLoggedInUser(ewr.userId);
                const isBot = prevUser.isOfflineSailingBot;
                mlog_1.default.error('duplicate user (_login)', {
                    userId: user.userId,
                    isBot,
                    connId: user.getConnId(),
                });
                prevUser.disconnect(UserConnection.DisconnectReason.EnterWorldKick);
                return prevUser.waitForLogout();
            }
            return null;
        })
            .then(() => {
            user.accountId = ewr.accountId;
            user.pubId = ewr.pubId;
            user.accessLevel = ewr.accessLevel;
            user.revision = ewr.revision;
            user.patchRevision = ewr.patchRevision;
            user.countryCreated = ewr.countryCreated;
            if (isTestBot && isDevLogin) {
                user.setIsTestBot(isTestBot);
            }
            return _loginImpl(user, packet, ewr.userId, ewr.pubId, reconnect, lang, deviceType, body);
        });
    });
}
function _loginImpl(user, packet, userId, pubId, reconnect, lang, deviceType, body) {
    // for glog, billing
    const { isDevLogin, osv, dm, deviceLang, lineLang, v, sk, country_ip, os_modulation, emulator, loading_time, loginPlatform, adjust_id, gps_adid, idfa, idfv, udid, steamAppId, steamUserId, channel, subChannel, } = body;
    const townManager = typedi_1.Container.get(townManager_1.TownManager);
    const { nationManager, nationIntimacy, userDbConnPoolMgr, userCacheRedis, userRedis, sailRedis, guildRedis, rankingRedis, townRedis, } = typedi_1.Container.get(server_1.LobbyService);
    let bIsNewUser = false;
    // cms.Const.UserComBackDay로 체크한 복귀유저 여부
    // 복귀 유저 출석판은 EventPageDesc.comeBackDay로 복귀 여부 체크하기 때문에 제외
    let bIsReturnUser = false;
    // for tuEnterWorldPostProcess
    let energyChange;
    let exploreTicketChange;
    let exploreQuickModeChange;
    let karmaChange;
    let eventPageCmsIdsToDelete;
    let weeklyEventChanges;
    let passEventEventPageCmsIdsToDelete;
    let attendanceEventPageCmsIdsToDelete;
    let hotTimeMails = [];
    let newLastReceiveHotTimeUtc;
    let newLastGameState;
    const shieldChanges = [];
    const reputationChanges = [];
    const expiredEventPageCmsIdsOfEventShop = []; // 추후 삭제 필요
    let notifiedMileageMonth;
    let gameEventPageCmsIdsToDelete;
    let waypointSupplyTicketChange;
    let chatTranslationChange;
    let expiredQuestGroupIds;
    let hotSpotProductCmsIdsToDelete;
    let resetPopupCountHotSpotProducts;
    const restrictedProductsToDelete = [];
    const openDurationProducts = [];
    let expiredEquipmentIds = [];
    const shipSlotChanges = [];
    const costumeShipSlotChanges = [];
    const curTimeUtc = mutil.curTimeUtc();
    const newEnterWorldToken = mutil.generateEnterWorldToken(user.accountId);
    const userDbPool = userDbConnPoolMgr.getPoolByUserId(userId);
    const curDate = new Date(curTimeUtc * 1000);
    let sailId;
    let offlineBattleHistories = {};
    let userGuildSync;
    let userLightInfos;
    let guildData;
    return (0, tuEnterWorld_1.default)(userDbPool, userId, pubId, lang, curTimeUtc, country_ip, curTimeUtc)
        .then((ret) => {
        bIsNewUser = ret.bIsNewUser;
        // 신규 유저일 경우 userCacheRedis 에 default 정보 적기.
        if (bIsNewUser) {
            userCacheRedis['setDefault'](userId, pubId).catch((err) => {
                mlog_1.default.error('userCacheRedis setDefault is failed.', {
                    userId,
                    err: err.message,
                });
            });
        }
        const attendances = [];
        if (ret.lastLoginTimeUtc) {
            bIsReturnUser =
                curTimeUtc - ret.lastLoginTimeUtc >= cms_1.default.Const.UserComeBackDay.value * formula_1.SECONDS_PER_DAY;
            // 복귀 유저 출석부 지급.
            const revivalUserAttendance = cmsEx.getRevivalUserAttendance();
            for (const elem of revivalUserAttendance) {
                if (curTimeUtc - ret.lastLoginTimeUtc >= elem.comeBackDay * formula_1.SECONDS_PER_DAY) {
                    attendances.push({
                        eventPageCmsId: elem.id,
                        accum: 0,
                        consecutive: 0,
                        lastAttendanceTimeUtc: null,
                        lastRewardedConsecutiveAttendanceCmsId: null,
                        startTimeUtc: curTimeUtc,
                        endTimeUtc: (0, formula_1.GetNextContentResetTimeByAddDays)(curTimeUtc, elem.activeDay, cms_1.default.ContentsResetHour.EventPageReset.hour),
                    });
                }
            }
        }
        // 신규 유저 출석부 지급
        if (bIsNewUser) {
            const newUserAttendance = cmsEx.getNewUserAttendance();
            const curDate = new Date(curTimeUtc * 1000);
            for (const elem of newUserAttendance) {
                if (curDate >= mutil.newDateByCmsDateStr(elem.startDate)) {
                    attendances.push({
                        eventPageCmsId: elem.id,
                        accum: 0,
                        consecutive: 0,
                        lastAttendanceTimeUtc: null,
                        lastRewardedConsecutiveAttendanceCmsId: null,
                        startTimeUtc: curTimeUtc,
                        endTimeUtc: null,
                    });
                }
            }
        }
        return (0, tuUpdateNewOrRevivalUserBenefit_1.default)(userDbPool, userId, attendances);
    })
        .then(() => {
        return userCacheRedis['updateUserHeartBeatWhenEnterWorld'](user.accountId, curTimeUtc, userId, mconf_1.default.appId);
    })
        .then(async () => {
        // puUserLoad 호출
        const userLoadResult = await (0, puUserLoad_1.default)(userDbPool, userId, curTimeUtc);
        // puUserLoad2 호출
        const userLoad2Result = await (0, puUserLoad2_1.default)(userDbPool, userId, curTimeUtc);
        // 두 결과를 병합
        const mergedResult = {
            ...userLoadResult,
            ...userLoad2Result,
        };
        user.login(mergedResult, curTimeUtc, deviceType, sk, country_ip, isDevLogin && !v ? const_1.DEV_APP_VERSION : v, lineLang, {
            osv,
            deviceLang,
            adjsutId: adjust_id,
            gpsAdid: gps_adid,
            idfa,
            idfv,
            udid,
            steamAppId,
            steamUserId: steamUserId ? BigInt(steamUserId) : null,
            channel,
            subChannel,
        });
        // shield 시간 충전
        lodash_1.default.forOwn(cms_1.default.Shield, (elem) => {
            const change = user.userShield.buildShieldChange(elem.id, curTimeUtc, user.level, user.companyStat);
            if (lodash_1.default.isEqual(change, user.userShield.getShield(elem.id))) {
                return;
            }
            shieldChanges.push(change);
        });
        // enter world 후처리
        // 시간 흐름에 따른 energy, karma 변경 사항.
        if (bIsNewUser) {
            energyChange = {
                lastUpdateTimeUtc: curTimeUtc,
                energy: 0,
            };
        }
        else {
            energyChange = user.userEnergy.buildEnergyChange(curTimeUtc, user.level, user.level);
        }
        karmaChange = user.getKarmaChange(curTimeUtc);
        exploreTicketChange = user.userExplore.buildTicketChange(curTimeUtc);
        exploreQuickModeChange = user.userExplore.buildQuickModeChange(curTimeUtc);
        // hot time.
        const hotTimeCmses = cmsEx.getCurHotTimeBonus(curTimeUtc);
        if (hotTimeCmses) {
            const overHotTime = cms_1.default.Const.OverHotTime.value;
            for (const elem of hotTimeCmses) {
                // 시간 검사.
                if (!(0, formula_1.isContentsTimeUtcInHours)(curTimeUtc, elem.startHour, elem.endHour, overHotTime)) {
                    continue;
                }
                const startDate = (0, formula_1.getDateOfSpecificLocalHourOfToday)(curTimeUtc, elem.startHour);
                const startTimeUtc = Math.floor(startDate.getTime() / 1000);
                const expireDate = (0, formula_1.getDateOfSpecificLocalHourOfToday)(curTimeUtc, elem.endHour);
                let expireTimeUtc = Math.floor(expireDate.getTime() / 1000);
                // 이미 보상을 받은 경우 제외.
                if (user.lastReceiveHotTimeUtc >= startTimeUtc &&
                    user.lastReceiveHotTimeUtc < expireTimeUtc + overHotTime) {
                    continue;
                }
                // 만료 시간 설정.
                // 남은 시간이 일정값보다 작을 경우 만료 시간을 늘려준다.
                const remainingSec = expireTimeUtc - curTimeUtc;
                if (remainingSec < overHotTime) {
                    expireTimeUtc += overHotTime - remainingSec;
                }
                // 보상 결정
                let attachment;
                if (elem.rewardFixeds && elem.rewardFixeds.length > 0) {
                    // levels 가 없고 rewardFixeds 이 있을 경우 rewardFixeds[0] 을 보상으로 첨부한다.
                    // levels 의 배열 길이를 항상 rewardFixeds 의 배열 길이 -1 으로 하여 levels 범위안에 들지 않을 경우
                    // rewardFixeds 배열의 마지막 보상을 첨부한다.
                    attachment = cmsEx.convertRewardFixedToCustomAttachmentStr(elem.rewardFixeds[elem.rewardFixeds.length - 1], false, curTimeUtc);
                    if (elem.levels && elem.levels.length > 0) {
                        for (let i = elem.levels.length - 1; i >= 0; i--) {
                            if (elem.levels[i] < user.level) {
                                break;
                            }
                            attachment = cmsEx.convertRewardFixedToCustomAttachmentStr(elem.rewardFixeds[i], false, curTimeUtc);
                        }
                    }
                }
                hotTimeMails.push(new mailBuilder_1.BuilderMailCreateParams(user.userMails.generateNewDirectMailId(), elem.mailId, curTimeUtc, expireTimeUtc, 0, null, null, null, null, attachment).getParam());
            }
        }
        if (hotTimeMails && hotTimeMails.length > 0) {
            newLastReceiveHotTimeUtc = curTimeUtc;
        }
        // 마일리지 만료 안내
        const newPendingDirectMails = [];
        const curDayOfMonth = curDate.getDate();
        // 만료 7일전 안내이기에 매달 20일에 발송한다.
        if (curDayOfMonth >= 20) {
            const expireMileageInThisMonth = user.userPoints.getExpireMileageInThisMonth(curTimeUtc);
            if (expireMileageInThisMonth &&
                expireMileageInThisMonth.value > 0 &&
                !expireMileageInThisMonth.bIsExpirationNotified) {
                notifiedMileageMonth = expireMileageInThisMonth.month;
                const mileageMailCms = cms_1.default.Mail[mailDesc_1.MileageExpirationMailCmsId];
                let expireTimeUtc = null;
                let bShouldSetExpirationWhenReceiveAttachment = 0;
                if (mileageMailCms.mailKeepTime > 0) {
                    expireTimeUtc = curTimeUtc + mileageMailCms.mailKeepTime;
                }
                else if (mileageMailCms.mailKeepTime === -1) {
                    bShouldSetExpirationWhenReceiveAttachment = 1;
                }
                newPendingDirectMails.push(new mailBuilder_1.BuilderMailCreateParams(null, mileageMailCms.id, Math.floor(new Date(mutil.getLocalFullYear(curDate), mutil.getLocalMonth(curDate), 20).getTime() / 1000), expireTimeUtc, bShouldSetExpirationWhenReceiveAttachment, null, null, null, expireMileageInThisMonth.value, null).getPendingMailParam(user.userId));
            }
        }
        // 선택할 수 있는 국가들 중 평판이 없는 국가가 있을 경우 추가해준다.
        // (기본값에서 20%까지 자연 상승해야 되기 때문에 null인 경우 기본값으로 생각하는 방법을 사용하기 힘듦)
        const selectableNations = cmsEx.getSelectableNations();
        for (const nationCms of selectableNations) {
            if (!user.userReputation.has(nationCms.id)) {
                reputationChanges.push({
                    nationCmsId: nationCms.id,
                    updateTimeUtc: curTimeUtc,
                    reputation: user.userReputation.getNewValue(nationCms.id, curTimeUtc, 0),
                });
            }
        }
        // 계정 생성이 완료된 경우 gameState 값을 lastGameState 에 넣고 gameState 는 NONE 으로 변경
        const oldGameState = user.userState.getGameState();
        if (oldGameState > gameState_1.GAME_STATE.CREATE_ACCOUNT_MAX) {
            // db 상의 lastGameState 가 NONE이 아닌 경우 lastGameState 를 유지한다.
            if (user.userState.getRawLastGameState() === gameState_1.GAME_STATE.NONE) {
                newLastGameState = oldGameState;
            }
            // 모의전 로비 상태는 순단으로 인한 재접속시에만 유지한다
            // 만약 gameState 가 모의전 로비상태인 경우 정상 로그인이면 마을/해양으로 변경한다
            if (0 === reconnect &&
                (user.userState.isInOceanBattleLobby() || user.userState.isInTownBattleLobby())) {
                if (user.userState.isInOceanBattleLobby()) {
                    newLastGameState = gameState_1.GAME_STATE.IN_OCEAN;
                }
                else {
                    newLastGameState = gameState_1.GAME_STATE.IN_TOWN;
                }
                mlog_1.default.info('converted battleLobby state', { newLastGameState });
            }
            else if (user.userState.isInOceanSalvage()) {
                // SalvageEnter 패킷 호출 시 oceanDoodadId가 필요한데 타이밍 이슈로
                // 생성이 안될수 있기 때문에 인양의 경우 재 접속 시 IN_OCEAN으로 설정한다.
                newLastGameState = gameState_1.GAME_STATE.IN_OCEAN;
            }
        }
        // EventPage류
        // 이벤트 미션중 시간이 지난 이벤트를 삭제
        eventPageCmsIdsToDelete = user.userAchievement.getExpiredTimeEventPage(curTimeUtc);
        // 신규 7일 임무가 있으면 시작 날짜 저장
        weeklyEventChanges = user.userAchievement.createWeeklyEvents(curTimeUtc);
        // 만료된 패스 이벤트 삭제
        passEventEventPageCmsIdsToDelete =
            user.userPassEvent.getEventPageCmsIdsToDeleteOnLogin(curTimeUtc);
        // 만료된 출석부 삭제
        attendanceEventPageCmsIdsToDelete = user.userAttendance.getExpiredAttendances(new Date(curTimeUtc * 1000));
        // 만료된 복귀유저 출석부 삭제
        for (const attendance of user.userAttendance.getCombackAttendances()) {
            if (curTimeUtc >= attendance.endTimeUtc) {
                attendanceEventPageCmsIdsToDelete.push(attendance.eventPageCmsId);
            }
        }
        // 만료된 이벤트 게임 삭제
        gameEventPageCmsIdsToDelete = user.userEventGames.getExpiredGames(curTimeUtc);
        // 이벤트 상점 임시 코드
        // -> 서버 재연결 시 eventShopProducts가 초기화되는 이슈가 있어, 임시로 enter_world에서
        // EVENT_SHOP_GET_PRODUCTS 패킷 작업을 임시로 처리함 추후 수정 필요
        const eventPageCmses = cmsEx.getEventPagesForPageType(eventPageDesc_1.EventPageType.EVENT_SHOP);
        // 만료된 eventshop의 eventPageCmsIds
        if (eventPageCmses && eventPageCmses.length > 0) {
            for (const cms of eventPageCmses) {
                if (cmsEx.isFilteredByCountryCode(cms.localBitflag)) {
                    continue;
                }
                const lastExchangeTime = (0, formula_1.getUtcOfLastEventExchangeTime)(cms.endDate, cms.shopRemainHour);
                if (curTimeUtc > lastExchangeTime) {
                    expiredEventPageCmsIdsOfEventShop.push(cms.id);
                }
            }
        }
        waypointSupplyTicketChange = user.userSailWaypoints.buildTicketChange(user, curTimeUtc);
        chatTranslationChange = user.userChatTranslationCount.replenishIf1DayPassed();
        expiredQuestGroupIds = user.questManager.getExpiredQuestGroupIds(curTimeUtc);
        hotSpotProductCmsIdsToDelete = user.userCashShop.getHotSpotProductToDelete(curTimeUtc);
        // 등장 횟수가 존재하는 핫스팟 상품은 db에서 삭제되지 않으므로
        // hotSpotProductCmsIdsToDelete이랑 resetPopupCountHotSpotProducts의 id 가 중복되지 않음
        resetPopupCountHotSpotProducts = Object.values(lodash_1.default.cloneDeep(user.userCashShop.getNeedResetPopupCountHotSpotProducts(curTimeUtc)));
        for (const product of resetPopupCountHotSpotProducts) {
            product.popupCount = 0;
            product.lastResetTimeUtc = curTimeUtc;
        }
        const isInBattle = (newLastGameState >= gameState_1.GAME_STATE.TOWN_BATTLE_MIN &&
            newLastGameState <= gameState_1.GAME_STATE.TOWN_BATTLE_MAX) ||
            (newLastGameState >= gameState_1.GAME_STATE.OCEAN_BATTLE_MIN &&
                newLastGameState <= gameState_1.GAME_STATE.OCEAN_BATTLE_MAX);
        const isInDuel = newLastGameState === gameState_1.GAME_STATE.IN_TOWN_DUEL ||
            newLastGameState === gameState_1.GAME_STATE.IN_TOWN_BATTLE_DUEL ||
            newLastGameState === gameState_1.GAME_STATE.IN_OCEAN_DUEL ||
            newLastGameState === gameState_1.GAME_STATE.IN_OCEAN_BATTLE_DUEL;
        if (!isInBattle && !isInDuel) {
            if (!gameState_1.GsUtil.isInOcean(newLastGameState)) {
                const expiredShipSlotItemIds = user.userInven.getExpiredEquippedShipSlotItemIds(curTimeUtc);
                for (const shipSlotId of expiredShipSlotItemIds) {
                    const shipSlotItem = user.userInven.getShipSlotItem(shipSlotId);
                    if (shipSlotItem.isEquippedCostumeShipSlot) {
                        const shipSlotCms = cms_1.default.ShipSlot[shipSlotItem.shipSlotCmsId];
                        costumeShipSlotChanges.push({
                            slotSubType: shipSlotCms.slotSubType,
                        });
                    }
                    else {
                        shipSlotChanges.push({
                            shipId: shipSlotItem.equippedShipId,
                            slotIndex: shipSlotItem.equippedShipSlotIdx,
                            shipSlotItemId: 0,
                            mateCmsId: null,
                            isLocked: 0,
                        });
                    }
                }
            }
            expiredEquipmentIds = user.userMates.getExpiredEquippedMateEquipments(curTimeUtc);
        }
        if (bIsReturnUser) {
            // 복귀 유저 전용 상품
            const restrictedProducts = user.userCashShop.getRestrictedProducts();
            const returnUserCashShopCmses = cmsEx.getReturnUserCashShopCms();
            for (const cashShopCms of returnUserCashShopCmses) {
                if (user.userCashShop.isActiveProduct(cashShopCms.id, curDate, undefined, 1, [
                    userCashShop_1.UNBUYABLE_REASON.SOLD_OUT,
                    userCashShop_1.UNBUYABLE_REASON.PREVIOUS_ID,
                ]) === userCashShop_1.UNBUYABLE_REASON.BUYABLE) {
                    // 이전에 샀더라도 복귀유저면 다시 구매 가능
                    if (restrictedProducts[cashShopCms.id]) {
                        restrictedProductsToDelete.push(cashShopCms.id);
                    }
                    openDurationProducts.push({
                        cmsId: cashShopCms.id,
                        startTimeUtc: curTimeUtc,
                        endTimeUtc: curTimeUtc + cashShopCms.returnUserSaleDurationDays * formula_1.SECONDS_PER_DAY,
                    });
                }
            }
        }
        return (0, tuEnterWorldPostProcess_1.default)(userDbPool, userId, energyChange, karmaChange, waypointSupplyTicketChange, eventPageCmsIdsToDelete, weeklyEventChanges, passEventEventPageCmsIdsToDelete, attendanceEventPageCmsIdsToDelete, hotTimeMails, newLastReceiveHotTimeUtc, reputationChanges, newLastGameState, shieldChanges, exploreTicketChange, exploreQuickModeChange, expiredEventPageCmsIdsOfEventShop, newPendingDirectMails, notifiedMileageMonth, gameEventPageCmsIdsToDelete, chatTranslationChange, expiredQuestGroupIds, hotSpotProductCmsIdsToDelete, resetPopupCountHotSpotProducts, restrictedProductsToDelete, openDurationProducts, expiredEquipmentIds, shipSlotChanges, costumeShipSlotChanges, curTimeUtc);
    })
        .then(() => {
        // u_ship_slots shipSlotItemId 컬럼 체크용 로그
        mlog_1.default.info('shipSlotChange enterWorld', { userId: user.userId, shipSlotChanges });
        user.userEnergy.applyEnergyChange(energyChange, { user, rsn, add_rsn });
        user.applyKarma(karmaChange, null);
        user.userSailWaypoints.applyTicketChange(waypointSupplyTicketChange);
        if (newLastGameState !== undefined) {
            user.userState.onEnterWorld(newLastGameState);
        }
        user.userExplore.applyTicketChange(exploreTicketChange, {
            user,
            rsn,
            add_rsn,
            maxQuickModeCount: user.userExplore.getMaxTicketCount(user),
        });
        user.userExplore.applyQuickModeChange(exploreQuickModeChange, {
            user,
            rsn,
            add_rsn,
            maxQuickModeCount: user.userExplore.getMaxQuickModeCount(user),
        });
        if (eventPageCmsIdsToDelete && eventPageCmsIdsToDelete.length > 0) {
            user.userAchievement.deleteEventPage(eventPageCmsIdsToDelete);
        }
        if (weeklyEventChanges && weeklyEventChanges.length > 0) {
            for (const change of weeklyEventChanges) {
                user.userAchievement.setWeeklyEvent(change.eventPageCmsId, change.weeklyEventStartTimeUtc);
            }
        }
        if (expiredEventPageCmsIdsOfEventShop && expiredEventPageCmsIdsOfEventShop.length > 0) {
            for (const cmsId of expiredEventPageCmsIdsOfEventShop) {
                user.userEventShop.deleteProductsByExpiredEventPage(cmsId);
            }
        }
        if (passEventEventPageCmsIdsToDelete && passEventEventPageCmsIdsToDelete.length > 0) {
            user.userPassEvent.deletePassEvents(passEventEventPageCmsIdsToDelete);
        }
        if (attendanceEventPageCmsIdsToDelete && attendanceEventPageCmsIdsToDelete.length > 0) {
            user.userAttendance.deleteAttendances(attendanceEventPageCmsIdsToDelete);
        }
        if (gameEventPageCmsIdsToDelete && gameEventPageCmsIdsToDelete.length > 0) {
            user.userEventGames.deleteEventGame(gameEventPageCmsIdsToDelete);
        }
        if (expiredQuestGroupIds && expiredQuestGroupIds.length > 0) {
            user.questManager.deleteQuestGroupIds(expiredQuestGroupIds);
        }
        if (hotSpotProductCmsIdsToDelete && hotSpotProductCmsIdsToDelete.length > 0) {
            user.userCashShop.deleteHotSpotProducts(hotSpotProductCmsIdsToDelete);
        }
        if (resetPopupCountHotSpotProducts && resetPopupCountHotSpotProducts.length > 0) {
            for (const product of resetPopupCountHotSpotProducts) {
                user.userCashShop.setHotSpotProduct(product);
            }
        }
        if (restrictedProductsToDelete && restrictedProductsToDelete.length > 0) {
            user.userCashShop.deleteRestrictedProducts(restrictedProductsToDelete);
        }
        if (expiredEquipmentIds && expiredEquipmentIds.length > 0) {
            for (const id of expiredEquipmentIds) {
                user.userMates.unequipMateEquipment(id, user.companyStat, user.userPassives, user.userFleets, user.userSailing, user.userTriggers, user.userBuffs, null, { user, rsn, add_rsn });
            }
        }
        if (shipSlotChanges && shipSlotChanges.length > 0) {
            for (const change of shipSlotChanges) {
                const userShip = user.userFleets.getShip(change.shipId);
                userShip.applyEquipSlotItem(user, change, { user, rsn, add_rsn }, null);
            }
        }
        if (costumeShipSlotChanges && costumeShipSlotChanges.length > 0) {
            for (const change of costumeShipSlotChanges) {
                user.userFleets.applyCostumeShipSlotItem(user, change, { user, rsn, add_rsn }, null);
            }
        }
        if (openDurationProducts && openDurationProducts.length > 0) {
            for (const product of openDurationProducts) {
                user.userCashShop.setOpenDurationProduct(product);
            }
        }
        if (hotTimeMails && hotTimeMails.length > 0) {
            for (const mail of hotTimeMails) {
                user.userMails.addDirectMail(mail, { user });
            }
        }
        if (newLastReceiveHotTimeUtc) {
            user.lastReceiveHotTimeUtc = newLastReceiveHotTimeUtc;
        }
        for (const elem of shieldChanges) {
            user.userShield.applyShieldChange(elem, { user, rsn, add_rsn });
        }
        for (const change of reputationChanges) {
            user.userReputation.set(change.nationCmsId, {
                reputation: change.reputation,
                updateTimeUtc: change.updateTimeUtc,
            }, { user, rsn, add_rsn });
        }
        if (notifiedMileageMonth) {
            const curMonthMileage = user.userPoints.getExpireMileageInThisMonth(curTimeUtc);
            curMonthMileage.bIsExpirationNotified = true;
        }
        if (chatTranslationChange) {
            user.userChatTranslationCount.applyTranslationNub(chatTranslationChange);
        }
        user.userTitles.deleteUserTitles(curTimeUtc, null);
        // resolve pending mails.
        return user.userMails.resolvePendingDirectMails(false, user);
    })
        .then(() => {
        return user.userPoints.queryCash(user);
    })
        .then(() => {
        // write enterWorldToken to userCacheRedis
        return userCacheRedis['setEnterWorldToken'](user.accountId, newEnterWorldToken, curTimeUtc);
    })
        .then(() => {
        if (user.userState.isInOcean()) {
            return offlineSailingBotLogicInLobby.loadSailState(user, curTimeUtc);
        }
        return null;
    })
        .then((loadSailStateResult) => {
        // get sailId.
        if (user.userState.isInOcean()) {
            if (loadSailStateResult.sailId) {
                sailId = loadSailStateResult.sailId;
            }
        }
        return null;
    })
        .then(() => {
        // 인카운트 회피가능한 유저목록을 얻어오기.
        return userCacheRedis['loadAvoidableEncountUsers'](user.userId);
    })
        .then((avoidableEncountUsers) => {
        const users = avoidableEncountUsers.map((param) => JSON.parse(param));
        user.userEncount.setupAvoidableEncountUser(users);
        return null;
    })
        .then(() => {
        if (user.userGuild.guildId) {
            return guildRedis['getGuild'](user.userGuild.guildId).then((guild) => {
                if (!guild) {
                    // redis에 없는 경우에는 rdb에서 제거해주자.
                    mlog_1.default.error('there-is-no-guild-in-redis', {
                        userId: user.userId,
                        guildId: user.userGuild.guildId,
                    });
                    return (0, puUserUpdateGuildId_1.default)(userDbPool, user.userId, 0).then(() => {
                        user.userGuild.guildId = 0;
                    });
                }
                guildData = JSON.parse(guild);
                if (!guildData.members[user.userId]) {
                    // redis에 member 없는 경우에는 rdb에서 제거해주자.
                    mlog_1.default.error('there-is-no-guild-member-in-redis', {
                        userId: user.userId,
                        guildId: user.userGuild.guildId,
                    });
                    return (0, puUserUpdateGuildId_1.default)(userDbPool, user.userId, 0).then(() => {
                        user.userGuild.guildId = 0;
                        guildData = undefined;
                    });
                }
                // 만약 혼자만 남았는데 길드장이 아닐경우 강제로 변경해준다.
                if (lodash_1.default.keys(guildData.members).length === 1) {
                    if (guildData.members[user.userId].grade !== lobby_1.GUILD_MEMBER_GRADE.MASTER) {
                        mlog_1.default.warn('change-gulid-master-by-force', {
                            userId: user.userId,
                            guildId: user.userGuild.guildId,
                            grade: guildData.members[user.userId].grade,
                        });
                        guildData.members[user.userId].grade = lobby_1.GUILD_MEMBER_GRADE.MASTER;
                        guildRedis['updateGuildMember'](user.userGuild.guildId, user.userId, JSON.stringify(guildData.members[user.userId])).catch((e) => {
                            mlog_1.default.error('[guild] updateGuildMember redis error', {
                                userId: this._userId,
                                error: e.message,
                                stack: mconf_1.default.isDev ? undefined : e.stack,
                            });
                        });
                    }
                }
                const userIds = [];
                lodash_1.default.forOwn(guildData.members, (elem) => {
                    userIds.push(elem.userId);
                });
                if (guildData.members[user.userId].grade == lobby_1.GUILD_MEMBER_GRADE.MASTER) {
                    lodash_1.default.forOwn(guildData.applicants, (elem) => {
                        userIds.push(elem.userId);
                    });
                }
                else {
                    const guildAccessPermissionCms = cms_1.default.GuildAccessPermission[guildData.members[user.userId].grade];
                    if (guildAccessPermissionCms && guildAccessPermissionCms.manageMember) {
                        lodash_1.default.forOwn(guildData.applicants, (elem) => {
                            userIds.push(elem.userId);
                        });
                    }
                }
                const worldConfg = mconf_1.default.getWorldConfig();
                return (0, userCacheRedisHelper_1.getUserLightInfos)(userIds, userCacheRedis, userRedis, guildRedis, townRedis, userDbConnPoolMgr, worldConfg.mysqlUserDb.shardFunction).then((result) => {
                    userLightInfos = result;
                    userLightInfos[user.userId].isOnline = 1;
                    user.userGuild.setGuildAppearance({
                        guildName: guildData.guild.guildName,
                        grade: guildData.members[user.userId].grade,
                        emblemImageCmsId: guildData.guild.emblemImageCmsId,
                        emblemColorCmsId: guildData.guild.emblemColorCmsId,
                        emblemBorderCmsId: guildData.guild.emblemBorderCmsId,
                    });
                    userGuildSync = {
                        add: {
                            userGuild: {
                                guild: {
                                    members: {
                                        [user.userId]: {
                                            isOnline: true,
                                        },
                                    },
                                },
                            },
                        },
                    };
                    (0, guildPubsub_1.onGuildPublish)(guildData, userLightInfos, [user.userId], userGuildSync);
                    guildUtil_1.GuildUtil.changeMasterIfLongTermLogout(user, user.userGuild.guildId, guildData, userLightInfos);
                    lodash_1.default.merge(userGuildSync, guildUtil_1.GuildUtil.buildGuildSyncDataAll(user, guildData, userLightInfos));
                });
            });
        }
    })
        .then(() => {
        // 친구들에게 로그인 알림 통보.
        const friendIds = user.userFriends.getFriendIdsByState(userFriends_1.FRIEND_STATE.ESTABLISHED);
        return friendUtil_1.FriendUtil.notifyOnlineFriends(user.userId, user.userName, friendIds, lobby_1.FRIEND_NOTIFICATION_TYPE.LOGIN, mutil.curTimeUtc());
    })
        .then(() => {
        return userRedis['loadQuestDailyLimit'](user.userId);
    })
        .then((ret) => {
        if (!ret) {
            // 기본값으로 간주
            return null;
        }
        const expireTimeUtc = parseInt(ret[0], 10);
        const completedCounts = JSON.parse(ret[1]);
        if (!(Number.isInteger(expireTimeUtc) && completedCounts && typeof completedCounts === 'object')) {
            mlog_1.default.error('unexpected quest daily limit data type!', {
                userId: user.userId,
                ret,
            });
            return null;
        }
        user.questManager.setDailyLimitCompleted({
            expireTimeUtc,
            counts: completedCounts,
        });
        return null;
    })
        .then(() => {
        return userEventRanking_1.EventRankingUtil.initToEventRanking(user, curTimeUtc);
    })
        .then(() => {
        return user.userTown.initTownInvestmentAccumPoints(user, curTimeUtc, townRedis);
    })
        .then(() => {
        return sailRedis['getOfflineBattleHistories'](user.userId);
    })
        .then((obHistories) => {
        // 오프라인항해 중 전투보상 내역을 클라이언트에게 전달.(단순 정보 전달용)
        if (obHistories) {
            obHistories.forEach((param) => {
                const obHistory = JSON.parse(param);
                if (obHistory.addedDucat && obHistory.addedDucat !== 0) {
                    offlineBattleHistories.addedDucat =
                        obHistory.addedDucat +
                            (offlineBattleHistories.addedDucat ? offlineBattleHistories.addedDucat : 0);
                }
                if (obHistory.addedUserExp && obHistory.addedUserExp !== 0) {
                    offlineBattleHistories.addedUserExp =
                        obHistory.addedUserExp +
                            (offlineBattleHistories.addedUserExp ? offlineBattleHistories.addedUserExp : 0);
                }
                if (obHistory.fameGain && obHistory.fameGain !== 0) {
                    offlineBattleHistories.fameGain =
                        obHistory.fameGain +
                            (offlineBattleHistories.fameGain ? offlineBattleHistories.fameGain : 0);
                }
                // 중복된 항해사는 exp를 병합.
                if (obHistory.mates && obHistory.mates.length > 0) {
                    obHistory.mates.forEach((mate) => {
                        if (!offlineBattleHistories.mates) {
                            offlineBattleHistories.mates = [];
                        }
                        const found = offlineBattleHistories.mates.findIndex((m) => m.mateCmsId === mate.mateCmsId);
                        if (found !== -1) {
                            offlineBattleHistories.mates[found].addedExp += mate.addedExp;
                        }
                        else {
                            offlineBattleHistories.mates.push({
                                mateCmsId: mate.mateCmsId,
                                addedExp: mate.addedExp,
                            });
                        }
                    });
                }
                // 중복된 보급품,교역품은  병합.
                if (obHistory.rewards && obHistory.rewards.length > 0) {
                    obHistory.rewards.forEach((reward) => {
                        if (!offlineBattleHistories.rewards) {
                            offlineBattleHistories.rewards = [];
                        }
                        const found = offlineBattleHistories.rewards.findIndex((r) => r.type === reward.type && r.cmsId === reward.cmsId);
                        if (found !== -1) {
                            offlineBattleHistories.rewards[found].quantity += reward.quantity;
                        }
                        else {
                            offlineBattleHistories.rewards.push({
                                type: reward.type,
                                cmsId: reward.cmsId,
                                quantity: reward.quantity,
                            });
                        }
                    });
                }
            });
        }
        let location;
        let sailState;
        if (sailId) {
            sailState = user.userSailing.getSailState();
            location = sailState.location;
        }
        // build sync.
        const sync = {
            add: lodash_1.default.merge({
                user: {
                    enterWorldToken: newEnterWorldToken,
                },
                server: {
                    bIsNonPK: mconf_1.default.bIsNonPK,
                },
            }, user.getLoginSyncData(), nationManager.getSyncDataOfSelectableNations(), nationIntimacy.getSyncData(), townManager.getSyncData()),
        };
        // build sync guild data
        if (userGuildSync) {
            lodash_1.default.merge(sync, userGuildSync);
        }
        const userNationSync = (0, userNation_1.buildSyncDataOfBaseElection)(user, curTimeUtc);
        if (userNationSync) {
            lodash_1.default.merge(sync, userNationSync);
        }
        // game log.
        if (bIsNewUser) {
            user.glog('common_register', {
                os: deviceType.toUpperCase(),
                osv,
                dm,
                lang: deviceLang,
                lang_game: lineLang,
                v,
                sk,
                platform: loginPlatform,
                country_ip,
            });
        }
        let region_id = null;
        const townCms = cms_1.default.Town[user.userTown.getLastTownCmsId()];
        if (gameState_1.GsUtil.isInTown(newLastGameState) && townCms) {
            region_id = townCms.RegionId;
        }
        else if (sailState) {
            region_id = sailState.region.id;
        }
        user.glog('common_login', {
            os: deviceType.toUpperCase(),
            osv,
            dm,
            lang: deviceLang,
            lang_game: lineLang,
            v,
            sk,
            platform: loginPlatform,
            country_ip,
            os_modulation,
            emulator,
            loading_time,
            region_id,
            town_id: user.userState.isInTown() ? user.userTown.getLastTownCmsId() : null,
            coordinates: (0, oceanCoordinate_1.getGLogCoordinate)(location),
        });
        user.glog('common_adid', {
            os: deviceType.toUpperCase(),
            sk,
            platform: loginPlatform,
            country_ip,
            adjust_id,
            gps_adid: gps_adid ? gps_adid : null,
            idfa: idfa ? idfa : null,
            idfv: idfv ? idfv : null,
        });
        user.glogLoginOutSave(const_2.GLOG_LOGIN_OUT_SAVE_TYPE.UserLogin, curTimeUtc, 0, rsn, add_rsn);
        if (mconf_1.default.isSDO) {
            const now = mutil.curTimeUtc();
            const firstFleetStat = user.companyStat.getFleetStat(ex_1.FirstFleetIndex); // 1함대
            const leaderMate = user.userMates.getLeaderMate(user.userFleets);
            sdoGLogs_1.SdoGLogEvents.uwo_character_login_glog({
                mid: user.accountId,
                character_id: user.pubId,
                character_name: user.userName,
                character_level: user.level,
                channel_id: user.channel,
                sub_channel_id: user.subChannel,
                platform: user.platform,
                device_id: user.udid,
                ip: user.countryIp,
                power: firstFleetStat.getCombatPower(),
                port: 0,
                guild_id: user.userGuild.guildId.toString(),
                map_id: mconf_1.default.worldId,
                representive_id: user.userMates.getRepresentedMateCmsId().toString(),
                supervisor_id: leaderMate ? leaderMate.getNub().cmsId.toString() : '',
                tmp_pin: location ? `${location.latitude},${location.longitude}` : '0,0',
                tmp_activity: user.userEnergy.getCurrentEnergy(now, user.level),
                tmp_diamond: user.userPoints.paidRedGem,
                tmp_free_diamond: user.userPoints.freeRedGem,
                tmp_fight: firstFleetStat.getCombatPower(),
                tmp_gold: user.userPoints.getPoint(cmsEx.BlueGemPointCmsId),
                tmp_silver: user.userPoints.getPoint(cmsEx.DucatPointCmsId),
                tmp_mile: user.userPoints.getMileage(now),
                tmp_exp: user.exp,
                total_pay: 0, // TODO u_users 테이블에 `accumPay` 정도의 필드를 추가해서 대응!
            });
        }
        if (mconf_1.default.isSDO) {
            survey_1.Survey.conditionalRequestSurvey(user);
        }
        // write isOnline to userCacheRedis
        userCacheRedis['setOnline'](userId, 1).catch((err) => {
            mlog_1.default.error('userCacheRedis setOnline is failed.', {
                userId,
                isOnline: 1,
                err: err.message,
            });
        });
        // 정산과 관련있을 수 있는 일,월요일을 제외한 날에 u_direct_mail_pendings 에서 유효하지 않은 메일을 삭제한다.
        if (new Date().getDay() > 1) {
            (0, puDirectMailPendingsDelete_1.default)(userDbPool, user.userId).catch((err) => {
                mlog_1.default.error('puDirectMailPendingsDelete is failed.', { err: err.message });
            });
        }
        // 로그인 할때 유저의 랭킹이 없을 수 있으므로 반영해준다.
        if (user.companyJobCmsId) {
            const { rankingManager } = typedi_1.Container.get(server_1.LobbyService);
            rankingManager.updateRanking(rankingDesc_1.RANKING_CMS_ID.COMPANY_LEVEL, user.userId, user.exp, user.userId, true);
            const combatPower = user.userFleets.getFirstFleetCombatPower(user.companyStat);
            rankingManager.updateRanking(rankingDesc_1.RANKING_CMS_ID.COMPANY_COMBAT_POWER, user.userId, combatPower, user.userId, true);
        }
        // build resp.
        // serverTimeUtc은 위에 선언한 currentTime으로 사용하면 로그인시 부하가 심할 때 오차가 클 수 있다.
        // 그렇기 때문에 mutil.curTimeUtc()로  새로 utc를 얻어와 적용해줘야한다.
        const resp = {
            pingInterval: mconf_1.default.ping.interval,
            serverTimeUtc: mutil.curTimeUtc(),
            serverTimeZoneOffsetMin: mutil.getTimezoneOffset(),
            reconnect,
            sailId,
            offlineBattleHistories,
            bIsNewUser,
            sync,
            clientVolanteUrl: mconf_1.default.clientVolanteUrl,
            appGuardId: user.getAppGuardId(),
        };
        //! 온라인 유저나 오프라인 유저에 공통으로 적용되어야 하는 버프들은 아래 함수에 추가
        return user.addAllBuffsIfNeeded(curTimeUtc, guildData, bIsReturnUser, resp).then(() => {
            return resp;
        });
    })
        .then((resp) => {
        return user.sendJsonPacket(packet.seqNum, packet.type, resp);
    })
        .then(() => {
        user.setLoggedIn();
    });
}
//# sourceMappingURL=enterWorld.js.map