// ----------------------------------------------------------------------------
// COPYRIGHT (C)2022 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import * as _ from 'lodash';
import Container from 'typedi';

import cms from '../cms';
import * as cmsEx from '../cms/ex';
import { getOceanNpcCms } from '../cms/ex';
import * as CMSConst from '../cms/const';
import mlog from '../motiflib/mlog';
import * as mutil from '../motiflib/mutil';
import * as formula from '../formula/index';

import UserBattle, {
  BattleEndMateBattleExpGain,
  BattleFreeTakebackChange,
  BattleInfo,
  BattleParam,
  BattleParamShip,
  BattleQuickModeChange,
} from './userBattle';
import {
  ACTION_COUNTER_TYPE,
  BattleLosses,
  BattleResult,
  BattleResultType,
  BattleReward,
  BattleRewardTable,
  isBattleWin,
} from './battleResult';
import { GameStateChange } from './userState';
import Mate, { MateExpChange } from './mate';
import { KarmaChange, User, UserExpLevelChange } from './user';
import { ItemChange } from './userInven';
import { InsuranceUnpaidChange, PointChange } from './userPoints';
import { QuestContextChange } from './quest';
import { ShipCargoChange, ShipChange } from './ship';
import { UserReputationChange } from './userReputation';
import { Challenge, Sync } from './type/sync';
import { EnergyChange } from './userEnergy';
import { MError, MErrorCode } from '../motiflib/merror';
import { BattleAudit } from './battleAudit';
import { LobbyService } from './server';
import UserFleets from './userFleets';
import { BC } from '../motiflib/battleCommon';
import { QuestUtil } from './questUtil';
import { GAME_ENTER_STATE, GAME_STATE } from '../motiflib/model/lobby/gameState';
import { EncountTargetType } from '../motiflib/model/ocean/enum';
import { REWARD_TYPE } from '../cms/rewardDesc';
import { AccumulateParam } from './userAchievement';
import { OCEAN_NPC_TYPE } from '../cms/oceanNpcDesc';
import { getGLogCoordinate, GLogCoordinate } from '../cms/oceanCoordinate';
import { PrData, RewardData } from '../motiflib/gameLog';
import { RewardAndPaymentSpec, RNP_TYPE } from './UserChangeTask/rewardAndPaymentChangeSpec';
import { Changes, CHANGE_TASK_RESULT, TryData } from './UserChangeTask/userChangeTask';
import {
  opAddMateInjuryState,
  opApplyEnergyChange,
  opApplyFreeTakebackChange,
  opApplyGameStateChange,
  opApplyInsuranceUnpaidChange,
  opApplyItemChange,
  opApplyMateExpChange,
  opApplyPointChange,
  opApplyQuickModeChange,
  opApplyShipCargoChange,
  opBattleLogEnd,
  opDropQuest,
  opRecordNationEventOccur,
  opSetChallenge,
  opSetFame,
  opSetGameOverLosses,
  opSetQuestFlags,
  opSetReputation,
  opSetShipDurabilityAndSailor,
  opSetShipLife,
  opSetUserExpLevel,
  opSetBattleEndResult,
  opSetBattleRewards,
  opApplyKarmaChange,
} from './UserChangeTask/userChangeOperator';
import { RaidBattleResult, BattleType } from '../motiflib/model/lobby';
import { Shield } from './userShield';
import { BattleEndClashInfo, ClashSession } from './clash';
import { Reentry } from './userReentry';
import { RewardFixedDesc } from '../cms/rewardFixedDesc';
import { ClashUtil } from './clashUtil';

// ----------------------------------------------------------------------------
// 보급품 타입
// ----------------------------------------------------------------------------
export enum CARGO_SUPPLY_TYPE {
  WATER = 0,
  FOOD = 1,
  LUMBER = 2,
  AMMO = 3,

  MAX_TYPE = 4,
}

// ----------------------------------------------------------------------------
// 모의전/도전용 보급품 정보
// ----------------------------------------------------------------------------
export interface CARGO_SUPPLIES {
  [cargoType: number]: number;
}

// ----------------------------------------------------------------------------
// 전투의 종료 처리 과정에서 필요한 데이터 구조 정의
// ----------------------------------------------------------------------------
interface Bey_NationIntimacyChange {
  userNationCmsId: number;
  enemyNationCmsId: number;
  nationDiplomacyCmsId: number;
}

interface Bey_ArenaLogInfo {
  myGrade: number;
  myScore: number;
  myChangedScore: number;
  enemyGrade: number;
  enemyScore: number;
  enemyChangedScore: number;
}

interface Bey_AreanInfo {
  bValidArena?: boolean; // 전투의 유효성
  logInfo?: Bey_ArenaLogInfo;
}

// 경험치 누적용 오브젝트.
interface ExpAccum {
  userExp: number;
  mateExps: {
    [mateCmsId: number]: {
      mate: Mate;
      expAmount: number;
    };
  };
}

// ----------------------------------------------------------------------------
// 운영 로그 관련 기능
// ----------------------------------------------------------------------------
namespace _Glog {
  export const LogColPve = 'battle_pve_finish';
  export const LogColPvp = 'battle_pvp_finish';
  export const LogColChallenge = 'battle_challenge_finish';
  export const LogColArena = 'battle_arena_finish';
  export const LogColRaid = 'boss_raid_finish';
  export const LogColGuildRaid = 'guild_boss_raid_finish';
  export const LogColFriendly = 'battle_friendly_finish';
  export const LogColInfiniteLighthouse = 'lighthouse_finish';
  export const LogColClash = 'battle_clash_end';
}

// ----------------------------------------------------------------------------
// 전투의 종료과정 처리시, 매우 크고 다양한 데이터가 필요하기 때문에,
// 자체 인터페이스로 정의함.
// ----------------------------------------------------------------------------
export interface BattleEndYardData {
  curTimeUtc: number;
  battleId: string;
  battleParam: BattleParam;
  logCount: number;
  battleResult: BattleResult;
  defeatedOceanNpcs: number[];
  gameStateChange?: GameStateChange; // 필수 필드이기는 하지만, 검증 이후 세팅됨.
  freeTakebackChange?: BattleFreeTakebackChange;
  quickModeChange?: BattleQuickModeChange;
  mateExpChanges: MateExpChange[];
  mateExpGains: BattleEndMateBattleExpGain[];
  userExpLevelChange?: UserExpLevelChange;
  userExpGain: number;
  leaderMateCmsId?: number;
  battleFame?: number;
  fameGain?: number;
  totalGainBattleFame?: number; // 획득 명성 ( 최대치 보정하지 않은, 업적 용도 )
  consumedItemChanges: ItemChange[];
  rewardTable: BattleRewardTable;
  ducatGains: number;
  ducatChange?: PointChange; //전투시 중첩 소모로 인해 변경된 유저의 듀캇.
  blueGemChange?: PointChange; // 전투시 중첩 소모로 인해 변경된 유저의 블루잼.

  questContextChanges?: QuestContextChange[];

  battleLosses?: BattleLosses; // 일반전투 only
  shipChanges?: ShipChange[]; // 일반전투 only (내구도/선원수 변경)
  shipCargoChanges?: ShipCargoChange[]; // 일반전투 only
  shipLifeChanges?: ShipChange[]; // 일반전투 only (수명 변경)
  insuranceUnpaidChange?: InsuranceUnpaidChange; // 일반전투 only
  nationIntimacyChange?: Bey_NationIntimacyChange; // 일반전투 only
  reputationChanges?: UserReputationChange[]; // 일반전투 only

  challengeUpdate?: Challenge; // 도전전투 only
  challengeQuestCmsId?: number; // 도전전투 only
  challenegeLossEnergyChange?: EnergyChange; // 도전 전투 only

  questCmsId?: number; // 전투에만 한정적으로 사용되는 퀘스트

  arenaInfo?: Bey_AreanInfo; // 모의전 only

  raidDmg?: number; // Raid only

  karmaChange?: KarmaChange;

  shieldChange?: Shield; // 소탕 only
  sweepTicketChange?: { count: number; buyCount: number }; // 소탕 only

  clash?: BattleEndClashInfo;
  reentry?: Reentry;
}

// ----------------------------------------------------------------------------
// 전투 종료 관련 공용 처리 로직 클래스
// ----------------------------------------------------------------------------
export class BattleEndYard {
  protected _user: User;
  protected _yd: BattleEndYardData;

  // --------------------------------------------------------------------------
  constructor(user: User, yd: BattleEndYardData) {
    this._user = user;
    this._yd = yd;
  }

  // --------------------------------------------------------------------------
  getYardData(): BattleEndYardData {
    return this._yd;
  }

  // --------------------------------------------------------------------------
  // 최소 검증
  // --------------------------------------------------------------------------
  minimalValidate(overrideBattleParam?: BattleParam): void {
    const battleParam = overrideBattleParam ? overrideBattleParam : this._yd.battleParam;
    if (battleParam.bTest) {
      return;
    }

    // 전투 유효성 체크.
    if (!this._yd.battleId || !battleParam) {
      mlog.warn('Cannot end battle with no battle info.', { userId: this._user.userId });
      throw new MError('invalid-battle-id', MErrorCode.INVALID_BATTLE_ID);
    }

    if (!this._yd.battleResult) {
      mlog.warn('Cannot end battle without no result.', { user: this._user.userId });
      throw new MError('battle-result-missing', MErrorCode.INVALID_REQUEST);
    }

    BattleAudit.validateResult(this._user, this._yd, overrideBattleParam);
  }

  // --------------------------------------------------------------------------
  // 무료 무르기가 활성화 된 전투에서, 무르기를 한 경우, 무료 무르기 업데이트가 필요하다.
  // 단, 전투 시작 후, 일일 무료 무르기 초기화 시간 전에 끝났을 경우에만 한다.
  // --------------------------------------------------------------------------
  determineFreeTakebackChange(): void {
    // 전투에서 무르기를 사용하지 않은경우 리턴.
    if (!this._yd.battleResult.takebackInfo) {
      return;
    }

    // 이번 전투에서 사용한 무료무르기 횟수를 가져온다.
    let usingFreeTurnTakebackCount: number = 0;
    let usingFreePhaseTakebackCount: number = 0;
    if (!this._yd.battleParam.freeTakebackData.unlimitedByCash.bTurn) {
      if (this._yd.battleResult.takebackInfo.turnCountByFree !== undefined) {
        usingFreeTurnTakebackCount += this._yd.battleResult.takebackInfo.turnCountByFree;
      }
    } else {
      // 무제한 무르기 횟수 제한 검사
      let turnCountByUnlimited = this._yd.battleResult.takebackInfo.turnCountByUnlimited;
      if (turnCountByUnlimited !== undefined) {
        const constTurnTakebackCountByUnlimitCash: number = CMSConst.get(
          'BattleTurnTakebackCountByUnlimitCash'
        );

        if (turnCountByUnlimited > constTurnTakebackCountByUnlimitCash) {
          throw new MError(
            'over unlimit turn takeback count.',
            MErrorCode.OVER_UNLIMIT_TURN_TAKE_BACK_COUNT,
            {
              turnCountByUnlimited,
              constTurnTakebackCountByUnlimitCash,
            }
          );
        }
      }
    }

    if (!this._yd.battleParam.freeTakebackData.unlimitedByCash.bPhase) {
      if (this._yd.battleResult.takebackInfo.phaseCountByFree !== undefined) {
        usingFreePhaseTakebackCount += this._yd.battleResult.takebackInfo.phaseCountByFree;
      }
    } else {
      // 무제한 무르기 횟수 제한 검사
      let phaseCountByUnlimited = this._yd.battleResult.takebackInfo.phaseCountByUnlimited;
      if (phaseCountByUnlimited !== undefined) {
        const constPhaseTakebackCountByUnlimitCash: number = CMSConst.get(
          'BattlePhaseTakebackCountByUnlimitCash'
        );

        if (phaseCountByUnlimited > constPhaseTakebackCountByUnlimitCash) {
          throw new MError(
            'over unlimit phase takeback count.',
            MErrorCode.OVER_UNLIMIT_PHASE_TAKE_BACK_COUNT,
            {
              phaseCountByUnlimited,
              constPhaseTakebackCountByUnlimitCash,
            }
          );
        }
      }
    }

    // 리셋이 된 경우 먼저 체크
    if (
      formula.HasContentsResetTimePassed(
        this._yd.curTimeUtc,
        this._user.userBattle.lastFreeTakebackUpdateTimeUtc,
        cms.ContentsResetHour.BattleTakebackCostReset.hour
      )
    ) {
      // 전투 시작 시간에는 리셋이 되지 않았지만,
      // 전투 종료 시간에만 리셋이 된 경우에는 사용횟수를 초기화 한다.
      const startBattleTimeUtc: number = this._yd.battleParam.randomSeed;
      if (
        !formula.HasContentsResetTimePassed(
          startBattleTimeUtc,
          this._user.userBattle.lastFreeTakebackUpdateTimeUtc,
          cms.ContentsResetHour.BattleTakebackCostReset.hour
        )
      ) {
        this._yd.freeTakebackChange = {
          usedFreeTurnTakebackCount: 0,
          usedFreePhaseTakebackCount: 0,
          updateTimeUtc: this._yd.curTimeUtc,
        };
      } else {
        // 전투 시작 시간에 리셋이 되는 경우에는
        // 메모리 & 디비에 업데이트 되지 않고, 파라미터 값에만 초기화된 값을 사용한다.
        // 그래서 현 전투 때에 발생된 횟 수만 업데이트 한다.
        this._yd.freeTakebackChange = {
          usedFreeTurnTakebackCount: usingFreeTurnTakebackCount,
          usedFreePhaseTakebackCount: usingFreePhaseTakebackCount,
          updateTimeUtc: this._yd.curTimeUtc,
        };
      }

      return;
    }

    // 사용할 수 있는 횟수보다 더 많이 사용한 경우
    if (this._yd.battleResult.takebackInfo.turnCountByFree !== undefined) {
      const maxFreeTurnTakebackCountInBattle: number =
        this._yd.battleParam.freeTakebackData.freeTakebackCountInfo.turn;

      if (maxFreeTurnTakebackCountInBattle < usingFreeTurnTakebackCount) {
        throw new MError(
          'over free turn takeback count.',
          MErrorCode.OVER_FREE_TURN_TAKE_BACK_COUNT,
          {
            maxFreeTurnTakebackCountInBattle,
            usingFreeTurnTakebackCount,
          }
        );
      }
    }

    if (this._yd.battleResult.takebackInfo.phaseCountByFree !== undefined) {
      const maxFreePhaseTakebackCountInBattle: number =
        this._yd.battleParam.freeTakebackData.freeTakebackCountInfo.phase;

      if (maxFreePhaseTakebackCountInBattle < usingFreePhaseTakebackCount) {
        throw new MError(
          'over free turn takeback count.',
          MErrorCode.OVER_FREE_PHASE_TAKE_BACK_COUNT,
          {
            maxFreePhaseTakebackCountInBattle,
            usingFreePhaseTakebackCount,
          }
        );
      }
    }

    // 사용횟수 업데이트
    if (usingFreeTurnTakebackCount > 0 || usingFreePhaseTakebackCount > 0) {
      let accUsedFreeTurnTakebackCount: number =
        this._user.userBattle.usedFreeTurnTakebackCount + usingFreeTurnTakebackCount;
      let accUsedFreePhaseTakebackCount: number =
        this._user.userBattle.usedFreePhaseTakebackCount + usingFreePhaseTakebackCount;

      this._yd.freeTakebackChange = {
        usedFreeTurnTakebackCount: accUsedFreeTurnTakebackCount,
        usedFreePhaseTakebackCount: accUsedFreePhaseTakebackCount,
        updateTimeUtc: this._yd.curTimeUtc,
      };
    }
  }

  // --------------------------------------------------------------------------
  // 항해사가 획득하는 경험치 계산.
  // --------------------------------------------------------------------------
  private static _buildMateExpChanges(
    user: User,
    defeatedOceanNpcs: number[],
    battleResult: BattleResult,
    battleParam: BattleParam,
    mateExpChanges: MateExpChange[],
    expGains: BattleEndMateBattleExpGain[]
  ): number {
    mlog.debug('[buildMateExpChanges]', {
      battleResult,
      defeatedOceanNpcs,
    });

    if (battleResult.type !== BattleResultType.AllyWin) {
      // If user lost the battle, no exp gain.
      return;
    }

    if (defeatedOceanNpcs.length === 0) {
      // If not pve, no exp gain.
      return;
    }

    let expRanking: number[] = battleResult.expRanking;
    if (
      !expRanking ||
      expRanking.length === 0 ||
      expRanking.length > cms.Define.MaxFleetShipCount
    ) {
      throw new MError('Invalid exp ranking.', MErrorCode.INVALID_BATTLE_RESULT, {
        battleResult,
      });
    }

    // 국가 순위 보너스
    let nationRankBonus: number;
    const { nationManager } = Container.get(LobbyService);
    const nationRank = nationManager.getPowerRank(user.nationCmsId);
    if (nationRank) {
      const nationRankingEffectCms =
        cms.NationRankingEffect[cmsEx.NATION_RANKING_EFFECT_CMS_ID.EXP];
      nationRankBonus = nationRankingEffectCms.rankingEffectVal[nationRank - 1];
    }

    // 여러 함대의 경험치 누적을 위한 임시 오브젝트.
    const expAccum: ExpAccum = {
      userExp: 0,
      mateExps: {},
    };

    // 물리친 적 함대들을 돌면서 획득 경험치를 취합한다.
    const firstFleet = user.userFleets.getFirstFleet();
    for (const oceanNpcCmsId of defeatedOceanNpcs) {
      const oceanNpcCms = getOceanNpcCms()[oceanNpcCmsId];
      const baseExp = oceanNpcCms.exp;

      let rankNum = 0;

      const levelDiff =
        user.level -
        (battleParam.enemy.level !== undefined ? battleParam.enemy.level : oceanNpcCms.rank);

      for (const shipFormIdx of expRanking) {
        const ship = firstFleet.getShipByFormationIndex(shipFormIdx);
        if (!ship) {
          mlog.warn('No ship at formation:', { userId: user.userId, shipFormIdx });
          continue;
        }

        const shipSlots = ship.getSlots();
        _.forOwn(shipSlots, (shipSlot) => {
          if (!shipSlot.mateCmsId) {
            return;
          }

          // Determine the slot's job type penalty.
          const jobPenalty = UserFleets.getShipSlotExpJobPanelty(
            ship,
            shipSlot.slotIndex,
            cmsEx.JOB_TYPE.BATTLE,
            user.userShipBlueprints
          );

          // Calc exp amount to be gained.
          const mate = user.userMates.getMate(shipSlot.mateCmsId);
          if (!mate) {
            return;
          }

          const expAmount = formula.CalcBattleExpAmount(
            baseExp,
            rankNum,
            levelDiff,
            jobPenalty,
            nationRankBonus
          );

          // mlog.warn('[dbg] battle-end exp ', {
          //   baseExp,
          //   rankNum,
          //   levelDiff,
          //   jobPenalty,
          //   nationRankBonus,
          //   expAmount,
          // });

          // leader mate's exp is user exp.
          if (
            shipFormIdx === cmsEx.FlagShipFormationIndex &&
            shipSlot.slotIndex === cmsEx.ShipSlotIndexCaptainRoom
          ) {
            expAccum.userExp += expAmount;
          }

          // 획득 경험치 누적.
          if (!expAccum.mateExps[shipSlot.mateCmsId]) {
            expAccum.mateExps[shipSlot.mateCmsId] = {
              mate,
              expAmount,
            };
          } else {
            expAccum.mateExps[shipSlot.mateCmsId].expAmount += expAmount;
          }
        });

        ++rankNum;
      }
    }

    // 누적된 경험치를 반영.
    for (const [_, mateExpInfo] of Object.entries(expAccum.mateExps)) {
      const mate = mateExpInfo.mate;
      const newLevelAndExp = mate.calcExpLevel(
        cmsEx.JOB_TYPE.BATTLE,
        mateExpInfo.expAmount,
        user.companyStat,
        user.level,
        firstFleet.fleetIndex
      );

      if (newLevelAndExp) {
        mateExpChanges.push(newLevelAndExp);

        // Store the gain amount.
        expGains.push({
          mateCmsId: mate.getNub().cmsId,
          amount: newLevelAndExp.exp - mate.getExp(cmsEx.JOB_TYPE.BATTLE),
        });
      }
    }

    return expAccum.userExp;
  }

  // --------------------------------------------------------------------------
  // 선단/항해사 경험치 획득량 계산.
  // --------------------------------------------------------------------------
  determineMateAndUserExp(energyToConsume: number = 0): void {
    if (
      this._yd.battleParam.bTest ||
      this._yd.battleResult.type !== BattleResultType.AllyWin ||
      this._yd.battleParam.arena
    ) {
      return;
    }

    const gainedUserExp = BattleEndYard._buildMateExpChanges(
      this._user,
      this._yd.defeatedOceanNpcs,
      this._yd.battleResult,
      this._yd.battleParam,
      this._yd.mateExpChanges,
      this._yd.mateExpGains
    );

    this._yd.userExpLevelChange = this._user.calcExpLevel(
      gainedUserExp,
      this._yd.curTimeUtc,
      energyToConsume
    );
    if (this._yd.userExpLevelChange) {
      this._yd.userExpGain = this._yd.userExpLevelChange.exp - this._user.exp;
    } else {
      if (energyToConsume > 0) {
        this._yd.userExpLevelChange = {
          exp: undefined,
          oldLevel: undefined,
          level: undefined,
          energyChange: this._user.userEnergy.buildEnergyChangeWithConsume(
            mutil.curTimeUtc(),
            this._user.level,
            this._user.level,
            energyToConsume,
            true
          ),
        };
      }
    }

    // mlog.verbose('[TEMP] BattleExpGains:', {
    //   userId: user.userId,
    //   mateExpGains: yard.mateExpGains,
    //   gainedUserExp,
    //   mateExpChanges: yard.mateExpChanges,
    // });
  }

  // --------------------------------------------------------------------------
  // 획득 명성 계산.
  // --------------------------------------------------------------------------
  determineLeaderMateFame(): void {
    if (this._yd.battleParam.bTest || this._yd.battleParam.arena) {
      return;
    }

    const oceanNpcId = this._yd.battleParam.enemy.oceanNpcId;
    if (
      !this._yd.battleParam.bTest &&
      oceanNpcId &&
      this._yd.battleResult.type === BattleResultType.AllyWin
    ) {
      const oceanNpcCms = getOceanNpcCms()[oceanNpcId];
      const leader = this._user.userMates.getLeaderMate(this._user.userFleets);
      const gainFame = Mate.calcGainFame(
        leader.getStat(this._user.companyStat),
        cmsEx.JOB_TYPE.BATTLE,
        oceanNpcCms.fame ?? 0,
        this._user.nationCmsId,
        this._user.companyStat.getFleetStat(cmsEx.FirstFleetIndex)
      );
      this._yd.totalGainBattleFame = gainFame;
      this._yd.battleFame = leader.calcFame(cmsEx.JOB_TYPE.BATTLE, gainFame);
      if (this._yd.battleFame !== undefined) {
        this._yd.leaderMateCmsId = leader.getNub().cmsId;
        this._yd.fameGain = this._yd.battleFame - leader.getFame(cmsEx.JOB_TYPE.BATTLE);
      }
    }
  }

  // --------------------------------------------------------------------------
  // 아이템 소모될것 누적.
  // --------------------------------------------------------------------------
  determineItemChanges(): void {
    if (this._yd.battleParam.bTest || this._yd.battleParam.arena) {
      return;
    }

    const itemInven = this._user.userInven.itemInven;
    _.forOwn(this._yd.battleResult.itemsUsed, (usedCount, itemCmsIdStr) => {
      const cmsId = parseInt(itemCmsIdStr, 10);
      this._yd.consumedItemChanges.push(itemInven.buildItemChange(cmsId, -usedCount, true));
    });
  }

  // --------------------------------------------------------------------------
  // 퀘스트 플래그 변경.
  // --------------------------------------------------------------------------
  determineQuestContextChanges(): void {
    this._yd.questContextChanges = QuestUtil.buildQuestContextChanges(
      this._user,
      this._yd.battleResult
    );
  }

  // --------------------------------------------------------------------------
  // 새로운 상태 결정
  // --------------------------------------------------------------------------
  determineNewGameState(): void {
    const bWin = isBattleWin(this._yd.battleResult.type);
    if (this._user.userState.isInTownBattle()) {
      if (bWin) {
        this._yd.gameStateChange = this._user.userState.buildGameStateChange(
          GAME_STATE.IN_TOWN_BATTLE_REWARD,
          GAME_ENTER_STATE.ENTERING
        );
      } else {
        this._yd.gameStateChange = this._user.userState.buildGameStateChange(GAME_STATE.IN_TOWN);
      }
    } else {
      if (bWin) {
        this._yd.gameStateChange = this._user.userState.buildGameStateChange(
          GAME_STATE.IN_OCEAN_BATTLE_REWARD,
          GAME_ENTER_STATE.ENTERING
        );
      } else {
        // if (yard.battleParam.bTest) {
        //   newGameState = GAME_STATE.IN_OCEAN_TEST_BATTLE_WRECKED;
        // } else {
        //   newGameState = GAME_STATE.IN_OCEAN_BATTLE_WRECKED;
        // }

        // TODO jaykay IN_OCEAN 으로 해도 될지 고민 필요. 실제로는 전투에 남아 있는 상황이기 때문

        this._yd.gameStateChange = this._user.userState.buildGameStateChange(GAME_STATE.IN_OCEAN);
      }
    }
  }

  // --------------------------------------------------------------------------
  // 보상중에 두캇은 ducatChange 에 합병.
  // --------------------------------------------------------------------------
  mergeRewardDucatToDucatChange(): void {
    if (
      this._yd.rewardTable[REWARD_TYPE.POINT] &&
      this._yd.rewardTable[REWARD_TYPE.POINT][cmsEx.DucatPointCmsId]
    ) {
      this._yd.ducatGains = this._yd.rewardTable[REWARD_TYPE.POINT][cmsEx.DucatPointCmsId].Quantity;
      delete this._yd.rewardTable[REWARD_TYPE.POINT][cmsEx.DucatPointCmsId];
      if (Object.keys(this._yd.rewardTable[REWARD_TYPE.POINT]).length === 0) {
        delete this._yd.rewardTable[REWARD_TYPE.POINT];
      }

      // TODO: https://wiki.line.games/pages/viewpage.action?pageId=22088969
      this._yd.ducatChange.value += this._yd.ducatGains;
    }
  }

  // --------------------------------------------------------------------------
  // 쾌속모드 비용 처리.
  // --------------------------------------------------------------------------
  determineQuickModeCost(): void {
    // 블루젬 쾌속모드 비용.
    const quickModeBlueGemCost = this._yd.battleResult.quickModeInfo
      ? this._yd.battleResult.quickModeInfo.quickModeBlueGemCost
      : undefined;
    if (quickModeBlueGemCost) {
      this._yd.blueGemChange.value -= quickModeBlueGemCost;
    }
  }

  // ----------------------------------------------------------------------------
  // 국가간 우호도 변경.
  // ----------------------------------------------------------------------------
  determineNationIntimacyChange(bAttackAndWin: boolean, enemyNationCmsId: number): void {
    // 우호도 및 평판 변화
    // 기획: https://wiki.line.games/pages/viewpage.action?pageId=6946999
    if (!bAttackAndWin) {
      return;
    }

    // const enemyNationCmsId = this._yd.battleParam.enemy.nationCmsId;
    const enemyNationCms = cms.Nation[enemyNationCmsId];
    let nationDiplomacyCmsIdToRecordIntimacyEvent;

    if (this._user.nationCmsId && enemyNationCms && enemyNationCms.canSelect) {
      let nationDiplomacyCmsId;
      if (this._user.nationCmsId === enemyNationCmsId) {
        nationDiplomacyCmsId = cmsEx.NATION_DIPLOMACY_CMS_ID.WIN_IN_BATTLE_WITH_MY_NATION_FLEET;
      } else {
        nationDiplomacyCmsId = cmsEx.NATION_DIPLOMACY_CMS_ID.WIN_IN_BATTLE_WITH_OTHER_NATION_FLEET;
      }

      const oceanNpcDesc = getOceanNpcCms()[this._yd.battleParam.enemy.oceanNpcId];
      if (oceanNpcDesc && oceanNpcDesc.OceanNpcType !== OCEAN_NPC_TYPE.QUEST) {
        this._yd.reputationChanges = [];
        nationDiplomacyCmsIdToRecordIntimacyEvent =
          this._user.userReputation.buildReputationIntimacyChanges(
            enemyNationCmsId,
            this._user.nationCmsId,
            nationDiplomacyCmsId,
            this._yd.reputationChanges,
            this._yd.curTimeUtc
          );

        this._yd.nationIntimacyChange = {
          userNationCmsId: this._user.nationCmsId,
          enemyNationCmsId,
          nationDiplomacyCmsId: nationDiplomacyCmsIdToRecordIntimacyEvent,
        };
      }
    }
  }

  // ----------------------------------------------------------------------------
  // 보험 처리.
  // ----------------------------------------------------------------------------
  determineInsurance(): void {
    // 보험 처리
    this._yd.insuranceUnpaidChange = _.cloneDeep(this._user.userPoints.getInsurance());
    const insuranceCms = cms.BankInsurance[this._user.userPoints.getInsuranceCmsId()];
    let curTotalInsurance =
      this._yd.insuranceUnpaidChange.unpaidDucat +
      this._yd.insuranceUnpaidChange.unpaidSailor +
      this._yd.insuranceUnpaidChange.unpaidShip +
      this._yd.insuranceUnpaidChange.unpaidTradeGoods;
    const maxInsurance = insuranceCms.maxIndemnity;

    // 선원 손실 보험 지급
    if (this._yd.battleLosses.sailor) {
      if (insuranceCms) {
        const insuranceToAdd = Math.max(
          0,
          Math.min(
            Math.ceil(
              (cms.Const.DefaultSailorGold.value *
                this._yd.battleLosses.sailor *
                insuranceCms.insuranceCoverage) /
                100
            ),
            maxInsurance - curTotalInsurance
          )
        );

        this._yd.insuranceUnpaidChange.unpaidSailor += insuranceToAdd;
        curTotalInsurance += insuranceToAdd;
      }

      delete this._yd.battleLosses.sailor;
    }

    // 침몰된 배 보험 지급
    if (
      this._yd.battleLosses.sunkShipIds &&
      this._yd.battleLosses.sunkShipIds.length > 0 &&
      insuranceCms
    ) {
      for (const shipId of this._yd.battleLosses.sunkShipIds) {
        const userShip = this._user.userFleets.getShip(shipId);
        const shipCms = cms.Ship[userShip.getNub().cmsId];
        const durability = userShip.getMaxDurability(this._user.companyStat);
        const cost = formula.CalcShipyardRepairingPrice(
          cms.Const.ShipRepairGold.value,
          durability,
          cms.Const['ShipRepairSizePer' + shipCms.shipSize].value
        );

        const insuranceToAdd = Math.max(
          0,
          Math.min(
            Math.ceil((cost * insuranceCms.insuranceCoverage) / 100),
            maxInsurance - curTotalInsurance
          )
        );

        this._yd.insuranceUnpaidChange.unpaidShip += insuranceToAdd;
        curTotalInsurance += insuranceToAdd;
      }
    }

    const curUnpaid = this._user.userPoints.getInsurance();
    if (
      this._yd.insuranceUnpaidChange.unpaidDucat === curUnpaid.unpaidDucat &&
      this._yd.insuranceUnpaidChange.unpaidSailor === curUnpaid.unpaidSailor &&
      this._yd.insuranceUnpaidChange.unpaidTradeGoods === curUnpaid.unpaidTradeGoods &&
      this._yd.insuranceUnpaidChange.unpaidShip === curUnpaid.unpaidShip
    ) {
      this._yd.insuranceUnpaidChange = null;
    }
  }

  // --------------------------------------------------------------------------
  // 무르기 비용 처리.
  // --------------------------------------------------------------------------
  determineTakebackCost(): void {
    // 블루젬 무르기 비용.
    const takebackBlueGemCost = this._yd.battleResult.takebackInfo
      ? this._yd.battleResult.takebackInfo.turnCost
      : undefined;
    if (takebackBlueGemCost) {
      this._yd.blueGemChange.value -= takebackBlueGemCost;
    }

    // 두캇 무르기 비용.
    const takebackDucatCost = this._yd.battleResult.takebackInfo
      ? this._yd.battleResult.takebackInfo.phaseCost
      : undefined;
    if (takebackDucatCost) {
      this._yd.ducatChange.value -= takebackDucatCost;
    }
  }

  // ----------------------------------------------------------------------------
  // 국가간 우호도 변경.
  // ----------------------------------------------------------------------------
  determineKarmaChange(user: User): void {
    // 해적 및 강적 제거 시 카르마 감소. (단 강적 중 토벌은 제외)
    // 기획: https://wiki.line.games/pages/viewpage.action?pageId=124187679

    //  감소 할 카르마.
    //  해적이면 -2 강적이면 -5. 해적이자 강적인 경우도 있는데 이럴 때에는 -5로 기획에서 확인.
    let subtractionKarma: number = 0;
    if (this._yd.battleResult.type == BattleResultType.AllyWin) {
      const oceanNpcCms = getOceanNpcCms()[this._yd.battleParam.enemy.oceanNpcId];
      if (oceanNpcCms) {
        if (oceanNpcCms.OceanNpcType === OCEAN_NPC_TYPE.ELITE) {
          if (this._yd.battleParam.battleType === BattleType.Encount) {
            subtractionKarma = cms.Const.KillEliteNPCPirateGetKarma.value;
          }
        } else {
          if (this._yd.battleParam.enemy.nationCmsId === cmsEx.NATION_CMS_ID.PIRATE) {
            subtractionKarma = cms.Const.KillNPCPirateGetKarma.value;
          }
        }
      }
    }

    if (!subtractionKarma) {
      return;
    }

    // https://jira.line.games/browse/UWO-25177
    // 기획에서 소탕으로 명시했지만 sweep_local_npc(소탕) 뿐만 아니라 해적 잡았을 때 해당함.
    const firstFleetStat = user.companyStat.getFleetStat(cmsEx.FirstFleetIndex);
    subtractionKarma -= firstFleetStat.getWpe(cmsEx.PASSIVE_EFFECT.KARMA_DECREASE);

    this._yd.karmaChange = this._user.getKarmaChangeWithAdd(mutil.curTimeUtc(), subtractionKarma);
  }

  // --------------------------------------------------------------------------
  // 전투에 한정된 퀘스트를 제거할 때
  // --------------------------------------------------------------------------
  determineQuestCmsId() {
    this._yd.questCmsId = this._yd.battleParam.questCmsId;
  }

  // --------------------------------------------------------------------------
  // 쾌속모드 활성화 된 전투에서, 쾌속모드를 한 경우, 쾌속모드 카운트 업데이트가 필요하다.
  // 단, 전투 시작 후, 일일 쾌속모드 초기화 시간 전에 끝났을 경우에만 한다.
  // --------------------------------------------------------------------------
  determineQuickModeChange(): void {
    if (
      // 전투 도중 리셋시간이 지났으면 쾌속의 사용 유무를 떠나 리셋.
      formula.HasContentsResetTimePassed(
        this._yd.curTimeUtc,
        this._yd.battleParam.randomSeed, // 전투 시작시간 UTC
        cms.ContentsResetHour.BattleQuickModeReset.hour
      )
    ) {
      this._yd.quickModeChange = {
        quickModeCount: 0,
        updateTimeUtc: this._yd.curTimeUtc,
      };
      return;
    }

    let bPrevBattleQuickModeReset = formula.HasContentsResetTimePassed(
      this._yd.battleParam.randomSeed,
      this._user.userBattle.lastQuickModeCountUpdateTimeUtc,
      cms.ContentsResetHour.BattleQuickModeReset.hour
    );

    let prevQuickModeCount = this._user.userBattle.quickModeCount;
    // 최근 전투 종료 시간과 현재 전투 시작시 사이에 리셋 시간이 지났는지 체크.
    if (bPrevBattleQuickModeReset) {
      prevQuickModeCount = 0;
      if (!this._yd.battleResult.quickModeInfo || !this._yd.battleResult.quickModeInfo.bQuickMode) {
        // 전투에서 퀵모드를 사용하지 않은데 리셋이 필요한경우에 DB 업데이트.
        this._yd.quickModeChange = {
          quickModeCount: prevQuickModeCount,
          updateTimeUtc: this._yd.curTimeUtc,
        };
        return;
      }
    }

    // 전투에서 퀵모드를 사용하지 않은경우 리턴.
    if (!this._yd.battleResult.quickModeInfo || !this._yd.battleResult.quickModeInfo.bQuickMode) {
      return;
    }

    // 배틀파람 시작시 만들지 않은경우 DB 업데이트 않음.
    if (!this._yd.battleParam.quickModeData) {
      return;
    }

    // 전투 시작시점의 무제한 쾌속모드 상품인 경우 DB 업데이트 하지 않음.
    // 리셋 정보는 저장해야하기 때문에 리셋 밑에서 리턴.
    const bPrevUnLimitedCountByCash = this._yd.battleParam.quickModeData.bUnLimitedCountByCash;
    if (bPrevUnLimitedCountByCash) {
      return;
    }

    // 전투 시작지점의 버프 레벨에 따른 쾌속모드 횟수 최대치를 체크하여.
    // 블루잼 사용으로 인한 퀵모드면  DB 업데이트 하지 않음.
    if (!bPrevBattleQuickModeReset) {
      const companyExpCms = cms.CompanyExp[this._user.level];
      const maxQuickModeCount = companyExpCms.quickModeResetCount;
      let maxCountByCash = 0;
      const prevMaxCountByCash = this._yd.battleParam.quickModeData.addedMaxCountByCash;
      if (prevMaxCountByCash) {
        maxCountByCash = prevMaxCountByCash;
      }
      const totalMaxQuickModeCount = maxCountByCash + maxQuickModeCount;
      if (prevQuickModeCount >= totalMaxQuickModeCount) {
        return;
      }
    }

    // 쾌속모드를 사용했고 전투 도중 리셋시간이 지나지 않았으면 카운팅.
    this._yd.quickModeChange = {
      quickModeCount: prevQuickModeCount + 1,
      updateTimeUtc: this._yd.curTimeUtc,
    };
  }

  // --------------------------------------------------------------------------
  // 재화 변경 발생시, 유효값 범위 안으로 강제하고,
  // 재화 변경이 없을시 다시 undefined 로 세팅.
  // --------------------------------------------------------------------------
  normalizePointsChange() {
    if (this._yd.ducatChange) {
      // 두캇 변경이 없으면 아예 undefined 로 세팅.
      if (this._yd.ducatChange.value === this._user.userPoints.getPoint(cmsEx.DucatPointCmsId)) {
        this._yd.ducatChange = undefined;
      } else {
        // 두캇값의 유효 레인지 체크.
        const hardCap = cms.Point[cmsEx.DucatPointCmsId].hardCap;
        this._yd.ducatChange.value = Math.min(this._yd.ducatChange.value, hardCap);
      }
    }

    if (this._yd.blueGemChange) {
      // 블루잼 변경이 없으면 아예 undefined 로 세팅.
      if (
        this._yd.blueGemChange.value === this._user.userPoints.getPoint(cmsEx.BlueGemPointCmsId)
      ) {
        this._yd.blueGemChange = undefined;
      } else {
        // 블루잼값의 유효 레인지 체크.
        const hardCap = cms.Point[cmsEx.BlueGemPointCmsId].hardCap;
        this._yd.blueGemChange.value = Math.min(this._yd.blueGemChange.value, hardCap);
      }
    }
  }

  // --------------------------------------------------------------------------
  // 누적될 업적건수 모으기
  // --------------------------------------------------------------------------
  protected gatherAchvAccumParams(accums: AccumulateParam[]): void {
    // 승/패/도주
    accums.push({
      achievementTermsCmsId:
        this._yd.battleResult.type === BattleResultType.AllyWin
          ? cmsEx.ACHIEVEMENT_TERMS.BATTLE_VICTORY
          : this._yd.battleResult.type === BattleResultType.Draw // 도주한 경우
          ? cmsEx.ACHIEVEMENT_TERMS.BATTLE_ESCAPE
          : cmsEx.ACHIEVEMENT_TERMS.BATTLE_DEFEAT, // 패배, 항복
      addedValue: 1,
    });

    // 포기한 경우..
    if (this._yd.battleResult.type === BattleResultType.Surrender) {
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.BATTLE_GIVE_UP,
        addedValue: 1,
      });
    }

    const enemyNationCmsId: number | undefined = this._yd.battleParam.enemy.nationCmsId;
    const targetUserId: number | undefined = this._yd.battleParam.targetUserId;
    // 유저가 상대인 경우 enemy.oceanNpcId 가
    // cmsEx.OCEAN_NPC_CMS_ID.USER_DATA_NPC_CMS_ID(15000010) 인 것 참고
    const enemyOceanNpcCmsId: number = this._yd.battleParam.enemy.oceanNpcId;

    if (targetUserId) {
      // 유저가 상대인 경우(PvP)

      if (this._yd.battleResult.type === BattleResultType.AllyWin) {
        // 유저 상대로 승리
        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.BATTLE_VICTORY_FROM_USER,
          addedValue: 1,
        });

        // 특정 국가 유저 상대로 승리
        if (enemyNationCmsId) {
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.BATTLE_VICTORY_FROM_USER_SPECIFIC_NATION,
            targets: [enemyNationCmsId],
            addedValue: 1,
          });
        }
      }
    } else if (!this._yd.battleParam.bTest && enemyOceanNpcCmsId) {
      // NPC가 상대인 경우(PvE)

      if (this._yd.battleResult.type !== BattleResultType.Draw) {
        // 특정 NPC 상대로 승리/패배
        accums.push({
          achievementTermsCmsId:
            this._yd.battleResult.type === BattleResultType.AllyWin
              ? cmsEx.ACHIEVEMENT_TERMS.BATTLE_VICTORY_FROM_SPECIFIC_NPC
              : cmsEx.ACHIEVEMENT_TERMS.BATTLE_DEFEAT_FROM_SPECIFIC_NPC,
          targets: [enemyOceanNpcCmsId],
          addedValue: 1,
        });
      }

      const oceanNpcCms = getOceanNpcCms()[enemyOceanNpcCmsId];
      const oceanNpcJobType = oceanNpcCms.jobType;

      if (this._yd.battleResult.type === BattleResultType.AllyWin) {
        // 특정 국가/특정 직업 성향 NPC 상대로 승리
        if (enemyNationCmsId && oceanNpcJobType) {
          accums.push({
            achievementTermsCmsId:
              cmsEx.ACHIEVEMENT_TERMS.BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION_SPECIFIC_JOB_TYPE,
            targets: [enemyNationCmsId, oceanNpcJobType],
            addedValue: 1,
          });
        }

        // 특정 국가 NPC 상대로 승리
        if (enemyNationCmsId) {
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.BATTLE_VICTORY_FROM_NPC_SPECIFIC_NATION,
            targets: [enemyNationCmsId],
            addedValue: 1,
          });
        }

        // 특정 직업 성향 NPC 상대로 승리
        if (oceanNpcJobType) {
          accums.push({
            achievementTermsCmsId:
              cmsEx.ACHIEVEMENT_TERMS.BATTLE_VICTORY_FROM_NPC_SPECIFIC_JOB_TYPE,
            targets: [oceanNpcJobType],
            addedValue: 1,
          });
        }

        // 엘리트 선단 격침.
        if (oceanNpcCms.OceanNpcType === OCEAN_NPC_TYPE.ELITE) {
          accums.push({
            achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.DEFEAT_ELITE_FLEET,
            addedValue: 1,
          });
        }
      }
    }

    if (this._yd.rewardTable[REWARD_TYPE.CAPTURED_SHIP]) {
      const capturedShipsCount = Object.values(
        this._yd.rewardTable[REWARD_TYPE.CAPTURED_SHIP]
      ).reduce((sum, elem) => sum + elem.Quantity, 0);
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.CAPTURE_SHIP,
        addedValue: capturedShipsCount,
      });
    }

    if (this._yd.battleResult.myBattleActionCount) {
      _.forOwn(this._yd.battleResult.myBattleActionCount, (count, actionCounterTypeStr) => {
        const actionCounterType: ACTION_COUNTER_TYPE = parseInt(actionCounterTypeStr, 10);
        accums.push({
          achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.BATTLE_ACTION,
          targets: [actionCounterType],
          addedValue: count,
        });
      });
    }

    // 전투 내부에서 카운팅 하는 업적.
    for (const bAchv of this._yd.battleResult.myBattleAchievements) {
      accums.push({
        achievementTermsCmsId: bAchv.termsCmsId,
        targets: bAchv.target ? [bAchv.target] : undefined,
        addedValue: bAchv.cnt,
      });
    }

    if (this._yd.totalGainBattleFame !== undefined && this._yd.totalGainBattleFame > 0) {
      accums.push({
        achievementTermsCmsId: cmsEx.ACHIEVEMENT_TERMS.GAIN_FAME,
        targets: [cmsEx.JOB_TYPE.BATTLE],
        addedValue: this._yd.totalGainBattleFame,
      });
    }
  }

  // --------------------------------------------------------------------------
  // 업적 처리. (일반/도전 전투 공통 로직)
  // --------------------------------------------------------------------------
  accumulateAchievements(sync: Sync, rsn: string, add_rsn: string): Promise<Sync> {
    // accumulate achievement
    const accums: AccumulateParam[] = [];
    this.gatherAchvAccumParams(accums);

    return this._user.userAchievement.accumulate(accums, this._user, sync, {
      user: this._user,
      rsn,
      add_rsn,
    });
  }

  // --------------------------------------------------------------------------
  // glog battle_result_data 구성
  // --------------------------------------------------------------------------
  private _build_battle_result_data(bp: BattleParam): any {
    const br = this._yd.battleResult;

    const takeback_info = {
      phase_cnt: null,
      phase_cost: null,
      turn_cnt: null,
      turn_cost: null,
    };

    const company_exp = this._yd.userExpGain;

    // 각 선박의 선장이 획득한 전투 경험치.
    const exp = [];

    const firstFleet = this._user.userFleets.getFirstFleet();
    const userShips = firstFleet.getShips();
    for (const [shipId, userShip] of Object.entries(userShips)) {
      let captainMateExpGain = 0;

      const captainMateCmsId = userShip.getSlotMateCmsId(cmsEx.ShipSlotIndexCaptainRoom);
      for (const expGain of this._yd.mateExpGains) {
        if (expGain.mateCmsId === captainMateCmsId) {
          captainMateExpGain = expGain.amount;
          break;
        }
      }

      exp.push({
        ship_idx: userShip.nub.formationIndex,
        exp: captainMateExpGain,
      });
    }

    const mission_clear = [];
    if (this._yd.battleResult.missionsCleared) {
      const idxArray =
        this._yd.battleResult.missionsCleared.challenge ||
        this._yd.battleResult.missionsCleared.oceanNpc;
      if (idxArray) {
        for (const idx of idxArray) {
          // Lua 인덱스라 1 을 빼줌.
          const battleTermsListCmsId = bp.mission.btlIds[idx - 1];
          mission_clear.push(battleTermsListCmsId);
        }
      }
    }

    if (br.takebackInfo) {
      takeback_info.phase_cnt =
        (br.takebackInfo.phaseCountByCost ? br.takebackInfo.phaseCountByCost : 0) +
        (br.takebackInfo.phaseCountByFree ? br.takebackInfo.phaseCountByFree : 0);
      takeback_info.turn_cnt =
        (br.takebackInfo.turnCountByCost ? br.takebackInfo.turnCountByCost : 0) +
        (br.takebackInfo.turnCountByFree ? br.takebackInfo.turnCountByFree : 0);

      takeback_info.phase_cost = br.takebackInfo.phaseCost || 0;
      takeback_info.turn_cost = br.takebackInfo.turnCost || 0;
    }

    const quickmode_info = {
      is_use: null,
      quickmode_cost: null,
    };

    if (br.quickModeInfo) {
      quickmode_info.is_use = br.quickModeInfo.bQuickMode ? 1 : 0;
      if (br.quickModeInfo.quickModeBlueGemCost) {
        quickmode_info.quickmode_cost = br.quickModeInfo.quickModeBlueGemCost;
      }
    }

    const is_auto = br.bAuto ? 1 : 0;
    const battle_speed = br.speed;

    return {
      is_win: isBattleWin(br.type) ? 1 : 0,
      is_giveup: br.type === BattleResultType.Surrender ? 1 : 0,
      result_type: br.type,
      turn: br.numTurns,
      action_order_idx: br.actionOrderIdx,
      company_exp,
      exp,
      mission_clear,
      takeback_info,
      quickmode_info,
      pt: this._yd.curTimeUtc - bp.randomSeed,
      is_auto,
      battle_speed,
    };
  }

  // ----------------------------------------------------------------------------
  // 운영 로그
  // ----------------------------------------------------------------------------
  glogBattleEnd(
    user: User,
    rsn: string,
    add_rsn: string,
    rbr?: RaidBattleResult,
    guildDataForLog?: any,
    pr_data?: PrData[],
    bpForGLog?: BattleParam,
    clashDataForLog?: any
  ): void {
    const bp: BattleParam = bpForGLog ? bpForGLog : this._yd.battleParam;

    if (bp.bTest) {
      // 개발용 모의 전투는 로그를 남기지 않는다.
      return;
    }

    let logCollection: string;
    if (bp.challenge) {
      logCollection = _Glog.LogColChallenge;
    } else if (bp.arena) {
      logCollection = _Glog.LogColArena;
    } else if (bp.raid) {
      if (bp.battleType === BattleType.Raid) {
        logCollection = _Glog.LogColRaid;
      } else if (bp.battleType === BattleType.GuildRaid) {
        logCollection = _Glog.LogColGuildRaid;
      } else {
        logCollection = 'unknown';
      }
    } else if (bp.infiniteLighthouse) {
      logCollection = _Glog.LogColInfiniteLighthouse;
    } else {
      if (bp.targetType === EncountTargetType.Npc) {
        logCollection = _Glog.LogColPve;
      } else {
        if (bp.battleType === BattleType.Friendly) {
          logCollection = _Glog.LogColFriendly;
        } else if (bp.battleType === BattleType.Clash) {
          logCollection = _Glog.LogColClash;
        } else {
          logCollection = _Glog.LogColPvp;
        }
      }
    }

    // region_id
    let region_id: number = null;

    // coordinates
    let coordinates: GLogCoordinate = null;

    const sailState = this._user.userSailing.getSailState();
    if (sailState) {
      region_id = sailState.region ? sailState.region.id : null;
      coordinates = getGLogCoordinate(sailState.location);
    } else {
      const townCmsId = this._user.userTown.getTownCmsId();
      const townCms = cms.Town[townCmsId];
      region_id = townCms.RegionId;
    }

    const battle_id = parseInt(this._yd.battleId);

    let battle_ship_data;
    const battle_start_data = UserBattle.buildGlog_battle_start_data(this._user, bp);
    if (bp.arena) {
      battle_ship_data = UserBattle.buildGlog_arena_battle_ship_data(this._user, bp.ally.ships);
    } else {
      battle_ship_data = UserBattle.buildGlog_battle_ship_data(this._user, bp.ally.ships);
    }
    const enemy_ship_data = UserBattle.buildGlog_enemy_ship_data(bp.enemy.ships);
    const battle_result_data = this._build_battle_result_data(bp);

    let battle_challenge_data: any;
    if (bp.challenge) {
      const userChallenge = this._user.userChallenges.getChallenge(bp.challenge.cmsId);

      battle_challenge_data = {
        challenge_id: bp.challenge.cmsId,
        difficulty: bp.challenge.difficulty,
        is_retry: bp.challenge.bRetry ? 1 : 0,
        clear_difficulty: userChallenge ? userChallenge.clearedDifficulty : null,
        clear_mission: userChallenge ? userChallenge.clearedMissionField : null,
      };
    }

    const battle_summary_data = UserBattle.buildGlog_battle_summary_data(this._yd.battleResult);

    // reward_data
    const reward_data: RewardData[] = [];

    // 두캇
    if (this._yd.ducatChange) {
      reward_data.push({
        type: REWARD_TYPE[REWARD_TYPE.POINT],
        id: cmsEx.DucatPointCmsId,
        uid: null,
        amt: this._yd.ducatGains,
      });
    }
    this._user.userPoints.getPoint(cmsEx.DucatPointCmsId);

    // 전투 명성
    if (this._yd.fameGain) {
      reward_data.push({
        type: REWARD_TYPE[REWARD_TYPE.BATTLE_FAME],
        id: null,
        uid: null,
        amt: this._yd.fameGain,
      });
    }

    // 기타 보상
    _.forOwn(this._yd.rewardTable, (elem, typeStr) => {
      const type = REWARD_TYPE[parseInt(typeStr)];
      _.forOwn(elem, (battleReward, idStr) => {
        reward_data.push({
          type,
          id: parseInt(idStr),
          uid: null,
          amt: battleReward.Quantity,
        });
      });
    });

    // loss_data
    let loss_data: RewardData[];
    if (!isBattleWin(this._yd.battleResult.type)) {
      if (logCollection === _Glog.LogColPvp) {
        loss_data = UserBattle.buildGlog_loss_data(user, bp);
      }
    }

    let clash_finish_info;

    if (this._yd.clash) {
      if (this._yd.clash.rewardFixedId) {
        const rewardFixedCms: RewardFixedDesc = cms.RewardFixed[this._yd.clash.rewardFixedId];
        for (const elem of rewardFixedCms.rewardFixed) {
          reward_data.push({
            type: REWARD_TYPE[elem.Type],
            id: elem.Id,
            uid: null,
            amt: elem.Quantity,
          });
        }
      }

      const session: ClashSession = user.userClash.getSession(mutil.curTimeUtc());
      clash_finish_info = {
        score: session.score,
        get_clash_score: session.score - clashDataForLog.score,
        season_id: ClashUtil.getSeasonId(session.sessionId),
        winningStreak: session.winStreak,
        battle_validate: clashDataForLog.bValidBattle,
      };
    }

    mlog.verbose('[CLASH] battleEndGlog', {
      clash_finish_info,
      reward_data,
    });

    let enemy_type;
    let enemy_id;
    let arena_start_info;
    let arena_finish_info;
    let enemy_arena_start_info;
    let enemy_arena_finish_info;
    let is_revenge;
    let other_fleets;

    if (bp.arena) {
      enemy_type = bp.arena.enemyType;

      arena_start_info = UserBattle.buildGlog_arena_start_info(
        bp.arena.myGrade,
        bp.arena.myScore,
        bp.arena.sessionId
      );

      if (this._yd.arenaInfo.logInfo) {
        arena_finish_info = UserBattle.buildGlog_arena_finish_info(
          this._yd.arenaInfo.logInfo.myGrade,
          this._yd.arenaInfo.logInfo.myScore,
          this._yd.arenaInfo.logInfo.myChangedScore,
          bp.arena.sessionId
        );
      }

      if (1 === enemy_type) {
        // user
        enemy_id = bp.arena.opponentId;

        enemy_arena_start_info = UserBattle.buildGlog_arena_start_info(
          bp.arena.opponentGrade,
          bp.arena.opponentScore,
          bp.arena.sessionId
        );

        if (this._yd.arenaInfo.logInfo) {
          enemy_arena_finish_info = UserBattle.buildGlog_arena_finish_info(
            this._yd.arenaInfo.logInfo.enemyGrade,
            this._yd.arenaInfo.logInfo.enemyScore,
            this._yd.arenaInfo.logInfo.enemyChangedScore,
            bp.arena.sessionId
          );
        }
      } else {
        // npc
        const botDesc = cms.ArenaMatchBot[bp.arena.opponentId];
        enemy_id = botDesc ? botDesc.fleetId : null;

        enemy_arena_start_info = {
          tier_id: null,
          tier_name: null,
          arena_score: null,
          type: null,
          matchGroup: null,
          season_id: bp.arena.sessionId,
        };

        enemy_arena_finish_info = {
          tier_id: null,
          tier_name: null,
          arena_score: null,
          type: null,
          matchGroup: null,
          season_id: bp.arena.sessionId,
        };
      }

      is_revenge = bp.arena.mode === 1 ? 0 : 1;
    } else if (bp.clash) {
      enemy_type = bp.targetType;
      const { opponentOrigin } = ClashUtil.convertOriginData(this._user.userId, bp.clash);
      enemy_id = opponentOrigin.userId;
    } else {
      // 모의전 이외 공통
      enemy_type = bp.targetType;
      enemy_id = bp.targetLogId;
      other_fleets = UserBattle.buildGlog_other_fleets(bp);
    }

    let is_elite;
    if (logCollection === _Glog.LogColPve) {
      const npcCms = getOceanNpcCms()[bp.enemy.oceanNpcId];
      if (npcCms) {
        is_elite = npcCms.OceanNpcType === OCEAN_NPC_TYPE.ELITE ? 1 : 0;
      }
    }

    let is_offline: number = undefined;
    let is_user: number = undefined;
    if (!bp.challenge && !bp.arena && !bp.clash) {
      is_offline = bp.bOfflineSailing ? 1 : 0;
      is_user = this._user.isOfflineSailingBot ? 0 : 1;
    }

    const formation_id = bp.ally.battleFormationCmsId;
    const formationCms = cms.BattleFormation[formation_id];
    const formation_name = formationCms ? formationCms.name : null;

    let quest_id = undefined;
    if (!bp.challenge && !bp.clash) {
      quest_id = bp.encountQuestCmsId || null;
    }

    // for raid-battle
    let dmg;
    let boss_raid_id;
    let boss_raid_name;
    let remaining_time;
    let remaining_hp;
    let remaining_hp_rate;
    let guild_data;
    let boss_accum_damage;
    if (bp.raid && rbr) {
      if (bp.battleType === BattleType.Raid) {
        const { raidManager } = Container.get(LobbyService);
        const raid = raidManager.getRaid(bp.raid.bossRaidCmsId);
        if (raid) {
          const rMNub = raidManager.getNub();
          boss_raid_id = bp.raid.bossRaidCmsId;
          dmg = rbr.actualDamage;
          boss_raid_name = cms.BossRaid[bp.raid.bossRaidCmsId].name;
          remaining_time = rMNub.stateEndAt - mutil.curTimeUtc();
          boss_accum_damage = rbr.bossAccumulatedDamage;
          // remaining_hp = rbr.curHp;
          // remaining_hp_rate = Math.floor((rbr.curHp / rbr.maxHp) * 100);
        }
      } else if (bp.battleType === BattleType.GuildRaid) {
        const { guildRaidManager } = Container.get(LobbyService);

        const raid = guildRaidManager.getGuildRaid(user.userGuild.guildId, bp.raid.bossRaidCmsId);
        if (raid) {
          const raidNub = raid.getNub();
          boss_raid_id = bp.raid.bossRaidCmsId;
          dmg = rbr.actualDamage;
          boss_raid_name = cms.GuildBossRaid[bp.raid.bossRaidCmsId].name;
          remaining_time = raidNub.stateEndAt - mutil.curTimeUtc();
          remaining_hp = rbr.curHp;
          remaining_hp_rate = Math.floor((rbr.curHp / rbr.maxHp) * 100);
          guild_data = guildDataForLog;
        }
      }
    }

    let batle_lighhouse_data;
    let sweep_battle_flag;
    if (bp.battleType === BattleType.InfiniteLighthouse) {
      const infiniteLighthouseCms = cms.InfiniteLighthouse[bp.infiniteLighthouse.stageCmsId];
      batle_lighhouse_data = {
        lighhouse_id: bp.infiniteLighthouse.sessionId,
        stage_group: infiniteLighthouseCms.stageGroup,
        stage: infiniteLighthouseCms.stage,
        is_retry: false,
      };
      sweep_battle_flag = false;
    }

    this._user.glog(logCollection, {
      rsn,
      add_rsn,
      battle_id,
      region_id,
      coordinates,
      is_elite,
      combat_power: bp.ally.combatPower,
      nation: this._user.nationCmsId,
      enemy_type,
      enemy_id,
      enemy_level: bp.enemy.level,
      enemy_combat_power: bp.enemy.combatPower,
      enemy_nation: bp.enemy.nationCmsId,
      battle_result_data,
      battle_start_data,
      battle_ship_data,
      enemy_ship_data,
      reward_data,
      loss_data,
      battle_challenge_data,
      battle_summary_data,
      is_offline,
      is_user,
      other_fleets,
      arena_start_info,
      enemy_arena_start_info,
      arena_finish_info,
      enemy_arena_finish_info,
      formation_name,
      formation_id,
      is_revenge,
      quest_id,
      boss_raid_id,
      dmg,
      boss_accum_damage,
      boss_raid_name,
      remaining_time,
      remaining_hp,
      remaining_hp_rate,
      guild_data,
      batle_lighhouse_data,
      sweep_battle_flag,
      pr_data,
      clash_finish_info,
    });

    // 전투 종료시, 전투중 제독 명령 사용 로그를 만들어 전송해야 한다.
    for (const baoInfo of this._yd.battleResult.skillUsed.orders) {
      const battleSkillCms = cms.BattleSkill[baoInfo.cmsId];

      const order_name = battleSkillCms.name;
      const order_id = baoInfo.cmsId;
      const phase = baoInfo.phase;
      const turn = baoInfo.turn;

      this._user.glog('admiral_order_battle', {
        rsn,
        add_rsn,
        order_name,
        order_id,
        battle_id,
        phase,
        turn,
      });
    }
  }
} // class BattleEndYard

// ----------------------------------------------------------------------------
// 기타 전투 종료시 사용되는 함수들
// ----------------------------------------------------------------------------
export namespace BattleEndUtil {
  // ----------------------------------------------------------------------------
  // 승리한 경우, 전투 결과에서 유저가 격침시킨 함대들을 뽑는다. (난입 포함)
  // ----------------------------------------------------------------------------
  function buildDefeatedOceanNpcs(bp: BattleParam, result: BattleResult): number[] {
    const oceanNpcArr: number[] = [];

    if (result.type !== BattleResultType.AllyWin) {
      return oceanNpcArr;
    }

    // 먼저 주적을 첨부하고.
    if (bp.targetType === EncountTargetType.Npc) {
      oceanNpcArr.push(bp.enemy.oceanNpcId);
    }

    // 턴수를 체크해서 난입이 된 적 함대들을 모은다. (유저함대는 난입 안함)
    for (const rFleets of bp.reinforcements) {
      if (rFleets.turnNum > result.numTurns) {
        continue;
      }

      if (rFleets.faction !== BC.Faction.Enemy) {
        continue;
      }

      oceanNpcArr.push(rFleets.oceanNpcId);
    }

    return oceanNpcArr;
  }

  // ----------------------------------------------------------------------------
  // BattleEndYardData 생성 헬퍼
  // ----------------------------------------------------------------------------
  export function buildBattleEndYardData(
    user: User,
    curTimeUtc: number,
    battleInfo: BattleInfo,
    battleResult: BattleResult
  ): BattleEndYardData {
    const defeatedOceanNpcs = battleInfo.multiId
      ? []
      : buildDefeatedOceanNpcs(battleInfo.battleParam, battleResult);

    const yd: BattleEndYardData = {
      curTimeUtc,
      battleId: battleInfo.battleId,
      battleParam: battleInfo.battleParam,
      logCount: battleInfo.logCount,
      battleResult,
      defeatedOceanNpcs,
      mateExpChanges: [],
      mateExpGains: [],
      userExpGain: 0,
      consumedItemChanges: [],
      rewardTable: {},
      ducatGains: 0,
      ducatChange: {
        cmsId: cmsEx.DucatPointCmsId,
        value: user.userPoints.getPoint(cmsEx.DucatPointCmsId),
      },
      blueGemChange: {
        cmsId: cmsEx.BlueGemPointCmsId,
        value: user.userPoints.getPoint(cmsEx.BlueGemPointCmsId),
      },
    };

    return yd;
  }

  // ----------------------------------------------------------------------------
  // 기존 battleEnd.ts 에서 복사해옴
  // battleEnd 에서만 사용됨. 반드시 determineLifeChanges 이후에 호출되어야 됨.
  // ----------------------------------------------------------------------------
  export function recoverFlagShipIfWrecked(
    flagShipId: number,
    yd: BattleEndYardData,
    user: User
  ): void {
    const flagShipChange = yd.shipChanges.find((elem) => elem.id === flagShipId);
    if (!flagShipChange) {
      return;
    }

    let bWrecked = false;

    if (flagShipChange.durability === 0) {
      flagShipChange.durability = 1;
      bWrecked = true;
    }

    if (flagShipChange.sailor === 0) {
      flagShipChange.sailor = 1;
      bWrecked = true;
    }

    if (!bWrecked) {
      return;
    }

    // 기함의 경우 자동회복되어 ship.onWreckedShip 을 타지 않기 때문에 여기서 수명을 감소시킨다.
    const lifeChange = yd.shipLifeChanges.find((elem) => elem.id === flagShipId);
    if (lifeChange) {
      lifeChange.life = Math.max(0, lifeChange.life - cms.Const.WreckLossShipLifeValue.value);
    } else {
      const userShip = user.userFleets.getShip(flagShipId);
      const oldLife = userShip.getNub().life;
      yd.shipLifeChanges.push({
        id: flagShipId,
        life: Math.max(0, oldLife - cms.Const.WreckLossShipLifeValue.value),
      });
    }
  } // recoverFlagShipIfWrecked

  // ----------------------------------------------------------------------------
  // 기존 battleEnd.ts 에서 복사해옴
  // ----------------------------------------------------------------------------
  export function recoverShipAutomatically(
    rewards: BattleRewardTable,
    user: User,
    shipChanges: ShipChange[],
    shipCargoChanges: ShipCargoChange[],
    shipInjurySailors: { [shipId: number]: number }
  ): void {
    let rewardWaters = 0;
    let rewardLumbers = 0;
    if (rewards[REWARD_TYPE.DEPART_SUPPLY]) {
      if (rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER]) {
        rewardWaters = rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER].Quantity;
      }
      if (rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER]) {
        rewardLumbers = rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER].Quantity;
      }
    }
    const firstFleet = user.userFleets.getFirstFleet();
    const fleetWaters = firstFleet.getCargoQuantity(cmsEx.SUPPLY_CMS_ID.WATER);
    const fleetLumbers = firstFleet.getCargoQuantity(cmsEx.SUPPLY_CMS_ID.LUMBER);

    let neededWaters = 0;
    let neededLumbers = 0;
    const constBattleRestoreCostForDurability = cms.Const.BattleRestoreCostForDurability.value;
    const constBattleRestoreCostForSailor = cms.Const.BattleRestoreCostForSailor.value;
    const STAT_MAX_SAILOR = cmsEx.STAT_TYPE.SHIP_MAX_SAILOR;

    for (const ship of shipChanges) {
      if (ship.durability === 0 || ship.sailor === 0) {
        continue;
      }

      if (shipInjurySailors && shipInjurySailors[ship.id] !== undefined) {
        neededWaters += (shipInjurySailors[ship.id] * constBattleRestoreCostForSailor) / 1000;
      }

      const userShip = user.userFleets.getShip(ship.id);
      const damage = userShip.getMaxDurability(user.companyStat) - ship.durability;
      neededLumbers += (damage * constBattleRestoreCostForDurability) / 1000;
    }

    neededWaters = Math.ceil(neededWaters);
    neededLumbers = Math.ceil(neededLumbers);

    const ships = firstFleet.getShipsOrderByFormationIndex();

    // 선원 회복
    if (rewardWaters + fleetWaters > 0 && neededWaters > 0) {
      let watersToUnloadFromFleet = 0;

      // 모두 회복이 가능한 경우
      if (neededWaters <= rewardWaters + fleetWaters) {
        for (const ship of shipChanges) {
          if (ship.durability === 0 || ship.sailor === 0) {
            continue;
          }

          if (shipInjurySailors[ship.id]) {
            ship.sailor += shipInjurySailors[ship.id];
          }
        }

        // 먼저 보상에서 얻은 물을 사용
        if (rewardWaters > 0) {
          rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER].Quantity -= neededWaters;
          if (rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER].Quantity < 0) {
            watersToUnloadFromFleet =
              -rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER].Quantity;
            rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER].Quantity = 0;
          }

          if (rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER].Quantity === 0) {
            delete rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER];
            if (Object.keys(rewards[REWARD_TYPE.DEPART_SUPPLY]).length === 0) {
              delete rewards[REWARD_TYPE.DEPART_SUPPLY];
            }
          }
        } else {
          watersToUnloadFromFleet = neededWaters;
        }
      } else {
        // 전체 회복이 안 될 경우.
        let totalWaters = rewardWaters + fleetWaters;

        // 되도록 균등한 비율의 선원이 되도록 회복.
        for (let targetPercent = 0.2; targetPercent <= 1; targetPercent += 0.2) {
          let bIsWaterEnough = true;
          for (const ship of ships) {
            if (Math.floor((totalWaters * 1000) / constBattleRestoreCostForSailor) === 0) {
              // 회복을 1도 못 하는 상황
              bIsWaterEnough = false;
              break;
            }

            const shipChange = shipChanges.find((shipChange) => {
              return shipChange.id === ship.getNub().id;
            });
            if (!shipChange || shipChange.durability === 0 || shipChange.sailor === 0) {
              continue;
            }

            const maxSailor = ship.getStat(user.companyStat).get(STAT_MAX_SAILOR);
            const curPercent = shipChange.sailor / maxSailor;
            if (curPercent >= targetPercent) {
              continue;
            }

            if (!shipInjurySailors[ship.getNub().id]) {
              continue;
            }

            const targetSailor = Math.floor(maxSailor * targetPercent);
            if (targetSailor === shipChange.sailor) {
              continue;
            }
            const sailorToRepair = Math.min(
              shipInjurySailors[ship.getNub().id],
              Math.min(
                targetSailor - shipChange.sailor,
                Math.floor((totalWaters * 1000) / constBattleRestoreCostForSailor)
              )
            );

            shipInjurySailors[ship.getNub().id] -= sailorToRepair;
            shipChange.sailor += sailorToRepair;
            totalWaters -= (sailorToRepair * constBattleRestoreCostForSailor) / 1000;
          }

          if (!bIsWaterEnough) {
            break;
          }
        }

        if (rewardWaters > 0) {
          delete rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.WATER];
          if (Object.keys(rewards[REWARD_TYPE.DEPART_SUPPLY]).length === 0) {
            delete rewards[REWARD_TYPE.DEPART_SUPPLY];
          }
        }
        watersToUnloadFromFleet = fleetWaters;
      }

      // 남은 물은 현재 적재물에서 차감
      if (watersToUnloadFromFleet > 0) {
        for (const ship of ships) {
          const cargoChangeIndex = _.findIndex(shipCargoChanges, (change) => {
            return change.shipId === ship.getNub().id && change.cmsId === cmsEx.SUPPLY_CMS_ID.WATER;
          });
          if (cargoChangeIndex === -1) {
            let shipWaters = ship.getCargoQuantity(cmsEx.SUPPLY_CMS_ID.WATER);
            if (shipWaters > 0) {
              const quantityToUnload = Math.min(shipWaters, watersToUnloadFromFleet);
              watersToUnloadFromFleet -= quantityToUnload;
              shipWaters -= quantityToUnload;
              shipCargoChanges.push({
                shipId: ship.getNub().id,
                cmsId: cmsEx.SUPPLY_CMS_ID.WATER,
                quantity: shipWaters,
                pointInvested: 0,
              });
            }
          } else {
            const quantityToUnload = Math.min(
              shipCargoChanges[cargoChangeIndex].quantity,
              watersToUnloadFromFleet
            );
            watersToUnloadFromFleet -= quantityToUnload;
            shipCargoChanges[cargoChangeIndex].quantity -= quantityToUnload;
            if (shipCargoChanges[cargoChangeIndex].quantity === 0) {
              shipCargoChanges.splice(cargoChangeIndex, 1);
            }
          }

          if (watersToUnloadFromFleet === 0) {
            break;
          }
        }
      }
    }

    // 내구 수리
    if (rewardLumbers + fleetLumbers > 0 && neededLumbers > 0) {
      let lumbersToUnloadFromFleet = 0;

      // 목재로 모두 수리가 가능한 경우
      if (neededLumbers <= rewardLumbers + fleetLumbers) {
        for (const ship of shipChanges) {
          if (ship.durability === 0 || ship.sailor === 0) {
            continue;
          }

          const userShip = user.userFleets.getShip(ship.id);
          ship.durability = userShip.getMaxDurability(user.companyStat);
        }

        // 먼저 보상에서 얻은 자재를 사용
        if (rewardLumbers > 0) {
          rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER].Quantity -= neededLumbers;
          if (rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER].Quantity < 0) {
            lumbersToUnloadFromFleet =
              -rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER].Quantity;
            rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER].Quantity = 0;
          }

          if (rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER].Quantity === 0) {
            delete rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER];
            if (Object.keys(rewards[REWARD_TYPE.DEPART_SUPPLY]).length === 0) {
              delete rewards[REWARD_TYPE.DEPART_SUPPLY];
            }
          }
        } else {
          lumbersToUnloadFromFleet = neededLumbers;
        }
      } else {
        // 전체 수리가 안 될 경우.
        let totalLumbers = rewardLumbers + fleetLumbers;
        // const shipDurabilityPercents: { shipId: number; percent: number }[] = [];

        // 되도록 균등한 비율의 내구도가 되도록 수리.
        for (let targetPercent = 0.2; targetPercent <= 1; targetPercent += 0.2) {
          let bIsLumberEnough = true;
          for (const ship of ships) {
            if (Math.floor((totalLumbers * 1000) / constBattleRestoreCostForDurability) === 0) {
              // 수리를 1도 못 하는 상황
              bIsLumberEnough = false;
              break;
            }

            const shipChange = shipChanges.find((shipChange) => {
              return shipChange.id === ship.getNub().id;
            });
            if (!shipChange || shipChange.durability === 0 || shipChange.sailor === 0) {
              continue;
            }

            const maxDurability = ship.getMaxDurability(user.companyStat);
            const curPercent = shipChange.durability / maxDurability;
            if (curPercent >= targetPercent) {
              continue;
            }

            const targetDurability = Math.floor(maxDurability * targetPercent);
            if (targetDurability === shipChange.durability) {
              continue;
            }
            const durabilityToRepair = Math.min(
              targetDurability - shipChange.durability,
              Math.floor((totalLumbers * 1000) / constBattleRestoreCostForDurability)
            );

            shipChange.durability += durabilityToRepair;
            totalLumbers -= (durabilityToRepair * constBattleRestoreCostForDurability) / 1000;
          }

          if (!bIsLumberEnough) {
            break;
          }
        }

        if (rewardLumbers > 0) {
          delete rewards[REWARD_TYPE.DEPART_SUPPLY][cmsEx.SUPPLY_CMS_ID.LUMBER];
          if (Object.keys(rewards[REWARD_TYPE.DEPART_SUPPLY]).length === 0) {
            delete rewards[REWARD_TYPE.DEPART_SUPPLY];
          }
        }
        lumbersToUnloadFromFleet = fleetLumbers;
      }

      // 남은 목재는 현재 적재물에서 차감
      if (lumbersToUnloadFromFleet > 0) {
        for (const ship of ships) {
          const cargoChangeIndex = _.findIndex(shipCargoChanges, (change: ShipCargoChange) => {
            return (
              change.shipId === ship.getNub().id && change.cmsId === cmsEx.SUPPLY_CMS_ID.LUMBER
            );
          });
          if (cargoChangeIndex === -1) {
            let shipLumbers = ship.getCargoQuantity(cmsEx.SUPPLY_CMS_ID.LUMBER);
            if (shipLumbers > 0) {
              const quantityToUnload = Math.min(shipLumbers, lumbersToUnloadFromFleet);
              lumbersToUnloadFromFleet -= quantityToUnload;
              shipLumbers -= quantityToUnload;
              shipCargoChanges.push({
                shipId: ship.getNub().id,
                cmsId: cmsEx.SUPPLY_CMS_ID.LUMBER,
                quantity: shipLumbers,
                pointInvested: 0,
              });
            }
          } else {
            const quantityToUnload = Math.min(
              shipCargoChanges[cargoChangeIndex].quantity,
              lumbersToUnloadFromFleet
            );
            lumbersToUnloadFromFleet -= quantityToUnload;
            shipCargoChanges[cargoChangeIndex].quantity -= quantityToUnload;
            if (shipCargoChanges[cargoChangeIndex].quantity === 0) {
              shipCargoChanges.splice(cargoChangeIndex, 1);
            }
          }

          if (lumbersToUnloadFromFleet === 0) {
            break;
          }
        }
      }
    }
  } // recoverShipAutomatically

  // ----------------------------------------------------------------------------
  // 전투 시작시와 종료시 아군 선박들의 수명을 비교해서 달라진 경우,
  // 수명 변경 내용을 기록.
  // ----------------------------------------------------------------------------
  export function determineLifeChanges(
    user: User,
    yd: BattleEndYardData,
    bpShips: BattleParamShip[]
  ) {
    yd.shipLifeChanges = [];

    const br = yd.battleResult;
    const brShips = br.allyShips;

    for (let i = 0; i < bpShips.length; ++i) {
      const bpShipBefore = bpShips[i];
      const brShipAfter = brShips[i];

      if (bpShipBefore.life !== brShipAfter.life) {
        const userShip = user.userFleets.getFirstFleet().getShipByFormationIndex(i + 1);

        // 이상한 수명값인지 체크
        const shipStat = userShip.getStat(user.companyStat);
        if (brShipAfter.life < 0 || brShipAfter.life > shipStat.get(cmsEx.STAT_TYPE.SHIP_LIFE)) {
          throw new MError('Invalid ship life after battle.', MErrorCode.INVALID_REQUEST, {
            battleResult: yd.battleResult,
          });
        }

        yd.shipLifeChanges.push({
          id: userShip.getNub().id,
          life: brShipAfter.life,
        });
      }
    }
  }
} // namespace BattleUtil

// ----------------------------------------------------------------------------
// 전투 정산용 reward & payment spec
// ----------------------------------------------------------------------------
export class BattleEndSpec extends RewardAndPaymentSpec {
  protected _yd: BattleEndYardData;

  constructor(yd: BattleEndYardData) {
    super();

    this._yd = yd;
  }

  accumulate(user: User, tryData: TryData, changes: Changes): CHANGE_TASK_RESULT {
    this._user = user;
    this._tryData = tryData;
    this._changes = changes;

    const taskResults: CHANGE_TASK_RESULT[] = [];
    const yard = this._yd;

    if (yard.mateExpChanges) {
      for (const change of yard.mateExpChanges) {
        taskResults.push(
          opApplyMateExpChange(user, tryData, changes, cmsEx.JOB_TYPE.BATTLE, change)
        );
      }
    }

    if (yard.userExpLevelChange && yard.userExpLevelChange.energyChange) {
      taskResults.push(
        opApplyEnergyChange(user, tryData, changes, yard.userExpLevelChange.energyChange)
      );
    } else if (yard.challenegeLossEnergyChange) {
      taskResults.push(
        opApplyEnergyChange(user, tryData, changes, yard.challenegeLossEnergyChange)
      );
    }

    if (
      yard.userExpLevelChange &&
      yard.userExpLevelChange.exp !== undefined &&
      yard.userExpLevelChange.level !== undefined
    ) {
      taskResults.push(
        opSetUserExpLevel(
          user,
          tryData,
          changes,
          yard.userExpLevelChange.exp,
          yard.userExpLevelChange.level
        )
      );
    }

    if (yard.freeTakebackChange) {
      taskResults.push(opApplyFreeTakebackChange(user, tryData, changes, yard.freeTakebackChange));
    }

    if (yard.quickModeChange) {
      taskResults.push(opApplyQuickModeChange(user, tryData, changes, yard.quickModeChange));
    }

    if (yard.ducatChange) {
      taskResults.push(opApplyPointChange(user, tryData, changes, yard.ducatChange));
    }
    if (yard.blueGemChange) {
      taskResults.push(opApplyPointChange(user, tryData, changes, yard.blueGemChange));
    }

    if (yard.leaderMateCmsId !== undefined && yard.battleFame !== undefined) {
      taskResults.push(opSetFame(user, tryData, changes, cmsEx.JOB_TYPE.BATTLE, yard.battleFame));
    }

    taskResults.push(opApplyGameStateChange(user, tryData, changes, yard.gameStateChange));

    // 항해사 부상 적용
    if (yard.battleLosses && yard.battleLosses.injuredMateCmsIds) {
      _.forOwn(yard.battleLosses.injuredMateCmsIds, (injuryTimeSec, strMateCmsId) => {
        taskResults.push(
          opAddMateInjuryState(
            user,
            tryData,
            changes,
            parseInt(strMateCmsId, 10),
            yard.curTimeUtc,
            injuryTimeSec
          )
        );
      });
    }

    if (
      yard.battleLosses &&
      (yard.battleLosses.sunkShipIds ||
        yard.battleLosses.injuredMateCmsIds ||
        yard.battleLosses.recoverableSailors)
    ) {
      taskResults.push(
        opSetGameOverLosses(user, tryData, changes, {
          sunkShipIds: yard.battleLosses.sunkShipIds,
          recoverableSailors: yard.battleLosses.recoverableSailors,
          injuredMateCmsIds: yard.battleLosses.injuredMateCmsIds,
        })
      );
    }

    if (yard.shipChanges) {
      for (const change of yard.shipChanges) {
        taskResults.push(
          opSetShipDurabilityAndSailor(
            user,
            tryData,
            changes,
            change.id,
            change.durability,
            change.sailor
          )
        );
      }
    }

    if (yard.shipCargoChanges) {
      for (const change of yard.shipCargoChanges) {
        taskResults.push(opApplyShipCargoChange(user, tryData, changes, change));
      }
    }

    if (yard.shipLifeChanges) {
      for (const change of yard.shipLifeChanges) {
        taskResults.push(opSetShipLife(user, tryData, changes, change.id, change.life));
      }
    }

    if (yard.insuranceUnpaidChange) {
      taskResults.push(
        opApplyInsuranceUnpaidChange(user, tryData, changes, yard.insuranceUnpaidChange)
      );
    }

    if (yard.questContextChanges) {
      for (const change of yard.questContextChanges) {
        taskResults.push(
          opSetQuestFlags(user, tryData, changes, change.cmsId, change.uflags, change.lflags)
        );
      }
    }

    if (yard.consumedItemChanges) {
      for (const change of yard.consumedItemChanges) {
        taskResults.push(opApplyItemChange(user, tryData, changes, change));
      }
    }

    if (yard.reputationChanges) {
      for (const change of yard.reputationChanges) {
        taskResults.push(
          opSetReputation(
            user,
            tryData,
            changes,
            change.nationCmsId,
            change.reputation,
            change.updateTimeUtc
          )
        );
      }
    }

    taskResults.push(opBattleLogEnd(user, tryData, changes, yard.battleId));

    if (yard.nationIntimacyChange && yard.nationIntimacyChange.nationDiplomacyCmsId !== undefined) {
      taskResults.push(
        opRecordNationEventOccur(
          user,
          tryData,
          changes,
          yard.nationIntimacyChange.userNationCmsId,
          yard.nationIntimacyChange.enemyNationCmsId,
          yard.nationIntimacyChange.nationDiplomacyCmsId
        )
      );
    }

    if (yard.challengeUpdate) {
      taskResults.push(opSetChallenge(user, tryData, changes, yard.challengeUpdate));
    }

    if (yard.challengeQuestCmsId) {
      taskResults.push(opDropQuest(user, tryData, changes, yard.challengeQuestCmsId));
    }

    // 전투에서만 한정적으로 사용된 퀘스트가 있을 경우 전투 종료 후 퀘스트를 제거한다.
    if (yard.questCmsId) {
      taskResults.push(opDropQuest(user, tryData, changes, yard.questCmsId));
    }

    taskResults.push(
      opSetBattleEndResult(user, tryData, changes, {
        mateExpGains: yard.mateExpGains,
        ducatGains: yard.ducatGains,
        userExpGain: yard.userExpGain,
        fameGain: yard.fameGain,
        missionsCleared: yard.battleResult.missionsCleared,
        numTurns: yard.battleResult.numTurns,
        mapCmsId: yard.battleParam.mapCmsId,
        oceanNpcId: yard.battleParam.enemy.oceanNpcId,
        allyShips: yard.challengeUpdate ? yard.battleResult.allyShips : undefined,
        mission: yard.battleParam.mission,
        sunkShipIds: yard.battleLosses ? yard.battleLosses.sunkShipIds : undefined,
      })
    );

    // 받을 수 있는 보상은 여기서 지급한다.
    if (yard.rewardTable) {
      const bIsBound = yard.battleParam.battleType === BattleType.Challenge;
      const arr = [];
      _.forOwn(yard.rewardTable, (elem) => {
        _.forOwn(elem, (battleReward: BattleReward) => {
          if (battleReward.Type === REWARD_TYPE.CAPTURED_SHIP) {
            battleReward.receivedQuantity = 0;
            for (let i = 0; i < battleReward.Quantity; i++) {
              const res = this._tryElem(
                battleReward,
                {
                  type: RNP_TYPE.NONE,
                  bIsBound,
                  bIsAccum: true,
                },
                yard.curTimeUtc
              );
              if (res === CHANGE_TASK_RESULT.OK) {
                battleReward.receivedQuantity++;
              } else {
                break;
              }
            }
          } else if (
            battleReward.Type === REWARD_TYPE.DEPART_SUPPLY ||
            battleReward.Type === REWARD_TYPE.TRADE_GOODS ||
            battleReward.Type === REWARD_TYPE.SMUGGLE_GOODS
          ) {
            const res = this._tryElem(
              battleReward,
              {
                type: RNP_TYPE.NONE,
                bIsBound,
                bIsAccum: true,
                bSelectLoadsIfExceededRatio: true,
              },
              yard.curTimeUtc
            );

            if (res === CHANGE_TASK_RESULT.OK) {
              battleReward.receivedQuantity = battleReward.Quantity;
            }
          } else {
            const res = this._tryElem(
              battleReward,
              {
                type: RNP_TYPE.NONE,
                bIsBound,
                bIsAccum: true,
              },
              yard.curTimeUtc
            );
            if (res === CHANGE_TASK_RESULT.OK) {
              battleReward.receivedQuantity = battleReward.Quantity;
            }
          }

          arr.push(battleReward);
        });
      });

      if (arr.length > 0) {
        taskResults.push(opSetBattleRewards(user, tryData, changes, arr));
      }
    }

    if (yard.karmaChange) {
      taskResults.push(opApplyKarmaChange(user, tryData, changes, yard.karmaChange));
    }

    for (const res of taskResults) {
      if (res > CHANGE_TASK_RESULT.OK_MAX) {
        return res;
      }
    }

    return CHANGE_TASK_RESULT.OK;
  }
} // class BattleEndSpec
