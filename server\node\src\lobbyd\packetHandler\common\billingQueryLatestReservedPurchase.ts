// ----------------------------------------------------------------------------
// COPYRIGHT (C)2017 BY MOTIF CO., LTD. ALL RIGHTS RESERVED.
// ----------------------------------------------------------------------------

import _ from 'lodash';

import mhttp from '../../../motiflib/mhttp';
import { MError, MErrorCode } from '../../../motiflib/merror';
import { User } from '../../user';
import { CPacket, CONNECTION_STATE } from '../../userConnection';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../index';

// ----------------------------------------------------------------------------
// 단순히 LG Billing Server API 로 이어줌.
// ----------------------------------------------------------------------------

interface RequestBody {
  productId: string;
}

interface ResponseBody {
  billingApiResp: unknown;
}

// ----------------------------------------------------------------------------
export class Cph_Common_BillingQueryLatestReservedPurchase implements ClientPacketHandler {
  // --------------------------------------------------------------------------
  constructor() {}

  // --------------------------------------------------------------------------
  testGameState(user: User): boolean {
    return true;
  }

  // --------------------------------------------------------------------------
  async exec(user: User, packet: CPacket): Promise<any> {
    user.ensureConnState(CONNECTION_STATE.LOGGED_IN);

    const reqBody: RequestBody = packet.bodyObj;

    return Promise.resolve()
      .then(() => {
        return mhttp.lgbillingd.queryLatestReservedPurchaseByProductId(
          user.storeCode,
          reqBody.productId,
          user.userId.toString()
        );
      })
      .then((billingApiResp) => {
        return user.sendJsonPacket<ResponseBody>(packet.seqNum, packet.type, {
          billingApiResp,
        });
      });
  }
}
